import { MigrationInterface, QueryRunner } from 'typeorm';

export class AlterReceiptsTable1659030478714 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
        ALTER TABLE receipt
        ADD COLUMN previous_credits decimal(19,2) NULL,
        ADD COLUMN total_credits decimal(19,2) NULL
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}
