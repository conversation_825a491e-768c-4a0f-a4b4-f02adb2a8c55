import { <PERSON><PERSON><PERSON>, <PERSON>NotEmpt<PERSON>, <PERSON><PERSON>ption<PERSON>, ValidateIf } from 'class-validator';

export enum DscRegiserQueyType {
  ORGANIZATION = 'ORGANIZATION',
  CLIENT = 'CLIENT',
  CLIENT_GROUP = 'CLIENT_GROUP'
}

export class FindDscRegisterDto {

  @IsOptional()
  sort: string;
  
  @IsNotEmpty()
  @IsEnum(DscRegiserQueyType)
  type: DscRegiserQueyType;

  @ValidateIf((o: FindDscRegisterDto) => o.type === DscRegiserQueyType.CLIENT)
  @IsOptional()
  clientId: number;

  @IsOptional()
  search: string;

  @IsOptional()
  clientGroupId: number;

  @IsOptional()
  limit: number;

  @IsOptional()
  offset: number;
}
