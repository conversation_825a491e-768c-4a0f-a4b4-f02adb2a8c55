import {
  AfterLoad,
  BaseEntity,
  Column,
  CreateDateColumn,
  Entity,
  JoinColumn,
  ManyToOne,
  OneToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import Task from './task.entity';
import Storage from 'src/modules/storage/storage.entity';

export enum StageOfWorkStatus {
  PENDING = 'PENDING',
  DONE = 'DONE',
}

export enum StageOfWorkType {
  STAGE_OF_WORK = 'STAGE_OF_WORK',
  DELIVERABLES = 'DELIVERABLES',
}

export enum AttributeType {
  REFERENCE_NUMBER = 'REFERENCE_NUMBER',
  ATTACHMENT = 'ATTACHMENT',
}

export interface IExtraAttributes {
  type: AttributeType;
  title: string;
  value: any;
}

@Entity()
class StageOfWork extends BaseEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  name: string;

  @Column({
    type: 'enum',
    enum: StageOfWorkType,
  })
  type: StageOfWorkType;

  @Column({ nullable: true })
  description: string;

  @Column({
    type: 'enum',
    enum: StageOfWorkStatus,
    default: StageOfWorkStatus.PENDING,
  })
  status: StageOfWorkStatus;

  @Column({ default: false })
  referenceNumber: boolean;

  @Column({ nullable: true })
  referenceNumberValue: string;

  @Column({ type: 'json' })
  extraAttributes: Array<IExtraAttributes>;

  @ManyToOne(() => Task, (task) => task.stageOfWorks)
  task: Task;

  @OneToOne(() => Storage, (storage) => storage.stageOfWork, { cascade: true })
  storage: Storage;

  @Column()
  attachmentFile: string;

  attachmentFileUrl: string;

  @CreateDateColumn()
  createdAt: string;

  @UpdateDateColumn()
  updatedAt: string;

  @AfterLoad()
  renderUrl() {
    if (this.attachmentFile) {
      this.attachmentFileUrl = `${process.env.AWS_BASE_URL}/${this.attachmentFile}`;
    }
  }
}

export default StageOfWork;
