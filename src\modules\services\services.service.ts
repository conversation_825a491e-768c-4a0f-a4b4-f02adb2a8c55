import {
  BadRequestException,
  ConflictException,
  Injectable,
  InternalServerErrorException,
  NotFoundException,
} from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { Event_Actions } from 'src/event-listeners/actions';
import Category from 'src/modules/categories/categories.entity';
import { User } from 'src/modules/users/entities/user.entity';
import { Brackets, createQueryBuilder, getManager, In, Not } from 'typeorm';
import CreateServiceDto from './dto/create-service.dto';
import FindServicesDto from './dto/find-services.dto';
import ImportServicesDto from './dto/import-services.dto';
import { Checklist } from './entities/checklist.entity';
import Milestone from './entities/milestone.entity';
import { Service } from './entities/service.entity';
import StageOfWork from './entities/stage-of-work.entity';
import { SubTask } from './entities/subtask.entity';
import Label from '../labels/label.entity';
import BulkUpdateDto from './dto/bulk-update.dto';
import ServicePreferences from './entities/service-preferences.entity';
import * as xlsx from 'xlsx';
import { StorageService } from '../storage/storage.service';
import Storage, { StorageSystem } from '../storage/storage.entity';
import ServiceStageOfWork from './entities/stage-of-work.entity';
import { OneDriveStorageService } from '../ondrive-storage/onedrive-storage.service';
import * as ExcelJS from 'exceljs';
import { GoogleDriveStorageService } from '../ondrive-storage/googledrive-storage.service';
import { ServiceFavorite } from './entities/service_favorite.entity';
import Task from '../tasks/entity/task.entity';
import { TaskRecurringStatus } from '../tasks/dto/types';

@Injectable()
export class ServicesService {
  constructor(
    private eventEmitter: EventEmitter2,
    private storageService: StorageService,
    private oneDriveService: OneDriveStorageService,
    private googleDriveService: GoogleDriveStorageService,
  ) {}

  async create(userId: number, data: CreateServiceDto) {
    let user = await User.findOne({
      where: { id: userId },
      relations: ['organization'],
    });

    data.name = data.name.trim();

    let existingService = await Service.findOne({
      where: {
        name: data.name,
        category: { id: data.category },
        ...(data.subCategory && { subCategory: { id: data.subCategory } }),
        organization: { id: user.organization.id },
      },
    });

    if (existingService) {
      throw new ConflictException(
        'Service already exists with the given Category, Subcategory and Name',
      );
    }

    let labelIds: number[] = [];
    let labelss: Label[] = [];

    if (data.labels) {
      labelIds = (data.labels as Label[]).map((label) => label.id);
      labelss = await Label.find({ where: { id: In(labelIds) } });
    }

    let category = await Category.findOne({ where: { id: data.category } });
    let subCategory = await Category.findOne({ where: { id: data.subCategory } });

    let service = new Service();
    service.name = data.name;
    service.description = data.description;
    service.hourlyPrice = data.hourlyPrice;
    service.totalPrice = data.totalPrice;
    service.organization = user.organization;
    service.category = category;
    service.subCategory = subCategory;
    service.checklists = data.checklists as Checklist[];
    service.milestones = data.milestones as Milestone[];
    service.stageOfWorks = data.stageOfWork as StageOfWork[];
    service.subTasks = data.subTasks as SubTask[];
    service.subtaskServices =
      data.subtaskServices?.length === 0 ? null : JSON.stringify(data.subtaskServices);
    service.state = data.state;
    service.clientSubCategory = JSON.stringify(data.clientSubCategory);
    service.serviceProcedure =
      data.serviceProcedure?.length === 0 ? null : JSON.stringify(data.serviceProcedure);
    service.serviceFaqs = data.serviceFaqs?.length === 0 ? null : JSON.stringify(data.serviceFaqs);
    service.linkedServices =
      data.linkedServices?.length === 0 ? null : JSON.stringify(data.linkedServices);
    service.labels = labelss;
    service.isRecurring = data.isRecurring;
    service.frequency = data.frequency;
    service.recurringFrequencyDetails = data.recurringFrequencyDetails;

    await service.save();

    this.eventEmitter.emit(Event_Actions.SERVICE_ADDED, {
      userId,
      serviceId: service.id,
      serviceName: service.name,
    });

    return service;
  }

  async findAll(userId: number, query: FindServicesDto) {
    let user = await User.findOne({
      where: { id: userId },
      relations: ['organization'],
    });
    let services = createQueryBuilder(Service, 'service')
      .leftJoinAndSelect('service.category', 'category')
      .leftJoinAndSelect('service.subCategory', 'subCategory')
      .leftJoinAndSelect('service.organization', 'organization')
      .leftJoinAndSelect('service.labels', 'labels')
      .orderBy('service.name', 'ASC')
      // .where('organization.id = :organizationId', {
      //   organizationId: user.organization.id,
      // });
      .andWhere('service.fromAdmin is not true')
      .andWhere('(service.defaultOne = :defaultOne OR organization.id = :organizationId)', {
        defaultOne: true,
        organizationId: user.organization.id,
      });
    const sort = typeof query?.sort === 'string' ? JSON.parse(query.sort) : query?.sort;
    if (sort?.column) {
      const columnMap: Record<string, string> = {
        category: 'service.category',
        subCategory: 'service.subCategory',
        name: 'service.name',
        organizations: 'service.name',
      };
      const column = columnMap[sort.column] || sort.column;
      services.orderBy(column, sort.direction.toUpperCase());
    } else {
      services.orderBy('service.createdAt', 'DESC');
    }
    if (query.selectedType === 'all') {
      services.andWhere('(service.defaultOne = :defaultOne OR service.fromAdmin = :fromAdmin)', {
        defaultOne: true,
        fromAdmin: false,
      });
    }

    if (query.selectedType === 'vider') {
      services.andWhere('service.defaultOne = :defaultOne', {
        defaultOne: true,
      });
    }

    if (query.selectedType === 'organization') {
      services.andWhere('service.organization_id = :fromadmin', {
        fromadmin: user.organization.id,
      });
    }

    if (query.search) {
      services.andWhere(
        new Brackets((qb) => {
          qb.where('service.name LIKE :search', {
            search: `%${query.search}%`,
          });
          qb.orWhere('category.name LIKE :search', {
            search: `%${query.search}%`,
          });
          qb.orWhere('subCategory.name LIKE :search', {
            search: `%${query.search}%`,
          });
          qb.orWhere('labels.name LIKE :search', {
            search: `%${query.search}%`,
          });
        }),
      );
    }
    if (query.category === '0') {
      query.category = null;
    }

    if (query.category) {
      services.andWhere('category.id = :category', {
        category: query.category,
      });
    }

    if (query.subCategory) {
      services.andWhere('subCategory.id = :subCategory', {
        subCategory: query.subCategory,
      });
    }

    if (query.offset) {
      services.skip(query.offset);
    }

    if (query.limit) {
      services.take(query.limit);
    }

    let data = await services.getManyAndCount();

    const [service, totalCount] = data;

    const organizationBasedServices = await ServicePreferences.find({
      where: {
        organizationId: user.organization.id,
        serviceId: In(service.map((service) => service.id)),
      },
    });
    const servicePreferencesMap = organizationBasedServices.reduce((acc, preference) => {
      acc[preference.serviceId] = true;
      return acc;
    }, {});

    const favoriteRows = await ServiceFavorite.find({
      where: {
        organizationId: user.organization.id,
      },
      relations: ['service'],
    });

    // Convert to a map for fast lookup
    const favoriteMap = favoriteRows.reduce((acc, row) => {
      acc[row?.service?.id] = true;
      return acc;
    }, {} as Record<number, boolean>);

    // Set the isFavorite flag properly
    service.forEach((service) => {
      service['serviceIsActive'] = servicePreferencesMap[service.id] ? true : false;
      service['isFavorite'] = favoriteMap[service.id] ? true : false;
    });

    return {
      totalCount: totalCount,
      result: service,
    };
  }

  async getServiceTasksDetails(userId: number, query: any) {
    let user = await User.findOne({
      where: { id: userId },
      relations: ['organization'],
    });
    const sort = typeof query?.sort === 'string' ? JSON.parse(query.sort) : query?.sort;
    let sortQuery = '';
    if (sort?.column) {
      sortQuery = 'ORDER BY ' + `${sort.column} ${sort.direction}`;
    }
    let sql = `
    SELECT 
        s.id AS serviceId,
        s.name AS name,
        s.category_id AS categoryId,
        s.sub_category_id AS subCategoryId,
        COUNT(CASE WHEN (t.recurring_status = 'created' OR t.recurring_status IS NULL) THEN t.id ELSE NULL END) AS total_count,
        s.default_one AS defaultOne,
        SUM(CASE WHEN t.status = 'todo' AND (t.recurring_status = 'created' OR t.recurring_status IS NULL) THEN 1 ELSE 0 END) AS todo_count,
        SUM(CASE WHEN t.status = 'completed' AND (t.recurring_status = 'created' OR t.recurring_status IS NULL) THEN 1 ELSE 0 END) AS completed_count,
        SUM(CASE WHEN t.status = 'on_hold' AND (t.recurring_status = 'created' OR t.recurring_status IS NULL) THEN 1 ELSE 0 END) AS on_hold_count,
        SUM(CASE WHEN t.status = 'under_review' AND (t.recurring_status = 'created' OR t.recurring_status IS NULL) THEN 1 ELSE 0 END) AS under_review_count,
        SUM(CASE WHEN t.status = 'terminated' AND (t.recurring_status = 'created' OR t.recurring_status IS NULL) THEN 1 ELSE 0 END) AS terminated_count,
        SUM(CASE WHEN t.status = 'deleted' AND (t.recurring_status = 'created' OR t.recurring_status IS NULL) THEN 1 ELSE 0 END) AS deleted_count,
        SUM(CASE WHEN t.status = 'in_progress' AND (t.recurring_status = 'created' OR t.recurring_status IS NULL) THEN 1 ELSE 0 END) AS in_progress_count,
        COUNT(DISTINCT(rp.id)) AS total_rp_count,
        c.name as categoryName,
        sb.name as subCategoryName
    FROM 
        service s
    LEFT JOIN category c ON c.id = s.category_id
    LEFT JOIN 
        category sb ON sb.id = s.sub_category_id
    LEFT JOIN
        task t ON t.service_id = s.id
                AND t.organization_id = ${user.organization.id}  AND 
                t.parent_task_id IS NULL
    LEFT JOIN 
        recurring_profile rp ON rp.id = t.recurring_profile_id
    WHERE 
        (s.organization_id = ${
          user.organization.id
        } OR s.default_one = ${true}) AND s.from_admin = false`;

    if (query.search) {
      sql += ` AND s.name LIKE '%${query.search}%'`;
    }

    if (query.category === '0') {
      query.category = null;
    }

    if (query.category) {
      sql += ` AND c.id = ${query.category}`;
    }

    if (query.subCategory) {
      sql += ` AND sb.id = ${query.subCategory}`;
    }
    sql += `
    GROUP BY s.id, s.name, c.name, sb.name
    ${sortQuery || 'ORDER BY s.name ASC'}
    LIMIT ${query.limit || 10}
    OFFSET ${query.offset || 0}
  `;

    // sql += ` GROUP BY s.id
    // `

    // if (query.limit) {
    //   sql += ` LIMIT ${query.limit}`;
    // }

    // if (query.offset) {
    //   sql += ` OFFSET ${query.offset}`;
    // }

    let totalService = await getManager().query(sql);

    const organizationBasedServices = await ServicePreferences.find({
      where: {
        organizationId: user.organization.id,
        serviceId: In(totalService.map((service) => service.serviceId)),
      },
    });
    const servicePreferencesMap = organizationBasedServices.reduce((acc, preference) => {
      acc[preference.serviceId] = true;
      return acc;
    }, {});

    totalService.forEach((service) => {
      service['serviceIsActive'] = servicePreferencesMap[service.serviceId] ? true : false;
    });
    return totalService;
  }
  async getServicesDashboardexport(userId: number, body: FindServicesDto) {
    const query = { ...body, offset: 0, limit: 1000000 };
    const services = await this.getServiceTasksDetails(userId, query);

    if (!services || services.length === 0) {
      throw new BadRequestException('No Data for Export');
    }

    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Service Dashboard');

    // Define headers
    const headers = [
      { header: 'S.No', key: 'serialNo' },
      { header: 'Service Category', key: 'serviceCategory' },
      { header: 'Service Sub Category', key: 'serviceSubCategory' },
      { header: 'Service Name', key: 'serviceName' },
      { header: 'Todo', key: 'todo' },
      { header: 'In Progress', key: 'inProgress' },
      { header: 'On Hold', key: 'onHold' },
      { header: 'Under Review', key: 'underReview' },
      { header: 'Completed', key: 'completed' },
      { header: 'Terminated', key: 'terminated' },
      { header: 'Deleted', key: 'deleted' },
      { header: 'Service Created By', key: 'serviceCreatedBy' },
    ];

    worksheet.columns = headers.map((h) => ({ key: h.key, header: h.header }));

    // Map data with proper serial numbers
    services.forEach((service, index) => {
      worksheet.addRow({
        serialNo: index + 1, // Properly assign incrementing serial numbers
        serviceCategory: service?.categoryName,
        serviceSubCategory: service?.subCategoryName,
        serviceName: service?.name,
        todo: service?.todo_count || 0,
        inProgress: service?.in_progress_count || 0,
        onHold: service?.on_hold_count || 0,
        underReview: service?.under_review_count || 0,
        completed: service?.completed_count || 0,
        terminated: service?.terminated_count || 0,
        deleted: service?.deleted_count || 0,
        serviceCreatedBy: service?.defaultOne ? 'VIDER' : 'Organization',
      });
    });

    // Style header row
    const headerRow = worksheet.getRow(1);
    headerRow.font = { bold: true };
    headerRow.eachCell((cell) => {
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '64B5F6' },
      };
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' },
      };
    });

    // Set column widths dynamically
    worksheet.columns.forEach((column) => {
      if (column.key === 'serviceName') {
        column.width = 50;
      } else if (['serviceCategory', 'serviceSubCategory'].includes(column.key)) {
        column.width = 30;
      } else {
        column.width = column.header.length + 5;
      }
      column.alignment = { wrapText: true, horizontal: 'center', vertical: 'middle' };
    });

    // Freeze header row
    worksheet.views = [{ state: 'frozen', ySplit: 1 }];

    // Generate and return Excel buffer
    return await workbook.xlsx.writeBuffer();
  }

  async exportservicesReport(userId: number, body: FindServicesDto) {
    let services = await this.findAll(userId, body);

    // Initialize Serial Counter
    let serialCounter = 1;

    // Transform service data into rows
    let rows = services.result.map((service: any) => {
      const serviceStatus = service?.serviceIsActive ? 'Inactive' : 'Active';
      return {
        'S.No': serialCounter++, // Assign and then increment
        'Service Created By': body.selectedType === 'vider' ? 'VIDER' : 'Organization',
        'Service Category': service?.category?.name || '',
        'Service Sub Category': service?.subCategory?.name || '',
        'Service Name': service?.name || '',
        'Status': serviceStatus,
      };
    });

    if (rows.length > 0) {
      // Create a new workbook and worksheet
      const workbook = new ExcelJS.Workbook();
      const sheetName =
        body.selectedType === 'all'
          ? 'All Services'
          : body.selectedType === 'organization'
          ? 'Organization Services'
          : 'Vider Services';
      const worksheet = workbook.addWorksheet(sheetName);

      // Define headers
      const headers = [
        { header: 'S.No', key: 'S.No' },
        { header: 'Service Created By', key: 'Service Created By' },
        { header: 'Service Category', key: 'Service Category' },
        { header: 'Service Sub Category', key: 'Service Sub Category' },
        { header: 'Service Name', key: 'Service Name' },
        { header: 'Status', key: 'Status' },
      ];

      worksheet.columns = headers;

      // Add rows and apply conditional formatting for Status
      rows.forEach((row) => {
        const addedRow = worksheet.addRow(row);
        const statusCell = addedRow.getCell('Status');

        // Apply color to the "Status" column based on its value
        if (row['Status'] === 'Active') {
          statusCell.font = { color: { argb: 'FF00B050' }, bold: true }; // Green
        } else if (row['Status'] === 'Inactive') {
          statusCell.font = { color: { argb: 'FFFF0000' }, bold: true }; // Red
        }
      });

      // Style the header row
      const headerRow = worksheet.getRow(1);
      headerRow.font = { bold: true };
      headerRow.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
      headerRow.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: '64B5F6' } };

      // Adjust column widths and wrap text
      worksheet.columns.forEach((column) => {
        if (column.key === 'Service Name') {
          column.width = 50;
        } else if (column.key === 'Service Category' || column.key === 'Service Sub Category') {
          column.width = 30;
        } else {
          column.width = column.header.length + 5;
        }
        column.alignment = { wrapText: true, horizontal: 'center', vertical: 'middle' };
      });

      // Freeze the header row
      worksheet.views = [{ state: 'frozen', ySplit: 1 }];

      // Write workbook to buffer
      const buffer = await workbook.xlsx.writeBuffer();
      return buffer;
    } else {
      throw new BadRequestException('No Data for Export');
    }
  }

  async getPreferedServices(userId: number, query: FindServicesDto) {
    let user = await User.findOne({
      where: { id: userId },
      relations: ['organization'],
    });

    const servicePreferenceData = await ServicePreferences.find({
      where: {
        organizationId: user.organization.id,
      },
    });
    let excludedServiceIds = servicePreferenceData.map((preference) => preference.serviceId);
    let services = createQueryBuilder(Service, 'service')
      .leftJoinAndSelect('service.category', 'category')
      .leftJoinAndSelect('service.subCategory', 'subCategory')
      .leftJoinAndSelect('service.organization', 'organization')
      .leftJoinAndSelect('service.labels', 'labels')
      .andWhere('service.fromAdmin is not true')
      .andWhere('(service.defaultOne = :defaultOne OR organization.id = :organizationId)', {
        defaultOne: true,
        organizationId: user.organization.id,
      });

    if (excludedServiceIds.length > 0) {
      services.andWhere('service.id NOT IN (:...excludedServiceIds)', { excludedServiceIds });
    }

    if (query.search) {
      services.andWhere(
        new Brackets((qb) => {
          qb.where('service.name LIKE :search', {
            search: `%${query.search}%`,
          });
          qb.orWhere('category.name LIKE :search', {
            search: `%${query.search}%`,
          });
          qb.orWhere('subCategory.name LIKE :search', {
            search: `%${query.search}%`,
          });
          qb.orWhere('labels.name LIKE :search', {
            search: `%${query.search}%`,
          });
        }),
      );
    }
    if (query.category === '0') {
      query.category = null;
    }

    if (query.category) {
      services.andWhere('category.id = :category', {
        category: query.category,
      });
    }

    if (query.subCategory) {
      services.andWhere('subCategory.id = :subCategory', {
        subCategory: query.subCategory,
      });
    }

    if (query.offset) {
      services.skip(query.offset);
    }

    if (query.limit) {
      services.take(query.limit);
    }

    services.orderBy('service.name');

    let data = await services.getManyAndCount();

    const favoriteRows = await createQueryBuilder(ServiceFavorite, 'serviceFavorite')
      .leftJoinAndSelect('serviceFavorite.service', 'service')
      .leftJoinAndSelect('service.category', 'category')
      .leftJoinAndSelect('service.subCategory', 'subCategory')
      .where('serviceFavorite.organizationId = :orgId', { orgId: user.organization.id })
      .andWhere('service.id IS NOT NULL');

    if (query.search) {
      favoriteRows.andWhere(
        new Brackets((qb) => {
          qb.where('service.name LIKE :search', {
            search: `%${query.search}%`,
          });
          qb.orWhere('category.name LIKE :search', {
            search: `%${query.search}%`,
          });
          qb.orWhere('subCategory.name LIKE :search', {
            search: `%${query.search}%`,
          });
          // qb.orWhere('labels.name LIKE :search', {
          //   search: `%${query.search}%`,
          // });
        }),
      );
    }

    if (query.category) {
      favoriteRows.andWhere('category.id = :category', {
        category: query.category,
      });
    }

    if (query.subCategory) {
      favoriteRows.andWhere('subCategory.id = :subCategory', {
        subCategory: query.subCategory,
      });
    }
    const result = await favoriteRows.getMany();
    const filteredFavorites = await result.filter(
      (fav) => !excludedServiceIds.includes(fav?.service?.id),
    );
    return {
      totalCount: data[1],
      result: data[0],
      favoriteRows: filteredFavorites,
    };
  }

  async findDefaultServices(query: FindServicesDto) {
    let services = createQueryBuilder(Service, 'service')
      .leftJoinAndSelect('service.category', 'category')
      .leftJoinAndSelect('service.subCategory', 'subCategory')
      .where('service.defaultOne = true');

    if (query.search) {
      services.andWhere('service.name LIKE :search', {
        search: `%${query.search}%`,
      });
    }

    if (query.category) {
      services.andWhere('category.id = :category', {
        category: query.category,
      });
    }

    if (query.subCategory) {
      services.andWhere('subCategory.id = :subCategory', {
        subCategory: query.subCategory,
      });
    }

    if (query.offset) {
      services.skip(query.offset);
    }

    if (query.limit) {
      services.take(query.limit);
    }

    let data = await services.getManyAndCount();

    return {
      totalCount: data[1],
      result: data[0],
    };
  }

  async importServices(userId: number, data: ImportServicesDto) {
    const entityManager = getManager();
    try {
      let user = await User.findOne({
        where: { id: userId },
        relations: ['organization'],
      });
      const orgId = user.organization.id;
      const entityManager = getManager();

      let repo = createQueryBuilder(Service, 'service')
        .leftJoinAndSelect('service.category', 'category')
        .leftJoinAndSelect('service.subCategory', 'subCategory')
        .leftJoinAndSelect('service.checklists', 'checklist')
        .leftJoinAndSelect('checklist.checklistItems', 'checklistItems')
        .leftJoinAndSelect('service.milestones', 'milestone')
        .leftJoinAndSelect('service.stageOfWorks', 'stageOfWork')
        .leftJoinAndSelect('service.subTasks', 'subTask');

      if (data.selectAll) {
        repo.where('service.defaultOne = true');
      }

      if (!data.selectAll) {
        repo.whereInIds(data.services);
      }

      let services = await repo.getMany();

      let newServices = [];

      for (let service of services) {
        let existingService = await Service.findOne({
          where: {
            name: service.name,
            organization: { id: user.organization.id },
          },
        });

        if (existingService) {
          throw new ConflictException('Service already exists');
        }

        let { category, subCategory, ...rest } = service;
        const categoryId = category.id;
        // const subCategoryName = subCategory.name

        const orgCategoryIdQuery = `SELECT id FROM category where admin_category_id=${categoryId} and organization_id = ${orgId};`;
        const getOrgCategoryId = await entityManager.query(orgCategoryIdQuery);

        if (getOrgCategoryId.length) {
          const orgCategoryId = getOrgCategoryId[0].id;
          const subCategoryName = subCategory.name;

          const subCategoryIdFromCategoryQuery = `SELECT id FROM category where parent_category_id=${orgCategoryId} and name="${subCategoryName}";`;
          const getSubCategoryIdFromCategory = await entityManager.query(
            subCategoryIdFromCategoryQuery,
          );

          category.id = orgCategoryId;
          subCategory.id = getSubCategoryIdFromCategory[0].id;
        }

        let formattedService = JSON.stringify(rest, (key, value) => {
          if (key === 'id') {
            return undefined;
          }
          return value;
        });

        let parsedService = JSON.parse(formattedService);

        let newService = new Service();
        newService.name = parsedService.name;
        newService.description = parsedService.description;
        newService.hourlyPrice = parsedService.hourlyPrice;
        newService.totalPrice = parsedService.totalPrice;
        newService.category = category;
        newService.subCategory = subCategory;
        newService.organization = user.organization;
        newService.defaultOne = false;
        newService.checklists = parsedService.checklists;
        newService.stageOfWorks = parsedService.stageOfWorks;
        newService.subTasks = parsedService.subTasks;
        newService.milestones = parsedService.milestones;
        newService.fromAdmin = true;
        newService.adminServiceId = service.id;

        newServices.push(newService);
      }

      await Service.save(newServices);

      return { message: 'Services imported' };
    } catch (e) {
      console.log(e);
      throw new InternalServerErrorException(e);
    }
  }

  async findOne(userId: number, id: number) {
    let user = await User.findOne({
      where: { id: userId },
      relations: ['organization'],
    });

    let service = await createQueryBuilder(Service, 'service')
      .leftJoinAndSelect('service.checklists', 'checklist')
      .leftJoinAndSelect('checklist.checklistItems', 'checklistItems')
      .leftJoinAndSelect('service.milestones', 'milestone')
      .leftJoinAndSelect('service.stageOfWorks', 'stageOfWork')
      .leftJoinAndSelect('stageOfWork.storage', 'storage')
      .leftJoinAndSelect('service.subTasks', 'subTask')
      .leftJoinAndSelect('service.category', 'category')
      .leftJoinAndSelect('service.subCategory', 'subCategory')
      .leftJoinAndSelect('service.organization', 'organization')
      .leftJoinAndSelect('service.labels', 'labels')
      .where('service.id = :serviceId', { serviceId: id })
      .getOne();

    //For services of deleted organization
    if (!service || (!service.defaultOne && service.organization === null)) {
      return null;
    }

    if (service.defaultOne) {
      return service;
    } else if (user.organization && user.organization.id === service.organization.id) {
      return service;
    } else {
      return null;
    }
  }

  async update(userId: number, id: number, data: CreateServiceDto) {
    let service = await Service.findOne({ where: { id } });

    if (!service) {
      throw new NotFoundException('Service not found');
    }

    data.name = data.name.trim();

    let user = await User.findOne({
      where: { id: userId },
      relations: ['organization'],
    });

    let existingService = await Service.findOne({
      where: {
        name: data.name,
        id: Not(id),
        category: { id: data.category },
        ...(data.subCategory && { subCategory: { id: data.subCategory } }),
        organization: { id: user.organization.id },
      },
    });

    let existingDefaultService = await Service.findOne({
      where: {
        name: data.name,
        id: Not(id),
        category: { id: data.category },
        ...(data.subCategory && { subCategory: { id: data.subCategory } }),
        defaultOne: true,
      },
    });

    if (existingService || existingDefaultService) {
      throw new ConflictException(
        'Service already exists with the given Category, Subcategory and Name',
      );
    }

    let labelIds: number[] = [];
    let labelss: Label[] = [];

    if (data.labels) {
      labelIds = (data.labels as Label[]).map((label) => label.id);
      labelss = await Label.find({ where: { id: In(labelIds) } });
    }

    let category = await Category.findOne({ where: { id: data.category } });
    let subCategory = await Category.findOne({ where: { id: data.subCategory } });

    service.name = data.name;
    service.description = data.description;
    service.hourlyPrice = data.hourlyPrice;
    service.totalPrice = data.totalPrice;
    service.category = category;
    service.subCategory = subCategory;
    service.checklists = data.checklists as Checklist[];
    service.milestones = data.milestones as Milestone[];
    service.stageOfWorks = data.stageOfWork as ServiceStageOfWork[];
    service.subTasks = data.subTasks as SubTask[];
    service.version = service.version + 1;
    service.subtaskServices =
      data.subtaskServices?.length === 0 ? null : JSON.stringify(data.subtaskServices);
    service.state = data.state;
    service.clientSubCategory = JSON.stringify(data.clientSubCategory);
    service.serviceProcedure =
      data.serviceProcedure?.length === 0 ? null : JSON.stringify(data.serviceProcedure);
    service.serviceFaqs = data.serviceFaqs?.length === 0 ? null : JSON.stringify(data.serviceFaqs);
    service.linkedServices =
      data.linkedServices?.length === 0 ? null : JSON.stringify(data.linkedServices);
    service.labels = labelss;
    service.isRecurring = data.isRecurring;
    service.frequency = data.frequency;
    service.recurringFrequencyDetails = data.recurringFrequencyDetails;

    await service.save();

    if (!service.defaultOne) {
      this.eventEmitter.emit(Event_Actions.SERVICE_UPDATED, {
        userId,
        serviceId: service.id,
        serviceName: service.name,
      });
    }

    return service;
  }

  async delete(id: number) {
    let service = await Service.findOne({ where: { id: id } });
    if (!service) {
      throw new NotFoundException('Service not found');
    }

    await service.remove();
    return service;
  }

  async bulkUpdate(userId: number, data: BulkUpdateDto) {
    const { ids, status } = data;
    let user = await User.findOne({
      where: { id: userId },
      relations: ['organization'],
    });

    try {
      await getManager().transaction(async (transactionalEntityManager) => {
        if (status === 'ACTIVE') {
          await Promise.all(
            ids.map(async (serviceId) => {
              await transactionalEntityManager.delete(ServicePreferences, {
                serviceId,
                organizationId: user.organization.id,
              });
            }),
          );
        } else if (status === 'INACTIVE') {
          await Promise.all(
            ids.map(async (serviceId) => {
              //   await transactionalEntityManager.insert(ServicePreferences, {
              //     serviceId,
              //     organizationId: user.organization.id,
              //   });
              const existingPreference = await transactionalEntityManager.findOne(
                ServicePreferences,
                {
                  where: {
                    serviceId,
                    organizationId: user.organization.id,
                  },
                },
              );

              if (!existingPreference) {
                await transactionalEntityManager.save(ServicePreferences, {
                  serviceId,
                  organizationId: user.organization.id,
                });
              }
            }),
          );
        }
      });
      return 'success';
    } catch (error) {
      console.error('Error in bulk update for service status:', error);
      throw error;
    }
  }

  async bulkDelete(userId: number, ids: number[]) {
    let user = await User.findOne({
      where: { id: userId },
      relations: ['organization'],
    });
    try {
      const deletedServices = [];
      const upcomingTasks = await createQueryBuilder(Task, 'task')
        .leftJoinAndSelect('task.service', 'service')
        .where('service.id IN (:...ids)', { ids: ids })
        .andWhere(
          new Brackets((qb) => {
            qb.where('task.recurringStatus IS NULL').orWhere(
              'task.recurringStatus = :recurringStatus',
              { recurringStatus: TaskRecurringStatus.PENDING },
            );
          }),
        )
        .getMany();

      let serviceNames = [];
      for (let i of upcomingTasks) {
        if (!serviceNames.includes(i.service.name)) {
          serviceNames.push(i.service.name);
        }
      }
      if (serviceNames.length > 0) {
        return { errorsList: serviceNames };
      }
      for (const id of ids) {
        const service = await Service.findOne({ where: { id: id } });
        if (!service) {
          throw new Error(`Service with ID ${id} not found`);
        }
        await service.remove();
        deletedServices.push(service);
        await ServicePreferences.delete({
          serviceId: id,
          organizationId: user.organization.id,
        });
      }
      return deletedServices;
    } catch (error) {
      console.error('Error in bulk delete:', error);
      throw error;
    }
  }

  // async bulkClone(userId:number,ids:number[]){
  //   try {
  //     await getManager().transaction(async () => {
  //         for (const id of ids) {
  //             await this.cloneService(userId, id,);
  //         }
  //     });
  //     return 'success';
  // } catch (error) {
  //     console.error('Error in bulk clone:', error);
  //     throw error;
  // }
  // }

  async cloneService(userId: number, id: number, body: any) {
    let user = await User.findOne({
      where: { id: userId },
      relations: ['organization'],
    });

    let service = await createQueryBuilder(Service, 'service')
      .leftJoinAndSelect('service.checklists', 'checklist')
      .leftJoinAndSelect('checklist.checklistItems', 'checklistItems')
      .leftJoinAndSelect('service.stageOfWorks', 'stageOfWork')
      .leftJoinAndSelect('service.milestones', 'milestone')
      .leftJoinAndSelect('service.category', 'category')
      .leftJoinAndSelect('service.subCategory', 'subCategory')
      .leftJoinAndSelect('service.organization', 'organization')
      .leftJoinAndSelect('service.labels', 'labels')
      .where('service.id = :serviceId', { serviceId: id })
      .getOne();

    const stageOfWorkIds = service.stageOfWorks.map((stageOfWork) => stageOfWork.id);
    const stagesOfWork = await StageOfWork.findByIds(stageOfWorkIds, { relations: ['storage'] });

    let modifiedStagesOfWork = stagesOfWork.map((stageOfWork) => {
      let formattedStageOfWork = JSON.stringify(stageOfWork, (key, value) => {
        if (key === 'id') {
          return undefined;
        }
        return value;
      });
      return JSON.parse(formattedStageOfWork);
    });

    if (!service) {
      throw new NotFoundException('Service not found');
    }

    let { category, subCategory, ...rest } = service;

    let formattedService = JSON.stringify(rest, (key, value) => {
      if (key === 'id') {
        return undefined;
      }
      return value;
    });

    let parsedService = JSON.parse(formattedService);

    let newService = new Service();
    newService.name = `${rest.name} (1)`;
    newService.description = rest.description;
    newService.hourlyPrice = rest.hourlyPrice;
    newService.totalPrice = rest.totalPrice;
    newService.category = category;
    newService.subCategory = subCategory;
    newService.state = rest.state;
    newService.labels = rest.labels;
    newService.clientSubCategory = rest.clientSubCategory;
    newService.checklists = parsedService.checklists;
    newService.stageOfWorks = modifiedStagesOfWork;
    newService.organization = user.organization;
    newService.state = parsedService.state;
    newService.subtaskServices = rest.subtaskServices;
    newService.linkedServices = rest.linkedServices;
    newService.isRecurring = rest.isRecurring;
    newService.recurringFrequencyDetails = rest.recurringFrequencyDetails;
    newService.serviceProcedure = rest.serviceProcedure;
    newService.serviceFaqs = rest.serviceFaqs;
    newService.fromAdmin = false;
    newService.adminServiceId = null;
    await newService.save();

    if (body.serviceIsactive === 'add preference') {
      let newServicePreferences = new ServicePreferences();
      newServicePreferences.serviceId = id;
      newServicePreferences.organizationId = user.organization.id;
      await newServicePreferences.save();
    } else if (body.serviceIsactive === 'delete preference') {
      const servicePreference = await ServicePreferences.findOne({
        where: {
          serviceId: id,
          organizationId: user.organization.id,
        },
      });
      if (servicePreference) {
        await servicePreference.remove();
      }
    }

    return newService;
  }

  async updateAdminServices(userId: number) {
    let user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
    let services = await Service.find({
      where: {
        organization: { id: user.organization.id },
        fromAdmin: true,
      },
    });

    for (let service of services) {
      let adminService = await Service.findOne({
        where: { id: service.adminServiceId },
        relations: [
          'category',
          'subCategory',
          'checklists',
          'checklists.checklistItems',
          'milestones',
          'stageOfWorks',
          'subTasks',
        ],
      });

      if (!adminService) continue;

      if (service.version === adminService.version) {
        continue;
      }

      let { category, subCategory, ...rest } = adminService;

      let formattedService = JSON.stringify(rest, (key, value) => {
        if (key === 'id') return undefined;
        return value;
      });

      let parsedService = JSON.parse(formattedService);

      service.name = parsedService.name;
      service.description = parsedService.description;
      service.hourlyPrice = parsedService.hourlyPrice;
      service.totalPrice = parsedService.totalPrice;
      service.category = category;
      service.subCategory = subCategory;
      service.checklists = parsedService.checklists;
      service.stageOfWorks = parsedService.stageOfWorks;
      service.subTasks = parsedService.subTasks;
      service.milestones = parsedService.milestones;
      service.version = adminService.version;
      await service.save();
    }

    return 'Updated';
  }

  async servicePreference(userId: number, id: number, action: string) {
    let user = await User.findOne({
      where: { id: userId },
      relations: ['organization'],
    });

    if (action === 'addPreference') {
      let newServicePreferences = new ServicePreferences();
      newServicePreferences.serviceId = id;
      newServicePreferences.organizationId = user.organization.id;
      await newServicePreferences.save();
    } else if (action === 'deletePreference') {
      const servicePreference = await ServicePreferences.findOne({
        where: {
          serviceId: id,
          organizationId: user.organization.id,
        },
      });
      if (servicePreference) {
        await servicePreference.remove();
      }
    }
    return 'success';
  }

  async addMilestone(id: number, userId: number, body: any) {
    let storage: Storage;
    let service = await Service.findOne({ where: { id } });
    const milestone = new ServiceStageOfWork();
    milestone.name = body.name;
    milestone.type = body.type;
    milestone.description = body.description;
    milestone.referenceNumber = body.referenceNumber;
    milestone.referenceNumberValue = body.referenceNumberValue;
    milestone.extraAttributes = body.extraAttributes;
    if (body?.storage) {
      storage = await this.storageService.addAttachements(userId, body.storage);
    }
    milestone.service = service;
    const mi = await milestone.save();
    if (storage) {
      storage.serviceStageOfWork = mi;
      await storage.save();
    }
  }

  async updateMilestone(id: number, userId: number, body: any) {
    let storage: Storage;
    const user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
    const stageOfWork = await ServiceStageOfWork.findOne({
      where: { id: id },
      relations: ['storage'],
    });

    if (body?.storage) {
      if (stageOfWork?.storage?.id) {
        if (body.storage.name !== stageOfWork?.storage?.name) {
          if (stageOfWork?.storage.storageSystem === StorageSystem.AMAZON) {
            this.storageService.deleteAwsFile(stageOfWork?.storage.file);
          } else if (stageOfWork?.storage.storageSystem === StorageSystem.MICROSOFT) {
            this.oneDriveService.deleteOneDriveFile(userId, stageOfWork?.storage?.fileId);
          } else if (stageOfWork?.storage.storageSystem === StorageSystem.GOOGLE) {
            this.googleDriveService.deleteGoogleDriveFile(userId, stageOfWork?.storage?.fileId);
          }
        }
        storage = await Storage.findOne({ where: { id: stageOfWork?.storage?.id } });
        storage.fileType = body?.storage?.fileType;
        storage.fileSize = body?.storage?.fileSize;
        storage.name = body?.storage?.name;
        storage.file = body?.storage?.upload;
        storage.show = body?.storage?.show;
        storage.storageSystem = body?.storage?.storageSystem;
        storage.webUrl = body?.storage?.webUrl;
        storage.downloadUrl = body?.storage?.downloadUrl;
        storage.fileId = body?.storage?.fileId;
        storage.authId = user.organization.id;
      } else {
        storage = await this.storageService.addAttachements(userId, body?.storage);
        // user.imageStorage = storage;
      }
    } else {
      if (stageOfWork?.storage?.id) {
        const existingPStorage = await Storage.findOne({ where: { id: stageOfWork?.storage?.id } });
        const existingStorage = await existingPStorage.remove();
        if (existingStorage) {
          if (existingStorage.storageSystem === StorageSystem.AMAZON) {
            this.storageService.deleteAwsFile(existingStorage.file);
          } else if (existingStorage.storageSystem === StorageSystem.MICROSOFT) {
            this.oneDriveService.deleteOneDriveFile(userId, existingStorage.fileId);
          } else if (existingStorage.storageSystem === StorageSystem.GOOGLE) {
            this.googleDriveService.deleteGoogleDriveFile(userId, existingStorage.fileId);
          }
          stageOfWork.storage = null;
        }
      }
    }
    stageOfWork.name = body.name;
    stageOfWork.description = body.description;
    stageOfWork.referenceNumber = body.referenceNumber;
    stageOfWork.referenceNumberValue = body.referenceNumberValue;
    stageOfWork.extraAttributes = body.extraAttributes;
    const sw = await stageOfWork.save();
    if (storage) {
      storage.serviceStageOfWork = sw;
      await storage.save();
    }
  }

  async deleteMilestone(id: number, userId: number) {
    let stageOfWork = await ServiceStageOfWork.findOne({ where: { id }, relations: ['storage'] });
    const deletestageOfWork = await stageOfWork.remove();
    if (deletestageOfWork) {
      if (deletestageOfWork.storage?.storageSystem == StorageSystem.MICROSOFT) {
        const fileId = deletestageOfWork.storage.fileId;
        await this.oneDriveService.deleteOneDriveFile(userId, fileId);
      } else if (deletestageOfWork.storage?.storageSystem == StorageSystem.GOOGLE) {
        const fileId = deletestageOfWork.storage.fileId;
        await this.googleDriveService.deleteGoogleDriveFile(userId, fileId);
      } else if (deletestageOfWork.storage?.storageSystem == StorageSystem.AMAZON) {
        this.storageService.deleteAwsFile(deletestageOfWork.storage.file);
      }
    }
    return deletestageOfWork;
  }

  async addFavorite(userId: number, body: any) {
    try {
      const user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
      const service = await Service.findOne({ where: { id: body?.id } });
      const favService = await ServiceFavorite.findOne({
        where: { service: service, organizationId: user?.organization?.id },
      });
      if (favService) {
      } else {
        const favorite = new ServiceFavorite();
        favorite.service = service;
        favorite.organizationId = user?.organization?.id;
        await favorite.save();
      }
    } catch (error) {
      console.log('Error Occur while Service addFavorite', error?.message);
    }
  }

  async removeFavorite(userId: number, body: any) {
    try {
      const user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
      const service = await Service.findOne({ where: { id: body?.id } });
      const favService = await ServiceFavorite.findOne({
        where: { service: service, organizationId: user?.organization?.id },
      });
      if (favService) {
        await favService.remove();
      }
    } catch (error) {
      console.log('Error Occur While RemoveFavories', error?.message);
    }
  }
}
