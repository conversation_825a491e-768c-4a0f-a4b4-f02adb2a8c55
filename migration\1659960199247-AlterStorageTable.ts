import { MigrationInterface, QueryRunner } from 'typeorm';

export class AlterStorageTable1659960199247 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
        ALTER TABLE storage
        DROP COLUMN local_link,
        MODIFY COLUMN type enum('folder','file','link','local_path') not null
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}
