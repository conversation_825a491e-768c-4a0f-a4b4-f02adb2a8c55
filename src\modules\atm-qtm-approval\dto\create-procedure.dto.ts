import { Type } from "class-transformer";
import { IsNotEmpty, IsOptional, ValidateNested } from "class-validator";
import { Level } from "./create-approval-aprocess.dto";



class CreareProcedureDto {

    @IsNotEmpty()
    module: string;

    @IsOptional()
    category: number;

    @IsNotEmpty()
    name: string;

    // @IsNotEmpty()
    // approvalId: number;

    @IsOptional()
    description: string;

    @IsNotEmpty({ message: 'Approval levels are required' })
    @ValidateNested({ each: true }) // Use `each: true` to validate each element in the array
    @Type(() => Level) // Use the `@Type()` decorator to specify the nested class
    approvalLevels: Level[];
};

export default CreareProcedureDto