import {
  IsArray,
  IsEnum,
  IsNotEmpty,
  IsNumberString,
  IsOptional,
  IsString,
  ValidateIf,
} from 'class-validator';
import { CategoryEnum, SubCategoryEnum } from 'src/modules/clients/dto/types';
import { DateFilters, PriorityEnum, QueryTypeEnum, TaskStatus } from './types';

class FindTasksQuery {
  @IsOptional()
  sort: any;


  @IsOptional()
  task: string;

  @IsOptional()
  pageSize: any;

  @IsOptional()
  @IsString()
  page: number;

  @IsOptional()
  offset: number;

  @IsOptional()
  limit: number;

  @IsOptional()
  pending: string;

  @IsOptional()
  client: number;

  @IsOptional()
  clientId: number;

  @IsOptional()
  clientGroup: number;

  @IsOptional()
  search: string;

  @IsOptional()
  assignee: [];

  @IsOptional()
  userId: number;

  @IsOptional()
  taskLeader: [];

  @IsOptional()
  createdBy: any[];

  @IsOptional()
  completedBy: [];

  @IsOptional()
  financialYear: [];

  @IsOptional()
  status: any;

  @IsOptional()
  billingType: [];

  @IsOptional()
  billable: String[];

  @IsOptional()
  priority: PriorityEnum[];

  @IsOptional()
  tags: [];

  @IsOptional()
  taskType: Array<'recurring' | 'non_recurring'>;

  @IsOptional()
  startDate: DateFilters[];

  @IsOptional()
  dueOn: DateFilters[];

  @IsOptional()
  createdOn: DateFilters[];

  @IsOptional()
  completedOn: DateFilters[];

  @IsOptional()
  category: number[];

  @IsOptional()
  subCategory: number[];

  @IsOptional()
  clientCategory: CategoryEnum[];

  @IsOptional()
  clientSubCategory: SubCategoryEnum[];

  @IsOptional()
  customDates: string;

  @IsOptional()
  onHoldRemarkType: string[];

  @IsOptional()
  startDates: string;

  @IsOptional()
  endDates: string;

  @IsOptional()
  @IsArray()
  selected: [];

  @IsOptional()
  subTasks: boolean;

  @IsOptional()
  removeCompleted: any;

  @IsOptional()
  mainTaks: string;

  @IsOptional()
  allTasks: string;

  @IsOptional()
  taskValue: 'task' | 'sub-task' | 'approval-task';

  @IsOptional()
  serviceDetails: number | null;

  @IsOptional()
  fromDate: string

  @IsOptional()
  toDate: string;

  @IsOptional()
  fy: string[];


}

export default FindTasksQuery;
