<!DOCTYPE html>
<html>
  <head> </head>

  <body style="font-family: 'inter', sans-serif; background-color: #faf8f5">
    <table>
      <tr>
        <td>
          <div
            style="
              background-color: #08153c;
              width: 100%;
              padding-top: 15px;
              padding-bottom: 15px;
              text-align: center;
            "
          >
            <a href="https://vider.in/atom.html"
              ><img src="https://vider.in/images/atom.png" style="width: 102px"
            /></a>
          </div>
        </td>
      </tr>
      <tr>
        <td>
          <p
            style="
              color: #000;
              text-align: center;
              padding: 0px;
              font-size: 12px;
              font-style: normal;
              font-weight: 600;
              line-height: 150.523%;
              letter-spacing: 0.72px;
            "
          >
            Hi <%= fullname %>
          </p>
        </td>
      </tr>
      <tr>
        <td>
          <p
            style="
              color: #000;
              text-align: center;
              padding: 0px;
              font-size: 19px;
              font-style: normal;
              font-weight: 600;
              line-height: 150.523%;
              letter-spacing: 1.14px;
            "
          >
            Your Task For Today.
          </p>
        </td>
      </tr>
      <tr>
        <td
          style="
            background: #fff;
            box-shadow: 0px 0px 4px 0px rgba(0, 0, 0, 0.25);
            padding: 20px;
            padding-top: 20px;
          "
        >
          <% if(getDueTasks) { %>
          <table>
            <tr>
              <td>
                <p
                  style="
                    color: #08153c;
                    font-size: 13.84px;
                    font-style: normal;
                    font-weight: 800;
                    line-height: 150.523%;
                    letter-spacing: 0.83px;
                  "
                >
                  Task Due Today:
                </p>
              </td>
            </tr>
            <tr>
              <td>
                <table style="border-spacing: 0px; border: 0.923px solid #cfcfcf">
                  <thead>
                    <tr>
                      <th
                        style="
                          border: 0.923px solid #cfcfcf;
                          background: rgba(8, 21, 60, 0.88);
                          padding: 10px;
                          color: #fff;
                          font-size: 11.082px;
                          font-style: normal;
                          font-weight: 500;
                          line-height: 150.523%;
                          letter-spacing: 0.665px;
                          width: 10vw;
                        "
                      >
                        S.No
                      </th>
                      <th
                        style="
                          border: 0.923px solid #cfcfcf;
                          background: rgba(8, 21, 60, 0.88);
                          padding: 10px;
                          color: #fff;
                          font-size: 11.082px;
                          font-style: normal;
                          font-weight: 500;
                          line-height: 150.523%;
                          letter-spacing: 0.665px;
                          width: 25vw;
                        "
                      >
                        Client Name
                      </th>
                      <th
                      style="
                        border: 0.923px solid #cfcfcf;
                        background: rgba(8, 21, 60, 0.88);
                        padding: 10px;
                        color: #fff;
                        font-size: 11.082px;
                        font-style: normal;
                        font-weight: 500;
                        line-height: 150.523%;
                        letter-spacing: 0.665px;
                        width: 25vw;
                      "
                      >
                        Task ID
                      </th>
                      <th
                        style="
                          border: 0.923px solid #cfcfcf;
                          background: rgba(8, 21, 60, 0.88);
                          padding: 10px;
                          color: #fff;
                          font-size: 11.082px;
                          font-style: normal;
                          font-weight: 500;
                          line-height: 150.523%;
                          letter-spacing: 0.665px;
                          width: 40vw;
                        "
                      >
                        Task Name
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    <% for(let i=0; i < getDueTasks.length; i++ ) { %> <% let incrementedValue=i +
                    1; %>
                    <tr>
                      <td
                        style="
                          text-align: center;
                          padding: 10px;
                          color: #5f5f5f;
                          font-size: 11.533px;
                          font-style: normal;
                          font-weight: 600;
                          line-height: 150.523%;
                          letter-spacing: 0.692px;
                          border: 0.923px solid #cfcfcf;
                        "
                      >
                        <%= incrementedValue %>
                      </td>
                      <td
                        style="
                          text-align: center;
                          padding: 10px;
                          color: #5f5f5f;
                          font-size: 11.533px;
                          font-style: normal;
                          font-weight: 600;
                          line-height: 150.523%;
                          letter-spacing: 0.692px;
                          border: 0.923px solid #cfcfcf;
                        "
                      >
                        <%= getDueTasks[i].client.displayName %>
                      </td>
                      <td
                        style="
                          text-align: center;
                          padding: 10px;
                          color: #5f5f5f;
                          font-size: 11.533px;
                          font-style: normal;
                          font-weight: 600;
                          line-height: 150.523%;
                          letter-spacing: 0.692px;
                          border: 0.923px solid #cfcfcf;
                        "
                      >
                        <%= getDueTasks[i].taskNumber %>
                      </td>
                      <td
                        style="
                          text-align: center;
                          padding: 10px;
                          color: #5f5f5f;
                          font-size: 11.533px;
                          font-style: normal;
                          font-weight: 600;
                          line-height: 150.523%;
                          letter-spacing: 0.692px;
                          border: 0.923px solid #cfcfcf;
                        "
                      >
                        <%= getDueTasks[i].name %>
                      </td>
                    </tr>
                    <% } %>
                  </tbody>
                </table>
              </td>
            </tr>
          </table>
          <% } %> <% if(generalEvents) { %>
          <table>
            <tr>
              <td>
                <p
                  style="
                    color: #08153c;
                    font-size: 13.84px;
                    font-style: normal;
                    font-weight: 800;
                    line-height: 150.523%;
                    letter-spacing: 0.83px;
                  "
                >
                  General Events :
                </p>
              </td>
            </tr>
            <tr>
              <td>
                <table style="border-spacing: 0px; border: 0.923px solid #cfcfcf">
                  <thead>
                    <tr>
                      <th
                        style="
                          border: 0.923px solid #cfcfcf;
                          background: rgba(8, 21, 60, 0.88);
                          padding: 10px;
                          color: #fff;
                          font-size: 11.082px;
                          font-style: normal;
                          font-weight: 500;
                          line-height: 150.523%;
                          letter-spacing: 0.665px;
                          width: 10vw;
                        "
                      >
                        S.No
                      </th>
                      <th
                        style="
                          border: 0.923px solid #cfcfcf;
                          background: rgba(8, 21, 60, 0.88);
                          padding: 10px;
                          color: #fff;
                          font-size: 11.082px;
                          font-style: normal;
                          font-weight: 500;
                          line-height: 150.523%;
                          letter-spacing: 0.665px;
                          width: 30vw;
                        "
                      >
                        Event Title
                      </th>
                      <th
                        style="
                          border: 0.923px solid #cfcfcf;
                          background: rgba(8, 21, 60, 0.88);
                          padding: 10px;
                          color: #fff;
                          font-size: 11.082px;
                          font-style: normal;
                          font-weight: 500;
                          line-height: 150.523%;
                          letter-spacing: 0.665px;
                          width: 30vw;
                        "
                      >
                        Event Location
                      </th>
                      <th
                        style="
                          border: 0.923px solid #cfcfcf;
                          background: rgba(8, 21, 60, 0.88);
                          padding: 10px;
                          color: #fff;
                          font-size: 11.082px;
                          font-style: normal;
                          font-weight: 500;
                          line-height: 150.523%;
                          letter-spacing: 0.665px;
                          width: 30vw;
                        "
                      >
                        Event Duration
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    <% for(let i=0; i < generalEvents.length; i++ ) { %> <% let incrementedValue=i +
                    1; %>
                    <tr>
                      <td
                        style="
                          text-align: center;
                          padding: 10px;
                          color: #5f5f5f;
                          font-size: 11.533px;
                          font-style: normal;
                          font-weight: 600;
                          line-height: 150.523%;
                          letter-spacing: 0.692px;
                          border: 0.923px solid #cfcfcf;
                        "
                      >
                        <%= incrementedValue %>
                      </td>
                      <td
                        style="
                          text-align: center;
                          padding: 10px;
                          color: #5f5f5f;
                          font-size: 11.533px;
                          font-style: normal;
                          font-weight: 600;
                          line-height: 150.523%;
                          letter-spacing: 0.692px;
                          border: 0.923px solid #cfcfcf;
                        "
                      >
                        <%= generalEvents[i].title %>
                      </td>
                      <td
                        style="
                          text-align: center;
                          padding: 10px;
                          color: #5f5f5f;
                          font-size: 11.533px;
                          font-style: normal;
                          font-weight: 600;
                          line-height: 150.523%;
                          letter-spacing: 0.692px;
                          border: 0.923px solid #cfcfcf;
                        "
                      >
                        <%= generalEvents[i].location %>
                      </td>
                      <td
                        style="
                          text-align: center;
                          padding: 10px;
                          color: #5f5f5f;
                          font-size: 11.533px;
                          font-style: normal;
                          font-weight: 600;
                          line-height: 150.523%;
                          letter-spacing: 0.692px;
                          border: 0.923px solid #cfcfcf;
                        "
                      >
                        <%= generalEvents[i].duration %>
                      </td>
                    </tr>
                    <% } %>
                  </tbody>
                </table>
              </td>
            </tr>
          </table>
          <% } %> <% if(taskEvents) { %>
          <table>
            <tr>
              <td>
                <p
                style="
                  color: #08153c;
                  font-size: 13.84px;
                  font-style: normal;
                  font-weight: 800;
                  line-height: 150.523%;
                  letter-spacing: 0.83px;
                "
              >
                    Task Events
              </p>
              </td>
            </tr>
            <tr>
              <td>
                <table style="border-spacing: 0px; border: 0.923px solid #cfcfcf">
                  <thead>
                    <tr>
                      <th
                        style="
                          border: 0.923px solid #cfcfcf;
                          background: rgba(8, 21, 60, 0.88);
                          padding: 10px;
                          color: #fff;
                          font-size: 11.082px;
                          font-style: normal;
                          font-weight: 500;
                          line-height: 150.523%;
                          letter-spacing: 0.665px;
                          width: 7vw;
                        "
                      >
                        S.No
                      </th>
                      <th
                        style="
                          border: 0.923px solid #cfcfcf;
                          background: rgba(8, 21, 60, 0.88);
                          padding-left: 70px;
                          padding-right: 70px;
                          color: #fff;
                          font-size: 11.082px;
                          font-style: normal;
                          font-weight: 500;
                          line-height: 150.523%;
                          letter-spacing: 0.665px;
                          width: 28vw;
                        "
                      >
                        Client
                      </th>
                      <th
                        style="
                          border: 0.923px solid #cfcfcf;
                          background: rgba(8, 21, 60, 0.88);
                          padding-left: 70px;
                          padding-right: 70px;
                          color: #fff;
                          font-size: 11.082px;
                          font-style: normal;
                          font-weight: 500;
                          line-height: 150.523%;
                          letter-spacing: 0.665px;
                          width: 28vw;
                        "
                      >
                        Task
                      </th>
                      <th
                        style="
                          border: 0.923px solid #cfcfcf;
                          background: rgba(8, 21, 60, 0.88);
                          padding: 10px;
                          padding-left: 10px;
                          padding-right: 10px;
                          color: #fff;
                          font-size: 11.082px;
                          font-style: normal;
                          font-weight: 500;
                          line-height: 150.523%;
                          letter-spacing: 0.665px;
                          width: 10vw;
                        "
                      >
                        Event Title
                      </th>
                      <th
                        style="
                          border: 0.923px solid #cfcfcf;
                          background: rgba(8, 21, 60, 0.88);
                          padding: 10px;
                          padding-left: 10px;
                          padding-right: 10px;
                          color: #fff;
                          font-size: 11.082px;
                          font-style: normal;
                          font-weight: 500;
                          line-height: 150.523%;
                          letter-spacing: 0.665px;
                          width: 10vw;
                        "
                      >
                        Event Location
                      </th>
                      <th
                        style="
                          border: 0.923px solid #cfcfcf;
                          background: rgba(8, 21, 60, 0.88);
                          padding: 10px;
                          color: #fff;
                          font-size: 11.082px;
                          font-style: normal;
                          font-weight: 500;
                          line-height: 150.523%;
                          letter-spacing: 0.665px;
                          width: 15vw;
                        "
                      >
                        Event Duration
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    <% for(let i=0; i < taskEvents.length; i++ ) { %> <% let incrementedValue=i + 1;
                    %>
                    <tr>
                      <td
                        style="
                          text-align: center;
                          padding: 10px;
                          color: #5f5f5f;
                          font-size: 11.533px;
                          font-style: normal;
                          font-weight: 600;
                          line-height: 150.523%;
                          letter-spacing: 0.692px;
                          border: 0.923px solid #cfcfcf;
                        "
                      >
                        <%= incrementedValue %>
                      </td>
                      <td
                        style="
                          text-align: center;
                          padding: 10px;
                          color: #5f5f5f;
                          font-size: 11.533px;
                          font-style: normal;
                          font-weight: 600;
                          line-height: 150.523%;
                          letter-spacing: 0.692px;
                          border: 0.923px solid #cfcfcf;
                        "
                      >
                        <%= taskEvents[i].client.displayName %>
                      </td>
                      <td
                        style="
                          text-align: center;
                          padding: 10px;
                          color: #5f5f5f;
                          font-size: 11.533px;
                          font-style: normal;
                          font-weight: 600;
                          line-height: 150.523%;
                          letter-spacing: 0.692px;
                          border: 0.923px solid #cfcfcf;
                        "
                      >
                        <%= taskEvents[i].task.name %>
                      </td>
                      <td
                        style="
                          text-align: center;
                          padding: 10px;
                          color: #5f5f5f;
                          font-size: 11.533px;
                          font-style: normal;
                          font-weight: 600;
                          line-height: 150.523%;
                          letter-spacing: 0.692px;
                          border: 0.923px solid #cfcfcf;
                        "
                      >
                        <%= taskEvents[i].title %>
                      </td>
                      <td
                        style="
                          text-align: center;
                          padding: 10px;
                          color: #5f5f5f;
                          font-size: 11.533px;
                          font-style: normal;
                          font-weight: 600;
                          line-height: 150.523%;
                          letter-spacing: 0.692px;
                          border: 0.923px solid #cfcfcf;
                        "
                      >
                        <%= taskEvents[i].location %>
                      </td>
                      <td
                        style="
                          text-align: center;
                          padding: 10px;
                          color: #5f5f5f;
                          font-size: 11.533px;
                          font-style: normal;
                          font-weight: 600;
                          line-height: 150.523%;
                          letter-spacing: 0.692px;
                          border: 0.923px solid #cfcfcf;
                        "
                      >
                        <%= taskEvents[i].duration %>
                      </td>
                    </tr>
                    <% } %>
                  </tbody>
                </table>
              </td>
            </tr>
          </table>
          <% } %> <% if(getDSCExpiry) { %>
          <table>
            <tr>
              <td>
                <p
                  style="
                    color: #08153c;
                    font-size: 13.84px;
                    font-style: normal;
                    font-weight: 800;
                    line-height: 150.523%;
                    letter-spacing: 0.83px;
                  "
                >
                  DSC Expiry:
                </p>
              </td>
            </tr>
            <tr>
              <td>
                <table style="border-spacing: 0px; border: 0.923px solid #cfcfcf">
                  <thead>
                    <tr>
                      <th
                        style="
                          border: 0.923px solid #cfcfcf;
                          background: rgba(8, 21, 60, 0.88);
                          padding: 10px;
                          color: #fff;
                          font-size: 11.082px;
                          font-style: normal;
                          font-weight: 500;
                          line-height: 150.523%;
                          letter-spacing: 0.665px;
                          width: 10vw;
                        "
                      >
                        S.No
                      </th>
                      <th
                        style="
                          border: 0.923px solid #cfcfcf;
                          background: rgba(8, 21, 60, 0.88);
                          padding: 10px;
                          color: #fff;
                          font-size: 11.082px;
                          font-style: normal;
                          font-weight: 500;
                          line-height: 150.523%;
                          letter-spacing: 0.665px;
                          width: 35vw;
                        "
                      >
                        DSC Holder
                      </th>
                      <th
                        style="
                          border: 0.923px solid #cfcfcf;
                          background: rgba(8, 21, 60, 0.88);
                          padding: 10px;
                          color: #fff;
                          font-size: 11.082px;
                          font-style: normal;
                          font-weight: 500;
                          line-height: 150.523%;
                          letter-spacing: 0.665px;
                          width: 35vw;
                        "
                      >
                        Mobile Number
                      </th>
                      <th
                        style="
                          border: 0.923px solid #cfcfcf;
                          background: rgba(8, 21, 60, 0.88);
                          padding: 10px;
                          color: #fff;
                          font-size: 11.082px;
                          font-style: normal;
                          font-weight: 500;
                          line-height: 150.523%;
                          letter-spacing: 0.665px;
                          width: 35vw;
                        "
                      >
                        Email
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    <% for(let i=0; i < getDSCExpiry.length; i++) { %>
                         <% let incrementedValue=i + 1; %>
                    <tr>
                      <td
                        style="
                          text-align: center;
                          padding: 10px;
                          color: #5f5f5f;
                          font-size: 11.533px;
                          font-style: normal;
                          font-weight: 600;
                          line-height: 150.523%;
                          letter-spacing: 0.692px;
                          border: 0.923px solid #cfcfcf;
                        "
                      >
                        <%= incrementedValue %>
                      </td>
                      <td
                        style="
                          text-align: center;
                          padding: 10px;
                          color: #5f5f5f;
                          font-size: 11.533px;
                          font-style: normal;
                          font-weight: 600;
                          line-height: 150.523%;
                          letter-spacing: 0.692px;
                          border: 0.923px solid #cfcfcf;
                        "
                      >
                        <%= getDSCExpiry[i].holderName %>
                      </td>
                      <td
                        style="
                          text-align: center;
                          padding: 10px;
                          color: #5f5f5f;
                          font-size: 11.533px;
                          font-style: normal;
                          font-weight: 600;
                          line-height: 150.523%;
                          letter-spacing: 0.692px;
                          border: 0.923px solid #cfcfcf;
                        "
                      >
                        <%= getDSCExpiry[i].mobileNumber %>
                      </td>
                      <td
                        style="
                          text-align: center;
                          padding: 10px;
                          color: #5f5f5f;
                          font-size: 11.533px;
                          font-style: normal;
                          font-weight: 600;
                          line-height: 150.523%;
                          letter-spacing: 0.692px;
                          border: 0.923px solid #cfcfcf;
                        "
                      >
                        <%= getDSCExpiry[i].email %>
                      </td>
                    </tr>
                    <% } %>
                  </tbody>
                </table>
              </td>
            </tr>
          </table>
          <% } %>
          <% if(events) { %>
            <table>
              <tr>
                <td>
                  <p
                    style="
                      color: #08153c;
                      font-size: 13.84px;
                      font-style: normal;
                      font-weight: 800;
                      line-height: 150.523%;
                      letter-spacing: 0.83px;
                    "
                  >
                    Statutory Compliance Today:
                  </p>
                </td>
              </tr>
              <tr>
                <td>
                  <table style="border-spacing: 0px; border: 0.923px solid #cfcfcf">
                    <thead>
                      <tr>
                        <th
                          style="
                            border: 0.923px solid #cfcfcf;
                            background: rgba(8, 21, 60, 0.88);
                            padding: 10px;
                            color: #fff;
                            font-size: 11.082px;
                            font-style: normal;
                            font-weight: 600;
                            line-height: 150.523%;
                            letter-spacing: 0.665px;
                            width: 10vw;
                          "
                        >
                          S.No
                        </th>
                        <th
                          style="
                            text-align: left;
                            border: 0.923px solid #cfcfcf;
                            background: rgba(8, 21, 60, 0.88);
                            padding: 10px;
                            color: #fff;
                            font-size: 11.082px;
                            font-style: normal;
                            font-weight: 600;
                            line-height: 150.523%;
                            letter-spacing: 0.665px;
                            width: 70vw;
                          "
                        >
                          Compliance Description
                        </th>
                      </tr>
                    </thead>
                    <tbody>
                      <% for(let i=0; i < events.length; i++) { %> <% let incrementedValue=i + 1; %>
                      <tr>
                        <td
                          style="
                            text-align: center;
                            padding: 10px;
                            color: #5f5f5f;
                            font-size: 11.533px;
                            font-style: normal;
                            font-weight: 600;
                            line-height: 150.523%;
                            letter-spacing: 0.692px;
                            border: 0.923px solid #cfcfcf;
                          "
                        >
                          <%= incrementedValue %>
                        </td>
                        <td
                          style="
                            text-align: left;
                            text-align: left;
                            padding: 10px;
                            color: #5f5f5f;
                            font-size: 11.533px;
                            font-style: normal;
                            font-weight: 600;
                            line-height: 150.523%;
                            letter-spacing: 0.692px;
                            border: 0.923px solid #cfcfcf;
                          "
                        >
                          <%= events[i].title %>
                        </td>
                      </tr>
                      <% } %>
                    </tbody>
                  </table>
                </td>
              </tr>
            </table>
            <% } %> 
        </td>
      </tr>
      <tr>
        <td>
          <p
            style="
              color: rgba(0, 0, 0, 0.69);
              text-align: center;
              font-size: 16.655px;
              font-style: normal;
              font-weight: 600;
              line-height: 150.523%;
              letter-spacing: 0.999px;
            "
          >
            To Know more about us, check out our website
          </p>
        </td>
      </tr>
      <tr>
        <td style="text-align: center">
          <button
            style="
              border-radius: 5px;
              background: #263154;
              width: 150px;
              border-width: 0px;
              height: 35px;
              color: #fff;
              text-align: center;
              font-size: 12px;
              font-style: normal;
              font-weight: 600;
              line-height: 150.523%;
              letter-spacing: 1.92px;
              text-transform: uppercase;
            "
          >
            WEBSITE
          </button>
        </td>
      </tr>
      <tr>
        <td>
          <div style="text-align: center; padding-top: 10px; padding-bottom: 10px">
            <a href="https://www.instagram.com/vider_india/"
              ><img
                src="https://vider.in/images/e-insta.jpg"
                alt="instagram"
                style="width: 26px; height: 26px; border-radius: 5px; padding: 5px"
            /></a>
            <a href="https://www.linkedin.com/company/viderindia/"
              ><img
                src="https://vider.in/images/e-linkedin.jpg"
                alt="linkedin"
                style="width: 26px; height: 26px; padding: 5px"
            /></a>
            <a href="https://www.facebook.com/vider.india"
              ><img
                src="https://vider.in/images/e-facebook.jpg"
                alt="facebook"
                style="width: 26px; height: 26px; padding: 5px"
            /></a>
            <a href="https://www.youtube.com/@Vider_India"
              ><img
                src="https://vider.in/images/e-youtube.jpg"
                alt="twitter"
                style="width: 26px; height: 26px; padding: 5px"
            /></a>
            <a href="https://twitter.com/Vider_India"
              ><img
                src="https://vider.in/images/twitter.png"
                alt="twitter"
                style="width: 26px; height: 26px; padding: 5px"
            /></a>
          </div>
        </td>
      </tr>
      <tr>
        <td>
          <p
            style="
              color: rgba(0, 0, 0, 0.84);
              text-align: center;
              font-size: 13.292px;
              font-style: normal;
              font-weight: 600;
              line-height: 150.523%;
              letter-spacing: 0.798px;
            "
          >
            Address: GVR’s HIG - 58 A, Phase 5, Kukatpally Housing Board Colony,<br />
            Kukatpally, Hyderabad, Telangana 500072.
          </p>
        </td>
      </tr>
      <tr>
        <td>
          <p
            style="
              color: rgba(0, 0, 0, 0.84);
              text-align: center;
              font-size: 16.199px;
              font-style: normal;
              font-weight: 600;
              line-height: 150.523%;
              letter-spacing: 0.972px;
            "
          >
            Email us at:
            <span
              href="mailto:<EMAIL>"
              style="
                color: rgba(19, 27, 213, 0.84);
                font-size: 16.199px;
                text-decoration: none;
                font-style: normal;
                font-weight: 600;
                line-height: 150.523%;
                letter-spacing: 0.972px;
              "
              ><EMAIL></span
            >
          </p>
        </td>
      </tr>
    </table>
  </body>
</html>