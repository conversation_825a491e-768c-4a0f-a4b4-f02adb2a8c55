import { Injectable } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import CronActivity from 'src/modules/cron-activity/cron-activity.entity';
import * as moment from 'moment';
import { Organization } from 'src/modules/organization/entities/organization.entity';
import { User, UserStatus, UserType } from 'src/modules/users/entities/user.entity';
import { Between, Brackets, createQueryBuilder } from 'typeorm';
import GstrAdditionalNoticeOrders from '../entity/gstrAdditionalOrdersAndNotices.entity';
import GstrNoticeOrders from '../entity/noticeOrders.entity';
import { sendnewMail } from 'src/emails/newemails';
import email from 'src/emails/email';
import * as ejs from 'ejs';
import * as puppeteer from 'puppeteer';
import * as AWS from 'aws-sdk';
import { sendDocumentTextMessage, sendWhatsAppTemplateMessageNotices } from 'src/modules/whatsapp/whatsapp.service';
import ViderWhatsappSessions from 'src/modules/whatsapp/entity/viderWhatsappSessions';
import { Permissions } from 'src/modules/events/permission';
import Client from 'src/modules/clients/entity/client.entity';
import { getUserDetails } from 'src/utils/re-use';

const s3 = new AWS.S3();
const BUCKET_NAME = process.env.AWS_BUCKET_NAME;
@Injectable()
export class GstrCronJobService {
  //  @Cron(CronExpression.EVERY_DAY_AT_3AM)
  async getAdditionalNoticeOrdersRecords() {
    if (process.env.Cron_Running === 'true') {
      console.log("-------- GST NOTICE CRON EXECUTION STARTED ---------")
      const cronData = new CronActivity();
      cronData.cronType = 'GSTR NOTICES';
      cronData.cronDate = moment().toDate().toString();
      cronData.startTime = moment().format('YYYY-MM-DD HH:mm:ss');
      const cornActivityID = await cronData.save();
      const errorList = [];
      try {
          const statusMap = {
          REPLIED: "Replied",
          NOT_REPLIED: "Not Replied",
          NA: "NA"
        };
        const totalOrganization = await Organization.createQueryBuilder('organization')
          .leftJoinAndSelect('organization.users', 'user')
          .where(
            "DATE_FORMAT(STR_TO_DATE(JSON_UNQUOTE(JSON_EXTRACT(config, '$.expirydate')), '%Y-%m-%d'), '%Y-%m-%d') >= :expirydate",
            { expirydate: moment().format('YYYY-MM-DD') },
          )
          .andWhere('user.status = :status', { status: 'active' })
          .andWhere('user.type = :type', { type: UserType.ORGANIZATION })
          .getMany();

        for (let organization of totalOrganization) {
          const users = organization?.users;
          if (users?.length > 0) {
            for (let user of users) {
              if (user.status === UserStatus.DELETED) return;
              const userData = await User.findOne({
                where: { id: user.id },
                relations: ['organization', 'role'],
              });

              const { role } = userData;
              const ViewAll = role.permissions.some(
                (p) => p.slug === Permissions.VIEW_ALL_CLIENT_MANAGERS,
              );
              const ViewAssigned = role.permissions.some(
                (p) => p.slug === Permissions.VIEW_ASSIGNED_CLIENT_MANAGERS,
              );

              let additionalNoticeOrdersArray = [];
              let noticeAndOrdersArray = [];
              if (ViewAll || ViewAssigned) {

                const today = moment().format('YYYY-MM-DD');
                const previousSeventhDay = moment().subtract(6, 'days').format('YYYY-MM-DD');
                const nextSeventhDay = moment().add(6, 'days').format('YYYY-MM-DD');
                try {
                  const additionalNoticeOrdersQuery = await GstrAdditionalNoticeOrders.createQueryBuilder('additionalNotice')
                    .leftJoinAndSelect('additionalNotice.client', 'client')
                    .where('additionalNotice.organizationId = :organizationId', {
                      organizationId: organization.id,
                    })
                    .andWhere('additionalNotice.caseFolderTypeName != :noticeType',{noticeType:'REPLIES'})
                    .andWhere(
                      new Brackets((qb) => {
                        qb.where(
                          'STR_TO_DATE(additionalNotice.categoryDate, "%d/%m/%Y") BETWEEN STR_TO_DATE(:previousSeventhDay,"%Y-%m-%d") AND STR_TO_DATE(:today,"%Y-%m-%d")',
                          { previousSeventhDay, today },
                        ).orWhere(
                          'STR_TO_DATE(additionalNotice.dueDate, "%d/%m/%Y") BETWEEN STR_TO_DATE(:today, "%Y-%m-%d") AND STR_TO_DATE(:nextSeventhDay, "%Y-%m-%d")',
                          { today, nextSeventhDay },
                        );
                      }),
                    )
                    .andWhere('client.status != :status', { status: UserStatus.DELETED });

                  if (!ViewAll) {
                    additionalNoticeOrdersQuery.andWhere(
                      (qb) => {
                        const subQuery = qb
                          .subQuery()
                          .select('1')
                          .from('client_client_managers_user', 'cm')
                          .where('cm.client_id = client.id')
                          .andWhere('cm.user_id = :userId')
                          .getQuery();
                        return `EXISTS (${subQuery})`;
                      },
                      { userId: userData.id },
                    );
                  }

                  const additionalNoticeOrders = await additionalNoticeOrdersQuery.getMany();

                  if (additionalNoticeOrders?.length > 0) {
                    additionalNoticeOrdersArray = additionalNoticeOrders.map((order) => ({
                      orgId: organization?.id,
                      clientName: order?.client?.displayName,
                      issuanceDate: moment(order?.categoryDate, 'DD/MM/YYYY').format('DD-MM-YYYY'),
                      dueDate:
                        order?.dueDate && moment(order.dueDate, 'DD/MM/YYYY', true).isValid()
                          ? moment(order.dueDate, 'DD/MM/YYYY').format('DD-MM-YYYY')
                          : order?.dueDate,
                      financialYear: order?.fy || '-',
                      folder: order?.caseFolderTypeName,
                      referenceNumber: order?.refNum,
                      type: order?.caseTypeName,
                      refStatus: order?.refStatus ? statusMap[order?.refStatus] : "-"
                    }));
                  }

                  const noticeOrdersQuery = await GstrNoticeOrders.createQueryBuilder('noticeOrder')
                    .leftJoinAndSelect('noticeOrder.client', 'client')
                    .where('noticeOrder.organizationId = :organizationId', {
                      organizationId: organization.id,
                    })
                    .andWhere(
                      new Brackets((qb) => {
                        qb.where(
                          'STR_TO_DATE(noticeOrder.dateOfIssuance, "%d/%m/%Y") BETWEEN STR_TO_DATE(:previousSeventhDay,"%Y-%m-%d") AND STR_TO_DATE(:today,"%Y-%m-%d")',
                          { previousSeventhDay, today },
                        ).orWhere(
                          'STR_TO_DATE(noticeOrder.dueDate, "%d/%m/%Y") BETWEEN STR_TO_DATE(:today, "%Y-%m-%d") AND STR_TO_DATE(:nextSeventhDay, "%Y-%m-%d")',
                          { today, nextSeventhDay },
                        );
                      }),
                    )
                    .andWhere('client.status != :status', { status: UserStatus.DELETED });
                  if (!ViewAll) {
                    noticeOrdersQuery.andWhere(
                      (qb) => {
                        const subQuery = qb
                          .subQuery()
                          .select('1')
                          .from('client_client_managers_user', 'cm')
                          .where('cm.client_id = client.id')
                          .andWhere('cm.user_id = :userId')
                          .getQuery();
                        return `EXISTS (${subQuery})`;
                      },
                      { userId: userData.id },
                    );
                  }
                  const noticeOrders = await noticeOrdersQuery.getMany();

                  if (noticeOrders?.length > 0) {
                    noticeAndOrdersArray = noticeOrders.map((order) => ({
                      orgId: organization?.id,
                      clientName: order?.client?.displayName,
                      orderNumber: order?.orderNumber,
                      issuanceDate: moment(order?.dateOfIssuance, 'DD/MM/YYYY').format('DD-MM-YYYY'),
                      dueDate:
                        order?.dueDate && moment(order.dueDate, 'DD/MM/YYYY', true).isValid()
                          ? moment(order.dueDate, 'DD/MM/YYYY').format('DD-MM-YYYY')
                          : order?.dueDate,
                      type: order?.type,
                      amount: order?.amountOfDemand,
                    }));
                  }
                  const orgId = organization?.id;

                  const organizations = await Organization.findOne({ id: orgId });


                  const addressParts = [
                    organizations.buildingNo || '',
                    organizations.floorNumber || '',
                    organizations.buildingName || '',
                    organizations.street || '',
                    organizations.location || '',
                    organizations.city || '',
                    organizations.district || '',
                    organizations.state || ''
                  ].filter(part => part && part.trim() !== '');
                  const pincode = organizations.pincode && organizations.pincode.trim() !== '' ? ` - ${organizations.pincode}` : '';

                  const address = addressParts.join(', ') + pincode;

                  if (additionalNoticeOrdersArray?.length > 0 || noticeAndOrdersArray?.length > 0) {
                    const users = organization?.users;
                    const mailOptions = {
                      data: {
                        additionalNoticeOrdersArray,
                        noticeAndOrdersArray,
                        userName: user?.fullName,
                        userId: user?.id,
                        websiteUrl: process.env.WEBSITE_URL,
                        phoneNumber: organization?.mobileNumber,
                        mail: organization?.email,
                        legalName: organization?.tradeName || organization?.legalName,
                        adress: address,
                      },
                      email: user?.email,
                      filePath: 'gstr-notice',
                      subject: 'GST Notices',
                      key: 'GSTR_NOTICE_MAIL',
                      id: user?.id,
                    };
                    await sendnewMail(mailOptions);
                  }
                } catch (error) {
                  console.log(`Error in getting Gstr Notice records in cron:`, error);
                }
              }
            }
          }
        }
      } catch (error) {
        errorList.push(error.message);
        const getcornActivityID = await createQueryBuilder(CronActivity, 'cronActivity')
          .where('id = :id', { id: cornActivityID.id })
          .getOne();
        getcornActivityID.responseData = errorList.length ? errorList.join(',') : 'Success';
        getcornActivityID.endTime = moment().format('YYYY-MM-DD HH:mm:ss');
        await getcornActivityID.save();
        return console.log(error.message);
      }
      const getcornActivityID = await createQueryBuilder(CronActivity, 'cronActivity')
        .where('id = :id', { id: cornActivityID.id })
        .getOne();
      getcornActivityID.responseData = errorList.length ? errorList.join(',') : 'Success';
      getcornActivityID.endTime = moment().format('YYYY-MM-DD HH:mm:ss');
      await getcornActivityID.save();
      console.log('GST NOTICE CRON EXECUTION COMPLETED!!!!');
      return 'GST NOTICE CRON EXECUTION COMPLETED!!!!';

    }
  }

  // @Cron(CronExpression.EVERY_5_MINUTES)

  @Cron(CronExpression.EVERY_DAY_AT_2AM)

  async getAdditionalNoticeOrdersRecordsWhatsapp() {
    if (process.env.Cron_Running === 'true') {
      const cronData = new CronActivity();
      cronData.cronType = 'GSTR NOTICES WHATSAPP';
      cronData.cronDate = moment().toDate().toString();
      cronData.startTime = moment().format('YYYY-MM-DD HH:mm:ss');
      const cornActivityID = await cronData.save();
      const errorList = [];
      try {
        const totalOrganization = await Organization.createQueryBuilder('organization')
          .leftJoinAndSelect('organization.users', 'user')
          .where(
            "DATE_FORMAT(STR_TO_DATE(JSON_UNQUOTE(JSON_EXTRACT(config, '$.expirydate')), '%Y-%m-%d'), '%Y-%m-%d') >= :expirydate",
            { expirydate: moment().format('YYYY-MM-DD') },
          )
          .andWhere('user.status = :status', { status: 'active' })
          .andWhere('user.type = :type', { type: UserType.ORGANIZATION, id: 827 })
          .getMany();

        for (let organization of totalOrganization) {
          let additionalNoticeOrdersArray = [];
          let noticeAndOrdersArray = [];

          const today = moment().format('YYYY-MM-DD');
          const previousSeventhDay = moment().subtract(6, 'days').format('YYYY-MM-DD');
          const nextSeventhDay = moment().add(6, 'days').format('YYYY-MM-DD');
          try {
            const additionalNoticeOrders = await GstrAdditionalNoticeOrders.createQueryBuilder('additionalNotice')
              .where('additionalNotice.organizationId = :organizationId', {
                organizationId: organization.id,
              })
              .andWhere(
                new Brackets((qb) => {
                  qb.where(
                    'STR_TO_DATE(additionalNotice.categoryDate, "%d/%m/%Y") BETWEEN STR_TO_DATE(:previousSeventhDay,"%Y-%m-%d") AND STR_TO_DATE(:today,"%Y-%m-%d")',
                    { previousSeventhDay, today },
                  ).orWhere(
                    'STR_TO_DATE(additionalNotice.dueDate, "%d/%m/%Y") BETWEEN STR_TO_DATE(:today, "%Y-%m-%d") AND STR_TO_DATE(:nextSeventhDay, "%Y-%m-%d")',
                    { today, nextSeventhDay },
                  );
                }),
              )
              .leftJoinAndSelect('additionalNotice.client', 'client')
              .getMany();

            if (additionalNoticeOrders?.length > 0) {
              additionalNoticeOrdersArray = additionalNoticeOrders.map((order) => ({
                orgId: organization?.id,
                clientName: order?.client?.displayName,
                issuanceDate: moment(order?.categoryDate, 'DD/MM/YYYY').format('DD-MM-YYYY'),
                dueDate:
                  order?.dueDate && moment(order.dueDate, 'DD/MM/YYYY', true).isValid()
                    ? moment(order.dueDate, 'DD/MM/YYYY').format('DD-MM-YYYY')
                    : order?.dueDate,
                financialYear: order?.fy || '-',
                folder: order?.caseFolderTypeName,
                referenceNumber: order?.refNum,
                type: order?.caseTypeName,
              }));
            }

            const noticeOrders = await GstrNoticeOrders.createQueryBuilder('noticeOrder')
              .where('noticeOrder.organizationId = :organizationId', {
                organizationId: organization.id,
              })
              .andWhere(
                new Brackets((qb) => {
                  qb.where(
                    'STR_TO_DATE(noticeOrder.dateOfIssuance, "%d/%m/%Y") BETWEEN STR_TO_DATE(:previousSeventhDay,"%Y-%m-%d") AND STR_TO_DATE(:today,"%Y-%m-%d")',
                    { previousSeventhDay, today },
                  ).orWhere(
                    'STR_TO_DATE(noticeOrder.dueDate, "%d/%m/%Y") BETWEEN STR_TO_DATE(:today, "%Y-%m-%d") AND STR_TO_DATE(:nextSeventhDay, "%Y-%m-%d")',
                    { today, nextSeventhDay },
                  );
                }),
              )
              .leftJoinAndSelect('noticeOrder.client', 'client')
              .getMany();

            if (noticeOrders?.length > 0) {
              noticeAndOrdersArray = noticeOrders.map((order) => ({
                orgId: organization?.id,
                clientName: order?.client?.displayName,
                orderNumber: order?.orderNumber,
                issuanceDate: moment(order?.dateOfIssuance, 'DD/MM/YYYY').format('DD-MM-YYYY'),
                dueDate:
                  order?.dueDate && moment(order.dueDate, 'DD/MM/YYYY', true).isValid()
                    ? moment(order.dueDate, 'DD/MM/YYYY').format('DD-MM-YYYY')
                    : order?.dueDate,
                type: order?.type,
                amount: order?.amountOfDemand,
              }));
            }


            if (additionalNoticeOrdersArray?.length > 0 || noticeAndOrdersArray?.length > 0) {
              const users = organization?.users;

              if (users?.length > 0) {
                for (let user of users) {
                  // Render the EJS template
                  const ejsData = {
                    additionalNoticeOrdersArray,
                    noticeAndOrdersArray,
                    userName: user?.fullName,
                    userId: user?.id,
                    websiteUrl: process.env.WEBSITE_URL,

                  };

                  const htmlContent = await ejs.renderFile('src/emails/templates/gstr-noticewhatsapp.ejs', ejsData);
                  // console.log(htmlContent)

                  // Convert HTML to PDF using Puppeteer
                  const browser = await puppeteer.launch({
                    headless: true, // Run in headless mode
                    // executablePath: '/home/<USER>/.cache/puppeteer/chrome/linux-131.0.6778.108/chrome-linux64/chrome', // Path to Chrome
                    executablePath: '/usr/bin/google-chrome',
                    args: ['--no-sandbox', '--disable-setuid-sandbox'], // Required for servers
                  });
                  const page = await browser.newPage();
                  await page.setContent(htmlContent);
                  const pdfBuffer = await page.pdf({
                    format: 'A4', // Standard page size
                    printBackground: true, // Include background colors and images
                    margin: {
                      top: '1cm',
                      bottom: '1cm',
                      left: '1cm',
                      right: '1cm',
                    },
                  });
                  await browser.close();

                  // Upload PDF to S3
                  const s3Params = {
                    Bucket: process.env.AWS_BUCKET_NAME,
                    Key: `gstr-notice-report-${organization.id}-${moment().format('YYYY-MM-DD HH:mm:ss')}.pdf`,
                    Body: pdfBuffer,
                    ContentType: 'application/pdf',
                  };
                  const uploadResult = await s3.upload(s3Params).promise();
                  const pdfLink = uploadResult.Location;
                  // Send PDF via WhatsApp
                  const title = 'GST notice'
                  const users = organization?.users;

                  //  if (users?.length > 0) {
                  //  for (let user of users) {
                  const sessionValidation = await ViderWhatsappSessions.findOne({
                    where: { userId: user?.id, status: 'ACTIVE' },
                  });
                  if (sessionValidation) {
                    const key = 'GSTR_NOTICE_WHATSAPP';
                    try {
                      const caption = "GSTR Notices Report";
                      const filename = "GSTR_Notice_Report.pdf";

                      await sendDocumentTextMessage(
                        user?.mobileNumber,
                        pdfLink,
                        caption,
                        filename,
                        user?.organization?.id,
                        user?.id,
                        title,
                        key
                      );
                    } catch (error) {
                      console.error('Error sending document message for user:', user.mobileNumber, error);
                    }
                  }
                  //  }
                  //  }


                }
              }
            }
          } catch (error) {
            console.log(`Error in getting Gstr Notice records in cron:`, error);
          }
        }
      } catch (error) {
        errorList.push(error.message);
        const getcornActivityID = await createQueryBuilder(CronActivity, 'cronActivity')
          .where('id = :id', { id: cornActivityID.id })
          .getOne();
        getcornActivityID.responseData = errorList.length ? errorList.join(',') : 'Success';
        getcornActivityID.endTime = moment().format('YYYY-MM-DD HH:mm:ss');
        await getcornActivityID.save();
        return console.log(error.message);
      }
      const getcornActivityID = await createQueryBuilder(CronActivity, 'cronActivity')
        .where('id = :id', { id: cornActivityID.id })
        .getOne();
      getcornActivityID.responseData = errorList.length ? errorList.join(',') : 'Success';
      getcornActivityID.endTime = moment().format('YYYY-MM-DD HH:mm:ss');
      await getcornActivityID.save();
    }
  }
}


