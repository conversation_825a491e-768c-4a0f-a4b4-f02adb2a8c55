import { Injectable, Logger } from '@nestjs/common';
import RecurringProfile from 'src/modules/recurring/entity/recurring-profile.entity';

@Injectable()
export class RecurringService {

  async getRecurringProfiles(clientId: number) {
    const recurringProfile = await RecurringProfile.createQueryBuilder('recurringProfile')
      .leftJoinAndSelect('recurringProfile.tasks', 'task')
      .leftJoinAndSelect('recurringProfile.client', 'client')
      .leftJoinAndSelect('task.members', 'member')
      .leftJoinAndSelect('task.approvalProcedures', 'approvalProcedures')
      .leftJoinAndSelect('member.imageStorage', 'imageStorage')
      .leftJoinAndSelect('recurringProfile.user', 'user')
      .where('client.id = :clientId', { clientId })
      .orderBy({ 'task.recurringStatus': 'ASC', 'task.taskStartDate': 'ASC' })
      .getMany();
    return recurringProfile;
  }
}
