import {
  Connection,
  EntitySubscriberInterface,
  EventSubscriber,
  InsertEvent,
  getManager,
  UpdateEvent,
} from 'typeorm';
import { User } from 'src/modules/users/entities/user.entity';
import {
  getUserDetails,
  insertINTONotificationUpdate,
  insertINTOnotification,
} from 'src/utils/re-use';
import { sendnewMail } from 'src/emails/newemails';
import OrganizationPreferences from 'src/modules/organization-preferences/entity/organization-preferences.entity';
import { ProformaInvoice } from 'src/modules/billing/entitities/proforma-invoice.entity';
import ViderWhatsappSessions from 'src/modules/whatsapp/entity/viderWhatsappSessions';
import { sendWhatsAppTextMessage } from 'src/modules/whatsapp/whatsapp.service';
import { Organization } from 'src/modules/organization/entities/organization.entity';

@EventSubscriber()
export class ProformaInvoiceSubscriber implements EntitySubscriberInterface<ProformaInvoice> {
  constructor(private readonly connection: Connection) {
    connection.subscribers.push(this);
  }

  listenTo() {
    return ProformaInvoice;
  }

  // async afterInsert(event: InsertEvent<ProformaInvoice>) {
  //   const entityManager = getManager();
  //   const { organization, invoiceNumber, client, grandTotal, particulars, emailCheck } = event?.entity;

  //   const invoiceDateFormat = new Date(event?.entity.invoiceDate).toLocaleDateString('en-GB');
  //   const invoiceDueDateFormat = new Date(event?.entity.invoiceDueDate).toLocaleDateString('en-GB');

  //   const organizationId = organization?.id;

  //   const clientName = client?.displayName || '';

  //   const getRoleQuery = `SELECT id FROM role where organization_id = ${organizationId}  and name = "Admin";`;
  //   let getRole = await entityManager.query(getRoleQuery);
  //   const roleId = getRole[0]?.id || '';
  //   const logInUser = await event.entity['userId'];

  //   const getUserQuery = `select id from user where organization_id=${organizationId} and role_id = ${roleId}`;
  //   let getUser = await entityManager.query(getUserQuery);
  //   const userIDs: User[] = getUser.map((row) => row.id);
  //   const userIdTwo = event.entity['userId'];
  //   const key = 'PROFORMA_INVOICE_CREATION_MAIL';
  //   if (emailCheck && key === 'PROFORMA_INVOICE_CREATION_MAIL') {
  //     const address = `${organization.buildingNo || " " ? organization.buildingNo || " " + ', ' : ''}${organization.floorNumber || " " ? organization.floorNumber || " " + ', ' : ''}${organization.buildingName || " " ? organization.buildingName + ', ' : ''}${organization.street ? organization.street + ', ' : ''}${organization.location ? organization.location + ', ' : ''}${organization.city ? organization.city + ', ' : ''}${organization.district ? organization.district + ', ' : ''}${organization.state ? organization.state + ', ' : ''}${organization.pincode ? organization.pincode : ''}`;
  //     const [{ amount, name }] = particulars;
  //     const invoiceNum = invoiceNumber;

  //     const mailOptions = {
  //       id: logInUser,
  //       key: 'PROFORMA_INVOICE_CREATION_MAIL',
  //       email: client?.email,
  //       clientMail: 'ORGANIZATION_CLIENT_EMAIL',
  //       data: {
  //         serviceName: name,
  //         legalName: organization?.tradeName || organization?.legalName,
  //         invoiceNumber: invoiceNum,
  //         invoiceDate: invoiceDateFormat,
  //         invoiceDueDateFormat: invoiceDueDateFormat,
  //         invoiceTotalAmount: amount,
  //         clientName: clientName,
  //         address: address,
  //         phoneNumber: organization?.mobileNumber,
  //         mail: organization?.email,
  //         userId: logInUser,
  //       },

  //       filePath: 'client-invoice-created',
  //       subject: 'Proforma Invoice for Services Rendered',
  //     };

  //     const orgPreferences: any = await OrganizationPreferences.findOne({ where: { organization: organizationId } })
  //     const clientPreferences = orgPreferences?.clientPreferences?.email;

  //     if (clientPreferences && clientPreferences[key]) {
  //       await sendnewMail(mailOptions);
  //     }
  //   }
  //   if (userIdTwo) {
  //     const getUsertwoQuery = `SELECT full_name FROM user where id = ${userIdTwo};`;
  //     let getusertwo = await entityManager.query(getUsertwoQuery);
  //     const userName = getusertwo[0]?.full_name || '';
  //     async function insertINTOnotifications(event: any, users: Array<User>) {
  //       let notifications = [];
  //       let title = 'Proforma Invoice Created';
  //       let body = `A New Proforma Invoice "<strong>${invoiceNumber}</strong>" for <strong>${clientName}</strong> has been added.`;
  //       // insertINTOnotification(title, body, users, organizationId);
  //       const key = 'PROFORMA_INVOICE_CREATED_PUSH';
  //       insertINTONotificationUpdate(title, body, users, organizationId, key);
  //       for (let a of users) {
  //         let getEmailQuery = `SELECT id, email,full_name FROM user where id = ${a};`;
  //         let getEmail = await entityManager.query(getEmailQuery);
  //         let mail = getEmail[0]?.email || '';
  //         let fullname = getEmail[0]?.full_name || '';
  //         let id = getEmail[0]?.id || '';
  //         let data = {
  //           clientName: clientName,
  //           invoiceNumber: invoiceNumber,
  //           invoiceDate: invoiceDateFormat,
  //           invoiceDueDate: invoiceDueDateFormat,
  //           username: fullname,
  //           invoiceAmount: grandTotal,
  //           userName: userName,
  //           userId: logInUser,
  //         };
  //         let IData = {
  //           id,
  //           key: 'PROFORMA_INVOICE_CREATED_MAIL',
  //           data: data,
  //           subject: `Proforma Invoice has been raised`,
  //           email: mail,
  //           filePath: 'invoice-created',
  //         };
  //         await sendnewMail(IData);
  //       }
  //       // await Notification.save(notifications);
  //     }
  //     insertINTOnotifications(this.afterInsert, userIDs);
  //   }
  // }
  async afterInsert(event: InsertEvent<ProformaInvoice>) {
    const entityManager = getManager();
    const { organization,
      invoiceNumber,
      client,
      clientGroup,
      grandTotal,
      particulars,
      emailCheck,
      invoiceDate,
      invoiceDueDate,
    } = event?.entity || {};

    const {
      id: organizationId,
      tradeName,
      legalName,
      mobileNumber,
      email,
      buildingNo,
      floorNumber,
      buildingName,
      street,
      location,
      city,
      district,
      state,
      pincode,
    } = organization || {};
    const logInUser = event.entity['userId'];
    const invoiceDateFormat = invoiceDate ? new Date(invoiceDate).toLocaleDateString('en-GB') : '';
    const invoiceDueDateFormat = invoiceDueDate
      ? new Date(invoiceDueDate).toLocaleDateString('en-GB')
      : '';
    const clientName = client?.displayName || '';
    const clientGroupName = clientGroup?.displayName;

    const getRoleQuery = 'SELECT id FROM role WHERE organization_id = ? AND name = ?';
    const getRole = await entityManager.query(getRoleQuery, [organizationId, 'Admin']);
    const roleId = getRole[0]?.id || '';

    const getUserQuery = 'SELECT id FROM user WHERE organization_id = ? AND role_id = ?';
    const getUser = await entityManager.query(getUserQuery, [organizationId, roleId]);
    const userIDs: User[] = getUser.map((row: any) => row.id);

    if (emailCheck && particulars?.length && particulars[0].amount && particulars[0].name) {
      const { amount, name: serviceName } = particulars[0];

      const addressParts = [
        buildingNo,
        floorNumber,
        buildingName,
        street,
        location,
        city,
        district,
        state,
      ].filter((part) => part && part.trim() !== '');

      const address = addressParts.join(', ') + (pincode?.trim() ? ` - ${pincode}` : '');

      const mailOptions = {
        id: logInUser,
        key: 'PROFORMA_INVOICE_CREATION_MAIL',
        email: client?.email,
        clientMail: 'ORGANIZATION_CLIENT_EMAIL',
        data: {
          serviceName,
          legalName: tradeName || legalName,
          invoiceNumber,
          invoiceDate: invoiceDateFormat,
          invoiceDueDate: invoiceDueDateFormat,
          invoiceTotalAmount: amount,
          clientName: clientName || clientGroupName,
          address: address,
          phoneNumber: mobileNumber,
          mail: email,
          userId: logInUser,
        },
        filePath: 'client-invoice-created',
        subject: 'Proforma Invoice for Services Rendered',
      };

      const orgPreferences: any = await OrganizationPreferences.findOne({
        where: { organization: organizationId },
      });
      const clientPreferences = orgPreferences?.clientPreferences?.email;

      if (clientPreferences?.['PROFORMA_INVOICE_CREATION_MAIL']) {
        // await sendnewMail(mailOptions);
      }
    }

    // Fetch user details and send notifications
    if (logInUser) {
      const getUsertwoQuery = 'SELECT full_name FROM user WHERE id = ?';
      const [getusertwo] = await entityManager.query(getUsertwoQuery, [logInUser]);
      const userName = getusertwo?.full_name || '';

      async function insertNotifications(users: User[]) {
        const title = 'Proforma Invoice Created';
        const body = `A New Proforma Invoice "<strong>${invoiceNumber}</strong>" for <strong>${
          clientName || clientGroupName
        }</strong> has been added.`;
        const key = 'PROFORMA_INVOICE_CREATED_PUSH';

        insertINTONotificationUpdate(title, body, users, organizationId, key);
        const organization = await Organization.findOne({ id: organizationId });

        const addressParts = [
          organization.buildingNo || '',
          organization.floorNumber || '',
          organization.buildingName || '',
          organization.street || '',
          organization.location || '',
          organization.city || '',
          organization.district || '',
          organization.state || '',
        ].filter((part) => part && part.trim() !== '');
        const pincode =
          organization.pincode && organization.pincode.trim() !== ''
            ? ` - ${organization.pincode}`
            : '';

        const address = addressParts.join(', ') + pincode;
        try {
          if (users !== undefined) {
            const sessionValidation = await ViderWhatsappSessions.findOne({
              where: { userId: users, status: 'ACTIVE' },
            });
            if (sessionValidation) {
              const adminUserDetails = await getUserDetails(users);

              const {
                full_name: userFullName,
                mobile_number: userPhoneNumber,
                id,
                organization_id,
              } = adminUserDetails;
              const key = 'PROFORMA_INVOICES_CREATED_WHATSAPP';
              const whatsappMessageBody = `
 Hi ${userFullName}

 A New Proforma Invoice ${invoiceNumber} for ${clientName || clientGroupName} has been added.
 We hope this helps!`;
              await sendWhatsAppTextMessage(
                `91${userPhoneNumber}`,
                whatsappMessageBody,
                organization_id,
                title,
                id,
                key,
              );
            }
          }
        } catch (error) {
          console.error('Error sending User WhatsApp notification:', error);
        }

        await Promise.all(
          users.map(async (user) => {
            const getEmailQuery = 'SELECT id, email, full_name FROM user WHERE id = ?';
            const [getEmail] = await entityManager.query(getEmailQuery, [user]);
            const mail = getEmail?.email || '';
            const fullname = getEmail?.full_name || '';
            const userId = getEmail?.id || '';
            const emailData = {
              clientName: clientName || clientGroupName,
              invoiceNumber,
              invoiceDate: invoiceDateFormat,
              invoiceDueDate: invoiceDueDateFormat,
              username: fullname,
              invoiceAmount: grandTotal,
              userName,
              userId: logInUser,
              adress: address,
              phoneNumber: organization?.mobileNumber,
              mail: organization?.email,
              legalName: organization?.tradeName || organization?.legalName,
            };

            const mailOptions = {
              id: userId,
              key: 'PROFORMA_INVOICE_CREATED_MAIL',
              data: emailData,
              subject: 'Proforma Invoice has been raised',
              email: mail,
              filePath: 'invoice-created',
            };

            await sendnewMail(mailOptions);
          }),
        );
      }

      insertNotifications(userIDs);
    }
  }

  async afterUpdate(event: UpdateEvent<ProformaInvoice>) {
    const entityManager = getManager();
    const { id, invoiceNumber, status, grandTotal } = event?.entity;
    const invoiceDateFormat = new Date(event?.entity.invoiceDate).toLocaleDateString('en-GB');
    const invoiceDueDateFormat = new Date(event?.entity.invoiceDueDate).toLocaleDateString('en-GB');
    if (status === 'APPROVAL_PENDING' || status === 'PARTIALLY_PAID') {
      const getInvoiceQuery = `SELECT organization_id,client_id FROM proforma_invoice where id = ${id};`;
      let getInvoice = await entityManager.query(getInvoiceQuery);
      const orgid = getInvoice[0]?.organization_id;
      const clientId = getInvoice[0]?.client_id;
      const getClientQuery = `SELECT display_name FROM client where id = ${clientId};`;
      let getClient = await entityManager.query(getClientQuery);
      const clientName = getClient[0]?.display_name;
      const clientGroupId = getInvoice[0]?.client_group_id;
      const getClientGroupQuery = `SELECT display_name FROM client_group where id = ${clientGroupId};`;
      let getClientGroup = await entityManager.query(getClientGroupQuery);
      const clientGroupName = getClientGroup[0]?.display_name;
      const getRoleQuery = `SELECT id FROM role where organization_id = ${orgid} and name = "Admin";`;
      let getRole = await entityManager.query(getRoleQuery);
      const role_id = getRole[0]?.id;
      const getuserQuery = `select id from user where organization_id=${orgid} and role_id = ${role_id}`;
      let getuser = await entityManager.query(getuserQuery);
      const userIDs: User[] = getuser.map((row) => row.id);
      const useridtwo = event.entity['userId'];
      if (useridtwo) {
        const getuserTwoQuery = `SELECT full_name FROM user where id = ${useridtwo};`;
        let getuserTwo = await entityManager.query(getuserTwoQuery);
        const userName = getuserTwo[0]?.full_name;

        async function insertINTOnotifications(event: any, users: Array<User>) {
          let notifications = [];
          let title = 'Proforma Invoice Edited';
          let body = `A Proforma Invoice "<strong>${invoiceNumber}</strong>" for <strong>${
            clientName || clientGroupName
          }</strong> has been Edited.`;
          insertINTOnotification(title, body, users, orgid);
          const organization = await Organization.findOne({ id: orgid });

            const addressParts = [
              organization.buildingNo || '',
              organization.floorNumber || '',
              organization.buildingName || '',
              organization.street || '',
              organization.location || '',
              organization.city || '',
              organization.district || '',
              organization.state || '',
            ].filter((part) => part && part.trim() !== '');
            const pincode =
              organization.pincode && organization.pincode.trim() !== ''
                ? ` - ${organization.pincode}`
                : '';
            const address = addressParts.join(', ') + pincode;
          for (let a of users) {
            let getEmailQuery = `SELECT email,full_name FROM user where id = ${a};`;
            let getEmail = await entityManager.query(getEmailQuery);
            let mail = getEmail[0]?.email;
            let fullname = getEmail[0]?.full_name;
            let data = {
              clientName: clientName || clientGroupName,
              invoiceNumber: invoiceNumber,
              invoiceDate: invoiceDateFormat,
              invoiceDueDate: invoiceDueDateFormat,
              username: fullname,
              invoiceAmount: grandTotal,
              userName: userName,
              adress: address,
              phoneNumber: organization?.mobileNumber,
              mail: organization?.email,
              legalName: organization?.tradeName || organization?.legalName,
            };
            let IData = {
              data: data,
              subject: `Proforma Invoice has been edited`,
              email: mail,
              filePath: 'invoice-updated',
            };
            // await sendMail(IData);
          }
          // await Notification.save(notifications);
        }
        insertINTOnotifications(this.afterUpdate, userIDs);
      }
    }
    if (status === 'CANCELLED') {
      const getInvoiceQuery = `SELECT organization_id,client_id, client_group_id FROM proforma_invoice where id = ${id};`;
      let getInvoice = await entityManager.query(getInvoiceQuery);
      const orgid = getInvoice[0]?.organization_id;
      const clientId = getInvoice[0]?.client_id;
      const clientGroupId = getInvoice[0]?.client_group_id;
      const getClientQuery = `SELECT display_name FROM client where id = ${clientId};`;
      let getClient = await entityManager.query(getClientQuery);
      const clientName = getClient[0]?.display_name;
      const getClientGroupQuery = `SELECT display_name FROM client_group where id = ${clientGroupId};`;
      let getClientGroup = await entityManager.query(getClientGroupQuery);
      const clientGroupName = getClientGroup[0]?.display_name;
      const getRoleQuery = `SELECT id FROM role where organization_id = ${orgid} and name = "Admin";`;
      let getRole = await entityManager.query(getRoleQuery);
      const role_id = getRole[0]?.id;
      const getUserQuery = `select id from user where organization_id=${orgid} and role_id = ${role_id}`;
      let getUser = await entityManager.query(getUserQuery);
      const userIDs: User[] = getUser.map((row) => row.id);
      const useridtwo = event.entity['userId'];
      if (useridtwo) {
        const getUserTwoQuery = `SELECT full_name FROM user where id = ${useridtwo};`;
        let getUserTwo = await entityManager.query(getUserTwoQuery);
        const userName = getUserTwo[0]?.full_name;
        async function insertINTOnotifications(event: any, users: Array<User>) {
          let notifications = [];
          let title = 'Proforma Invoice Cancelled';
          let body = `A Proforma Invoice "<strong>${invoiceNumber}</strong>" for <strong>${
            clientName || clientGroupName
          }</strong> has been cancelled.`;
          //   insertINTOnotification(title, body, users, orgid);
          const key = 'PROFORMA_INVOICE_CANCELLED_PUSH';
          insertINTONotificationUpdate(title, body, users, orgid, key);
          const organization = await Organization.findOne({ id: orgid });

          const addressParts = [
            organization.buildingNo || '',
            organization.floorNumber || '',
            organization.buildingName || '',
            organization.street || '',
            organization.location || '',
            organization.city || '',
            organization.district || '',
            organization.state || '',
          ].filter((part) => part && part.trim() !== '');
          const pincode =
            organization.pincode && organization.pincode.trim() !== ''
              ? ` - ${organization.pincode}`
              : '';

          const address = addressParts.join(', ') + pincode;
          try {
            if (users !== undefined) {
              const sessionValidation = await ViderWhatsappSessions.findOne({
                where: { userId: users, status: 'ACTIVE' },
              });
              if (sessionValidation) {
                const adminUserDetails = await getUserDetails(users);

                const {
                  full_name: userFullName,
                  mobile_number: userPhoneNumber,
                  id,
                  organization_id,
                } = adminUserDetails;
                const key = 'PROFORMA_INVOICE_CANCELLED_WHATSAPP';
                const whatsappMessageBody = `
    Hi ${userFullName}
   
    A Proforma Invoice ${invoiceNumber} for ${
                  clientName || clientGroupName
                } has been cancelled by ${userName}.
    We hope this helps!`;
                await sendWhatsAppTextMessage(
                  `91${userPhoneNumber}`,
                  whatsappMessageBody,
                  organization_id,
                  title,
                  id,
                  key,
                );
              }
            }
          } catch (error) {
            console.error('Error sending User WhatsApp notification:', error);
          }

          for (let a of users) {
            let getEmailQuery = `SELECT id, email,full_name FROM user where id = ${a};`;
            let getEmail = await entityManager.query(getEmailQuery);
            let mail = getEmail[0]?.email;
            let fullname = getEmail[0]?.full_name;
            let id = getEmail[0]?.id;
            let data = {
              clientName: clientName || clientGroupName,
              invoiceNumber: invoiceNumber,
              invoiceDate: invoiceDateFormat,
              invoiceDueDate: invoiceDueDateFormat,
              username: fullname,
              invoiceAmount: grandTotal,
              userName: userName,
              userId: useridtwo,
              adress: address,
              phoneNumber: organization?.mobileNumber,
              mail: organization?.email,
              legalName: organization?.tradeName || organization?.legalName,
            };
            let IData = {
              id,
              key: 'PROFORMA_INVOICE_CANCELLED_MAIL',
              data: data,
              subject: `Proforma Invoice has been cancelled`,
              email: mail,
              filePath: 'invoice-cancelled',
            };
            await sendnewMail(IData);
          }
          // await Notification.save(notifications);
        }
        insertINTOnotifications(this.afterUpdate, userIDs);
      }
    }
  }
}
