import { IsNumberString, IsOptional, IsString } from 'class-validator';

class FindServicesDto {
  @IsOptional()
  @IsString()
  offset: number;

  @IsOptional()
  sort: string;
  
  @IsOptional()
  @IsNumberString()
  limit: number;

  @IsOptional()
  @IsString()
  search: string;

  @IsOptional()
  @IsNumberString()
  category: string;

  @IsOptional()
  @IsNumberString()
  subCategory: string;

  @IsOptional()
  @IsString()
  selectedType:string;
}

export default FindServicesDto;
