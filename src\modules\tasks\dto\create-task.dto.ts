import { Type } from 'class-transformer';
import {
  ArrayMinSize,
  IsArray,
  IsBoolean,
  IsDateString,
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  Matches,
  ValidateIf,
  ValidateNested,
} from 'class-validator';
import {
  FeeType,
  PriorityEnum,
  RecurringFrequency,
  ServiceType,
  TaskStatusEnum,
  TaskType,
} from './types';
import BudgetedHours from 'src/modules/budgeted-hours/budgeted-hours.entity';
import ClientGroup from 'src/modules/client-group/client-group.entity';

class DateType {
  @IsNotEmpty()
  startDate: string;

  @IsNotEmpty()
  dueDate: string;

  @IsOptional()
  expectedCompletionDate: string;

  @IsNotEmpty()
  period: string;
}

class CreateTaskDto {
  @IsNotEmpty()
  @IsEnum(ServiceType)
  serviceType: string;

  @ValidateIf((o: CreateTaskDto) => o.serviceType === ServiceType.STANDARD)
  @IsNotEmpty()
  service: number;

  @IsOptional()
  @IsNumber()
  approvalHierarchy: number;

  @IsNotEmpty()
  @IsEnum(TaskType)
  taskType: string;

  @ValidateIf((o: CreateTaskDto) => o.serviceType === ServiceType.CUSTOM)
  @IsNotEmpty()
  @IsString()
  @Matches(/^[^<>:"\/\\|?*\.]*$/, {
    message: 'Task Name cannot contain <, >, :, ", /, \\, |, ?, *, or .',
  })
  name: string;

  @IsOptional()
  @IsNumber()
  parentTask: number;

  @IsOptional()
  @IsNumber()
  order: number;

  @IsOptional()
  @IsEnum(TaskStatusEnum)
  status: TaskStatusEnum;

  @IsOptional()
  @IsEnum(TaskStatusEnum)
  restore: TaskStatusEnum;

  @IsOptional()
  @IsDateString()
  startDate: string;

  @IsOptional()
  @IsDateString()
  dueDate: string;

  @ValidateIf((o: CreateTaskDto) => o.serviceType === ServiceType.CUSTOM)
  @IsNotEmpty()
  @IsNumber()
  category: number;

  @ValidateIf((o: CreateTaskDto) => o.serviceType === ServiceType.CUSTOM)
  @IsOptional()
  @IsNumber()
  subCategory: number;

  @IsOptional()
  @IsArray()
  client: number[];

  @IsNotEmpty()
  @IsArray()
  members: number[];

  @IsOptional()
  @IsArray()
  taskLeader: number[];

  @ValidateIf((o: CreateTaskDto) => o.taskType === TaskType.RECURRING)
  @IsNotEmpty()
  @IsEnum(RecurringFrequency)
  frequency: RecurringFrequency;

  @IsNotEmpty()
  @IsEnum(FeeType)
  feeType: FeeType;

  @IsOptional()
  @IsNumber()
  feeAmount: number;

  @IsOptional()
  @IsString()
  budgetedhours: number;

  @IsOptional()
  @IsBoolean()
  billable: boolean;

  @IsOptional()
  @IsString()
  bhallocation: string;

  @IsOptional()
  @IsString()
  description: string;

  @IsOptional()
  @IsEnum(PriorityEnum)
  priority: PriorityEnum;

  @IsOptional()
  @IsArray()
  labels: number[];

  @IsNotEmpty()
  @IsString()
  financialYear: string;

  @IsOptional()
  @IsDateString()
  expectedCompletionDate: string;

  @IsOptional()
  @IsDateString()
  createdDate: string;

  @ValidateIf((o: CreateTaskDto) => o.taskType === TaskType.RECURRING)
  @IsNotEmpty()
  @IsArray()
  @ArrayMinSize(1)
  @ValidateNested()
  @Type(() => DateType)
  dates: Array<DateType>;

  @IsOptional()
  budgetedHoursInSeconds: number;

  @IsOptional()
  userBudgetedHours: BudgetedHours[];

  @IsOptional()
  clientGroup: ClientGroup[];

  @IsOptional()
  @IsBoolean()
  isUdin: boolean;

  @IsOptional()
  taskCreationCheck: boolean;
}

export default CreateTaskDto;
