import { Organization } from 'src/modules/organization/entities/organization.entity';
import { Connection, EntitySubscriberInterface, EventSubscriber, InsertEvent } from 'typeorm';

@EventSubscriber()
export class OrganizationWalletSubscriber implements EntitySubscriberInterface<Organization> {
  constructor(private readonly connection: Connection) {
    connection.subscribers.push(this);
  }

  listenTo() {
    return Organization;
  }

  async beforeInsert(event: InsertEvent<Organization>) {}

  async afterInsert(event: InsertEvent<Organization>) {}
}
