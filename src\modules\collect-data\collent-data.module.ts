import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import CollectData from './collect-data.entity';
import { CollectDataController } from './collect-data.controller';
import { CollectDataService } from './collect-data.service';
import { AwsService } from '../storage/upload.service';
import { StorageService } from '../storage/storage.service';
import { CollectDataSubscriber } from 'src/event-subscribers/collectData.subscriber';
import { StorageListner } from 'src/event-listeners/storage.listener';
import { OneDriveStorageService } from '../ondrive-storage/onedrive-storage.service';
import { AttachmentsService } from '../tasks/services/attachments.service';
import { BharathStorageService } from '../storage/bharath-storage.service';
import { BharathCloudService } from '../storage/bharath-upload.service';
import { GoogleDriveStorageService } from '../ondrive-storage/googledrive-storage.service';

@Module({
  imports: [TypeOrmModule.forFeature([CollectData])],
  controllers: [CollectDataController],
  providers: [CollectDataService,
    AwsService,
    StorageService,
    CollectDataSubscriber,
    StorageListner,
    OneDriveStorageService,
    AttachmentsService,
    BharathStorageService,
    BharathCloudService,
    GoogleDriveStorageService
  ],
})
export class CollectDataModule { }
