import { CommonService } from './common.service';
import {
  BadRequestException,
  Body,
  Controller,
  Get,
  InternalServerErrorException,
  Param,
  Post,
  Query,
  Req,
  Res,
  UploadedFile,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import State from 'src/modules/states/state.entity';
import { getManager } from 'typeorm';
import {
  allcategoryandsubcategory,
  allusersefficiency,
  servicecategorystatusbytasks,
  detailedoverduetasks,
  userbasedmasterreport,
  upcommingtasks,
  taskhrmsdetails,
  usertasksbyorgid,
  statuswisetasks,
  overduetasks,
  highestnumberoftaskscompletion,
  clientinvoiceunbilled,
  clientinvoicebilled,
  clientinvoicebilling,
  clientproformainvoicebilling,
  clientinvoicereceipts,
  clientslistinvoice,
  clientsoverduereminder,
  clientdashboardactivitylog,
  clientpureagentreceivedanddue,
  clientdashboardamountdue,
  clientdashboardamountreceived,
  clientdashboardinvoiceunbilled,
  clientdashboardinvoicebilled,
  ClientTasksCompletedToBilled,
  ClientTasksCompletedToUnBilled,
  balancedueforinvoicesraisedreport,
  balancedueforinvoicesraisedreminder,
  invoiceoverduereports,
  receiptmanagementreport,
  taskscompletedtobilledtasks,
  taskscompletedtounbilledtasks,
  taskInvoice,
  detailedoverduecompletedtasks,
  proformaInvoiceReport,
  clientsgrouplistinvoice,
  clientGroupinvoiceunbilled,
  clientgroupinvoicebilled,
  clientgroupinvoicebilling,
  clientgroupinvoicereceipts,
  clientgroupproformainvoicebilling,
} from 'src/utils/sqlqueries';
import * as xlsx from 'xlsx';

import { Response } from 'express';
import * as fs from 'fs';
import * as path from 'path';

import { EventEmitter2 } from '@nestjs/event-emitter';
import { Event_Actions } from 'src/event-listeners/actions';
import { JwtAuthGuard } from '../users/jwt/jwt-auth.guard';
import { User } from '../users/entities/user.entity';
import { Permissions } from 'src/modules/events/permission';
import { CronAuthGuard } from 'src/cron-auth/api-key-auth.guard';
import { formattedAmount, getTotalGstReport } from '../billing/totalCalculations';

@Controller('common')
export class CommonController {
  public eventEmitter: EventEmitter2;
  public service: CommonService;

  public constructor(eventEmitter: EventEmitter2, service: CommonService) {
    this.eventEmitter = eventEmitter;
    this.service = service;
  }

  public static withEventEmitter(eventEmitter: EventEmitter2) {
    return new CommonController(eventEmitter, null);
  }

  public static withService(service: CommonService) {
    return new CommonController(null, service);
  }

  @Post('upload')
  @UseInterceptors(FileInterceptor('file'))
  uploadFile(@UploadedFile() file: Express.Multer.File) {
    return this.service.upload(file);
  }

  @Get('states')
  async getStates() {
    return await State.find();
  }

  @Post('commonsqlapi')
  async getcommonsqlAPI(@Body() sql: any) {
    let data = await getManager().query(sql);
    return data;
  }

  @UseGuards(JwtAuthGuard)
  @Post('exportqueryapi')
  async exportquerysqlAPI(@Req() req: any, @Body() payload: any) {
    let records = await this.getquerysqlAPI(req, payload);
    const columnMapping = {
      clientid: 'Client ID',
      clientname: 'Client',
      clientname_clientgroupname: 'Client / Client Group',
      billing_entity: 'Billing Entity',
      billingentity: 'Billing Entity',
      category: '	Service Category',
      sub_category: 'Sub Category',
      task_id: 'Task ID',
      tasknumber: 'Task ID',
      taskname: 'Task Name',
      fee: 'Service Fee',
      user_name: 'Employee Name ',
      full_name: 'Employee Name ',
      todo: 'Todo',
      in_progress: 'In Progress',
      Hold: 'On Hold',
      on_hold: 'On Hold',
      under_review: 'Under Review',
      completed: 'Completed',
      terminated: 'Terminated',
      deleted: 'Deleted',
      total: 'Total',
      totaltasksassigned: 'Assigned Task',
      terminated_count: 'Terminated Task',
      deleted_count: 'Deleted Task',
      completed_count: 'Completed Task',
      dueDate: 'Statutory Due Date',
      pureagent: 'Pure Agent',
      additionalchargers: 'Additional Charges',
      amounttotal: 'Total',
      invoicenumber: 'Invoice #',
      proformaInvoicenumber: 'Proforma Invoice #',
      invoicedate: 'Invoice Date',
      invoiceduedate: 'Invoice Due Date',
      daysoverdue: 'Overdue By',
      invoiceamount: 'Invoice Amount',
      amountrecieved: 'Amount Received',
      balancedueamount: 'Balance Due',
      receipttype: 'Receipt Type',
      receiptnumber: 'Receipt #',
      receiptcreateddate: 'Receipt Created Date',
      receiptdate: 'Receipt Date',
      receiptamount: 'Receipt Amount',
      status: 'Status',
      grandtotal: "Grand Total",
      igst: "IGST",
      sgst: "SGST",
      cgst: "CGST",
      taskLeaders: "Task Leaders",
      members: "Task Members"


    };
    // const modifiedRecords = records.map((record) => {
    //   const modifiedRecord = {};
    //   for (const key in columnMapping) {
    //     if (record[key] !== undefined) {
    //       modifiedRecord[columnMapping[key]] = key.includes('.')
    //         ? record[key.split('.')[0]][key.split('.')[1]]
    //         : record[key];
    //     }
    //   }
    //   return modifiedRecord;
    // });
    const modifiedRecords = records.map((record) => {
      const modifiedRecord: any = {};
      for (const key in columnMapping) {
        const mappedKey = columnMapping[key];

        if (record[key] !== undefined) {
          if (key === 'members' || key === 'taskLeaders') {
            modifiedRecord[mappedKey] = Array.isArray(record[key])
              ? record[key].map((item: any) => item.fullName || item.title || '').join(', ')
              : '';

          } else {
            modifiedRecord[mappedKey] = record[key];
          }
        }
      }
      return modifiedRecord;
    });


    if (!modifiedRecords.length) {
      throw new BadRequestException('No Data for Export !');
    }

    const sheetName = payload.sheetname.substring(0, 31);
    const worksheet = xlsx.utils.json_to_sheet(modifiedRecords);
    const workbook = xlsx.utils.book_new();
    xlsx.utils.book_append_sheet(workbook, worksheet, sheetName);
    let file = xlsx.write(workbook, { type: 'buffer' });
    return file;
  }

  @UseGuards(JwtAuthGuard)
  @Post('/clientbilled-export')
  async exportClientBilledTasks(@Req() request: any, @Body() body: any) {
    const payload = body;
    const clientId = payload.clientId;
    return this.service.exportClientBilledTasks(clientId, payload);
  }

  @UseGuards(JwtAuthGuard)
  @Post('/client-group-billed-export')
  async exportClientGroupBilledTasks(@Req() request: any, @Body() body: any) {
    const payload = body;
    const clientId = payload.clientId;
    return this.service.exportClientGroupBilledTasks(clientId, payload);
  }

  @UseGuards(JwtAuthGuard)
  @Post('/client-invoice-export')
  async exportClientInvoiceReport(@Req() request: any, @Body() body: any) {
    const payload = body;
    const clientId = payload.clientId;
    return this.service.exportClientInvoiceReport(clientId, payload);
  }

  @UseGuards(JwtAuthGuard)
  @Post('/client-proforma-invoice-export')
  async exportClientProformaInoviceReport(@Req() request: any, @Body() body: any) {
    const payload = body;
    const clientId = payload.clientId;
    return this.service.exportClientProformaInvoiceReport(clientId, payload);
  }

  @UseGuards(JwtAuthGuard)
  @Post('/client-group-invoice-export')
  async exportClientGroupInvoiceReport(@Req() request: any, @Body() body: any) {
    const payload = body;
    const clientId = payload.clientId;
    return this.service.exportClientGroupInvoiceReport(clientId, payload);
  }

  @UseGuards(JwtAuthGuard)
  @Post('/client-lsit-export')
  async exportClientOverviewlist(@Req() request: any, @Body() body: any) {
    const payload = body;
    const { userId } = request.user;
    const orgId = payload.organizationid;
    let user = await User.findOne({ where: { id: userId }, relations: ['role'] });
    let ViewAll = user.role.permissions.find(
      (permission) => permission.slug === Permissions.VIEW_ALL_CLIENT_MANAGERS,
    );
    let ViewAssigned = user.role.permissions.find(
      (permission) => permission.slug === Permissions.VIEW_ASSIGNED_CLIENT_MANAGERS,
    );
    return this.service.exportClientOverviewlist(orgId, payload, ViewAll, ViewAssigned, user);
  }

  @UseGuards(JwtAuthGuard)
  @Post('/client-group-lsit-export')
  async exportClientGroupOverviewlist(@Req() request: any, @Body() body: any) {
    const payload = body;
    const { userId } = request.user;
    const orgId = payload.organizationid;
    let user = await User.findOne({ where: { id: userId }, relations: ['role'] });
    let ViewAll = user.role.permissions.find(
      (permission) => permission.slug === Permissions.VIEW_ALL_CLIENT_MANAGERS,
    );
    let ViewAssigned = user.role.permissions.find(
      (permission) => permission.slug === Permissions.VIEW_ASSIGNED_CLIENT_MANAGERS,
    );
    return this.service.exportClientGroupOverviewlist(orgId, payload, user, ViewAll, ViewAssigned);
  }

  @UseGuards(JwtAuthGuard)
  @Post('/clientunbilled-export')
  async exportUnClientBilledTasks(@Req() request: any, @Body() body: any) {
    const payload = body;

    const clientId = payload.clientId;
    return this.service.exportUnClientBilledTasks(clientId, payload);
  }

  @UseGuards(JwtAuthGuard)
  @Post('/client-group-unbilled-export')
  async exportUnClientGroupBilledTasks(@Req() request: any, @Body() body: any) {
    const payload = body;
    const clientId = payload.clientId;
    return this.service.exportUnClientGroupUnBilledTasks(clientId, payload);
  }

  @UseGuards(JwtAuthGuard)
  @Post('/clientreceipts-export')
  async exportClientReceipts(@Req() request: any, @Body() body: any) {
    const payload = body;

    const clientId = payload.clientId;
    return this.service.exportClientReceipts(clientId, payload);
  }

  @UseGuards(JwtAuthGuard)
  @Post('/client-group-receipts-export')
  async exportClientGroupReceipts(@Req() request: any, @Body() body: any) {
    const payload = body;
    const clientId = payload.clientId;
    return this.service.exportClientGroupReceipts(clientId, payload);
  }

  @UseGuards(JwtAuthGuard)
  @Post('queryapi')
  async getquerysqlAPI(@Req() req: any, @Body() payload: any) {
    const { userId } = req.user;
    let user = await User.findOne({ where: { id: userId }, relations: ['role'] });
    let ViewAll = user.role.permissions.find(
      (permission) => permission.slug === Permissions.VIEW_ALL_CLIENT_MANAGERS,
    );
    let ViewAssigned = user.role.permissions.find(
      (permission) => permission.slug === Permissions.VIEW_ASSIGNED_CLIENT_MANAGERS,
    );

    if (payload.query == 'usertasksbyorgid') {
      payload.sql = usertasksbyorgid(payload);
    }
    if (payload.query == 'taskhrmsdetails') {
      payload.sql = taskhrmsdetails(payload);
    }
    if (payload.query == 'upcommingtasks') {
      payload.sql = upcommingtasks(payload);
    }
    if (payload.query == 'statuswisetasks') {
      payload.sql = statuswisetasks(payload);
    }
    if (payload.query == 'servicecategorystatusbytasks') {
      payload.category = payload.category.name;
      payload.sql = servicecategorystatusbytasks(payload);
    }
    if (payload.query == 'allcategoryandsubcategory') {
      payload.users = payload.users.id;
      payload.sql = allcategoryandsubcategory(payload);
    }
    if (payload.query == 'userbasedmasterreport') {
      payload.users = payload.users.id;
      payload.sql = userbasedmasterreport(payload);
    }
    if (payload.query == 'overduetasks') {
      payload.sql = overduetasks(payload);
    }
    if (payload.query == 'detailedoverduetasks') {
      payload.users = payload.users.id;
      payload.sql = detailedoverduetasks(payload);
    }
    if (payload.query == 'highestnumberoftaskscompletion') {
      payload.sql = highestnumberoftaskscompletion(payload);
    }
    if (payload.query == 'allusersefficiency') {
      payload.sql = allusersefficiency(payload);
    }

    if (payload.query == 'clientinvoiceunbilled') {
      payload.sortQuery = '';
      if (payload.sort?.column && payload?.sort?.direction) {
        payload.sortQuery = 'ORDER BY ' + `${payload.sort.column} ${payload.sort.direction}`;
      }
      payload.sql = clientinvoiceunbilled(payload);
    }
    if (payload.query == 'clientgroupinvoiceunbilled') {
      payload.sortQuery = '';
      if (payload.sort?.column && payload?.sort?.direction) {
        payload.sortQuery = 'ORDER BY ' + `${payload.sort.column} ${payload.sort.direction}`;
      }
      payload.sql = clientGroupinvoiceunbilled(payload);
    }
    if (payload.query == 'clientinvoicebilled') {
      payload.sortQuery = '';
      if (payload.sort?.column && payload?.sort?.direction) {
        payload.sortQuery = 'ORDER BY ' + `${payload.sort.column} ${payload.sort.direction}`;
      }
      payload.sql = clientinvoicebilled(payload);
    }
    if (payload.query == 'clientgroupinvoicebilled') {
      payload.sql = clientgroupinvoicebilled(payload);
    }
    if (payload.query == 'clientinvoicebilling') {
      payload.sql = clientinvoicebilling(payload);
    }
    if (payload.query == 'clientgroupinvoicebilling') {
      payload.sql = clientgroupinvoicebilling(payload);
    }
    if (payload.query == 'clientproformainvoicebilling') {
      payload.sql = clientproformainvoicebilling(payload);
    }
    if (payload.query == 'clientgroupproformainvoicebilling') {
      payload.sql = clientgroupproformainvoicebilling(payload);
    }
    if (payload.query == 'clientinvoicereceipts') {
      payload.sql = clientinvoicereceipts(payload);
    }
    if (payload.query == 'clientgroupinvoicereceipts') {
      payload.sql = clientgroupinvoicereceipts(payload);
    }
    if (payload.query == 'clientdashboardamountreceived') {
      payload.sql = clientdashboardamountreceived(payload);
    }
    if (payload.query == 'clientdashboardamountdue') {
      payload.sql = clientdashboardamountdue(payload);
    }
    if (payload.query == 'clientpureagentreceivedanddue') {
      payload.sql = clientpureagentreceivedanddue(payload);
    }
    if (payload.query == 'clientdashboardactivitylog') {
      payload.sql = clientdashboardactivitylog(payload);
    }
    if (payload.query == 'clientslistinvoice') {
      payload.sortQuery = '';
      if (payload.sort?.column && payload?.sort?.direction) {
        payload.sortQuery = 'ORDER BY ' + `${payload.sort.column} ${payload.sort.direction}`;
      }
      payload.sql = clientslistinvoice(payload, user, ViewAll, ViewAssigned);
    }
    if (payload.query == 'clientsgrouplistinvoice') {
      payload.sortQuery = '';
      if (payload.sort?.column && payload?.sort?.direction) {
        payload.sortQuery = 'ORDER BY ' + `${payload.sort.column} ${payload.sort.direction}`;
      }

      payload.sql = clientsgrouplistinvoice(payload, user, ViewAll, ViewAssigned);
    }
    if (payload.query == 'clientsoverduereminder') {
      payload.sortQuery = '';
      if (payload.sort?.column && payload?.sort?.direction) {
        payload.sortQuery = 'ORDER BY ' + `${payload.sort.column} ${payload.sort.direction}`;
      }
      payload.sql = clientsoverduereminder(payload, user, ViewAll, ViewAssigned);
    }
    if (payload.query == 'clientdashboardinvoiceunbilled') {
      payload.sql = clientdashboardinvoiceunbilled(payload);
    }
    if (payload.query == 'clientdashboardinvoicebilled') {
      payload.sql = clientdashboardinvoicebilled(payload);
    }
    if (payload.query == 'taskscompletedtobilledtasks') {
      if (payload.billingEntity) {
        payload.billingEntity = payload.billingEntity.join(',');
      }
      payload.sql = taskscompletedtobilledtasks(payload);
    }
    if (payload.query == 'taskscompletedtounbilledtasks') {
      payload.sql = taskscompletedtounbilledtasks(payload);
    }
    // if (payload.query == 'taskInvoiceReport') {
    //   payload.sql = taskInvoice(payload);
    // }
    if (payload.query == 'ClientTasksCompletedToBilled') {
      payload.clientType = payload.clients.type;
      payload.clients = payload.clients.id;
      if (payload.billingEntity) {
        payload.billingEntity = payload.billingEntity.join(',');
      }
      payload.sql = ClientTasksCompletedToBilled(payload);
    }

    if (payload.query == 'ClientTasksCompletedToUnBilled') {
      payload.clientType = payload.clients.type;
      payload.clients = payload.clients.id;
      payload.sql = ClientTasksCompletedToUnBilled(payload);
    }
    if (payload.query == 'balancedueforinvoicesraisedreport') {
      if (payload.billingEntity) {
        payload.billingEntity = payload.billingEntity.join(',');
      }
      payload.sql = balancedueforinvoicesraisedreport(payload);
      //hii
    }
    if (payload.query == 'balancedueforinvoicesraisedreminder') {
      // if (payload.billingEntity) {
      //   payload.billingEntity = payload.billingEntity.join(',');
      // }
      console.log('reminderrr', payload)
      payload.sql = balancedueforinvoicesraisedreminder(payload);
      //hii
    }
    if (payload.query == 'invoiceoverduereports') {
      if (payload.billingEntity) {
        payload.billingEntity = payload.billingEntity.join(',');
      }
      payload.sql = invoiceoverduereports(payload);
    }
    if (payload.query == 'receiptmanagementreport') {
      if (payload.billingEntity) {
        payload.billingEntity = payload.billingEntity.join(',');
      }
      payload.sql = receiptmanagementreport(payload);
    }
    if (payload.query == 'proformaInvoiceReport') {
      if (payload.billingEntity) {
        payload.billingEntity = payload.billingEntity.join(',');
      }
      if (payload.client) {
        payload.client = payload.client.join(',');
      }
      if (payload.status) {
        payload.status = payload.status.map((status: string) => `'${status}'`).join(', ');;
      };
      payload.sql = proformaInvoiceReport(payload);
    }
    if (
      payload.query == 'statuswisetasks' ||
      payload.query == 'userbasedmasterreport' ||
      payload.query == 'overduetasks' ||
      payload.query == 'detailedoverduetasks' ||
      payload.query == 'highestnumberoftaskscompletion' ||
      payload.query == 'taskscompletedtobilledtasks' ||
      payload.query == 'taskscompletedtounbilledtasks' ||
      payload.query == 'ClientTasksCompletedToBilled' ||
      payload.query == 'ClientTasksCompletedToUnBilled' ||
      payload.query == 'balancedueforinvoicesraisedreport' ||
      payload.query == 'invoiceoverduereports' ||
      payload.query == 'receiptmanagementreport' ||
      payload.query == 'proformaInvoiceReport'
    ) {
      this.eventEmitter.emit(Event_Actions.REPORT_GENERATED, { ...payload, user });
    }
    let data = [];

    if (payload.query == 'detailedoverduecompletedtasks') {
      payload.users = payload?.users?.id;
      data = await detailedoverduecompletedtasks(payload);

    } else if (payload.query == 'taskInvoiceReport') {
      console.log("T-0")
      data = await taskInvoice(payload);

      // return data;

    }
    else {
      data = await getManager().query(payload.sql);
    }
    if (
      payload.query == 'taskscompletedtobilledtasks'
    ) {
      return this.service.addTaskDataForReport(data);
    }

    return data;
  }

  @Post('/synccalender')
  async synccalender(@Body() body) {
    const events = await this.service.synccalender(body);
    return events;
  }

  @Post('/synccalenderstatus')
  async synccalenderstatus(@Body() body) {
    const status = await this.service.synccalenderstatus(body);
    return status;
  }

  @Post('/invoiceUpload')
  async getInvoicePdfAndSendNotification(@Body() body: any) {
    return this.service.uploadInvoice(body);
  }

  @Post('/invoiceProformaUpload')
  async getInvoiceProformaPdfAndSendNotification(@Body() body: any) {
    return this.service.uploadProformaInvoice(body);
  }

  @Post('/receiptUpload')
  async getReceiptPdfAndSendNotification(@Body() body: any) {
    return this.service.uploadReceipt(body);
  }

  @Post('/invoiceUploadForEdit')
  async getInvoicePdfAndSendNotificationForEdit(@Body() body: any) {
    return this.service.uploadInvoiceForEdit(body);
  }

  @Post('/invoiceProformaUploadForEdit')
  async getInvoiceProformaPdfAndSendNotificationForEdit(@Body() body: any) {
    return this.service.uploadProformaInvoiceForEdit(body);
  }

  @Post('/receiptUploadForEdit')
  async getReceiptPdfAndSendNotificationForEdit(@Body() body: any) {
    return this.service.uploadReceiptForEdit(body);
  }

  @Get('/download-log')
  async downloadLog(@Res() res: Response) {
    const logsDirectory = path.join(process.cwd(), 'logs');

    try {
      // Get all log files in the logsDirectory
      const logFiles = fs
        .readdirSync(logsDirectory)
        .filter((file) => file.startsWith('api-stderr'))
        .map((file) => ({ file, mtime: fs.statSync(path.join(logsDirectory, file)).mtime }))
        .sort((a, b) => b.mtime.getTime() - a.mtime.getTime());

      if (logFiles.length === 0) {
        return res.status(404).send('Log file not found');
      }

      const latestLogFile = logFiles[0].file;
      const logFilePath = path.join(logsDirectory, latestLogFile);

      // Read the entire content of the log file
      const fileContent = fs.readFileSync(logFilePath, 'utf-8');

      // Set the appropriate headers for the response
      res.set({
        'Content-Type': 'text/plain',
        'Content-Disposition': `attachment; filename=${latestLogFile}`,
      });

      // Send the file content in the response
      res.status(200).send({ data: fileContent });
    } catch (error) {
      console.error('Error opening log files:', error.message);
      res.status(500).send('Internal Server Error');
    }
  }

  @UseGuards(JwtAuthGuard)
  @Post('/invoiceReminder')
  async sendInvoiceReminder(@Req() req: any, @Body() body: any) {
    const { userId } = req.user;
    return this.service.InvoiceReminder(body, userId);
  }

  @UseGuards(JwtAuthGuard)
  @Get('/getOrganizationPreference')
  async getOrganizationPreference(@Req() req: any) {
    const { userId } = req.user;
    return this.service.getOrganizationPreference(userId);
  }

  @UseGuards(CronAuthGuard)
  @Get('/admin-exp-org')
  async sendOrganizationExpiryAlertMail() {
    return this.service.sendOrganizationExpiryAlertMail();
  }
}
