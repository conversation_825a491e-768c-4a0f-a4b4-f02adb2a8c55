"use strict";

const backendApiPath = './';

const backendApi = {
    name: "vider_server",
    cwd: backendApiPath,
    script: "dist/src/main.js",
    args: 'start',
    watch: false,
    exec_mode: "cluster",
    error_file: `logs/api-stderr.log`,
    out_file: `logs/api-stdout.log`,
    merge_logs: true,
    autorestart: true,
    restart_delay: 3000,
    instances: 2,
    min_uptime: "2m",
    max_restarts: 1e9,
    max_memory_restart: '1G',
    node_args: '--max-old-space-size=4096',
    env: {
        NODE_ENV: 'production'
    }
};

module.exports = {
    apps: [
        backendApi,
    ]
};