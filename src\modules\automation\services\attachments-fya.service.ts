import {
  ConflictException,
  forwardRef,
  Inject,
  Injectable,
  InternalServerErrorException,
} from '@nestjs/common';
import { StorageSystem } from 'src/modules/organization/entities/organization.entity';
import { User } from 'src/modules/users/entities/user.entity';
import AutFyaNotice from '../entities/aut_income_tax_eproceedings_fya_notice.entity';
import Storage, { StorageType } from 'src/modules/storage/storage.entity';
import { v4 as uuidv4 } from 'uuid';

import { AwsService } from 'src/modules/storage/upload.service';
import { StorageService } from 'src/modules/storage/storage.service';
import * as moment from 'moment';
import { OneDriveStorageService } from 'src/modules/ondrive-storage/onedrive-storage.service';
import AuthToken, { AuthTokenType } from 'src/modules/ondrive-storage/auth-token.entity';
import Activity, { ActivityType } from 'src/modules/activity/activity.entity';
import { Event_Actions } from 'src/event-listeners/actions';
import { BharathStorageService } from 'src/modules/storage/bharath-storage.service';
import { getName } from 'src/utils/FilterSpecialChars';
import { GoogleDriveStorageService } from 'src/modules/ondrive-storage/googledrive-storage.service';

@Injectable()
export class AttachmentFyaService {
  constructor(
    private uploadService: AwsService,
    private bharahServce: BharathStorageService,
    @Inject(forwardRef(() => StorageService))
    private storageService: StorageService,
    @Inject(forwardRef(() => OneDriveStorageService))
    private oneDriveStorageService: OneDriveStorageService,
    @Inject(forwardRef(() => GoogleDriveStorageService))
    private googleDriveStorageService: GoogleDriveStorageService,
  ) {}

  async saveAttachment(autFyaNoticeId: number, files: Express.Multer.File[], userId, body) {
    let user = await User.findOne({
      where: { id: userId },
      relations: ['organization'],
    });
    const storageSystem = user?.organization?.storageSystem;
    if (storageSystem === StorageSystem.AMAZON) {
      return await this.addAttachment(autFyaNoticeId, files, userId, body);
    } else if (storageSystem === StorageSystem.MICROSOFT) {
      return await this.addOneDriveAttachments(autFyaNoticeId, files, userId, body);
    } else if (storageSystem === StorageSystem.GOOGLE) {
      return await this.addGoogleAttachments(autFyaNoticeId, files, userId, body);
    }
  }

  async addAttachment(autFyaNoticeId: number, files: Express.Multer.File[], userId: number, body) {
    try {
      let autFyaNotice = await AutFyaNotice.findOne({
        where: { id: autFyaNoticeId },
        relations: ['client'],
      });

      let user = await User.findOne({
        where: { id: userId },
        relations: ['organization'],
      });

      let existingStorage = await this.existingFyaStorage(autFyaNotice, user);
      let attachments: Storage[] = [];
      let errors: string[] = [];

      for (let file of files) {
        const { buffer, mimetype, originalname, size } = file;
        const existingAttachement = await Storage.find({
          where: { parent: existingStorage, name: originalname },
        });
        if (existingAttachement?.length) {
          errors.push(`File name ${originalname} already exists`);
          continue;
        }

        const { freeSpace } = await this.storageService.getOrgStorage(userId);
        if (!(freeSpace - file.size >= 0)) {
          throw new ConflictException(
            'We regret to inform you that the storage limit for your organization has been exceeded. As a result, no further data can be stored until the storage usage is reduced or additional storage capacity is allocated.',
          );
        }

        let key = `storage/automation/${autFyaNotice?.client?.id}/e-proceedings ${file.originalname}`;

        let upload: any = await this.uploadService.upload(buffer, key, mimetype);
        let storage = new Storage();
        storage.name = originalname;
        storage.file = upload.Key;
        storage.fileSize = size;
        storage.fileType = mimetype;
        storage.client = autFyaNotice.client;
        storage.type = StorageType.FILE;
        storage.parent = existingStorage;
        storage.user = user;
        storage.storageSystem = StorageSystem.AMAZON;
        if (body?.type === 'FyaNotice') {
          storage.autFyaNotice = autFyaNotice;
        } else if (body?.type === 'Response') {
          storage.autProceedingResponseFya = body?.responseId;
        }
        attachments.push(storage);
      }
      await Storage.save(attachments);

      for (let i of attachments) {
        let collectactivity = new Activity();
        collectactivity.action = Event_Actions.ATTACHEMENT_ADDED;
        collectactivity.actorId = user.id;
        collectactivity.type = ActivityType.CLIENT;
        collectactivity.typeId = autFyaNotice?.client?.id;
        collectactivity.remarks = `Atom Pro Attachement "${i.name}" Added by ${user.fullName}`;
        await collectactivity.save();
      }
      if (errors?.length) {
        return { errors };
      } else {
        return {
          success: true,
        };
      }
    } catch (error) {
      console.log(error);
      throw new InternalServerErrorException(error);
    }
  }

  async getFY(year) {
    // console.log("Current_year", year);
    if (!year) return 'Others';
    const nextYear = Number(year) + 1;
    const shortNextYear = nextYear.toString().slice(-2);
    return `AY ${year}-${shortNextYear}`;
  }

  async existingFyaStorage(autFyaNotice: AutFyaNotice, user: User, storageType?: StorageSystem) {
    try {
      if (!autFyaNotice?.client) return null;
      let atomFolder: Storage;
      let clientFolder: Storage;
      let displayNameFolder: Storage;
      let atomProFolder: Storage;
      let incomeTaxFolder: Storage;
      let fyFolder: Storage;
      let eProcedingsFolder: Storage;

      if (storageType && storageType === StorageSystem.MICROSOFT) {
        atomFolder = await Storage.findOne({
          where: {
            name: 'Atom',
            organization: user?.organization,
            show: false,
          },
        });

        if (!atomFolder) {
          const folderData = await this.oneDriveStorageService.createOneDriveFolder(
            user?.id,
            'Atom',
          );
          atomFolder = new Storage();
          atomFolder.name = 'Atom';
          atomFolder.organization = user?.organization;
          atomFolder.type = StorageType.FOLDER;
          atomFolder.uid = uuidv4();
          atomFolder.fileId = folderData.id;
          atomFolder.show = false;
          atomFolder.storageSystem = StorageSystem.MICROSOFT;
          atomFolder.authId = user?.organization?.id;
          await atomFolder.save();
        }
        clientFolder = await Storage.findOne({
          where: {
            name: 'Clients',
            organization: user?.organization,
            show: false,
          },
        });

        if (!clientFolder) {
          const folderData = await this.oneDriveStorageService.createOneDriveFolder(
            user?.id,
            'Clients',
            atomFolder.fileId,
          );
          clientFolder = new Storage();
          clientFolder.name = 'Clients';
          clientFolder.organization = user?.organization;
          clientFolder.type = StorageType.FOLDER;
          clientFolder.uid = uuidv4();
          clientFolder.fileId = folderData.id;
          clientFolder.show = false;
          clientFolder.storageSystem = StorageSystem.MICROSOFT;
          clientFolder.authId = user?.organization.id;
          await clientFolder.save();
        }
        displayNameFolder = await Storage.findOne({
          where: {
            name: autFyaNotice?.client?.displayName,
            organization: user?.organization,
            show: false,
            type: StorageType.FOLDER,
          },
        });

        if (!displayNameFolder) {
          const folderData = await this.oneDriveStorageService.createOneDriveFolder(
            user?.id,
            autFyaNotice.client.displayName,
            clientFolder?.fileId,
          );
          displayNameFolder = new Storage();
          displayNameFolder.name = autFyaNotice.client?.displayName;
          displayNameFolder.organization = user.organization;
          displayNameFolder.type = StorageType.FOLDER;
          displayNameFolder.uid = uuidv4();
          displayNameFolder.fileId = folderData.id;
          displayNameFolder.show = false;
          displayNameFolder.storageSystem = StorageSystem.MICROSOFT;
          displayNameFolder.authId = user.organization.id;
          if (autFyaNotice?.client) {
            displayNameFolder.client = autFyaNotice.client;
          }
          await displayNameFolder.save();
        }
      } else if (storageType && storageType === StorageSystem.GOOGLE) {
        atomFolder = await Storage.findOne({
          where: {
            name: 'Atom',
            organization: user.organization.id,
            show: false,
            type: StorageType.FOLDER,
          },
        });
        if (!atomFolder) {
          const folderData = await this.googleDriveStorageService.createGoogleDriveFolder(
            user?.id,
            'Atom',
          );
          atomFolder = new Storage();
          atomFolder.name = 'Atom';
          atomFolder.organization = user.organization;
          atomFolder.type = StorageType.FOLDER;
          atomFolder.uid = uuidv4();
          atomFolder.fileId = folderData.id;
          atomFolder.show = false;
          atomFolder.storageSystem = StorageSystem.GOOGLE;
          atomFolder.authId = user.organization.id;
          await atomFolder.save();
        }
        clientFolder = await Storage.findOne({
          where: {
            name: 'Clients',
            organization: user.organization.id,
            show: false,
            type: StorageType.FOLDER,
          },
        });
        if (!clientFolder) {
          const folderData = await this.googleDriveStorageService.createGoogleDriveFolder(
            user?.id,
            'Clients',
            atomFolder.fileId,
          );
          clientFolder = new Storage();
          clientFolder.name = 'Clients';
          clientFolder.organization = user.organization;
          clientFolder.type = StorageType.FOLDER;
          clientFolder.uid = uuidv4();
          clientFolder.fileId = folderData.id;
          clientFolder.show = false;
          clientFolder.storageSystem = StorageSystem.GOOGLE;
          clientFolder.authId = user.organization.id;
          await clientFolder.save();
        }
        displayNameFolder = await Storage.findOne({
          where: {
            name: autFyaNotice.client.displayName,
            organization: user.organization.id,
            show: false,
            type: StorageType.FOLDER,
            client: autFyaNotice.client,
          },
        });
        if (!displayNameFolder) {
          const folderData = await this.googleDriveStorageService.createGoogleDriveFolder(
            user?.id,
            autFyaNotice.client.displayName,
            clientFolder?.fileId,
          );
          displayNameFolder = new Storage();
          displayNameFolder.name = autFyaNotice.client.displayName;
          displayNameFolder.organization = user.organization;
          displayNameFolder.type = StorageType.FOLDER;
          displayNameFolder.uid = uuidv4();
          displayNameFolder.fileId = folderData.id;
          displayNameFolder.show = false;
          displayNameFolder.storageSystem = StorageSystem.GOOGLE;
          displayNameFolder.authId = user.organization.id;
          if (autFyaNotice?.client) {
            displayNameFolder.client = autFyaNotice.client;
          }
          await displayNameFolder.save();
        }
      }
      if (autFyaNotice.client) {
        atomProFolder = await Storage.findOne({
          where: {
            name: 'Atom Pro',
            client: { id: autFyaNotice?.client?.id },
            show: true,
          },
        });
      }
      if (!atomProFolder) {
        atomProFolder = new Storage();
        atomProFolder.name = 'Atom Pro';
        atomProFolder.client = autFyaNotice.client;
        atomProFolder.type = StorageType.FOLDER;
        atomProFolder.uid = uuidv4();
        atomProFolder.show = true;
        atomProFolder.authId = autFyaNotice.organizationId;
        if (storageType && storageType === StorageSystem.MICROSOFT) {
          const folderData = await this.oneDriveStorageService.createOneDriveFolder(
            user?.id,
            'Atom Pro',
            displayNameFolder?.fileId,
          );
          atomProFolder.fileId = folderData.id;
          atomProFolder.storageSystem = StorageSystem.MICROSOFT;
        } else if (storageType && storageType === StorageSystem.GOOGLE) {
          const folderData = await this.googleDriveStorageService.createGoogleDriveFolder(
            user?.id,
            'Atom Pro',
            displayNameFolder?.fileId,
          );
          atomProFolder.fileId = folderData.id;
          atomProFolder.storageSystem = StorageSystem.GOOGLE;
        }
        await atomProFolder.save();
      }
      incomeTaxFolder = await Storage.findOne({
        where: {
          name: 'Income Tax',
          client: { id: autFyaNotice?.client?.id },
          show: true,
          parent: { id: atomProFolder?.id },
        },
      });
      if (!incomeTaxFolder) {
        incomeTaxFolder = new Storage();
        incomeTaxFolder.name = 'Income Tax';
        incomeTaxFolder.client = autFyaNotice.client;
        incomeTaxFolder.type = StorageType.FOLDER;
        incomeTaxFolder.uid = uuidv4();
        incomeTaxFolder.show = true;
        incomeTaxFolder.authId = autFyaNotice.organizationId;
        incomeTaxFolder.parent = atomProFolder;
        if (storageType && storageType === StorageSystem.MICROSOFT) {
          const folderData = await this.oneDriveStorageService.createOneDriveFolder(
            user?.id,
            'Income Tax',
            atomProFolder?.fileId,
          );
          incomeTaxFolder.fileId = folderData.id;
          incomeTaxFolder.storageSystem = StorageSystem.MICROSOFT;
        } else if (storageType && storageType === StorageSystem.GOOGLE) {
          const folderData = await this.googleDriveStorageService.createGoogleDriveFolder(
            user?.id,
            'Income Tax',
            atomProFolder?.fileId,
          );
          incomeTaxFolder.fileId = folderData.id;
          incomeTaxFolder.storageSystem = StorageSystem.GOOGLE;
        }
        await incomeTaxFolder.save();
      }

      fyFolder = await Storage.findOne({
        where: {
          name: await this.getFY(autFyaNotice?.assesmentYear),
          client: { id: autFyaNotice?.client?.id },
          show: true,
          parent: { id: incomeTaxFolder?.id },
        },
      });
      if (!fyFolder) {
        fyFolder = new Storage();
        fyFolder.name = await this.getFY(autFyaNotice?.assesmentYear);
        fyFolder.client = autFyaNotice.client;
        fyFolder.type = StorageType.FOLDER;
        fyFolder.uid = uuidv4();
        fyFolder.show = true;
        fyFolder.authId = autFyaNotice.organizationId;
        fyFolder.parent = incomeTaxFolder;

        if (storageType && storageType === StorageSystem.MICROSOFT) {
          const folderData = await this.oneDriveStorageService.createOneDriveFolder(
            user?.id,
            await this.getFY(autFyaNotice?.assesmentYear),
            incomeTaxFolder?.fileId,
          );
          fyFolder.fileId = folderData.id;
          fyFolder.storageSystem = StorageSystem.MICROSOFT;
        } else if (storageType && storageType === StorageSystem.GOOGLE) {
          const folderData = await this.googleDriveStorageService.createGoogleDriveFolder(
            user?.id,
            await this.getFY(autFyaNotice?.assesmentYear),
            incomeTaxFolder?.fileId,
          );
          fyFolder.fileId = folderData.id;
          fyFolder.storageSystem = StorageSystem.GOOGLE;
        }
        await fyFolder.save();
      }

      //  "e-Proceedings"
      eProcedingsFolder = await Storage.findOne({
        where: {
          name: 'e-Proceedings',
          client: { id: autFyaNotice?.client?.id },
          show: true,
          parent: { id: fyFolder?.id },
        },
      });
      if (!eProcedingsFolder) {
        eProcedingsFolder = new Storage();
        eProcedingsFolder.name = 'e-Proceedings';
        eProcedingsFolder.client = autFyaNotice.client;
        eProcedingsFolder.type = StorageType.FOLDER;
        eProcedingsFolder.uid = uuidv4();
        eProcedingsFolder.show = true;
        eProcedingsFolder.authId = autFyaNotice.organizationId;
        eProcedingsFolder.parent = fyFolder;
        if (storageType && storageType === StorageSystem.MICROSOFT) {
          const folderData = await this.oneDriveStorageService.createOneDriveFolder(
            user?.id,
            'e-Proceedings',
            fyFolder?.fileId,
          );
          eProcedingsFolder.fileId = folderData.id;
          eProcedingsFolder.storageSystem = StorageSystem.MICROSOFT;
        } else if (storageType && storageType === StorageSystem.GOOGLE) {
          const folderData = await this.googleDriveStorageService.createGoogleDriveFolder(
            user?.id,
            'e-Proceedings',
            fyFolder?.fileId,
          );
          eProcedingsFolder.fileId = folderData.id;
          eProcedingsFolder.storageSystem = StorageSystem.GOOGLE;
        }
        await eProcedingsFolder.save();
      }
      return eProcedingsFolder;
    } catch (error) {
      console.log(error);
      throw new InternalServerErrorException(error);
    }
  }

  async addOneDriveAttachments(
    autFyaNoticeId: number,
    files: Express.Multer.File[],
    userId: number,
    body: any,
  ) {
    try {
      let autFyaNotice = await AutFyaNotice.findOne({
        where: { id: autFyaNoticeId },
        relations: ['client'],
      });
      let user = await User.findOne({
        where: { id: userId },
        relations: ['organization'],
      });

      let token = await AuthToken.findOne({
        where: {
          organizationId: user.organization.id,
          type: AuthTokenType.MICROSFT,
        },
      });

      let autFyaNoticeStorage = await this.existingFyaStorage(
        autFyaNotice,
        user,
        StorageSystem.MICROSOFT,
      );

      let taskAttachments: Storage[] = [];
      let errors: string[] = [];

      for (let file of files) {
        const { buffer, mimetype, originalname, size } = file;
        const existingAttachement = await Storage.find({
          where: { parent: autFyaNoticeStorage, name: originalname },
        });
        if (existingAttachement?.length) {
          errors.push(`File name ${originalname} already exists`);
          continue;
        }
        // let key = `${taskStorage.fileId}:/${file.originalname.replace(/[<>:"\/\\|?*.]/g, '')}:`;
        let key = `${autFyaNoticeStorage.fileId}:/${file.originalname
          .replace(/[<>:"\/\\|?*]/g, '')
          .replace(/\.(?=.*\.)/g, '')}:`;
        let upload: any;
        try {
          upload = await this.oneDriveStorageService.upload(
            buffer,
            key,
            mimetype,
            token,
            file,
            userId,
          );
        } catch (err) {
          let error = err?.response?.data?.error;
          if (error.code === 'InvalidAuthenticationToken') {
            await this.oneDriveStorageService.refreshToken(token);
            upload = await this.oneDriveStorageService.upload(
              buffer,
              key,
              mimetype,
              token,
              file,
              userId,
            );
          } else if (error.code === 'quotaLimitReached') {
            throw new ConflictException({
              code: 'NO_STORAGE',
              message: error.message,
            });
          }
        }

        let storage = new Storage();
        storage.name = originalname;
        storage.file = upload.file;
        storage.webUrl = upload.webUrl;
        storage.downloadUrl = upload['@microsoft.graph.downloadUrl'];
        storage.fileId = upload.id;
        storage.authId = user.organization.id;
        storage.fileSize = size;
        storage.fileType = mimetype;
        storage.client = autFyaNotice.client;
        storage.type = StorageType.FILE;
        storage.parent = autFyaNoticeStorage;
        storage.storageSystem = StorageSystem.MICROSOFT;
        storage.user = user;
        if (body?.type === 'FyaNotice') {
          storage.autFyaNotice = autFyaNotice;
        } else if (body?.type === 'Response') {
          storage.autProceedingResponseFya = body?.responseId;
        }
        taskAttachments.push(storage);
      }

      await Storage.save(taskAttachments);

      if (errors?.length) {
        return { errors };
      } else {
        return {
          success: true,
        };
      }
    } catch (err) {
      console.log(err);
      throw new InternalServerErrorException(err);
    }
  }

  async addGoogleAttachments(
    autFyaNoticeId: number,
    files: Express.Multer.File[],
    userId: number,
    body: any,
  ) {
    try {
      let autFyaNotice = await AutFyaNotice.findOne({
        where: { id: autFyaNoticeId },
        relations: ['client'],
      });

      let user = await User.findOne({
        where: { id: userId },
        relations: ['organization'],
      });

      let token = await AuthToken.findOne({
        where: {
          organizationId: user.organization.id,
          type: AuthTokenType.GOOGLE,
        },
      });
      let fyaNoticeStorage = await this.existingFyaStorage(
        autFyaNotice,
        user,
        StorageSystem.GOOGLE,
      );
      let attachments: Storage[] = [];
      let errors: string[] = [];

      for (let file of files) {
        const { buffer, mimetype, originalname, size } = file;
        const existingAttachement = await Storage.find({
          where: { parent: fyaNoticeStorage, name: originalname },
        });
        if (existingAttachement?.length) {
          errors.push(`File name ${originalname} already exists`);
          continue;
        }
        // let key = `${taskStorage.fileId}:/${file.originalname.replace(/[<>:"\/\\|?*.]/g, '')}:`;
        let key = `${fyaNoticeStorage.fileId}:/${file.originalname
          .replace(/[<>:"\/\\|?*]/g, '')
          .replace(/\.(?=.*\.)/g, '')}:`;
        let upload: any;
        try {
          upload = await this.googleDriveStorageService?.uploadToGoogleDrive(
            file.originalname,
            token,
            buffer,
            fyaNoticeStorage?.fileId,
            userId,
          );
        } catch (err) {
          let error = err?.response?.data?.error;
          if (error.code === 'InvalidAuthenticationToken') {
            await this.googleDriveStorageService?.refreshToken(token);
            upload = await this.googleDriveStorageService.uploadToGoogleDrive(
              file.originalname,
              token,
              buffer,
              fyaNoticeStorage?.fileId,
              userId,
            );
          } else if (error.code === 'quotaLimitReached') {
            throw new ConflictException({
              code: 'NO_STORAGE',
              message: error.message,
            });
          }
        }
        let storage = new Storage();
        storage.name = originalname;
        storage.file = upload.file;
        storage.webUrl = upload.webUrl;
        storage.downloadUrl = upload['@microsoft.graph.downloadUrl'];
        storage.fileId = upload.id;
        storage.authId = user.organization.id;
        storage.fileSize = size;
        storage.fileType = mimetype;
        storage.client = autFyaNotice?.client;
        storage.type = StorageType.FILE;
        storage.parent = fyaNoticeStorage;
        storage.storageSystem = StorageSystem.GOOGLE;
        storage.user = user;
        if (body?.type === 'FyaNotice') {
          storage.autFyaNotice = autFyaNotice;
        } else if (body?.type === 'Response') {
          storage.autProceedingResponseFya = body?.responseId;
        }
        attachments.push(storage);
      }

      await Storage.save(attachments);

      if (errors?.length) {
        return { errors };
      } else {
        return {
          success: true,
        };
      }
    } catch (err) {
      console.log(err);
      throw new InternalServerErrorException(err);
    }
  }

  async deleteStorageFile(storageId: number, userId: number) {
    try {
      await this.storageService.removeFile(storageId, userId);
    } catch (err) {
      console.log(err);
      throw new InternalServerErrorException(err);
    }
  }
}
