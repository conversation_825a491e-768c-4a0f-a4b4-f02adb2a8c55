import { text } from 'stream/consumers';
import {
  BaseEntity,
  Column,
  CreateDateColumn,
  Entity,
  ManyToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';

@Entity()
class QtmTemplate extends BaseEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  name: string;

  @Column()
  templateCategoryId: number;

  @Column('json', { array: true })
  labels: object[];

  @Column('json', { array: true })
  metadata: object[];

  @Column({ type: 'text' })
  description: string;

  @Column()
  updatedBy: string;

  @Column()
  templateName: string;

  @Column({ type: 'text' })
  googleDrivelink: string;

  @Column()
  version: number;

  @UpdateDateColumn()
  lastUpdated: string;

  @Column({ default: false })
  saved: boolean;

  @Column()
  excerpt: string;

  @Column()
  subCatId: number;

  @Column()
  price: number;

  @Column('json', { array: true })
  imageUrls: object[];
}

export default QtmTemplate;
