import {
  Body,
  Controller,
  Get,
  Param,
  ParseIntPipe,
  Patch,
  Post,
  Query,
} from '@nestjs/common';
import { ChannelPartnerService } from '../services/channel-partner.service';

@Controller('channel-partner')
export class ChannelPartnerController {
  constructor(private readonly channelPartnerService: ChannelPartnerService) {}

  @Get('channel-partners')
  getAllPartners() {
    return this.channelPartnerService.getAllPartners();
  }

  @Get('active-channel-partners')
  getActivePartners() {
    return this.channelPartnerService.getActivePartners();
  }

  @Post('channel-partners')
  createPartner(@Body() dto: any) {
    return this.channelPartnerService.createPartner(dto);
  }

  @Patch('channel-partners/:id')
  updatePartner(@Param('id', ParseIntPipe) id: number, @Body() dto: any) {
    return this.channelPartnerService.updatePartner(id, dto);
  }

  @Patch('toggle/:id')
  updatePartnerToogle(@Param('id', ParseIntPipe) id: number, @Body() dto: any) {
    return this.channelPartnerService.updatePartnerToogle(id, dto);
  }

  @Get('coupons')
  getAllCoupons() {
    return this.channelPartnerService.getAllCoupons();
  }

  @Post('coupons')
  createCoupon(@Body() dto: any) {
    return this.channelPartnerService.createCoupon(dto);
  }

  @Patch('coupons/:id')
  updateCoupon(@Param('id', ParseIntPipe) id: number, @Body() dto: any) {
    return this.channelPartnerService.updateCoupon(id, dto);
  }

  //   // Validate Coupon Code (Signup flow)
  @Post('coupons/validate')
  validateCoupon(@Body() dto: any) {
    return this.channelPartnerService.validateCoupon(dto);
  }

  @Get('sign-ups')
  getSignUps(@Query() query: any) {
    return this.channelPartnerService.getSignUps(query);
  }

  @Patch('lead-status/:id')
  updateSignUpStatus(@Param('id', ParseIntPipe) id: number, @Body() data: any) {
    return this.channelPartnerService.updateSignUpStatus(id, data);
  }

  @Get('analytics/:id')
  async getPartnerAnalytics(@Param('id', ParseIntPipe) id: number) {
    return this.channelPartnerService.getPartnerAnalytics(id);
  }
}
