import {
  Connection,
  EntitySubscriberInterface,
  EventSubscriber,
  UpdateEvent,
  getManager,
} from 'typeorm';
import { getUserDetails, getUserIDs, insertINTONotificationUpdate } from 'src/utils/re-use';
import Task from 'src/modules/tasks/entity/task.entity';
import { sendnewMail } from 'src/emails/newemails';
import { TaskRecurringStatus } from 'src/modules/tasks/dto/types';
import { sendWhatsAppTextMessage } from 'src/modules/whatsapp/whatsapp.service';
import ViderWhatsappSessions from 'src/modules/whatsapp/entity/viderWhatsappSessions';
import { Organization } from 'src/modules/organization/entities/organization.entity';

@EventSubscriber()
export class PriorityChangeSubscriber implements EntitySubscriberInterface<Task> {
  constructor(private readonly connection: Connection) {
    connection.subscribers.push(this);
  }

  listenTo() {
    return Task;
  }

  
  async beforeUpdate(event: UpdateEvent<Task>) {
    if (event.databaseEntity) {
    }
  }

  async afterUpdate(event: UpdateEvent<Task>) {
    if(!event.entity.bulkUpdate){
      const taskId = event.entity.id;
      const oldPrioirty = event.entity?.['oldPrioirty'];
      const entityManager = getManager();
      const afterStatus = event.entity.status;
      if (taskId && event.entity.recurringStatus !== TaskRecurringStatus.PENDING && oldPrioirty) {
        const getTaskQuery = `SELECT client_id,organization_id, client_group_id FROM task where id = ${taskId};`;
        let getTask = await entityManager.query(getTaskQuery);
        const clientId = getTask[0]?.client_id;
        const orgId = getTask[0]?.organization_id;
        const clientGroupId = getTask[0]?.client_group_id;
        if(clientId){
          const getClientQuery = `SELECT display_name FROM client where id = ${clientId};`;
          let getClient = await entityManager.query(getClientQuery);
          const clientName = getClient[0]?.display_name;
          const userId = event.entity['userId'];
          if (userId) {
            const getUserQuery = `SELECT full_name FROM user where id = ${userId};`;
            let getUser = await entityManager.query(getUserQuery);
            const userName = getUser[0]?.full_name;
            const TaskName = event?.entity?.name;
            const TaskId = event?.entity?.taskNumber;

            const PriorityLevel = event?.entity?.priority;
            if (oldPrioirty !== PriorityLevel && PriorityLevel) {
              const taskId = event?.entity?.id;
              const usersId = await getUserIDs(taskId);
              const body = `priority of "<strong>${TaskName}</strong>" of "<strong>${clientName}</strong>" has changed from "<strong>${oldPrioirty}</strong>" to "<strong>${PriorityLevel}</strong>" by <strong>${userName}</strong>`;
              const title = 'Priority Changing';
              const key = 'PRIORITY_CHANGING_PUSH';
              // insertINTOnotification(title, body, usersId, orgId);
              insertINTONotificationUpdate(title, body, usersId, orgId, key,clientId);
              const organization = await Organization.findOne({ id: orgId });


    const addressParts = [
      organization.buildingNo || '',
      organization.floorNumber || '',
      organization.buildingName || '',
      organization.street || '',
      organization.location || '',
      organization.city || '',
      organization.district || '',
      organization.state || ''
    ].filter(part => part && part.trim() !== '');
    const pincode = organization.pincode && organization.pincode.trim() !== '' ? ` - ${organization.pincode}` : '';

    const address = addressParts.join(', ') + pincode;
              if (usersId) {
                for (let user of usersId) {
                  const userDetails = await getUserDetails(user);
                  await sendnewMail({
                    id: userDetails?.id,
                    key: 'PRIORITY_CHANGING_MAIL',
                    email: userDetails?.email,
                    data: {
                      taskUserName: userDetails?.full_name,
                      taskName: TaskName,
                      clientName: clientName,
                      taskId: event?.entity?.taskNumber,
                      newPriorityLevel: PriorityLevel,
                      oldPriorityLevel: oldPrioirty,
                      userId: event.entity['userId'],
                      adress: address,
                  phoneNumber: organization?.mobileNumber,
                  mail: organization?.email,
                  legalName: organization?.tradeName || organization?.legalName

                    },
                    filePath: 'priority-change',
                    subject: `Priority Change: ${TaskName} with ${clientName}`,
                  });
                }
              }
              //whatsapp
            try {
              if (usersId !== undefined) {
                for (let userId of usersId) {
                  const sessionValidation = await ViderWhatsappSessions.findOne({
                    where: { userId: userId,status:'ACTIVE' },
                  });
                  if (sessionValidation) {
                    const userDetails = await getUserDetails(userId);
                    const { full_name: userFullName, mobile_number: userPhoneNumber,id } = userDetails;
                    const key = 'PRIORITY_CHANGING_WHATSAPP';
                    const whatsappMessageBody = `
Hi ${userFullName}

${userName} has changed priority of task ${TaskName} from **${oldPrioirty}** to **${PriorityLevel}**

Task Details :
Task Id   : ${TaskId}
Task Name : ${TaskName}
Task Due Date : ${event?.entity?.dueDate}
Client Name : ${clientName}

We hope this helps!
          `;
                    await sendWhatsAppTextMessage(
                      `91${userPhoneNumber}`,
                      whatsappMessageBody,
                      orgId,
                      title,
                      id,
                      key,
                    );
                  }
                }
              }
            } catch (error) {
              console.error('Error sending User WhatsApp notification:', error);
            }
            }
          }
        }
        if(clientGroupId){
          const getClientQuery = `SELECT display_name FROM client_group where id = ${clientGroupId};`;
          let getClient = await entityManager.query(getClientQuery);
          const clientName = getClient[0]?.display_name;
          const userId = event.entity['userId'];
          if (userId) {
            const getUserQuery = `SELECT full_name FROM user where id = ${userId};`;
            let getUser = await entityManager.query(getUserQuery);
            const userName = getUser[0]?.full_name;
            const TaskName = event?.entity?.name;
            const PriorityLevel = event?.entity?.priority;
            if (oldPrioirty !== PriorityLevel && PriorityLevel) {
              const taskId = event?.entity?.id;
              const usersId = await getUserIDs(taskId);
              const body = `priority of "<strong>${TaskName}</strong>" of "<strong>${clientName}</strong>" has changed from "<strong>${oldPrioirty}</strong>" to "<strong>${PriorityLevel}</strong>" by <strong>${userName}</strong>`;
              const title = 'Priority Changing';
              const key = 'PRIORITY_CHANGING_PUSH';
              insertINTONotificationUpdate(title, body, usersId, orgId, key);
            }
          }
        }
      }
    }
  }
}
