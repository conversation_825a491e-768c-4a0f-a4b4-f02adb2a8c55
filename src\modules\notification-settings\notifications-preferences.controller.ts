import {
  Controller,
  Get,
  UseGuards,
  Request,
  Put,
  Body,
  ParseIntPipe,
  Param,
} from '@nestjs/common';
import { NotificationPreferencesService } from './notifications-preferences.services';
import { JwtAuthGuard } from '../users/jwt/jwt-auth.guard';
import UpdatePreferencesDto from './dto/preferences.dto';
import { NotificationAction, Notification_Actions } from './action';

@Controller('notifications-preferences')
export class PushNotificationsController {
  constructor(private readonly notificationPreferencesService: NotificationPreferencesService) {}
  @Get()
  async get(@Request() req: any) {
    return Notification_Actions;
  }

  @UseGuards(JwtAuthGuard)
  @Get('getPreferences')
  async getPreferences(@Request() req: any) {
    const { userId } = req.user;
    return this.notificationPreferencesService.get(userId);
  }

  @UseGuards(JwtAuthGuard)
  @Put('/update')
  async updatePreferences(@Request() req: any, @Body() body: UpdatePreferencesDto) {
    const { userId } = req.user;
    return this.notificationPreferencesService.update(userId, body);
  }

  @UseGuards(JwtAuthGuard)
  @Get('getUserPreferences/:id')
  async getPreferencesUser(@Request() req: any,@Param('id', ParseIntPipe) id: number) {
    const { userId } = req.user;
    return this.notificationPreferencesService.getUserPreferences(id,userId);
  }

  @Get('action')
  async getAction(@Request() req: any) {
    return NotificationAction;
  }

  @UseGuards(JwtAuthGuard)
  @Put('update-qtm-preferences')
  async updatePreferencesNew(@Request() req: any, @Body() body) {
    const { userId } = req.user;
    return this.notificationPreferencesService.updatePreferences(userId, body);
  }

  @UseGuards(JwtAuthGuard)
  @Put('update-whatsappconfig')
  async updateWhatsappConfig(@Request() req: any, @Body() body) {
    const { userId } = req.user;
    return this.notificationPreferencesService.updateWhatsappConfig(userId, body);
  }
}
