import Client from 'src/modules/clients/entity/client.entity';
import {
  BaseEntity,
  Column,
  CreateDateColumn,
  Entity,
  ManyToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import AutClientCredentials from './aut_client_credentials.entity';

@Entity()
class AutMyCas extends BaseEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  panNumber: string;

  @Column()
  caName: string;

  @Column()
  assesseCaId: string;

  @Column()
  caMembershipNum: string;

  @Column()
  caStatus: string;

  @Column()
  assignedDate: string;

  @Column()
  filingStatus: string;

  @Column()
  filingType: string;

  @Column()
  formTypeCd: string;

  @Column()
  isWithdrawable: string;

  @Column()
  formStatus: string;

  @Column()
  transactionNo: string;

  @Column()
  assessmentYear: string;

  @Column()
  financialYear: string;

  @Column()
  udinNumber: string;

  @Column()
  organizationId: number;

  @ManyToOne(() => Client, (client) => client.autMycas, { onDelete: 'SET NULL' })
  client: Client;

  

  @ManyToOne(
    () => AutClientCredentials,
    (autClientCredentials) => autClientCredentials.autMycas,
    { onDelete: 'SET NULL' },
  )
  autClientCredentials: AutClientCredentials;


}

export default AutMyCas;
