import Client from 'src/modules/clients/entity/client.entity';
import { Organization } from 'src/modules/organization/entities/organization.entity';
import Storage from 'src/modules/storage/storage.entity';
import {
  BaseEntity,
  Column,
  CreateDateColumn,
  Entity,
  JoinColumn,
  JoinTable,
  ManyToMany,
  ManyToOne,
  OneToMany,
  OneToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';


@Entity()
class ViderAi extends BaseEntity {

  @PrimaryGeneratedColumn()
  id: any;

  @Column()
  response:string;

  @OneToOne(() => Storage, (storage) => storage.viderAi, { cascade: true })
  storage: Storage;

  @CreateDateColumn()
  createdAt: string;

  @UpdateDateColumn()
  updatedAt: string;

  @Column()
  organizationId : number;

 



}

export default ViderAi;
