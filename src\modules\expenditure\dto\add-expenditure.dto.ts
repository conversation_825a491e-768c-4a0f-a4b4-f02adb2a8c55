import {
  IsBoolean,
  IsDateString,
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  ValidateIf,
} from 'class-validator';
import { ExpenditureStatus, ExpenditureType, TaskExpenditureType } from './types';

class AddExpenditureDto {
  @IsNotEmpty()
  particularName: string;

  @IsOptional()
  @IsDateString()
  date: string;

  @IsNotEmpty()
  @IsNumber()
  amount: number;

  @IsOptional()
  attachment: string;

  @IsNotEmpty()
  @IsEnum(ExpenditureType)
  type: ExpenditureType;

  @ValidateIf((o) => o.type === ExpenditureType.TASK)
  @IsNotEmpty()
  @IsNumber()
  task: number;

  @ValidateIf((o) => o.type === ExpenditureType.TASK)
  @IsNotEmpty()
  @IsEnum(TaskExpenditureType)
  taskExpenseType: TaskExpenditureType;

  @ValidateIf((o) => o.type === ExpenditureType.GENERAL)
  // @IsNotEmpty()
  @IsOptional()
  @IsNumber()
  client: number;

  @IsNotEmpty()
  @IsBoolean()
  includeInInvoice: boolean;

  @IsOptional()
  storage: any;

  @IsOptional()
  approvalStatus: ExpenditureStatus;

  @IsOptional()
  remarks: string;
}

export default AddExpenditureDto;
