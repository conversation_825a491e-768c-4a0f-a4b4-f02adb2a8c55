import {
  Connection,
  EntitySubscriberInterface,
  EventSubscriber,
  UpdateEvent,
  getManager,
} from 'typeorm';

import { getAdminIDsBasedOnOrganizationId } from 'src/utils/re-use';
import { getUserDetails } from 'src/utils/re-use';
import Lead from 'src/modules/leads/lead.entity';
import { sendnewMail } from 'src/emails/newemails';
import ViderWhatsappSessions from 'src/modules/whatsapp/entity/viderWhatsappSessions';
import { sendWhatsAppTextMessage } from 'src/modules/whatsapp/whatsapp.service';
import { Organization } from 'src/modules/organization/entities/organization.entity';

@EventSubscriber()
export class LeadToClientSubscriber implements EntitySubscriberInterface<Lead> {
  constructor(private readonly connection: Connection) {
    connection.subscribers.push(this);
  }

  listenTo() {
    return Lead;
  }

  beforeLeadStatus = '';
  async beforeUpdate(event: UpdateEvent<Lead>) {
    this.beforeLeadStatus = event.databaseEntity.status;
  }

  afterLeadStatus = '';
  async afterUpdate(event: UpdateEvent<Lead>) {
    const entityManager = getManager();
    const userId = event.entity['userId'];
    const userDetails = await getUserDetails(userId);
    this.afterLeadStatus = event?.entity?.status;
    const getOrgIdQuery = await Lead.findOne({
      where: { id: event.entity.id },
      relations: ['organization'],
    });

    const orgAdminId = await getAdminIDsBasedOnOrganizationId(getOrgIdQuery.organization.id);
    const adminDetails = await getUserDetails(orgAdminId[0]);
    const orgId = adminDetails?.organization_id;
      const organization = await Organization.findOne({ id: orgId });

      const addressParts = [
        organization.buildingNo || '',
        organization.floorNumber || '',
        organization.buildingName || '',
        organization.street || '',
        organization.location || '',
        organization.city || '',
        organization.district || '',
        organization.state || ''
      ].filter(part => part && part.trim() !== '');
      const pincode = organization.pincode && organization.pincode.trim() !== '' ? ` - ${organization.pincode}` : '';

      const address = addressParts.join(', ') + pincode;
    if (orgAdminId.length > 0) {
      const leadName = event?.entity?.name;
      const category = event?.entity?.category;
      const mobileNumber = event?.entity?.mobileNumber;
      const emailId = event?.entity?.email;
      const adminDetails = await getUserDetails(orgAdminId[0]);
      
      if (this.beforeLeadStatus === 'PENDING' && this.afterLeadStatus === 'converted') {
        await sendnewMail({
          id: adminDetails?.id,
          key: 'LEAD_TO_CLIENT_CONVERT_MAIL',
          email: adminDetails.email,
          data: {
            adminName: adminDetails?.full_name,
            leadName: leadName,
            userName: userDetails?.full_name,
            clientName: leadName,
            category: category,
            mobileNumber: mobileNumber,
            email: emailId,
            userId: event.entity['userId'],
            adress: address,
            phoneNumber: organization?.mobileNumber,
            mail: organization?.email,
            legalName: organization?.tradeName || organization?.legalName
          },
          filePath: 'lead-to-client-convert',
          subject: 'Lead converted',
        });
        //whatsapp
        const title = 'LEAD_CONVERTED';

        try {
          if (adminDetails !== undefined) {
            const sessionValidation = await ViderWhatsappSessions.findOne({
              where: { userId: userId, status: 'ACTIVE' },
            });
            if (sessionValidation) {
              const userDetails = await getUserDetails(userId);
              const { full_name: userFullName, mobile_number: userPhoneNumber, id, organization_id } = userDetails;
              const key = 'LEAD_CONVERTED_WHATSAPP';
              const whatsappMessageBody = `
Hi ${userFullName}

A Lead has been Converted to Client

Lead Name : ${event?.entity?.name}
Email Address : ${event.entity?.email}
Phone Number : ${event.entity?.mobileNumber}
Category : ${event?.entity.category}

We hope this helps!
    `;
              await sendWhatsAppTextMessage(
                `91${userPhoneNumber}`,
                whatsappMessageBody,
                organization_id,
                title,
                id,
                key,
              );
            }

          }
        } catch (error) {
          console.error('Error sending User WhatsApp notification:', error);
        }
      }
    }
  }
}
