import { IsDateString, IsEnum, IsNotEmpty, IsNumberString, IsOptional, IsString, ValidateIf } from 'class-validator';



class getLoghoursDto {
 

  @IsOptional()
  @IsString()
  duration: number;

  @IsOptional()
  userId: number;

  @IsOptional()
  @IsString()
  type: string;

  @IsOptional()
  @IsString()
  status: string;
  
  @IsOptional()
  @IsDateString()
  fromDate: string;

  @IsOptional()
  @IsDateString()
  toDate: string;
}

export default getLoghoursDto ;
