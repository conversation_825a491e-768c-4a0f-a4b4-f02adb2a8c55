import { BadRequestException, Injectable } from '@nestjs/common';
import * as moment from 'moment';
import Client from 'src/modules/clients/entity/client.entity';
import DscRegister from 'src/modules/dsc-register/entity/dsc-register.entity';
import Lead, { LeadStatusEnum } from 'src/modules/leads/lead.entity';
import Task from 'src/modules/tasks/entity/task.entity';
import { User, UserStatus, UserType } from 'src/modules/users/entities/user.entity';
import { createQueryBuilder, getManager, In, Not } from 'typeorm';
import { IQueryWeeklyLogHoursDto } from 'src/modules/stats/stats.controller';
import { dateFormation } from 'src/utils/datesFormation';
import Attendance from 'src/modules/attendance/attendance.entity';
import OrganizationPreferences from 'src/modules/organization-preferences/entity/organization-preferences.entity';
import ReceiptParticular, {
  ReceiptParticularStatus,
} from 'src/modules/billing/entitities/receipt-particular.entity';
import { InvoiceStatus } from 'src/modules/billing/entitities/invoice.entity';
import ReceiptCredit, {
  CreditType,
  ReceiptCreditStatus,
} from 'src/modules/billing/entitities/receipt-credit.entity';
import * as xlsx from 'xlsx';
import { LogHourType } from 'src/modules/log-hours/log-hour.entity';
import { BudgetedHourStatus } from 'src/modules/budgeted-hours/budgeted-hours.entity';
import { formatDate, getTitle } from 'src/utils';
@Injectable()
export class StatsService {
  async getTaskAnalytics(userId: number, query: any) {
    let user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
    const date = moment().subtract(15, 'days').format('YYYY-MM-DD HH:mm:ss');
    let sql = `
    select task.status as status, count(task.id) as count,
    sum(case when task.recurring = true then 1 else 0 end) as recurring,
    sum(case when task.recurring = false then 1 else 0 end) as non_recurring,
    sum(case when task.priority = 'high' then 1 else 0 end) as high,
    sum(case when task.priority = 'low' then 1 else 0 end) as low,
    sum(case when task.priority = 'medium' then 1 else 0 end) as medium,
    sum(case when task.priority = 'none' then 1 else 0 end) as none
    from task`;

    if (query.dashboardType === 'user') {
      sql += ` left join task_members_user tmu on tmu.task_id = task.id`;
    }

    if (query.clientId && query.clientId !== '') {
      sql += ` left join client c on c.id = task.client_id`;
    }

    sql += ` where task.organization_id = ${user.organization.id} and task.status not in ('terminated','deleted','completed')`;

    if (query.dashboardType === 'user') {
      sql += ` and tmu.user_id = ${userId}`;
    }

    if (query.clientId && query.clientId !== '') {
      sql += ` and c.id = ${Number(query.clientId)}`;
    }

    sql += ` and task.parent_task_id is null
    and (task.recurring_status is null or task.recurring_status = 'created')
    and task.status <> ''
    group by task.status;`;

    let tasks = await getManager().query(sql);
    let completedSql = `
    SELECT
        task.status AS status,
        COUNT(task.id) AS count,
        SUM(CASE WHEN task.recurring = true THEN 1 ELSE 0 END) AS recurring,
        SUM(CASE WHEN task.recurring = false THEN 1 ELSE 0 END) AS non_recurring,
        SUM(CASE WHEN task.priority = 'high' THEN 1 ELSE 0 END) AS high,
        SUM(CASE WHEN task.priority = 'low' THEN 1 ELSE 0 END) AS low,
        SUM(CASE WHEN task.priority = 'medium' THEN 1 ELSE 0 END) AS medium,
        SUM(CASE WHEN task.priority = 'none' THEN 1 ELSE 0 END) AS none
    FROM
        task `;
    if (query.dashboardType === 'user') {
      completedSql += ` left join task_members_user tmu on tmu.task_id = task.id`;
    }

    if (query.clientId && query.clientId !== '') {
      completedSql += ` left join client c on c.id = task.client_id`;
    }

    completedSql += ` WHERE task.organization_id = ${user.organization.id} AND task.status = 'completed'`;

    if (query.dashboardType === 'user') {
      completedSql += ` and tmu.user_id = ${userId}`;
    }

    if (query.clientId && query.clientId !== '') {
      completedSql += ` and c.id = ${Number(query.clientId)}`;
    }

    completedSql += ` AND task.status_updated_at >= STR_TO_DATE('${date}', '%Y-%m-%d %H:%i:%s')
      AND task.parent_task_id IS NULL
      AND (task.recurring_status IS NULL OR task.recurring_status = 'created')
      AND task.status <> ''
    GROUP BY
      task.status;`;
    let completedtasks = await getManager().query(completedSql);
    tasks = [...tasks, ...completedtasks];

    let recurringTasks = 0;
    let nonRecurringTasks = 0;
    let tasksByStatus: any = {
      todo: 0,
      in_progress: 0,
      under_review: 0,
      on_hold: 0,
      completed: 0,
    };
    let tasksByPriority: any = {
      high: 0,
      low: 0,
      medium: 0,
      none: 0,
    };

    tasks.forEach((task: any) => {
      tasksByStatus[task.status] = +task.count || 0;
      tasksByPriority['high'] += +task['high'] || 0;
      tasksByPriority['low'] += +task['low'] || 0;
      tasksByPriority['medium'] += +task['medium'] || 0;
      tasksByPriority['none'] += +task['none'] || 0;
      recurringTasks += +task.recurring || 0;
      nonRecurringTasks += +task.non_recurring || 0;
    });

    let totalTasks = recurringTasks + nonRecurringTasks;
    let recurringTasksPercentage = Math.round((recurringTasks / totalTasks) * 100);
    let nonRecurringTasksPercentage = Math.round((nonRecurringTasks / totalTasks) * 100);

    return {
      total:
        tasksByStatus.todo +
        tasksByStatus.in_progress +
        tasksByStatus.under_review +
        tasksByStatus.on_hold +
        tasksByStatus.completed,
      tasksByStatus,
      tasksByPriority,
      recurringTasks,
      nonRecurringTasks,
      recurringTasksPercentage: recurringTasksPercentage || 0,
      nonRecurringTasksPercentage: nonRecurringTasksPercentage || 0,
    };
  }

  async getTasksDueThisWeek(userId: number, query: any) {
    let user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
    let tasks = createQueryBuilder(Task, 'task')
      .leftJoin('task.organization', 'organization')
      .leftJoin('task.members', 'taskMembers')
      .leftJoinAndSelect('task.category', 'category')
      .leftJoinAndSelect('task.members', 'members')
      .leftJoinAndSelect('members.imageStorage', 'imageStorage')
      .leftJoinAndSelect('task.client', 'client')
      .where('organization.id = :id', { id: user.organization.id })
      .andWhere('task.status not in (:...status)', {
        status: ['terminated', 'deleted', 'completed'],
      })
      .andWhere("(task.recurring_status is null or task.recurring_status = 'created')")
      .andWhere('task.parentTask is null')
      .andWhere('task.dueDate between :start and :end', {
        start: moment().isoWeekday(1).format('YYYY-MM-DD'),
        end: moment().isoWeekday(7).format('YYYY-MM-DD'),
      })
      .orderBy('task.dueDate', 'ASC');

    if (query.dashboardType === 'user') {
      tasks.andWhere('taskMembers.id = :userId', { userId });
    }

    if (query.clientId && query.clientId !== "") {
      tasks.andWhere('client.id = :clientId', { clientId: query.clientId });
    }

    let result = await tasks.getMany();

    return result;
  }

  async getTasksByCategory(userId: number, query: any) {
    let user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
    const date = moment().subtract(15, 'days').format('YYYY-MM-DD HH:mm:ss');

    let sql = `select category.name, category.id,
    sum(case when task.recurring = true then 1 else 0 end) as recurring,
    sum(case when task.recurring = false then 1 else 0 end) as non_recurring
    from task left join category on category.id = task.category_id`;

    if (query.dashboardType === 'user') {
      sql += ` left join task_members_user tmu on tmu.task_id = task.id`;
    }

    sql += ` where task.organization_id = ${user.organization.id}
    AND (task.status IN ('todo', 'in_progress', 'on_hold', 'under_review')
    OR (task.status = 'completed' AND task.status_updated_at >= STR_TO_DATE('${date}', '%Y-%m-%d %H:%i:%s')))
    and task.parent_task_id is null and (task.recurring_status is null or task.recurring_status = 'created')`;

    if (query.dashboardType === 'user') {
      sql += ` and tmu.user_id = ${userId}`;
    }

    if (query.fromDate && query.toDate) {
      const { startTime, endTime } = dateFormation(query.fromDate, query.toDate);
      sql += ` and Date(task.created_at) >= '${startTime}' and Date(task.created_at) <= '${endTime}'`;
    }

    sql += ` group by category.id having category.id is not null`;

    let tasks = await getManager().query(sql);
    return tasks;
  }

  async getTasksByCategoryExport(userId: number, query: any) {
    let services = await this.getTasksByCategory(userId, query);
    let rows = services?.map((service) => {
      return {
        'Service Name': service?.name,
        'Recurring Tasks': service?.recurring,
        'Non-Recurring Tasks': service?.non_recurring,
      };
    });

    if (rows !== undefined && rows.length) {
      const worksheet = xlsx.utils.json_to_sheet(rows);
      const workbook = xlsx.utils.book_new();
      xlsx.utils.book_append_sheet(workbook, worksheet, 'task-by-service');
      let file = xlsx.write(workbook, { type: 'buffer' });
      return file;
    } else {
      throw new BadRequestException('No Data for Export');
    }
  }

  async getTasksByClientCategory(userId: number, query: any) {
    let user = await User.findOne({ where: { id: userId }, relations: ['organization'] });

    let sql = `select category as name,
    client.id as clientId,
    sum(case when task.recurring = true then 1 else 0 end) as recurring,
    sum(case when task.recurring = false then 1 else 0 end) as non_recurring
    from task left join client on client.id = task.client_id`;

    if (query.dashboardType === 'user') {
      sql += ` left join task_members_user tmu on tmu.task_id = task.id`;
    }

    sql += ` where task.organization_id = ${user.organization.id}
    and task.status not in ('terminated','deleted')
    and task.parent_task_id is null and (task.recurring_status is null or task.recurring_status = 'created')`;

    if (query.dashboardType === 'user') {
      sql += ` and tmu.user_id = ${userId}`;
    }

    if (query.fromDate && query.toDate) {
      const { startTime, endTime } = dateFormation(query.fromDate, query.toDate);
      sql += ` and Date(task.created_at) >= '${startTime}' and Date(task.created_at) <= '${endTime}'`;
    }

    sql += ` group by client.category having clientId is not null`;

    let tasks = await getManager().query(sql);
    return tasks;
  }

  async getTasksByClientCategoryExport(userId: number, query: any) {
    let clients = await this.getTasksByClientCategory(userId, query);
    let rows = clients?.map((client) => {

      return {
        'Client Category': (client?.name).split(' ')
          .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
          .join(' '),
        'Recurring Tasks': client?.recurring,
        'Non-Recurring Tasks': client?.non_recurring
      };
    });

    if (rows !== undefined && rows.length) {
      const worksheet = xlsx.utils.json_to_sheet(rows);
      const workbook = xlsx.utils.book_new();
      xlsx.utils.book_append_sheet(workbook, worksheet, 'task-by-clientcategories');
      let file = xlsx.write(workbook, { type: 'buffer' });
      return file;
    } else {
      throw new BadRequestException('No Data for Export');
    }
  }

  async getTasksByService(userId: number, query: any) {
    let user = await User.findOne({ where: { id: userId }, relations: ['organization'] });

    let sql = `left join service s on s.id = task.service_id 
    left join log_hour lh on lh.task_id = task.id`;

    if (query.dashboardType === 'user') {
      sql += ` left join task_members_user tmu on tmu.task_id = task.id 
      
      `;
    }

    sql += ` where task.organization_id = ${user.organization.id}
    and task.status not in ('terminated','deleted')
    and (task.recurring_status is null or task.recurring_status = 'created')
    and task.parent_task_id is null
    `;

    if (query.fromDate && query.toDate) {
      const { startTime, endTime } = dateFormation(query.fromDate, query.toDate);
      sql += ` and Date(task.created_at) >= '${startTime}' and Date(task.created_at) <= '${endTime}'`;
    }

    if (query.dashboardType === 'user') {
      sql += ` and tmu.user_id = ${userId}`;
    }

    if (query.search) {
      sql += ` and s.name like '%${query.search}%'`;
    }

    sql += ` group by s.id having s.id is not null`;

    let countSql = `select count(s.id) from task ${sql}`;

    let resultSql = `select s.name, count(task.id) as count,
      sum(case when lh.id is not null then lh.duration else 0 end) as totalLogHours from task ${sql} 
      order by count desc
      limit ${query?.offset || 0}, ${query?.limit || 100}`;

    let count = await getManager().query(countSql);
    let result = await getManager().query(resultSql);
    return {
      totalCount: count.length,
      result,
    };
  }

  async exportTasksByServiceReport(userId: number, query: any) {
    let services = await this.getTasksByService(userId, query);
    let rows = services?.result.map((service) => {
      const totalLogHours = service.totalLogHours;
      const hours = Math.floor(totalLogHours / (1000 * 60 * 60));
      const remainingMilliseconds = totalLogHours % (1000 * 60 * 60);
      const minutes = Math.floor(remainingMilliseconds / (1000 * 60));
      const formattedHours = String(hours).padStart(2, '0');
      const formattedMinutes = String(minutes).padStart(2, '0');
      // Formatted result in HH:MM format
      const formattedTime = `${formattedHours}:${formattedMinutes}`;

      return {
        'Service Category': (service?.name)
          .split(' ')
          .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
          .join(' '),
        'Number of Tasks': service?.count,
        'Total Log Hours': formattedTime,
      };
    });

    if (rows !== undefined && rows.length) {
      const worksheet = xlsx.utils.json_to_sheet(rows);
      const workbook = xlsx.utils.book_new();
      xlsx.utils.book_append_sheet(workbook, worksheet, 'task-by-service');
      let file = xlsx.write(workbook, { type: 'buffer' });
      return file;
    } else {
      throw new BadRequestException('No Data for Export');
    }
  }

  async getOverDueTasks(userId: number, query: any) {
    let user = await User.findOne({ where: { id: userId }, relations: ['organization'] });

    let tasks = createQueryBuilder(Task, 'task')
      .leftJoin('task.organization', 'organization')
      .leftJoinAndSelect('task.category', 'category')
      .leftJoinAndSelect('task.members', 'members')
      .leftJoinAndSelect('task.client', 'client')
      .where('organization.id = :id', { id: user.organization.id })
      .andWhere('task.status not in (:...status)', {
        status: ['terminated', 'deleted', 'completed'],
      })
      .andWhere("(task.recurring_status is null or task.recurring_status = 'created')")
      .andWhere('task.dueDate < :date', { date: moment().format('YYYY-MM-DD') })
      .andWhere('task.parentTask is null')

      .orderBy('task.dueDate', 'ASC')
      .skip(query?.offset || 0)
      .take(query?.limit || 1000);

    if (query.search && query.search !== '') {
      tasks.andWhere('(client.slug like :search OR task.name like :search)', {
        search: `%${query.search}%`,
      });
    }

    if (query.dashboardType === 'user') {
      tasks.andWhere('members.id = :userId', { userId });
    }

    if (query.fromDate && query.toDate) {
      const { startTime, endTime } = dateFormation(query.fromDate, query.toDate);
      tasks.andWhere('task.dueDate >= :startTime', {
        startTime,
      });
      tasks.andWhere('task.dueDate <= :endTime', {
        endTime,
      });

    }


    let result = await tasks.getManyAndCount();

    return {
      totalCount: result[1],
      result: result[0],
    };
  }

  async exportOverDueTasksReport(userId: number, query: any) {
    let tasks = await this.getOverDueTasks(userId, query);
    let rows = tasks?.result.map((task) => {
      return {
        'Task ID': task?.taskNumber,
        'Client Name': task?.client?.displayName,
        'Task Name': task?.name,
        'Statutory Due Date': formatDate(task?.dueDate),
        'Over Due By': moment().diff(task?.dueDate, "days") + " days",
        'Status': getTitle(task?.status),
        'Members': task?.members.map((assignee) => assignee.fullName).join(', '),
      };
    });

    if (rows !== undefined && rows.length) {
      const worksheet = xlsx.utils.json_to_sheet(rows);
      const workbook = xlsx.utils.book_new();
      xlsx.utils.book_append_sheet(workbook, worksheet, 'over-due-tasks');
      let file = xlsx.write(workbook, { type: 'buffer' });
      return file;
    } else {
      throw new BadRequestException('No Data for Export');
    }
  }

  async getClientAnalytics(userId: number) {
    let user = await User.findOne({ where: { id: userId }, relations: ['organization'] });

    let clients = await Client.count({
      where: {
        organization: { id: user.organization.id },
        status: Not([UserStatus.DELETED]),
      },
    });

    let leads = await Lead.count({
      where: {
        organization: { id: user.organization.id },
      },
    });

    let converted = await Lead.count({
      where: {
        organization: { id: user.organization.id },
        status: LeadStatusEnum.CONVERTED,
      },
    });

    let notConverted = leads - converted;

    let convertedPercent = Math.round((converted / leads) * 100);
    let notConvertedPercent = Math.round((notConverted / leads) * 100);
    return {
      totalClients: clients,
      convertedLeads: converted,
      notConvertedLeads: notConverted,
      convertedLeadsPercent: convertedPercent || 0,
      notConvertedLeadsPercent: notConvertedPercent || 0,
    };
  }

  async getClientBillingAnalytics(query, userId: number) {
    let user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
    const sql = await getManager().query(`SELECT 
    c.id,
    c.display_name,
    COUNT(DISTINCT CASE WHEN t.billable=false THEN t.id END) AS nonbillabletasks,
    COUNT(DISTINCT CASE WHEN t.billable=true THEN t.id END) AS billabletasks,
    COUNT(DISTINCT CASE WHEN t.payment_status = 'UNBILLED' AND t.billable=true THEN t.id END) AS unbilledtasks,
    COUNT(DISTINCT t.id) AS totaltasks,
    COUNT(DISTINCT CASE WHEN t.payment_status = 'BILLED' AND t.billable=true THEN t.id END) AS billedtasks,
    COUNT(DISTINCT CASE WHEN t.payment_status = 'UNBILLED' AND t.billable=true THEN t.id END) AS unbilledtasks,
    IFNULL((SELECT SUM(CASE WHEN rc.type='CREDIT' THEN rc.amount ELSE 0 END) - SUM(CASE WHEN rc.type='DEBIT' THEN rc.amount ELSE 0 END)
    FROM receipt_credit rc WHERE rc.client_id =${query.clientId} AND rc.status='${ReceiptCreditStatus.CREATED}'), 0) AS credits,

    IFNULL((SELECT SUM(invoice.total_charges) FROM invoice  WHERE invoice.client_id = ${query.clientId} AND invoice.status!='${InvoiceStatus.CANCELLED}'), 0) AS pureagent,

    IFNULL((SELECT SUM(rp.pure_agent_amount) FROM receipt_particular rp INNER JOIN receipt r on rp.receipt_id=r.id where r.client_id=${query.clientId} and r.status ='${ReceiptParticularStatus.CREATED}' and rp.status='${ReceiptParticularStatus.CREATED}' ),0) AS pureagentreceived,

    (IFNULL((SELECT SUM(invoice.total_charges) FROM invoice  WHERE invoice.client_id = ${query.clientId} AND invoice.status!='${InvoiceStatus.CANCELLED}'), 0))-
    IFNULL((IFNULL((SELECT SUM(rp.pure_agent_amount) FROM receipt_particular rp INNER JOIN receipt r on rp.receipt_id=r.id where r.client_id=${query.clientId} and r.status='${ReceiptParticularStatus.CREATED}'  and rp.status='${ReceiptParticularStatus.CREATED}' ),0)),0) AS pureagentdue,

    IFNULL((SELECT ifnull(sum(invoice.grand_total)-sum(invoice.total_charges),0) FROM invoice WHERE invoice.client_id = ${query.clientId} AND invoice.status!='${InvoiceStatus.CANCELLED}' ), 0) AS serviceamount,

    IFNULL((SELECT SUM(rp.service_amount) FROM receipt_particular rp INNER JOIN receipt r on rp.receipt_id=r.id where r.client_id=${query.clientId} and r.status ='${ReceiptParticularStatus.CREATED}'  and rp.status='${ReceiptParticularStatus.CREATED}' ),0) AS serviceamountreceived,

    ( IFNULL((SELECT ifnull(sum(invoice.grand_total)-sum(invoice.total_charges),0) FROM invoice WHERE invoice.client_id = ${query.clientId} AND invoice.status!='${InvoiceStatus.CANCELLED}'), 0) )-
    ( IFNULL((SELECT SUM(rp.service_amount) FROM receipt_particular rp INNER JOIN receipt r on rp.receipt_id=r.id where r.client_id=${query.clientId} and r.status='${ReceiptParticularStatus.CREATED}'  and rp.status='${ReceiptParticularStatus.CREATED}' ),0)) AS serviceamountdue
FROM 
    client c 
    LEFT JOIN task t ON c.id = t.client_id 
    AND (t.recurring_status = 'created' OR t.recurring_status IS NULL) 
    AND t.status != 'terminated' 
    AND t.status != 'deleted' 
    AND t.parent_task_id IS NULL
WHERE 
    c.id = '${query.clientId}' 
     AND (t.billable is true OR t.id is null OR t.billable is false)
    AND c.status != 'deleted'
    AND c.organization_id = ${user.organization.id};`);

    const [{ id,
      display_name,
      nonbillabletasks,
      billabletasks,
      unbilledtasks,
      totaltasks,
      billedtasks,
      dueamount,
      total_count,
      credits,
      pureagent,
      pureagentreceived,
      pureagentdue,
      serviceamount,
      serviceamountreceived,
      serviceamountdue
    }] = sql

    return {
      id,
      display_name,
      nonbillabletasks,
      billabletasks,
      unbilledtasks,
      totaltasks,
      billedtasks,
      dueamount,
      total_count,
      credits,
      pureagent,
      pureagentreceived,
      pureagentdue,
      serviceamount,
      serviceamountreceived,
      serviceamountdue,
    };
  }

  async getDueDscRegisters(userId: number, query: any) {
    let user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
    let dscRegistersquery = await createQueryBuilder(DscRegister, 'dsc_register')
      .leftJoin('dsc_register.organization', 'organization')
      .leftJoinAndSelect('dsc_register.clients', 'clients')
      .where('organization.id = :id', { id: user.organization.id })
      .andWhere('dsc_register.expiryDate between :start and :end', {
        start: moment().isoWeekday(1).format('YYYY-MM-DD'),
        end: moment().isoWeekday(7).format('YYYY-MM-DD'),
      });

    if (query.clientId && query.clientId !== '') {
      dscRegistersquery.andWhere('clients.id = :clientId', { clientId: query.clientId })
    }

    const dscRegisters = dscRegistersquery.getMany();
    return dscRegisters;
  }

  async getClientsByCategory(userId: number) {
    let user = await User.findOne({ where: { id: userId }, relations: ['organization'] });

    let clients = await getManager().query(`
      select category,
      sum(case when status = 'ACTIVE' then 1 else 0 end) as activeClients,
      sum(case when status = 'INACTIVE' then 1 else 0 end) as inactiveClients
      from client
      where organization_id = ${user.organization.id}
      group by category;
    `);
    return clients;
  }

  async getClientsByCategoryExport(userId: number) {
    let clients = await this.getClientsByCategory(userId);
    let rows = clients?.map((client) => {
      return {
        'Category': client?.category
          ?.split('_')
          .map((client) => client.charAt(0).toUpperCase() + client.slice(1))
          .join(' '),
        'Active Clients': client?.activeClients,
        'Inactive Clients': client?.inactiveClients,
      };
    });

    if (rows !== undefined && rows.length) {
      const worksheet = xlsx.utils.json_to_sheet(rows);
      const workbook = xlsx.utils.book_new();
      xlsx.utils.book_append_sheet(workbook, worksheet, 'over-due-tasks');
      let file = xlsx.write(workbook, { type: 'buffer' });
      return file;
    } else {
      throw new BadRequestException('No Data for Export');
    }
  }

  async getTotalLogHours(userId: number, query) {
    let user = await User.findOne({ where: { id: userId }, relations: ['organization'] });

    let sql = `select sum(duration) as totalLogHours from log_hour
    left join user u on u.id = log_hour.user_id
    where u.organization_id = ${user.organization.id}
    and u.type = 'ORGANIZATION' 
  `;

    if (query.dashboardType === 'user') {
      sql += ` and u.id = ${userId}`;
    }
    if (query.fromDate && query.toDate) {
      const { startTime, endTime } = dateFormation(query.fromDate, query.toDate);
      sql += ` and Date(log_hour.completed_date) >= '${startTime}' and Date(log_hour.completed_date) <= '${endTime}'`;
    }

    let data = await getManager().query(sql);
    const milliseconds = data[0].totalLogHours;
    const hours = Math.floor(milliseconds / 1000 / 60 / 60);
    const minutes = Math.floor((milliseconds / 1000 / 60) % 60);
    const formattedTime = `${hours.toString().padStart(2, '0')}:${minutes
      .toString()
      .padStart(2, '0')}`;
    return formattedTime;
  }

  async getWeeklyLogHours(userId: number, query: IQueryWeeklyLogHoursDto) {
    let user = await User.findOne({ where: { id: userId }, relations: ['organization'] });

    let sql = `select date(lh.completed_date) as date, sum(lh.duration) as totalLogHours 
    from log_hour lh left join user u on u.id = lh.user_id 
    left join organization o on u.organization_id = o.id 
    where u.organization_id = ${user.organization.id}`;

    if (query.dashboardType === 'user') {
      sql += ` and u.id = ${userId}`;
    }

    const { startTime, endTime } = dateFormation(query.startDate, query.endDate);

    sql += ` and completed_date >= '${startTime}' and completed_date <= '${endTime}'
    group by date(completed_date);`;

    let data = await getManager().query(sql);
    const getDate = (add: number) => {
      return moment(query.startDate).add(add, 'day').format('DD-MM-YYYY');
    };

    const findData = (date: string) => {
      let result = data?.find((d: any) => moment(d.date).format('DD-MM-YYYY') === date);
      let logHours = result ? Math.round(result?.totalLogHours / 1000 / 60) : 0;
      return logHours;
    };

    let startDate = moment(query.startDate).format('DD-MM-YYYY');

    let result = {
      [startDate]: findData(startDate),
      [getDate(1)]: findData(getDate(1)),
      [getDate(2)]: findData(getDate(2)),
      [getDate(3)]: findData(getDate(3)),
      [getDate(4)]: findData(getDate(4)),
      [getDate(5)]: findData(getDate(5)),
      [getDate(6)]: findData(getDate(6)),
    };
    return result;
  }

  async getEmployeeTasksByStatus(userId: number, query: any) {
    let user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
    const date = moment().subtract(15, 'days').format('YYYY-MM-DD HH:mm:ss');

    let countSql = `select count(u.id) as count from task
        left join task_members_user tm on tm.task_id = task.id
        left join user u on u.id = tm.user_id
        where task.organization_id = ${user.organization.id} 
        and u.type = 'ORGANIZATION' 
        AND (task.status IN ('todo', 'in_progress', 'on_hold', 'under_review')
        OR (task.status = 'completed' AND task.status_updated_at >= STR_TO_DATE('${date}', '%Y-%m-%d %H:%i:%s'))) AND 
        task.parent_task_id is null and
        (task.recurring_status is null or task.recurring_status = 'created')`;

    if (query.dashboardType === 'user') {
      countSql += ` and u.id = ${userId}`;
    }

    if (query.fromDate && query.toDate) {
      countSql += ` and Date(task.task_start_date) >= '${moment(query.fromDate).format(
        'YYYY-MM-DD',
      )}' and Date(task.task_start_date) <= '${moment(query.toDate).format('YYYY-MM-DD')}'`;
    }

    countSql += ` group by u.id having u.id is not null`;

    let count = await getManager().query(countSql);

    let sql = `select u.id as id,u.full_name as fullName,
    sum(case when task.status IN ('todo','in_progress','on_hold','under_review') or (task.status='completed' AND task.due_date >=task.status_updated_at)  then 1 else 0 end) as assigined,
    sum(case when task.status IN ('todo','in_progress','on_hold','under_review') AND task.due_date<'${moment().format('YYYY-MM-DD').toString()}'  then 1 else 0 end) as overdue,
    sum(case when task.status = 'todo' AND task.due_date>='${moment().format('YYYY-MM-DD').toString()}' then 1 else 0 end) as todo,
    sum(case when task.status = 'in_progress' AND task.due_date>='${moment().format('YYYY-MM-DD').toString()}' then 1 else 0 end) as inProgress,
    sum(case when task.status = 'completed' AND task.due_date>='${moment().format('YYYY-MM-DD').toString()}' then 1 else 0 end) as completed,
    sum(case when task.status = 'on_hold' AND task.due_date>='${moment().format('YYYY-MM-DD').toString()}' then 1 else 0 end) as onHold,
    sum(case when task.status = 'under_review' AND task.due_date>='${moment().format('YYYY-MM-DD').toString()}' then 1 else 0 end) as underReview
    from task left join task_members_user tm on tm.task_id = task.id
    left join user u on u.id = tm.user_id
    where task.organization_id = ${user.organization.id} 
    AND u.type = 'ORGANIZATION' 
    AND (task.status IN ('todo', 'in_progress', 'on_hold', 'under_review')
    OR (task.status = 'completed' AND task.status_updated_at >= STR_TO_DATE('${date}', '%Y-%m-%d %H:%i:%s'))) AND 
    task.parent_task_id is null and
    (task.recurring_status is null or task.recurring_status = 'created')`;

    if (query.dashboardType === 'user') {
      sql += ` and u.id = ${userId}`;
    }

    if (query.fromDate && query.toDate) {
      const { startTime, endTime } = dateFormation(query.fromDate, query.toDate);
      sql += ` and Date(task.task_start_date) >= '${startTime}' and Date(task.task_start_date) <= '${endTime}'`;
    }

    sql += ` group by u.id having u.id is not null 
    limit ${query?.offset || 0}, ${query?.limit || 10}`;

    let result = await getManager().query(sql);

    return {
      totalCount: count.length,
      result,
    };
  }

  async exportEmployeeTasksByStatusReport(userId: number, query: any) {
    let tasks = await this.getEmployeeTasksByStatus(userId, query);
    let rows = tasks?.result.map((task) => {
      return {
        'Employee Name': task?.fullName,
        'Todo': task?.todo,
        'In Progress': task?.inProgress,
        'On Hold': task?.onHold,
        'Under Review': task?.underReview,
        'Completed': task?.completed,
        'Total': +task.todo + +task.inProgress + +task.onHold + +task.underReview + +task.completed
      };
    });

    if (rows !== undefined && rows.length) {
      const worksheet = xlsx.utils.json_to_sheet(rows);
      const workbook = xlsx.utils.book_new();
      xlsx.utils.book_append_sheet(workbook, worksheet, 'over-due-tasks');
      let file = xlsx.write(workbook, { type: 'buffer' });
      return file;
    } else {
      throw new BadRequestException('No Data for Export');
    }
  }

  async getEmployeeAttendance(userId: number, query: any) {
    const user = await User.findOne({ where: { id: userId }, relations: ['organization'] });

    const organizationPreferences = await OrganizationPreferences.findOne({
      where: { organization: user.organization },
      order: { id: 'DESC' },
    });

    let holiday = false;
    let holidayType;

    const day = moment(query?.date, 'YYYY-MM-DD').format('dddd');
    const holidayPreferences = organizationPreferences.holidayPreferences;

    const weekends = holidayPreferences['updateweekend']
      ? holidayPreferences['updateweekend'].map((item) => item.label)
      : [];

    holiday = weekends.includes(day) ? true : false;

    if (holiday) {
      holidayType = 'Weekend';
    }

    const holidayDates = holidayPreferences['addholiday'].map((item) =>
      moment(item.date).format('YYYY-MM-DD'),
    );

    if (!holiday) {
      holiday = holidayDates.includes(moment(query?.date).format('YYYY-MM-DD')) ? true : false;
      if (holiday) {
        holidayType = holidayPreferences['addholiday']?.filter(
          (item) =>
            moment(item.date).format('YYYY-MM-DD') === moment(query?.date).format('YYYY-MM-DD'),
        )[0]?.holiday;
      }
    }

    const userLimit = createQueryBuilder(User, 'user')
      .leftJoinAndSelect('user.organization', 'organization')
      .leftJoinAndSelect('user.role', 'role')
      .where('user.organization.id = :orgId', { orgId: user.organization.id })
      .andWhere('user.type = :type', { type: UserType.ORGANIZATION });

    if (query?.search) {
      userLimit.andWhere('user.fullName like :fullName', { fullName: `%${query?.search}%` });
    }

    if (query?.role) {
      userLimit.andWhere('role.name = :name', { name: query?.role });
    }

    const userList = await userLimit.getMany();

    const ActiveUserList = userList.filter((item) => item.status === UserStatus.ACTIVE);
    const InactiveUserList = userList.filter((item) => item.status === UserStatus.INACTIVE);
    const DeletedUserList = userList.filter((item) => item.status === UserStatus.DELETED);
    const newUserList = [...ActiveUserList, ...InactiveUserList, ...DeletedUserList];

    const getAttendance = await Attendance.find({
      where: {
        organization: user.organization,
        attendanceDate: moment(query?.date).format('YYYY-MM-DD'),
      },
    });

    const userAttendanceList = getAttendance.map((item) => item.userId);

    for (let useritem of newUserList) {
      if (userAttendanceList.includes(useritem.id)) {
        useritem['attendance'] = getAttendance.filter((item) => item.userId === useritem.id)[0];
      }

      if (holiday) {
        if (useritem['attendance']) {
          useritem['attendance'].type = 'Holiday';
          useritem['attendance'].description = holidayType;
        } else {
          useritem['attendance'] = {
            type: 'Holiday',
            description: holidayType,
          };
        }
      }
    }
    return newUserList;
  }

  //Service Dashboard
  async getServiceAnalytics(userId: number, query: any) {
    const { serviceId, financialYear } = query;
    let user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
    const date = moment().format('YYYY-MM-DD');
    let sql = `
    select status, count(task.id) as count,user_id,SUM(task.fee_amount) as task_fee_amount,
    sum(case when task.recurring = true then 1 else 0 end) as recurring,
    sum(case when task.recurring = false then 1 else 0 end) as non_recurring,
    sum(case when task.priority = 'high' then 1 else 0 end) as high,
    sum(case when task.priority = 'low' then 1 else 0 end) as low,
    sum(case when task.priority = 'medium' then 1 else 0 end) as medium,
    sum(case when task.priority = 'none' then 1 else 0 end) as none,
    SUM(CASE WHEN task.billable = true THEN 1 ELSE 0 END) AS billable_true,
    SUM(CASE WHEN task.billable = false THEN 1 ELSE 0 END) AS billable_false,
    SUM(CASE WHEN task.payment_status = 'UNBILLED' THEN 1 ELSE 0 END) AS unbilled_task,
    SUM(CASE WHEN task.payment_status = 'ESTIMATED' THEN 1 ELSE 0 END) AS estimated_task,
    SUM(CASE WHEN task.payment_status = 'BILLED' THEN 1 ELSE 0 END) AS billed_task,
    SUM(CASE WHEN task.due_date < '${date}' AND task.status NOT IN ('completed','terminated','deleted') THEN 1 ELSE 0 END) AS over_due_tasks
    from task`;

    sql += ` where organization_id = ${user.organization.id} and service_id = ${serviceId} `;
    if (financialYear) {
      sql += `and task.financial_year = '${financialYear}'`;
    }
    sql += ` and task.parent_task_id is null
    and (task.recurring_status is null or task.recurring_status = 'created')
    and task.status <> ''
    group by task.status;`;

    let tasks = await getManager().query(sql);

    const totalClientsInOrgSql = `SELECT COUNT(id) AS total_clients FROM client WHERE organization_id = ${user?.organization?.id}`;
    let totalClientsRes = await getManager().query(totalClientsInOrgSql);

    const serviceAvailedClientsSql = ` SELECT COUNT(DISTINCT(client_id)) AS total_clients_availed FROM task WHERE organization_id = ${user?.organization?.id} AND service_id = ${serviceId} AND  (task.recurring_status is null or task.recurring_status = 'created') AND task.parent_task_id is NULL;`;
    let totalAvaliedClients = await getManager().query(serviceAvailedClientsSql);

    //LOGHOURS USER WISE
    let loghourSql = `
    SELECT 
          u.full_name,
          l.user_id,SUM(l.duration) as duration
    FROM
          service s INNER JOIN task t ON s.id=t.service_id
    INNER JOIN 
          log_hour l ON l.task_id=t.id
    INNER JOIN 
          user u ON l.user_id=u.id
    WHERE 
          s.id=${serviceId}
          AND t.organization_id=${user.organization.id}
          AND l.type='${LogHourType.TASK}'`;

    if (financialYear) {
      loghourSql += ` and t.financial_year = '${financialYear}'`;
    }

    loghourSql += `GROUP BY l.user_id
     ORDER BY duration DESC
     LIMIT 5
     `;

    let loghourRes = await getManager().query(loghourSql);

    let budgetedLoghoursSql = `SELECT 
            SUM(b.budgeted_hours) as total_budgeted_hours
        FROM
             task t 
        INNER JOIN 
            budgeted_hours b ON b.task_id=t.id 
        WHERE 
            t.service_id=${serviceId}
            AND t.organization_id=${user.organization.id}
            AND t.parent_task_id IS NULL
            AND b.status = '${BudgetedHourStatus.ACTIVE}'
            AND (t.recurring_status is null or t.recurring_status = 'created')
            AND t.status <> ''`;

    if (financialYear) {
      budgetedLoghoursSql += ` AND t.financial_year = '${financialYear}'`;
    }

    let budgetedAndLoghoursRes = await getManager().query(budgetedLoghoursSql);

    let logSql = ` SELECT 
          SUM(l.duration) as total_log_hours
        FROM
          task t 
        INNER JOIN 
          log_hour l ON l.task_id = t.id
        WHERE 
          t.service_id=${serviceId}
          AND t.organization_id=${user.organization.id}
          AND 
          l.type = '${LogHourType.TASK}'
          AND t.parent_task_id IS NULL
          AND (t.recurring_status is null or t.recurring_status = 'created')
          AND t.status <> ''`;

    if (financialYear) {
      logSql += ` AND t.financial_year = '${financialYear}'`;
    }

    let logSqlRes = await getManager().query(logSql);



    const budgetedAndLoghours = {
      total_budgeted_hours: budgetedAndLoghoursRes.length > 0 ? budgetedAndLoghoursRes[0].total_budgeted_hours : null,
      total_log_hours: logSqlRes?.length > 0 ? logSqlRes[0].total_log_hours : null
    }

    let remarkStatusSql = `
    SELECT 
        sum(case when a.remark_type = 'pending_at_department' then 1 else 0 end) as pen_at_dept,
        sum(case when a.remark_type = 'pending_at_client' then 1 else 0 end) as pen_at_clin,
        sum(case when a.remark_type = 'others' then 1 else 0 end) as other
    from 
        (
          SELECT 
              type_id,
              MAX(id) AS latest_activity_id
          FROM
              activity
          WHERE 
              type = 'task'
          GROUP BY
              type_id
        ) latest_activities
    INNER JOIN 
        activity a ON latest_activities.latest_activity_id = a.id
    INNER JOIN 
        task t ON a.type_id = t.id
    WHERE  
        t.organization_id = ${user.organization.id} and t.service_id = ${serviceId} AND a.type = 'task' AND t.status = 'on_hold' `;

    if (financialYear) {
      remarkStatusSql += ` and t.financial_year = '${financialYear}'`;
    }
    let remarksStatus = await getManager().query(remarkStatusSql);

    const remarks = remarksStatus?.length > 0 ? remarksStatus[0] : null;

    let expenditureAmountsSql = `
      SELECT 
            SUM(e.amount) AS total_amount,e.task_expense_type
      FROM 
            expenditure e 
      LEFT JOIN 
            task t
      ON 
          t.id = e.task_id 
      WHERE
            t.organization_id = ${user.organization.id} 
            AND t.service_id = ${serviceId}
            AND e.type = 'TASK'`;

    if (financialYear) {
      expenditureAmountsSql += ` AND t.financial_year = '${financialYear}'`;
    }

    expenditureAmountsSql += `GROUP BY
            e.task_expense_type
      `;

    let expenditureAmount = await getManager().query(expenditureAmountsSql);

    let recurringTasks = 0;
    let nonRecurringTasks = 0;
    let billableTasks = 0;
    let nonBillableTasks = 0;
    let unbilledTasks = 0;
    let billedtasks = 0;
    let estimatedTasks = 0;
    let totalClients = 0;
    let totalClientsAvalied = 0;
    let totalClientsUnavalied = 0;
    let total_fee_amount = 0;
    let overDueCount = 0;
    let tasksByStatus: any = {
      todo: 0,
      in_progress: 0,
      under_review: 0,
      on_hold: 0,
      completed: 0,
      terminated: 0,
      deleted: 0,
    };
    let tasksByPriority: any = {
      high: 0,
      low: 0,
      medium: 0,
      none: 0,
    };

    totalAvaliedClients.map((i) => {
      totalClientsAvalied = parseInt(i.total_clients_availed);
    });

    totalClientsRes.map((i) => {
      totalClients = parseInt(i.total_clients);
    });

    tasks.forEach((task: any) => {
      tasksByStatus[task.status] = +task.count || 0;
      tasksByPriority['high'] += +task['high'] || 0;
      tasksByPriority['low'] += +task['low'] || 0;
      tasksByPriority['medium'] += +task['medium'] || 0;
      tasksByPriority['none'] += +task['none'] || 0;
      total_fee_amount += +task.task_fee_amount || 0;
      recurringTasks += +task.recurring || 0;
      nonRecurringTasks += +task.non_recurring || 0;
      billableTasks += +task.billable_true || 0;
      nonBillableTasks += +task.billable_false || 0;
      billedtasks += +task.billed_task || 0;
      estimatedTasks += +task.estimated_task || 0;
      unbilledTasks += +task.unbilled_task || 0;
      overDueCount += +task.over_due_tasks || 0;
    });

    let totalTasks = recurringTasks + nonRecurringTasks;
    let recurringTasksPercentage = Math.round((recurringTasks / totalTasks) * 100);
    let nonRecurringTasksPercentage = Math.round((nonRecurringTasks / totalTasks) * 100);
    return {
      total:
        tasksByStatus.todo +
        tasksByStatus.in_progress +
        tasksByStatus.under_review +
        tasksByStatus.on_hold +
        tasksByStatus.completed +
        tasksByStatus.deleted +
        tasksByStatus.terminated,
      tasksByStatus,
      tasksByPriority,
      recurringTasks,
      nonRecurringTasks,
      recurringTasksPercentage: recurringTasksPercentage || 0,
      nonRecurringTasksPercentage: nonRecurringTasksPercentage || 0,
      billableTasks,
      nonBillableTasks,
      billedtasks,
      unbilledTasks,
      estimatedTasks,
      totalClients,
      totalClientsAvalied,
      totalClientsUnavalied: totalClients - totalClientsAvalied,
      loghourRes,
      remarks,
      total_fee_amount,
      expenditureAmount,
      overDueCount,
      budgetedAndLoghours,
    };
  }
}
