import { NextInvoiceNumberDto } from "src/modules/billing/dto/find-invoices.dto";
import { Invoice, InvoiceType } from "src/modules/billing/entitities/invoice.entity";
import { ProformaInvoice } from "src/modules/billing/entitities/proforma-invoice.entity";
import Receipt from "src/modules/billing/entitities/receipt.entity";
import OrganizationPreferences from "src/modules/organization-preferences/entity/organization-preferences.entity";
import { BillingEntity } from "src/modules/organization/entities/billing-entity.entity";
import { Organization } from "src/modules/organization/entities/organization.entity";
import { User } from "src/modules/users/entities/user.entity";
import { Like } from "typeorm";

export function generateClientId(id: number) {
  if (id < 10000) {
    return 'VD' + id.toString().padStart(4, '0');
  }
  return 'VD' + id;
}

export async function generateInvoiceNumber(userId, id: number) {

  //   if (id < 10000) {
  //     return 'INV' + id.toString().padStart(4, '0');
  //   }
  //   return 'INV' + id;
  // }
  let user = await User.findOne({ where: { id: userId }, relations: ['organization'] });

  let organization = await Organization.findOne({ where: { id: user.organization.id } });

  const organizationPreferences = await OrganizationPreferences.findOne({
    where: { organization },
    order: { id: 'DESC' },
  });
  if (id < 10000) {
    return organizationPreferences.invoicePreferences["invoicePrefix"] + id.toString().padStart(4, '0');
  }
  return organizationPreferences.invoicePreferences["invoicePrefix"] + id;
}

export function generateEmpoyeeId(org: string, id: number) {
  if (id < 10000) {
    return org.slice(0, 3).toUpperCase() + id.toString().padStart(4, '0');
  }
  return org.slice(0, 3).toUpperCase() + id;
}

export function generateEstimateId(id: number) {
  if (id < 10000) {
    return 'EST-' + id.toString().padStart(4, '0');
  }
  return 'EST' + id;
}

export async function generateInvoiceId(userId: number, id: number, query: NextInvoiceNumberDto, zeroLength) {
  const billingEntity = await BillingEntity.findOne({ where: { id: query.billingEntity } });
  const prefix = query.type === InvoiceType.INVOICE ? billingEntity?.prefix : billingEntity?.proformaPrefix;
  const prefixNumber = query.type == InvoiceType.INVOICE ? billingEntity.prefixNumber : billingEntity?.proformaPrefixNumber;
  let UsedInvNumbers: (Invoice | ProformaInvoice)[] = [];
  if (prefix) {
    if (query.type === InvoiceType.INVOICE) {
      UsedInvNumbers = await Invoice.find({
        where:
          { billingEntity: query.billingEntity, invoiceNumber: Like(`${prefix}%`) },
        select: ['invoiceNumber']
      });
    } else if (query.type === InvoiceType.PROFORMA) {
      UsedInvNumbers = await ProformaInvoice.find({
        where:
          { billingEntity: query.billingEntity, invoiceNumber: Like(`${prefix}%`) },
        select: ['invoiceNumber']
      });
    }


    const existingNumbers: string[] = UsedInvNumbers.map((item) => item.invoiceNumber)
    let nextInvNumber = parseInt(prefixNumber);
    while (existingNumbers.includes(prefix + zeroLength + nextInvNumber.toString().padStart(1, '0'))) {
      nextInvNumber++;
    }
    return { prifix: prefix, number: zeroLength + nextInvNumber.toString().padStart(1, '0') };
  } else {
    return null;
  }

}

export async function getnerateReceiptNumber(userId: number, id: number, billing: number, zeroLength) {
  const billingEntity = await BillingEntity.findOne({ where: { id: billing } });
  if (billingEntity?.receiptPrefix) {
    const UsedRecNumbers = await Receipt.find({
      where:
        { billingEntity: billing, receiptNumber: Like(`${billingEntity.receiptPrefix}%`) },
      select: ['receiptNumber']
    });
    const existingNumbers: string[] = UsedRecNumbers.map((item) => item.receiptNumber);
    let nextRecNumber = parseInt(billingEntity.receiptPrefixNumber);
    while (existingNumbers.includes(billingEntity?.receiptPrefix + zeroLength + nextRecNumber.toString().padStart(1, '0'))) {
      nextRecNumber++;
    }
    return { prifix: billingEntity?.receiptPrefix, number: zeroLength + nextRecNumber.toString().padStart(1, '0') };


  } else {
    return null;
  }


}

// export function getnerateReceiptNumber(count: number) {
//   if (count < 10000) {
//     return 'REC-' + count.toString().padStart(4, '0');
//   }
//   return 'REC' + count;
// }

export const getTitle = (key: string) => {
  key = key || '';
  return key
    ?.split('_')
    .map((item) => item.charAt(0).toUpperCase() + item.slice(1))
    .join(' ');
};

export const formatDate = (dateString) => {
  if (!dateString) return ' '; 
  
  const date = new Date(dateString);
  if (isNaN(date.getTime())) return ' ';

  const day = date.getDate().toString().padStart(2, '0');
  const month = (date.getMonth() + 1).toString().padStart(2, '0');
  const year = date.getFullYear();
  
  return `${day}-${month}-${year}`;
};


export const getDuration = (milliseconds: number) => {
  let seconds = milliseconds / 1000;
  let minutes = seconds / 60;
  let hours = Math.floor(minutes / 60);
  let remainingMinutes = Math.floor(minutes % 60);
  let hoursString = hours < 10 ? `0${hours}` : hours;
  let minutesString = remainingMinutes < 10 ? `0${remainingMinutes}` : remainingMinutes;
  return `${hoursString}:${minutesString}`;
};

export const getHours = (milliseconds: number) => {
  return Math.round(+milliseconds / 1000 / 60 / 60);
};
