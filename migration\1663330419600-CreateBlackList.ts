import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateBlackList1663330419600 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `create table black_list (
        id int not null auto_increment,
        token varchar(255) not null,
        created_at datetime(6) default current_timestamp(6),
        primary key (id)
      );`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}
