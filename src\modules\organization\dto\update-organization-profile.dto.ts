import { IsNotEmpty, IsOptional } from 'class-validator';

export class UpdateOrganizationProfileDto {
  @IsOptional()
  legalName: string;

  @IsOptional()
  tradeName: string;

  @IsOptional()
  constitutionOfBusiness: string;

  @IsOptional()
  placeOfSupply: string;

  @IsOptional()
  firstName: string;

  @IsOptional()
  middleName: string;

  @IsOptional()
  lastName: string;

  @IsOptional()
  mobileNumber: string;

  @IsOptional()
  alternateMobileNumber: string;

  @IsOptional()
  gstVerified: boolean;

  @IsOptional()
  gstNumber: string;

  @IsOptional()
  gstAttachment: string;

  @IsOptional()
  logo: string;

  @IsOptional()
  panVerified: boolean;

  @IsOptional()
  panNumber: string;

  @IsOptional()
  panAttachment: string;

  @IsOptional()
  buildingName: string;

  @IsOptional()
  buildingNumber: string;

  @IsOptional()
  street: string;

  @IsOptional()
  district: string;

  @IsOptional()
  location: string;

  @IsOptional()
  floorNumber: string;

  @IsOptional()
  state: string;

  @IsOptional()
  city: string;

  @IsOptional()
  pincode: string;

  @IsOptional()
  website: string;

  @IsOptional()
  email: string;

  @IsOptional()
  category: string;

  @IsOptional()
  primaryContactFullName: string;

  @IsOptional()
  fullName: string;

  @IsOptional()
  primaryContactEmail: string;

  @IsOptional()
  primaryContactMobileNumber: string;

  @IsOptional()
  registrationNumber: string;

  @IsOptional()
  registrationDate: string;

  @IsOptional()
  config: string;

  @IsOptional()
  prefix: string;

  @IsOptional()
  prefixNumber: string;

  @IsOptional()
  creditPeriod: string;

  @IsOptional()
  terms: any;

  @IsOptional()
  autoGenerate: boolean;

  @IsOptional()
  smtp: string;

  @IsOptional()
  orgGstStorage: any;

  @IsOptional()
  orgPanStorage: any;

  @IsOptional()
  loggedIn: string;

  @IsOptional()
  primaryContactDesignation: string;

  @IsOptional()
  udinUsers: any;

  @IsOptional()
  countryCode: string;

  @IsOptional()
  alternateCountryCode: string;

  @IsOptional()
  primaryContactCountryCode: string;
}
