import { Injectable } from '@nestjs/common';
import { OnEvent } from '@nestjs/event-emitter';
import { sendNotification } from 'src/notifications/notify';
import { User } from 'src/modules/users/entities/user.entity';
import { createQueryBuilder } from 'typeorm';
import Task from '../../modules/tasks/entity/task.entity';
import { Event_Actions } from '../actions';

interface CreateTask {
  actor?: string;
  userId: number;
  clientId: number;
  task: Task;
}

@Injectable()
export class SubTaskListener {
  @OnEvent(Event_Actions.SUBTASK_CREATED, { async: true })
  async onSubtaskCreated(event: CreateTask) {
    const { task } = event;
    try {
      let taskData = await createQueryBuilder(Task, 'task')
        .leftJoinAndSelect('task.parentTask', 'parentTask')
        .leftJoinAndSelect('task.organization', 'organization')
        .leftJoinAndSelect('task.client', 'client')
        .leftJoinAndSelect('task.clientGroup', 'clientGroup')
        .leftJoinAndSelect('client.clientManager', 'clientManager')
        .leftJoinAndSelect('task.user', 'user')
        .leftJoinAndSelect('task.members', 'members')
        .leftJoinAndSelect('organization.users', 'users')
        .leftJoinAndSelect('users.role', 'role')
        .where('task.id = :taskId', { taskId: task.id })
        .getOne();

      let memberIds = taskData.members.map((member: User) => member.id);

      let orgAdmin = taskData.organization.users.find(
        (user: User) => user.role.defaultRole,
      );

      let clientManager = taskData.client.clientManager;

      let userIds = [...memberIds];

      if (orgAdmin) {
        userIds.push(orgAdmin.id);
      }

      if (clientManager) {
        userIds.push(clientManager.id);
      }
      let notification = {
        title: 'Subtask created',
        body: `Subtask "${taskData.name}" for the task "${taskData?.parentTask?.name}" has been created by ${taskData.user.fullName}`,
      };

      // await sendNotification(userIds, notification);

    } catch (err) {
      console.log(err);
    }
  }
}
