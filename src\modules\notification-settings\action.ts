export const Notification_Actions = {
  Mail_Notification: {
    CLIENT_CREATE_MAIL: 'Client Creation',
    NEW_LEAD_CREATED_MAIL: 'Lead Creation',
    COMMENT_ADDED_MAIL: 'Comment Added',
    CLIENT_CREADENTIAL_MAIL: 'Client Credentials Updated',
    TASK_STATUS_UPDATED_ASSIGNED_MAIL: 'Task Status Changed',
    DSC_ACTIVITY_ISSUE_MAIL: 'DSC issued',
    DSC_ACTIVITY_RECEIVED_MAIL: 'DSC received',
    NEW_TASK_DELETED_MAIL: 'Task Deleted',
    LEAD_TO_CLIENT_CONVERT_MAIL: 'Lead Converted',
    EXPORT_INVOICES_MAIL: 'Export Invoices',
    INVOICE_CREATED_MAIL: 'Invoices Created',
    INVOICE_CANCELLED_MAIL: 'Invoices Cancelled',
    // INVOICE_UPDATED_MAIL: "Invoice Edited",
    INVOICE_OVERDUE_MAIL: 'Invoice Overdue',
    RECEIPT_ADDED_MAIL: 'Receipt Created',
    REPORT_GENERATED_MAIL: 'Report Generated',
    TASK_CREATION_MAIL: 'New Task Created',
    // USER_INVITED_MAIL: "User Invited",
    //USER_SIGNED_UP_MAIL: "User Signed Up",
    //RESET_PASSWORD_MAIL: "Reset Password",
    //RECURRING_PROFILE_TERMINATED_MAIL: "Recurring Profile Terminated",
    //GENERAL_EVENT_CREATED_MAIL: "General Event Created",
    //CLIENT_DELETED_MAIL: "Client Deleted",
    //UNDERSPENT_WORKING_HOURS_MAIL: "Underspent Working Hours",
    //OVERSPENT_WORKING_HOURS_MAIL: "Overspent Working Hours",
    // TASK_EVENT_CREATED_MAIL: "Task Related Event Created",
  },

  Push_Notification: {
    ORGANIZATION_USER_INVITED_PUSH: 'Organization User Invited',
    USER_SIGNED_UP_PUSH: 'User Signed Up',
    CLIENT_CREATED_PUSH: 'Client Created',
    CLIENT_PROFILE_UPDATION_PUSH: 'Client Profile Updation',
    CLIENT_CREDENTIALS_ADDED_PUSH: 'Client Credential Added',
    CLIENT_CREDENTIALS_UPDATED_PUSH: 'Client Credentials Updated',
    CREDENTIALS_HAVE_BEEN_DELETED_PUSH: 'Credentials Have been Deleted',
    CLIENT_STATUS_HAS_BEEN_UPDATED_PUSH: 'Client Status Has Been Updated',
    USER_PROFILE_UPDATION_PUSH: 'User Profile Updation',
    // USER_DETAILS_UPDATION_PUSH: "User Details Updation",
    USER_ADDED_TO_ALREADY_CREATED_TASK_PUSH: 'User Added To Already Created Task',
    USER_REMOVED_FROM_ALREADY_CREATED_TASK_PUSH: 'User Removed From Already Created Task',
    USER_STATUS_DEACTIVATED_PUSH: 'User Status Deactivated',
    USER_STATUS_ACTIVATED_PUSH: 'User Status Activated',
    DSC_ISSUED_PUSH: 'DSC issued',
    DSC_RECEIVED_PUSH: 'DSC received',
    DSC_ADDED_PUSH: 'DSC Added',
    TASK_CREATED_PUSH: 'Task Created',
    TASK_STATUS_CHANGE_PUSH: 'Task Status Change',
    TASK_COMMENTS_PUSH: 'Task Comments',
    TASK_EVENT_CREATION_PUSH: 'Task Event Creation',
    TASK_DUE_DATE_CHANGED_PUSH: 'Task Due Date Changed',
    TASK_DELETED_PUSH: 'Task Deleted',
    TASK_TERMINATED_PUSH: 'Task Terminated',
    TASK_EXPENDITURE_PUSH: 'Task Expenditure',
    TASK_ATTACHMENT_UPLOAD: 'Task Attachments Uploaded',
    SUBTASK_CREATED_PUSH: 'Subtasks Created',
    CHECKLIST_ITEM_ADDED_PUSH: 'Checklist Item Added',
    // CHECKLIST_ITEM_EDITED_PUSH: "Checklist Item Edited",
    // CHECKLIST_ITEM_STATUS_CHANGED_PUSH: "Checklist Item Status Changed",
    PRIORITY_CHANGING_PUSH: 'Priority Changing',
    LEAD_CREATED_PUSH: 'Lead Created',
    DELETION_OF_USER_PUSH: 'Deletion Of User',
    FEE_PUSH: 'Fee',
    EXPORTED_INVOICES_PUSH: 'Export Invoices',
    INVOICE_CREATED_PUSH: 'Invoices Created',
    INVOICE_CANCELLED_PUSH: 'Invoice Cancelled',
    INVOICE_DOWNLOADED_PUSH: 'Invocies Downloaded',
    GENERATED_PUSH: 'Report Generated',
    RECEIPT_CREATED_PUSH: 'Receipt Created',
    // INVOICE_EDITED_PUSH: "Invoice Edited",
    INVOICE_OVERDUE_PUSH: 'Invoice Overdue',
    DELETED_USER_RESTORATION_PUSH: 'Deleted User Restoration',
    NEW_MESSAGE_RECEIVED_PUSH: 'New Chat Message Received',
    COLLECT_DATA: 'Collect Data',
    //RECURRING_PROFILE_EDIT_PUSH: "Recurring Profile Edit",
    //TASK_RESTORED_PUSH: "Task Restored",
    //DELIVERABLES_TASK_COMPLETED_PUSH: "Deliverables",
    //CLIENT_PASSWORDS_PUSH: "Client Passwords",
    //CHANGE_IN_TASK_MEMBERS_ADDITION_PUSH: "Change In Task Members Addition",
    //DELETION_PUSH: "Deletion",
    //CHANGE_IN_TASK_PARTICULARS_PUSH: "Change In Task Particulars",
    //DSC_EXPIRY_PUSH: "DSC Expiry",
    //DELETION_OF_CLIENT_PUSH: "Deletion Of Client",
    //ORGANISTAION_DETAILS_UPDATED_PUSH: "Organisation Details Updated",
    //REPORT_EXPORTED_TO_EXCEL_PUSH: "Report Exported To Excel",
    //DSC_DELETED_PUSH: "DSC Deleted",
    //CHECKLIST_SELECT_ALL_PUSH: "Checklist Select All",
  },
};

export const NotificationAction = {
  'User': {
    // 'Organisation User Invited': {
    //   ORG_USER_INVITED_PUSH: 'none',
    //   ORG_USER_INVITED_MAIL: 'none',
    //   ORG_USER_INVITED_WHATSAPP: 'Organisation User Invited',
    // },
    // 'User Signed Up': {
    //   USER_SIGNED_UP_PUSH: 'none',
    //   USER_SIGNED_UP_MAIL: 'none',
    //   USER_SIGNED_UP_WHATSAPP: 'User Signed Up',
    // },
    'User Profile Updation': {
      USER_PROFILE_UPDATION_PUSH: 'User Profile Updation',
      USER_PROFILE_UPDATION_MAIL: 'none',
      USER_PROFILE_UPDATION_WHATSAPP: 'User Profile Updation',
    },
    // 'User Added To Already Created Task': {
    //   USER_ADDED_TO_ALREADY_CREATED_TASK_PUSH: 'User Added To Already Created Task',
    //   USER_ADDED_TO_ALREADY_CREATED_TASK_MAIL: 'User Added To Already Created Task',
    //   USER_ADDED_TO_ALREADY_CREATED_TASK_WHATSAPP: 'User Added To Already Created Task',
    // },
    // 'User Removed From Already Created Task': {
    //   USER_REMOVED_FROM_ALREADY_CREATED_TASK_PUSH: 'User Removed From Already Created Task',
    //   USER_REMOVED_FROM_ALREADY_CREATED_TASK_MAIL: 'User Removed From Already Created Task',
    //   USER_REMOVED_FROM_ALREADY_CREATED_TASK_WHATSAPP: 'User Removed From Already Created Task',
    // },
    'User Status Deactivated': {
      USER_STATUS_DEACTIVATED_PUSH: 'User Status Deactivated', //complete
      USER_STATUS_DEACTIVATED_MAIL: 'User Status Deactivated', // no template
      USER_STATUS_DEACTIVATED_WHATSAPP: 'User Status Deactivated',
    },
    'User Status Activated': {
      USER_STATUS_ACTIVATED_PUSH: 'User Status Activated', //complete
      USER_STATUS_ACTIVATED_MAIL: 'User Status Activated', // no template
      USER_STATUS_ACTIVATED_WHATSAPP: 'User Status Activated',
    },
    'Deletion of User': {
      DELETION_OF_USER_PUSH: 'Deletion of User', //complete
      USER_DELETION_MAIL: 'Deletion of User', // no template
      USER_DELETION_WHATSAPP: 'Deletion of User',
    },
    'Deleted User Restoration': {
      DELETED_USER_RESTORATION_PUSH: 'Deleted User Restoration', //complete
      DELETED_USER_RESTORATION_MAIL: 'Deleted User Restoration', // no template
      DELETED_USER_RESTORATION_WHATSAPP: 'Deleted User Restoration',
    },
  },
  'Client': {
    'Lead Created': {
      LEAD_CREATED_PUSH: 'Lead Created',
      NEW_LEAD_CREATED_MAIL: 'Lead Creation',
      LEAD_CREATED_WHATSAPP: 'Lead Created',
    },
    'Lead Converted': {
      LEAD_CONVERTED_PUSH: 'Lead Converted', // not
      LEAD_TO_CLIENT_CONVERT_MAIL: 'Lead Converted', // complete
      LEAD_CONVERTED_WHATSAPP: 'Lead Converted',
    },
    'Client Created': {
      CLIENT_CREATED_PUSH: 'Client Created',
      CLIENT_CREATE_MAIL: 'Client Creation',
      CLIENT_CREATED_WHATSAPP: 'Client Created',
    },
    'Client Profile Updation': {
      CLIENT_PROFILE_UPDATION_PUSH: 'Client Profile Updation',
      CLIENT_PROFILE_UPDATION_MAIL: 'none',
      CLIENT_PROFILE_UPDATION_WHATSAPP: 'Client Profile Updation',
    },
    'Client Credentials Added': {
      CLIENT_CREDENTIALS_ADDED_PUSH: 'Client Credential Added',
      CLIENT_CREDENTIALS_ADDED_MAIL: 'Client Credential Added',
      CLIENT_CREDENTIALS_ADDED_WHATSAPP: 'Client Credential Added',
    },
    'Client Credentials Updated': {
      CLIENT_CREDENTIALS_UPDATED_PUSH: 'Client Credentials Updated',
      CLIENT_CREDENTIALS_UPDATED_MAIL: 'Client Credentials Updated',
      CLIENT_CREDENTIALS_UPDATED_WHATSAPP: 'Client Credentials Updated',
    },
    'Client Credentials Deleted': {
      CREDENTIALS_HAVE_BEEN_DELETED_PUSH: 'Credentials Have been Deleted', //complete
      CREDENTIALS_DELETED_MAIL: 'Credentials Have Been Deleted', // no template
      CREDENTIALS_DELETED_WHATSAPP: 'Credentials Have Been Deleted',
    },
    'Client Status Change': {
      CLIENT_STATUS_HAS_BEEN_UPDATED_PUSH: 'Client Status Updated',
      CLIENT_STATUS_UPDATED_MAIL: 'Client Status Updated',
      CLIENT_STATUS_UPDATED_WHATSAPP: 'Client Status Updated',
    },
    'Client Deleted': {
      CLIENT_DELETED_PUSH: 'none', //complete
      CLIENT_DELETED_MAIL: 'none',
      CLIENT_DELETED_WHATSAPP: 'Client Deleted',
    },
  },
  'Client Group': {
    'Client Group Created': {
      CLIENT_GROUP_CREATED_PUSH: 'Client Group Created',
      CLIENT_GROUP_CREATE_MAIL: 'none',
      CLIENT_GROUP_CREATED_WHATSAPP: 'Client Group Created',
    },
    'Client Group Profile Updation': {
      CLIENT_GROUP_PROFILE_UPDATION_PUSH: 'Client Group Profile Updation',
      CLIENT_PROFILE_UPDATION_MAIL: 'none',
      CLIENT_GROUP_PROFILE_UPDATION_WHATSAPP: 'Client Group Profile Updation',
    },
    'Client Group Status Change': {
      CLIENT_GROUP_STATUS_HAS_BEEN_UPDATED_PUSH: 'Client Group Status Updated',
      CLIENT_GROUP_STATUS_UPDATED_MAIL: 'none',
      CLIENT_GROUP_STATUS_UPDATED_WHATSAPP: 'Client Group Status Updated',
    },
  },
  'Task': {
    'Task Created': {
      TASK_CREATED_PUSH: 'Task Created',
      TASK_CREATION_MAIL: 'New Task Created',
      TASK_CREATED_WHATSAPP: 'Task Created',
    },
    'User Added To Already Created Task': {
      USER_ADDED_TO_ALREADY_CREATED_TASK_PUSH: 'User Added To Already Created Task', //complete
      USER_ADDED_TO_ALREADY_CREATED_TASK_MAIL: 'User Added To Already Created Task', // no mail
      USER_ADDED_TO_ALREADY_CREATED_TASK_WHATSAPP: 'User Added To Already Created Task',
    },
    'User Removed From Already Created Task': {
      USER_REMOVED_FROM_ALREADY_CREATED_TASK_PUSH: 'User Removed From Already Created Task', //complete
      USER_REMOVED_FROM_ALREADY_CREATED_TASK_MAIL: 'User Removed From Already Created Task', // no mail
      USER_REMOVED_FROM_ALREADY_CREATED_TASK_WHATSAPP: 'User Removed From Already Created Task',
    },
    'Priority Changing': {
      PRIORITY_CHANGING_PUSH: 'Priority Changing', // complete
      PRIORITY_CHANGING_MAIL: 'Priority Changing', // no template
      PRIORITY_CHANGING_WHATSAPP: 'Priority Changing',
    },
    'Service Fee': {
      FEE_PUSH: 'Fee', //complete
      FEE_MAIL: 'Fee', // no template
      FEE_WHATSAPP: 'Fee',
    },
    'Task Status Change': {
      TASK_STATUS_CHANGE_PUSH: 'Task Status Change',
      TASK_STATUS_CHANGE_MAIL: 'Task Status Change',
      TASK_STATUS_CHANGE_WHATSAPP: 'Task Status Change Whatsapp',
    },
    'Checklist Added': {
      CHECKLIST_ADDED_PUSH: 'Checklist Added',
      CHECKLIST_ADDED_MAIL: 'Checklist Added',
      CHECKLIST_ADDED_WHATSAPP: 'none',
    },
    'Checklist Item Added': {
      CHECKLIST_ITEM_ADDED_PUSH: 'Checklist Item Added',
      CHECKLIST_ITEM_ADDED_MAIL: 'Checklist Item Added',
      CHECKLIST_ITEM_ADDED_WHATSAPP: 'none',
    },
    'Task Comments': {
      TASK_COMMENTS_PUSH: 'Task Comments',
      COMMENT_ADDED_MAIL: 'Comment Added',
      TASK_COMMENTS_WHATSAPP: 'Task Comment Whatsapp',
    },
    'Task Event Creation': {
      TASK_EVENT_CREATION_PUSH: 'Task Event Creation',
      TASK_EVENT_CREATION_MAIL: 'Task Event Creation',
      TASK_EVENT_CREATION_WHATSAPP: 'Task Event Creation',
    },
    'Task Due Date Changed': {
      TASK_DUE_DATE_CHANGED_PUSH: 'Task Due Date Changed', //complete
      TASK_DUE_DATE_CHANGED_MAIL: 'Task Due Date Changed', //no template
      TASK_DUE_DATE_CHANGED_WHATSAPP: 'Task Due Date Changed',
    },
    'Task Expenditure': {
      TASK_EXPENDITURE_PUSH: 'Task Expenditure',
      TASK_EXPENDITURE_MAIL: 'Task Expenditure',
      TASK_EXPENDITURE_WHATSAPP: 'none',
    },
    'Task Approval': {
      TASK_APPROVAL_PUSH: 'TASK Approval',
      TASK_APPROVAL_MAIL: 'none',
      TASK_APPROVAL_WHATSAPP: 'TASK Approval',
    },
    'Subtask Created': {
      SUBTASK_CREATED_PUSH: 'Subtasks Created', //complete
      SUBTASKS_CREATED_MAIL: 'Subtasks Created', // no template
      SUBTASKS_CREATED_WHATSAPP: 'Subtasks Created',
    },
    'Task Deleted': {
      TASK_DELETED_PUSH: 'Task Deleted',
      NEW_TASK_DELETED_MAIL: 'Task Deleted',
      TASK_DELETED_WHATSAPP: 'Task Deleted',
    },
    'Task Terminated': {
      TASK_TERMINATED_PUSH: 'Task Terminated', //complete
      TASK_TERMINATED_MAIL: 'Task Terminated', // no template
      TASK_TERMINATED_WHATSAPP: 'Task Terminated',
    },

    'Task Collect Data': {
      COLLECT_DATA_PUSH: 'Collect Data',
      COLLECT_DATA_MAIL: 'none',
      COLLECT_DATA_WHATSAPP: 'client-document-uploaded',
    },
    'Task Attachment Uploaded': {
      TASK_ATTACHMENT_UPLOAD_PUSH: 'Task Attachments Uploaded',
      TASK_ATTACHMENT_UPLOAD_MAIL: 'none',
      TASK_ATTACHMENT_UPLOAD_WHATSAPP: 'none',
    },
  },
  'Billing': {
    'Invoice Created': {
      INVOICE_CREATED_PUSH: 'Invoices Created',
      INVOICE_CREATED_MAIL: 'Invoices Created',
      INVOICES_CREATED_WHATSAPP: 'Invoices Created',
    },
    'Invoice Cancelled': {
      INVOICE_CANCELLED_PUSH: 'Invoice Cancelled', //complete
      INVOICE_CANCELLED_MAIL: 'Invoice Cancelled', // complete
      INVOICE_CANCELLED_WHATSAPP: 'Invoice Cancelled',
    },
    'Invoice Downloaded': {
      INVOICE_DOWNLOADED_PUSH: 'Invoices Downloaded', // complete
      INVOICES_DOWNLOADED_MAIL: 'Invoices Downloaded', // no template
      INVOICES_DOWNLOADED_WHATSAPP: 'Invoices Downloaded',
    },
    'Invoice Overdue': {
      INVOICE_OVERDUE_PUSH: 'Invoice Overdue',
      INVOICE_OVERDUE_MAIL: 'Invoice Overdue',
      INVOICE_OVERDUE_WHATSAPP: 'none',
    },
    'Export Invoices': {
      EXPORTED_INVOICES_PUSH: 'Export Invoices', // complete
      EXPORT_INVOICES_MAIL: 'Export Invoices', // complete
      EXPORT_INVOICES_WHATSAPP: 'Export Invoices',
    },
    'Proforma Invoice Created': {
      PROFORMA_INVOICE_CREATED_PUSH: 'Proforma Invoices Created', //complete
      PROFORMA_INVOICE_CREATED_MAIL: 'Proforma Invoices Created', // complete
      PROFORMA_INVOICES_CREATED_WHATSAPP: 'Proforma Invoices Created',
    },
    'Proforma Invoice Cancelled': {
      PROFORMA_INVOICE_CANCELLED_PUSH: 'Proforma Invoice Cancelled', //complete
      PROFORMA_INVOICE_CANCELLED_MAIL: 'Proforma Invoice Cancelled', // complete
      PROFORMA_INVOICE_CANCELLED_WHATSAPP: 'Proforma Invoice Cancelled',
    },
    'Proforma Invoice Downloaded': {
      PROFORMA_INVOICE_DOWNLOADED_PUSH: 'Proforma Invoices Downloaded', // complete
      PROFORMA_INVOICES_DOWNLOADED_MAIL: 'Proforma Invoices Downloaded', // no template
      PROFORMA_INVOICES_DOWNLOADED_WHATSAPP: 'Proforma Invoices Downloaded',
    },
    'Export Proforma Invoices': {
      EXPORTED_PROFORMA_INVOICES_PUSH: 'Export Proforma Invoices', // complete
      EXPORT_PROFORMA_INVOICES_MAIL: 'Export Proforma Invoices', // complete
      EXPORT_PROFORMA_INVOICES_WHATSAPP: 'Export Proforma Invoices',
    },
    'Receipt Created': {
      RECEIPT_CREATED_PUSH: 'Receipt Created',
      RECEIPT_ADDED_MAIL: 'Receipt Created',
      RECEIPT_CREATED_WHATSAPP: 'Receipt Created',
    },
  },
  'Register': {
    'DSC Added': {
      DSC_ADDED_PUSH: 'DSC Added', //complete
      DSC_ADDED_MAIL: 'DSC Added', // no template
      DSC_ADDED_WHATSAPP: 'DSC Added',
    },
    'DSC Issued': {
      DSC_ISSUED_PUSH: 'DSC Issued',
      DSC_ACTIVITY_ISSUE_MAIL: 'DSC issued',
      DSC_ISSUED_WHATSAPP: 'DSC Issued',
    },
    'DSC Received': {
      DSC_RECEIVED_PUSH: 'DSC Received', //complete
      DSC_ACTIVITY_RECEIVED_MAIL: 'DSC received', // complete
      DSC_RECEIVED_WHATSAPP: 'DSC received',
    },
    'Document In & Out': {
      DOCUMENT_IN_OUT_PUSH: 'Doc In Out', //complete
      DOCUMENT_IN_OUT_MAIL: 'Doc In Out', // complete
      DOCUMENT_IN_OUT_WHATSAPP: 'Doc In Out',
    },
  },
  'Atom Pro': {
    'GSTR Notices': {
      GSTR_NOTICE_PUSH: 'none',
      GSTR_NOTICE_MAIL: 'Gstr notice',
      GSTR_NOTICE_WHATSAPP: 'Gstr notice',
    },
    'Income Tax Notices': {
      INCOMETAX_NOTICE_PUSH: 'none',
      INCOMETAX_NOTICE_MAIL: 'Incometax Notices',
      INCOMETAX_NOTICE_WHATSAPP: 'Incometax Notices',
    },
    'Traces Notices': {
      TRACES_NOTICE_PUSH: 'none',
      TRACES_NOTICE_MAIL: 'Traces Notices',
      TRACES_NOTICE_WHATSAPP: 'none',
    },
  },
  'Miscellaneous': {
    'Everyday Summary Report': {
      SUMMARY_REPORT_PUSH: 'none',
      SUMMARY_REPORT_MAIL: 'none',
      SUMMARY_REPORT_WHATSAPP: 'Summary Report',
    },
    'General Event Creation': {
      GENERAL_EVENT_CREATION_PUSH: 'General Event Creation',
      GENERAL_EVENT_CREATION_MAIL: 'General Event Creation',
      GENERAL_EVENT_CREATION_WHATSAPP: 'General Event Creation',
    },
    'New Chat Message Received': {
      NEW_MESSAGE_RECEIVED_PUSH: 'New Chat Message Received',
      NEW_CHAT_MESSAGE_RECEIVED_MAIL: 'New Chat Message Received',
      NEW_CHAT_MESSAGE_RECEIVED_WHATSAPP: 'none',
    },
    'Report Generated': {
      GENERATED_PUSH: 'Report Generated', //complete
      REPORT_GENERATED_MAIL: 'Report Generated', // complete
      REPORT_GENERATED_WHATSAPP: 'Report Generated',
    },
    'Time Sheet Add': {
      TIMESHEET_ADDED_PUSH: 'Time Sheet Add',
      TIMESHEET_ADDED_MAIL: 'Time Sheet Add',
      TIMESHEET_ADDED_WHATSAPP: 'none',
    },
    'Check In Check Out': {
      CHECKIN_CHECKOUT_PUSH: 'none',
      CHECKIN_CHECKOUT_MAIL: 'none',
      CHECKIN_CHECKOUT_WHATSAPP: 'Check In Check Out',
    },
    'Leave Request' :{
      LEAVE_ABSENT_REQUEST_PUSH :'none',
            LEAVE_ABSENT_REQUEST_MAIL :'none',
      LEAVE_ABSENT_REQUEST_WHATSAPP :'Leave Request',


    },
    // 'ATTENDANCE CHECKIN/CHECKOUT REMINDER' :{
    //   ATTENDANCE_CHECKINCHECKOUT_REMINDER_PUSH :'none',
    //   ATTENDANCE_CHECKINCHECKOUT_REMINDER_MAIL :'none',
    //   ATTENDANCE_CHECKINCHECKOUT_REMINDER_WHATSAPP :'ATTENDANCE CHECKIN/CHECKOUT REMINDER',


    // }

  },
};

export const ClientNotificationAction = {
  Client: {
    'Creation of Collect Data': {
      CREATION_COLLECT_DATA_PUSH: 'none',
      CREATION_COLLECT_DATA_MAIL: 'Creation of Collect Data',
      CREATION_COLLECT_DATA_WHATSAPP: 'Creation of Collect Data',
    },
    'Updation of Collect Data': {
      UPDATION_COLLECT_DATA_PUSH: 'none',
      UPDATION_COLLECT_DATA_MAIL: 'Updation of Collect Data',
      UPDATION_COLLECT_DATA_WHATSAPP: 'Updation of Collect Data',
    },
    'Document In & Out Register': {
      DOCUMENT_IN_OUT_PUSH: 'none',
      DOCUMENT_IN_OUT_MAIL: 'Document In Out',
      DOCUMENT_IN_OUT_WHATSAPP: 'Document In Out',
    },
    'DSC Issue': {
      DSC_ISSUE_PUSH: 'none',
      DSC_ISSUE_MAIL: 'DSC Issue',
      DSC_ISSUE_WHATSAPP: 'DSC Issue',
    },
    'DSC Receive': {
      DSC_RECEIVE_PUSH: 'none',
      DSC_RECEIVE_MAIL: 'DSC Receive',
      DSC_RECEIVE_WHATSAPP: 'DSC Receive',
    },
    'Invoice Proforma Creation': {
      INVOICE_PROFORMA_CREATION_PUSH: 'none',
      INVOICE_PROFORMA_CREATION_MAIL: 'Invoice Creation',
      INVOICE_PROFORMA_CREATION_WHATSAPP: 'Invoice Creation',
    },
    'Invoice Proforma Edited': {
      INVOICE_PROFORMA_EDITED_PUSH: 'none',
      INVOICE_PROFORMA_EDITED_MAIL: 'Invoice Edited',
      INVOICE_PROFORMA_EDITED_WHATSAPP: 'Invoice Edited',
    },
    'Invoice Creation': {
      INVOICE_CREATION_PUSH: 'none',
      INVOICE_CREATION_MAIL: 'Invoice Creation',
      INVOICE_CREATION_WHATSAPP: 'Invoice Creation',
    },
    'Invoice Edited': {
      INVOICE_EDITED_PUSH: 'none',
      INVOICE_EDITED_MAIL: 'Invoice Edited',
      INVOICE_EDITED_WHATSAPP: 'Invoice Edited',
    },
    'Invoice Reminder': {
      INVOICE_REMINDER_PUSH: 'none',
      INVOICE_REMINDER_MAIL: 'Invoice Remind',
      INVOICE_REMINDER_WHATSAPP: 'Invoice Reminder',
    },
    'Receipt Creation': {
      RECEIPT_CREATION_PUSH: 'none',
      RECEIPT_CREATION_MAIL: 'Receipt Creation',
      RECEIPT_CREATION_WHATSAPP: 'Receipt Creation',
    },
    'Receipt Edited': {
      RECEIPT_EDITED_PUSH: 'none',
      RECEIPT_EDITED_MAIL: 'Receipt Edited',
      RECEIPT_EDITED_WHATSAPP: 'Receipt Edited',
    },
    'Client Portal Access': {
      CLIENT_PORTAL_ACCESS_PUSH: 'none',
      CLIENT_PORTAL_ACCESS_MAIL: 'Client Portal Access',
      CLIENT_PORTAL_ACCESS_WHATSAPP: 'none',
    },
    'Task Completed' :{
       TASK_COMPLETED_PUSH: 'none',
      TASK_COMPLETED_MAIL: 'none',
      TASK_COMPLETED_WHATSAPP: 'Task Completed',
    },
     'Task Event Reminder' :{
       TASK_EVENT_REMINDER_PUSH: 'none',
      TASK_EVENT_REMINDER_MAIL: 'none',
      TASK_EVENT_REMINDER_WHATSAPP: 'Task Event Reminder',
    },
    'Task Event Create' :{
      TASK_EVENT_CREATE_CLIENT_PUSH: 'none',
      TASK_EVENT_CREATE_CLIENT_WHATSAPP_MAIL: 'none',
      TASK_EVENT_CREATE_CLIENT_WHATSAPP: 'Task Event Create',
    }
    
  },
};


export const OrganizationNotification ={
  Organization:{
     'ATTENDANCE CHECKIN/CHECKOUT REMINDER' :{
      ATTENDANCE_CHECKINCHECKOUT_REMINDER_PUSH :'none',
      ATTENDANCE_CHECKINCHECKOUT_REMINDER_MAIL :'none',
      ATTENDANCE_CHECKINCHECKOUT_REMINDER_WHATSAPP :'ATTENDANCE CHECKIN/CHECKOUT REMINDER',


    }
}}