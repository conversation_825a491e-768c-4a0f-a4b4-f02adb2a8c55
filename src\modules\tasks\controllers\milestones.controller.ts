import {
  Body,
  Delete,
  Get,
  Param,
  ParseIntPipe,
  Post,
  Put,
  Query,
} from '@nestjs/common';
import AddMileStoneDto from '../dto/add-milestone.dto';
import { TasksController } from './task.controller';

export class MilestonesController extends TasksController {
  @Post('/:taskId/milestones')
  addMilestone(
    @Body() body: AddMileStoneDto,
    @Param('taskId', ParseIntPipe) taskId: number,
  ) {
    return this.milestoneService.addMileStone(taskId, body);
  }

  @Get('/milestones')
  getMilestones(@Query('taskId', ParseIntPipe) taskId: number) {
    return this.milestoneService.getMilestones(taskId);
  }

  @Put('/milestones/:id')
  updateMilestone(
    @Body() body: AddMileStoneDto,
    @Param('id', ParseIntPipe) id: number,
  ) {
    return this.milestoneService.updateMilestone(id, body);
  }

  @Delete('/milestones/:id')
  deleteMilestone(@Param('id', ParseIntPipe) id: number) {
    return this.milestoneService.deleteMilestone(id);
  }
}
