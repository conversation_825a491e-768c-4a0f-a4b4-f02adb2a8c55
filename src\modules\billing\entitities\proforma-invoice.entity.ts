import Client from 'src/modules/clients/entity/client.entity';
import { BillingEntity } from 'src/modules/organization/entities/billing-entity.entity';
import { Organization } from 'src/modules/organization/entities/organization.entity';
import Task from 'src/modules/tasks/entity/task.entity';
import {
  BaseEntity,
  Column,
  CreateDateColumn,
  Entity,
  JoinColumn,
  ManyToOne,
  OneToMany,
  OneToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import InvoiceAddress from './invoice-address.entity';
import InvoiceBankDetails from './invoice-bank-details.entity';
import InvoiceOtherParticular from './invoice-other-particular.entity';
import InvoiceParticular from './invoice-particular.entity';
import ReceiptParticular from './receipt-particular.entity';
import { IsOptional } from 'class-validator';
import ClientGroup from 'src/modules/client-group/client-group.entity';
import { User } from 'src/modules/users/entities/user.entity';


export enum InvoiceStatus {
  DRAFT = 'DRAFT',
  APPROVAL_PENDING = 'APPROVAL_PENDING',
  APPROVED = 'APPROVED',
  CANCELLED = 'CANCELLED',
  EMAIL_SENT = 'EMAIL_SENT',
  INVOICED = 'INVOICED',
  PARTIALLY_PAID = 'PARTIALLY_PAID',
  PAID = 'PAID',
};

export enum InvoiceType {
  INVOICE = 'INVOICE',
  PROFORMA = 'PROFORMA',
};

export enum ProformaStatus {
  CREATED = 'CREATED',
  CONVERTED = 'CONVERTED',
  CANCELLED = 'CANCELLED',
  CLOSED = 'CLOSED',
};

export enum TdsSection {
  '194C' = '194C',
  '194H' = '194H',
  '194I_A' = '194I(a)',
  '194I_B' = '194I(b)',
  '194IB' = '194IB',
  '194J' = '194J',
  '194M' = '194M',
  '194O' = '194O',
  '194Q' = '194Q',
  '194S' = '194S',
  'TDS195' = '195',
};

export enum TdsRate {
  'Point1' = '0.1',
  'One' = '1',
  'Two' = '2',
  'Five' = '5',
  'Ten' = '10',
  'Twenty' = '20',
  'Thirty' = '30',
};

export enum SupplyTypes {
  PAYMENT_OF_IGST = 'PAYMENT_OF_IGST',
  UNDER_BOND = 'UNDER_BOND',
}


@Entity()
export class ProformaInvoice extends BaseEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({
    type: 'enum',
    enum: ProformaStatus,
    default: ProformaStatus.CREATED,
  })
  status: ProformaStatus;

  @Column({ type: 'enum', enum: TdsSection, default: null })
  tdsSection: TdsSection

  @Column({ type: 'enum', enum: TdsRate, default: null })
  tdsRate: TdsRate


  @Column()
  placeOfSupply: string;

  @ManyToOne(() => BillingEntity, (billingEntity) => billingEntity.proformaInvoice)
  billingEntity: BillingEntity;

  @ManyToOne(() => Client, (client) => client.proformaInvoice)
  client: Client;

  @ManyToOne(() => ClientGroup, (clientGroup) => clientGroup.proformaInvoice)
  clientGroup: ClientGroup;

  @ManyToOne(() => Organization, (org) => org.proformaInvoice)
  organization: Organization;

  @OneToMany(() => InvoiceParticular, (ip) => ip.proformaInvoice, { cascade: true })
  particulars: InvoiceParticular[];

  @OneToMany(() => InvoiceOtherParticular, (ip) => ip.proformaInvoice, {
    cascade: true,
  })
  otherParticulars: InvoiceOtherParticular[];

  @OneToMany(() => Task, (task) => task.ProformaInvoice)
  tasks: Task[];

  @OneToOne(() => InvoiceAddress, { cascade: true })
  @JoinColumn()
  billingEntityAddress: InvoiceAddress;

  @OneToOne(() => InvoiceAddress, { cascade: true })
  @JoinColumn()
  billingAddress: InvoiceAddress;

  @OneToOne(() => InvoiceAddress, { cascade: true })
  @JoinColumn()
  shippingAddress: InvoiceAddress;

  @OneToOne(() => InvoiceBankDetails, { cascade: true })
  @JoinColumn()
  bankDetails: InvoiceBankDetails;

  @Column()
  invoiceNumber: string;

  @Column({ type: 'date' })
  invoiceDate: string;

  @Column()
  terms: string;

  @Column({ type: 'date' })
  invoiceDueDate: string;

  @Column()
  termsAndConditions: string;

  @Column('json')
  termsAndConditionsCopy: object[];

  // @Column()
  // subTotal: number;

  @Column({ type: 'decimal', precision: 19, scale: 2 })
  subTotal: number;

  // @Column()
  // totalGstAmount: number;

  @Column({ type: 'decimal', precision: 19, scale: 2 })
  totalGstAmount: number;

  @Column()
  totalCharges: number;

  @Column()
  adjustment: number;

  @Column({ type: 'text', nullable: true })
  narration: string;

  @Column()
  roundOff: string;

  // @Column()
  // grandTotal: number;

  @Column({ type: 'decimal', precision: 19, scale: 2 })
  grandTotal: number;

  @IsOptional()
  invoiceUser: any;

  @Column()
  whatsappCheck: boolean;

  @Column()
  emailCheck: boolean;

  @Column({ default: false })
  divideTax: boolean;

  @Column({ type: 'enum', enum: SupplyTypes, default: null })
  supplyType: SupplyTypes;

  @Column({ default: false })
  hasTds: boolean;

  @Column({ default: false })
  tdsView: boolean;

  @OneToOne(() => User, { cascade: true })
  @JoinColumn()
  signatureUser: User


  @CreateDateColumn()
  createdAt: string;

  @UpdateDateColumn()
  updatedAt: string;
}
