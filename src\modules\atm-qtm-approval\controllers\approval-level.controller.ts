import { Body, Controller, Get, Post, Put, Query, Req, UseGuards } from '@nestjs/common';
import { ApprovalLevelService } from '../services/approval-level.service';
import { JwtAuthGuard } from 'src/modules/users/jwt/jwt-auth.guard';
import { UpdateTaskApprovalsDto } from '../dto/update-approvals.dto';

@Controller('approval-level')
export class ApprovalLevelController {
  constructor(private service: ApprovalLevelService) {}

  @UseGuards(JwtAuthGuard)
  @Get()
  find(@Req() req: any) {
    const { userId } = req.user;
    return this.service.find(userId);
  }
  @UseGuards(JwtAuthGuard)
  @Get('tasks')
  findTasks(@Query() query) {
    return this.service.findTasks(query);
  }

  @UseGuards(JwtAuthGuard)
  @Put('status-change')
  changeStatus(@Req() req: any, @Body() body: any) {
    const { userId } = req.user;
    return this.service.changeStatus(userId, body);
  }

  @UseGuards(JwtAuthGuard)
  @Post('/task')
  updateTaskApprovals(@Body() body: UpdateTaskApprovalsDto, @Req() req) {
    const { userId } = req.user;

    return this.service.updateTaskApprovals(body, userId);
  }
}
