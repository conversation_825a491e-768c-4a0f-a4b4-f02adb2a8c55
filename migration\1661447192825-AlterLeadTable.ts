import { MigrationInterface, QueryRunner } from 'typeorm';

export class AlterLeadTable1661447192825 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      "ALTER TABLE `lead` modify COLUMN status enum('PENDING', 'CONVERTED') not null default 'PENDING'",
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}
