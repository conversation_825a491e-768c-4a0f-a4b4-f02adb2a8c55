import { Invoice } from 'src/modules/billing/entitities/invoice.entity';
import Receipt from 'src/modules/billing/entitities/receipt.entity';
import {
  AfterLoad,
  BaseEntity,
  Column,
  CreateDateColumn,
  Entity,
  JoinColumn,
  JoinTable,
  ManyToMany,
  ManyToOne,
  OneToMany,
  OneToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { BankAccount } from './bank-account.entity';
import { OrganizationLicense } from './organization-license.entity';
import { Organization } from './organization.entity';
import Storage from 'src/modules/storage/storage.entity';
import ReceiptCredit from 'src/modules/billing/entitities/receipt-credit.entity';
import { ProformaInvoice } from 'src/modules/billing/entitities/proforma-invoice.entity';
import { User } from 'src/modules/users/entities/user.entity';

@Entity()
export class BillingEntity extends BaseEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ nullable: true })
  category: string;

  @Column({ nullable: true })
  legalName: string;

  @Column({ nullable: true })
  tradeName: string;

  @Column({ nullable: true })
  locationOfSupply: string;

  @Column({ nullable: true })
  constitutionOfBusiness: string;

  @Column({ nullable: true })
  registrationNumber: string;

  @Column({ nullable: true })
  registrationDate: string;

  @Column({ default: false })
  gstVerified: boolean;

  @Column({ nullable: true })
  gstNumber: string;

  @Column({ nullable: true })
  gstStatus: string;

  @Column({ nullable: true })
  gstAttachment: string;

  @Column({ nullable: true })
  panNumber: string;

  @Column({ default: false })
  panVerified: boolean;

  @Column({ nullable: true })
  panAttachment: string;

  @Column({ nullable: true })
  firstName: string;

  @Column({ nullable: true })
  middleName: string;

  @Column({ nullable: true })
  lastName: string;

  @Column({ nullable: true })
  mobileNumber: string;

  @Column({ nullable: true })
  alternateMobileNumber: string;

  @Column({ nullable: true })
  email: string;

  @Column({ nullable: true })
  website: string;

  @Column({ nullable: true })
  primaryContactFullName: string;

  @Column({ nullable: true })
  primaryContactEmail: string;

  @Column({ nullable: true })
  primaryContactDesignation: string;

  @Column({ nullable: true })
  primaryContactMobileNumber: string;

  @Column({ nullable: true })
  floorNumber: string;

  @Column({ nullable: true })
  buildingNumber: string;

  @Column({ nullable: true })
  buildingName: string;

  @Column({ nullable: true })
  street: string;

  @Column({ nullable: true })
  city: string;

  @Column({ nullable: true })
  district: string;

  @Column({ nullable: true })
  location: string;

  @Column({ nullable: true })
  state: string;

  @Column({ nullable: true })
  pincode: string;

  @Column({ nullable: true })
  prefix: string;

  @Column({ nullable: true })
  receiptPrefix: string;

  @Column({ nullable: true })
  prefixNumber: string;

  @Column({ nullable: true })
  receiptPrefixNumber: string;

  @Column({ nullable: true })
  creditPeriod: string;

  @Column({ nullable: true })
  proformaCreditPeriod: string;

  @Column()
  terms: string;

  @Column('json')
  termsCopy: object[];

  @Column()
  receiptTerms: string;

  @Column('json')
  receiptTermsCopy: object[];

  @Column({ nullable: true })
  proformaPrefix: string;

  @Column({ nullable: true })
  proformaPrefixNumber: string;

  @Column('json')
  proformaTerms: object[];

  @Column({ nullable: true })
  proformaLocationOfSupply: string;

  @Column({ default: false })
  proformaAutoGenerate: boolean;

  @Column({ default: false })
  autoGenerate: boolean;

  @Column({ default: true })
  showClientData: boolean;

  @Column({ default: true })
  showClientDataProforma: boolean;

  @Column({ default: true })
  showDiscount: boolean;

  @Column({ default: false })
  receiptAutoGenerate: boolean;

  @Column({ default: false })
  default: boolean;

  @Column({ nullable: true })
  logo: string;

  @Column({ nullable: true })
  tag: string;

  @OneToOne(() => Storage, (storage) => storage.signatureStorage, { cascade: true })
  signatureStorage: Storage;

  @OneToOne(() => Storage, (storage) => storage.logStorage, { cascade: true })
  logStorage: Storage;

  @Column({ nullable: true })
  signature: string;

  @Column({ nullable: false })
  hasGst: boolean;

  @Column({ default: true })
  isActive: boolean;

  @Column({ nullable: true })
  countryCode: string;

  @Column({ nullable: true })
  alternateCountryCode: string;

  @Column({ nullable: true })
  primaryContactCountryCode: string;

  @ManyToOne(() => Organization, (org) => org.billingEntities)
  organization: Organization;

  @OneToMany(() => OrganizationLicense, (license) => license.billingEntity)
  licenses: OrganizationLicense[];

  @OneToMany(() => BankAccount, (bankAccount) => bankAccount.billingEntity)
  bankAccounts: BankAccount[];

  @OneToMany(() => Invoice, (invoice) => invoice.billingEntity)
  invoices: Invoice[];

  @OneToMany(() => ProformaInvoice, (proformaInvoice) => proformaInvoice.billingEntity)
  proformaInvoice: ProformaInvoice[];

  @OneToMany(() => Receipt, (receipt) => receipt.billingEntity)
  receipts: Receipt[];

  @OneToMany(() => ReceiptCredit, (rc) => rc.billingEntity)
  receiptCredits: ReceiptCredit[];

  @ManyToMany(() => User, (user) => user.billingEntity)
  @JoinTable()
  signatures: User[];

  @OneToOne(() => User, (user) => user.defaultBillingEntity)
  @JoinColumn()
  defaultSignature: User

  @CreateDateColumn()
  createdAt: string;

  @UpdateDateColumn()
  updatedAt: string;

  logoUrl: string;

  gstAttachmentUrl: string;

  panAttachmentUrl: string;

  signatureUrl: string;

  @AfterLoad()
  renderUrl() {
    if (this.logo) {
      this.logoUrl = `${process.env.AWS_BASE_URL}/${this.logo}`;
    }
    if (this.gstAttachment) {
      this.gstAttachmentUrl = `${process.env.AWS_BASE_URL}/${this.gstAttachment}`;
    }
    if (this.panAttachment) {
      this.panAttachmentUrl = `${process.env.AWS_BASE_URL}/${this.panAttachment}`;
    }

    if (this.signature) {
      this.signatureUrl = `${process.env.AWS_BASE_URL}/${this.signature}`;
    }
  }
}
