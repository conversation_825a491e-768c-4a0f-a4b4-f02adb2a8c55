import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { Form, FormSchema } from 'src/modules/forms/schemas/form.schema';
import { Validation, ValidationSchema } from 'src/modules/forms/schemas/validation.schema';
import { AdminController } from './admin.controller';
import { AdminService } from './admin.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import QtmTemplate from './entities/quantumTemplateFeed.entity';
import QtmTemplateCat from './entities/quantumTemplateCategories';
import QtmTemplateSubcat from './entities/quantumTemplateSubCategories';
import QtmCategories from './entities/quantumCategories';
import QtmLabel from './entities/documentLabels';
import QtmActivity from './entities/qtmActivity.entity';
import { AwsService } from '../storage/upload.service';
import PosterEvents from '../poster/poster-events.entity';
import PosterEventTypes from '../poster/poster-event-types.entity';
import BroadcastEmailTemplates from '../communication/entity/broadcast-email-templates-entity';


@Module({
  imports: [
    MongooseModule.forFeature([{ name: Form.name, schema: FormSchema }]),
    MongooseModule.forFeature([{ name: Validation.name, schema: ValidationSchema }]),
    TypeOrmModule.forFeature([
      QtmTemplate,
      QtmTemplateCat,
      QtmTemplateSubcat,
      QtmCategories,
      QtmLabel,
      QtmActivity,
      PosterEvents,
      PosterEventTypes,
      BroadcastEmailTemplates,
    ]),
  ],
  controllers: [AdminController],
  providers: [AdminService, AwsService],
})
export class AdminModule { }
