import { Body, Controller, Get, Post, Query, Req, UseGuards } from '@nestjs/common';
import { JwtAuthGuard } from 'src/modules/users/jwt/jwt-auth.guard';
import { TanDashboardService } from '../service/tan-dashboard-service';

@Controller('incometaxtan-dashboard')
export class TanDashboardController {
  constructor(private service: TanDashboardService) {}

  @UseGuards(JwtAuthGuard)
  @Get('/forms-udin')
  getFormsUdinAnalytics(@Req() req: any, @Query() query: any) {
    const { userId } = req.user;
    return this.service.getFormsUdinAnalytics(userId, query.assessmentYear);
  }

  @UseGuards(JwtAuthGuard)
  @Get('verification')
  incometaxClientCheck(@Req() req: any, @Query() query: any) {
    const { userId } = req.user;
    return this.service.incometaxClientCheck(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Post('/exportTanInvalid')
  async exportTanInvalid(@Req() req: any, @Body() body: any) {
    const { userId } = req?.user;
    const query = body;
    return this.service.exportTanInvalid(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Get('/config-status')
  getIncometaxConfigStatus(@Req() req: any, @Query() query: any) {
    const { userId } = req.user;
    return this.service.getIncometaxConfigStatus(userId, query.startDates);
  }

  @UseGuards(JwtAuthGuard)
  @Get('/forms-analytics')
  getFormsAnalytics(@Req() req: any, @Query() query: any) {
    const { userId } = req.user;
    return this.service.getFormsAnalytics(userId, query.assessmentYear);
  }

  @UseGuards(JwtAuthGuard)
  @Get('/tds-analytics')
  getFormsNavigateAnalytics(@Req() req: any, @Query() query: any) {
    const { userId } = req.user;
    return this.service.getFormsNavigateAnalytics(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Get('/forms-correction-analytics')
  getFormsCorrectionAnalytics(@Req() req: any, @Query() query: any) {
    const { userId } = req.user;
    return this.service.getFormsCorrectionAnalytics(userId, query.assessmentYear);
  }

  @UseGuards(JwtAuthGuard)
  @Get('eProceedingNotices-excel')
  eExcelProccedidingFyiNotice(@Req() req: any, @Query() query: any) {
    const { userId } = req.user;
    return this.service.getExcelCombinedNoticesCount(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Get('tracesNotices')
  tracesNotice(@Req() req: any, @Query() query: any) {
    const { userId } = req.user;
    return this.service.tracesNotice(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Get('/fya-events-excel')
  getExcelFyaEvents(@Req() req: any, @Query() query: any) {
    const { userId } = req.user;
    return this.service.getExcelFyaEvents(userId, query.startDates);
  }

  @UseGuards(JwtAuthGuard)
  @Get('/response-due-events-excel')
  getExcelResponseDueEvents(@Req() req: any, @Query() query: any) {
    const { userId } = req.user;
    return this.service.getExcelResponseDueEvents(userId, query.startDates);
  }

  @UseGuards(JwtAuthGuard)
  @Get('/trace-issue-event')
  getTracesEvents(@Req() req: any, @Query() query: any) {
    const { userId } = req.user;
    return this.service.getTracesEvents(userId, query.startDates);
  }
  @Get('verification-tan')
  incometaxTanClientCheck(@Req() req: any, @Query() query: any) {
    const { userId } = req.user;
    return this.service.incometaxTanClientCheck(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Get('verification-traces')
  tracesClientCheck(@Req() req: any, @Query() query: any) {
    const { userId } = req.user;
    return this.service.tracesClientCheck(userId, query);
  }
}
