import { Injectable } from '@nestjs/common';
import StageOfWork from 'src/modules/tasks/entity/stage-of-work.entity';
import { StorageService } from './storage.service';

@Injectable()
export class StageOfWorkService {
  async getStageOfWork(id: number) {
    let stageOfWorks = await StageOfWork.find({
      where: {
        task: {
          id,
        },
      }, relations: ['storage']
    });

    return stageOfWorks;
  }

}
