import { Injectable } from '@nestjs/common';
import * as moment from 'moment';
import { User } from 'src/modules/users/entities/user.entity';
import { Brackets, createQueryBuilder } from 'typeorm';
import { CreateTokenDto } from './dto/create-token.dto';
import { Notification } from './notification.entity';
import { Token } from './token.entity';
import UpdateNotificationsDto from './dto/update.dto';
import { dateFormation } from 'src/utils/datesFormation';
import { sendMailViaAny } from 'src/emails/newemails';
import { compile } from 'ejs';
import { DscRegiserQueyType, FindDscRegisterDto } from 'src/modules/dsc-register/dto/find-dsc-register.dto';
import DscRegister from 'src/modules/dsc-register/entity/dsc-register.entity';
import ClientGroup from 'src/modules/client-group/client-group.entity';
import ReminderEmailLog from './reminder-email-log.entity';

@Injectable()
export class NotificationsService {
  async saveToken(userId: number, data: CreateTokenDto) {
    let existingUser = await Token.findOne({
      where: { user: { id: userId } },
    });
    if (existingUser) {
      if (existingUser.token == data.token) {
        return 'Token already exist';
      }
      existingUser.token = data.token;
      await existingUser.save();
      return existingUser;
    }

    let user = await User.findOne({ where: { id: userId } });
    let newUser = new Token();
    newUser.user = user;
    newUser.token = data.token;
    await newUser.save();
    return newUser;
  }

  async getAll(userId: number, query: any) {
    try {
      let notifications = createQueryBuilder(Notification, 'notification')
        .leftJoinAndSelect('notification.user', 'user')
        .orderBy('notification.createdAt', 'DESC')
        .where('user.id = :userId', { userId })

      if (query.fromDate && query.toDate) {
        const { startTime, endTime } = dateFormation(query.fromDate, query.toDate)
        notifications.andWhere('notification.createdAt >= :fromDate', {
          fromDate: startTime,
        });
        notifications.andWhere('notification.createdAt <= :toDate', {
          toDate: endTime,
        });
      } else {
        notifications.andWhere('notification.createdAt >= :date', {
          date: moment().subtract(7, 'days').format('YYYY-MM-DD'),
        });
      }
      return await notifications.getMany();
    } catch (error) {
      console.log('error is occur while getting getAll Notification', error);
    }
  }

  async getAllCount(userId: number, query: any) {
    try {
      let notificationsCount = createQueryBuilder(Notification, 'notification')
        .leftJoin('notification.user', 'user')
        .orderBy('notification.createdAt', 'DESC')
        .where('user.id=:userId', { userId })
        .andWhere('notification.status="unread"')
        .andWhere('notification.createdAt >=:date', {
          date: moment().subtract(7, 'days').format('YYYY-MM-DD'),
        });
      const count = await notificationsCount.getManyAndCount();
      return count;
    } catch (error) {
      console.log('error is occur while getting getAllCount Notification', error);
    }
  }

  async update(userId: number, data) {
    try {
      if (data.length > 0) {
        for (let i of data) {
          let notification = await Notification.findOne(i);
          notification.status = 'read';
          await notification.save();
        }
      }
    } catch (error) {
      console.log('error is occur while getting update Notification', error);
    }
  }
  
    async getNotificationReminderData(userId: number, query: FindDscRegisterDto) {
      console.log(userId,'userId')
      const user = await User.findOne({ 
        where: { id: userId }, 
        relations: ['organization', 'role']
      });
      // const ViewAll = user.role.permissions.some(
      //   (permission) => permission.slug === Permissions.VIEW_ALL_CLIENT_MANAGERS
      // );
      // const ViewAssigned = user.role.permissions.some(
      //   (permission) => permission.slug === Permissions.VIEW_ASSIGNED_CLIENT_MANAGERS
      // );
      let dscRegisters = createQueryBuilder(DscRegister, 'dscRegister')
        .select([
          'dscRegister',
          'client.id',
          'client.displayName',
          'clientManagers.id',
          'clientManagers.fullName',
          'clientGroup.id',
          'clientGroup.displayName',
          'clientGroupManagers.id',
          'clientGroupManagers.fullName',
          'organization.id'
        ])
        .leftJoin('dscRegister.clients', 'client')
        .leftJoin('client.clientManagers', 'clientManagers')
        .leftJoin('dscRegister.clientGroups', 'clientGroup')
        .leftJoin('clientGroup.clientGroupManagers', 'clientGroupManagers')
        .leftJoin('dscRegister.organization', 'organization');
  
      if (query.type === DscRegiserQueyType.CLIENT) {
        dscRegisters.andWhere(
          qb => {
            const subQuery = qb.subQuery()
              .select('dsc.id')
              .from(DscRegister, 'dsc')
              .innerJoin('dsc.clients', 'subClient')
              .where('subClient.id = :clientId', { clientId: query.clientId })
              .getQuery();
            return `dscRegister.id IN ${subQuery}`;
          }
        );
      }
  
  
      if (query.type === DscRegiserQueyType.CLIENT_GROUP) {
        const clientGroup = await createQueryBuilder(ClientGroup, 'clientGroup')
        .select([
            'clientGroup',
            'organization.id',
            'clients.id',
            'clients.displayName'
          ])
          .leftJoin('clientGroup.organization', 'organization')
          .leftJoin('clientGroup.clients', 'clients')
          .where('clientGroup.id = :id', { id: query.clientGroupId })
          .andWhere('organization.id = :organizationId', { organizationId: user.organization.id })
          .getOne();
  
        const clientGroupIDs = clientGroup?.clients?.map((item) => item.id) || [];
  
        dscRegisters.andWhere(
          new Brackets((qb) => {
            qb.where('clientGroup.id = :clientGroupId', { clientGroupId: query.clientGroupId });
  
            if (clientGroupIDs.length) {
              qb.orWhere('client.id IN (:...clientGroupIDs)', { clientGroupIDs });
            }
          })
        );
      }
      // if (!ViewAll && ViewAssigned) {
      //   dscRegisters.andWhere(
      //     new Brackets(qb => {
      //       qb.where('client.id IS NOT NULL AND clientManagers.id = :userId', { userId })
      //         .orWhere('clientGroup.id IS NOT NULL AND clientGroupManagers.id = :userId', { userId })
      //         .orWhere('client.id IS NULL AND clientGroup.id IS NULL');
      //     })
      //   );
      // }else if(!ViewAll && !ViewAssigned){
      //   dscRegisters.andWhere('client.id IS NULL AND clientGroup.id IS NULL');
      // }
  
      if (query.type === DscRegiserQueyType.ORGANIZATION) {
        dscRegisters.andWhere('organization.id = :organizationId', {
          organizationId: user.organization.id,
        });
      }
  
      if (query.search) {
        dscRegisters.andWhere(
          new Brackets((qb) => {
            qb.where('dscRegister.holderName LIKE :search', { search: `%${query.search}%` })
              .orWhere('dscRegister.tokenNumber LIKE :search', { search: `%${query.search}%` })
              .orWhere('client.displayName LIKE :search', { search: `%${query.search}%` })
              .orWhere('clientGroup.displayName LIKE :search', { search: `%${query.search}%` });
          })
        );
      }
  // ✅ Add DSC Expiry Filter (Next 30 Days)
const now = new Date();
const in30Days = new Date();
in30Days.setDate(now.getDate() + 30);

dscRegisters.andWhere('dscRegister.expiryDate BETWEEN :now AND :in30Days', {
  now: now.toISOString(),
  in30Days: in30Days.toISOString(),
});
      if (query.offset >= 0) {
        dscRegisters.skip(query.offset);
      }
  
      if (query.limit) {
        dscRegisters.take(query.limit);
      }
      const sort = (typeof query?.sort === 'string') ? JSON.parse(query.sort) : query?.sort;
      if (sort?.column) {
        const columnMap: Record<string, string> = {
          holderName: 'dscRegister.holderName',
          expiryDate: 'dscRegister.expiryDate',
          daysexpiryDate: 'dscRegister.expiryDate',
          tokenNumber: 'dscRegister.tokenNumber',
        };
        const column = columnMap[sort.column] || sort.column;
        dscRegisters.orderBy(column, sort.direction.toUpperCase());
      } else {
        dscRegisters.orderBy('dscRegister.createdAt', 'DESC');
      };
  
      const [data, totalCount] = await dscRegisters.getManyAndCount();
  console.log(data,'data')
      return {
        data, 
        totalCount
      };
    }
//    async  sendReminderNotification(userId: number, data: {
//   title: string;
//   subject: string;
//   content: string;
//   recipients: string[];
// }) {
//   console.log('reminder sending started for user:', userId,data);

//   for (const email of data.recipients) {
//     // const payload = {
//     //   userId,
//     //   title: data.title,
//     //   subject: data.subject,
//     //   content: data.content,
//     //   to: email, // 👈 Send to one email at a time
//     // };
//  await new Promise((resolve: any) => {
//             setTimeout(async () => {
//               let mailOptions = {
//                 from: {
//                   name: 'Vider',
//                   address: process.env.FROM_EMAIL,
//                 },
//                 to: email,
//                 subject: data?.subject,
//                 html: data?.content,
//                 userId: userId,
//                 // fromTemplate: true,
//               };

//               try {
//                 await sendMailViaAny(mailOptions);
//                 console.log(`Email sent to ${email} for notification reminder`);
//               } catch (error) {
//                 console.log(`Failed to send email to ${email} for notification reminder`, error);
//                 // hasError = true;
//               }

//               resolve();
//             }, 10000);
//           });
//           // if (hasError) break;
//     // console.log(`${data.content} Sending to:', ${email}`);

//     // Replace this with actual email send logic/API call
//     // await sendEmail(payload);
//   }

//   console.log('✅ All reminder emails sent.');
// }

// async sendReminderNotification(userId: number, data: {
//   title: string;
//   subject: string;
//   content: string;
//    recipients: {
//     email: string;
//     [key: string]: any; // dynamic fields like display_name, dueamount, etc.
//   }[];
// }) {
//   console.log('reminder sending started for user:', userId, data);
// console.log(data.recipients,'data.recipients')
// console.log(data,'dataaa')
// function replacePlaceholders(template: string, values: Record<string, any>): string {
//     return template.replace(/{{(.*?)}}/g, (_, key) => {
//       const trimmedKey = key.trim();
//       return values[trimmedKey] ?? '';
//     });
//   }
//   for (const recipient of data.recipients) {
//     console.log(recipient,'recipient')
//     const { email, name, amount, due_date } = recipient;
// const htmlContent = replacePlaceholders(data.content, {
//     display_name: recipient.name,
//   dueamount: recipient.amount,
//   due_date: recipient.due_date,
//     });    console.log(`Preparing email for ${email}`, recipient);


//     await new Promise((resolve: any) => {
//       setTimeout(async () => {
//         let mailOptions = {
//           from: {
//             name: 'Vider',
//             address: process.env.FROM_EMAIL,
//           },
//           to: email,
//           subject: data?.subject,
//           html: htmlContent,
//           userId: userId,
//         };

//         try {
//           await sendMailViaAny(mailOptions);
//           console.log(`Email sent to ${email} for notification reminder`);
//         } catch (error) {
//           console.log(`❌ Failed to send email to ${email}`, error);
//         }

//         resolve();
//       }, 10000);
//     });
//   }

//   console.log('✅ All reminder emails sent.');
// }
async sendReminderNotification(userId: number, data: {
  title: string;
  subject: string;
  content: string;
  recipients: {
    id: number | string;
      display_name?: string;
    email: string;
    [key: string]: any; // dynamic fields like display_name, dueamount, etc.
  }[];
  organizationId: number;
    reminderType: string;
}) {
  console.log('📨 Reminder sending started for user:', userId);

  console.log(data,'data')
  console.log(data.recipients, 'Recipients List');
 const user = await User.findOne({
    where: { id: userId },
    relations: ['organization'], // if you have relation
  });

  if (!user) {
    throw new Error(`User with id ${userId} not found`);
  }
  function replacePlaceholders(template: string, values: Record<string, any>): string {
    return template.replace(/{{(.*?)}}/g, (_, key) => {
      const trimmedKey = key.trim();
      return values[trimmedKey] ?? '';
    });
  }


  const clientDetails = []

  for (const recipient of data.recipients) {
    const { email,display_name,id ,clientname_clientgroupname,clients, clientGroups} = recipient;
    const htmlContent = replacePlaceholders(data.content, recipient);
    console.log(`📧 Preparing email for ${email}`, recipient);
const DSCClientName =
    display_name ||
    clients?.[0]?.displayName ||
    clientGroups?.[0]?.displayName ||
    '';
    await new Promise((resolve) => {
      setTimeout(async () => {
        const mailOptions = {
          from: {
            name: 'Vider',
            address: process.env.FROM_EMAIL,
          },
          to: email,
          subject: data.subject,
          html: htmlContent,
          userId,
        };

        try {
          await sendMailViaAny(mailOptions);
          console.log(`✅ Email sent to ${email}`);
        clientDetails.push({
          id,
          displayName: display_name || clientname_clientgroupname ||DSCClientName,  // 👈 take from recipient
          email,
          isMailSent: true,
        });
        } catch (error) {
          console.error(`❌ Failed to send email to ${email}`, error);
           clientDetails.push({
            id, 
            email,
            displayName:display_name || clientname_clientgroupname ||DSCClientName ,
            isMailSent: false
          })
        }

        resolve(null);
      }, 1000); // Delay each email by 1 second
    });
  }

  const organizationId = user.organization?.id;

 // 🔹 Save one log row for this reminder action
  const reminderEmailLog = new ReminderEmailLog()
  reminderEmailLog.userId = userId;
  reminderEmailLog.organizationId = organizationId;
  reminderEmailLog.reminderType = data.title,
  reminderEmailLog.clientDetails = clientDetails
 
  await reminderEmailLog.save();

  console.log("✅ Reminder log saved with client details.");
  console.log('✅ All reminder emails processed.');
}


async getMailRemindersSent(userId: number, query: any) {
  console.log('mail reminders sent', userId);
    const { limit, offset } = query;

  // get user with organization
  const user = await User.findOne({
where: { id: userId },
    relations: ['organization'],
  });

  if (!user?.organization) {
    throw new Error('User has no organization');
  }

  // fetch logs for that organization
  const response = await ReminderEmailLog.findAndCount({
    where: { userId  },
    order: { createdAt: 'DESC' }, // optional: order by time
    take: query?.limit || 20,     // optional: pagination
    skip: query?.offset || 0,
  });


  return response;
}


}
