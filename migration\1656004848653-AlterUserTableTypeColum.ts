import { MigrationInterface, QueryRunner } from 'typeorm';

export class AlterUserTableTypeColum1656004848653
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE user 
               MODIFY COLUMN type enum('ORGANIZATION', 'CLIENT', 'CLIENT_USER' ) default 'ORGANIZATION' not null`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}
