import {
  BadRequestException,
  ConflictException,
  ForbiddenException,
  Injectable,
  InternalServerErrorException,
} from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import * as moment from 'moment-timezone';
import Client from 'src/modules/clients/entity/client.entity';
import { User, UserType } from 'src/modules/users/entities/user.entity';
import { Brackets, createQueryBuilder, getManager, getRepository, In } from 'typeorm';
import { TaskStatusEnum } from '../tasks/dto/types';
import TaskStatus from '../tasks/entity/task-status.entity';
import Task from '../tasks/entity/task.entity';
import AddLogHour, { UpdateLogHour } from './dto/add-log-hour.dto';
import { AddUserLogHourDto } from './dto/add-user-log-hour.dto';
import EndTimerDto from './dto/end-timer.dto';
import { GetUserLogHoursDto, GetUserLogHoursStatsDto } from './dto/get-user-log-hours.dto';
import StartTimerDto from './dto/start-timer.dto';
import LogHour, { LogHourType, TimerStatus } from './log-hour.entity';
import * as xlsx from 'xlsx';
// const { generateAuthToken } = require('aws-msk-iam-sasl-signer-js');
// import { Kafka, Consumer, EachMessagePayload } from 'kafkajs';
// import moment from 'moment';
import * as ExcelJS from 'exceljs';
import { getAdminIDsBasedOnOrganizationId } from 'src/utils/re-use';
import { dateFormation } from 'src/utils/datesFormation';
import { sendnewMail } from 'src/emails/newemails';
import ClientGroup from '../client-group/client-group.entity';
import Activity, { ActivityType } from '../activity/activity.entity';
import { Event_Actions } from 'src/event-listeners/actions';
import OrganizationPreferences from '../organization-preferences/entity/organization-preferences.entity';
import { Organization } from '../organization/entities/organization.entity';
import CronActivity from '../cron-activity/cron-activity.entity';
import { formatDate } from 'src/utils';
import { EXPENDITURE_STATUS, ExpenditureStatus } from '../expenditure/dto/types';
import LogHourTitle from './entity/log-hour-title.entity';
import { EditApprovalsDto } from './dto/edit-approvals.dto';
import * as dayjs from 'dayjs';
import { capitalize, keyBy } from 'lodash';
@Injectable()
export class LogHoursService {
  async find(taskId: number) {
    let logHours = await createQueryBuilder(LogHour, 'taskLogHour')
      .leftJoin('taskLogHour.task', 'task')
      .leftJoinAndSelect('taskLogHour.user', 'user')
      .where('task.id = :taskId', { taskId })
      .andWhere('taskLogHour.status = :status', { status: TimerStatus.STOPPED })
      .andWhere('taskLogHour.duration<> :duration', { duration: 0 })
      .getMany();

    let timeline = await this.getTimeline(taskId);

    return { logHours, timeline: timeline };
  }
  async findStartedLogHoursByUserId(userId: number) {
    return await createQueryBuilder(LogHour, 'logHour')
      .leftJoinAndSelect('logHour.user', 'user')
      .leftJoinAndSelect('logHour.task', 'task')
      .where('logHour.status = :status', { status: TimerStatus.STARTED })
      .andWhere('logHour.user.id = :userId', { userId })
      .getMany();
  }

  async findUserLogHours(userId: number, query: GetUserLogHoursDto) {
    let user = await User.findOne({ where: { id: userId }, relations: ['organization'] });

    let logHours = createQueryBuilder(LogHour, 'logHour')
      .leftJoin('logHour.user', 'user')
      .leftJoin('user.organization', 'organization')
      .leftJoinAndSelect('logHour.task', 'task')
      .leftJoinAndSelect('logHour.client', 'client')
      .leftJoinAndSelect('logHour.clientGroup', 'clientGroup')
      .where('user.id = :userId', { userId })
      .andWhere('organization.id = :orgId', { orgId: user.organization.id })
      .andWhere('logHour.status = :status', { status: TimerStatus.STOPPED })
      .andWhere('logHour.duration != :duration', { duration: 0 })
      .andWhere('task.parentTask is null')
      .skip(query.offset || 0)
      .take(query.limit || 10)
      .orderBy('logHour.completedDate', 'DESC');
    if (query.search) {
      logHours.andWhere('logHour.title LIKE :search', { search: `%${query.search}%` });
      logHours.orWhere('task.name LIKE :search', { search: `%${query.search}%` });
      logHours.orWhere('client.displayName LIKE :search', { search: `%${query.search}%` });
      logHours.orWhere('clientGroup.displayName LIKE :search', { search: `%${query.search}%` });
    }

    if (query.fromDate && query.toDate) {
      const { startTime, endTime } = dateFormation(query.fromDate, query.toDate);
      if (query.fromDate) {
        logHours.andWhere('logHour.completedDate >= :fromDate', { fromDate: startTime });
      }

      if (query.toDate) {
        logHours.andWhere('logHour.completedDate <= :toDate', { toDate: endTime });
      }
    }

    let data = await logHours.getManyAndCount();

    return {
      totalCount: data[1],
      result: data[0],
    };
  }

  async getUserLogHourStats(userId: number, query) {
    const sql = () => {
      let sql = `
    select sum(case when type = 'GENERAL' then duration else 0 end) as generalLogHours,
    sum(case when type = 'TASK' then duration else 0 end) as taskLogHours
    from log_hour where user_id = ${userId}`;
      if (query.fromDate && query.toDate) {
        const { startTime, endTime } = dateFormation(query.fromDate, query.toDate);
        sql += ` and Date(completed_date) >='${startTime}' and Date(completed_date) <='${endTime}'`;
      }

      return sql;
    };

    let result = await getManager().query(sql());

    return result[0];
  }

  async getTimeline(taskId: number) {
    let taskStatus = await TaskStatus.find({
      where: { task: { id: taskId } },
      order: { createdAt: 'DESC' },
    });
    const getStatus = (status: TaskStatusEnum) => {
      let result = taskStatus.filter((taskStatus) => {
        return taskStatus.status === status;
      });

      return result[0] ? result[0].createdAt : null;
    };

    let todo = getStatus(TaskStatusEnum.TODO);
    let inProgress = getStatus(TaskStatusEnum.IN_PROGRESS);
    let onHold = getStatus(TaskStatusEnum.ON_HOLD);
    let underReview = getStatus(TaskStatusEnum.UNDER_REVIEW);
    let completed = getStatus(TaskStatusEnum.COMPLETED);

    return {
      todo,
      in_progress: inProgress,
      on_hold: onHold,
      under_review: underReview,
      completed,
    };
  }

  async add(body: AddLogHour, userId) {
    const { startTime, endTime } = dateFormation(body.completedDate, body.completedDate);

    let userData = await User.findOne({
      where: { id: userId },
      relations: ['organization', 'organization.organizationPreferences'],
    });
    let task = await Task.findOne({
      where: { id: body.taskId },
      relations: ['client', 'clientGroup'],
    });

    for (const user of body.users) {
      const userDetails = await User.findOne({ where: { id: user }, relations: ['organization'] });
      let totalLogHours = await getManager().query(
        `SELECT SUM(duration) as count FROM log_hour WHERE user_id=${user} AND Date(completed_date) >='${startTime}' and Date(completed_date) <='${endTime}'`,
      );

      // Normalize input formats
      const completedDate = moment(body.completedDate).format('YYYY-MM-DD');
      const startDateTime = moment(body.startDateTime, 'HH:mm').format('HH:mm:ss');
      const endDateTime = moment(body.endDateTime, 'HH:mm').format('HH:mm:ss');

      // Check for overlapping time slots
      const overlappingLogHours = await getManager().query(
        `SELECT id 
            FROM log_hour
            WHERE user_id = ? 
              AND DATE(completed_date) = ? 
              AND (
                (? BETWEEN start_time_new AND end_time_new) OR
                (? BETWEEN start_time_new AND end_time_new) OR
                (start_time_new BETWEEN ? AND ?) OR
                (end_time_new BETWEEN ? AND ?)
              )`,
        [
          user, // User ID
          completedDate, // Completed Date (normalized)
          startDateTime, // New Start Time
          endDateTime, // New End Time
          startDateTime, // New Start Time
          endDateTime, // New End Time
          startDateTime, // New Start Time
          endDateTime, // New End Time
        ],
      );

      if (overlappingLogHours.length > 0) {
        throw new BadRequestException('Time slot overlaps with an existing log hour');
      }

      const entityManager = getManager();
      let adminUser = await createQueryBuilder(User, 'user')
        .leftJoinAndSelect('user.organization', 'organization')
        .leftJoinAndSelect('user.role', 'role')
        .where('organization.id = :id', { id: userDetails.organization.id })
        .andWhere('role.defaultRole = :defaultRole', { defaultRole: 1 })
        .getOne();

      const result = await entityManager.query(
        `
        WITH RECURSIVE manager_hierarchy AS (
            -- Base case: Start with the given user_id
            SELECT user_id, manager_id
            FROM organization_hierarchy
            WHERE user_id = ?
            
            UNION ALL
            
            -- Recursive case: Fetch the next level manager until manager_id = 123826
            SELECT oh.user_id, oh.manager_id
            FROM organization_hierarchy oh
            INNER JOIN manager_hierarchy mh ON oh.user_id = mh.manager_id
            WHERE mh.manager_id != ? -- Stop recursion when manager_id = admin_id
        )
        -- Final output: Get all user_id in the hierarchy
        SELECT user_id
        FROM manager_hierarchy
    `,
        [userDetails.id, adminUser.id],
      );

      const mangers = result?.map((result) => result.user_id);
      mangers.push(adminUser.id);
      const filterReqUser = mangers.filter((u) => u != userDetails.id);
      let users = await User.find({
        where: {
          id: In(filterReqUser),
        },
      });

      const organizationPreferences = await OrganizationPreferences.findOne({
        where: { organization: userDetails.organization.id },
        order: { id: 'DESC' },
      });
      if (organizationPreferences?.taskPreferences?.['isTaskLeader']) {
        const taskLeaders = await Task.findOne({
          where: { id: task?.id },
          relations: ['taskLeader', 'members'],
        });
        const taskLeaderss = taskLeaders.taskLeader;
        // const filterTLeaders = aa.filter(u => (taskLeaders.members.map(i => i.id)).includes(u.id));
        const filterTLeaders = taskLeaderss.filter((u) => u.id != userDetails.id);
        users.push(...filterTLeaders);
        users = Array.from(new Map(users.map((user) => [user.id, user])).values());
      }

      let totalLogHoursCount = Number(totalLogHours[0].count) + Number(body.duration);
      let logHour = new LogHour();
      logHour.task = task;
      logHour.client = task.client;
      logHour.clientGroup = task.clientGroup;
      logHour.startTimeNew = body?.startDateTime;
      logHour.endTimeNew = body?.endDateTime;
      logHour.billable = body?.billable;
      logHour.user = userDetails;
      logHour['userId'] = userId;
      if (totalLogHoursCount > 86400000) {
        throw new BadRequestException('24 hours completed');
      }
      logHour.duration = body.duration;
      logHour.completedDate = completedDate;
      logHour.status = TimerStatus.STOPPED;
      logHour.description = body.description;
      if (
        userDetails.id != adminUser.id &&
        userData.organization.organizationPreferences?.[0]?.approvals?.['logHour']
      ) {
        logHour.approvalStatus = EXPENDITURE_STATUS.PENDING;
        logHour.managers = users;
        logHour.requestedAt = moment().utc().format('YYYY-MM-DD HH:mm:ss.SSSSSS');
      }
      logHour['userId'] = userId;
      await logHour.save();
      const newHours = Math.floor(moment.duration(logHour.duration).asHours());
      const newremainingDuration = moment
        .duration(logHour.duration)
        .subtract(Math.floor(moment.duration(logHour.duration).asHours()), 'hours');
      const newremainingMinutes = Math.floor(newremainingDuration.asMinutes());

      let taskactivity = new Activity();
      taskactivity.action = Event_Actions.LOG_HOURS_ADDED;
      taskactivity.actorId = userId;
      taskactivity.type = ActivityType.TASK;
      taskactivity.typeId = body.taskId;
      taskactivity.remarks = `Log Hour ${newHours ? newHours : 0} hrs ${
        newremainingMinutes ? newremainingMinutes : 0
      } Mns Added by ${userData.fullName}`;
      await taskactivity.save();
    }

    return { success: true };
  }

  async approvalsStatusChange(userId: number, body: EditApprovalsDto) {
    const user = await User.findOne(userId);
    const attendances = await LogHour.find({
      where: {
        id: In(body.loghourIds),
        approvalStatus: ExpenditureStatus.REJECTED,
      },
    });
    if (attendances.length) {
      throw new ConflictException("Can't Approve Declined Requests");
    }
    const attendancesApproved = await LogHour.find({
      where: {
        id: In(body.loghourIds),
        approvalStatus: ExpenditureStatus.APPROVED,
      },
    });
    if (attendancesApproved.length) {
      throw new ConflictException("Can't Re Approved");
    }

    const logHoursRecords = await LogHour.find({
      where: { id: In(body.loghourIds) },
      relations: ['managers', 'user', 'client', 'clientGroup', 'task'],
    });

    logHoursRecords.forEach((record) => {
      record.approvalStatus = body?.status;
      record.reviewer = user;
      record.reviewedAt = moment().utc().format('YYYY-MM-DD HH:mm:ss.SSSSSS');
      record.approvalDescription = body.description;
      record['userId'] = userId;
    });
    await LogHour.save(logHoursRecords);

    return true;
  }

  async addUserLogHour(userId: number, body: AddUserLogHourDto) {
    const { startTime, endTime } = dateFormation(body.completedDate, body.completedDate);
    let totalLogHours = await getManager().query(
      `SELECT SUM(duration) as count FROM log_hour WHERE user_id=${userId} AND Date(completed_date) >='${startTime}' and Date(completed_date) <='${endTime}'`,
    );
    let totalLogHoursCount = Number(totalLogHours[0].count) + Number(body.duration);
    let user = await User.findOne({ where: { id: userId } });
    let client = await Client.findOne({ where: { id: body.client } });
    let clientGroup = await ClientGroup.findOne({ where: { id: body.clientGroup } });
    let logHour = new LogHour();
    logHour.user = user;
    logHour.status = TimerStatus.STOPPED;

    if (totalLogHoursCount > 86400000) {
      throw new BadRequestException('24 hours completed');
    }
    logHour.duration = body.duration;
    logHour.completedDate = body.completedDate;
    logHour.title = body.title.trim();
    logHour.description =
      body.description !== null && body.description !== undefined
        ? body.description.trim()
        : body.description;
    logHour.type = body.logHourType;
    logHour.client = client;
    logHour.clientGroup = clientGroup;
    if (body.logHourType === LogHourType.TASK) {
      let task = await Task.findOne({
        where: { id: body.task },
        relations: ['client', 'clientGroup'],
      });
      logHour.task = task;
    }
    logHour['userId'] = userId;
    await logHour.save();
    const newHours = Math.floor(moment.duration(logHour.duration).asHours());
    const newremainingDuration = moment
      .duration(logHour.duration)
      .subtract(Math.floor(moment.duration(logHour.duration).asHours()), 'hours');
    const newremainingMinutes = Math.floor(newremainingDuration.asMinutes());

    if (body.logHourType === LogHourType.TASK) {
      let taskactivity = new Activity();
      taskactivity.action = Event_Actions.LOG_HOURS_ADDED;
      taskactivity.actorId = userId;
      taskactivity.type = ActivityType.TASK;
      taskactivity.typeId = body.task;
      taskactivity.remarks = `Log Hour ${newHours ? newHours : 0} hrs ${
        newremainingMinutes ? newremainingMinutes : 0
      } Mns Added by ${user.fullName}`;
      await taskactivity.save();
    }
    return logHour;
  }

  async update(id: number, body: UpdateLogHour, userId: number) {
    let logHour = await LogHour.findOne({
      where: { id },
      relations: ['task', 'user', 'managers', 'client', 'clientGroup'],
    });
    let userData = await User.findOne({ where: { id: userId }, relations: ['organization'] });
    const { startTime, endTime } = dateFormation(body.completedDate, body.completedDate);

    const completedDate = moment(body.completedDate).format('YYYY-MM-DD');
    const startTimeNew = moment(body.startTimeNew, 'HH:mm').format('HH:mm:ss');
    const endTimeNew = moment(body.endTimeNew, 'HH:mm').format('HH:mm:ss');

    // Check for overlapping log hours
    const overlappingLogHours = await getManager().query(
      `SELECT id 
       FROM log_hour
       WHERE user_id = ? 
         AND DATE(completed_date) = ? 
         AND id != ? 
         AND (
           (? BETWEEN start_time_new AND end_time_new) OR
           (? BETWEEN start_time_new AND end_time_new) OR
           (start_time_new BETWEEN ? AND ?) OR
           (end_time_new BETWEEN ? AND ?)
         )`,
      [
        userId, // User ID
        completedDate, // Completed Date
        id, // Exclude current log hour ID
        startTimeNew, // New Start Time
        endTimeNew, // New End Time
        startTimeNew, // New Start Time
        endTimeNew, // New End Time
        startTimeNew, // New Start Time
        endTimeNew, // New End Time
      ],
    );

    if (overlappingLogHours.length > 0) {
      throw new BadRequestException('Time slot overlaps with an existing log hour');
    }
    let totalLogHours = await getManager().query(
      `SELECT SUM(duration) as count FROM log_hour WHERE user_id=${userId} AND Date(completed_date) >='${startTime}' and Date(completed_date) <='${endTime}'`,
    );

    let totalLogHoursCount = Number(totalLogHours[0].count) + Number(body.duration);
    if (totalLogHoursCount > 86400000) {
      throw new BadRequestException('24 hours completed');
    }
    logHour.duration = body.duration;
    logHour.billable = body?.billable;
    // logHour.completedDate = body.completedDate;
    logHour.description =
      body.description !== null && body.description !== undefined
        ? body.description.trim()
        : body.description;
    logHour['userId'] = userId;
    if (body.startTimeNew !== null && body.endTimeNew !== null) {
      logHour.startTimeNew = body.startTimeNew;
      logHour.endTimeNew = body.endTimeNew;
    }

    if (body.approvalStatus == ExpenditureStatus.REJECTED) {
      logHour.reviewer = null;
      logHour.approvalStatus = EXPENDITURE_STATUS.PENDING;
      logHour.reviewedAt = null;
      logHour.requestedAt = moment().utc().format('YYYY-MM-DD HH:mm:ss.SSSSSS');
    }
    await logHour.save();

    const newHours = Math.floor(moment.duration(logHour.duration).asHours());
    const newremainingDuration = moment
      .duration(logHour.duration)
      .subtract(Math.floor(moment.duration(logHour.duration).asHours()), 'hours');
    const newremainingMinutes = Math.floor(newremainingDuration.asMinutes());

    if (logHour.task) {
      let taskactivity = new Activity();
      taskactivity.action = Event_Actions.LOG_HOURS_UPDATED;
      taskactivity.actorId = userId;
      taskactivity.type = ActivityType.TASK;
      taskactivity.typeId = logHour.task.id;
      taskactivity.remarks = `Log Hour ${newHours ? newHours : 0} hrs ${
        newremainingMinutes ? newremainingMinutes : 0
      } Mns Added by ${userData.fullName}`;
      await taskactivity.save();
    }
    return { success: true };
  }

  async delete(id: number, userId) {
    try {
      let userData = await User.findOne({ where: { id: userId }, relations: ['organization'] });
      let taskLogHour = await LogHour.findOne({ where: { id }, relations: ['task'] });
      const newHours = Math.floor(moment.duration(taskLogHour.duration).asHours());
      const newremainingDuration = moment
        .duration(taskLogHour.duration)
        .subtract(Math.floor(moment.duration(taskLogHour.duration).asHours()), 'hours');
      const newremainingMinutes = Math.floor(newremainingDuration.asMinutes());
      await taskLogHour.remove();

      let taskactivity = new Activity();
      taskactivity.action = Event_Actions.LOG_HOURS_DELETED;
      taskactivity.actorId = userId;
      taskactivity.type = ActivityType.TASK;
      taskactivity.typeId = taskLogHour?.task?.id;
      taskactivity.remarks = `Log Hour ${newHours ? newHours : 0} hrs ${
        newremainingMinutes ? newremainingMinutes : 0
      } Mns Added by ${userData.fullName}`;
      await taskactivity.save();

      return { success: true };
    } catch (error) {
      console.log(error);
    }
  }

  async bulkDelete(userId: number, ids: number[]) {
    // try {
    const user = await User.findOne({
      where: { id: userId },
      relations: ['organization'],
    });

    const logHours = await LogHour.find({
      where: { id: In(ids) },
      relations: ['task'],
    });

    const preferences = await OrganizationPreferences.findOne({
      where: { organization: user.organization.id },
      order: { id: 'DESC' },
    });

    const taskPreferences = preferences?.['taskPreferences'];
    const isPrevious = taskPreferences?.['isPrevious'];
    const previousDayDate = taskPreferences?.['previousDayDate'];

    //  Check before deleting
    for (const logHour of logHours) {
      const isValidDateForEdit = isPrevious
        ? moment(logHour?.completedDate).add(previousDayDate, 'days').format('YYYY-MM-DD') >=
          moment().format('YYYY-MM-DD')
        : moment(logHour?.completedDate).isSame(moment(), 'day');

      if (!isValidDateForEdit) {
        throw new BadRequestException("You can't delete log hours that are Not editable");
      }
      if (logHour.approvalStatus == EXPENDITURE_STATUS.APPROVED) {
        throw new BadRequestException("You can't delete Approved Log Hours");
      }
    }

    //  Proceed to deletion only if all are invalid for edit
    for (const logHour of logHours) {
      const duration = moment.duration(logHour.duration);
      const hours = Math.floor(duration.asHours());
      const remainingDuration = duration.subtract(hours, 'hours');
      const minutes = Math.floor(remainingDuration.asMinutes());

      await logHour.remove();

      if (logHour.task?.id) {
        const activity = new Activity();
        activity.action = Event_Actions.LOG_HOURS_DELETED;
        activity.actorId = userId;
        activity.type = ActivityType.TASK;
        activity.typeId = logHour.task.id;
        activity.remarks = `Log Hour ${hours || 0} hrs ${minutes || 0} Mns Deleted by ${
          user.fullName
        }`;
        await activity.save();
      }
    }

    return { success: true, message: `${logHours.length} log hours deleted.` };
    // } catch (error) {
    //   console.error(error);
    //   return { success: false, message: 'Error while deleting log hours' };
    // }
  }

  // async bulkDelete(userId: number, ids: number[]) {
  //   console.log({ userId, ids })
  //   try {
  //     const user = await User.findOne({
  //       where: { id: userId },
  //       relations: ['organization'],
  //     });

  //     const logHours = await LogHour.find({
  //       where: { id: In(ids) },
  //       relations: ['task'],
  //     });

  //     const preferences = await OrganizationPreferences.findOne({
  //       where: { organization: user.organization.id },
  //       order: { id: 'DESC' },
  //     });
  //     const taskPreferences = preferences?.['taskPreferences'];
  //     const isPrevious = taskPreferences?.['isPrevious'];
  //     const previousDayDate = taskPreferences?.['previousDayDate'];

  //     for (const logHour of logHours) {

  //       const isValidDateForEdit = isPrevious
  //         ? moment(logHour?.completedDate)
  //           .add(previousDayDate, 'days')
  //           .format("YYYY-MM-DD") >= moment().format("YYYY-MM-DD")
  //         : moment(logHour?.completedDate).isSame(moment(), 'day'); // Allow "Edit" if "isPrevious" is false

  //       const duration = moment.duration(logHour.duration);
  //       const hours = Math.floor(duration.asHours());
  //       const remainingDuration = duration.subtract(hours, 'hours');
  //       const minutes = Math.floor(remainingDuration.asMinutes());

  //       // Delete log hour
  //       await logHour.remove();

  //       // Log activity
  //       if (logHour.task?.id) {
  //         const activity = new Activity();
  //         activity.action = Event_Actions.LOG_HOURS_DELETED;
  //         activity.actorId = userId;
  //         activity.type = ActivityType.TASK;
  //         activity.typeId = logHour.task.id;
  //         activity.remarks = `Log Hour ${hours || 0} hrs ${minutes || 0} Mns Deleted by ${user.fullName}`;
  //         await activity.save();
  //       }
  //     }

  //     return { success: true, message: `${logHours.length} log hours deleted.` };
  //   } catch (error) {
  //     console.error(error);
  //     return { success: false, message: 'Error while deleting log hours' };
  //   }
  // }

  async startTimer(userId: number, body: StartTimerDto) {
    let user = await User.findOne({ where: { id: userId } });
    let task = await Task.findOne({
      where: { id: body.taskId },
      relations: ['members', 'taskLeader', 'client', 'clientGroup'],
    });

    if (!task) {
      throw new BadRequestException('Invalid task ID');
    }
    const startedLogHours = await this.findStartedLogHoursByUserId(userId);
    const existingTaskNumber =
      startedLogHours.length > 0 ? startedLogHours[0].task.taskNumber : null;

    let assignedMember = task.members.some((member) => {
      return member.id === userId;
    });

    let taskLeader = task.taskLeader.filter((item) => item.id === userId);

    if (!assignedMember && !taskLeader.length) {
      throw new ForbiddenException('You are not allowed to start timer for this task');
    }
    let existingTaskLogHour = await LogHour.findOne({
      where: {
        status: TimerStatus.STARTED,
        user: { id: userId },
      },
    });
    if (
      existingTaskLogHour &&
      existingTaskLogHour.status == TimerStatus.STARTED &&
      existingTaskLogHour.user.id == userId
    ) {
      throw new BadRequestException(`The timer for this task ID ${existingTaskNumber}`);
    }
    let taskLogHour = new LogHour();
    taskLogHour.startTime = body.startTime;
    taskLogHour.status = TimerStatus.STARTED;
    taskLogHour.task = task;
    taskLogHour.user = user;
    taskLogHour.client = task.client;
    taskLogHour.clientGroup = task.clientGroup;
    taskLogHour['userId'] = userId;
    await taskLogHour.save();
    return { success: true };
  }

  async endTimer(id: number, userId: number, body: EndTimerDto) {
    let taskLogHour = await LogHour.findOne({ where: { id }, relations: ['user'] });
    const { startTime, endTime } = dateFormation(
      moment().format('YYYY-MM-DD'),
      moment().format('YYYY-MM-DD'),
    );
    let totalLogHours = await getManager().query(
      `SELECT SUM(duration) as count FROM log_hour WHERE user_id=${userId} AND Date(completed_date) >='${startTime}' and Date(completed_date) <='${endTime}'`,
    );
    if (taskLogHour.user?.id !== userId) {
      throw new ForbiddenException('You are not allowed to end timer for this task');
    }
    taskLogHour.endTime = +body.endTime;
    taskLogHour.duration = +body.endTime - +taskLogHour.startTime;
    const duration =
      body.endTime - +taskLogHour.startTime - ((body.endTime - +taskLogHour.startTime) % 60000);
    if (duration > 0 && totalLogHours[0].count < 86400000) {
      taskLogHour.duration = +duration;
      taskLogHour.status = TimerStatus.STOPPED;
      taskLogHour.completedDate = moment().format('YYYY-MM-DD');
      taskLogHour['userId'] = userId;
      await taskLogHour.save();
    } else if (totalLogHours[0].count > 86400000) {
      throw new BadRequestException('24 hours completed');
    } else {
      taskLogHour.duration = 0;
      taskLogHour.status = TimerStatus.STOPPED;
      taskLogHour.completedDate = moment().format('YYYY-MM-DD');
      taskLogHour['userId'] = userId;
      await taskLogHour.save();
    }

    return { success: true };
  }

  // @Cron(CronExpression.EVERY_MINUTE)
  async sendLogHoursReportToEmployee() {
    if (process.env.Cron_Running === 'true') {
      console.log('cron instance is running', new Date());

      // @Cron(CronExpression.EVERY_HOUR)
      // async sendLogReportToEmployee() {
      //   const cronData = new CronActivity();
      //   cronData.cronType = `Multiple Cron Running`;
      //   cronData.cronDate = moment().toDate().toString();
      //   cronData.startTime = moment().format("YYYY-MM-DD HH:mm:ss");
      //   const cornActivityID = await cronData.save();

      //   console.log('checking log');

      //   const getcornActivityID = await createQueryBuilder(CronActivity,'cronActivity')
      //     .where('id = :id',{ id: cornActivityID.id })
      //     .getOne();
      //   getcornActivityID.responseData = "Completed";
      //   getcornActivityID.endTime = moment().format("YYYY-MM-DD HH:mm:ss");
      //   await getcornActivityID.save();
      // }
    } else {
      console.log('Sending log hours report to employees from jenkins', new Date());


    }
  }

  // @Cron(CronExpression.EVERY_DAY_AT_4AM)
  // async sendInvocieOverDueMessage() {
  //   if (process.env.Cron_Running === 'true') {
  //     const userIdTwo = GetUserIdOne();
  //     if (userIdTwo !== undefined) {
  //       const entityManager = getManager();
  //       const getUserQuery = `SELECT organization_id,full_name FROM user where id = ${userIdTwo};`;
  //       const getUser = await entityManager.query(getUserQuery);
  //       const orgId = getUser[0].organization_id;
  //       const userName = getUser[0].full_name;
  //       const getInvoiceQuery = `SELECT id,invoice_number,invoice_due_date,status FROM invoice where organization_id = ${orgId};`;
  //       const getInvoice = await entityManager.query(getInvoiceQuery);
  //       const presentDate = new Date();
  //       const invoiceOverDueList = getInvoice.filter(
  //         (item) =>
  //           item.invoice_due_date < presentDate &&
  //           item.status !== 'PAID' &&
  //           item.status !== 'CANCELLED',
  //       );
  //       const invoiceList = invoiceOverDueList.map((item) => ({
  //         number: item.invoice_number,
  //         dueDate: new Date(item.invoice_due_date).toISOString().substring(0, 10),
  //       }));
  //       const invoiceString = invoiceList.map(
  //         (item) => `  ${item.number}  \n` + '                        ',
  //       );
  //       const getRoleQuery = `SELECT id FROM role where organization_id = ${orgId}  and name = "Admin";`;
  //       let getRole = await entityManager.query(getRoleQuery);
  //       const role_id = getRole[0].id;

  //       const getUserTwoQuery = `select id,email from user where organization_id=${orgId} and role_id = ${role_id} and type = 'ORGANIZATION'`;
  //       let getUserTwo = await entityManager.query(getUserTwoQuery);
  //       const userIDs = getUserTwo.map((row) => [row.id, row.email]);
  //       const user: User[] = userIDs.map((row) => row[0]);

  //       if (invoiceString.length !== 0) {
  //         let title = `Invoice Overdue`;
  //         let body = `List of Invoices Overdue : ${invoiceString}`;
  //         const key = 'INVOICE_OVERDUE_PUSH';
  //         // insertINTOnotification(title, body, user, orgId);
  //         insertINTONotificationUpdate(title, body, user, orgId, key);

  //         for (let a of userIDs) {
  //           let getEmailQuery = `SELECT id,  email,full_name FROM user where id = ${a[0]} and type = 'ORGANIZATION';`;
  //           let getEmail = await entityManager.query(getEmailQuery);
  //           let mail = getEmail[0].email;
  //           let fullname = getEmail[0].full_name;
  //           let id = getEmail[0].id;
  //           let data = {
  //             invoiceNumber: invoiceString,
  //             username: fullname,
  //             userName: userName,
  //           };
  //           let IData = {
  //             id,
  //             key: 'INVOICE_OVERDUE_MAIL',
  //             data: data,
  //             subject: `Invoice Over Due`,
  //             email: mail,
  //             filePath: 'invoice-overdue',
  //           };
  //           await sendnewMail(IData);
  //         }
  //       }
  //     }
  //   }
  // }

  // async addDailyLogHourTimeSheet(userId: number, body: any) {
  //   const { timeSheetItems } = body.data;

  //   let DurationTime = 0;
  //   for (let i of timeSheetItems) {
  //     DurationTime += i.duration;
  //   }
  //   const { startTime, endTime } = dateFormation(body.dateSelectd, body.dateSelectd);
  //   let totalLogHours = await getManager().query(
  //     `SELECT SUM(duration) as count FROM log_hour WHERE user_id=${userId} AND Date(completed_date) >='${startTime}' and Date(completed_date) <='${endTime}'`,
  //   );
  //   let totalLogHoursCount = Number(totalLogHours[0].count) + Number(DurationTime);
  //   if (totalLogHoursCount > 86400000) {
  //     throw new BadRequestException('24 hours completed');
  //   }
  //   let user = await User.findOne({ where: { id: body?.selectedUser } });

  //   for (let item of timeSheetItems) {
  //     if (item.logHourType === 'TASK') {
  //       let client = null;
  //       let task = await Task.findOne({
  //         where: { id: item.task.id },
  //         relations: ['client'],
  //       });
  //       if (item?.client?.id) {
  //         client = await Client.findOne({ where: { id: item.client.id } });
  //       } else {
  //         client = task.client;
  //       }

  //       let logHour = new LogHour();
  //       logHour.duration = item.duration;
  //       logHour.description = item.description;
  //       logHour.client = client;
  //       logHour.task = task;
  //       logHour.type = item.logHourType;
  //       logHour.title = item.title;
  //       logHour.user = user;
  //       logHour.status = TimerStatus.STOPPED;
  //       logHour.completedDate = body.dateSelectd;

  //       logHour.save();
  //     } else if (item.logHourType === 'GENERAL') {
  //       let logHour = new LogHour();
  //       logHour.title = item.title;
  //       logHour.description = item.description;
  //       logHour.type = item.logHourType;
  //       logHour.duration = item.duration;
  //       logHour.user = user;
  //       logHour.status = TimerStatus.STOPPED;
  //       logHour.completedDate = body.dateSelectd;
  //       logHour.save();
  //     }
  //   }
  // }

  async addDailyLogHourTimeSheet(userId: number, body: any) {
    const logInUser = await User.findOne({
      where: {
        id: userId,
      },
      relations: ['organization', 'organization.organizationPreferences'],
    });
    const { timeSheetItems } = body.data;
    let DurationTime = 0;
    for (let item of timeSheetItems) {
      DurationTime += item.duration;
    }

    const { startTime, endTime } = dateFormation(body.dateSelectd, body.dateSelectd);
    let totalLogHours = await getManager().query(
      `SELECT SUM(duration) as count FROM log_hour WHERE user_id = ? AND Date(completed_date) >= ? AND Date(completed_date) <= ?`,
      [body.selectedUser, startTime, endTime],
    );

    let totalLogHoursCount = Number(totalLogHours[0].count) + Number(DurationTime);
    if (totalLogHoursCount > 86400000) {
      throw new BadRequestException('24 hours completed');
    }

    let user = await User.findOne({
      where: { id: body.selectedUser },
      relations: ['organization'],
    });
    if (!user) {
      throw new BadRequestException('User not found');
    }

    // **Validate new timesheet items against each other first**
    for (let i = 0; i < timeSheetItems.length; i++) {
      const itemA = timeSheetItems[i];
      const startA = moment(itemA.startDateTime, 'HH:mm').valueOf();
      const endA = moment(itemA.endDateTime, 'HH:mm').valueOf();

      for (let j = i + 1; j < timeSheetItems.length; j++) {
        const itemB = timeSheetItems[j];
        const startB = moment(itemB.startDateTime, 'HH:mm').valueOf();
        const endB = moment(itemB.endDateTime, 'HH:mm').valueOf();

        if (
          (startA < endB && startA >= startB) ||
          (startB < endA && startB >= startA) ||
          startA === endB ||
          startB === endA
        ) {
          throw new BadRequestException(
            `Timesheet items overlap with each other "${
              itemA.logHourType === 'GENERAL' ? itemA?.title : itemA?.task?.name
            }", "${itemB.logHourType === 'GENERAL' ? itemB?.title : itemB?.task?.name}"`,
          );
        }
      }
    }

    // **Pre-check Phase 2: Check overlaps with the database log entries**
    for (let item of timeSheetItems) {
      const startTimeNew = moment(item.startDateTime, 'HH:mm').format('HH:mm:ss');
      const endTimeNew = moment(item.endDateTime, 'HH:mm').format('HH:mm:ss');
      const completedDate = moment(body.dateSelectd).format('YYYY-MM-DD');

      const overlappingLogHours = await getManager().query(
        `SELECT id 
       FROM log_hour 
       WHERE user_id = ? 
         AND DATE(completed_date) = ? 
         AND (
           (? BETWEEN start_time_new AND end_time_new) OR
           (? BETWEEN start_time_new AND end_time_new) OR
           (start_time_new BETWEEN ? AND ?) OR
           (end_time_new BETWEEN ? AND ?)
         )`,
        [
          body.selectedUser,
          completedDate,
          startTimeNew,
          endTimeNew,
          startTimeNew,
          endTimeNew,
          startTimeNew,
          endTimeNew,
        ],
      );

      if (overlappingLogHours.length > 0) {
        throw new BadRequestException(
          `Timesheet item overlaps with already logged hours for title: "${item.title}"`,
        );
      }
    }

    const entityManager = getManager();
    let adminUser = await createQueryBuilder(User, 'user')
      .leftJoinAndSelect('user.organization', 'organization')
      .leftJoinAndSelect('user.role', 'role')
      .where('organization.id = :id', { id: user.organization.id })
      .andWhere('role.defaultRole = :defaultRole', { defaultRole: 1 })
      .getOne();

    const result = await entityManager.query(
      `
      WITH RECURSIVE manager_hierarchy AS (
          -- Base case: Start with the given user_id
          SELECT user_id, manager_id
          FROM organization_hierarchy
          WHERE user_id = ?
          
          UNION ALL
          
          -- Recursive case: Fetch the next level manager until manager_id = 123826
          SELECT oh.user_id, oh.manager_id
          FROM organization_hierarchy oh
          INNER JOIN manager_hierarchy mh ON oh.user_id = mh.manager_id
          WHERE mh.manager_id != ? -- Stop recursion when manager_id = admin_id
      )
      -- Final output: Get all user_id in the hierarchy
      SELECT user_id
      FROM manager_hierarchy
  `,
      [user.id, adminUser.id],
    );

    const mangers = result?.map((result) => result.user_id);
    mangers.push(adminUser.id);
    const filterReqUser = mangers.filter((u) => u != user.id);

    let users = await User.find({
      where: {
        id: In(filterReqUser),
      },
    });

    const organizationPreferences = await OrganizationPreferences.findOne({
      where: { organization: user.organization.id },

      order: { id: 'DESC' },
    });

    for (let item of timeSheetItems) {
      let logHour = new LogHour();
      logHour.duration = item?.duration;
      logHour.description = item?.description;
      logHour.type = item?.logHourType;
      logHour.user = user;
      logHour.status = TimerStatus.STOPPED;
      logHour['userId'] = userId;
      logHour.completedDate = body?.dateSelectd;
      logHour.createdBy = logInUser;
      logHour.billable = item?.billable;
      if (item.enterInHours === 'CLOCK') {
        logHour.startTimeNew = item.startDateTime;
        logHour.endTimeNew = item.endDateTime;
      }

      if (item.logHourType === 'TASK') {
        let task = await Task.findOne({
          where: { id: item?.task?.id },
          relations: ['client', 'clientGroup'],
        });
        if (!task) {
          throw new BadRequestException('Task not found');
        }

        let client = item?.client?.id
          ? await Client.findOne({ where: { id: item?.client?.id } })
          : task?.client;

        let clientGroup = item?.clientGroup?.id
          ? await ClientGroup.findOne({ where: { id: item?.clientGroup?.id } })
          : task?.clientGroup;

        logHour.client = client;
        logHour.clientGroup = clientGroup;
        logHour.task = task;
        logHour.title = item?.title;
      } else if (item.logHourType === 'GENERAL') {
        logHour.title = item?.logHourTitle?.name;
        logHour.logHourTitleId = item?.logHourTitle?.id;
        let client = item?.client?.id
          ? await Client.findOne({ where: { id: item?.client?.id } })
          : null;

        let clientGroup = item?.clientGroup?.id
          ? await ClientGroup.findOne({ where: { id: item?.clientGroup?.id } })
          : null;

        logHour.client = client;
        logHour.clientGroup = clientGroup;
      }
      if (
        item.logHourType === 'TASK' &&
        organizationPreferences?.taskPreferences?.['isTaskLeader']
      ) {
        const taskLeaders = await Task.findOne({
          where: { id: item?.task?.id },
          relations: ['taskLeader', 'members'],
        });
        const taskLeaderss = taskLeaders.taskLeader;
        // const filterTLeaders = aa.filter(u => (taskLeaders.members.map(i => i.id)).includes(u.id));
        const filterTLeaders = taskLeaderss.filter((u) => u.id != user.id);

        users.push(...filterTLeaders);
        users = Array.from(new Map(users.map((user) => [user.id, user])).values());
      }

      if (
        user.id != adminUser.id &&
        logInUser.organization.organizationPreferences?.[0]?.approvals?.['logHour']
      ) {
        logHour.approvalStatus = EXPENDITURE_STATUS.PENDING;
        logHour.managers = users;
        logHour.requestedAt = moment().utc().format('YYYY-MM-DD HH:mm:ss.SSSSSS');
      }
      logHour['userId'] = userId;
      await logHour.save();
    }
  }

  async getUserTimesheet(query: any) {
    const userId = query.userId;

    let user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
    let logHours = createQueryBuilder(LogHour, 'logHour')
      .leftJoinAndSelect('logHour.user', 'user')
      .leftJoin('user.organization', 'organization')
      .leftJoinAndSelect('logHour.task', 'task')
      .leftJoinAndSelect('logHour.client', 'client')
      .leftJoinAndSelect('logHour.clientGroup', 'clientGroup')
      .where('user.id = :userId', { userId })
      .andWhere('organization.id = :orgId', { orgId: user.organization.id })
      .andWhere('logHour.status = :status', { status: TimerStatus.STOPPED })
      .andWhere('logHour.duration != :duration', { duration: 0 })
      .andWhere('task.parentTask is null')
      // .andWhere(
      //   new Brackets((qb) => {
      //     qb.where('logHour.approvalStatus IS NULL').orWhere(
      //       'logHour.approvalStatus = :approvedStatus',
      //       { approvedStatus: ExpenditureStatus.APPROVED },
      //     )
      //   }),
      // )
      .skip(query.offset || 0)
      .take(query.limit || 10)
      .orderBy('logHour.completedDate', 'DESC');

    const sort = typeof query?.sort === 'string' ? JSON.parse(query.sort) : query?.sort;
    if (sort?.column) {
      const columnMap: Record<string, string> = {
        completedDate: 'logHour.completedDate',
        displayName: 'client.displayName',
        type: 'logHour.type',
        tasknumber: 'task.taskNumber',
        title: 'logHour.title',
        duration: 'logHour.duration',
      };
      const column = columnMap[sort.column] || sort.column;
      logHours.orderBy(column, sort.direction.toUpperCase());
    } else {
      logHours.orderBy('logHour.completedDate', 'DESC');
      logHours.orderBy('logHour.id', 'DESC');
    }

    if (query.search) {
      logHours.andWhere('logHour.title LIKE :search', { search: `%${query.search}%` });
      logHours.orWhere('task.name LIKE :search', { search: `%${query.search}%` });
      logHours.orWhere('client.displayName LIKE :search', { search: `%${query.search}%` });
    }
    if (query.fromDate && query.toDate) {
      const { startTime, endTime } = dateFormation(query.fromDate, query.toDate);
      if (query.fromDate) {
        logHours.andWhere('logHour.completedDate >= :fromDate', { fromDate: startTime });
      }

      if (query.toDate) {
        logHours.andWhere('logHour.completedDate <= :toDate', { toDate: endTime });
      }
    } else if (query.date) {
      logHours.andWhere('DATE(logHour.completedDate) = :completedDate', {
        completedDate: query.date,
      });
    }

    let data = await logHours.getManyAndCount();

    return {
      totalCount: data[1],
      result: data[0],
    };
  }

  async exportuserTimeSheetReport(userId: number, query: GetUserLogHoursStatsDto) {
    const newQuery = { ...query, offset: 0, limit: 100000000 };
    let tasks = await this.getUserTimesheet(newQuery);

    const formatDuration = (duration) => {
      if (duration === 0) {
        return '00:00';
      }

      const hours = Math.floor(duration / (1000 * 60 * 60));
      const minutes = Math.floor((duration % (1000 * 60 * 60)) / (1000 * 60));

      const formattedHours = String(hours).padStart(2, '0');
      const formattedMinutes = String(minutes).padStart(2, '0');

      return `${formattedHours}:${formattedMinutes}`;
    };
    if (!tasks.result.length) throw new BadRequestException('No Data for Export');

    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Timesheet');
    const headers = [
      { header: 'S.No', key: 'serialNo' },
      { header: 'Type', key: 'type' },
      { header: 'Date', key: 'completedDate' },
      { header: 'Client / Client Group', key: 'client' },
      { header: 'Task ID', key: 'taskNumber' },
      { header: 'Title | Task Name', key: 'name' },
      { header: 'Start Time', key: 'startTime' },
      { header: 'End Time', key: 'endTime' },
      { header: 'Duration', key: 'duration' },
      { header: 'Status', key: 'status' },
      { header: 'Description', key: 'description' },
    ];

    worksheet.columns = headers;

    let serialCounter = 1; // Initialize a counter

    // Initialize array to store max lengths for each column
    const columnMaxLengths = Array(headers.length).fill(0);

    tasks.result.forEach((task) => {
      function capitalizeFirstLetter(string) {
        return string.charAt(0).toUpperCase() + string.slice(1);
      }
      const rowData = {
        serialNo: serialCounter++,
        type: task.type,
        completedDate: formatDate(task.completedDate),
        client: task?.client ? task?.client?.displayName : task?.clientGroup?.displayName,
        taskNumber: task?.task?.taskNumber,
        name: task?.task?.name,
        startTime: task?.startTimeNew
          ? moment(task.startTimeNew, 'HH:mm:ss').format('hh:mm A')
          : '--',
        endTime: task?.endTimeNew ? moment(task.endTimeNew, 'HH:mm:ss').format('hh:mm A') : '--',
        duration: formatDuration(task.duration),
        description: task.description,
        status: task?.approvalStatus,
      };

      const row = worksheet.addRow(rowData);
      const statusCell = row.getCell('status');
      switch (rowData.status?.toLowerCase()) {
        case 'pending':
          statusCell.font = { color: { argb: 'FFA500' }, bold: true }; // Orange
          break;
        case 'rejected':
          statusCell.font = { color: { argb: 'F63338' }, bold: true };
          break;
        case 'approved':
          statusCell.font = { color: { argb: '008000' }, bold: true };
          break;
        default:
          statusCell.font = { color: { argb: '000000' }, bold: true };
          break;
      }

      worksheet.columns.forEach((column, colIndex) => {
        const headerLength = column.header?.length || 0; // Length of header
        const cellLength = rowData[column.key]?.toString().length || 0; // Length of cell value
        columnMaxLengths[colIndex] = Math.max(
          columnMaxLengths[colIndex] || 0,
          headerLength,
          cellLength,
        );
      });
    });

    // Apply column widths with padding
    worksheet.columns.forEach((column, colIndex) => {
      column.width = columnMaxLengths[colIndex] + 3; // Add padding for readability
    });

    // Style the header row and center alignment
    const headerRow = worksheet.getRow(1);
    headerRow.font = { bold: true };
    headerRow.eachCell((cell) => {
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '64B5F6' },
      };
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' },
      };
      cell.alignment = { horizontal: 'center', vertical: 'middle' };
    });

    worksheet.columns.forEach((column) => {
      if (column.key === 'name' || column.key === 'taskLeaders') {
        column.width = 50;
        column.alignment = { wrapText: true, horizontal: 'center', vertical: 'middle' };
      } else if (column.key === 'description') {
        column.width = 100;
        column.alignment = { wrapText: true, horizontal: 'center', vertical: 'middle' };
      } else {
        column.alignment = { horizontal: 'center', vertical: 'middle' };
      }
    });

    // Apply wrapText for all rows
    worksheet.eachRow((row) => {
      row.eachCell((cell) => {
        cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true }; // Center alignment and wrap text
      });
    });

    // Freeze the header row
    worksheet.views = [{ state: 'frozen', ySplit: 1 }];

    const buffer = await workbook.xlsx.writeBuffer();
    return buffer;
  }
  async getUserTimesheetStats(query: any) {
    const userId = query.userId;
    const user = await User.findOne({ where: { id: userId }, relations: ['organization'] });

    const organizationPreferenses = await OrganizationPreferences.findOne({
      where: { organization: user.organization },
    });
    const holidayPreferences: any = organizationPreferenses?.holidayPreferences;
    let standardWorkingHours: any = '00';
    if (holidayPreferences) {
      const updateOvertime = holidayPreferences?.updateovertime;
      if (updateOvertime) {
        const hoursInMilliseconds = parseInt(updateOvertime.hours.value) * 60 * 60 * 1000;
        const minutesInMilliseconds = parseInt(updateOvertime.minutes.value) * 60 * 1000;

        standardWorkingHours = hoursInMilliseconds + minutesInMilliseconds;
      }
    }

    const sql = `
            SELECT 
              log_hour.user_id as userId,
        user.full_name as fullName,
              DATE(log_hour.completed_date) AS logDate,
              SUM(CASE WHEN log_hour.type = 'GENERAL' THEN duration ELSE 0 END) AS generalLogHours,
              SUM(CASE WHEN log_hour.type = 'TASK' THEN duration ELSE 0 END) AS taskLogHours,
              @standardWorkingHours := ${standardWorkingHours} AS standardWorkingHours,
              @totalLoggedHours := SUM(log_hour.duration) AS totalLoggedHours,
              @overtime := GREATEST(@totalLoggedHours - @standardWorkingHours, 0) AS overtime,
              @undertime := GREATEST(@standardWorkingHours  - @totalLoggedHours, 0) AS undertime
            FROM log_hour 
            JOIN user ON log_hour.user_id = user.id
            WHERE user_id = ? AND DATE(completed_date) = ?
            AND (log_hour.approval_status is null or log_hour.approval_status=?)
            GROUP BY user_id, DATE(completed_date)
          `;

    // Execute the SQL query with parameters
    const result = await getManager().query(sql, [userId, query.date, ExpenditureStatus.APPROVED]);
    // this.getOrganizationTimesheet(userId);
    if (result[0]) {
      return result[0];
    } else {
      return {
        user_id: userId,
        log_date: query.date,
        generalLogHours: 0,
        taskLogHours: 0,
        standardWorkingHours: standardWorkingHours,
        totalLoggedHours: 0,
        overtime: 0,
        undertime: standardWorkingHours,
      };
    }
  }

  async timesheetReport(userId: number, query: any) {
    try {
      const user = await User.findOne({
        where: { id: userId },
        relations: ['organization'],
      });

      if (!user || !user.organization) {
        throw new Error('User or organization not found');
      }

      const organizationId = user.organization.id;

      const logHours = await createQueryBuilder(LogHour, 'logHour')
        .leftJoinAndSelect('logHour.user', 'user')
        .leftJoin('user.organization', 'organization')
        .leftJoinAndSelect('logHour.client', 'client')
        .leftJoinAndSelect('logHour.task', 'task')
        .leftJoinAndSelect('task.category', 'category')
        .leftJoinAndSelect('task.subCategory', 'subCategory')
        .leftJoinAndSelect('logHour.reviewer', 'reviewer')
        .leftJoinAndSelect(
          'user.Attendance',
          'Attendance',
          'DATE(Attendance.attendanceDate) = DATE(logHour.completedDate)', // Filter Attendance for the same date
        )
        .where('organization.id = :orgId', { orgId: organizationId });

      if (query.client) {
        logHours.andWhere('client.id');
      }

      if (query.dates) {
        let parsedDates = null;
        if (!query.isjson) {
          parsedDates = query.dates ? JSON.parse(query.dates) : null;
        } else {
          parsedDates = query.dates;
        }
        if (parsedDates) {
          const startDate = moment(parsedDates?.formDate, 'DD-MM-YYYY').format('YYYY-MM-DD');
          const endDate = moment(parsedDates?.toDate, 'DD-MM-YYYY').format('YYYY-MM-DD');
          logHours.andWhere('DATE(logHour.completedDate) BETWEEN :startDate AND :endDate', {
            startDate: startDate,
            endDate: endDate,
          });
        }
      } else if (query.date) {
        logHours.andWhere('DATE(logHour.completedDate) = :completedDate', {
          completedDate: moment(query.date).format('YYYY-MM-DD'),
        });
      }

      if (query?.billingType?.length) {
        let billableType = [];
        if (query?.billingType?.includes('billable')) {
          billableType.push(true);
        }
        if (query?.billingType?.includes('nonbillable')) {
          billableType.push(false);
        }
        logHours.andWhere('logHour.billable in (:...billable)', {
          billable: billableType,
        });
      }

      if (query?.approvalStatus?.length) {
        logHours.andWhere('logHour.approvalStatus in (:...approvalStatus)', {
          approvalStatus: query?.approvalStatus,
        });
      }

      if (query?.logHourType?.length) {
        logHours.andWhere('logHour.type in (:...type)', {
          type: query?.logHourType,
        });
      }

      if (query?.user?.length) {
        logHours.andWhere('user.id in (:...userId)', {
          userId: query?.user,
        });
      }

      if (query?.logHourTitles?.length) {
        logHours.andWhere('logHour.logHourTitleId in (:...logHourTitleId)', {
          logHourTitleId: query?.logHourTitles,
        });
      }

      if (query?.clients?.length) {
        logHours.andWhere('client.id in (:...clientId)', {
          clientId: query?.clients,
        });
      }
      if (query?.categories?.length) {
        logHours.andWhere('category.id in (:...categoryId)', {
          categoryId: query?.categories,
        });

        if (query?.subCategories?.length) {
          logHours.andWhere('subCategory.id in (:...subCategoryId)', {
            subCategoryId: query?.subCategories,
          });
        }
      }

      if (query?.search) {
        logHours.andWhere(
          new Brackets((qb) => {
            qb.where('task.name LIKE :search', {
              search: `%${query.search}%`,
            });
            qb.orWhere('client.displayName LIKE :search', {
              search: `%${query.search}%`,
            });
            qb.orWhere('task.taskNumber LIKE :search', {
              search: `%${query.search}%`,
            });
            qb.orWhere('user.fullName LIKE :search', {
              search: `%${query.search}%`,
            });
          }),
        );
      }

      const result = await logHours.getMany();

      let groupedData = {};
      let totals = { totalBillable: 0, totalNonBillable: 0, totalDuration: 0 };

      if (query.type === 'groupByDate') {
        groupedData = result.reduce((acc, log) => {
          const date = new Date(log.completedDate).toISOString().split('T')[0];

          if (!acc[date]) {
            acc[date] = {
              entries: [],
              totalBillable: 0,
              totalNonBillable: 0,
              totalDuration: 0,
            };
          }

          const duration = Number(log.duration) || 0; // Ensure duration is a number

          acc[date].entries.push(log);
          acc[date].totalDuration += duration;

          if (log.billable) {
            acc[date].totalBillable += duration;
            totals.totalBillable += duration;
          } else {
            acc[date].totalNonBillable += duration;
            totals.totalNonBillable += duration;
          }

          totals.totalDuration += duration;
          return acc;
        }, {});

        // Convert to array, sort in DESCENDING order, then convert back to an object
        groupedData = Object.fromEntries(
          Object.entries(groupedData).sort(
            ([dateA], [dateB]) => new Date(dateB).getTime() - new Date(dateA).getTime(),
          ),
        );
      } else if (query.type === 'groupByUser') {
        groupedData = result.reduce((acc, log) => {
          const userName = log.user?.fullName || 'Unknown User';

          if (!acc[userName]) {
            acc[userName] = {
              entries: [],
              totalBillable: 0,
              totalNonBillable: 0,
              totalDuration: 0,
            };
          }

          const duration = Number(log.duration) || 0; // Ensure duration is a number

          acc[userName].entries.push(log);
          acc[userName].totalDuration += duration;

          if (log.billable) {
            acc[userName].totalBillable += duration;
            totals.totalBillable += duration;
          } else {
            acc[userName].totalNonBillable += duration;
            totals.totalNonBillable += duration;
          }

          totals.totalDuration += duration;
          return acc;
        }, {});

        // Convert to array, sort in ALPHABETICAL order, then convert back to object
        groupedData = Object.fromEntries(
          Object.entries(groupedData).sort(([userA], [userB]) => userA.localeCompare(userB)),
        );
      }

      const transformedData = Object.keys(groupedData).map((key) => ({
        title: key,
        entries: groupedData[key].entries,
        totalBillable: groupedData[key].totalBillable,
        totalNonBillable: groupedData[key].totalNonBillable,
        totalDuration: groupedData[key].totalDuration,
      }));

      return {
        groupedData: transformedData,
        globalTotals: {
          totalBillable: totals.totalBillable,
          totalNonBillable: totals.totalNonBillable,
          totalDuration: totals.totalDuration,
        },
      };
    } catch (error) {
      console.error('Error occurred while fetching new timesheet:', error);
      throw error;
    }
  }

  async exportLogHoursTimesheetReport(userId: number, body: any) {
    const newQuery = { ...body, offset: 0, limit: 100000000, isjson: true };
    let timesheets = await this.timesheetReport(userId, newQuery);

    const workbook = new ExcelJS.Workbook();

    // Sheet 1 - Group by User
    const userSheet = workbook.addWorksheet('Group by User');
    const userHeaders = [
      { header: 'S.No', key: 'serialNo' },
      { header: 'User Name', key: 'userName' },
      { header: 'Date', key: 'date' },
      { header: 'Attendance Type', key: 'attendanceType' },
      { header: 'Check-In & Check-Out', key: 'checkInOut' },
      { header: 'Hours (HH:MM)', key: 'hours' },
      { header: 'Client / Clients Group', key: 'client' },
      { header: 'Log Hr Type', key: 'logHrType' },
      { header: 'Service Category', key: 'serviceCategory' },
      { header: 'Service Sub-Category', key: 'subCategory' },
      { header: 'Task ID', key: 'taskId' },
      { header: 'Task Name | Title', key: 'taskName' },
      { header: 'Billing Type', key: 'billingType' },
      { header: 'Start & End Time', key: 'StartEndTime' },
      { header: 'Duration', key: 'duration' },
      { header: 'Approved / Declined By', key: 'approvedDeclined' },
      { header:" Description" , key:'description'}
    ];
    userSheet.columns = userHeaders;

    let userSerial = 1;

    // Group entries by userName
    const groupedByUser = {};

    timesheets.groupedData.forEach((group) => {
      group.entries.forEach((entry) => {
        const formattedDate = entry.completedDate
          ? moment(entry.completedDate).format('DD-MM-YYYY')
          : 'Unknown Date';

        if (!groupedByUser[entry.user?.fullName]) {
          groupedByUser[entry.user?.fullName] = [];
        }
        groupedByUser[entry.user?.fullName].push({ ...entry, date: formattedDate });
      });
    });

    // Sort users and process entries
    const sortedUsers = Object.keys(groupedByUser).sort();

    sortedUsers?.forEach((userName) => {
      const entries = groupedByUser[userName].sort((a, b) => {
        const dateA = a.date ? moment(a.date, 'DD-MM-YYYY').toDate().getTime() : 0;
        const dateB = b.date ? moment(b.date, 'DD-MM-YYYY').toDate().getTime() : 0;
        return dateB - dateA; // Sort by latest dates first
      });

      entries.forEach((entry) => {
        userSheet.addRow({
          serialNo: userSerial++,
          userName: userName,
          date: entry.date,
          attendanceType: entry.user?.Attendance?.[0]?.type || '',
          checkInOut: entry?.user?.Attendance?.[0]
            ? `${
                entry?.user?.Attendance?.[0]?.checkin_time
                  ? moment(entry?.user?.Attendance?.[0]?.checkin_time).format('hh:mm A')
                  : ' '
              } - ${
                entry?.user?.Attendance?.[0]?.checkout_time
                  ? moment(entry?.user?.Attendance?.[0]?.checkout_time).format('hh:mm A')
                  : ' '
              }`
            : ' ',
          hours: entry?.user?.Attendance?.[0]?.hours_logged
            ? entry.user.Attendance[0].hours_logged.split(':').slice(0, 2).join(':')
            : ' ',
          client: entry.client?.displayName || '',
          logHrType: capitalize(entry.type) || '',
          serviceCategory: entry.task?.category?.name || '',
          subCategory: entry.task?.subCategory?.name || '',
          taskId: entry.task?.taskNumber || '',
          taskName: entry.task?.name || entry.title || '',
          billingType: entry.billable ? 'Billable' : 'Non-Billable',
          StartEndTime: `${
            entry?.startTimeNew ? moment(entry.startTimeNew, 'HH:mm:ss').format('hh:mmA') : ' '
          } - ${entry?.endTimeNew ? moment(entry.endTimeNew, 'HH:mm:ss').format('hh:mmA') : ' '}`,
          // duration: entry?.duration
          //   ? moment.utc(+entry.duration).format("HH:mm")
          //   : " ",
          duration: entry?.duration ? +entry.duration / (1000 * 60 * 60 * 24) : null,

          approvedDeclined: entry.reviewer?.fullName || '',
          description: entry?.description || '',
        });
      });
    });

    userSheet.getColumn('duration').numFmt = '[h]:mm';

    // Freeze the header row
    userSheet.views = [{ state: 'frozen', ySplit: 1 }];

    // Style header row
    const userHeaderRow = userSheet.getRow(1);
    userHeaderRow.font = { bold: true };
    userHeaderRow.eachCell((cell) => {
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '64B5F6' },
      };
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' },
      };
      cell.alignment = { horizontal: 'center', vertical: 'middle' };
    });

    // Align all other rows to center
    userSheet.eachRow({ includeEmpty: false }, (row, rowNumber) => {
      if (rowNumber !== 1) {
        row.eachCell((cell, colNumber) => {
          cell.alignment = { horizontal: 'center', vertical: 'middle' };

          if (colNumber === 4) {
            // Assuming 'attendanceType' is in the 4th column
            const attendanceType = cell.value;

            if (typeof attendanceType === 'string') {
              const typeToFontColor: { [key: string]: string } = {
                Present: '#008000', // Green
                Absent: '#FF0000', // Red
                Leave: '#9932CC', // Purple
                Holiday: '#FFD700', // Gold
              };

              const fontColor = typeToFontColor[attendanceType] || '#000000'; // Default to black if not found

              cell.font = {
                color: { argb: fontColor.replace('#', '') },
                bold: true,
              };
            }
          }
          if (colNumber === 8) {
            // Assuming 'attendanceType' is in the 4th column
            const logHrType = cell.value;

            if (typeof logHrType === 'string') {
              const typeToFontColor: { [key: string]: string } = {
                General: '#18C6F6', // Green
                Task: '#F618C2', // Red
              };

              const fontColor = typeToFontColor[logHrType] || '#000000'; // Default to black if not found

              cell.font = {
                color: { argb: fontColor.replace('#', '') },
                bold: true,
              };
            }
          }
          if (colNumber === 13) {
            // Assuming 'attendanceType' is in the 4th column
            const billingType = cell.value;

            if (typeof billingType === 'string') {
              const typeToFontColor: { [key: string]: string } = {
                Billable: '#F69918', // Green
              };

              const fontColor = typeToFontColor[billingType] || '#372482'; // Default to black if not found

              cell.font = {
                color: { argb: fontColor.replace('#', '') },
                bold: true,
              };
            }
          }
        });
      }
    });
    userSheet.columns.forEach((column) => {
      if (column.key === 'userName' || column.key === 'client') {
        column.width = 50;
        column.alignment = { wrapText: true, horizontal: 'center', vertical: 'middle' }; // Enable text wrap
      } else {
        column.alignment = { horizontal: 'center', vertical: 'middle' }; // Apply center alignment for other columns
      }
    });

    // Auto-adjust column widths
    userSheet.columns.forEach((column) => {
      let maxLength = column.header?.toString().length || 10;
      column.eachCell({ includeEmpty: true }, (cell) => {
        const value = cell.value ? cell.value.toString() : '';
        maxLength = Math.max(maxLength, value.length);
      });
      column.width = maxLength + 3;
    });

    // Sheet 2 - Group by Date
    const dateSheet = workbook.addWorksheet('Group by Date');
    const dateHeaders = [
      { header: 'S.No', key: 'serialNo' },
      { header: 'Date', key: 'date' },
      { header: 'User Name', key: 'userName' },
      { header: 'Attendance Type', key: 'attendanceType' },
      { header: 'Check-In & Check-Out', key: 'checkInOut' },
      { header: 'Hours (HH:MM)', key: 'hours' },
      { header: 'Client / Clients Group', key: 'client' },
      { header: 'Log Hr Type', key: 'logHrType' },
      { header: 'Service Category', key: 'serviceCategory' },
      { header: 'Service Sub-Category', key: 'subCategory' },
      { header: 'Task ID', key: 'taskId' },
      { header: 'Task Name | Title', key: 'taskName' },
      { header: 'Billing Type', key: 'billingType' },
      { header: 'Start & End Time', key: 'StartEndTime' },
      { header: 'Duration', key: 'duration' },
      { header: 'Approved / Declined By', key: 'approvedDeclined' },
    ];
    dateSheet.columns = dateHeaders;

    let dateSerial = 1;

    // Group entries by completedDate (same logic reused)
    const groupedByDateSheet = {};
    timesheets.groupedData.forEach((group) => {
      group.entries.forEach((entry) => {
        const formattedDate = entry.completedDate
          ? moment(entry.completedDate).format('DD-MM-YYYY')
          : 'Unknown Date';

        if (!groupedByDateSheet[formattedDate]) {
          groupedByDateSheet[formattedDate] = [];
        }
        groupedByDateSheet[formattedDate].push({ ...entry, userName: group.title });
      });
    });

    // Sort and write to sheet
    const sortedDatesSheet = Object.keys(groupedByDateSheet).sort(
      (a, b) =>
        moment(a, 'DD-MM-YYYY').toDate().getTime() - moment(b, 'DD-MM-YYYY').toDate().getTime(),
    );

    sortedDatesSheet.forEach((dateKey) => {
      const entries = groupedByDateSheet[dateKey];

      entries.forEach((entry) => {
        dateSheet.addRow({
          serialNo: dateSerial++,
          date: dateKey,
          userName: entry.user?.fullName || '',
          attendanceType: entry.user?.Attendance?.[0]?.type || '',
          checkInOut: entry?.user?.Attendance?.[0]
            ? `${
                entry?.user?.Attendance?.[0]?.checkin_time
                  ? moment(entry?.user?.Attendance?.[0]?.checkin_time).format('hh:mm A')
                  : ' '
              } - ${
                entry?.user?.Attendance?.[0]?.checkout_time
                  ? moment(entry?.user?.Attendance?.[0]?.checkout_time).format('hh:mm A')
                  : ' '
              }`
            : ' ',
          hours: entry?.user?.Attendance?.[0]?.hours_logged
            ? entry.user.Attendance[0].hours_logged.split(':').slice(0, 2).join(':')
            : ' ',
          client: entry.client?.displayName || '',
          logHrType: capitalize(entry.type) || '',
          serviceCategory: entry.task?.category?.name || '',
          subCategory: entry.task?.subCategory?.name || '',
          taskId: entry.task?.taskNumber || '',
          taskName: entry.task?.name || entry.title || '',
          billingType: entry.billable ? 'Billable' : 'Non-Billable',
          StartEndTime: `${
            entry?.startTimeNew ? moment(entry.startTimeNew, 'HH:mm:ss').format('hh:mmA') : ' '
          } - ${entry?.endTimeNew ? moment(entry.endTimeNew, 'HH:mm:ss').format('hh:mmA') : ' '}`,
          // duration: entry?.duration
          //   ? moment.utc(+entry.duration).format("HH:mm")
          //   : " ",
          duration: entry?.duration ? +entry.duration / (1000 * 60 * 60 * 24) : null,

          approvedDeclined: entry.reviewer?.fullName || '',
        });
      });
    });
    // Freeze the header row
    dateSheet.views = [{ state: 'frozen', ySplit: 1 }];
    dateSheet.getColumn('duration').numFmt = '[h]:mm';

    // Style header row
    const dateHeaderRow = dateSheet.getRow(1);
    dateHeaderRow.font = { bold: true };
    dateHeaderRow.eachCell((cell) => {
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '64B5F6' },
      };
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' },
      };
      cell.alignment = { horizontal: 'center', vertical: 'middle' };
    });

    // Align all cell values to center except header
    dateSheet.eachRow({ includeEmpty: false }, (row, rowNumber) => {
      if (rowNumber !== 1) {
        row.eachCell((cell, colNumber) => {
          cell.alignment = { horizontal: 'center', vertical: 'middle' };

          if (colNumber === 4) {
            // Assuming 'attendanceType' is in the 4th column
            const attendanceType = cell.value;

            if (typeof attendanceType === 'string') {
              const typeToFontColor: { [key: string]: string } = {
                Present: '#008000', // Green
                Absent: '#FF0000', // Red
                Leave: '#9932CC', // Purple
                Holiday: '#FFD700', // Gold
              };

              const fontColor = typeToFontColor[attendanceType] || '#000000'; // Default to black if not found

              cell.font = {
                color: { argb: fontColor.replace('#', '') },
                bold: true,
              };
            }
          }
          if (colNumber === 8) {
            // Assuming 'attendanceType' is in the 4th column
            const logHrType = cell.value;

            if (typeof logHrType === 'string') {
              const typeToFontColor: { [key: string]: string } = {
                General: '#18C6F6', // Green
                Task: '#F618C2', // Red
              };

              const fontColor = typeToFontColor[logHrType] || '#000000'; // Default to black if not found

              cell.font = {
                color: { argb: fontColor.replace('#', '') },
                bold: true,
              };
            }
          }
          if (colNumber === 13) {
            // Assuming 'attendanceType' is in the 4th column
            const billingType = cell.value;

            if (typeof billingType === 'string') {
              const typeToFontColor: { [key: string]: string } = {
                Billable: '#F69918', // Green
              };

              const fontColor = typeToFontColor[billingType] || '#372482'; // Default to black if not found

              cell.font = {
                color: { argb: fontColor.replace('#', '') },
                bold: true,
              };
            }
          }
        });
      }
    });

    // Auto-adjust column widths
    dateSheet.columns.forEach((column) => {
      let maxLength = column.header?.toString().length || 10;
      column.eachCell({ includeEmpty: true }, (cell) => {
        const value = cell.value ? cell.value.toString() : '';
        maxLength = Math.max(maxLength, value.length);
      });
      column.width = maxLength + 3;
    });

    // Write the workbook to a buffer
    const buffer = await workbook.xlsx.writeBuffer();
    return buffer;
  }

  async logHourTime(userId: number, query: { selectedUser: number; dateSelectd: any }) {
    // Validate inputs
    if (!query.selectedUser || !query.dateSelectd) {
      throw new Error('selectedUser and dateSelected are required in the query.');
    }

    const sql = `
      SELECT 
        log_hour.user_id as userId,
        user.full_name as fullName,
        DATE(log_hour.completed_date) AS logDate,
        SUM(log_hour.duration) AS totalLoggedHours
      FROM log_hour 
      JOIN user ON log_hour.user_id = user.id
      WHERE log_hour.user_id = ? AND DATE(log_hour.completed_date) = ?
      GROUP BY log_hour.user_id, DATE(log_hour.completed_date)
    `;

    try {
      // Execute the SQL query with parameters
      const result = await getManager().query(sql, [query.selectedUser, query.dateSelectd]);
      return result?.[0];
    } catch (error) {
      console.error('Error executing SQL query:', error);
      throw new Error('Failed to fetch logged hours.');
    }
  }

  @Cron(CronExpression.EVERY_DAY_AT_4PM)
  async getOrganizationTimesheet() {
    if (process.env.Cron_Running === 'true') {
      const cronData = new CronActivity();
      cronData.cronType = 'TIME SHEET';
      cronData.cronDate = moment().toDate().toString();
      cronData.startTime = moment().format('YYYY-MM-DD HH:mm:ss');
      const cornActivityID = await cronData.save();

      const errorList = [];
      try {
        const totalOrganization = await Organization.createQueryBuilder('organization')
          .leftJoinAndSelect('organization.users', 'user')
          .leftJoinAndSelect('organization.organizationPreferences', 'organizationPreferences')
          .where(
            "DATE_FORMAT(STR_TO_DATE(JSON_UNQUOTE(JSON_EXTRACT(config, '$.expirydate')), '%Y-%m-%d'), '%Y-%m-%d') >= :expirydate",
            { expirydate: moment().format('YYYY-MM-DD') },
          )
          .andWhere(
            "JSON_UNQUOTE(JSON_EXTRACT(organizationPreferences.taskPreferences, '$.isEmailSend')) = 'true'",
          )
          .andWhere('user.status = :status', { status: 'active' })
          .andWhere('user.type = :type', { type: UserType.ORGANIZATION })
          .getMany();

        for (let organization of totalOrganization) {
          const users = organization?.users;
          const date = new Date();
          const formattedDateForQuery = moment(date).format('YYYY-MM-DD');

          const organizationPreferences = await OrganizationPreferences.findOne({
            where: { organization: organization },
          });

          const holidayPreferences: any = organizationPreferences?.holidayPreferences;
          let standardWorkingHours: number = 0;
          if (holidayPreferences) {
            const updateOvertime = holidayPreferences?.updateovertime;
            if (updateOvertime) {
              const hoursInMilliseconds = parseInt(updateOvertime.hours.value) * 60 * 60 * 1000;
              const minutesInMilliseconds = parseInt(updateOvertime.minutes.value) * 60 * 1000;
              standardWorkingHours = hoursInMilliseconds + minutesInMilliseconds;
            }
          }

          const TimeSheetReport = [];
          const formatedDate = moment(date).format('DD-MM-YYYY');

          for (let orgUser of users) {
            const sql = `
          SELECT
            log_hour.user_id as userId,
            user.full_name as fullName,
            user.mobile_number as mobileNumber,
            DATE(log_hour.completed_date) AS logDate,
            SUM(CASE WHEN log_hour.type = 'GENERAL' THEN log_hour.duration ELSE 0 END) AS generalLogHours,
            SUM(CASE WHEN log_hour.type = 'TASK' THEN log_hour.duration ELSE 0 END) AS taskLogHours,
            @standardWorkingHours := ${standardWorkingHours} AS standardWorkingHours,
            @totalLoggedHours := SUM(log_hour.duration) AS totalLoggedHours,
            @overtime := GREATEST(@totalLoggedHours - @standardWorkingHours, 0) AS overtime,
            @undertime := GREATEST(@standardWorkingHours - @totalLoggedHours, 0) AS undertime
          FROM log_hour
          JOIN user ON log_hour.user_id = user.id
          WHERE log_hour.user_id = ? AND DATE(log_hour.completed_date) = ?
          GROUP BY log_hour.user_id, DATE(log_hour.completed_date)
        `;

            const result = await getManager().query(sql, [orgUser.id, formattedDateForQuery]);

            if (result[0]) {
              TimeSheetReport.push({
                ...result[0],
                logDate: moment(result[0].logDate),
                generalLogHours: formatDuration(result[0].generalLogHours),
                taskLogHours: formatDuration(result[0].taskLogHours),
                standardWorkingHours: formatDuration(result[0].standardWorkingHours),
                totalLoggedHours: formatDuration(result[0].totalLoggedHours),
                overtime: formatDuration(result[0].overtime),
                undertime: formatDuration(result[0].undertime),
              });
            } else {
              TimeSheetReport.push({
                userId: orgUser.id,
                fullName: orgUser.fullName,
                mobileNumber: orgUser.mobileNumber,
                logDate: formatedDate,
                generalLogHours: formatDuration(0),
                taskLogHours: formatDuration(0),
                standardWorkingHours: formatDuration(standardWorkingHours),
                totalLoggedHours: formatDuration(0),
                overtime: formatDuration(0),
                undertime: formatDuration(standardWorkingHours),
              });
            }
          }

          function formatDuration(durationInMillis: number): string {
            const hours = Math.floor(durationInMillis / 3600000); // 1 hour = 3600000 milliseconds
            const minutes = Math.floor((durationInMillis % 3600000) / 60000); // 1 minute = 60000 milliseconds

            const formattedHours = hours.toString().padStart(2, '0');
            const formattedMinutes = minutes.toString().padStart(2, '0');

            return `${formattedHours}:${formattedMinutes}`;
          }

          if (TimeSheetReport?.length) {
            const orgAdmins = await getAdminIDsBasedOnOrganizationId(organization.id);
            const organizations = await Organization.findOne({ id: organization.id });

            const addressParts = [
              organizations.buildingNo || '',
              organizations.floorNumber || '',
              organizations.buildingName || '',
              organizations.street || '',
              organizations.location || '',
              organizations.city || '',
              organizations.district || '',
              organizations.state || '',
            ].filter((part) => part && part.trim() !== '');
            const pincode =
              organizations.pincode && organizations.pincode.trim() !== ''
                ? ` - ${organizations.pincode}`
                : '';
            const address = addressParts.join(', ') + pincode;

            for (let orgAdmin of orgAdmins) {
              const adminUser = await User.findOne({ where: { id: orgAdmin } });
              const formatedDate = moment(date).format('DD-MM-YYYY');
              const mailData = {
                id: adminUser?.id,
                key: 'TIMESHEET',
                email: adminUser?.email,
                data: {
                  TimeSheetReport: TimeSheetReport,
                  userName: adminUser.fullName,
                  date: formatedDate,
                  adress: address,
                  phoneNumber: organization?.mobileNumber,
                  mail: organization?.email,
                  legalName: organization?.tradeName || organization?.legalName,
                },
                filePath: 'timesheet-daily',
                subject: `Daily Timesheet Summary - ${formatedDate}`,
              };
              await sendnewMail(mailData);
            }
          }
        }
      } catch (error) {
        errorList.push(error.message);
        const getcornActivityID = await createQueryBuilder(CronActivity, 'cronActivity')
          .where('id = :id', { id: cornActivityID.id })
          .getOne();
        getcornActivityID.responseData = errorList.length ? errorList.join(',') : 'Success';
        getcornActivityID.endTime = moment().format('YYYY-MM-DD HH:mm:ss');
        await getcornActivityID.save();
        return console.log(error.message);
      }
      const getcornActivityID = await createQueryBuilder(CronActivity, 'cronActivity')
        .where('id = :id', { id: cornActivityID.id })
        .getOne();
      getcornActivityID.responseData = errorList.length ? errorList.join(',') : 'Success';
      getcornActivityID.endTime = moment().format('YYYY-MM-DD HH:mm:ss');
      await getcornActivityID.save();
    }
  }

  async importTimesheetOrg(userId: number, file: Express.Multer.File) {
    try {
      // Fetch the user and their organization
      const user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
      if (!user) {
        throw new Error('User not found');
      }

      // Read the Excel file
      const workbook = xlsx.read(file.buffer, { cellDates: true });
      const sheet = workbook.Sheets[workbook.SheetNames[0]];
      const dataFromExcel = xlsx.utils.sheet_to_json(sheet);
      let errorsArray = [];
      let addedArray = 0;
      if (dataFromExcel.length > 0) {
        // Filter rows that have a 'User' field
        // const filteredData = dataFromExcel.filter((item) => item['User']);

        if (dataFromExcel.length > 0) {
          // Function to parse Excel serial time or string time
          function parseTime(timeValue) {
            if (typeof timeValue === 'number') {
              // Handle Excel serial time
              const excelEpoch = new Date(1899, 11, 30); // Excel's epoch is 1899-12-30
              const jsDate = new Date(excelEpoch.getTime() + timeValue * 24 * 60 * 60 * 1000);
              return jsDate;
            } else if (typeof timeValue === 'string') {
              // Handle string time (e.g., "11:30")
              const [hours, minutes] = timeValue.split(':');
              const jsDate = new Date();
              jsDate.setHours(parseInt(hours, 10));
              jsDate.setMinutes(parseInt(minutes, 10));
              return jsDate;
            } else if (timeValue instanceof Date) {
              // Handle JavaScript Date object
              return timeValue;
            } else {
              throw new Error('Invalid time format');
            }
          }

          // Process each row in the filtered data
          for (const [index, item] of dataFromExcel.entries()) {

            // Parse and format the date

            let timesheetDate = null;

            if (
              (' ' + item['Date'])?.trim() !== 'undefined' &&
              (' ' + item['Date'])?.trim() !== undefined
            ) {
              const dateStr = item['Date'];
              if (
                typeof dateStr === 'string' &&
                dateStr !== null &&
                dateStr !== undefined &&
                dateStr !== 'undefined'
              ) {
                const trimmedDateString = dateStr?.trim();
                timesheetDate = moment(trimmedDateString, 'DD-MM-YYYY', true) // Specify format & enable strict parsing
                  .format('YYYY-MM-DD');
              } else if (
                typeof dateStr === 'object' &&
                dateStr !== null &&
                dateStr !== undefined &&
                dateStr !== 'undefined'
              ) {
                const dateObj = moment.utc(dateStr);
                const dateObjAdd = dateObj?.add(1, 'days');
                timesheetDate = moment(dateObjAdd).subtract(1, 'day').format('YYYY-MM-DD');
              }
            }

            // timesheetDate = item['Date'] ? moment(item['Date']).format('YYYY-MM-DD') : null;
            // Find the user in the organization
            const loghourUser = await User.findOne({
              where: { fullName: item['User'], organization: user.organization },
            });
            if (timesheetDate) {
              if (loghourUser) {
                const logHourType = item['Log hour type'];
                const clientId = item['Client ID'];
                const clientOrClientGroup = item['Client/Client group name'];
                const taskId = item['Task ID'];
                const taskName = item['Task name'];
                const description = item['Description'];
                const startTimee = item['Start time'];
                const endTimee = item['End time'];
                const hoursTime = item['Hours'];
                const minutesTime = item['Minutes'];

                if (logHourType) {
                  let taskBasedOnId = null;
                  let taskBasedOnName = null;
                  if (taskId || taskName) {
                    taskBasedOnId = await Task.findOne({
                      where: { taskNumber: taskId, organization: user.organization },
                    });

                    taskBasedOnName = await Task.findOne({
                      where: { name: taskName, organization: user.organization },
                    });
                  }
                  let clientBasedOnId = null;
                  let clientBasedOnName = null;
                  if (clientId || clientOrClientGroup) {
                    if (clientId) {
                      clientBasedOnId = await Client.findOne({
                        where: { clientId: clientId, organization: user.organization },
                      });
                    } else if (clientOrClientGroup) {
                      clientBasedOnName = await Client.findOne({
                        where: {
                          displayName: clientOrClientGroup,
                          organization: user.organization,
                        },
                      });
                    } else {
                      const errorDuplicate = `row ${index + 2}: No matching client found`;
                      errorsArray.push(errorDuplicate);
                    }
                  }

                  let formattedStartTime = null;
                  let formattedEndTime = null;
                  let startDateTime = null;
                  let endDateTime = null;
                  let timesheetDuration = null;

                  if (startTimee || endTimee || hoursTime || minutesTime) {
                    if (startTimee && endTimee) {
                      // Function to format time as HH:MM
                      function formatTime(date) {
                        const hours = String(date.getHours()).padStart(2, '0');
                        const minutes = String(date.getMinutes()).padStart(2, '0');
                        return `${hours}:${minutes}`;
                      }

                      formattedStartTime = formatTime(parseTime(startTimee));
                      formattedEndTime = formatTime(parseTime(endTimee));
                      // const trimmedDateString = dateStr?.trim();
                      // timesheetDate = moment(trimmedDateString, 'DD-MM-YYYY', true) // Specify format & enable strict parsing
                      //   .format('YYYY-MM-DD');
                      const startTime: any = moment
                        .duration(moment(formattedStartTime, 'HH:mm', true).format('HH:mm'))
                        .asMilliseconds();
                      const endTime: any = moment
                        .duration(moment(formattedEndTime, 'HH:mm', true).format('HH:mm'))
                        .asMilliseconds();
                      startDateTime = moment(formattedStartTime, 'HH:mm', true).format('HH:mm');
                      endDateTime = moment(formattedEndTime, 'HH:mm', true).format('HH:mm');
                      const startTimedur = startTime;
                      const endTimedur = endTime;
                      if (endTimedur <= startTimedur) {
                        errorsArray.push(`Row ${index + 2}: End time must be after start time`);
                        continue;
                      }
                      timesheetDuration = endTimedur - startTimedur;
                    } else if (hoursTime || minutesTime) {
                      timesheetDuration = moment
                        .duration(
                          `${hoursTime ? hoursTime : '00'}:${minutesTime ? minutesTime : '00'}`,
                        )
                        .asMilliseconds();

                    } else {
                      const errorDuplicate = `row ${
                        index + 2
                      }: Either start/end time or hours/minutes must be provided`;
                      errorsArray.push(errorDuplicate);
                      continue;
                    }
                  }
                  const { startTime, endTime } = dateFormation(timesheetDate, timesheetDate);
                  let totalLogHours = await getManager().query(
                    `SELECT SUM(duration) as count FROM log_hour WHERE user_id=${loghourUser.id} AND Date(completed_date) >='${startTime}' and Date(completed_date) <='${endTime}'`,
                  );
                  if (timesheetDuration) {
                    let totalLogHoursCount =
                      Number(totalLogHours[0].count) + Number(timesheetDuration);


                    let logHour = new LogHour();
                    if (logHourType === 'Task') {
                      if (taskBasedOnId || taskBasedOnName) {
                      } else {
                        const errorDuplicate = `row ${index + 2} Task is manditory for Task Type`;
                        errorsArray.push(errorDuplicate);
                        continue;
                      }
                    }
                    logHour.task = taskBasedOnId || taskBasedOnName || null;
                    logHour.client = clientBasedOnId || clientBasedOnName || null;
                    logHour.startTimeNew = startDateTime;
                    logHour.endTimeNew = endDateTime;
                    logHour.user = loghourUser;
                    logHour['userId'] = userId;
                    if (totalLogHoursCount > 86400000) {
                      const errorDuplicate = `row ${
                        index + 2
                      }: Total hours exceed 24 hours for the day`;
                      errorsArray.push(errorDuplicate);
                      continue;
                    }
                    logHour.duration = timesheetDuration;
                    logHour.completedDate = timesheetDate;
                    logHour.status = TimerStatus.STOPPED;
                    logHour.description = description;

                    logHour['userId'] = userId;

                    addedArray += 1;

                  } else {
                    const errorDuplicate = `row ${index + 2
                      }: Either start/end time or hours/minutes must be provided`;
                    errorsArray.push(errorDuplicate);
                    continue;
                  }

                } else {
                  const errorDuplicate = `row ${index + 2} Please Select Log Hour Type`;
                  errorsArray.push(errorDuplicate);
                }
              } else {
                const errorDuplicate = `row ${index + 2} User is not present in this organization`;
                errorsArray.push(errorDuplicate);
              }
            } else {
              const errorDuplicate = `row ${index + 2} Please select specific date`;
              errorsArray.push(errorDuplicate);
            }
          }
        } else {
        }
        return [...errorsArray, addedArray];
      } else {
      }
    } catch (error) {
      console.error('Error importing timesheet:', error);
      throw error;
    }
  }

  async importTimesheet(userId: number, file: Express.Multer.File) {
    try {
      // Fetch the user and their organization
      const user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
      if (!user) {
        throw new Error('User not found');
      }

      // Read the Excel file
      const workbook = xlsx.read(file.buffer, { cellDates: true });
      const sheet = workbook.Sheets[workbook.SheetNames[0]];
      const dataFromExcel = xlsx.utils.sheet_to_json(sheet);
      let errorsArray: string[] = [];
      let successCount = 0;

      if (dataFromExcel.length === 0) {
        errorsArray.push('The Excel file is empty');
        return { errors: errorsArray, successCount };
      }

      // Function to parse Excel serial time or string time
      function parseTime(timeValue) {
        if (typeof timeValue === 'number') {
          // Handle Excel serial time
          const excelEpoch = new Date(1899, 11, 30); // Excel's epoch is 1899-12-30
          const jsDate = new Date(excelEpoch.getTime() + timeValue * 24 * 60 * 60 * 1000);
          return jsDate;
        } else if (typeof timeValue === 'string') {
          // Handle string time (e.g., "11:30")
          const [hours, minutes] = timeValue.split(':');
          const jsDate = new Date();
          jsDate.setHours(parseInt(hours, 10));
          jsDate.setMinutes(parseInt(minutes, 10));
          return jsDate;
        } else if (timeValue instanceof Date) {
          // Handle JavaScript Date object
          return timeValue;
        }
        throw new Error('Invalid time format');
      }

      // Process each row in the data
      for (const [index, item] of dataFromExcel.entries()) {
        const rowNumber = index + 2; // +2 because Excel rows start at 1 and header is row 1

        try {
          // Validate required fields
          if (!item['User*']) {
            errorsArray.push(`Row ${rowNumber}: User name is required`);
            continue;
          }

          if (!item['Date(DD-MM-YYYY)*']) {
            errorsArray.push(`Row ${rowNumber}: Date is required`);
            continue;
          }

          // Parse and format the date
          let timesheetDate = null;
          const dateStr = item['Date(DD-MM-YYYY)*'];

          if (typeof dateStr === 'string' && dateStr.trim()) {
            timesheetDate = moment(dateStr.trim(), 'DD-MM-YYYY', true).format('YYYY-MM-DD');
            if (timesheetDate === 'Invalid date') {
              errorsArray.push(`Row ${rowNumber}: Invalid date format (expected DD-MM-YYYY)`);
              continue;
            }
          } else if (dateStr instanceof Date) {
            timesheetDate = moment(dateStr).format('YYYY-MM-DD');
          } else {
            errorsArray.push(`Row ${rowNumber}: Invalid date value`);
            continue;
          }

          // Find the user in the organization
          const loghourUser = await User.findOne({
            where: { fullName: item['User*'], organization: user.organization },
          });

          if (!loghourUser) {
            errorsArray.push(`Row ${rowNumber}: User "${item['User*']}" not found in organization`);
            continue;
          }

          // Validate log hour type
          const logHourType = item['Log hour type*'];
          if (!logHourType) {
            errorsArray.push(`Row ${rowNumber}: Log hour type is required`);
            continue;
          }

          // Process task information
          const taskId = item['Task ID'];
          const taskName = item['Task name'];
          let task = null;

          if (logHourType === 'Task' && !taskId && !taskName) {
            errorsArray.push(`Row ${rowNumber}: Task ID or Task name is required for Task type`);
            continue;
          }

          const title = item['Title'];
          if (logHourType === 'General' && !title) {
            errorsArray.push(`Row ${rowNumber}: Title is required for General type`);
            continue;
          }

          if (taskId || taskName) {
            task = await Task.findOne({
              where: [
                { taskNumber: taskId, organization: user.organization },
                { name: taskName, organization: user.organization },
              ],
            });

            if (!task && logHourType === 'Task') {
              errorsArray.push(
                `Row ${rowNumber}: Task not found with ID "${taskId}" or name "${taskName}"`,
              );
              continue;
            }
          }

          // Process client information
          const clientId = item['Client ID'];
          const clientName = item['Client/Client group name'];
          let client = null;
          let clientGroup = null;

          if (clientId || clientName) {
            client = await Client.findOne({
              where: [
                { clientId: clientId, organization: user.organization },
                { displayName: clientName, organization: user.organization },
              ],
            });

            clientGroup = await ClientGroup.findOne({
              where: [
                { clientId: clientId, organization: user.organization },
                { displayName: clientName, organization: user.organization },
              ],
            });

            // if (!client) {
            //   errorsArray.push(
            //     `Row ${rowNumber}: Client not found with ID "${clientId}" or name "${clientName}"`,
            //   );
            //   continue;
            // }
          }

          // Process time information
          const startTimee = item['Start time'];
          const endTimee = item['End time'];
          const hoursTime = item['Hours'];
          const minutesTime = item['Minutes'];

          let timesheetDuration = null;
          let startDateTime = null;
          let endDateTime = null;

          if (startTimee && endTimee) {
            try {
              const startTime = parseTime(startTimee);
              const endTime = parseTime(endTimee);

              startDateTime = moment(startTime).format('HH:mm');
              endDateTime = moment(endTime).format('HH:mm');

              const startMs = moment.duration(startDateTime).asMilliseconds();
              const endMs = moment.duration(endDateTime).asMilliseconds();

              if (endMs <= startMs) {
                errorsArray.push(`Row ${rowNumber}: End time must be after start time`);
                continue;
              }

              timesheetDuration = endMs - startMs;
            } catch (e) {
              errorsArray.push(`Row ${rowNumber}: Invalid time format`);
              continue;
            }
          } else if (hoursTime || minutesTime) {
            const hours = hoursTime ? parseInt(hoursTime) : 0;
            const minutes = minutesTime ? parseInt(minutesTime) : 0;
            if (hours < 0 || minutes < 0 || minutes >= 60) {
              errorsArray.push(`Row ${rowNumber}: Invalid hours/minutes value`);
              continue;
            }

            timesheetDuration = moment.duration({ hours, minutes }).asMilliseconds();
          } else {
            errorsArray.push(
              `Row ${rowNumber}: Either start/end time or hours/minutes must be provided`,
            );
            continue;
          }

          // Check total hours for the day
          const { startTime, endTime } = dateFormation(timesheetDate, timesheetDate);
          const totalLogHours = await getManager().query(
            `SELECT SUM(duration) as count FROM log_hour WHERE user_id=${loghourUser.id} AND Date(completed_date) >='${startTime}' and Date(completed_date) <='${endTime}'`,
          );
          const totalDuration =
            (totalLogHours[0]?.count ? Number(totalLogHours[0].count) : 0) +
            Number(timesheetDuration);
          if (totalDuration > 86400000) {
            // 24 hours in milliseconds
            errorsArray.push(`Row ${rowNumber}: Total hours exceed 24 hours for the day`);
            continue;
          }

          // Create log hour entry
          const logHour = new LogHour();
          if (logHourType === 'General') {
            logHour.title = title;
          }
          if (logHourType === 'Task') {
            logHour.task = task;
          }
          logHour.type = logHourType;
          logHour.client = client;
          logHour.clientGroup = clientGroup;
          logHour.startTimeNew = startDateTime;
          logHour.endTimeNew = endDateTime;
          logHour.user = loghourUser;
          logHour.duration = timesheetDuration;
          logHour.completedDate = timesheetDate;
          logHour.status = TimerStatus.STOPPED;
          logHour.description = item['Description'];
          logHour.billable = item['Type'] === 'Billable';
          await logHour.save();
          successCount++;
        } catch (error) {
          console.error(`Error processing row ${rowNumber}:`, error);
          errorsArray.push(`Row ${rowNumber}: Internal server error`);
        }
      }

      return [...errorsArray, successCount];
    } catch (error) {
      console.error('Error importing timesheet:', error);
      throw error;
    }
  }

  async createLogHourTitle(userId, body) {
    try {
      const user = await User.findOne({
        where: { id: userId },
        relations: ['organization'],
      });

      const checkTitle = await LogHourTitle.find({
        where: { name: body.name, organizationId: user?.organization?.id },
      });
      if (checkTitle.length) {
        throw new BadRequestException('Log Hour Title is Already Exists !');
      }

      const logHourTitle = new LogHourTitle();
      logHourTitle.name = body?.name;
      logHourTitle.createdBy = user;
      logHourTitle.organizationId = user?.organization?.id;
      await logHourTitle.save();
    } catch (err) {
      console.log('Error Occur createLogHourTitle', err?.message);
      throw new InternalServerErrorException(err);
    }
  }

  async getLogHourTitle(userId: number, query: any) {
    const user = await User.findOne({
      where: { id: userId },
      relations: ['organization'],
    });
    const logHourTitle = createQueryBuilder(LogHourTitle, 'logHourTitle').where(
      'logHourTitle.organizationId = :organizationId',
      { organizationId: user.organization.id },
    );

    if (query.search) {
      logHourTitle.andWhere('logHourTitle.name like :search', {
        search: `%${query.search}%`,
      });
    }

    let result = await logHourTitle.getMany();
    return result;
  }

  async deleteLogHourTitle(ids: number[]) {
    await getRepository(LogHourTitle)
      .createQueryBuilder()
      .delete()
      .where('id IN (:...ids)', { ids: ids })
      .execute();

    return { success: true };
  }

  async timesheetStartTime(userId, body) {
    try {
      const user = await User.findOne({ where: { id: userId } });
      if (!user) {
        return null;
      }
      const istTime = moment().tz('Asia/Kolkata').format('YYYY-MM-DD');
      const getStartTime = moment().tz('Asian/Kolkata').valueOf();
      const logHour = new LogHour();
      logHour.description = body?.description;
      logHour.type = body?.logHourType;
      logHour.user = user;
      logHour.status = TimerStatus.STARTED;
      logHour['userId'] = userId;
      logHour.completedDate = istTime;
      logHour.createdBy = userId;
      logHour.billable = body?.billable;
      logHour.startTime = getStartTime;

      if (body.logHourType === 'TASK') {
        let task = await Task.findOne({
          where: { id: body?.task?.id },
          relations: ['client', 'clientGroup'],
        });
        if (!task) {
          throw new BadRequestException('Task not found');
        }

        let client = body?.client?.id
          ? await Client.findOne({ where: { id: body?.client?.id } })
          : task?.client;

        let clientGroup = body?.clientGroup?.id
          ? await ClientGroup.findOne({ where: { id: body?.clientGroup?.id } })
          : task?.clientGroup;

        logHour.client = client;
        logHour.clientGroup = clientGroup;
        logHour.task = task;
        logHour.title = body?.title;
      } else if (body.logHourType === 'GENERAL') {
        logHour.title = body?.logHourTitle?.name;
        logHour.logHourTitleId = body?.logHourTitle?.id;
        let client = body?.client?.id
          ? await Client.findOne({ where: { id: body?.client?.id } })
          : null;

        let clientGroup = body?.clientGroup?.id
          ? await ClientGroup.findOne({ where: { id: body?.clientGroup?.id } })
          : null;

        logHour.client = client;
        logHour.clientGroup = clientGroup;
      }
      await logHour.save();
      return true;
    } catch (error) {
    }
  }

  // async timesheetStopTime(userId, body) {
  //   console.log({ userId, body });
  //   try {
  //     const logHour = await LogHour.findOne({ where: { id: body?.id } });
  //     if (logHour) {
  //       const endTime = moment().tz('Asian/Kolkata').valueOf();
  //       const startTime = Number(body.startTime);
  //       if (startTime && endTime) {
  //         const duration = endTime - startTime;
  //         console.log({ duration });
  //         logHour.duration = duration;
  //         logHour.endTime = endTime;
  //         logHour.status = TimerStatus.STOPPED;
  //         console.log(logHour);
  //         await logHour.save();
  //       }
  //     }
  //   } catch (error) {
  //     console.log('Error Occor in timesheetStopTimer:', error?.message);
  //   }
  // }

  async timesheetStopTime(userId, body) {
    try {
      const user = await User.findOne({ where: { id: userId } });
      if (!user) {
        return null;
      }

      const logHour = await LogHour.findOne({
        where: { id: body?.id },
        relations: ['user', 'client', 'clientGroup', 'task'],
      });

      if (!logHour) {
        throw new Error('LogHour not found');
      }

      const startTime = Number(logHour.startTime);
      const endTime = moment.tz('Asia/Kolkata').valueOf();

      if (endTime - startTime <= 60 * 1000) {
        throw new BadRequestException('Time difference must be more than 1 minute');
      }

      const startMoment = moment.tz(startTime, 'Asia/Kolkata').startOf('day');
      const endMoment = moment.tz(endTime, 'Asia/Kolkata').startOf('day');

      const totalDays = endMoment.diff(startMoment, 'days') + 1;
      for (let i = 0; i < totalDays; i++) {
        const currentDay = moment(startMoment).add(i, 'days');
        const dayStart = i === 0 ? startTime : currentDay.valueOf();
        const dayEnd = i === totalDays - 1 ? endTime : currentDay.clone().endOf('day').valueOf();

        const completedDate = currentDay.format('YYYY-MM-DD');
        const startTimeNew = moment.tz(dayStart, 'Asia/Kolkata').format('HH:mm:00');
        const endTimeNew = moment.tz(dayEnd, 'Asia/Kolkata').format('HH:mm:00');

        // Check for string format overlap
        const stringOverlap = await LogHour.createQueryBuilder('logHour')
          .leftJoin('logHour.user', 'user')
          .where('user.id = :userId', { userId: user.id })
          .andWhere('logHour.status = :status', { status: TimerStatus.STOPPED })
          .andWhere('logHour.completedDate = :completedDate', { completedDate })
          .andWhere('logHour.startTimeNew < :endTimeNew', { endTimeNew })
          .andWhere('logHour.endTimeNew > :startTimeNew', { startTimeNew })
          .getOne();

        // Check for numeric format overlap
        const numericOverlap = await LogHour.createQueryBuilder('logHour')
          .leftJoin('logHour.user', 'user')
          .where('user.id = :userId', { userId: user.id })
          .andWhere('logHour.status = :status', { status: TimerStatus.STOPPED })
          .andWhere('logHour.startTime < :dayEnd', { dayEnd })
          .andWhere('logHour.endTime > :dayStart', { dayStart })
          .getOne();

        if (stringOverlap || numericOverlap) {
          throw new BadRequestException(
            `Time conflict on ${completedDate} from ${startTimeNew} to ${endTimeNew}`,
          );
        }
      }

      for (let i = 0; i < totalDays; i++) {
        const currentDay = moment(startMoment).add(i, 'days');
        const dayStart = i === 0 ? startTime : currentDay.valueOf();
        const dayEnd = i === totalDays - 1 ? endTime : currentDay.clone().endOf('day').valueOf();

        const duration = dayEnd - dayStart;

        const entry = i === 0 ? logHour : new LogHour();
        if (i !== 0) {
          entry.createdBy = user;
          entry.user = logHour.user;
          entry.client = logHour.client;
          entry.clientGroup = logHour.clientGroup;
          entry.task = logHour.task;
          entry.description = logHour.description;
          entry.type = logHour.type;
          entry.billable = logHour.billable;
          entry.title = logHour.title;
          entry.status = TimerStatus.STOPPED;
          entry.logHourTitleId = logHour.logHourTitleId;
        }
        entry.startTimeNew = moment.tz(dayStart, 'Asia/Kolkata').format('HH:mm:00');
        entry.endTimeNew = moment.tz(dayEnd, 'Asia/Kolkata').format('HH:mm:00');
        entry.startTime = dayStart;
        entry.endTime = dayEnd;
        entry.duration = duration;
        entry.status = TimerStatus.STOPPED;
        entry.completedDate = currentDay.format('YYYY-MM-DD');
        await entry.save();
      }

      return { success: true };
    } catch (error) {
      throw new InternalServerErrorException(error);
      return { success: false, error: error?.message };
    }
  }

  async timesheetPauseTime(userId, body) {
    try {
      const user = await User.findOne({ where: { id: userId } });
      if (!user) {
        return null;
      }

      const logHour = await LogHour.findOne({
        where: { id: body?.id },
        relations: ['user', 'client', 'clientGroup', 'task'],
      });

      if (!logHour) {
        throw new Error('LogHour not found');
      }

      const startTime = Number(logHour.startTime);
      const endTime = moment.tz('Asia/Kolkata').valueOf();

      if (endTime - startTime <= 60 * 1000) {
        throw new BadRequestException('Time difference must be more than 1 minute');
      }

      const startMoment = moment.tz(startTime, 'Asia/Kolkata').startOf('day');
      const endMoment = moment.tz(endTime, 'Asia/Kolkata').startOf('day');
      const totalDays = endMoment.diff(startMoment, 'days') + 1;
      for (let i = 0; i < totalDays; i++) {
        const currentDay = moment(startMoment).add(i, 'days');
        const dayStart = i === 0 ? startTime : currentDay.valueOf();
        const dayEnd = i === totalDays - 1 ? endTime : currentDay.clone().endOf('day').valueOf();

        const completedDate = currentDay.format('YYYY-MM-DD');
        const startTimeNew = moment.tz(dayStart, 'Asia/Kolkata').format('HH:mm:00');
        const endTimeNew = moment.tz(dayEnd, 'Asia/Kolkata').format('HH:mm:00');

        // Check for string format overlap
        const stringOverlap = await LogHour.createQueryBuilder('logHour')
          .leftJoin('logHour.user', 'user')
          .where('user.id = :userId', { userId: user.id })
          .andWhere('logHour.status = :status', { status: TimerStatus.STOPPED })
          .andWhere('logHour.completedDate = :completedDate', { completedDate })
          .andWhere('logHour.startTimeNew < :endTimeNew', { endTimeNew })
          .andWhere('logHour.endTimeNew > :startTimeNew', { startTimeNew })
          .getOne();

        // Check for numeric format overlap
        const numericOverlap = await LogHour.createQueryBuilder('logHour')
          .leftJoin('logHour.user', 'user')
          .where('user.id = :userId', { userId: user.id })
          .andWhere('logHour.status = :status', { status: TimerStatus.STOPPED })
          .andWhere('logHour.startTime < :dayEnd', { dayEnd })
          .andWhere('logHour.endTime > :dayStart', { dayStart })
          .getOne();

        if (stringOverlap || numericOverlap) {
          throw new BadRequestException(
            `Time conflict on ${completedDate} from ${startTimeNew} to ${endTimeNew}`,
          );
        }
      }

      for (let i = 0; i < totalDays; i++) {
        const currentDay = moment(startMoment).add(i, 'days');
        const dayStart = i === 0 ? startTime : currentDay.valueOf();
        const dayEnd = i === totalDays - 1 ? endTime : currentDay.clone().endOf('day').valueOf();

        const duration = dayEnd - dayStart;

        const entry = i === 0 ? logHour : new LogHour();
        if (i !== 0) {
          entry.createdBy = user;
          entry.user = logHour.user;
          entry.client = logHour.client;
          entry.clientGroup = logHour.clientGroup;
          entry.task = logHour.task;
          entry.description = logHour.description;
          entry.type = logHour.type;
          entry.billable = logHour.billable;
          entry.title = logHour.title;
          entry.status = TimerStatus.STOPPED;
          entry.logHourTitleId = logHour.logHourTitleId;
        }

        entry.startTimeNew = moment.tz(dayStart, 'Asia/Kolkata').format('HH:mm:00');
        entry.endTimeNew = moment.tz(dayEnd, 'Asia/Kolkata').format('HH:mm:00');
        entry.startTime = dayStart;
        entry.endTime = dayEnd;
        entry.duration = duration;
        entry.status = TimerStatus.STOPPED;
        entry.completedDate = currentDay.format('YYYY-MM-DD');

        await entry.save();
        if (i + 1 === totalDays) {
          const newpauseItem = new LogHour();
          newpauseItem.createdBy = user;
          newpauseItem.user = logHour.user;
          newpauseItem.client = logHour.client;
          newpauseItem.clientGroup = logHour.clientGroup;
          newpauseItem.task = logHour.task;
          newpauseItem.description = logHour.description;
          newpauseItem.type = logHour.type;
          newpauseItem.billable = logHour.billable;
          newpauseItem.title = logHour.title;
          newpauseItem.status = TimerStatus.STOPPED;
          newpauseItem.logHourTitleId = logHour.logHourTitleId;
          newpauseItem.duration = 0;
          newpauseItem.status = TimerStatus.PAUSED;
          newpauseItem.startTimeNew = moment.tz(dayStart, 'Asia/Kolkata').format('HH:mm:00');
          newpauseItem.endTimeNew = moment.tz(dayEnd, 'Asia/Kolkata').format('HH:mm:00');
          newpauseItem.startTime = dayStart;
          newpauseItem.endTime = dayEnd;
          newpauseItem.duration = duration;
          newpauseItem.completedDate = currentDay.format('YYYY-MM-DD');
          await newpauseItem.save();
        }
      }

      return { success: true };
    } catch (error) {
      throw new InternalServerErrorException(error);
      return { success: false, error: error?.message };
    }
  }

  async deleteTimerLoghour(id: number, userId: number) {
    try {
      let logHour = await LogHour.findOne({ where: { id } });
      await logHour.remove();
    } catch (error) {
    }
  }

  async timesheetRestartTime(userId, body) {
    try {
      const user = await User.findOne({ where: { id: userId } });
      if (!user) {
        return null;
      }

      const logHour = await LogHour.findOne({
        where: { id: body?.id },
        relations: ['user', 'client', 'clientGroup', 'task'],
      });

      if (!logHour) {
        throw new Error('LogHour not found');
      }
      const istTime = moment().tz('Asia/Kolkata').format('YYYY-MM-DD');
      const getStartTime = moment().tz('Asian/Kolkata').valueOf();
      logHour.status = TimerStatus.STARTED;
      logHour.completedDate = istTime;
      logHour.startTime = getStartTime;
      logHour.endTime = null;
      logHour.startTimeNew = moment.tz('Asia/Kolkata').format('HH:mm:00');
      logHour.endTimeNew = null;
      logHour.duration = null;
      await logHour.save();
    } catch (error) {
    }
  }
  async getTimesheetStartTime(userId: number, query: any) {
    try {
      const user = await User.findOne({
        where: { id: userId },
        relations: ['organization'],
      });

      const timeSheet = await LogHour.findOne({
        where: { user: user, status: In([TimerStatus.STARTED, TimerStatus.PAUSED]) },
        relations: ['task', 'client'],
      });
      return timeSheet ? timeSheet : 'NO DATA';
    } catch (error) {
      console.log('Error Occur in getTimesheetStartTime', error?.message);
    }
  }

  // @Cron(CronExpression.EVERY_10_MINUTES)
  // async logHourBillableCron() {
  //   console.log('Billable cron');

  //   const logHourData = await LogHour.find({ where: { type: 'TASK' }, relations: ['task'] });
  //   for (let loghour of logHourData) {
  //     if (loghour?.task) {
  //       const taskBillableType = loghour.task.billable;
  //       loghour.billable = taskBillableType;
  //       await loghour.save();
  //     }
  //   }
  //   console.log('Cron completed');
  // }

  // @Cron(CronExpression.EVERY_10_MINUTES)
  // async logHourBillableCron() {
  //   console.log('Starting billable cron job');

  //   try {
  //     // Consider adding pagination if you have many records
  //     const logHourData = await LogHour.find({
  //       where: { type: 'TASK' },
  //       relations: ['task'],
  //     });

  //     let updatedCount = 0;

  //     for (const loghour of logHourData) {
  //       if (loghour?.task) {
  //         const taskBillableType = loghour.task.billable;
  //         // Only update if different to avoid unnecessary DB operations
  //         if (loghour.billable !== taskBillableType) {
  //           loghour.billable = taskBillableType;
  //           console.log(loghour.id, 'logHourId');
  //           await loghour.save();
  //           updatedCount++;
  //         }
  //       }
  //     }

  //     console.log(`Cron completed. Updated ${updatedCount} records.`);
  //   } catch (error) {
  //     console.error('Error in billable cron job:', error);
  //     // Consider adding error reporting here
  //   }
  // }
}
