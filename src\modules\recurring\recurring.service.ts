import { Injectable, Logger } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { <PERSON>ron, CronExpression } from '@nestjs/schedule';
import * as moment from 'moment';
import { Brackets, createQueryBuilder } from 'typeorm';
import { TaskRecurringStatus, TaskStatusEnum } from '../tasks/dto/types';
import Task from '../tasks/entity/task.entity';
import RecurringProfile from './entity/recurring-profile.entity';
import { Service } from '../services/entities/service.entity';
import { User, UserStatus } from '../users/entities/user.entity';
import Checklist from '../tasks/entity/checklist.entity';
import Milestone from '../tasks/entity/milestone.entity';
import StageOfWork from '../tasks/entity/stage-of-work.entity';
import { Organization } from '../organization/entities/organization.entity';
import ChecklistItem from '../tasks/entity/checklist-item.entity';
import Activity, { ActivityType } from '../activity/activity.entity';
import { Event_Actions } from 'src/event-listeners/actions';
import TaskStatus from '../tasks/entity/task-status.entity';
import axios from 'axios';
import ApprovalProcedures from '../atm-qtm-approval/entities/approval-procedures.entity';
import { TasksService } from '../tasks/services/tasks.service';
import BudgetedHours, { BudgetedHourStatus } from '../budgeted-hours/budgeted-hours.entity';
import CronActivity from '../cron-activity/cron-activity.entity';
import UdinTask, { UdinTaskStatus, userType } from '../udin-task/udin-task.entity';
import { count } from 'console';

@Injectable()
export class RecurringService {
  constructor(private eventEmitter: EventEmitter2, private tasksService: TasksService) { }
  private readonly logger = new Logger();

  async getRecurringProfiles(query: any) {
    const recurringProfile = await RecurringProfile.createQueryBuilder('recurringProfile')
      .leftJoinAndSelect('recurringProfile.tasks', 'task')
      .leftJoinAndSelect('recurringProfile.client', 'client')
      .leftJoinAndSelect('recurringProfile.clientGroup', 'clientGroup')
      .leftJoinAndSelect('task.members', 'member')
      .leftJoinAndSelect('task.approvalProcedures', 'approvalProcedures')
      .leftJoinAndSelect('member.imageStorage', 'imageStorage')
      .leftJoinAndSelect('recurringProfile.user', 'user');

    if (query?.clientId) {
      recurringProfile.where('client.id = :clientId', { clientId: query?.clientId });
    } else if (query?.clientGroupId) {
      recurringProfile.where('clientGroup.id = :clientGroupId', {
        clientGroupId: query?.clientGroupId,
      });
    }
    return await recurringProfile
      .orderBy({ 'task.recurringStatus': 'ASC', 'task.taskStartDate': 'ASC' })
      .getMany();
  }

  async getRecurringProfilesTask(query: any, userId) {
    const user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
    const recurringProfile = await RecurringProfile.createQueryBuilder('recurringProfile')
      .leftJoin('recurringProfile.tasks', 'task')
      .leftJoinAndSelect('recurringProfile.client', 'client')
      .leftJoinAndSelect('recurringProfile.clientGroup', 'clientGroup')
      .leftJoin('task.members', 'member')
      .leftJoin('task.approvalProcedures', 'approvalProcedures')
      .leftJoin('member.imageStorage', 'imageStorage')
      .leftJoin('recurringProfile.user', 'user')
      .leftJoin('user.organization', 'organization')
      .leftJoin('task.service', 'service')
      .where('organization.id = :organizationId', { organizationId: user?.organization.id });

    if (query.search) {
      recurringProfile.andWhere(
        new Brackets((qb) => {
          qb.where('recurringProfile.name LIKE :search', {
            search: `%${query.search}%`,
          });
          // qb.orWhere('category.name LIKE :search', {
          //   search: `%${query.search}%`,
          // });
        }),
      );
    }

    if (query?.serviceId) {
      recurringProfile.andWhere('service.id = :serviceId', { serviceId: query?.serviceId });
    }

    if (query?.clientId) {
      recurringProfile.where('client.id = :clientId', { clientId: query?.clientId });
    } else if (query?.clientGroupId) {
      recurringProfile.where('clientGroup.id = :clientGroupId', {
        clientGroupId: query?.clientGroupId,
      });
    }

    if (query.offset) {
      recurringProfile.skip(query.offset);
    }

    if (query.limit) {
      recurringProfile.take(query.limit);
    }

    const result = await recurringProfile.getManyAndCount();
    return {
      data: result[0],
      count: result[1],
    };
  }

  async bulkUpdateTask(userId, id, body) {
    const LoginUser = await User.findOne({
      where: { id: userId },
      relations: ['organization'],
    });

    const recurringProfile = await createQueryBuilder(Task, 'task')
      .leftJoinAndSelect('task.labels', 'labels')
      .leftJoinAndSelect('task.service', 'service')
      .leftJoinAndSelect('task.udinTask', 'udinTask')
      .leftJoinAndSelect('task.taskLeader', 'taskLeader')
      .leftJoinAndSelect('task.members', 'members')
      .leftJoinAndSelect('task.recurringProfile', 'recurringProfile')
      .where('task.recurringStatus = :status', { status: TaskRecurringStatus.PENDING })
      .andWhere('recurringProfile.id = :id', { id: Number(id) })
      .orderBy('task.taskStartDate', 'ASC')
      .getMany();

    for (const task of recurringProfile) {
      task.members = body?.members;
      task.labels = body?.labels;
      task.taskLeader = body?.taskLeader;
      task.billable = body?.billable;
      task.feeType = body?.feeType;
      task.feeAmount = body?.feeAmount;
      task.priority = body?.priority;
      task.budgetedhours = body?.budgetedHoursInSeconds || 0;
      task.approvalProcedures = body?.approvalProcedures?.id ?? null;

      const taskDates = body?.dates?.filter((d) => d?.taskId === task.id);
      if (taskDates?.length) {
        const d0 = taskDates[0];
        task.taskStartDate = d0?.startDate || null;
        task.dueDate = d0?.dueDate || null;
        task.expectedCompletionDate = d0?.expectedCompletionDate || null;
      }

      if (body?.budgetedHoursInSeconds) {
        const existingRows = await createQueryBuilder(BudgetedHours, 'bh')
          .leftJoinAndSelect('bh.task', 'task')
          .leftJoinAndSelect('bh.user', 'user')
          .where('task.id = :taskId', { taskId: task.id })
          .getMany();

        const existingUserIds = existingRows.map((r) => r.user.id);
        const newUserIds = Object.keys(body?.taskBudgetedHoursData || {})
          .map((s) => Number(s))
          .filter((n) => !Number.isNaN(n));

        const usersToDeactivate = existingUserIds.filter((uid) => !newUserIds.includes(uid));

        for (const userIdStr of Object.keys(body?.taskBudgetedHoursData || {})) {
          const uid = Number(userIdStr);
          if (Number.isNaN(uid)) continue;

          const entry = body.taskBudgetedHoursData[userIdStr];
          const ms = Number(entry?.userBudgetedHoursInSeconds) || 0;

          let row = existingRows.find((r) => r.user.id === uid);
          if (!row) {
            const userEntity = await User.findOne({ where: { id: uid } });
            if (!userEntity) continue;

            row = new BudgetedHours();
            row.task = task;
            row.user = userEntity;
            row.organization = LoginUser?.organization;
          }

          row.budgetedHours = ms;
          row.status = BudgetedHourStatus.ACTIVE;
          await row.save();
        }

        if (usersToDeactivate.length > 0) {
          await createQueryBuilder()
            .update(BudgetedHours)
            .set({ status: BudgetedHourStatus.INACTIVE })
            .where('task.id = :taskId', { taskId: task.id })
            .andWhere('user.id IN (:...userIds)', { userIds: usersToDeactivate })
            .execute();

        }
      } else {
        task.budgetedhours = 0;

        const budgetedHours = await createQueryBuilder(BudgetedHours, 'bh')
          .leftJoinAndSelect('bh.task', 'task')
          .leftJoinAndSelect('bh.user', 'user')
          .where('task.id = :taskId', { taskId: task.id })
          .getMany();

        for (const r of budgetedHours) {
          r.budgetedHours = 0;
          r.status = BudgetedHourStatus.INACTIVE;
          await r.save();
        }
      }

      task.isUdin = !!body?.udinUpdatedStatus;

      if (!body?.udinUpdatedStatus) {
        if (task.udinTask) {
          const udinTask = task.udinTask;
          udinTask.task = task;
          udinTask.udinTaskStatus = UdinTaskStatus.INACTIVE;
          await udinTask.save();
        }
      } else {
        if (task.udinTask) {
          const udinTask = task.udinTask;
          udinTask.task = task;
          udinTask.udinTaskStatus = UdinTaskStatus.ACTIVE;
          await udinTask.save();
        } else {
          const udintask = new UdinTask();
          udintask.task = task;
          udintask.userType = userType.ORGANIZATION;
          udintask.organization = LoginUser.organization;
          udintask.recurringProfile = id;
          udintask.udinTaskStatus = UdinTaskStatus.ACTIVE;
          await udintask.save();
        }
      }
      task['userId'] = LoginUser.id;
      await task.save();
    }

    const activity = new Activity();
    activity.action = Event_Actions.RECURRING_PROFILE_UPDATED;
    activity.actorId = LoginUser.id;
    activity.type = body.client ? ActivityType.CLIENT : ActivityType.CLIENT_GROUP;
    activity.typeId = body.client ? body.client : body.clientGroup;
    activity.remarks = `"${recurringProfile[0]?.service?.name || recurringProfile[0]?.recurringProfile?.name
      }" Recurring Profile Updated by ${LoginUser.fullName}`;
    await activity.save();
  }

  @Cron(CronExpression.EVERY_DAY_AT_MIDNIGHT)
  async handleRecurringTasks() {
    if (process.env.Cron_Running === 'true') {
      const cronData = new CronActivity();
      cronData.cronType = 'RECURRING TASKS';
      cronData.cronDate = moment().toDate().toString();
      cronData.startTime = moment().format('YYYY-MM-DD HH:mm:ss');
      const cornActivityID = await cronData.save();

      const today = moment().format('YYYY-MM-DD');
      const BATCH_SIZE = 100;
      const errorList: string[] = [];
      const organizationList: Record<string, number> = {};

      try {
        let offset = 0;
        while (true) {
          const tasks = await createQueryBuilder(Task, 'task')
            .leftJoinAndSelect('task.approvalProcedures', 'approvalProcedures')
            .leftJoinAndSelect('task.organization', 'organization')
            .leftJoinAndSelect('task.client', 'client')
            .leftJoinAndSelect('task.clientGroup', 'clientGroup')
            .leftJoinAndSelect('task.user', 'user')
            .leftJoinAndSelect('task.service', 'service')
            .where('task.recurring = :recurring', { recurring: true })
            .andWhere(
              new Brackets(qb => {
                qb.where('client.id IS NULL OR client.status <> :clientStatus', { clientStatus: UserStatus.DELETED })
                  .andWhere('clientGroup.id IS NULL OR clientGroup.status <> :clientGroupStatus', { clientGroupStatus: UserStatus.DELETED });
              })
            )
            .andWhere('task.recurringStatus = :status', { status: TaskRecurringStatus.PENDING })
            .andWhere('task.taskStartDate <= :today', { today })
            .andWhere('organization.id IS NOT NULL')
            .skip(offset)
            .take(BATCH_SIZE)
            .getMany();
          if (!tasks.length) break;

          for (const task of tasks) {
            try {
              if (task.organization && (task?.client || task?.clientGroup)) {
                const recurringCount = await createQueryBuilder(Task, 'task')
                  .leftJoin('task.organization', 'organization')
                  .where('organization.id = :id', { id: task.organization.id })
                  .andWhere('task.taskNumber LIKE :taskNumber', { taskNumber: '%RRT%' })
                  .getCount();

                const orgIdStr = String(task.organization.id);
                organizationList[orgIdStr] = (organizationList[orgIdStr] || 0) + 1;
                const rrtId = 'RRT' + String(recurringCount + organizationList[orgIdStr]).padStart(4, '0');

                task.taskNumber = rrtId;
                task.createdDate = moment().toDate();
                task.recurringStatus = TaskRecurringStatus.CREATED;

                const taskStatus = new TaskStatus();
                taskStatus.restore = TaskStatusEnum.TODO;
                taskStatus.status = TaskStatusEnum.TODO;
                taskStatus.task = task;
                taskStatus.user = task.user;

                const activity = new Activity();
                activity.action = Event_Actions.TASK_CREATED;
                activity.type = task.client ? ActivityType.CLIENT : ActivityType.CLIENT_GROUP;
                activity.typeId = task?.client ? task.client.id : task.clientGroup.id;
                activity.remarks = `"${task.name}" Task created Automatically on Date`;
                await Promise.all([
                  taskStatus.save(),
                  activity.save(),
                ]);
                if (task.service) {
                  const service = await createQueryBuilder(Service, 'service')
                    .leftJoinAndSelect('service.category', 'category')
                    .leftJoinAndSelect('service.subCategory', 'subCategory')
                    .leftJoinAndSelect('service.checklists', 'checklists')
                    .leftJoinAndSelect('checklists.checklistItems', 'checklistItems')
                    .leftJoinAndSelect('service.milestones', 'milestones')
                    .leftJoinAndSelect('service.stageOfWorks', 'stageOfWorks')
                    .where('service.id = :id', { id: task.service.id })
                    .getOne();

                  task.description = service.description;
                  task.checklists = await Promise.all(service.checklists.map(checklist => {
                    const cl = new Checklist();
                    cl.name = checklist.name;
                    cl.checklistItems = checklist.checklistItems.map(item => {
                      const ci = new ChecklistItem();
                      ci.name = item.name;
                      ci.description = item.description;
                      return ci;
                    });
                    return cl;
                  }));

                  task.milestones = await Promise.all(service.milestones.map(m => {
                    const milestone = new Milestone();
                    delete m?.id;
                    Object.assign(milestone, m);
                    return milestone;
                  }));

                  task.stageOfWorks = await Promise.all(service.stageOfWorks.map(s => {
                    const stage = new StageOfWork();
                    delete s?.id;
                    Object.assign(stage, s);
                    return stage;
                  }));
                }
                if (task.approvalProcedures) {
                  const approval = await ApprovalProcedures.findOne({
                    where: { id: task.approvalProcedures.id, organization: task.organization.id },
                    relations: ['approval', 'approval.approvalLevels', 'approval.approvalLevels.user'],
                    select: ['id'],
                  });
                  try {
                    const data = JSON.stringify({
                      processKey: 'genericApprovalProcess',
                      metaData: {
                        typeOfApproval: 'ATOM_TASK',
                        approvalProcessId: `Level${approval.approval.approvalLevels.length}ApprovalProcess`,
                      },
                    });

                    const response = await axios.post(`${process.env.CAMUNDA_URL}/vider/quantum/api/process`, data, {
                      headers: { 'Content-Type': 'application/json' },
                      maxBodyLength: Infinity,
                    });
                    const processInstanceId = response?.data?.processInstanceId;
                    if (processInstanceId) {
                      task.approvalStatus = [
                        {
                          status: `Approval Levels (${approval?.approval?.approvalLevels.length})`,
                          completed: false,
                        },
                      ];
                      task.processInstanceId = processInstanceId;
                      this.tasksService.approvalProcess(processInstanceId, approval);
                    };
                  } catch (err) {
                    console.log(err);
                  }
                }
                const taskRecurring = async () => {
                  let completedtask = await Task.findOne({
                    where: { id: task?.id },
                    relations: [
                      'user',
                      'checklists',
                      'service',
                      'organization',
                      'taskLeader',
                      'client',
                      'clientGroup',
                      'taskLogHours',
                      'recurringProfile',
                      'milestones',
                      'taskStatus',
                      'members',
                      'labels',
                      'category',
                      'subCategory',
                      'approvalProcedures',
                      'taskBudgetedHours',
                      'udinTask'
                    ],
                  });
                  let organization = await Organization.findOne({
                    where: { id: completedtask?.organization?.id },
                  });
                  if (completedtask.recurring === true) {
                    if (completedtask.frequency !== 'custom') {
                      const recurringProfile = completedtask.recurringProfile;
                      const user = completedtask.user;
                      let service: Service = completedtask.service;

                      let nameExt = '';
                      let StartDate = '';
                      let dueDate = '';
                      let expectedCompletionDate = '';
                      let taskName = '';
                      const splitYear = completedtask.financialYear.split('-');
                      const oldYear = String(Number(splitYear[0]) + 1);
                      const newYear = String(Number(splitYear[1]) + 1);
                      const newFinancialYear = oldYear + '-' + newYear;

                      const getStartDate = () => {
                        const startDateObj = moment.utc(completedtask.taskStartDate);
                        const startDateObjAdd = startDateObj.add(1, 'year');
                        StartDate = startDateObjAdd.format('YYYY-MM-DD');
                      };

                      const getDueDate = () => {
                        const dueDateObj = moment.utc(completedtask.dueDate);
                        const dueDateObjAdd = dueDateObj.add(1, 'year');
                        dueDate = dueDateObjAdd.format('YYYY-MM-DD');
                      };

                      const getExpectedCompletionDate = () => {
                        if (task.expectedCompletionDate) {
                          const expectedCompletionDateObj = moment.utc(
                            completedtask.expectedCompletionDate,
                          );
                          const expectedCompletionDateObjAdd = expectedCompletionDateObj.add(
                            1,
                            'year',
                          );
                          expectedCompletionDate = expectedCompletionDateObjAdd.format('YYYY-MM-DD');
                        } else {
                          expectedCompletionDate = task.expectedCompletionDate;
                        }
                      };
                      const oldTaskName = completedtask.name.split('-');
                      if (completedtask.frequency === 'monthly') {
                        taskName = oldTaskName.slice(0, -3).join('-');

                        const oldTaskDate = oldTaskName[oldTaskName.length - 1];
                        const oldYear = String(Number(oldTaskDate.slice(-4)) + 1);
                        let oldString = oldTaskName[oldTaskName.length - 1].slice(-4);
                        let splitString = oldTaskDate.split(oldString);
                        let newDate = splitString.join(oldYear);
                        oldTaskName[oldTaskName.length - 1] = newDate;

                        nameExt = '- ' + newFinancialYear + ' -' + newDate;

                        getStartDate();
                        getDueDate();
                        getExpectedCompletionDate();
                      } else if (completedtask.frequency === 'quarterly') {
                        const oldName = completedtask.name.split('-');
                        taskName = oldName.slice(0, -4).join('-');
                        let newString = String(Number(oldName[oldName.length - 1].slice(-4)) + 1);
                        let newDateName = oldName[oldName.length - 1];
                        let oldString = oldName[oldName.length - 1].slice(-4);
                        let splitString = newDateName.split(oldString);
                        let newDate = splitString.join(newString);
                        oldName[oldName.length - 1] = newDate;
                        let middleName = oldName[oldName.length - 2];
                        nameExt = '- ' + newFinancialYear + ' -' + middleName + '-' + newDate;

                        getStartDate();
                        getDueDate();
                        getExpectedCompletionDate();
                      } else if (completedtask.frequency === 'half_yearly') {
                        const oldName = completedtask.name.split('-');
                        taskName = oldName.slice(0, -4).join('-');
                        let newString = String(Number(oldName[oldName.length - 1].slice(-4)) + 1);
                        let newDateName = oldName[oldName.length - 1];
                        let oldString = oldName[oldName.length - 1].slice(-4);
                        let splitString = newDateName.split(oldString);
                        let newDate = splitString.join(newString);
                        oldName[oldName.length - 1] = newDate;
                        let middleName = oldName[oldName.length - 2];
                        nameExt = '- ' + newFinancialYear + ' -' + middleName + '-' + newDate;

                        getStartDate();
                        getDueDate();
                        getExpectedCompletionDate();
                      } else if (completedtask.frequency === 'yearly') {
                        const oldName = completedtask.name.split('-');
                        taskName = oldName.slice(0, -3).join('-');
                        const newDate = oldName[oldName.length - 1];
                        nameExt = '- ' + newFinancialYear + ' -' + newDate;

                        getStartDate();
                        getDueDate();
                        getExpectedCompletionDate();
                      } else {
                        taskName = completedtask.name;

                        getStartDate();
                        getDueDate();
                        getExpectedCompletionDate();
                      }

                      let pendingTask = new Task();
                      pendingTask.name = taskName + nameExt;
                      pendingTask.description = completedtask.description;
                      pendingTask.category = completedtask.category;
                      pendingTask.subCategory = completedtask.subCategory;
                      pendingTask.client = completedtask.client;
                      pendingTask.clientGroup = completedtask.clientGroup;
                      pendingTask.priority = completedtask.priority;
                      pendingTask.feeType = completedtask.feeType;
                      pendingTask.feeAmount = completedtask.feeAmount;
                      pendingTask.budgetedhours = completedtask.budgetedhours;
                      pendingTask.labels = completedtask.labels;
                      pendingTask.members = completedtask.members;
                      pendingTask.taskLeader = completedtask.taskLeader;
                      pendingTask.organization = organization;
                      pendingTask.financialYear = newFinancialYear;
                      pendingTask.recurring = true;
                      pendingTask.recurringProfile = recurringProfile;
                      pendingTask.frequency = completedtask.frequency;
                      pendingTask.taskStartDate = StartDate;
                      pendingTask.dueDate = dueDate;
                      pendingTask.expectedCompletionDate = expectedCompletionDate;
                      pendingTask.recurringStatus = TaskRecurringStatus.PENDING;
                      pendingTask.restore = TaskStatusEnum.PENDING;
                      pendingTask.budgetedhours = completedtask.budgetedhours;
                      pendingTask.taskLeader = completedtask.taskLeader;
                      pendingTask.isUdin = completedtask?.isUdin ? true : false;

                      if (service) {
                        pendingTask.name = service.name + nameExt;
                        pendingTask.description = service.description;
                        pendingTask.category = completedtask.category;
                        pendingTask.subCategory = completedtask.subCategory;
                        pendingTask.service = service;
                      }
                      if (completedtask.approvalProcedures) {
                        const approval = await ApprovalProcedures.findOne({
                          where: {
                            id: task?.approvalProcedures?.id,
                            organization: task?.organization?.id,
                          },
                          relations: [
                            'approval',
                            'approval.approvalLevels',
                            'approval.approvalLevels.user',
                          ],
                          select: ['id'],
                        });
                        pendingTask.approvalProcedures = approval;
                      }
                      pendingTask['userId'] = user?.id;
                      await Task.save(pendingTask);
                      for (let item of completedtask?.taskBudgetedHours) {
                        let userData = await User.findOne({
                          where: { id: item?.user?.id },
                          relations: ['organization'],
                        });
                        let budgetedHours = new BudgetedHours();
                        budgetedHours.status = BudgetedHourStatus.ACTIVE;
                        budgetedHours.budgetedHours = item['budgetedHours'];
                        budgetedHours.organization = user?.organization;
                        budgetedHours.task = pendingTask;
                        budgetedHours.user = userData;
                        await budgetedHours.save();
                      }

                      if (completedtask?.isUdin) {
                        if (!completedtask?.udinTask) {
                          let udintask = new UdinTask();
                          udintask.task = pendingTask;
                          udintask.userType = userType.ORGANIZATION;
                          udintask.organization = user?.organization;
                          udintask.recurringProfile = recurringProfile;
                          udintask.udinTaskStatus = UdinTaskStatus.ACTIVE;
                          await udintask.save();
                        }
                      }
                    }
                  }
                };
                await taskRecurring();
                await task.save();
              };

            } catch (taskError) {
              console.error('Task loop error:', taskError);
              errorList.push(taskError.message);
            }
          }
          offset += BATCH_SIZE;
        }
      } catch (e) {
        console.error('Batch loop error:', e);
        errorList.push(e.message);
      } finally {
        const cronResult = await CronActivity.findOne({ where: { id: cornActivityID.id } });
        cronResult.responseData = errorList.length ? errorList.join(',') : 'Success';
        cronResult.endTime = moment().format('YYYY-MM-DD HH:mm:ss');
        await cronResult.save();
      }
    }
  }

  @Cron(CronExpression.EVERY_DAY_AT_1AM)
  async handleNonRecurringTasks() {
    if (process.env.Cron_Running === 'true') {
      const cronData = new CronActivity();
      cronData.cronType = 'NON - RECURRING TASKS';
      cronData.cronDate = moment().toDate().toString();
      cronData.startTime = moment().format('YYYY-MM-DD HH:mm:ss');
      const cornActivityID = await cronData.save();

      let today = moment().format('YYYY-MM-DD');
      let task = await createQueryBuilder(Task, 'task')
        .leftJoinAndSelect('task.approvalProcedures', 'approvalProcedures')
        .leftJoinAndSelect('task.organization', 'organization')
        .leftJoinAndSelect('task.client', 'client')
        .leftJoinAndSelect('task.clientGroup', 'clientGroup')
        .leftJoinAndSelect('task.user', 'user')
        .leftJoinAndSelect('task.service', 'service')
        .where('task.recurring = :recurring', { recurring: false })
        .andWhere(
          new Brackets(qb => {
            qb.where('client.id IS NULL OR client.status <> :clientStatus', { clientStatus: UserStatus.DELETED })
              .andWhere('clientGroup.id IS NULL OR clientGroup.status <> :clientGroupStatus', { clientGroupStatus: UserStatus.DELETED });
          })
        )
        .andWhere('task.recurringStatus = :status', { status: TaskRecurringStatus.PENDING })
        .andWhere('task.taskStartDate <= :today', { today })
        .andWhere('task.parentTask IS NULL')
        .andWhere('organization.id IS NOT NULL');
      const tasks = await task.getMany();

      function generateNRTId(id: number) {
        if (id < 10000) {
          return 'NRT' + id.toString().padStart(4, '0');
        }
        return 'NRT' + id;
      }

      let organizationList = {};
      const errorList = [];

      try {
        if (tasks.length > 0) {
          for (const [index, task] of tasks.entries()) {
            if (task.organization && (task?.client || task?.clientGroup)) {
              let recurringCount = await createQueryBuilder(Task, 'task')
                .leftJoin('task.organization', 'organization')
                .where('organization.id = :id', { id: task?.organization?.id })
                .andWhere('task.taskNumber LIKE :taskNumber', { taskNumber: '%NRT%' })
                .getCount();
              if (Object.keys(organizationList).includes(String(task?.organization?.id))) {
                organizationList[String(task?.organization?.id)] =
                  organizationList[String(task?.organization?.id)] + 1;
              } else {
                organizationList[String(task?.organization?.id)] = 1;
              }
              task.taskNumber = generateNRTId(
                recurringCount + Number(organizationList[String(task?.organization?.id)]),
              );
              task.createdDate = moment().toDate();
              task.recurringStatus = TaskRecurringStatus.CREATED;

              let taskStatus = new TaskStatus();
              taskStatus.restore = TaskStatusEnum.TODO;
              taskStatus.status = TaskStatusEnum.TODO;
              taskStatus.task = task;
              taskStatus.user = task?.user;
              await taskStatus.save();

              let activity = new Activity();
              activity.action = Event_Actions.TASK_CREATED;
              activity.actorId = undefined;
              activity.type = task?.client ? ActivityType.CLIENT : ActivityType.CLIENT_GROUP;
              activity.typeId = task?.client ? task?.client?.id : task?.clientGroup?.id;
              activity.remarks = `"${task?.name}" Task created Automatically on Date`;
              await activity.save();
              if (task.service) {
                let service: Service;
                let result = await createQueryBuilder(Service, 'service')
                  .leftJoinAndSelect('service.category', 'category')
                  .leftJoinAndSelect('service.subCategory', 'subCategory')
                  .leftJoinAndSelect('service.checklists', 'checklists')
                  .leftJoinAndSelect('checklists.checklistItems', 'checklistItems')
                  .leftJoinAndSelect('service.subTasks', 'subTasks')
                  .leftJoinAndSelect('service.milestones', 'milestones')
                  .leftJoinAndSelect('service.stageOfWorks', 'stageOfWorks')
                  .where('service.id = :id', { id: task?.service?.id })
                  .getOne();
                service = result;

                task.description = service.description;
                task.checklists = service.checklists.map((checklist) => {
                  let result = new Checklist();
                  delete checklist?.id;
                  result.name = checklist.name;
                  result.checklistItems = checklist.checklistItems.map((item) => {
                    let newItem = new ChecklistItem();
                    delete item?.id;
                    newItem.name = item.name;
                    newItem.description = item.description;
                    return newItem;
                  });
                  return result;
                });

                task.milestones = service.milestones.map((milestone) => {
                  let result = new Milestone();
                  delete milestone?.id;
                  Object.assign(result, milestone);
                  return result;
                });

                task.stageOfWorks = service.stageOfWorks.map((stageOfWork) => {
                  let result = new StageOfWork();
                  delete stageOfWork?.id;
                  Object.assign(result, stageOfWork);
                  return result;
                });
              }
              if (task.approvalProcedures) {
                const approval = await ApprovalProcedures.findOne({
                  where: { id: task?.approvalProcedures?.id, organization: task?.organization?.id },
                  relations: [
                    'approval',
                    'approval.approvalLevels',
                    'approval.approvalLevels.user',
                  ],
                  select: ['id'],
                });
                // task.approvalProcedures = approval;
                try {
                  const data = JSON.stringify({
                    processKey: 'genericApprovalProcess',
                    metaData: {
                      typeOfApproval: 'ATOM_TASK',
                      approvalProcessId: `Level${approval?.approval?.approvalLevels.length}ApprovalProcess`,
                    },
                  });
                  let config: any = {
                    method: 'post',
                    maxBodyLength: Infinity,
                    url: `${process.env.CAMUNDA_URL}/vider/quantum/api/process`,
                    headers: {
                      'Content-Type': 'application/json',
                    },
                    data: data,
                  };
                  await axios.request(config).then(async (response) => {
                    const processInstanceId = response?.data?.processInstanceId;

                    if (processInstanceId)
                      task.approvalStatus = [
                        {
                          status: `Approval Levels (${approval?.approval?.approvalLevels.length})`,
                          completed: false,
                        },
                      ];
                    task.processInstanceId = processInstanceId;
                    this.tasksService.approvalProcess(processInstanceId, approval);
                  });
                } catch (err) {
                  console.log(err);
                }
              }
            }
          }
          await Task.save(tasks);
        }
      } catch (error) {
        errorList.push(error.message);
        const getcornActivityID = await createQueryBuilder(CronActivity, 'cronActivity')
          .where('id = :id', { id: cornActivityID?.id })
          .getOne();
        getcornActivityID.responseData = errorList.length ? errorList.join(',') : 'Success';
        getcornActivityID.endTime = moment().format('YYYY-MM-DD HH:mm:ss');
        await getcornActivityID.save();
        return console.log(error.message);
      }

      const getcornActivityID = await createQueryBuilder(CronActivity, 'cronActivity')
        .where('id = :id', { id: cornActivityID?.id })
        .getOne();
      getcornActivityID.responseData = errorList.length ? errorList.join(',') : 'Success';
      getcornActivityID.endTime = moment().format('YYYY-MM-DD HH:mm:ss');
      await getcornActivityID.save();
    }
  }

  @Cron('30 1 * * *')
  async handleNonRecurringSubTasks() {
    if (process.env.Cron_Running === 'true') {
      const cronData = new CronActivity();
      cronData.cronType = 'SUB TASKS';
      cronData.cronDate = moment().toDate().toString();
      cronData.startTime = moment().format('YYYY-MM-DD HH:mm:ss');
      const cornActivityID = await cronData.save();

      let today = moment().format('YYYY-MM-DD');

      let task = await createQueryBuilder(Task, 'task')
        .leftJoinAndSelect('task.approvalProcedures', 'approvalProcedures')
        .leftJoinAndSelect('task.organization', 'organization')
        .leftJoinAndSelect('task.client', 'client')
        .leftJoinAndSelect('task.clientGroup', 'clientGroup')
        .leftJoinAndSelect('task.user', 'user')
        .leftJoinAndSelect('task.service', 'service')
        .leftJoinAndSelect('task.parentTask', 'parentTask')
        .where('task.recurring = :recurring', { recurring: false })
        .andWhere(
          new Brackets(qb => {
            qb.where('client.id IS NULL OR client.status <> :clientStatus', { clientStatus: UserStatus.DELETED })
              .andWhere('clientGroup.id IS NULL OR clientGroup.status <> :clientGroupStatus', { clientGroupStatus: UserStatus.DELETED });
          })
        )
        .andWhere('task.recurringStatus = :status', { status: TaskRecurringStatus.PENDING })
        .andWhere('task.taskStartDate <= :today', { today })
        .andWhere('task.parentTask IS NOT NULL')
        .andWhere('organization.id IS NOT NULL');
      const tasks = await task.getMany();

      function generateSBTId(id: number) {
        if (id < 10000) {
          return 'SBT' + id.toString().padStart(4, '0');
        }
        return 'SBT' + id;
      }

      let organizationList = {};
      const errorList = [];
      try {
        if (tasks.length > 0) {
          for (const [index, task] of tasks.entries()) {
            if (task.organization && (task?.client || task?.clientGroup)) {
              let recurringCount = await createQueryBuilder(Task, 'task')
                .leftJoin('task.organization', 'organization')
                .where('organization.id = :id', { id: task?.organization?.id })
                .andWhere('task.taskNumber LIKE :taskNumber', { taskNumber: '%SBT%' })
                .getCount();
              if (Object.keys(organizationList).includes(String(task?.organization?.id))) {
                organizationList[String(task?.organization?.id)] =
                  organizationList[String(task?.organization?.id)] + 1;
              } else {
                organizationList[String(task?.organization?.id)] = 1;
              }
              task.taskNumber = generateSBTId(
                recurringCount + Number(organizationList[String(task?.organization?.id)]),
              );
              task.createdDate = moment().toDate();
              task.recurringStatus = TaskRecurringStatus.CREATED;

              let taskStatus = new TaskStatus();
              taskStatus.restore = TaskStatusEnum.TODO;
              taskStatus.status = TaskStatusEnum.TODO;
              taskStatus.task = task;
              taskStatus.user = task?.user;
              await taskStatus.save();

              let activity = new Activity();
              activity.action = Event_Actions.TASK_CREATED;
              activity.actorId = undefined;
              activity.type = task?.client ? ActivityType.CLIENT : ActivityType.CLIENT_GROUP;
              activity.typeId = task?.client ? task?.client?.id : task?.clientGroup?.id;
              activity.remarks = `"${task?.name}" Task created Automatically on Date`;
              await activity.save();
              if (task.service) {
                let service: Service;
                let result = await createQueryBuilder(Service, 'service')
                  .leftJoinAndSelect('service.category', 'category')
                  .leftJoinAndSelect('service.subCategory', 'subCategory')
                  .leftJoinAndSelect('service.checklists', 'checklists')
                  .leftJoinAndSelect('checklists.checklistItems', 'checklistItems')
                  .leftJoinAndSelect('service.subTasks', 'subTasks')
                  .leftJoinAndSelect('service.milestones', 'milestones')
                  .leftJoinAndSelect('service.stageOfWorks', 'stageOfWorks')
                  .where('service.id = :id', { id: task?.service?.id })
                  .getOne();
                service = result;

                task.description = service.description;
                task.checklists = service.checklists.map((checklist) => {
                  let result = new Checklist();
                  delete checklist?.id;
                  result.name = checklist.name;
                  result.checklistItems = checklist.checklistItems.map((item) => {
                    let newItem = new ChecklistItem();
                    delete item?.id;
                    newItem.name = item.name;
                    newItem.description = item.description;
                    return newItem;
                  });
                  return result;
                });

                task.milestones = service.milestones.map((milestone) => {
                  let result = new Milestone();
                  delete milestone?.id;
                  Object.assign(result, milestone);
                  return result;
                });

                task.stageOfWorks = service.stageOfWorks.map((stageOfWork) => {
                  let result = new StageOfWork();
                  delete stageOfWork?.id;
                  Object.assign(result, stageOfWork);
                  return result;
                });
              }
              if (task.approvalProcedures) {
                const approval = await ApprovalProcedures.findOne({
                  where: { id: task?.approvalProcedures?.id, organization: task?.organization?.id },
                  relations: [
                    'approval',
                    'approval.approvalLevels',
                    'approval.approvalLevels.user',
                  ],
                  select: ['id'],
                });
                // task.approvalProcedures = approval;
                try {
                  const data = JSON.stringify({
                    processKey: 'genericApprovalProcess',
                    metaData: {
                      typeOfApproval: 'ATOM_TASK',
                      approvalProcessId: `Level${approval?.approval?.approvalLevels.length}ApprovalProcess`,
                    },
                  });
                  let config: any = {
                    method: 'post',
                    maxBodyLength: Infinity,
                    url: `${process.env.CAMUNDA_URL}/vider/quantum/api/process`,
                    headers: {
                      'Content-Type': 'application/json',
                    },
                    data: data,
                  };
                  await axios.request(config).then(async (response) => {
                    const processInstanceId = response?.data?.processInstanceId;

                    if (processInstanceId)
                      task.approvalStatus = [
                        {
                          status: `Approval Levels (${approval?.approval?.approvalLevels.length})`,
                          completed: false,
                        },
                      ];
                    task.processInstanceId = processInstanceId;
                    this.tasksService.approvalProcess(processInstanceId, approval);
                  });
                } catch (err) {
                  console.log(err);
                }
              }
            }
          }
          await Task.save(tasks);
        }
      } catch (error) {
        errorList.push(error.message);
        const getcornActivityID = await createQueryBuilder(CronActivity, 'cronActivity')
          .where('id = :id', { id: cornActivityID?.id })
          .getOne();
        getcornActivityID.responseData = errorList.length ? errorList.join(',') : 'Success';
        getcornActivityID.endTime = moment().format('YYYY-MM-DD HH:mm:ss');
        await getcornActivityID.save();
        return console.log(error.message);
      }
      const getcornActivityID = await createQueryBuilder(CronActivity, 'cronActivity')
        .where('id = :id', { id: cornActivityID?.id })
        .getOne();
      getcornActivityID.responseData = errorList.length ? errorList.join(',') : 'Success';
      getcornActivityID.endTime = moment().format('YYYY-MM-DD HH:mm:ss');
      await getcornActivityID.save();
    }
  }
}
