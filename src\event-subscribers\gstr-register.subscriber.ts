import GstrRegister from "src/modules/gstr-register/entity/gstr-register.entity";
import { Connection, EntitySubscriberInterface, EventSubscriber, InsertEvent } from "typeorm";

@EventSubscriber()
export class GstrRegisterSubscriber implements EntitySubscriberInterface<GstrRegister>{
    constructor(private readonly connection: Connection) {
        connection.subscribers.push(this);

    }
    listenTo() {
        return GstrRegister
    }

    async afterInsert(event: InsertEvent<GstrRegister>) {
        // console.log("afterInsert");
        // console.log(event.entity);

    }


}