import { Base<PERSON>ntity, Column, CreateDateColumn, Enti<PERSON>, PrimaryGeneratedColumn, UpdateDateColumn } from "typeorm";


enum CloudTypes {
    BHARATHCLOUD = 'BHARATHCLOUD',
    AMAZON = 'AMAZON',
};


@Entity()
class CloudCredentials extends BaseEntity {
    @PrimaryGeneratedColumn()
    id: number;

    @Column()
    accessKey: string;

    @Column()
    secretKey: string;

    @Column()
    bucketName: string;

    @Column()
    endPoint: string;

    @Column({ type: 'enum', enum: CloudTypes, default: CloudTypes.BHARATHCLOUD })
    type: CloudTypes;

    @Column()
    organizationId: number;

    @CreateDateColumn()
    createdAt: string;

    @UpdateDateColumn()
    updatedAt: string;

}

export default CloudCredentials;