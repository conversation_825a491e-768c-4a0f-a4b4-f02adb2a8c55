import Client from 'src/modules/clients/entity/client.entity';
import { User } from 'src/modules/users/entities/user.entity';
import { BaseEntity, Column, Entity, JoinColumn, ManyToOne, OneToOne, PrimaryGeneratedColumn } from 'typeorm';

@Entity()
class ContactPerson extends BaseEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  name: string;

  @Column()
  role: string;

  @Column()
  mobile: string;

  @Column()
  email: string;

  @Column({ nullable: true })
  countryCode: string;

  @ManyToOne(() => Client, (client) => client.contactPersons, { onDelete: 'SET NULL' })
  client: Client;

  @OneToOne(() => User, (user) => user.clientUser, { cascade: true, onDelete: 'CASCADE' })
  @JoinColumn()
  user: User;
}

export default ContactPerson;
