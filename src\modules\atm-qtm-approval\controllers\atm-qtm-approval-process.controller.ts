import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  ParseIntPipe,
  Post,
  Put,
  Req,
  UseGuards,
} from '@nestjs/common';
import { AtmQtmApprovalService } from '../services/atm-qtm-approval-process.service';
import { JwtAuthGuard } from 'src/modules/users/jwt/jwt-auth.guard';
import CreateApprovalProcessDto from '../dto/create-approval-aprocess.dto';
import CreareProcedureDto from '../dto/create-procedure.dto';

@Controller('atm-qtm-approval')
export class AtmQtmApprovalController {
  constructor(private service: AtmQtmApprovalService) { }

  @UseGuards(JwtAuthGuard)
  @Get('/procedure')
  getProcedures(@Req() req: any) {
    const { userId } = req.user;
    return this.service.getProcedures(userId);
  }

  @UseGuards(JwtAuthGuard)
  @Get('/procedure/:id')
  findProcedure(@Param('id', ParseIntPipe) id: number, @Req() req: any) {
    const { userId } = req.user;
    return this.service.findProcedure(userId, id);
  }

  @UseGuards(JwtAuthGuard)
  @Put('/procedure/:id')
  updateProcedure(@Param('id', ParseIntPipe) id: number, @Body() body: CreareProcedureDto, @Req() req: any) {
    const { userId } = req.user;
    return this.service.updateProcedure(id, body, userId);
  }

  @UseGuards(JwtAuthGuard)
  @Post('/procedure')
  createProcedure(@Body() body: CreareProcedureDto, @Req() req: any) {
    const { userId } = req.user;
    return this.service.createProcedure(body, userId);
  }

  @Delete('procedure/:id')
  deleteProcedure(@Param('id', ParseIntPipe) id: number) {
    return this.service.deleteProcedure(id);
  };
}
