import {
  BaseEntity,
  Column,
  CreateDateColumn,
  Entity,
  ManyToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import TanClientCredentials from './tan-client-credentials.entity';

export enum PortalStatus {
  YES = 'YES',
  NO = 'NO',
}


@Entity()
class TraceOutstandingDemand extends BaseEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  organizationId: number;

  @Column()
  clientId: number;

  @Column()
  tanClientCredentialsId: number;

  @Column()
  name: string;

  @Column()
  financialYear: string;

  @Column('decimal', { precision: 15, scale: 2, nullable: true })
  manualAmount: number;

  @Column('decimal', { precision: 15, scale: 2, nullable: true })
  processedAmount: number;

  @Column()
  quarter: string;

  @Column()
  formType: string;

  @Column('decimal', { precision: 15, scale: 2, nullable: true })
  netPayableAmount: number;

  @Column('json', { nullable: true })
  tokenSummary: object;

  @Column('json', { nullable: true })
  demandSummary: object;

  @Column('json', { nullable: true })
  panErrorSummary: object;

   @Column('json', { nullable: true })
  correctionSummary: object;

  @CreateDateColumn()
  createdAt: string;

  @UpdateDateColumn()
  updatedAt: string;

  @ManyToOne(() => TanClientCredentials, (tan) => tan.traceOutstandingDemands, {
    onDelete: 'SET NULL',
    nullable: true,
  })
  tanClientCredentials: TanClientCredentials;

  @Column({type:"enum",enum:PortalStatus,nullable:true})
  isInPortal: PortalStatus | null;



}

export default TraceOutstandingDemand;
