import { Body, Controller, Get, Post } from '@nestjs/common';
import { createQueryBuilder } from 'typeorm';
import State from './state.entity';
import District from './district.entity.';

@Controller('states')
export class StatesController {
  @Post()
  async create(@Body() body) {
    await createQueryBuilder(State, 'state')
      .insert()
      .values(body.states)
      .execute();
    return {
      success: true,
    };
  }

  @Get()
  async get() {
    let states = await State.find();
    return states;
  }

  @Get('/districts')
  async getDistricts() {
    let disticts = await District.find();
    return disticts;
  }
}
