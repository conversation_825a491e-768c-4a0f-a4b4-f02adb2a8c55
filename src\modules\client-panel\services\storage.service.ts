import {
  BadRequestException,
  ConflictException,
  Inject,
  Injectable,
  InternalServerErrorException,
  forwardRef,
} from '@nestjs/common';
import * as moment from 'moment';
import Client from 'src/modules/clients/entity/client.entity';
import { User } from 'src/modules/users/entities/user.entity';
import { In, createQuery<PERSON>uilder, getConnection, getManager } from 'typeorm';
import { v4 as uuidv4 } from 'uuid';
import Task from 'src/modules/tasks/entity/task.entity';
import CreateFolderDto from 'src/modules/storage/dto/create-folder.dto';
import CreateLinkDto, { LinkUserType } from 'src/modules/storage/dto/create-link.dto';
import { FindStorageDto } from 'src/modules/storage/dto/FindStorage.dto';
import MoveFileDto from 'src/modules/storage/dto/move-file.dto';
import { RenameFileDto } from 'src/modules/storage/dto/rename-file.dto';
import { TotalStorageDto } from 'src/modules/storage/dto/total-storage.dto';
import { IUploadBody } from 'src/modules/storage/storage.controller';
import Storage, { StorageType } from 'src/modules/storage/storage.entity';
import { AwsService } from './upload.service';
import { Organization, StorageSystem } from 'src/modules/organization/entities/organization.entity';
import CollectData from 'src/modules/collect-data/collect-data.entity';
import { OneDriveStorageService } from './onedrive-storage.service';
import { AttachmentsService } from './attachments.service';

export interface IExisting {
  name: string;
  parent: string;
  type: 'client' | 'organization' | 'chat' | 'clientGroup' | 'viderAi';
  orgId: number;
  clientId: number;
  roomId: number;
}

export interface IUpload {
  file: Express.Multer.File;
  body: IUploadBody;
  userId: number;
}

@Injectable()
export class StorageService {
  constructor(private awsService: AwsService) { }


  async findStorage(query: FindStorageDto, userId: number) {
    const { folderId, clientId, type } = query;
    const user = await User.findOne(userId, { relations: ['organization'] });

    let storage = getConnection()
      .createQueryBuilder(Storage, 'storage')
      .leftJoin('storage.parent', 'parent')
      .leftJoinAndSelect('storage.user', 'user');

    if (type === 'client') {
      storage
        .leftJoin('storage.client', 'client')
        .where('client.id = :clientId', { clientId })
        .andWhere('storage.show !=:show', { show: false });
    }

    if (type === 'organization') {
      storage
        .leftJoin('storage.organization', 'organization')
        .where('organization.id = :orgId', { orgId: user.organization.id })
        .andWhere('storage.show !=:show', { show: false });
    }

    if (query.search) {
      storage.andWhere('storage.name LIKE :search', { search: `%${query.search}%` });
    }

    if (!query.search) {
      if (folderId) {
        storage.andWhere('parent.uid = :folderId', { folderId });
      }

      if (!folderId) {
        storage.andWhere('storage.parent is null');
      }
    }

    let data = await storage.getMany();
    let breadCrumbs = await getManager().query(`
     WITH RECURSIVE cte_storage(id, parent_id, name, uid) as (
       SELECT id,
       parent_id,
       name, uid from storage WHERE uid = '${folderId}'
        UNION ALL
       SELECT s.id, 
       s.parent_id,
       s.name,
       s.uid from cte_storage as cs JOIN storage AS s on cs.parent_id = s.id
      )
      SELECT name, uid from cte_storage order by id;
    `);

    return {
      result: data,
      breadCrumbs,
    };
  }

  async uploadFile(args: IUpload) {
    const { file, body, userId } = args;
    const { buffer, mimetype } = file;



    try {
      let user = await User.findOne({
        where: { id: userId },
        relations: ['organization'],
      });

      let storage = new Storage();
      storage.fileType = file.mimetype;
      storage.fileSize = file.size;
      storage.name = file.originalname;
      storage.type = StorageType.FILE;
      storage.uid = uuidv4();
      storage.storageSystem = StorageSystem.AMAZON;
      storage.user = user;

      let existingFile = await this.existing({
        name: file.originalname,
        parent: body.folderId,
        type: body.type,
        clientId: body.clientId,
        orgId: user.organization.id,
        roomId: body.roomId,
      });
      if (existingFile) {
        throw new ConflictException('File with this name already exists');
      }

      const { storageLimit, freeSpace } = await this.getOrgStorage(userId);
      if (!(freeSpace - +file.size > 0)) {
        throw new ConflictException(
          'We regret to inform you that the storage limit for your organization has been exceeded. As a result, no further data can be stored until the storage usage is reduced or additional storage capacity is allocated.',
        );
      }

      if (body.type === 'client') {
        let client = await Client.findOne({ where: { id: body.clientId } });
        let key = `storage/${client.clientId}/${file.originalname}`;
        let upload: any = await this.awsService.upload(buffer, key, mimetype);
        storage.file = upload.Key;
        storage.client = client;
      }

      if (body.type === 'organization') {
        let key = `storage/orgniazation-${user.organization.id}/${file.originalname}`;
        let upload: any = await this.awsService.upload(buffer, key, mimetype);
        storage.file = upload.Key;
        storage.organization = user.organization;
      }

      if (body.folderId) {
        let folder = await Storage.findOne({ where: { uid: body.folderId } });
        storage.parent = folder;
      }

      await storage.save();
      return storage;
    } catch (err) {
      throw new BadRequestException(err);
    }
  }

  async attachementsUpload(args: IUpload) {
    const { file, body, userId } = args;
    let user = await User.findOne({
      where: { id: userId },
      relations: ['organization'],
    });

    const { buffer, mimetype } = file;
    try {
      let user = await User.findOne({
        where: { id: userId },
        relations: ['organization'],
      });

      let existingFile = await this.existing({
        name: file.originalname,
        parent: body.folderId,
        type: body.type,
        clientId: body.clientId,
        orgId: user.organization.id,
        roomId: body.roomId,
      });

      if (body.type !== 'chat' && existingFile) {
        throw new ConflictException('File with this name already exists');
      }

      const { storageLimit, freeSpace } = await this.getOrgStorage(userId);
      if (!(freeSpace - +file.size > 0)) {
        throw new ConflictException(
          'We regret to inform you that the storage limit for your organization has been exceeded. As a result, no further data can be stored until the storage usage is reduced or additional storage capacity is allocated.',
        );
      }

      let key: string;
      let upload: any;

      if (body.type === 'client') {
        let client = await Client.findOne({ where: { id: body.clientId } });
        key = `storage/${client?.clientId ?? body?.clientId}/${file.originalname}`;
        upload = await this.awsService.upload(buffer, key, mimetype);
      }

      if (body.type === 'organization') {
        key = `storage/orgniazation-${user.organization.id}/${file.originalname}`;
        upload = await this.awsService.upload(buffer, key, mimetype);
      }

      if (body.type === 'chat') {
        key = `storage/chat-${body.roomId}-${moment().valueOf()}/${file.originalname}`;
        upload = await this.awsService.upload(buffer, key, mimetype);
      }

      return {
        key,
        upload: upload.key,
        fileSize: file.size,
        fileType: file.mimetype,
        clientId: body.clientId,
        name: file.originalname,
        storageSystem: StorageSystem.AMAZON
      };
    } catch (err) {
      console.error(err);
      throw new BadRequestException(err);
    }
  }

  async addAttachements(userId: number, body: any) {
    let user = await User.findOne({
      where: { id: userId },
      relations: ['organization'],
    });
    let client = await Client.findOne({ where: { id: body.clientId } });
    let storage = new Storage();
    storage.fileType = body.fileType;
    storage.fileSize = body.fileSize;
    storage.name = body.name;
    storage.type = StorageType.FILE;
    storage.uid = uuidv4();
    storage.user = user;
    storage.storageSystem = body.storageSystem;
    storage.webUrl = body.webUrl;
    storage.downloadUrl = body.downloadUrl;
    storage.fileId = body.fileId;
    storage.authId = user.organization.id;
    storage.show = body?.show;
    if (body.type === 'client') {
      storage.file = body.upload;
      storage.client = client;
    }
    if (body.type === 'organization') {
      storage.file = body.upload;
      storage.organization = user.organization;
    }
    return storage.save();
  }

  async getStorageTree(clientId: number) {
    let storage = getConnection()
      .createQueryBuilder(Storage, 'storage')
      .select([
        'storage.id as id',
        'storage.name as name',
        'storage.file as file',
        'storage.type as type',
        'parent.id as parent',
      ])
      .leftJoin('storage.client', 'client')
      .leftJoin('storage.parent', 'parent')
      .where('client.id = :clientId', { clientId })
      .andWhere('storage.type IN (:...type)', { type: [StorageType.FOLDER, StorageType.FILE] });

    let data = await storage.getRawMany();

    return data;
  }

  async getTotalStorage(query: TotalStorageDto) {
    let storage = await createQueryBuilder(Storage, 'storage')
      .select('SUM(storage.fileSize) as totalStorage')
      .leftJoin('storage.client', 'client')
      .where('client.id = :clientId', { clientId: query.clientId })
      .getRawOne();

    return +storage?.totalStorage ?? 0;
  }

  async getOrgStorage(userId: number) {
    const user = await User.findOne(userId, { relations: ['organization'] });
    let orgStorage = await createQueryBuilder(Storage, 'storage')
      .select('SUM(storage.fileSize) as totalStorage')
      .leftJoin('storage.organization', 'organization')
      .where('organization.id = :orgId', { orgId: user.organization.id })
      .getRawOne();

    let clientStorage = await createQueryBuilder(Storage, 'storage')
      .select('SUM(storage.fileSize) as totalStorage')
      .leftJoin('storage.client', 'client')
      .leftJoin('client.organization', 'organization')
      .where('organization.id = :orgId', { orgId: user.organization.id })
      .getRawOne();

    let unexpriedLinks = await createQueryBuilder(CollectData, 'collectData')
      .leftJoin('collectData.user', 'user')
      .leftJoin('user.organization', 'organization')
      .where('organization.id = :orgId', { orgId: user.organization.id })
      .andWhere('collectData.active=:active', { active: true })
      .getCount();

    let organization = await Organization.findOne({ where: { id: user.organization.id } });
    const orgLimit = organization.config?.['stroagelimit'] || 53687091200;

    return {
      storageLimit: orgLimit,
      totalStorageUsed:
        +clientStorage?.totalStorage + +orgStorage?.totalStorage + unexpriedLinks * 209715200 || 0,
      freeSpace:
        orgLimit -
        (+clientStorage?.totalStorage + +orgStorage?.totalStorage + unexpriedLinks * 209715200) ??
        0,
      unExpriedLinks: unexpriedLinks * 209715200,
    };
  }

  async existing(props: IExisting) {
    const { name, parent, type, orgId, clientId, roomId } = props;

    let existing = createQueryBuilder(Storage, 'storage');

    let where = `storage.name = :name`;

    if (type === 'client') {
      existing.leftJoin('storage.client', 'client');
      where += ` and client.id = :clientId`;
    }

    if (type === 'organization') {
      existing.leftJoin('storage.organization', 'organization');
      where += ` and organization.id = :orgId`;
    }
    if (type === 'chat') {
      existing.leftJoin('storage.room', 'room');
      where += ` and room.id = :roomId`;
    }

    if (parent) {
      existing.leftJoin('storage.parent', 'parent');
      where += ` and parent.uid = :parent`;
    }

    if (!parent) {
      where += ` and storage.parent is null`;
    }

    existing.where(`(${where})`, { name, parent, clientId, orgId, roomId });

    let result = await existing.getOne();
    return Boolean(result);
  }

  async getAutOrgStorage(orgId: number) {
    let orgStorage = await createQueryBuilder(Storage, 'storage')
      .select('SUM(storage.fileSize) as totalStorage')
      .leftJoin('storage.organization', 'organization')
      .where('organization.id = :orgId', { orgId })
      .getRawOne();

    let clientStorage = await createQueryBuilder(Storage, 'storage')
      .select('SUM(storage.fileSize) as totalStorage')
      .leftJoin('storage.client', 'client')
      .leftJoin('client.organization', 'organization')
      .where('organization.id = :orgId', { orgId })
      .getRawOne();

    let unexpiredLinks = await createQueryBuilder(CollectData, 'collectData')
      .leftJoin('collectData.user', 'user')
      .leftJoin('user.organization', 'organization')
      .where('organization.id = :orgId', { orgId })
      .andWhere('collectData.active = :active', { active: true })
      .getCount();

    let organization = await Organization.findOne({ where: { id: orgId } });
    const orgLimit = organization.config?.['stroagelimit'] || 53687091200;

    return {
      storageLimit: orgLimit,
      totalStorageUsed:
        +clientStorage?.totalStorage + +orgStorage?.totalStorage + unexpiredLinks * 209715200 || 0,
      freeSpace:
        orgLimit -
        (+clientStorage?.totalStorage + +orgStorage?.totalStorage + unexpiredLinks * 209715200) ??
        0,
      unexpiredLinks: unexpiredLinks * 209715200,
    };
  }

  async deleteAwsFile(key: string) {
    return this.awsService.delete(key)
  }
}
