import { Injectable } from "@nestjs/common";
import { User } from "src/modules/users/entities/user.entity";
import { Wallets } from "../entities/wallets.entity";
import { TransactionType, Type, WalletsTransactions } from "../entities/wallets-transactions.entity";
import { WalletActivity } from "../entities/wallet-activity.entity";
import CreateTransactionDto from "../dto/create-transaction.dto";



@Injectable()
export class WalletService {

    async addMoneyToWallet(id: number, data: any) {
        const user = await User.findOne({
            where: {
                id: id
            }
        });
        const wallet = await Wallets.findOne({
            where: {
                organization: user.organization.id,
            }
        });
        wallet.balance = data.balance;
        await wallet.save();


    };

    async getCurrentWalletBalance(userId: number) {
        const user = await User.findOne({
            where: {
                id: userId
            }
        });
        const wallet = await Wallets.findOne({
            where: {
                organization: user.organization.id,
            }
        });
        return wallet;
    };

    async addWalletTransaction(data: CreateTransactionDto, userId: number) {
        const user = await User.findOne({
            where: {
                id: userId,
            }
        });

        try{
            const walletsTransactions = new WalletsTransactions();
        walletsTransactions.amount = data.amount;
        walletsTransactions.description = data.description;
        walletsTransactions.type = data.type;
        walletsTransactions.transactionType = data.transactionType;
        walletsTransactions.updatedBy = userId;
        walletsTransactions.organization_id = user.organization.id;
        await walletsTransactions.save();
        

        }catch(err){
             console.error(err)
        };
        
    };

    async addWalletActivity(data: any, userId: number) {
        const walletactivity = new WalletActivity();
        walletactivity.activityType = data.activitytype;
        walletactivity.credits = data.amount;
        walletactivity.activityUserId = userId;
        await walletactivity.save();
         
        if (data.activitytype === "Quantum") {
            this.addWalletTransaction(data, userId);
        };
    };

    async viewWalletActivity(userId: number) {
        const user = await User.findOne({
            where: {
                id: userId
            }
        });
       
        const walletTransactions = await WalletsTransactions.find({
            where: {
                organization_id: user.organization.id
            }
        });
        return walletTransactions

    };

    async viewWalletHistory(userId:number){
        const user=await User.findOne({
            where:{
                id:userId
            }
        });

        const walletTransactions = await WalletActivity.find({
            where: {
                activityUserId:userId
            }
        });
        return walletTransactions

    }

    async getDebitBalance(userId: number) {
        const user = await User.findOne({
            where: {
                id: userId
            }
        })
        const transactions = await WalletsTransactions
            .createQueryBuilder('transaction')
            .select('transaction.transactionType', 'transactionType')
            .addSelect('SUM(transaction.amount)', 'totalAmount')
            .groupBy('transaction.transactionType')
            .where('transaction.type=:type', { type: Type.DEBIT })
            .andWhere('transaction.organization_id=:organizationId', { organizationId: user.organization.id })
            .getRawMany();

        const getAmount = (type: TransactionType) => {
            const filter: any[] = transactions.filter((item) => item.transactionType === type);
            if (filter.length) {
                return filter[0].totalAmount
            } else {
            };
        };

        let FeesAmount = getAmount(TransactionType.FEES) || 0;
        let AmountAmount = getAmount(TransactionType.AMOUNT) || 0;
        let GstAmount = getAmount(TransactionType.GST) || 0;

        let totalAmount = parseFloat(FeesAmount) + parseFloat(AmountAmount) + parseFloat(GstAmount);
        let FeesAmountPercentage = Math.round((FeesAmount / totalAmount) * 100);
        let AmountAmountPercentage = Math.round((AmountAmount / totalAmount) * 100);
        let GstAmountPercentage = Math.round((GstAmount / totalAmount) * 100);

        return {
            FeesAmount,
            AmountAmount,
            GstAmount,
            FeesAmountPercentage,
            AmountAmountPercentage,
            GstAmountPercentage,
            totalAmount


        }





        // transactions.forEach((transaction:any)=>{

        // })


        return transactions;

    };


};