import Client from 'src/modules/clients/entity/client.entity';
import Task from 'src/modules/tasks/entity/task.entity';
import { User } from 'src/modules/users/entities/user.entity';
import {
  AfterLoad,
  BaseEntity,
  Column,
  CreateDateColumn,
  Entity,
  JoinColumn,
  ManyToOne,
  OneToMany,
  OneToOne,
  PrimaryGeneratedColumn,
  RelationId,
  UpdateDateColumn,
} from 'typeorm';
import { Organization } from 'src/modules/organization/entities/organization.entity';
import Kyb from '../kyb/kyb.entity';
import { UserProfile } from '../users/entities/user-profile.entity';
import Expenditure from '../expenditure/expenditure.entity';
import { OrganizationLicense } from '../organization/entities/organization-license.entity';
import { BillingEntity } from '../organization/entities/billing-entity.entity';
import { BankAccount } from '../organization/entities/bank-account.entity';
// import StageOfWork as  from '../services/entities/stage-of-work.entity';
// Use the imported StageOfWork as d in your code
import ChatRoom from '../chats/chat-room.entity';
import ChatMessage from '../chats/chat-message.entity';
import DscApply from '../dsc-register/entity/dsc-apply.entity';
import { fetchRedirectUrl } from 'src/utils/re-use';
import { getBharahCloudLink, getDirectLink } from 'src/utils/getDirectLink';
import ClientGroup from '../client-group/client-group.entity';
import StageOfWork from '../tasks/entity/stage-of-work.entity';
import ServiceStageOfWork from '../services/entities/stage-of-work.entity';
import InvoiceBankDetails from '../billing/entitities/invoice-bank-details.entity';
import Posters from '../poster/posters.entity';
import DocumentsData from '../document-in-out/entity/documents-data.entity';
import ViderAi from '../vider-ai/viderAi.entity';
import PosterEvents from '../poster/poster-events.entity';
import Event from '../events/event.entity';
import PosterConfig from '../poster/poster-config.entity';
import AutFyaNotice from '../automation/entities/aut_income_tax_eproceedings_fya_notice.entity';
import GstrAdditionalNoticeOrders from '../gstr-automation/entity/gstrAdditionalOrdersAndNotices.entity';
import GstrNoticeOrders from '../gstr-automation/entity/noticeOrders.entity';
import AutProceedingResponseFya from '../automation/entities/aut_income_tax_eproceedings_fyi_notice_response_fya.entity';

export enum StorageType {
  FOLDER = 'folder',
  FILE = 'file',
  LINK = 'link',
  LOCAL_PATH = 'local_path',
}

export enum StorageSystem {
  AMAZON = 'AMAZON',
  MICROSOFT = 'MICROSOFT',
  GOOGLE = 'GOOGLE',
  BHARATHCLOUD = 'BHARATHCLOUD',
}

let ignoreFileTypes = [
  'application/pdf',
  //  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
];

@Entity()
class Storage extends BaseEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  uid: string;

  @Column()
  name: string;

  @Column({ type: 'enum', enum: StorageType })
  type: StorageType;

  @Column({ nullable: true })
  fileType: string;

  @ManyToOne(() => PosterEvents, (posterEvents) => posterEvents.storage)
  posterEvents: PosterEvents;

  @ManyToOne(() => Event, (event) => event.storage)
  event: Event;


  @OneToOne(() => PosterConfig, (PosterConfig) => PosterConfig.orgLogo)
  posterConfig: PosterConfig;

  @Column({ nullable: true })
  file: string;

  @Column({ nullable: true })
  filePath: string;

  @Column({ nullable: true })
  downloadUrl: string;

  @Column({ nullable: true })
  fileId: string;

  @Column({ nullable: true })
  collectId: number;

  @Column({ nullable: true })
  origin: string;

  @Column({ default: true })
  show: boolean;

  @Column({ type: 'enum', enum: StorageSystem, default: StorageSystem.AMAZON })
  storageSystem: StorageSystem;

  @Column({ nullable: true })
  webUrl: string;

  @Column({ nullable: true })
  authId: number;

  @ManyToOne(() => Client, (client) => client.storage, { onDelete: 'SET NULL' })
  @JoinColumn()
  client: Client;

  @ManyToOne(() => ClientGroup, (clientGroup) => clientGroup.storage, { onDelete: 'SET NULL' })
  @JoinColumn()
  clientGroup: ClientGroup;

  @ManyToOne(() => ChatRoom, (chatRoom) => chatRoom.storage, { onDelete: 'SET NULL' })
  @JoinColumn()
  room: ChatRoom;

  @ManyToOne(() => Task, (task) => task.attachments)
  @JoinColumn()
  task: Task;

  @ManyToOne(() => AutFyaNotice, (autFyaNotice) => autFyaNotice.storage)
  @JoinColumn()
  autFyaNotice: AutFyaNotice;

  @ManyToOne(
    () => AutProceedingResponseFya,
    (autProceedingResponseFya) => autProceedingResponseFya.storage,
  )
  @JoinColumn()
  autProceedingResponseFya: AutProceedingResponseFya;

  @ManyToOne(
    () => GstrAdditionalNoticeOrders,
    (gstrAdditionalNoticeOrders) => gstrAdditionalNoticeOrders.storage,
  )
  @JoinColumn()
  gstrAdditionalNoticeOrders: GstrAdditionalNoticeOrders;

  @ManyToOne(() => GstrNoticeOrders, (gstrNoticeOrders) => gstrNoticeOrders.storage)
  @JoinColumn()
  gstrNoticeOrders: GstrNoticeOrders;

  @ManyToOne(() => DocumentsData, (documentsData) => documentsData.attachments)
  @JoinColumn()
  documentsData: DocumentsData;

  @ManyToOne(() => Organization, (organization) => organization.storage, { onDelete: 'SET NULL' })
  @JoinColumn()
  organization: Organization;

  @ManyToOne(() => Storage, (storage) => storage.children, { onDelete: 'CASCADE' })
  parent: Storage;

  @OneToMany(() => Storage, (storage) => storage.parent)
  children: Storage[];

  @ManyToOne(() => User, (user) => user.storage)
  user: User;

  @OneToOne(() => Kyb, (kyb) => kyb.storage)
  @JoinColumn()
  kyb: Kyb;

  @OneToOne(() => UserProfile, (userProfile) => userProfile.addharStorage)
  @JoinColumn()
  addharStorage: UserProfile;

  @OneToOne(() => UserProfile, (userProfile) => userProfile.profileSign)
  profileSign: UserProfile;

  @OneToOne(() => DscApply, (dscApply) => dscApply.dscAadhar)
  dscAadhar: DscApply;

  @OneToOne(() => DscApply, (dscApply) => dscApply.dscPan)
  dscPan: DscApply;

  @OneToOne(() => DscApply, (dscApply) => dscApply.dscPhoto)
  dscPhoto: DscApply;

  @OneToOne(() => UserProfile, (userProfile) => userProfile.panStorage)
  @JoinColumn()
  panStorage: UserProfile;

  @OneToOne(() => UserProfile, (userProfile) => userProfile.drivingLicenseStorage)
  @JoinColumn()
  drivingLicenseStorage: UserProfile;

  @OneToOne(() => Expenditure, (expenditure) => expenditure.storage)
  @JoinColumn()
  expenditure: Expenditure;

  @OneToOne(() => User, (user) => user.imageStorage)
  @JoinColumn()
  userImage: User;

  @OneToOne(() => Posters, (posters) => posters.storage)
  poster: Posters;
  @OneToOne(() => ViderAi, (viderAi) => viderAi.storage)
  @JoinColumn()
  viderAi: ViderAi;

  @OneToOne(() => Client, (client) => client.storage)
  @JoinColumn()
  clientImage: Client;

  @OneToOne(() => ClientGroup, (clientGroup) => clientGroup.storage)
  @JoinColumn()
  clientGroupImage: ClientGroup;

  @OneToOne(() => OrganizationLicense, (organizationLicense) => organizationLicense.storage)
  @JoinColumn()
  organizationLicense: OrganizationLicense;

  @OneToOne(() => BillingEntity, (billingEntity) => billingEntity.logStorage)
  @JoinColumn()
  logStorage: BillingEntity;

  @OneToOne(() => BillingEntity, (billingEntity) => billingEntity.signatureStorage)
  @JoinColumn()
  signatureStorage: BillingEntity;

  @OneToOne(() => BankAccount, (bankAccount) => bankAccount.upiStorage)
  @JoinColumn()
  upiStorage: BankAccount;

  @OneToOne(() => StageOfWork, (stageOfWork) => stageOfWork.storage)
  @JoinColumn()
  stageOfWork: StageOfWork;

  @OneToOne(() => ServiceStageOfWork, (serviceStageOfWork) => serviceStageOfWork.storage)
  @JoinColumn()
  serviceStageOfWork: ServiceStageOfWork;

  @OneToOne(() => ChatMessage, (chatMessage) => chatMessage.storage)
  chatMessage: ChatMessage;

  @OneToOne(() => Organization, (organization) => organization.orgGstStorage)
  @JoinColumn()
  orgGstStorage: Organization;

  @OneToOne(() => Organization, (organization) => organization.orgPanStorage)
  @JoinColumn()
  orgPanStorage: Organization;

  @OneToOne(
    () => InvoiceBankDetails,
    (invoiceBankDetails) => invoiceBankDetails.invoiceBankAttachement,
  )
  @JoinColumn()
  invoiceBankAttachement: InvoiceBankDetails;

  @Column({ nullable: true })
  fileSize: number;

  fileUrl: string;

  @RelationId((storage: Storage) => storage.posterEvents)
  posterEventsId: number;

  error: string;

  @CreateDateColumn()
  createdAt: string;

  @UpdateDateColumn()
  updatedAt: string;

  @AfterLoad()
  async renderImageUrl() {
    if (this.storageSystem == 'GOOGLE' && this.fileId) {
      this.webUrl = `https://drive.google.com/file/d/${this.fileId}/view`;
      this.fileUrl = `https://drive.google.com/thumbnail?id=${this.fileId}`;
      this.downloadUrl = `https://drive.google.com/uc?export=download&id=${this.fileId}`;
      // this.fileUrl = `https://drive.google.com/uc?export=view&id=${this.fileId}`;
    } else if (this.file || (this.storageSystem === 'MICROSOFT' && this.fileId)) {
      if (this.storageSystem == 'AMAZON') {
        this.fileUrl = `${process.env.AWS_BASE_URL}/${this.file}`;
      } else if (this.storageSystem === 'MICROSOFT') {
        this.downloadUrl = this.webUrl;
        // this.downloadUrl = https://graph.microsoft.com/v1.0/me/drive/items/${this.fileId}/content;
        try {
          if (ignoreFileTypes.includes(this.fileType)) return null;
          const aa = await getDirectLink(this.fileId, this.authId, this.id);
          this.fileUrl = aa;
          this.downloadUrl = aa;
          if (!this.fileUrl) {
            this.fileUrl = this.webUrl;
          }
        } catch (err) {
          const message = err?.response?.message;
          if (message == 'invalid_grant') {
            this.error = 'invalid_grant';
          }
        }
      } else if (this.storageSystem === 'BHARATHCLOUD') {
        this.fileUrl = await getBharahCloudLink(this.authId, this.file);
      }
    }
  }
}

export default Storage;