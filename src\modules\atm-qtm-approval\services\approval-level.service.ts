import { BadRequestException, Injectable } from '@nestjs/common';
import { User } from 'src/modules/users/entities/user.entity';
import ApprovalProcedures from '../entities/approval-procedures.entity';
import { createQueryBuilder } from 'typeorm';
import Task from 'src/modules/tasks/entity/task.entity';
import axios from 'axios';
import { TaskStatusEnum } from 'src/modules/tasks/dto/types';
import { each } from 'lodash';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { Event_Actions } from 'src/event-listeners/actions';
import { UpdateTaskApprovalsDto } from '../dto/update-approvals.dto';
import Activity, { ActivityType } from 'src/modules/activity/activity.entity';
import { compareSpecificUrls } from 'src/utils/inv-comparision';
import * as moment from 'moment';

@Injectable()
export class ApprovalLevelService {
  constructor(private eventEmitter: EventEmitter2) { }

  async find(userId: number) {
    let user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
    let result = await createQueryBuilder(ApprovalProcedures, 'approvalProcedures')
      .leftJoin('approvalProcedures.organization', 'organization')
      .leftJoinAndSelect('approvalProcedures.approval', 'approval')
      .leftJoinAndSelect('approval.approvalLevels', 'approvalLevel')
      .leftJoinAndSelect('approvalLevel.user', 'user')
      .where('organization.id = :orgId', { orgId: user.organization.id })
      .andWhere('approvalProcedures.module=:module', { module: 'task' })
      .andWhere('approvalProcedures.status=:status', { status: 'created' })
      .getMany();
    return result;
  }

  async findTasks(data: any) {
    let task = await Task.findOne({ where: { id: data.taskId } });

    if (task.processInstanceId) {
      let result: any = [];

      result = await this.getApprovals(task.processInstanceId, task.status);

      //  else {
      //     result = await this.recentSnapshot(task.processInstanceId)
      // }
      return { result, taskStatus: task.status };
    }
    return [];
  }

  async getApprovals(id: string, status: string) {
    try {
      const url = `${process.env.CAMUNDA_URL}/vider/quantum/api/process/${id}`;
      let data: any = {
        method: 'get',
        maxBodyLength: Infinity,
        headers: {
          'Content-Type': 'application/json',
        },
      };

      const response = await axios.get(url, data);
      const result = response?.data?.tasks.filter(
        (task) => task.name !== 'holdTillDocumentIsSubmitted',
      );
      const taskSnapshots = response?.data?.data?.taskSnapshots;
      const taskSnapshotsMainTasks = taskSnapshots?.map((snapshot) => snapshot.tasks);
      let resultTasks: any = [];
      const firstTask = result.filter((task) => {
        return task.name[task.name.length - 1] == 1;
      });
      for (let taskitems of result) {
        const name = taskitems.name;
        let rejectionArray: any = [];
        if (response?.data?.data?.taskSnapshots) {
          rejectionArray = taskSnapshotsMainTasks
            ?.map((subArray) => {
              const task = subArray?.find(
                (task) =>
                  task?.name === name &&
                  (task?.status === 'DECLINED' ||
                    task?.status === 'AUTO_DECLINED' ||
                    task?.status === 'APPROVED'),
              );

              if (task) {
                return {
                  lastUpdatedOn: task.lastUpdatedOn,
                  comments: task.comments,
                  name: task.name,
                  status: task.status,
                };
              }

              return null; // Return null if no matching task is found
            })
            .filter((task) => task !== null);
        }
        if (status == TaskStatusEnum.UNDER_REVIEW) {
          rejectionArray.push({
            lastUpdatedOn: taskitems.lastUpdatedOn,
            comments: taskitems.comments,
            name: taskitems.name,
            status: taskitems.status,
          });
        }

        if (status !== TaskStatusEnum.UNDER_REVIEW) {
          if (firstTask[0].status) {
            rejectionArray.push({
              lastUpdatedOn: taskitems.lastUpdatedOn,
              comments: taskitems.comments,
              name: taskitems.name,
              status: taskitems.status,
            });
          }
        }

        taskitems.rejectionArray = rejectionArray;
        resultTasks.push(taskitems);
      }

      resultTasks.sort((a: any, b: any) => {
        const lastCharA = a.name.charAt(a.name.length - 1);
        const lastCharB = b.name.charAt(b.name.length - 1);

        if (lastCharA < lastCharB) {
          return -1;
        } else if (lastCharA > lastCharB) {
          return 1;
        } else {
          return 0;
        }
      });
      return resultTasks;
    } catch (err) {
      console.error(err);
    }
  }

  async changeStatus(id: number, body: any) {
    const compareEnvUrls = compareSpecificUrls(process.env.CAMUNDA_URL, process.env.SERVER_URL);
    if (!compareEnvUrls) {
      console.error("It appears that Camunda is currently connected to the incorrect environment. This misconfiguration may lead to unexpected behavior or issues, as it could be interacting with the wrong database or set of processes. To resolve this, we should verify the connection settings and ensure that Camunda is properly configured to connect to the intended environment")
      throw new BadRequestException("Error in Approval Management");
    }
    const user = await User.findOne({
      where: {
        id,
      },
    });
    // try {
    const url = `${process.env.CAMUNDA_URL}/vider/quantum/api/task/complete`;
    const data = {
      taskId: body.approvalId,
      status: body.status,
      comments: body.remarks,
      userId: user.id,
    };
    const response = await axios.put(url, data);
    if (body.status === 'DECLINED' && response?.data) {
      let task = await Task.findOne({
        where: {
          id: body.taskId,
        },
      });
      task.status = TaskStatusEnum.IN_PROGRESS;
      task.statusUpdatedAt = moment().format('YYYY-MM-DD HH:mm:ss.SSSSSS');
      await task.save();
    }
    let task = await Task.findOne({
      where: {
        id: body.taskId,
      },
      relations: ['approvalProcedures', 'client', 'members', 'organization', 'clientGroup'],
    });

    const nextLevelDetails = await this.getNextApprovalDetails(
      task.processInstanceId,
      body?.level,
    );
    const approval = await ApprovalProcedures.findOne({
      where: { id: task.approvalProcedures.id, organization: user.organization.id },
      relations: ['approval', 'approval.approvalLevels', 'approval.approvalLevels.user'],
      select: ['id'],
    });

    if (response?.data && body.status === 'DECLINED') {
      let task = await Task.findOne({
        where: {
          id: body.taskId,
        },
        relations: ['organization', 'members', 'approvalProcedures', 'client', 'clientGroup'],
      });
      const numberLevel = body?.level.charAt(body?.level.length - 1);
      this.eventEmitter.emit(Event_Actions.CAMUNDA_TASK_DECLINE, { task, user });
      task.approvalStatus = [
        {
          status: `Level ${numberLevel} / ${approval?.approval?.approvalLevels.length} Rejected`,
          user: user.fullName,
        },
      ];
      await task.save();
      let activity = new Activity();
      activity.action = Event_Actions.APPROVAL_REJECTED;
      activity.actorId = user.id;
      activity.type = ActivityType.TASK;
      activity.typeId = task?.id;
      activity.remarks = `${approval?.approval?.name} rejected approval to the task submitted by ${user?.fullName}`;
      await activity.save();
      return { changeStatus: true };
    }

    if (response?.data && body.status === 'APPROVED') {
      const numberLevel = body?.level.charAt(body?.level.length - 1);
      if (nextLevelDetails.length) {
        this.eventEmitter.emit(Event_Actions.CAMUNDA_TASK_APPROVAL, {
          nextLevelDetails,
          task,
          approval,
          user,
        });
        task.approvalStatus = [
          {
            status: `Level ${numberLevel}/${approval?.approval?.approvalLevels.length} Approved`,
            user: user.fullName,
            progress: true,
          },
        ];
        await task.save();
      } else {
        this.eventEmitter.emit(Event_Actions.CAMUNDA_TASK_APPROVAL_COMPLETE, { task });
        task.approvalStatus = [
          {
            status: 'Approvals Completed',
            completed: true,
            user: null,
          },
        ];
        await task.save();
      }
      let activity = new Activity();
      activity.action = Event_Actions.APPROVAL_APPROVED;
      activity.actorId = user.id;
      activity.type = ActivityType.TASK;
      activity.typeId = task?.id;
      activity.remarks = `${approval?.approval?.name} granted approval to the task submitted by ${user?.fullName}`;
      await activity.save();
    }
    return { changeStatus: false };
    // } catch (err) {
    //   console.error(err);
    // }
  }

  async getNextApprovalDetails(id: string, level: string) {
    try {
      const url = `${process.env.CAMUNDA_URL}/vider/quantum/api/process/${id}`;
      let data: any = {
        method: 'get',
        maxBodyLength: Infinity,
        headers: {
          'Content-Type': 'application/json',
        },
      };

      const response = await axios.get(url, data);
      const result = response?.data?.tasks?.filter(
        (task: any) => task.name === 'holdTillDocumentIsSubmitted',
      );
      if (result[0]?.status) {
        const arr = [];
        return arr;
      } else {
        const lastDigit = parseInt(level.slice(-1));
        const newLastDigit = (lastDigit + 1) % 10;
        const updatedString = level.slice(0, -1) + newLastDigit;
        const nextLevel = response?.data?.tasks.filter((task: any) => task.name === updatedString);
        return nextLevel;
      }
    } catch (err) {
      console.error(err);
    }
  }

  async updateTaskApprovals(body: UpdateTaskApprovalsDto, userId: number) {
    const compareEnvUrls = compareSpecificUrls(process.env.CAMUNDA_URL, process.env.SERVER_URL);
    if (!compareEnvUrls) {
      console.error("It appears that Camunda is currently connected to the incorrect environment. This misconfiguration may lead to unexpected behavior or issues, as it could be interacting with the wrong database or set of processes. To resolve this, we should verify the connection settings and ensure that Camunda is properly configured to connect to the intended environment")
      throw new BadRequestException("Error in Approval Management");
    }

    const { taskId, approvalHierarchyId, removeApproval } = body;

    let task = await Task.findOne({ where: { id: taskId }, relations: ['approvalProcedures'] });
    let user = await User.findOne({ where: { id: userId } });
    const oldApprovalId = task?.approvalProcedures?.id;
    const oldApprovalName = task?.approvalProcedures?.name;

    if (removeApproval) {
      task.approvalProcedures = null;
      task.processInstanceId = null;
      task.approvalStatus = null;
    }

    if (approvalHierarchyId) {
      const approval = await ApprovalProcedures.findOne({
        where: { id: approvalHierarchyId, organization: user.organization.id },
        relations: ['approval', 'approval.approvalLevels', 'approval.approvalLevels.user'],
        select: ['id', 'name'],
      });
      task.approvalProcedures = approval;

      // try {
      const data = JSON.stringify({
        processKey: 'genericApprovalProcess',
        metaData: {
          typeOfApproval: 'ATOM_TASK',
          approvalProcessId: `Level${approval?.approval?.approvalLevels.length}ApprovalProcess`,
        },
      });

      let config: any = {
        method: 'post',
        maxBodyLength: Infinity,
        url: `${process.env.CAMUNDA_URL}/vider/quantum/api/process`,
        headers: { 'Content-Type': 'application/json' },
        data: data,
      };

      const response = await axios.request(config);
      const processInstanceId = response?.data?.processInstanceId;

      // if (processInstanceId) {
      //   task.approvalStatus = [{
      //     status: `Approval Levels (${approval?.approval?.approvalLevels.length})`,
      //     completed: false,
      //   }];
      // }
      task.processInstanceId = processInstanceId;
      const processApprovals = await this.approvalProcess(processInstanceId, approval);
      if (processApprovals) {
        task.approvalStatus = [{
          status: `Approval Levels (${approval?.approval?.approvalLevels.length})`,
          completed: false,
        }];
      }

      // } catch (err) {
      //   console.log(`Error on creating camunda approval process: ${err}`);
      // }
    }

    await task.save();

    let taskactivity = new Activity();
    taskactivity.action = removeApproval ? Event_Actions.APPROVAL_REMOVED :
      oldApprovalId === undefined ? Event_Actions.APPROVAL_ADDED : Event_Actions.APPROVAL_UPDATED;
    taskactivity.actorId = userId;
    taskactivity.type = ActivityType.TASK;
    taskactivity.typeId = body.taskId;
    taskactivity.remarks = removeApproval ?
      `Approval Process "${oldApprovalName}" removed by ${user.fullName}` :
      oldApprovalId === undefined ?
        `Approval Process "${task.approvalProcedures.name}" added by ${user.fullName}` :
        `Approval Process updated from "${oldApprovalName}" to "${task.approvalProcedures.name}" by ${user.fullName}`;
    await taskactivity.save();

    return 'success';
  }

  async approvalProcess(id: string, approvalData: ApprovalProcedures) {
    // try {
    const url = `${process.env.CAMUNDA_URL}/vider/quantum/api/process/${id}`;
    let data: any = {
      method: 'get',
      maxBodyLength: Infinity,
      headers: { 'Content-Type': 'application/json' },
    };

    const response = await axios.get(url, data);
    const { approval } = approvalData;
    const { approvalLevels } = approval;
    const assignApprovalTasks = response?.data?.tasks
      .filter((item: any) => item.name !== 'holdTillDocumentIsSubmitted')
      .map((item: any) => {
        let levelNumber = parseInt(item.name.slice(-1));
        const foundUser = approvalLevels.find((level) => level.level === levelNumber);
        const { user } = foundUser;
        const { id } = user;
        return `${process.env.CAMUNDA_URL}/vider/quantum/api/task/${item.id}/assign/${id}`;
      });

    const makeApiCall = async (url: string) => {
      // try {
      let config: any = {
        method: 'put',
        maxBodyLength: Infinity,
        headers: { 'Content-Type': 'application/json' },
      };
      const response = await axios.put(url, config);
      return response.data;
      // } catch (error) {
      // console.error('Error for', url, ':', error);
      // throw error;
      // }
    };

    await Promise.all(assignApprovalTasks.map((endpoint) => makeApiCall(endpoint)));
    return true;
    // } catch (err) {
    // console.log(err);
    // }
  }
}
