import { User } from 'src/modules/users/entities/user.entity';
import {
  BaseEntity,
  Column,
  CreateDateColumn,
  Entity,
  ManyToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
//atom_to_qtmrequests
@Entity()
class AtomToQtmrequests extends BaseEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  status: boolean;

  @Column()
  organizationId: number;

  @ManyToOne(() => User, (user) => user.atomToQtmrequests)
  user: User;

  @CreateDateColumn()
  createdAt: string;

  @UpdateDateColumn()
  updatedAt: string;
}

export default AtomToQtmrequests;
