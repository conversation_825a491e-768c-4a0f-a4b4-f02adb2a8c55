import {
  Body,
  Controller,
  Get,
  Param,
  ParseIntPipe,
  Patch,
  Post,
  Put,
  Query,
  Request,
  UploadedFile,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { JwtAuthGuard } from 'src/modules/users/jwt/jwt-auth.guard';
import BulkDeleteDto from '../dto/bulk-delete-dto';
import BulkUpdateDto from '../dto/bulk-update.dto';
import CreateClientDto from '../dto/create-client.dto';
import FindQueryDto from '../dto/find-query.dto';
import { UpdateClientDto } from '../dto/update-client.dto';
import { ClientService } from '../services/clients.service';

@Controller('client')
export class ClientController {
  constructor(private service: ClientService) { }

  @UseGuards(JwtAuthGuard)
  @Post()
  create(@Request() req: any, @Body() body: CreateClientDto) {
    const { userId } = req.user;
    return this.service.create(userId, body);
  }

  @UseGuards(JwtAuthGuard)
  @Get()
  getAll(@Request() req: any, @Query() query: FindQueryDto) {
    const { userId } = req.user;
    return this.service.findAll(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Get('client-manager')
  findAllClientManagerClient(@Request() req: any, @Query() query: FindQueryDto) {
    const { userId } = req.user;
    return this.service.findAllClientManagerClient(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Get('client-group-manager')
  findAllClientGroupManagerClient(@Request() req: any, @Query() query: FindQueryDto) {
    const { userId } = req.user;
    return this.service.findAllClientGroupManagerClient(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Get('/withoutClients')
  getWithoutClients(@Request() req: any, @Query() query: FindQueryDto) {
    const { userId } = req.user;
    return this.service.getWithoutClients(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Get('/nonclientgroupclient')
  getNonClientGroupClient(@Request() req: any, @Query() query: FindQueryDto) {
    const { userId } = req.user;
    return this.service.getNonClientGroupClient(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Get('/task')
  getTaskCreateClientAll(@Request() req: any, @Query() query: FindQueryDto) {
    const { userId } = req.user;
    return this.service.getTaskCreateClientAll(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Get('/import-data')
  getImportData(@Request() req: any) {
    const { userId } = req.user;
    return this.service.getImportData(userId);
  }
  
  @UseGuards(JwtAuthGuard)
  @Get('/task-new')
  getTaskCreateClientAllNew(@Request() req: any, @Query() query: FindQueryDto) {
    const { userId } = req.user;
    return this.service.getTaskCreateClientAllNew(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Get('/storage')
  getStorageClientAll(@Request() req: any, @Query() query: FindQueryDto) {
    const { userId } = req.user;
    return this.service.getStorageClientAll(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Get('/:id/details')
  getOne(@Request() req: any, @Param('id', ParseIntPipe) id: number) {
    const { userId } = req.user;
    return this.service.findOne(id, userId);
  }
  @UseGuards(JwtAuthGuard)
  @Get('/:id/inv')
  getOneInv(@Request() req: any, @Param('id', ParseIntPipe) id: number) {
    const { userId } = req.user;
    return this.service.findOne(id, userId);
  }

  @UseGuards(JwtAuthGuard)
  @Get('/deleted')
  getDeleted(@Request() req: any, @Query() query: FindQueryDto, @Body() payload: any) {
    const { userId } = req.user;
    return this.service.findDeleted(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Patch('/:id/update')
  update(
    @Param('id', ParseIntPipe) id: number,
    @Body() body: UpdateClientDto,
    @Request() req: any,
  ) {
    const { userId } = req.user;
    return this.service.update(userId, id, body);
  }

  @UseGuards(JwtAuthGuard)
  @Post('/addDomainRecord')
  addDomainRecord(
    @Body() body: any,
    @Request() req: any,
  ) {
    return this.service.addDomainRecord(body);
  }

  @UseGuards(JwtAuthGuard)
  @Post('/import')
  @UseInterceptors(FileInterceptor('file'))
  importClients(@UploadedFile() file: Express.Multer.File, @Request() req: any) {
    const { userId } = req.user;
    return this.service.importClients(userId, file);
  }

  @UseGuards(JwtAuthGuard)
  @Post('/bulk-delete')
  bulkDelete(@Body() body: BulkDeleteDto, @Request() req: any) {
    const { userId } = req.user;
    return this.service.bulkDelete(body.ids, userId);
  }

  @UseGuards(JwtAuthGuard)
  @Post('/:id/restore')
  restoreClient(@Param('id', ParseIntPipe) id: number, @Request() req: any, @Body() body: any) {
    const { userId } = req.user;
    return this.service.restoreClient(id, userId, body);
  }

  @UseGuards(JwtAuthGuard)
  @Post('/bulk-restore')
  restoreBulkClient(@Request() req: any, @Body() body: any) {
    const { userId } = req.user;
    return this.service.restoreBulkClient(userId, body);
  }

  @UseGuards(JwtAuthGuard)
  @Patch('/bulk-update')
  bulkUpdate(@Body() body: BulkUpdateDto, @Request() req: any) {
    const { userId } = req.user;
    return this.service.bulkUpdate(body, userId);
  }

  @UseGuards(JwtAuthGuard)
  @Get('/all')
  getAllClients(@Request() req: any) {
    const { userId } = req.user;
    return this.service.getAllClients(userId);
  }
  @UseGuards(JwtAuthGuard)
  @Put('/image/:id')
  updateImage(@Request() req: any, @Param('id', ParseIntPipe) id: number, @Body() data: any) {
    const { userId } = req.user;
    return this.service.updateImage(id, userId, data);
  }

  @UseGuards(JwtAuthGuard)
  @Post('/bulkupdate')
  @UseInterceptors(FileInterceptor('file'))
  bulkUpdateClients(@UploadedFile() file: Express.Multer.File, @Request() req: any) {
    const { userId } = req.user;
    return this.service.bulkUpdateClients(userId, file);
  }
}
