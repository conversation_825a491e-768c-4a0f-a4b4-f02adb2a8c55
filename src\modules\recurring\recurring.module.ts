import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import RecurringProfile from './entity/recurring-profile.entity';
import { RecurringController } from './recurring.controller';
import { RecurringService } from './recurring.service';
import { RecurringSubscriber } from 'src/event-subscribers/recurring.subscriber';
import { TasksService } from '../tasks/services/tasks.service';
import { PromiseService } from '../gstr-register/services/promise.service';
import { GstrRegisterService } from '../gstr-register/services/gstr-register.service';

@Module({
  imports: [TypeOrmModule.forFeature([RecurringProfile])],
  controllers: [RecurringController],
  providers: [
    RecurringService,
    TasksService,
    RecurringSubscriber,
    PromiseService,
    GstrRegisterService,
  ],
})
export class RecurringModule {}
