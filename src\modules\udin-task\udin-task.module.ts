import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import UdinTask from './udin-task.entity';
import { UdinTaskService } from './udin-task.service';
import { UdinTaskController } from './udin-task.controller';

@Module({
  imports: [TypeOrmModule.forFeature([UdinTask])],
  controllers: [UdinTaskController],
  providers: [UdinTaskService],
})
export class UdinTaskModule {}