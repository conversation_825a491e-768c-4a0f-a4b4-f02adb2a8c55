import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  ParseIntPipe,
  Post,
  Put,
  Query,
  Request,
  UseGuards,
} from '@nestjs/common';
import { AddBankAccountDto, BankAccountDto } from '../dto/add-bank-account.dto';
import { JwtAuthGuard } from '../../users/jwt/jwt-auth.guard';
import { BankAccountsService } from '../services/bank-accounts.service';
import { GetBankAccountsDto } from '../dto/get-bank-accounts.dto';

@Controller('bank-accounts')
export class BankAccountsController {
  constructor(private service: BankAccountsService) { }

  @UseGuards(JwtAuthGuard)
  @Post()
  async create(@Request() req: any, @Body() body: AddBankAccountDto) {
    const { userId } = req.user;
    return this.service.create(userId, body);
  }

  @UseGuards(JwtAuthGuard)
  @Get()
  async get(@Request() req: any, @Query() query: GetBankAccountsDto) {
    const { userId } = req.user;
    return this.service.get(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Put(':id')
  async update(@Request() req: any, @Param('id', ParseIntPipe) id: number, @Body() body: BankAccountDto) {
    const { userId } = req.user;
    return this.service.update(id, userId, body);
  }

  @UseGuards(JwtAuthGuard)
  @Put('default/:id')
  async default(@Request() req: any, @Param('id', ParseIntPipe) id: number, @Body() body: BankAccountDto) {
    return this.service.default(id, body);
  }

  @UseGuards(JwtAuthGuard)
  @Delete(':id')
  async delete(@Request() req: any, @Param('id', ParseIntPipe) id: number) {
    const { userId } = req.user;
    return this.service.delete(userId, id);
  }
}
