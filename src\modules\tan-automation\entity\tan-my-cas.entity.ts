import Client from 'src/modules/clients/entity/client.entity';
import {
  BaseEntity,
  Column,
  CreateDateColumn,
  Entity,
  ManyToOne,
  PrimaryGeneratedColumn,
} from 'typeorm';
import TanClientCredentials from './tan-client-credentials.entity';

@Entity()
class TanMyCas extends BaseEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  panNumber: string;

  @Column()
  tanNumber: string;

  @Column()
  caName: string;

  @Column()
  assesseCaId: string;

  @Column()
  caMembershipNum: string;

  @Column()
  caStatus: string;

  @Column()
  assignedDate: string;

  @Column()
  filingStatus: string;

  @Column()
  filingType: string;

  @Column()
  formTypeCd: string;

  @Column()
  isWithdrawable: string;

  @Column()
  formStatus: string;

  @Column()
  transactionNo: string;

  @Column()
  assessmentYear: string;

  @Column()
  financialYear: string;

  @Column()
  udinNumber: string;

  @Column()
  organizationId: number;

  @Column()
  clientId: number;

  // @ManyToOne(() => Client, (client) => client.autMycas, { onDelete: 'SET NULL' })
  // client: Client;

  @ManyToOne(
    () => TanClientCredentials,
    (tanClientCredentials) => tanClientCredentials.tanMycas,
    { onDelete: 'SET NULL' },
  )
  tanClientCredentials: TanClientCredentials;

}

export default TanMyCas;
