import { Modu<PERSON> } from "@nestjs/common";
import { TypeOrmModule } from "@nestjs/typeorm";
import GstrRegister from "./entity/gstr-register.entity";
import { GstrPromise } from "./entity/promise.entity";
import { GstrRegisterController } from "./controllers/gstr-register.controller";
import { GstrRegisterService } from "./services/gstr-register.service";
import { PromiseController } from "./controllers/promise.controller";
import { PromiseService } from "./services/promise.service";
import { ReturnsData } from "./entity/returns-data.entity";
import { GstrRegisterSubscriber } from "src/event-subscribers/gstr-register.subscriber";



@Module({
    imports: [TypeOrmModule.forFeature([GstrRegister, GstrPromise, ReturnsData])],
    controllers: [GstrRegisterController, PromiseController],
    providers: [GstrRegisterService,
        PromiseService,
        GstrRegisterSubscriber
    ]
})

export class GstrRegisterModule { }