import {
  Connection,
  EntitySubscriberInterface,
  EventSubscriber,
  InsertEvent,
  getManager,
  UpdateEvent,
} from 'typeorm';
import { Invoice } from '../modules/billing/entitities/invoice.entity';
import { Notification } from 'src/notifications/notification.entity';
import { User } from 'src/modules/users/entities/user.entity';
import {
  getUserDetails,
  insertINTONotificationUpdate,
  insertINTOnotification,
} from 'src/utils/re-use';
import { sendnewMail } from 'src/emails/newemails';
import OrganizationPreferences from 'src/modules/organization-preferences/entity/organization-preferences.entity';
import ViderWhatsappSessions from 'src/modules/whatsapp/entity/viderWhatsappSessions';
import { sendWhatsAppTextMessage } from 'src/modules/whatsapp/whatsapp.service';
import { Organization } from 'src/modules/organization/entities/organization.entity';

@EventSubscriber()
export class InvoiceSubscriber implements EntitySubscriberInterface<Invoice> {
  constructor(private readonly connection: Connection) {
    connection.subscribers.push(this);
  }

  listenTo() {
    return Invoice;
  }

  async beforeInsert(event: InsertEvent<Invoice>) {}

  async afterInsert(event: InsertEvent<Invoice>) {
    const entityManager = getManager();
    const {
      organization,
      invoiceNumber,
      client,
      clientGroup,
      grandTotal,
      particulars,
      emailCheck,
      id: invoiceId,
    } = event?.entity;
    const invoiceDateFormat = new Date(event?.entity.invoiceDate).toLocaleDateString('en-GB');
    const invoiceDueDateFormat = new Date(event?.entity.invoiceDueDate).toLocaleDateString('en-GB');
    const organizationId = organization?.id;
    const clientName = client?.displayName || clientGroup?.displayName;
    const getRoleQuery = `SELECT id FROM role where organization_id = ${organizationId}  and name = "Admin";`;
    let getRole = await entityManager.query(getRoleQuery);
    const roleId = getRole[0]?.id || '';
    const logInUser = await event.entity['userId'];

    const getUserQuery = `select id from user where organization_id=${organizationId} and role_id = ${roleId}`;
    let getUser = await entityManager.query(getUserQuery);
    const userIDs: User[] = getUser.map((row) => row.id);
    const userIdTwo = event.entity['userId'];
    const key = 'INVOICE_CREATION_MAIL';
    if (emailCheck && key === 'INVOICE_CREATION_MAIL' && client) {
      // const address = `${organization.buildingNo || " " ? organization.buildingNo || " " + ', ' : ''}${organization.floorNumber || " " ? organization.floorNumber || " " + ', ' : ''}${organization.buildingName || " " ? organization.buildingName + ', ' : ''}${organization.street ? organization.street + ', ' : ''}${organization.location ? organization.location + ', ' : ''}${organization.city ? organization.city + ', ' : ''}${organization.district ? organization.district + ', ' : ''}${organization.state ? organization.state + ', ' : ''}${organization.pincode ? organization.pincode : ''}`;
      const addressParts = [
        organization.buildingNo || '',
        organization.floorNumber || '',
        organization.buildingName || '',
        organization.street || '',
        organization.location || '',
        organization.city || '',
        organization.district || '',
        organization.state || '',
      ].filter((part) => part && part.trim() !== '');
      const pincode =
        organization.pincode && organization.pincode.trim() !== ''
          ? ` - ${organization.pincode}`
          : '';

      const address = addressParts.join(', ') + pincode;
      const [{ amount, name }] = particulars;
      const invoiceNum = invoiceNumber;

      const mailOptions = {
        id: logInUser,
        key: 'INVOICE_CREATION_MAIL',
        email: client?.email,
        clientMail: 'ORGANIZATION_CLIENT_EMAIL',
        data: {
          serviceName: name,
          legalName: organization?.tradeName || organization?.legalName,
          invoiceNumber: invoiceNum,
          invoiceDate: invoiceDateFormat,
          invoiceDueDateFormat: invoiceDueDateFormat,
          invoiceTotalAmount: grandTotal,
          clientName: clientName,
          address: address,
          phoneNumber: organization?.mobileNumber,
          mail: organization?.email,
          userId: logInUser,
        },

        filePath: 'client-invoice-created',
        subject: 'Invoice for Services Rendered',
        invoiceId,
      };

      const orgPreferences: any = await OrganizationPreferences.findOne({
        where: { organization: organizationId },
      });
      const clientPreferences = orgPreferences?.clientPreferences?.email;

      if (clientPreferences && clientPreferences[key]) {
        // await sendnewMail(mailOptions);
      }
    }
    if (userIdTwo) {
      const getUsertwoQuery = `SELECT full_name FROM user where id = ${userIdTwo};`;
      let getusertwo = await entityManager.query(getUsertwoQuery);
      const userName = getusertwo[0]?.full_name || '';
      async function insertINTOnotifications(event: any, users: Array<User>) {
        let notifications = [];
        let title = 'Invoice Created';
        let body = `A New Invoice "<strong>${invoiceNumber}</strong>" for <strong>${clientName}</strong> has been added.`;
        const key = 'INVOICE_CREATED_PUSH';
        insertINTONotificationUpdate(title, body, users, organizationId, key, invoiceId);
        const organization = await Organization.findOne({ id: organizationId });

        const addressParts = [
          organization.buildingNo || '',
          organization.floorNumber || '',
          organization.buildingName || '',
          organization.street || '',
          organization.location || '',
          organization.city || '',
          organization.district || '',
          organization.state || '',
        ].filter((part) => part && part.trim() !== '');
        const pincode =
          organization.pincode && organization.pincode.trim() !== ''
            ? ` - ${organization.pincode}`
            : '';

        const address = addressParts.join(', ') + pincode;
        if (client) {
          for (let a of users) {
            let getEmailQuery = `SELECT id, email,full_name FROM user where id = ${a};`;
            let getEmail = await entityManager.query(getEmailQuery);
            let mail = getEmail[0]?.email || '';
            let fullname = getEmail[0]?.full_name || '';
            let id = getEmail[0]?.id || '';
            let data = {
              clientName: clientName,
              invoiceNumber: invoiceNumber,
              invoiceDate: invoiceDateFormat,
              invoiceDueDate: invoiceDueDateFormat,
              username: fullname,
              invoiceAmount: grandTotal,
              userName: userName,
              userId: logInUser,
              adress: address,
              phoneNumber: organization?.mobileNumber,
              mail: organization?.email,
              legalName: organization?.tradeName || organization?.legalName,
            };
            let IData = {
              id,
              key: 'INVOICE_CREATED_MAIL',
              data: data,
              subject: `Invoice has been raised`,
              email: mail,
              filePath: 'invoice-created',
            };
            await sendnewMail(IData);
          }
        }
        //whatsapp
        try {
          if (userIDs !== undefined) {
            const sessionValidation = await ViderWhatsappSessions.findOne({
              where: { userId: userIDs, status: 'ACTIVE' },
            });
            if (sessionValidation) {
              const adminUserDetails = await getUserDetails(userIDs);

              const {
                full_name: userFullName,
                mobile_number: userPhoneNumber,
                id,
                organization_id,
              } = adminUserDetails;
              const key = 'INVOICES_CREATED_WHATSAPP';
              const whatsappMessageBody = `
 Hi ${userFullName}

An invoice ${invoiceNumber} for ${clientName} have been created by ${userName}

Invoice number: ${invoiceNumber}
Client name: ${clientName}
Invoice amount: ${grandTotal}
Invoice Due date: ${invoiceDueDateFormat}

We hope this helps!`;
              await sendWhatsAppTextMessage(
                `91${userPhoneNumber}`,
                whatsappMessageBody,
                organization_id,
                title,
                id,
                key,
              );
            }
          }
        } catch (error) {
          console.error('Error sending User WhatsApp notification:', error);
        }
        // await Notification.save(notifications);
      }
      insertINTOnotifications(this.afterInsert, userIDs);
    }
  }

  async afterUpdate(event: UpdateEvent<Invoice>) {
    const entityManager = getManager();
    const { id, invoiceNumber, status, grandTotal } = event?.entity;
    const invoiceDateFormat = new Date(event?.entity.invoiceDate).toLocaleDateString('en-GB');
    const invoiceDueDateFormat = new Date(event?.entity.invoiceDueDate).toLocaleDateString('en-GB');
    if (status === 'APPROVAL_PENDING' || status === 'PARTIALLY_PAID') {
      const getInvoiceQuery = `SELECT organization_id,client_id FROM invoice where id = ${id};`;
      let getInvoice = await entityManager.query(getInvoiceQuery);
      const orgid = getInvoice[0]?.organization_id;
      const clientId = getInvoice[0]?.client_id;
      const getClientQuery = `SELECT display_name FROM client where id = ${clientId};`;
      let getClient = await entityManager.query(getClientQuery);
      if (getClient) {
        const clientName = getClient[0]?.display_name;
        const getRoleQuery = `SELECT id FROM role where organization_id = ${orgid} and name = "Admin";`;
        let getRole = await entityManager.query(getRoleQuery);
        const role_id = getRole[0]?.id;
        const getuserQuery = `select id from user where organization_id=${orgid} and role_id = ${role_id}`;
        let getuser = await entityManager.query(getuserQuery);
        const userIDs: User[] = getuser.map((row) => row.id);
        const useridtwo = event.entity['userId'];
        if (useridtwo) {
          const getuserTwoQuery = `SELECT full_name FROM user where id = ${useridtwo};`;
          let getuserTwo = await entityManager.query(getuserTwoQuery);
          const userName = getuserTwo[0]?.full_name;

          async function insertINTOnotifications(event: any, users: Array<User>) {
            let notifications = [];
            let title = 'Invoice Edited';
            let body = `A Invoice "<strong>${invoiceNumber}</strong>" for <strong>${clientName}</strong> has been Edited.`;
            insertINTOnotification(title, body, users, orgid);
            const organization = await Organization.findOne({ id: orgid });

            const addressParts = [
              organization.buildingNo || '',
              organization.floorNumber || '',
              organization.buildingName || '',
              organization.street || '',
              organization.location || '',
              organization.city || '',
              organization.district || '',
              organization.state || '',
            ].filter((part) => part && part.trim() !== '');
            const pincode =
              organization.pincode && organization.pincode.trim() !== ''
                ? ` - ${organization.pincode}`
                : '';
            const address = addressParts.join(', ') + pincode;
            for (let a of users) {
              let getEmailQuery = `SELECT email,full_name FROM user where id = ${a};`;
              let getEmail = await entityManager.query(getEmailQuery);
              let mail = getEmail[0]?.email;
              let fullname = getEmail[0]?.full_name;
              let data = {
                clientName: clientName,
                invoiceNumber: invoiceNumber,
                invoiceDate: invoiceDateFormat,
                invoiceDueDate: invoiceDueDateFormat,
                username: fullname,
                invoiceAmount: grandTotal,
                userName: userName,
                adress: address,
                phoneNumber: organization?.mobileNumber,
                mail: organization?.email,
                legalName: organization?.tradeName || organization?.legalName,
              };
              let IData = {
                data: data,
                subject: `Invoice has been edited`,
                email: mail,
                filePath: 'invoice-updated',
              };
              // await sendMail(IData);
            }
            // await Notification.save(notifications);
          }
          insertINTOnotifications(this.afterUpdate, userIDs);
        }
      }
    }
    if (status === 'CANCELLED') {
      const getInvoiceQuery = `SELECT organization_id,client_id,client_group_id FROM invoice where id = ${id};`;
      let getInvoice = await entityManager.query(getInvoiceQuery);
      const orgid = getInvoice[0]?.organization_id;
      const clientId = getInvoice[0]?.client_id;
      const clientGroupId = getInvoice[0]?.client_group_id;
      const getClientQuery = `SELECT display_name FROM client where id = ${clientId};`;
      let getClient = await entityManager.query(getClientQuery);
      const getClientGroupQuery = `SELECT display_name FROM client_group where id = ${clientGroupId};`;
      let getClientGroup = await entityManager.query(getClientGroupQuery);
      const clientName = getClient[0]?.display_name;
      const clientGroupName = getClientGroup[0]?.display_name;
      if (clientName) {
        const clientName = getClient[0]?.display_name;
        const getRoleQuery = `SELECT id FROM role where organization_id = ${orgid} and name = "Admin";`;
        let getRole = await entityManager.query(getRoleQuery);
        const role_id = getRole[0]?.id;
        const getUserQuery = `select id from user where organization_id=${orgid} and role_id = ${role_id}`;
        let getUser = await entityManager.query(getUserQuery);
        const userIDs: User[] = getUser.map((row) => row.id);
        const useridtwo = event.entity['userId'];
        if (useridtwo) {
          const getUserTwoQuery = `SELECT full_name FROM user where id = ${useridtwo};`;
          let getUserTwo = await entityManager.query(getUserTwoQuery);
          const userName = getUserTwo[0]?.full_name;
          async function insertINTOnotifications(event: any, users: Array<User>) {
            let title = 'Invoice Cancelled';
            let body = `A Invoice "<strong>${invoiceNumber}</strong>" for <strong>${clientName}</strong> has been cancelled.`;
            const key = 'INVOICE_CANCELLED_PUSH';
            insertINTONotificationUpdate(title, body, users, orgid, key, id);
            const organization = await Organization.findOne({ id: orgid });

            const addressParts = [
              organization.buildingNo || '',
              organization.floorNumber || '',
              organization.buildingName || '',
              organization.street || '',
              organization.location || '',
              organization.city || '',
              organization.district || '',
              organization.state || '',
            ].filter((part) => part && part.trim() !== '');
            const pincode =
              organization.pincode && organization.pincode.trim() !== ''
                ? ` - ${organization.pincode}`
                : '';

            const address = addressParts.join(', ') + pincode;

            for (let a of users) {
              let getEmailQuery = `SELECT id, email,full_name FROM user where id = ${a};`;
              let getEmail = await entityManager.query(getEmailQuery);
              let mail = getEmail[0]?.email;
              let fullname = getEmail[0]?.full_name;
              let id = getEmail[0]?.id;
              let data = {
                clientName: clientName,
                invoiceNumber: invoiceNumber,
                invoiceDate: invoiceDateFormat,
                invoiceDueDate: invoiceDueDateFormat,
                username: fullname,
                invoiceAmount: grandTotal,
                userName: userName,
                userId: useridtwo,
                adress: address,
                phoneNumber: organization?.mobileNumber,
                mail: organization?.email,
                legalName: organization?.tradeName || organization?.legalName,
              };
              let IData = {
                id,
                key: 'INVOICE_CANCELLED_MAIL',
                data: data,
                subject: `Invoice has been cancelled`,
                email: mail,
                filePath: 'invoice-cancelled',
              };
              await sendnewMail(IData);
            }
          }
          insertINTOnotifications(this.afterUpdate, userIDs);
          const title = 'Invoice Cancelled';
          try {
            if (userIDs !== undefined) {
              const sessionValidation = await ViderWhatsappSessions.findOne({
                where: { userId: userIDs, status: 'ACTIVE' },
              });
              if (sessionValidation) {
                const adminUserDetails = await getUserDetails(userIDs);

                const {
                  full_name: userFullName,
                  mobile_number: userPhoneNumber,
                  id,
                  organization_id,
                } = adminUserDetails;
                const key = 'INVOICE_CANCELLED_WHATSAPP';
                const whatsappMessageBody = `
 Hi ${userFullName}
 An invoice ${invoiceNumber} for ${clientName} is cancelled by ${userName}

Invoice number: ${invoiceNumber}
Client name: ${clientName}
Invoice amount: ${grandTotal}
Invoice Due date: ${invoiceDueDateFormat}

We hope this helps!`;
                await sendWhatsAppTextMessage(
                  `91${userPhoneNumber}`,
                  whatsappMessageBody,
                  organization_id,
                  title,
                  id,
                  key,
                );
              }
            }
          } catch (error) {
            console.error('Error sending User WhatsApp notification:', error);
          }
        }
      }
      if (clientGroupName) {
        const clientName = getClientGroup[0]?.display_name;
        const getRoleQuery = `SELECT id FROM role where organization_id = ${orgid} and name = "Admin";`;
        let getRole = await entityManager.query(getRoleQuery);
        const useridtwo = event.entity['userId'];

        const getUserTwoQuery = `SELECT full_name FROM user where id = ${useridtwo};`;
        let getUserTwo = await entityManager.query(getUserTwoQuery);
        const userName = getUserTwo[0]?.full_name;

        const role_id = getRole[0]?.id;
        const getUserQuery = `select id from user where organization_id=${orgid} and role_id = ${role_id}`;
        let getUser = await entityManager.query(getUserQuery);
        const userIDs: User[] = getUser.map((row) => row.id);
        if (useridtwo) {
          async function insertINTOnotifications(event: any, users: Array<User>) {
            let title = 'Invoice Cancelled';
            let body = `A Invoice "<strong>${invoiceNumber}</strong>" for <strong>${clientName}</strong> has been cancelled.`;
            const key = 'INVOICE_CANCELLED_PUSH';
            insertINTONotificationUpdate(title, body, users, orgid, key);
          }
          insertINTOnotifications(this.afterUpdate, userIDs);
        }
      }
    }
  }
}
