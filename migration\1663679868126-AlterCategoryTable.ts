import { MigrationInterface, QueryRunner } from 'typeorm';

export class AlterCategoryTable1663679868126 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
        ALTER TABLE category
        ADD COLUMN from_admin boolean default false,
        ADD COLUMN admin_category_id int null,
        ADD COLUMN version int default 1 not null;
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}
