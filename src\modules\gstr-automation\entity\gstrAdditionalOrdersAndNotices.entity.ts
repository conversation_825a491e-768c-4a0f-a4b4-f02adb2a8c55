import Client from 'src/modules/clients/entity/client.entity';
import Storage from 'src/modules/storage/storage.entity';
import { BaseEntity, Column, Entity, ManyToOne, OneToMany, PrimaryGeneratedColumn } from 'typeorm';
import { CreatedType } from './noticeOrders.entity';

export enum StorageSystem {
  AMAZON = 'AMAZON',
  MICROSOFT = 'MICROSOFT',
  GOOGLE = 'GOOGLE',
  BHARATHCLOUD = 'BHARATHCLOUD',
}

@Entity()
class GstrAdditionalNoticeOrders extends BaseEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  gstIn: string;

  @Column()
  name: string;

  @Column()
  caseTypeId: number;

  @Column()
  arn: string;

  @Column()
  caseTypeCode: string;

  @Column()
  caseId: number;

  @Column()
  dateOfIssuance: string;

  @Column()
  description: string;

  @Column()
  caseTypeName: string;

  @Column()
  refId: string;

  @Column()
  status: string;

  @Column()
  itemId: number;

  @Column()
  caseFolderId: number;

  @Column()
  caseCfItemMapId: string;

  @Column({ type: 'enum', enum: StorageSystem, default: null })
  storageSystem: StorageSystem;

  @ManyToOne(() => Client, (client) => client.gstrAdditionall, { onDelete: 'SET NULL' })
  client: Client;

  @Column()
  organizationId: number;

  @Column()
  gstrCredentialsId: number;

  @Column('json')
  docDetails: object;

  @Column()
  fy: string;

  @Column('json')
  attatchments: object;

  @Column()
  caseFolderTypeName: String;

  @Column()
  categoryDate: string;

  @Column()
  section: string;

  @Column()
  personalHearning: string;

  @Column()
  categoryType: string;

  @Column()
  dueDate: string;

  @Column()
  refNum: string;

  @Column()
  nm: string;

  @Column()
  designation: string;

  @Column()
  venue: String;

  @Column()
  appealDemandAttachments: string;

  @Column()
  refStatus: string;

  @Column()
  caseStatus: string;

  @Column()
  summary: string;

  @Column({ type: 'enum', enum: CreatedType, default: null })
  createdType: CreatedType;

  @OneToMany(() => Storage, (storage) => storage.gstrAdditionalNoticeOrders, {
    cascade: true,
  })
  storage: Storage[];
}

export default GstrAdditionalNoticeOrders;
