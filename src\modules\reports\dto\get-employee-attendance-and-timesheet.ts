import { ArrayMinSize, IsArray, IsNotEmpty } from 'class-validator';

class getEmployeeAttendanceandTimesheetDto {
  @IsNotEmpty()
  @IsArray()
  @ArrayMinSize(1)
  users: Array<number>;

  @IsNotEmpty()
  fromDate: string;

  @IsNotEmpty()
  toDate: string;

  @IsNotEmpty()
  holidayscount: number;
  
  @IsNotEmpty()
  countOfSelectedDates: number;

  
}

export default getEmployeeAttendanceandTimesheetDto;