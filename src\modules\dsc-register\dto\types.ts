export interface FindQueryProps {
  category: string[];
  subCategory: string[] | null;
  monthAdded: string;
  offset: number;
  limit: number;
  search: string;
}

export enum CategoryEnum {
  INDIVIDUAL = 'individual',
  HUF = 'huf',
  PARTNERSHIP_FIRM = 'partnership_firm',
  LLP = 'llp',
  COMPNAY = 'company',
  TRUST = 'trust',
  SOCIETY = 'society',
  AOP = 'aop',
  BOI = 'boi',
  COROPORATIONS = 'corporations',
  GOVERNMENT = 'govermanment',
  ARTIFICIAL_JUDICIAL_PERSON = 'artificial_judicial_person',
}

export enum SubCategoryEnum {
  INDIAN = 'indian',
  FOREIGN = 'foreign',
  PRIVATE = 'private',
  PUBLIC = 'public',
  GOVERNMENT = 'government',
  OPC = 'opc',
  SEC_8 = 'sec_8',
  TRUST = 'trust',
  PRIVATE_DISCRETIONARY_TRUST = 'private_discretionary_trust',
  SOCIETY = 'society',
  COOPERATIVE_SOCIETY = 'cooperative_society',
  STATE = 'state',
  CENTRAL = 'central',
}
