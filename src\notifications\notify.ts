import { User } from 'src/modules/users/entities/user.entity';
import { createQueryBuilder } from 'typeorm';
import { Notification } from './notification.entity';
import { Token } from './token.entity';
import { messaging } from 'firebase';
import { IDscRegisterIssue, IDscRegisterReceive } from './interfaces';

export interface INotification {
  title: string;
  body: string;
}

export async function sendNotification(
  userIds: number[],
  notification: INotification,
) {
  if (!userIds || !userIds.length) return;

  try {
    let data = await createQueryBuilder(Token, 'token')
      .leftJoinAndSelect('token.user', 'user')
      .where('user.id IN (:...ids)', {
        ids: userIds,
      })
      .getMany();

    if (!data.length) return;

    let tokens = data.map((token: Token) => token.token);
    let users = data.map((token: Token) => token.user);

    let responses = await messaging.sendEachForMulticast({
      tokens,
      notification,
      android: {
        notification: {
          channelId: "default_channel", // must match what's created in Android code
        },
      },
      webpush: {
        notification: {
          title: notification.title,
          body: notification.body,
          icon: "https://vider.in/images/vider-favicon.png",
        },
        fcmOptions: {
          link: "https://vider.in",
        },
      },
    });


    await saveNotifications(users, notification);
  } catch (error) {
  }
}

async function saveNotifications(users: User[], notification: INotification) {
  try {
    let notifications = [];
    for (let user of users) {
      let newNotification = new Notification();
      newNotification.title = notification.title;
      newNotification.body = notification.body;
      newNotification.user = user;
      notifications.push(newNotification);
    }
    // await Notification.save(notifications);
  } catch (error) {
    console.log(error);
  }
}

async function DscRegisterIssue(data: IDscRegisterIssue) {
  const { userName, clientName, issuedTo, userIds, clientGroupName } = data;
  let notification = {
    title: `DSC Register Issued`,
    body: `${userName} has issued ${clientName ? clientName : clientGroupName} DSC to ${issuedTo}`,
  };

  await sendNotification(userIds, notification);
}

async function DscRegisterReceive(data: IDscRegisterReceive) {
  const { receivedBy, clientName, receivedFrom, userIds } = data;

  let notification = {
    title: `DSC Register Received`,
    body: `${receivedBy} has received ${clientName} DSC from ${receivedFrom}`,
  };

  await sendNotification(userIds, notification);
}

async function clientCreated(client: any) {
  let notification = {
    title: `${client?.displayName}'s Profile has been Created`,
    body: `Lets manage ${client?.displayName}'s information`,
  };
  client.clientManagers = client.clientManagers.map(item => item.id);
  

  await sendNotification([...client.clientManagers], notification);
}

async function clientUpdated(client: any) {
  let notification = {
    title: `Client Profile Update`,
    body: `${client?.displayName}'s Profile has been updated`,
  };
  await sendNotification([client.clientManager.id], notification);
}

async function userJoined({ userFullname, orgAdminId }) {
  let notification = {
    title: `New member has joined your organization`,
    body: `${userFullname} has joined your organization`,
  };
  // await sendNotification([orgAdminId], notification);
}

async function taskCreated({ taskData, userIds }) {
  let notification = {
    title: `Task created`,
    body: `Task "${taskData.name}" for the client  "${taskData.client.displayName}" has been created by ${taskData.user.fullName}`,
  };
  await sendNotification(userIds, notification);
}


async function leadCreated({leadName,userName,usersList}) {
  let notification = {
        title: 'Lead created',
        body: `New lead ${leadName} has been created by ${userName}`,
      };
  await sendNotification(usersList, notification);
}

async function taskCreatedPush({taskName,clientName,names,usersList}) {
  let notification = {
        title: 'Task Created',
        body: `${taskName} for ${clientName} has been created and assigned to ${names}`,
      };
  await sendNotification(usersList, notification);
}

async function createClientPush({created_name,fullName,adminsList}) {
  let notification = {
        title: 'Client Created',
        body: `${created_name} have created ${fullName} profile.`,
      };
  await sendNotification(adminsList, notification);
}
 




async function taskUpdated({ userFullname, taskName, userIds }) {
  let notification = {
    title: 'Task Updated',
    body: `${userFullname} has updated ${taskName}`,
  };
  await sendNotification(userIds, notification);
}

async function taskStatusUpdated({ userFullname, taskName, taskStatus, userIds }) {
  let notification = {
    title: 'Task Status Updated',
    body: `${userFullname} has moved ${taskName}" to ${taskStatus}`,
  };
  await sendNotification(userIds, notification);
}

async function taskDeleted({ userFullname, taskName, taskStatus, userIds }) {
  let notification = {
    title: 'Task Status Updated',
    body: `${userFullname} has moved ${taskName}" to ${taskStatus}`,
  };
  await sendNotification(userIds, notification);
}

export default {
  DscRegisterIssue,
  DscRegisterReceive,
  clientCreated,
  clientUpdated,
  userJoined,
  taskCreated,
  taskUpdated,
  taskStatusUpdated,
  taskDeleted,
  leadCreated,
  taskCreatedPush,
  createClientPush
};
