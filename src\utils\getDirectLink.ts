import { InternalServerErrorException } from "@nestjs/common";
import axios from "axios";
import { response } from "express";
import { auth } from "firebase-admin";
import AuthToken from "src/modules/ondrive-storage/auth-token.entity";
import { Organization } from "src/modules/organization/entities/organization.entity";
import CloudCredentials from "src/modules/storage/cloud-credentials.entity";


export async function getDirectLink(fileId: string, authId: number, id: number) {
    if (!fileId || !authId) return null;
    const token = await AuthToken.findOne({
        where: {
            organizationId: authId
        }
    });
    try {
        const getUrl = await getData(fileId, token);
        return getUrl;
    } catch (e) {
        if (e?.response?.data?.error.code === 'InvalidAuthenticationToken') {
            await refreshToken(token);
            const getUrl = await getData(fileId, token);
            return getUrl
        }
    }
};

export async function getBharahCloudLink(authId: number, file: string) {
    const cloudCredentials = await CloudCredentials.findOne({ where: { organizationId: authId } });
    return `${cloudCredentials.endPoint}/${cloudCredentials.bucketName}/${file}`
}



export async function refreshToken(token: AuthToken) {
    try {
        const data = new URLSearchParams({
            client_id: process.env.ONE_DRIVE_CLIENT_ID,
            client_secret: process.env.ONE_DRIVE_CLIENT_SECRET,
            scope: process.env.ONE_DRIVE_SCOPE,
            redirect_uri: `${process.env.WEBSITE_URL}/onedrive-auth`,
            grant_type: process.env.ONE_DRIVE_GRANT_TYPE_REFRESH,
            refresh_token: token.refreshToken,
        });

        let res = await axios({
            method: 'POST',
            url: process.env.ONE_DRIVE_AUTH_TOKEN_URL,
            headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
            data,
        });

        token.accessToken = res.data.access_token;
        token.refreshToken = res.data.refresh_token;
        await token.save();
        return token;
    } catch (err) {
        let error = err?.response?.data?.error;
        throw new InternalServerErrorException(error);
    }
};


export async function getData(fileId, token) {
    const url = `https://graph.microsoft.com/v1.0/me/drive/items/${fileId}`;
    const response = await axios.get(url, {
        headers: {
            'Authorization': `Bearer ${token.accessToken}`
        }
    });

    return response.data['@microsoft.graph.downloadUrl'];
};