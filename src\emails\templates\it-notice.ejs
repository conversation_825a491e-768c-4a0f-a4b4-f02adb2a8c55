<!DOCTYPE html>
<html>
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width,initial-scale=1" />
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Montserrat:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap"
      rel="stylesheet"
    />
  </head>

  <body style="margin: 0; padding: 0; font-family: Mulish">
    <div style="background-color: aliceblue" align="center">
      <table
        style="
          background: #ffffff;
          font-family: Mulish;
          color: #404040;
          padding: 40px;
          margin: auto;
          width: 100%;
        "
      >
        <!-- <tr>
          <td>
            <img
              src="https://jss-vider.s3.ap-south-1.amazonaws.com/headerejs.jpg"
              alt="ATOM Pro Logo"
              width="100%"
              height="35px"
            />
          </td>
        </tr> -->

        <tr>
          <td
            style="margin-top: 20px; height: auto; display: block; font-size: 15px; color: #182f53"
          >
            Dear <%= userName %>,
          </td>
        </tr>
        <tr>
          <td
            style="
              margin-top: 10px;
              height: auto;
              display: block;
              font-size: 15px;
              color: #182f53;
              margin-bottom: 20px;
            "
          >
            Daily Updates by ATOM Pro
          </td>
        </tr>

        <tr>
          <td
            style="
              background-color: #2c5c9c;
              color: white;
              border: none;
              font-size: 16px;
              border-radius: 10px;
              text-align: center;
              text-decoration: none;
            "
          >
            Income Tax | PAN (Excel)
          </td>
        </tr>

        <% if (fyaRecordsArray.length> 0) { %>
        <tr>
          <td
            style="
              background-color: #0e47a1;
              color: white;
              margin-top: 50px;
              padding: 10px;
              font-size: 16px;
              border-top-right-radius: 8px;
              border-top-left-radius: 8px;
              display: inline-block;
              margin-top: 50px;
            "
          >
            e-Proceeding (For Your Action)
          </td>
        </tr>
        <tr>
          <td>
            <table style="border-spacing: 0px; border: 0.923px solid #cfcfcf; margin-bottom: 100px">
              <thead>
                <tr>
                  <th
                    style="
                      border: 0.923px solid #cfcfcf;
                      background: #e4eeff;
                      padding: 5px;
                      color: #404040;
                      font-size: 11.082px;
                      font-style: normal;
                      font-weight: 500;
                      line-height: 150.523%;
                      letter-spacing: 0.665px;
                      width: 5vw;
                      font-weight: bold;
                    "
                  >
                    S.No
                  </th>
                  <th
                    style="
                      border: 0.923px solid #cfcfcf;
                      background: #e4eeff;
                      padding: 5px;
                      color: #404040;
                      font-size: 11.082px;
                      font-style: normal;
                      font-weight: 500;
                      line-height: 150.523%;
                      letter-spacing: 0.665px;
                      width: 20vw;
                      font-weight: bold;
                    "
                  >
                    Client Name
                  </th>
                  <th
                    style="
                      border: 0.923px solid #cfcfcf;
                      background: #e4eeff;
                      padding: 5px;
                      color: #404040;
                      font-size: 11.082px;
                      font-style: normal;
                      font-weight: 500;
                      line-height: 150.523%;
                      letter-spacing: 0.665px;
                      width: 20vw;
                      font-weight: bold;
                    "
                  >
                    PAN
                  </th>
                  <th
                    style="
                      border: 0.923px solid #cfcfcf;
                      background: #e4eeff;
                      padding: 5px;
                      color: #404040;
                      font-size: 11.082px;
                      font-style: normal;
                      font-weight: 500;
                      line-height: 150.523%;
                      letter-spacing: 0.665px;
                      width: 10vw;
                      font-weight: bold;
                    "
                  >
                    AY
                  </th>
                  <th
                    style="
                      border: 0.923px solid #cfcfcf;
                      background: #e4eeff;
                      padding: 5px;
                      color: #404040;
                      font-size: 11.082px;
                      font-style: normal;
                      font-weight: 500;
                      line-height: 150.523%;
                      letter-spacing: 0.665px;
                      width: 10vw;
                      font-weight: bold;
                    "
                  >
                    Proceeding Name
                  </th>
                    <th
                    style="
                      border: 0.923px solid #cfcfcf;
                      background: #e4eeff;
                      padding: 5px;
                      color: #404040;
                      font-size: 11.082px;
                      font-style: normal;
                      font-weight: 500;
                      line-height: 150.523%;
                      letter-spacing: 0.665px;
                      width: 8vw;
                      font-weight: bold;
                    "
                  >
                    Section
                  </th>
                  <th
                    style="
                      border: 0.923px solid #cfcfcf;
                      background: #e4eeff;
                      padding: 5px;
                      color: #404040;
                      font-size: 11.082px;
                      font-style: normal;
                      font-weight: 500;
                      line-height: 150.523%;
                      letter-spacing: 0.665px;
                      width: 10vw;
                      font-weight: bold;
                    "
                  >
                    DIN
                  </th>

                  <th
                    style="
                      border: 0.923px solid #cfcfcf;
                      background: #e4eeff;
                      padding: 5px;
                      color: #404040;
                      font-size: 11.082px;
                      font-style: normal;
                      font-weight: 500;
                      line-height: 150.523%;
                      letter-spacing: 0.665px;
                      width: 10vw;
                      font-weight: bold;
                    "
                  >
                    Issues On
                  </th>
                  <th
                    style="
                      border: 0.923px solid #cfcfcf;
                      background: #e4eeff;
                      padding: 5px;
                      color: #404040;
                      font-size: 11.082px;
                      font-style: normal;
                      font-weight: 500;
                      line-height: 150.523%;
                      letter-spacing: 0.665px;
                      width: 15vw;
                      font-weight: bold;
                    "
                  >
                    Response Due Date
                  </th>
                   <th
                    style="
                      border: 0.923px solid #cfcfcf;
                      background: #e4eeff;
                      padding: 5px;
                      color: #404040;
                      font-size: 11.082px;
                      font-style: normal;
                      font-weight: 500;
                      line-height: 150.523%;
                      letter-spacing: 0.665px;
                      width: 15vw;
                      font-weight: bold;
                    "
                  >
                    Response Submitted Date
                  </th>
                </tr>
              </thead>
              <tbody>
                 <% 
                  const MAX_ROWS = 20;
                  const total = fyaRecordsArray.length;
                  const showCount = Math.min(MAX_ROWS, total);
                %>

                <% for (let i = 0; i < showCount; i++) { 
                    let incrementedValue = i + 1;
                    const row = fyaRecordsArray[i];
                %>
                <tr>
                  <td
                    style="
                      text-align: center;
                      padding: 5px;
                      color: #5f5f5f;
                      font-size: 11.533px;
                      font-style: normal;
                      font-weight: 600;
                      line-height: 150.523%;
                      letter-spacing: 0.692px;
                      border: 0.923px solid #cfcfcf;
                    "
                  >
                    <%= incrementedValue %>
                  </td>
                  <td
                    style="
                      text-align: center;
                      padding: 5px;
                      color: #5f5f5f;
                      font-size: 11.533px;
                      font-style: normal;
                      font-weight: 600;
                      line-height: 150.523%;
                      letter-spacing: 0.692px;
                      border: 0.923px solid #cfcfcf;
                    "
                  >
                    <%= row.clientName %>
                  </td>
                  <td
                    style="
                      text-align: center;
                      padding: 5px;
                      color: #5f5f5f;
                      font-size: 11.533px;
                      font-style: normal;
                      font-weight: 600;
                      line-height: 150.523%;
                      letter-spacing: 0.692px;
                      border: 0.923px solid #cfcfcf;
                    "
                  >
                    <%= row.panNumber %>
                  </td>
                  <td
                    style="
                      text-align: center;
                      padding: 5px;
                      color: #5f5f5f;
                      font-size: 11.533px;
                      font-style: normal;
                      font-weight: 600;
                      line-height: 150.523%;
                      letter-spacing: 0.692px;
                      border: 0.923px solid #cfcfcf;
                    "
                  >
                    <%= row.ay %>
                  </td>
                  <td
                    style="
                      text-align: center;
                      padding: 5px;
                      color: #5f5f5f;
                      font-size: 11.533px;
                      font-style: normal;
                      font-weight: 600;
                      line-height: 150.523%;
                      letter-spacing: 0.692px;
                      border: 0.923px solid #cfcfcf;
                    "
                  >
                    <%= row.proceedingName %>
                  </td>
                  <td
                    style="
                      text-align: center;
                      padding: 5px;
                      color: #5f5f5f;
                      font-size: 11.533px;
                      font-style: normal;
                      font-weight: 600;
                      line-height: 150.523%;
                      letter-spacing: 0.692px;
                      border: 0.923px solid #cfcfcf;
                    "
                  >
                    <%= row.section %>
                  </td>
                  <td
                    style="
                      text-align: center;
                      padding: 5px;
                      color: #5f5f5f;
                      font-size: 11.533px;
                      font-style: normal;
                      font-weight: 600;
                      line-height: 150.523%;
                      letter-spacing: 0.692px;
                      border: 0.923px solid #cfcfcf;
                    "
                  >
                    <%= row.din %>
                  </td>

                  <td
                    style="
                      text-align: center;
                      padding: 5px;
                      color: #5f5f5f;
                      font-size: 11.533px;
                      font-style: normal;
                      font-weight: 600;
                      line-height: 150.523%;
                      letter-spacing: 0.692px;
                      border: 0.923px solid #cfcfcf;
                    "
                  >
                    <%= row.issuedOnDate %>
                  </td>
                  <td
                    style="
                      text-align: center;
                      padding: 5px;
                      color: #5f5f5f;
                      font-size: 11.533px;
                      font-style: normal;
                      font-weight: 600;
                      line-height: 150.523%;
                      letter-spacing: 0.692px;
                      border: 0.923px solid #cfcfcf;
                    "
                  >
                    <%= row.responseDueDate %>
                  </td>
                   <td
                    style="
                      text-align: center;
                      padding: 5px;
                      color: #5f5f5f;
                      font-size: 11.533px;
                      font-style: normal;
                      font-weight: 600;
                      line-height: 150.523%;
                      letter-spacing: 0.692px;
                      border: 0.923px solid #cfcfcf;
                    "
                  >
                    <%= row.responseSubmitted %>
                  </td>
                </tr>
                <% } %>
                 <% if (total > MAX_ROWS) { %>
                <tr>
                  <td colspan="10" style="text-align:right; padding:8px; border:0.923px solid #cfcfcf; background:#fafafa;">
                    Showing <%= showCount %> of <%= total %> records.
                    &nbsp; <a href="<%= websiteUrl %>/atom-pro/income-tax/temp-notices" target="_blank" style="color:#0e47a1; text-decoration:none;">View all notices</a>
                  </td>
                </tr>
                <% } %>
              </tbody>
            </table>
          </td>
        </tr>
        <% } %> <% if (fyiRecordsArray.length> 0) { %>
        <tr>
          <td
            style="
              background-color: #0e47a1;
              color: white;
              padding: 10px;
              font-size: 16px;
              border-top-right-radius: 8px;
              border-top-left-radius: 8px;
              display: inline-block;
            "
          >
            e-Proceeding (For Your Information)
          </td>
        </tr>
        <tr>
          <td>
            <table style="border-spacing: 0px; border: 0.923px solid #cfcfcf">
              <thead>
                <tr>
                  <th
                    style="
                      border: 0.923px solid #cfcfcf;
                      background: #e4eeff;
                      padding: 5px;
                      color: #404040;
                      font-size: 11.082px;
                      font-style: normal;
                      font-weight: 500;
                      line-height: 150.523%;
                      letter-spacing: 0.665px;
                      width: 3vw;
                    "
                  >
                    S.No
                  </th>
                  <th
                    style="
                      border: 0.923px solid #cfcfcf;
                      background: #e4eeff;
                      padding: 5px;
                      color: #404040;
                      font-size: 11.082px;
                      font-style: normal;
                      font-weight: 500;
                      line-height: 150.523%;
                      letter-spacing: 0.665px;
                      font-weight: bold;
                      width: 20vw;
                    "
                  >
                    Client Name
                  </th>
                  <th
                    style="
                      border: 0.923px solid #cfcfcf;
                      background: #e4eeff;
                      padding: 5px;
                      color: #404040;
                      font-size: 11.082px;
                      font-style: normal;
                      font-weight: 500;
                      line-height: 150.523%;
                      letter-spacing: 0.665px;
                      width: 10vw;
                      font-weight: bold;
                    "
                  >
                    PAN
                  </th>
                  <th
                    style="
                      border: 0.923px solid #cfcfcf;
                      background: #e4eeff;
                      padding: 5px;
                      color: #404040;
                      font-size: 11.082px;
                      font-style: normal;
                      font-weight: 500;
                      line-height: 150.523%;
                      letter-spacing: 0.665px;
                      width: 10vw;
                      font-weight: bold;
                    "
                  >
                    AY
                  </th>
                  <th
                    style="
                      border: 0.923px solid #cfcfcf;
                      background: #e4eeff;
                      padding: 5px;
                      color: #404040;
                      font-size: 11.082px;
                      font-style: normal;
                      font-weight: 500;
                      line-height: 150.523%;
                      letter-spacing: 0.665px;
                      width: 10vw;
                      font-weight: bold;
                    "
                  >
                    Proceeding Name
                  </th>
                  <th
                    style="
                      border: 0.923px solid #cfcfcf;
                      background: #e4eeff;
                      padding: 5px;
                      color: #404040;
                      font-size: 11.082px;
                      font-style: normal;
                      font-weight: 500;
                      line-height: 150.523%;
                      letter-spacing: 0.665px;
                      width: 8vw;
                      font-weight: bold;
                    "
                  >
                    Section
                  </th>
                  <th
                    style="
                      border: 0.923px solid #cfcfcf;
                      background: #e4eeff;
                      padding: 5px;
                      color: #404040;
                      font-size: 11.082px;
                      font-style: normal;
                      font-weight: 500;
                      line-height: 150.523%;
                      letter-spacing: 0.665px;
                      width: 10vw;
                      font-weight: bold;
                    "
                  >
                    DIN
                  </th>

                  <th
                    style="
                      border: 0.923px solid #cfcfcf;
                      background: #e4eeff;
                      padding: 5px;
                      color: #404040;
                      font-size: 11.082px;
                      font-style: normal;
                      font-weight: 500;
                      line-height: 150.523%;
                      letter-spacing: 0.665px;
                      width: 10vw;
                      font-weight: bold;
                    "
                  >
                    Issues On
                  </th>
                  <th
                    style="
                      border: 0.923px solid #cfcfcf;
                      background: #e4eeff;
                      padding: 5px;
                      color: #404040;
                      font-size: 11.082px;
                      font-style: normal;
                      font-weight: 500;
                      line-height: 150.523%;
                      letter-spacing: 0.665px;
                      width: 15vw;
                      font-weight: bold;
                    "
                  >
                    Response Due Date
                  </th>
                   <th
                    style="
                      border: 0.923px solid #cfcfcf;
                      background: #e4eeff;
                      padding: 5px;
                      color: #404040;
                      font-size: 11.082px;
                      font-style: normal;
                      font-weight: 500;
                      line-height: 150.523%;
                      letter-spacing: 0.665px;
                      width: 15vw;
                      font-weight: bold;
                    "
                  >
                    Response Submitted Date
                  </th>
                </tr>
              </thead>
              <tbody>
                 <% 
                  const MAX_ROWS = 20;
                  const total = fyiRecordsArray.length;
                  const showCount = Math.min(MAX_ROWS, total);
                %>

                <% for (let i = 0; i < showCount; i++) { 
                    let incrementedValue = i + 1;
                    const row = fyiRecordsArray[i];
                %>
                <tr>
                  <td
                    style="
                      text-align: center;
                      padding: 5px;
                      color: #5f5f5f;
                      font-size: 11.533px;
                      font-style: normal;
                      font-weight: 600;
                      line-height: 150.523%;
                      letter-spacing: 0.692px;
                      border: 0.923px solid #cfcfcf;
                    "
                  >
                    <%= incrementedValue %>
                  </td>
                  <td
                    style="
                      text-align: center;
                      padding: 5px;
                      color: #5f5f5f;
                      font-size: 11.533px;
                      font-style: normal;
                      font-weight: 600;
                      line-height: 150.523%;
                      letter-spacing: 0.692px;
                      border: 0.923px solid #cfcfcf;
                    "
                  >
                    <%= row.clientName %>
                  </td>
                  <td
                    style="
                      text-align: center;
                      padding: 5px;
                      color: #5f5f5f;
                      font-size: 11.533px;
                      font-style: normal;
                      font-weight: 600;
                      line-height: 150.523%;
                      letter-spacing: 0.692px;
                      border: 0.923px solid #cfcfcf;
                    "
                  >
                    <%= row.panNumber %>
                  </td>
                  <td
                    style="
                      text-align: center;
                      padding: 5px;
                      color: #5f5f5f;
                      font-size: 11.533px;
                      font-style: normal;
                      font-weight: 600;
                      line-height: 150.523%;
                      letter-spacing: 0.692px;
                      border: 0.923px solid #cfcfcf;
                    "
                  >
                    <%= row.ay %>
                  </td>
                  <td
                    style="
                      text-align: center;
                      padding: 5px;
                      color: #5f5f5f;
                      font-size: 11.533px;
                      font-style: normal;
                      font-weight: 600;
                      line-height: 150.523%;
                      letter-spacing: 0.692px;
                      border: 0.923px solid #cfcfcf;
                    "
                  >
                    <%= row.proceedingName %>
                  </td>

                    <td
                    style="
                      text-align: center;
                      padding: 5px;
                      color: #5f5f5f;
                      font-size: 11.533px;
                      font-style: normal;
                      font-weight: 600;
                      line-height: 150.523%;
                      letter-spacing: 0.692px;
                      border: 0.923px solid #cfcfcf;
                    "
                  >
                    <%= row.section %>
                  </td>
                  <td
                    style="
                      text-align: center;
                      padding: 5px;
                      color: #5f5f5f;
                      font-size: 11.533px;
                      font-style: normal;
                      font-weight: 600;
                      line-height: 150.523%;
                      letter-spacing: 0.692px;
                      border: 0.923px solid #cfcfcf;
                    "
                  >
                    <%= row.din %>
                  </td>

                  <td
                    style="
                      text-align: center;
                      padding: 10px;
                      color: #5f5f5f;
                      font-size: 11.533px;
                      font-style: normal;
                      font-weight: 600;
                      line-height: 150.523%;
                      letter-spacing: 0.692px;
                      border: 0.923px solid #cfcfcf;
                    "
                  >
                    <%= row.issuedOnDate %>
                  </td>
                  <td
                    style="
                      text-align: center;
                      padding: 10px;
                      color: #5f5f5f;
                      font-size: 11.533px;
                      font-style: normal;
                      font-weight: 600;
                      line-height: 150.523%;
                      letter-spacing: 0.692px;
                      border: 0.923px solid #cfcfcf;
                    "
                  >
                    <%= row.responseDueDate %>
                  </td>
                   <td
                    style="
                      text-align: center;
                      padding: 10px;
                      color: #5f5f5f;
                      font-size: 11.533px;
                      font-style: normal;
                      font-weight: 600;
                      line-height: 150.523%;
                      letter-spacing: 0.692px;
                      border: 0.923px solid #cfcfcf;
                    "
                  >
                    <%= row.responseSubmitted %>
                  </td>
                </tr>
                <% } %>
                 <% if (total > MAX_ROWS) { %>
                <tr>
                  <td colspan="10" style="text-align:right; padding:8px; border:0.923px solid #cfcfcf; background:#fafafa;">
                    Showing <%= showCount %> of <%= total %> records.
                    &nbsp; <a href="<%= websiteUrl %>/atom-pro/income-tax/temp-notices" target="_blank" style="color:#0e47a1; text-decoration:none;">View all notices</a>
                  </td>
                </tr>
                <% } %>
              </tbody>
            </table>
          </td>
        </tr>
        <% } %>

        <tr>
          <td align="left">
            <p style="font-size: 14px">
              Please log in to your account on our platform as soon as possible to review the
              notices. It is crucial to address these promptly to ensure compliance and avoid any
              potential penalties or disruptions to your business operations.
            </p>
          </td>
        </tr>
        <tr>
          <td style="font-family: Mulish; font-size: 12px; color: maroon">
            <strong>Note:</strong> This is an automated email. Please do not reply, as this mailbox
            is not monitored.
          </td>
        </tr>
            <tr>
        <td>
          <p style="margin-top: 3px; font-size: 17px">

            <span style="font-weight: bold; color: #aa1adc;">Disclaimer:</span>
            Please be aware that the preceding message is an automatically generated email originating from our system,
            and the associated mailbox is not actively monitored for incoming replies. Therefore, we kindly request that
            you refrain from attempting to respond directly to this notification. For any inquiries or assistance,
            please contact us via the mobile number or email address provided below. Thank you for your understanding.
        </td>
      </tr>

       
      <tr>
        <td>
          <hr style="border: #1C34FF 3px solid;" />
        </td>
      </tr>
    <tr>
        <td align="center">
          <h2><%= legalName %></h2>
         
        </td>
      </tr>
    <tr>  
        <td align="center">
          <table style="width: 210px">
      
        <tr>
          <td>
            <p style="margin-top: 3px; font-size: 17px">
  
              <span style="font-weight: bold">Address:</span>
              <%= adress %></td>
        </tr>
        <td align="center">
          <table style="width: 700px;">
            <tr align="center">
              <td align="center">
                <p style="margin-top: 3px; font-size: 17px">
                  
                </p>
              </td>
            </tr>
            <tr>
              <td align="center">
                <table style="margin: auto;">
                  <tr>
                    <td align="left" style="padding-right: 5px;">
                      <p style="margin-top: 3px; font-size: 17px;"><strong>Contact:</strong> <%=phoneNumber %></p>
                    </td>
                    <td align="right" style="padding-right: 10px;">
                      <p style="margin-top: 3px; font-size: 17px;">
                        |<strong> Email:</strong> <%=mail %></p>
                    </td>
                  </tr>
                </table>
              </td>
            </tr>
            <!-- <tr>
              <td align="center">
                <h2 style="margin: 0 auto;">Powered By: <img style="height: 30px;" src="https://jss-vider.s3.ap-south-1.amazonaws.com/vider_Logo.jpg" alt="" /></h2>
              </td>
            </tr>  -->
          </table>
        </td>
      </tr>
    </table>
  </div>
</body>

</html>