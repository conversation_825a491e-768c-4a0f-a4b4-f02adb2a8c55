import {
  Connection,
  EntitySubscriberInterface,
  EventSubscriber,
  UpdateEvent,
  getManager,
} from 'typeorm';
import { User } from 'src/modules/users/entities/user.entity';

@EventSubscriber()
export class UserStatusSubscriber implements EntitySubscriberInterface<User> {

  listenTo() {
    return User;
  }

  userBeforeStatus = '';
  async beforeUpdate(event: UpdateEvent<User>) {
  }

  async afterUpdate(event: UpdateEvent<User>) {
  }
}
