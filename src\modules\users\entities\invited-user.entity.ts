import { Organization } from 'src/modules/organization/entities/organization.entity';
import {
  BaseEntity,
  Column,
  CreateDateColumn,
  Entity,
  ManyToOne,
  PrimaryGeneratedColumn,
} from 'typeorm';
import { User } from './user.entity';

export enum InvitedUserStatus {
  PENDING = 'PENDING',
  CANCELLED = 'CANCELLED',
  JOINED = 'JOINED',
}

@Entity()
export class InvitedUser extends BaseEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  fullName: string;

  @Column()
  mobileNumber: string;

  @Column({ unique: true })
  email: string;

  @Column()
  role: number;

  @CreateDateColumn()
  createdAt: string;

  @ManyToOne(() => Organization, (organization) => organization.users, {
    eager: true,
  })
  organization: Organization;


  @ManyToOne(() => User, (user) => user.invitedUser, {
    eager: true,
  })
  manager: User

  @Column({ type: 'enum', enum: InvitedUserStatus, default: InvitedUserStatus.PENDING })
  status: InvitedUserStatus;

  @Column({ nullable: true })
  countryCode: string;
}
