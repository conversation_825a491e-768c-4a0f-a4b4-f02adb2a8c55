import { Injectable, BadRequestException } from '@nestjs/common';
import { S3 } from 'aws-sdk';

@Injectable()
export class AwsService {
  async upload(buffer: Buffer, key: string, contentType = '') {
    const bucketS3 = process.env.AWS_BUCKET_NAME;
    return await this.uploadS3(buffer, bucketS3, key, contentType);
  }

  async get(key: string) {
    try {
      const bucketS3 = process.env.AWS_BUCKET_NAME;
      return await this.getFileFromS3(bucketS3, key);
    } catch (err) {
      throw new BadRequestException(err);
    }
  }

  async getFileFromS3(bucket, key) {
    const s3 = this.getS3();
    const params = {
      Bucket: bucket,
      Key: key,
    };
    return new Promise((resolve, reject) => {
      s3.getObject(params, (err, data) => {
        if (err) {
          console.error(err);
          reject(err.message);
        }
        resolve(data);
      });
    });
  }

  async uploadS3(file: Buffer, bucket, key, contentType) {
    const s3 = this.getS3();
    const params = {
      Bucket: bucket,
      Key: key,
      Body: file,
      ContentType: contentType,
    };
    return new Promise((resolve, reject) => {
      s3.upload(params, (err, data) => {
        if (err) {
          console.error(err);
          reject(err.message);
        }
        resolve(data);
      });
    });
  }

  async deleteFile(key: string) {
    if (!key) return null;
    try {
      const s3 = this.getS3();
      const params = {
        Bucket: process.env.AWS_BUCKET_NAME,
        Key: key,
      };
      return new Promise((resolve, reject) => {
        s3.deleteObject(params, (err, data) => {
          if (err) {
            console.error(err);
            reject(err.message);
          }
          resolve(data);
        });
      });
    } catch (err) {
      console.error(err);
    }
  }

  getS3() {
    return new S3({
      accessKeyId: process.env.AWS_ACCESS_KEY_ID,
      secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
      region: 'ap-south-1',
    });
  }
}
