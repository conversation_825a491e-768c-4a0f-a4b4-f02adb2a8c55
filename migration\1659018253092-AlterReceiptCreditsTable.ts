import { MigrationInterface, QueryRunner } from 'typeorm';

export class AlterReceiptCreditsTable1659018253092
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
     ALTER TABLE receipt_credit
        DROP FOREIGN KEY receipt_credit_ibfk_2,
        DROP FOREIGN KEY receipt_credit_ibfk_3,
        DROP INDEX billing_entity_id,
        DROP INDEX organization_id,
        DROP COLUMN organization_id,
        DROP COLUMN billing_entity_id
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}
