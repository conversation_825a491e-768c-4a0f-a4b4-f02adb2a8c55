import { Controller, Get, UseGuards } from "@nestjs/common";
import { GstrCronJobService } from "../service/gstr-cron-job-service";
import { CronAuthGuard } from "src/cron-auth/api-key-auth.guard";

@Controller('gst-cron')

export class GstCronController{
    constructor(private service:GstrCronJobService){}
    @UseGuards(CronAuthGuard)
    @Get('/notices')
    async getAdditionalNoticeOrdersRecords(){
        return this.service.getAdditionalNoticeOrdersRecords();
    }

}