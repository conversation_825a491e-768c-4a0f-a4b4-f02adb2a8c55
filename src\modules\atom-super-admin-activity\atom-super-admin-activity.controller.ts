import {
  BadRequestException,
  Body,
  Controller,
  Delete,
  Get,
  Param,
  ParseIntPipe,
  Post,
  Put,
  Query,
  Req,
  Request,
  UseGuards,
} from '@nestjs/common';
import { AtomSuperAdminActivityService } from './atom-super-admin-activity.service';
import { JwtAuthGuard } from '../users/jwt/jwt-auth.guard';
import CreateAtomSuperAdminActivityDto from './dto/atom-super-admin-activity.dto';
import { Cron } from '@nestjs/schedule';

@Controller('atomSuperAdminActivity')
export class AtomSuperAdminActivityController {
  constructor(private readonly atomSuperAdminActivityService: AtomSuperAdminActivityService) {}

  @Get('/prodCheck')
  prodCheck(@Body() body: any) {
    // return this.atomSuperAdminActivityService.prodCheck(body);
  }

  @UseGuards(JwtAuthGuard)
  @Post('/')
  create(@Body() body: CreateAtomSuperAdminActivityDto, @Req() req: any) {
    const { userId } = req.user;
    return this.atomSuperAdminActivityService.create(userId, body);
  }

  @UseGuards(JwtAuthGuard)
  @Put('/:id')
  update(
    @Req() req: any,
    @Body() body: CreateAtomSuperAdminActivityDto,
    @Param('id', ParseIntPipe) id: number,
  ) {
    const { userId } = req.user;
    return this.atomSuperAdminActivityService.update(userId, id, body);
  }
}
