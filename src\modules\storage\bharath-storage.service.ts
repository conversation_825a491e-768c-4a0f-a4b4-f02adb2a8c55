import { BadRequestException, ConflictException, forwardRef, Inject, Injectable, InternalServerErrorException, UnprocessableEntityException } from "@nestjs/common";
import { IExisting, IUpload, StorageService } from "./storage.service";
import { User } from "../users/entities/user.entity";
import Storage, { StorageSystem, StorageType } from "./storage.entity";
import { v4 as uuidv4 } from 'uuid';
import { createQueryBuilder } from "typeorm";
import CollectData from "../collect-data/collect-data.entity";
import { Organization } from "../organization/entities/organization.entity";
import Client from "../clients/entity/client.entity";
import { BharathCloudService } from "./bharath-upload.service";
import Activity, { ActivityType } from "../activity/activity.entity";
import { Event_Actions } from "src/event-listeners/actions";
import Task from "../tasks/entity/task.entity";
import { AttachmentsService } from "../tasks/services/attachments.service";
import * as moment from 'moment';
import { getName } from "src/utils/FilterSpecialChars";
import { Cron, CronExpression } from "@nestjs/schedule";
import ClientGroup from "../client-group/client-group.entity";
import CloudCredentials from "./cloud-credentials.entity";
import { EventEmitter2 } from "@nestjs/event-emitter";

@Injectable()
export class BharathStorageService {
    constructor(private bharathService: BharathCloudService,
        @Inject((forwardRef(() => AttachmentsService)))
        private attachmentsService: AttachmentsService,
        private eventEmitter: EventEmitter2,
        // private storageService: StorageService


    ) { }


    async uploadFile(args: IUpload) {
        const { file, body, userId } = args;
        const { buffer, mimetype } = file;
        const user = await User.findOne({
            where: { id: userId },
            relations: ['organization'],
        });


        let storage = new Storage();
        storage.fileType = file.mimetype;
        storage.fileSize = file.size;
        storage.name = file.originalname;
        storage.type = StorageType.FILE;
        storage.uid = uuidv4();
        storage.storageSystem = StorageSystem.BHARATHCLOUD;
        storage.authId = user.organization.id;
        storage.user = user;

        let existingFile = await this.existing({
            name: file.originalname,
            parent: body.folderId,
            type: body.type,
            clientId: body.clientId,
            clientGroupId: body.clientGroup,
            orgId: user.organization.id,
            roomId: body.roomId,
        });
        if (existingFile) {
            throw new ConflictException('File with this name already exists');
        }

        let key: string;
        let upload: any;
        let atomFolder: Storage;
        let clientFolder: Storage;
        let dispalayNameFolder: Storage;


        try {

            if (body.type === 'client') {
                let client = await Client.findOne({ where: { id: body.clientId }, relations: ['organization'] });
                if (body.folderId) {
                    const folder = await Storage.findOne({ where: { uid: body.folderId } });
                    key = `${folder.filePath}/${file.originalname}`;
                } else {

                    dispalayNameFolder = await Storage.findOne({
                        where: {
                            name: client.displayName,
                            organization: client.organization.id,
                            show: false
                        }
                    });

                    if (!dispalayNameFolder) {
                        atomFolder = await Storage.findOne({
                            where: {
                                name: "Atom",
                                organization: client.organization.id,
                                show: false
                            }
                        });
                        if (!atomFolder) {
                            const atom = 'Atom';
                            // const folderData = await this.createOneDriveFolder(userId, "Atom");
                            // const folderData = await this.bharathService.createS3Folder(process.env.BHARATH_BUCKET_NAME, atom);
                            atomFolder = new Storage();
                            atomFolder.name = atom;
                            atomFolder.organization = user.organization;
                            atomFolder.type = StorageType.FOLDER;
                            atomFolder.uid = uuidv4();
                            // atomFolder.fileId = folderData.id;
                            atomFolder.filePath = atom;
                            atomFolder.show = false;
                            atomFolder.storageSystem = StorageSystem.BHARATHCLOUD;
                            atomFolder.authId = user.organization.id;
                            await atomFolder.save();
                        };
                        clientFolder = await Storage.findOne({
                            where: {
                                name: "Clients",
                                organization: user.organization.id,
                                show: false
                            }
                        });
                        if (!clientFolder) {
                            // const folderData = await this.createOneDriveFolder(userId, "Clients", atomFolder.fileId);
                            const clients = 'Clients';
                            // const folderData = await this.bharathService.createS3Folder(process.env.BHARATH_BUCKET_NAME, `${atomFolder.filePath}/${clients}`);
                            clientFolder = new Storage();
                            clientFolder.name = clients;
                            clientFolder.organization = user.organization;
                            clientFolder.type = StorageType.FOLDER;
                            clientFolder.uid = uuidv4();
                            // clientFolder.fileId = folderData.id;
                            clientFolder.filePath = `${atomFolder.filePath}/${clients}`
                            clientFolder.show = false;
                            clientFolder.storageSystem = StorageSystem.BHARATHCLOUD;
                            clientFolder.authId = user.organization.id;
                            clientFolder.parent = atomFolder;
                            await clientFolder.save();
                        };
                        dispalayNameFolder = await Storage.findOne({
                            where: {
                                name: client.displayName,
                                organization: user.organization.id,
                                show: false
                            }
                        });
                        if (!dispalayNameFolder) {
                            const name = getName(client.displayName);
                            // const folderData = await this.createOneDriveFolder(userId, name, clientFolder?.fileId);
                            // const folderData = await this.bharathService.createS3Folder(process.env.BHARATH_BUCKET_NAME, `${clientFolder.filePath}/${name}`);
                            dispalayNameFolder = new Storage();
                            dispalayNameFolder.name = name;
                            dispalayNameFolder.organization = user.organization;
                            dispalayNameFolder.type = StorageType.FOLDER;
                            dispalayNameFolder.uid = uuidv4();
                            // dispalayNameFolder.fileId = folderData.id;
                            dispalayNameFolder.filePath = `${clientFolder.filePath}/${name}`;
                            dispalayNameFolder.show = false;
                            dispalayNameFolder.storageSystem = StorageSystem.BHARATHCLOUD;
                            dispalayNameFolder.authId = user.organization.id;
                            dispalayNameFolder.parent = clientFolder;
                            await dispalayNameFolder.save();
                        };

                    };
                    key = `${dispalayNameFolder.filePath}/${file.originalname}`;
                };
                upload = await this.bharathService.upload(user.organization.id, buffer, key, mimetype);
                storage.client = client;
            };
            if (body.type === 'organization') {
                let orgFolder: Storage;
                if (body?.folderId) {
                    const parentFolder = await Storage.findOne({ where: { uid: body.folderId } });
                    key = `${parentFolder.filePath}/${file.originalname}`;
                } else {
                    atomFolder = await Storage.findOne({
                        where: {
                            name: "Atom",
                            organization: user.organization.id,
                            show: false
                        }
                    });
                    if (!atomFolder) {
                        const atom = "Atom";
                        // const folderData = await this.bharathService.createS3Folder(process.env.BHARATH_BUCKET_NAME, atom);
                        atomFolder = new Storage();
                        atomFolder.name = atom;
                        atomFolder.organization = user.organization;
                        atomFolder.type = StorageType.FOLDER;
                        atomFolder.uid = uuidv4();
                        atomFolder.authId = user.organization.id;
                        // atomFolder.fileId = folderData.id;
                        atomFolder.filePath = atom;
                        atomFolder.show = false;
                        atomFolder.storageSystem = StorageSystem.BHARATHCLOUD;
                        atomFolder.authId = user.organization.id;
                        await atomFolder.save();
                    };

                    orgFolder = await Storage.findOne({
                        where: {
                            name: "Organization Storage",
                            organization: user.organization.id,
                            show: false
                        }
                    });
                    if (!orgFolder) {
                        const orgStorage = 'Organization Storage';
                        // const folderData = await this.bharathService.createS3Folder(process.env.BHARATH_BUCKET_NAME, `${atomFolder.filePath}/${orgStorage}`);
                        orgFolder = new Storage();
                        orgFolder.name = "Organization Storage";
                        orgFolder.organization = user.organization;
                        orgFolder.type = StorageType.FOLDER;
                        orgFolder.uid = uuidv4();
                        // orgFolder.fileId = folder.id;
                        orgFolder.authId = user.organization.id;
                        orgFolder.filePath = `${atomFolder.filePath}/${orgStorage}`;
                        orgFolder.show = false;
                        orgFolder.storageSystem = StorageSystem.BHARATHCLOUD;
                        orgFolder.authId = user.organization.id;
                        orgFolder.parent = atomFolder;
                        await orgFolder.save();
                    };
                    key = `${orgFolder.filePath}/${file.originalname}`;
                };
                upload = await this.bharathService.upload(user.organization.id, buffer, key, mimetype);
                storage.organization = user.organization;
            };

        } catch (err) {
            throw new BadRequestException(err);
        };
        storage.file = upload.Key;
        // storage.webUrl = upload.webUrl;
        // storage.downloadUrl = upload["@microsoft.graph.downloadUrl"];
        // storage.fileId = upload.id;
        storage.authId = user.organization.id;

        if (body.folderId) {
            let folder = await Storage.findOne({ where: { uid: body.folderId } });
            storage.parent = folder;
        };
        await storage.save();
        return storage;
    };


    async existing(props: IExisting) {
        const { name, parent, type, orgId, clientId, roomId } = props;

        let existing = createQueryBuilder(Storage, 'storage');

        let where = `storage.name = :name`;

        if (type === 'client') {
            existing.leftJoin('storage.client', 'client');
            where += ` and client.id = :clientId`;
        }

        if (type === 'organization') {
            existing.leftJoin('storage.organization', 'organization');
            where += ` and organization.id = :orgId`;
        }
        if (type === 'chat') {
            existing.leftJoin('storage.room', 'room');
            where += ` and room.id = :roomId`;
        }

        if (parent) {
            existing.leftJoin('storage.parent', 'parent');
            where += ` and parent.uid = :parent`;
        }

        if (!parent) {
            where += ` and storage.parent is null`;
        }

        existing.where(`(${where})`, { name, parent, clientId, orgId, roomId });

        let result = await existing.getOne();
        return Boolean(result);
    };

    async getOrgStorage(userId: number) {
        const user = await User.findOne(userId, { relations: ['organization'] });
        let orgStorage = await createQueryBuilder(Storage, 'storage')
            .select('SUM(storage.fileSize) as totalStorage')
            .leftJoin('storage.organization', 'organization')
            .where('organization.id = :orgId', { orgId: user.organization.id })
            .getRawOne();

        let clientStorage = await createQueryBuilder(Storage, 'storage')
            .select('SUM(storage.fileSize) as totalStorage')
            .leftJoin('storage.client', 'client')
            .leftJoin('client.organization', 'organization')
            .where('organization.id = :orgId', { orgId: user.organization.id })
            .getRawOne();

        let unexpriedLinks = await createQueryBuilder(CollectData, 'collectData')
            .leftJoin('collectData.user', 'user')
            .leftJoin('user.organization', 'organization')
            .where('organization.id = :orgId', { orgId: user.organization.id })
            .andWhere('collectData.active=:active', { active: true })
            .getCount();

        let organization = await Organization.findOne({ where: { id: user.organization.id } });
        const orgLimit = organization.config?.['stroagelimit'] || 53687091200;

        return {
            storageLimit: orgLimit,
            totalStorageUsed:
                +clientStorage?.totalStorage + +orgStorage?.totalStorage + unexpriedLinks * 209715200 || 0,
            freeSpace:
                orgLimit -
                (+clientStorage?.totalStorage + +orgStorage?.totalStorage + unexpriedLinks * 209715200) ??
                0,
            unExpriedLinks: unexpriedLinks * 209715200,
        };
    };

    async addAttachments(taskId: number, files: Express.Multer.File[], userId: number) {
        try {
            let task = await Task.findOne({
                where: { id: taskId },
                relations: ['client', 'category', 'subCategory', 'user', 'organization'],
            });

            let user = await User.findOne({
                where: { id: userId },
                relations: ['organization'],
            });




            let taskStorage = await this.attachmentsService.existingClientTaskStorage(task, StorageSystem.BHARATHCLOUD);
            let taskAttachments: Storage[] = [];
            let errors: string[] = [];

            for (let file of files) {
                const { buffer, mimetype, originalname, size } = file;
                const existingAttachement = await Storage.find({ where: { parent: taskStorage, name: originalname } })
                if (existingAttachement?.length) {
                    errors.push(`File name ${originalname} already exists`);
                    continue;
                }
                let key = `${taskStorage.filePath}/${file.originalname}`;
                let upload: any;
                try {
                    upload = await this.bharathService.upload(user.organization.id, buffer, key, mimetype);
                } catch (err) {
                    console.error(err);

                };
                let storage = new Storage();
                storage.name = originalname;
                storage.file = upload.Key;
                storage.authId = user.organization.id;
                storage.task = task;
                storage.fileSize = size;
                storage.fileType = mimetype;
                storage.client = task.client;
                storage.type = StorageType.FILE;
                storage.parent = taskStorage;
                storage.filePath = upload.Key;
                storage.storageSystem = StorageSystem.BHARATHCLOUD;
                storage.user = user;
                taskAttachments.push(storage);
            };

            await Storage.save(taskAttachments);

            if (errors?.length) {
                return { errors }
            } else {
                return {
                    success: true,
                };
            };

        } catch (err) {
            console.log(err);
            throw new InternalServerErrorException(err);
        }
    };

    async attachementsUpload(args: IUpload) {
        const { file, body, userId, } = args;
        const { stageid } = body;
        const { buffer, mimetype } = file;
        const user = await User.findOne({
            where: { id: userId },
            relations: ['organization'],
        });







        let existingFile = await this.existing({
            name: file.originalname,
            parent: body.folderId,
            type: body.type,
            clientId: body.clientId,
            clientGroupId: null,
            orgId: user.organization.id,
            roomId: body.roomId,
        });

        if (body.type !== 'chat' && existingFile) {
            throw new ConflictException('File with this name already exists');
        }

        let key: string;
        let upload: any;

        try {
            if (body.type === 'client') {
                let client = await Client.findOne({ where: { id: body.clientId }, relations: ['organization'] });
                let dispalayNameFolder: Storage;
                let atomFolder: Storage;
                let clientFolder: Storage;
                dispalayNameFolder = await Storage.findOne({
                    where: {
                        name: client.displayName,
                        organization: client.organization.id,
                        show: false
                    }
                });

                if (!dispalayNameFolder) {
                    atomFolder = await Storage.findOne({
                        where: {
                            name: "Atom",
                            organization: client.organization.id,
                            show: false
                        }
                    });
                    if (!atomFolder) {
                        const atom = 'Atom';
                        atomFolder = new Storage();
                        atomFolder.name = atom
                        atomFolder.organization = user.organization;
                        atomFolder.type = StorageType.FOLDER;
                        atomFolder.uid = uuidv4();
                        atomFolder.filePath = atom;
                        atomFolder.show = false;
                        atomFolder.storageSystem = StorageSystem.BHARATHCLOUD;
                        atomFolder.authId = user.organization.id;
                        await atomFolder.save();
                    };
                    clientFolder = await Storage.findOne({
                        where: {
                            name: "Clients",
                            organization: user.organization.id,
                            show: false
                        }
                    });
                    if (!clientFolder) {
                        const clients = 'Clients';
                        clientFolder = new Storage();
                        clientFolder.name = clients;
                        clientFolder.organization = user.organization;
                        clientFolder.type = StorageType.FOLDER;
                        clientFolder.uid = uuidv4();
                        clientFolder.filePath = `${atomFolder.filePath}/${clients}`
                        clientFolder.show = false;
                        clientFolder.storageSystem = StorageSystem.BHARATHCLOUD;
                        clientFolder.authId = user.organization.id;
                        clientFolder.parent = atomFolder;
                        await clientFolder.save();
                    };
                    dispalayNameFolder = await Storage.findOne({
                        where: {
                            name: client.displayName,
                            organization: user.organization.id,
                            show: false
                        }
                    });
                    if (!dispalayNameFolder) {
                        const displayName = getName(client.displayName)
                        // const folderData = await this.createOneDriveFolder(userId, client.displayName, clientFolder?.fileId);
                        dispalayNameFolder = new Storage();
                        dispalayNameFolder.name = displayName;
                        dispalayNameFolder.organization = user.organization;
                        dispalayNameFolder.type = StorageType.FOLDER;
                        dispalayNameFolder.uid = uuidv4();
                        dispalayNameFolder.filePath = `${clientFolder.filePath}/${displayName}`
                        dispalayNameFolder.show = false;
                        dispalayNameFolder.storageSystem = StorageSystem.BHARATHCLOUD;
                        dispalayNameFolder.authId = user.organization.id;
                        dispalayNameFolder.parent = clientFolder;
                        await dispalayNameFolder.save();
                    };

                };
                key = `${dispalayNameFolder.filePath}/${file.originalname}`

                upload = await this.bharathService.upload(user.organization.id, buffer, key, mimetype);

            }

            if (body.type === 'clientGroup') {
                let clientGroup = await ClientGroup.findOne({ where: { id: body.clientId }, relations: ['organization'] });
                let dispalayNameFolder: Storage;
                let atomFolder: Storage;
                let clientFolder: Storage;
                dispalayNameFolder = await Storage.findOne({
                    where: {
                        name: clientGroup.displayName,
                        organization: clientGroup.organization.id,
                        show: false
                    }
                });

                if (!dispalayNameFolder) {
                    atomFolder = await Storage.findOne({
                        where: {
                            name: "Atom",
                            organization: clientGroup.organization.id,
                            show: false
                        }
                    });
                    if (!atomFolder) {
                        const atom = 'Atom';
                        atomFolder = new Storage();
                        atomFolder.name = atom;
                        atomFolder.organization = user.organization;
                        atomFolder.type = StorageType.FOLDER;
                        atomFolder.uid = uuidv4();
                        atomFolder.filePath = atom;
                        atomFolder.show = false;
                        atomFolder.storageSystem = StorageSystem.BHARATHCLOUD;
                        atomFolder.authId = user.organization.id;
                        await atomFolder.save();
                    };
                    clientFolder = await Storage.findOne({
                        where: {
                            name: "Clients",
                            organization: user.organization.id,
                            show: false
                        }
                    });
                    if (!clientFolder) {
                        // const folderData = await this.createOneDriveFolder(userId, "Clients", atomFolder.fileId);
                        const clients = 'Clients';
                        clientFolder = new Storage();
                        clientFolder.name = clients;
                        clientFolder.organization = user.organization;
                        clientFolder.type = StorageType.FOLDER;
                        clientFolder.uid = uuidv4();
                        // clientFolder.fileId = folderData.id;
                        clientFolder.filePath = `${atomFolder.filePath}/${clients}`
                        clientFolder.show = false;
                        clientFolder.storageSystem = StorageSystem.BHARATHCLOUD;
                        clientFolder.authId = user.organization.id;
                        clientFolder.parent = atomFolder;
                        await clientFolder.save();
                    };
                    dispalayNameFolder = await Storage.findOne({
                        where: {
                            name: clientGroup.displayName,
                            organization: user.organization.id,
                            show: false
                        }
                    });
                    if (!dispalayNameFolder) {
                        // const folderData = await this.createOneDriveFolder(userId, clientGroup.displayName, clientFolder?.fileId);
                        const displayName = getName(clientGroup.displayName)
                        dispalayNameFolder = new Storage();
                        dispalayNameFolder.name = displayName;
                        dispalayNameFolder.organization = user.organization;
                        dispalayNameFolder.type = StorageType.FOLDER;
                        dispalayNameFolder.uid = uuidv4();
                        // dispalayNameFolder.fileId = folderData.id;
                        dispalayNameFolder.filePath = `${dispalayNameFolder.filePath}/${displayName}`
                        dispalayNameFolder.show = false;
                        dispalayNameFolder.storageSystem = StorageSystem.BHARATHCLOUD;
                        dispalayNameFolder.authId = user.organization.id;
                        dispalayNameFolder.parent = clientFolder;
                        await dispalayNameFolder.save();
                    };
                };
                key = `${dispalayNameFolder.filePath}/${file.originalname}`
                // key = `root:/Atom/Clients/${client.displayName}/${file.originalname}:`;
                upload = await this.bharathService.upload(user.organization.id, buffer, key, mimetype);
            }

            if (body.type === 'organization') {
                let atomFolder: Storage;
                let orgFolder: Storage;

                atomFolder = await Storage.findOne({
                    where: {
                        name: "Atom",
                        organization: user.organization.id,
                        show: false
                    }
                });
                if (!atomFolder) {
                    // const folderData = await this.createOneDriveFolder(userId, "Atom");
                    const atom = 'Atom';
                    atomFolder = new Storage();
                    atomFolder.name = atom;
                    atomFolder.organization = user.organization;
                    atomFolder.type = StorageType.FOLDER;
                    atomFolder.uid = uuidv4();
                    // atomFolder.fileId = folderData.id;
                    atomFolder.filePath = atom;
                    atomFolder.show = false;
                    atomFolder.storageSystem = StorageSystem.BHARATHCLOUD;
                    atomFolder.authId = user.organization.id;
                    await atomFolder.save();
                };
                orgFolder = await Storage.findOne({
                    where: {
                        name: "Organization Storage",
                        organization: user.organization.id,
                        show: false
                    }
                });
                if (!orgFolder) {
                    // const folder = await this.createOneDriveFolder(userId, "Organization Storage", atomFolder.fileId);
                    const orgStorage = "Organization Storage";
                    orgFolder = new Storage();
                    orgFolder.name = orgStorage;
                    orgFolder.organization = user.organization;
                    orgFolder.type = StorageType.FOLDER;
                    orgFolder.uid = uuidv4();
                    // orgFolder.fileId = folder.id;
                    orgFolder.filePath = `${atomFolder.filePath}/${orgStorage}`
                    orgFolder.show = false;
                    orgFolder.storageSystem = StorageSystem.BHARATHCLOUD;
                    orgFolder.authId = user.organization.id;
                    orgFolder.parent = atomFolder;
                    await orgFolder.save();
                };


                key = `${orgFolder.filePath}/${file.originalname}`;
                upload = await this.bharathService.upload(user.organization.id, buffer, key, mimetype);
            }

            if (body.type === 'chat') {
                key = `Atom/Organization Storage/chat-${body.roomId}-${moment().valueOf()}/${file.originalname}:`;
                upload = await this.bharathService.upload(user.organization.id, buffer, key, mimetype);
            };

        } catch (err) {
            throw new BadRequestException(err);
        }

        return {
            key,
            upload: upload.Key,
            fileSize: file.size,
            fileType: file.mimetype,
            clientId: body.clientId,
            name: file.originalname,
            authId: user.organization.id,
            storageSystem: StorageSystem.BHARATHCLOUD,
            file: upload.file,
            filePath: upload.key,
            stageid

        };
    };

    async getBharathCloudItems(userId: number, data: any) {
        const user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
        const cloudCredentials = await CloudCredentials.findOne({ where: { organizationId: user.organization.id } });
        if (!cloudCredentials) {
            throw new UnprocessableEntityException({ message: 'Add Bharath Cloud Credentials', code: 'NO_KEY' });
        }
        const { endPoint, bucketName } = cloudCredentials;

        const allObjects = await this.bharathService.listObjects(user.organization.id, data.id);
        const files = allObjects.Contents;
        const folders = allObjects.CommonPrefixes
        return { files, folders, endPoint, bucketName };
    }

    async collectDataAddAttc(
        origin: string,
        collectId: any,
        taskId: number,
        files: Express.Multer.File[],
    ) {
        try {
            let task = await Task.findOne({
                where: { id: taskId },
                relations: ['client', 'category', 'subCategory', 'organization', 'user', 'clientGroup'],
            });

            let taskStorage = await this.attachmentsService.existingClientTaskStorage(task, StorageSystem.BHARATHCLOUD);

            let taskAttachments: Storage[] = [];
            const collectData = await CollectData.findOne({ where: { uid: collectId } })

            for (let file of files) {
                const { buffer, mimetype, originalname } = file;
                let key = `${taskStorage.filePath}/${file.originalname}`
                let upload: any;

                try {
                    upload = await this.bharathService.upload(task.organization.id, buffer, key, mimetype);

                } catch (err) {
                    throw new BadRequestException(err);
                }

                let storage = new Storage();
                storage.name = originalname;
                storage.file = upload.Key;
                storage.webUrl = upload.webUrl;
                storage.authId = task.organization.id;
                storage.filePath = upload.Key;
                storage.task = task;
                storage.fileType = mimetype;
                storage.fileSize = file.size;
                storage.client = task.client;
                storage.clientGroup = task.clientGroup;
                storage.type = StorageType.FILE;
                storage.parent = taskStorage;
                storage.storageSystem = StorageSystem.BHARATHCLOUD;
                storage.collectId = collectData?.id;
                storage.origin = origin;
                taskAttachments.push(storage);
            };

            await Storage.save(taskAttachments);
            this.eventEmitter.emit(Event_Actions.COLLECT_DATA_FILES_UPLOAD, {
                origin,
                collectId,
                taskId,
                files: files.map(file => file.originalname)
            });

            return {
                success: true,
            };
        } catch (err) {
            console.log(err);
            throw new InternalServerErrorException(err);
        }
    };

    async deleteB3File(userId: number, key: string) {
        const user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
        return this.bharathService.deleteFile(user.organization.id, key);
    }
    async deleteB3Folder(userId: number, key: string) {
        const user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
        return this.bharathService.deleteFolder(user.organization.id, key)
    };
}