import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  ParseIntPipe,
  Post,
  Put,
  Req,
  UseGuards,
} from '@nestjs/common';
import { JwtAuthGuard } from 'src/modules/users/jwt/jwt-auth.guard';
import { CategoriesService } from './categories.service';
import CreateCategoryDto from './dto/create-category.dto';
import ImportCategoriesDto from './dto/import-categories.dto';

//http://localhost:3000/categories/default

@Controller('categories')
export class CategoriesController {
  constructor(private service: CategoriesService) { }

  @UseGuards(JwtAuthGuard)
  @Post()
  create(@Body() body: CreateCategoryDto, @Req() req: any) {
    const { userId } = req.user;
    return this.service.create(userId, body);
  }

  @UseGuards(JwtAuthGuard)
  @Get()
  findAll(@Req() req: any) {
    const { userId } = req.user;
    return this.service.findAll(userId);
  }

  @UseGuards(JwtAuthGuard)
  @Get('/category/:id')
  findOne(@Param('id', ParseIntPipe) id: number, @Req() req: any) {
    const { userId } = req.user;
    return this.service.findOne(userId, id);
  }

  @UseGuards(JwtAuthGuard)
  @Get('/default')
  findDefaultCategories(@Req() req: any) {
    return this.service.findDefaultCategories();
  }

  @UseGuards(JwtAuthGuard)
  @Post('/import')
  importCategories(@Body() body: ImportCategoriesDto, @Req() req: any) {
    const { userId } = req.user;
    return this.service.importCategories(userId, body);
  }

  @UseGuards(JwtAuthGuard)
  @Put('/:id')
  update(@Param('id', ParseIntPipe) id: number, @Body() body: CreateCategoryDto, @Req() req: any) {
    const { userId } = req.user;
    return this.service.update(id, body, userId);
  }

  @Delete('/:id')
  delete(@Param('id', ParseIntPipe) id) {
    return this.service.delete(id);
  }

  @UseGuards(JwtAuthGuard)
  @Post('/update-admin-categories')
  updateAdminCategories(@Req() req: any) {
    const { userId } = req.user;
    return this.service.updateAdminCategories(userId);
  }
}
