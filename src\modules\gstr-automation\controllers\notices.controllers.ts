import {
  Body,
  Controller,
  Get,
  Param,
  ParseIntPipe,
  Post,
  Query,
  Req,
  Request,
  UseGuards,
} from '@nestjs/common';
import { JwtAuthGuard } from 'src/modules/users/jwt/jwt-auth.guard';
import { GstrService } from '../service/notices.service';
import { AttachmentGstService } from '../service/attachments-gst.service';

@Controller('gstr')
export class GstrController {
  constructor(private service: GstrService, protected attachmentGstService: AttachmentGstService) {}

  @UseGuards(JwtAuthGuard)
  @Get('order-notices/:id')
  async getOrderNotices(
    @Request() req: any,
    @Param('id', ParseIntPipe) id: number,
    @Query() query: any,
  ) {
    const { userId } = req.user;
    return this.service.getOrderNotices(userId, id, query);
  }
  @UseGuards(JwtAuthGuard)
  @Post('export-gstr-clientnotices')
  async gstClientNoticeandordersExport(@Request() req: any, @Body() body: any) {
    const { userId } = req.user;
    const query = body;
    return this.service.gstClientNoticeandordersExport(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Get('order-notice/:id')
  async getOrderNotice(@Request() req: any, @Param('id', ParseIntPipe) id: number) {
    const { userId } = req.user;
    return this.service.getOrderNotice(userId, id);
  }

  @UseGuards(JwtAuthGuard)
  @Get('additional-notice/:id')
  async getGstrAdditionalDeails(@Request() req: any, @Param('id', ParseIntPipe) id: number) {
    const { userId } = req.user;
    return this.service.getGstrAdditionalDeailss(userId, id);
  }

  @UseGuards(JwtAuthGuard)
  @Get('gstr-profile/:id')
  async getGstrProfile(@Request() req: any, @Param('id', ParseIntPipe) id: number) {
    const { userId } = req.user;
    return this.service.getGstrProfile(userId, id);
  }

  @UseGuards(JwtAuthGuard)
  @Get('gstr-compliance/:id')
  async getGstrClientCompliance(@Request() req: any, @Param('id', ParseIntPipe) id: number) {
    const { userId } = req.user;
    return this.service.getGstrClientCompliance(userId, id);
  }

  @UseGuards(JwtAuthGuard)
  @Get('gstr-add')
  async getAddNoticeAndOrders(@Request() req: any, @Query() query: any) {
    const { userId } = req.user;
    return this.service.getAddNoticeAndOrders(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Post('export-gstr-add')
  async exportAdditionalGstNoticeAndOrders(@Request() req: any, @Body() body: any) {
    const { userId } = req.user;
    const query = body;
    return this.service.exportAdditionalGstNoticeAndOrders(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Get('gstr-notices-orders')
  async getNoticeAndOrders(@Request() req: any, @Query() query: any) {
    const { userId } = req.user;
    return this.service.getNoticeAndOrders(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Post('export-gstr-notices-orders')
  async exportGstNoticeAndOrders(@Request() req: any, @Body() body: any) {
    const { userId } = req.user;
    const query = body;
    return this.service.exportGstNoticeAndOrders(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Get('reports')
  getclientReport(@Request() req: any, @Query() query: any) {
    const { userId } = req.user;
    return this.service.getGstrReport(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Post('export-reports')
  async exportGstClientReport(@Request() req: any, @Body() body: any) {
    const { userId } = req.user;
    const query = body;
    return this.service.exportGstClientReport(userId, query);
  }
  @UseGuards(JwtAuthGuard)
  @Get('additional/:id')
  async getGstrAdditionalNoticeOrders(
    @Request() req: any,
    @Param('id', ParseIntPipe) id: number,
    @Query() query: any,
  ) {
    const { userId } = req.user;
    return this.service.getGstrAdditionalNoticeOrders(userId, id, query);
  }
  @UseGuards(JwtAuthGuard)
  @Post('export-referencenotices')
  async exportreferenceBasedNotices(@Request() req: any, @Body() body: any) {
    const { userId } = req.user;
    const query = body;
    return this.service.exportreferenceBasedNotices(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Get('/notice-and-order-dueDate-events')
  getFyaEvents(@Req() req: any, @Query() query: any) {
    const { userId } = req.user;
    return this.service.getNoticeAndOrderDueDateEvents(userId, query.startDates);
  }

  @UseGuards(JwtAuthGuard)
  @Get('/notice-and-order-issue-events')
  getNoticeAndOrderIssueDateEvents(@Req() req: any, @Query() query: any) {
    const { userId } = req.user;
    return this.service.getNoticeAndOrderIssueDateEvents(userId, query.startDates);
  }

  @UseGuards(JwtAuthGuard)
  @Get('/add-notice-issue-events')
  getAdditionalNoticeOrderIssueDateEvents(@Req() req: any, @Query() query: any) {
    const { userId } = req.user;
    return this.service.getAdditionalNoticeOrderIssueDateEvents(userId, query.startDates);
  }

  @UseGuards(JwtAuthGuard)
  @Get('/add-notice-dueDate-events')
  getAdditionalNoticeOrderDueDateEvents(@Req() req: any, @Query() query: any) {
    const { userId } = req.user;
    return this.service.getAdditionalNoticeOrderDueDateEvents(userId, query.startDates);
  }

  @UseGuards(JwtAuthGuard)
  @Get('new-updates')
  getGstrUpdates(@Req() req: any, @Query() query: any) {
    const { userId } = req.user;
    return this.service.getGstrUpdates(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Get('update/:id')
  findGstrUpdateItem(@Req() req: any, @Param('id', ParseIntPipe) id: number) {
    const { userId } = req.user;
    return this.service.findGstrUpdateItem(userId, id);
  }

  @UseGuards(JwtAuthGuard)
  @Get('scheduling')
  async organizationScheduling(@Req() req: any) {
    const { userId } = req.user;
    return this.service.organizationGstrScheduling(userId);
  }

  @UseGuards(JwtAuthGuard)
  @Get('demands')
  async getGstrDemands(@Req() req:any, @Query() query:any){
    const {userId} = req.user
    return this.service.getAllGstrDemands(userId,query);
  }

  @UseGuards(JwtAuthGuard)
  @Get('demand/:id')
  async getClientGstrDemand(@Req() req:any,@Param('id',ParseIntPipe) id:number){
    const {userId} = req.user;
    return this.service.getClientDemands(userId,id)
  }

  @UseGuards(JwtAuthGuard)
  @Get('ledgers')
  async getGstrAllLedgers(@Req() req:any, @Query() query:any){
    const {userId} = req.user
    return this.service.getAllGstrLedgers(userId,query);
  }

  @UseGuards(JwtAuthGuard)
  @Get('ledger/:id')
  async getClientLedgers(@Req() req:any, @Query() query:any, @Param('id',ParseIntPipe) id:number){
    const{userId} = req.user;
    return this.service.getClientLedgers(userId,query,id)
  }

  @UseGuards(JwtAuthGuard)
  @Post('/demands-export')
  async exportDemands(@Req() req: any, @Body() body: any) {
    const { userId } = req?.user;
    const query = body;
    return this.service.exportDemands(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Post('/ledgers-export')
  async exportLedgers(@Req() req: any, @Body() body: any) {
    const { userId } = req?.user;
    const query = body;
    return this.service.exportLedgers(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Post('/export-clientdemand')
  async exportClientDemand(@Req() req: any, @Body() body: any) {
    const { userId } = req?.user;
    const query = body;
    return this.service.exportClientDemand(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Post('/export-clientledger')
  async exportClientLedger(@Req() req: any, @Body() body: any) {
    const { userId } = req?.user;
    const query = body;
    return this.service.exportClientLedger(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Get('case-type-name')
  getCaseTypeNames(@Req() req: any) {
    const { userId } = req.user;
    return this.service.getCaseTypeNames(userId);
  }

  @UseGuards(JwtAuthGuard)
  @Get('case-folder-type-name')
  getCaseFolderTypeNames(@Req() req: any) {
    const { userId } = req.user;
    return this.service.getCaseFolderTypeNames(userId);
  }
}
