import { BadRequestException, Injectable, InternalServerErrorException } from '@nestjs/common';
import { User, UserStatus } from 'src/modules/users/entities/user.entity';
import { Brackets, Connection, create<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, get<PERSON><PERSON><PERSON>, IsNull, Like, Not } from 'typeorm';
import AutProfileDetails from '../entities/aut-profile-details.entity';
import AutIncomeTaxForms from '../entities/aut_income_tax_forms.entity';
import AutClientCredentials, {
  IncomeTaxStatus,
  syncStatus,
} from '../entities/aut_client_credentials.entity';
import Client from 'src/modules/clients/entity/client.entity';
import AutIncometaxReturns from '../entities/aut_incometax_returns.entity';
import AutOutstandingDemand from '../entities/aut-outstanding-demand.entity';
import AutEProceeding from '../entities/aut_income_tax_eproceedings_fya.entity';
import AutEProceedingFya from '../entities/aut_income_tax_eproceedings_fya.entity';
import AutFyaNotice from '../entities/aut_income_tax_eproceedings_fya_notice.entity';
import AutEProceedingFyi from '../entities/aut_income_tax_eproceedings_fyi.entity';
import AutFyiNotice from '../entities/aut_income_tax_eproceedings_fyi_notice.entity';
import axios from 'axios';
import AutJurisdictionDetails from '../entities/aut-jurisdiction-details.entity';
import AutActivity from '../entities/aut_activity.entity';
import { dateFormation } from 'src/utils/datesFormation';
import AutomationMachines from '../entities/automation_machines.entity';
import { ClientPasswordService } from 'src/modules/clients/services/client-passwords.service';
import { ClientService } from 'src/modules/clients/services/clients.service';
import { TAN_REGEX } from 'src/utils/validations/regex-pattrens';
import { RegistrationType } from 'src/modules/gstr-register/entity/gstr-register.entity';
import OrganizationPreferences from 'src/modules/organization-preferences/entity/organization-preferences.entity';
import Password, { IsExistingAtomPro } from 'src/modules/clients/entity/password.entity';
import GstrCredentials, {
  GstrStatus,
} from 'src/modules/gstr-automation/entity/gstrCredentials.entity';
import checkGstrUsername from 'src/utils/validations/atom-pro/gstrUserName';
import checkGstrPassword from 'src/utils/validations/atom-pro/gstrPassword';
import checkPanNumber, { checkTanNumber } from 'src/utils/validations/atom-pro/panNumber';
import checkGstNumber from 'src/utils/validations/atom-pro/gstinPattern';
import { createClientCredentials, updateClientCredentials } from 'src/utils/atomProReUse';
import * as xlsx from 'xlsx';
import * as ExcelJS from 'exceljs';
import {
  calculateAssessmentYear,
  calculateAssessmentYear1,
  formatAmount,
  generateAssessmentYear,
  getFilingType,
  getFormType,
  getverificationStatus,
} from 'src/utils/re-use';
import { formatDate } from 'src/utils';
import { capitalize } from 'lodash';
import AutIncometaxEChallan from '../entities/aut_incometax_e-challan.entity';
import AutEChallan from '../entities/aut_incometax_e-challan.entity';
import AutMyCas from '../entities/aut_incometax_my-cas.entity';
import AutoIncomeTaxForms from '../entities/aut_income_tax_forms.entity';
import TanClientCredentials from 'src/modules/tan-automation/entity/tan-client-credentials.entity';
import AutomationMachinesArchive from '../entities/automation_machines_archive.entity';
import { Permissions } from 'src/modules/events/permission';
import checkTracesUserId from 'src/utils/validations/atom-pro/tracesUserId';
import checkTracesPassword from 'src/utils/validations/atom-pro/tracesPassword';
import IncTempEproFya from '../entities/inc_temp_epro_fya.entity';
import IncTempEproFyi from '../entities/inc_temp_epro_fyi.entity';
import * as moment from 'moment';

const categoryLabels = {
  individual: 'Individual',
  huf: 'Hindu Undivided Family',
  partnership_firm: 'Partnership Firm',
  llp: 'Limited Liability Partnership',
  company: 'Company',
  opc: 'OPC',
  public: 'Public Limited',
  government: 'Government',
  sec_8: 'Section-8',
  foreign: 'Foreign',
  aop: 'Association of Persons',
  boi: 'Body of Individuals',
  trust: 'Trust',
  public_trust: 'Public Trust',
  private_discretionary_trust: 'Private Discretionary Trust',
  state: 'State',
  central: 'Central',
  local_authority: 'Local Authority',
  artificial_judicial_person: 'Artificial Juridical Person',
};

@Injectable()
export class AutIncomeTaxFormsService {
  constructor(
    private clientPasswordsService: ClientPasswordService,
    private clientService: ClientService,
  ) { }
  async findAll(userId: number, query: any) {
    try {
      const { limit, offset } = query;
      let user = await User.findOne({
        where: { id: userId },
        relations: ['organization', 'role'],
      });
      let ViewAll = user.role.permissions.find(
        (permission) => permission.slug === Permissions.VIEW_ALL_CLIENT_MANAGERS,
      );
      let ViewAssigned = user.role.permissions.find(
        (permission) => permission.slug === Permissions.VIEW_ASSIGNED_CLIENT_MANAGERS,
      );
      let forms = createQueryBuilder(AutIncomeTaxForms, 'forms')
        .leftJoinAndSelect('forms.client', 'client')
        .leftJoin('client.clientManagers', 'clientManagers')
        .leftJoinAndSelect('forms.autClientCredentials', 'clientCredentials')
        .where('forms.organizationId = :id', { id: user?.organization?.id })
        .andWhere('client.status != :status', { status: UserStatus.DELETED })
        .andWhere('clientCredentials.status != :disStatus', {
          disStatus: IncomeTaxStatus.DISABLE,
        });
      const sort = typeof query?.sort === 'string' ? JSON.parse(query.sort) : query?.sort;
      if (sort?.column) {
        const columnMap: Record<string, string> = {
          id: 'forms.refYear',
          clientName: 'client.displayName',
          formDesc: 'forms.formDesc',
          ackDt: 'forms.ackDt',
          filingTypeCd: 'forms.filingTypeCd',
          udinNum: 'forms.udinNum',
        };
        const column = columnMap[sort.column] || sort.column;
        forms.orderBy(column, sort.direction.toUpperCase());
      }
      if (ViewAssigned && !ViewAll) {
        forms.andWhere('clientManagers.id = :userId', { userId });
      }
      if (query.search) {
        forms.andWhere('client.displayName LIKE :search', { search: `%${query.search}%` });
        forms.orWhere('clientCredentials.panNumber LIKE :search', { search: `%${query.search}%` });
      }

      if (query.formDesc) {
        forms.andWhere('forms.formDesc = :formDesc', {
          formDesc: query.formDesc,
        });
      }

      if (query.filingType) {
        forms.andWhere('forms.filingTypeCd = :filingType', {
          filingType: query.filingType,
        });
      }

      if (query.assessmentYear) {
        forms.andWhere(
          new Brackets((qb) => {
            qb.where('forms.refYear = :assY AND forms.refYearType = :assessmentYearType', {
              assY: query.assessmentYear,
              assessmentYearType: 'AY',
            }).orWhere('forms.refYear = :finY AND forms.refYearType = :financialYearType', {
              finY: parseInt(query.assessmentYear) - 1,
              financialYearType: 'FY',
            });
          }),
        );
      }

      // if (query.financialYear) {
      //   forms.andWhere('forms.refYear = :refYearFy', {
      //     refYearFy: query.financialYear,
      //   });
      //   forms.andWhere('forms.refYearType = :refYearTypeFy', {
      //     refYearTypeFy: 'FY',
      //   });
      // }

      if (query.clientCategory) {
        forms.andWhere(`client.category = :clientCategory`, {
          clientCategory: query.clientCategory,
        });
      }

      if (query.udinStat) {
        switch (query.udinStat) {
          case 'UDIN_APPLICABLE':
            forms.andWhere('forms.isUdinApplicable is true');
            break;
          case 'UDIN_NOT_APPLICABLE':
            forms.andWhere('forms.isUdinApplicable is false');
            break;
          case 'UDIN_COMPLETED':
            forms.andWhere('forms.udinNum is not null');
            break;
          case 'UDIN_PENDING':
            forms.andWhere('forms.udinNum is null').andWhere('forms.isUdinApplicable is true');
          default:
            break;
        }
      }

      if (offset >= 0) {
        forms.skip(offset);
      }

      if (limit) {
        forms.take(limit);
      }

      forms.addSelect(
        `COALESCE(
          STR_TO_DATE(NULLIF(forms.ackDt, 'NA'), '%d-%b-%Y'),'0000-01-01'
        )`,
        'issueDateOrder',
      );
      forms.addOrderBy('issueDateOrder', 'DESC');

      let result = await forms.getManyAndCount();
      return {
        count: result[1],
        result: result[0],
      };
    } catch (error) {
      console.log('error occur while getting findAll', error);
    }
  }

  async exportIncometaxForms(userId: number, query: any) {
    const id = query.incometaxid;
    const newQuery = { ...query, offset: 0, limit: ********* };
    let itforms = await this.findAll(userId, newQuery);

    if (!itforms.result.length) throw new BadRequestException('No Data for Export');
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Inc Tax Forms');
    const headers = [
      { header: 'S.No', key: 'serialNo' },
      { header: 'FY', key: 'fy' },
      { header: 'AY', key: 'ay' },
      { header: 'Client Name', key: 'clientName' },
      { header: 'PAN', key: 'pan' },
      { header: 'Filing Type', key: 'filingType' },
      { header: 'Form Name', key: 'formName' },
      { header: 'Acknowledgement #', key: 'acknowledgmentNum' },
      { header: 'Date of Filing', key: 'dateofFiling' },
      { header: 'UDIN', key: 'udin' },
    ];

    worksheet.columns = headers;
    const columnMaxLengths = Array(headers.length).fill(0);
    let serialCounter = 1; // Initialize a counter

    itforms.result.forEach((itform) => {
      const fy =
        itform?.refYearType === 'FY'
          ? calculateAssessmentYear1(itform?.refYear)
          : calculateAssessmentYear1(itform?.refYear, -1);

      // For AY, apply a +1 offset to go one year forward
      const ay =
        itform?.refYearType === 'AY'
          ? calculateAssessmentYear1(itform?.refYear)
          : calculateAssessmentYear1(itform?.refYear, 1);

      const rowData = {
        serialNo: serialCounter++, // Assign and then increment the counter
        fy: fy,
        ay: ay,
        clientName: itform?.client?.displayName,
        pan: itform?.autClientCredentials?.panNumber,
        filingType: itform?.filingTypeCd,
        formName: itform?.formDesc,
        acknowledgmentNum: itform?.ackNum,
        dateofFiling: formatDate(itform?.ackDt),
        udin: itform?.udinNum,
      };

      const row = worksheet.addRow(rowData);
      const typeCell = row.getCell('filingType');
      if (rowData.filingType === 'Original') {
        typeCell.font = {
          color: { argb: '4B0082' }, // Gold color for "Self"
          bold: true, // Bold text
        };
      } else if (rowData.filingType === 'Revised') {
        typeCell.font = {
          color: { argb: 'FF8C00' }, // Gold color for "Self"
          bold: true, // Bold text
        };
      }

      worksheet.columns.forEach((column, colIndex) => {
        const headerLength = column.header?.length || 0;
        const cellLength = rowData[column.key]?.toString().length || 0;
        columnMaxLengths[colIndex] = Math.max(
          columnMaxLengths[colIndex] || 0,
          headerLength,
          cellLength,
        );
      });
    });

    worksheet.columns.forEach((column, colIndex) => {
      column.width = columnMaxLengths[colIndex] + 3;
    });

    const headerRow = worksheet.getRow(1);
    headerRow.font = { bold: true };
    headerRow.eachCell((cell) => {
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '64B5F6' },
      };
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' },
      };
      cell.alignment = { horizontal: 'center', vertical: 'middle' };
    });
    worksheet.columns.forEach((column) => {
      if (column.key === 'clientName' || column.key === 'formName') {
        column.width = 50;
        column.alignment = { wrapText: true, horizontal: 'center', vertical: 'middle' };
      } else {
        column.alignment = { horizontal: 'center', vertical: 'middle' };
      }
    });

    worksheet.eachRow((row) => {
      row.eachCell((cell) => {
        cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
      });
    });

    worksheet.views = [{ state: 'frozen', ySplit: 1 }];

    const buffer = await workbook.xlsx.writeBuffer();
    return buffer;
  }
  async findForm(userId: number, id: number) {
    try {
      const user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
      const incomeTaxForm = await AutIncomeTaxForms.findOne({
        where: { id, organizationId: user?.organization?.id },
        relations: ['client', 'client.autProfileDetails'],
      });
      let myCaDetails;
      if (incomeTaxForm?.caMembershipNo) {
        myCaDetails = await AutMyCas.findOne({
          where: { caMembershipNum: incomeTaxForm?.caMembershipNo },
        });
      }
      return { ...incomeTaxForm, myCaDetails };
    } catch (error) {
      console.log('error occur while getting findForm', error);
    }
  }

  async getClientAutCredentials(userId: number, query: any) {
    try {
      const { offset, limit } = query;
      let user = await User.findOne({
        where: { id: userId },
        relations: ['organization', 'role'],
      });

      let ViewAll = user.role.permissions.find(
        (permission) => permission.slug === Permissions.VIEW_ALL_CLIENT_MANAGERS,
      );
      let ViewAssigned = user.role.permissions.find(
        (permission) => permission.slug === Permissions.VIEW_ASSIGNED_CLIENT_MANAGERS,
      );

      let clientCredentials = createQueryBuilder(AutClientCredentials, 'clientCredentials')
        .leftJoinAndSelect('clientCredentials.client', 'client')
        .leftJoin('client.clientManagers', 'clientManagers')
        .where('clientCredentials.organizationId = :id', { id: user?.organization?.id })
        .andWhere('client.status != :status', { status: UserStatus.DELETED })
        .andWhere(
          new Brackets((qb) => {
            qb.where('clientCredentials.status IS NULL').orWhere(
              'clientCredentials.status = :enabledStatus',
              { enabledStatus: IncomeTaxStatus.ENABLE },
            );
          }),
        );
      if (ViewAssigned && !ViewAll) {
        clientCredentials.andWhere('clientManagers.id = :userId', { userId });
      }

      const sort = typeof query?.sort === 'string' ? JSON.parse(query.sort) : query?.sort;
      if (sort?.column) {
        const columnMap: Record<string, string> = {
          client: 'client.clientId',
          name: 'client.displayName',
          Category: 'client.category',
        };
        const column = columnMap[sort.column] || sort.column;
        clientCredentials.orderBy(column, sort.direction.toUpperCase());
      } else {
        clientCredentials.orderBy('client.createdAt', 'DESC');
      }
      if (query.search) {
        clientCredentials.andWhere(
          new Brackets((qb) => {
            qb.where('client.displayName LIKE :search', {
              search: `%${query.search}%`,
            });
            qb.orWhere('clientCredentials.panNumber LIKE :search', {
              search: `%${query.search}%`,
            });
          }),
        );
      }

      if (offset >= 0) {
        clientCredentials.skip(offset);
      }

      if (limit) {
        clientCredentials.take(limit);
      }
      let result = await clientCredentials.getManyAndCount();
      return {
        count: result[1],
        result: result[0],
      };
    } catch (error) {
      console.log('error occur while getting getClientAutCredentials', error);
    }
  }

  async exportClientAutCredentials(userId: number, query: any) {
    const newQuery = { ...query, offset: 0, limit: ********* };
    let credentials = await this.getClientAutCredentials(userId, newQuery);

    if (!credentials.result.length) throw new BadRequestException('No Data for Export');
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Inc Tax Clients');
    const headers = [
      { header: 'S.No', key: 'serialNo' },
      { header: 'Client ID', key: 'clientId' },
      { header: 'Category', key: 'category' },
      { header: 'Client Name', key: 'clientName' },
      { header: 'PAN', key: 'pan' },
      { header: 'Password', key: 'password' },
    ];

    worksheet.columns = headers;
    const columnMaxLengths = Array(headers.length).fill(0);
    let serialCounter = 1; // Initialize a counter

    credentials.result.forEach((credential) => {
      const rowData = {
        serialNo: serialCounter++, // Assign and then increment the counter
        clientId: credential?.client?.clientId,
        category: categoryLabels[credential?.client?.category],
        clientName: credential?.client?.displayName,
        pan: credential?.panNumber,
        password: credential?.password,
      };

      const row = worksheet.addRow(rowData);

      worksheet.columns.forEach((column, colIndex) => {
        const headerLength = column.header?.length || 0;
        const cellLength = rowData[column.key]?.toString().length || 0;
        columnMaxLengths[colIndex] = Math.max(
          columnMaxLengths[colIndex] || 0,
          headerLength,
          cellLength,
        );
      });
    });

    worksheet.columns.forEach((column, colIndex) => {
      column.width = columnMaxLengths[colIndex] + 3;
    });

    const headerRow = worksheet.getRow(1);
    headerRow.font = { bold: true };
    headerRow.eachCell((cell) => {
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '64B5F6' },
      };
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' },
      };
      cell.alignment = { horizontal: 'center', vertical: 'middle' };
    });

    worksheet.eachRow((row) => {
      row.eachCell((cell) => {
        cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
      });
    });

    worksheet.views = [{ state: 'frozen', ySplit: 1 }];

    const buffer = await workbook.xlsx.writeBuffer();
    return buffer;
  }
  async addClientAutCredentials(userId: number, body: any) {
    try {
      let user = await User.findOne({
        where: { id: userId },
        relations: ['organization'],
      });

      const credential = await AutClientCredentials.findOne({
        where: { organizationId: user?.organization?.id, panNumber: body?.panNumber },
      });

      const organizationPreferences: any = await OrganizationPreferences.findOne({
        where: { organization: user.organization },
      });
      if (organizationPreferences) {
        const organizationLimit = organizationPreferences?.automationConfig?.incomeTaxLimit;
        if (organizationLimit) {
          const autClientCredential = await AutClientCredentials.count({
            where: { organizationId: user.organization.id, status: IncomeTaxStatus.ENABLE },
          });
          if (organizationLimit >= autClientCredential) {
            if (credential) {
              throw new BadRequestException(
                'Specified pannumber Utilize your organization already',
              );
            } else {
              const client = await Client.findOne({ where: { id: body?.selectedClient?.id } });
              const details = {
                website: 'Income Tax | e-Filing (PAN)',
                websiteUrl: 'https://eportal.incometax.gov.in/iec/foservices/#/login',
                loginId: body?.panNumber,
                password: body?.password,
                client: client,
                isaddAtomPro: IsExistingAtomPro.YES,
                userId,
              };

              const passwordCheck = await Password.findOne({where :{client:client,website:details.website,loginId:details.loginId},order:{createdAt:'DESC'}});

              let password = null;
              if(passwordCheck){
               password = await updateClientCredentials(details,passwordCheck.id);
              }else{
               password = await createClientCredentials(details);
              }
              const clientCredentials = new AutClientCredentials();
              clientCredentials.panNumber = body?.panNumber;
              clientCredentials.password = body?.password;
              clientCredentials.client = client;
              clientCredentials.organizationId = user?.organization?.id;
              clientCredentials.syncStatus = syncStatus.NOTSYNC;
              clientCredentials.passwordId = password?.id;
              clientCredentials.status = IncomeTaxStatus.ENABLE;
              await clientCredentials.save();
              

              // if (clientCredentials) {
              //   const autActivity = new AutActivity();
              //   autActivity.action = 'CLIENT_ADDITION ';
              //   autActivity.remarks = `${body?.selectedClient?.displayName} is now enrolled in the Income tax automation system by ${user?.fullName}`;
              //   autActivity.userId = userId;
              //   autActivity.clientId = body?.selectedClient?.id;
              //   autActivity.autClientCredentialsId = clientCredentials?.id;
              //   await autActivity.save();
              // }
            }
          } else {
            throw new BadRequestException('Maximum Income tax Client Count Reached');
          }
        }
      }
    } catch (error) {
      console.log('Error occur while add the incomeTax client credentials', error);
      throw new InternalServerErrorException(error);
    }
  }

  async getClientCredential(id: number) {
    try {
      const clientCredential = await AutClientCredentials.findOne({
        where: { id },
        relations: ['client'],
      });
      return clientCredential;
    } catch (error) {
      console.log('error occured while fetching income tax client credentials', error);
    }
  }

  async getAllClients(userId: number, data: any) {
    let user = await User.findOne({
      where: { id: userId },
      relations: ['organization'],
    });

    const entityManager = getManager();
    //     const query = `
    //     SELECT id, display_name as displayName, status
    //     FROM client
    //     WHERE organization_id = ${user?.organization.id}
    //     AND status != 'DELETED'
    //     AND id NOT IN (
    //         SELECT client_id
    //         FROM aut_client_credentials
    //     )
    // `;

    let query = `
  SELECT id, display_name as displayName, status
  FROM client
  WHERE organization_id = ${user?.organization.id}
    AND status != 'DELETED'
    AND id NOT IN (
      SELECT client_id
      FROM aut_client_credentials
      WHERE organization_id = ${user?.organization.id}
        AND client_id IS NOT NULL
    )
`;

    if (data?.search) {
      query += ` AND display_name LIKE '%${data?.search}%'`;
    }
    if (data?.limit) {
      query += ` LIMIT ${data?.limit}`;
      if (data?.page) {
        query += ` OFFSET ${data?.page}`;
      }
    }
    let clients = await entityManager.query(query);
    return clients;
  }

  async getIncomeTaxProfile(userId: number, id: number) {
    try {
      const user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
      const checkClientCredentials = await AutClientCredentials.findOne({
        where: { id: id, organizationId: user.organization.id },
      });
      if (checkClientCredentials) {
        const checkStatus = await AutomationMachines.findOne({
          where: { autoCredentials: id, status: 'PENDING' },
        });

        if (checkStatus) {
          console.log(checkStatus);
        }

        const lastCompletedMachine = await AutomationMachines.findOne({
          where: { autoCredentials: id, status: 'COMPLETED' },
          order: {
            id: 'DESC',
          }, // Assuming you have a createdAt field indicating creation timestamp
        });
        try {
          const clientCredential = await AutClientCredentials.findOne({
            where: { id },
            relations: ['client'],
          });

          if (clientCredential) {
            const profileDetails = await AutProfileDetails.findOne({
              where: { ClientId: clientCredential?.client?.id, organization: user.organization },
            });
            return { profileDetails, lastCompletedMachine, checkClientCredentials: true };
          }
        } catch (error) {
          console.log('error occured while fetching income tax client credentials', error);
        }
      } else {
        {
          checkClientCredentials;
        }
      }
    } catch (error) {
      console.log('error occure while getting getIncomeTaxProfile', error);
    }
  }

  async getIncomeTaxJurisdiction(id: number) {
    try {
      const clientCredentials = await AutClientCredentials.findOne({
        where: { id },
        relations: ['client'],
      });

      if (clientCredentials) {
        const jurisdictionsDetails = await AutJurisdictionDetails.findOne({
          where: { clientId: clientCredentials?.client?.id },
        });
        return jurisdictionsDetails;
      }
    } catch (error) {
      console.log('error occured while fetching income tax jurisdiction details', error);
    }
  }

  async getClientform(id: number, query: any, userId: number) {
    const user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
    const { limit, offset } = query;
    try {
      const clientCredential = await AutClientCredentials.findOne({
        where: { id, organizationId: user?.organization?.id },
        relations: ['client'],
      });

      if (clientCredential) {
        const formDetails = await createQueryBuilder(AutIncomeTaxForms, 'autIncomeTaxForms')
          .leftJoinAndSelect('autIncomeTaxForms.client', 'client')
          .leftJoinAndSelect('autIncomeTaxForms.autClientCredentials', 'autClientCredentials')
          .where('autClientCredentials.id =:id', { id: id });

        if (offset) {
          formDetails.skip(offset);
        }

        if (limit) {
          formDetails.take(limit);
        }

        formDetails.addSelect(
          `COALESCE(
            STR_TO_DATE(NULLIF(autIncomeTaxForms.ackDt, 'NA'), '%d-%b-%Y'),'0000-01-01'
          )`,
          'issueDateOrder',
        );

        formDetails.addOrderBy('issueDateOrder', 'DESC');

        let result = await formDetails.getManyAndCount();
        return {
          count: result[1],
          result: result[0],
          accessDenied: true,
        };
      } else {
        return { accessDenied: false };
      }
    } catch (error) {
      console.log('error occured while fetching income tax client forms', error);
    }
  }

  async exportClientForm(userId: number, query: any) {
    const id = query.incometaxid;

    const newQuery = { ...query, offset: 0, limit: ********* };
    let clientforms = await this.getClientform(id, newQuery, userId);

    if (!clientforms.result.length) throw new BadRequestException('No Data for Export');
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Inc Tax Forms');
    const headers = [
      { header: 'S.No', key: 'serialNo' },
      { header: 'FY', key: 'fy' },
      { header: 'AY', key: 'ay' },
      { header: 'Client Name', key: 'clientName' },
      { header: 'PAN', key: 'pan' },
      { header: 'Filing Type', key: 'filingType' },
      { header: 'Form Name', key: 'formName' },
      { header: 'Acknowledgement #', key: 'acknowledgmentNum' },
      { header: 'Date of Filing', key: 'dateofFiling' },
      { header: 'UDIN', key: 'udin' },
    ];

    worksheet.columns = headers;
    const columnMaxLengths = Array(headers.length).fill(0);
    let serialCounter = 1; // Initialize a counter
    clientforms.result.forEach((itform) => {
      const fy =
        itform?.refYearType === 'FY'
          ? calculateAssessmentYear1(itform?.refYear)
          : calculateAssessmentYear1(itform?.refYear, -1);

      // For AY, apply a +1 offset to go one year forward
      const ay =
        itform?.refYearType === 'AY'
          ? calculateAssessmentYear1(itform?.refYear)
          : calculateAssessmentYear1(itform?.refYear, 1);

      const rowData = {
        serialNo: serialCounter++, // Assign and then increment the counter
        fy: fy,
        ay: ay,
        clientName: itform?.client?.displayName,
        pan: itform?.autClientCredentials?.panNumber,
        filingType: itform?.filingTypeCd,
        formName: itform?.formDesc,
        acknowledgmentNum: itform?.ackNum,
        dateofFiling: formatDate(itform?.ackDt),
        udin: itform?.udinNum,
      };

      const row = worksheet.addRow(rowData);
      const typeCell = row.getCell('filingType');
      if (rowData.filingType === 'Original') {
        typeCell.font = {
          color: { argb: '4B0082' }, // Gold color for "Self"
          bold: true, // Bold text
        };
      } else if (rowData.filingType === 'Revised') {
        typeCell.font = {
          color: { argb: 'FF8C00' }, // Gold color for "Self"
          bold: true, // Bold text
        };
      }

      worksheet.columns.forEach((column, colIndex) => {
        const headerLength = column.header?.length || 0;
        const cellLength = rowData[column.key]?.toString().length || 0;
        columnMaxLengths[colIndex] = Math.max(
          columnMaxLengths[colIndex] || 0,
          headerLength,
          cellLength,
        );
      });
    });

    worksheet.columns.forEach((column, colIndex) => {
      column.width = columnMaxLengths[colIndex] + 3;
    });

    const headerRow = worksheet.getRow(1);
    headerRow.font = { bold: true };
    headerRow.eachCell((cell) => {
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '64B5F6' },
      };
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' },
      };
      cell.alignment = { horizontal: 'center', vertical: 'middle' };
    });
    worksheet.columns.forEach((column) => {
      if (column.key === 'clientName' || column.key === 'formName') {
        column.width = 50;
        column.alignment = { wrapText: true, horizontal: 'center', vertical: 'middle' };
      } else {
        column.alignment = { horizontal: 'center', vertical: 'middle' };
      }
    });

    worksheet.eachRow((row) => {
      row.eachCell((cell) => {
        cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
      });
    });

    worksheet.views = [{ state: 'frozen', ySplit: 1 }];

    const buffer = await workbook.xlsx.writeBuffer();
    return buffer;
  }
  async getIncomeTaxReturns(userId: number, query: any) {
    try {
      const { limit, offset } = query;
      let user = await User.findOne({
        where: { id: userId },
        relations: ['organization', 'role'],
      });
      let ViewAll = user.role.permissions.find(
        (permission) => permission.slug === Permissions.VIEW_ALL_CLIENT_MANAGERS,
      );
      let ViewAssigned = user.role.permissions.find(
        (permission) => permission.slug === Permissions.VIEW_ASSIGNED_CLIENT_MANAGERS,
      );
      let returns = createQueryBuilder(AutIncometaxReturns, 'returns')
        .leftJoinAndSelect('returns.client', 'client')
        .leftJoin('client.clientManagers', 'clientManagers')
        .leftJoinAndSelect('returns.autClientCredentials', 'clientCredentials')
        .where('returns.organizationId = :id', { id: user?.organization?.id })
        .andWhere('client.status != :status', { status: UserStatus.DELETED })
        .andWhere('clientCredentials.status != :disStatus', {
          disStatus: IncomeTaxStatus.DISABLE,
        });

      const sort = typeof query?.sort === 'string' ? JSON.parse(query.sort) : query?.sort;
      if (sort?.column) {
        const columnMap: Record<string, string> = {
          assmentYear: 'returns.assmentYear',
          category: 'client.category',
          clientId: 'client.displayName',
          filingTypeCd: 'returns.filingTypeCd',
          id: 'returns.submitTmstmp',
          verStatus: 'returns.verStatus',
          formtypeCd: 'returns.formtypeCd',
          timeLineDate: 'returns.timeLineDate',
          timeLineDesc: 'returns.timeLineDesc',
        };
        const column = columnMap[sort.column] || sort.column;
        returns.orderBy(column, sort.direction.toUpperCase());
      } else {
        returns.orderBy('returns.submitTmstmp', 'DESC');
      }
      if (query.search) {
        const whereClause = `(
          returns.ackNum like '%${query.search}%' or
          client.displayName like '%${query.search}%' or
          clientCredentials.panNumber like '%${query.search}%'
        )`;
        returns.andWhere(whereClause);
      }

      if (ViewAssigned && !ViewAll) {
        returns.andWhere('clientManagers.id = :userId', { userId });
      }

      if (query.itrType) {
        returns.andWhere('returns.formtypeCd = :itrType', {
          itrType: query.itrType,
        });
      }
      if (query.filingType) {
        returns.andWhere('returns.filingTypeCd = :filingType', {
          filingType: query.filingType,
        });
      }
      if (query.assessmentYear) {
        returns.andWhere('returns.assmentYear = :assessmentYear', {
          assessmentYear: query.assessmentYear,
        });
      }

      if (query.clientCategory) {
        returns.andWhere(`client.category = :clientCategory`, {
          clientCategory: query.clientCategory,
        });
      }
      if (query.status) {
        if (query.status === 'empty') {
          returns.andWhere('returns.verStatus IS NULL');
        } else if (query.status === 'Y') {
          returns.andWhere('returns.verStatus = :vestatus', {
            vestatus: query.status,
          });
        } else if (query.status === 'N') {
          returns.andWhere('returns.verStatus = :aastatus', {
            aastatus: query.status,
          });
        } else if (query.status === 'X') {
          returns.andWhere('returns.verStatus = :aastatus', {
            aastatus: query.status,
          });
        }
      }

      if (offset >= 0) {
        returns.skip(offset);
      }

      if (limit) {
        returns.take(limit);
      }

      // returns.orderBy('returns.submitTmstmp', 'DESC');

      let result = await returns.getManyAndCount();
      return {
        count: result[1],
        result: result[0],
      };
    } catch (error) {
      console.log('error occured while fetching getIncomeTaxReturns', error);
    }
  }

  async exportIncomeTaxReturns(userId: number, query: any) {
    const newQuery = { ...query, offset: 0, limit: ********* };
    let itreturns = await this.getIncomeTaxReturns(userId, newQuery);

    if (!itreturns.result.length) throw new BadRequestException('No Data for Export');
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Inc Tax Returns');
    const headers = [
      { header: 'S.No', key: 'serialNo' },
      { header: 'AY', key: 'ay' },
      { header: 'Category', key: 'category' },
      { header: 'Client Name', key: 'clientName' },
      { header: 'PAN', key: 'pan' },

      { header: 'Filing Type', key: 'filingType' },
      { header: 'ITR', key: 'itr' },
      { header: 'Acknowledgement #', key: 'acknowledgeNum' },
      { header: 'Filing Date', key: 'filingDate' },
      { header: 'e-Verification Status', key: 'everficationStatus' },
      { header: 'Updated Time', key: 'timeLineDate' },
      { header: 'Description', key: 'description' },
    ];

    worksheet.columns = headers;
    const columnMaxLengths = Array(headers.length).fill(0);
    let serialCounter = 1; // Initialize a counter

    itreturns.result.forEach((itreturn) => {
      const rowData = {
        serialNo: serialCounter++, // Assign and then increment the counter
        ay: calculateAssessmentYear(itreturn?.assmentYear),
        category: categoryLabels[itreturn?.client?.category],
        clientName: itreturn?.client?.displayName,
        pan: itreturn?.autClientCredentials?.panNumber,
        filingType: getFilingType(itreturn?.filingTypeCd),
        itr: getFormType(itreturn?.formtypeCd),
        acknowledgeNum: itreturn?.ackNum,
        filingDate: formatDate(itreturn?.ackDt),
        everficationStatus: getverificationStatus(itreturn?.verStatus),
        timeLineDate: itreturn?.timeLineDate
          ? moment(parseInt(itreturn?.timeLineDate)).format('DD MMM YYYY hh:mm')
          : '-',
        description: itreturn?.timeLineDesc,
      };

      const row = worksheet.addRow(rowData);

      // Set custom cell colors for the 'e-Verification Status' column
      const everficationCell = row.getCell('everficationStatus');
      if (rowData.everficationStatus === 'Verified') {
        everficationCell.font = {
          color: { argb: 'FF00B050' },
          bold: true, //  green
        };
      } else if (rowData.everficationStatus === 'Not Verified') {
        everficationCell.font = {
          color: { argb: 'FFFF0000' },
          bold: true, // red
        };
      } else if (rowData.everficationStatus === 'NA') {
        everficationCell.font = {
          color: { argb: '000000' },
          bold: true, // BLACK
        };
      }
      worksheet.columns.forEach((column, colIndex) => {
        const headerLength = column.header?.length || 0;
        const cellLength = rowData[column.key]?.toString().length || 0;
        columnMaxLengths[colIndex] = Math.max(
          columnMaxLengths[colIndex] || 0,
          headerLength,
          cellLength,
        );
      });
    });

    worksheet.columns.forEach((column, colIndex) => {
      column.width = columnMaxLengths[colIndex] + 3;
    });

    const headerRow = worksheet.getRow(1);
    headerRow.font = { bold: true };
    headerRow.eachCell((cell) => {
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '64B5F6' },
      };
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' },
      };
      cell.alignment = { horizontal: 'center', vertical: 'middle' };
    });

    worksheet.eachRow((row) => {
      row.eachCell((cell) => {
        cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
      });
    });

    worksheet.views = [{ state: 'frozen', ySplit: 1 }];

    const buffer = await workbook.xlsx.writeBuffer();
    return buffer;
  }

  async findReturn(userId: number, id: number) {
    try {
      const user = await User.findOne({ where: { id: userId }, relations: ['organization'] });

      const incometaxReturns = await AutIncometaxReturns.findOne({
        where: { id, organizationId: user?.organization?.id },
        relations: ['client', 'client.autProfileDetails'],
      });
      return incometaxReturns;
    } catch (error) {
      console.log('error occured while fetching findReturn', error);
    }
  }

  async getClientReturn(id: number, query: any, userId: number) {
    const { limit, offset } = query;
    const user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
    try {
      const clientCredential = await AutClientCredentials.findOne({
        where: { id, organizationId: user.organization.id },
      });
      if (clientCredential) {
        const returnDetails = await createQueryBuilder(AutIncometaxReturns, 'autIncometaxReturns')
          .leftJoin('autIncometaxReturns.autClientCredentials', 'autClientCredentials')
          .where('autClientCredentials.id =:id', { id: id })
          .andWhere(`autIncometaxReturns.organizationId=:organization`, {
            organization: user.organization.id,
          });
        const sort = typeof query?.sort === 'string' ? JSON.parse(query.sort) : query?.sort;
        if (sort?.column) {
          const columnMap: Record<string, string> = {
            assmentYear: 'autIncometaxReturns.assmentYear',
            filingTypeCd: 'autIncometaxReturns.filingTypeCd',
            id: 'autIncometaxReturns.submitTmstmp',
            verStatus: 'autIncometaxReturns.verStatus',
            formtypeCd: 'autIncometaxReturns.formtypeCd',
            timeLineDate: 'autIncometaxReturns.timeLineDate',
            timeLineDesc: 'autIncometaxReturns.timeLineDesc',
          };
          const column = columnMap[sort.column] || sort.column;
          returnDetails.orderBy(column, sort.direction.toUpperCase());
        } else {
          returnDetails.orderBy('autIncometaxReturns.submitTmstmp', 'DESC');
        }
        if (offset) {
          returnDetails.skip(offset);
        }

        if (limit) {
          returnDetails.take(limit);
        }

        // returnDetails.orderBy('autIncometaxReturns.submitTmstmp', "DESC");

        let result = await returnDetails.getManyAndCount();
        // console.log(result)
        return {
          count: result[1],
          result: result[0],
          accessDenied: true,
        };
      } else {
        return { accessDenied: false };
      }
    } catch (error) {
      console.log('error occured while fetching income tax client forms', error);
    }
  }

  async exportIncomeTaxClientReturnsexport(userId: number, query: any) {
    const id = query.incometaxid;
    const newQuery = { ...query, offset: 0, limit: ********* };
    let clientreports = await this.getClientReturn(id, newQuery, userId);

    if (!clientreports.result.length) throw new BadRequestException('No Data for Export');
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Inc Tax Returns');
    const headers = [
      { header: 'S.No', key: 'serialNo' },
      { header: 'AY', key: 'ay' },
      { header: 'Filing Type', key: 'filingType' },
      { header: 'ITR', key: 'itr' },
      { header: 'Acknowledgement #', key: 'acknowledgeNum' },
      { header: 'Filing Date', key: 'filingDate' },
      { header: 'e-Verification Status', key: 'everficationStatus' },
    ];

    worksheet.columns = headers;
    const columnMaxLengths = Array(headers.length).fill(0);
    let serialCounter = 1; // Initialize a counter

    clientreports.result.forEach((itreturn) => {
      const rowData = {
        serialNo: serialCounter++, // Assign and then increment the counter
        ay: calculateAssessmentYear(itreturn?.assmentYear),
        filingType: getFilingType(itreturn?.filingTypeCd),
        itr: getFormType(itreturn?.formtypeCd),
        acknowledgeNum: itreturn?.ackNum,
        filingDate: formatDate(itreturn?.ackDt),
        everficationStatus: getverificationStatus(itreturn?.verStatus),
      };

      const row = worksheet.addRow(rowData);

      // Set custom cell colors for the 'e-Verification Status' column
      const everficationCell = row.getCell('everficationStatus');
      if (rowData.everficationStatus === 'Verified') {
        everficationCell.font = {
          color: { argb: 'FF00B050' },
          bold: true, //  green
        };
      } else if (rowData.everficationStatus === 'Not Verified') {
        everficationCell.font = {
          color: { argb: 'FFFF0000' },
          bold: true, // red
        };
      } else if (rowData.everficationStatus === 'NA') {
        everficationCell.font = {
          color: { argb: '000000' },
          bold: true, // BLACK
        };
      }
      worksheet.columns.forEach((column, colIndex) => {
        const headerLength = column.header?.length || 0;
        const cellLength = rowData[column.key]?.toString().length || 0;
        columnMaxLengths[colIndex] = Math.max(
          columnMaxLengths[colIndex] || 0,
          headerLength,
          cellLength,
        );
      });
    });

    worksheet.columns.forEach((column, colIndex) => {
      column.width = columnMaxLengths[colIndex] + 3;
    });

    const headerRow = worksheet.getRow(1);
    headerRow.font = { bold: true };
    headerRow.eachCell((cell) => {
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '64B5F6' },
      };
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' },
      };
      cell.alignment = { horizontal: 'center', vertical: 'middle' };
    });

    worksheet.eachRow((row) => {
      row.eachCell((cell) => {
        cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
      });
    });

    worksheet.views = [{ state: 'frozen', ySplit: 1 }];

    const buffer = await workbook.xlsx.writeBuffer();
    return buffer;
  }
  async getIncomeTaxDemands(userId: number, query: any) {
    try {
      const { limit, offset, section, assessmentYear, clientCategory, sortValue } = query;
      let user = await User.findOne({
        where: { id: userId },
        relations: ['organization', 'role'],
      });

      let ViewAll = user.role.permissions.find(
        (permission) => permission.slug === Permissions.VIEW_ALL_CLIENT_MANAGERS,
      );
      let ViewAssigned = user.role.permissions.find(
        (permission) => permission.slug === Permissions.VIEW_ASSIGNED_CLIENT_MANAGERS,
      );

      let demands = createQueryBuilder(AutOutstandingDemand, 'demands')
        .leftJoinAndSelect('demands.client', 'client')
        .leftJoin('client.clientManagers', 'clientManagers')
        .leftJoinAndSelect('client.autClientCredentials', 'autClientCredentials')
        .where('demands.organizationId = :id', { id: user?.organization?.id })
        .andWhere(
          new Brackets((qb) => {
            qb.where('demands.isInPortal != :portalStatus', { portalStatus: 'NO' }).orWhere(
              'demands.isInPortal IS NULL',
            );
          }),
        )
        .andWhere('client.status != :status', { status: UserStatus.DELETED })
        .andWhere('autClientCredentials.status != :disStatus', {
          disStatus: IncomeTaxStatus.DISABLE,
        });

      if (ViewAssigned && !ViewAll) {
        demands.andWhere('clientManagers.id = :userId', { userId });
      }

      const sort = typeof query?.sort === 'string' ? JSON.parse(query.sort) : query?.sort;
      if (sort?.column) {
        const columnMap: Record<string, string> = {
          assessmentYear: 'demands.assessmentYear',
          category: 'client.category',
          name: 'client.displayName',
          sectionCodeText: 'demands.sectionCodeText',
          demandRaisedDate: 'demands.demandDateMilli',
          currentStatus: 'demands.currentStatus',
          outstandingDemandAmount: 'demands.outstandingDemandAmount',
        };
        const column = columnMap[sort.column] || sort.column;
        demands.orderBy(column, sort.direction.toUpperCase());
      }
      if (query.search) {
        demands.andWhere(
          new Brackets((qb) => {
            qb.where('demands.pan LIKE :pansearch', {
              pansearch: `%${query.search}%`,
            });
            qb.orWhere('client.displayName LIKE :namesearch', {
              namesearch: `%${query.search}%`,
            });
          }),
        );
      }

      if (query.section) {
        demands.andWhere('demands.sectionCodeText like :search', { search: `%${query.section}%` });
      }

      if (query.assessmentYear) {
        if (query.assessmentYear === 'null') {
          demands.andWhere('demands.assessmentYear = 0');
        } else {
          demands.andWhere('demands.assessmentYear like :as', { as: `%${query.assessmentYear}%` });
        }
      }

      if (query.clientCategory) {
        demands.andWhere(`client.category = :clientCategory`, {
          clientCategory: query.clientCategory,
        });
      }

      demands.orderBy('demands.demandDateMilli', 'DESC');

      if (query.interval) {
        const now = Date.now();
        const interval = query.interval;

        if (interval === 'last15days') {
          // const last15days = new Date();
          // last15days.setDate(now.getDate() - 15);
          const last15days = now - 15 * 24 * 60 * 60 * 1000;
          demands.andWhere('CAST(`demands`.`demand_date_milli` AS SIGNED) >= :last15days', {
            last15days,
          });
        } else if (interval === 'last1month') {
          // const last1month = new Date();
          // last1month.setMonth(now.getMonth() - 1);
          const last1month = now - 30 * 24 * 60 * 60 * 1000;
          demands.andWhere('CAST(`demands`.`demand_date_milli` AS SIGNED) >= :last1month', {
            last1month,
          });
        } else if (interval === 'last1week') {
          // const last1week = new Date();
          // last1week.setDate(now.getDate() - 7);
          const last1week = now - 7 * 24 * 60 * 60 * 1000;
          demands.andWhere('CAST(`demands`.`demand_date_milli` AS SIGNED) >= :last1week', {
            last1week,
          });
        }
      }

      if (query.sortValue) {
        if (query.sortValue === 'AMOUNT_DESC') {
          demands.orderBy('demands.outstandingDemandAmount', 'DESC');
        } else if (query.sortValue === 'AMOUNT_ASC') {
          demands.orderBy('demands.outstandingDemandAmount');
        } else if (query.sortValue === 'DATE_NEWEST') {
          demands.orderBy('demands.demandDateMilli', 'DESC');
        } else if (query.sortValue === 'DATE_OLDEST') {
          demands.orderBy('demands.demandDateMilli', 'ASC');
        }
      }
      if (offset >= 0) {
        demands.skip(offset);
      }

      if (limit) {
        demands.take(limit);
      }

      let result = await demands.getManyAndCount();
      return {
        count: result[1],
        result: result[0],
      };
    } catch (error) {
      console.log('error occured while fetching getIncomeTaxDemands', error);
    }
  }

  async exportIncomeTaxDemands(userId: number, query: any) {
    const newQuery = { ...query, offset: 0, limit: ********* };
    let demands = await this.getIncomeTaxDemands(userId, newQuery);
    if (!demands.result.length) throw new BadRequestException('No Data for Export');
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Inc Tax Demands');
    const headers = [
      { header: 'S.No', key: 'serialNo' },
      { header: 'AY', key: 'ay' },
      { header: 'Category', key: 'category' },
      { header: 'Client Name', key: 'clientName' },
      { header: 'PAN', key: 'pan' },
      { header: 'Section', key: 'section' },
      { header: 'Date of Demand Raised', key: 'demandRaised' },
      { header: 'Outstanding Demand (₹)', key: 'outstandingDemand' },
      { header: 'Interest Start Date', key: 'interestStartDate' },
      { header: 'Interest Accrued (₹)', key: 'interestAccured' },
    ];

    worksheet.columns = headers;
    const columnMaxLengths = Array(headers.length).fill(0);
    let serialCounter = 1; // Initialize a counter
    demands.result.forEach((demand) => {
      const rowData = {
        serialNo: serialCounter++, // Assign and then increment the counter
        ay: calculateAssessmentYear(demand?.assessmentYear),
        category: categoryLabels[demand?.client?.category],
        clientName: demand?.client?.displayName,
        section: demand?.sectionCodeText,
        pan: demand?.pan,
        demandRaised: formatDate(demand?.demandRaisedDate),
        interestStartDate: formatDate(demand?.interestStartDate),
        //  'Interest Accrued Upto':"",
        interestAccured: 1 * demand?.accruedInterestComputed,
        outstandingDemand: 1 * demand?.outstandingDemandAmount,
      };

      const row = worksheet.addRow(rowData);

      worksheet.columns.forEach((column, colIndex) => {
        const headerLength = column.header?.length || 0;
        const cellLength = rowData[column.key]?.toString().length || 0;
        columnMaxLengths[colIndex] = Math.max(
          columnMaxLengths[colIndex] || 0,
          headerLength,
          cellLength,
        );
      });
    });

    worksheet.columns.forEach((column, colIndex) => {
      column.width = columnMaxLengths[colIndex] + 2;
    });

    const headerRow = worksheet.getRow(1);
    headerRow.font = { bold: true };
    headerRow.eachCell((cell) => {
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '64B5F6' },
      };
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' },
      };
      cell.alignment = { horizontal: 'center', vertical: 'middle' };
    });

    worksheet.columns.forEach((column) => {
      if (column.key === 'clientName') {
        column.width = 50;
        column.alignment = { wrapText: true, horizontal: 'center', vertical: 'middle' };
      } else {
        column.alignment = { horizontal: 'center', vertical: 'middle' };
      }
    });

    worksheet.eachRow((row) => {
      row.eachCell((cell) => {
        cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
      });
    });

    worksheet.views = [{ state: 'frozen', ySplit: 1 }];

    const buffer = await workbook.xlsx.writeBuffer();
    return buffer;
  }
  async findDemand(userId: number, id: number) {
    try {
      const user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
      // const incometaxDemands = await AutOutstandingDemand.findOne({
      //   where: { id, organizationId: user?.organization?.id },
      //   relations: ['client','autDemandResponse'],
      // });
      // return incometaxDemands;
      let latestDemandResponse = [];
      const incometaxDemands = await AutOutstandingDemand.findOne({
        where: { id, organizationId: user?.organization?.id },
        relations: ['client'],
      });
      const demandsResponse = await AutOutstandingDemand.findOne({
        where: { id, organizationId: user?.organization?.id },
        relations: ['autDemandResponse'],
      });
      if (demandsResponse?.autDemandResponse?.length > 0) {
        latestDemandResponse = [
          demandsResponse.autDemandResponse.sort(
            (a, b) => parseInt(b.lastUpdatedAtMs) - parseInt(a.lastUpdatedAtMs),
          )[0],
        ];
      }
      // console.log('...incometaxDemands,latestDemandResponse',{...incometaxDemands,latestDemandResponse})
      return { ...incometaxDemands, latestDemandResponse };
    } catch (error) {
      console.log('error occure while getting the findDemand');
    }
  }

  async getClientDemand(id: number, query: any, userId: number) {
    const user = await User.findOne({ where: { id: userId }, relations: ['organization'] });

    const { offset, limit } = query;
    try {
      const clientCredential = await AutClientCredentials.findOne({
        where: { id, organizationId: user?.organization?.id },
        relations: ['client'],
      });
      if (clientCredential) {
        const demandDetails = await createQueryBuilder(AutOutstandingDemand, 'autOutstandingDemand')
          .leftJoinAndSelect('autOutstandingDemand.client', 'client')
          .where('client.id =:id', { id: clientCredential?.client?.id })
          .andWhere(
            new Brackets((qb) => {
              qb.where('autOutstandingDemand.isInPortal != :portalStatus', {
                portalStatus: 'NO',
              }).orWhere('autOutstandingDemand.isInPortal IS NULL');
            }),
          )
          .andWhere('autOutstandingDemand.organizationId = :orgId', {
            orgId: user?.organization?.id,
          });
        const sort = typeof query?.sort === 'string' ? JSON.parse(query.sort) : query?.sort;
        if (sort?.column) {
          const columnMap: Record<string, string> = {
            assessmentYear: 'autOutstandingDemand.assessmentYear',
            demandRaisedDate: 'autOutstandingDemand.demandDateMilli',
            sectionCodeText: 'autOutstandingDemand.sectionCodeText',
            outstandingDemandAmount: 'autOutstandingDemand.outstandingDemandAmount',
          };
          const column = columnMap[sort.column] || sort.column;
          demandDetails.orderBy(column, sort.direction.toUpperCase());
        }
        if (offset) {
          demandDetails.skip(offset);
        }

        if (limit) {
          demandDetails.take(limit);
        }

        demandDetails.orderBy('autOutstandingDemand.demandDateMilli', 'DESC');

        let result = await demandDetails.getManyAndCount();
        return {
          count: result[1],
          result: result[0],
          accessDenied: true,
        };
      } else {
        return { accessDenied: false };
      }
    } catch (error) {
      console.log('error occured while fetching income tax client demands', error);
    }
  }

  async exportclientDemand(userId: number, query: any) {
    const newQuery = { ...query, offset: 0, limit: ********* };
    const id = query.incometaxid;
    let clientdemands = await this.getClientDemand(id, newQuery, userId);
    if (!clientdemands.result.length) throw new BadRequestException('No Data for Export');
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Inc Tax Demands');
    const headers = [
      { header: 'S.No', key: 'serialNo' },
      { header: 'AY', key: 'ay' },
      { header: 'Section', key: 'section' },
      { header: 'Date of Demand Raised', key: 'demandRaised' },
      { header: 'Outstanding Demand (₹)', key: 'outstandingDemand' },
      // { header: 'Original Demand (₹)', key: 'originalDemand' },
      { header: 'Interest Start Date', key: 'interestStartDate' },
      { header: 'Interest Accrued (₹)', key: 'interestAccured' },
    ];

    worksheet.columns = headers;
    const columnMaxLengths = Array(headers.length).fill(0);
    let serialCounter = 1; // Initialize a counter

    clientdemands.result.forEach((demand) => {
      const rowData = {
        serialNo: serialCounter++, // Assign and then increment the counter
        ay: calculateAssessmentYear(demand?.assessmentYear),

        section: demand?.sectionCodeText,
        demandRaised: formatDate(demand?.demandRaisedDate),
        outstandingDemand: 1 * demand?.outstandingDemandAmount || '0',
        //  originalDemand: ( demand?.originalOutstandingDemandAmount || '0'),
        interestStartDate: formatDate(demand?.interestStartDate),
        //  'Interest Accrued Upto':"",
        interestAccured: 1 * demand?.accruedInterestComputed || '0',
      };

      const row = worksheet.addRow(rowData);

      worksheet.columns.forEach((column, colIndex) => {
        const headerLength = column.header?.length || 0;
        const cellLength = rowData[column.key]?.toString().length || 0;
        columnMaxLengths[colIndex] = Math.max(
          columnMaxLengths[colIndex] || 0,
          headerLength,
          cellLength,
        );
      });
    });

    worksheet.columns.forEach((column, colIndex) => {
      column.width = columnMaxLengths[colIndex] + 2;
    });

    const headerRow = worksheet.getRow(1);
    headerRow.font = { bold: true };
    headerRow.eachCell((cell) => {
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '64B5F6' },
      };
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' },
      };
      cell.alignment = { horizontal: 'center', vertical: 'middle' };
    });

    worksheet.columns.forEach((column) => {
      if (column.key === 'clientName') {
        column.width = 50;
        column.alignment = { wrapText: true, horizontal: 'center', vertical: 'middle' };
      } else {
        column.alignment = { horizontal: 'center', vertical: 'middle' };
      }
    });

    worksheet.eachRow((row) => {
      row.eachCell((cell) => {
        cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
      });
    });

    worksheet.views = [{ state: 'frozen', ySplit: 1 }];

    const buffer = await workbook.xlsx.writeBuffer();
    return buffer;
  }

  async getIncomeTaxEproceedings(userId: number, query: any) {
    try {
      const { limit, offset } = query;
      let user = await User.findOne({
        where: { id: userId },
        relations: ['organization', 'role'],
      });
      let ViewAll = user.role.permissions.find(
        (permission) => permission.slug === Permissions.VIEW_ALL_CLIENT_MANAGERS,
      );
      let ViewAssigned = user.role.permissions.find(
        (permission) => permission.slug === Permissions.VIEW_ASSIGNED_CLIENT_MANAGERS,
      );

      let epro = createQueryBuilder(AutEProceedingFya, 'autEProceedingFya')
        .leftJoinAndSelect('autEProceedingFya.client', 'client')
        .leftJoin('client.clientManagers', 'clientManagers')
        .leftJoinAndSelect('autEProceedingFya.notices', 'notice')
        .leftJoinAndSelect('client.autClientCredentials', 'autClientCredentials')
        .where('autEProceedingFya.organizationId = :id', { id: user?.organization?.id })
        .andWhere('client.status != :status', { status: UserStatus.DELETED })
        .andWhere('autClientCredentials.status != :disStatus', {
          disStatus: IncomeTaxStatus.DISABLE,
        });

      if (ViewAssigned && !ViewAll) {
        epro.andWhere('clientManagers.id = :userId', { userId });
      }

      if (query.search) {
        epro.andWhere(
          new Brackets((qb) => {
            qb.where('autEProceedingFya.pan LIKE :pansearch', {
              pansearch: `%${query.search}%`,
            });
            qb.orWhere('client.displayName LIKE :namesearch', {
              namesearch: `%${query.search}%`,
            });
            qb.orWhere('autEProceedingFya.proceedingName LIKE :prosearch', {
              prosearch: `%${query.search}%`,
            });
          }),
        );
      }

      if (query.section) {
        if (query.section === 'null') {
          epro.andWhere('autEProceedingFya.noticeName is NULL');
        } else {
          epro.andWhere('autEProceedingFya.noticeName like :sectionsearch', {
            sectionsearch: `%${query.section}%`,
          });
        }
      }

      if (query?.type) {
        epro.andWhere('autEProceedingFya.type like :type', {
          type: query?.type,
        });
      }

      if (query.assessmentYear) {
        if (query.assessmentYear === 'null') {
          epro.andWhere('autEProceedingFya.assesmentYear = 0');
        } else {
          epro.andWhere('autEProceedingFya.assesmentYear like :as', {
            as: `%${query.assessmentYear}%`,
          });
        }
      }

      if (query.clientCategory) {
        epro.andWhere(`client.category = :clientCategory`, {
          clientCategory: query.clientCategory,
        });
      }

      if (offset) {
        epro.skip(offset);
      }

      if (limit) {
        epro.take(limit);
      }

      epro.orderBy('autEProceedingFya.assesmentYear', 'DESC');

      let result = await epro.getManyAndCount();
      return {
        count: result[1],
        result: result[0],
      };
    } catch (error) {
      console.log('error occured while fetching getIncomeTaxEproceedings', error);
    }
  }

  async exportIncomeTaxEproceedings(userId: number, query: any) {
    const newQuery = { ...query, offset: 0, limit: ********* };
    let fyiactions = await this.getIncomeTaxEproceedings(userId, newQuery);
    if (!fyiactions.result.length) throw new BadRequestException('No Data for Export');
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('e-Proceedings (FYA)');
    const headers = [
      { header: 'S.No', key: 'serialNo' },
      { header: 'AY', key: 'ay' },
      { header: 'Type', key: 'type' },
      { header: 'Category', key: 'category' },
      { header: 'Client Name', key: 'clientName' },
      { header: 'PAN', key: 'pan' },
      { header: 'Section', key: 'section' },
      { header: 'Proceeding Name', key: 'proceedingName' },
    ];

    worksheet.columns = headers;
    const columnMaxLengths = Array(headers.length).fill(0);
    let serialCounter = 1; // Initialize a counter
    fyiactions.result.forEach((fyiaction) => {
      const rowData = {
        serialNo: serialCounter++, // Assign and then increment the counter
        ay: generateAssessmentYear(fyiaction?.assesmentYear),
        type: fyiaction?.type,
        category: categoryLabels[fyiaction?.client?.category],
        clientName: fyiaction?.client?.displayName,
        pan: fyiaction?.pan,
        section: fyiaction?.noticeName || ' ',
        proceedingName: fyiaction?.proceedingName,
      };

      const row = worksheet.addRow(rowData);

      // Apply conditional coloring for the "Type" column
      const typeCell = row.getCell('type'); // Get the cell for the "Type" column
      if (rowData.type === 'Self') {
        typeCell.font = {
          color: { argb: '4B0082' },
          bold: true, // Bold text
        };
      } else if (rowData.type === 'Other') {
        typeCell.font = {
          color: { argb: 'FF8C00' },
          bold: true, // Bold text
        };
      }

      worksheet.columns.forEach((column, colIndex) => {
        const headerLength = column.header?.length || 0;
        const cellLength = rowData[column.key]?.toString().length || 0;
        columnMaxLengths[colIndex] = Math.max(
          columnMaxLengths[colIndex] || 0,
          headerLength,
          cellLength,
        );
      });
    });

    worksheet.columns.forEach((column, colIndex) => {
      column.width = columnMaxLengths[colIndex] + 2;
    });

    const headerRow = worksheet.getRow(1);
    headerRow.font = { bold: true };
    headerRow.eachCell((cell) => {
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '64B5F6' },
      };
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' },
      };
      cell.alignment = { horizontal: 'center', vertical: 'middle' };
    });

    worksheet.columns.forEach((column) => {
      if (column.key === 'clientName') {
        column.width = 50;
        column.alignment = { wrapText: true, horizontal: 'center', vertical: 'middle' };
      } else {
        column.alignment = { horizontal: 'center', vertical: 'middle' };
      }
    });

    worksheet.eachRow((row) => {
      row.eachCell((cell) => {
        cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
      });
    });

    worksheet.views = [{ state: 'frozen', ySplit: 1 }];

    const buffer = await workbook.xlsx.writeBuffer();
    return buffer;
  }

  async getIncomeTaxFyiEproceedings(userId: number, query: any) {
    try {
      const { limit, offset } = query;
      let user = await User.findOne({
        where: { id: userId },
        relations: ['organization', 'role'],
      });
      let ViewAll = user.role.permissions.find(
        (permission) => permission.slug === Permissions.VIEW_ALL_CLIENT_MANAGERS,
      );
      let ViewAssigned = user.role.permissions.find(
        (permission) => permission.slug === Permissions.VIEW_ASSIGNED_CLIENT_MANAGERS,
      );

      let epro = createQueryBuilder(AutEProceedingFyi, 'autEProceedingFyi')
        .leftJoinAndSelect('autEProceedingFyi.client', 'client')
        .leftJoin('client.clientManagers', 'clientManagers')
        .leftJoinAndSelect('autEProceedingFyi.notices', 'notice')
        .leftJoinAndSelect('client.autClientCredentials', 'autClientCredentials')
        .where('autEProceedingFyi.organizationId = :id', { id: user?.organization?.id })
        .andWhere('client.status != :status', { status: UserStatus.DELETED })
        .andWhere('autClientCredentials.status != :disStatus', {
          disStatus: IncomeTaxStatus.DISABLE,
        });
      if (ViewAssigned && !ViewAll) {
        epro.andWhere('clientManagers.id = :userId', { userId });
      }

      if (query.search) {
        epro.andWhere(
          new Brackets((qb) => {
            qb.where('autEProceedingFyi.pan LIKE :pansearch', {
              pansearch: `%${query.search}%`,
            });
            qb.orWhere('client.displayName LIKE :namesearch', {
              namesearch: `%${query.search}%`,
            });
            qb.orWhere('autEProceedingFyi.proceedingName LIKE :prosearch', {
              prosearch: `%${query.search}%`,
            });
          }),
        );
      }

      if (query.type) {
        epro.andWhere('autEProceedingFyi.type like :type', {
          type: query?.type,
        });
      }

      if (query.section) {
        if (query.section === 'null') {
          epro.andWhere('autEProceedingFyi.noticeName is NUll');
        } else {
          epro.andWhere('autEProceedingFyi.noticeName like :sectionsearch', {
            sectionsearch: `%${query.section}%`,
          });
        }
      }

      if (query.assessmentYear) {
        if (query.assessmentYear === 'null') {
          epro.andWhere('autEProceedingFyi.assessmentYear = 0');
        } else {
          epro.andWhere('autEProceedingFyi.assessmentYear like :as', {
            as: `%${query.assessmentYear}%`,
          });
        }
      }

      if (query.clientCategory) {
        epro.andWhere(`client.category = :clientCategory`, {
          clientCategory: query.clientCategory,
        });
      }
      epro.orderBy('autEProceedingFyi.assessmentYear', 'DESC');

      if (offset) {
        epro.skip(offset);
      }

      if (limit) {
        epro.take(limit);
      }
      let result = await epro.getManyAndCount();
      return {
        count: result[1],
        result: result[0],
      };
    } catch (error) {
      console.log('error occured while fetching getIncomeTaxFyiEproceedings', error);
    }
  }
  async exportIncomeTaxFyiEproceedings(userId: number, query: any) {
    const newQuery = { ...query, offset: 0, limit: ********* };
    let fyis = await this.getIncomeTaxFyiEproceedings(userId, newQuery);
    if (!fyis.result.length) throw new BadRequestException('No Data for Export');
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('e-Proceedings (FYI)');
    const headers = [
      { header: 'S.No', key: 'serialNo' },
      { header: 'AY', key: 'ay' },
      { header: 'Type', key: 'type' },
      { header: 'Category', key: 'category' },
      { header: 'Client Name', key: 'clientName' },
      { header: 'PAN', key: 'pan' },
      { header: 'Section', key: 'section' },
      { header: 'Proceeding Name', key: 'proceedingName' },
    ];

    worksheet.columns = headers;
    const columnMaxLengths = Array(headers.length).fill(0);
    let serialCounter = 1; // Initialize a counter

    fyis.result.forEach((fyiaction) => {
      const rowData = {
        serialNo: serialCounter++, // Assign and then increment the counter
        ay: calculateAssessmentYear(fyiaction?.assessmentYear),
        type: fyiaction?.type,
        category: categoryLabels[fyiaction?.client?.category],
        clientName: fyiaction?.client?.displayName,
        pan: fyiaction?.pan,
        section: fyiaction?.noticeName || ' ',
        proceedingName: fyiaction?.proceedingName,
      };

      const row = worksheet.addRow(rowData);
      // Apply conditional coloring for the "Type" column
      const typeCell = row.getCell('type'); // Get the cell for the "Type" column
      if (rowData.type === 'Self') {
        typeCell.font = {
          color: { argb: '4B0082' },
          bold: true, // Bold text
        };
      } else if (rowData.type === 'Other') {
        typeCell.font = {
          color: { argb: 'FF8C00' },
          bold: true, // Bold text
        };
      }

      worksheet.columns.forEach((column, colIndex) => {
        const headerLength = column.header?.length || 0;
        const cellLength = rowData[column.key]?.toString().length || 0;
        columnMaxLengths[colIndex] = Math.max(
          columnMaxLengths[colIndex] || 0,
          headerLength,
          cellLength,
        );
      });
    });

    worksheet.columns.forEach((column, colIndex) => {
      column.width = columnMaxLengths[colIndex] + 2;
    });

    const headerRow = worksheet.getRow(1);
    headerRow.font = { bold: true };
    headerRow.eachCell((cell) => {
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '64B5F6' },
      };
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' },
      };
      cell.alignment = { horizontal: 'center', vertical: 'middle' };
    });

    worksheet.columns.forEach((column) => {
      if (column.key === 'clientName') {
        column.width = 50;
        column.alignment = { wrapText: true, horizontal: 'center', vertical: 'middle' };
      } else {
        column.alignment = { horizontal: 'center', vertical: 'middle' };
      }
    });

    worksheet.eachRow((row) => {
      row.eachCell((cell) => {
        cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
      });
    });

    worksheet.views = [{ state: 'frozen', ySplit: 1 }];

    const buffer = await workbook.xlsx.writeBuffer();
    return buffer;
  }
  async findEproceeding(userId: number, id: number) {
    try {
      let user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
      const queryBuilder = await createQueryBuilder(AutEProceedingFya, 'eproceeding')
        .leftJoinAndSelect('eproceeding.client', 'client')
        .leftJoinAndSelect('client.autProfileDetails', 'autProfileDetails')
        .leftJoinAndSelect('eproceeding.notices', 'notices')
        .leftJoinAndSelect('notices.responses', 'responses')
        .where('eproceeding.id = :id', { id })
        .andWhere('eproceeding.organizationId = :orgId', { orgId: user?.organization?.id });
      const incometaxEpro = await queryBuilder.getOne();
      return incometaxEpro;
    } catch (error) {
      console.log('error occure while fetching findEproceeding', error);
    }
  }

  async findEproceedingFyi(userId: number, id: number) {
    try {
      let user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
      const queryBuilder = await createQueryBuilder(AutEProceedingFyi, 'eproceeding')
        .leftJoinAndSelect('eproceeding.client', 'client')
        .leftJoinAndSelect('eproceeding.notices', 'notices')
        .leftJoinAndSelect('notices.responses', 'responses')
        .where('eproceeding.id = :id', { id })
        .andWhere('eproceeding.organizationId = :orgId', { orgId: user?.organization?.id });
      const incometaxEpro = await queryBuilder.getOne();
      return incometaxEpro;
    } catch (error) {
      console.log('error occure while fetching findEproceedingFyi', error);
    }
  }

  async getFyaNotice(userId, id) {
    try {
      const user = await User.findOne({ where: { id: userId }, relations: ['organization'] });

      const queryBuilder = await createQueryBuilder(AutFyaNotice, 'autFyaNotice')
        .select([
          // All required columns from AutFyaNotice
          'autFyaNotice.id',
          'autFyaNotice.pan',
          'autFyaNotice.proceedingReqId',
          'autFyaNotice.nameOfAssesse',
          'autFyaNotice.eProceedingId',
          'autFyaNotice.documentIdentificationNumber',
          'autFyaNotice.proceedingName',
          'autFyaNotice.itrType',
          'autFyaNotice.assesmentYear',
          'autFyaNotice.financialYear',
          'autFyaNotice.noticeSection',
          'autFyaNotice.description',
          'autFyaNotice.issuedOn',
          'autFyaNotice.responseDueDate',
          'autFyaNotice.manualDueDate',
          'autFyaNotice.lastResponseSubmittedOn',
          'autFyaNotice.documentReferenceId',
          'autFyaNotice.noticeAttatchments',
          'autFyaNotice.noticeLetters',
          'autFyaNotice.remark',
          'autFyaNotice.remarkSubmittedOn',
          'autFyaNotice.respType',
          'autFyaNotice.organizationId',
          'autFyaNotice.createdType',
          'autFyaNotice.storageSystem',

          // Related table `id` fields only
          'client.id',
          'client.displayName',
          'eProceeding.id',
          'responses.id',
          'responses.responseType',
          'responses.attachments',
          'responses.remarks',
          'responses.submittedOn',
          'responses.createdType',
          'responses.storageSystem',
          // 'storage.id',
          // 'storage.name',
          // 'storage.webUrl',
          // 'storage.storageSystem',

          // 'resStorage.id',
          // 'resStorage.name',
          // 'resStorage.webUrl',
          // 'resStorage.storageSystem',
        ])
        .leftJoin('autFyaNotice.client', 'client')
        .leftJoin('autFyaNotice.responses', 'responses')
        .leftJoin('autFyaNotice.eProceeding', 'eProceeding')
        .leftJoinAndSelect('autFyaNotice.storage', 'storage')
        .leftJoinAndSelect('responses.storage', 'resStorage')
        .where('autFyaNotice.id = :id', { id })
        .andWhere('autFyaNotice.organizationId = :orgId', { orgId: user?.organization?.id });
      const notice = await queryBuilder.getOne();

      // const notice = await AutFyaNotice.findOne({
      //   where: { id, organizationId: user?.organization?.id },
      //   relations: ['client', 'responses', 'eProceeding', 'storage', 'responses.storage'],
      // });
      return notice;
    } catch (error) {
      console.log('error occure while fetching getFyaNotice', error);
    }
  }

  async getFyiNotice(userId: number, id: number) {
    try {
      const user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
      const notice = await AutFyiNotice.findOne({
        where: { id, organizationId: user?.organization?.id },
        relations: ['client', 'responses', 'eProceeding'],
      });
      return notice;
    } catch (error) {
      console.log('error occure while fetching getFyiNotice', error);
    }
  }

  async getClientEproceeding(id: number) {
    try {
      const clientCredential = await AutClientCredentials.findOne({
        where: { id },
        relations: ['client'],
      });
      if (clientCredential) {
        const eproDetails = await AutEProceeding.find({
          where: { client: clientCredential?.client },
        });
        return eproDetails;
      }
    } catch (error) {
      console.log('error occured while fetching income tax client demands', error);
    }
  }
  ////////123
  async getClientProceedingFya(id: number, query: any, userId: number) {
    const { limit, offset } = query;
    const user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
    try {
      const clientCredential = await AutClientCredentials.findOne({
        where: { id, organizationId: user?.organization?.id },
        relations: ['client'],
      });
      if (clientCredential) {
        const clientEpro = createQueryBuilder(AutEProceedingFya, 'autEProceedingFya')
          .leftJoinAndSelect('autEProceedingFya.client', 'client')
          .leftJoinAndSelect('autEProceedingFya.notices', 'notice')
          .where('client.id =:id', { id: clientCredential?.client?.id });
        clientEpro.orderBy('autEProceedingFya.assesmentYear', 'DESC');

        if (query.type) {
          clientEpro.andWhere('autEProceedingFya.type LIKE :eProType', {
            eProType: query?.type,
          });
        }

        if (query.search) {
          clientEpro.andWhere('autEProceedingFya.proceedingName LIKE :prosearch', {
            prosearch: `%${query.search}%`,
          });
        }

        if (query.section) {
          if (query.section === 'null') {
            clientEpro.andWhere('autEProceedingFya.noticeName is NULL');
          } else {
            clientEpro.andWhere('autEProceedingFya.noticeName like :sectionsearch', {
              sectionsearch: `%${query.section}%`,
            });
          }
        }

        if (query.assessmentYear) {
          if (query.assessmentYear === 'null') {
            clientEpro.andWhere('autEProceedingFya.assesmentYear = 0');
          } else {
            clientEpro.andWhere('autEProceedingFya.assesmentYear like :as', {
              as: `%${query.assessmentYear}%`,
            });
          }
        }

        if (offset >= 0) {
          clientEpro.skip(offset);
        }

        if (limit) {
          clientEpro.take(limit);
        }

        let result = await clientEpro.getManyAndCount();
        return {
          count: result[1],
          result: result[0],
          accessDenied: true,
        };
      } else {
        return { accessDenied: false };
      }
    } catch (error) {
      console.log('error occured while fetching income tax client demands', error);
    }
  }

  async exportClientproceedingFya(userId: number, query: any) {
    const newQuery = { ...query, offset: 0, limit: ********* };
    const id = query.incometaxid;
    let fyas = await this.getClientProceedingFya(id, newQuery, userId);
    if (!fyas.result.length) throw new BadRequestException('No Data for Export');
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('e-Proceedings (FYA)');
    const headers = [
      { header: 'S.No', key: 'serialNo' },
      { header: 'AY', key: 'ay' },
      { header: 'Type', key: 'type' },
      { header: 'Section', key: 'section' },
      { header: 'Proceeding Name', key: 'proceedingName' },
    ];

    worksheet.columns = headers;
    const columnMaxLengths = Array(headers.length).fill(0);
    let serialCounter = 1; // Initialize a counter

    fyas.result.forEach((fyiaction) => {
      const rowData = {
        serialNo: serialCounter++, // Assign and then increment the counter
        ay: generateAssessmentYear(fyiaction?.assesmentYear),
        type: fyiaction?.type,
        section: fyiaction?.noticeName || ' ',
        proceedingName: fyiaction?.proceedingName,
      };

      const row = worksheet.addRow(rowData);
      // Apply conditional coloring for the "Type" column
      const typeCell = row.getCell('type'); // Get the cell for the "Type" column
      if (rowData.type === 'Self') {
        typeCell.font = {
          color: { argb: '4B0082' },
          bold: true, // Bold text
        };
      } else if (rowData.type === 'Other') {
        typeCell.font = {
          color: { argb: 'FF8C00' },
          bold: true, // Bold text
        };
      }

      worksheet.columns.forEach((column, colIndex) => {
        const headerLength = column.header?.length || 0;
        const cellLength = rowData[column.key]?.toString().length || 0;
        columnMaxLengths[colIndex] = Math.max(
          columnMaxLengths[colIndex] || 0,
          headerLength,
          cellLength,
        );
      });
    });

    worksheet.columns.forEach((column, colIndex) => {
      column.width = columnMaxLengths[colIndex] + 2;
    });

    const headerRow = worksheet.getRow(1);
    headerRow.font = { bold: true };
    headerRow.eachCell((cell) => {
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '64B5F6' },
      };
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' },
      };
      cell.alignment = { horizontal: 'center', vertical: 'middle' };
    });

    worksheet.eachRow((row) => {
      row.eachCell((cell) => {
        cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
      });
    });

    worksheet.views = [{ state: 'frozen', ySplit: 1 }];

    const buffer = await workbook.xlsx.writeBuffer();
    return buffer;
  }

  async getFyaSections(userId: number) {
    try {
      let user = await User.findOne({
        where: { id: userId },
        relations: ['organization'],
      });

      const sectionsData = await createQueryBuilder(AutEProceedingFya, 'autEProceedingFya')
        .select('autEProceedingFya.noticeName', 'noticeName')
        .where('autEProceedingFya.organizationId = :id', { id: user.organization.id })
        .groupBy('autEProceedingFya.noticeName')
        .getRawMany();

      const filteredSections = sectionsData
        .map((section) => section.noticeName)
        .filter((section) => section !== '' && section !== null);

      return filteredSections;
    } catch (error) {
      console.error('Error occurred while fetching sections:', error);
      throw error;
    }
  }

  async getFyiSections(userId: number) {
    try {
      let user = await User.findOne({
        where: { id: userId },
        relations: ['organization'],
      });

      const sectionsData = await createQueryBuilder(AutEProceedingFyi, 'autEProceedingFyi')
        .select('autEProceedingFyi.noticeName', 'noticeName')
        .where('autEProceedingFyi.organizationId = :id', { id: user.organization.id })
        .groupBy('autEProceedingFyi.noticeName')
        .getRawMany();

      const filteredSections = sectionsData
        .map((section) => section.noticeName)
        .filter((section) => section !== '' && section !== null);

      return filteredSections;
    } catch (error) {
      console.error('Error occurred while fetching sections:', error);
      throw error;
    }
  }

  async getDemandsSections(userId: number) {
    try {
      let user = await User.findOne({
        where: { id: userId },
        relations: ['organization'],
      });

      const sectionsData = await createQueryBuilder(AutOutstandingDemand, 'autOutstandingDemand')
        .select('autOutstandingDemand.sectionCodeText', 'sectionCodeText')
        .where('autOutstandingDemand.organizationId = :id', { id: user.organization.id })
        .groupBy('autOutstandingDemand.sectionCodeText')
        .getRawMany();

      const filteredSections = sectionsData
        .map((section) => section.sectionCodeText)
        .filter((section) => section !== '' && section !== null);

      return filteredSections;
    } catch (error) {
      console.error('Error occurred while fetching sections:', error);
      throw error;
    }
  }

  //123
  async getClientProceedingFyi(id: number, query: any, userId: number) {
    const user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
    const { limit, offset } = query;
    try {
      const clientCredential = await AutClientCredentials.findOne({
        where: { id, organizationId: user?.organization?.id },
        relations: ['client'],
      });
      if (clientCredential) {
        let clientEpro = createQueryBuilder(AutEProceedingFyi, 'autEProceedingFyi')
          .leftJoinAndSelect('autEProceedingFyi.client', 'client')
          .leftJoinAndSelect('autEProceedingFyi.notices', 'notice')
          .where('client.id = :id', { id: clientCredential?.client?.id });
        clientEpro.orderBy('autEProceedingFyi.assessmentYear', 'DESC');

        if (query.type) {
          clientEpro.andWhere('autEProceedingFyi.type LIKE :eProType', {
            eProType: query?.type,
          });
        }

        if (query.search) {
          clientEpro.andWhere('autEProceedingFyi.proceedingName LIKE :prosearch', {
            prosearch: `%${query.search}%`,
          });
        }

        if (query.section) {
          if (query.section === 'null') {
            clientEpro.andWhere('autEProceedingFyi.noticeName is NULL');
          } else {
            clientEpro.andWhere('autEProceedingFyi.noticeName like :sectionsearch', {
              sectionsearch: `%${query.section}%`,
            });
          }
        }

        if (query.assessmentYear) {
          if (query.assessmentYear === 'null') {
            clientEpro.andWhere('autEProceedingFyi.assessmentYear = 0');
          } else {
            clientEpro.andWhere('autEProceedingFyi.assessmentYear like :as', {
              as: `%${query.assessmentYear}%`,
            });
          }
        }

        if (offset >= 0) {
          clientEpro.skip(offset);
        }

        if (limit) {
          clientEpro.take(limit);
        }

        // epro.orderBy('autEProceedingFya.id', 'DESC');

        let result = await clientEpro.getManyAndCount();
        return {
          count: result[1],
          result: result[0],
          accessDenied: true,
        };
      } else {
        return { accessDenied: false };
      }
    } catch (error) {
      console.log('Error occurred while fetching income tax client demands', error);
    }
  }

  async exportClientproceedingFyi(userId: number, query: any) {
    const newQuery = { ...query, offset: 0, limit: ********* };
    const id = query.incometaxid;
    let fyis = await this.getClientProceedingFyi(id, newQuery, userId);
    if (!fyis.result.length) throw new BadRequestException('No Data for Export');
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('e-Proceedings (FYI)');
    const headers = [
      { header: 'S.No', key: 'serialNo' },
      { header: 'AY', key: 'ay' },
      { header: 'Type', key: 'type' },
      { header: 'Section', key: 'section' },
      { header: 'Proceeding Name', key: 'proceedingName' },
    ];

    worksheet.columns = headers;
    const columnMaxLengths = Array(headers.length).fill(0);
    let serialCounter = 1; // Initialize a counter

    fyis.result.forEach((fyiaction) => {
      const rowData = {
        serialNo: serialCounter++, // Assign and then increment the counter
        ay: calculateAssessmentYear(fyiaction?.assessmentYear),
        type: fyiaction?.type,
        section: fyiaction?.noticeName || ' ',
        proceedingName: fyiaction?.proceedingName,
      };

      const row = worksheet.addRow(rowData);

      // Apply conditional coloring for the "Type" column
      const typeCell = row.getCell('type'); // Get the cell for the "Type" column
      if (rowData.type === 'Self') {
        typeCell.font = {
          color: { argb: '4B0082' },
          bold: true, // Bold text
        };
      } else if (rowData.type === 'Other') {
        typeCell.font = {
          color: { argb: 'FF8C00' },
          bold: true, // Bold text
        };
      }
      worksheet.columns.forEach((column, colIndex) => {
        const headerLength = column.header?.length || 0;
        const cellLength = rowData[column.key]?.toString().length || 0;
        columnMaxLengths[colIndex] = Math.max(
          columnMaxLengths[colIndex] || 0,
          headerLength,
          cellLength,
        );
      });
    });

    worksheet.columns.forEach((column, colIndex) => {
      column.width = columnMaxLengths[colIndex] + 2;
    });

    const headerRow = worksheet.getRow(1);
    headerRow.font = { bold: true };
    headerRow.eachCell((cell) => {
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '64B5F6' },
      };
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' },
      };
      cell.alignment = { horizontal: 'center', vertical: 'middle' };
    });

    worksheet.eachRow((row) => {
      row.eachCell((cell) => {
        cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
      });
    });

    worksheet.views = [{ state: 'frozen', ySplit: 1 }];

    const buffer = await workbook.xlsx.writeBuffer();
    return buffer;
  }
  async getAuthToken() {
    const axios = require('axios');
    let data = JSON.stringify({
      entity: '',
      serviceName: 'wLoginService',
    });

    let config = {
      method: 'post',
      maxBodyLength: Infinity,
      url: 'https://eportal.incometax.gov.in/iec/loginapi/login',
      headers: {
        'Content-Type': 'application/json',
        'Cookie':
          '4a75cee7266fb5ae654dc5e51e6a9fe3=d572fbc9cf9510b71280bde0385ff167; 83b39a8b4ea14550011a0e5e6ca7f4cc=469b38b021805b99b052a60d9c64a00c',
      },
      data: data,
    };

    axios
      .request(config)
      .then((response) => {
        // console.log(response);
        const abc = response.data;
        // console.log('bbbbbbbbbbbbbbbbbbbbbbbbbbbbb', response.headers);
        // let data1: any = {
        //   aadhaarMobileValidated: `${abc.aadhaarMobileValidated}`,
        //   contactEmail: null,
        //   contactMobile: null,
        //   contactPan: null,
        //   dtoService: `${abc.dtoService}`,
        //   email: null,
        //   entity: `${abc.entity}`,
        //   entityType: `${abc.entityType}`,
        //   errors: [],
        //   exemptedPan: `${abc.exemptedPan}`,
        //   forgnDirEmailId: null,
        //   imagePath: null,
        //   imgByte: null,
        //   mobileNo: null,
        //   otp: null,
        //   otpGenerationFlag: null,
        //   otpSourceFlag: null,
        //   otpValdtnFlg: null,
        //   pass: 'bWFub2prYWxpeWExKg==',
        //   passValdtnFlg: null,
        //   reqId: `${abc.reqId}`,
        //   role: `${abc.role}`,
        //   secAccssMsg: '',
        //   secLoginOptions: '',
        //   serviceName: 'loginService',
        //   uidValdtnFlg: `${abc.uidValdtnFlg}`,
        //   userConsent: `${abc.userConsent}`,
        // };
        // console.log({ data1 });
        // let config1 = {
        //   method: 'post',
        //   maxBodyLength: Infinity,
        //   url: 'https://eportal.incometax.gov.in/iec/loginapi/login',
        //   headers: {
        //     'Content-Type': 'application/json',
        //     'Origin': 'https://eportal.incometax.gov.in',
        //     'Referer': 'https://eportal.incometax.gov.in/iec/foservices/',
        //   },
        //   data: data1,
        // };

        // axios
        //   .request(config1)
        //   .then((response) => {
        //     console.log(JSON.stringify(response.data));
        //   })
        //   .catch((error) => {
        //     console.log(error);
        //   });
      })
      .catch((error) => {
        console.log(error);
      });
  }

  async updateClientAutCredentials(id: any, body: any, userId) {
    try {
      const clientCredential = await AutClientCredentials.findOne({
        where: { id },
        relations: ['client'],
      });
      if (clientCredential.passwordId) {
        const password = await Password.findOne({
          where: { id: clientCredential.passwordId },
          relations: ['client'],
        });
        password.password = body.password;
        password['userId'] = userId;
        await password.save();
      }
      if (clientCredential) {
        clientCredential.password = body.password;
        await clientCredential.save();
        return clientCredential;
      }
    } catch (error) {
      console.log('Error occur while update the incomeTax client credentials', error);
    }
  }

  async sendActivityData(userId: number, body: any) {
    try {
      let user = await User.findOne({
        where: { id: userId },
      });
      const autoClient = await AutClientCredentials.findOne({
        where: { id: body.id },
        relations: ['client'],
      });

      const moduleArray = body.selectedModules;
      const moduleObject = {
        P: 'Basic Profile',
        F: 'Forms',
        R: 'Returns',
        OD: 'Outstanding Demand',
        EP: 'e-Proceedings',
      };
      const matchedValues = moduleArray?.map((item: string) => moduleObject[item] || 'Not Found');
      const autActivity = new AutActivity();
      autActivity.modules = body.selectedModules;
      autActivity.action = `DATA_SYNCHRONIZATION`;
      autActivity.userId = userId;
      autActivity.clientId = autoClient?.client?.id;
      autActivity.autClientCredentialsId = body.id;
      autActivity.remarks = `${user?.fullName
        } synchronizes client data for specific modules of ${matchedValues.join(', ')}`;
      autActivity.save();
    } catch (error) {
      console.log('Error occurred while sendActivityData', error);
    }
  }

  async getActivityLogData(id: any, query: any, userId: number) {
    try {
      const user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
      const clientCredential = await AutClientCredentials.findOne({
        where: { id, organizationId: user?.organization?.id },
        relations: ['client'],
      });
      if (clientCredential) {
        const autActivity = await createQueryBuilder(AutomationMachines, 'autActivity')
          .leftJoinAndSelect('autActivity.user', 'user')
          .where('autActivity.autoCredentials =:id', { id: id });

        if (query.fromDate && query.toDate) {
          const { startTime, endTime } = dateFormation(query.fromDate, query.toDate);
          autActivity
            .andWhere('Date(autActivity.created_at) >= :startTime', { startTime })
            .andWhere('Date(autActivity.created_at) <= :endTime', { endTime });
        }

        autActivity.orderBy('autActivity.id', 'DESC');
        let result = await autActivity.getMany();
        return { result, accessDenied: true };
      } else {
        return { accessDenied: false };
      }
    } catch (error) {
      console.error('Error fetching activity log data:', error);
      return null; // Or throw an error
    }
  }

  async getActivityArchiveLogData(id: any, query: any, userId: number) {
    try {
      const user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
      const clientCredential = await AutClientCredentials.findOne({
        where: { id, organizationId: user?.organization?.id },
        relations: ['client'],
      });
      if (clientCredential) {
        const autActivity = await createQueryBuilder(AutomationMachinesArchive, 'autActivity')
          .leftJoinAndSelect('autActivity.user', 'user')
          .where('autActivity.autoCredentials =:id', { id: id });

        if (query.fromDate && query.toDate) {
          const { startTime, endTime } = dateFormation(query.fromDate, query.toDate);
          autActivity
            .andWhere('Date(autActivity.created_at) >= :startTime', { startTime })
            .andWhere('Date(autActivity.created_at) <= :endTime', { endTime });
        }

        autActivity.orderBy('autActivity.id', 'DESC');
        let result = await autActivity.getMany();
        return { result, accessDenied: true };
      } else {
        return { accessDenied: false };
      }
    } catch (error) {
      console.error('Error fetching activity log data:', error);
      return null; // Or throw an error
    }
  }

  async checkAutomationInOrganization(userId: any) {
    try {
      let user = await User.findOne({
        where: { id: userId },
        relations: ['organization'],
      });
      // const automation = await AutomationMachines.find({ relations: ['autoCredentials'] });
      const sectionsData = await createQueryBuilder(AutomationMachines, 'automationMachines')
        .leftJoin('automationMachines.autoCredentials', 'autoCredentials')
        .where('autoCredentials.organizationId = :id', { id: user.organization.id })
        .andWhere('automationMachines.status = :status', { status: 'PENDING' })
        .getCount();
      return sectionsData;
    } catch (error) {
      console.error('Error fetching checkAutomationInOrganization', error);
    }
  }

  async importCredentialsss(userId: number, data: any) {
    try {
      if (!data.length) {
        throw new BadRequestException('No Data Available');
      }

      let errorsArray = [];
      let incomeTaxNewAdded = 0;
      let gstNewAdded = 0;
      let tanNewAdded = 0;
      let isIncomeTaxClientLimitReach = false;
      let isGstrClientLimitReach = false;
      let isTanClientLimitReach = false;
      const user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
      let createCredentials = async (userId: number, data: any) => {
        const client = await Client.findOne({ where: { id: data.client } });
        let isLoginIDExists = await Password.findOne({
          where: { client: client, loginId: data.loginId.trim() },
        });
        if (isLoginIDExists) {
          console.log('LoginID already Exists!!!');
        } else {
          const password = new Password();
          password.website = data.website.trim();
          password.websiteUrl = data.websiteUrl.trim();
          password.loginId = data.loginId.trim();
          password.password = data.password.trim();
          password.client = client;
          password['userId'] = userId;
          password.isExistingAtomPro = data.isExistingAtomPro;
          await password.save();
          return password;
        }
      };

      let createTanAndTracesCredentials = async (
        userId: number,
        tanDetails: any,
        traceDetails: any,
      ) => {
        const client = await Client.findOne({ where: { id: tanDetails?.client?.id } });
        let tanPassword: Password, tracePassword: Password;

        await getManager().transaction(async (transactionalEntityManager) => {
          // Create TAN credentials within the transaction
          tanPassword = new Password();
          tanPassword.website = tanDetails.website.trim();
          tanPassword.websiteUrl = tanDetails.websiteUrl.trim();
          tanPassword.loginId = tanDetails.loginId;
          tanPassword.password = tanDetails.password;
          tanPassword.client = client;
          tanPassword['userId'] = userId;
          tanPassword.isExistingAtomPro = tanDetails.isExistingAtomPro;
          await transactionalEntityManager.save(tanPassword);

          // Create Traces credentials within the transaction
          tracePassword = new Password();
          tracePassword.website = traceDetails.website.trim();
          tracePassword.websiteUrl = traceDetails.websiteUrl.trim();
          tracePassword.loginId = traceDetails.loginId;
          tracePassword.password = traceDetails.password;
          tracePassword.tracesTan = tanDetails?.loginId;
          tracePassword.client = client;
          tracePassword['userId'] = userId;
          tracePassword.isExistingAtomPro = traceDetails.isExistingAtomPro;
          await transactionalEntityManager.save(tracePassword);
        });

        return { tanPassword, tracePassword };
      };

      for (const [index, item] of data.entries()) {
        if (item?.client) {
          const organizationPreferences: any = await OrganizationPreferences.findOne({
            where: { organization: user?.organization },
          });

          const organizationIncomeTaxLimit =
            organizationPreferences?.automationConfig?.incomeTaxLimit;
          const organizationGstrLimit = organizationPreferences?.automationConfig?.gstrLimit;
          const organizationTanLimit = organizationPreferences?.automationConfig?.tanLimit;
          const incomeTaxAccess = organizationPreferences?.automationConfig?.incomeTax === 'YES';
          const gstrAccess = organizationPreferences?.automationConfig?.gstr === 'YES';
          const tanAccess = organizationPreferences?.automationConfig?.tan === 'YES';

          const client = await Client.findOne(item.client);

          if (item?.panNumber && item?.panPassword) {
            const details = {
              website: 'Income Tax | e-Filing (PAN)',
              websiteUrl: 'https://eportal.incometax.gov.in/iec/foservices/#/login',
              loginId: String(item?.panNumber),
              password: String(item?.panPassword),
              client: item?.client,
              isExistingAtomPro: IsExistingAtomPro.NO,
            };
            const incomeTaxPan = String(item?.panNumber);
            const checkValidPan = checkPanNumber(incomeTaxPan);
            if (checkValidPan) {
              const errorDuplicate = `row ${index + 2} Income Tax Invalid PAN, ClientName: ${client.displayName
                }, PAN : ${incomeTaxPan}`;
              errorsArray = [...errorsArray, errorDuplicate];
              await createCredentials(userId, details);
            } else {
              const existingClinet = await AutClientCredentials.findOne({
                where: {
                  clientId: item.client,
                },
              });

              if (existingClinet) {
                await createCredentials(userId, details);
                const errorExistingClient = `row ${index + 2
                  } This client is already existing in Atom Pro Income Tax, ClientName: ${client.displayName
                  }, PAN : ${item.panNumber}`;
                errorsArray = [...errorsArray, errorExistingClient];
              } else {
                const existingRecord = await AutClientCredentials.findOne({
                  where: { organizationId: user?.organization?.id, panNumber: item.panNumber },
                });

                if (existingRecord) {
                  const errorExistingRecord = `row ${index + 2
                    } This PAN is already existing in Atom Pro Income Tax, Client Name: ${client.displayName
                    }, PAN : ${item.panNumber}`;
                  errorsArray = [...errorsArray, errorExistingRecord];
                  await createCredentials(userId, details);
                } else {
                  // insert into income client tax Table
                  const autClientCredential = await AutClientCredentials.count({
                    where: {
                      organizationId: user?.organization?.id,
                      status: IncomeTaxStatus.ENABLE,
                    },
                  });
                  if (incomeTaxAccess) {
                    if (organizationIncomeTaxLimit > autClientCredential) {
                      details.isExistingAtomPro = IsExistingAtomPro.YES;
                      const passwordData: any = await createCredentials(userId, details);
                      const clientCredentials = new AutClientCredentials();
                      clientCredentials.panNumber = String(item.panNumber).trim();
                      clientCredentials.password = String(item?.panPassword).trim();
                      clientCredentials.client = client;
                      clientCredentials.organizationId = user?.organization?.id;
                      clientCredentials.syncStatus = syncStatus.NOTSYNC;
                      clientCredentials.status = IncomeTaxStatus.ENABLE;
                      clientCredentials.passwordId = passwordData?.id;
                      await clientCredentials.save();

                      incomeTaxNewAdded += 1;
                    } else {
                      isIncomeTaxClientLimitReach = true;
                      const errorLimitExit = `row ${index + 2
                        } Client Not Added in Atom Pro Income Tax due to Client Limit Reached, Client Name: ${client.displayName
                        }, PAN: ${item.panNumber}`;
                      errorsArray = [...errorsArray, errorLimitExit];
                      await createCredentials(userId, details);
                    }
                  } else {
                    isIncomeTaxClientLimitReach = true;
                    const errorLimitExit = `row ${index + 2
                      } Subscribe Atom Pro Income Tax to access for this Client, Client Name: ${client.displayName
                      }, PAN: ${item.panNumber}`;
                    errorsArray = [...errorsArray, errorLimitExit];
                    await createCredentials(userId, details);
                  }
                }
              }
            }
          }

          if (item?.gstUsername && item?.gstPassword) {
            const details = {
              website: 'GST | e-Filing',
              websiteUrl: 'https://services.gst.gov.in/services/login',
              loginId: String(item?.gstUsername),
              password: String(item?.gstPassword),
              client: item?.client,
              isExistingAtomPro: IsExistingAtomPro.NO,
            };
            const gstrUsername = String(item?.gstUsername);
            const gstrPassword = String(item?.gstPassword);

            const checkValidGstrUserName = checkGstrUsername(gstrUsername);
            const checkValidGstrPassword = checkGstrPassword(gstrPassword);
            if (checkValidGstrUserName) {
              const errorDuplicate = `row ${index + 2} GSTR User Id Invalid, ClientName: ${client.displayName
                }, Id : ${gstrUsername}`;
              errorsArray = [...errorsArray, errorDuplicate];
              await createCredentials(userId, details);
            } else {
              if (checkValidGstrPassword) {
                const errorDuplicate = `row ${index + 2} GSTR Password Invalid, ClientName: ${client.displayName
                  }, Id : ${gstrUsername}`;
                errorsArray = [...errorsArray, errorDuplicate];
                await createCredentials(userId, details);
              } else {
                // insert into Gstr client  Table
                const existingClinet = await GstrCredentials.findOne({
                  where: {
                    clientId: item.client,
                  },
                });
                if (existingClinet) {
                  const errorExistingClient = `row ${index + 2
                    } This client is already existing in Atom Pro GSTR, Client Name: ${client.displayName
                    }, User Id : ${item.gstId}`;
                  errorsArray = [...errorsArray, errorExistingClient];

                  await createCredentials(userId, details);
                } else {
                  const checkCredential = await GstrCredentials.findOne({
                    where: { organizationId: user?.organization?.id, userName: item.gstUsername },
                  });

                  if (checkCredential) {
                    await createCredentials(userId, details);
                    const errorExistingRecord = `row ${index + 2
                      } This User Id is already existing in Atom Pro GSTR, Client Name: ${client.displayName
                      }, User Id : ${item.gstId}`;
                    errorsArray = [...errorsArray, errorExistingRecord];
                  } else {
                    const gstrCredentialsCount = await GstrCredentials.count({
                      where: { organizationId: user?.organization?.id, status: GstrStatus.ENABLE },
                    });
                    if (gstrAccess) {
                      if (organizationGstrLimit > gstrCredentialsCount) {
                        details.isExistingAtomPro = IsExistingAtomPro.YES;

                        const passwordData = await createCredentials(userId, details);
                        const gstrCredentials = new GstrCredentials();
                        gstrCredentials.userName = String(item?.gstUsername.trim());
                        gstrCredentials.password = String(item?.gstPassword).trim();
                        gstrCredentials.userId = user?.id;
                        gstrCredentials.clientId = client.id;
                        gstrCredentials.organizationId = user?.organization?.id;
                        gstrCredentials.status = GstrStatus.ENABLE;
                        gstrCredentials.passwordId = passwordData?.id;
                        await gstrCredentials.save();
                        gstNewAdded += 1;
                      } else {
                        await createCredentials(userId, details);
                        isGstrClientLimitReach = true;
                        const errorExistingRecord = `row ${index + 2
                          } Client Not Added in Atom Pro GSTR due to Client Limit Reached, Client Name: ${client.displayName
                          }, User Id : ${item.gstId}`;
                        errorsArray = [...errorsArray, errorExistingRecord];
                      }
                    } else {
                      await createCredentials(userId, details);
                      isGstrClientLimitReach = true;
                      const errorExistingRecord = `row ${index + 2
                        } Subscribe Atom Pro GSTR to access for this Client, Client Name: ${client.displayName
                        }, GST Id : ${item.gstId}`;
                      errorsArray = [...errorsArray, errorExistingRecord];
                    }
                  }
                }
              }
            }
          }

          if (item?.gstNumber) {
            const gstNumber = item?.gstNumber;
            const checkValidGstNumber = checkGstNumber(gstNumber);
            if (checkValidGstNumber) {
              const errorExistingRecord = `row ${index + 2} Invalid GST Number, Client Name: ${client.displayName
                }, GST Id : ${item.gstId}`;
              errorsArray = [...errorsArray, errorExistingRecord];
            } else {
              const updatedClient = await this.getClientGstUpdatePayload(item, client);
              await this.clientService.update(userId, item.client, { ...client, ...updatedClient });
            }
          }

          // if (item?.tanNumber && item?.tanPassword) {
          //   const details = {
          //     website: 'Income Tax | e-Filing (TAN)',
          //     websiteUrl: 'https://eportal.incometax.gov.in/iec/foservices/#/login',
          //     loginId: String(item?.tanNumber),
          //     password: String(item?.tanPassword),
          //     client: item?.client,
          //     isExistingAtomPro: IsExistingAtomPro.NO,
          //   };
          //   const incomeTaxTan = String(item?.tanNumber);
          //   const checkValidTan = checkTanNumber(incomeTaxTan);
          //   if (checkValidTan) {
          //     const errorDuplicate = `row ${index + 2} Income Tax Invalid TAN, ClientName: ${client.displayName
          //       }, TAN : ${incomeTaxTan}`;
          //     errorsArray = [...errorsArray, errorDuplicate];
          //     await createCredentials(userId, details);
          //   } else {
          //     const existingClinet = await TanClientCredentials.findOne({
          //       where: {
          //         clientId: item.client,
          //       },
          //     });

          //     if (existingClinet) {
          //       await createCredentials(userId, details);
          //       const errorExistingClient = `row ${index + 2
          //         } This client is already existing in Atom Pro Income Tax, ClientName: ${client.displayName
          //         }, TAN : ${item.tanNumber}`;
          //       errorsArray = [...errorsArray, errorExistingClient];
          //     } else {
          //       const existingRecord = await TanClientCredentials.findOne({
          //         where: { organizationId: user?.organization?.id, tanNumber: item.tanNumber },
          //       });

          //       if (existingRecord) {
          //         const errorExistingRecord = `row ${index + 2
          //           } This TAN is already existing in Atom Pro Income Tax, Client Name: ${client.displayName
          //           }, TAN : ${item.tanNumber}`;
          //         errorsArray = [...errorsArray, errorExistingRecord];
          //         await createCredentials(userId, details);
          //       } else {
          //         // insert into income client tax Table
          //         const tanClientCredential = await TanClientCredentials.count({
          //           where: {
          //             organizationId: user?.organization?.id,
          //             status: IncomeTaxStatus.ENABLE,
          //           },
          //         });
          //         if (tanAccess) {
          //           if (organizationTanLimit > tanClientCredential) {
          //             details.isExistingAtomPro = IsExistingAtomPro.YES;
          //             const passwordData: any = await createCredentials(userId, details);
          //             const clientCredentials = new TanClientCredentials();
          //             clientCredentials.tanNumber = String(item.tanNumber).trim();
          //             clientCredentials.password = String(item?.tanPassword).trim();
          //             clientCredentials.client = client;
          //             clientCredentials.organizationId = user?.organization?.id;
          //             clientCredentials.status = IncomeTaxStatus.ENABLE;
          //             clientCredentials.passwordId = passwordData?.id;
          //             await clientCredentials.save();

          //             tanNewAdded += 1;
          //           } else {
          //             isIncomeTaxClientLimitReach = true;
          //             const errorLimitExit = `row ${index + 2
          //               } Client Not Added in Atom Pro Income Tax TAN due to Client Limit Reached, Client Name: ${client?.displayName
          //               }, TAN: ${item?.tanNumber}`;
          //             errorsArray = [...errorsArray, errorLimitExit];
          //             await createCredentials(userId, details);
          //           }
          //         } else {
          //           isIncomeTaxClientLimitReach = true;
          //           const errorLimitExit = `row ${index + 2
          //             } Subscribe Atom Pro Income Tax TAN to access for this Client, Client Name: ${client?.displayName
          //             }, TAN: ${item?.tanNumber}`;
          //           errorsArray = [...errorsArray, errorLimitExit];
          //           await createCredentials(userId, details);
          //         }
          //       }
          //     }
          //   }
          // }

          if (item?.tanNumber || item?.tracesTan) {
            let incomeTaxTan =
              (' ' + item?.tanNumber)?.trim() !== 'undefined' ? item?.tanNumber : null;
            let incomeTaxTanPassword =
              (' ' + item?.tanPassword)?.trim() !== 'undefined' ? item?.tanPassword : null;
            let tracesUserId =
              (' ' + item?.tracesUserId)?.trim() !== 'undefined' ? item?.tracesUserId : null;
            let tracesPassword =
              (' ' + item?.tracesPassword)?.trim() !== 'undefined' ? item?.tracesPassword : null;
            let tracesTan =
              (' ' + item?.tracesTan)?.trim() !== 'undefined' ? item?.tracesTan : null;

            //Case 1: Only Income Tax TAN data available without Traces.
            if (incomeTaxTan && !tracesTan) {
              if (!incomeTaxTanPassword) {
                const errorDuplicate = `row ${index + 2
                  } Mandatory Fields missing for this TAN, ClientName: ${client.displayName
                  }, TAN: ${incomeTaxTan}`;
                errorsArray = [...errorsArray, errorDuplicate];
              } else {
                const tanDetails = {
                  website: 'Income Tax | e-Filing (TAN)',
                  websiteUrl: 'https://eportal.incometax.gov.in/iec/foservices/#/login',
                  loginId: incomeTaxTan,
                  password: incomeTaxTanPassword,
                  client: client,
                  isExistingAtomPro: IsExistingAtomPro.NO,
                };
                const traceDetails = {
                  website: 'Income Tax | Traces (Tax Deductor)',
                  websiteUrl: 'https://www.tdscpc.gov.in/app/login.xhtml?usr=Ded',
                  loginId: null,
                  password: null,
                  client: client,
                  isExistingAtomPro: IsExistingAtomPro.NO,
                };
                const checkValidTan = checkTanNumber(incomeTaxTan);
                if (checkValidTan) {
                  const errorDuplicate = `row ${index + 2} Income Tax Invalid TAN, Client Name: ${client.displayName
                    }, TAN: ${incomeTaxTan}`;
                  errorsArray = [...errorsArray, errorDuplicate];
                  // await createTanAndTracesCredentials(userId,tanDetails,traceDetails);
                } else {
                  let tanExistingDetails = await Password.findOne({
                    where: {
                      client: client,
                      loginId: incomeTaxTan,
                      website: 'Income Tax | e-Filing (TAN)',
                    },
                  });
                  let tracesExistingDetails = await Password.findOne({
                    where: {
                      client: client,
                      tracesTan: incomeTaxTan,
                      website: 'Income Tax | Traces (Tax Deductor)',
                    },
                  });

                  if (tanExistingDetails || tracesExistingDetails) {
                    const errorDuplicate = `row ${index + 2
                      } Income Tax TAN or Traces already exists, Client Name: ${client.displayName
                      }, TAN: ${incomeTaxTan}`;
                    errorsArray = [...errorsArray, errorDuplicate];
                    // await createTanAndTracesCredentials(userId,tanDetails,traceDetails);
                  } else {
                    try {
                      let clientIsExisting = await TanClientCredentials.findOne({
                        where: { clientId: client },
                      });
                      const tanClilentCredentialsCount = await TanClientCredentials.count({
                        where: {
                          organizationId: user?.organization?.id,
                          status: IncomeTaxStatus.ENABLE,
                        },
                      });
                      if (clientIsExisting) {
                        const errorDuplicate = `row ${index + 2
                          } This client is already existing in Atom Pro Income Tax ClientName: ${client.displayName
                          }, TAN: ${incomeTaxTan}`;
                        errorsArray = [...errorsArray, errorDuplicate];
                        // await createTanAndTracesCredentials(userId,tanDetails,traceDetails);
                      } else {
                        let tanIsExisting = await TanClientCredentials.findOne({
                          where: {
                            organizationId: user?.organization?.id,
                            tanNumber: incomeTaxTan,
                          },
                        });
                        if (tanIsExisting) {
                          const errorDuplicate = `row ${index + 2
                            } This TAN is already existing in Atom Pro Income Tax, ClientName: ${client.displayName
                            }, TAN: ${incomeTaxTan}`;
                          errorsArray = [...errorsArray, errorDuplicate];
                          // await createTanAndTracesCredentials(userId,tanDetails,traceDetails);
                        } else {
                          if (tanAccess) {
                            if (organizationTanLimit > tanClilentCredentialsCount) {
                              tanDetails.isExistingAtomPro = IsExistingAtomPro.YES;
                              traceDetails.isExistingAtomPro = IsExistingAtomPro.YES;
                              let { tanPassword, tracePassword } =
                                await createTanAndTracesCredentials(
                                  userId,
                                  tanDetails,
                                  traceDetails,
                                );
                              const clientCredentials = new TanClientCredentials();
                              clientCredentials.tanNumber = incomeTaxTan;
                              clientCredentials.password = incomeTaxTanPassword;
                              clientCredentials.client = client;
                              clientCredentials.organizationId = user?.organization?.id;
                              clientCredentials.status = IncomeTaxStatus.ENABLE;
                              clientCredentials.passwordId = tanPassword?.id;
                              clientCredentials.traceUserId = null;
                              clientCredentials.tracePassword = null;
                              await clientCredentials.save();
                              tanNewAdded += 1;
                            } else {
                              const errorDuplicate = `row ${index + 2
                                } Client Not Added in Atom Pro Income Tax due to Client Limit Reached, ClientName: ${client.displayName
                                }, TAN: ${incomeTaxTan}`;
                              errorsArray = [...errorsArray, errorDuplicate];
                              await createTanAndTracesCredentials(userId, tanDetails, traceDetails);
                            }
                          } else {
                            const errorDuplicate = `row ${index + 2
                              } Subscribe Atom Pro Income Tax to access for this Client, Client Name: ${client.displayName
                              }, TAN: ${incomeTaxTan}`;
                            errorsArray = [...errorsArray, errorDuplicate];
                            await createTanAndTracesCredentials(userId, tanDetails, traceDetails);
                          }
                        }
                      }
                    } catch (error) {
                      errorsArray.push(
                        `row ${index + 2
                        } Failed to create credentials for TAN and Traces, ClientName: ${client.displayName
                        }, Error: ${error.message}`,
                      );
                    }
                  }
                }
              }
            }
            //Case 2: Only Traces Data avaliable without Income Tax TAN data.
            else if (!incomeTaxTan && tracesTan) {
              if (!tracesPassword || !tracesUserId) {
                const errorDuplicate = `row ${index + 2
                  } Traces Details missing for this client, ClientName: ${client.displayName
                  }, Traces: ${tracesTan}`;
                errorsArray = [...errorsArray, errorDuplicate];
              } else {
                const tanDetails = {
                  website: 'Income Tax | e-Filing (TAN)',
                  websiteUrl: 'https://eportal.incometax.gov.in/iec/foservices/#/login',
                  loginId: tracesTan,
                  password: null,
                  client: client,
                  isExistingAtomPro: IsExistingAtomPro.NO,
                };
                const traceDetails = {
                  website: 'Income Tax | Traces (Tax Deductor)',
                  websiteUrl: 'https://www.tdscpc.gov.in/app/login.xhtml?usr=Ded',
                  loginId: tracesUserId,
                  password: tracesPassword,
                  client: client,
                  isExistingAtomPro: IsExistingAtomPro.NO,
                };
                const checkValidTan = checkTanNumber(tracesTan);

                if (checkValidTan) {
                  const errorDuplicate = `row ${index + 2} Income Tax Invalid TAN, Client Name: ${client.displayName
                    }, TAN: ${tracesTan}`;
                  errorsArray = [...errorsArray, errorDuplicate];
                  // await createTanAndTracesCredentials(userId,tanDetails,traceDetails)
                } else {
                  const checkValidTracesUserId = checkTracesUserId(tracesUserId);
                  const checkValidTracesPassword = checkTracesPassword(tracesPassword);
                  if (checkValidTracesUserId) {
                    const errorDuplicate = `row ${index + 2
                      } Trace User Id id Invalid, Client Name: ${client.displayName
                      }, Trace User Id: ${tracesUserId}`;
                    errorsArray = [...errorsArray, errorDuplicate];
                  }
                  if (checkValidTracesPassword) {
                    const errorDuplicate = `row ${index + 2
                      } Trace Password is Invalid, Client Name: ${client.displayName
                      }, Trace Password: ${tracesPassword}`;
                    errorsArray = [...errorsArray, errorDuplicate];
                  } else {
                    let tanExistingDetails = await Password.findOne({
                      where: {
                        client: client,
                        loginId: tracesTan,
                        website: 'Income Tax | e-Filing (TAN)',
                      },
                    });
                    let tracesExistingDetails = await Password.findOne({
                      where: {
                        client: client,
                        tracesTan: tracesTan,
                        website: 'Income Tax | Traces (Tax Deductor)',
                      },
                    });

                    if (tanExistingDetails || tracesExistingDetails) {
                      const errorDuplicate = `row ${index + 2
                        } Income Tax TAN or Traces already exists, Client Name: ${client.displayName
                        }, TAN: ${tracesTan}`;
                      errorsArray = [...errorsArray, errorDuplicate];
                      // await createTanAndTracesCredentials(userId,tanDetails,traceDetails)
                    } else {
                      try {
                        let clientIsExisting = await TanClientCredentials.findOne({
                          where: { clientId: client },
                        });
                        const tanClilentCredentialsCount = await TanClientCredentials.count({
                          where: {
                            organizationId: user?.organization?.id,
                            status: IncomeTaxStatus.ENABLE,
                          },
                        });
                        if (clientIsExisting) {
                          const errorDuplicate = `row ${index + 2
                            } This client is already existing in Atom Pro Income Tax ClientName: ${client.displayName
                            }, TAN: ${tracesTan}`;
                          errorsArray = [...errorsArray, errorDuplicate];
                          // await createTanAndTracesCredentials(userId,tanDetails,traceDetails);
                        } else {
                          let tanIsExisting = await TanClientCredentials.findOne({
                            where: { organizationId: user?.organization?.id, tanNumber: tracesTan },
                          });
                          if (tanIsExisting) {
                            const errorDuplicate = `row ${index + 2
                              } This TAN is already existing in Atom Pro Income Tax, ClientName: ${client.displayName
                              }, TAN: ${tracesTan}`;
                            errorsArray = [...errorsArray, errorDuplicate];
                            // await createTanAndTracesCredentials(userId,tanDetails,traceDetails);
                          } else {
                            if (tanAccess) {
                              if (organizationTanLimit > tanClilentCredentialsCount) {
                                tanDetails.isExistingAtomPro = IsExistingAtomPro.YES;
                                traceDetails.isExistingAtomPro = IsExistingAtomPro.YES;
                                let { tanPassword, tracePassword } =
                                  await createTanAndTracesCredentials(
                                    userId,
                                    tanDetails,
                                    traceDetails,
                                  );
                                const clientCredentials = new TanClientCredentials();
                                clientCredentials.tanNumber = tracesTan;
                                clientCredentials.password = incomeTaxTanPassword;
                                clientCredentials.client = client;
                                clientCredentials.organizationId = user?.organization?.id;
                                clientCredentials.status = IncomeTaxStatus.ENABLE;
                                clientCredentials.passwordId = tanPassword?.id;
                                clientCredentials.traceUserId = tracesUserId;
                                clientCredentials.tracePassword = tracesPassword;
                                await clientCredentials.save();
                                tanNewAdded += 1;
                              } else {
                                const errorDuplicate = `row ${index + 2
                                  } Client Not Added in Atom Pro Income Tax due to Client Limit Reached, ClientName: ${client.displayName
                                  }, TAN: ${tracesTan}`;
                                errorsArray = [...errorsArray, errorDuplicate];
                                await createTanAndTracesCredentials(
                                  userId,
                                  tanDetails,
                                  traceDetails,
                                );
                              }
                            } else {
                              const errorDuplicate = `row ${index + 2
                                } Subscribe Atom Pro Income Tax to access for this Client, Client Name: ${client.displayName
                                }, TAN: ${tracesTan}`;
                              errorsArray = [...errorsArray, errorDuplicate];
                              await createTanAndTracesCredentials(userId, tanDetails, traceDetails);
                            }
                          }
                        }
                      } catch (error) {
                        errorsArray.push(
                          `row ${index + 2
                          } Failed to create credentials for TAN and Traces, ClientName: ${client.displayName
                          }, Error: ${error.message}`,
                        );
                      }
                    }
                  }
                }
              }
            }
            //Case 3: If Both TAN and Traces data are available.
            else if (incomeTaxTan && tracesTan) {
              if (incomeTaxTan !== tracesTan) {
                const errorDuplicate = `row ${index + 2
                  } Income Tax TAN doesn't match with Trace TAN, ClientName: ${client.displayName
                  }, Traces: ${tracesTan}`;
                errorsArray = [...errorsArray, errorDuplicate];
              } else {
                const tanDetails = {
                  website: 'Income Tax | e-Filing (TAN)',
                  websiteUrl: 'https://eportal.incometax.gov.in/iec/foservices/#/login',
                  loginId: incomeTaxTan,
                  password: incomeTaxTanPassword,
                  client: client,
                  isExistingAtomPro: IsExistingAtomPro.NO,
                };
                const traceDetails = {
                  website: 'Income Tax | Traces (Tax Deductor)',
                  websiteUrl: 'https://www.tdscpc.gov.in/app/login.xhtml?usr=Ded',
                  loginId: tracesUserId,
                  password: tracesPassword,
                  client: client,
                  isExistingAtomPro: IsExistingAtomPro.NO,
                };
                const checkValidTan = checkTanNumber(tracesTan);
                if (checkValidTan) {
                  const errorDuplicate = `row ${index + 2} Income Tax Invalid TAN, Client Name: ${client.displayName
                    }, TAN: ${incomeTaxTan}`;
                  errorsArray = [...errorsArray, errorDuplicate];
                  // await createTanAndTracesCredentials(userId,tanDetails,traceDetails)
                } else {
                  const checkValidTracesUserId = checkTracesUserId(tracesUserId);
                  const checkValidTracesPassword = checkTracesPassword(tracesPassword);
                  if (checkValidTracesUserId) {
                    const errorDuplicate = `row ${index + 2
                      } Trace User Id id Invalid, Client Name: ${client.displayName
                      }, Trace User Id: ${tracesUserId}`;
                    errorsArray = [...errorsArray, errorDuplicate];
                  }
                  if (checkValidTracesPassword) {
                    const errorDuplicate = `row ${index + 2
                      } Trace Password is Invalid, Client Name: ${client.displayName
                      }, Trace Password: ${tracesPassword}`;
                    errorsArray = [...errorsArray, errorDuplicate];
                  } else {
                    let tanExistingDetails = await Password.findOne({
                      where: {
                        client: client,
                        loginId: incomeTaxTan,
                        website: 'Income Tax | e-Filing (TAN)',
                      },
                    });
                    let tracesExistingDetails = await Password.findOne({
                      where: {
                        client: client,
                        tracesTan: incomeTaxTan,
                        website: 'Income Tax | Traces (Tax Deductor)',
                      },
                    });

                    if (tanExistingDetails || tracesExistingDetails) {
                      const errorDuplicate = `row ${index + 2
                        } Income Tax TAN or Traces already exists, Client Name: ${client.displayName
                        }, TAN: ${incomeTaxTan}`;
                      errorsArray = [...errorsArray, errorDuplicate];
                      // await createTanAndTracesCredentials(userId,tanDetails,traceDetails)
                    } else {
                      try {
                        let clientIsExisting = await TanClientCredentials.findOne({
                          where: { clientId: client },
                        });
                        const tanClilentCredentialsCount = await TanClientCredentials.count({
                          where: {
                            organizationId: user?.organization?.id,
                            status: IncomeTaxStatus.ENABLE,
                          },
                        });
                        if (clientIsExisting) {
                          const errorDuplicate = `row ${index + 2
                            } This client is already existing in Atom Pro Income Tax ClientName: ${client.displayName
                            }, TAN: ${incomeTaxTan}`;
                          errorsArray = [...errorsArray, errorDuplicate];
                          // await createTanAndTracesCredentials(userId,tanDetails,traceDetails);
                        } else {
                          let tanIsExisting = await TanClientCredentials.findOne({
                            where: {
                              organizationId: user?.organization?.id,
                              tanNumber: incomeTaxTan,
                            },
                          });
                          if (tanIsExisting) {
                            const errorDuplicate = `row ${index + 2
                              } This TAN is already existing in Atom Pro Income Tax, ClientName: ${client.displayName
                              }, TAN: ${incomeTaxTan}`;
                            errorsArray = [...errorsArray, errorDuplicate];
                            // await createTanAndTracesCredentials(userId,tanDetails,traceDetails);
                          } else {
                            if (tanAccess) {
                              if (organizationTanLimit > tanClilentCredentialsCount) {
                                tanDetails.isExistingAtomPro = IsExistingAtomPro.YES;
                                traceDetails.isExistingAtomPro = IsExistingAtomPro.YES;
                                let { tanPassword, tracePassword } =
                                  await createTanAndTracesCredentials(
                                    userId,
                                    tanDetails,
                                    traceDetails,
                                  );
                                const clientCredentials = new TanClientCredentials();
                                clientCredentials.tanNumber = incomeTaxTan;
                                clientCredentials.password = incomeTaxTanPassword;
                                clientCredentials.client = client;
                                clientCredentials.organizationId = user?.organization?.id;
                                clientCredentials.status = IncomeTaxStatus.ENABLE;
                                clientCredentials.passwordId = tanPassword?.id;
                                clientCredentials.traceUserId = tracesUserId;
                                clientCredentials.tracePassword = tracesPassword;
                                await clientCredentials.save();
                                tanNewAdded += 1;
                              } else {
                                const errorDuplicate = `row ${index + 2
                                  } Client Not Added in Atom Pro Income Tax due to Client Limit Reached, ClientName: ${client.displayName
                                  }, TAN: ${tracesTan}`;
                                errorsArray = [...errorsArray, errorDuplicate];
                                await createTanAndTracesCredentials(
                                  userId,
                                  tanDetails,
                                  traceDetails,
                                );
                              }
                            } else {
                              const errorDuplicate = `row ${index + 2
                                } Subscribe Atom Pro Income Tax to access for this Client, Client Name: ${client.displayName
                                }, TAN: ${tracesTan}`;
                              errorsArray = [...errorsArray, errorDuplicate];
                              await createTanAndTracesCredentials(userId, tanDetails, traceDetails);
                            }
                          }
                        }
                      } catch (error) {
                        errorsArray.push(
                          `row ${index + 2
                          } Failed to create credentials for TAN and Traces, ClientName: ${client.displayName
                          }, Error: ${error.message}`,
                        );
                      }
                    }
                  }
                }
              }
            }
          } else if ((' ' + client['Income Tax (TAN) - ATOM Pro'])?.trim()) {
            const errorDuplicate = `row ${index + 2
              } Income Tax TAN or Trace TAN is not provided, Client Name: ${client.displayName}`;
            errorsArray = [...errorsArray, errorDuplicate];
          }
        } else {
          const errorExistingClient = `row ${index + 2}  Select Atom Pro Client`;
          errorsArray = [...errorsArray, errorExistingClient];
        }
      }

      return {
        errorsArray,
        incomeTaxNewAdded,
        gstNewAdded,
        isGstrClientLimitReach,
        isIncomeTaxClientLimitReach,
        isTanClientLimitReach,
        tanNewAdded,
      };
    } catch (error) {
      console.log(error);
    }
  }

  async getClientGstUpdatePayload(item: any, client: any) {
    try {
      const existingClient = await Client.findOne({ where: { gstNumber: item?.gstNumber } });
      if (existingClient) {
        return null;
      }
      const gstDetailsData = await this.clientService.gstData(item?.gstNumber);

      if (gstDetailsData.data.sts) {
        const address: any = {
          communicationfulladdress: this.clientService.getGstAddress(
            gstDetailsData?.data?.pradr?.addr,
          ),
        };
        address['billingfulladdress'] = address?.communicationfulladdress;
        address['shippingfulladdress'] = address?.communicationfulladdress;

        client.issameaddress = true;
        client.gstVerified = true;
        client.gstNumber = gstDetailsData?.data?.gstin;
        client.legalName = gstDetailsData?.data?.lgnm;
        client.address = address;
        client.gstRegistrationDate = gstDetailsData?.data?.rgdt || null;
        client.tradeName = gstDetailsData?.data?.tradeNam;
        client.constitutionOfBusiness = gstDetailsData?.data?.ctb;
        client.placeOfSupply = gstDetailsData?.data?.pradr?.addr?.stcd;
        client.buildingName = gstDetailsData?.data?.pradr?.addr?.bnm;
        client.street = gstDetailsData?.data?.pradr?.addr?.st;
        client.city = gstDetailsData?.data?.pradr?.addr?.dst;
        client.state = gstDetailsData?.data?.pradr?.addr?.stcd;
        client.pincode = gstDetailsData?.data?.pradr?.addr?.pncd;

        const gstCharAt13 = client.gstNumber.charAt(13);
        const extractedNumber = client.gstNumber.substring(2, client.gstNumber.length - 3);
        if (gstCharAt13 === 'D') {
          const pattern = TAN_REGEX.test(extractedNumber);
          if (pattern) {
            client.tanNumber = client.tanNumber;
          } else {
            client.panNumber = client.panNumber;
          }
          client.registrationType = RegistrationType.TAX_DEDUCTOR;
        } else if (gstCharAt13 === 'Z' || gstCharAt13 === 'C') {
          client.panNumber = client.panNumber;
          client.registrationType =
            gstCharAt13 === 'Z'
              ? RegistrationType.REGULAR_TAXPAYER
              : RegistrationType.TAX_COLLECTOR;
        }
      } else {
        client.gstNumber = item.gstNumber;
      }

      return client;
    } catch (error) {
      console.log('error occur while getClientGstUpdatePayload', error);
    }
  }

  async organizationScheduling(userId: any) {
    try {
      const user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
      const organizarionId = user.organization.id;
      let data = JSON.stringify({
        modules: ['P', 'F', 'R', 'OD', 'RWA', 'EP'],
        orgId: organizarionId,
        type: 'INCOMETAX',
      });

      let config: any = {
        method: 'post',
        maxBodyLength: Infinity,
        url: `${process.env.CAMUNDA_URL}/vider/quantum/api/automation/bulk/sync`,
        headers: {
          'X-USER-ID': userId,
          'Content-Type': 'application/json',
        },
        data: data,
      };

      axios
        .request(config)
        .then((response) => {
          console.log(JSON.stringify(response.data));
        })
        .catch((error) => {
          console.log(error);
        });
    } catch (error) {
      console.log('error occur while organizationScheduling', error);
    }
  }

  async clientEChallan(userId: number, query: any, id: number) {
    const user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
    const { limit, offset, search, sortValue, assessmentYear } = query;
    try {
      const clientCredential = await AutClientCredentials.findOne({
        where: { id, organizationId: user?.organization?.id },
        relations: ['client'],
      });

      if (clientCredential) {
        const challanDetails = await createQueryBuilder(
          AutIncometaxEChallan,
          'autIncomeTaxEChallan',
        )
          .select([
            'autIncomeTaxEChallan.id',
            'autIncomeTaxEChallan.assessmentYear',
            'autIncomeTaxEChallan.acin',
            'autIncomeTaxEChallan.minorDesc',
            'autIncomeTaxEChallan.minorHead',
            'autIncomeTaxEChallan.totalAmt',
            'autIncomeTaxEChallan.paymentTime',
            'autIncomeTaxEChallan.basicTax',
            'autIncomeTaxEChallan.surCharge',
            'autIncomeTaxEChallan.eduCess',
            'autIncomeTaxEChallan.interest',
            'autIncomeTaxEChallan.penalty',
            'autIncomeTaxEChallan.others',
            'client.id',
            'client.displayName',
            'autClientCredentials.id',
          ])
          .leftJoin('autIncomeTaxEChallan.client', 'client')
          .leftJoin('autIncomeTaxEChallan.autClientCredentials', 'autClientCredentials')
          .where('autClientCredentials.id =:id', { id: id });

        challanDetails.orderBy('autIncomeTaxEChallan.paymentTime', 'DESC');

        const sort = typeof query?.sort === 'string' ? JSON.parse(query.sort) : query?.sort;
        if (sort?.column) {
          const columnMap: Record<string, string> = {
            id: 'autIncomeTaxEChallan.assessmentYear',
            minorDesc: 'autIncomeTaxEChallan.minorDesc',
            paymentTime: 'autIncomeTaxEChallan.paymentTime',
            acin: 'autIncomeTaxEChallan.acin',
            totalAmt: 'autIncomeTaxEChallan.totalAmt',
          };
          const column = columnMap[sort.column] || sort.column;
          challanDetails.orderBy(column, sort.direction.toUpperCase());
        }

        if (search) {
          challanDetails.andWhere(
            new Brackets((qb) => {
              qb.where('autIncomeTaxEChallan.acin LIKE :acin', {
                acin: `%${search}%`,
              });
              qb.orWhere('autIncomeTaxEChallan.minorDesc LIKE :minorDesc', {
                minorDesc: `%${search}%`,
              });
              qb.orWhere('autIncomeTaxEChallan.minorHead LIKE :minorHead', {
                minorHead: `%${search}%`,
              });
            }),
          );
        }

        if (assessmentYear) {
          if (assessmentYear === 'null') {
            challanDetails.andWhere('autIncomeTaxEChallan.assessmentYear = 0');
          } else {
            challanDetails.andWhere('autIncomeTaxEChallan.assessmentYear like :as', {
              as: `%${assessmentYear}%`,
            });
          }
        }

        if (sortValue) {
          switch (sortValue) {
            case 'AMOUNT_DESC':
              challanDetails.orderBy('autIncomeTaxEChallan.totalAmt', 'DESC');
              break;
            case 'AMOUNT_ASC':
              challanDetails.orderBy('autIncomeTaxEChallan.totalAmt', 'ASC');
              break;
            case 'DATE_NEWEST':
              challanDetails.orderBy('autIncomeTaxEChallan.paymentTime', 'DESC');
              break;
            case 'DATE_OLDEST':
              challanDetails.orderBy('autIncomeTaxEChallan.paymentTime', 'ASC');
              break;
            default:
              break;
          }
        }

        if (offset) {
          challanDetails.skip(offset);
        }

        if (limit) {
          challanDetails.take(limit);
        }

        let result = await challanDetails.getManyAndCount();
        return {
          count: result[1],
          result: result[0],
          accessDenied: true,
        };
      } else {
        return { accessDenied: false };
      }
    } catch (error) {
      console.log('error occured while fetching income tax client e-Challan', error);
    }
  }

  async exportClientEchallan(userId: number, query: any) {
    const newQuery = { ...query, offset: 0, limit: ********* };
    const id = query.incometaxid;
    let echallans = await this.clientEChallan(userId, newQuery, id);
    if (!echallans.result.length) throw new BadRequestException('No Data for Export');
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Inc Tax Challans');
    const headers = [
      { header: 'S.No', key: 'serialNo' },
      { header: 'AY', key: 'ay' },
      { header: 'Type of Payment', key: 'typeofPayment' },
      { header: ' Date of Payment', key: 'paymentDate' },
      { header: 'Alternate CIN', key: 'alternateCIN' },
      { header: 'BSR Code', key: 'bsr' },
      { header: 'Challan #', key: 'challan' },
      { header: 'Tax (₹)', key: 'tax' },
      { header: 'Surcharge (₹)', key: 'surCharge' },
      { header: 'Cess (₹)', key: 'cess' },
      { header: 'Interest (₹)', key: 'interest' },
      { header: 'Penality (₹)', key: 'penalty' },
      { header: 'Others (₹)', key: 'others' },
      { header: 'Total (₹)', key: 'total' },
    ];

    worksheet.columns = headers;
    const columnMaxLengths = Array(headers.length).fill(0);
    let serialCounter = 1; // Initialize a counter

    echallans.result.forEach((echallan) => {
      const rowData = {
        serialNo: serialCounter++, // Assign and then increment the counter
        ay: echallan?.assessmentYear,
        typeofPayment: `${echallan?.minorDesc} (${echallan?.minorHead})`,
        alternateCIN: echallan?.acin,
        bsr: echallan?.acin?.slice(0, 7),
        challan: echallan?.acin?.toString().slice(-5),
        paymentDate: formatDate(echallan?.paymentTime),
        tax: 1 * echallan?.basicTax,
        surCharge: 1 * echallan?.surCharge,
        cess: 1 * echallan?.eduCess,
        interest: 1 * echallan?.interest,
        penalty: 1 * echallan?.penalty,
        others: 1 * echallan?.others,
        total: 1 * echallan?.totalAmt,
      };

      const row = worksheet.addRow(rowData);

      worksheet.columns.forEach((column, colIndex) => {
        const headerLength = column.header?.length || 0;
        const cellLength = rowData[column.key]?.toString().length || 0;
        columnMaxLengths[colIndex] = Math.max(
          columnMaxLengths[colIndex] || 0,
          headerLength,
          cellLength,
        );
      });
    });

    worksheet.columns.forEach((column, colIndex) => {
      column.width = columnMaxLengths[colIndex] + 2;
    });

    const headerRow = worksheet.getRow(1);
    headerRow.font = { bold: true };
    headerRow.eachCell((cell) => {
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '64B5F6' },
      };
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' },
      };
      cell.alignment = { horizontal: 'center', vertical: 'middle' };
    });

    worksheet.eachRow((row) => {
      row.eachCell((cell) => {
        cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
      });
    });

    worksheet.views = [{ state: 'frozen', ySplit: 1 }];

    const buffer = await workbook.xlsx.writeBuffer();
    return buffer;
  }
  async findEchallan(userId: number, id: number) {
    try {
      const user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
      const incomeTaxEchallan = await AutEChallan.findOne({
        where: { id, organizationId: user?.organization?.id },
        relations: ['client', 'client.autProfileDetails'],
      });
      return incomeTaxEchallan;
    } catch (error) {
      console.log('error occur while getting findForm', error);
    }
  }

  async findEchallans(userId: number, query: any) {
    try {
      const { limit, offset, assessmentYear, clientCategory, sortValue, search } = query;
      let user = await User.findOne({
        where: { id: userId },
        relations: ['organization', 'role'],
      });
      let ViewAll = user.role.permissions.find(
        (permission) => permission.slug === Permissions.VIEW_ALL_CLIENT_MANAGERS,
      );
      let ViewAssigned = user.role.permissions.find(
        (permission) => permission.slug === Permissions.VIEW_ASSIGNED_CLIENT_MANAGERS,
      );
      let eChallans = createQueryBuilder(AutEChallan, 'autEChallan')
        .select([
          'autEChallan.id',
          'autEChallan.pan',
          'autEChallan.assessmentYear',
          'autEChallan.acin',
          'autEChallan.minorDesc',
          'autEChallan.minorHead',
          'autEChallan.totalAmt',
          'autEChallan.paymentTime',
          'autEChallan.basicTax',
          'autEChallan.surCharge',
          'autEChallan.eduCess',
          'autEChallan.interest',
          'autEChallan.penalty',
          'autEChallan.others',
          'client.id',
          'client.displayName',
          'autClientCredentials.id',
          'clientManagers',
        ])
        .leftJoin('autEChallan.client', 'client')
        .leftJoin('client.clientManagers', 'clientManagers')
        .leftJoin('client.autClientCredentials', 'autClientCredentials')
        .where('autEChallan.organizationId = :id', { id: user?.organization?.id })
        .andWhere('client.status != :status', { status: UserStatus.DELETED })
        .andWhere('autClientCredentials.status != :disStatus', {
          disStatus: IncomeTaxStatus.DISABLE,
        });

      eChallans.orderBy('autEChallan.paymentTime', 'DESC');

      const sort = typeof query?.sort === 'string' ? JSON.parse(query.sort) : query?.sort;
      if (sort?.column) {
        const columnMap: Record<string, string> = {
          id: 'autEChallan.assessmentYear',
          name: 'client.displayName',
          minorDesc: 'autEChallan.minorDesc',
          paymentTime: 'autEChallan.paymentTime',
          acin: 'autEChallan.acin',
          totalAmt: 'autEChallan.totalAmt',
        };
        const column = columnMap[sort.column] || sort.column;
        eChallans.orderBy(column, sort.direction.toUpperCase());
      }
      if (search) {
        eChallans.andWhere(
          new Brackets((qb) => {
            qb.where('autEChallan.pan LIKE :pansearch', {
              pansearch: `%${search}%`,
            });
            qb.orWhere('client.displayName LIKE :namesearch', {
              namesearch: `%${search}%`,
            });
          }),
        );
      }

      if (ViewAssigned && !ViewAll) {
        eChallans.andWhere('clientManagers.id = :userId', { userId });
      }

      if (assessmentYear) {
        if (assessmentYear === 'null') {
          eChallans.andWhere('autEChallan.assessmentYear = 0');
        } else {
          eChallans.andWhere('autEChallan.assessmentYear like :as', { as: `%${assessmentYear}%` });
        }
      }

      if (clientCategory) {
        eChallans.andWhere(`autEChallan.minorDesc like :clientCategory`, {
          clientCategory: clientCategory,
        });
      }

      if (sortValue) {
        switch (sortValue) {
          case 'AMOUNT_DESC':
            eChallans.orderBy('autEChallan.totalAmt', 'DESC');
            break;
          case 'AMOUNT_ASC':
            eChallans.orderBy('autEChallan.totalAmt', 'ASC');
            break;
          case 'DATE_NEWEST':
            eChallans.orderBy('autEChallan.paymentTime', 'DESC');
            break;
          case 'DATE_OLDEST':
            eChallans.orderBy('autEChallan.paymentTime', 'ASC');
            break;
          default:
            break;
        }
      }

      if (offset >= 0) {
        eChallans.skip(offset);
      }

      if (limit) {
        eChallans.take(limit);
      }

      let result = await eChallans.getManyAndCount();
      return {
        count: result[1],
        result: result[0],
      };
    } catch (error) {
      console.log('error occured while fetching getIncomeTaxEChallans', error);
    }
  }

  async exportIncometaxEchallan(userId: number, query: any) {
    const newQuery = { ...query, offset: 0, limit: ********* };
    const id = query.incometaxid;
    let echallans = await this.findEchallans(userId, newQuery);
    if (!echallans.result.length) throw new BadRequestException('No Data for Export');
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Inc Tax Challans');
    const headers = [
      { header: 'S.No', key: 'serialNo' },
      { header: 'AY', key: 'ay' },
      { header: 'Client Name', key: 'clientName' },
      { header: 'PAN', key: 'pan' },
      { header: 'Type of Payment', key: 'typeofPayment' },
      { header: 'Date of Payment', key: 'paymentDate' },
      { header: 'Alternate CIN', key: 'alternateCIN' },
      { header: 'BSR Code', key: 'bsr' },
      { header: 'Challan #', key: 'challan' },
      { header: 'Tax (₹)', key: 'tax' },
      { header: 'Surcharge (₹)', key: 'surCharge' },
      { header: 'Cess (₹)', key: 'cess' },
      { header: 'Interest (₹)', key: 'interest' },
      { header: 'Penality (₹)', key: 'penalty' },
      { header: 'Others (₹)', key: 'others' },
      { header: 'Total (₹)', key: 'total' },
    ];

    worksheet.columns = headers;
    const columnMaxLengths = Array(headers.length).fill(0);
    let serialCounter = 1; // Initialize a counter
    echallans.result.forEach((echallan) => {
      const {
        client: { displayName: clientName },
      } = echallan;
      const rowData = {
        serialNo: serialCounter++, // Assign and then increment the counter
        ay: echallan?.assessmentYear,
        clientName: clientName,
        pan: echallan?.pan,
        typeofPayment: `${echallan?.minorDesc} (${echallan?.minorHead})`,
        alternateCIN: echallan?.acin,
        paymentDate: formatDate(echallan?.paymentTime),
        bsr: echallan?.acin?.slice(0, 7),
        challan: echallan?.acin?.toString().slice(-5),
        tax: 1 * echallan?.basicTax,
        surCharge: 1 * echallan?.surCharge,
        cess: 1 * echallan?.eduCess,
        interest: 1 * echallan?.interest,
        penalty: 1 * echallan?.penalty,
        others: 1 * echallan?.others,
        total: 1 * echallan?.totalAmt,
      };

      const row = worksheet.addRow(rowData);

      worksheet.columns.forEach((column, colIndex) => {
        const headerLength = column.header?.length || 0;
        const cellLength = rowData[column.key]?.toString().length || 0;
        columnMaxLengths[colIndex] = Math.max(
          columnMaxLengths[colIndex] || 0,
          headerLength,
          cellLength,
        );
      });
    });

    worksheet.columns.forEach((column, colIndex) => {
      column.width = columnMaxLengths[colIndex] + 2;
    });

    const headerRow = worksheet.getRow(1);
    headerRow.font = { bold: true };
    headerRow.eachCell((cell) => {
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '64B5F6' },
      };
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' },
      };
      cell.alignment = { horizontal: 'center', vertical: 'middle' };
    });

    worksheet.columns.forEach((column) => {
      if (column.key === 'clientName') {
        column.width = 50;
        column.alignment = { wrapText: true, horizontal: 'center', vertical: 'middle' };
      } else {
        column.alignment = { horizontal: 'center', vertical: 'middle' };
      }
    });

    worksheet.eachRow((row) => {
      row.eachCell((cell) => {
        cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
      });
    });

    worksheet.views = [{ state: 'frozen', ySplit: 1 }];

    const buffer = await workbook.xlsx.writeBuffer();
    return buffer;
  }

  async clientMycas(userId: number, query: any, id: number) {
    const user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
    const { limit, offset } = query;
    try {
      const clientCredential = await AutClientCredentials.findOne({
        where: { id, organizationId: user?.organization?.id },
        relations: ['client'],
      });

      if (clientCredential) {
        const myCasDetails = await createQueryBuilder(AutMyCas, 'autMycas')
          .select([
            'autMycas.id',
            'autMycas.panNumber',
            'autMycas.caName',
            'autMycas.caMembershipNum',
            'autMycas.caStatus',
            'autMycas.assignedDate',
            'autMycas.filingType',
            'autMycas.filingStatus',
            'autMycas.formTypeCd',
            'autMycas.isWithdrawable',
            'autMycas.formStatus',
            'autMycas.transactionNo',
            'autMycas.assessmentYear',
            'autMycas.financialYear',
            'autMycas.udinNumber',
            'client.id',
            'client.displayName',
            'autClientCredentials.id',
          ])
          .leftJoin('autMycas.client', 'client')
          .leftJoin('autMycas.autClientCredentials', 'autClientCredentials')
          .where('autClientCredentials.id =:id', { id: id });

        myCasDetails.orderBy('autMycas.assignedDate', 'DESC');
        const sort = typeof query?.sort === 'string' ? JSON.parse(query.sort) : query?.sort;
        if (sort?.column) {
          const columnMap: Record<string, string> = {
            id: 'autMycas.assessmentYear',
            caName: 'autMycas.caName',
            caMembershipNum: 'autMycas.caMembershipNum',
            filingType: 'autMycas.filingType',
            formTypeCd: 'autMycas.formTypeCd',
            transactionNo: 'autMycas.transactionNo',
            assignedDate: 'autMycas.assignedDate',
            udinNumber: 'autMycas.udinNumber',
          };
          const column = columnMap[sort.column] || sort.column;
          myCasDetails.orderBy(column, sort.direction.toUpperCase());
        }

        if (offset) {
          myCasDetails.skip(offset);
        }

        if (limit) {
          myCasDetails.take(limit);
        }

        let result = await myCasDetails.getManyAndCount();
        return {
          count: result[1],
          result: result[0],
          accessDenied: true,
        };
      } else {
        return { accessDenied: false };
      }
    } catch (error) {
      console.log('error occured while fetching income tax client clientMycas', error);
    }
  }

  async exportClientMycas(userId: number, query: any) {
    const id = query.incometaxid;
    const newQuery = { ...query, offset: 0, limit: ********* };
    let mycas = await this.clientMycas(userId, newQuery, id);

    if (!mycas.result.length) throw new BadRequestException('No Data for Export');
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Inc Tax AR(CA)');
    const headers = [
      { header: 'S.No', key: 'serialNo' },
      { header: 'AY', key: 'ay' },
      { header: 'CA Name', key: 'caName' },
      { header: 'CA Membership #', key: 'caMembership' },
      { header: 'Filing Type', key: 'filingType' },
      { header: 'Form Name', key: 'formName' },
      { header: 'Transaction ID', key: 'transactionId' },
      { header: 'UDIN', key: 'udin' },
    ];

    worksheet.columns = headers;
    const columnMaxLengths = Array(headers.length).fill(0);
    let serialCounter = 1; // Initialize a counter
    mycas.result.forEach((myca) => {
      const rowData = {
        serialNo: serialCounter++, // Assign and then increment the counter
        ay: myca?.assessmentYear,
        caName: myca?.caName,
        caMembership: myca?.caMembershipNum,
        filingType: getFilingType(myca?.filingType),
        formName: myca?.formTypeCd,
        transactionId: myca?.transactionNo,
        udin: myca?.udinNumber,
      };

      const row = worksheet.addRow(rowData);

      worksheet.columns.forEach((column, colIndex) => {
        const headerLength = column.header?.length || 0;
        const cellLength = rowData[column.key]?.toString().length || 0;
        columnMaxLengths[colIndex] = Math.max(
          columnMaxLengths[colIndex] || 0,
          headerLength,
          cellLength,
        );
      });
    });

    worksheet.columns.forEach((column, colIndex) => {
      column.width = columnMaxLengths[colIndex] + 3;
    });

    const headerRow = worksheet.getRow(1);
    headerRow.font = { bold: true };
    headerRow.eachCell((cell) => {
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '64B5F6' },
      };
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' },
      };
      cell.alignment = { horizontal: 'center', vertical: 'middle' };
    });

    worksheet.eachRow((row) => {
      row.eachCell((cell) => {
        cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
      });
    });

    worksheet.views = [{ state: 'frozen', ySplit: 1 }];

    const buffer = await workbook.xlsx.writeBuffer();
    return buffer;
  }
  async findMycas(userId: number, query: any) {
    try {
      const { limit, offset, assessmentYear, clientCategory, sortValue, search, filingValue } =
        query;
      let user = await User.findOne({
        where: { id: userId },
        relations: ['organization', 'role'],
      });
      let ViewAll = user.role.permissions.find(
        (permission) => permission.slug === Permissions.VIEW_ALL_CLIENT_MANAGERS,
      );
      let ViewAssigned = user.role.permissions.find(
        (permission) => permission.slug === Permissions.VIEW_ASSIGNED_CLIENT_MANAGERS,
      );
      let myCasDetails = createQueryBuilder(AutMyCas, 'autMycas')
        .select([
          'autMycas.id',
          'autMycas.panNumber',
          'autMycas.caName',
          'autMycas.caMembershipNum',
          'autMycas.caStatus',
          'autMycas.assignedDate',
          'autMycas.filingType',
          'autMycas.filingStatus',
          'autMycas.formTypeCd',
          'autMycas.isWithdrawable',
          'autMycas.formStatus',
          'autMycas.transactionNo',
          'autMycas.assessmentYear',
          'autMycas.financialYear',
          'autMycas.udinNumber',
          'client.id',
          'client.displayName',
          'autClientCredentials.id',
          'clientManagers',
        ])
        .leftJoin('autMycas.client', 'client')
        .leftJoin('client.clientManagers', 'clientManagers')
        .leftJoin('client.autClientCredentials', 'autClientCredentials')
        .where('autMycas.organizationId = :id', { id: user?.organization?.id })
        .andWhere('client.status != :status', { status: UserStatus.DELETED })
        .andWhere('autClientCredentials.status != :disStatus', {
          disStatus: IncomeTaxStatus.DISABLE,
        });

      if (ViewAssigned && !ViewAll) {
        myCasDetails.andWhere('clientManagers.id = :userId', { userId });
      }
      myCasDetails.orderBy('autMycas.assignedDate', 'DESC');
      const sort = typeof query?.sort === 'string' ? JSON.parse(query.sort) : query?.sort;
      if (sort?.column) {
        const columnMap: Record<string, string> = {
          id: 'autMycas.assessmentYear',
          name: 'autMycas.caName',
          filingType: 'autMycas.filingType',
          formTypeCd: 'autMycas.formTypeCd',
          transactionNo: 'autMycas.transactionNo',
          assignedDate: 'autMycas.assignedDate',
          udinNumber: 'autMycas.udinNumber',
        };
        const column = columnMap[sort.column] || sort.column;
        myCasDetails.orderBy(column, sort.direction.toUpperCase());
      }

      if (search) {
        myCasDetails.andWhere(
          new Brackets((qb) => {
            qb.where('autMycas.panNumber LIKE :panSearch', {
              panSearch: `%${search}%`,
            });
            qb.orWhere('client.displayName LIKE :nameSearch', {
              nameSearch: `%${search}%`,
            });
            qb.orWhere('autMycas.caName LIKE :caNameSearch', {
              caNameSearch: `%${search}%`,
            });
            qb.orWhere('autMycas.caMembershipNum LIKE :membershipSearch', {
              membershipSearch: `%${search}%`,
            });
          }),
        );
      }

      if (assessmentYear) {
        if (assessmentYear === 'null') {
          myCasDetails.andWhere('autMycas.assessmentYear = 0');
        } else {
          myCasDetails.andWhere('autMycas.assessmentYear like :as', { as: `%${assessmentYear}%` });
        }
      }

      if (clientCategory) {
        myCasDetails.andWhere(`autMycas.formTypeCd like :clientCategory`, {
          clientCategory: clientCategory,
        });
      }

      if (sortValue) {
        switch (sortValue) {
          case 'DATE_NEWEST':
            myCasDetails.orderBy('autMycas.assignedDate', 'DESC');
            break;
          case 'DATE_OLDEST':
            myCasDetails.orderBy('autMycas.assignedDate', 'ASC');
            break;
          case 'UDIN_COMPLETED':
            myCasDetails.andWhere('autMycas.udinNumber is not null');
            break;
          case 'UDIN_PENDING':
            myCasDetails.andWhere('autMycas.udinNumber is null');
          default:
            break;
        }
      }

      if (filingValue) {
        if (filingValue === 'NA') {
          myCasDetails.andWhere('autMycas.filingType IS NULL');
        } else {
          myCasDetails.andWhere('autMycas.filingType LIKE :filingValue', {
            filingValue: `%${filingValue}%`,
          });
        }
      }

      if (offset >= 0) {
        myCasDetails.skip(offset);
      }

      if (limit) {
        myCasDetails.take(limit);
      }

      let result = await myCasDetails.getManyAndCount();
      return {
        count: result[1],
        result: result[0],
      };
    } catch (error) {
      console.log('error occured while fetching findMycas', error);
    }
  }

  async exportIncometaxMyCas(userId: number, query: any) {
    const id = query.incometaxid;
    const newQuery = { ...query, offset: 0, limit: ********* };
    let mycas = await this.findMycas(userId, newQuery);

    if (!mycas.result.length) throw new BadRequestException('No Data for Export');
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Inc Tax AR(CA)');
    const headers = [
      { header: 'S.No', key: 'serialNo' },
      { header: 'AY', key: 'ay' },
      { header: 'Client Name', key: 'clientName' },
      { header: 'PAN', key: 'pan' },
      { header: 'CA Name', key: 'caName' },
      { header: 'CA Membership #', key: 'caMembership' },
      { header: 'Filing Type', key: 'filingType' },
      { header: 'Form Name', key: 'formName' },
      { header: 'Assigned Date', key: 'assignedDate' },
      { header: 'Transaction ID', key: 'transactionId' },
      { header: 'UDIN', key: 'udin' },
    ];

    worksheet.columns = headers;
    const columnMaxLengths = Array(headers.length).fill(0);
    let serialCounter = 1; // Initialize a counter

    mycas.result.forEach((myca) => {
      const {
        client: { displayName: clientName },
      } = myca;
      const rowData = {
        serialNo: serialCounter++, // Assign and then increment the counter
        ay: myca?.assessmentYear,
        clientName: clientName,
        pan: myca?.panNumber,
        caName: myca?.caName,
        caMembership: myca?.caMembershipNum,
        filingType: getFilingType(myca?.filingType),
        formName: myca?.formTypeCd,
        assignedDate: formatDate(myca?.assignedDate),
        transactionId: myca?.transactionNo,
        udin: myca?.udinNumber,
      };

      const row = worksheet.addRow(rowData);

      worksheet.columns.forEach((column, colIndex) => {
        const headerLength = column.header?.length || 0;
        const cellLength = rowData[column.key]?.toString().length || 0;
        columnMaxLengths[colIndex] = Math.max(
          columnMaxLengths[colIndex] || 0,
          headerLength,
          cellLength,
        );
      });
    });

    worksheet.columns.forEach((column, colIndex) => {
      column.width = columnMaxLengths[colIndex] + 3;
    });
    worksheet.columns.forEach((column) => {
      if (column.key === 'clientName' || column.key === 'caName') {
        column.width = 50;
        column.alignment = { wrapText: true, horizontal: 'center', vertical: 'middle' };
      } else {
        column.alignment = { horizontal: 'center', vertical: 'middle' };
      }
    });

    const headerRow = worksheet.getRow(1);
    headerRow.font = { bold: true };
    headerRow.eachCell((cell) => {
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '64B5F6' },
      };
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' },
      };
      cell.alignment = { horizontal: 'center', vertical: 'middle' };
    });

    worksheet.eachRow((row) => {
      row.eachCell((cell) => {
        cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
      });
    });

    worksheet.views = [{ state: 'frozen', ySplit: 1 }];

    const buffer = await workbook.xlsx.writeBuffer();
    return buffer;
  }

  async getMycaFormTypes(userId: number) {
    try {
      let user = await User.findOne({
        where: { id: userId },
        relations: ['organization'],
      });

      const formTypeData = await createQueryBuilder(AutMyCas, 'autMyCas')
        .select('autMyCas.formTypeCd', 'formType')
        .where('autMyCas.organizationId = :id', { id: user.organization.id })
        .groupBy('autMyCas.formTypeCd')
        .getRawMany();

      const filteredSections = formTypeData
        .map((section) => section.formType)
        .filter((section) => section !== '' && section !== null);

      return filteredSections;
    } catch (error) {
      console.error('Error occurred while fetching form types:', error);
      throw error;
    }
  }

  async findFyaTempNotices(userId: number, query: any) {
    try {
      const { limit, offset, fromDate, toDate } = query;
      let user = await User.findOne({
        where: { id: userId },
        relations: ['organization'],
      });
      let epro = createQueryBuilder(IncTempEproFya, 'incTempEproFya')
        .leftJoinAndSelect('incTempEproFya.client', 'client')
        .leftJoinAndSelect('client.autClientCredentials', 'autClientCredentials')
        .where('incTempEproFya.organizationId = :id', { id: user?.organization?.id })
        .andWhere('client.status != :status', { status: UserStatus.DELETED })
        .andWhere('autClientCredentials.status != :disStatus', {
          disStatus: IncomeTaxStatus.DISABLE,
        });

      if (fromDate && toDate) {
        const formattedFromDate = new Date(fromDate)
          .toLocaleDateString('en-GB')
          .split('/')
          .join('-');
        const formattedToDate = new Date(toDate).toLocaleDateString('en-GB').split('/').join('-');

        // epro.andWhere(
        //   `STR_TO_DATE(incTempEproFya.noticeSentDate, '%d-%m-%Y') BETWEEN STR_TO_DATE(:fromDate, '%d-%m-%Y') AND STR_TO_DATE(:toDate, '%d-%m-%Y')`,
        //   { fromDate: formattedFromDate, toDate: formattedToDate },
        // );
        epro.andWhere(
          new Brackets((qb) => {
            qb.where(
              `STR_TO_DATE(incTempEproFya.noticeSentDate, '%d-%m-%Y') BETWEEN STR_TO_DATE(:fromDate, '%d-%m-%Y') AND STR_TO_DATE(:toDate, '%d-%m-%Y')`,
              { fromDate: formattedFromDate, toDate: formattedToDate },
            )
              .orWhere(
                `STR_TO_DATE(incTempEproFya.proceedingLimitationDate, '%d-%m-%Y') BETWEEN STR_TO_DATE(:fromDate, '%d-%m-%Y') AND STR_TO_DATE(:toDate, '%d-%m-%Y')`,
                { fromDate: formattedFromDate, toDate: formattedToDate },
              )
              .orWhere(
                `STR_TO_DATE(incTempEproFya.dateOfCompliance, '%d-%m-%Y') BETWEEN STR_TO_DATE(:fromDate, '%d-%m-%Y') AND STR_TO_DATE(:toDate, '%d-%m-%Y')`,
                { fromDate: formattedFromDate, toDate: formattedToDate },
              )
              .orWhere(
                `STR_TO_DATE(incTempEproFya.dateResponseSubmitted, '%d-%m-%Y') BETWEEN STR_TO_DATE(:fromDate, '%d-%m-%Y') AND STR_TO_DATE(:toDate, '%d-%m-%Y')`,
                { fromDate: formattedFromDate, toDate: formattedToDate },
              );
          }),
        );
      }

      if (query.search) {
        epro.andWhere(
          new Brackets((qb) => {
            qb.where('incTempEproFya.pan LIKE :pansearch', {
              pansearch: `%${query.search}%`,
            });
            qb.orWhere('client.displayName LIKE :namesearch', {
              namesearch: `%${query.search}%`,
            });
            qb.orWhere('incTempEproFya.proceedingName LIKE :prosearch', {
              prosearch: `%${query.search}%`,
            });
          }),
        );
      }

      if (query.section) {
        if (query.section === 'null') {
          epro.andWhere('incTempEproFya.noticeSection is NULL');
        } else {
          epro.andWhere('incTempEproFya.noticeSection like :sectionsearch', {
            sectionsearch: `%${query.section}%`,
          });
        }
      }

      if (query.assessmentYear) {
        if (query.assessmentYear === 'null') {
          epro.andWhere('incTempEproFya.ay = 0');
        } else {
          epro.andWhere('incTempEproFya.ay like :as', {
            as: `%${query.assessmentYear}%`,
          });
        }
      }

      if (query.clientCategory) {
        epro.andWhere(`client.category = :clientCategory`, {
          clientCategory: query.clientCategory,
        });
      }
      const sort = typeof query?.sort === 'string' ? JSON.parse(query.sort) : query?.sort;
      if (sort?.column) {
        const columnMap: Record<string, string> = {
          assesmentYear: 'incTempEproFya.ay',
          type: 'incTempEproFya.type',
          category: 'client.category',
          displayName: 'client.displayName',
          noticeSentDate: 'incTempEproFya.noticeSentDate',
          proceedingName: 'incTempEproFya.proceedingName',
          proceedingStatus: 'incTempEproFya.proceedingStatus',
          dateOfCompliance: 'incTempEproFya.dateOfCompliance',
          dateResponseSubmitted: 'incTempEproFya.dateResponseSubmitted',
          noticeSection: 'incTempEproFya.noticeSection',
          proceedingLimitationDate: 'incTempEproFya.proceedingLimitationDate',
        };
        const column = columnMap[sort.column] || sort.column;
        epro.orderBy(column, sort.direction.toUpperCase());
      }

      if (query.caseStatus) {
        epro.andWhere('incTempEproFya.proceedingStatus LIKE :caseStatus', {
          caseStatus: query?.caseStatus,
        });
      }

      epro.addSelect(`STR_TO_DATE(incTempEproFya.noticeSentDate, '%d-%m-%Y')`, 'parsedDate')
      .orderBy('parsedDate', 'DESC');

      if (offset) {
        epro.skip(offset);
      }

      if (limit) {
        epro.take(limit);
      }

      let result = await epro.getManyAndCount();
      return {
        count: result[1],
        result: result[0],
      };
    } catch (error) {
      console.log('error occured while fetching getIncomeTaxEproceedings', error);
    }
  }
  async exportIncomeTaxTempEproceedingsFyA(userId: number, query: any) {
    const newQuery = { ...query, offset: 0, limit: ********* };
    let clients = await this.findFyaTempNotices(userId, newQuery);
    if (!clients.result.length) throw new BadRequestException('No Data for Export');
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('e-Proceedings (FYA)');
    const headers = [
      { header: 'S.No', key: 'serialNo' },
      { header: 'AY', key: 'ay' },
      { header: 'Notice Sent date', key: 'sentDate' },
      { header: 'Type', key: 'type' },
      { header: 'Category', key: 'category' },
      { header: 'Client Name', key: 'displayName' },
      { header: 'PAN', key: 'pan' },
      { header: 'Proceeding Name', key: 'proceedingName' },
      { header: 'Section', key: 'section' },
      { header: 'Notice DIN', key: 'din' },
      { header: 'Compliance Date', key: 'complianceDate' },
      { header: 'Response Sumbitted', key: 'responseSubmitted' },
      { header: 'Proceeding Status', key: 'proceedingStatus' },
      { header: 'Limitation Date', key: 'proceedingLimitationDate' },
    ];

    worksheet.columns = headers;
    const columnMaxLengths = Array(headers.length).fill(0);
    let serialCounter = 1; // Initialize a counter

    clients.result.forEach((client) => {
      const rowData = {
        serialNo: serialCounter++, // Assign and then increment the counter
        ay: client?.ay === '0-01' ? 'NA' : client?.ay || 'NA',
        type: capitalize(client?.type),
        category: client?.client?.category,
        displayName: client?.client?.displayName,
        pan: client?.pan,
        sentDate: client?.noticeSentDate,
        proceedingName: client?.proceedingName,
        proceedingStatus: client?.proceedingStatus,
        din: client?.noticeDin,
        complianceDate: client?.dateOfCompliance,
        responseSubmitted: client?.dateResponseSubmitted,
        section: client?.noticeSection,
        proceedingLimitationDate: client?.proceedingLimitationDate,
      };

      const row = worksheet.addRow(rowData);
      // Apply conditional coloring for the "Type" column

      const typeCell = row.getCell('type'); // Get the cell for the "Type" column
      if (rowData.type === 'Self') {
        typeCell.font = {
          color: { argb: '4B0082' },
          bold: true, // Bold text
        };
      } else if (rowData.type === 'Other') {
        typeCell.font = {
          color: { argb: 'FF8C00' },
          bold: true, // Bold text
        };
      }
      const statusCell = row.getCell('proceedingStatus');

      if (rowData.proceedingStatus === 'Open') {
        statusCell.font = {
          color: { argb: '228B22' },
          bold: true,
        };
      } else if (rowData.proceedingStatus === 'Pending') {
        statusCell.font = {
          color: { argb: 'FFA500' },
          bold: true,
        };
      } else if (rowData.proceedingStatus === 'Blocked') {
        statusCell.font = {
          color: { argb: 'DC143C' },
          bold: true,
        };
      } else if (rowData.proceedingStatus === 'Re-enabled') {
        statusCell.font = {
          color: { argb: '1E90FF' },
          bold: true,
        };
      }

      worksheet.columns.forEach((column, colIndex) => {
        const headerLength = column.header?.length || 0;
        const cellLength = rowData[column.key]?.toString().length || 0;
        columnMaxLengths[colIndex] = Math.max(
          columnMaxLengths[colIndex] || 0,
          headerLength,
          cellLength,
        );
      });
    });

    worksheet.columns.forEach((column, colIndex) => {
      column.width = columnMaxLengths[colIndex] + 2;
    });

    const headerRow = worksheet.getRow(1);
    headerRow.font = { bold: true };
    headerRow.eachCell((cell) => {
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '64B5F6' },
      };
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' },
      };
      cell.alignment = { horizontal: 'center', vertical: 'middle' };
    });

    worksheet.eachRow((row) => {
      row.eachCell((cell) => {
        cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
      });
    });

    worksheet.views = [{ state: 'frozen', ySplit: 1 }];

    const buffer = await workbook.xlsx.writeBuffer();
    return buffer;
  }
  async findFyiTempNotices(userId: number, query: any) {
    try {
      const { limit, offset, fromDate, toDate } = query;
      let user = await User.findOne({
        where: { id: userId },
        relations: ['organization'],
      });
      let epro = createQueryBuilder(IncTempEproFyi, 'incTempEproFyi')
        .leftJoinAndSelect('incTempEproFyi.client', 'client')
        .leftJoinAndSelect('client.autClientCredentials', 'autClientCredentials')
        .where('incTempEproFyi.organizationId = :id', { id: user?.organization?.id })
        .andWhere('client.status != :status', { status: UserStatus.DELETED })
        .andWhere('autClientCredentials.status != :disStatus', {
          disStatus: IncomeTaxStatus.DISABLE,
        });

      if (fromDate && toDate) {
        const formattedFromDate = new Date(fromDate)
          .toLocaleDateString('en-GB')
          .split('/')
          .join('-');
        const formattedToDate = new Date(toDate).toLocaleDateString('en-GB').split('/').join('-');

        // epro.andWhere(
        //   `STR_TO_DATE(incTempEproFyi.noticeSentDate, '%d-%m-%Y') BETWEEN STR_TO_DATE(:fromDate, '%d-%m-%Y') AND STR_TO_DATE(:toDate, '%d-%m-%Y')`,
        //   { fromDate: formattedFromDate, toDate: formattedToDate },
        // );
        epro.andWhere(
          new Brackets((qb) => {
            qb.where(
              `STR_TO_DATE(incTempEproFyi.noticeSentDate, '%d-%m-%Y') BETWEEN STR_TO_DATE(:fromDate, '%d-%m-%Y') AND STR_TO_DATE(:toDate, '%d-%m-%Y')`,
              { fromDate: formattedFromDate, toDate: formattedToDate },
            )
              .orWhere(
                `STR_TO_DATE(incTempEproFyi.proceedingLimitationDate, '%d-%m-%Y') BETWEEN STR_TO_DATE(:fromDate, '%d-%m-%Y') AND STR_TO_DATE(:toDate, '%d-%m-%Y')`,
                { fromDate: formattedFromDate, toDate: formattedToDate },
              )
              .orWhere(
                `STR_TO_DATE(incTempEproFyi.dateOfCompliance, '%d-%m-%Y') BETWEEN STR_TO_DATE(:fromDate, '%d-%m-%Y') AND STR_TO_DATE(:toDate, '%d-%m-%Y')`,
                { fromDate: formattedFromDate, toDate: formattedToDate },
              )
              .orWhere(
                `STR_TO_DATE(incTempEproFyi.dateResponseSubmitted, '%d-%m-%Y') BETWEEN STR_TO_DATE(:fromDate, '%d-%m-%Y') AND STR_TO_DATE(:toDate, '%d-%m-%Y')`,
                { fromDate: formattedFromDate, toDate: formattedToDate },
              );
          }),
        );
      }

      if (query.caseStatus) {
        epro.andWhere('incTempEproFyi.proceedingStatus LIKE :caseStatus', {
          caseStatus: query?.caseStatus,
        });
      }

      if (query.search) {
        epro.andWhere(
          new Brackets((qb) => {
            qb.where('incTempEproFyi.pan LIKE :pansearch', {
              pansearch: `%${query.search}%`,
            });
            qb.orWhere('client.displayName LIKE :namesearch', {
              namesearch: `%${query.search}%`,
            });
            qb.orWhere('incTempEproFyi.proceedingName LIKE :prosearch', {
              prosearch: `%${query.search}%`,
            });
          }),
        );
      }

      if (query.section) {
        if (query.section === 'null') {
          epro.andWhere('incTempEproFyi.noticeSection is NUll');
        } else {
          epro.andWhere('incTempEproFyi.noticeSection like :sectionsearch', {
            sectionsearch: `%${query.section}%`,
          });
        }
      }

      if (query.assessmentYear) {
        if (query.assessmentYear === 'null') {
          epro.andWhere('incTempEproFyi.ay = 0');
        } else {
          epro.andWhere('incTempEproFyi.ay like :as', {
            as: `%${query.assessmentYear}%`,
          });
        }
      }

      if (query.clientCategory) {
        epro.andWhere(`client.category = :clientCategory`, {
          clientCategory: query.clientCategory,
        });
      }
      const sort = typeof query?.sort === 'string' ? JSON.parse(query.sort) : query?.sort;
      if (sort?.column) {
        const columnMap: Record<string, string> = {
          assesmentYear: 'incTempEproFyi.ay',
          type: 'incTempEproFyi.type',
          category: 'client.category',
          displayName: 'client.displayName',
          noticeSentDate: 'incTempEproFyi.noticeSentDate',
          proceedingName: 'incTempEproFyi.proceedingName',
          proceedingStatus: 'incTempEproFyi.proceedingStatus',
          dateOfCompliance: 'incTempEproFyi.dateOfCompliance',
          dateResponseSubmitted: 'incTempEproFyi.dateResponseSubmitted',
          noticeSection: 'incTempEproFyi.noticeSection',
          proceedingLimitationDate: 'incTempEproFyi.proceedingLimitationDate',
        };
        const column = columnMap[sort.column] || sort.column;
        epro.orderBy(column, sort.direction.toUpperCase());
      }
      
      epro.addSelect(`STR_TO_DATE(incTempEproFyi.noticeSentDate, '%d-%m-%Y')`, 'parsedDate')
      .orderBy('parsedDate', 'DESC');

      if (offset) {
        epro.skip(offset);
      }

      if (limit) {
        epro.take(limit);
      }
      let result = await epro.getManyAndCount();
      return {
        count: result[1],
        result: result[0],
      };
    } catch (error) {
      console.log('error occured while fetching getIncomeTaxFyiEproceedings', error);
    }
  }
  async exportIncomeTaxTempEproceedingsFyi(userId: number, query: any) {
    const newQuery = { ...query, offset: 0, limit: ********* };
    let clients = await this.findFyiTempNotices(userId, newQuery);
    if (!clients.result.length) throw new BadRequestException('No Data for Export');
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('e-Proceedings (FYI)');
    const headers = [
      { header: 'S.No', key: 'serialNo' },
      { header: 'AY', key: 'ay' },
      { header: 'Notice Sent date', key: 'sentDate' },
      { header: 'Type', key: 'type' },
      { header: 'Category', key: 'category' },
      { header: 'Client Name', key: 'displayName' },
      { header: 'PAN', key: 'pan' },
      { header: 'Proceeding Name', key: 'proceedingName' },
      { header: 'Section', key: 'section' },
      { header: 'Notice DIN', key: 'din' },
      { header: 'Compliance Date', key: 'complianceDate' },
      { header: 'Response Sumbitted', key: 'responseSubmitted' },
      { header: 'Proceeding Status', key: 'proceedingStatus' },
      { header: 'Limitation Date', key: 'proceedingLimitationDate' },
    ];

    worksheet.columns = headers;
    const columnMaxLengths = Array(headers.length).fill(0);
    let serialCounter = 1; // Initialize a counter

    clients.result.forEach((client) => {
      const rowData = {
        serialNo: serialCounter++, // Assign and then increment the counter
        ay: client?.ay === '0-01' ? 'NA' : client?.ay || 'NA',
        type: capitalize(client?.type),
        category: client?.client?.category,
        displayName: client?.client?.displayName,
        pan: client?.pan,
        sentDate: client?.noticeSentDate,
        proceedingName: client?.proceedingName,
        proceedingStatus: client?.proceedingStatus,
        din: client?.noticeDin,
        complianceDate: client?.dateOfCompliance,
        responseSubmitted: client?.dateResponseSubmitted,
        section: client?.noticeSection,
        proceedingLimitationDate: client?.proceedingLimitationDate,
      };

      const row = worksheet.addRow(rowData);
      // Apply conditional coloring for the "Type" column

      const typeCell = row.getCell('type'); // Get the cell for the "Type" column
      if (rowData.type === 'Self') {
        typeCell.font = {
          color: { argb: '4B0082' },
          bold: true, // Bold text
        };
      } else if (rowData.type === 'Other') {
        typeCell.font = {
          color: { argb: 'FF8C00' },
          bold: true, // Bold text
        };
      }
      const statusCell = row.getCell('proceedingStatus');
      if (rowData.proceedingStatus === 'Closed') {
        statusCell.font = {
          color: { argb: 'A9A9A9' }, // Grey
          bold: true,
        };
      } else if (rowData.proceedingStatus === 'Submitted') {
        statusCell.font = {
          color: { argb: '008080' }, // Teal
          bold: true,
        };
      } else if (rowData.proceedingStatus === 'Lapsed') {
        statusCell.font = {
          color: { argb: '800000' }, // Maroon
          bold: true,
        };
      }

      worksheet.columns.forEach((column, colIndex) => {
        const headerLength = column.header?.length || 0;
        const cellLength = rowData[column.key]?.toString().length || 0;
        columnMaxLengths[colIndex] = Math.max(
          columnMaxLengths[colIndex] || 0,
          headerLength,
          cellLength,
        );
      });
    });

    worksheet.columns.forEach((column, colIndex) => {
      column.width = columnMaxLengths[colIndex] + 2;
    });

    const headerRow = worksheet.getRow(1);
    headerRow.font = { bold: true };
    headerRow.eachCell((cell) => {
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '64B5F6' },
      };
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' },
      };
      cell.alignment = { horizontal: 'center', vertical: 'middle' };
    });

    worksheet.eachRow((row) => {
      row.eachCell((cell) => {
        cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
      });
    });

    worksheet.views = [{ state: 'frozen', ySplit: 1 }];

    const buffer = await workbook.xlsx.writeBuffer();
    return buffer;
  }
  async getClientExcelProceedingFyi(id: number, query: any, userId: number) {
    const user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
    const { limit, offset, fromDate, toDate } = query;
    try {
      const clientCredential = await AutClientCredentials.findOne({
        where: { id, organizationId: user?.organization?.id },
        relations: ['client'],
      });
      if (clientCredential) {
        let clientEpro = createQueryBuilder(IncTempEproFyi, 'incTempEproFyi')
          .leftJoinAndSelect('incTempEproFyi.client', 'client')
          .where('client.id = :id', { id: clientCredential?.client?.id });
        // clientEpro.orderBy('incTempEproFyi.noticeSentDate', 'DESC');

        if (fromDate && toDate) {
          const formattedFromDate = new Date(fromDate)
            .toLocaleDateString('en-GB')
            .split('/')
            .join('-');
          const formattedToDate = new Date(toDate).toLocaleDateString('en-GB').split('/').join('-');

          // clientEpro.andWhere(
          //   `STR_TO_DATE(incTempEproFyi.noticeSentDate, '%d-%m-%Y') BETWEEN STR_TO_DATE(:fromDate, '%d-%m-%Y') AND STR_TO_DATE(:toDate, '%d-%m-%Y')`,
          //   { fromDate: formattedFromDate, toDate: formattedToDate },
          // );
          clientEpro.andWhere(
            new Brackets((qb) => {
              qb.where(
                `STR_TO_DATE(incTempEproFyi.noticeSentDate, '%d-%m-%Y') BETWEEN STR_TO_DATE(:fromDate, '%d-%m-%Y') AND STR_TO_DATE(:toDate, '%d-%m-%Y')`,
                { fromDate: formattedFromDate, toDate: formattedToDate },
              )
                .orWhere(
                  `STR_TO_DATE(incTempEproFyi.proceedingLimitationDate, '%d-%m-%Y') BETWEEN STR_TO_DATE(:fromDate, '%d-%m-%Y') AND STR_TO_DATE(:toDate, '%d-%m-%Y')`,
                  { fromDate: formattedFromDate, toDate: formattedToDate },
                )
                .orWhere(
                  `STR_TO_DATE(incTempEproFyi.dateOfCompliance, '%d-%m-%Y') BETWEEN STR_TO_DATE(:fromDate, '%d-%m-%Y') AND STR_TO_DATE(:toDate, '%d-%m-%Y')`,
                  { fromDate: formattedFromDate, toDate: formattedToDate },
                )
                .orWhere(
                  `STR_TO_DATE(incTempEproFyi.dateResponseSubmitted, '%d-%m-%Y') BETWEEN STR_TO_DATE(:fromDate, '%d-%m-%Y') AND STR_TO_DATE(:toDate, '%d-%m-%Y')`,
                  { fromDate: formattedFromDate, toDate: formattedToDate },
                );
            }),
          );
        }

        if (query.caseStatus) {
          clientEpro.andWhere('incTempEproFyi.proceedingStatus LIKE :caseStatus', {
            caseStatus: query?.caseStatus,
          });
        }

        if (query.search) {
          clientEpro.andWhere('incTempEproFyi.proceedingName LIKE :prosearch', {
            prosearch: `%${query.search}%`,
          });
        }

        if (query.section) {
          if (query.section === 'null') {
            clientEpro.andWhere('incTempEproFyi.noticeSection is NULL');
          } else {
            clientEpro.andWhere('incTempEproFyi.noticeSection like :sectionsearch', {
              sectionsearch: `%${query.section}%`,
            });
          }
        }

        if (query.assessmentYear) {
          if (query.assessmentYear === 'null') {
            clientEpro.andWhere('incTempEproFyi.ay = 0');
          } else {
            clientEpro.andWhere('incTempEproFyi.ay like :as', {
              as: `%${query.assessmentYear}%`,
            });
          }
        }
        const sort = typeof query?.sort === 'string' ? JSON.parse(query.sort) : query?.sort;
        if (sort?.column) {
          const columnMap: Record<string, string> = {
            ay: 'incTempEproFyi.ay',
            type: 'incTempEproFyi.type',
            noticeSentDate: 'incTempEproFyi.noticeSentDate',
            proceedingName: 'incTempEproFyi.proceedingName',
            proceedingStatus: 'incTempEproFyi.proceedingStatus',
            dateOfCompliance: 'incTempEproFyi.dateOfCompliance',
            dateResponseSubmitted: 'incTempEproFyi.dateResponseSubmitted',
            noticeSection: 'incTempEproFyi.noticeSection',
            proceedingLimitationDate: 'incTempEproFyi.proceedingLimitationDate',
          };
          const column = columnMap[sort.column] || sort.column;
          clientEpro.orderBy(column, sort.direction.toUpperCase());
        } else {
          clientEpro.orderBy('incTempEproFyi.createdAt', 'DESC');
        }
        if (offset >= 0) {
          clientEpro.skip(offset);
        }

        if (limit) {
          clientEpro.take(limit);
        }

        clientEpro.addSelect(
          `STR_TO_DATE(incTempEproFyi.noticeSentDate, '%d-%m-%Y')`,
          'parsedDate',
        );
        // .orderBy('parsedDate', 'DESC');

        let result = await clientEpro.getManyAndCount();
        return {
          count: result[1],
          result: result[0],
          accessDenied: true,
        };
      } else {
        return { accessDenied: false };
      }
    } catch (error) {
      console.log('Error occurred while fetching income tax excel-fyi', error);
    }
  }
  async exportClientTempNoticeFYI(userId: number, query: any) {
    const newQuery = { ...query, offset: 0, limit: ********* };
    const id = query.incometaxid;
    let clients = await this.getClientExcelProceedingFyi(id, newQuery, userId);
    if (!clients.result.length) throw new BadRequestException('No Data for Export');
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('e-Proceedings (FYI)');
    const headers = [
      { header: 'S.No', key: 'serialNo' },
      { header: 'AY', key: 'ay' },
      { header: 'Type', key: 'type' },
      { header: 'Notice Sent date', key: 'sentDate' },
      { header: 'Proceeding Name', key: 'proceedingName' },
      { header: 'Proceeding Status', key: 'proceedingStatus' },
      { header: 'Notice DIN', key: 'din' },
      { header: 'Compliance Date', key: 'complianceDate' },
      { header: 'Response Sumbitted', key: 'responseSubmitted' },
      { header: 'Section', key: 'section' },
      { header: 'Limitation Date', key: 'proceedingLimitationDate' },
    ];

    worksheet.columns = headers;
    const columnMaxLengths = Array(headers.length).fill(0);
    let serialCounter = 1; // Initialize a counter

    clients.result.forEach((client) => {
      const rowData = {
        serialNo: serialCounter++, // Assign and then increment the counter
        ay: client?.ay === '0-01' ? 'NA' : client?.ay || 'NA',
        type: capitalize(client?.type),
        sentDate: client?.noticeSentDate,
        proceedingName: client?.proceedingName,
        proceedingStatus: client?.proceedingStatus,
        din: client?.noticeDin,
        complianceDate: client?.dateOfCompliance,
        responseSubmitted: client?.dateResponseSubmitted,
        section: client?.noticeSection,
        proceedingLimitationDate: client?.proceedingLimitationDate,
      };

      const row = worksheet.addRow(rowData);
      // Apply conditional coloring for the "Type" column
      const typeCell = row.getCell('type'); // Get the cell for the "Type" column
      if (rowData.type === 'Self') {
        typeCell.font = {
          color: { argb: '4B0082' },
          bold: true, // Bold text
        };
      } else if (rowData.type === 'Other') {
        typeCell.font = {
          color: { argb: 'FF8C00' },
          bold: true, // Bold text
        };
      }
      const statusCell = row.getCell('proceedingStatus');
      if (rowData.proceedingStatus === 'Closed') {
        statusCell.font = {
          color: { argb: 'A9A9A9' }, // Grey
          bold: true,
        };
      } else if (rowData.proceedingStatus === 'Submitted') {
        statusCell.font = {
          color: { argb: '008080' }, // Teal
          bold: true,
        };
      } else if (rowData.proceedingStatus === 'Lapsed') {
        statusCell.font = {
          color: { argb: '800000' }, // Maroon
          bold: true,
        };
      }

      worksheet.columns.forEach((column, colIndex) => {
        const headerLength = column.header?.length || 0;
        const cellLength = rowData[column.key]?.toString().length || 0;
        columnMaxLengths[colIndex] = Math.max(
          columnMaxLengths[colIndex] || 0,
          headerLength,
          cellLength,
        );
      });
    });

    worksheet.columns.forEach((column, colIndex) => {
      column.width = columnMaxLengths[colIndex] + 2;
    });

    const headerRow = worksheet.getRow(1);
    headerRow.font = { bold: true };
    headerRow.eachCell((cell) => {
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '64B5F6' },
      };
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' },
      };
      cell.alignment = { horizontal: 'center', vertical: 'middle' };
    });

    worksheet.eachRow((row) => {
      row.eachCell((cell) => {
        cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
      });
    });

    worksheet.views = [{ state: 'frozen', ySplit: 1 }];

    const buffer = await workbook.xlsx.writeBuffer();
    return buffer;
  }
  async getClientExcelProceedingFya(id: number, query: any, userId: number) {
    const { limit, offset, fromDate, toDate } = query;
    const user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
    try {
      const clientCredential = await AutClientCredentials.findOne({
        where: { id, organizationId: user?.organization?.id },
        relations: ['client'],
      });
      if (clientCredential) {
        const clientEpro = createQueryBuilder(IncTempEproFya, 'incTempEproFya')
          .leftJoinAndSelect('incTempEproFya.client', 'client')
          .where('client.id =:id', { id: clientCredential?.client?.id });

        clientEpro
          .addSelect(`STR_TO_DATE(incTempEproFya.noticeSentDate, '%d-%m-%Y')`, 'parsedDate')
          .orderBy('parsedDate', 'DESC');

        if (fromDate && toDate) {
          const formattedFromDate = new Date(fromDate)
            .toLocaleDateString('en-GB')
            .split('/')
            .join('-');
          const formattedToDate = new Date(toDate).toLocaleDateString('en-GB').split('/').join('-');

          // clientEpro.andWhere(
          //   `STR_TO_DATE(incTempEproFya.noticeSentDate, '%d-%m-%Y') BETWEEN STR_TO_DATE(:fromDate, '%d-%m-%Y') AND STR_TO_DATE(:toDate, '%d-%m-%Y') `,
          //   { fromDate: formattedFromDate, toDate: formattedToDate },
          // );
          clientEpro.andWhere(
            new Brackets((qb) => {
              qb.where(
                `STR_TO_DATE(incTempEproFya.noticeSentDate, '%d-%m-%Y') BETWEEN STR_TO_DATE(:fromDate, '%d-%m-%Y') AND STR_TO_DATE(:toDate, '%d-%m-%Y')`,
                { fromDate: formattedFromDate, toDate: formattedToDate },
              )
                .orWhere(
                  `STR_TO_DATE(incTempEproFya.proceedingLimitationDate, '%d-%m-%Y') BETWEEN STR_TO_DATE(:fromDate, '%d-%m-%Y') AND STR_TO_DATE(:toDate, '%d-%m-%Y')`,
                  { fromDate: formattedFromDate, toDate: formattedToDate },
                )
                .orWhere(
                  `STR_TO_DATE(incTempEproFya.dateOfCompliance, '%d-%m-%Y') BETWEEN STR_TO_DATE(:fromDate, '%d-%m-%Y') AND STR_TO_DATE(:toDate, '%d-%m-%Y')`,
                  { fromDate: formattedFromDate, toDate: formattedToDate },
                )
                .orWhere(
                  `STR_TO_DATE(incTempEproFya.dateResponseSubmitted, '%d-%m-%Y') BETWEEN STR_TO_DATE(:fromDate, '%d-%m-%Y') AND STR_TO_DATE(:toDate, '%d-%m-%Y')`,
                  { fromDate: formattedFromDate, toDate: formattedToDate },
                );
            }),
          );
        }

        if (query.caseStatus) {
          clientEpro.andWhere('incTempEproFya.proceedingStatus LIKE :caseStatus', {
            caseStatus: query?.caseStatus,
          });
        }

        if (query.search) {
          clientEpro.andWhere('incTempEproFya.proceedingName LIKE :prosearch', {
            prosearch: `%${query.search}%`,
          });
        }

        if (query.section) {
          if (query.section === 'null') {
            clientEpro.andWhere('incTempEproFya.noticeSection is NULL');
          } else {
            clientEpro.andWhere('incTempEproFya.noticeSection like :sectionsearch', {
              sectionsearch: `%${query.section}%`,
            });
          }
        }

        if (query.assessmentYear) {
          if (query.assessmentYear === 'null') {
            clientEpro.andWhere('incTempEproFya.ay = 0');
          } else {
            clientEpro.andWhere('incTempEproFya.ay like :as', {
              as: `%${query.assessmentYear}%`,
            });
          }
        }
        const sort = typeof query?.sort === 'string' ? JSON.parse(query.sort) : query?.sort;
        if (sort?.column) {
          const columnMap: Record<string, string> = {
            assesmentYear: 'incTempEproFya.ay',
            type: 'incTempEproFya.type',
            noticeSentDate: 'incTempEproFya.noticeSentDate',
            proceedingName: 'incTempEproFya.proceedingName',
            proceedingStatus: 'incTempEproFya.proceedingStatus',
            dateOfCompliance: 'incTempEproFya.dateOfCompliance',
            dateResponseSubmitted: 'incTempEproFya.dateResponseSubmitted',
            noticeSection: 'incTempEproFya.noticeSection',
            proceedingLimitationDate: 'incTempEproFya.proceedingLimitationDate',
          };
          const column = columnMap[sort.column] || sort.column;
          clientEpro.orderBy(column, sort.direction.toUpperCase());
        } else {
          clientEpro.orderBy('incTempEproFya.createdAt', 'DESC');
        }

        if (offset >= 0) {
          clientEpro.skip(offset);
        }

        if (limit) {
          clientEpro.take(limit);
        }

        let result = await clientEpro.getManyAndCount();
        return {
          count: result[1],
          result: result[0],
          accessDenied: true,
        };
      } else {
        return { accessDenied: false };
      }
    } catch (error) {
      console.log('error occured while fetching income tax excel-fya', error);
    }
  }
  async exportClientTempNoticeFYA(userId: number, query: any) {
    const newQuery = { ...query, offset: 0, limit: ********* };
    const id = query.incometaxid;
    let clients = await this.getClientExcelProceedingFya(id, newQuery, userId);
    if (!clients?.result?.length) throw new BadRequestException('No Data for Export');
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('e-Proceedings (FYA)');
    const headers = [
      { header: 'S.No', key: 'serialNo' },
      { header: 'AY', key: 'ay' },
      { header: 'Type', key: 'type' },
      { header: 'Notice Sent date', key: 'sentDate' },
      { header: 'Proceeding Name', key: 'proceedingName' },
      { header: 'Proceeding Status', key: 'proceedingStatus' },
      { header: 'Notice DIN', key: 'din' },
      { header: 'Compliance Date', key: 'complianceDate' },
      { header: 'Response Sumbitted', key: 'responseSubmitted' },
      { header: 'Section', key: 'section' },
      { header: 'Limitation Date', key: 'proceedingLimitationDate' },
    ];

    worksheet.columns = headers;
    const columnMaxLengths = Array(headers.length).fill(0);
    let serialCounter = 1; // Initialize a counter

    clients.result.forEach((client) => {
      const rowData = {
        serialNo: serialCounter++, // Assign and then increment the counter
        ay: client?.ay === '0-01' ? 'NA' : client?.ay || 'NA',
        type: capitalize(client?.type),
        sentDate: client?.noticeSentDate,
        proceedingName: client?.proceedingName,
        proceedingStatus: client?.proceedingStatus,
        din: client?.noticeDin,
        complianceDate: client?.dateOfCompliance,
        responseSubmitted: client?.dateResponseSubmitted,
        section: client?.noticeSection,
        proceedingLimitationDate: client?.proceedingLimitationDate,
      };

      const row = worksheet.addRow(rowData);
      // Apply conditional coloring for the "Type" column
      const typeCell = row.getCell('type'); // Get the cell for the "Type" column
      if (rowData.type === 'Self') {
        typeCell.font = {
          color: { argb: '4B0082' },
          bold: true, // Bold text
        };
      } else if (rowData.type === 'Other') {
        typeCell.font = {
          color: { argb: 'FF8C00' },
          bold: true, // Bold text
        };
      }
      const statusCell = row.getCell('proceedingStatus');

      if (rowData.proceedingStatus === 'Open') {
        statusCell.font = {
          color: { argb: '228B22' },
          bold: true,
        };
      } else if (rowData.proceedingStatus === 'Pending') {
        statusCell.font = {
          color: { argb: 'FFA500' },
          bold: true,
        };
      } else if (rowData.proceedingStatus === 'Blocked') {
        statusCell.font = {
          color: { argb: 'DC143C' },
          bold: true,
        };
      } else if (rowData.proceedingStatus === 'Re-enabled') {
        statusCell.font = {
          color: { argb: '1E90FF' },
          bold: true,
        };
      }

      worksheet.columns.forEach((column, colIndex) => {
        const headerLength = column.header?.length || 0;
        const cellLength = rowData[column.key]?.toString().length || 0;
        columnMaxLengths[colIndex] = Math.max(
          columnMaxLengths[colIndex] || 0,
          headerLength,
          cellLength,
        );
      });
    });

    worksheet.columns.forEach((column, colIndex) => {
      column.width = columnMaxLengths[colIndex] + 2;
    });

    const headerRow = worksheet.getRow(1);
    headerRow.font = { bold: true };
    headerRow.eachCell((cell) => {
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '64B5F6' },
      };
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' },
      };
      cell.alignment = { horizontal: 'center', vertical: 'middle' };
    });

    worksheet.eachRow((row) => {
      row.eachCell((cell) => {
        cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
      });
    });

    worksheet.views = [{ state: 'frozen', ySplit: 1 }];

    const buffer = await workbook.xlsx.writeBuffer();
    return buffer;
  }

  async getExcelFyaSections(userId: number) {
    try {
      let user = await User.findOne({
        where: { id: userId },
        relations: ['organization'],
      });

      const sectionsData = await createQueryBuilder(IncTempEproFya, 'incTempEproFya')
        .select('incTempEproFya.noticeSection', 'noticeSection')
        .where('incTempEproFya.organizationId = :id', { id: user.organization.id })
        .groupBy('incTempEproFya.noticeSection')
        .getRawMany();

      const filteredSections = sectionsData
        .map((section) => section.noticeSection)
        .filter((section) => section !== '' && section !== null && section !== 'null');

      return filteredSections;
    } catch (error) {
      console.error('Error occurred while fetching excel-fya-sections:', error);
      throw error;
    }
  }

  async getExcelFyiSections(userId: number) {
    try {
      let user = await User.findOne({
        where: { id: userId },
        relations: ['organization'],
      });

      const sectionsData = await createQueryBuilder(IncTempEproFyi, 'incTempEproFyi')
        .select('incTempEproFyi.noticeSection', 'noticeSection')
        .where('incTempEproFyi.organizationId = :id', { id: user.organization.id })
        .groupBy('incTempEproFyi.noticeSection')
        .getRawMany();

      const filteredSections = sectionsData
        .map((section) => section.noticeSection)
        .filter((section) => section !== '' && section !== null && section !== 'null');

      return filteredSections;
    } catch (error) {
      console.error('Error occurred while fetching excel-fyi-sections:', error);
      throw error;
    }
  }
}
