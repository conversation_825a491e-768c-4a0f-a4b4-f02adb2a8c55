import { Injectable } from '@nestjs/common';
import { OnEvent } from '@nestjs/event-emitter';
import { sendNotification } from 'src/notifications/notify';
import { User } from 'src/modules/users/entities/user.entity';
import { createQueryBuilder } from 'typeorm';
import { Event_Actions } from '../actions';

interface AddExpenditure {
  userId: number;
  taskId: number;
  taskName: string;
}

@Injectable()
export class ExpenditureListener {
  @OnEvent(Event_Actions.EXPENDITURE_ADDED, { async: true })
  async handleAddExpenditure(event: AddExpenditure) {
    try {
      const { userId, taskName } = event;
      let user = await createQueryBuilder(User, 'user')
        .leftJoinAndSelect('user.organization', 'organization')
        .leftJoinAndSelect('organization.users', 'users')
        .leftJoinAndSelect('users.role', 'role')
        .where('user.id = :id', { id: userId })
        .getOne();

      let orgAdmin = user.organization.users.find(
        (user: User) => user.role.defaultRole,
      );

      if (!orgAdmin) return;

      let userIds = [orgAdmin.id];

      let notification = {
        title: 'Expenditure Added',
        body: `${user.fullName} has added expenditure for ${taskName}`,
      };

      await sendNotification(userIds, notification);
    } catch (err) {
      console.log(err);
    }
  }
}
