import { Injectable } from '@nestjs/common';
import { User } from 'src/modules/users/entities/user.entity';
import { createQueryBuilder } from 'typeorm';
import { CreatePosterDto } from './dto/create-poster.dto';
import PosterConfig from './poster-config.entity';
import * as sharp from 'sharp';
import Storage, { StorageSystem, StorageType } from '../storage/storage.entity';
import { AwsService } from '../storage/upload.service';
import PosterEvents from './poster-events.entity';
import PosterEventTypes from './poster-event-types.entity';
import Event from 'src/modules/events/event.entity';
import { OneDriveStorageService } from 'src/modules/ondrive-storage/onedrive-storage.service';
import { BharathCloudService } from 'src/modules/storage/bharath-upload.service';
import { StorageService } from 'src/modules/storage/storage.service';
import axios from 'axios';
import { PDFDocument, StandardFonts, rgb } from 'pdf-lib';

@Injectable()
export class PosterService {
  constructor(
    private awsService: AwsService,
    private oneDriveService: OneDriveStorageService,
    private bharathService: BharathCloudService,
    private storageService: StorageService,
  ) { }

  async create(userId: number, body: CreatePosterDto) {
    let user = await User.findOne({
      where: { id: userId },
      relations: ['organization'],
    });

    let posters = await createQueryBuilder(PosterConfig, 'PosterConfig')
      .leftJoinAndSelect('PosterConfig.organization', 'organization')
      .where('organization.id = :orgId', { orgId: user?.organization?.id })
      .getOne();
    if (posters) {
      posters.firmName = body?.firmName?.trim();
      posters.firmTagLine = body?.firmTagLine?.trim();
      posters.mobileNumber = body?.mobileNumber?.trim();
      posters.email = body?.email?.trim();
      posters.website = body?.website?.trim();
      posters.address = body?.address?.trim();
      posters.branchesInfo = body?.branchesInfo;
      // posters.user = user;
      posters.organization = user?.organization;
      // posters['userId'] = userId;
      await posters.save();
      return posters;
    } else {
      let poster = new PosterConfig();
      poster.firmName = body?.firmName?.trim();
      poster.firmTagLine = body?.firmTagLine?.trim();
      poster.mobileNumber = body?.mobileNumber?.trim();
      poster.email = body?.email?.trim();
      poster.website = body?.website?.trim();
      poster.address = body?.address?.trim();
      poster.branchesInfo = body?.branchesInfo;
      // poster.user = user;
      poster.organization = user?.organization;
      // poster['userId'] = userId;
      await poster.save();
      return poster;
    }
  }

  // async updateImage(userId: number, data: any) {
  //   const user = await User.findOne({ where: { id: userId }, relations: ['organization'] });

  //   const posterConfig = await PosterConfig.findOne({
  //     where: { organization: user.organization.id },
  //     relations: ['orgLogo'],
  //   });
  //   let iStorage: Storage;
  //   if (data?.name) {
  //     if (posterConfig?.orgLogo?.id) {
  //       if (data.name !== posterConfig?.orgLogo?.name) {
  //         if (user?.organization.storageSystem === StorageSystem.AMAZON) {
  //           this.awsService.deleteFile(posterConfig?.orgLogo?.file);
  //         } else if (user?.organization.storageSystem === StorageSystem.MICROSOFT) {
  //           this.oneDriveService.deleteOneDriveFile(userId, posterConfig?.orgLogo?.fileId);
  //         } else if (user?.organization.storageSystem === StorageSystem.BHARATHCLOUD) {
  //           this.bharathService.deleteFile(user.organization.id, posterConfig?.orgLogo?.file);
  //         }
  //       }
  //       iStorage = await Storage.findOne({ where: { id: posterConfig?.orgLogo?.id } });
  //       iStorage.fileType = data?.fileType;
  //       iStorage.fileSize = data?.fileSize;
  //       iStorage.name = data?.name;
  //       iStorage.file = data?.upload;
  //       iStorage.show = data?.show;
  //       iStorage.storageSystem = data?.storageSystem;
  //       iStorage.webUrl = data?.webUrl;
  //       iStorage.downloadUrl = data?.downloadUrl;
  //       iStorage.fileId = data?.fileId;
  //       iStorage.authId = user.organization.id;
  //       iStorage.filePath = data?.filePath;
  //       // user.imageStorage = iStorage;
  //     } else {
  //       iStorage = await this.storageService.addAttachements(userId, data);
  //       // user.imageStorage = storage;
  //     }
  //   } else {
  //     if (posterConfig?.orgLogo?.id) {
  //       const existingPStorage = await Storage.findOne({
  //         where: { id: posterConfig?.orgLogo?.id },
  //       });
  //       const existingStorage = await existingPStorage.remove();
  //       if (existingStorage) {
  //         if (existingStorage.storageSystem === StorageSystem.AMAZON) {
  //           this.awsService.deleteFile(existingStorage.file);
  //         } else if (existingStorage.storageSystem === StorageSystem.MICROSOFT) {
  //           this.oneDriveService.deleteOneDriveFile(userId, existingStorage.fileId);
  //         } else if (existingStorage.storageSystem === StorageSystem.BHARATHCLOUD) {
  //           this.bharathService.deleteFile(user.organization.id, existingStorage.file);
  //         }
  //       }
  //       posterConfig.orgLogo = null;
  //     }
  //   }
  //   if (iStorage) {
  //     iStorage.posterConfig = posterConfig;
  //     await iStorage.save();
  //   }
  // }

  async updateImage(userId: number, data: any) {
    const user = await User.findOne({ where: { id: userId }, relations: ['organization'] });

    const posterConfig = await PosterConfig.findOne({
      where: { organization: user.organization.id },
      relations: ['orgLogo'], // because logo_id lives here
    });

    let iStorage: Storage;

    if (data?.name) {
      if (posterConfig?.orgLogo?.id) {
        if (data.name !== posterConfig?.orgLogo?.name) {
          if (user.organization.storageSystem === StorageSystem.AMAZON) {
            this.awsService.deleteFile(posterConfig.orgLogo.file);
          } else if (user.organization.storageSystem === StorageSystem.MICROSOFT) {
            this.oneDriveService.deleteOneDriveFile(userId, posterConfig.orgLogo.fileId);
          } else if (user.organization.storageSystem === StorageSystem.BHARATHCLOUD) {
            this.bharathService.deleteFile(user.organization.id, posterConfig.orgLogo.file);
          }
        }

        iStorage = await Storage.findOne({ where: { id: posterConfig.orgLogo.id } });
        Object.assign(iStorage, {
          fileType: data.fileType,
          fileSize: data.fileSize,
          name: data.name,
          file: data.upload,
          show: data.show,
          storageSystem: data.storageSystem,
          webUrl: data.webUrl,
          downloadUrl: data.downloadUrl,
          fileId: data.fileId,
          authId: user.organization.id,
          filePath: data.filePath,
        });
      } else {
        iStorage = await this.storageService.addAttachements(userId, data);
      }

      if (iStorage) {
        await iStorage.save();
        posterConfig.orgLogo = iStorage; // ✅ Set storage to poster config
        await posterConfig.save(); // ✅ Save the foreign key
      }
    } else {
      // Remove logo case
      if (posterConfig?.orgLogo?.id) {
        const existingStorage = await Storage.findOne({ where: { id: posterConfig.orgLogo.id } });
        if (existingStorage) {
          await existingStorage.remove();

          if (existingStorage.storageSystem === StorageSystem.AMAZON) {
            this.awsService.deleteFile(existingStorage.file);
          } else if (existingStorage.storageSystem === StorageSystem.MICROSOFT) {
            this.oneDriveService.deleteOneDriveFile(userId, existingStorage.fileId);
          } else if (existingStorage.storageSystem === StorageSystem.BHARATHCLOUD) {
            this.bharathService.deleteFile(user.organization.id, existingStorage.file);
          }
        }

        posterConfig.orgLogo = null;
        await posterConfig.save();
      }
    }
  }

  async getPosterConfig(userId: number, query: any) {
    const user = await User.findOne(userId, { relations: ['organization'] });

    const poster = await createQueryBuilder(PosterConfig, 'poster')
      .innerJoin('poster.organization', 'organization')
      .leftJoinAndSelect('poster.orgLogo', 'orgLogo')
      .where('organization.id = :orgId', { orgId: user.organization.id })
      .getOne();

    return poster;
  }

  async getPosterEventTypes() {
    return await createQueryBuilder(PosterEventTypes, 'posterEventTypes')
      .select([
        'posterEventTypes.id AS id',
        'posterEventTypes.name AS name'
      ])
      .orderBy('posterEventTypes.name', 'ASC')
      .getRawMany();
  }


  async getPosterEventsByType(typeName: string) {
    if (typeName === 'Due Dates') {
      const today = new Date();

      const matchedEvents = await createQueryBuilder(Event, 'event')
        .innerJoin(Storage, 'storage', 'storage.event_id = event.id') // events only with storage
        .where('event.defaultOne = :default', { default: true })
        .andWhere('event.date >= :today', { today })
        .orderBy('event.date', 'ASC')
        .select(['event.id AS id', 'event.title AS name', 'event.date AS date'])
        .getRawMany();

      return matchedEvents.map((e) => ({
        id: e.id,
        name: e.name,
        date: e.date,
      }));
    } else {
      const type = await createQueryBuilder(PosterEventTypes, 'type')
        .leftJoinAndSelect('type.events', 'events')
        .innerJoin(Storage, 'storage', 'storage.poster_events_id = events.id') // poster events only with storage
        .where('type.name = :name', { name: typeName })
        .orderBy('events.name', 'ASC')
        .getOne();

      return type?.events ?? [];
    }
  }

  async createTemplate1(userId: number, body: any) {
    let user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
    if (!user) throw new Error('User not found');

    let storage = await Storage.findOne(body.id);
    if (!storage) throw new Error('Storage file not found');

    const imageData: any = await this.awsService.getFileFromS3(
      process.env.AWS_BUCKET_NAME,
      storage.file,
    );
    if (!imageData || !imageData.Body) throw new Error('Failed to retrieve image data');

    const baseWidth = 1080;
    const baseHeight = 955;

    const baseImage = await sharp(imageData.Body).resize(baseWidth, baseHeight).toBuffer();

    const poster = await createQueryBuilder(PosterConfig, 'poster')
      .leftJoinAndSelect('poster.organization', 'organization')
      .leftJoinAndSelect('poster.orgLogo', 'orgLogo')
      .where('organization.id = :orgId', { orgId: user.organization.id })
      .getOne();

    if (!poster) throw new Error('Poster details not found');

    const logoStorage = poster.orgLogo;
    const hasLogo = !!logoStorage?.file;

    let logoBuffer: Buffer | null = null;
    if (hasLogo) {
      try {
        if (logoStorage?.storageSystem == StorageSystem.AMAZON) {
          const logoData: any = await this.awsService.getFileFromS3(
            process.env.AWS_BUCKET_NAME,
            logoStorage.file,
          );
          if (logoData?.Body) {
            const circleMask = Buffer.from(`
            <svg width="150" height="150">
              <circle cx="75" cy="75" r="75" fill="white"/>
            </svg>
          `);

            logoBuffer = await sharp(logoData.Body)
              .resize(160, 160)
              .composite([{ input: circleMask, blend: 'dest-in' }])
              .png()
              .toBuffer();
          }
        } else if (logoStorage?.storageSystem == StorageSystem.MICROSOFT) {
          const metadata: any = await this.oneDriveService.getMetadataByFileId(
            userId,
            logoStorage.fileId,
          );

          // Get download URL for the file content
          const downloadUrl = metadata['@microsoft.graph.downloadUrl'];

          if (!downloadUrl) {
            throw new Error('Download URL not found in metadata');
          }

          const response = await axios.get(downloadUrl, { responseType: 'arraybuffer' });

          const circleMask = Buffer.from(`
    <svg width="150" height="150">
      <circle cx="75" cy="75" r="75" fill="white"/>
    </svg>
  `);

          logoBuffer = await sharp(response.data)
            .resize(160, 160)
            .composite([{ input: circleMask, blend: 'dest-in' }])
            .png()
            .toBuffer();
        }
      } catch (err) {
        console.warn('Logo retrieval failed, skipping logo.');
      }
    }

    const hasBranches = poster.branchesInfo && poster.branchesInfo.length > 0;
    const extensionHeight = hasBranches ? 395 : 300;

    const gradientDefs = hasBranches
      ? `
        <defs>
          <linearGradient id="grad" x1="0" y1="0" x2="0" y2="1">
            <stop offset="45%" stop-color="${body.backgroundColor1 || '#FFFFFF'}"/>
            <stop offset="45%" stop-color="${body.backgroundColor2 || '#FFFFFF'}"/>
            <stop offset="85%" stop-color="${body.backgroundColor2 || '#FFFFFF'}"/>
            <stop offset="85%" stop-color="${body.backgroundColor1 || '#FFFFFF'}"/>
            <stop offset="100%" stop-color="${body.backgroundColor1 || '#FFFFFF'}"/>
          </linearGradient>
        </defs>`
      : `
        <defs>
          <linearGradient id="grad" x1="0" y1="0" x2="0" y2="1">
            <stop offset="60%" stop-color="${body.backgroundColor1 || '#FFFFFF'}"/>
            <stop offset="60%" stop-color="${body.backgroundColor2 || '#FFFFFF'}"/>
            <stop offset="80%" stop-color="${body.backgroundColor2 || '#FFFFFF'}"/>
            <stop offset="80%" stop-color="${body.backgroundColor1 || '#FFFFFF'}"/>
            <stop offset="100%" stop-color="${body.backgroundColor1 || '#FFFFFF'}"/>
          </linearGradient>
        </defs>`;

    const extendedImage = await sharp(baseImage)
      .extend({
        bottom: extensionHeight,
        background: { r: 10, g: 73, b: 122, alpha: 1 },
      })
      .toBuffer();

    function escapeXML(str: string): string {
      return str
        .replace(/&/g, '&amp;')
        .replace(/"/g, '&quot;')
        .replace(/'/g, '&apos;')
        .replace(/</g, '&lt;')
        .replace(/>/g, '&gt;');
    }

    const cityStartXLeft = '615';
    const cityStartXRight = '950';
    const branchCount = poster.branchesInfo?.length || 0;

    let cityStartY: number;
    if (branchCount === 3) {
      cityStartY = 230;
    } else if (branchCount === 4) {
      cityStartY = 205;
    } else if (branchCount === 5 || branchCount === 6) {
      cityStartY = 230;
    } else {
      cityStartY = 205;
    }

    const extendedLineLength = poster.firmName.length * 10;
    const citySpacing = 40;
    const textX = hasLogo ? baseWidth * 0.55 : baseWidth * 0.5;
    const otherDetailsX = hasBranches ? 20 : baseWidth / 2;
    const textAnchor = hasBranches ? 'left' : 'middle';
    const yElements = branchCount === 1 || branchCount === 2 ? 240 : 220;

    const contactText = hasBranches
      ? poster.branchesInfo
        .map((contact, index) => {
          const buildingIcon =
            'data:image/png;base64,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'; // your full base64 string
          const phoneIcon =
            'data:image/png;base64,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'; // your full base64 string

          if (branchCount === 1) {
            const y = cityStartY + 90;
            return `
      <image href="${buildingIcon}" x="${baseWidth / 2 - 200}" y="${y - 20
              }" width="25" height="25" />
      <text x="${baseWidth / 2 - 170}" y="${y}" font-size="30" text-anchor="start" fill="${body.backgroundColor1
              }">
        ${contact['branch']}
      </text>
      <image href="${phoneIcon}" x="${baseWidth / 2 + 50}" y="${y - 20}" width="25" height="25" />
      <text x="${baseWidth / 2 + 80}" y="${y}" font-size="30" text-anchor="start" fill="${body.backgroundColor1
              }">
        ${contact['branchNumber']}
      </text>`;
          } else if (branchCount === 2) {
            const xPositions = [baseWidth * 0.25, baseWidth * 0.75];
            const y = cityStartY + 90;
            const x = xPositions[index] - 80;
            return `
      <image href="${buildingIcon}" x="${x - 150}" y="${y - 20}" width="25" height="25" />
      <text x="${x - 120}" y="${y}" font-size="26" text-anchor="start" fill="${body.backgroundColor1
              }">
        ${contact['branch']}
      </text>
      <image href="${phoneIcon}" x="${x + 80}" y="${y - 20}" width="25" height="25" />
      <text x="${x + 110}" y="${y}" font-size="26" text-anchor="start" fill="${body.backgroundColor1
              }">
        ${contact['branchNumber']}
      </text>`;
          } else if (branchCount === 3 || branchCount === 4) {
            const y = cityStartY + index * citySpacing;
            const x = baseWidth / 1.2;
            return `
      <image href="${buildingIcon}" x="${x - 330}" y="${y - 18}" width="16" height="16" />
      <text x="${x - 300}" y="${y}" font-size="25" text-anchor="start" fill="${body.backgroundColor1
              }">
        ${contact['branch']}
      </text>
      <image href="${phoneIcon}" x="${x - 100}" y="${y - 18}" width="16" height="16" />
      <text x="${x - 70}" y="${y}" font-size="25" text-anchor="start" fill="${body.backgroundColor1
              }">
        ${contact['branchNumber']}
      </text>`;
          } else {
            const row = Math.floor(index / 2);
            const col = index % 2;
            const x = col === 0 ? parseInt(cityStartXLeft) : parseInt(cityStartXRight);
            const y = cityStartY + row * citySpacing;
            return `
      <image href="${buildingIcon}" x="${x - 190}" y="${y - 16}" width="14" height="14" />
      <text x="${x - 160}" y="${y}" font-size="18" text-anchor="start" fill="${body.backgroundColor1
              }">
        ${contact['branch']}
      </text>
      <image href="${phoneIcon}" x="${x - 25}" y="${y - 16}" width="14" height="14" />
      <text x="${x}" y="${y}" font-size="18" text-anchor="start" fill="${body.backgroundColor1}">
        ${contact['branchNumber']}
      </text>`;
          }
        })
        .join('')
      : '';

    const textOverlaySvg = ` 
      <svg width="${baseWidth}" height="${extensionHeight}" xmlns="http://www.w3.org/2000/svg">
        <rect width="100%" height="100%" fill="url(#grad)"/>
        ${gradientDefs}
        <text x="${textX}" y="80" font-size="${poster.firmName.length > 30 ? 35 : 40
      }" font-weight="bold" text-anchor="middle" fill="${body.backgroundColor2}">
          ${escapeXML(poster.firmName.toUpperCase())}
        </text>
        <line x1="${textX - extendedLineLength}" y1="100" x2="${textX + extendedLineLength
      }" y2="100" stroke="Red" stroke-width="5" />
        <text x="${textX}" y="140" font-size="30" text-anchor="middle" fill="${body.backgroundColor2
      }">
          ${escapeXML(poster.firmTagLine.toUpperCase())}
        </text>
        ${contactText}
        ${(() => {
        const contactElements = [];

        if (poster.mobileNumber)
          contactElements.push({ text: poster.mobileNumber, type: 'mobile' });

        if (poster.email) contactElements.push({ text: poster.email, type: 'email' });

        if (poster.website) contactElements.push({ text: poster.website, type: 'website' });

        const iconMap = {
          mobile:
            'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAgAAAAIACAYAAAD0eNT6AAAACXBIWXMAAA7DAAAOwwHHb6hkAAAAGXRFWHRTb2Z0d2FyZQB3d3cuaW5rc2NhcGUub3Jnm+48GgAAIABJREFUeJzt3Xe4bmV5rv3zXvSqWBGwEkFUQCmKICKCBQyiUexfLFGJJTHZ5tu6v1hwW7IxakDdNhQlRSXYEqXYUETA2LCgWABFEUWqAkuq9/fHeCdOVlaZ5X3HPcr5Ow4OWbBY85JjMZ/rvZ9nPCNQKzJzC2BnYPt5f9wLuC2wMbDV5H83Aa4CrgNWAr8FLgXOBy6Y/HEe8IOIuKnd/xeSpKGI6gBDlZlbA/sBewMPBXYF1pvil7gW+DpwJnAWcEZEXDnFX1+SNGAWgCnKzNsDjwUOAx4DrN/il78Z+CpwAnBCRFzc4teWJGlcMnO9zHxSZp6amTdlN9ycmV/OzGdn5sbV/44kSRqMzNw8M1+QmT+sXOkX4MrMPDoz71H970ySpN7KzI0y8+WThbVPbsrMj2TmDtX/DiVJ6pXMPDAzf1C7ji/bjZn5nszcpvrfpyRJnZaZ98zMz9au21N3bWa+PjM3qf73K0lqn08BrENmPgl4L81z+kN0AfC8iPhidRBJUntWVAfoqszcJDOPpnmsbqiLPzSXEX0hm22BLarDSJLa4QRgNTLznsBJwH2qs7TsAuCwiPhWdRBJ0mw5AVhFZt4fOJ3xLf7QTAPOyMznVAeRJM2WBWCezHw48BVg2+IolTYGjp1sCWxQHUaSNBtuAUxk5uOBjwAbVWfpkFOBQyPimuogkqTpsgAAmflY4GO4+K/OGcBBEXF1dRBJ0vSMvgC4+C+IJUCSBmbUBcDFf1EsAZI0IKMtAC7+S2IJkKSBGGUBcPFfFkuAJA3A6AqAi/9UWAIkqedGVQBc/KfKEiBJPTaaAuDiPxOWAEnqqVEUABf/mbIESFIPDb4AuPi3whIgST0z6ALg4t8qS4Ak9chgC4CLfwlLgCT1xCALgIt/KUuAJPXA4AqAi38nWAIkqeMGVQBc/DvFEiBJHTaYAuDi30mWAEnqqEEUABf/TrMESFIH9b4AuPj3giVAkjqm1wXAxb9XLAGS1CG9LQAu/r1kCZCkjuhlAXDx7zVLgCR1QO8KgIv/IFgCJKlYrwqAi/+gWAIkqVBvCoCL/yBZAiSpSC8KgIv/oFkCJKlA5wuAi/8oWAIkqWWdLgAu/qNiCZCkFnW2ALj4j5IlQJJa0skC4OI/apYASWpB5wqAi7+wBEjSzHWqALj4ax5LgCTNUGcKgIu/VsMSIEkz0okC4OKvtbAESNIMlBcAF38tgCVAkqastAC4+GsRLAGSNEVlBcDFX0tgCZCkKSkpAC7+WgZLgCRNQesFwMVfU2AJkKRlarUAuPhriiwBkrQMrRUAF3/NgCVAkpaolQLg4q8ZsgRI0hLMvAC4+KsFlgBJWqSZFgAXf7XIEiBJizCzAuDirwKWAElaoJkUABd/FbIESNICTL0AuPirAywBkrQOUy0ALv7qEEuAJK3Fimn9Qpn5BOATuPirG/YBTszMzauDSFIXTWUCkJkPB07BxV/d4yRAklZj2QUgM3cGvgzcdvlxpJmwBEjSKpZVADJzW+DrwF2mE0eamdOBgyPimuogktQFSz4DkJkbAMfj4q9+2Bc4JTO3qA4iSV2wnEOA76Q5aCX1hQcDJWliSVsAmfkk4IQpZ5Ha4pkASaO36AKQmdsB3wW2mn4cqTWWAEmjtqgtgMxcAfwrLv7qP7cDJI3aYs8APA/YbxZBpAIeDJQ0WgveAsjMOwPn4qd/DY/bAZJGZzETgDfh4q9hcjtA0ugsaAKQmQ8AvskU3x0gdZCTAEmjsdAF/R8X8XOlvtoHONkzAZLGYJ2LemY+CjiwhSxSF7gdIGkUFvKp/pUzTyF1i08HSBq8tRaAzNyP5puhNDZuB0gatHVNAF7eSgqpm9wOkDRYa3wKIDO3B36Mh/8knw6QNDhrW9xfvI6/L42F2wGSBme1E4DM3Bi4GC/+keY7HTg4Iq6pDiJJy7WmT/iPw8VfWpVPB0gajDUVgGe0mkLqD7cDJA3Cf9sCyMzb04z/N2w/jtQbbgdI6rXVTQAOwcVfWpd9gf+YnJeRpN5ZXQE4tPUUUj89Ajg+M9evDiJJi3WrLYDM3AS4FNisJo7US++PiOdVh5CkxVh1AvAwXPylxfqLzHxpdQhJWoxVC8AjSlJI/ffmzPStmZJ6wwIgTcf6wIcyc+vqIJK0ELcUgMy8DfDAwixS390RODYz1/iODUnqivkTgD2B9aqCSANxEPDC6hCStC7zC8CDy1JIw/IPmbltdQhJWptVJwCSlm9L4J+qQ0jS2tyyV5mZFwJ3K8wiDc0jIuKL1SEkaXUCIDM3B37HGl4PLGlJvg48OCKyOogkrWpuC+C+uPhL07Yn8PjqEJK0OnMFYKfSFNJwvbI6gCStzlwBuGdpCmm4dsvM/atDSNKq5grANqUppGF7WXUASVrVXAHYrjSFNGwHZeZdq0NI0nxOAKTZWwH8P9UhJGk+C4DUjmdVB5Ck+SIzNwJ+j48BSrN2/4j4fnUISYJmArANLv5SGw6tDiBJc1YAd64OIY3En1YHkKQ5K4BNq0NII7FHZvrfm6ROWAFsXB1CGokN8LXbkjpiBbBRdQhpRPauDiBJ4ARAatvO1QEkCSwAUtvuXx1AksAtAKltO2Tm+tUhJMkJgNSuDYCtq0NI0grATyNSu3z5lqRyK4DrqkNII+MEQFI5C4DUvi2rA0jSCpoXAUlqz+bVASRpBbCyOoQ0MptUB5AkJwBS+26uDiBJFgCpfTdVB5AkC4DUvuurA0iSZwCk9l1RHUCSVgCXVoeQRuay6gCSNFcA3JOU2mPpllRuRUT8AfhNdRBpJBL4WXUISVox+d9flaaQxuPiiPDcjaRyFgCpXT+uDiBJYAGQ2vbN6gCSBH8sAL8uTSGNx9nVASQJnABIbftqdQBJgj8WgItKU0jj8NOIuKA6hCTBHwuAB5Ok2ft8dQBJmjNXAC4AbqwMIo3Ap6sDSNKcFQARcSPw0+Is0pBdA3yuOoQkzVkx789/WJZCGr4TI8I3b0rqjPkF4EdlKaTh+0B1AEmazwIgzd7P8QCgpI6xAEiz956IuLk6hCTNF3N/kpl3xLcCStN2LXD3iLi8OogkzXfLBCAiLsX3lEvT9h4Xf0ldtGKVH3+jJIU0TNcCb6kOIUmrs2oB+HpJCmmY3hIRF1eHkKTVcQIgzcavgTdXh5CkNXECIM3G30bE1dUhJGlNYtW/kJm/ALYryCINxWci4jHVISRpbVadAIDbANJyXAUcXh1CktbFAiBN14si4sLqEJK0LqsrAJ4DkJbmmIj4cHUISVqI1Z0BuB1w2er+nqQ1OgM4ICKurw4iSQvx3yYAEXEF8L2CLFJfXQg80cVfUp+sbgsA4EtthpB67FLgoIi4pDqIJC3GmgrAF1tNIfXTVcBjIuLc6iCStFir3efPzNvSnANYr904Um9cQvPJ/+zqIJK0FKudAETEVcB3W84i9cWFwMNc/CX12Zq2AMBtAGl1zgQeEhE/rg4iScthAZAW7hhg/4j4VXUQSVquNT7rn5m3AS7HcwDSb4G/ioh/qQ4iSdOyxglARPwW+FaLWaQu+hyws4u/pKFZ2xYAwGdbSSF1zy+Bp0bEoyLiF9VhJGna1lUAPt1KCqk7fgscAdwnIo4vziJJM7PW+/4zcwVwMXDnduJIZa4A3gW8dXIdtiQN2lonABHxB+CklrJIFb4DvAC4a0S80sVf0lis841/mfkE4OMtZJHaciHwEeBDEeGFV5JGaSEFYDOaa4E3nn0caWauB/4TeC/whYjI4jySVGqdBQAgM08GHjPjLNIsnA8cBfxbRFxZHUaSumJdTwHM8WkA9c2FwOE0p/nf4eIvSbe20AnA3YGfzTaKNBW/Bl4DfCAibqwOI0ldtaACAJCZ3wF2mWEWaTkSeB/wcj/tS9K6LXQLAOBjM0shLc9PgAMj4gUu/pK0MIuZAGwPnDfDLNJS/AvwlxGxsjqIJPXJgicAEXE+vhxI3XEd8PyI+HMXf0lavMVsAQB4N7q64DzgQRHxvuogktRXC94CAMjMu9E8DbCof06aonOAR0fExdVBJKnPFjUBiIifA1+dURZpXU4DHuriL0nLt9gtAHAbQDX+E3hMRPy2OogkDcGiR/mZuTVwEbDe9ONIq/UlmsX/+uogkjQUi54ARMSvgdNnkEVane8BT3Dxl6TpWsoWADSvUpVm7QLgURFxVXUQSRqaJZ3mz8zbAhcDm0w3jnSLa4EHR8T3q4NI0hAtaQIw+UT2H1POIs33Yhd/SZqdpW4BABw7tRTSrb0vIo6rDiFJQ7bkC30ycwVwPnCPqaWR4PvAnhHx++ogkjRkS54ARMQfgA9OL4pEAoe7+EvS7C1nCwDg/cDN0wgiAcdExBnVISRpDJZ9p39mfhZ45BSyaNwuA3aKiMuqg0jSGCx3AgDNFEBarle4+EtSe6YxAdgQ+CVwh+XH0UidT/Pp/8bqIJI0FsueAETEDcCHp5BF4/U6F39JateyJwAAmXl/mjvbpcX6CXDfiLipOogkjck0zgAQEefQvLFNWqy3uPhLUvumUgAm3jHFX0vjcC1uH0lSiWkWgE8CP5vir6fhOz4iflcdQpLGaGoFICJuBo6Z1q+nUfARUkkqMpVDgHMy847Az4GNp/nrapAuiIjtq0NI0lhNcwuAiLgUOH6av6YG66TqAJI0ZlMtABNvm8GvqeE5pTqAJI3ZVLcA5mTmWcBes/i1NQjXA3eIiGuqg0jSWM1iAgA+Eqi1O8vFX5JqzaoAnAD8eka/tvrv29UBJGnsZlIAJu8H8CyA1uS71QEkaexmcgYAIDO3pHkk8Daz+hrqrT0i4pvVISRpzGa1BcDkhrf3zerXV28lcG51CEkau5lNAAAyc1vgAmDDWX4d9cqVEXG76hCSNHYzmwAARMQvgQ/N8muody6vDiBJmnEBmDgS+EMLX0f9cEV1AElSCwUgIn4InDjrr6PecAIgSR3QxgQA4E0tfR11303VASRJLRWAiPgKcEYbX0udt0l1AElSexMAgH9s8WupuywAktQBbRaATwHntPj11E2bVQeQJLVYACLiD8Dr2vp66qwtqwNIkmZ8EdCqMjNoXgSzS5tfV51yM7Dp5H0RkqQibW4BEBEJvKHNr6nOWQ+4V3UISRq7VgvAxAn4Nrix26E6gCSNXesFYDIFeH3bX1edcp/qAJI0dhUTAICP4hRgzPapDiBJY1dSADwLMHr7Z+YG1SEkacyqJgDgWYAx2wLYqzqEJI1ZWQFwCjB6j6wOIEljVjkBgOYsgLcDjpMFQJIKlRaAye2Ar67MoDJ7ZuZW1SEkaayqJwBExCeAM6tzqHXrAftVh5CksSovABOvqA6gEm4DSFKRThSAiDgdOLE6h1p3YHUASRqrVl8GtDaZuTPNi4I6UUrUmh0i4ifVISRpbDqz2EbE94B/rc6h1j2xOoAkjVFnJgAAmXl34EfARtVZ1JpvRMSe1SEkaWw6MwEAiIgLgXdW51Cr9shMXw8sSS3rVAGYeB1wZXUIterx1QEkaWw6VwAi4krgLdU51KqnVAeQpLHp1BmAOZm5GfAT4C7VWdSa+0fE96tDSNJYdG4CABAR1wKvqs6hVj2nOoAkjUknJwAAmbkC+C9gj+osasVvgO0i4sbqIJI0Bp2cAMAtLwr6GyCrs6gVdwIeWx1CksaiswUAICLOAD5WnUOteV51AEkai85uAczJzHsCPwA2rs6imUtgp4j4UXUQSRq6Tk8AACLip8A/VedQKwJ4UXUISRqDzk8AADJzc+DH+FjgGFwN3DUiflsdRJKGrPMTAICIuAb4++ocasUWwLOrQ0jS0PViAgC3PBb4NWD36iyaufOBHSPi5uogkjRUvZgAwC2PBf41PhY4BtsDT64OIUlD1psJwJzMPB4XhzH4AbDzpPhJkqasNxOAef4OuKY6hGbuvsCh1SEkaah6VwAi4hfA66tzqBWvyszeTakkqQ96+c01MzcAvk3zKVHDdnBEnFwdQpKGppcFACAz9wVOo8f/H7Qg3wAeFBEe/pSkKerdFsCciDgd+LfqHJq5PYAnVYeQpKHp9afnzLwz8EPgttVZNFM/Bu7vq4IlaXp6OwEAiIhLgFdX59DM7QA8pzqEJA1JrycAcMsNgWcBD6rOopm6GLh3RKysDiJJQ9DrCQDcckPgXwFeGDNs2wB/Wx1Ckoai9xOAOZn5HuAF1Tk0UyuBnSLi59VBJKnvhlQAbkdzIPCO1Vk0U/8WEc+sDiFJfdf7LYA5EXEFzTXBGranZ+be1SEkqe8GMwGYk5mnAI+uzqGZ+hawpy8KkqSlG8wEYJ6/BK6tDqGZ2g14bnUISeqzwRWAiPgZ8KrqHJq5IzPzTtUhJKmvBlcAJo4GzqwOoZm6HfCW6hCS1FeDOwMwJzN3pnmRzIbVWTRTj4qIz1WHkKS+GeoEgIj4HnBkdQ7N3Hszc7PqEJLUN4MtABNvAM6tDqGZugfw99UhJKlvBrsFMCcz9wLOYPhlZ8xuAvaOiK9XB5Gkvhj8ohgRXwXeVZ1DM7U+cFxmblwdRJL6YvAFYOLlwE+rQ2imdgJeUx1Ckvpi8FsAczLzYODE6hyaqZuAfSLia9VBJKnrxjIBICJOAt5fnUMztT7wQbcCJGndRlMAJv4GOL86hGZqJ3z8U5LWaTRbAHMy8xHA5xnh//cRSeBxEfHp6iCS1FVjmwAQEacC76jOoZkK4NjM3Lo6iCR11Sg/BWfmpsDZwA7VWTRTnwEOioisDiJJXTO6CQBARKwEng3cXBxFs/Vo4CXVISSpi0ZZAAAi4ix8m9wYvCkzd6sOIUldM8otgDmZuRHwdWDn6iyaqQuB3SLiiuogktQVo50AAETE9cCzgBurs2im7k5zP8CoC68kzTfqAgAQEWcDb6zOoZk7hOYeCEkSI98CmJOZGwBfBdwrHrYbgf0m5z8kadQsABOZuRPwDWDT6iyaqV8Ae0bEJdVBJKnS6LcA5kTEucDLqnNo5u4KfDwzN6wOIkmVLADzRMS7gf+ozqGZ2xt4a3UISarkFsAqMvMOwHeAbaqzaOZeEBHHVIeQpAoWgNXIzEcCp+CEZOhuBA6IiNOrg0hS21zgViMiPgf8U3UOzdwGwEcyc7vqIJLUNicAazC5JfAs4IHVWTRz3wb2jYhrqoNIUlucAKzB5JbAZwArq7No5h4AHJ+Z61UHkaS2WADWwkcDR+Vg4MjqEJLUFrcAFiAzPwkcWp1DrXhRRLyrOoQkzZoFYAF8NHBUbgQOiYjPVAeRpFlyC2ABIuIy4M+BP1Rn0cxtAHw0M3evDiJJs2QBWKCI+ALw+uocasXmwMmZuUN1EEmaFbcAFmFySvxzwP7VWdSKnwD7RMSl1UGkacrMjYFdgV2AewHbA9sBt5/3x3xXAb8HrgB+DvwSOA84B/h+RPysleCaKgvAImXmXYCzgTtXZ1ErvgY8IiKurQ4iLVVm3gbYDzgAeBhwf2D9KX6J3wBfAU4HPhsRP5jir60ZsQAsQWYeCHwGt1DG4hTg0Ii4oTqItFCTw8tPAJ5EM7XcoMUvfx7wn8CHI+IbLX5dLYIFYIky87XAq6tzqDUnAE+LiJurg0hrkpkraD7lPw94PNCF116fA3wAODYirqoOoz+yACyR5wFG6VjgeRGR1UGk+TJzA+BpwMuB+xbHWZNrgPcDR3lmoBssAMvgeYBROioi/rY6hAS3fOJ/JvC/gbsXx1moG4FjgNdFxK+rw4yZBWCZPA8wSq+NiCOqQ2jcMvMA4C00p/n76FrgTcCRk3evqGUuWssUEZ8H3lCdQ616TWb+f9UhNE6ZuVVmvodmC7Kviz/AZsBrgXMy81HVYcbICcAUTM4DfB54eHEUtev/jYg3V4fQeGTmITT76HeszjJlCbwbeFlE/L46zFhYAKbE8wCj9T8i4p+qQ2jYJhf3vAl4CcP+vv0D4BkR8e3qIGMw5N9IrcvMvYEv0e7ztqr38oh4U3UIDdPkef6PA/tWZ2nJ9cDhEXFcdZCh8wzAFEXEmcArqnOodf8nM19SHULDk5m7At9iPIs/wEbABzPzyMlTDpoRJwAzkJnH0bw9UOORwN9FxFurg2gYMvNBwMnA7aqzFPoP4Ck+JTAbFoAZyMzNgbNo7tvWuLwyInwqRMsyORX/CWDT6iwdcArwxIhYWR1kaCwAM5KZ9wa+DtymOota94aIeGV1CPVTZu5Lc7fIJtVZOuTLwGMj4prqIENiAZihySM7n8SzFmP0TuAlXhusxcjMnYHTgK2qs3TQGcBBEXF1dZChcGGaoYj4FPAP1TlU4kXAuyZ3REjrNHmU+BRc/NdkH+DEyRarpsAJwIxNTrF+GjioOotKfJLmLYLXVQdRd01e5vMFxnXaf6mcBEyJE4AZi4g/0Lys46fVWVTi8cCn/NSidTgaF/+FchIwJU4AWjJ5nvdMPNU7Vt+k+dRyaXUQdUtmPprmcT+/Hy+Ok4BlcgLQkoj4Ds2+sMZpd+DLmXm36iDqjsktfx/ExX8pnAQskwWgRZOrLd9enUNl7gOclZkPrA6izvhHYOvqED22L3CSJWBpbJ0tm5wK/xQeChyza4GnRsSnq4OozuTdIV/B78PT4HbAEvgbr0Bm3g74L+BPqrOozE3AiyPivdVB1L7J00HfAJwGTc/pwMFeFrRwbgEUiIgrgEOAq6qzqMz6wHsy82hfeDJKT8TFf9r2BU7JzC2qg/SFE4BCmXkw8J+Al8WM278Dz/Gu83GYFL7vAverzjJQTgIWyE8ehSLiJOB/VedQuScDp2XmttVB1Ion4OI/Sx4MXCAnAB2Qme8D/qI6h8r9Cnh8RHytOohmJzO/jJf+tMGDgetgAeiAzNwY+CKwV3UWlbseeEFE/HN1EE1fZt4f+F51jhGxBKyFWwAdMLkn/s+Ai6qzqNxGwAcz8x98kdAgHV4dYGS8LGgtnAB0yOSCmK/gdcFqnAY8JSIuqQ6i5ZsUul8Cd67OMkJOAlbDCUCHRMTZwLMA3yEvgP2Ab2Tmg6uDaCr2x8W/yj7AyT4ieGsWgI6JiI8Cb6zOoc7YDvhSZj6nOoiW7RnVAUbO7YBVuAXQQZkZwIeBp1RnUaccA7w0In5fHUSLk5m3oRn/b1adRd4TMMcJQAdFRALPpnl9sDTn+cA3JyfJ1S9Px8W/K7wxcMIC0FGTJwMeB/ykOos6ZSfga5n5/OogWpTnVQfQrXgmAAtAp0XE5TTvDLiiOos6ZRPgvZn5gcz0U2XHZeZDgN2qc+i/Gf2ZAAtAx0XEj2heHHJDdRZ1zrNpnhLYvTqI1uq11QG0RqO+NtgC0AMR8SV8PFCrdx/gq5l5hBcHdU9mPhR4ZHUOrdVozwRYAHoiIj4CvK46hzppfeA1wFcy80+qw+hWXl8dQAsyyjMBFoB+OQL4t+oQ6qy9aJ4S+PPqIILMPJTmMif1w+jOBHgPQM9k5obAycAjqrOo006meamQ75coMHnu/xyai5zUL6O5NtgJQM9ExA3AYcCPqrOo0w4CzsnMF0wullK73oKLf1+NZjvAbww9NdnrPQu4Q3UWdZ7TgBZl5gHA5/D7a98N/sZAJwA9FRHn0VwUtLI6izpvbhrw4sz0v/kZysytgeNw8R+CwT8i6DeDHouIs4AnAzdVZ1Hn3QZ4B829AQ+qDjNEmbkBcDywbXUWTc2gHxG0APRcRJwIPBfvCNDCPBA4KzPfk5lbVocZmKOBh1WH0NQN9kyABWAAIuJfaB4RlBZiBfAC4AeZ+VQPCS5fZr4YeGF1Ds3MIB8R9D/8AcnMtwF/VZ1DvfN1mtcMn1UdpI8y89nA+/ED1RgM6hFBC8CATA54/TvNuwOkxUjgo8DLIuIX1WH6IjMPAz4MeA3zeAymBFgABiYzNwE+Czy0Oot66WrgSOCoiLi2OkyXZeYzgQ/QXMWscRnEI4IWgAGa3EJ2GrBrdRb11mXAm2mKwPXVYbomM18KvBXH/mPW+0mABWCgMnNbmt+gd6/Ool77OfAG4P0RcXN1mGqTq7iPAXzfgqDnJcACMGCZeT+aUdVW1VnUe9+jKQInRMQfqsNUyMy70ryMa9/qLOqU3m4HWAAGLjMfDJwKbFqdRYNwAc0ZgWMjYjQXUGXmn9F88r9ddRZ1Ui8nARaAEcjMxwMn4GElTc8FwJuAf46I31eHmZXJZUlHAc+pzqLO690kwAIwEpMTy8fhoSVN129pfl+9eUiPD04uR3oSzVv97locR/3Rq0mABWBEMvOvgLdV59Ag3UgzZTo6Ir5WHWY5MnM34O3A3tVZ1Eu9mQRYAEYmM18NvLY6hwbtXJqpwPsj4rLqMAuVmbsCLwOejhf7aHl6MQmwAIxQZr6Z5hudNEvXAZ8AjgW+2NXHCDPz4cArgEcXR9GwdH4SYAEYocn+5rtpXggjteFy4CSabYKTq58gyMxtgMNo3qS5S2UWDVqnJwEWgJHKzPVo7jA/rDqLRufXNJOBk2gmA61cOZyZ9wQeAxwKHIhjfrWjs5MAC8CITW41+yRwUHUWjdb1NN8gP0NzX8X3p3H18GTKtT3wQJr3YjwG2GG5v660RJ0sARaAkcvMTYFT8HYzdcNNwPk0Nw+eC1wM/IZmC+EyYP6dA1sCGwB3ArYGtgW2Ae5L8x6MLVtLLa1b50qABUBzl52cCuxenUWSBqxTZwIsAAIgM+9A8wbB+1ZnkaQB60wJsADoFpm5Hc2Y6h7FUSRpyDqxHeC1sLpFRFwE7Af8rDiKJA3ZvsApmblFZQgLgG4lIn4OPBL4ZXUWSRqwfYCTK0uAWwBarcy8N82ZgLtUZ5GkASs7E+AEQKsVET8B9qe5tEWSNBtlkwALgNYoIn4EPIrm+WtJ0myUlAALgNYqIr5Hc23q5dVZJGnAWi8BFgCtU0R8h+Zg4BXVWSRpwFotARYALUhEnA08FvhddRZJGrDWSoAFQAsWEV+leXFQ+Q1WkjRgrZQAHwPUomXmPjQHAYdBAAAM00lEQVQvENq8OoskDdhMHxF0AqBFi4gzgCdw6zezSZKma6aTAAuAliQiPg8cCqysziJJA7YPcGJmTn3i6haAliUz9wVOBErvtJakgZv6doAFQMuWmQ8FTsISIEmzNNUSYAHQVGTmHsBnga2qs0jSgE2tBFgANDWZuTtNCbhddRZJGrCplAALgKYqMx8IfA64fXUWSRqwZZcAnwLQVE1uDDwQuLQ6iyQN2LIfEXQCoJnIzJ2BzwN3qs4iSQN2Os0k4NrF/oMWAM1MZu4InApsU51FkgbsC8DBEXHDYv4htwA0MxHxI2B/4KLqLJI0YAcAH8jMRX2otwBopiLix8C+wE+rs0jSgD0deONi/gG3ANSKzLwHzZmA7YujSNKQPTciPrCQn2gBUGsyc2vgM8Au1VkkaaCuBfaIiB+u6ye6BaDWRMSvgYcDZxVHkaSh2gz498zceF0/0QKgVkXElcAjaS4LkiRN384s4DyAWwAqkZkbAR8GnlCdRZIGKIGHRMR/reknOAFQiYi4HjgMOK46iyQNUABHre3RQAuAykTEzcBzgLdVZ5GkAdoLeMaa/qZbAOqEzDwCeE11DkkamF8C94mIa1b9G04A1AkRcQTwiuockjQw2wKHr+5vOAFQp2TmXwL/F8upJE3LRcC9IuLG+X/Rb7LqlIh4N/As4KbqLJI0ENvRHLq+FScA6qTMfCTwMWDJ77qWJN3iWxGx+/y/YAFQZ2XmXsCngDtUZ5GkAdhr/r0AbgGosyLiqzSPsZxfnUWSBuDJ83/gBECdl5l3AU4CHlCdRZJ67CLgbhGR4ARAPRARvwIehu8PkKTl2A54yNwPLADqhYi4GngczcFASdLSPHHuTywA6o2IuA54CvDO6iyS1FP7zf2JZwDUS5n5cuAf8PewJC3GzcBWEXG1EwD1UkQcSfMioRvX9XMlSbdYD3gwuAWgHouI42j2s1ZWZ5GkHnkoWADUcxHxKeAA4NLqLJLUE7uCBUADMLkw6MHAudVZJKkHtgcPUGlAMnMr4OPAw4ujSFKXrQQ2dwKgwYiIK4FHA/9SnUWSOmxT4M4WAA1KRNxA8zrh/w1kcRxJ6qp7WQA0OBGREfEa4GnAddV5JKmDbmsB0GBFxPHAgcBl1VkkqWM2swBo0CLiDJqXX/ykOoskdYiHADV8EXEeTQn4SnUWSeoIC4DGISIup3lC4BPVWSSpAzawAGg0ImIlzdXBr8UnBCSN27UWAI3K5AmBI2ieEPh9cRxJqnKNBUCjNHlCYB/gF9VZJKmAEwCNV0ScDewNfLM6iyS1zAmAxi0iLqJ5Nea/VmeRpBb9ypcBSUBmBvA/gTfiWzIlDVsCm1sApHky87HAh4Atq7NI0oz8KiK28ZOONE9EnAjsC/ysOIokzcr54KhT+m8i4rvAXsCZ1VkkaQbOAwuAtFoRcQnwCOAD1Vkkacq+DhYAaY0i4vqIeC5wOHBDdR5JmpIzADwEKC1AZu4BfAy4W3UWSVqGq4GtIuJmJwDSAkTEN4A9gC9WZ5GkZfiviLgZ3AKQFiwiLqV5o+A7qrNI0hJ9ae5P3AKQliAznwG8F9i0OoskLcJ9I+JcsABIS5aZDwA+DtyzOoskLcCPI2LHuR+4BSAtUUR8G9gT+Gx1FklagBPm/8ACIC1DRFwOHAy8ieZ+bUnqqk/M/4FbANKUZOaTgPfjewQkdc/ZEbHb/L/gBECakoj4KM2jgt+uziJJq3j7qn/BCYA0ZZm5MXAk8NfVWSQJuBLYLiJWzv+LTgCkKYuI6yLipcAzgWuq80gavfeuuviDEwBppjJzJ5qTt/erziJplK4DdoiIX6z6N5wASDM0uXBjD+CY6iySRun/rm7xBycAUmsy88+BdwKbVWeRNApXA38SEb9Z3d90AiC1JCL+mebioHOqs0gahTetafEHJwBS6zJzc+A9wNOrs0garIuBHSNijQeRnQBILYuIayLiGcDzgWur80gapBeubfEHJwBSqczcEfgQsNu6fq4kLdCHJh8y1soCIBXLzA2AvwdehVM5SctzGXC/te39z/GbjVQsIm6MiCOAxwKXFMeR1G8vXsjiDxYAqTMi4hRgZ+DT1Vkk9dLbI+LfF/qT3QKQOiYzg+aA4FHAJsVxJPXDmcD+EXHDQv8BC4DUUZl5P+DDNFMBSVqTXwF7RMTFi/mH3AKQOioivg/shdcIS1qzlcATFrv4gxMAqRcy889oLg+6Q3UWSZ2xEjgkIk5dyj/sBEDqgYj4OM0bBT9ZnUVSJ6wEHrfUxR+cAEi9k5mH0UwDtqrOIqnE3OL/heX8IhYAqYcy8+7AscAjqrNIatVUFn9wC0DqpYi4EDgQOBzfJyCNxdQWf3ACIPVeZm4PHAfsU51F0sxMdfEHJwBS70XE+cDDgVcAC74ERFJvTH3xBycA0qBk5i7APwO7VmeRNBUzWfzBCYA0KBHxXZrLg44EbiqOI2l55p7zn/riD04ApMGaTAOOBXavziJp0Wb2yX+OEwBpoOZNA14BXF8cR9LCzXzxBycA0ihk5p8A7wP2q84iaa1aWfzBCYA0ChFxHrA/zb0B1xTHkbR6rS3+4ARAGp3MvAfwXuCRxVEk/VGriz9YAKTRmrxT4F3A7auzSCPX+uIPbgFIoxURJwC7AB+vziKN2Ewf9VsbJwCSyMzHAm8H7lmdRRqRkk/+c5wASCIiTgTuB7wWHxmU2lC6+IMTAEmryMx7A++kedugpOkrX/zBAiBpDSaHBN8B3Kk6izQgnVj8wS0ASWswOSS4I/A24A/FcaQh6MziD04AJC1AZu4OvBvYozqL1FOdWvzBCYCkBYiIbwIPAf4H8NviOFLflD3qtzZOACQtSmbeDngN8GJgveI4Utd17pP/HAuApCXJzAcCRwP7VmeROqqziz9YACQtU2YeQnNQ8B7FUaQu6fTiD54BkLRMEfEp4L7AK/BNgxL0YPEHJwCSpigztwPeCDwTv79onHqx+IP/gUqagczcDzgKeEB1FqlFc6f9T60OshBuAUiauog4DdgNeDJwQXEcqQ1zn/x7sfiDEwBJM5aZGwLPBl4P3LE2jTQTvRn7z2cBkNSKzNwKeDnw18AmxXGkaenl4g8WAEktmxwUfBXwF3iRkPqtt4s/WAAkFcnM+wJHAIcVR5GWoteLP1gAJBXLzAOAI4Hdq7NIC9Sr0/5rYgGQ1AmZeSDwf7AIqNt6/8l/jo8BSuqEiPg8sCfwOODs4jjS6gxm8QcnAJI6KDMD+FPgdcCuxXEkGNjiDxYASR2WmSuAJ9IUgR2L42i8Brf4gwVAUg/MKwKvB3YojqNxGeTiDxYAST2SmesDzwD+J80bCKVZGsRp/zWxAEjqnXlnBP4X8JDiOBqmwX7yn2MBkNRrmflQmiuGH4vf0zQdg1/8wf9YJA1EZu4C/B3wNGD94jjqr1Es/mABkDQwmXlP4G+A5+NLh7Q4o1n8wQIgaaAy8y7Ai4AXAHcqjqPuG/SBv9WxAEgatMzcEDgU+Fs8MKjVG9Un/zkWAEmjkZm7Ay8FngpsUBxH3TDKxR8sAJJGKDPvCryQ5pzAHYrjqM7vaBb/06qDVLAASBqtzNwEeDrwYuCBxXHUrsuBx0TEN6qDVLEASBK3bA/8BU0huE1xHM3WhcDBEfGD6iCVLACSNE9mbgwcQvP0wAH4fXJovgo8PiIuqQ5Szd/YkrQGmbkj8FzgWcCdi+No+Y4DDo+I66uDdIEFQJLWITM3oHn3wHOBR+MTBH1zLfCSiPhgdZAusQBI0iJk5u2BJ9FcObwvsKI2kdbhO8DTIuLc6iBdYwGQpCXKzG1pysBhwN74PbVLbgTeCrw6Im6oDtNF/maVpCnIzHvTTAWeCuxUHGfszgReGBHfrQ7SZRYASZqyzNyV5vrhQ4Dd8XttWy6ieTX0hyMiq8N0nb8pJWmGJtsEf0pTBg4ANq5NNEiXAf8IvCMiVlaH6QsLgCS1JDM3Ax5JUwb+FN9SuFyXAkcDb4uIq6vD9I0FQJIKZOYKYA/gEZM/9gE2LQ3VH98HjgL+NSKuqw7TVxYASeqAyWuL9wL2pykEewEblobqlt8DHwWOBU5zj3/5LACS1EGZuSnNVGB/mkcMdwO2KA3VvhuAU4GPASdExG+L8wyKBUCSemCyZXAfYM/JH3sADwA2qsw1A78BPg+cDHw6Iq4qzjNYFgBJ6qnJFcW70BSCXYEdJ39sU5lrkS4GzqJ5dv9LwNmO99thAZCkgcnMLWiKwA40lxLtMPnxvajbRrgeuAA4B/ge8F2axf7nRXlGzwIgSSMyOVtwF2BrmjccbkPzOOI2kx9vDGw1+em3pVknbkPzzoMtJ3/9dzQL+krgGpprd68CbqJ5Jv83NJ/sLwEupFn4f+kn+275/wGmyOQvh0UQNwAAAABJRU5ErkJggg==',
          email:
            'data:image/png;base64,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',
          website:
            'data:image/png;base64,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', // <-- paste base64 for website icon
          address:
            'data:image/png;base64,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',
        };

        const numBranches = poster.branchesInfo?.length || 0;
        const noBranches = numBranches === 0;
        const addressY = noBranches ? 280 : 375;
        const paddingX = 15;
        const paddingY = poster.website ? 5 : 8;

        if (numBranches >= 3) {
          const fontSize = 30;
          const x = otherDetailsX;
          const elementHeights = contactElements.map(
            (item) =>
              (['email', 'website'].includes(item.type) && item.text.length > 20
                ? 25
                : fontSize) +
              paddingY * 2 +
              10,
          );
          const totalHeight = elementHeights.reduce((a, b) => a + b, 0);
          const startY = 260 - totalHeight / 2;
          let currentY = startY;

          return `
      ${contactElements
              .map((item) => {
                const fSize = item.text.length > 30 ? 20 : 30;
                const mult = item.text.length > 30 ? 10 : 14;
                const rectHeight = fSize + paddingY * 2;
                const y = currentY + fSize;
                const textWidth = item.text.length * 15;
                const iconY = currentY + (rectHeight - 16) / 2;

                const iconSvg = `<image href="${iconMap[item.type]}" x="${x - textWidth / 2 - paddingX + 8
                  }" y="${iconY - 10}" width="20" height="20" />`;

                const textSvg = `
        <rect x="${x - textWidth / 2 - paddingX + 10}" y="${currentY}" 
              width="${textWidth + paddingX * mult + 150}" height="${rectHeight}" 
              fill="${body.backgroundColor1}" rx="10" ry="10" />
        ${iconSvg}
        <text x="${x + 13}" y="${y}" font-size="${fSize}" font-weight="bold" 
              text-anchor="${textAnchor}" fill="${body.backgroundColor2}">
          ${item.text}
        </text>`;

                currentY += rectHeight + 10;
                return textSvg;
              })
              .join('')}
      <image href="${iconMap.address}" x="30" y="${addressY - 20}" width="25" height="25" />
      <text x="70" y="${addressY}" font-size="25" text-anchor="${textAnchor}" fill="${body.backgroundColor2
            }">
        ${poster.address}
      </text>`;
        } else {
          const count = contactElements.length;
          const sectionWidth = baseWidth / count;

          return `
      ${contactElements
              .map((item, index) => {
                const fontSize = item.text.length > 30 ? 20 : 30;
                const textWidth = item.text.length * 15;
                const x = sectionWidth * (index + 0.5);
                const y = yElements;
                const rectX = x - textWidth / 2 - paddingX;
                const rectY = y - fontSize + 8 - paddingY;
                const rectWidth = textWidth + paddingX * 2;
                const rectHeight = fontSize + paddingY * 2;
                const iconY = rectY + (rectHeight - 16) / 2;

                return `
          <rect x="${rectX}" y="${rectY}" width="${rectWidth + 50}" height="${rectHeight}" 
                fill="${body.backgroundColor1}" rx="10" ry="10" />
          <image href="${iconMap[item.type]}" x="${rectX + 8}" y="${iconY - 5
                  }" width="25" height="25" />
          <text x="${rectX + 40}" y="${y}" font-size="${fontSize}" font-weight="bold" 
                text-anchor="start" fill="${body.backgroundColor2}">
            ${item.text}
          </text>`;
              })
              .join('')}
      <image href="${iconMap.address}" x="30" y="${addressY - 20}" width="25" height="25" />
      <text x="70" y="${addressY}" font-size="25" 
            text-anchor="start" fill="${body.backgroundColor2}">
        ${poster.address}
      </text>`;
        }
      })()}

      </svg>`;

    const textOverlayBuffer = await sharp(Buffer.from(textOverlaySvg)).png().toBuffer();

    const composites: sharp.OverlayOptions[] = [
      { input: textOverlayBuffer, top: baseHeight, left: 0 },
    ];
    if (logoBuffer) {
      composites.push({ input: logoBuffer, top: baseHeight + 10, left: 20 });
    }

    const finalImage = await sharp(extendedImage).composite(composites).toBuffer();

    return {
      imageBase64: `data:image/png;base64,${finalImage.toString('base64')}`,
    };
  }

  async createTemplate2(userId: number, body: any) {
    const user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
    if (!user) throw new Error('User not found');

    const storage = await Storage.findOne(body.id);
    if (!storage) throw new Error('Storage file not found');

    const imageData: any = await this.awsService.getFileFromS3(
      process.env.AWS_BUCKET_NAME,
      storage.file,
    );
    if (!imageData || !imageData.Body) throw new Error('Failed to retrieve image data');

    const baseWidth = 1080;
    const baseHeight = 955;

    const baseImage = await sharp(imageData.Body).resize(baseWidth, baseHeight).toBuffer();

    const poster = await createQueryBuilder(PosterConfig, 'poster')
      .leftJoinAndSelect('poster.organization', 'organization')
      .leftJoinAndSelect('poster.orgLogo', 'orgLogo')
      .where('organization.id = :orgId', { orgId: user.organization.id })
      .getOne();

    if (!poster) throw new Error('Poster details not found');

    const hasBranches = poster.branchesInfo && poster.branchesInfo.length > 0;
    const extensionHeight = hasBranches ? 245 : 100;

    const gradientDefs = `
        <defs>
          <linearGradient id="grad" x1="0" y1="0" x2="0" y2="1">
            <stop offset="${hasBranches ? '80%' : '50%'}" stop-color="${body.backgroundColor2 || '#FFFFFF'
      }"/>
            <stop offset="${hasBranches ? '80%' : '50%'}" stop-color="${body.backgroundColor1 || '#FFFFFF'
      }"/>
            <stop offset="100%" stop-color="${body.backgroundColor1 || '#FFFFFF'}"/>
          </linearGradient>
        </defs>`;

    const extendedImage = await sharp(baseImage)
      .extend({
        top: 150,
        bottom: extensionHeight,
        background: { r: 10, g: 73, b: 122, alpha: 1 },
      })
      .toBuffer();
    let logoBuffer: Buffer | null = null;

    if (poster.orgLogo?.file) {
      if (poster.orgLogo?.storageSystem == StorageSystem.AMAZON) {
        const logoData: any = await this.awsService.getFileFromS3(
          process.env.AWS_BUCKET_NAME,
          poster.orgLogo.file,
        );
        if (!logoData || !logoData.Body) throw new Error('Failed to retrieve logo');

        const roundedSquareMask = Buffer.from(`
          <svg width="160" height="160">
            <rect width="160" height="160" rx="15" ry="15" fill="white" />
          </svg>
        `);

        logoBuffer = await sharp(logoData.Body)
          .resize(160, 160)
          .composite([{ input: roundedSquareMask, blend: 'dest-in' }])
          .png()
          .toBuffer();
      } else if (poster.orgLogo?.storageSystem == StorageSystem.MICROSOFT) {
        const metadata: any = await this.oneDriveService.getMetadataByFileId(
          userId,
          poster?.orgLogo?.fileId,
        );
        // console.log({ metadata });

        // Get download URL for the file content
        const downloadUrl = metadata['@microsoft.graph.downloadUrl'];

        if (!downloadUrl) {
          throw new Error('Download URL not found in metadata');
        }

        const response = await axios.get(downloadUrl, { responseType: 'arraybuffer' });

        const circleMask = Buffer.from(`
    <svg width="150" height="150">
      <circle cx="75" cy="75" r="75" fill="white"/>
    </svg>
  `);

        logoBuffer = await sharp(response.data)
          .resize(160, 160)
          .composite([{ input: circleMask, blend: 'dest-in' }])
          .png()
          .toBuffer();
      }
    }

    function escapeXML(str: string): string {
      return str
        .replace(/&/g, '&amp;')
        .replace(/"/g, '&quot;')
        .replace(/'/g, '&apos;')
        .replace(/</g, '&lt;')
        .replace(/>/g, '&gt;');
    }

    const cityStartXLeft = '615';
    const cityStartXRight = '945';
    const branchCount = poster.branchesInfo?.length || 0;
    const cityStartY = [3, 5, 6].includes(branchCount) ? 80 : 55;
    const extendedLineLength = poster.firmName.length * 20;
    const citySpacing = 40;
    const textX = baseWidth * 0.05;
    const otherDetailsX = hasBranches ? 20 : baseWidth / 2;
    const textAnchor = hasBranches ? 'left' : 'middle';
    const yElements = branchCount === 0 ? 35 : branchCount === 1 || branchCount === 2 ? 90 : 70;

    const contactText = hasBranches
      ? poster.branchesInfo
        .map((contact, index) => {
          const buildingIcon =
            'data:image/png;base64,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'; // your full base64 string
          const phoneIcon =
            'data:image/png;base64,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'; // your full base64 string

          if (branchCount === 1) {
            const y = cityStartY + 90;
            return `
      <image href="${buildingIcon}" x="${baseWidth / 2 - 200}" y="${y - 20
              }" width="25" height="25" />
      <text x="${baseWidth / 2 - 170}" y="${y}" font-size="30" text-anchor="start" fill="${body.backgroundColor1
              }">
        ${contact['branch']}
      </text>
      <image href="${phoneIcon}" x="${baseWidth / 2 + 50}" y="${y - 20}" width="25" height="25" />
      <text x="${baseWidth / 2 + 80}" y="${y}" font-size="30" text-anchor="start" fill="${body.backgroundColor1
              }">
        ${contact['branchNumber']}
      </text>`;
          } else if (branchCount === 2) {
            const xPositions = [baseWidth * 0.25, baseWidth * 0.75];
            const y = cityStartY + 90;
            const x = xPositions[index] - 80;
            return `
      <image href="${buildingIcon}" x="${x - 150}" y="${y - 20}" width="25" height="25" />
      <text x="${x - 120}" y="${y}" font-size="26" text-anchor="start" fill="${body.backgroundColor1
              }">
        ${contact['branch']}
      </text>
      <image href="${phoneIcon}" x="${x + 80}" y="${y - 20}" width="25" height="25" />
      <text x="${x + 110}" y="${y}" font-size="26" text-anchor="start" fill="${body.backgroundColor1
              }">
        ${contact['branchNumber']}
      </text>`;
          } else if (branchCount === 3 || branchCount === 4) {
            const y = cityStartY + index * citySpacing;
            const x = baseWidth / 1.2;
            return `
      <image href="${buildingIcon}" x="${x - 330}" y="${y - 18}" width="16" height="16" />
      <text x="${x - 300}" y="${y}" font-size="25" text-anchor="start" fill="${body.backgroundColor1
              }">
        ${contact['branch']}
      </text>
      <image href="${phoneIcon}" x="${x - 100}" y="${y - 18}" width="16" height="16" />
      <text x="${x - 70}" y="${y}" font-size="25" text-anchor="start" fill="${body.backgroundColor1
              }">
        ${contact['branchNumber']}
      </text>`;
          } else {
            const row = Math.floor(index / 2);
            const col = index % 2;
            const x = col === 0 ? parseInt(cityStartXLeft) : parseInt(cityStartXRight);
            const y = cityStartY + row * citySpacing;
            return `
      <image href="${buildingIcon}" x="${x - 190}" y="${y - 13}" width="14" height="14" />
      <text x="${x - 160}" y="${y}" font-size="18" text-anchor="start" fill="${body.backgroundColor1
              }">
        ${contact['branch']}
      </text>
      <image href="${phoneIcon}" x="${x - 25}" y="${y - 13}" width="14" height="14" />
      <text x="${x}" y="${y}" font-size="18" text-anchor="start" fill="${body.backgroundColor1}">
        ${contact['branchNumber']}
      </text>`;
          }
        })

        .join('')
      : '';

    const topExtensionSvg = `
        <svg width="${baseWidth}" height="200" xmlns="http://www.w3.org/2000/svg">
          <defs>
            <linearGradient id="gradTop" x1="0" y1="0" x2="0" y2="1">
              <stop offset="0%" stop-color="${body.backgroundColor1 || '#FFFFFF'}"/>
              <stop offset="100%" stop-color="${body.backgroundColor1 || '#FFFFFF'}"/>
            </linearGradient>
          </defs>
          <rect width="100%" height="100%" fill="url(#gradTop)"/>
          <text x="${textX}" y="80" font-size="${poster.firmName.length > 30 ? 35 : 40
      }" font-weight="bold" text-anchor="left" fill="${body.backgroundColor2}">
            ${escapeXML(poster.firmName.toUpperCase())}
          </text>
          <line x1="${textX}" y1="100" x2="${textX + extendedLineLength
      }" y2="100" stroke="Red" stroke-width="5" />
          <text x="${textX}" y="140" font-size="30" text-anchor="left" fill="${body.backgroundColor2
      }">
            ${escapeXML(poster.firmTagLine.toUpperCase())}
          </text>
        </svg>`;

    const topExtensionBuffer = await sharp(Buffer.from(topExtensionSvg)).png().toBuffer();

    const textOverlaySvg = `
        <svg width="${baseWidth}" height="${extensionHeight}" xmlns="http://www.w3.org/2000/svg">
          ${gradientDefs}
          <rect width="100%" height="100%" fill="url(#grad)"/>
          ${contactText}
          ${(() => {
        const contactElements = [];

        if (poster.mobileNumber)
          contactElements.push({ text: poster.mobileNumber, type: 'mobile' });

        if (poster.email) contactElements.push({ text: poster.email, type: 'email' });

        if (poster.website) contactElements.push({ text: poster.website, type: 'website' });

        const iconMap = {
          mobile:
            'data:image/png;base64,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',
          email:
            'data:image/png;base64,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',
          website:
            'data:image/png;base64,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', // <-- paste base64 for website icon
          address:
            'data:image/png;base64,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',
        };

        const numBranches = poster.branchesInfo?.length || 0;
        const noBranches = numBranches === 0;
        const addressY = noBranches ? 80 : 230;
        const paddingX = 15;
        const paddingY = poster.website ? 5 : 8;

        if (numBranches >= 3) {
          const fontSize = 30;
          const x = otherDetailsX;
          const elementHeights = contactElements.map(
            (item) =>
              (['email', 'website'].includes(item.type) && item.text.length > 20
                ? 25
                : fontSize) +
              paddingY * 2 +
              10,
          );
          const totalHeight = elementHeights.reduce((a, b) => a + b, 0);
          const startY = 120 - totalHeight / 2;
          let currentY = startY;

          return `
      ${contactElements
              .map((item) => {
                const fSize = item.text.length > 30 ? 20 : 30;
                const mult = item.text.length > 30 ? 10 : 14;
                const rectHeight = fSize + paddingY * 2;
                const y = currentY + fSize;
                const textWidth = item.text.length * 15;
                const iconY = currentY + (rectHeight - 16) / 2;

                const iconSvg = `<image href="${iconMap[item.type]}" x="${x - textWidth / 2 - paddingX + 8
                  }" y="${iconY - 10}" width="20" height="20" />`;

                const textSvg = `
        <rect x="${x - textWidth / 2 - paddingX + 10}" y="${currentY}" 
              width="${textWidth + paddingX * mult + 100}" height="${rectHeight}" 
              fill="${body.backgroundColor1}" rx="10" ry="10" />
        ${iconSvg}
        <text x="${x + 13}" y="${y}" font-size="${fSize}" font-weight="bold" 
              text-anchor="${textAnchor}" fill="${body.backgroundColor2}">
          ${item.text}
        </text>`;

                currentY += rectHeight + 10;
                return textSvg;
              })
              .join('')}
      <image href="${iconMap.address}" x="30" y="${addressY - 20}" width="25" height="25" />
      <text x="70" y="${addressY}" font-size="25" text-anchor="${textAnchor}" fill="${body.backgroundColor2
            }">
        ${poster.address}
      </text>`;
        } else {
          const count = contactElements.length;
          const sectionWidth = baseWidth / count;

          return `
      ${contactElements
              .map((item, index) => {
                const fontSize = item.text.length > 30 ? 20 : 30;
                const textWidth = item.text.length * 15;
                const x = sectionWidth * (index + 0.5);
                const y = yElements;
                const rectX = x - textWidth / 2 - paddingX;
                const rectY = y - fontSize + 8 - paddingY;
                const rectWidth = textWidth + paddingX * 2;
                const rectHeight = fontSize + paddingY * 2;
                const iconY = rectY + (rectHeight - 16) / 2;

                return `
          <rect x="${rectX}" y="${rectY}" width="${rectWidth + 50}" height="${rectHeight}" 
                fill="${body.backgroundColor1}" rx="10" ry="10" />
          <image href="${iconMap[item.type]}" x="${rectX + 8}" y="${iconY - 5
                  }" width="25" height="25" />
          <text x="${rectX + 40}" y="${y}" font-size="${fontSize}" font-weight="bold" 
                text-anchor="start" fill="${body.backgroundColor2}">
            ${item.text}
          </text>`;
              })
              .join('')}
      <image href="${iconMap.address}" x="30" y="${addressY - 20}" width="25" height="25" />
      <text x="70" y="${addressY}" font-size="25" 
            text-anchor="start" fill="${body.backgroundColor2}">
        ${poster.address}
      </text>`;
        }
      })()}
        </svg>`;
    const textOverlayBuffer = await sharp(Buffer.from(textOverlaySvg)).png().toBuffer();

    const composites = [
      { input: topExtensionBuffer, top: 0, left: 0 },
      { input: textOverlayBuffer, top: baseHeight + 150, left: 0 },
    ];

    if (logoBuffer) {
      composites.push({ input: logoBuffer, top: 20, left: 900 });
    }

    const finalImage = await sharp(extendedImage).composite(composites).toBuffer();

    return {
      imageBase64: `data:image/png;base64,${finalImage.toString('base64')}`,
    };
  }

  async createTemplate3(userId: number, body: any) {
    const user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
    if (!user) throw new Error('User not found');

    const storage = await Storage.findOne(body.id);
    if (!storage) throw new Error('Storage file not found');

    const imageData: any = await this.awsService.getFileFromS3(
      process.env.AWS_BUCKET_NAME,
      storage.file,
    );
    if (!imageData || !imageData.Body) throw new Error('Failed to retrieve image data');

    const baseWidth = 1080;
    const baseHeight = 955;

    const baseImage = await sharp(imageData.Body).resize(baseWidth, baseHeight).toBuffer();

    const poster = await createQueryBuilder(PosterConfig, 'poster')
      .leftJoinAndSelect('poster.organization', 'organization')
      .leftJoinAndSelect('poster.orgLogo', 'orgLogo') // include logo info
      .where('organization.id = :orgId', { orgId: user.organization.id })
      .getOne();

    if (!poster) throw new Error('Poster details not found');

    const hasBranches = poster.branchesInfo && poster.branchesInfo.length > 0;
    const extensionHeight = hasBranches ? 245 : 100;

    const gradientDefs = `
      <defs>
        <linearGradient id="grad" x1="0" y1="0" x2="0" y2="1">
          <stop offset="${hasBranches ? '80%' : '50%'}" stop-color="${body.backgroundColor2 || '#FFFFFF'
      }"/>
          <stop offset="${hasBranches ? '80%' : '50%'}" stop-color="${body.backgroundColor1 || '#FFFFFF'
      }"/>
          <stop offset="100%" stop-color="${body.backgroundColor1 || '#FFFFFF'}"/>
        </linearGradient>
      </defs>`;

    const extendedImage = await sharp(baseImage)
      .extend({
        top: 150,
        bottom: extensionHeight,
        background: { r: 10, g: 73, b: 122, alpha: 1 },
      })
      .toBuffer();

    let logoBuffer: Buffer | null = null;

    if (poster.orgLogo?.file) {
      if (poster.orgLogo?.storageSystem == StorageSystem.AMAZON) {
        const logoData: any = await this.awsService.getFileFromS3(
          process.env.AWS_BUCKET_NAME,
          poster.orgLogo.file,
        );
        if (!logoData || !logoData.Body) throw new Error('Failed to retrieve logo');

        const roundedSquareMask = Buffer.from(`
          <svg width="160" height="160">
            <rect width="160" height="160" rx="15" ry="15" fill="white" />
          </svg>
        `);

        logoBuffer = await sharp(logoData.Body)
          .resize(160, 160)
          .composite([{ input: roundedSquareMask, blend: 'dest-in' }])
          .png()
          .toBuffer();
      } else if (poster.orgLogo?.storageSystem == StorageSystem.MICROSOFT) {
        const metadata: any = await this.oneDriveService.getMetadataByFileId(
          userId,
          poster?.orgLogo?.fileId,
        );
        // console.log({ metadata });

        // Get download URL for the file content
        const downloadUrl = metadata['@microsoft.graph.downloadUrl'];

        if (!downloadUrl) {
          throw new Error('Download URL not found in metadata');
        }

        const response = await axios.get(downloadUrl, { responseType: 'arraybuffer' });

        const circleMask = Buffer.from(`
    <svg width="150" height="150">
      <circle cx="75" cy="75" r="75" fill="white"/>
    </svg>
  `);

        logoBuffer = await sharp(response.data)
          .resize(160, 160)
          .composite([{ input: circleMask, blend: 'dest-in' }])
          .png()
          .toBuffer();
      }
    }

    function escapeXML(str: string): string {
      return str
        .replace(/&/g, '&amp;')
        .replace(/"/g, '&quot;')
        .replace(/'/g, '&apos;')
        .replace(/</g, '&lt;')
        .replace(/>/g, '&gt;');
    }

    const cityStartXLeft = '615';
    const cityStartXRight = '945';
    const branchCount = poster.branchesInfo?.length || 0;
    const cityStartY = [3, 5, 6].includes(branchCount) ? 80 : 55;

    const citySpacing = 40;
    const textX = baseWidth * 0.05;
    const otherDetailsX = hasBranches ? 20 : baseWidth / 2;
    const textAnchor = hasBranches ? 'left' : 'middle';
    const yElements = branchCount === 0 ? 35 : branchCount === 1 || branchCount === 2 ? 90 : 70;
    const contactText = hasBranches
      ? poster.branchesInfo
        .map((contact, index) => {
          const buildingIcon =
            'data:image/png;base64,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'; // your full base64 string
          const phoneIcon =
            'data:image/png;base64,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'; // your full base64 string

          if (branchCount === 1) {
            const y = cityStartY + 90;
            return `
      <image href="${buildingIcon}" x="${baseWidth / 2 - 200}" y="${y - 20
              }" width="25" height="25" />
      <text x="${baseWidth / 2 - 170}" y="${y}" font-size="30" text-anchor="start" fill="${body.backgroundColor1
              }">
        ${contact['branch']}
      </text>
      <image href="${phoneIcon}" x="${baseWidth / 2 + 50}" y="${y - 20}" width="25" height="25" />
      <text x="${baseWidth / 2 + 80}" y="${y}" font-size="30" text-anchor="start" fill="${body.backgroundColor1
              }">
        ${contact['branchNumber']}
      </text>`;
          } else if (branchCount === 2) {
            const xPositions = [baseWidth * 0.25, baseWidth * 0.75];
            const y = cityStartY + 90;
            const x = xPositions[index] - 80;
            return `
      <image href="${buildingIcon}" x="${x - 150}" y="${y - 20}" width="25" height="25" />
      <text x="${x - 120}" y="${y}" font-size="26" text-anchor="start" fill="${body.backgroundColor1
              }">
        ${contact['branch']}
      </text>
      <image href="${phoneIcon}" x="${x + 80}" y="${y - 20}" width="25" height="25" />
      <text x="${x + 110}" y="${y}" font-size="26" text-anchor="start" fill="${body.backgroundColor1
              }">
        ${contact['branchNumber']}
      </text>`;
          } else if (branchCount === 3 || branchCount === 4) {
            const y = cityStartY + index * citySpacing;
            const x = baseWidth / 1.2;
            return `
      <image href="${buildingIcon}" x="${x - 330}" y="${y - 18}" width="16" height="16" />
      <text x="${x - 300}" y="${y}" font-size="25" text-anchor="start" fill="${body.backgroundColor1
              }">
        ${contact['branch']}
      </text>
      <image href="${phoneIcon}" x="${x - 100}" y="${y - 18}" width="16" height="16" />
      <text x="${x - 70}" y="${y}" font-size="25" text-anchor="start" fill="${body.backgroundColor1
              }">
        ${contact['branchNumber']}
      </text>`;
          } else {
            const row = Math.floor(index / 2);
            const col = index % 2;
            const x = col === 0 ? parseInt(cityStartXLeft) : parseInt(cityStartXRight);
            const y = cityStartY + row * citySpacing;
            return `
      <image href="${buildingIcon}" x="${x - 190}" y="${y - 13}" width="14" height="14" />
      <text x="${x - 160}" y="${y}" font-size="18" text-anchor="start" fill="${body.backgroundColor1
              }">
        ${contact['branch']}
      </text>
      <image href="${phoneIcon}" x="${x - 25}" y="${y - 13}" width="14" height="14" />
      <text x="${x}" y="${y}" font-size="18" text-anchor="start" fill="${body.backgroundColor1}">
        ${contact['branchNumber']}
      </text>`;
          }
        })

        .join('')
      : '';

    const topExtensionSvg = `
      <svg width="${baseWidth}" height="200" xmlns="http://www.w3.org/2000/svg">
        <defs>
          <linearGradient id="gradTop" x1="0" y1="0" x2="0" y2="1">
            <stop offset="0%" stop-color="${body.backgroundColor1 || '#FFFFFF'}"/>
            <stop offset="100%" stop-color="${body.backgroundColor1 || '#FFFFFF'}"/>
          </linearGradient>
        </defs>
        <rect width="100%" height="100%" fill="url(#gradTop)"/>
        <text x="${baseWidth - 30}" y="80" font-size="${poster.firmName.length > 20 ? 40 : 45
      }" font-weight="bold" text-anchor="end" fill="${body.backgroundColor2}">
          ${escapeXML(poster.firmName.toUpperCase())}
        </text>
        <line x1="${baseWidth - 30 - poster.firmName.length * 20}" y1="100" x2="${baseWidth - 30
      }" y2="100" stroke="Red" stroke-width="5" />
        <text x="${baseWidth - 30}" y="140" font-size="30" text-anchor="end" fill="${body.backgroundColor2
      }">
          ${escapeXML(poster.firmTagLine.toUpperCase())}
        </text>
      </svg>`;

    const topExtensionBuffer = await sharp(Buffer.from(topExtensionSvg)).png().toBuffer();

    const contactAndAddressSvg = (() => {
      const contactElements = [];

      if (poster.mobileNumber) contactElements.push({ text: poster.mobileNumber, type: 'mobile' });

      if (poster.email) contactElements.push({ text: poster.email, type: 'email' });

      if (poster.website) contactElements.push({ text: poster.website, type: 'website' });

      const iconMap = {
        mobile:
          'data:image/png;base64,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',
        email:
          'data:image/png;base64,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',
        website:
          'data:image/png;base64,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', // <-- paste base64 for website icon
        address:
          'data:image/png;base64,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',
      };

      const numBranches = poster.branchesInfo?.length || 0;
      const noBranches = numBranches === 0;
      const addressY = noBranches ? 80 : 230;
      const paddingX = 15;
      const paddingY = poster.website ? 5 : 8;

      if (numBranches >= 3) {
        const fontSize = 30;
        const x = otherDetailsX;
        const elementHeights = contactElements.map(
          (item) =>
            (['email', 'website'].includes(item.type) && item.text.length > 20 ? 25 : fontSize) +
            paddingY * 2 +
            10,
        );
        const totalHeight = elementHeights.reduce((a, b) => a + b, 0);
        const startY = 120 - totalHeight / 2;
        let currentY = startY;

        return `
      ${contactElements
            .map((item) => {
              const fSize = item.text.length > 30 ? 20 : 30;
              const mult = item.text.length > 30 ? 10 : 14;
              const rectHeight = fSize + paddingY * 2;
              const y = currentY + fSize;
              const textWidth = item.text.length * 15;
              const iconY = currentY + (rectHeight - 16) / 2;

              const iconSvg = `<image href="${iconMap[item.type]}" x="${x - textWidth / 2 - paddingX + 8
                }" y="${iconY - 10}" width="20" height="20" />`;

              const textSvg = `
        <rect x="${x - textWidth / 2 - paddingX + 10}" y="${currentY}" 
              width="${textWidth + paddingX * mult + 100}" height="${rectHeight}" 
              fill="${body.backgroundColor1}" rx="10" ry="10" />
        ${iconSvg}
        <text x="${x + 13}" y="${y}" font-size="${fSize}" font-weight="bold" 
              text-anchor="${textAnchor}" fill="${body.backgroundColor2}">
          ${item.text}
        </text>`;

              currentY += rectHeight + 10;
              return textSvg;
            })
            .join('')}
      <image href="${iconMap.address}" x="30" y="${addressY - 20}" width="25" height="25" />
      <text x="70" y="${addressY}" font-size="25" text-anchor="${textAnchor}" fill="${body.backgroundColor2
          }">
        ${poster.address}
      </text>`;
      } else {
        const count = contactElements.length;
        const sectionWidth = baseWidth / count;

        return `
      ${contactElements
            .map((item, index) => {
              const fontSize = item.text.length > 30 ? 20 : 30;
              const textWidth = item.text.length * 15;
              const x = sectionWidth * (index + 0.5);
              const y = yElements;
              const rectX = x - textWidth / 2 - paddingX;
              const rectY = y - fontSize + 8 - paddingY;
              const rectWidth = textWidth + paddingX * 2;
              const rectHeight = fontSize + paddingY * 2;
              const iconY = rectY + (rectHeight - 16) / 2;

              return `
          <rect x="${rectX}" y="${rectY}" width="${rectWidth + 50}" height="${rectHeight}" 
                fill="${body.backgroundColor1}" rx="10" ry="10" />
          <image href="${iconMap[item.type]}" x="${rectX + 8}" y="${iconY - 5
                }" width="25" height="25" />
          <text x="${rectX + 40}" y="${y}" font-size="${fontSize}" font-weight="bold" 
                text-anchor="start" fill="${body.backgroundColor2}">
            ${item.text}
          </text>`;
            })
            .join('')}
      <image href="${iconMap.address}" x="30" y="${addressY - 20}" width="25" height="25" />
      <text x="70" y="${addressY}" font-size="25" 
            text-anchor="start" fill="${body.backgroundColor2}">
        ${poster.address}
      </text>`;
      }
    })();

    const textOverlaySvg = `
  <svg width="${baseWidth}" height="${extensionHeight}" xmlns="http://www.w3.org/2000/svg">
    ${gradientDefs}
    <rect width="100%" height="100%" fill="url(#grad)"/>
    ${contactText}
    ${contactAndAddressSvg}
  </svg>`;

    const textOverlayBuffer = await sharp(Buffer.from(textOverlaySvg)).png().toBuffer();

    const composites = [
      { input: topExtensionBuffer, top: 0, left: 0 },
      { input: textOverlayBuffer, top: baseHeight + 150, left: 0 },
    ];

    if (logoBuffer) {
      composites.push({ input: logoBuffer, top: 20, left: 20 });
    }

    const finalImage = await sharp(extendedImage).composite(composites).toBuffer();

    return {
      imageBase64: `data:image/png;base64,${finalImage.toString('base64')}`,
    };
  }

  async getPosters(query) {
    const qb = Storage.createQueryBuilder('storage');
    if (query.eventType === 'DD') {
      qb.where('storage.event_id = :type', { type: query.type });
    } else {
      qb.where('storage.poster_events_id = :type', { type: query.type });
    }
    const posters = await qb.getMany();
    return posters;
  }
}
