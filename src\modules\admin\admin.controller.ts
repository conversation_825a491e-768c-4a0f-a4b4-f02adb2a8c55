import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  ParseIntPipe,
  Post,
  Put,
  Query,
  Req,
  UploadedFiles,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { CreateEventDto } from 'src/modules/events/dto/create-event.dto';
import CreateFormDto from 'src/modules/forms/dto/create-form.dto';
import CreateValidationDto from 'src/modules/forms/dto/create-validation.dto';
import CreateCategoryDto from '../categories/dto/create-category.dto';
import CreateServiceDto from '../services/dto/create-service.dto';
import { AdminService } from './admin.service';
import { UpdateOrganizationProfileDto } from '../organization/dto/update-organization-profile.dto';
import { JwtAuthGuard } from '../users/jwt/jwt-auth.guard';
import { AtomSuperAdminActivity } from '../atom-super-admin-activity/atom-super-admin-activity.entity';
import { createQueryBuilder, getManager } from 'typeorm';
import CreateQuantumTemplateDto from './dto/createQuantumTemplate.dto';
import CreateQuantumCategoryDto from './dto/createQuantumCategories.dto';
import { CreateDocumentLabelDto } from './dto/createDocumentLabelDto';
import { query } from 'express';
import { FilesInterceptor } from '@nestjs/platform-express';
import { PosterEventsDto } from './dto/poster-events.dto';
import { PosterEventTypesDto } from './dto/poster-event-types.dto';

@Controller('admin')
export class AdminController {
  constructor(private service: AdminService) {}

  @Get('/dashboard')
  async getSuperAdminDashboard() {
    return await this.service.getSuperAdminDashboard();
  }

  @Get('/organizations')
  getOrganizations(@Query() query: any) {
    return this.service.getOrganizations(query);
  }
  
  @Get('/organizations/:id')
  getOrganization(@Param() param) {
    return this.service.getOrganization(param.id);
  }

  @Get('allOrganizationUsers')
  getAllOrganizationUsers(@Query() query: any) {
    return this.service.getAllOrganizationUsers(query);
  }

  @Get('/organizationUsers/:id')
  getOrganizationUsers(@Param() param, @Query() query: any) {
    return this.service.getOrganizationUsers(param.id, query);
  }

  @Put('/organizations/:id')
  updateOrganization(
    @Param('id', ParseIntPipe) id: number,
    @Body() body: UpdateOrganizationProfileDto,
  ) {
    return this.service.updateOrganization(id, body);
  }

  @Post('/categories')
  createCategory(@Body() body) {
    return this.service.createCategory(body);
  }

  @Get('/prismblog/:id')
  getServiceBlogDetails(@Param() param: any) {
    return this.service.getServiceBlogDetails(param.id);
  }

  @Get('/prismblogs')
  getAllServiceBlogDetails() {
    return this.service.getAllServiceBlogDetails();
  }

  @UseGuards(JwtAuthGuard)
  @Post('/adminCategoryInProd')
  async createAdminCategoryInProd(@Body() body, @Req() req: any) {
    const { userId } = req.user;
    const serviceResponse = await this.service.createAdminCategoryInProd(body);
    const activityData = {
      serviceResponse,
      ...body,
    };
    const activityResponse = await this.service.createAdminServiceActivity(userId, activityData);
    return {
      serviceResponse,
      activityResponse,
    };
  }

  @UseGuards(JwtAuthGuard)
  @Post('/updateAdminCategoryInProd')
  async updateAdminCategoryInProd(@Body() body, @Req() req: any) {
    const { userId } = req.user;
    const serviceResponse = await this.service.updateAdminCategoryInProd(body);
    const activityData = {
      serviceResponse,
      ...body,
    };
    const activityResponse = await this.service.createAdminServiceActivity(userId, activityData);
    return {
      serviceResponse,
      activityResponse,
    };
  }

  @Get('/categories')
  getCategories() {
    return this.service.getCategories();
  }

  @Get('/newCategories')
  getNewCategories() {
    return this.service.getNewCategories();
  }

  @Put('/categories/:id')
  updateCategory(@Param('id', ParseIntPipe) id: number, @Body() body: CreateCategoryDto) {
    return this.service.update(id, body);
  }

  @Post('/labels')
  createLabel(@Body() body) {
    return this.service.createLabel(body);
  }

  @Get('/labels')
  getLabels() {
    return this.service.getLabels();
  }

  @Get('/newLabels')
  getNewLabels() {
    return this.service.getNewLabels();
  }

  @UseGuards(JwtAuthGuard)
  @Post('/adminLabelInProd')
  async createOrUpdateAdminLabelInProd(@Body() body, @Req() req: any) {
    const { userId } = req.user;
    const activityRecord = await AtomSuperAdminActivity.findOne({
      where: {
        typeId: body.id,
        type: 'Label',
      },
    });
    let serviceResponse;
    if (activityRecord) {
      const prodEventCheck = `SELECT * FROM vider.label WHERE id = ${activityRecord.prodTypeId};`;
      const entityManager = getManager();
      const response = await entityManager.query(prodEventCheck);
      serviceResponse = await this.service.updateAdminlabelInProd(body);
    }

    if (!activityRecord) {
      serviceResponse = await this.service.createOrUpdateAdminLabelInProd(body);
    }

    const activityData = {
      serviceResponse,
      ...body,
    };
    const activityResponse = await this.service.createAdminServiceActivity(userId, activityData);

    return {
      serviceResponse,
      activityResponse,
    };
  }

  @Post('/services')
  createSevice(@Body() body) {
    return this.service.createService(body);
  }

  @Put('/services/:id')
  update(@Param('id', ParseIntPipe) id: number, @Body() body: CreateServiceDto) {
    return this.service.updateService(id, body);
  }

  @Get('/events')
  getEvents(@Query() query: any) {
    return this.service.getEvents(query.startDates);
  }

  @Get('/newEvents')
  getNewEvents() {
    return this.service.getNewEvents();
  }

  @UseGuards(JwtAuthGuard)
  @Post('/events')
  createEvent(@Body() body: CreateEventDto, @Req() req: any) {
    const { userId } = req.user;
    return this.service.createEvent(body, userId);
  }

  @UseGuards(JwtAuthGuard)
  @Post('/adminEventInProd')
  async createAdminEventInProd(@Body() body, @Req() req: any) {
    const { userId } = req.user;
    const serviceResponse = await this.service.createAdminEventInProd(body);
    const activityData = {
      serviceResponse,
      ...body,
    };
    const activityResponse = await this.service.createAdminServiceActivity(userId, activityData);
    return {
      serviceResponse,
      activityResponse,
    };
  }

  @UseGuards(JwtAuthGuard)
  @Post('/updateAdminEventInProd')
  async updateAdminEventInProd(@Body() body, @Req() req: any) {
    const { userId } = req.user;
    const serviceResponse = await this.service.updateAdminEventInProd(body);
    const activityData = {
      serviceResponse,
      ...body,
    };
    const activityResponse = await this.service.createAdminServiceActivity(userId, activityData);
    return {
      serviceResponse,
      activityResponse,
    };
  }

  @Get('/services')
  getServices(@Query() query: any) {
    return this.service.getServices(query);
  }

  @Get('/newServices')
  getNewServices() {
    return this.service.getNewServices();
  }

  @Post('/roles')
  createRole(@Body() body) {
    return this.service.createRole(body);
  }

  @Get('/roles')
  getRoles() {
    return this.service.getRoles();
  }

  @Get('/roles/:roleId')
  getDefaultRoles(@Param('roleId', ParseIntPipe) roleId: number) {
    return this.service.getDefaultRoles(roleId);
  }

  @Get('/newRoles')
  getNewRoles() {
    return this.service.getNewRoles();
  }

  @UseGuards(JwtAuthGuard)
  @Post('/adminRoleInProd')
  async createAdminRoleInProd(@Body() body, @Req() req: any) {
    const { userId } = req.user;
    const adminRoleResponse = await this.service.createAdminRoleInProd(userId, body);

    const activityData = {
      prodTypeId: adminRoleResponse?.insertId,
      ...body,
    };

    const roleAndPermissionInProdResponse = await this.service.createRolePermissionsInProd(
      adminRoleResponse?.insertId,
      body,
    );

    if (adminRoleResponse?.insertId) {
      const activityResponse = await this.service.createAtomSuperAdminActivity(
        userId,
        activityData,
      );
      return {
        adminRoleResponse,
        activityResponse,
        roleAndPermissionInProdResponse,
      };
    }
    return {
      adminRoleResponse,
    };
  }

  @UseGuards(JwtAuthGuard)
  @Put('/adminRoleInProd')
  async updateAdminRoleInProd(@Body() body, @Req() req: any) {
    const { userId } = req.user;
    const updateAdminRoleResponse = await this.service.updateAdminRoleInProd(userId, body);

    const activityData = {
      prodTypeId: body?.atomSuperAdminActivityProdTypeId,
      ...body,
    };

    const roleAndPermissionInProdResponse = await this.service.updateRolePermissionsInProd(
      body?.atomSuperAdminActivityProdTypeId,
      body,
    );

    const activityResponse = await this.service.createAtomSuperAdminActivity(userId, activityData);

    return {
      updateAdminRoleResponse,
      activityResponse,
      roleAndPermissionInProdResponse,
    };
  }

  @UseGuards(JwtAuthGuard)
  @Post('/createAdminServiceInProd')
  async createAdminServiceInProd(@Body() body, @Req() req: any) {
    const { userId } = req.user;
    const serviceResponse = await this.service.createAdminServiceInProd(body);

    const activityData = {
      serviceResponse,
      ...body,
    };
    const activityResponse = await this.service.createAdminServiceActivity(userId, activityData);
    return {
      serviceResponse,
      activityResponse,
    };
  }

  @UseGuards(JwtAuthGuard)
  @Post('/updateAdminServiceInProd')
  async updateAdminServiceInProd(@Body() body, @Req() req: any) {
    const { userId } = req.user;
    const serviceResponse = await this.service.updateAdminServiceInProd(body);

    const activityData = {
      serviceResponse,
      ...body,
    };
    const activityResponse = await this.service.createAdminServiceActivity(userId, activityData);

    return {
      serviceResponse,
      activityResponse,
    };
  }

  @Get('/servicesChecklists/:id')
  getServiceChecklists(@Param() param: any) {
    return this.service.getServiceChecklists(param.id);
  }

  @Post('/forms')
  createForm(@Body() body: CreateFormDto) {
    return this.service.createForm(body);
  }

  @Get('/forms')
  getForms() {
    return this.service.getForms();
  }

  @Post('forms/:id/clone')
  async cloneForm(@Param('id') id: string) {
    return this.service.cloneForm(id);
  }

  @Post('/form-validations')
  createFormValidation(@Body() body: CreateValidationDto) {
    return this.service.createFormValidation(body);
  }

  @Get('/form-validations')
  getFormValidations() {
    return this.service.getFormValidations();
  }

  @Get('/quantumTemplates/:id')
  getQuantumTemplate(@Param('id', ParseIntPipe) id: number) {
    return this.service.getQuantumTemplate(id);
  }

  @Get('/quantumTemplates')
  getQuantumTemplates() {
    return this.service.getQuantumTemplates();
  }

  @Post('/quantumTemplate')
  createQuantumTemplate(@Body() body: CreateQuantumTemplateDto) {
    return this.service.createQuantumTemplate(body);
  }

  @Put('/quantumTemplate/:id')
  updateQuantumTemplate(
    @Param('id', ParseIntPipe) id: number,
    @Body() body: CreateQuantumTemplateDto,
  ) {
    return this.service.updateQuantumTemplate(id, body);
  }

  @Delete('/quantumTemplate/:id')
  deleteQuantumTemplate(@Param('id', ParseIntPipe) id: number) {
    return this.service.deleteQuantumTemplate(id);
  }

  @Get('/documentLabels')
  getDocumentLabels() {
    return this.service.getDocumentLabels();
  }

  @Post('/documentLabels')
  createDocumentLabel(@Body() body: CreateDocumentLabelDto) {
    return this.service.createDocumentLabel(body);
  }

  @Delete('/documentLabel/:id')
  deleteDocumentLabel(@Param('id', ParseIntPipe) id: number) {
    return this.service.deleteDocumentLabel(id);
  }

  @Get('/quantumCategoires')
  getQuantumTemplateCategories() {
    return this.service.getQuantumTemplateCategories();
  }

  @Put('/quantumCategoires/:id')
  updateQuantumCategory(
    @Param('id', ParseIntPipe) id: number,
    @Body() body: CreateQuantumCategoryDto,
  ) {
    return this.service.updateQuantumCategory(id, body);
  }

  @Delete('/quantumCategoires/:id')
  deleteQuantumCategory(@Param('id', ParseIntPipe) id: number) {
    return this.service.deleteQuantumCategory(id);
  }

  @Post('/createQuantumCategories')
  createQuantumCategories(@Body() body: CreateQuantumCategoryDto) {
    return this.service.createQuantumCategories(body);
  }

  @Get('/getQtmNewCategories')
  getNewQtmCategories() {
    return this.service.getNewQtmCategories();
  }

  @UseGuards(JwtAuthGuard)
  @Post('/createAdminQtmCategoryInProd')
  async createAdminQtmCategoryInProd(@Body() body, @Req() req: any) {
    const { userId } = req.user;
    const combinedResponses = await this.service.createAdminQtmCategoryInProd(body);
    const activityData = {
      combinedResponses,
      ...body,
    };
    const activityResponse = await this.service.createQtmResponsesToQtmActivity(
      userId,
      activityData,
    );
    return {
      combinedResponses,
      activityResponse,
    };
  }

  @UseGuards(JwtAuthGuard)
  @Put('/updateAdminQtmCategoryInProd')
  async updateAdminQtmCategoryInProd(@Body() body, @Req() req: any) {
    const { userId } = req.user;
    const combinedResponses = await this.service.updateAdminQtmCategoryInProd(body);
    const activityData = {
      combinedResponses,
      ...body,
    };
    const activityResponse = await this.service.createQtmResponsesToQtmActivity(
      userId,
      activityData,
    );
    return {
      combinedResponses,
      activityResponse,
    };
  }

  @Get('/newQtmTemplates')
  getNewQtmTemplates() {
    return this.service.getNewQtmTemplates();
  }

  @UseGuards(JwtAuthGuard)
  @Post('/createAdminQtmTemplateInProd')
  async createAdminQtmTemplateInProd(@Body() body, @Req() req: any) {
    const { userId } = req.user;
    const templateResponse = await this.service.createAdminQtmTemplateInProd(body);
    const activityData = {
      prodTypeId: templateResponse?.insertId,
      ...body,
    };
    const qtmActivityResponse = await this.service.createQtmSuperAdminActivity(
      userId,
      activityData,
    );
    return {
      templateResponse,
      qtmActivityResponse,
    };
  }

  @UseGuards(JwtAuthGuard)
  @Put('/updateAdminQtmTemplateInProd')
  async updateAdminQtmTemplateInProd(@Body() body, @Req() req: any) {
    const { userId } = req.user;
    const templateResponse = await this.service.updateAdminQtmTemplateInProd(body);
    const activityData = {
      prodTypeId: templateResponse?.prodTypeId,
      ...body,
    };
    const qtmActivityResponse = await this.service.createQtmSuperAdminActivity(
      userId,
      activityData,
    );
    return {
      templateResponse,
      qtmActivityResponse,
    };
  }

  @Get('/notificationConfig/:id')
  getNotificationConfig(@Param() param) {
    return this.service.getNotificationConfig(param.id);
  }

  @Put('/notificationConfig/:id')
  updateNotificationConfig(@Param('id', ParseIntPipe) id: number, @Body() body) {
    return this.service.updateNotificationConfig(id, body);
  }

  @Get('getQuantumConfig/:id')
  getQauntumConfig(@Param() param) {
    return this.service.getQauntumConfig(param.id);
  }

  @Put('/quantumConfig/:id')
  updateQuantumConfig(@Param('id', ParseIntPipe) id: number, @Body() body) {
    return this.service.updateQuantumConfig(id, body);
  }

  @Get(`/quantumRequests`)
  getQuantumRequests(@Query() query: any) {
    return this.service.getQuantumRequests(query);
  }

  @Get('getAutomationConfig/:id')
  getAutomationConfig(@Param() param) {
    return this.service.getAutomationConfig(param.id);
  }

  @Put('/automationConfig/:id')
  updateAutomationConfig(@Param('id', ParseIntPipe) id: number, @Body() body) {
    return this.service.updateAutomationConfig(id, body);
  }

  @Get(`/atomProMachines`)
  getAtomProMachines(@Query() query: any) {
    return this.service.getAtomProMachines(query);
  }

  @Put('/atomProMachines')
  deleteAtomProMachines(@Body() body: any) {
    return this.service.deleteAtomProMachines(body);
  }

  @Put(`/updateInqueueStatus`)
  updateInqueueStatus() {
    return this.service.updateInqueueStatus();
  }

  @Put(`/deleteServerRowsAndChangeStatus`)
  deleteServerRowsAndChangeStatus() {
    return this.service.deleteServerRowsAndChangeStatus();
  }

  @Put('/updateServerStatus')
  updateAutomationServerStatusToAvailable() {
    return this.service.updateAutomationServerStatusToAvailable();
  }

  @Get('atomProLimitRequests')
  getAtomProLimitRequests() {
    return this.service.getAtomProLimitRequests();
  }

  @Put('/atomProLimitRequests/:id')
  updateOrganizationLimitRequest(@Param('id', ParseIntPipe) id: number, @Body() body) {
    return this.service.updateOrganizationLimitRequest(id, body);
  }

  @Get('automationScheduling/:id')
  getAutomationScheduling(@Param() param) {
    return this.service.getAutomationScheduling(param.id);
  }

  @Put('/gstrEnableStatus/:id')
  gstrEnableStatus(@Param() param) {
    return this.service.gstrEnableStatus(param.id);
  }

  @Put('/gstrDisableStatus/:id')
  gstrDisableStatus(@Param() param) {
    return this.service.gstrDisableStatus(param.id);
  }

  @Put('/incometaxEnableStatus/:id')
  incometaxEnableStatus(@Param() param) {
    return this.service.incometaxEnableStatus(param.id);
  }

  @Put('/incometaxDisableStatus/:id')
  incometaxDisableStatus(@Param() param) {
    return this.service.incometaxDisableStatus(param.id);
  }

  @Put('/scheduling')
  automationScheduling(@Body() body: any) {
    return this.service.automationScheduling(body);
  }

  @Get('/posters')
  getPosters(@Query() query: any) {
    return this.service.getPosters(query);
  }

  @Post('/attachments')
  @UseInterceptors(FilesInterceptor('files'))
  addAttachments(
    @UploadedFiles() files: Express.Multer.File[],
    @Body('selectedType') selectedType: string,
    @Body('eventType') eventType: string,
  ) {
    return this.service.saveAttachments(files, selectedType, eventType);
  }

  @UseGuards(JwtAuthGuard)
  @Get('/poster-event-types')
  getPosterEventTypes(@Req() request: any, @Query() query: any) {
    const { userId } = request.user;
    return this.service.getPosterEventTypes(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Post('/posters/event-types')
  createPosterEventType(@Body() body: PosterEventTypesDto, @Req() request: any) {
    const { userId } = request.user;
    return this.service.createPosterEventType(userId, body);
  }

  @UseGuards(JwtAuthGuard)
  @Get('/newPosterEventTypes')
  getNewPosterEventTypes() {
    return this.service.getNewPosterEventTypes();
  }

  @UseGuards(JwtAuthGuard)
  @Post('/adminPosterEventTypesInProd')
  async createAdminPosterEventTypesInProd(@Body() body, @Req() req: any) {
    const { userId } = req.user;
    const serviceResponse = await this.service.createAdminPosterEventTypesInProd(body);
    const activityData = {
      serviceResponse,
      ...body,
    };
    const activityResponse = await this.service.createAdminServiceActivity(userId, activityData);
    return {
      serviceResponse,
      activityResponse,
    };
  }

  @UseGuards(JwtAuthGuard)
  @Post('/updateAdminPosterEventTypesInProd')
  async updateAdminPosterEventTypesInProd(@Body() body, @Req() req: any) {
    const { userId } = req.user;
    const serviceResponse = await this.service.updateAdminPosterEventTypesInProd(body);
    const activityData = {
      serviceResponse,
      ...body,
    };
    const activityResponse = await this.service.createAdminServiceActivity(userId, activityData);
    return {
      serviceResponse,
      activityResponse,
    };
  }

  @UseGuards(JwtAuthGuard)
  @Put('/posters/event-types/:id')
  updatePosterEventType(
    @Body() body: PosterEventTypesDto,
    @Param('id', ParseIntPipe) id: number,
    @Req() request: any,
  ) {
    return this.service.updatePosterEventType(id, body);
  }

  @UseGuards(JwtAuthGuard)
  @Delete('/posters/event-types/:id')
  deletePosterEventTypes(@Param('id', ParseIntPipe) id: number, @Req() request: any) {
    return this.service.deletePosterEventTypes(id);
  }

  @UseGuards(JwtAuthGuard)
  @Get('/poster-events')
  getPosterEvents(@Req() request: any, @Query() query: any) {
    const { userId } = request.user;
    return this.service.getPosterEvents(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Post('/posters/events')
  createPosterEvent(@Body() body: PosterEventsDto, @Req() request: any) {
    const { userId } = request.user;
    return this.service.createPosterEvent(userId, body);
  }

  @UseGuards(JwtAuthGuard)
  @Put('/posters/events/:id')
  updatePosterEvent(
    @Body() body: PosterEventsDto,
    @Param('id', ParseIntPipe) id: number,
    @Req() request: any,
  ) {
    return this.service.updatePosterEvent(id, body);
  }

  @UseGuards(JwtAuthGuard)
  @Delete('/posters/events/:id')
  deletePosterEvents(@Param('id', ParseIntPipe) id: number, @Req() request: any) {
    return this.service.deletePosterEvents(id);
  }

  @UseGuards(JwtAuthGuard)
  @Get('/newPosterEvents')
  getNewPosterEvents() {
    return this.service.getNewPosterEvents();
  }

  @UseGuards(JwtAuthGuard)
  @Post('/adminPosterEventsInProd')
  async createAdminPosterEventsInProd(@Body() body, @Req() req: any) {
    const { userId } = req.user;
    const serviceResponse = await this.service.createAdminPosterEventsInProd(body);
    const activityData = {
      serviceResponse,
      ...body,
    };
    const activityResponse = await this.service.createAdminServiceActivity(userId, activityData);
    return {
      serviceResponse,
      activityResponse,
    };
  }

  @UseGuards(JwtAuthGuard)
  @Post('/updateAdminPosterEventsInProd')
  async updateAdminPosterEventsInProd(@Body() body, @Req() req: any) {
    const { userId } = req.user;
    const serviceResponse = await this.service.updateAdminPosterEventsInProd(body);
    const activityData = {
      serviceResponse,
      ...body,
    };
    const activityResponse = await this.service.createAdminServiceActivity(userId, activityData);
    return {
      serviceResponse,
      activityResponse,
    };
  }

  @Get('/newPosterImages')
  getNewPosterImages() {
    return this.service.getNewPosterImages();
  }

  @UseGuards(JwtAuthGuard)
  @Post('/createNewPosterImageInProd')
  createNewPosterImageInProd(@Body() body, @Req() req: any) {
    const { userId } = req.user;
    return this.service.createNewPosterImageInProd(body, userId);
  }

  @Put('/roles/:id')
  updateAdminRole(@Param() param, @Body() body: any) {
    return this.service.updateAdminRole(param.id, body);
  }

  @UseGuards(JwtAuthGuard)
  @Post('/create-email-template')
  async createEmailTemplate(@Req() req: any, @Body() body: any) {
    const { userId } = req.user;
    return await this.service.createEmailTemplate(body, userId);
  }

  @UseGuards(JwtAuthGuard)
  @Get('/get-email-template')
  async getEmailTemplates(@Req() req: any, @Query() query: any) {
    const { userId } = req.user;
    return this.service.getEmailTemplates(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Get('/get-one-emailTemplate/:id')
  async getOneEmailTemplate(@Param('id', ParseIntPipe) id: number, @Req() req: any) {
    const { userId } = req.user;
    return this.service.getOneEmailTemplate(userId, id);
  }

  @UseGuards(JwtAuthGuard)
  @Put('update-email-temlate/:id')
  async updateEmailTemplate(@Param('id', ParseIntPipe) id: number, @Body() body, @Req() req: any) {
    const { userId } = req.user;
    return this.service.updateEmailTemplate(userId, id, body);
  }

  @UseGuards(JwtAuthGuard)
  @Delete('/delete-email-template/:id')
  async deleteEmailTemplate(@Param('id', ParseIntPipe) id: number, @Req() req: any) {
    const { userId } = req.user;
    return this.service.deleteEmailTemplate(id, userId);
  }

  @Get('/newEmailTemplates')
  getNewEmailTemplates() {
    return this.service.getNewEmailTemplates();
  }

  @UseGuards(JwtAuthGuard)
  @Post('/createAdminEmailTemplatesInProd')
  async createAdminEmailTemplateInProd(@Body() body, @Req() req: any) {
    const { userId } = req.user;
    const serviceResponse = await this.service.createAdminEmailTemplateInProd(body);
    const activityData = {
      serviceResponse,
      ...body,
    };
    const activityResponse = await this.service.createAdminServiceActivity(userId, activityData);
    return {
      serviceResponse,
      activityResponse,
    };
  }

  @UseGuards(JwtAuthGuard)
  @Post('/updateAdminEmailTemplatesInProd')
  async updateAdminEmailTemplateInProd(@Body() body, @Req() req: any) {
    const { userId } = req.user;
    const serviceResponse = await this.service.updateAdminEmailTemplateInProd(body);
    const activityData = {
      serviceResponse,
      ...body,
    };
    const activityResponse = await this.service.createAdminServiceActivity(userId, activityData);
    return {
      serviceResponse,
      activityResponse,
    };
  }
}
