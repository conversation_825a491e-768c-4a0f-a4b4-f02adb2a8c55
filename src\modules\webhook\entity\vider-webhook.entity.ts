import {
    BaseEntity,
    Column,
    CreateDateColumn,
    Entity,
    ManyToOne,
    OneToMany,
    PrimaryGeneratedColumn,
    UpdateDateColumn,
} from 'typeorm';

@Entity()
class ViderWebhook extends BaseEntity {
    @PrimaryGeneratedColumn()
    id: number;

    @Column({ type: 'timestamp', nullable: true })
    createdTimestamp: Date;

    @UpdateDateColumn()
    lastUpdated: string

    @Column()
    source: string

    @Column('json')
    payload: string

    @Column()
    comments: string

    @Column()
    status: string

    @Column()
    destinationApplication: string

}

export default ViderWebhook;