import {
  Connection,
  EntitySubscriberInterface,
  EventSubscriber,
  InsertEvent,
  UpdateEvent,
  getManager,
} from 'typeorm';
import {
  insertINTONotificationUpdate,
  convertMillisecondsToHours,
  convertMillisecToHrsAndMins,
} from 'src/utils/re-use';
import { sendnewMail } from 'src/emails/newemails';
import LogHour, { LogHourType } from 'src/modules/log-hours/log-hour.entity';
import { EXPENDITURE_STATUS } from 'src/modules/expenditure/dto/types';
import { User } from 'src/modules/users/entities/user.entity';
import BudgetedHours from 'src/modules/budgeted-hours/budgeted-hours.entity';
import { Organization } from 'src/modules/organization/entities/organization.entity';

let oldApprovalStatus: EXPENDITURE_STATUS;
@EventSubscriber()
export class LogHourSubscriber implements EntitySubscriberInterface<LogHour> {
  constructor(private readonly connection: Connection) {
    connection.subscribers.push(this);
  }

  listenTo() {
    return LogHour;
  }

  async beforeInsert(event: InsertEvent<LogHour>) {}

  async afterInsert(event: InsertEvent<LogHour>) {
    const LogHours = event?.entity;
    const {
      duration,
      title: logTitle,
      client,
      clientGroup,
      task,
      user,
      type,
      approvalStatus,
      managers,
      status,
    } = LogHours;
    const userName = user?.fullName;
    const userId: any = user?.id;
    const orgId = user?.organization?.id;
    if (status !== 'started' && status !== 'paused') {
      function formatDuration(durationInMillis) {
        const hours = Math.floor(durationInMillis / 3600000); // 1 hour = 3600000 milliseconds
        const minutes = Math.floor((durationInMillis % 3600000) / 60000); // 1 minute = 60000 milliseconds

        const formattedHours = hours.toString().padStart(2, '0');
        const formattedMinutes = minutes.toString().padStart(2, '0');

        return `${formattedHours}:${formattedMinutes}`;
      }
      const logDuration = formatDuration(duration);
      const clientName = client ? client?.displayName : clientGroup?.displayName;

      if (type === 'TASK') {
        const taskName = task?.name;

        let title = 'Task Log Hour Add';
        const body = `Logged ${logDuration} hours on Task: ${taskName} for ${
          client ? 'Client' : 'Clients Group'
        }: ${clientName}`;
        const key = 'TIMESHEET_ADDED_PUSH';

        async function checkBudgetHour(user, task) {
          const budgetedHours = await BudgetedHours.findOne({ where: { task, user } });


          if (budgetedHours) {
            const entityManager = getManager();
            const query = `
            SELECT SUM(duration) AS totalDuration 
            FROM log_hour 
            WHERE user_id = ? 
              AND task_id = ? 
          `;

            const params = [user.id, task.id];

            const [result] = await entityManager.query(query, params);
            const loggedDuration = Number(duration) + Number(result?.totalDuration) || 0;

            if (loggedDuration > budgetedHours?.budgetedHours) {
              const diff = loggedDuration - budgetedHours?.budgetedHours;
              let title = 'Employee Timesheet Variance';

              const managersBody = `"<strong>${
                user.fullName
              }</strong>" has recorded additional time for Task ID <strong>${
                task?.taskNumber
              }</strong> | Task Name <strong>${taskName}</strong>
             of Client <strong>${
               task?.client?.displayName
             }</strong> which continues to exceed its budget hours. Budgeted: <strong>${convertMillisecToHrsAndMins(
                budgetedHours?.budgetedHours,
              )}</strong>, 
             Current Logged: <strong>${convertMillisecToHrsAndMins(
               duration,
             )}</strong> (additional), Total Logged (until now): <strong>${convertMillisecToHrsAndMins(
                loggedDuration,
              )}</strong>, Difference: <strong>${convertMillisecToHrsAndMins(diff)}</strong>.`;
              insertINTONotificationUpdate(title, managersBody, [user?.id], orgId, key);
            } else {
            }
          }
        }

        await checkBudgetHour(user, task);

        if (approvalStatus === EXPENDITURE_STATUS.PENDING) {
          title = 'Task Log Hours Approval Request';
          const users: any[] = managers.map((user) => user.id);
          //Managers
          // const managersBody = `A log hour request from ${user?.fullName} is pending your approval`;
          const managersBody = `A log hours request from <strong>${user?.fullName}</strong> for <strong>${clientName}</strong> on <strong>${task?.taskNumber}</strong>, <strong>${taskName}</strong> for <strong>${logDuration}</strong> is pending your approval`;
          insertINTONotificationUpdate(title, managersBody, users, orgId, key);
          //Employees
          // const employeeBody = `Your log hours request has been sent to your superiors for approval`;
          const employeeBody = `Your log hours request for <strong>${clientName}</strong> on <strong>${task?.taskNumber}</strong>, <strong>${taskName}</strong> for <strong>${logDuration}</strong> has been sent to your superiors for approval`;
          insertINTONotificationUpdate(title, employeeBody, [user.id as any], orgId, key);
        } else {
          // If No Approvals
          insertINTONotificationUpdate(title, body, [userId], orgId, key);
        }
        const organization = await Organization.findOne({ id: orgId });
        
                const addressParts = [
                  organization.buildingNo || '',
                  organization.floorNumber || '',
                  organization.buildingName || '',
                  organization.street || '',
                  organization.location || '',
                  organization.city || '',
                  organization.district || '',
                  organization.state || '',
                ].filter((part) => part && part.trim() !== '');
                const pincode =
                  organization.pincode && organization.pincode.trim() !== ''
                    ? ` - ${organization.pincode}`
                    : '';
                const address = addressParts.join(', ') + pincode;

        await sendnewMail({
          id: user?.id,
          key: 'TIMESHEET_ADDED_MAIL',
          email: user?.email,
          data: {
            taskName: taskName,
            clientName: clientName || '',
            duration: logDuration,
            userName: userName,
            userId: event.entity['userId'],
            adress: address,
            phoneNumber: organization?.mobileNumber,
            mail: organization?.email,
            legalName: organization?.tradeName || organization?.legalName,
          },
          filePath: 'timesheet-add-task',
          subject: `Timesheet Update: Task Logged`,
        });
      } else if (type === 'GENERAL') {
        let title = 'General Log Hour Add';
        const body = `Logged ${logDuration} hours for general with Title: ${logTitle}`;
        const key = 'TIMESHEET_ADDED_PUSH';

        if (approvalStatus === EXPENDITURE_STATUS.PENDING) {
          title = 'General Log Hours Approval Request';
          const users: any[] = managers.map((user) => user.id);
          //Managers
          // const managersBody = `A log hour request from ${user?.fullName} is pending your approval`;
          const managersBody = `A log hour request from <strong>${
            user?.fullName
          }</strong> for <strong>${logTitle}</strong> ${
            clientName ? `with <strong>${clientName}</strong>` : ''
          } covering <strong>${logDuration}</strong> is pending your approval`;
          insertINTONotificationUpdate(title, managersBody, users, orgId, key);
          //Employees
          // const employeeBody = `Your log hours request has been sent to your superiors for approval`;
          const employeeBody = `Your log hours request for <strong>${logTitle}</strong> ${
            clientName ? `with <strong>${clientName}</strong>` : ''
          } covering <strong>${logDuration}</strong> has been sent to your superiors for approval`;
          insertINTONotificationUpdate(title, employeeBody, [user.id as any], orgId, key);
        } else {
          //If No Approvals
          insertINTONotificationUpdate(title, body, [userId], orgId, key);
        }
        const organization = await Organization.findOne({ id: orgId });
        
                const addressParts = [
                  organization.buildingNo || '',
                  organization.floorNumber || '',
                  organization.buildingName || '',
                  organization.street || '',
                  organization.location || '',
                  organization.city || '',
                  organization.district || '',
                  organization.state || '',
                ].filter((part) => part && part.trim() !== '');
                const pincode =
                  organization.pincode && organization.pincode.trim() !== ''
                    ? ` - ${organization.pincode}`
                    : '';
                const address = addressParts.join(', ') + pincode;


        await sendnewMail({
          id: user?.id,
          key: 'TIMESHEET_ADDED_MAIL',
          email: user?.email,
          data: {
            timeSheetTitle: title,
            duration: logDuration,
            userName: userName,
            userId: event.entity['userId'],
            adress: address,
            phoneNumber: organization?.mobileNumber,
            mail: organization?.email,
            legalName: organization?.tradeName || organization?.legalName,
          },
          filePath: 'timesheet-add',
          subject: `Timesheet Update: General Hours Logged`,
        });
      }
    } else {
    }
  }

  async beforeUpdate(event: UpdateEvent<LogHour>) {
    oldApprovalStatus = event?.databaseEntity?.approvalStatus;
  }

  async afterUpdate(event: UpdateEvent<LogHour>) {
    const {
      title: logTitle,
      duration,
      approvalStatus,
      type,
      userId,
      user,
      managers,
      client,
      clientGroup,
      task,
    } = event.entity;

    const logInUser = await User.findOne({ where: { id: userId }, relations: ['organization'] });
    const orgId = logInUser?.organization?.id;
    const key = 'TIMESHEET_ADDED_PUSH';
    const clientName = client ? client?.displayName : clientGroup?.displayName;
    function formatDuration(durationInMillis) {
      const hours = Math.floor(durationInMillis / 3600000); // 1 hour = 3600000 milliseconds
      const minutes = Math.floor((durationInMillis % 3600000) / 60000); // 1 minute = 60000 milliseconds

      const formattedHours = hours.toString().padStart(2, '0');
      const formattedMinutes = minutes.toString().padStart(2, '0');

      return `${formattedHours}:${formattedMinutes}`;
    }
    const logDuration = formatDuration(duration);
    if (
      oldApprovalStatus === EXPENDITURE_STATUS.PENDING &&
      approvalStatus === EXPENDITURE_STATUS.REJECTED
    ) {
      let title: string;
      let body;
      let employeeBody;
      let managerBody;
      if (type === 'TASK') {
        title = 'Task Log Hours Rejected';
        body = `You have rejected <strong>${user?.fullName}</strong>'s log hour request for <strong>${clientName}</strong> on <strong>${task?.taskNumber}</strong>, <strong>${task?.name}</strong> for <strong>${logDuration}</strong>`;
        employeeBody = `Your log hour request for <strong>${clientName}</strong> on <strong>${task?.taskNumber}</strong>, <strong>${task?.name}</strong> for <strong>${logDuration}</strong> has been rejected by <strong>${logInUser.fullName}</strong>`;
        managerBody = `<strong>${logInUser?.fullName}</strong> has rejected <strong>${user?.fullName}</strong>'s log hour request for <strong>${clientName}</strong> on <strong>${task?.taskNumber}</strong>, <strong>${task?.name}</strong> for <strong>${logDuration}</strong>`;
      } else if (type === LogHourType.GENERAL) {
        title = 'General Log Hours Rejected';
        body = `You have rejected <strong>${
          user?.fullName
        }</strong>'s log hour request for <strong>${logTitle}</strong> ${
          clientName ? `with <strong>${clientName}</strong>` : ''
        } covering <strong>${logDuration}</strong>`;
        employeeBody = `Your log hour request for  <strong>${logTitle}</strong>  with <strong>${clientName}</strong>  covering  <strong>${logDuration}</strong> has been rejected by  <strong>${logInUser.fullName}</strong>`;
        managerBody = `<strong>${logInUser?.fullName}</strong> has rejected  <strong>${
          user?.fullName
        }</strong>'s log hour request for  <strong>${logTitle}</strong> ${
          clientName ? `with <strong>${clientName}</strong>` : ''
        } covering  <strong>${logDuration}</strong>`;
      }

      //Login User
      insertINTONotificationUpdate(title, body, [logInUser.id as any], orgId, key);
      //Employee
      insertINTONotificationUpdate(title, employeeBody, [user.id as any], orgId, key);
      //Managers
      const managerUsers: any[] = managers.map((user) => user.id).filter((id) => id != userId);
      insertINTONotificationUpdate(title, managerBody, managerUsers, orgId, key);
    } else if (
      oldApprovalStatus === EXPENDITURE_STATUS.PENDING &&
      approvalStatus === EXPENDITURE_STATUS.APPROVED
    ) {
      let title: string;
      let body: string;
      let employeeBody: string;
      let managerBody: string;
      if (type === 'TASK') {
        title = 'Task Log Hours Approved';
        body = `You have approved <strong>${user.fullName}</strong>'s log hour request for <strong>${clientName}</strong> on <strong>${task?.taskNumber}</strong>, <strong>${task?.name}</strong> for <strong>${logDuration}</strong>`;
        employeeBody = `Your log hour request for <strong>${clientName}</strong> on <strong>${task?.taskNumber}</strong>, <strong>${task?.name}</strong> for <strong>${logDuration}</strong> has been approved by <strong>${logInUser.fullName}</strong>`;
        managerBody = `<strong>${logInUser.fullName}</strong> has approved <strong>${user.fullName}</strong>'s log hour request for <strong>${clientName}</strong> on <strong>${task?.taskNumber}</strong>, <strong>${task?.name}</strong> for <strong>${logDuration}</strong>`;
      } else if (type === LogHourType.GENERAL) {
        title = 'General Log Hours Approved';
        body = `You have approved <strong>${
          user.fullName
        }</strong>'s log hour request for <strong>${logTitle}</strong> ${
          clientName ? `with <strong>${clientName}</strong>` : ''
        } covering <strong>${logDuration}</strong>`;
        employeeBody = `Your log hour request for <strong>${logTitle}</strong> ${
          clientName ? `with <strong>${clientName}</strong>` : ''
        } covering <strong>${logDuration}</strong> has been approved by <strong>${
          logInUser.fullName
        }</strong>`;
        managerBody = `<strong>${logInUser.fullName}</strong> has approved <strong>${
          user.fullName
        }</strong>'s log hour request for <strong>${logTitle}</strong> ${
          clientName ? `with <strong>${clientName}</strong>` : ''
        } covering <strong>${logDuration}</strong>`;
      }

      //Login User
      insertINTONotificationUpdate(title, body, [logInUser.id as any], orgId, key);
      //Employee
      insertINTONotificationUpdate(title, employeeBody, [user.id as any], orgId, key);

      //Managers
      const managerUsers: any[] = managers.map((user) => user.id).filter((id) => id != userId);
      insertINTONotificationUpdate(title, managerBody, managerUsers, orgId, key);
    } else if (
      oldApprovalStatus === EXPENDITURE_STATUS.REJECTED &&
      approvalStatus === EXPENDITURE_STATUS.PENDING
    ) {
      let title: string;
      let body: string;
      let managerBody: string;
      if (type === 'TASK') {
        title = 'Task Log Hours Approval Request';
        body = `Your modified log hour request for <strong>${clientName}</strong> on <strong>${task?.taskNumber}</strong>, <strong>${task?.name}</strong> for <strong>${logDuration}</strong> has been sent to your superiors for approval`;
        managerBody = `<strong>${user.fullName}</strong> has resubmitted the log hour request with modifications for <strong>${clientName}</strong> on <strong>${task?.taskNumber}</strong>, <strong>${task?.name}</strong> for <strong>${logDuration}</strong> for your approval`;
      } else if (type === LogHourType.GENERAL) {
        title = 'General Log Hours Approval Request';
        body = `Your modified log hour request for <strong>${logTitle}</strong> ${
          clientName ? `with <strong>${clientName}</strong>` : ''
        } covering <strong>${logDuration}</strong> has been sent to your superiors for approval`;
        managerBody = `<strong>${
          user.fullName
        }</strong> has resubmitted the log hour request for <strong>${logTitle}</strong> ${
          clientName ? `with <strong>${clientName}</strong>` : ''
        } covering <strong>${logDuration}</strong> with modifications for your approval.`;
      }
      //Employee User
      insertINTONotificationUpdate(title, body, [user.id as any], orgId, key);
      //Mangers
      const managerUsers: any[] = managers.map((user) => user.id).filter((id) => id != userId);
      insertINTONotificationUpdate(title, managerBody, managerUsers, orgId, key);
    }
  }
}
