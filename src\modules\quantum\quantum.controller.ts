import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  ParseIntPipe,
  Post,
  Put,
  Query,
  Req,
  UseGuards,
} from '@nestjs/common';
import { JwtAuthGuard } from 'src/modules/users/jwt/jwt-auth.guard';

import { query } from 'express';
import { QuantumService } from './quantum.service';

@Controller('quantum')
export class QuantumController {
  constructor(private service: QuantumService) {}

  @UseGuards(JwtAuthGuard)
  @Get('/createdTemplates')
  async getCreatedTemplates(@Req() request: any, @Query() query: any) {
    const { userId } = request.user;
    return this.service.getCreatedTemplates(userId, query);
  }

  @Get('/getPreviewDetails/:id')
  async getPreviewDetails(@Param('id') id: any) {
    return this.service.getPreviewDetails(id);
  }

  @Get('qtm-tempaltes')
  async getQuantumTemplates(@Query() query: any) {
    return this.service.getQuantumTemplates(query);
  }

  @UseGuards(JwtAuthGuard)
  @Get('/syncToQuantum')
  async syncToQuantum(@Req() request: any) {
    const { userId } = request.user;
    return this.service.syncToQuantum(userId);
  }

  @Get('/qtm-categories')
  async getQtmqtmCategories() {
    return this.service.getQtmCategories();
  }

  @UseGuards(JwtAuthGuard)
  @Post('/downlaod-document')
  async downlaodQtmDocument(@Body() body: any, @Req() request: any) {
    const { userId } = request.user;
    return this.service.downlaodQtmDocument(body, userId);
  }

  @Delete('delete-document/:id')
  async deleteDocument(@Param('id') id) {
    return this.service.deleteDocument(id);
  }

  @Get('/client-documents')
  async getClientQtmDocuments(@Param('id') id: any, @Query() query: any) {
    return this.service.getClientQtmDocuments(id, query);
  }

  @UseGuards(JwtAuthGuard)
  @Get('/quantum-analytics')
  async getQuantumAnalytics(@Req() request: any) {
    const { userId } = request.user;
    return this.service.getQuantumAnalytics(userId);
  }

  @UseGuards(JwtAuthGuard)
  @Get('/quantum-user-analytics')
  async getQuantumDocumentAnalytics(@Req() request: any, @Query() query: any) {
    const { userId } = request.user;
    return this.service.getQuantumDocumentAnalytics(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Get('config')
  async getQtmqtmConfig(@Req() request: any) {
    const { userId } = request.user;
    return this.service.getQtmqtmConfig(userId);
  }

  @UseGuards(JwtAuthGuard)
  @Post('sendRequest')
  async sendQuantumRequest(@Req() request: any) {
    const { userId } = request.user;
    return this.service.sendQuantumRequest(userId);
  }
}
