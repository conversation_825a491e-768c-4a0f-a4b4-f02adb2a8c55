import { Optional } from '@nestjs/common';
import { Organization } from 'src/modules/organization/entities/organization.entity';
import {
  BaseEntity,
  Column,
  CreateDateColumn,
  Entity,
  ManyToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import AutClientCredentials from './aut_client_credentials.entity';
import { User } from 'src/modules/users/entities/user.entity';
import GstrCredentials from 'src/modules/gstr-automation/entity/gstrCredentials.entity';
import TanClientCredentials from 'src/modules/tan-automation/entity/tan-client-credentials.entity';

export enum TypeEnum {
  INCOMETAX = 'INCOMETAX',
  GSTR = 'GSTR',
  TAN = 'TAN',
  TRACES = 'TRACES',
}

@Entity()
class AutomationMachines extends BaseEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  machineName: string;

  @ManyToOne(
    () => AutClientCredentials,
    (autClientCredentials) => autClientCredentials.autoCredentials,
    { onDelete: 'SET NULL' },
  )
  autoCredentials: AutClientCredentials;

  @ManyToOne(() => GstrCredentials, (gstrCredentials) => gstrCredentials.gstrCredentials, {
    onDelete: 'SET NULL',
  })
  gstrCredentials: GstrCredentials;

  @ManyToOne(
    () => TanClientCredentials,
    (tanClientCredentials) => tanClientCredentials.tanCredentials,
    { onDelete: 'SET NULL' },
  )
  tanCredentials: TanClientCredentials;

  @Column('json')
  modules: object;

  @Column()
  status: string;

  @ManyToOne(() => User, (user) => user.automationMachines)
  user: User;

  @Column()
  remarks: string;

  @CreateDateColumn()
  createdAt: string;

  @UpdateDateColumn()
  updatedAt: string;

  @Column('json')
  completeModules: object;

  @Column({ default: TypeEnum.INCOMETAX, type: 'enum', enum: TypeEnum })
  type: TypeEnum;

  @Column('json')
  failedModules: object;
}

export default AutomationMachines;
