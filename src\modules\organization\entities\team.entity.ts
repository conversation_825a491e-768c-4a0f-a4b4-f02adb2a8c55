import { User } from 'src/modules/users/entities/user.entity';
import {
  BaseEntity,
  Column,
  Entity,
  JoinTable,
  ManyToMany,
  ManyToOne,
  PrimaryGeneratedColumn,
} from 'typeorm';
import { Organization } from './organization.entity';

@Entity()
class Team extends BaseEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ unique: true })
  name: string;

  @ManyToMany(() => User)
  @JoinTable({ name: 'team_members' })
  members: User[];

  @Column({ nullable: true, type: 'simple-array' })
  tags: string[];

  @ManyToOne(() => Organization, (organization) => organization.teams)
  organization: Organization;
}

export default Team;
