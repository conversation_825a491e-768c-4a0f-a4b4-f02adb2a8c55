import { Optional } from '@nestjs/common';
import Client from 'src/modules/clients/entity/client.entity';
import { Organization } from 'src/modules/organization/entities/organization.entity';
import {
  BaseEntity,
  Column,
  CreateDateColumn,
  Entity,
  ManyToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import AutClientCredentials from './aut_client_credentials.entity';

@Entity()
class AutUpdateTracker extends BaseEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column('json')
  returns: object;

  @Column('json')
  forms: object;

  @Column('json')
  outstandingDemand: object;

  @Column('json')
  eProceedingFya: object;

  @Column('json')
  eProceedingFyi: object;

  @Column('json')
  eChallan: object;

  @Column('json')
  myCas: object;

  @Column('json')
  tempNoticeFya: object;

  @Column('json')
  tempNoticeFyi: object;

  @Column()
  isChange: boolean;

  @Column()
  organizationId: number;

  @Column()
  clientId: number;

  @ManyToOne(() => Client, (client) => client.autUpdateTracker, { onDelete: 'SET NULL' })
  client: Client;

  @Column()
  autClientCredentialsId: number;

  @CreateDateColumn()
  createdAt: string;

  @UpdateDateColumn()
  updatedAt: string;
}

export default AutUpdateTracker;
