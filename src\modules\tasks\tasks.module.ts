import { Module, forwardRef } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { CommentListener } from 'src/event-listeners/task/comment-listener';
import { ExpenditureListener } from 'src/event-listeners/task/expenditure.listener';
import { MilestoneListener } from 'src/event-listeners/task/milestone-listener';
import { SubTaskListener } from 'src/event-listeners/task/sub-task.listener';
import { TaskListener } from 'src/event-listeners/task/task.listener';
import { AwsService } from 'src/modules/storage/upload.service';
import { NotificationsService } from 'src/notifications/notifications.service';
import { TaskSubscriber } from '../../event-subscribers/task.subscriber';
import { AttachmentsController } from './controllers/attachments.controller';
import { ChecklistsController } from './controllers/checklists.controller';
import { CommentsController } from './controllers/comments.controller';
import { MilestonesController } from './controllers/milestones.controller';
import { StageOfWorkController } from './controllers/stage-of-work.controller';
import { TasksController } from './controllers/task.controller';
import ChecklistItem from './entity/checklist-item.entity';
import Checklist from './entity/checklist.entity';
import TaskComment from './entity/comment.entity';
import Milestone from './entity/milestone.entity';
import StageOfWork from './entity/stage-of-work.entity';
import TaskStatus from './entity/task-status.entity';
import Task from './entity/task.entity';
import { AttachmentsService } from './services/attachments.service';
import { ChecklistsService } from './services/checklists.service';
import { CommentsServie } from './services/comments.service';
import { MilestonesService } from './services/milestones.service';
import { StageOfWorkService } from './services/stage-of-work.service';
import { TasksService } from './services/tasks.service';
import { ChecklistitemSubscriber } from 'src/event-subscribers/checklistitem.subscriber';
import { TaskcommentSubscriber } from 'src/event-subscribers/taskcomment.subscriber';
import { SubTaskSubscribers } from 'src/event-subscribers/subtask.subscriber';
import { TaskStatusSubscriber } from 'src/event-subscribers/taskStatusChange.subscriber';
import { PriorityChangeSubscriber } from 'src/event-subscribers/prioritychanging.subscriber';
import { TaskExpenditureSubscriber } from 'src/event-subscribers/taskExpenditure.subscriber';
import { TaskDeleteSubscriber } from 'src/event-subscribers/taskDelete.subscriber';
import { APP_INTERCEPTOR } from '@nestjs/core';
import { InsertionInterceptor } from 'src/interceptor/insertion.interceptor';
import { StorageService } from '../storage/storage.service';
import { ChecklistSubscriber } from 'src/event-subscribers/checklist.sebscriber';
import { OneDriveStorageService } from '../ondrive-storage/onedrive-storage.service';
import { OneDriveStorageModule } from '../ondrive-storage/onedrive-storage.module';
import { BharathStorageService } from '../storage/bharath-storage.service';
import { BharathCloudService } from '../storage/bharath-upload.service';
import { GoogleDriveStorageService } from '../ondrive-storage/googledrive-storage.service';
import { PromiseService } from '../gstr-register/services/promise.service';
import { GstrRegisterService } from '../gstr-register/services/gstr-register.service';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      Task,
      TaskComment,
      Checklist,
      ChecklistItem,
      Milestone,
      StageOfWork,
      TaskStatus,
    ]),
  ],
  controllers: [
    TasksController,
    ChecklistsController,
    AttachmentsController,
    CommentsController,
    MilestonesController,
    StageOfWorkController,
  ],
  providers: [
    TasksService,
    AwsService,
    ChecklistsService,
    CommentsServie,
    AttachmentsService,
    MilestonesService,
    NotificationsService,
    StageOfWorkService,
    TaskListener,
    CommentListener,
    ExpenditureListener,
    MilestoneListener,
    SubTaskListener,
    TaskSubscriber,
    ChecklistitemSubscriber,
    TaskcommentSubscriber,
    SubTaskSubscribers,
    TaskStatusSubscriber,
    PriorityChangeSubscriber,
    TaskExpenditureSubscriber,
    TaskDeleteSubscriber,
    StorageService,
    ChecklistSubscriber,
    OneDriveStorageService,
    BharathStorageService,
    BharathCloudService,
    GoogleDriveStorageService,
    PromiseService,
    GstrRegisterService,

    // Use to catch all request - did for expiry date
    // {
    //   provide: APP_INTERCEPTOR,
    //   useClass: InsertionInterceptor,
    // },
  ],
})
export class TasksModule {}
