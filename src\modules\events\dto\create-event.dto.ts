import {
  IsArray,
  IsBoolean,
  IsDateString,
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  ValidateIf,
} from 'class-validator';
import { EventType, Reminders } from '../types';

export enum EventTypeEnum {
  TASK = 'TASK',
  EVENT = 'EVENT',
  GLOBAL = 'GLOBAL',
}

export class CreateEventDto {
  @IsNotEmpty()
  title: string;

  @IsNotEmpty()
  @IsEnum(EventTypeEnum)
  type: EventTypeEnum;

  @IsOptional()
  location: string;

  @ValidateIf((o: CreateEventDto) => o.type === EventTypeEnum.TASK)
  @IsOptional()
  client: number;

  @ValidateIf((o: CreateEventDto) => o.type === EventTypeEnum.TASK)
  @IsOptional()
  clientGroup: number;

  @ValidateIf((o: CreateEventDto) => o.type === EventTypeEnum.TASK)
  @IsNotEmpty()
  @IsNumber()
  task: number;

  @ValidateIf((o: CreateEventDto) => o.type === EventTypeEnum.TASK)
  @IsNotEmpty()
  @IsArray()
  members: number[];

  @IsNotEmpty()
  @IsDateString()
  date: string;

  @IsOptional()
  @IsDateString()
  startTime: string;

  @IsOptional()
  @IsDateString()
  endTime: string;

  @IsOptional()
  // @IsEnum(Reminders)
  reminder: Reminders;

  @IsOptional()
  @IsString()
  notes: string;

  @IsOptional()
  @IsEnum(EventType)
  eventType: EventType;

  @IsOptional()
@IsBoolean()
whatsappEnabled?: boolean;

}
