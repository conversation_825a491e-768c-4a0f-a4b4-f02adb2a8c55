import {
  Connection,
  EntitySubscriberInterface,
  EventSubscriber,
  UpdateEvent,
  getManager,
} from 'typeorm';
import { User } from 'src/modules/users/entities/user.entity';
import {
  getAdminIDsBasedOnOrganizationId,
  getAllOrganizationUsersBasedOnOrganizationId,
  getAllOrganizationUsersBasedOnUserId,
  getUserDetails,
  insertINTONotificationUpdate,
} from 'src/utils/re-use';
import { sendnewMail } from 'src/emails/newemails';
import { sendWhatsAppTextMessage } from 'src/modules/whatsapp/whatsapp.service';
import ViderWhatsappSessions from 'src/modules/whatsapp/entity/viderWhatsappSessions';
import { Organization } from 'src/modules/organization/entities/organization.entity';
const moment = require('moment');

@EventSubscriber()
export class UserStatusSubscriber implements EntitySubscriberInterface<User> {
  constructor(private readonly connection: Connection) {
    connection.subscribers.push(this);
  }

  listenTo() {
    return User;
  }

  userBeforeStatus = '';
  async beforeUpdate(event: UpdateEvent<User>) {
    this.userBeforeStatus = event.databaseEntity.status;
  }

  async afterUpdate(event: UpdateEvent<User>) {
    const entityManager = getManager();
    const userName = event.entity?.fullName;
    const orgId = event.entity?.organization?.id;
    const organization = await Organization.findOne({ id: orgId });
     const isSuperAdmin = event.entity['isSuperAdmin']; //for super admin reset password 
if(!isSuperAdmin){
    const addressParts = [
      organization.buildingNo || '',
      organization.floorNumber || '',
      organization.buildingName || '',
      organization.street || '',
      organization.location || '',
      organization.city || '',
      organization.district || '',
      organization.state || ''
    ].filter(part => part && part.trim() !== '');
    const pincode = organization.pincode && organization.pincode.trim() !== '' ? ` - ${organization.pincode}` : '';

    const address = addressParts.join(', ') + pincode;
    const userStatus = event.entity?.status;
    if (this.userBeforeStatus !== userStatus) {
      const userid = event.entity['userId'];
     
      if (userid) {
        const sqlQuery = `SELECT full_name FROM user where id = ${userid};`;
        const response = await entityManager.query(sqlQuery);
        const UserName = response[0]?.full_name;
        const orgAdmins = await getAdminIDsBasedOnOrganizationId(orgId);
        const allOrgUsers = await getAllOrganizationUsersBasedOnOrganizationId(orgId);
        // const orgAdmins = getOrgAdmins.map(admin => admin.id)
        const notificationMessages = {
          DELETED: {
            key: 'DELETION_OF_USER_PUSH',
            title: 'Deletion of User',
            body: `<strong>${userName}</strong> profile has been deleted by <strong>${UserName}</strong>.`,
          },
          INACTIVE: {
            key: 'USER_STATUS_DEACTIVATED_PUSH',
            title: 'User Status Deactivated',
            body: `<strong>${userName}</strong> status has been deactivated by <strong>${UserName}</strong>.`,
          },
          ACTIVE: {
            key:
              this.userBeforeStatus === 'DELETED'
                ? 'DELETED_USER_RESTORATION_PUSH'
                : 'USER_STATUS_ACTIVATED_PUSH',

            title:
              this.userBeforeStatus === 'DELETED'
                ? 'Restoration of user'
                : 'User Status Activated',
            body:
              this.userBeforeStatus === 'DELETED'
                ? `<strong>${userName}</strong> profile has been restored by <strong>${UserName}</strong>.`
                : `<strong>${userName}</strong> status has been activated by <strong>${UserName}</strong>.`,
          },
        };
        const { title, body, key } = notificationMessages[userStatus];
        // insertINTOnotification(title, body, orgAdmins, orgId);
        insertINTONotificationUpdate(title, body, orgAdmins, orgId, key, userid);
        if (title === 'User Status Deactivated') {
          if (event?.entity?.id) {
            await sendnewMail({
              id: event?.entity?.id,
              key: 'USER_STATUS_DEACTIVATED_MAIL',
              email: event?.entity?.email,
              data: {
                userName: userName,
                deactiveUserName: userName,
                deactivatedBy: UserName,
                adress: address,
                phoneNumber: organization?.mobileNumber,
                mail: organization?.email,
                legalName: organization?.tradeName || organization?.legalName,
                date: moment(event?.entity?.updatedAt).format('DD-MM-YYYY'),
                userId: userid,
              },
              filePath: 'user-status-deactivated',
              subject: 'User Status Deactivated',
            });
          }
          // all users
          if (allOrgUsers) {
            for (let user of allOrgUsers) {
              await sendnewMail({
                id: user?.id,
                key: 'USER_STATUS_DEACTIVATED_MAIL',
                email: user?.email,
                data: {
                  userName: userName,
                  deactiveUserName: userName,
                  deactivatedBy: UserName,
                  date: moment(event?.entity?.updatedAt).format('DD-MM-YYYY'),
                  userId: userid,
                  adress: address,
                  phoneNumber: organization?.mobileNumber,
                  mail: organization?.email,
                  legalName: organization?.tradeName || organization?.legalName
                },
                filePath: 'user-status-deactivated-user',
                subject: 'User Status Deactivated',
              });
            }
            for (let user of orgAdmins) {

              try {
                if (orgAdmins !== undefined) {
                  const sessionValidation = await ViderWhatsappSessions.findOne({
                    where: { userId: user, status: 'ACTIVE' },
                  });
                  if (sessionValidation) {
                    const userDetails = await getUserDetails(user);
                    const { full_name: userFullName, mobile_number: userPhoneNumber, id, organization_id } = userDetails;
                    const key = 'USER_STATUS_DEACTIVATED_WHATSAPP';
                    const whatsappMessageBody =
                      `Hi ${userFullName}
 ${userName}  profile has been deactivated by  ${userFullName}
 Do not forget to activate the same whenever required!
              
 We hope this helps!
               `;
                    await sendWhatsAppTextMessage(
                      `91${userPhoneNumber}`,
                      whatsappMessageBody,
                      organization_id,
                      title,
                      id,
                      key,
                    );
                  }

                }
              } catch (error) {
                console.error('Error sending User WhatsApp notification:', error);
              }
            }
          }
        }

        // user Status activated
        if (title === 'User Status Activated') {
          if (event?.entity?.id) {
            await sendnewMail({
              id: event?.entity?.id,
              key: 'USER_STATUS_ACTIVATED_MAIL',
              email: event?.entity?.email,
              data: {
                userName: userName,
                activeUserName: userName,
                activatedBy: UserName,
                date: moment(event?.entity?.updatedAt).format('DD-MM-YYYY'),
                userId: userid,
                adress: address,
                phoneNumber: organization?.mobileNumber,
                mail: organization?.email,
                legalName: organization?.tradeName || organization?.legalName
              },
              filePath: 'user-status-activated',
              subject: 'User Status activated',
            });
          }
          // all users
          if (allOrgUsers) {
            for (let user of allOrgUsers) {
              await sendnewMail({
                id: user?.id,
                key: 'USER_STATUS_ACTIVATED_MAIL',
                email: user?.email,
                data: {
                  userName: userName,
                  activeUserName: userName,
                  activatedBy: UserName,
                  date: moment(event?.entity?.updatedAt).format('DD-MM-YYYY'),
                  userId: userid,
                  adress: address,
                  phoneNumber: organization?.mobileNumber,
                  mail: organization?.email,
                  legalName: organization?.tradeName || organization?.legalName
                },
                filePath: 'user-status-activated-user',
                subject: 'User Status activated',
              });
            }
            for (let user of orgAdmins) {

              try {
                if (orgAdmins !== undefined) {
                  const sessionValidation = await ViderWhatsappSessions.findOne({
                    where: { userId: user, status: 'ACTIVE' },
                  });
                  if (sessionValidation) {
                    const userDetails = await getUserDetails(user);
                    const { full_name: userFullName, mobile_number: userPhoneNumber, id, organization_id } = userDetails;
                    const key = 'USER_STATUS_ACTIVATED_WHATSAPP';
                    const whatsappMessageBody = `
 Hi ${userFullName}
 
 ${userName} profile has been activated by ${userFullName}

 We hope this helps!
 `;
                    await sendWhatsAppTextMessage(
                      `91${userPhoneNumber}`,
                      whatsappMessageBody,
                      organization_id,
                      title,
                      id,
                      key,
                    );
                  }

                }
              } catch (error) {
                console.error('Error sending User WhatsApp notification:', error);
              }
            }
          }
        }

        if (title === 'Deletion of User') {
          if (event?.entity?.id) {
            await sendnewMail({
              id: event?.entity?.id,
              key: 'USER_DELETION_MAIL',
              email: event?.entity?.email,
              data: {
                userName: userName,
                deletedBy: UserName,
                date: moment(event?.entity?.updatedAt).format('DD-MM-YYYY'),
                userId: userid,
                adress: address,
                phoneNumber: organization?.mobileNumber,
                mail: organization?.email,
                legalName: organization?.tradeName || organization?.legalName
              },
              filePath: 'user-deletion',
              subject: 'Account Update: Your Account Has Been Deleted',
            });
          }
          // all users
          if (allOrgUsers) {
            for (let user of allOrgUsers) {
              await sendnewMail({
                id: user?.id,
                key: 'USER_DELETION_MAIL',
                email: user?.email,
                data: {
                  adminName: user?.fullName,
                  userName: userName,
                  deletedBy: UserName,
                  date: moment(event?.entity?.updatedAt).format('DD-MM-YYYY'),
                  userId: userid,
                  adress: address,
                  phoneNumber: organization?.mobileNumber,
                  mail: organization?.email,
                  legalName: organization?.tradeName || organization?.legalName
                },
                filePath: 'user-deletion-user',
                subject: 'Account Update: User Account Has Been Deleted',
              });
            }
            for (let user of orgAdmins) {

              try {
                if (orgAdmins !== undefined) {
                  const sessionValidation = await ViderWhatsappSessions.findOne({
                    where: { userId: user, status: 'ACTIVE' },
                  });
                  if (sessionValidation) {
                    const userDetails = await getUserDetails(user);
                    const { full_name: userFullName, mobile_number: userPhoneNumber, id, organization_id } = userDetails;
                    const key = 'USER_DELETION_WHATSAPP';
                    const whatsappMessageBody = `
 Hi ${userFullName}
 
 ${userName} profile has been deleted by ${UserName}
 Do not forget to restore the same whenever required!

 We hope this helps!
 `;
                    await sendWhatsAppTextMessage(
                      `91${userPhoneNumber}`,
                      whatsappMessageBody,
                      organization_id,
                      title,
                      id,
                      key,
                    );
                  }

                }
              } catch (error) {
                console.error('Error sending User WhatsApp notification:', error);
              }
            }
          }
        }
        if (title === 'Deleted User Restoration') {
          if (event?.entity?.id) {
            await sendnewMail({
              id: event?.entity?.id,
              key: 'DELETED_USER_RESTORATION_MAIL',
              email: event?.entity?.email,
              data: {
                userName: userName,
                restoredBy: UserName,
                date: moment(event?.entity?.updatedAt).format('DD-MM-YYYY'),
                userId: userid,
                adress: address,
                phoneNumber: organization?.mobileNumber,
                mail: organization?.email,
                legalName: organization?.tradeName || organization?.legalName
              },
              filePath: 'user-restoration',
              subject: 'Account Update: Your Deleted Account Has Been Restored',
            });
          }

          // all users
          if (allOrgUsers) {
            for (let user of allOrgUsers) {
              await sendnewMail({
                id: user?.id,
                key: 'DELETED_USER_RESTORATION_MAIL',
                email: user?.email,
                data: {
                  adminName: user?.fullName,
                  userName: userName,
                  restoredBy: UserName,
                  date: moment(event?.entity?.updatedAt).format('DD-MM-YYYY'),
                  userId: userid,
                  adress: address,
                  phoneNumber: organization?.mobileNumber,
                  mail: organization?.email,
                  legalName: organization?.tradeName || organization?.legalName
                },
                filePath: 'user-restoration-user',
                subject: 'Account Update: Your Deleted Account Has Been Restored',
              });
            }
            for (let user of orgAdmins) {

              try {
                if (orgAdmins !== undefined) {
                  const sessionValidation = await ViderWhatsappSessions.findOne({
                    where: { userId: user, status: 'ACTIVE' },
                  });
                  if (sessionValidation) {
                    const userDetails = await getUserDetails(user);
                    const { full_name: userFullName, mobile_number: userPhoneNumber, id, organization_id } = userDetails;
                    const key = 'DELETED_USER_RESTORATION_WHATSAPP';
                    const whatsappMessageBody = `
 Hi ${userFullName}
 
 ${userName} profile has been restored by ${UserName}

 We hope this helps!
 `;
                    await sendWhatsAppTextMessage(
                      `91${userPhoneNumber}`,
                      whatsappMessageBody,
                      organization_id,
                      title,
                      id,
                      key,
                    );
                  }

                }
              } catch (error) {
                console.error('Error sending User WhatsApp notification:', error);
              }
            }
          }

        }
      }
    }
  }
  }
}
