import {
  ArrayMinSize,
  IsArray,
  IsDateString,
  IsEnum,
  IsNotEmpty,
  IsOptional,
} from 'class-validator';
import { LogHourType } from 'src/modules/log-hours/log-hour.entity';
import { BillableType } from './get-employee-log-hours-report';

class getLogHoursDto {
  @IsNotEmpty()
  @IsArray()
  @ArrayMinSize(1)
  users: Array<number>;

  @IsOptional()
  @IsEnum(LogHourType)
  type: LogHourType;

  @IsOptional()
  @IsDateString()
  fromDate: string;

  @IsOptional()
  @IsDateString()
  toDate: string;

  @IsNotEmpty()
  @IsArray()
  @ArrayMinSize(1)
  clients: Array<any>;

  @IsOptional()
  @IsEnum(BillableType)
  billableType: BillableType;

  @IsOptional()
  category: Array<number>;

  @IsOptional()
  subCategory: Array<number>;
}

export default getLogHoursDto;
