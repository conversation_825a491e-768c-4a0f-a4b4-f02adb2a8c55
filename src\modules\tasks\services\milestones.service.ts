import { Injectable } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { Event_Actions } from 'src/event-listeners/actions';
import AddMileStoneDto from '../dto/add-milestone.dto';
import Milestone from '../entity/milestone.entity';
import Task from '../entity/task.entity';

@Injectable()
export class MilestonesService {
  constructor(private eventEmitter: EventEmitter2) {}

  async addMileStone(taskId: number, data: AddMileStoneDto) {
    let task = await Task.findOne(taskId);
    let milestone = new Milestone();
    milestone.name = data.name;
    milestone.description = data.description;
    milestone.referenceNumber = data.referenceNumber;
    milestone.task = task;
    await milestone.save();

    this.eventEmitter.emit(Event_Actions.MILESTONE_CREATED, {
      task,
    });

    return milestone;
  }

  async updateMilestone(id: number, data: AddMileStoneDto) {
    let milestone = await Milestone.findOne({
      where: {
        id,
      },
      relations: ['task'],
    });
    milestone.name = data.name;
    milestone.description = data.description;
    milestone.referenceNumber = data.referenceNumber;
    milestone.referenceNumberValue = data.referenceNumberValue;
    milestone.status = data.status;
    await milestone.save();

    this.eventEmitter.emit(Event_Actions.MILESTONE_UPDATED, {
      task: milestone.task,
    });

    return milestone;
  }

  async getMilestones(id: number) {
    let milestones = await Milestone.find({
      where: {
        task: {
          id,
        },
      },
    });

    return milestones;
  }

  async deleteMilestone(id: number) {
    let milestone = await Milestone.findOne({
      where: {
        id,
      },
      relations: ['task'],
    });
    await milestone.remove();

    this.eventEmitter.emit(Event_Actions.MILESTONE_DELETED, {
      task: milestone.task,
    });

    return milestone;
  }
}
