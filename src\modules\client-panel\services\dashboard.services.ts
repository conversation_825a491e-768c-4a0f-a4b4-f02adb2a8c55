import { Injectable } from '@nestjs/common';
import { User } from 'src/modules/users/entities/user.entity';
import { createQ<PERSON><PERSON><PERSON><PERSON><PERSON>, getManager, getRepository, Like } from 'typeorm';
import Client from 'src/modules/clients/entity/client.entity';
import * as moment from 'moment';
import AutClientCredentials from 'src/modules/automation/entities/aut_client_credentials.entity';
import AutOutstandingDemand from 'src/modules/automation/entities/aut-outstanding-demand.entity';
import AutEProceedingFya from 'src/modules/automation/entities/aut_income_tax_eproceedings_fya.entity';
import AutFyaNotice from 'src/modules/automation/entities/aut_income_tax_eproceedings_fya_notice.entity';
import AutEProceedingFyi from 'src/modules/automation/entities/aut_income_tax_eproceedings_fyi.entity';
import AutFyiNotice from 'src/modules/automation/entities/aut_income_tax_eproceedings_fyi_notice.entity';
import AutoIncomeTaxForms from 'src/modules/automation/entities/aut_income_tax_forms.entity';
import AutIncometaxReturns from 'src/modules/automation/entities/aut_incometax_returns.entity';
import AutomationMachines from 'src/modules/automation/entities/automation_machines.entity';

@Injectable()
export class AutDashboardService {
  async findCount(userId: number, query: any) {
    const user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
    const organizationId = user?.organization?.id;
    if (organizationId) {
      const clientCredential = await AutClientCredentials.count({
        where: { organizationId },
      });
      let formsCount: any;
      let returnsCount: any;
      let eProceedingFyaCount: any;
      let eProceedingFyiCount: any;
      let outstandingDemandCount: any;
      if (query?.assessmentYear !== '') {
        formsCount = await AutoIncomeTaxForms.count({
          where: { organizationId, refYear: query?.assessmentYear?.substring(0, 4) },
        });
        returnsCount = await AutIncometaxReturns.count({
          where: { organizationId, assmentYear: query?.assessmentYear?.substring(0, 4) },
        });
        outstandingDemandCount = await AutOutstandingDemand.count({
          where: { organizationId, assessmentYear: query?.assessmentYear?.substring(0, 4) },
        });
        eProceedingFyaCount = await AutEProceedingFya.count({
          where: { organizationId, assesmentYear: query?.assessmentYear?.substring(0, 4) },
        });
        eProceedingFyiCount = await AutEProceedingFyi.count({
          where: { organizationId, assessmentYear: query?.assessmentYear?.substring(0, 4) },
        });
      } else {
        formsCount = await AutoIncomeTaxForms.count({ where: { organizationId } });
        returnsCount = await AutIncometaxReturns.count({ where: { organizationId } });
        eProceedingFyaCount = await AutEProceedingFya.count({ where: { organizationId } });
        eProceedingFyiCount = await AutEProceedingFyi.count({ where: { organizationId } });
        outstandingDemandCount = await AutOutstandingDemand.count({
          where: { organizationId },
        });
      }

      return {
        clientCredentialCount: clientCredential,
        formsCount,
        returnsCount,
        eProceedingFyaCount,
        eProceedingFyiCount,
        outstandingDemandCount,
      };
    }
  }

  async statusWiseReturnsCount(userId: number, query: any) {
    const user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
    const organizationId = user?.organization?.id;
    if (organizationId) {
      let sql = `
    SELECT
      SUM(CASE WHEN filing_type_cd = 'O' THEN 1 ELSE 0 END) AS original,
      SUM(CASE WHEN filing_type_cd = 'D' THEN 1 ELSE 0 END) AS defective,
      SUM(CASE WHEN filing_type_cd = 'R' THEN 1 ELSE 0 END) AS revised,
      SUM(CASE WHEN filing_type_cd = 'T' THEN 1 ELSE 0 END) AS rectification,
      SUM(CASE WHEN filing_type_cd = 'U' THEN 1 ELSE 0 END) AS updated
    FROM 
      aut_incometax_returns
    WHERE
      organization_id = ${organizationId}
  `;

      if (query.assessmentYear && query.assessmentYear !== '') {
        sql += ` AND assment_year = '${query.assessmentYear}'`;
      }
      const returnsCount = await AutIncometaxReturns.count({ where: { organizationId } });
      const result = await getManager().query(sql);
      const statusObj = result[0];
      statusObj['returnsCount'] = returnsCount;
      return statusObj;
    }
  }

  async typeWiseReturnsCount(userId: number, query: any) {
    const user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
    const organizationId = user?.organization?.id;
    if (organizationId) {
      let sql = `
    SELECT
      SUM(CASE WHEN formtype_cd = '1' THEN 1 ELSE 0 END) AS ITR1,
      SUM(CASE WHEN formtype_cd = '2' THEN 1 ELSE 0 END) AS ITR2,
      SUM(CASE WHEN formtype_cd = '2A' THEN 1 ELSE 0 END) AS ITR2A,
      SUM(CASE WHEN formtype_cd = '3' THEN 1 ELSE 0 END) AS ITR3,
      SUM(CASE WHEN formtype_cd = '4' THEN 1 ELSE 0 END) AS ITR4,
      SUM(CASE WHEN formtype_cd = '4S' THEN 1 ELSE 0 END) AS ITR4S,
      SUM(CASE WHEN formtype_cd = '5' THEN 1 ELSE 0 END) AS ITR5,
      SUM(CASE WHEN formtype_cd = '6' THEN 1 ELSE 0 END) AS ITR6,
       SUM(CASE WHEN formtype_cd = '7' THEN 1 ELSE 0 END) AS ITR7
    FROM 
      aut_incometax_returns
    WHERE
      organization_id = ${organizationId}
  `;

      if (query.assessmentYear && query.assessmentYear !== '') {
        sql += ` AND assment_year = '${query.assessmentYear}'`;
      }

      const result = await getManager().query(sql);
      return result[0];
    }
  }

  async verificationWiseReturnsCount(userId: number, query: any) {
    const user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
    const organizationId = user?.organization.id;
    if (organizationId) {
      let sql = `
      SELECT 
        SUM(CASE WHEN ver_status = 'Y' THEN 1 ELSE 0 END) AS verified,
        SUM(CASE WHEN ver_status = 'N' THEN 1 ELSE 0 END) AS notVerified,
        SUM(CASE WHEN ver_status = 'X' THEN 1 ELSE 0 END) AS none
      FROM 
        aut_incometax_returns
      WHERE 
        organization_id = ${organizationId}
      `;

      if (query.assessmentYear && query.assessmentYear !== '') {
        sql += ` AND assment_year = '${query.assessmentYear}'`;
      }

      const result = await getManager().query(sql);
      return result[0];
    }
  }

  async getDemandRaisedDates(
    userId: number,
    interval: '1week' | '15days' | '1month' | '1year',
  ): Promise<number> {
    try {
      const user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
      const organizationId = user?.organization?.id;

      let intervalQuery = '';
      switch (interval) {
        case '1week':
          intervalQuery = 'INTERVAL 1 WEEK';
          break;
        case '15days':
          intervalQuery = 'INTERVAL 15 DAY';
          break;
        case '1month':
          intervalQuery = 'INTERVAL 1 MONTH';
          break;
        case '1year':
          intervalQuery = 'INTERVAL 1 YEAR';
          break;
        default:
          throw new Error('Invalid interval');
      }

      const sql = `
        SELECT COUNT(*) AS count
        FROM aut_outstanding_demand
        WHERE demand_raised_date >= DATE_SUB(CURDATE(), ${intervalQuery})
        AND organization_id = ${organizationId};
      `;

      const result = await getManager().query(sql);
      return parseInt(result[0].count);
    } catch (error) {
      console.error('Error fetching demand raised filter dates:', error);
      throw error;
    }
  }

  async getNoticeDates(
    organizationId: number,
    interval: '1week' | '15days' | '1month' | '1year',
    dateColumn: 'issued_on' | 'response_due_date' | 'remark_submitted_on',
    query: any,
  ): Promise<number> {
    try {
      if (!organizationId) {
        throw new Error('Organization not found for the user.');
      }

      let intervalQuery = '';
      switch (interval) {
        case '1week':
          intervalQuery = 'INTERVAL 1 WEEK';
          break;
        case '15days':
          intervalQuery = 'INTERVAL 15 DAY';
          break;
        case '1month':
          intervalQuery = 'INTERVAL 1 MONTH';
          break;
        case '1year':
          intervalQuery = 'INTERVAL 1 YEAR';
          break;
        default:
          throw new Error('Invalid interval');
      }

      let additionalCondition = '';
      let joinClause = '';
      if (dateColumn === 'response_due_date') {
        additionalCondition = 'AND remark_submitted_on IS NOT NULL';
      }

      if (dateColumn === 'remark_submitted_on') {
        joinClause = `
          LEFT JOIN aut_proceeding_response_fya AS r ON aut_fya_notice.id = r.fya_notice_id
        `;
        additionalCondition = `
          AND r.submitted_on >= DATE_SUB(CURDATE(), ${intervalQuery})
        `;
      } else {
        additionalCondition = `
          AND ${dateColumn} >= DATE_SUB(CURDATE(), ${intervalQuery})
        `;
      }

      let sql = `
      SELECT COUNT(*) AS count
      FROM aut_fya_notice
      ${joinClause}
      WHERE aut_fya_notice.organization_id = ${organizationId}
      ${additionalCondition}
    `;
      if (query.assessmentYear && query.assessmentYear !== '') {
        sql += `AND assesment_year = '${query.assessmentYear}'`;
      }

      const result = await getManager().query(sql);
      return parseInt(result[0].count);
    } catch (error) {
      console.error(`Error fetching ${dateColumn} dates:`, error);
      throw error;
    }
  }

  async getNoticeFyaResponseDueDates(
    organizationId: number,
    interval: '1week' | '15days' | '1month' | '1year',
    dateColumn: 'issued_on' | 'response_due_date' | 'remark_submitted_on',
    query: any,
  ): Promise<number> {
    try {
      if (!organizationId) {
        throw new Error('Organization not found for the user.');
      }

      let intervalQuery = '';
      switch (interval) {
        case '1week':
          intervalQuery = 'INTERVAL 1 WEEK';
          break;
        case '15days':
          intervalQuery = 'INTERVAL 15 DAY';
          break;
        case '1month':
          intervalQuery = 'INTERVAL 1 MONTH';
          break;
        case '1year':
          intervalQuery = 'INTERVAL 1 YEAR';
          break;
        default:
          throw new Error('Invalid interval');
      }

      let additionalCondition = '';
      if (dateColumn === 'response_due_date') {
        additionalCondition = 'AND remark_submitted_on IS NULL';
      }

      let sql = `
      SELECT count(*) as count
      FROM aut_fya_notice
      WHERE ${dateColumn} BETWEEN CURDATE() AND DATE_ADD(CURDATE(), ${intervalQuery})
      AND organization_id = ${organizationId}
      ${additionalCondition}
    `;

      if (query.assessmentYear && query.assessmentYear !== '') {
        sql += ` AND assesment_year = '${query.assessmentYear}'`;
      }

      const result = await getManager().query(sql);
      return parseInt(result[0]?.count, 10);
    } catch (error) {
      console.error(`Error fetching ${dateColumn} dates:`, error);
      throw error;
    }
  }

  async getNoticeFyiDates(
    organizationId: number,
    interval: '1week' | '15days' | '1month' | '1year',
    dateColumn: 'issued_on' | 'response_due_date' | 'remark_submitted_on',
    query: any,
  ): Promise<number> {
    try {
      if (!organizationId) {
        throw new Error('Organization not found for the user.');
      }

      let intervalQuery = '';
      switch (interval) {
        case '1week':
          intervalQuery = 'INTERVAL 1 WEEK';
          break;
        case '15days':
          intervalQuery = 'INTERVAL 15 DAY';
          break;
        case '1month':
          intervalQuery = 'INTERVAL 1 MONTH';
          break;
        case '1year':
          intervalQuery = 'INTERVAL 1 YEAR';
          break;
        default:
          throw new Error('Invalid interval');
      }

      let additionalCondition = '';
      let joinClause = '';
      if (dateColumn === 'response_due_date') {
        additionalCondition = 'AND remark_submitted_on IS NOT NULL';
      }

      if (dateColumn === 'remark_submitted_on') {
        joinClause = `
          LEFT JOIN aut_proceeding_response_fyi AS r ON aut_fyi_notice.id = r.fyi_notice_id
        `;
        additionalCondition = `
          AND r.submitted_on >= DATE_SUB(CURDATE(), ${intervalQuery})
        `;
      } else {
        additionalCondition = `
          AND ${dateColumn} >= DATE_SUB(CURDATE(), ${intervalQuery})
        `;
      }

      let sql = `
      SELECT COUNT(*) AS count
      FROM aut_fyi_notice
      ${joinClause}
      WHERE aut_fyi_notice.organization_id = ${organizationId}
      ${additionalCondition}
    `;

      if (query?.assessmentYear && query?.assessmentYear !== '') {
        sql += ` AND assessment_year = '${query.assessmentYear}'`;
      }

      const result = await getManager().query(sql);
      return parseInt(result[0]?.count);
    } catch (error) {
      console.error(`Error fetching ${dateColumn} dates:`, error);
      throw error;
    }
  }

  async getNoticeFyiResponseDueDates(
    organizationId: number,
    interval: '1week' | '15days' | '1month' | '1year',
    dateColumn: 'issued_on' | 'response_due_date' | 'remark_submitted_on',
    query: any,
  ): Promise<number> {
    try {
      if (!organizationId) {
        throw new Error('Organization not found for the user.');
      }

      let intervalQuery = '';
      switch (interval) {
        case '1week':
          intervalQuery = 'INTERVAL 1 WEEK';
          break;
        case '15days':
          intervalQuery = 'INTERVAL 15 DAY';
          break;
        case '1month':
          intervalQuery = 'INTERVAL 1 MONTH';
          break;
        case '1year':
          intervalQuery = 'INTERVAL 1 YEAR';
          break;
        default:
          throw new Error('Invalid interval');
      }

      let additionalCondition = '';
      if (dateColumn === 'response_due_date') {
        additionalCondition = 'AND remark_submitted_on IS NULL';
      }

      let sql = `
      SELECT COUNT(*) AS count
      FROM aut_fyi_notice
      WHERE ${dateColumn} BETWEEN CURDATE() AND DATE_ADD(CURDATE(), ${intervalQuery})
      AND organization_id = ${organizationId}
      ${additionalCondition}
    `;

      if (query?.assessmentYear && query?.assessmentYear !== '') {
        sql += `AND assessment_year = '${query?.assessmentYear}'`;
      }
      const result = await getManager().query(sql);
      return parseInt(result[0]?.count, 10);
    } catch (error) {
      console.error(`Error fetching ${dateColumn} dates:`, error);
      throw error;
    }
  }

  async getCombinedNoticesCount(userId: number, query: any) {
    const user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
    const organizationId = user?.organization?.id;
    if (organizationId) {
      // FYA Notices
      const fyaLast1WeekIssued = await this.getNoticeDates(
        organizationId,
        '1week',
        'issued_on',
        query,
      );
      const fyaLast15DaysIssued = await this.getNoticeDates(
        organizationId,
        '15days',
        'issued_on',
        query,
      );
      const fyaLast1MonthIssued = await this.getNoticeDates(
        organizationId,
        '1month',
        'issued_on',
        query,
      );

      const fyaLast1WeekDue = await this.getNoticeFyaResponseDueDates(
        organizationId,
        '1week',
        'response_due_date',
        query,
      );
      const fyaLast15DaysDue = await this.getNoticeFyaResponseDueDates(
        organizationId,
        '15days',
        'response_due_date',
        query,
      );
      const fyaLast1MonthDue = await this.getNoticeFyaResponseDueDates(
        organizationId,
        '1month',
        'response_due_date',
        query,
      );

      const fyaLast1WeekSubmit = await this.getNoticeDates(
        organizationId,
        '1week',
        'remark_submitted_on',
        query,
      );
      const fyaLast15DaysSubmit = await this.getNoticeDates(
        organizationId,
        '15days',
        'remark_submitted_on',
        query,
      );
      const fyaLast1MonthSubmit = await this.getNoticeDates(
        organizationId,
        '1month',
        'remark_submitted_on',
        query,
      );

      // FYI Notices
      const fyiLast1WeekIssued = await this.getNoticeFyiDates(
        organizationId,
        '1week',
        'issued_on',
        query,
      );
      const fyiLast15DaysIssued = await this.getNoticeFyiDates(
        organizationId,
        '15days',
        'issued_on',
        query,
      );
      const fyiLast1MonthIssued = await this.getNoticeFyiDates(
        organizationId,
        '1month',
        'issued_on',
        query,
      );

      const fyiLast1WeekDue = await this.getNoticeFyiResponseDueDates(
        organizationId,
        '1week',
        'response_due_date',
        query,
      );
      const fyiLast15DaysDue = await this.getNoticeFyiResponseDueDates(
        organizationId,
        '15days',
        'response_due_date',
        query,
      );
      const fyiLast1MonthDue = await this.getNoticeFyiResponseDueDates(
        organizationId,
        '1month',
        'response_due_date',
        query,
      );

      const fyiLast1WeekSubmit = await this.getNoticeFyiDates(
        organizationId,
        '1week',
        'remark_submitted_on',
        query,
      );
      const fyiLast15DaysSubmit = await this.getNoticeFyiDates(
        organizationId,
        '15days',
        'remark_submitted_on',
        query,
      );
      const fyiLast1MonthSubmit = await this.getNoticeFyiDates(
        organizationId,
        '1month',
        'remark_submitted_on',
        query,
      );

      return {
        issueData: {
          last1WeekIssued: fyaLast1WeekIssued + fyiLast1WeekIssued,
          last15DaysIssued: fyaLast15DaysIssued + fyiLast15DaysIssued,
          last1MonthIssued: fyaLast1MonthIssued + fyiLast1MonthIssued,
        },
        responseDueData: {
          last1WeekDue: fyaLast1WeekDue + fyiLast1WeekDue,
          last15DaysDue: fyaLast15DaysDue + fyiLast15DaysDue,
          last1MonthDue: fyaLast1MonthDue + fyiLast1MonthDue,
        },
        submitData: {
          last1WeekSubmit: fyaLast1WeekSubmit + fyiLast1WeekSubmit,
          last15DaysSubmit: fyaLast15DaysSubmit + fyiLast15DaysSubmit,
          last1MonthSubmit: fyaLast1MonthSubmit + fyiLast1MonthSubmit,
        },
      };
    } else {
      return {
        issueData: {
          last1WeekIssued: 0,
          last15DaysIssued: 0,
          last1MonthIssued: 0,
        },
        responseDueData: {
          last1WeekDue: 0,
          last15DaysDue: 0,
          last1MonthDue: 0,
        },
        submitData: {
          last1WeekSubmit: 0,
          last15DaysSubmit: 0,
          last1MonthSubmit: 0,
        },
      };
    }
  }

  async demandRaisedfilterDates(userId: number) {
    try {
      const last1Week = await this.getDemandRaisedDates(userId, '1week');
      const last15Days = await this.getDemandRaisedDates(userId, '15days');
      const last1Month = await this.getDemandRaisedDates(userId, '1month');
      const last1Year = await this.getDemandRaisedDates(userId, '1year');

      return {
        last1Week,
        last15Days,
        last1Month,
        last1Year,
      };
    } catch (error) {
      console.error('Error fetching demand raised filter dates:', error);
      throw error;
    }
  }

  async incometaxClientCheck(userId) {
    const user = await User.findOne({
      where: { id: userId },
      relations: ['organization'],
    });
    const entityManager = getRepository(AutomationMachines);

    // Directly use a raw SQL query to get the desired result
    const query = await entityManager
      .createQueryBuilder('automationMachines')
      .leftJoinAndSelect('automationMachines.autoCredentials', 'autoCredentials')
      .leftJoinAndSelect('autoCredentials.client', 'client')
      .where('autoCredentials.organizationId = :id', { id: user.organization.id })
      .andWhere(
        "LOWER(automationMachines.remarks) NOT LIKE :remarks", 
        { remarks: 'success%' }
      )
      .andWhere((qb) => {
        const subQuery = qb
          .subQuery()
          .select('MAX(innerAutomationMachines.id)', 'maxId')
          .from(AutomationMachines, 'innerAutomationMachines')
          .leftJoin('innerAutomationMachines.autoCredentials', 'innerAutoCredentials')
          .where('innerAutoCredentials.organizationId = :id', { id: user.organization.id })
          .groupBy('innerAutoCredentials.id')
          .getQuery();
        return 'automationMachines.id IN ' + subQuery;
      })
      .getMany();
    const filteredRows = query.filter((item) => !item.remarks?.toLowerCase().startsWith('success'));

    const totalClients = await AutClientCredentials.count({
      where: { organizationId: user.organization.id },
    });

    // const uniquePansCount = await Client.count({ where: { organization: user.organization } });

    const client = await createQueryBuilder(Client, 'client')
      .select('COUNT(DISTINCT client.pan_number)', 'count')
      .where('client.organization_id = :orgId', { orgId: user.organization?.id })
      .andWhere('client.pan_number IS NOT NULL')
      .getRawOne();

    const count = parseInt(client.count);
    const result = {
      filteredRows,
      totalClients,
      count: filteredRows.length,
      uniquePansCount: count,
    };
    return result;
  }

  async getFyaEvents(userId, query: Date) {
    let user = await User.findOne(userId, { relations: ['organization'] });
    let fyaNotice = createQueryBuilder(AutFyaNotice, 'fyaNotice');
    if (query) {
      const startOfMonth = moment(`${query}`).startOf('month').format('YYYY-MM-DD');
      const endOfMonth = moment(query).endOf('month').format('YYYY-MM-DD');
      fyaNotice
        .where(`Date(fyaNotice.issuedOn) BETWEEN '${startOfMonth}' AND '${endOfMonth}'`)
        .andWhere('fyaNotice.organizationId = :organizationId', {
          organizationId: user?.organization?.id,
        });
    }

    let fyiNotice = createQueryBuilder(AutFyiNotice, 'fyiNotice');
    if (query) {
      const startOfMonth = moment(`${query}`).startOf('month').format('YYYY-MM-DD');
      const endOfMonth = moment(query).endOf('month').format('YYYY-MM-DD');
      fyiNotice
        .where(`Date(fyiNotice.issuedOn) BETWEEN '${startOfMonth}' AND '${endOfMonth}'`)
        .andWhere('fyiNotice.organizationId = :organizationId', {
          organizationId: user?.organization?.id,
        });
    }
    const result = await fyaNotice.getMany();
    const result2 = await fyiNotice.getMany();

    const fyaNotices = result.map((notice) => ({ ...notice, type: 'FYA' }));
    const fyiNotices = result2.map((notice) => ({ ...notice, type: 'FYI' }));
    const result1and2 = [...fyaNotices, ...fyiNotices];
    return result1and2;
  }

  async getResponseDueEvents(userId, query: Date) {
    let user = await User.findOne(userId, { relations: ['organization'] });
    let fyaNotice = createQueryBuilder(AutFyaNotice, 'fyaNotice');
    if (query) {
      const startOfMonth = moment(`${query}`).startOf('month').format('YYYY-MM-DD');
      const endOfMonth = moment(query).endOf('month').format('YYYY-MM-DD');
      fyaNotice
        .where(`Date(fyaNotice.responseDueDate) BETWEEN '${startOfMonth}' AND '${endOfMonth}'`)
        .andWhere('fyaNotice.organizationId = :organizationId', {
          organizationId: user?.organization?.id,
        });
    }

    let fyiNotice = createQueryBuilder(AutFyiNotice, 'fyiNotice');
    if (query) {
      const startOfMonth = moment(`${query}`).startOf('month').format('YYYY-MM-DD');
      const endOfMonth = moment(query).endOf('month').format('YYYY-MM-DD');
      fyiNotice
        .where(`Date(fyiNotice.responseDueDate) BETWEEN '${startOfMonth}' AND '${endOfMonth}'`)
        .andWhere('fyiNotice.organizationId = :organizationId', {
          organizationId: user?.organization?.id,
        })
        .andWhere('fyiNotice.remarkSubmittedOn IS NUll');
    }
    const result = await fyaNotice.getMany();
    const result2 = await fyiNotice.getMany();
    const fyaNotices = result.map((notice) => ({ ...notice, type: 'FYA' }));
    const fyiNotices = result2.map((notice) => ({ ...notice, type: 'FYI' }));
    const result1and2 = [...fyaNotices, ...fyiNotices];

    return result1and2;
  }

  async getNoticeAndOrdersIssueDates(
    organizationId: number,
    interval: '1week' | '15days' | '1month' | '1year',
    dateColumn: 'date_of_issuance',
    query: any,
  ): Promise<number> {
    try {
      if (!organizationId) {
        throw new Error('Organization not found for the user.');
      }

      let intervalQuery = '';
      switch (interval) {
        case '1week':
          intervalQuery = 'INTERVAL 1 WEEK';
          break;
        case '15days':
          intervalQuery = 'INTERVAL 15 DAY';
          break;
        case '1month':
          intervalQuery = 'INTERVAL 1 MONTH';
          break;
        case '1year':
          intervalQuery = 'INTERVAL 1 YEAR';
          break;
        default:
          throw new Error('Invalid interval');
      }

      let sql = `
        SELECT COUNT(*) AS count
        FROM gstr_notice_orders
        WHERE STR_TO_DATE(${dateColumn}, '%d/%m/%Y') >= DATE_SUB(CURDATE(), ${intervalQuery})        
        AND organization_id = ${organizationId}
      `;
      // if (query.assessmentYear && query.assessmentYear !== '') {
      //   sql += `AND assesment_year = '${query.assessmentYear}'`;
      // }

      const result = await getManager().query(sql);
      return parseInt(result[0].count);
    } catch (error) {
      console.error(`Error fetching ${dateColumn} dates:`, error);
      throw error;
    }
  }

  async getAddNoticeAndOrderIssueDates(
    organizationId: number,
    interval: '1week' | '15days' | '1month' | '1year',
    dateColumn: 'date_of_issuance',
    query: any,
  ): Promise<number> {
    try {
      if (!organizationId) {
        throw new Error('Organization not found for the user.');
      }

      let intervalQuery = '';
      switch (interval) {
        case '1week':
          intervalQuery = 'INTERVAL 1 WEEK';
          break;
        case '15days':
          intervalQuery = 'INTERVAL 15 DAY';
          break;
        case '1month':
          intervalQuery = 'INTERVAL 1 MONTH';
          break;
        case '1year':
          intervalQuery = 'INTERVAL 1 YEAR';
          break;
        default:
          throw new Error('Invalid interval');
      }

      let sql = `
        SELECT COUNT(*) AS count
        FROM gstr_additional_notice_orders
        WHERE ${dateColumn} >= DATE_SUB(CURDATE(), ${intervalQuery})
        AND organization_id = ${organizationId}
      `;

      if (query?.assessmentYear && query?.assessmentYear !== '') {
        sql += ` AND fy = '${query.assessmentYear}'`;
      }

      const result = await getManager().query(sql);
      return parseInt(result[0]?.count);
    } catch (error) {
      console.error(`Error fetching ${dateColumn} dates:`, error);
      throw error;
    }
  }

  async getNoticeAndOrderDueDates(
    organizationId: number,
    interval: '1week' | '15days' | '1month' | '1year',
    dateColumn: 'due_date',
    query: any,
  ): Promise<number> {
    try {
      if (!organizationId) {
        throw new Error('Organization not found for the user.');
      }

      let intervalQuery = '';
      switch (interval) {
        case '1week':
          intervalQuery = 'INTERVAL 1 WEEK';
          break;
        case '15days':
          intervalQuery = 'INTERVAL 15 DAY';
          break;
        case '1month':
          intervalQuery = 'INTERVAL 1 MONTH';
          break;
        case '1year':
          intervalQuery = 'INTERVAL 1 YEAR';
          break;
        default:
          throw new Error('Invalid interval');
      }

      let sql = `
      SELECT COUNT(*) AS count
      FROM gstr_notice_orders
      WHERE STR_TO_DATE(${dateColumn},'%d/%m/%Y') BETWEEN CURDATE() AND DATE_ADD(CURDATE(), ${intervalQuery})
      AND organization_id = ${organizationId}
    `;

      // if (query?.assessmentYear && query?.assessmentYear !== '') {
      //   sql += `AND fy = '${query?.assessmentYear}'`;
      // }
      const result = await getManager().query(sql);
      return parseInt(result[0]?.count, 10);
    } catch (error) {
      console.error(`Error fetching ${dateColumn} dates:`, error);
      throw error;
    }
  }
  async getAddNoticeAndOrderDueDates(
    organizationId: number,
    interval: '1week' | '15days' | '1month' | '1year',
    dateColumn: 'due_date',
    query: any,
  ): Promise<number> {
    try {
      if (!organizationId) {
        throw new Error('Organization not found for the user.');
      }

      let intervalQuery = '';
      switch (interval) {
        case '1week':
          intervalQuery = 'INTERVAL 1 WEEK';
          break;
        case '15days':
          intervalQuery = 'INTERVAL 15 DAY';
          break;
        case '1month':
          intervalQuery = 'INTERVAL 1 MONTH';
          break;
        case '1year':
          intervalQuery = 'INTERVAL 1 YEAR';
          break;
        default:
          throw new Error('Invalid interval');
      }

      let sql = `
      SELECT COUNT(*) AS count
      FROM gstr_additional_notice_orders
      WHERE STR_TO_DATE(${dateColumn},'%d/%m/%Y') BETWEEN CURDATE() AND DATE_ADD(CURDATE(), ${intervalQuery})
      AND organization_id = ${organizationId}
    `;

      if (query?.assessmentYear && query?.assessmentYear !== '') {
        sql += `AND fy = '${query?.assessmentYear}'`;
      }
      const result = await getManager().query(sql);
      return parseInt(result[0]?.count, 10);
    } catch (error) {
      console.error(`Error fetching ${dateColumn} dates:`, error);
      throw error;
    }
  }

  async getNoticeOrdersDateCount(userId: number, query: any) {
    const user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
    const organizationId = user?.organization?.id;
    if (organizationId) {
      // FYA Notices
      const last1WeekIssueDate = await this.getNoticeAndOrdersIssueDates(
        organizationId,
        '1week',
        'date_of_issuance',
        query,
      );
      const last15DaysIssueDate = await this.getNoticeAndOrdersIssueDates(
        organizationId,
        '15days',
        'date_of_issuance',
        query,
      );
      const last1MonthIssueDate = await this.getNoticeAndOrdersIssueDates(
        organizationId,
        '1month',
        'date_of_issuance',
        query,
      );

      // FYI Notices
      const nxt1WeekDueDate = await this.getNoticeAndOrderDueDates(
        organizationId,
        '1week',
        'due_date',
        query,
      );
      const nxt15DaysDueDate = await this.getNoticeAndOrderDueDates(
        organizationId,
        '15days',
        'due_date',
        query,
      );
      const nxt1MonthDueDate = await this.getNoticeAndOrderDueDates(
        organizationId,
        '1month',
        'due_date',
        query,
      );

      return {
        issuanceDate: {
          last1Week: last1WeekIssueDate,
          last15Days: last15DaysIssueDate,
          last1Month: last1MonthIssueDate,
        },
        dueDate: {
          nxt1Week: nxt1WeekDueDate,
          nxt15Days: nxt15DaysDueDate,
          nxt1Month: nxt1MonthDueDate,
        },
      };
    } else {
      return {
        issuanceDate: {
          last1Week: 0,
          last15Days: 0,
          last1Month: 0,
        },
        dueDate: {
          nxt1Week: 0,
          nxt15Days: 0,
          nxt1Month: 0,
        },
      };
    }
  }

  async getAdditionalNoticeOrdersDateCount(userId: number, query: any) {
    const user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
    const organizationId = user?.organization?.id;
    if (organizationId) {
      const last1WeekIssueDate = await this.getAddNoticeAndOrderIssueDates(
        organizationId,
        '1week',
        'date_of_issuance',
        query,
      );
      const last15DaysIssueDate = await this.getAddNoticeAndOrderIssueDates(
        organizationId,
        '15days',
        'date_of_issuance',
        query,
      );
      const last1MonthIssueDate = await this.getAddNoticeAndOrderIssueDates(
        organizationId,
        '1month',
        'date_of_issuance',
        query,
      );
      const nxt1WeekDueDate = await this.getAddNoticeAndOrderDueDates(
        organizationId,
        '1week',
        'due_date',
        query,
      );
      const nxt15DaysDueDate = await this.getAddNoticeAndOrderDueDates(
        organizationId,
        '15days',
        'due_date',
        query,
      );
      const nxt1MonthDueDate = await this.getAddNoticeAndOrderDueDates(
        organizationId,
        '1month',
        'due_date',
        query,
      );

      return {
        issuanceDate: {
          last1Week: last1WeekIssueDate,
          last15Days: last15DaysIssueDate,
          last1Month: last1MonthIssueDate,
        },
        dueDate: {
          nxt1WeekDue: nxt1WeekDueDate,
          nxt15DaysDue: nxt15DaysDueDate,
          nxt1MonthDue: nxt1MonthDueDate,
        },
      };
    } else {
      return {
        issuanceDate: {
          last1Week: 0,
          last15Days: 0,
          last1Month: 0,
        },
        dueDate: {
          nxt1WeekDue: 0,
          nxt15DaysDue: 0,
          nxt1MonthDue: 0,
        },
      };
    }
  }
}
