import { BadRequestException, Injectable } from '@nestjs/common';
import GstrRegister from '../entity/gstr-register.entity';
import { User, UserStatus } from '../../users/entities/user.entity';
import Client from '../../clients/entity/client.entity';
import FindQueryDto from '../dto/find-query.dto';
import { Brackets, createQueryBuilder, getConnection, getManager } from 'typeorm';
import CreateGstrDto from '../dto/create-gstrs.dto';
import FindComplianceDto from '../dto/find-compliance.dto';
import { ReturnsData } from '../entity/returns-data.entity';
import axios from 'axios';
import { compareFiscalMonths, getCurrentFinancialYear, getFinancialYearFromRtnPrd, getMonthYear, getReturnPrdFromYearMonth, getRtnPrdFromFyAndQtr, getRtnPrdFromFyMonFreq, incrementFinancialYear } from 'src/utils/datesFormation';
import * as xlsx from 'xlsx';
import ClientGroup from 'src/modules/client-group/client-group.entity';
import { Permissions } from 'src/modules/tasks/permission';

const categoryLabels = {
  individual: 'Individual',
  huf: 'Hindu Undivided Family',
  partnership_firm: 'Partnership Firm',
  llp: 'Limited Liability Partnership',
  company: 'Company',
  opc: 'OPC',
  public: 'Public Limited',
  government: 'Government',
  sec_8: 'Section-8',
  foreign: 'Foreign',
  aop: 'Association of Persons',
  boi: 'Body of Individuals',
  trust: 'Trust',
  public_trust: 'Public Trust',
  private_discretionary_trust: 'Private Discretionary Trust',
  state: 'State',
  central: 'Central',
  local_authority: 'Local Authority',
  artificial_judicial_person: 'Artificial Juridical Person',
};

@Injectable()
export class GstrRegisterService {

  async create(data: CreateGstrDto, userId: number) {
    let gstrRegisters = [];
    let user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
    for (const clientItem of data.clients) {
      let client = await Client.findOne({ id: clientItem });
      let gstrRegister = new GstrRegister();
      gstrRegister.client = client;
      gstrRegister.registrationType = data.registrationType;
      gstrRegister.organization = user.organization;
      gstrRegisters.push(gstrRegister);
    }
    if (gstrRegisters.length > 0) {
      await GstrRegister.save(gstrRegisters);
    }
    if (data.syncAll) {
      await this.syncClients(data);
    }
    return gstrRegisters;
  }

  async syncClients(data: any) {
    function comparisonYear(fy: string, month: string) {
      const [startYear, endYearSuffix] = fy.split('-');
      const endYear = `20${endYearSuffix}`;
      const year = (month === '01' || month === '02' || month === '03') ? endYear : startYear;
      return `${year}`;
    };
    const clientDetails = await createQueryBuilder(Client, 'client')
      .select(['client.id', 'client.gstNumber', 'client.gstRegistrationDate'])
      .whereInIds(data.clients)
      .getMany();
    const clientGroupDetails = await createQueryBuilder(ClientGroup, 'clientGroup')
      .select(['clientGroup.id', 'clientGroup.gstNumber', 'clientGroup.gstRegistrationDate'])
      .whereInIds(data.clientsGroup)
      .getMany();
    const results = [];
    for (const client of clientDetails) {
      const [, month] = client.gstRegistrationDate.split('/');
      const regYear = client.gstRegistrationDate.slice(-4);
      let financialYear: string;
      if (parseInt(month) <= 3) {
        financialYear = `${parseInt(regYear) - 1}-${regYear.slice(-2)}`;
      } else {
        financialYear = `${regYear}-${parseInt(regYear.slice(-2)) + 1}`;
      }
      const result = await fetchWithRetry(
        { id: client.id, gstNumber: client.gstNumber, fixedFy: data.financialYear },
        financialYear,
      );
      results.push(result);
    }
    for (const clientGroup of clientGroupDetails) {
      const [, month] = clientGroup.gstRegistrationDate.split('/');
      const regYear = clientGroup.gstRegistrationDate.slice(-4);
      let financialYear: string;
      if (parseInt(month) <= 3) {
        financialYear = `${parseInt(regYear) - 1}-${regYear.slice(-2)}`;
      } else {
        financialYear = `${regYear}-${parseInt(regYear.slice(-2)) + 1}`;
      }
      const result = await fetchWithRetry(
        { groupId: clientGroup.id, gstNumber: clientGroup.gstNumber, fixedFy: data.financialYear },
        financialYear,
      );
      results.push(result);
    }

    return results;

    async function fetchWithRetry(item: any, financialYear: any) {
      const registerId = await GstrRegister.findOne({ where: { client: item.id } });
      const registerGroupId = await GstrRegister.findOne({ where: { clientGroup: item.groupId } });
      const headers = { Authorization: process.env.FYN_AUTH };
      const url = `${process.env.FYN_RETURNS}/${item.gstNumber}/${financialYear}`;
      try {
        const response = await axios.get(url, { headers });
        if (response?.data?.EFiledlist?.length > 0) {
          try {
            const returnsData = [];
            await getManager().transaction(async (transactionalEntityManager) => {
              for (const i of response?.data?.EFiledlist) {
                const returnData = new ReturnsData();
                returnData.gstrRegister = registerId ? registerId : registerGroupId;
                returnData.financialYear = getFinancialYearFromRtnPrd(i.ret_prd);
                returnData.valid = i.valid;
                returnData.mof = i.mof;
                returnData.dof = i.dof;
                returnData.rtntype = i.rtntype;
                returnData.retPrd = i.ret_prd;
                returnData.arn = i.arn;
                returnData.status = i.status;
                returnsData.push(returnData);
              }
              if (returnsData.length > 0) {
                await transactionalEntityManager.insert(ReturnsData, returnsData);
              }
            });
          } catch (error) {
            console.error('Failed to save returns data:', error);
          }
        } else {
          console.log(response?.data?.error?.message);
        }
        if (financialYear !== getCurrentFinancialYear()) {
          const nextYear = incrementFinancialYear(financialYear);
          return fetchWithRetry(item, nextYear);
        } else {
          return true;
        }
      } catch (e) {
        console.error(
          `Failed to fetch data for GST number: ${item.gstNumber} in year: ${financialYear}`,
          e,
        );
        throw e;
      }
    }
  }

  async findClients(userId: number, query: any) {
    let user = await User.findOne({
      where: { id: userId },
      relations: ['organization'],
    });
    let clients = await createQueryBuilder(Client, 'client')
      .leftJoinAndSelect('client.organization', 'organization')
      .leftJoinAndSelect('client.gstrRegister', 'gstrRegister')
      .where('organization.id = :organization', { organization: user.organization.id })
      .andWhere('gstrRegister.id is null')
      .andWhere('client.registrationType=:registration', { registration: query.registrationType })
      .andWhere('client.gstVerified is true')
      .andWhere('client.status=:status', { status: UserStatus.ACTIVE })
      .getMany();

    return clients;
  }

  async findAll(userId: number, query: FindQueryDto) {
    const { limit, offset, search, type, category, subCategory, jurisdiction, status } = query;
    function comparisonYear(fy: string, month: string) {
      const [startYear, endYearSuffix] = fy.split('-');
      const endYear = `20${endYearSuffix}`;
      const year = (month === '01' || month === '02' || month === '03') ? endYear : startYear;
      return `${year}`;
    };
    let user = await User.findOne({ where: { id: userId }, relations: ['organization', 'role'] });
    let ViewAll = user.role.permissions.find(
      (permission) => permission.slug === Permissions.VIEW_ALL_CLIENT_MANAGERS,
    );
    let ViewAssigned = user.role.permissions.find(
      (permission) => permission.slug === Permissions.VIEW_ASSIGNED_CLIENT_MANAGERS,
    );

    let getrClients = getConnection()
      .createQueryBuilder(GstrRegister, 'gstrRegister')
      .addSelect([
        'client.displayName',
        'client.clientId',
        'client.gstNumber',
        'client.category',
        'client.id',
        'client.status',
        'client.gstRegistrationDate',
        'clientGroup.displayName',
        'clientGroup.clientId',
        'clientGroup.gstNumber',
        'clientGroup.id',
        'clientGroup.gstRegistrationDate',
        'clientGroup.status',
        'clientManagers.id',
        'clientGroupManagers.id'
      ])
      .leftJoin('gstrRegister.client', 'client')
      .leftJoin('client.clientManagers', 'clientManagers')
      .leftJoin('gstrRegister.clientGroup', 'clientGroup')
      .leftJoin('clientGroup.clientGroupManagers', 'clientGroupManagers');
    if (['GSTR1', 'GSTR1A', 'GSTR2X', 'GSTR3B', 'GSTR7', 'GSTR8'].includes(query.rtntype)) {
      getrClients.leftJoinAndSelect(
        'gstrRegister.returnsData',
        'returnsData',
        'returnsData.financialYear = :financialYear AND returnsData.rtntype=:rtntype AND  LEFT(ret_prd, 2) =:selectedMonth AND RIGHT(ret_prd, 4)=:comparisonYear',
        {
          financialYear: query.selectedYear,
          rtntype: query.rtntype,
          selectedMonth: query.selectedMonth,
          comparisonYear: comparisonYear(query.selectedYear, query.selectedMonth,),
        },
      );
    } else if (['CMP08'].includes(query.rtntype)) {
      getrClients.leftJoinAndSelect(
        'gstrRegister.returnsData',
        'returnsData',
        'returnsData.financialYear = :financialYear AND (returnsData.rtntype=:rtntype OR returnsData.rtntype=:rtntypex) AND  LEFT(ret_prd, 2) =:selectedQuarter AND RIGHT(ret_prd, 4)=:comparisonYear',
        {
          financialYear: query.selectedYear,
          rtntype: query.rtntype,
          rtntypex: 'GSTR4',
          selectedQuarter: query.selectedQuarter,
          comparisonYear: comparisonYear(query.selectedYear, query.selectedQuarter),
        },
      );
    } else if (['GSTR4X', 'GSTR9', 'GSTR9C'].includes(query.rtntype)) {
      const rtnTypeCondition = query.rtntype === 'GSTR4X' ? ['GSTR4X', 'GSTR9A'] : [query.rtntype];
      getrClients.leftJoinAndSelect(
        'gstrRegister.returnsData',
        'returnsData',
        'returnsData.financialYear = :financialYear AND returnsData.rtntype IN (:...rtntype) AND RIGHT(ret_prd,4) = :comparisonYear',
        {
          financialYear: query.selectedYear,
          rtntype: rtnTypeCondition,
          comparisonYear: `20${query.selectedYear.split('-')[1]}`,
        },
      );
    }

    getrClients
      .where('gstrRegister.organization = :organization', { organization: user.organization.id });
    getrClients.andWhere(new Brackets(qb => {
      qb.where('client.id IS NOT NULL AND client.status != :clientStatus', { clientStatus: UserStatus.DELETED })
        .orWhere('clientGroup.id IS NOT NULL AND clientGroup.status != :clientGroupStatus', { clientGroupStatus: UserStatus.DELETED });
    }))

      .andWhere('gstrRegister.registrationType = :type', { type });
    if (status?.length && status[0] === 'Filed') {
      getrClients.andWhere('returnsData.status=:status', { status: 'Filed' });
    } else if (status?.length && status[0] === 'Not Filed') {
      getrClients.andWhere('returnsData.status is null');
    }

    if (ViewAssigned && !ViewAll) {
      getrClients.andWhere(
        new Brackets(qb => {
          qb.where('client.id IS NOT NULL AND clientManagers.id = :userId', { userId })
            .orWhere('clientGroup.id IS NOT NULL AND clientGroupManagers.id = :userId', { userId });
        })
      );
    }

    if (category?.length) {
      getrClients.andWhere('client.category in (:...category)', { category });
    }

    if (subCategory?.length) {
      getrClients.andWhere('client.subCategory in (:...subCategory)', { subCategory });
    }

    if (jurisdiction?.length) {
      getrClients.andWhere('LEFT(client.gstNumber, 2) in (:...jurisdiction)', { jurisdiction });
    }


    if (search) {
      const searchTerm = `%${search}%`;

      getrClients.andWhere(
        new Brackets((qb) => {
          qb.where('client.displayName LIKE :searchTerm', { searchTerm })
            .orWhere('client.gstNumber LIKE :searchTerm', { searchTerm })
            .orWhere('clientGroup.displayName LIKE :searchTerm', { searchTerm })
            .orWhere('clientGroup.gstNumber LIKE :searchTerm', { searchTerm });
        })
      );
    }

    const sort = (typeof query?.sort === "string") ? JSON.parse(query.sort) : query?.sort;
    if (sort?.column) {
      const columnMap: Record<string, string> = {
        clientId: 'client.clientId',
        category: 'client.category',
        displayName: 'client.displayName',
        status: 'returnsData.status',
        dof: 'returnsData.dof'
      };
      const column = columnMap[sort.column] || sort.column;
      getrClients.orderBy(column, sort.direction.toUpperCase());
    }

    if (offset >= 0) {
      getrClients.skip(offset);
    }

    if (limit) {
      getrClients.take(limit);
    }

    let result = await getrClients.getManyAndCount();
    const resultData: any = result[0]?.map(client => {
      if (client?.returnsData.length) {
        return client
      } else {
        const returnPrdFromYearMonth = getReturnPrdFromYearMonth(query?.selectedYear, query?.selectedMonth || query?.selectedQuarter || '03')
        if (
          compareFiscalMonths((getMonthYear(client?.client ? client?.client?.gstRegistrationDate : client?.clientGroup?.gstRegistrationDate)), (returnPrdFromYearMonth)) ||
          (query.rtntype === 'GSTR1A' && returnPrdFromYearMonth.slice(2) + returnPrdFromYearMonth.slice(0, 2) < "202407")
        ) {
          return {
            ...client, returnsData: [{
              arn: "",
              createdAt: "",
              data: null,
              dof: "",
              financialYear: "",
              id: null,
              mof: "",
              retPrd: "",
              rtntype: "",
              status: "NA",
              updatedAt: "",
              valid: "Y"
            }]
          }
        } else {
          return { ...client }
        }
      }
    })
    return {
      count: result[1],
      result: resultData,
    };
  }

  async getCompliance(query: FindComplianceDto) {
    let client;
    let clientGroup;

    if (query.clientId) {
      client = await Client.findOne(query.clientId);
    }

    if (query.clientGroupId) {
      clientGroup = await ClientGroup.findOne(query.clientGroupId);
    }

    if (query.clientId) {
      const gstRegister = await GstrRegister.findOne({ where: { client: query.clientId } });
      if (!gstRegister) {
        return 'Not-synchronized';
      }
    }

    if (query.clientGroupId) {
      const gstRegister = await GstrRegister.findOne({ where: { clientGroup: query.clientGroupId } });
      if (!gstRegister) {
        return 'Not-synchronized';
      }
    }
    const [startYear, endYear] = query.selectedYear.split('-').map((y) => parseInt(y, 10));
    const start = `${startYear}`;
    const end = `${20 + `${endYear}`}`;

    const resultquery = await createQueryBuilder(ReturnsData, 'returnsData')
      .leftJoin('returnsData.gstrRegister', 'gstrRegister')
      .leftJoinAndSelect('gstrRegister.client', 'client')
      .leftJoinAndSelect('gstrRegister.clientGroup', 'clientGroup')
      .select(['returnsData', 'client.id']);

    if (query.clientId) {
      resultquery.where('client.id = :clientId', { clientId: query.clientId })
    }

    if (query.clientGroupId) {
      resultquery.where('clientGroup.id = :clientGroupId', { clientGroupId: query.clientGroupId })
    }

    resultquery.andWhere(
      `(
                    (substring(returnsData.retPrd, 1, 2) BETWEEN '04' AND '12' AND substring(returnsData.retPrd, 3, 6) = :start) OR 
                    (substring(returnsData.retPrd, 1, 2) BETWEEN '01' AND '03' AND substring(returnsData.retPrd, 3, 6) = :end)
                )`,
      { start: start, end: end },
    );

    if (['GSTR1', 'GSTR1A', 'GSTR2X', 'GSTR3B', 'GSTR7', 'GSTR8'].includes(query.rtntype)) {
      resultquery
        .orderBy(
          `CASE
                        WHEN substring(returnsData.retPrd, 1, 2) = '04' AND substring(returnsData.retPrd, 3, 6) = :start THEN 1
                        WHEN substring(returnsData.retPrd, 1, 2) = '05' AND substring(returnsData.retPrd, 3, 6) = :start THEN 2
                        WHEN substring(returnsData.retPrd, 1, 2) = '06' AND substring(returnsData.retPrd, 3, 6) = :start THEN 3
                        WHEN substring(returnsData.retPrd, 1, 2) = '07' AND substring(returnsData.retPrd, 3, 6) = :start THEN 4
                        WHEN substring(returnsData.retPrd, 1, 2) = '08' AND substring(returnsData.retPrd, 3, 6) = :start THEN 5
                        WHEN substring(returnsData.retPrd, 1, 2) = '09' AND substring(returnsData.retPrd, 3, 6) = :start THEN 6
                        WHEN substring(returnsData.retPrd, 1, 2) = '10' AND substring(returnsData.retPrd, 3, 6) = :start THEN 7
                        WHEN substring(returnsData.retPrd, 1, 2) = '11' AND substring(returnsData.retPrd, 3, 6) = :start THEN 8
                        WHEN substring(returnsData.retPrd, 1, 2) = '12' AND substring(returnsData.retPrd, 3, 6) = :start THEN 9
                        WHEN substring(returnsData.retPrd, 1, 2) = '01' AND substring(returnsData.retPrd, 3, 6) = :end THEN 10
                        WHEN substring(returnsData.retPrd, 1, 2) = '02' AND substring(returnsData.retPrd, 3, 6) = :end THEN 11
                        WHEN substring(returnsData.retPrd, 1, 2) = '03' AND substring(returnsData.retPrd, 3, 6) = :end THEN 12
                    END`,
          'ASC',
        )
        .limit(12);
      resultquery.andWhere(
        'returnsData.financialYear = :financialYear AND returnsData.rtntype = :rtntype',
        {
          financialYear: query.selectedYear,
          rtntype: query.rtntype,
        },
      );

    } else if (['CMP08'].includes(query.rtntype)) {
      resultquery
        .orderBy(
          `CASE
                        WHEN substring(returnsData.retPrd, 1, 2) = '06' AND substring(returnsData.retPrd, 3, 6) = :start THEN 1
                        WHEN substring(returnsData.retPrd, 1, 2) = '09' AND substring(returnsData.retPrd, 3, 6) = :start THEN 2
                        WHEN substring(returnsData.retPrd, 1, 2) = '12' AND substring(returnsData.retPrd, 3, 6) = :start THEN 3
                        WHEN substring(returnsData.retPrd, 1, 2) = '03' AND substring(returnsData.retPrd, 3, 6) = :end THEN 4
                    END`,
          'ASC',
        )
        .limit(4);
      resultquery.andWhere(
        'returnsData.financialYear = :financialYear AND (returnsData.rtntype = :rtntype OR returnsData.rtntype=:rtntypee)',
        {
          financialYear: query.selectedYear,
          rtntype: query.rtntype,
          rtntypee: 'GSTR4'
        },
      );


    } else {
      const rtnTypeCondition = query.rtntype === 'GSTR4X' ? ['GSTR4X', 'GSTR9A'] : [query.rtntype];
      resultquery
        .orderBy(
          `CASE
                        WHEN substring(returnsData.retPrd, 1, 2) = '03' AND substring(returnsData.retPrd, 3, 6) = :end THEN 1
                    END`,
          'ASC',
        )
        .limit(1);
      resultquery.andWhere(
        'returnsData.financialYear = :financialYear AND returnsData.rtntype IN (:...rtntype)',
        {
          financialYear: query.selectedYear,
          rtntype: rtnTypeCondition,
        },
      );

    }


    const r = await resultquery.getManyAndCount();

    const monthMapping = {
      '04': 'April',
      '05': 'May',
      '06': 'June',
      '07': 'July',
      '08': 'August',
      '09': 'September',
      '10': 'October',
      '11': 'November',
      '12': 'December',
      '01': 'January',
      '02': 'February',
      '03': 'March',
    };

    const emptyMonthData = {
      id: null,
      mof: null,
      dof: '-',
      retPrd: null,
      arn: '-',
      status: 'Not Filed',
      legalName: null,
      tradeName: null,
      frequency: null,
    };

    if (['GSTR1', 'GSTR1A', 'GSTR2X', 'GSTR3B', 'GSTR7', 'GSTR8'].includes(query.rtntype)) {
      const fiscalYearMonths = ['04', '05', '06', '07', '08', '09', '10', '11', '12', '01', '02', '03',
      ];

      let results = fiscalYearMonths.map((month) => ({
        ...emptyMonthData,
        legalName: client ? client?.legalName : clientGroup?.legalName,
        tradeName: client ? client?.tradeName : clientGroup?.tradeName,
        frequency: monthMapping[month],
      }));
      let filedMonths = fiscalYearMonths.slice();
      r[0].forEach((dataItem) => {
        const monthIndex = fiscalYearMonths.indexOf(dataItem.retPrd.substring(0, 2));
        if (monthIndex !== -1) {
          filedMonths = filedMonths.filter((ele) => ele != dataItem.retPrd.substring(0, 2));
          results[monthIndex] = {
            ...dataItem,
            legalName: client ? client?.legalName : clientGroup?.legalName,
            tradeName: client ? client?.tradeName : clientGroup?.tradeName,
            frequency: monthMapping[dataItem.retPrd.slice(0, 2)],
          };
        };
      });
      const resultss = results?.map(item => {
        if (query.rtntype == "GSTR1A" && compareFiscalMonths("072024", getRtnPrdFromFyMonFreq(query?.selectedYear, item?.frequency))) {
          return { ...item, status: "NA" }
        } else if (compareFiscalMonths((getMonthYear(client ? client?.gstRegistrationDate : clientGroup?.gstRegistrationDate)), (getRtnPrdFromFyMonFreq(query?.selectedYear, item?.frequency)))) {
          return { ...item, status: "NA" }
        } else {
          return { ...item }
        }
      });

      return resultss;
    } else if (['CMP08'].includes(query.rtntype)) {
      const quarterMapping = {
        '06': 'Q1 (Apr - Jun)',
        '09': 'Q2 (Jul - Sep)',
        '12': 'Q3 (Oct - Dec)',
        '03': 'Q4 (Jan - Mar)',
      };
      const fiscalYearQuarter = ['06', '09', '12', '03'];
      let results2 = fiscalYearQuarter.map((quarter) => ({
        ...emptyMonthData,
        legalName: client ? client?.legalName : clientGroup?.tradeName,
        tradeName: client ? client?.tradeName : clientGroup?.tradeName,
        frequency: quarterMapping[quarter],
      }));


      r[0].forEach((dataItem) => {
        const monthIndex = fiscalYearQuarter.indexOf(dataItem.retPrd.substring(0, 2));
        if (monthIndex !== -1) {
          results2[monthIndex] = {
            ...dataItem,
            legalName: client ? client?.legalName : clientGroup?.legalName,
            tradeName: client ? client?.tradeName : clientGroup?.tradeName,
            frequency: quarterMapping[dataItem.retPrd.slice(0, 2)],
          };
        };
      });

      function getMonthYear(dateString: string) {
        const [day, month, year] = dateString.split('/').map(Number);
        const adjustedYear = (month === 1 || month === 2 || month === 3) ? year : year;
        return `${month.toString().padStart(2, '0')}${adjustedYear}`;
      };
      const resultss = results2?.map(item => {
        if (item?.status !== "Filed" && compareFiscalMonths((getMonthYear(client ? client?.gstRegistrationDate : clientGroup?.gstRegistrationDate)), (getRtnPrdFromFyAndQtr(query?.selectedYear, item?.frequency)))) {
          return { ...item, status: "NA" }
        } else {
          return item;
        };
      });
      return resultss;
    } else {
      const fiscalYear = ['03'];
      const yearMapping = {
        '03': 'Yearly',
      };
      let results3 = fiscalYear.map((year) => ({
        ...emptyMonthData,
        legalName: client ? client?.legalName : clientGroup?.legalName,
        tradeName: client ? client?.tradeName : clientGroup?.tradeName,
        frequency: yearMapping[year],
      }));

      r[0].forEach((dataItem) => {
        const monthIndex = fiscalYear.indexOf(dataItem.retPrd.substring(0, 2));
        if (monthIndex !== -1) {
          results3[monthIndex] = {
            ...dataItem,
            legalName: client ? client?.legalName : clientGroup?.legalName,
            tradeName: client ? client?.tradeName : clientGroup?.tradeName,
            frequency: yearMapping[dataItem.retPrd.slice(0, 2)],
          };
        };
      });

      const resultss = results3?.map(item => {
        if (compareFiscalMonths((getMonthYear(client ? client?.gstRegistrationDate : clientGroup?.gstRegistrationDate)), (getRtnPrdFromFyMonFreq(query?.selectedYear, 'March')))) {
          return { ...item, status: "NA" }
        } else {
          return { ...item }
        }
      });
      return resultss;
    }
  };

  async exportGstr1FF(userId: number, query: any) {
    const newQuery = { ...query, offset: 0, limit: null, ...query.query };
    let gstrClients = await this.findAll(userId, newQuery);

    let rows = gstrClients.result.map((gstr: any) => {
      const returnsData = gstr?.returnsData[0];
      const returnStatus = returnsData?.status;
      const dateOfFiling = returnsData?.dof;
      const arn = returnsData?.arn;
      const financialYear = query?.selectedYear;
      const categoryLabel = categoryLabels[gstr?.client?.category] || '-';

      return {
        'Financial Year': financialYear,
        'Client ID': gstr?.client ? gstr?.client?.clientId : gstr?.clientGroup?.clientId,
        'Category': categoryLabel,
        'GSTIN': gstr?.client ? gstr?.client?.gstNumber : gstr?.clientGroup?.gstNumber,
        'Client Name': gstr?.client ? gstr?.client?.displayName : gstr?.clientGroup.displayName,
        'Return Status': returnStatus || "Not Filed",
        'Date of Filing': dateOfFiling || "-",
        'ARN': arn || "-",
      };
    });

    if (rows !== undefined && rows.length) {
      const worksheet = xlsx.utils.json_to_sheet(rows);
      const workbook = xlsx.utils.book_new();
      xlsx.utils.book_append_sheet(workbook, worksheet, 'gstr');
      let file = xlsx.write(workbook, { type: 'buffer' });
      return file;
    } else {
      throw new BadRequestException('No Data for Export');
    }
  }

  async exportGst2x(userId: number, query: any) {
    const newQuery = { ...query, offset: 0, limit: null, ...query.query };
    let gstrClients = await this.findAll(userId, newQuery);

    let rows = gstrClients.result.map((gstr: any) => {
      const returnsData = gstr?.returnsData[0];
      const returnStatus = returnsData?.status;
      const dateOfFiling = returnsData?.dof;
      const arn = returnsData?.arn;
      const financialYear = query?.selectedYear;
      return {
        'Financial Year': financialYear,
        'Client ID': gstr?.client ? gstr?.client.clientId : gstr?.clientGroup?.clientId,
        'Category': categoryLabels[gstr?.client?.category] || "-",
        'GSTIN': gstr?.client ? gstr?.client?.gstNumber : gstr?.clientGroup?.gstNumber,
        'Client Name': gstr?.client ? gstr?.client?.displayName : gstr?.clientGroup?.displayName,
        'Return Status': returnStatus || "Not Filed",
        'Date of Filing': dateOfFiling || "-",
        'ARN': arn || "-",
      };
    });

    if (rows !== undefined && rows.length) {
      const worksheet = xlsx.utils.json_to_sheet(rows);
      const workbook = xlsx.utils.book_new();
      xlsx.utils.book_append_sheet(workbook, worksheet, 'GSTR2X');
      let file = xlsx.write(workbook, { type: 'buffer' });
      return file;
    } else {
      throw new BadRequestException('No Data for Export');
    }
  }
  async exportGst3b(userId: number, query: any) {
    const newQuery = { ...query, offset: 0, limit: null, ...query.query };
    let gstrClients = await this.findAll(userId, newQuery);

    let rows = gstrClients.result.map((gstr: any) => {
      const returnsData = gstr?.returnsData[0];
      const returnStatus = returnsData?.status;
      const dateOfFiling = returnsData?.dof;
      const arn = returnsData?.arn;
      const financialYear = query?.selectedYear;
      return {
        'Financial Year': financialYear,
        'Client ID': gstr?.client ? gstr?.client.clientId : gstr?.clientGroup?.clientId,
        'Category': categoryLabels[gstr?.client?.category] || "-",
        'GSTIN': gstr?.client ? gstr?.client?.gstNumber : gstr?.clientGroup?.gstNumber,
        'Client Name': gstr?.client ? gstr?.client?.displayName : gstr?.clientGroup?.displayName,
        'Return Status': returnStatus || "Not Filed",
        'Date of Filing': dateOfFiling || "-",
        'ARN': arn || "-",
      };
    });

    if (rows !== undefined && rows.length) {
      const worksheet = xlsx.utils.json_to_sheet(rows);
      const workbook = xlsx.utils.book_new();
      xlsx.utils.book_append_sheet(workbook, worksheet, 'GSTR3B');
      let file = xlsx.write(workbook, { type: 'buffer' });
      return file;
    } else {
      throw new BadRequestException('No Data for Export');
    }
  }
  async exportGstrCmp8(userId: number, query: any) {
    const newQuery = { ...query, offset: 0, limit: null, ...query.query };
    let gstrClients = await this.findAll(userId, newQuery);

    let rows = gstrClients.result.map((gstr: any) => {
      const returnsData = gstr?.returnsData[0];
      const returnStatus = returnsData?.status;
      const dateOfFiling = returnsData?.dof;
      const arn = returnsData?.arn;
      const financialYear = query?.selectedYear;
      return {
        'Financial Year': financialYear,
        'Client ID': gstr?.client ? gstr?.client?.clientId : gstr?.clientGroup?.clientId,
        'Category': categoryLabels[gstr?.client?.category] || "-",
        'GSTIN': gstr?.client ? gstr?.client?.gstNumber : gstr?.clientGroup?.gstNumber,
        'Client Name': gstr?.client ? gstr?.client?.displayName : gstr?.clientgroup?.displayName,
        'Return Status': returnStatus || "Not Filed",
        'Date of Filing': dateOfFiling || "-",
        'ARN': arn || "-",
      };
    });

    if (rows !== undefined && rows.length) {
      const worksheet = xlsx.utils.json_to_sheet(rows);
      const workbook = xlsx.utils.book_new();
      xlsx.utils.book_append_sheet(workbook, worksheet, 'CMP08');
      let file = xlsx.write(workbook, { type: 'buffer' });
      return file;
    } else {
      throw new BadRequestException('No Data for Export');
    }
  }
  async exportGstr4(userId: number, query: any) {
    const newQuery = { ...query, offset: 0, limit: null, ...query.query };
    let gstrClients = await this.findAll(userId, newQuery);

    let rows = gstrClients.result.map((gstr: any) => {
      const returnsData = gstr?.returnsData[0];
      const returnStatus = returnsData?.status;
      const dateOfFiling = returnsData?.dof;
      const arn = returnsData?.arn;

      // Adjust the financial year if `selectedYear` is provided
      let adjustedFinancialYear = query?.selectedYear;
      if (adjustedFinancialYear) {
        const [startYear, endYear] = adjustedFinancialYear.split('-').map(Number);
        adjustedFinancialYear = `${startYear - 1}-${endYear - 1}`;
      }

      return {
        'Financial Year': adjustedFinancialYear || "-",
        'Client ID': gstr?.client ? gstr?.client?.clientId : gstr?.clientGroup?.clientId,
        'Category': categoryLabels[gstr?.client?.category] || "-",
        'GSTIN': gstr?.client ? gstr?.client?.gstNumber : gstr?.clientGroup?.gstNumber,
        'Client Name': gstr?.client ? gstr?.client?.displayName : gstr?.clientGroup?.displayName,
        'Return Status': returnStatus || "Not Filed",
        'Date of Filing': dateOfFiling || "-",
        'ARN': arn || "-",
      };
    });

    if (rows !== undefined && rows.length) {
      const worksheet = xlsx.utils.json_to_sheet(rows);
      const workbook = xlsx.utils.book_new();
      xlsx.utils.book_append_sheet(workbook, worksheet, 'GSTR4');
      let file = xlsx.write(workbook, { type: 'buffer' });
      return file;
    } else {
      throw new BadRequestException('No Data for Export');
    }
  }
  async exportGstr9(userId: number, query: any) {
    const newQuery = { ...query, offset: 0, limit: null, ...query.query };
    let gstrClients = await this.findAll(userId, newQuery);

    let rows = gstrClients.result.map((gstr: any) => {
      const returnsData = gstr?.returnsData[0];
      const returnStatus = returnsData?.status;
      const dateOfFiling = returnsData?.dof;
      const arn = returnsData?.arn;

      // Adjust the financial year if `selectedYear` is provided
      let adjustedFinancialYear = query?.selectedYear;
      if (adjustedFinancialYear) {
        const [startYear, endYear] = adjustedFinancialYear.split('-').map(Number);
        adjustedFinancialYear = `${startYear - 1}-${endYear - 1}`;
      }

      return {
        'Financial Year': adjustedFinancialYear || "-",
        'Client ID': gstr?.client ? gstr?.client.clientId : gstr?.clientGroup?.clientId,
        'Category': categoryLabels[gstr?.client?.category] || "-",
        'GSTIN': gstr?.client ? gstr?.client?.gstNumber : gstr?.clientGroup?.gstNumber,
        'Client Name': gstr?.client ? gstr?.client?.displayName : gstr?.clientGroup?.displayName,
        'Return Status': returnStatus || "Not Filed",
        'Date of Filing': dateOfFiling || "-",
        'ARN': arn || "-",
      };
    });

    if (rows !== undefined && rows.length) {
      const worksheet = xlsx.utils.json_to_sheet(rows);
      const workbook = xlsx.utils.book_new();
      xlsx.utils.book_append_sheet(workbook, worksheet, 'gstr9');
      let file = xlsx.write(workbook, { type: 'buffer' });
      return file;
    } else {
      throw new BadRequestException('No Data for Export');
    }
  }

  async exportGstr9c(userId: number, query: any) {
    const newQuery = { ...query, offset: 0, limit: null, ...query.query };
    let gstrClients = await this.findAll(userId, newQuery);

    let rows = gstrClients.result.map((gstr: any) => {
      const returnsData = gstr?.returnsData[0];
      const returnStatus = returnsData?.status;
      const dateOfFiling = returnsData?.dof;
      const arn = returnsData?.arn;

      // Adjust the financial year if `selectedYear` is provided
      let adjustedFinancialYear = query?.selectedYear;
      if (adjustedFinancialYear) {
        const [startYear, endYear] = adjustedFinancialYear.split('-').map(Number);
        adjustedFinancialYear = `${startYear - 1}-${endYear - 1}`;
      }

      return {
        'Financial Year': adjustedFinancialYear || "-",
        'Client ID': gstr?.client ? gstr?.client?.clientId : gstr?.clientGroup?.clientId,
        'Category': categoryLabels[gstr?.client?.category] || "-",
        'GSTIN': gstr?.client ? gstr?.client?.gstNumber : gstr?.clientGroup?.gstNumber,
        'Client Name': gstr?.client ? gstr?.client?.displayName : gstr?.clientGroup?.displayName,
        'Return Status': returnStatus || "Not Filed",
        'Date of Filing': dateOfFiling || "-",
        'ARN': arn || "-",
      };
    });

    if (rows !== undefined && rows.length) {
      const worksheet = xlsx.utils.json_to_sheet(rows);
      const workbook = xlsx.utils.book_new();
      xlsx.utils.book_append_sheet(workbook, worksheet, 'gstr9c');
      let file = xlsx.write(workbook, { type: 'buffer' });
      return file;
    } else {
      throw new BadRequestException('No Data for Export');
    }
  }

  async exportGstrtaxdeductor(userId: number, query: any) {
    const newQuery = { ...query, offset: 0, limit: null, ...query.query };
    let gstrClients = await this.findAll(userId, newQuery);

    let rows = gstrClients.result.map((gstr: any) => {
      const returnsData = gstr?.returnsData[0];
      const returnStatus = returnsData?.status;
      const dateOfFiling = returnsData?.dof;
      const arn = returnsData?.arn;

      // Adjust the financial year if `selectedYear` is provided
      let adjustedFinancialYear = query?.selectedYear;
      if (adjustedFinancialYear) {
        const [startYear, endYear] = adjustedFinancialYear.split('-').map(Number);
        adjustedFinancialYear = `${startYear - 1}-${endYear - 1}`;
      }

      return {
        'Financial Year': adjustedFinancialYear || "-",
        'Client ID': gstr?.client ? gstr?.client?.clientId : gstr?.clientGroup?.clientId,
        'Category': categoryLabels[gstr?.client?.category] || "-",
        'GSTIN': gstr?.client ? gstr?.client?.gstNumber : gstr?.clientGroup?.gstNumber,
        'Client Name': gstr?.client ? gstr?.client?.displayName : gstr?.clientGroup?.displayName,
        'Return Status': returnStatus || "Not Filed",
        'Date of Filing': dateOfFiling || "-",
        'ARN': arn || "-",
      };
    });

    if (rows !== undefined && rows.length) {
      const worksheet = xlsx.utils.json_to_sheet(rows);
      const workbook = xlsx.utils.book_new();
      xlsx.utils.book_append_sheet(workbook, worksheet, 'TaxCollector');
      let file = xlsx.write(workbook, { type: 'buffer' });
      return file;
    } else {
      throw new BadRequestException('No Data for Export');
    }
  }
  async exportGstrtaxCollector(userId: number, query: any) {
    const newQuery = { ...query, offset: 0, limit: null, ...query.query };
    let gstrClients = await this.findAll(userId, newQuery);

    let rows = gstrClients.result.map((gstr: any) => {
      const returnsData = gstr?.returnsData[0];
      const returnStatus = returnsData?.status;
      const dateOfFiling = returnsData?.dof;
      const arn = returnsData?.arn;

      // Adjust the financial year if `selectedYear` is provided
      let adjustedFinancialYear = query?.selectedYear;
      if (adjustedFinancialYear) {
        const [startYear, endYear] = adjustedFinancialYear.split('-').map(Number);
        adjustedFinancialYear = `${startYear - 1}-${endYear - 1}`;
      }

      return {
        'Financial Year': adjustedFinancialYear || "-",
        'Client ID': gstr?.client ? gstr?.client?.clientId : gstr?.clientGroup?.clientId,
        'Category': categoryLabels[gstr?.client?.category] || "-",
        'GSTIN': gstr?.client ? gstr?.client?.gstNumber : gstr?.clientGroup?.gstNumber,
        'Client Name': gstr?.client ? gstr?.client?.displayName : gstr?.clientGroup?.displayName,
        'Return Status': returnStatus || "Not Filed",
        'Date of Filing': dateOfFiling || "-",
        'ARN': arn || "-",
      };
    });

    if (rows !== undefined && rows.length) {
      const worksheet = xlsx.utils.json_to_sheet(rows);
      const workbook = xlsx.utils.book_new();
      xlsx.utils.book_append_sheet(workbook, worksheet, 'TaxCollector');
      let file = xlsx.write(workbook, { type: 'buffer' });
      return file;
    } else {
      throw new BadRequestException('No Data for Export');
    }
  }


}



