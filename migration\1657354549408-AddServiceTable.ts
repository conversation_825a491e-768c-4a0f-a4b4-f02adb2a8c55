import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddServiceTable1657354549408 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `create table service (
            id int not null auto_increment,
            name varchar(255) not null,
            description text null,
            default_one boolean not null default false,
            hourly_price int null,
            total_price int null,
            created_at datetime(6) default current_timestamp(6),
            updated_at datetime(6) default current_timestamp(6),
            primary key (id)
        );`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}
