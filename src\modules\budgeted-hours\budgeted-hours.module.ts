import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import BudgetedHours from './budgeted-hours.entity';
import { BudgetedHoursService } from './budgeted-hours.service';
import { BudgetedHoursController } from './budgeted-hours.controller';

@Module({
  imports: [TypeOrmModule.forFeature([BudgetedHours])],
  controllers: [BudgetedHoursController],
  providers: [BudgetedHoursService],
})
export class BudgetedHoursModule {}