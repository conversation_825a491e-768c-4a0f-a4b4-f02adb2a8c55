import {
  BadRequestException,
  ConflictException,
  Injectable,
  InternalServerErrorException,
} from '@nestjs/common';
import { User } from 'src/modules/users/entities/user.entity';
import { createQueryBuilder, IsNull } from 'typeorm';
import Category from './categories.entity';
import CreateCategoryDto from './dto/create-category.dto';
import ImportCategoriesDto from './dto/import-categories.dto';
import Task from '../tasks/entity/task.entity';
import { TaskRecurringStatus } from '../tasks/dto/types';
import { Service } from '../services/entities/service.entity';

@Injectable()
export class CategoriesService {
  async create(userId: number, data: CreateCategoryDto) {
    try {
      let user = await User.findOne({
        where: { id: userId },
        relations: ['organization'],
      });

      let existingCategory = await Category.findOne({
        where: {
          name: data.name,
          organization: { id: user.organization?.id },
        },
      });

      if (existingCategory) {
        throw new BadRequestException('Category with this name already exists');
      }

      let category = new Category();
      category.name = data.name;
      category.image = data.image;
      category.color = data.color;
      category.subCategories = data.subCategories;
      category.organization = user.organization;
      await category.save();
      return category;
    } catch (err) {
      console.log(err);
      throw new InternalServerErrorException(err);
    }
  }

  async findAll(userId: number) {
    let user = await User.findOne({
      where: { id: userId },
      relations: ['organization'],
    });

    let categories = await createQueryBuilder(Category, 'category')
      .leftJoinAndSelect('category.subCategories', 'subCategories')
      .leftJoinAndSelect('category.organization', 'organization')
      .where('category.parentCategory is null')
      // .andWhere('organization.id = :orgId', { orgId: user.organization.id })
      .andWhere('(category.defaultOne = :defaultOne OR category.organization_id = :organization)', {
        defaultOne: true,
        organization: user.organization.id
      })
      .getMany();

    return categories;
  }

  async findOne(userId: number, id: number) {
    let user = await User.findOne({
      where: { id: userId },
      relations: ['organization'],
    });

    let categories = await Category.findOne({
      where: { id: id },
      relations: ['subCategories', 'organization'],
    });

    return categories;
  }

  async findDefaultCategories() {
    let categories = await createQueryBuilder(Category, 'category')
      .leftJoinAndSelect('category.subCategories', 'subCategories')
      .where('category.parentCategory is null')
      .andWhere('category.defaultOne = true')
      .getMany();

    return categories;
  }

  async importCategories(userId: number, data: ImportCategoriesDto) {
    try {
      let user = await User.findOne({
        where: { id: userId },
        relations: ['organization'],
      });

      let repo = createQueryBuilder(Category, 'category').leftJoinAndSelect(
        'category.subCategories',
        'subCategories',
      );

      if (data.selectAll) {
        repo.where('category.defaultOne = true');
      }

      if (!data.selectAll) {
        repo.whereInIds(data.categories);
      }

      let categories = await repo.getMany();
      let newCategories = [];

      for (let category of categories) {
        let existingCategory = await Category.findOne({
          where: {
            name: category.name,
            parentCategory: IsNull(),
            organization: { id: user.organization.id },
          },
          relations: ['parentCategory'],
        });

        if (existingCategory) {
          throw new ConflictException('Category already exists');
        }

        let newCategory = new Category();
        newCategory.name = category.name;
        newCategory.image = category.image;
        newCategory.color = category.color;
        newCategory.organization = user.organization;
        newCategory.fromAdmin = true;
        newCategory.adminCategoryId = category.id;
        newCategory.subCategories = category.subCategories.map((sub) => {
          let newSub = new Category();
          newSub.name = sub.name;
          return newSub;
        });
        newCategories.push(newCategory);
      }

      await Category.save(newCategories);

      return { message: 'New categories imported' };
    } catch (e) {
      console.log(e);
      throw new InternalServerErrorException(e);
    }
  }

  async updateAdminCategories(userId: number) {
    let user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
    let categories = await Category.find({
      where: {
        organization: { id: user.organization.id },
        fromAdmin: true,
      },
    });

    for (let category of categories) {
      let adminCategory = await Category.findOne({
        where: { id: category.adminCategoryId },
        relations: ['subCategories'],
      });

      if (!adminCategory) continue;

      if (category.version === adminCategory.version) {
        continue;
      }

      category.name = adminCategory.name;
      category.image = adminCategory.image;
      category.color = adminCategory.color;
      category.subCategories = adminCategory.subCategories.map((sub) => {
        let newSub = new Category();
        newSub.name = sub.name;
        return newSub;
      });

      await category.save();
    }

    return 'Updated';
  }

  async update(id: number, data: CreateCategoryDto, userId: number) {

    let user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
    let category = await Category.findOne({ where: { id }, relations: ['subCategories'] });

    const oldSubCategories = category.subCategories.map(eachItem => eachItem.id);
    const newSubCategories = data?.subCategories.filter(item => Boolean(item?.id)).map(eachItem => eachItem.id);

    const removedSubCategoryIds = oldSubCategories.filter(
      (id) => !newSubCategories.includes(id)
    );

    for (let i of removedSubCategoryIds) {
      let isThereAnyTasks = await createQueryBuilder(Task, 'task')
        .leftJoinAndSelect('task.category', 'category')
        .leftJoinAndSelect('task.subCategory', 'subCategory')
        .where('subCategory.id = :SCid', { SCid: i })
        .andWhere('task.recurringStatus = :status', { status: TaskRecurringStatus.PENDING })
        .getMany();
      if (isThereAnyTasks.length) {
        throw new ConflictException("Can't Update, Deleting Sub Category(s) have Upcoming Tasks !");
      }
    }

    for (let i of removedSubCategoryIds) {
      let services = await createQueryBuilder(Service, 'service')
        .leftJoinAndSelect('service.category', 'category')
        .leftJoinAndSelect('service.subCategory', 'subCategory')
        .leftJoinAndSelect('service.organization', 'organization')
        .leftJoinAndSelect('service.labels', 'labels')
        .andWhere('subCategory.id = :subCategory', {
          subCategory: i,
        })
        .getMany();
      if (services.length) {
        throw new ConflictException("Can't Update, Deleting Sub Category(s) have Services !");
      }
    }

    for (let i of removedSubCategoryIds) {
      let category = await Category.findOne({ where: { id: i } });
      category.remove();
    }

    category.name = data.name;
    category.image = data.image;
    category.color = data.color;
    category.subCategories = data.subCategories;
    await category.save();
    return category;
  }

  async delete(id: number) {
    const tasks = await createQueryBuilder(Task, 'task')
      .leftJoinAndSelect('task.category', 'category')
      .leftJoinAndSelect('task.subCategory', 'subCategory')
      .where(
        '(category.id = :id OR subCategory.parentCategory = :id)',
        { id }
      )
      .andWhere('task.recurringStatus = :status', { status: TaskRecurringStatus.PENDING })
      .getMany()

    if (tasks.length) {
      throw new ConflictException("Can't Delete, Category have Upcoming Tasks !");
    }

    let services = await createQueryBuilder(Service, 'service')
      .leftJoinAndSelect('service.category', 'category')
      .leftJoinAndSelect('service.subCategory', 'subCategory')
      .leftJoinAndSelect('service.organization', 'organization')
      .leftJoinAndSelect('service.labels', 'labels')
      .andWhere('category.id = :category', {
        category: id,
      })
      .getMany();

    if (services.length) {
      throw new ConflictException("Can't Delete, Category have Services !");
    }

    let category = await Category.findOne({ where: { id }, relations: ['subCategories'] });
    for (let i of category.subCategories) {
      let category = await Category.findOne({ where: { id: i.id } });
      category.remove();
    }
    return await category.remove();
  }
}
