import { Body, Controller, Get, Post, Req, UseGuards } from '@nestjs/common';
import { JwtAuthGuard } from '../users/jwt/jwt-auth.guard';
import { getClientsReportDto } from './dto/get-clients-report.dto';
import getLogHoursDto from './dto/get-log-hours.dto';
import getEmployeeLogHoursDto from './dto/get-employee-log-hours-report';
import { getTasksReport } from './dto/get-tasks-report.dto';
import { ReportsService } from './reports.service';
import getEmployeeLogHoursDynamicDto from './dto/get-employee-dynamic-loghour-reports';
import getLogHoursFeeDto from './dto/get-loh-hours-fee.dto';
import { getLeadsReportDto } from './dto/get-leads-report.dto';
import { getDscRegisterDto } from './dto/get-dsc-register.dto';
import { getUsersDto } from './dto/get-users-report.dto';
import getExpenditureDto from './dto/getExpenditure.dto';
import getLoghoursDto from './get-log-hours.dto';
import FindTasksQuery from '../tasks/dto/find-query.dto';
import getEmployeeAttendanceDto from './dto/get-employee-attendance.dto';
import FindExpenditureDto from '../expenditure/dto/find-expenditure.dto';
import FindQueryDto from '../clients/dto/find-query.dto';
import getEmployeeTimeSheetDto from './dto/get-employee-timesheet-report';
import { User } from '../users/entities/user.entity';
import getEmployeeAttendanceandTimesheetDto from './dto/get-employee-attendance-and-timesheet';

@UseGuards(JwtAuthGuard)
@Controller('reports')
export class ReportsController {
  constructor(private service: ReportsService) { }

  @UseGuards(JwtAuthGuard)
  @Post('/log-hours')
  async getLogHours(@Req() req: any, @Body() body: getLogHoursDto) {
    const { userId } = req.user;
    let user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
    return this.service.getLogHours(body, user);
  }

  @UseGuards(JwtAuthGuard)
  @Post('/log-hours/export')
  async exportLogHours(@Req() req: any, @Body() body: getLogHoursDto) {
    const { userId } = req.user;
    let user = await User.findOne({ where: { id: userId } });
    return this.service.exportLogHours(body, user);
  }

  @Post('/log-hours-fee')
  async getLogHoursFee(@Req() req: any, @Body() body: getLogHoursFeeDto) {
    const { userId } = req.user;
    return this.service.getLogHoursFee(userId, body);
  }


  @Post('/on-hold-tasks')
  async getOnHoldTasks(@Req() req: any, @Body() body: getLogHoursFeeDto) {
    const { userId } = req.user;
    return this.service.getOnHoldTasks(userId, body);
  }

  @Post('/on-hold-tasks/export')
  async exportOnHoldTasks(@Req() req: any, @Body() body: getLogHoursFeeDto) {
    const { userId } = req.user;
    return this.service.exportOnHoldTasks(userId, body);
  }
  @Post('/log-hours-fee/export')
  async exportgetLogHoursFee(@Req() req: any, @Body() body: getLogHoursFeeDto) {
    const { userId } = req.user;
    return this.service.exportLogHoursFee(userId, body);
  }

  @UseGuards(JwtAuthGuard)
  @Post('/employee-log-hours')
  async getEmployeeLogHours(@Req() req: any, @Body() body: getEmployeeLogHoursDto) {
    const { userId } = req.user;
    let user = await User.findOne({ where: { id: userId } });
    return this.service.getEmployeeLogHours(body, user);
  }

  @Post('/employee-time-sheet')
  async getEmployeeTimeSheet(@Body() body: getEmployeeTimeSheetDto, @Req() req: any) {
    const { userId } = req.user;
    return this.service.getEmployeeTimeSheet(body, userId);
  }

  @Post('/employee-time-sheet-attendance')
  async getEmployeeTimeSheetReport(@Body() body: getEmployeeAttendanceandTimesheetDto, @Req() req: any) {
    const { userId } = req.user;
    return this.service.getEmployeeTimeSheetReport(body, userId);
  }

  @Post('/employee-time-sheet/export')
  async exportEmployeeTimeSheet(@Body() body: getEmployeeTimeSheetDto, @Req() req: any) {
    const { userId } = req.user;
    return this.service.exportEmployeeTimeSheet(body, userId);
  }

  @Post('/log-hours/timesheetexport')
  async exportTimeSheetReport(@Req() req: any, @Body() body: getLoghoursDto) {
    const { userId } = req.user;
    return this.service.exportTimeSheetReport(userId, body);
  }

  @UseGuards(JwtAuthGuard)
  @Post('/employee-attendance')
  async getEmployeeAttendance(@Req() req: any, @Body() body: getEmployeeAttendanceDto) {
    const { userId } = req.user;
    let loginuser = await User.findOne({ where: { id: userId } });
    return this.service.getEmployeeAttendance(body, loginuser);
  }
  @UseGuards(JwtAuthGuard)
  @Post('/employee-attendance-and-timesheet')
  async getEmployeeAttendanceandTimesheet(@Req() req: any, @Body() body: getEmployeeAttendanceandTimesheetDto) {
    const { userId } = req.user;
    let loginuser = await User.findOne({ where: { id: userId } });
    return this.service.getEmployeeAttendanceandTimesheet(body, loginuser);
  }
  @UseGuards(JwtAuthGuard)
  @Post('/employee-attendance-and-timesheet/export')
  async exportEmployeeAttendanceandTimesheet(@Req() req: any, @Body() body: getEmployeeAttendanceandTimesheetDto) {
    const { userId } = req.user;
    return this.service.exportEmployeeAttendanceandTimesheet(body, userId);
  }
  @Post('/employee-log-hours/export')
  async exportEmployeeLogHours(@Body() body: getEmployeeLogHoursDynamicDto) {
    return this.service.exportEmployeeLogHours(body);
  }

  @UseGuards(JwtAuthGuard)
  @Post('/employee-attendance/export')
  async exportEmployeeAttendance(@Req() req: any, @Body() body: getEmployeeAttendanceDto) {
    const { userId } = req.user;
    let loginuser = await User.findOne({ where: { id: userId } });
    return this.service.exportEmployeeAttendance(body, loginuser);
  }

  @Post('/clients')
  async getClientsReport(@Req() req: any, @Body() body: getClientsReportDto) {
    const { userId } = req.user;
    return this.service.getClientsReport(userId, body);
  }
  @Post('/clients/export')
  async exportClientsReport(@Req() req: any, @Body() body: getClientsReportDto) {
    const { userId } = req.user;
    return this.service.exportClientsReport(userId, body);
  }

  @Post('/leads')
  async getLeadsReport(@Req() req: any, @Body() body: getLeadsReportDto) {
    const { userId } = req.user;
    return this.service.getLeadsReport(userId, body);
  }
  @Post('/leads/leadsexport')
  async exportLeadsReport(@Req() req: any, @Body() body: getLeadsReportDto) {
    const { userId } = req.user;
    return this.service.exportLeadsReport(userId, body);
  }

  @Post('/all')
  async getUsersReport(@Req() req: any, @Body() body: getUsersDto) {
    const { userId } = req.user;
    return this.service.getUsersReport(userId, body);
  }
  @Post('/all/userexports')
  async exportUsersReport(@Req() req: any, @Body() body: getUsersDto) {
    const { userId } = req.user;
    return this.service.exportUsersReport(userId, body);
  }
  @Post('/invited')
  async getInviteUsersReport(@Req() req: any, @Body() body: getUsersDto) {
    const { userId } = req.user;
    return this.service.getInviteUsersReport(userId, body);
  }
  @Post('/invited/userexports')
  async exportInviteUsersReport(@Req() req: any, @Body() body: getUsersDto) {
    const { userId } = req.user;
    return this.service.exportInviteUsersReport(userId, body);
  }
  @Post('/dsc-register')
  async getDscReport(@Req() req: any, @Body() body: getDscRegisterDto) {
    const { userId } = req.user;
    return this.service.getDscReport(userId, body);
  }
  @Post('/dsc-register/dscregisterexport')
  async exportDscRegisterReport(@Req() req: any, @Body() body: getDscRegisterDto) {
    const { userId } = req.user;
    return this.service.exportDscRegisterReport(userId, body);
  }

  @Post('/expenditure')
  async getExpenditureReport(@Req() req: any, @Body() body: FindExpenditureDto) {
    const { userId } = req.user;
    return this.service.getExpenditureReport(userId, body);
  }
  @Post('/expenditure/expenditureexport')
  async exportExpenditureReport(@Req() req: any, @Body() body: FindExpenditureDto) {
    const { userId } = req.user;
    return this.service.exportExpenditureReport(userId, body);
  }
  //User Expenditure Export Report
  @Post('/expenditure')
  async getUserExpenditureReport(@Req() req: any, @Body() body: FindExpenditureDto) {
    const { userId } = req.user;
    return this.service.getExpenditureReport(userId, body);
  }
  @Post('/expenditure/expenditureuserexport')
  async exportUserExpenditureReport(@Req() req: any, @Body() body: FindExpenditureDto) {
    const { userId } = req.user;
    return this.service.exportUserExpenditureReport(userId, body);
  }

  @Post('/log-hours')
  async getLoghoursReport(@Req() req: any, @Body() body: getLoghoursDto) {
    const { userId } = req.user;
    return this.service.getLoghoursReport(userId, body);
  }
  @Post('/log-hours/loghoursexport')
  async exportLoghoursReport(@Req() req: any, @Body() body: getLoghoursDto) {
    const { userId } = req.user;
    return this.service.exportLoghoursReport(userId, body);
  }

  //User Loghours Export Report
  @Post('/log-hours')
  async getUserLoghoursReport(@Req() req: any, @Body() body: getLoghoursDto) {
    const { userId } = req.user;
    return this.service.getUserLoghoursReport(userId, body);
  }
  @Post('/log-hours/loghoursuserexport')
  async exportUserLoghoursReport(@Req() req: any, @Body() body: getLoghoursDto) {
    const { userId } = req.user;
    return this.service.exportUserLoghoursReport(userId, body);
  }

  @Post('/clients')
  async getClientsPageReport(@Req() req: any, @Body() body: FindQueryDto) {
    const { userId } = req.user;
    return this.service.getClientsPageReport(userId, body);
  }
  @Post('/clients/clientexport')
  async exportClientsPageReport(@Req() req: any, @Body() body: FindQueryDto) {
    const { userId } = req.user;
    return this.service.exportClientsPageReport(userId, body);
  }

  @Post('/clientgroup/clientsgroupexport')
  async exportClientsGroupPageReport(@Req() req: any, @Body() body: FindQueryDto) {
    const { userId } = req.user;
    return this.service.exportClientsGroupPageReport(userId, body);
  }

  @UseGuards(JwtAuthGuard)
  @Post('/tasks')
  async getTasksReport(@Req() req: any, @Body() body: getTasksReport) {
    const { userId } = req.user;
    return this.service.getTasksReport(userId, body);
  }

  @UseGuards(JwtAuthGuard)
  @Post('/subtasks')
  async getSubTasksReport(@Req() req: any, @Body() body: getTasksReport) {
    const { userId } = req.user;
    return this.service.getSubTasksReport(userId, body);
  }

  @UseGuards(JwtAuthGuard)
  @Post('/comments')
  async getCommentsReport(@Req() req: any, @Body() body: getTasksReport) {
    const { userId } = req.user;
    return this.service.getCommentsReport(userId, body);
  }

  @Post('/tasks/export')
  async exportTasksRepprt(@Req() req: any, @Body() body: getTasksReport) {
    const query = body;
    const { userId } = req.user;
    return this.service.exportTasksReport(userId, query);
  }
  @Post('/subtasks/export')
  async exportSubTasksRepprt(@Req() req: any, @Body() body: getTasksReport) {
    const query = body;
    const { userId } = req.user;
    return this.service.exportSubTasksReport(userId, query);
  }

  @Post('/comments/export')
  async exportCommentsRepprt(@Req() req: any, @Body() body: getTasksReport) {
    const query = body;
    const { userId } = req.user;
    return this.service.exportCommentsReport(userId, query);
  }

  // @Get('/tasks')
  // async getTasksPageReport(@Req() req: any, @Body() body: getTasksReport) {
  //   const { userId } = req.user;
  //   return this.service.getTasksPageReport(userId, body);
  // }

  @Post('/tasks/tasksexport')
  async exportTasksPageReport(@Req() req: any, @Body() body: FindTasksQuery) {
    const { userId } = req.user;
    return this.service.exportTasksPageReport(userId, body);
  }
  //Setting user report
  @Post('/tasks/user')
  async getUserTasksPageReport(@Req() req: any, @Body() body: FindTasksQuery) {
    const { userId } = req.user;
    return this.service.getUserTasksPageReport(userId, body);
  }
  @Post('/tasks/userTasksexport')
  async exportUserTasksPageReport(@Req() req: any, @Body() body: FindTasksQuery) {
    const { userId } = req.user;
    return this.service.exportUserTasksPageReport(userId, body);
  }

  //Manage users task report
  @Post('/tasks/user')
  async getUserCardTasksPageReport(@Req() req: any, @Body() body: FindTasksQuery) {
    const { userId } = req.user;
    return this.service.getUserCardTasksPageReport(userId, body);
  }

  @Post('/tasks/userCardTasksexport')
  async exportUserCardTasksPageReport(@Req() req: any, @Body() body: FindTasksQuery) {
    const { userId } = req.user;
    return this.service.exportUserCardTasksPageReport(userId, body);
  }

  //recurring tasks report
  @Post('/tasks/recurring')
  async getUserRecurringTasksPageReport(@Req() req: any, @Body() body: FindTasksQuery) {
    const { userId } = req.user;
    return this.service.getUserRecurringTasksPageReport(userId, body);
  }

  @Post('/tasks/recurring/upcomingrecurringreport')
  async exportUserRecurringTasksPageReport(@Req() req: any, @Body() body: FindTasksQuery) {
    const { userId } = req.user;
    return this.service.exportUserRecurringTasksPageReport(userId, body);
  }

  // Completed tasks report
  @Post('/tasks/completedtasks')
  async getCompletedTasksReport(@Req() req: any, @Body() body: FindTasksQuery) {
    const { userId } = req.user;
    return this.service.getCompletedTasksReport(userId, body);
  }
  @Post('/tasks/completedtasksreport')
  async exportCompletedTasksReport(@Req() req: any, @Body() body: FindTasksQuery) {
    const { userId } = req.user;
    return this.service.exportCompletedTasksReport(userId, body);
  }

  //Deleted tasks report
  @Post('/tasks/deletedtasks')
  async getDeletedTasksReport(@Req() req: any, @Body() body: FindTasksQuery) {
    const { userId } = req.user;
    return this.service.getDeletedTasksReport(userId, body);
  }
  @Post('/tasks/deletedtasksreport')
  async exportDeletedTasksReport(@Req() req: any, @Body() body: FindTasksQuery) {
    const { userId } = req.user;
    return this.service.exportDeletedTasksReport(userId, body);
  }

  //deleted clients report
  @Post('/clients')
  async getDeletedClientsPageReport(@Req() req: any, @Body() body: FindQueryDto) {
    const { userId } = req.user;
    return this.service.getDeletedClientsPageReport(userId, body);
  }
  @Post('/clients/deletedclients')
  async exportDeletedClientsPageReport(@Req() req: any, @Body() body: FindQueryDto) {
    const { userId } = req.user;
    return this.service.exportDeletedClientsPageReport(userId, body);
  }

  @Post('/clients/deletedclientsgroup')
  async exportDeletedClientsGroupPageReport(@Req() req: any, @Body() body: FindQueryDto) {
    const { userId } = req.user;
    return this.service.exportDeletedClientsGroupPageReport(userId, body);
  }

  //deleted clients report
  @Post('/users')
  async geDeletedtUsersReport(@Req() req: any, @Body() body: getUsersDto) {
    const { userId } = req.user;
    return this.service.geDeletedtUsersReport(userId, body);
  }
  @Post('/users/deletedUsersReport')
  async exportDeletedUsersReport(@Req() req: any, @Body() body: getUsersDto) {
    const { userId } = req.user;
    return this.service.exportDeletedUsersReport(userId, body);
  }

  //Client Pending tasks
  @Post('/tasks')
  async getClientPendingTasksReport(@Req() req: any, @Body() body: FindTasksQuery) {
    const { userId } = req.user;
    const clientId = body.client;
    return this.service.getClientPendingTasksReport(clientId, body, userId);
  }
  @Post('/tasks/clientPendingTasksReport')
  async exportClientPendingTasksReport(@Req() req: any, @Body() body: FindTasksQuery) {
    const { userId } = req.user;
    const clientId = body.client;
    return this.service.exportClientPendingTasksReport(clientId, body, userId);
  }

  @Post('/tasks-delay/export')
  async exportTaskDelay(@Body() body: any) {
    return this.service.exportTaskDelayReport(body);
  }

  @Post('/errors')
  async exportErrors(@Req() req: any, @Body() body: any) {
    const { userId } = req.user;
    return this.service.exportErrorsReport(userId, body);
  }

  @Post('/client-import-errors')
  async exportClientImportErrors(@Req() req: any, @Body() body: any) {
    const { userId } = req.user;
    return this.service.exportClientImportErrors(userId, body);
  }

  @Post('/extensionclients')
  async getExtensionClients(@Req() req: any, @Body() body: getClientsReportDto) {
    const { userId } = req.user;
    return this.service.getExtesnsionClients(userId, body);
  }
}
