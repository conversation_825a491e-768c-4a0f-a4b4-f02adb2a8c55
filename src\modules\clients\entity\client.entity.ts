import ContactPerson from 'src/modules/clients/entity/contact-person.entity';
import Event from 'src/modules/events/event.entity';
import Label from 'src/modules/labels/label.entity';
import RecurringProfile from 'src/modules/recurring/entity/recurring-profile.entity';
import Storage from 'src/modules/storage/storage.entity';
import ClientPin from 'src/modules/clients/entity/client-pin.entity';
import Task from 'src/modules/tasks/entity/task.entity';
import { Organization } from 'src/modules/organization/entities/organization.entity';
import { User, UserStatus } from 'src/modules/users/entities/user.entity';
import {
  AfterLoad,
  BaseEntity,
  BeforeInsert,
  Column,
  CreateDateColumn,
  Entity,
  JoinColumn,
  JoinTable,
  ManyToMany,
  ManyToOne,
  OneToMany,
  OneToOne,
  PrimaryGeneratedColumn,
} from 'typeorm';
import { CategoryEnum, SubCategoryEnum } from '../dto/types';
import Password from './password.entity';
import { Invoice } from 'src/modules/billing/entitities/invoice.entity';
import Receipt from 'src/modules/billing/entitities/receipt.entity';
import ReceiptCredit from 'src/modules/billing/entitities/receipt-credit.entity';
import LogHour from 'src/modules/log-hours/log-hour.entity';
import Expenditure from 'src/modules/expenditure/expenditure.entity';
import DscRegister from 'src/modules/dsc-register/entity/dsc-register.entity';
import GstrRegister, {
  RegistrationType,
  YesOrNoType,
} from 'src/modules/gstr-register/entity/gstr-register.entity';
import { GstrPromise } from 'src/modules/gstr-register/entity/promise.entity';
import CollectData from 'src/modules/collect-data/collect-data.entity';
import Kyb from 'src/modules/kyb/kyb.entity';
import BudgetedHours from 'src/modules/budgeted-hours/budgeted-hours.entity';
import QtmActivity from 'src/modules/admin/entities/qtmActivity.entity';
import AutClientCredentials from 'src/modules/automation/entities/aut_client_credentials.entity';
import AutIncometaxReturns from 'src/modules/automation/entities/aut_incometax_returns.entity';
import AutOutstandingDemand from 'src/modules/automation/entities/aut-outstanding-demand.entity';
import AutEProceedingFya from 'src/modules/automation/entities/aut_income_tax_eproceedings_fya.entity';
import AutFyaNotice from 'src/modules/automation/entities/aut_income_tax_eproceedings_fya_notice.entity';
import AutEProceedingFyi from 'src/modules/automation/entities/aut_income_tax_eproceedings_fyi.entity';
import AutFyiNotice from 'src/modules/automation/entities/aut_income_tax_eproceedings_fyi_notice.entity';
import AutoIncomeTaxForms from 'src/modules/automation/entities/aut_income_tax_forms.entity';
import AutProfileDetails from 'src/modules/automation/entities/aut-profile-details.entity';
import ClientGroup from 'src/modules/client-group/client-group.entity';
import AutUpdateTracker from 'src/modules/automation/entities/aut_update_tracker.entity';
import UdinTask from 'src/modules/udin-task/udin-task.entity';
import GstrCredentials from 'src/modules/gstr-automation/entity/gstrCredentials.entity';
import GstrNoticeOrders from 'src/modules/gstr-automation/entity/noticeOrders.entity';
import ClientGroupBroadcast from 'src/modules/communication/entity/client-group-broadcast.entity';
import { ClientPermission } from './client-permission.entity';
import GstrAdditionalNoticeOrders from 'src/modules/gstr-automation/entity/gstrAdditionalOrdersAndNotices.entity';
import { ProformaInvoice } from 'src/modules/billing/entitities/proforma-invoice.entity';
import AutIncometaxEChallan from 'src/modules/automation/entities/aut_incometax_e-challan.entity';
import AutMyCas from 'src/modules/automation/entities/aut_incometax_my-cas.entity';
import TanClientCredentials from 'src/modules/tan-automation/entity/tan-client-credentials.entity';
import TanUpdateTracker from 'src/modules/tan-automation/entity/tan-update-tracker.entity';
import TanProfile from 'src/modules/tan-automation/entity/tan-profile.entity';
import DocumentInOut from 'src/modules/document-in-out/entity/document-in-out.entity';
import IncTempEproFya from 'src/modules/automation/entities/inc_temp_epro_fya.entity';
import IncTempEproFyi from 'src/modules/automation/entities/inc_temp_epro_fyi.entity';
import TanCommunicationInbox from 'src/modules/tan-automation/entity/tan-communication-inbox.entity';
import TanTempEproFya from 'src/modules/tan-automation/entity/tan_temp_epro_fya.entity';
import TanTempEproFyi from 'src/modules/tan-automation/entity/tan_temp_epro_fyi.entity';

@Entity()
class Client extends BaseEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ nullable: true })
  clientId: string;

  @Column()
  slug: string;

  @Column()
  displayName: string;

  @Column({ default: CategoryEnum.INDIVIDUAL, type: 'enum', enum: CategoryEnum })
  category: CategoryEnum;

  @Column({ default: 'NONE', type: 'enum', enum: SubCategoryEnum })
  subCategory: SubCategoryEnum;

  @Column({ type: 'enum', enum: RegistrationType })
  registrationType: RegistrationType;

  @Column()
  @Column()
  email: string;

  @Column()
  mobileNumber: string;

  @Column({ nullable: true })
  alternateMobileNumber: string;

  @Column({ nullable: true })
  authorizedPerson: string;

  @Column({ nullable: true })
  designation: string;

  @Column({ default: false })
  gstVerified: boolean;

  @Column({ nullable: true })
  gstNumber: string;
  @Column({ nullable: true })
  tanNumber: string;

  @Column({ nullable: true })
  legalName: string;

  @Column({ nullable: true })
  tradeName: string;

  @Column({ nullable: true })
  placeOfSupply: string;

  @Column({ nullable: true })
  constitutionOfBusiness: string;

  @Column({ default: false })
  panVerified: boolean;

  @Column({ nullable: true })
  panNumber: string;

  @Column({ nullable: true })
  firstName: string;

  @Column({ nullable: true })
  lastName: string;

  @Column({ nullable: true })
  middleName: string;

  @Column({ nullable: true })
  fullName: string;

  @Column({ nullable: true })
  buildingName: string;

  @Column({ nullable: true })
  street: string;

  @Column({ nullable: true })
  city: string;

  @Column({ nullable: true })
  state: string;

  @Column({ nullable: true })
  pincode: string;

  @Column({ nullable: true })
  clientNumber: string;

  @Column({ type: 'text', nullable: true })
  notes: string;

  @Column({ nullable: true, type: 'date' })
  dob: string;

  @Column({ nullable: true })
  gstRegistrationDate: string;

  @Column({ nullable: true })
  image: string;

  // @Column({ type: 'enum', enum: YesOrNoType })
  // incomeTaxAudit: YesOrNoType;

  // @Column({ type: 'enum', enum: YesOrNoType })
  // gstAnnualForm: YesOrNoType;

  @Column('json', { array: true })
  localDirectoryPath: object[];

  @Column({ nullable: true })
  countryCode: string;

  @Column({ nullable: true })
  alternateCountryCode: string;

  @ManyToOne(() => Organization, (organization) => organization.clients)
  organization: Organization;

  @ManyToOne(() => User, (user) => user.clients)
  createdBy: User;

  @OneToOne(() => User, (user) => user.client, { cascade: true })
  @JoinColumn()
  user: User;

  @OneToOne(() => CollectData, (collectData) => collectData.user)
  collectData: CollectData;

  @ManyToOne(() => User, (user) => user.clientsAsManager)
  clientManager: User;

  @ManyToMany(() => User, (user) => user.clientsManager)
  @JoinTable()
  clientManagers: User[];

  @OneToMany(() => Storage, (storage) => storage.client)
  storage: Storage[];

  @OneToMany(() => ContactPerson, (contactPerson) => contactPerson.client)
  contactPersons: ContactPerson[];

  @OneToMany(() => Task, (task) => task.client)
  tasks: Task[];

  @OneToMany(() => UdinTask, (udinTask) => udinTask.client)
  udinTasks: UdinTask[];

  @ManyToMany(() => Label)
  @JoinTable()
  labels: Label[];

  @ManyToMany(() => ClientGroup, (clientGroup) => clientGroup.clients)
  groupClients: ClientGroup[];

  @OneToMany(() => RecurringProfile, (recurringProfile) => recurringProfile.client)
  recurringProfiles: RecurringProfile[];

  @OneToMany(() => DocumentInOut, (documentInOut) => documentInOut.client)
  documentInOut: DocumentInOut[];
  
  @OneToMany(() => Password, (password) => password.client)
  passwords: Password[];

  @OneToMany(() => Kyb, (kyb) => kyb.client)
  kyb: Kyb[];

  @OneToMany(() => ClientPin, (clientPin) => clientPin.client)
  clientPins: ClientPin[];

  @ManyToMany(() => Task, (task) => task.taskLeader)
  assignedLeaderTasks: Task[];

  @ManyToMany(() => ClientGroupBroadcast, (clientGroupBroadcast) => clientGroupBroadcast.clients)
  clientGroupBroadcast: ClientGroupBroadcast[];

  @ManyToMany(() => DscRegister, (dscRegister) => dscRegister.clients)
  dscRegisters: DscRegister[];

  @OneToMany(() => Event, (event) => event.task)
  events: Event[];

  @OneToMany(() => Invoice, (invoice) => invoice.client)
  invoices: Invoice[];

  @OneToMany(() => ProformaInvoice, (proformaInvoice) => proformaInvoice.client)
  proformaInvoice: ProformaInvoice[];


  @OneToMany(() => Receipt, (receipt) => receipt.client)
  receipts: Receipt[];

  @OneToMany(() => ReceiptCredit, (rc) => rc.client)
  receiptCredits: ReceiptCredit[];

  @OneToMany(() => LogHour, (logHour) => logHour.client)
  logHours: LogHour[];

  @OneToMany(() => LogHour, (logHour) => logHour.client)
  budgetedHours: BudgetedHours[];

  @ManyToMany(() => ClientPermission, (clientPermission) => clientPermission.permissions)
  @JoinTable()
  permissions: ClientPermission[];

  @OneToMany(() => Expenditure, (exp) => exp.client)
  expenditure: Expenditure[];

  @OneToMany(() => GstrRegister, (gstrRegister) => gstrRegister.client)
  gstrRegister: GstrRegister[];

  @OneToMany(() => GstrPromise, (gstrPromise) => gstrPromise.client)
  gstrPromises: GstrPromise[];

  //Automation

  @OneToMany(() => AutProfileDetails, (autProfileDetails) => autProfileDetails.client)
  autProfileDetails: AutProfileDetails[];

  @OneToMany(() => AutUpdateTracker, (autUpdateTracker) => autUpdateTracker.client)
  autUpdateTracker: AutUpdateTracker[];

  @OneToMany(() => TanUpdateTracker, (tanUpdateTracker) => tanUpdateTracker.client)
  tanUpdateTracker: TanUpdateTracker[];

  //quantum

  @OneToMany(() => QtmActivity, (qtmActivity) => qtmActivity.client)
  qtmActivitys: QtmActivity[];
  // quntum

  //aut_i_t_f
  @OneToMany(() => AutoIncomeTaxForms, (autIncomeTaxForms) => autIncomeTaxForms.client)
  autIncomeTaxForms: AutoIncomeTaxForms[];

  @OneToMany(() => AutClientCredentials, (autClientCredentials) => autClientCredentials.client)
  autClientCredentials: AutClientCredentials[];

  @OneToMany(() => AutIncometaxReturns, (autIncometaxReturns) => autIncometaxReturns.client)
  autIncometaxReturns: AutIncometaxReturns[];

  @OneToMany(() => AutOutstandingDemand, (autOutstandingDemand) => autOutstandingDemand.client)
  autOutstandingDemand: AutOutstandingDemand[];

  @OneToMany(() => AutEProceedingFya, (auteproceedingfya) => auteproceedingfya.client)
  auteproceedingfya: AutEProceedingFya[];

  @OneToMany(() => AutFyaNotice, (autfyanotice) => autfyanotice.client)
  autfyanotice: AutFyaNotice[];

  @OneToMany(() => AutEProceedingFyi, (auteproceedingfyi) => auteproceedingfyi.client)
  auteproceedingfyi: AutEProceedingFyi[];

  @OneToMany(() => AutFyiNotice, (autfyinotice) => autfyinotice.client)
  autfyinotice: AutFyiNotice[];

  @OneToMany(() => AutIncometaxEChallan, (autChallan) => autChallan.client)
  autChallan: AutIncometaxEChallan[];

  @OneToMany(() => AutMyCas, (autMycas) => autMycas.client)
  autMycas: AutMyCas[];

  @OneToMany(() => GstrCredentials, (gstrCredentials) => gstrCredentials.client)
  gstrCredentials: GstrCredentials[];

  @OneToMany(() => GstrNoticeOrders, (gstrNoticeOrders) => gstrNoticeOrders.client)
  gstrNoticeOrders: GstrNoticeOrders[];

  @OneToMany(() => GstrAdditionalNoticeOrders, (gstrAdditional) => gstrAdditional.client)
  gstrAdditionall: GstrAdditionalNoticeOrders[];


  @OneToMany(() => TanClientCredentials, (tanClientCredentials) => tanClientCredentials.client)
  tanClientCredentials: TanClientCredentials[];

  @OneToMany(() => IncTempEproFya, (incTempEproFya) => incTempEproFya.client)
  incTempEproFya: IncTempEproFya[];

  @OneToMany(() => IncTempEproFyi, (incTempEproFyi) => incTempEproFyi.client)
  incTempEproFyi: IncTempEproFyi[];

  @OneToMany(() => TanCommunicationInbox, (tanCommunicationInbox) => tanCommunicationInbox.client)
  tanCommunicationInbox: TanCommunicationInbox[];

  @OneToMany(() => TanTempEproFya, (tanTempEproFya) => tanTempEproFya.client)
  tanTempEproFya: TanTempEproFya[];

  @OneToMany(() => TanTempEproFyi, (tanTempEproFyi) => tanTempEproFyi.client)
  tanTempEproFyi: TanTempEproFyi[];


  // @OneToMany(() => TanProfile, (tanProfile) => tanProfile.client)
  // tanProfile: TanProfile[];

  @OneToOne(() => Storage, (storage) => storage.clientImage, { cascade: true })
  clientImage: Storage;

  @Column({ type: 'timestamp', nullable: true })
  inactiveAt: Date;

  @Column({ default: false })
  clientPortalAccess: boolean;

  @CreateDateColumn()
  createdAt: string;

  @CreateDateColumn()
  updatedAt: string;

  @Column({ type: 'enum', enum: UserStatus, default: UserStatus.ACTIVE })
  status: UserStatus;

  imageUrl: string;

  @Column('json', { array: true })
  address: object[];

  @Column({ default: false })
  issameaddress: boolean;

  @BeforeInsert()
  sluggify() {
    this.slug = this.displayName.toLowerCase().split(' ').join('-');
  }

  @AfterLoad()
  renderImageUrl() {
    if (this.image) {
      this.imageUrl = `${process.env.AWS_BASE_URL}/${this.image}`;
    }
  }
}

export default Client;
