import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import DocumentInOut from './entity/document-in-out.entity';
import DocumentData from './entity/documents-data.entity';
import { DocumentInOutController } from './document-in-out.controller';
import { DocumentInOutService } from './doucment-in-out.service';
import DocumentsData from './entity/documents-data.entity';
import { StorageService } from '../storage/storage.service';
import { AwsService } from '../storage/upload.service';
import { BharathCloudService } from '../storage/bharath-upload.service';
import { BharathStorageService } from '../storage/bharath-storage.service';
import { OneDriveStorageService } from '../ondrive-storage/onedrive-storage.service';
import { AttachmentsService } from '../tasks/services/attachments.service';
import { DocumentInOutSubscriber } from 'src/event-subscribers/documentInOut.subscriber';
import { GoogleDriveStorageService } from '../ondrive-storage/googledrive-storage.service';

@Module({
  imports: [TypeOrmModule.forFeature([DocumentInOut, DocumentsData])],
  controllers: [DocumentInOutController],
  providers: [DocumentInOutService,
    StorageService,
    AwsService,
    BharathCloudService,
    BharathStorageService,
    OneDriveStorageService,
    GoogleDriveStorageService,
    AttachmentsService,
    DocumentInOutSubscriber
  ],

})
export class DocumentInOutModule { }
