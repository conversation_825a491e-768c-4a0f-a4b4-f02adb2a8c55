import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import PosterConfig from './poster-config.entity';
import { PosterController } from './poster.controller';
import { PosterService } from './poster.service';
import { AwsService } from '../storage/upload.service';
import Posters from './posters.entity';
import PosterEvents from './poster-events.entity';
import PosterEventTypes from './poster-event-types.entity';
import { BharathCloudService } from '../storage/bharath-upload.service';
import { BharathStorageService } from '../storage/bharath-storage.service';
import { OneDriveStorageService } from '../ondrive-storage/onedrive-storage.service';
import { StorageService } from '../storage/storage.service';
import { AttachmentsService } from '../tasks/services/attachments.service';
import { GoogleDriveStorageService } from '../ondrive-storage/googledrive-storage.service';
import { Newsletter } from './newsletter.entity';

@Module({
  imports: [TypeOrmModule.forFeature([PosterConfig, PosterEvents, PosterEventTypes, Posters, Newsletter])],
  controllers: [PosterController],
  providers: [PosterService,
    AwsService,
    BharathCloudService,
    BharathStorageService,
    OneDriveStorageService,
    GoogleDriveStorageService,
    StorageService,
    AttachmentsService
  ],
})
export class PosterModule { }
