import * as crypto from 'crypto';

const algorithm = 'aes-256-cbc';
const ivLength = 16;

function getKey(): Buffer {
  const keyHex = process.env.ENCRYPTION_KEY || '';
  if (!keyHex || keyHex.length !== 64) {
    throw new Error(`❌ Invalid ENCRYPTION_KEY, Please Check the ENCRYPTION_KEY in env`);
  }
  return Buffer.from(keyHex, 'hex');
}

export function encrypt(text: string): string {
  const key = getKey();

  const iv = crypto.randomBytes(ivLength);
  const cipher = crypto.createCipheriv(algorithm, key, iv);
  let encrypted = cipher.update(text, 'utf8', 'hex');
  encrypted += cipher.final('hex');
  return `${iv.toString('hex')}:${encrypted}`;
}

export function decrypt(text: string): string {
  const key = getKey();

  const [ivHex, encrypted] = text.split(':');
  const iv = Buffer.from(ivHex, 'hex');
  const decipher = crypto.createDecipheriv(algorithm, key, iv);
  let decrypted = decipher.update(encrypted, 'hex', 'utf8');
  decrypted += decipher.final('utf8');
  return decrypted;
}
