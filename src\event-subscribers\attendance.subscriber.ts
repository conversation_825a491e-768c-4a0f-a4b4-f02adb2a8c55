import {
  Connection,
  EntitySubscriberInterface,
  EventSubscriber,
  InsertEvent,
  UpdateEvent,
} from 'typeorm';
import {
  getAdminIDsBasedOnOrganizationId,
  getUserDetails,
  insertINTONotificationUpdate,
} from 'src/utils/re-use';
import Attendance, { AttendanceStatus } from 'src/modules/attendance/attendance.entity';
import { EXPENDITURE_STATUS } from 'src/modules/expenditure/dto/types';
import { User } from 'src/modules/users/entities/user.entity';
import * as moment from 'moment';
import ViderWhatsappSessions from 'src/modules/whatsapp/entity/viderWhatsappSessions';
import { fullMobileNumberWithCountry } from 'src/utils/validations/fullMobileWithCountry';
import { sendWhatsAppTextMessage } from 'src/modules/whatsapp/whatsapp.service';




let oldStatus: EXPENDITURE_STATUS;
@EventSubscriber()
export class AttendanceSubscriber implements EntitySubscriberInterface<Attendance> {
  constructor(private readonly connection: Connection) {
    connection.subscribers.push(this);
  }
  listenTo() {
    return Attendance;
  }
  async afterInsert(event: InsertEvent<Attendance>) {
    let { user, userId, attendanceDate, checkin_time, type, organization, status, managers } = event.entity;
    let title: string;

    if (type === AttendanceStatus.Leave && status == EXPENDITURE_STATUS.PENDING) {
      title = 'Leave Request';
      const key = 'ATTENDANCE_PUSH';
      const orgId = organization?.id;
      const user = await User.findOne(userId)
      // const body = `A leave request from ${user.fullName} is pending your approval`;
      const body = `A leave request received from <strong>${user.fullName}</strong> for <strong>${moment(attendanceDate).format('DD-MM-YYYY')}</strong> is pending your approval`;
      const users: any[] = managers.map(user => user.id);
      //Managers
      insertINTONotificationUpdate(title, body, users, orgId, key);
      try {
        if (users !== undefined) {
          for (let userId of users) {
            const sessionValidation = await ViderWhatsappSessions.findOne({
              where: { userId: userId, status: 'ACTIVE' },
            });
            if (sessionValidation) {
              const adminUserDetails = await getUserDetails(userId);
              const {
                full_name: userFullName,
                mobile_number: userPhoneNumber,
                country_code: countryCode,
              } = adminUserDetails;
              const userWhatsAppNumber = fullMobileNumberWithCountry(userPhoneNumber, countryCode);
              const key = 'LEAVE_ABSENT_REQUEST_WHATSAPP';
              const whatsappMessageBody = `
    Hi ${userFullName}
A leave request received from ${user.fullName} for ${moment(attendanceDate).format('DD-MM-YYYY')} is pending for your approval
We hope this helps!
    `;
  console.log(userWhatsAppNumber,'userWhatsAppNumberr')
              await sendWhatsAppTextMessage(
                userWhatsAppNumber,
                whatsappMessageBody,
                orgId,
                title,
                userId,
                key,
              );
            }
          }
        }
      } catch (error) {
        console.error('Error sending User WhatsApp notification:', error);
      }
      //Employees
      // const employeeBody = `Your leave request has been sent to your superiors for approval`;
      const employeeBody = `Your leave request for <strong>${moment(attendanceDate).format("DD-MM-YYYY")}</strong> has been sent to your superiors for approval`;
      insertINTONotificationUpdate(title, employeeBody, [user.id as any], orgId, key)
  

    } 
    else {
      if (type === 'Leave' && userId) {
        const userDetails: any = await getUserDetails(userId);
        title = 'User Attendance';
        const body = `Your leave application on ${moment(attendanceDate).format('DD-MM-YYYY')} has been submitted. Enjoy planning your well-deserved break! Remember to finalize any urgent tasks before your absence.`;
        const orgId = organization?.id;
        const key = 'ATTENDANCE_PUSH';

        const user: any = [event.entity?.userId];
        insertINTONotificationUpdate(title, body, user, orgId, key);

        // admin notification
        const AdminIds = await getAdminIDsBasedOnOrganizationId(organization?.id);

        const adminBody = `Time to plan! ${userDetails?.full_name} has submitted a leave request on ${moment(attendanceDate).format('DD-MM-YYYY')}. Please review their upcoming tasks and delegate them to other team members to avoid any delays.`;
        insertINTONotificationUpdate(title, adminBody, AdminIds, orgId, key);
      };
    }
  };

  async beforeUpdate(event: UpdateEvent<Attendance>) {
    oldStatus = event?.databaseEntity?.status;
  }

  async afterUpdate(event: UpdateEvent<Attendance>) {
    const { status, loginUser, organization, userId, managers, attendanceDate } = event.entity;
    const user = await User.findOne(loginUser);
    const employee = await User.findOne(userId)
    const key = 'ATTENDANCE_PUSH';
    let title = 'User Attendance';
    const orgId = organization?.id;


    if (oldStatus == EXPENDITURE_STATUS.PENDING && status == EXPENDITURE_STATUS.REJECTED) {
      title = 'Leave Rejected';
      const body = `You have rejected <strong>${employee.fullName}</strong>'s leave request for <strong>${moment(attendanceDate).format('DD-MM-YYYY')}</strong>`;

      //Login User
      insertINTONotificationUpdate(title, body, [user.id as any], orgId, key);

      //Employee
      const employeeBody = `Your leave request for <strong>${moment(attendanceDate).format('DD-MM-YYYY')}</strong> has been rejected by <strong>${user.fullName}</strong>`;
      insertINTONotificationUpdate(title, employeeBody, [employee.id as any], orgId, key);

      //Managers
      const managerUsers: any[] = managers.map(user => user.id).filter(id => id != loginUser);
      const managerBody = `<strong>${user.fullName}</strong> has rejected <strong>${employee.fullName}</strong>'s leave request for <strong>${moment(attendanceDate).format('DD-MM-YYYY')}</strong>`;

      insertINTONotificationUpdate(title, managerBody, managerUsers, orgId, key);
    } else if (oldStatus == EXPENDITURE_STATUS.PENDING && status == EXPENDITURE_STATUS.APPROVED) {
      title = "Leave Approved";
      const body = `You have approved <strong>${employee.fullName}</strong>'s leave request for <strong>${moment(attendanceDate).format('DD-MM-YYYY')}</strong>`;
      //Login User
      insertINTONotificationUpdate(title, body, [user.id as any], orgId, key);

      //Employee
      const employeeBody = `Your leave request for <strong>${moment(attendanceDate).format('DD-MM-YYYY')}</strong> has been approved by <strong>${user.fullName}</strong>`;
      insertINTONotificationUpdate(title, employeeBody, [employee.id as any], orgId, key);

      //Managers
      const managerUsers: any[] = managers.map(user => user.id).filter(id => id != loginUser);
      const managerBody = `<strong>${user.fullName}</strong> has approved <strong>${employee.fullName}</strong>'s leave request for <strong>${moment(attendanceDate).format('DD-MM-YYYY')}</strong>`;
      insertINTONotificationUpdate(title, managerBody, managerUsers, orgId, key);
    }
    if (oldStatus === EXPENDITURE_STATUS.REJECTED && status == EXPENDITURE_STATUS.PENDING) {
      title = " Leave Request";
      //Login User
      const body = `Your modified leave request for <strong>${moment(attendanceDate).format('DD-MM-YYYY')}</strong> has been sent to your superiors for approval`;
      insertINTONotificationUpdate(title, body, [user.id as any], orgId, key);

      //Managers
      const managerUsers: any[] = managers.map(user => user.id).filter(id => id != loginUser);
      const managerBody = `<strong>${employee.fullName}</strong> has resubmitted the leave request with modifications for <strong>${moment(attendanceDate).format('DD-MM-YYYY')}</strong> for your approval`;
      insertINTONotificationUpdate(title, managerBody, managerUsers, orgId, key);
    }
  };

}
