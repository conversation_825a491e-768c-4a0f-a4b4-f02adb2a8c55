import { Type } from "class-transformer";
import { ArrayNotEmpty, IsInt, IsNotEmpty, IsNumber, IsOptional, IsString, Matches } from "class-validator";

export class FindBillingAmounts {
    @IsNotEmpty()
    @IsString()
    @Matches(/^\d{2}-\d{2}-\d{4}$/, { message: 'startDate must be in DD-MM-YYYY format' })
    startDate: string;


    @IsNotEmpty()
    @IsString()
    @Matches(/^\d{2}-\d{2}-\d{4}$/, { message: 'startDate must be in DD-MM-YYYY format' })
    endDate: string;

    @IsOptional()
    @ArrayNotEmpty()
    @IsInt({ each: true })
    @Type(() => Number)
    billingE: number[];
};

