import { Type } from 'class-transformer';
import { IsNotEmpty, IsOptional, ValidateNested } from 'class-validator';
import { AccountType } from '../entities/bank-account.entity';

export class BankAccountDto {
  @IsNotEmpty()
  bankName: string;

  @IsNotEmpty()
  branchName: string;

  @IsNotEmpty()
  accountNumber: string;

  @IsNotEmpty()
  ifscCode: string;

  @IsOptional()
  upiId: string;

  @IsOptional()
  accountName: string;

  @IsOptional()
  accountType: AccountType;

  @IsOptional()
  upiAttachment: string;

  @IsOptional()
  billingEntityId: number;

  @IsOptional()
  upiStorage: any
}

export class AddBankAccountDto {
  @IsOptional()
  billingEntityId: number;

  @IsNotEmpty()
  @ValidateNested()
  @Type(() => BankAccountDto)
  data: BankAccountDto;
}
