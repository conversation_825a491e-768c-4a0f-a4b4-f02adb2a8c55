import { BadRequestException, Injectable, InternalServerErrorException } from '@nestjs/common';
import { User } from 'src/modules/users/entities/user.entity';
import { createQueryBuilder, getConnection, getManager } from 'typeorm';
import axios from 'axios';
import AutomationMachines, {
  TypeEnum,
} from 'src/modules/automation/entities/automation_machines.entity';
import OrganizationPreferences from 'src/modules/organization-preferences/entity/organization-preferences.entity';
import GstrCredentials, {
  GstrStatus,
} from 'src/modules/gstr-automation/entity/gstrCredentials.entity';

@Injectable()
export class GstrClientService {
  async getGstrClients(userId: number, body: any) {
    const user = await User.findOne(userId, { relations: ['organization'] });
    let clients = getConnection()
      .createQueryBuilder(GstrCredentials, 'gstrCredentials')
      .leftJoinAndSelect('gstrCredentials.client', 'client')
      .leftJoinAndSelect('client.organization', 'organization')
      .where('organization.id=:organization', { organization: user?.organization?.id });
    if (body.offset >= 0) {
      clients.skip(body.offset);
    }

    if (body.limit) {
      clients.take(body.limit);
    }

    let result = await clients.getManyAndCount();
    return {
      count: result[1],
      result: result[0],
    };
  }

  async getGstrClientCredential(id: number, query: any) {
    try {
      const IncomeTaxClient = await createQueryBuilder(GstrCredentials, 'AutClientCredentials')
        .leftJoinAndSelect('AutClientCredentials.client', 'client')
        .where('client.id = :clientId', { clientId: parseInt(query.clientId) })
        .getOne();
      return IncomeTaxClient;
    } catch (error) {
      console.log('error occured while fetching income tax client credentials', error);
    }
  }

  async getAtomClients(userId) {
    let user = await User.findOne({
      where: { id: userId },
      relations: ['organization'],
    });

    const entityManager = getManager();
    const query = `
      SELECT id, display_name as displayName, status
      FROM client 
      WHERE organization_id = ${user?.organization.id} 
      AND status != 'DELETED'
      AND id NOT IN (
          SELECT client_id 
          FROM gstr_credentials
      )   
  `;

    let clients = await entityManager.query(query);
    return clients;
  }

  async addGstrCredentials(userId, body) {
    try {
      let user = await User.findOne({
        where: { id: userId },
        relations: ['organization'],
      });

      const credential = await GstrCredentials.findOne({
        where: { organizationId: user?.organization?.id, userName: body?.userName },
      });

      const organizationPreferences: any = await OrganizationPreferences.findOne({
        where: { organization: user.organization },
      });
      if (organizationPreferences) {
        const organizationLimit = organizationPreferences?.automationConfig?.gstrLimit;
        if (organizationLimit) {
          const gstrCredentialsCount = await GstrCredentials.count({
            where: { organizationId: user.organization.id, status: GstrStatus.ENABLE },
          });
          if (organizationLimit >= gstrCredentialsCount) {
            if (credential) {
              throw new BadRequestException(
                'Specified pannumber Utilize your organization already',
              );
            } else {
              const gstrCredentials = new GstrCredentials();
              gstrCredentials.userName = body?.userName.trim();
              gstrCredentials.password = body?.password.trim();
              gstrCredentials.userId = userId;
              gstrCredentials.clientId = body.clientId;
              gstrCredentials.organizationId = user?.organization?.id;
              gstrCredentials.save();
            }
          } else {
            throw new BadRequestException('Maximum Gstr Client Count Reached');
          }
        }
      }
    } catch (error) {
      console.log('Error occur while add the incomeTax client credentials', error);
      throw new InternalServerErrorException(error);
    }
  }

  async updateGstrCredentials(id, body) {
    try {
      const clientCredential = await GstrCredentials.findOne({
        where: { id },
        relations: ['client'],
      });
      if (clientCredential) {
        clientCredential.password = body.password;
        clientCredential.save();
        return clientCredential;
      }
    } catch (error) {
      console.log('Error occur while update the gstr client credentials', error);
    }
  }

  async sendSingleGstrCamundaRequest(userId: number, data: any) {
    try {
      let config: any = {
        method: 'post',
        maxBodyLength: Infinity,
        url: `${process.env.CAMUNDA_URL}/vider/quantum/api/automation`,
        headers: {
          'X-USER-ID': userId,
          'Content-Type': 'application/json',
        },
        data: JSON.stringify(data),
      };
      const response = await axios(config);

      const responseData = response?.data;
      return responseData[JSON.stringify(data[0].gstrCredentialsId)];
      if (
        responseData[JSON.stringify(data[0].autoCredentialsId)] ===
        'There is already an active request present'
      ) {
        return 'There is already an active request present';
      } else {
        return true;
      }
    } catch (error) {
      console.log('error in sendSingleIncometaxAutomationRequestToCamunda', error);
    }
  }

  async createGsrRequest(userId, id) {
    let data1 = [
      {
        type: 'GSTR',
        gstrCredentialsId: id,
      },
    ];
    return this.sendSingleGstrCamundaRequest(userId, data1);
  }

  async bulkGstrSync(userId, data) {
    let alreadyPrecentList = [];
    if (data?.selectedIds) {
      for (let autoClient of data?.selectedIds) {
        const checkAutomation = await AutomationMachines.findOne({
          where: { gstrCredentials: autoClient.id, status: 'PENDING' },
        });
        if (checkAutomation) {
          alreadyPrecentList.push(autoClient);
        } else {
          this.createGsrRequest(userId, autoClient?.id);
        }
      }
    }
    return alreadyPrecentList;
  }
}
