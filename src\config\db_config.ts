import { TypeOrmModuleOptions } from '@nestjs/typeorm';
import { SnakeNamingStrategy } from 'typeorm-naming-strategies';
import { MyCustomLogger } from './MyCustomLogger';

export default (): TypeOrmModuleOptions => ({
  type: 'mysql',
  host: process.env.DB_HOST,
  port: parseInt(process.env.DB_PORT),
  username: process.env.DB_USERNAME,
  password: process.env.DB_PASSWORD,
  database: process.env.DB_NAME,
  timezone: 'Z',
  logging: ['error', 'info', 'warn', 'log'],
  namingStrategy: new SnakeNamingStrategy(),
  synchronize: false,
  autoLoadEntities: true,
  extra:{
    max:500
  },

  // logger: new MyCustomLogger()
});
