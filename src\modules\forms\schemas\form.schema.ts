import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';
import { FormType } from '../dto/types';
import { Page, PageSchema } from './page.schema';

export type FormDocument = Form & Document;

@Schema({ timestamps: true })
export class Form extends Document {
  @Prop({ default: false })
  defaultOne: boolean;

  @Prop({ required: false, defualt: null })
  organizationId: number;

  @Prop({ required: false, default: null })
  taskId: number;

  @Prop({ required: false, default: null })
  clientId: number;

  @Prop({ enum: FormType, required: false, default: FormType.TEMPLATE })
  type: FormType;

  @Prop({ required: true })
  name: string;

  @Prop()
  description: string;

  @Prop()
  tags: string[];

  @Prop({ type: [PageSchema], required: false, default: [] })
  pages: Page[];
}

export const FormSchema = SchemaFactory.createForClass(Form);
