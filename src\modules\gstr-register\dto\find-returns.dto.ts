import { IsNotEmpty, IsOptional } from 'class-validator';
import { RegistrationType } from '../entity/gstr-register.entity';

class FindReturnsDto {

    @IsNotEmpty()
    clientDetails: any[];

    @IsNotEmpty()
    financialYear: string;

    @IsOptional()
    month: string;

    @IsOptional()
    quarter: string;

    @IsOptional()
    type: RegistrationType;

    @IsOptional()
    rtntype: string;
}

export default FindReturnsDto;
