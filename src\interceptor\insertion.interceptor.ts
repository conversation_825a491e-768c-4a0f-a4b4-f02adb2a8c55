import { Injectable, NestInterceptor, ExecutionContext, CallHandler, UnprocessableEntityException } from '@nestjs/common';
import { Observable, from } from 'rxjs';
import { mergeMap, tap } from 'rxjs/operators';
import { TasksService } from 'src/modules/tasks/services/tasks.service';
import { User } from 'src/modules/users/entities/user.entity';
import * as moment from 'moment';

@Injectable()
export class InsertionInterceptor implements NestInterceptor {
    constructor(private readonly tasksService: TasksService) { }

    getUserOrgbyId = async (data: any) => {
        let user = await User.findOne({ where: { id: data?.id }, relations: ['organization'] });
        return user;
    }

    intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
        const request = context.switchToHttp().getRequest();

        const globalRequest = request?.user;

        return from(
            User.findOne({
                where: { id: globalRequest?.userId },
                relations: ['organization'],
            })
        ).pipe(
            mergeMap((user) => {
                if (request?.method !== 'GET') {
                    if (user) {
                        if (user?.organization?.config) {
                            const config: any = user?.organization?.config;
                            if (config?.expirydate) {
                                let today = moment().format('YYYY-MM-DD');
                                const expiryDate = moment(config.expirydate, 'YYYY-MM-DD');

                                const duration = moment.duration(expiryDate.diff(today));
                                const daysDifference = duration.asDays();

                                if (daysDifference <= 0) {
                                    // throw new UnprocessableEntityException('Your Trial expired');
                                }

                                // Access the user details and perform necessary operations
                                // console.log('Before insertion:', user);
                            }
                        }
                    }
                }

                // Continue with the request handling
                return next.handle().pipe(
                    tap(() => {
                        // Perform any additional actions after insertion if needed
                        // console.log('After insertion');
                    })
                );
            })
        );

    }

}