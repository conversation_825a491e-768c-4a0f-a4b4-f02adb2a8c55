import { MigrationInterface, QueryRunner } from 'typeorm';

export class AlterTasksTable1663743745585 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
        ALTER TABLE task
        MODIFY COLUMN status enum('todo', 'in_progress', 'on_hold', 'under_review', 'done', 'terminated', 'deleted', 'completed') NOT NULL DEFAULT 'todo';
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}
