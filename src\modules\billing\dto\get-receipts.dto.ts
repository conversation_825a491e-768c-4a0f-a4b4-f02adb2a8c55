import { IsNumberString, IsOptional } from 'class-validator';

export class GetReceiptsDto {
  @IsOptional()
  search: string;

  @IsOptional()
  offset: number;

  @IsOptional()
  limit: number;

  @IsOptional()
  @IsNumberString()
  client: number;

  @IsOptional()
  billingEntity: []

  @IsOptional()
  sort: string;

  @IsOptional()
  fromDate: Date;

  @IsOptional()
  toDate: Date;

  @IsOptional()
  status: string;

}
