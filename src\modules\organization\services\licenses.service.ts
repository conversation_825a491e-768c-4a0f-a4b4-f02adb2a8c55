import { Injectable, UnprocessableEntityException } from '@nestjs/common';
import { User } from 'src/modules/users/entities/user.entity';
import { OrganizationLicense } from '../../organization/entities/organization-license.entity';
import { CreateOrganizationLicenseDto } from '../dto/create-organization-license.dto';
import { GetBankAccountsDto } from '../dto/get-bank-accounts.dto';
import { BillingEntity } from '../entities/billing-entity.entity';
import Storage, { StorageSystem } from 'src/modules/storage/storage.entity';
import { StorageService } from 'src/modules/storage/storage.service';
import { OneDriveStorageService } from 'src/modules/ondrive-storage/onedrive-storage.service';
import { BharathCloudService } from 'src/modules/storage/bharath-upload.service';
import { GoogleDriveStorageService } from 'src/modules/ondrive-storage/googledrive-storage.service';

@Injectable()
export class LicensesService {
  constructor(private storageService: StorageService,
    private oneDriveService: OneDriveStorageService,
    private googleDriveService: GoogleDriveStorageService,
    private bharathService: BharathCloudService

  ) { }
  async getOrganizationLicenses(userId: number, query: GetBankAccountsDto) {
    if (query.billingEntityId) {
      let billingEntity = await BillingEntity.findOne({
        where: { id: query.billingEntityId }

      });

      let result = await OrganizationLicense.find({
        where: { billingEntity: { id: billingEntity.id }, relations: ['storage'] },
      });


      return result;
    }

    let user = await User.findOne({
      where: { id: userId },
      relations: ['organization'],
    });

    let licenses = await OrganizationLicense.find({
      where: { organization: { id: user.organization.id }, }, relations: ['storage']
    });

    return licenses;
  }

  async createOrganizationLicense(userId: number, data: CreateOrganizationLicenseDto) {
    let user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
    let license = new OrganizationLicense();
    license.organization = user.organization;
    license.name = data.name;
    let storage: Storage;
    if (data?.storage) {
      if (license?.storage?.id) {
        storage = await Storage.findOne({ where: { id: license?.storage?.id } });
        storage.fileType = data?.storage.fileType;
        storage.fileSize = data?.storage.fileSize;
        storage.name = data?.storage.name;
        storage.file = data?.storage.upload;
        storage.show = data?.storage.show;
        storage.storageSystem = data?.storage?.storageSystem;
        storage.webUrl = data?.storage?.webUrl;
        storage.downloadUrl = data?.storage?.downloadUrl;
        storage.fileId = data?.storage?.fileId;
        storage.authId = user.organization.id;

        // license.storage = storage;
      } else {
        storage = await this.storageService.addAttachements(userId, data?.storage);
        // license.storage = storage;
      }
    } else {
      if (license?.storage?.id) {
        const existingStorage = await Storage.findOne({ where: { id: license?.storage?.id } });
        await existingStorage.remove();
        if (existingStorage) {
          if (existingStorage.storageSystem === StorageSystem.AMAZON) {
            this.storageService.deleteAwsFile(existingStorage.file)
          } else if (existingStorage.storageSystem === StorageSystem.MICROSOFT) {
            this.oneDriveService.deleteOneDriveFile(userId, existingStorage.fileId);
          } else if (existingStorage.storageSystem === StorageSystem.GOOGLE) {
            this.googleDriveService.deleteGoogleDriveFile(userId, existingStorage.fileId);
          } else if (existingStorage.storageSystem === StorageSystem.BHARATHCLOUD) {
            this.bharathService.deleteFile(user.organization.id, existingStorage.file)
          }
        }
        license.storage = null;
      }
    }

    license.attachment = data.attachment;
    license.licenseNumber = data.licenseNumber;
    license.type = data.type;

    if (data.billingEntityId) {
      let billingEntity = await BillingEntity.findOne({ where: { id: data.billingEntityId } });
      let BEDuplicate = await OrganizationLicense.find({ where: { licenseNumber: data.licenseNumber, billingEntity: data.billingEntityId } });
      if (BEDuplicate.length) {
        throw new UnprocessableEntityException('License Number is Already to this Billing Entity');
      }
      license.organization = null;
      license.billingEntity = billingEntity;
    } else {
      const orgDuplicate = await OrganizationLicense.find({ where: { licenseNumber: data.licenseNumber, organization: user.organization } });
      if (orgDuplicate.length) {
        throw new UnprocessableEntityException('License Number is Already to this Organisation');
      }
    }

    const li = await license.save();
    if (storage) {
      storage.organizationLicense = li;
      await storage.save();
    }
    return license;
  }

  async updateOrganizationLicense(licenseId: number, userId: number, data: CreateOrganizationLicenseDto) {
    const user = await User.findOne({ where: { id: userId }, relations: ['organization'] })
    let license = await OrganizationLicense.findOne({
      where: { id: licenseId }, relations: ['organization',
        'billingEntity', 'storage']
    });
    if (data.licenseNumber !== license.licenseNumber) {
      if (license.billingEntity) {
        let BEDuplicate = await OrganizationLicense.find({ where: { licenseNumber: data.licenseNumber, billingEntity: data.billingEntityId } });
        if (BEDuplicate.length) {
          throw new UnprocessableEntityException('License Number is Already to this Billing Entity');
        }
      }
      if (license.organization) {
        const orgDuplicate = await OrganizationLicense.find({ where: { licenseNumber: data.licenseNumber, organization: license.organization } });
        if (orgDuplicate.length) {
          throw new UnprocessableEntityException('License Number is Already to this Organisation');
        }
      }
    }
    license.name = data.name;
    license.attachment = data.attachment;
    let storage: Storage;
    if (data?.storage) {
      if (license?.storage?.id) {
        if (data.storage.name !== license?.storage?.name) {
          const oldOneDriveFileId = license?.storage?.fileId;
          if (data.storage.storageSystem === StorageSystem.MICROSOFT) {
            await this.oneDriveService.deleteOneDriveFile(userId, oldOneDriveFileId);
          } else if (data.storage.storageSystem === StorageSystem.GOOGLE) {
            await this.googleDriveService.deleteGoogleDriveFile(userId, oldOneDriveFileId);
          } else if (data.storage.storageSystem === StorageSystem.AMAZON) {
            await this.storageService.deleteAwsFile(license?.storage?.file)
          } else if (data.storage.storageSystem === StorageSystem.BHARATHCLOUD) {
            await this.bharathService.deleteFile(user.organization.id, license?.storage?.file)
          }
        }

        storage = await Storage.findOne({ where: { id: license?.storage?.id } });
        storage.fileType = data?.storage.fileType;
        storage.fileSize = data?.storage.fileSize;
        storage.name = data?.storage.name;
        storage.file = data?.storage.upload;
        storage.show = data?.storage.show;
        storage.storageSystem = data?.storage?.storageSystem;
        storage.webUrl = data?.storage?.webUrl;
        storage.downloadUrl = data?.storage?.downloadUrl;
        storage.fileId = data?.storage?.fileId;
        storage.authId = user.organization.id;
        // license.storage = storage;

      } else {
        storage = await this.storageService.addAttachements(userId, data?.storage);
        // license.storage = storage;
      }
    } else {
      if (license?.storage?.id) {
        const existingStorage = await Storage.findOne({ where: { id: license?.storage?.id } });
        const removeStorage = await existingStorage.remove();
        if (removeStorage) {
          if (removeStorage.storageSystem === StorageSystem.MICROSOFT) {
            this.oneDriveService.deleteOneDriveFile(userId, removeStorage.fileId);
          } else if (removeStorage.storageSystem === StorageSystem.GOOGLE) {
            this.googleDriveService.deleteGoogleDriveFile(userId, removeStorage.fileId);
          } else if (removeStorage.storageSystem === StorageSystem.AMAZON) {
            this.storageService.deleteAwsFile(removeStorage.file);
          }
        }
        license.storage = null;
      }
    }
    license.licenseNumber = data.licenseNumber;
    license.type = data.type;
    const li = await license.save();
    if (storage) {
      storage.organizationLicense = li;
      await storage.save();
    }
    return license;
  }

  async deleteOrganizationLicense(userId: number, licenseId: number) {
    const user = await User.findOne({ where: { id: userId }, relations: ['organization'] })
    let license = await OrganizationLicense.findOne({ where: { id: licenseId }, relations: ['storage'] });
    const deleteLicense = await license.remove();
    if (deleteLicense.storage) {
      if (deleteLicense.storage?.storageSystem == StorageSystem.MICROSOFT) {
        const fileId = deleteLicense.storage.fileId;
        await this.oneDriveService.deleteOneDriveFile(userId, fileId);
      } else if (deleteLicense.storage?.storageSystem == StorageSystem.GOOGLE) {
        const fileId = deleteLicense.storage.fileId;
        await this.googleDriveService.deleteGoogleDriveFile(userId, fileId);
      } else if (deleteLicense.storage?.storageSystem == StorageSystem.AMAZON) {
        await this.storageService.deleteAwsFile(deleteLicense.storage.file);
      } else if (deleteLicense.storage?.storageSystem === StorageSystem.BHARATHCLOUD) {
        await this.bharathService.deleteFile(user.organization.id, deleteLicense.storage.file)
      }
    }
  }
}
