import { Organization } from 'src/modules/organization/entities/organization.entity';
import {
  BaseEntity,
  Column,
  CreateDateColumn,
  Entity,
  JoinColumn,
  OneToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { GetStartedStatus } from './types';

@Entity()
export class GetStarted extends BaseEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ type: 'enum', enum: GetStartedStatus, default: GetStartedStatus.PENDING })
  status: GetStartedStatus;

  @Column({ default: false })
  importClients: boolean;

  @Column({ default: false })
  importUsers: boolean;

  @Column({ default: false })
  importTasks: boolean;

  @Column({ default: false })
  importDsc: boolean;

  @Column({ default: false })
  createUser: boolean;

  @Column({ default: false })
  selectServices: boolean;

  @OneToOne(() => Organization)
  @JoinColumn()
  organization: Organization;

  @CreateDateColumn()
  createdAt: string;

  @UpdateDateColumn()
  updatedAt: string;
}
