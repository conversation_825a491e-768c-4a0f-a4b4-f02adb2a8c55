import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

export type ValidationDocument = Validation & Document;

@Schema({ timestamps: true })
export class Validation extends Document {
  @Prop({ default: false })
  defaultOne: boolean;

  @Prop({ required: false, defualt: null })
  organizationId: number;

  @Prop({ required: true })
  name: string;

  @Prop({ required: true })
  format: string;

  @Prop({ required: false, default: null })
  message: string;
}

export const ValidationSchema = SchemaFactory.createForClass(Validation);
