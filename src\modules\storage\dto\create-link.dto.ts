import { <PERSON><PERSON>num, IsNotEmpty, <PERSON><PERSON><PERSON><PERSON>, ValidateIf } from 'class-validator';

export enum LinkUserType {
  ORGANIZATION = 'organization',
  TASK = 'task',
  CLIENT = 'client',
  CLIENT_GROUP = 'clientGroup'
}

class CreateLinkDto {
  @IsNotEmpty()
  name: string;

  @IsOptional()
  file: string;

  @IsNotEmpty()
  @IsEnum(LinkUserType)
  type: LinkUserType;

  @ValidateIf((o: CreateLinkDto) => o.type === LinkUserType.CLIENT)
  @IsNotEmpty()
  clientId: number;

  @ValidateIf((o: CreateLinkDto) => o.type === LinkUserType.TASK)
  @IsNotEmpty()
  taskId: number;

  @IsOptional()
  folderId: string;

  @IsOptional()
  local: boolean;
}

export default CreateLinkDto;
