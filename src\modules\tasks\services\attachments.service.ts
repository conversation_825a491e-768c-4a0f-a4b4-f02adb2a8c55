import {
  BadRequestException,
  ConflictException,
  Inject,
  Injectable,
  InternalServerErrorException,
  UnprocessableEntityException,
  forwardRef,
} from '@nestjs/common';
import Storage, { StorageType } from 'src/modules/storage/storage.entity';
import { AwsService } from 'src/modules/storage/upload.service';
import { createQueryBuilder, In } from 'typeorm';
import Task from '../entity/task.entity';
import { v4 as uuidv4 } from 'uuid';
import { User } from 'src/modules/users/entities/user.entity';
import { StorageService } from 'src/modules/storage/storage.service';
import * as moment from 'moment';
import { OneDriveStorageService } from 'src/modules/ondrive-storage/onedrive-storage.service';
import AuthToken, { AuthTokenType } from 'src/modules/ondrive-storage/auth-token.entity';
import { StorageSystem } from 'src/modules/organization/entities/organization.entity';
import Activity, { ActivityType } from 'src/modules/activity/activity.entity';
import { Event_Actions } from 'src/event-listeners/actions';
import { BharathStorageService } from 'src/modules/storage/bharath-storage.service';
import { getName } from 'src/utils/FilterSpecialChars';
import { GoogleDriveStorageService } from 'src/modules/ondrive-storage/googledrive-storage.service';
import { doc } from 'prettier';
import DocumentsData from 'src/modules/document-in-out/entity/documents-data.entity';
import { sendNotification } from 'src/notifications/notify';
import { insertINTONotificationUpdate } from 'src/utils/re-use';
import { getUserIDs } from 'src/utils/re-use';

@Injectable()
export class AttachmentsService {
  constructor(
    private uploadService: AwsService,
    private bharahServce: BharathStorageService,
    @Inject(forwardRef(() => StorageService))
    private storageService: StorageService,
    @Inject(forwardRef(() => OneDriveStorageService))
    private oneDriveStorageService: OneDriveStorageService,
    @Inject(forwardRef(() => GoogleDriveStorageService))
    private googleDriveStorageService: GoogleDriveStorageService,
  ) {}

  async findAttachments(id: number) {
    let taskAttachments = await Storage.find({
      where: { task: { id } },
      relations: ['task', 'documentsData'],
    });
    return taskAttachments;
  }

  // async addAttachments(taskId: number, files: Express.Multer.File[], userId: number) {
  //   try {
  //     let task = await Task.findOne({
  //       where: { id: taskId },
  //       relations: ['client', 'category', 'subCategory'],
  //     });

  //     let user = await User.findOne({
  //       where: { id: userId },
  //       relations: ['organization'],
  //     });

  //     let token = await AuthToken.findOne({
  //       where: {
  //         organizationId: user.organization.id,
  //         type: AuthTokenType.MICROSFT,
  //       },
  //     });
  //     const searchParams = new URLSearchParams({
  //       client_id: process.env.ONE_DRIVE_CLIENT_ID,
  //       scope: process.env.ONE_DRIVE_SCOPE,
  //       response_type: process.env.ONE_DRIVE_RESPONSE_TYPE,
  //       redirect_uri: `${process.env.WEBSITE_URL}/onedrive-auth`,
  //     });

  //     if (!token) {
  //       throw new UnprocessableEntityException({
  //         code: 'NO_TOKEN',
  //         message: 'User has not been authenticated with Microsoft',
  //         authorizationUrl: `${process.env.ONE_DRIVE_AUTH_URL}}?${searchParams.toString()}`,
  //       });
  //       // throw new UnprocessableEntityException('No authentication token found. Please authenticate OneDrive.');
  //     }

  //     let taskStorage = await this.existingClientTaskStorage(task);

  //     let taskAttachments: Storage[] = [];

  //     for (let file of files) {
  //       const { buffer, mimetype, originalname, size } = file;

  //       const { freeSpace, } = await this.storageService.getOrgStorage(userId);

  //       if (!((freeSpace - (file.size)) >= 0)) {
  //         throw new ConflictException('We regret to inform you that the storage limit for your organization has been exceeded. As a result, no further data can be stored until the storage usage is reduced or additional storage capacity is allocated.');
  //       };

  //       // let key = `storage/tasks/${taskId}-${moment().valueOf()}/${file.originalname}`;organization
  //       let key = ``
  //       let upload: any;
  //       // let upload: any = await this.uploadService.upload(buffer, key, mimetype);
  //       try {
  //         upload = await this.oneDriveService.upload(buffer, key, mimetype, token, file)
  //       } catch (err) {
  //         let error = err.response.data?.error;
  //         if (error.code === 'InvalidAuthenticationToken') {
  //           await this.oneDriveService.refreshToken(token);
  //           upload = await this.oneDriveService.upload(buffer, key, mimetype, token, file);
  //         } else if (error.code === 'quotaLimitReached') {
  //           throw new ConflictException({
  //             code: 'NO_STORAGE',
  //             message: error.message,
  //           });
  //         }

  //       }

  //       let storage = new Storage();
  //       storage.name = originalname;
  //       storage.file = upload.Key;
  //       storage.task = task;
  //       storage.fileSize = size;
  //       storage.fileType = mimetype;
  //       storage.client = task.client;
  //       storage.type = StorageType.FILE;
  //       storage.parent = taskStorage;
  //       storage.user = user;
  //       taskAttachments.push(storage);
  //     }

  //     await Storage.save(taskAttachments);

  //     return {
  //       success: true,
  //     };
  //   } catch (err) {
  //     console.log(err);
  //     throw new InternalServerErrorException(err);
  //   }
  // }

  // async saveAttachments(
  //   taskId: number,
  //   files: Express.Multer.File[],
  //   userId: number,
  //   docId?: number,
  // ) {
  //   let user = await User.findOne({
  //     where: { id: userId },
  //     relations: ['organization'],
  //   });
  //   const storageSystem = user?.organization?.storageSystem;
  //   if (storageSystem === StorageSystem.AMAZON) {
  //     return await this.addAttachments(taskId, files, userId, docId);
  //   } else if (storageSystem === StorageSystem.MICROSOFT) {
  //     return await this.oneDriveStorageService.addAttachments(taskId, files, userId, docId);
  //   } else if (storageSystem === StorageSystem.BHARATHCLOUD) {
  //     return await this.bharahServce.addAttachments(taskId, files, userId);
  //   } else if (storageSystem === StorageSystem.GOOGLE) {
  //     return await this.googleDriveStorageService.addAttachments(taskId, files, userId);
  //   }
  // }

  async saveAttachments(
    taskId: number,
    files: Express.Multer.File[],
    userId: number,
    docId?: number,
  ) {
    const user = await User.findOne({
      where: { id: userId },
      relations: ['organization'],
    });

    const storageSystem = user?.organization?.storageSystem;
    let result: any;

    if (storageSystem === StorageSystem.AMAZON) {
      result = await this.addAttachments(taskId, files, userId, docId);
    } else if (storageSystem === StorageSystem.MICROSOFT) {
      result = await this.oneDriveStorageService.addAttachments(taskId, files, userId, docId);
    } else if (storageSystem === StorageSystem.BHARATHCLOUD) {
      result = await this.bharahServce.addAttachments(taskId, files, userId);
    } else if (storageSystem === StorageSystem.GOOGLE) {
      result = await this.googleDriveStorageService.addAttachments(taskId, files, userId);
    }

    // ⬇️ Only notify if files were successfully uploaded
    if (result?.success) {
      const task = await Task.findOne({
        where: { id: taskId },
        relations: ['organization', 'client'],
      });
      const userIds = await getUserIDs(taskId);
      const users = userIds;
      const fileNames = files.map((f) => f.originalname).join(', ');
      const title = 'Task Attachments Uploaded';
      const key = 'TASK_ATTACHMENT_UPLOAD_PUSH';
      const attachmentWord = files.length > 1 ? 'attachments' : 'attachment';
      const body = `"<strong>${user.fullName}</strong>" has uploaded ${attachmentWord}: <strong>${fileNames}</strong> for the task "<strong>${task.taskNumber}</strong>" - "<strong>${task.name}</strong>" for client "<strong>${task?.client?.displayName}</strong>".`;
      
      await insertINTONotificationUpdate(title, body, users, task.organization.id, key);
    }
    return result;
  }

  async addAttachments(
    taskId: number,
    files: Express.Multer.File[],
    userId: number,
    docId?: number,
  ) {
    try {
      let task = await Task.findOne({
        where: { id: taskId },
        relations: ['client', 'category', 'subCategory', 'organization', 'clientGroup'],
      });

      let user = await User.findOne({
        where: { id: userId },
        relations: ['organization'],
      });
      let documentData: DocumentsData;
      if (docId) {
        documentData = await DocumentsData.findOne(docId);
      }
      let taskStorage = await this.existingClientTaskStorage(task);

      let taskAttachments: Storage[] = [];
      let errors: string[] = [];

      for (let file of files) {
        const { buffer, mimetype, originalname, size } = file;
        const existingAttachement = await Storage.find({
          where: { parent: taskStorage, name: originalname },
        });
        if (existingAttachement?.length) {
          errors.push(`File name ${originalname} already exists`);
          continue;
        }

        const { freeSpace } = await this.storageService.getOrgStorage(userId);

        if (!(freeSpace - file.size >= 0)) {
          throw new ConflictException(
            'We regret to inform you that the storage limit for your organization has been exceeded. As a result, no further data can be stored until the storage usage is reduced or additional storage capacity is allocated.',
          );
        }

        let key = `storage/tasks/${taskId}-${moment().valueOf()}/${file.originalname}`;

        let upload: any = await this.uploadService.upload(buffer, key, mimetype);
        let storage = new Storage();
        storage.name = originalname;
        storage.file = upload.Key;
        storage.task = task;
        storage.fileSize = size;
        storage.fileType = mimetype;
        storage.client = task.client;
        storage.clientGroup = task.clientGroup;
        storage.type = StorageType.FILE;
        storage.parent = taskStorage;
        storage.user = user;
        storage.storageSystem = StorageSystem.AMAZON;
        if (docId) {
          storage.documentsData = documentData;
        }
        taskAttachments.push(storage);
      }

      await Storage.save(taskAttachments);

      for (let i of taskAttachments) {
        let collectactivity = new Activity();
        collectactivity.action = Event_Actions.ATTACHEMENT_ADDED;
        collectactivity.actorId = user.id;
        collectactivity.type = ActivityType.TASK;
        collectactivity.typeId = task.id;
        collectactivity.remarks = `Attachement "${i.name}" Added by ${user.fullName}`;
        await collectactivity.save();
      }
      if (errors?.length) {
        return { errors };
      } else {
        return {
          success: true,
        };
      }
    } catch (err) {
      console.log(err);
      throw new InternalServerErrorException(err);
    }
  }

  async addAttachmentsFromStorage(taskId: number, fileIds: number[], userId: number) {
    try {
      let task = await Task.findOne({
        where: { id: taskId },
        relations: ['client', 'category', 'subCategory', 'user', 'organization', 'clientGroup'],
      });

      let files = await Storage.find({
        where: { id: In(fileIds) },
        relations: ['client', 'clientGroup'],
      });

      const user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
      const storageType = user.organization.storageSystem;
      let taskStorage = await this.existingClientTaskStorage(task, storageType);
      let taskAttachments: Storage[] = [];

      for (let file of files) {
        let existingFile: any =
          file.storageSystem === StorageSystem.MICROSOFT
            ? await this.oneDriveStorageService.copyFile(file.fileId, userId, taskStorage?.fileId)
            : await this.uploadService.get(file.file);

        let storage = new Storage();
        storage.name = file.name;
        if (storageType === StorageSystem.AMAZON) {
          let key = `storage/tasks/${taskId}/${file.file}`;
          let uploadedFile: any = await this.uploadService.upload(
            existingFile?.Body as Buffer,
            key,
            file.fileType,
          );
          storage.file = uploadedFile.Key;
        } else if (storageType === StorageSystem.MICROSOFT) {
          storage.webUrl = existingFile.webUrl;
          storage.fileId = existingFile.id;
          storage.file = existingFile?.thumbnails[0]?.large?.url;
          storage.downloadUrl = existingFile['@microsoft.graph.downloadUrl'];
        }
        storage.authId = task?.organization?.id;
        storage.fileSize = file.fileSize;
        storage.storageSystem = storageType;
        storage.task = task;
        storage.fileType = file.fileType;
        storage.client = file.client;
        storage.clientGroup = file.clientGroup;
        storage.type = StorageType.FILE;
        storage.parent = taskStorage;
        taskAttachments.push(storage);
      }

      await Storage.save(taskAttachments);

      return {
        success: true,
      };
    } catch (err) {
      throw new BadRequestException(err);
    }
  }

  async existingClientTaskStorage(task: Task, storageType?: StorageSystem) {
    if (!task.client && !task.clientGroup) return null;
    let atomFolder: Storage;
    let clientFolder: Storage;
    let dispalayNameFolder: Storage;

    if (storageType && storageType === StorageSystem.MICROSOFT) {
      atomFolder = await Storage.findOne({
        where: {
          name: 'Atom',
          organization: task.organization.id,
          show: false,
        },
      });
      if (!atomFolder) {
        const folderData = await this.oneDriveStorageService.createOneDriveFolder(
          task?.user?.id,
          'Atom',
        );
        atomFolder = new Storage();
        atomFolder.name = 'Atom';
        atomFolder.organization = task.organization;
        atomFolder.type = StorageType.FOLDER;
        atomFolder.uid = uuidv4();
        atomFolder.fileId = folderData.id;
        atomFolder.show = false;
        atomFolder.storageSystem = StorageSystem.MICROSOFT;
        atomFolder.authId = task.organization.id;
        await atomFolder.save();
      }
      clientFolder = await Storage.findOne({
        where: {
          name: 'Clients',
          organization: task.organization.id,
          show: false,
        },
      });
      if (!clientFolder) {
        const folderData = await this.oneDriveStorageService.createOneDriveFolder(
          task?.user?.id,
          'Clients',
          atomFolder.fileId,
        );
        clientFolder = new Storage();
        clientFolder.name = 'Clients';
        clientFolder.organization = task.organization;
        clientFolder.type = StorageType.FOLDER;
        clientFolder.uid = uuidv4();
        clientFolder.fileId = folderData.id;
        clientFolder.show = false;
        clientFolder.storageSystem = StorageSystem.MICROSOFT;
        clientFolder.authId = task.organization.id;
        await clientFolder.save();
      }
      dispalayNameFolder = await Storage.findOne({
        where: {
          name: task.client ? task.client.displayName : task?.clientGroup?.displayName,
          organization: task.organization.id,
          show: false,
          type: StorageType.FOLDER,
          ...(task?.client && { client: task.client }),
          ...(task?.clientGroup && { clientGroup: task.clientGroup }),
        },
      });
      if (!dispalayNameFolder) {
        const folderData = await this.oneDriveStorageService.createOneDriveFolder(
          task?.user?.id,
          task.client ? task.client.displayName : task.clientGroup.displayName,
          clientFolder?.fileId,
        );
        dispalayNameFolder = new Storage();
        dispalayNameFolder.name = task.client
          ? task.client.displayName
          : task.clientGroup.displayName;
        dispalayNameFolder.organization = task.organization;
        dispalayNameFolder.type = StorageType.FOLDER;
        dispalayNameFolder.uid = uuidv4();
        dispalayNameFolder.fileId = folderData.id;
        dispalayNameFolder.show = false;
        dispalayNameFolder.storageSystem = StorageSystem.MICROSOFT;
        dispalayNameFolder.authId = task.organization.id;
        if (task?.client) {
          dispalayNameFolder.client = task.client;
        } else if (task?.clientGroup) {
          dispalayNameFolder.clientGroup = task.clientGroup;
        }
        await dispalayNameFolder.save();
      }
    } else if (storageType && storageType === StorageSystem.GOOGLE) {
      atomFolder = await Storage.findOne({
        where: {
          name: 'Atom',
          organization: task.organization.id,
          show: false,
          type: StorageType.FOLDER,
        },
      });
      if (!atomFolder) {
        const folderData = await this.googleDriveStorageService.createGoogleDriveFolder(
          task?.user?.id,
          'Atom',
        );
        atomFolder = new Storage();
        atomFolder.name = 'Atom';
        atomFolder.organization = task.organization;
        atomFolder.type = StorageType.FOLDER;
        atomFolder.uid = uuidv4();
        atomFolder.fileId = folderData.id;
        atomFolder.show = false;
        atomFolder.storageSystem = StorageSystem.GOOGLE;
        atomFolder.authId = task.organization.id;
        await atomFolder.save();
      }
      clientFolder = await Storage.findOne({
        where: {
          name: 'Clients',
          organization: task.organization.id,
          show: false,
          type: StorageType.FOLDER,
        },
      });
      if (!clientFolder) {
        const folderData = await this.googleDriveStorageService.createGoogleDriveFolder(
          task?.user?.id,
          'Clients',
          atomFolder.fileId,
        );
        clientFolder = new Storage();
        clientFolder.name = 'Clients';
        clientFolder.organization = task.organization;
        clientFolder.type = StorageType.FOLDER;
        clientFolder.uid = uuidv4();
        clientFolder.fileId = folderData.id;
        clientFolder.show = false;
        clientFolder.storageSystem = StorageSystem.GOOGLE;
        clientFolder.authId = task.organization.id;
        await clientFolder.save();
      }
      dispalayNameFolder = await Storage.findOne({
        where: {
          name: task.client ? task.client.displayName : task?.clientGroup?.displayName,
          organization: task.organization.id,
          show: false,
          type: StorageType.FOLDER,
          ...(task.client && { client: task.client }),
          ...(task.clientGroup && { clientGroup: task.clientGroup }),
        },
      });
      if (!dispalayNameFolder) {
        const folderData = await this.googleDriveStorageService.createGoogleDriveFolder(
          task?.user?.id,
          task.client ? task.client.displayName : task.clientGroup.displayName,
          clientFolder?.fileId,
        );
        dispalayNameFolder = new Storage();
        dispalayNameFolder.name = task.client
          ? task.client.displayName
          : task.clientGroup.displayName;
        dispalayNameFolder.organization = task.organization;
        dispalayNameFolder.type = StorageType.FOLDER;
        dispalayNameFolder.uid = uuidv4();
        dispalayNameFolder.fileId = folderData.id;
        dispalayNameFolder.show = false;
        dispalayNameFolder.storageSystem = StorageSystem.GOOGLE;
        dispalayNameFolder.authId = task.organization.id;
        if (task?.client) {
          dispalayNameFolder.client = task.client;
        } else if (task?.clientGroup) {
          dispalayNameFolder.clientGroup = task.clientGroup;
        }
        await dispalayNameFolder.save();
      }
    } else if (storageType && storageType === StorageSystem.BHARATHCLOUD) {
      atomFolder = await Storage.findOne({
        where: {
          name: 'Atom',
          organization: task.organization.id,
          show: false,
        },
      });
      if (!atomFolder) {
        const atom = 'Atom';
        // const folderData = await this.oneDriveStorageService.createOneDriveFolder(task?.user?.id, "Atom");
        atomFolder = new Storage();
        atomFolder.name = 'Atom';
        atomFolder.organization = task.organization;
        atomFolder.type = StorageType.FOLDER;
        atomFolder.uid = uuidv4();
        // atomFolder.fileId = folderData.id;
        atomFolder.filePath = atom;
        atomFolder.show = false;
        atomFolder.storageSystem = StorageSystem.BHARATHCLOUD;
        atomFolder.authId = task.organization.id;
        await atomFolder.save();
      }
      clientFolder = await Storage.findOne({
        where: {
          name: 'Clients',
          organization: task.organization.id,
          show: false,
        },
      });
      if (!clientFolder) {
        // const folderData = await this.oneDriveStorageService.createOneDriveFolder(task?.user?.id, "Clients", atomFolder.fileId);
        const clients = 'Clients';
        clientFolder = new Storage();
        clientFolder.name = 'Clients';
        clientFolder.organization = task.organization;
        clientFolder.type = StorageType.FOLDER;
        clientFolder.uid = uuidv4();
        // clientFolder.fileId = folderData.id;
        clientFolder.filePath = `${atomFolder.filePath}/${clients}`;
        clientFolder.show = false;
        clientFolder.storageSystem = StorageSystem.BHARATHCLOUD;
        clientFolder.authId = task.organization.id;
        await clientFolder.save();
      }
      dispalayNameFolder = await Storage.findOne({
        where: {
          name: task.client.displayName,
          organization: task.organization.id,
          show: false,
          client: task.client,
        },
      });
      if (!dispalayNameFolder) {
        const dispalyName = getName(task.client.displayName);
        // const folderData = await this.oneDriveStorageService.createOneDriveFolder(task?.user?.id, task.client.displayName, clientFolder?.fileId);
        dispalayNameFolder = new Storage();
        dispalayNameFolder.name = task.client.displayName;
        dispalayNameFolder.organization = task.organization;
        dispalayNameFolder.type = StorageType.FOLDER;
        dispalayNameFolder.uid = uuidv4();
        // dispalayNameFolder.fileId = folderData.id;
        dispalayNameFolder.filePath = `${clientFolder.filePath}/${dispalyName}`;
        dispalayNameFolder.show = false;
        dispalayNameFolder.storageSystem = StorageSystem.BHARATHCLOUD;
        dispalayNameFolder.authId = task.organization.id;
        dispalayNameFolder.client = task.client;
        await dispalayNameFolder.save();
      }
    }

    let taskFinYearStorage = null;
    if (task.client) {
      taskFinYearStorage = await Storage.findOne({
        where: {
          name: task.financialYear,
          client: { id: task?.client?.id },
        },
      });
    }
    if (task.clientGroup) {
      taskFinYearStorage = await Storage.findOne({
        where: {
          name: task.financialYear,
          clientGroup: { id: task?.clientGroup?.id },
        },
      });
    }

    if (!taskFinYearStorage) {
      taskFinYearStorage = new Storage();
      taskFinYearStorage.name = task.financialYear;
      taskFinYearStorage.client = task.client;
      taskFinYearStorage.clientGroup = task?.clientGroup;
      taskFinYearStorage.type = StorageType.FOLDER;
      taskFinYearStorage.uid = uuidv4();
      taskFinYearStorage.authId = task.organization.id;
      if (storageType && storageType === StorageSystem.MICROSOFT) {
        const folderData = await this.oneDriveStorageService.createOneDriveFolder(
          task?.user?.id,
          task.financialYear,
          dispalayNameFolder?.fileId,
        );
        taskFinYearStorage.fileId = folderData.id;
        taskFinYearStorage.storageSystem = StorageSystem.MICROSOFT;
      } else if (storageType && storageType === StorageSystem.GOOGLE) {
        const folderData = await this.googleDriveStorageService.createGoogleDriveFolder(
          task?.user?.id,
          task.financialYear,
          dispalayNameFolder?.fileId,
        );
        taskFinYearStorage.fileId = folderData.id;
        taskFinYearStorage.storageSystem = StorageSystem.GOOGLE;
      } else if (storageType && storageType == StorageSystem.BHARATHCLOUD) {
        taskFinYearStorage.filePath = `${dispalayNameFolder.filePath}/${task.financialYear}`;
      }
      await taskFinYearStorage.save();
    }

    let taskCategoryStorage = null;
    if (task.client) {
      taskCategoryStorage = await Storage.findOne({
        where: {
          name: task.category?.name,
          parent: { id: taskFinYearStorage.id },
          client: { id: task.client?.id },
        },
        relations: ['parent'],
      });
    }
    if (task.clientGroup) {
      taskCategoryStorage = await Storage.findOne({
        where: {
          name: task.category?.name,
          parent: { id: taskFinYearStorage.id },
          clientGroup: { id: task.clientGroup?.id },
        },
        relations: ['parent'],
      });
    }

    if (!taskCategoryStorage && task.category) {
      taskCategoryStorage = new Storage();
      taskCategoryStorage.name = task.category?.name;
      taskCategoryStorage.client = task.client;
      taskCategoryStorage.clientGroup = task?.clientGroup;
      taskCategoryStorage.type = StorageType.FOLDER;
      taskCategoryStorage.parent = taskFinYearStorage;
      taskCategoryStorage.authId = task.organization.id;
      taskCategoryStorage.uid = uuidv4();
      if (storageType && storageType === StorageSystem.MICROSOFT) {
        const folderData = await this.oneDriveStorageService.createOneDriveFolder(
          task?.user?.id,
          task.category?.name,
          taskFinYearStorage?.fileId,
        );
        taskCategoryStorage.fileId = folderData.id;
        taskCategoryStorage.storageSystem = StorageSystem.MICROSOFT;
      } else if (storageType && storageType === StorageSystem.GOOGLE) {
        const folderData = await this.googleDriveStorageService.createGoogleDriveFolder(
          task?.user?.id,
          task.category?.name,
          taskFinYearStorage?.fileId,
        );
        taskCategoryStorage.fileId = folderData.id;
        taskCategoryStorage.storageSystem = StorageSystem.GOOGLE;
      } else if (storageType && storageType === StorageSystem.BHARATHCLOUD) {
        taskCategoryStorage.filePath = `${taskFinYearStorage.filePath}/${task.category?.name}`;
      }

      await taskCategoryStorage.save();
    }

    let taskSubCategoryStorage = null;
    if (task.client) {
      taskSubCategoryStorage = await Storage.findOne({
        where: {
          name: task.subCategory?.name,
          parent: { id: taskCategoryStorage?.id },
          client: { id: task.client?.id },
        },
        relations: ['parent'],
      });
    }
    if (task.clientGroup) {
      taskSubCategoryStorage = await Storage.findOne({
        where: {
          name: task.subCategory?.name,
          parent: { id: taskCategoryStorage?.id },
          clientGroup: { id: task.clientGroup?.id },
        },
        relations: ['parent'],
      });
    }

    if (!taskSubCategoryStorage && task.subCategory) {
      taskSubCategoryStorage = new Storage();
      taskSubCategoryStorage.name = task.subCategory?.name;
      taskSubCategoryStorage.client = task.client;
      taskSubCategoryStorage.clientGroup = task?.clientGroup;
      taskSubCategoryStorage.type = StorageType.FOLDER;
      taskSubCategoryStorage.parent = taskCategoryStorage;
      taskSubCategoryStorage.uid = uuidv4();
      taskSubCategoryStorage.authId = task.organization.id;
      if (storageType && storageType === StorageSystem.MICROSOFT) {
        const folderData = await this.oneDriveStorageService.createOneDriveFolder(
          task?.user?.id,
          task.subCategory?.name,
          taskCategoryStorage?.fileId,
        );
        taskSubCategoryStorage.fileId = folderData.id;
        taskSubCategoryStorage.storageSystem = StorageSystem.MICROSOFT;
      } else if (storageType && storageType === StorageSystem.GOOGLE) {
        const folderData = await this.googleDriveStorageService.createGoogleDriveFolder(
          task?.user?.id,
          task.subCategory?.name,
          taskCategoryStorage?.fileId,
        );
        taskSubCategoryStorage.fileId = folderData.id;
        taskSubCategoryStorage.storageSystem = StorageSystem.GOOGLE;
      } else if (storageType && storageType === StorageSystem.BHARATHCLOUD) {
        taskSubCategoryStorage.filePath = `${taskCategoryStorage.filePath}/${task.subCategory?.name}`;
      }
      await taskSubCategoryStorage.save();
    }

    let taskStorage = await createQueryBuilder(Storage, 'storage')
      .leftJoinAndSelect('storage.parent', 'parent')
      .where('storage.name = :name', { name: task.name })
      .andWhere('(parent.id = :subCategory or parent.id = :category)', {
        subCategory: taskSubCategoryStorage?.id,
        category: taskCategoryStorage?.id,
      })
      .getOne();

    if (!taskStorage) {
      let storage = new Storage();
      storage.name = task.name;
      storage.client = task.client;
      storage.clientGroup = task?.clientGroup;
      storage.type = StorageType.FOLDER;
      storage.uid = uuidv4();
      storage.parent = taskSubCategoryStorage || taskCategoryStorage || taskFinYearStorage;
      storage.authId = task.organization.id;
      if (storageType && storageType === StorageSystem.MICROSOFT) {
        const folderData = await this.oneDriveStorageService.createOneDriveFolder(
          task?.user?.id,
          task?.name,
          taskSubCategoryStorage?.fileId ||
            taskCategoryStorage?.fileId ||
            taskFinYearStorage?.fileId,
        );
        storage.fileId = folderData.id;
        storage.storageSystem = StorageSystem.MICROSOFT;
      } else if (storageType && storageType === StorageSystem.GOOGLE) {
        const folderData = await this.googleDriveStorageService.createGoogleDriveFolder(
          task?.user?.id,
          task?.name,
          taskSubCategoryStorage?.fileId ||
            taskCategoryStorage?.fileId ||
            taskFinYearStorage?.fileId,
        );
        storage.fileId = folderData.id;
        storage.storageSystem = StorageSystem.GOOGLE;
      } else if (storageType && storageType === StorageSystem.BHARATHCLOUD) {
        storage.filePath = `${
          taskSubCategoryStorage?.filePath ||
          taskCategoryStorage?.filePath ||
          taskFinYearStorage?.filePath
        }/${task?.name}`;
      }

      taskStorage = await Storage.save(storage);
    }
    return taskStorage;
  }
}
