import { generateEmpoyeeId, getTitle } from 'src/utils';
import {
  Connection,
  EntitySubscriberInterface,
  EventSubscriber,
  InsertEvent,
  UpdateEvent,
  getManager,
} from 'typeorm';
import Client from '../modules/clients/entity/client.entity';
import {
  getAdminEmailsBasedOnOrganizationId,
  getAdminIDsBasedOnOrganizationId,
  getAdminIdsWithClientId,
  getAllOrganizationUsersBasedOnClientId,
  getUserDetails,
  getUserNamewithUserId,
  insertINTONotificationUpdate,
  insertINTOnotification,
} from 'src/utils/re-use';
import {
  sendWhatsAppTemplateMessage,
  sendWhatsAppTextMessage,
} from 'src/modules/whatsapp/whatsapp.service';
import ViderWhatsappSessions from 'src/modules/whatsapp/entity/viderWhatsappSessions';
import { sendMailViaAny, sendnewMail } from 'src/emails/newemails';
import * as moment from 'moment';
import GstrRegister from 'src/modules/gstr-register/entity/gstr-register.entity';
import { fullMobileNumberWithCountry } from 'src/utils/validations/fullMobileWithCountry';
import { User } from 'src/modules/users/entities/user.entity';
import { Organization } from 'src/modules/organization/entities/organization.entity';
import notify from 'src/notifications/notify';
// const { Kafka } = require('kafkajs')
// const { generateAuthToken } = require('aws-msk-iam-sasl-signer-js')

// async function oauthBearerTokenProvider(region) {
//    // Uses AWS Default Credentials Provider Chain to fetch credentials
//    const authTokenResponse = await generateAuthToken({ region });
//    return {
//        value: authTokenResponse.token
//    }
// }


let clientOldDetails: Client;
@EventSubscriber()
export class ClientSubscriber implements EntitySubscriberInterface<Client> {
  constructor(private readonly connection: Connection) {
    connection.subscribers.push(this);
  }

  listenTo() {
    return Client;
  }

  async beforeInsert(event: InsertEvent<Client>) {
    let orgId = event?.entity?.organization?.id;
    let count = await this.connection.manager.getRepository(Client).count({
      where: {
        organization: {
          id: orgId,
        },
      },
    });
    let orgName = event?.entity?.organization?.legalName?.slice(0, 3);
    if (orgName) {
      event.entity.clientId = generateEmpoyeeId(orgName, count + 1);
    }
  }

  async afterInsert(event: InsertEvent<Client>) {
    if (event.entity['userId']) {
      const userName = await getUserNamewithUserId(event.entity['userId']);
      const entityManager = getManager();
      const { id: clintId, user, createdBy, displayName, category, subCategory, email, mobileNumber } =
        event.entity;
      let organization = await Organization.findOne({ where: { id: user.organization.id } });
      const clientSmtpMail = organization?.othersSmtp?.[1].auth?.user;
      const { id } = createdBy;
      let fullName;
      if (user) {
        fullName = user.fullName;
      } else {
        fullName = displayName;
      }
      const getuserQuery = `SELECT full_name, organization_id FROM user where id=${id}`;
      const getCreated_name = await entityManager.query(getuserQuery);
      const created_name = getCreated_name[0]?.full_name;
      const orgId = getCreated_name[0]?.organization_id;
      const adminsList = await getAdminIDsBasedOnOrganizationId(orgId);
      const adminMails = await getAdminEmailsBasedOnOrganizationId(orgId);
      const title = 'Client Created';
      const body = `<strong>${created_name}</strong> have created <strong>${fullName}</strong> profile.`;

      // insertINTOnotification(title, body, adminsList, orgId);
      const key = 'CLIENT_CREATED_PUSH';
      insertINTONotificationUpdate(title, body, adminsList, orgId, key, clintId);
      await notify.createClientPush({ created_name, fullName, adminsList })

      const addressParts = [
        organization.buildingNo || '',
        organization.floorNumber || '',
        organization.buildingName || '',
        organization.street || '',
        organization.location || '',
        organization.city || '',
        organization.district || '',
        organization.state || '',
      ].filter((part) => part && part.trim() !== '');
      const pincode =
        organization.pincode && organization.pincode.trim() !== ''
          ? ` - ${organization.pincode}`
          : '';

      const address = addressParts.join(', ') + pincode;

      if (adminsList !== undefined) {
        for (let i = 0; i < adminMails.length; i++) {
          const data = {
            clientName: displayName,
            userName: userName,
            clientCategory: getTitle(category),
            adminName: adminMails[i]?.full_name,
            email: adminMails[i]?.email,
            clientSmtpMail1: clientSmtpMail || '<EMAIL>',
            userId: event.entity['userId'],
            adress: address,
            phoneNumber: organization?.mobileNumber,
            mail: organization?.email,
            legalName: organization?.tradeName || organization?.legalName,
          };
          const jsonData = JSON.stringify(data);
          const mailOptions = {
            id: adminMails[i].id,
            key: 'CLIENT_CREATE_MAIL',
            data: data,
            email: adminMails[i].email,
            filePath: 'client-create',
            subject: 'Client Registration | Vider',
          };
          await sendnewMail(mailOptions);
        }
      }
      try {
        const categoryValue = subCategory !== '' ? `${category} and ${subCategory}` : `${category}`;
        if (adminsList !== undefined) {
          for (let userId of adminsList) {
            const sessionValidation = await ViderWhatsappSessions.findOne({
              where: { userId: userId, status: 'ACTIVE' },
            });
            if (sessionValidation) {
              const adminUserDetails = await getUserDetails(userId);
              const {
                full_name: userFullName,
                mobile_number: userPhoneNumber,
                country_code: countryCode,
              } = adminUserDetails;
              const userWhatsAppNumber = fullMobileNumberWithCountry(userPhoneNumber, countryCode);
              const key = 'CLIENT_CREATED_WHATSAPP';
              const whatsappMessageBody = `
    Hi ${userFullName}
    A new Client has been created in ATOM:
    
    Client name: ${fullName}
    Email address: ${email}
    Phone number: ${mobileNumber}
    Category: ${category}
     ${subCategory ? `*Sub-Category:* ${subCategory}` : ''}
    
    We hope this helps!
    `;
              await sendWhatsAppTextMessage(
                userWhatsAppNumber,
                whatsappMessageBody,
                orgId,
                title,
                userId,
                key,
              );
            }
          }
        }
      } catch (error) {
        console.error('Error sending Client WhatsApp notification:', error);
      }
    }
  }

  async beforeUpdate(event: UpdateEvent<Client>) {
    clientOldDetails = event?.databaseEntity;

  }

  async afterUpdate(event: UpdateEvent<Client>) {
    //   const run = async () => {
    //     const kafka = new Kafka({
    //         clientId: 'my-app',
    //         brokers: [
    //    'b-2-public.vidermskcluster.b3wq8s.c2.kafka.ap-south-1.amazonaws.com:9198', 
    //    'b-1-public.vidermskcluster.b3wq8s.c2.kafka.ap-south-1.amazonaws.com:9198'
    //  ],
    //         ssl: true,
    //         sasl: {
    //             mechanism: 'oauthbearer',
    //             oauthBearerProvider: () => oauthBearerTokenProvider('ap-south-1')
    //         }
    //     })
    //  const admin = kafka.admin();
    //  try {
    //      await admin.createTopics({
    //        topics: [
    //          {
    //           //  topic: 'prod-client-update',
    //             topic: 'client-update',

    //            numPartitions: 3, // Specify the number of partitions for the topic
    //            replicationFactor: 2, // Specify the replication factor (must be <= number of brokers)
    //          },
    //        ],
    //      });
    //    } catch (error) {
    //      console.error(`Error creating topic, error`);
    //    } finally {
    //      await admin.disconnect();
    //    }

    //     const producer = kafka.producer()
    //    //  const consumer = kafka.consumer({ groupId: 'new-group' })
    //     // Producing
    //     await producer.connect()
    //     await producer.send({
    //         topic: 'client-update',
    //         messages: [
    //         {key:'Client Update',value: JSON.stringify({entityDetails: event.entity, clientOldDetails}) }
    //        ]

    //     })

    //  }         

    //  run().catch(console.error)

    const entity_manager = getManager();
    const { displayName, id, id: clientId, } = event.entity;

    const clientNewDetials = event.entity;
    const logInUserId = event?.entity?.['userId'];
    let user = await User.findOne({
      where: {
        id: logInUserId,
      },
    });
    let organizationsmtp = await Organization.findOne({ where: { id: user?.organization?.id } });
    const othersSmtpMail = organizationsmtp?.othersSmtp?.[1].auth?.user;
    const title = 'Client Deleted';
    console.log(clientOldDetails, 'clientOldDetails')
    if (clientOldDetails) {
      const admins_list = await getAdminIdsWithClientId(clientOldDetails?.id);

      if (event.entity.status == 'DELETED') {
        //whatsapp
        try {
          if (admins_list !== undefined) {
            const sessionValidation = await ViderWhatsappSessions.findOne({
              where: { userId: admins_list, status: 'ACTIVE' },
            });
            if (sessionValidation) {
              const adminUserDetails = await getUserDetails(admins_list);

              const { full_name: userFullName, mobile_number: userPhoneNumber, id, organization_id } = adminUserDetails;
              const key = 'CLIENT_DELETED_WHATSAPP';
              const whatsappMessageBody = `
  Hi ${userFullName}
  
  ${clientOldDetails?.displayName} profile have been deleted by {username}
  please restore the same from archives if this was done by mistake!
  
  We hope this helps!`;
              await sendWhatsAppTextMessage(
                `91${userPhoneNumber}`,
                whatsappMessageBody,
                organization_id,
                title,
                id,
                key,
              );
            }

          }
        } catch (error) {
          console.error('Error sending User WhatsApp notification:', error);
        }
      }
      if (event.entity['userId']) {
        const userName = await getUserNamewithUserId(event.entity['userId']);
        if (event.entity.status !== 'DELETED' && id) {
          const orgQuery = `SELECT organization_id FROM client where id = ${id};`;
          const orgIdSql = await entity_manager.query(orgQuery);
          const orgId = orgIdSql[0].organization_id;

          const ignoreKeys = ['inactiveAt', 'createdAt', 'updatedAt'];
          const labelNames = {
            clientManager: 'Client Manager',
            id: 'ID',
            clientId: 'Client ID',
            slug: 'Slug',
            displayName: 'Display Name',
            category: 'Category',
            subCategory: 'Subcategory',
            email: 'Email',
            mobileNumber: 'Mobile Number',
            alternateMobileNumber: 'Alternate Mobile Number',
            authorizedPerson: 'Authorized Person',
            designation: 'Designation',
            gstVerified: 'GST Verified',
            gstNumber: 'GST Number',
            legalName: 'Legal Name',
            tradeName: 'Trade Name',
            placeOfSupply: 'Place of Supply',
            constitutionOfBusiness: 'Constitution of Business',
            panVerified: 'PAN Verified',
            panNumber: 'PAN Number',
            firstName: 'First Name',
            lastName: 'Last Name',
            fullName: 'Full Name',
            buildingName: 'Building Name',
            street: 'Street',
            city: 'City',
            state: 'State',
            pincode: 'Pincode',
            notes: 'Notes',
            dob: 'Date of Birth',
            image: 'Image',
            clientPortalAccess: 'Client Portal Access',
            status: 'Status',
            labels: 'Labels',
            issameaddress: 'Address same as above',
          };

          if (clientOldDetails !== undefined) {
            const differentKeys = Object.keys(clientOldDetails)
              .filter((key) => {
                if (key === 'clientManager' && clientOldDetails[key] && clientNewDetials[key]) {
                  return clientOldDetails[key].id !== clientNewDetials[key].id;
                } else {
                  return (
                    !ignoreKeys.includes(key) &&
                    clientNewDetials.hasOwnProperty(key) &&
                    clientOldDetails[key] !== clientNewDetials[key]
                  );
                }
              })
              .map((key) => ({
                key,
                label: labelNames.hasOwnProperty(key) ? labelNames[key] : key,
              }))
              .map((obj) => obj.label);
            const admins_list = await getAdminIdsWithClientId(id);
            const allOrgUsers = await getAllOrganizationUsersBasedOnClientId(id);
            const listOfUsersIds: any = allOrgUsers.map((obj) => obj.id);
            const addressParts = [
              organizationsmtp.buildingNo || '',
              organizationsmtp.floorNumber || '',
              organizationsmtp.buildingName || '',
              organizationsmtp.street || '',
              organizationsmtp.location || '',
              organizationsmtp.city || '',
              organizationsmtp.district || '',
              organizationsmtp.state || '',
            ].filter((part) => part && part.trim() !== '');
            const pincode =
              organizationsmtp.pincode && organizationsmtp.pincode.trim() !== ''
                ? ` - ${organizationsmtp.pincode}`
                : '';

            const address = addressParts.join(', ') + pincode;
            if (
              event.entity.status !== clientOldDetails['status'] &&
              event.entity.status !== undefined
            ) {
              const title = 'Client Status has been Updated';
              const body = `<b>${userName}</b> have succesfully updated "<b>${clientOldDetails['status']}</b>" to "<b>${event.entity.status}</b>" of "<b>${clientOldDetails['displayName']}</b>" profile`;
              // insertINTOnotification(title, body, admins_list, orgId);
              const key = 'CLIENT_STATUS_HAS_BEEN_UPDATED_PUSH';
              insertINTONotificationUpdate(title, body, listOfUsersIds, orgId, key, clientId);
              //whatsapp
              try {
                if (admins_list !== undefined) {
                  const sessionValidation = await ViderWhatsappSessions.findOne({
                    where: { userId: admins_list, status: 'ACTIVE' },
                  });
                  if (sessionValidation) {
                    const adminUserDetails = await getUserDetails(admins_list);

                    const { full_name: userFullName, mobile_number: userPhoneNumber, id, organization_id } = adminUserDetails;
                    const key = 'CLIENT_STATUS_UPDATED_WHATSAPP';
                    const whatsappMessageBody = `
  Hi ${userFullName}
  
  Status of ${displayName} have been changed to ${event.entity.status} from  by  ${userName}
  please revert the same if this was done by mistake!
  
  We hope this helps!`;
                    await sendWhatsAppTextMessage(
                      `91${userPhoneNumber}`,
                      whatsappMessageBody,
                      organization_id,
                      title,
                      id,
                      key,
                    );
                  }

                }
              } catch (error) {
                console.error('Error sending User WhatsApp notification:', error);
              }
              if (event.entity.status === 'ACTIVE') {
                if (allOrgUsers) {
                  for (let user of allOrgUsers) {
                    await sendnewMail({
                      id: user?.id,
                      key: 'CLIENT_STATUS_UPDATED_MAIL',
                      email: user?.email,
                      data: {
                        userName: user?.fullName,
                        clientName: displayName,
                        activatedBy: userName,
                        activationDate: moment(new Date()).format('DD-MM-YYYY'),
                        userId: event.entity['userId'],
                        legalName: organizationsmtp.legalName,
                        adress: address,
                        phoneNumber: organizationsmtp?.mobileNumber,
                        mail: organizationsmtp?.email,
                      },
                      filePath: 'client-status-update-active',
                      subject: 'Client Status has been Updated',
                    });
                  }
                }
              } else if (event.entity.status === 'INACTIVE') {
                if (allOrgUsers) {
                  for (let user of allOrgUsers) {
                    await sendnewMail({
                      id: user?.id,
                      key: 'CLIENT_STATUS_UPDATED_MAIL',
                      email: user?.email,
                      data: {
                        userName: user?.fullName,
                        clientName: displayName,
                        legalName: event.entity?.organization?.legalName ? event.entity?.organization?.legalName : organizationsmtp.legalName,
                        deactivatedBy: userName,
                        deactivationDate: moment(new Date()).format('DD-MM-YYYY'),
                        userId: event.entity['userId'],
                        adress: address,
                        phoneNumber: organizationsmtp?.mobileNumber,
                        mail: organizationsmtp?.email,
                      },
                      filePath: 'client-status-update-inactive',
                      subject: 'Client Status has been Updated',
                    });
                  }
                }
              }
            } else if (event.entity.gstNumber !== clientOldDetails['gstNumber']) {
              const title = 'Client profile Updated';
              const body = `<b>${userName}</b> have succesfully updated organization gst of "<b>${clientOldDetails['displayName']}</b>" profile`;
              //insertINTOnotification(title, body, admins_list);
            } else {
              const title = 'Client Profile Updation';
              //const body = `<b>${userName}</b> have succesfully updated "<b>${differentKeys}</b>" of "<b>${displayName}</b>" profile`;
              const body = `<b>${userName}</b> have succesfully updated "<b>${displayName}</b>" profile`;
              // insertINTOnotification(title, body, admins_list, orgId);
              const key = 'CLIENT_PROFILE_UPDATION_PUSH';
              insertINTONotificationUpdate(title, body, admins_list, orgId, key, clientId);
              //whatsapp
              try {
                if (admins_list !== undefined) {
                  const sessionValidation = await ViderWhatsappSessions.findOne({
                    where: { userId: admins_list, status: 'ACTIVE' },
                  });
                  if (sessionValidation) {
                    const adminUserDetails = await getUserDetails(admins_list);

                    const { full_name: userFullName, mobile_number: userPhoneNumber, id, organization_id } = adminUserDetails;
                    const key = 'CLIENT_PROFILE_UPDATION_WHATSAPP';
                    const whatsappMessageBody = `
       Hi ${userFullName}
      ${displayName} profile has been successfully updated by ${userName}
      
       We hope this helps!`;
                    await sendWhatsAppTextMessage(
                      `91${userPhoneNumber}`,
                      whatsappMessageBody,
                      organization_id,
                      title,
                      id,
                      key,
                    );
                  }

                }
              } catch (error) {
                console.error('Error sending User WhatsApp notification:', error);
              }
            }
          }
        }
      }
      if (event.entity['userId']) {
        if (id) {
          const orgQuery = `SELECT organization_id FROM client where id = ${id};`;
          const orgIdSql = await entity_manager.query(orgQuery);
          const orgId = orgIdSql[0].organization_id;

          const clientName = event.entity.displayName;
          const { organization, user } = event.entity;
          if (event.entity.clientPortalAccess === true &&
            clientOldDetails?.clientPortalAccess !== true) {
            const title = 'client portal  access';
            // const userPhoneNumber = `91${event.entity.mobileNumber}`;
            const userPhoneNumber = fullMobileNumberWithCountry(
              event.entity.mobileNumber,
              event.entity?.countryCode,
            );
            const templateName = 'clientportalaccess';
            const whatsappOptions = {
              to: userPhoneNumber,
              orgId,
              title,
              name: templateName,
              header: [
                {
                  type: 'text',
                  text: event.entity.displayName,
                },
              ],
              body: [],
            };
            await sendWhatsAppTemplateMessage(whatsappOptions);
          }
        }
      }
      //Add Client To GST Return Tracker
      if (!clientOldDetails?.gstVerified && clientNewDetials?.gstVerified) {
        let client = await Client.findOne({
          where: {
            id: clientOldDetails.id,
          },
          relations: ['organization'],
        });
        const existingRecord = await GstrRegister.findOne({
          where: {
            client: client,
          },
        });
        if (!existingRecord) {
          let gstrRegister = new GstrRegister();
          gstrRegister.client = clientOldDetails;
          gstrRegister.registrationType = event.entity.registrationType;
          gstrRegister.organization = client.organization;
          // await gstrRegister.save();
        }
      }
    }
  }
}
