import { MigrationInterface, QueryRunner } from 'typeorm';

export class AlterInvitedUserTable1663320563060 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
        ALTER TABLE invited_users
        RENAME TO invited_user,
        ADD COLUMN status enum('PENDING', 'CANCELLED', 'JOINED') default 'PENDING' not null;
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}
