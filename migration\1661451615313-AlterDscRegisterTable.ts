import { MigrationInterface, QueryRunner } from 'typeorm';

export class AlterDscRegisterTable1661451615313 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE dsc_register
       DROP FOREIGN KEY FK_029691aada7f20bd91d8c1ced29,
       ADD FOREIGN KEY (client_id) REFERENCES client(id) ON DELETE CASCADE;
    `,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}
