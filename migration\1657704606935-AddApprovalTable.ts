import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddApprovalTable1657704606935 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `create table approval (
            id int not null auto_increment,
            role_id int null,
            user_id int null,
            comment text null,
            status enum('PENDING','APPROVED') not null,
            task_id int null,
            ipro_id int null,
            created_at datetime(6) default current_timestamp(6),
            updated_at datetime(6) default current_timestamp(6),
            primary key (id)
        );`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}
