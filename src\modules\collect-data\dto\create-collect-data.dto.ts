import { IsNotEmpty, IsOptional } from 'class-validator';

class CreateCollectDataDto {
  @IsNotEmpty()
  appName: string;

  @IsNotEmpty()
  name: string;

  @IsOptional()
  notes: string;

  @IsOptional()
  id: number;

  @IsOptional()
  client: number;

  @IsOptional()
  clientGroup: number;

  @IsNotEmpty()
  task: number;

  @IsOptional()
  fields: any[];

  @IsOptional()
  confirmDocuments: string[];

  @IsOptional()
  whatsappCheck: boolean;

  @IsOptional()
  emailCheck: boolean;

}

export default CreateCollectDataDto;
