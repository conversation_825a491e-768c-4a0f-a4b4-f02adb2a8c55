import {
  Body,
  Controller,
  Get,
  Param,
  ParseIntPipe,
  Post,
  Put,
  Query,
  Req,
  UploadedFile,
  UseGuards,
  UseInterceptors,
  Request,
  Delete,
} from '@nestjs/common';
import { JwtAuthGuard } from 'src/modules/users/jwt/jwt-auth.guard';
import { IncomeTaxConfigService } from '../services/config.services';
import { FileInterceptor } from '@nestjs/platform-express';
import GetIncometaxClientDto from '../dto/get-incometax-client.dto';
import { CronAuthGuard } from 'src/cron-auth/api-key-auth.guard';

@Controller('incometax-config')
export class IncomeTaxConfigController {
  constructor(private service: IncomeTaxConfigService) {}

  @UseGuards(JwtAuthGuard)
  @Get('atom-client/:id')
  async incomeTaxClient(@Request() req: any, @Param('id', ParseIntPipe) id: number) {
    const { userId } = req.user;
    return this.service.incomeTaxAtomClient(userId, id);
  }

  @UseGuards(JwtAuthGuard)
  @Put('disableIncomeTaxClient')
  disableIncomeTaxClient(@Body() body: any, @Req() req: any) {
    const { userId } = req.user;
    return this.service.disableIncomeTaxClient(userId, body);
  }

  @UseGuards(JwtAuthGuard)
  @Put('disableIncomeTax/:id')
  disableIncomeTaxSingleClient(@Req() req: any, @Param('id', ParseIntPipe) id: number) {
    const { userId } = req.user;
    return this.service.disableIncomeTaxSingleClient(id, userId);
  }

  @UseGuards(JwtAuthGuard)
  @Get('deletedClients')
  getDeletedIncomeTaxClients(@Req() req: any, @Query() query: any) {
    const { userId } = req.user;
    return this.service.getDeletedIncomeTaxClients(userId, query);
  }
  @UseGuards(JwtAuthGuard)
  @Post('/exportdeletedClients')
  async exportuserUpcomingLeaderTasks(@Req() req: any, @Body() body: any) {
    const query = body;
    const { userId } = req.user;
    return this.service.exportuserUpcomingLeaderTasks(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Put('enableIncomeTax/:id')
  enableIncometaxClient(@Req() req: any, @Param('id', ParseIntPipe) id: number) {
    const { userId } = req.user;
    return this.service.enableIncometaxClient(id, userId);
  }

  @UseGuards(JwtAuthGuard)
  @Post('enableIncomeTax/bulk-restore')
  enableBulkIncometaxClient(@Req() req: any, @Body() body: any) {
    const { userId } = req.user;
    return this.service.enableBulkIncometaxClient(userId, body);
  }

  @UseGuards(JwtAuthGuard)
  @Get('bulkSyncStatus')
  getBulkSyncStatus(@Req() req: any) {
    const { userId } = req.user;
    return this.service.getBulkSyncStatus(userId);
  }

  @UseGuards(JwtAuthGuard)
  @Put('enableStatus')
  enableStatus(@Req() req: any) {
    const { userId } = req.user;
    return this.service.updateEnableStatus(userId);
  }

  @UseGuards(JwtAuthGuard)
  @Put('disableStatus')
  updateDisableStatus(@Req() req: any) {
    const { userId } = req.user;
    return this.service.updateDisableStatus(userId);
  }

  @UseGuards(JwtAuthGuard)
  @Post('scheduling')
  async organizationScheduling(@Req() req: any, @Body() body: any) {
    const { userId } = req.user;
    return this.service.organizationScheduling(userId, body);
  }

  @UseGuards(JwtAuthGuard)
  @Get('export-credentials')
  exportAtomProCredentilas(@Req() req: any) {
    const { userId } = req.user;
    return this.service.exportAtomProCredentilas(userId);
  }

  @UseGuards(JwtAuthGuard)
  @Post('/bulkupdate-credentials')
  @UseInterceptors(FileInterceptor('file'))
  bulkUpdateCredentials(@UploadedFile() file: Express.Multer.File, @Request() req: any) {
    const { userId } = req.user;
    return this.service.bulkUpdateCredentials(userId, file);
  }

  @UseGuards(JwtAuthGuard)
  @Get('incomeTaxClientTasks')
  getIncomeTaxClientTasks(@Req() req: any, @Query() query: GetIncometaxClientDto) {
    const { userId } = req.user;
    return this.service.getIncometaxClientTasks(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Post('fyaItem')
  createFyaItem(@Req() req: any, @Body() body: any) {
    const { userId } = req.user;
    return this.service.createFyaItem(userId, body);
  }

  @UseGuards(JwtAuthGuard)
  @Put('fyaItem')
  updateFyaItem(@Req() req: any, @Body() body: any) {
    const { userId } = req.user;
    return this.service.updateFyaItem(userId, body);
  }

  @UseGuards(JwtAuthGuard)
  @Delete('fyaItem/:id')
  deleteFyaItem(@Req() req: any, @Param('id', ParseIntPipe) id: number) {
    const { userId } = req.user;
    return this.service.deleteFyaItem(userId, id);
  }

  @UseGuards(CronAuthGuard)
  @Get('/exp-org')
  async getExpiringOrganizations() {
    return this.service.getExpiringOrganizations();
  }

  @UseGuards(JwtAuthGuard)
  @Post('fyaNotice')
  createFyaNotice(@Req() req: any, @Body() body: any) {
    const { userId } = req.user;
    return this.service.createFyaNotice(userId, body);
  }

  @UseGuards(JwtAuthGuard)
  @Post('manualDueDate')
  manualDueDate(@Req() req: any, @Body() body: any) {
    const { userId } = req.user;
    return this.service.manualDueDate(userId, body);
  }

  @UseGuards(JwtAuthGuard)
  @Put('fyaNotice')
  updateFyaNotice(@Req() req: any, @Body() body: any) {
    const { userId } = req.user;
    return this.service.updateFyaNotice(userId, body);
  }

  @UseGuards(JwtAuthGuard)
  @Delete('fyaNotice/:id')
  deleteFyaNotice(@Req() req: any, @Param('id', ParseIntPipe) id: number) {
    const { userId } = req.user;
    return this.service.deleteFyaNotice(userId, id);
  }
  @UseGuards(JwtAuthGuard)
  @Post('/export-errors')
  async exportErrors(@Req() req: any, @Body() body: any) {
    const { userId } = req.user;
    return this.service.exportErrorsReport(userId, body);
  }

  @UseGuards(JwtAuthGuard)
  @Post('noticeResponse')
  createNoticeResponse(@Req() req: any, @Body() body: any) {
    const { userId } = req.user;
    return this.service.createNoticeResponse(userId, body);
  }

  @UseGuards(JwtAuthGuard)
  @Put('noticeResponse')
  updateNoticeResponse(@Req() req: any, @Body() body: any) {
    const { userId } = req.user;
    return this.service.updateNoticeResponse(userId, body);
  }

  @UseGuards(JwtAuthGuard)
  @Delete('deleteResponse/:id')
  deleteNoticeResponse(@Req() req: any, @Param('id', ParseIntPipe) id: number) {
    const { userId } = req.user;
    return this.service.deleteNoticeResponse(userId, id);
  }
}
