import {
    BaseEntity,
    Column,
    CreateDateColumn,
    Entity,
    ManyToOne,
    PrimaryGeneratedColumn,
    UpdateDateColumn,
} from 'typeorm';
//   import Client from './client.entity';

@Entity()
class QtmTemplateSubcat extends BaseEntity {
    @PrimaryGeneratedColumn()
    id: number;

    @Column()
    name: string;

    @Column()
    catId: number;

    @Column()
    updatedBy: string;

    @CreateDateColumn()
    createdAt: string;

}

export default QtmTemplateSubcat;