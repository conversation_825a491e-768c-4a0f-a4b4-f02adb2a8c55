import { BadRequestException, Injectable, UnprocessableEntityException } from '@nestjs/common';
import { User } from '../../users/entities/user.entity';
import { AddBankAccountDto, BankAccountDto } from '../dto/add-bank-account.dto';
import { GetBankAccountsDto } from '../dto/get-bank-accounts.dto';
import { BankAccount } from '../entities/bank-account.entity';
import { BillingEntity } from '../entities/billing-entity.entity';
import Storage, { StorageSystem } from 'src/modules/storage/storage.entity';
import { StorageService } from 'src/modules/storage/storage.service';
import { OneDriveStorageService } from 'src/modules/ondrive-storage/onedrive-storage.service';
import { AwsService } from 'src/modules/storage/upload.service';
import { BharathStorageService } from 'src/modules/storage/bharath-storage.service';
import { createQueryBuilder } from 'typeorm';
import { GoogleDriveStorageService } from 'src/modules/ondrive-storage/googledrive-storage.service';

@Injectable()
export class BankAccountsService {
  constructor(private storageService: StorageService,
    private oneDriveService: OneDriveStorageService,
    private googleDriveService: GoogleDriveStorageService,
    private awsService: AwsService,
    private bharathService: BharathStorageService
  ) { }
  async create(userId: number, body: AddBankAccountDto) {
    let billingEntity = await BillingEntity.findOne({
      where: { id: body.billingEntityId },
      relations: ['organization'],
    });
    let BEDuplicate = await BankAccount.find({ where: { accountNumber: body.data.accountNumber, billingEntity: billingEntity } });
    if (BEDuplicate.length) {
      throw new UnprocessableEntityException('Account Number is Already to this Billing Entity');
    };


    let user = await User.findOne({
      where: { id: userId },
      relations: ['organization'],
    });

    let data = body.data;

    let bankAccount = new BankAccount();
    if (body.billingEntityId) {
      let existingbankAccount = await BankAccount.find({ where: { billingEntity: body.billingEntityId } });
      if (!existingbankAccount.length) {
        bankAccount.default = true;
      };

      bankAccount.organization = null;
      bankAccount.billingEntity = billingEntity;
    } else {
      const orgDuplicate = await BankAccount.find({ where: { accountNumber: data.accountNumber, organization: user.organization } });
      if (orgDuplicate.length) {
        throw new UnprocessableEntityException('Account Number is Already to this Organisation');
      }
    }
    bankAccount.bankName = data.bankName;
    bankAccount.branchName = data.branchName;
    bankAccount.accountNumber = data.accountNumber;
    bankAccount.ifscCode = data.ifscCode;
    bankAccount.upiId = data.upiId;
    bankAccount.accountName = data.accountName;
    bankAccount.accountType = data.accountType;

    let storage: Storage;
    if (data?.upiStorage) {
      if (bankAccount?.upiStorage?.id) {
        storage = await Storage.findOne({ where: { id: bankAccount?.upiStorage?.id } });
        storage.fileType = data.upiStorage.fileType;
        storage.fileSize = data.upiStorage.fileSize;
        storage.name = data.upiStorage.name;
        storage.file = data.upiStorage.upload;
        storage.show = false;
        storage.storageSystem = data.upiStorage?.storageSystem
        storage.webUrl = data.upiStorage.webUrl;
        storage.downloadUrl = data.upiStorage.downloadUrl;
        storage.fileId = data.upiStorage.fileId;
        storage.authId = user.organization.id;
        storage.filePath = data.upiStorage.filePath;
        // bankAccount.upiStorage = storage;
      } else {
        storage = await this.storageService.addAttachements(userId, { ...data.upiStorage, show: false });
        // bankAccount.upiStorage = storage;
      }
    };
    bankAccount.upiAttachment = data.upiAttachment;
    bankAccount.organization = user.organization;
    const account = await bankAccount.save();
    if (storage) {
      storage.upiStorage = account;
      await storage.save();
    }

    return bankAccount;

  }

  async get(userId: number, query: GetBankAccountsDto) {

    // try {
    //   const response = await fetch('https://1drv.ms/i/s!AC2IzXpgtKI3sYlw');
    //   if (response.ok) {
    //     const finalUrl = response.url;
    //     console.log(finalUrl);
    //     return finalUrl;
    //   } else {
    //     console.error('Failed to fetch redirect URL');
    //   }
    // } catch (error) {
    //   console.error('Error fetching redirect URL:', error);
    // }


    // fetchRedirectUrl();


    if (query.billingEntityId !== undefined) {
      let billingEntityId = query.billingEntityId;
      let billingEntity = await BillingEntity.findOne({
        where: { id: billingEntityId },
      });

      if (!billingEntity) {
        // Handle the case where billingEntity is not found
        // You can throw an error or return a default value
        return []; // Default to an empty array or another suitable default value
      }

      let result = await BankAccount.find({
        where: { billingEntity: { id: billingEntity.id } }, relations: ['upiStorage']
      });
      // let result = await createQueryBuilder(BankAccount, 'bankAccount')
      //   .leftJoinAndSelect('bankAccount.upiStorage', 'upiStorage')
      //   .leftJoin('bankAccount.billingEntity', 'billingEntity')
      //   .where('billingEntity.id=:be', { be: billingEntity.id })
      //   .getMany()

      // console.log(result);

      return result;
    }

    let user = await User.findOne({
      where: { id: userId },
      relations: ['organization'],
    });

    if (!user || !user.organization) {
      // Handle the case where user or user's organization is not found
      // You can throw an error or return a default value
      return []; // Default to an empty array or another suitable default value
    }

    let result = await BankAccount.find({
      where: { organization: { id: user.organization.id } },
    });

    return result;
  }

  async update(id: number, userId: number, data: BankAccountDto) {
    let bankAccount = await BankAccount.findOne({ where: { id }, relations: ['organization', 'billingEntity', 'upiStorage', 'billingEntity.organization'] });

    if (data.accountNumber !== bankAccount.accountNumber) {
      if (bankAccount.billingEntity) {
        let BEDuplicate = await BankAccount.find({ where: { accountNumber: data.accountNumber, billingEntity: bankAccount.billingEntity } });
        if (BEDuplicate.length) {
          throw new UnprocessableEntityException('Account Number is Already to this Billing Entity');
        }
      }
      if (bankAccount.organization) {
        const orgDuplicate = await BankAccount.find({ where: { accountNumber: data.accountNumber, organization: bankAccount.organization } });
        if (orgDuplicate.length) {
          throw new UnprocessableEntityException('Account Number is Already to this Organisation');
        }
      }
    }

    let storage: Storage;
    if (data?.upiStorage) {
      if (bankAccount?.upiStorage?.id) {
        if (data.upiStorage.name !== bankAccount?.upiStorage?.name) {
          const oldOneDriveFileId = bankAccount?.upiStorage?.fileId;
          if (data.upiStorage.storageSystem === StorageSystem.MICROSOFT) {
            await this.oneDriveService.deleteOneDriveFile(userId, oldOneDriveFileId);
          } else if (data.upiStorage.storageSystem === StorageSystem.GOOGLE) {
            await this.googleDriveService.deleteGoogleDriveFile(userId, oldOneDriveFileId);
          } else if (data.upiStorage.storageSystem === StorageSystem.AMAZON) {
            await this.awsService.deleteFile(bankAccount?.upiStorage?.file)
          } else if (data.upiStorage.storageSystem === StorageSystem.BHARATHCLOUD) {
            await this.bharathService.deleteB3File(userId, bankAccount?.upiStorage?.file)
          }
        }

        storage = await Storage.findOne({ where: { id: bankAccount?.upiStorage.id } });
        storage.fileType = data?.upiStorage.fileType;
        storage.fileSize = data?.upiStorage.fileSize;
        storage.name = data?.upiStorage.name;
        storage.file = data?.upiStorage.upload;
        storage.show = data?.upiStorage.show;
        storage.storageSystem = data?.upiStorage?.storageSystem;
        storage.webUrl = data.upiStorage.webUrl;
        storage.downloadUrl = data.upiStorage.downloadUrl;
        storage.fileId = data.upiStorage.fileId;
        storage.authId = bankAccount.billingEntity.organization.id;
        // bankAccount.upiStorage = storage;
      } else {
        storage = await this.storageService.addAttachements(userId, data?.upiStorage);
        // bankAccount.upiStorage = storage;
      }
    } else {
      if (bankAccount?.upiStorage?.id) {
        const existingStorage = await Storage.findOne({ where: { id: bankAccount?.upiStorage?.id } });
        const removeStorage = await existingStorage.remove();
        if (removeStorage) {
          if (removeStorage.storageSystem === StorageSystem.MICROSOFT) {
            this.oneDriveService.deleteOneDriveFile(userId, removeStorage.fileId);
          } else if (removeStorage.storageSystem === StorageSystem.GOOGLE) {
            this.googleDriveService.deleteGoogleDriveFile(userId, removeStorage.fileId);
          } else if (removeStorage.storageSystem === StorageSystem.AMAZON) {
            this.storageService.deleteAwsFile(removeStorage.file);
          } else if (removeStorage.storageSystem === StorageSystem.BHARATHCLOUD) {
            this.bharathService.deleteB3File(userId, removeStorage.file)
          }
        };
        bankAccount.upiStorage = null;
      }
    }
    bankAccount.bankName = data.bankName;
    bankAccount.branchName = data.branchName;
    bankAccount.accountNumber = data.accountNumber;
    bankAccount.ifscCode = data.ifscCode;
    bankAccount.upiId = data.upiId;
    bankAccount.accountName = data.accountName;
    bankAccount.accountType = data.accountType;
    bankAccount.upiAttachment = data.upiAttachment;
    const ba = await bankAccount.save();
    if (storage) {
      storage.upiStorage = ba;
      await storage.save();
    }

    return bankAccount;
  }

  async default(id: number, body: BankAccountDto) {
    let defaultBankAccount = await BankAccount.findOne({ where: { billingEntity: body.billingEntityId, default: true } });
    if (defaultBankAccount) {
      defaultBankAccount.default = false;
      await defaultBankAccount.save();
    }

    let bankAccount = await BankAccount.findOne({ where: { id } });
    bankAccount.default = true;
    await bankAccount.save();
  }

  async delete(userId: number, id: number) {
    let bankAccount = await BankAccount.findOne({ where: { id }, relations: ['upiStorage'] });
    const deleteBankAccount = await bankAccount.remove();
    if (deleteBankAccount?.upiStorage) {
      if (deleteBankAccount.upiStorage?.storageSystem == StorageSystem.MICROSOFT) {
        const fileId = deleteBankAccount.upiStorage.fileId;
        await this.oneDriveService.deleteOneDriveFile(userId, fileId);
      } else if (deleteBankAccount.upiStorage?.storageSystem == StorageSystem.AMAZON) {
        this.awsService.deleteFile(deleteBankAccount.upiStorage.file);
      } else if (deleteBankAccount.upiStorage?.storageSystem == StorageSystem.BHARATHCLOUD) {
        this.bharathService.deleteB3File(userId, deleteBankAccount.upiStorage.file)
      }
    }
    return deleteBankAccount;
  }
}
