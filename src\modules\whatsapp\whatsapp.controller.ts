import { Body, Controller, Get, Post, Query, Req, UseGuards } from '@nestjs/common';
import { WhatsappService, buildBodyAndSendMessage, sendWhatsAppMarketingTextMessage, sendWhatsAppTemplateMessage,sendWhatsAppTemplateMessageMarketing } from './whatsapp.service';

import { Cron, CronExpression } from '@nestjs/schedule';
import { getAdminIDsBasedOnOrganizationId, getUserDetails } from 'src/utils/re-use';
import ViderWhatsappSessions from './entity/viderWhatsappSessions';
import * as moment from 'moment';
import { User, UserType } from '../users/entities/user.entity';
import NotificationPreferences from '../notification-settings/notifications-preferences.entity';
import { Organization } from '../organization/entities/organization.entity';
import OrganizationPreferences from '../organization-preferences/entity/organization-preferences.entity';
import DscRegister from '../dsc-register/entity/dsc-register.entity';
import { createQueryBuilder } from 'typeorm';
import Task from '../tasks/entity/task.entity';
import Event from '../events/event.entity';
import CronActivity from '../cron-activity/cron-activity.entity';
import { JwtAuthGuard } from '../users/jwt/jwt-auth.guard';
import { get } from 'lodash';
import ViderMarketing from '../webhook-marketing/entity/vider-marketing.entity';
export enum UserStatus {
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE',
  DELETED = 'DELETED',
}
@Controller('whatsapp')
export class WhatsappController {
  constructor(private readonly whatsappService: WhatsappService) {}
  @Cron(CronExpression.EVERY_DAY_AT_3AM)
  async sendDailyWelcomeMessage() {
    if(process.env.WEBSITE_URL === 'https://atom.vider.in'&& process.env.Cron_Running === 'true'){

    const cronData = new CronActivity();
    cronData.cronType = "SEND DAILY WELCOME MESSAGE";
    cronData.cronDate = moment().toDate().toString();
    cronData.startTime = moment().format("YYYY-MM-DD HH:mm:ss");
    const cornActivityID = await cronData.save();

    try {  
      // const allOrganizations = await Organization.find();
       const organisationActiveList = await Organization.createQueryBuilder('organization')
                  .select(['organization.id', 'user.id', 'user.fullName', 'user.email'])
                  .leftJoin('organization.users', 'user')
                  .where(
                    "DATE_FORMAT(STR_TO_DATE(JSON_UNQUOTE(JSON_EXTRACT(config, '$.expirydate')), '%Y-%m-%d'), '%Y-%m-%d') >= :expirydate",
                    { expirydate: moment().format('YYYY-MM-DD') },
                  )
                  .andWhere('user.status = :status', { status: 'active' })
                  .andWhere('user.type = :type', { type: UserType.ORGANIZATION })
                  .getMany();
      for (const organization of organisationActiveList) {
        const organizationPreferences: any = await OrganizationPreferences.findOne({
          where: { organization: organization?.id },
        });
        const whatsappCheck = organizationPreferences?.notificationConfig?.whatsappPreferences;
        if (whatsappCheck) {
          const userDetails = await User.find({
            where: { organization: organization?.id, status: UserStatus.ACTIVE, type: UserType.ORGANIZATION },
          });

          for (const user of userDetails) {
            const checkWhatsAppPreference = await NotificationPreferences.findOne({
              where: { user: user?.id },
            });
            const { fullName, mobileNumber, id } = user;
            if (checkWhatsAppPreference?.whatsappConfig) {
              const checkUser = await ViderWhatsappSessions.findOne({
                where: { userId: id },
              });

              if (checkUser) {
                if (checkUser.isSent) {
                  if (checkUser.status === 'INACTIVE') {
                    const statusUpdatedTime: Date = new Date(checkUser.statusUpdatedTimestamp);
                    const currentTime: Date = new Date();

                    // Calculate the time difference in milliseconds
                    const timeDifference: number =
                      currentTime.getTime() - statusUpdatedTime.getTime();
                    const timeDifferenceInMinutes: number = timeDifference / 60000;
                    if (timeDifferenceInMinutes >= 30) {
                      const whatsappOptions = {
                        title: 'everyday message',
                        userId: id,
                        orgId: organization?.id,
                        to: `91${mobileNumber}`,
                        name: 'every_day_morning_message',
                        header: [
                          {
                            type: 'text',
                            text: fullName,
                          },
                        ],
                      };
                      await this.whatsappService.sendWhatsAppTemplateMessage(whatsappOptions);
                    }
                  } else {
                    const statusUpdatedTime: Date = new Date(checkUser.statusUpdatedTimestamp);
                    const currentTime: Date = new Date();

                    // Calculate the time difference in milliseconds
                    const timeDifference: number =
                      currentTime.getTime() - statusUpdatedTime.getTime();
                    const timeDifferenceInMinutes: number = timeDifference / 60000;

                    if (timeDifferenceInMinutes >= 30) {
                      const whatsappOptions = {
                        title: 'everyday message',
                        userId: id,
                        orgId: organization?.id,
                        to: `91${mobileNumber}`,
                        name: 'every_day_morning_message',
                        header: [
                          {
                            type: 'text',
                            text: fullName,
                          },
                        ],
                      };
                      await this.whatsappService.sendWhatsAppTemplateMessage(whatsappOptions);
                    }
                  }
                }
              } else {
                const whatsappOptions = {
                  title: 'everyday message',
                  userId: id,
                  orgId: organization?.id,
                  to: `91${mobileNumber}`,
                  name: 'every_day_morning_message',
                  header: [
                    {
                      type: 'text',
                      text: fullName,
                    },
                  ],
                };
                await this.whatsappService.sendWhatsAppTemplateMessage(whatsappOptions);
              }
            }
          }
        }
      }
    } catch (error) {
      const getcornActivityID = await createQueryBuilder(CronActivity,'cronActivity')
        .where('id = :id',{ id: cornActivityID.id })
        .getOne();
      getcornActivityID.responseData = error.message;
      getcornActivityID.endTime = moment().format("YYYY-MM-DD HH:mm:ss");
      await getcornActivityID.save();
      return console.log(error.message);
    }

    const getcornActivityID = await createQueryBuilder(CronActivity,'cronActivity')
      .where('id = :id',{ id: cornActivityID.id })
      .getOne();
    getcornActivityID.responseData = "Success";
    getcornActivityID.endTime = moment().format("YYYY-MM-DD HH:mm:ss");
    await getcornActivityID.save();
  }
}

  @Cron(CronExpression.EVERY_DAY_AT_2AM)
  async checkActiveSessionUsersAndSendMessage() {
    if(process.env.WEBSITE_URL === 'https://atom.vider.in' && process.env.Cron_Running === 'true'){

    const cronData = new CronActivity();
    cronData.cronType = "CHECK ACTIVE SESSION USERS";
    cronData.cronDate = moment().toDate().toString();
    cronData.startTime = moment().format("YYYY-MM-DD HH:mm:ss");
    const cornActivityID = await cronData.save();

    const sessionUsers = await ViderWhatsappSessions.find({
      where: { status: UserStatus.ACTIVE },
    });

    try {
      for (const user of sessionUsers) {
        const { userId, organizationId } = user;
        await buildBodyAndSendMessage(userId, organizationId);
      }        
    } catch (error) {
      const getcornActivityID = await createQueryBuilder(CronActivity,'cronActivity')
        .where('id = :id',{ id: cornActivityID.id })
        .getOne();
      getcornActivityID.responseData = error.message;
      getcornActivityID.endTime = moment().format("YYYY-MM-DD HH:mm:ss");
      await getcornActivityID.save();
      return console.log(error.message);
    }

    const getcornActivityID = await createQueryBuilder(CronActivity,'cronActivity')
      .where('id = :id',{ id: cornActivityID.id })
      .getOne();
    getcornActivityID.responseData = "Success";
    getcornActivityID.endTime = moment().format("YYYY-MM-DD HH:mm:ss");
    await getcornActivityID.save();
  }
}

// @Cron(CronExpression.EVERY_5_MINUTES)

async sendMarketingMessage() {
  const marketingUsers = await ViderMarketing.find({
    where: { status: 'pending' } // Assuming you track pending messages
});

  for (const user of marketingUsers) {
    
    const whatsappOptions:any = {
      title: 'marketing message',
      userId: 702,
      orgId: 136,
      // to: `91${user.mobileNumber}`,
            to: user.mobileNumber,

      name: 'ugadi_template',
      header: [
        {
          type: 'image',
          // link: 'https://jss-vider.s3.ap-south-1.amazonaws.com/header-image.jpeg',
          // link:'https://jss-vider.s3.ap-south-1.amazonaws.com/south-indian-image.jpg',
          link:'https://jss-vider.s3.ap-south-1.amazonaws.com/ugadi+template+image.jpg'
        },
      ],
    };
    await sendWhatsAppTemplateMessageMarketing(whatsappOptions);
    // await new Promise(resolve => setTimeout(resolve, 120000));

    // await ViderMarketing.update(
    //   { mobileNumber: user.mobileNumber }, // Find by phone number
    //   { status: 'sent' } // Only update the status
    // );

  }
}

// @Cron(CronExpression.EVERY_30_MINUTES)

async sendWebinarMarketingMessage() {
  const marketingUsers = await ViderMarketing.find({
    where: { status: 'pending' } // Assuming you track pending messages
});
  const userPhoneNumbers = [6302843241, 9492492970];

  for (const user of marketingUsers) {
    
    const whatsappOptions:any = {
      title: 'webinar marketing message',
      userId: 702,
      orgId: 136,
      // to: `91${user.mobileNumber}`,
            to: user.mobileNumber,

      name: 'webinar',
      header: [
        {
          type: 'image',
          // link: 'https://jss-vider.s3.ap-south-1.amazonaws.com/header-image.jpeg',
          link:'https://jss-vider.s3.ap-south-1.amazonaws.com/Webinar.jpg',
        },
      ],
    };
    await sendWhatsAppTemplateMessageMarketing(whatsappOptions);
    // await new Promise(resolve => setTimeout(resolve, 120000));

    // await ViderMarketing.update(
    //   { mobileNumber: user.mobileNumber }, // Find by phone number
    //   { status: 'sent' } // Only update the status
    // );

  }
}

  async sendTextMessage() {
    const session = new ViderWhatsappSessions();
    session.userId = 702;
    session.organizationId = 136;
    await session.save();
    // return this.whatsappService.sendWhatsAppTextMessage('919491679465', "text message from cron");
  }


@Cron("0 0 03 * * 1")
async sendWeeklyOverview() {
  if(process.env.WEBSITE_URL === 'https://atom.vider.in' && process.env.Cron_Running === 'true'){
  try {
    const statutoryComplaianceEvents = await Event.find({
      where: { defaultOne: true, date: moment().format('YYYY-MM-DD') },
    });
    const organisationActiveList = await Organization.createQueryBuilder('organization')
      .select(['organization.id', 'user.id', 'user.fullName', 'user.email', 'user.mobileNumber'])
      .leftJoin('organization.users', 'user')
      .where(
        "DATE_FORMAT(STR_TO_DATE(JSON_UNQUOTE(JSON_EXTRACT(config, '$.expirydate')), '%Y-%m-%d'), '%Y-%m-%d') >= :expirydate",
        { expirydate: moment().format('YYYY-MM-DD') },
      )
      .andWhere('user.status = :status', { status: 'active' })
      .andWhere('user.type = :type', { type : UserType.ORGANIZATION })
      .getMany();

    for (let organization of organisationActiveList) {
      try {
        const organizationPreferences: any = await OrganizationPreferences.findOne({
          where: { organization: organization.id },
        });
        const whatsappCheck = organizationPreferences?.notificationConfig?.whatsappPreferences;

        if (!organizationPreferences) {
          continue; // Skip processing if organization preferences not found
        }

        if (!whatsappCheck) {
          continue; // Skip processing if WhatsApp preferences are not enabled
        }

        const getDSCExpiry = await createQueryBuilder(DscRegister, 'dscRegister')
          .leftJoin('dscRegister.organization', 'organization')
          .where('organization.id = :id', { id: organization.id })
          .andWhere('dscRegister.expiryDate between :start and :end', {
            start: moment().isoWeekday(1).format('YYYY-MM-DD'),
            end: moment().isoWeekday(7).format('YYYY-MM-DD'),
          })
          .getMany();

        for (let user of organization.users) {


          const checkWhatsAppPreference = await NotificationPreferences.findOne({
            where: { user: user?.id },
          });

          if (checkWhatsAppPreference?.whatsappConfig) {

            try {
              const getDueTasksCount = await createQueryBuilder(Task, 'task')
                .leftJoin('task.members', 'user')
                .where('user.id = :id', { id: user.id })
                .andWhere('task.dueDate between :start and :end', {
                  start: moment().isoWeekday(1).format('YYYY-MM-DD'),
                  end: moment().isoWeekday(7).format('YYYY-MM-DD'),
                })
                .getCount();
  
              const eventsCount = await createQueryBuilder(Event, 'event')
                .leftJoin('event.user', 'user')
                .where('user.id = :id', { id: user.id })
                .andWhere('event.date  between :start and :end', {
                  start: moment().isoWeekday(1).format('YYYY-MM-DD'),
                  end: moment().isoWeekday(7).format('YYYY-MM-DD'),
                })
                .getCount();
  
              const dscsDueCount = getDSCExpiry.length;
              // if(whatsappCheck){
              const templateName = 'weeklyoverview';
              const userMobileNumber = `91${user?.mobileNumber}`;
              const whatsappOptions = {
                to: userMobileNumber,
                name: templateName,
                header: [
                  {
                    type: 'text',
                    text: user?.fullName,
                  },
                ],
                body: [getDueTasksCount, dscsDueCount, eventsCount],
                title: 'Weekly Overview',
                userId: user.id,
                orgId: organization.id,
              };
              await sendWhatsAppTemplateMessage(whatsappOptions);
            } catch (error) {
              console.log(error);
            }


          }
          
  
        }
      } catch (error) {
        console.log(error);
      }
    }
  } catch (error) {
    console.log(error);
  }
}
}
// @Cron(CronExpression.EVERY_10_MINUTES)

  async sendFlowMessage() {
    const userId = 702; // User ID to send the message to
    const userDetails = await getUserDetails(userId);
    const userMobile = userDetails?.mobile_number
    const userHeaderText = userDetails?.full_name
    const userIdd = userDetails.id
    const orgId = userDetails?.organization_id
    const templateName = 'floww'

    const whatsappOptions = {
      to: userMobile,
      name: templateName,
      header: [
        {
          type: 'text',
          text: userHeaderText,
        },
      ],
      body: [userHeaderText],

      title: 'flow',
      userId: userIdd,
      orgId: orgId,

    };
    try {
        await sendWhatsAppTemplateMessage(whatsappOptions);
    } catch (error) {
        console.error("Error sending message to user with ID:", userId, error);
    }
}

@Get('/requests')
async whatsappRequests(@Query() query: any) {
  return this.whatsappService.whatsappRequests(query);
}


@UseGuards(JwtAuthGuard)

@Get('/conversation')
async whatsappConversation(@Req() req: any,@Query() query: any) {
  const { userId } = req.user;
  return this.whatsappService.whatsappConversation(userId,query);
}
@UseGuards(JwtAuthGuard)

@Post('/sendTemplateMessageWithSelectedTemplate')
async sendTemplateMessageConversation(@Req() req: any,@Body() body: any) {
  const { userId } = req.user;
  return this.whatsappService.sendTemplateMessageConversation(body,userId);
}

@UseGuards(JwtAuthGuard)

@Post('/sendWhatsappMessage')
async sendWhatsappMessage(@Req() req: any,@Body() body: any) {
  const { userId } = req.user;
  return this.whatsappService.sendWhatsAppTextMessageConvo(body,userId);
}

@UseGuards(JwtAuthGuard)

@Post('/createMetaTemplate')
async createMetaTemplate(@Req() req: any,@Body() body: any){
  const { userId } = req.user;

  return this.whatsappService.createMetaTemplate(body,userId);

}

@Get('/default-meta-templates')

async getDefaultMetaTemplates (){
  return this.whatsappService.getDefaultMetaTemplates()
}


@UseGuards(JwtAuthGuard)
  @Get('get-conversation-clients')
  getConversationClients(@Req() req: any) {
    const { userId } = req.user;
    return this.whatsappService.getConversationClients(userId);
  }
}



