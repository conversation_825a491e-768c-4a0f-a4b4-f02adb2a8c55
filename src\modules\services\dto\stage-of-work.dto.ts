import { Type } from 'class-transformer';
import {
  <PERSON><PERSON>rray,
  IsBoolean,
  IsNotEmpty,
  IsOptional,
  ValidateNested,
} from 'class-validator';

class ExtraAttribute {
  @IsNotEmpty()
  type: string;

  @IsNotEmpty()
  title: string;
}

class StageOfWorkDto {
  @IsNotEmpty()
  type: string;

  @IsNotEmpty()
  name: string;

  @IsOptional()
  description: string;

  @IsOptional()
  @IsBoolean()
  referenceNumber: boolean;

  @IsOptional()
  attachmentFile:string;

  @IsOptional()
  @Type(() => ExtraAttribute)
  @IsArray()
  @ValidateNested()
  extraAttributes: Array<ExtraAttribute>;
}

export default StageOfWorkDto;
