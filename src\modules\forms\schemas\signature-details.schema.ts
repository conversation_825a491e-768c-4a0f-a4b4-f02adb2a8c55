import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Schema as MangooseSchema } from 'mongoose';

@Schema()
export class SignatureDetail extends Document {
  @Prop({ required: true })
  name: string;

  @Prop({ required: true })
  designation: string;

  @Prop({ type: MangooseSchema.Types.Mixed, required: false, default: null })
  value: any;
}

export const SignatureDetailSchema =
  SchemaFactory.createForClass(SignatureDetail);
