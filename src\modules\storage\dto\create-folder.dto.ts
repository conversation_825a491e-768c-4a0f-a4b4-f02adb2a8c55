import { IsNotEmpty, IsOptional, Matches, ValidateIf } from 'class-validator';

class CreateFolderDto {
  @IsNotEmpty()
  @Matches(/^[^<>:"\/\\|?*\.]*$/, {
    message: 'Folder Name cannot contain <, >, :, ", /, \\, |, ?, *, or .',
  })
  name: string;

  @ValidateIf((o: CreateFolderDto) => o.type === 'client')
  @IsNotEmpty()
  clientId: number;

  @ValidateIf((o: CreateFolderDto) => o.type === 'clientGroup')
  @IsNotEmpty()
  clientGroup: number;

  @IsOptional()
  parent: string;

  @IsOptional()
  roomId: number;

  @IsNotEmpty()
  type: 'organization' | 'client' | 'chat' | 'clientGroup';
}

export default CreateFolderDto;
