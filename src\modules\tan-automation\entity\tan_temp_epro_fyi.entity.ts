import { Optional } from '@nestjs/common';
import Client from 'src/modules/clients/entity/client.entity';
import { Organization } from 'src/modules/organization/entities/organization.entity';
import {
  BaseEntity,
  Column,
  CreateDateColumn,
  Entity,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';

export enum EproceedingTypeEnum {
  SELF = 'Self',
  OTHER = 'Other',
}

@Entity()
class TanTempEproFyi extends BaseEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  clientId: number;

  @ManyToOne(() => Client, (client) => client.tanTempEproFyi, { onDelete: 'SET NULL' })
  client: Client;

  @Column()
  organizationId: number;

  @Column()
  tanClientCredentialsId: number;

  @Column({ nullable: true, length: 255 })
  proceedingName: string;

  @Column({ nullable: true, length: 45 })
  pan: string;

  @Column({ nullable: true, length: 45 })
  ay: string;

  @Column({ nullable: true, length: 45 })
  proceedingLimitationDate: string;

  @Column({ nullable: true, length: 45 })
  proceedingStatus: string;

  @Column({ nullable: true, length: 45 })
  proceedingConcludedDate: string;

  @Column({ nullable: true, length: 56 })
  noticeDin: string;

  @Column({ nullable: true, length: 45 })
  noticeSentDate: string;

  @Column({ nullable: true, length: 45 })
  noticeSection: string;

  @Column({ nullable: true, length: 45 })
  dateOfCompliance: string;

  @Column({ nullable: true, length: 45 })
  dateResponseSubmitted: string;

  @CreateDateColumn({ type: 'datetime', precision: 6 })
  createdAt: Date;

  @UpdateDateColumn({ type: 'datetime', precision: 6 })
  updatedAt: Date;

  @Column({ length: 45 })
  uuid: string;

  @Column({ default: EproceedingTypeEnum.SELF, type: 'enum', enum: EproceedingTypeEnum })
  type: EproceedingTypeEnum;
}

export default TanTempEproFyi;
