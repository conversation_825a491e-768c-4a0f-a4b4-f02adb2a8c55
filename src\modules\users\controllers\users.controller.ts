import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  ParseIntPipe,
  Patch,
  Post,
  Put,
  Query,
  Req,
  Request,
  UploadedFile,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { ClientLoginDto } from 'src/modules/client-panel/dto/client-loging.dto';
import { CreateUserDto } from '../dto/create-user.dto';
import { DeactivateUserDto } from '../dto/deactivate-user.dto';
import { ChangePasswordDto, ForgotPasswordDto, ResetPasswordDto } from '../dto/forgot-password.dto';
import InviteUserDto from '../dto/invite-user.dto';
import SignUpDto, { JoinUserDto, OtpDto, VerifyOtpDto } from '../dto/sign-up.dto';
import { UpdateProfileDto } from '../dto/update-profile.dto';
import { JwtAuthGuard } from '../jwt/jwt-auth.guard';
import { LocalAuthGuard } from '../jwt/local-auth.guard';
import { UsersService } from '../services/users.service';
import { FileInterceptor } from '@nestjs/platform-express';
import { ResendMfaDto, VerifyMfaDto } from '../dto/sign-in.dto';

@Controller('users')
export class UsersController {
  constructor(private service: UsersService) {}

  @UseGuards(JwtAuthGuard)
  @Get()
  async get(@Request() req: any) {
    const { userId } = req.user;
    return this.service.get(userId);
  }

  @UseGuards(JwtAuthGuard)
  @Get('active-inactive')
  async getActiveInactive(@Request() req: any) {
    const { userId } = req.user;
    return this.service.getActiveInactive(userId);
  }

  @UseGuards(JwtAuthGuard)
  @Get('/getAval')
  async getAval(@Request() req: any, @Query() query: any) {
    const { startDate, dueDate, members } = query;
    const memberss = JSON.parse(members);
    const { userId } = req.user;
    return this.service.getAval(startDate, dueDate, memberss, userId);
  }

  @UseGuards(JwtAuthGuard)
  @Get('/getAvalUser')
  async getAvalUser(@Request() req: any, @Query() query: any) {
    const { startDate, dueDate, members } = query;
    const memberss = JSON.parse(members);
    const { userId } = req.user;
    return this.service.getAvalUser(startDate, dueDate, memberss, query);
  }
  @UseGuards(JwtAuthGuard)
  @Get('/reports')
  async getUsersForReports(@Request() req: any, @Query() query: any) {
    const { userId } = req.user;
    return this.service.getUsersForReports(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Get('/all')
  async getAll(@Request() req: any) {
    const { userId } = req.user;
    return this.service.getAll(userId);
  }

  @UseGuards(JwtAuthGuard)
  @Get('/all-organization-users')
  async getAllOrganizationUsers(@Request() req: any) {
    const { userId } = req.user;
    return this.service.getAllOrganizationUsers(userId);
  }

  @UseGuards(JwtAuthGuard)
  @Get('/delete-data/:id')
  async getUserDeleteData(@Request() req: any, @Param('id', ParseIntPipe) id: number) {
    return this.service.getUserDeleteData(id);
  }

  @UseGuards(LocalAuthGuard)
  @Post('/signin')
  async login(@Request() req: any) {
    return this.service.login(req.user);
  }

  @Post('/mfaVerify')
  async mfaVerify(@Body() body: VerifyMfaDto) {
    return this.service.mfaVerify(body);
  }

  @Post('/resendMfa')
  async resendMfa(@Body() body: ResendMfaDto) {
    return this.service.resendMfa(body);
  }

  @Post('/extensionsignin')
  async clientlogin(@Body() body: any) {
    return this.service.extensionlogin(body);
  }

  @Post('/super-admin-signin')
  async superAdminLogin(@Body() body: ClientLoginDto) {
    return this.service.superAdminLogin(body);
  }

  @Post('/otp')
  async sendOtp(@Body() body: OtpDto) {
    return this.service.sendOtp(body);
  }

  @Post('/otp-verify')
  async verifyOtp(@Body() body: VerifyOtpDto) {
    return this.service.verifyOtp(body);
  }

  @Post('/signup')
  async signup(@Body() body: SignUpDto) {
    return this.service.signup(body);
  }

  @UseGuards(JwtAuthGuard)
  @Get('/profile')
  async getProfile(@Request() req: any) {
    const { userId } = req.user;
    return this.service.getProfile(userId);
  }

  @UseGuards(JwtAuthGuard)
  @Get('/profile/:id')
  async getUser(@Request() req: any, @Param('id', ParseIntPipe) id: number) {
    const { userId } = req.user;
    return this.service.getProfileById(id, userId);
  }

  @UseGuards(JwtAuthGuard)
  @Get('/check/:id')
  async getUserCheck(@Request() req: any, @Param('id', ParseIntPipe) id: number) {
    const { userId } = req.user;
    return this.service.getUserCheckById(id, userId);
  }

  @UseGuards(JwtAuthGuard)
  @Patch('/profile')
  async updateProfile(@Request() req: any, @Body() body: UpdateProfileDto) {
    const { userId } = req.user;
    return this.service.updateProfile(userId, body);
  }

  @UseGuards(JwtAuthGuard)
  @Patch('/profile/:id')
  async updateUserProfile(
    @Param('id', ParseIntPipe) id: number,
    @Body() body: UpdateProfileDto,
    @Request() req: any,
  ) {
    const { userId } = req.user;
    return this.service.updateUserProfile(id, body, userId);
  }

  @UseGuards(JwtAuthGuard)
  @Put('/profile/image')
  async updateImage(@Request() req: any, @Body() body: any) {
    const { userId } = req.user;
    return this.service.updateImage(userId, body);
  }

  @Post('/forgot-password')
  async forgotPassword(@Body() body: ForgotPasswordDto) {
    return this.service.forgotPassword(body);
  }

  @Post('/reset-password')
  async resetPassword(@Body() body: ResetPasswordDto) {
    return this.service.resetPassword(body);
  }

  @UseGuards(JwtAuthGuard)
  @Patch('/change-password')
  async changePassword(@Request() req: any, @Body() body: ChangePasswordDto) {
    return this.service.changePassword(req.user.userId, body);
  }

  @Post('/create')
  async create(@Body() body: CreateUserDto) {
    return this.service.create(body);
  }

  @UseGuards(JwtAuthGuard)
  @Post('/invite-user')
  async inviteUser(@Request() req: any, @Body() body: InviteUserDto) {
    const { userId } = req.user;
    return this.service.inviteUser(userId, body);
  }

  @UseGuards(JwtAuthGuard)
  @Patch('/invite-user/edit')
  async editInviteUser(@Request() req: any, @Body() body: InviteUserDto) {
    const { userId } = req.user;
    return this.service.editInviteUser(userId, body);
  }

  @Post('/join')
  async joinUser(@Body() body: JoinUserDto) {
    return this.service.joinUser(body);
  }

  @UseGuards(JwtAuthGuard)
  @Get('/invited')
  async getInvitedUsers(@Req() req: any, @Query() query: any) {
    const { userId } = req.user;
    return this.service.findInvitedUsers(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Post('/invited/:id/cancel')
  async cancelInvitedUser(@Req() req: any, @Param('id', ParseIntPipe) id: number) {
    const { userId } = req.user;
    return this.service.cancelInvitation(id, userId);
  }

  @UseGuards(JwtAuthGuard)
  @Post('/invited/:id/resend')
  async resendInvitation(@Req() req: any, @Param('id', ParseIntPipe) id: number) {
    return this.service.resendInvitation(id);
  }

  @UseGuards(JwtAuthGuard)
  @Post(`/:id/delete`)
  async deleteUser(@Param('id', ParseIntPipe) id: number, @Req() req: any) {
    const { userId } = req.user;
    return this.service.deletedUser(id, userId);
  }

  @UseGuards(JwtAuthGuard)
  @Post(`/:id/restore`)
  async restoreUser(@Param('id', ParseIntPipe) id: number, @Body() body: any, @Req() req: any) {
    const { userId } = req.user;
    return this.service.restoreUser(id, userId, body);
  }

  @UseGuards(JwtAuthGuard)
  @Post(`/:id/activate-deactivate`)
  async activateOrDeactivate(
    @Param('id', ParseIntPipe) id: number,
    @Body() body: DeactivateUserDto,
    @Req() req: any,
  ) {
    const { userId } = req.user;
    return this.service.activateOrDeactivateUser(id, body, userId);
  }

  @UseGuards(JwtAuthGuard)
  @Get('/deleted')
  async getDeletedUsers(@Request() req: any, @Query() query: any) {
    const { userId } = req.user;
    return this.service.getDeletedUsers(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Post('/import')
  @UseInterceptors(FileInterceptor('file'))
  async importUsers(@UploadedFile() file: Express.Multer.File, @Request() req: any) {
    const { userId } = req.user;
    return this.service.importUsers(userId, file);
  }

  @Post('/forgotpassword-otp')
  async forgotPasswordSendOtp(@Body() body) {
    return this.service.forgotPasswordSendOtp(body);
  }

  @UseGuards(JwtAuthGuard)
  @Post('/create-non-org')
  async createNonOrganizationUser(@Body() data: any, @Request() req: any) {
    const { userId } = req.user;
    return this.service.createNonOrganizationUser(userId, data);
  }

  @Put('non-org-user/:id')
  update(@Param('id', ParseIntPipe) id: number, @Body() body: any) {
    return this.service.updateNonOrganizationUser(id, body);
  }

  @Delete('non-org-user/:id')
  delete(@Param('id', ParseIntPipe) id: number) {
    return this.service.deleteNonOrganizationUser(id);
  }

  @UseGuards(JwtAuthGuard)
  @Get('/all-users')
  async getOrgAndNonUsers(@Request() req: any) {
    const { userId } = req.user;
    return this.service.getOrgAndNonUsers(userId);
  };

  @UseGuards(JwtAuthGuard)
  @Get('/signature-users')
  async getSignatureUsers(@Request() req: any) {
    const { userId } = req.user;
    return this.service.getSignatureUsers(userId);
  }

  @UseGuards(JwtAuthGuard)
  @Get('/non-org-users')
  async getNonOrgUsers(@Request() req: any) {
    const { userId } = req.user;
    return this.service.getNonOrgUsers(userId);
  }

  @UseGuards(JwtAuthGuard)
  @Get('/client-manager')
  async getClientManagerClients(@Request() req: any, @Query() query: any) {
    const { userId } = req.user;
    return this.service.getClientManagerClients(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Post('/client-manager/export')
  async exportUserClientManagersReport(@Req() req: any, @Body() body: any) {
    const { userId } = req.user;
    return this.service.exportUserClientManagersReport(userId, body);
  }

  @UseGuards(JwtAuthGuard)
  @Get('/client-group-manager')
  async getClientManagerClientGroups(@Request() req: any, @Query() query: any) {
    const { userId } = req.user;
    return this.service.getClientManagerClientGroups(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Post('/client-group-manager/export')
  async exportUserClientGroupManagersReport(@Req() req: any, @Body() body: any) {
    const { userId } = req.user;
    return this.service.exportUserClientGroupManagersReport(userId, body);
  }

  @UseGuards(JwtAuthGuard)
  @Put('/profile/client-manager')
  async updateProfileClientManagers(@Request() req: any, @Body() body: any) {
    const { userId } = req.user;
    return this.service.updateProfileClientManagers(userId, body);
  }

  @UseGuards(JwtAuthGuard)
  @Put('/profile/remove-client-manager')
  async removeProfileClientManagers(@Request() req: any, @Body() body: any) {
    const { userId } = req.user;
    return this.service.removeProfileClientManagers(userId, body);
  }

  @UseGuards(JwtAuthGuard)
  @Put('/profile/remove-all-client-manager')
  async removeProfileAllClientManagers(@Request() req: any, @Body() body: any) {
    const { userId } = req.user;
    return this.service.removeProfileAllClientManagers(userId, body);
  }

  @UseGuards(JwtAuthGuard)
  @Put('/profile/client-group-manager')
  async updateProfileClientGroupManagers(@Request() req: any, @Body() body: any) {
    const { userId } = req.user;
    return this.service.updateProfileClientGroupManagers(userId, body);
  }

  @UseGuards(JwtAuthGuard)
  @Put('/profile/remove-client-group-manager')
  async removeProfileClientGroupManagers(@Request() req: any, @Body() body: any) {
    const { userId } = req.user;
    return this.service.removeProfileClientGroupManagers(userId, body);
  }

  @UseGuards(JwtAuthGuard)
  @Put('/profile/remove-all-client-group-manager')
  async removeProfileAllClientGroupManagers(@Request() req: any, @Body() body: any) {
    const { userId } = req.user;
    return this.service.removeProfileAllClientGroupManagers(userId, body);
  }

  @Post('/super-admin-forgot-password')
  async superAdminForgotPassword(@Body() body: any) {
    return this.service.superAdminForgotPassword(body);
  }

  @Post('/super-admin-reset-password')
  async superAdminResetPassword(@Body() body: ResetPasswordDto) {
    return this.service.superAdminResetPassword(body);
  }
}
