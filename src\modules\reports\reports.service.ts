import { BadRequestException, Injectable } from '@nestjs/common';
import { Brackets, createQuery<PERSON>uilder, getConnection, getManager, getRepository } from 'typeorm';
import LogHour, { LogHourType, TimerStatus } from 'src/modules/log-hours/log-hour.entity';
import * as xlsx from 'xlsx';
import * as moment from 'moment';
import { getClientsReportDto } from './dto/get-clients-report.dto';
import Client from '../clients/entity/client.entity';
import { User, UserStatus, UserType } from '../users/entities/user.entity';
import { formatDate, getDuration, getHours, getTitle } from 'src/utils';
import { getTasksReport } from './dto/get-tasks-report.dto';
import Task from '../tasks/entity/task.entity';
import {
  DateFilterKeys,
  DateFilters,
  DateKeys,
  IFilterByDate,
  TaskRecurringStatus,
  TaskStatusEnum,
} from '../tasks/dto/types';
import getLogHoursDto from './dto/get-log-hours.dto';
import getEmployeeLogHoursDto, { BillableType } from './dto/get-employee-log-hours-report';
import { Event_Actions } from 'src/event-listeners/actions';
import { EventEmitter2 } from '@nestjs/event-emitter';
import getEmployeeLogHoursDynamicDto from './dto/get-employee-dynamic-loghour-reports';
import getLogHoursFeeDto from './dto/get-loh-hours-fee.dto';
import { getLeadsReportDto } from './dto/get-leads-report.dto';
import Lead, { LeadStatusEnum } from '../leads/lead.entity';
import { getDscRegisterDto } from './dto/get-dsc-register.dto';
import DscRegister from '../dsc-register/entity/dsc-register.entity';
import { getUsersDto } from './dto/get-users-report.dto';
import { InvitedUser } from '../users/entities/invited-user.entity';
import { Role } from '../roles/entities/role.entity';
import getExpenditureDto from './dto/getExpenditure.dto';
import Expenditure from '../expenditure/expenditure.entity';
import getLoghoursDto from './get-log-hours.dto';
import { getUserInviteDto } from './dto/get-userinvited.dto';
import { Permissions } from '../tasks/permission';
import FindTasksQuery from '../tasks/dto/find-query.dto';
import { dateFormation } from 'src/utils/datesFormation';
import getEmployeeAttendanceDto from './dto/get-employee-attendance.dto';
import Attendance, { AttendanceStatus } from '../attendance/attendance.entity';
import FindExpenditureDto, {
  FindExpenditureQueryType,
} from '../expenditure/dto/find-expenditure.dto';
import FindQueryDto from '../clients/dto/find-query.dto';
import { TasksService } from '../tasks/services/tasks.service';
import { Organization } from '../organization/entities/organization.entity';
import { detailedoverduecompletedtasks } from 'src/utils/sqlqueries';
import { getCountryCode } from 'src/utils/getCountryCode';
import OrganizationPreferences from '../organization-preferences/entity/organization-preferences.entity';
import { fullMobileWithCountry } from 'src/utils/validations/fullMobileWithCountry';
import ClientGroup from '../client-group/client-group.entity';
import { ClientGroupService } from '../client-group/client-group.service';
import exp from 'constants';
import { formattedDate, formattedDateMonthYear, getExpenseType, getTimesheetDuration, getTotalLogHours } from 'src/utils/re-use';
import getEmployeeAttendanceandTimesheetDto from './dto/get-employee-attendance-and-timesheet';
import * as ExcelJS from 'exceljs'
import countries from 'src/utils/countries';
import { categoryDisplayNames, CLIENT_CATEGORIES, content1, content2, content3, content4, content5, getCountryLabel, STATES, subCategoryDisplayNames } from 'src/utils/clientExport';
import Activity, { ActivityType } from '../activity/activity.entity';
import { ClientService } from '../clients/services/clients.service';

@Injectable()
export class ReportsService {
  constructor(private eventEmitter: EventEmitter2,
    private tasksService: TasksService,
    public groupservice: ClientGroupService,
    private clientService: ClientService,
  ) { }


  async getLogHours(body: getLogHoursDto, user) {
    const entityManager = getManager();
    let { fromDate, toDate, users, clients, type, billableType } = body;
    let clientsIds = clients
      ?.filter((item) => item.type !== 'CLIENT_GROUP')
      .map((item) => item.id);
    let clientGroupIds = clients
      ?.filter((item) => item.type === 'CLIENT_GROUP')
      .map((item) => item.id);
    const query = 'getLogHours';
    let organizationid = user?.organization?.id;

    let logHours = createQueryBuilder(LogHour, 'logHour')
      .leftJoinAndSelect('logHour.user', 'user')
      .leftJoinAndSelect('logHour.task', 'task')
      .leftJoinAndSelect('logHour.client', 'client')
      .leftJoinAndSelect('logHour.clientGroup', 'clientGroup')
      .leftJoinAndSelect('task.category', 'category')
      .leftJoinAndSelect('task.subCategory', 'subCategory')
      .leftJoinAndSelect('task.taskLeader', 'taskLeader')
      .leftJoinAndSelect('taskLeader.imageStorage', 'imageStorage')
      .where('user.id IN (:...users)', { users: users })
      .andWhere('task.parentTask is null');
    if (type && type !== LogHourType.ALL) {
      logHours.andWhere('logHour.type = :type', { type: type });
    }
    if (billableType && billableType !== BillableType.ALL) {
      if (billableType === BillableType.BILLABLE) {
        logHours.andWhere('task.billable is true');
      } else {
        logHours.andWhere('task.billable is false');
      }
    }
    console.log({ clientsIds, clientGroupIds });
    if (clientsIds.length || clientGroupIds.length) {
      if (clientsIds?.length === 1 && clientsIds?.[0] === "ALL") {
        const query: any = {
          clientManagers: true,
          status: ["ACTIVE",],
        };
        clients = (await this.clientService.getTaskCreateClientAll(user.id, query))?.result;

        clientsIds = clients
          ?.filter((item) => item.type !== 'CLIENT_GROUP')
          .map((item) => item.id);
        clientGroupIds = clients
          ?.filter((item) => item.type === 'CLIENT_GROUP')
          .map((item) => item.id);
      }

      console.log({ clientsIds });
      logHours.andWhere(
        new Brackets((qb) => {
          if (clientsIds?.length) {
            qb.orWhere('client.id IN (:...clients)', { clients: clientsIds });
          }
          if (clientGroupIds?.length) {
            qb.orWhere('clientGroup.id IN (:...clientsGroup)', { clientsGroup: clientGroupIds });
          }
        }),
      );



    }

    if (fromDate) {
      logHours.andWhere('logHour.completedDate >= :fromDate', {
        fromDate: fromDate,
      });
    }

    if (toDate) {
      logHours.andWhere('logHour.completedDate <= :toDate', {
        toDate: moment(toDate).format('YYYY-MM-DD'),
      });
    }

    if (body.category?.length) {
      const categoryIds = body.category.map((category: any) => category?.id || category);
      logHours.andWhere('category.id IN (:...category)', { category: categoryIds });
    }

    if (body.subCategory?.length) {
      const subcategoryIds = body.subCategory.map(
        (subCategory: any) => subCategory?.id || subCategory,
      );
      logHours.andWhere('subCategory.id IN (:...subCategory)', { subCategory: subcategoryIds });
    }
    logHours.orderBy('logHour.completedDate', 'ASC');

    let result = await logHours.getMany();
    let totalLogHours = await logHours.select('SUM(logHour.duration) as totalDuration').getRawOne();
    this.eventEmitter.emit(Event_Actions.REPORT_GENERATED, {
      ...result,
      query,
      body,
      organizationid,
      user,
    });

    return {
      totalLogHours: Math.round(+totalLogHours.totalDuration / 1000 / 60 / 60),
      result: result,
    };
  }

  // async exportLogHours(body: getLogHoursDto, user) {
  //   let logHours = await this.getLogHours(body, user);

  //   if(!logHours.result.length){
  //     throw new BadRequestException('No Data for Export !');
  //   }
  //   let rows = logHours.result.map((logHour) => {
  //     return {
  //       'Date': moment(logHour.completedDate).format('DD-MM-YYYY'),
  //       'Client / Client Group': logHour.client ? logHour?.client?.displayName : logHour?.clientGroup?.displayName,
  //       'Type': logHour.type,
  //       'Task ID': logHour?.task?.taskNumber,
  //       'Title / Task Name': logHour.task ? logHour.task?.name : logHour.title,
  //       'User': logHour.user?.fullName,
  //       'Duration': moment.utc(+logHour?.duration).format('HH:mm'),
  //     };
  //   });

  //   const worksheet = xlsx.utils.json_to_sheet(rows);
  //   const workbook = xlsx.utils.book_new();
  //   xlsx.utils.book_append_sheet(workbook, worksheet, 'Employee Log Hours');
  //   let file = xlsx.write(workbook, { type: 'buffer' });
  //   return file;
  // }
  async exportLogHours(body: getLogHoursDto, user) {
    let logHours = await this.getLogHours(body, user);

    if (!logHours.result.length) {
      throw new BadRequestException('No Data for Export!');
    }

    let rows = logHours.result.map((logHour) => {
      // Convert duration from milliseconds to Excel's time format (fraction of a day)
      const durationInExcelFormat = Math.floor((+logHour?.duration) / (1000 * 60)) / (24 * 60);
      return {
        'Type': logHour.type,
        'Client / Client Group': logHour.client ? logHour?.client?.displayName : logHour?.clientGroup?.displayName,
        'Date': moment(logHour.completedDate).format('DD-MM-YYYY'),
        'Task ID': logHour?.task?.taskNumber,
        'Category': logHour?.task ? logHour?.task?.category?.name : "",
        'Sub Catergory': logHour?.task ? logHour?.task?.subCategory?.name : "",
        'Title / Task Name': logHour.task ? logHour.task?.name : logHour.title,
        'Employee': logHour.user?.fullName,
        'Task Leaders': logHour.task?.taskLeader?.map(i => i.fullName).join(","),
        'Billing Type': logHour.billable ? 'Billable' : 'Non-Billable',
        'Duration': {
          t: 'n',
          v: durationInExcelFormat,
          z: '[hh]:mm',
        },
      };
    });

    const worksheet = xlsx.utils.json_to_sheet(rows);
    const workbook = xlsx.utils.book_new();
    xlsx.utils.book_append_sheet(workbook, worksheet, 'Client Log Hours');

    // Apply the 'hh:mm' format to each cell in the 'Duration' column for display
    const durationColumnStart = 2; // Assuming the data starts from row 2 after headers
    rows.forEach((_, index) => {
      const cellAddress = `G${index + durationColumnStart}`;
      if (worksheet[cellAddress]) {
        worksheet[cellAddress].z = 'hh:mm'; // Set format to 'hh:mm' for display
      }
    });

    let file = xlsx.write(workbook, { type: 'buffer' });
    return file;
  }





  async getLogHoursFee(userId: number, body: getLogHoursFeeDto) {
    const { fromDate, toDate, clients, status } = body;
    const user = await User.findOne({ where: { id: userId } });
    const clientsIds = clients
      ?.filter((item) => item.type !== 'CLIENT_GROUP')
      .map((item) => item.id);
    const clientGroupIds = clients
      ?.filter((item) => item.type === 'CLIENT_GROUP')
      .map((item) => item.id);
    let tasks = createQueryBuilder(Task, 'task')
      .select([
        'task.id',
        'task.taskNumber as taskNumber',
        'task.name as name',
        'task.status as status',
        'COALESCE(client.displayName, clientGroup.displayName) as client',
        'client.id',
        'client.clientId',
        'clientGroup.id',
        'clientGroup.clientId',
        'task.feeAmount as fee',
        'SUM(taskLogHours.duration) AS totalLogHours',
      ])
      .leftJoin('task.taskLogHours', 'taskLogHours')
      .leftJoin('task.client', 'client')
      .leftJoin('task.clientGroup', 'clientGroup')
      .where('task.organization.id = :organizationId', { organizationId: user.organization.id })
      .andWhere('(task.recurringStatus is null or task.recurringStatus = :recurringStatus)', {
        recurringStatus: TaskRecurringStatus.CREATED,
      })
      .andWhere('task.billable is true')
      .andWhere(
        new Brackets((qb) => {
          qb.where('client.status IN (:clientStatus)', {
            clientStatus: [UserStatus.ACTIVE, UserStatus.INACTIVE],
          }).orWhere('clientGroup.status IN (:clientGroupStatus)', {
            clientGroupStatus: [UserStatus.ACTIVE, UserStatus.INACTIVE],
          });
        }),
      )
      .andWhere('task.status IN (:...status)', {
        status: [
          TaskStatusEnum.TODO,
          TaskStatusEnum.IN_PROGRESS,
          TaskStatusEnum.UNDER_REVIEW,
          TaskStatusEnum.ON_HOLD,
          TaskStatusEnum.COMPLETED,
        ],
      })
      .andWhere('task.parentTask is null');
    if (fromDate) {
      tasks.andWhere('task.taskStartDate >= :fromDate', {
        fromDate: fromDate,
      });
    }
    if (toDate) {
      tasks.andWhere('task.taskStartDate <= :toDate', {
        toDate: toDate,
      });
    }

    if (clientsIds.length || clientGroupIds.length) {
      tasks.andWhere(
        new Brackets((qb) => {
          if (clientsIds?.length) {
            qb.orWhere('client.id IN (:...clients)', { clients: clientsIds });
          }
          if (clientGroupIds?.length) {
            qb.orWhere('clientGroup.id IN (:...clientsGroup)', { clientsGroup: clientGroupIds });
          }
        }),
      );
    }

    if (status) {
      tasks.andWhere('task.status=:taskStatus', { taskStatus: status });
    }

    tasks.groupBy('task.id'); // Group by task properties
    const query = 'getLogHoursFee';
    const organizationid = user?.organization?.id;

    // Execute the query and get the result
    let result = await tasks.getRawMany();
    this.eventEmitter.emit(Event_Actions.REPORT_GENERATED, {
      ...result,
      query,
      body,
      organizationid,
      user,
    });
    return result;
  }
  // async getOnHoldTasks(userId: number, body: getLogHoursFeeDto) {
  //   const { fromDate, toDate, clients, status } = body;

  //   const user = await User.findOne({ where: { id: userId } });

  //   const clientsIds = clients
  //     ?.filter((item) => item.type !== 'CLIENT_GROUP')
  //     .map((item) => item.id);
  //   const clientGroupIds = clients
  //     ?.filter((item) => item.type === 'CLIENT_GROUP')
  //     .map((item) => item.id);

  //   // Subquery for latest activity
  //   const latestActivitySubQuery = createQueryBuilder(Activity, 'a')
  //     .select('MAX(a.createdAt)', 'maxCreatedAt')
  //     .addSelect('a.typeId', 'typeId')
  //     .where('a.type = :activityType', { activityType: ActivityType.TASK })
  //     .andWhere('a.action = :action', { action: 'Remark Added' })
  //     .groupBy('a.typeId');

  //   let tasks = createQueryBuilder(Task, 'task')
  //     .select([
  //       'task.id',
  //       'task.taskNumber as taskNumber',
  //       'task.name as name',
  //       'task.status as status',
  //       'COALESCE(client.displayName, clientGroup.displayName) as client',
  //       'client.id',
  //       'clientGroup.id',
  //       'category.name as categoryName',
  //       'subCategory.name as subCategoryName',
  //       // 'members.fullName',
  //       'activity.remarks as latestRemark',
  //       'activity.remarkType as remarkType',
  //       'activity.createdAt as latestRemarkDate',
  //     ])
  //     .leftJoin('task.client', 'client')
  //     .leftJoin('task.clientGroup', 'clientGroup')
  //     .leftJoin('task.category', 'category')
  //     .leftJoin('task.subCategory', 'subCategory')
  //     .leftJoin('task.members', 'members')
  //     .leftJoin('members.imageStorage', 'imageStorage')
  //     .addSelect(`GROUP_CONCAT(members.fullName SEPARATOR ', ')`, 'memberNames')

  //     // Join subquery first
  //     .leftJoin(
  //       '(' + latestActivitySubQuery.getQuery() + ')',
  //       'latest',
  //       'latest.typeId = task.id'
  //     )
  //     // Then join the activity table using the latest createdAt
  //     .leftJoin(
  //       Activity,
  //       'activity',
  //       'activity.typeId = task.id AND activity.createdAt = latest.maxCreatedAt AND activity.type = :activityType AND activity.action = :action',
  //       {
  //         activityType: ActivityType.TASK,
  //         action: 'Remark Added',
  //       }
  //     )
  //     .where('task.organization.id = :organizationId', { organizationId: user.organization.id })
  //     .andWhere('(task.recurringStatus is null or task.recurringStatus = :recurringStatus)', {
  //       recurringStatus: TaskRecurringStatus.CREATED,
  //     })
  //     .andWhere('task.billable is true')
  //     .andWhere(
  //       new Brackets((qb) => {
  //         qb.where('client.status IN (:clientStatus)', {
  //           clientStatus: [UserStatus.ACTIVE, UserStatus.INACTIVE],
  //         }).orWhere('clientGroup.status IN (:clientGroupStatus)', {
  //           clientGroupStatus: [UserStatus.ACTIVE, UserStatus.INACTIVE],
  //         });
  //       }),
  //     )
  //     .andWhere('task.status IN (:...status)', {
  //       status: [
  //         TaskStatusEnum.ON_HOLD,

  //       ],
  //     })
  //     .andWhere('task.parentTask is null');

  //   if (fromDate) {
  //     tasks.andWhere('task.taskStartDate >= :fromDate', {
  //       fromDate: fromDate,
  //     });
  //   }

  //   if (toDate) {
  //     tasks.andWhere('task.taskStartDate <= :toDate', {
  //       toDate: toDate,
  //     });
  //   }

  //   if (clientsIds.length || clientGroupIds.length) {
  //     tasks.andWhere(
  //       new Brackets((qb) => {
  //         if (clientsIds?.length) {
  //           qb.orWhere('client.id IN (:...clients)', { clients: clientsIds });
  //         }
  //         if (clientGroupIds?.length) {
  //           qb.orWhere('clientGroup.id IN (:...clientsGroup)', { clientsGroup: clientGroupIds });
  //         }
  //       }),
  //     );
  //   }

  //   if (status) {
  //     tasks.andWhere('task.status=:taskStatus', { taskStatus: status });
  //   }

  //   tasks.groupBy('task.id');

  //   const query = 'on-hold-tasks';
  //   const organizationid = user?.organization?.id;

  //   const result = await tasks.getRawMany();

  //   this.eventEmitter.emit(Event_Actions.REPORT_GENERATED, {
  //     ...result,
  //     query,
  //     body,
  //     organizationid,
  //     user,
  //   });

  //   return result;
  // };

  async getOnHoldTasks(userId: number, body: getLogHoursFeeDto) {
    const { fromDate, toDate, clients, status } = body;

    const user = await User.findOne({ where: { id: userId } });

    const clientsIds = clients
      ?.filter((item) => item.type !== 'CLIENT_GROUP')
      .map((item) => item.id);
    const clientGroupIds = clients
      ?.filter((item) => item.type === 'CLIENT_GROUP')
      .map((item) => item.id);

    // Subquery for latest activity
    const latestActivitySubQuery = createQueryBuilder(Activity, 'a')
      .select('MAX(a.createdAt)', 'maxCreatedAt')
      .addSelect('a.typeId', 'typeId')
      .where('a.type = :activityType', { activityType: ActivityType.TASK })
      .andWhere('a.action = :action', { action: 'Remark Added' })
      .groupBy('a.typeId');

    let tasksQuery = createQueryBuilder(Task, 'task')
      .select([
        'task.id as task_id',
        'task.taskNumber as taskNumber',
        'task.name as name',
        'task.status as status',
        'COALESCE(client.displayName, clientGroup.displayName) as client',
        'client.id as clientId',
        'client.clientId as clientNumber',
        'clientGroup.id as clientGroupId',
        'clientGroup.clientId as clientGroupNumber',
        'category.name as categoryName',
        'subCategory.name as subCategoryName',
        'activity.remarks as latestRemark',
        'activity.remarkType as remarkType',
        'activity.createdAt as latestRemarkDate',
      ])
      .leftJoin('task.client', 'client')
      .leftJoin('task.clientGroup', 'clientGroup')
      .leftJoin('task.category', 'category')
      .leftJoin('task.subCategory', 'subCategory')
      // no members join here to avoid extra rows
      .leftJoin(
        '(' + latestActivitySubQuery.getQuery() + ')',
        'latest',
        'latest.typeId = task.id'
      )
      .leftJoin(
        Activity,
        'activity',
        'activity.typeId = task.id AND activity.createdAt = latest.maxCreatedAt AND activity.type = :activityType AND activity.action = :action',
        {
          activityType: ActivityType.TASK,
          action: 'Remark Added',
        }
      )
      .where('task.organization.id = :organizationId', { organizationId: user.organization.id })
      .andWhere('(task.recurringStatus is null or task.recurringStatus = :recurringStatus)', {
        recurringStatus: TaskRecurringStatus.CREATED,
      })
      .andWhere('task.billable is true')
      .andWhere(
        new Brackets((qb) => {
          qb.where('client.status IN (:clientStatus)', {
            clientStatus: [UserStatus.ACTIVE, UserStatus.INACTIVE],
          }).orWhere('clientGroup.status IN (:clientGroupStatus)', {
            clientGroupStatus: [UserStatus.ACTIVE, UserStatus.INACTIVE],
          });
        })
      )
      .andWhere('task.status IN (:...status)', {
        status: [TaskStatusEnum.ON_HOLD],
      })
      .andWhere('task.parentTask is null');

    if (fromDate) {
      tasksQuery.andWhere('task.taskStartDate >= :fromDate', { fromDate });
    }

    if (toDate) {
      tasksQuery.andWhere('task.taskStartDate <= :toDate', { toDate });
    }

    if (clientsIds.length || clientGroupIds.length) {
      tasksQuery.andWhere(
        new Brackets((qb) => {
          if (clientsIds?.length) {
            qb.orWhere('client.id IN (:...clients)', { clients: clientsIds });
          }
          if (clientGroupIds?.length) {
            qb.orWhere('clientGroup.id IN (:...clientsGroup)', { clientsGroup: clientGroupIds });
          }
        })
      );
    }

    if (status) {
      tasksQuery.andWhere('task.status = :taskStatus', { taskStatus: status });
    }

    tasksQuery.groupBy('task.id');

    const result = await tasksQuery.getRawMany();

    // --- Fetch members and their image URLs per task ---
    const taskIds = result.map((task) => task.task_id);
    const memberMap: Record<number, { title: string; src: string }[]> = {};
    const leaderMap: Record<number, { title: string; src: string }[]> = {};

    if (taskIds.length > 0) {
      const memberRows = await getRepository(Task)
        .createQueryBuilder('task')
        .leftJoin('task.members', 'member')
        .leftJoin('member.imageStorage', 'imageStorage')
        .select([
          'task.id AS taskId',
          'member.fullName AS fullName',
          'imageStorage.file AS file',
          'imageStorage.fileId AS fileId',
          'imageStorage.storageSystem AS storageSystem',
          'imageStorage.webUrl AS webUrl',
        ])
        .where('task.id IN (:...ids)', { ids: taskIds })
        .getRawMany();

      for (const m of memberRows) {
        let src: string | null = null;
        if (m.storageSystem === 'AMAZON' && m.file) {
          src = `${process.env.AWS_BASE_URL}/${m.file}`;
        } else if (m.storageSystem === 'MICROSOFT' && m.webUrl) {
          src = m.webUrl;
        } else if (m.storageSystem === 'GOOGLE' && m.fileId) {
          src = `https://drive.google.com/thumbnail?id=${m.fileId}`;
        }

        if (!memberMap[m.taskId]) memberMap[m.taskId] = [];
        memberMap[m.taskId].push({ title: m.fullName, src });
      }
    }

    // --- Attach members array to each task result ---


    // --- Fetch Leaders and their image URLs per task ---
    if (taskIds.length > 0) {
      const leaderRows = await getRepository(Task)
        .createQueryBuilder('task')
        .leftJoin('task.taskLeader', 'taskLeader')
        .leftJoin('taskLeader.imageStorage', 'imageStorage')
        .select([
          'task.id AS taskId',
          'taskLeader.id as taskLeaderId',
          'taskLeader.fullName AS fullName',
          'imageStorage.file AS file',
          'imageStorage.fileId AS fileId',
          'imageStorage.storageSystem AS storageSystem',
          'imageStorage.webUrl AS webUrl',
        ])
        .where('task.id IN (:...ids)', { ids: taskIds })
        .getRawMany();
      const filteredLeads = leaderRows.filter(item => !!item?.taskLeaderId)
      for (const m of filteredLeads) {

        let src: string | null = null;
        if (m.storageSystem === 'AMAZON' && m.file) {
          src = `${process.env.AWS_BASE_URL}/${m.file}`;
        } else if (m.storageSystem === 'MICROSOFT' && m.webUrl) {
          src = m.webUrl;
        } else if (m.storageSystem === 'GOOGLE' && m.fileId) {
          src = `https://drive.google.com/thumbnail?id=${m.fileId}`;
        }

        if (!leaderMap[m.taskId]) leaderMap[m.taskId] = [];
        leaderMap[m.taskId].push({ title: m.fullName, src });
      }
    }

    let finalResult = result.map((task) => ({
      ...task,
      members: memberMap[task.task_id] || [],
      leaders: leaderMap[task.task_id] || [],
    }));




    // Optional analytics event
    this.eventEmitter.emit(Event_Actions.REPORT_GENERATED, {
      ...result,
      query: 'getOnHoldTasks',
      body,
      organizationid: user?.organization?.id,
      user,
    });

    return finalResult;
  }

  async exportOnHoldTasks(userId: number, body: getLogHoursFeeDto) {
    let tasks = await this.getOnHoldTasks(userId, body);
    if (!tasks.length) {
      throw new BadRequestException('No Data for Export !');
    }
    let rows = tasks.map((task) => {
      return {
        'Client / Client Group': task.client,
        'Service Category': task.categoryName,
        'Service Sub Category': task.subCategoryName,
        'Task ID': task?.taskNumber,
        'Task Name': task?.name,
        'Status': task.status
          ?.split("_")
          .map((item) => item.charAt(0).toUpperCase() + item.slice(1))
          .join(" "),
        'Type': task.remarkType
          ?.split("_")
          .map((item) => item.charAt(0).toUpperCase() + item.slice(1))
          .join(" "),
        'Last remark by name': task.latestRemark,
        'Last remark date and time': task.latestRemarkDate,
        'Assignee': task.members?.map(m => m.title)?.join(" , "),
        'Task leader': task.leaders?.map(m => m.title)?.join(" , ")

      };
    });

    const worksheet = xlsx.utils.json_to_sheet(rows);
    const workbook = xlsx.utils.book_new();
    xlsx.utils.book_append_sheet(workbook, worksheet, 'Time to Value');
    let file = xlsx.write(workbook, { type: 'buffer' });
    return file;
  }




  async exportLogHoursFee(userId: number, body: getLogHoursFeeDto) {
    let tasks = await this.getLogHoursFee(userId, body);
    const getDuration = (duration: number) => {
      const hours = Math.floor(duration / 1000 / 60 / 60);
      const minutes = Math.floor((duration / 1000 / 60) % 60);
      const formattedTime = `${hours.toString().padStart(2, '0')}:${minutes
        .toString()
        .padStart(2, '0')}`;
      return formattedTime;
    };

    if (!tasks.length) {
      throw new BadRequestException('No Data for Export !');
    }

    let rows = tasks.map((task) => {
      return {
        'Client / Client Group': task.client,
        'Task ID': task?.taskNumber,
        'Task Name': task?.name,
        'Status': task.status,
        'Log hours': getDuration(task.totalLogHours || 0),
        'Servce': task.fee || 0,
      };
    });

    const worksheet = xlsx.utils.json_to_sheet(rows);
    const workbook = xlsx.utils.book_new();
    xlsx.utils.book_append_sheet(workbook, worksheet, 'Time to Value');
    let file = xlsx.write(workbook, { type: 'buffer' });
    return file;
  }

  // async getEmployeeLogHours(body: getEmployeeLogHoursDto) {
  //   const entityManager = getManager();
  //   const { user } = body;
  //   const query = "getEmployeeLogHours"
  //   const sqlQuery = `SELECT organization_id FROM user where id = ${user} ;`
  //   let orgId = await entityManager.query(sqlQuery);
  //   let organizationid = orgId[0].organization_id

  //   if (new Date(body.fromDate) > new Date(body.toDate)) {
  //     throw new BadRequestException('From date must be less than or equal to to date');
  //   }

  //   let totalLogHours = await getManager().query(
  //     `SELECT SUM(duration) as duration FROM log_hour WHERE user_id = ${body.user}
  //     AND completed_date >= '${body.fromDate}' AND completed_date <= '${body.toDate}'`,
  //   );

  //   let logHours: any;

  //   const { startTime, endTime } = this.getDateRangeFormatted(body.fromDate);
  //   console.log(startTime, endTime);
  //   const toDateTomorrow = moment(body.toDate).add(1, 'days').format('YYYY-MM-DD');

  //   if (body.fromDate === body.toDate) {
  //     console.log(body.fromDate);

  //     logHours = createQueryBuilder(LogHour, 'logHour')
  //       .leftJoin('logHour.client', 'client')
  //       .leftJoin('logHour.user', 'user')
  //       .leftJoin('logHour.task', 'task')
  //       .select([
  //         'SUM(CASE WHEN logHour.type = "GENERAL" THEN logHour.duration ELSE 0 END) as generalLogHours',
  //         'SUM(CASE WHEN logHour.type = "TASK" THEN logHour.duration ELSE 0 END) as taskLogHours',
  //         'Date(logHour.completedDate) as date',
  //         'SUM(logHour.duration) as totalLogHours',
  //       ])
  //       .where('user.id = :userId', { userId: body.user })
  //       .andWhere('logHour.completedDate >= :fromDate', { fromDate: startTime })
  //       .andWhere('logHour.completedDate <= :toDate', {
  //         toDate: endTime
  //       })
  //       .groupBy('Date(logHour.completedDate)');

  //   } else {
  //     logHours = createQueryBuilder(LogHour, 'logHour')
  //       .leftJoin('logHour.client', 'client')
  //       .leftJoin('logHour.user', 'user')
  //       .leftJoin('logHour.task', 'task')
  //       .select([
  //         'SUM(CASE WHEN logHour.type = "GENERAL" THEN logHour.duration ELSE 0 END) as generalLogHours',
  //         'SUM(CASE WHEN logHour.type = "TASK" THEN logHour.duration ELSE 0 END) as taskLogHours',
  //         'Date(logHour.completedDate) as date',
  //         'SUM(logHour.duration) as totalLogHours',
  //       ])
  //       .where('user.id = :userId', { userId: body.user })
  //       .andWhere('logHour.completedDate >= :fromDate', { fromDate: body.fromDate })
  //       .andWhere('logHour.completedDate <= :toDate', {
  //         toDate: toDateTomorrow
  //       })
  //       .groupBy('Date(logHour.completedDate)');

  //   }

  //   let i = moment(body.fromDate);
  //   let j = moment(body.toDate);
  //   let dates = [];

  //   while (i <= j) {
  //     dates.push(i.format('YYYY-MM-DD'));
  //     i = i.add(1, 'days');
  //   }

  //   let result = await logHours.getRawMany();

  //   let rows = dates.map((date) => {
  //     let logHour = result.find((logHour: any) => moment(logHour.date).format('YYYY-MM-DD') === date);
  //     return {
  //       ...logHour,
  //       date: moment(date).format('DD-MM-YYYY'),
  //       generalLogHours: logHour ? getDuration(logHour.generalLogHours) : '00:00',
  //       taskLogHours: logHour ? getDuration(logHour.taskLogHours) : '00:00',
  //       totalLogHours: logHour ? getDuration(logHour.totalLogHours) : '00:00',
  //     };
  //   });

  //   this.eventEmitter.emit(Event_Actions.REPORT_GENERATED, { ...result, query, body, organizationid });

  //   return {
  //     totalLogHours: totalLogHours[0]?.duration ? getHours(totalLogHours[0].duration) : 0,
  //     result: rows,
  //   };
  // }

  async getEmployeeLogHours(body: getEmployeeLogHoursDto, user) {
    const entityManager = getManager();
    const { users, toDate, fromDate, type, billableType } = body;
    const query = 'getEmployeeLogHours';
    const sqlQuery = `SELECT organization_id FROM user where id IN (${[...users]}) ;`;
    let orgId = await entityManager.query(sqlQuery);
    let organizationid = orgId[0].organization_id;

    let clientQuery = `SELECT id FROM client WHERE organization_id = ${organizationid};`;
    let clientIds = await entityManager.query(clientQuery);
    const clientIdList = clientIds.map((row) => parseInt(row.id));

    if (new Date(body.fromDate) > new Date(body.toDate)) {
      throw new BadRequestException('From date must be less than or equal to to date');
    }
    let dateWiseEmployeeLoghours;

    try {
      if (type !== 'ALL') {
        dateWiseEmployeeLoghours = await getManager().query(`WITH RECURSIVE date_range AS (
          SELECT DATE('${fromDate}') AS date
          UNION ALL
          SELECT date + INTERVAL 1 DAY
          FROM date_range
          WHERE date < DATE('${toDate}')
        ),
        user_list AS (
            SELECT id AS user_id
            FROM user
            WHERE id IN (${users})
        )
        SELECT u.full_name,
              ul.user_id,
              d.date,
              COALESCE(SUM(lh.duration), 0) AS total_duration
        FROM user_list ul
        CROSS JOIN date_range d
        LEFT JOIN log_hour lh 
          ON ul.user_id = lh.user_id 
          AND d.date = DATE(lh.completed_date)
          AND lh.type = '${type}' 
        LEFT JOIN task t
            ON lh.task_id = t.id 
            AND (
              CASE 
                  WHEN '${billableType}' = 'BILLABLE' THEN t.billable = true
                  WHEN '${billableType}' = 'NON-BILLABLE' THEN t.billable = false
                  ELSE 1 = 1
              END
            )
        LEFT JOIN 
            user u 
            ON u.id = ul.user_id
        GROUP BY u.full_name, ul.user_id, d.date
        ORDER BY d.date, ul.user_id;
        `);
      } else {
        dateWiseEmployeeLoghours = await getManager().query(`WITH RECURSIVE date_range AS (
          SELECT DATE('${fromDate}') AS date
          UNION ALL
          SELECT date + INTERVAL 1 DAY
          FROM date_range
          WHERE date < DATE('${toDate}')
        ),
        user_list AS (
          SELECT id AS user_id
          FROM user
          WHERE id IN (${users})
        )
        SELECT u.full_name,
              ul.user_id,
              d.date,
              COALESCE(SUM(lh.duration), 0) as total_duration
        FROM user_list ul
        CROSS JOIN date_range d
        LEFT JOIN log_hour lh 
          ON ul.user_id = lh.user_id 
          AND d.date = DATE(lh.completed_date)
          LEFT JOIN task t
            ON lh.task_id = t.id
           
        LEFT JOIN user u 
            ON u.id = ul.user_id
            WHERE t.parent_task_id is null
        GROUP BY u.full_name, ul.user_id, d.date
        ORDER BY d.date, ul.user_id
       
        ;
        `);
      }
    } catch (error) {
      if (
        error?.sqlMessage ===
        'Recursive query aborted after 1001 iterations. Try increasing @@cte_max_recursion_depth to a larger value.'
      ) {
        throw new BadRequestException(
          `The maximum time frame for viewing the report is between 1 day and 999 days`,
        );
      } else {
        throw new Error(`${error?.sqlMessage}`);
      }
    }

    this.eventEmitter.emit(Event_Actions.REPORT_GENERATED, {
      ...dateWiseEmployeeLoghours,
      query,
      body,
      organizationid,
      user,
    });
    return dateWiseEmployeeLoghours;
  }

  async getEmployeeTimeSheet(body: any, userId: number) {
    const user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
    const users = await User.find({ where: { organization: user.organization } });
    const organizationPreferenses = await OrganizationPreferences.findOne({
      where: { organization: user.organization },
    });
    const holidayPreferences: any = organizationPreferenses?.holidayPreferences;
    let standardWorkingHours: any = '00';
    if (holidayPreferences) {
      const updateOvertime = holidayPreferences?.updateovertime;
      if (updateOvertime) {
        const hoursInMilliseconds = parseInt(updateOvertime.hours.value) * 60 * 60 * 1000;
        const minutesInMilliseconds = parseInt(updateOvertime.minutes.value) * 60 * 1000;

        standardWorkingHours = hoursInMilliseconds + minutesInMilliseconds;
      }
    }
    const TimeSheetReport = [];
    for (let orgUser of body.users) {
      const sql = `
      SELECT 
        log_hour.user_id as userId,
        user.full_name as fullName,
        user.mobile_number as mobileNumber,
        DATE(log_hour.completed_date) AS logDate,
        SUM(CASE WHEN log_hour.type = 'GENERAL' THEN log_hour.duration ELSE 0 END) AS generalLogHours,
        SUM(CASE WHEN log_hour.type = 'TASK' THEN log_hour.duration ELSE 0 END) AS taskLogHours,
        @standardWorkingHours := ${standardWorkingHours} AS standardWorkingHours,
        @totalLoggedHours := SUM(log_hour.duration) AS totalLoggedHours,
        @overtime := GREATEST(@totalLoggedHours - @standardWorkingHours, 0) AS overtime,
        @undertime := GREATEST(@standardWorkingHours  - @totalLoggedHours, 0) AS undertime
      FROM log_hour
      JOIN user ON log_hour.user_id = user.id
      WHERE log_hour.user_id = ? AND DATE(log_hour.completed_date) = ?
      GROUP BY log_hour.user_id, DATE(log_hour.completed_date)
    `;
      // Execute the SQL query with parameters
      const result = await getManager().query(sql, [orgUser, body.date]);
      if (result[0]) {
        TimeSheetReport.push(result[0]);
      } else {
        const user = await User.findOne({ where: { id: orgUser } });

        TimeSheetReport.push({
          userId: user.id,
          fullName: user.fullName,
          mobileNumber: user.mobileNumber,
          logDate: body.date,
          generalLogHours: 0,
          taskLogHours: 0,
          standardWorkingHours: standardWorkingHours,
          totalLoggedHours: 0,
          overtime: 0,
          undertime: standardWorkingHours,
        });
      }
    }
    return TimeSheetReport;
  }
  async getEmployeeTimeSheetReport(body: any, userId: number) {

    for (let orgUser of body.users) {
      const sql = `SELECT 
  a.user_id, 
  a.checkin_time, 
  a.checkout_time,
  a.hours_logged,
  a.type, 
  DATE(a.attendance_date) AS attendance_date,
  SUM(CASE 
      WHEN l.type = 'GENERAL' AND DATE(l.completed_date) = DATE(a.attendance_date) 
      THEN l.duration ELSE 0 END) AS generalLogHours,
  SUM(CASE 
      WHEN l.type = 'TASK' AND DATE(l.completed_date) = DATE(a.attendance_date) 
      THEN l.duration ELSE 0 END) AS taskLogHours
FROM 
  attendance a 
JOIN 
  log_hour l 
ON 
  a.user_id = l.user_id
WHERE 
  a.user_id IN (${body.users}) 
  AND (a.attendance_date >= '${body.fromDate}' AND a.attendance_date <= '${body.toDate}')
GROUP BY 
  a.attendance_date, a.user_id, a.checkin_time, a.checkout_time, a.type;
 `


      // Execute the SQL query with parameters
      const result = await getManager().query(sql);
      return result

    }
  }
  async exportEmployeeTimeSheet(body: any, userId: number) {
    const newQuery = { ...body, offset: 0, limit: 100000000 };
    let usersData = await this.getEmployeeTimeSheet(newQuery, userId);

    const formatDuration = (duration) => {
      if (duration === 0) {
        return '00:00';
      }

      const hours = Math.floor(duration / (1000 * 60 * 60));
      const minutes = Math.floor((duration % (1000 * 60 * 60)) / (1000 * 60));

      const formattedHours = String(hours).padStart(2, '0');
      const formattedMinutes = String(minutes).padStart(2, '0');

      return `${formattedHours}:${formattedMinutes}`;
    };
    if (!usersData.length) throw new BadRequestException('No Data for Export');

    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Timesheet');
    const headers = [
      { header: 'S.No', key: 'serialNo' },
      { header: 'Employee Name', key: 'fullName' },
      { header: 'Standard Working Hours', key: 'standardWorkingHours' },
      { header: 'General Log Hrs', key: 'generalLogHours' },
      { header: 'Task Log Hrs', key: 'taskLogHours' },
      { header: 'Total Logged Hrs', key: 'totalLoggedHours' },
      { header: 'Undertime', key: 'undertime' },
      { header: 'Overtime', key: 'overtime' },
      { header: 'Description', key: 'description' }


    ]


    worksheet.columns = headers;

    let serialCounter = 1; // Initialize a counter

    // Initialize array to store max lengths for each column
    const columnMaxLengths = Array(headers.length).fill(0);

    usersData.forEach((user) => {
      function capitalizeFirstLetter(string) {
        return string.charAt(0).toUpperCase() + string.slice(1);
      }
      const rowData = {
        serialNo: serialCounter++,
        fullName: getTitle(user['fullName']),
        standardWorkingHours: formatDuration(1 * user['standardWorkingHours']),
        generalLogHours: formatDuration(user['generalLogHours']),
        taskLogHours: formatDuration(user['taskLogHours']),
        totalLoggedHours: formatDuration(user['totalLoggedHours']),
        undertime: formatDuration(user['undertime']),
        overtime: formatDuration(user['overtime'])
      }


      const row = worksheet.addRow(rowData);


      worksheet.columns.forEach((column, colIndex) => {
        const headerLength = column.header?.length || 0; // Length of header
        const cellLength = rowData[column.key]?.toString().length || 0; // Length of cell value
        columnMaxLengths[colIndex] = Math.max(columnMaxLengths[colIndex] || 0, headerLength, cellLength);
      });
    });

    // Apply column widths with padding
    worksheet.columns.forEach((column, colIndex) => {
      column.width = columnMaxLengths[colIndex] + 3; // Add padding for readability
    });

    // Style the header row and center alignment
    const headerRow = worksheet.getRow(1);
    headerRow.font = { bold: true };
    headerRow.eachCell((cell) => {
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '64B5F6' },
      };
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' },
      };
      cell.alignment = { horizontal: 'center', vertical: 'middle' };
    });

    worksheet.columns.forEach((column) => {
      if (column.key === 'taskName' || column.key === 'members' || column.key === 'taskLeaders') {
        column.width = 50;
        column.alignment = { wrapText: true, horizontal: 'center', vertical: 'middle' };
      } else {
        column.alignment = { horizontal: 'center', vertical: 'middle' };
      }
    });

    // Apply wrapText for all rows
    worksheet.eachRow((row) => {
      row.eachCell((cell) => {
        cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true }; // Center alignment and wrap text
      });
    });

    // Freeze the header row
    worksheet.views = [{ state: 'frozen', ySplit: 1 }];

    const buffer = await workbook.xlsx.writeBuffer();
    return buffer;
  }
  async getUserTimesheet(query: any) {
    const userId = query?.userId;

    let user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
    let logHours = createQueryBuilder(LogHour, 'logHour')
      .leftJoin('logHour.user', 'user')
      .leftJoin('user.organization', 'organization')
      .leftJoinAndSelect('logHour.task', 'task')
      .leftJoinAndSelect('logHour.client', 'client')
      .leftJoinAndSelect('logHour.clientGroup', 'clientGroup')
      .where('user.id = :userId', { userId })
      .andWhere('organization.id = :orgId', { orgId: user.organization.id })
      .andWhere('logHour.status = :status', { status: TimerStatus.STOPPED })
      .andWhere('logHour.duration != :duration', { duration: 0 })
      .andWhere('task.parentTask is null')
      .orderBy('logHour.completedDate', 'DESC');

    if (query.fromDate && query.toDate) {
      const { startTime, endTime } = dateFormation(query.fromDate, query.toDate);
      if (query.fromDate) {
        logHours.andWhere('logHour.completedDate >= :fromDate', { fromDate: startTime });
      }

      if (query.toDate) {
        logHours.andWhere('logHour.completedDate <= :toDate', { toDate: endTime });
      }
    } else if (query.date) {
      logHours.andWhere('DATE(logHour.completedDate) = :completedDate', {
        completedDate: query.date,
      });
    }

    // if (query.date) {
    //   logHours.andWhere('DATE(logHour.completedDate) = :completedDate', {
    //     completedDate: query.date,
    //   });
    // }

    let data = await logHours.getMany();

    return data;
  }



  async exportTimeSheetReport(userId: number, body: getLoghoursDto) {
    const newQuery = { ...body, offset: 0, limit: 100000000 };
    let loghours = await this.getUserTimesheet(newQuery);


    if (!loghours?.length) throw new BadRequestException('No Data for Export');

    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Loghour');
    const headers = [
      { header: 'S.No', key: 'serialNo' },
      { header: 'Date', key: 'Date' },
      { header: 'Client / Client Group', key: 'clientName' },
      { header: 'Type', key: 'Type' },
      { header: 'Task Id', key: 'TaskId' },
      { header: 'Task Name | Title', key: 'TaskNameTitle' },
      { header: 'Start Time', key: 'startTime' },
      { header: 'End Time', key: 'endTime' },
      { header: 'Duration', key: 'Duration' },
      { header: 'Status', key: 'status' },
      { header: 'Description', key: 'description' },

    ]


    worksheet.columns = headers;

    let serialCounter = 1; // Initialize a counter

    // Initialize array to store max lengths for each column
    const columnMaxLengths = Array(headers.length).fill(0);

    loghours.forEach((loghour) => {
      function formatDuration(durationInMillis) {
        const hours = Math.floor(durationInMillis / 3600000); // 1 hour = 3600000 milliseconds
        const minutes = Math.floor((durationInMillis % 3600000) / 60000); // 1 minute = 60000 milliseconds

        const formattedHours = hours.toString().padStart(2, '0');
        const formattedMinutes = minutes.toString().padStart(2, '0');

        return `${formattedHours}:${formattedMinutes}`;
      }

      function capitalizeFirstLetter(string) {
        return string.charAt(0).toUpperCase() + string.slice(1);
      }
      const rowData = {
        serialNo: serialCounter++,
        Date: moment(loghour?.completedDate).format('DD-MM-YYYY'),
        clientName: loghour?.client ? loghour?.client.displayName : loghour?.clientGroup?.displayName,
        Type: capitalizeFirstLetter(loghour.type.toLowerCase()),
        TaskId: loghour?.task?.taskNumber,
        TaskNameTitle: loghour?.task ? loghour?.task?.name : loghour?.title,
        startTime: loghour?.startTime ||
          (loghour?.startTimeNew ? moment(loghour.startTimeNew, "HH:mm:ss").format("hh:mm A") : "--"),
        endTime: loghour?.endTime ||
          (loghour?.endTimeNew ? moment(loghour.endTimeNew, "HH:mm:ss").format("hh:mm A") : "--"),

        Duration: formatDuration(loghour?.duration),
        description: loghour?.description,
        status: loghour?.approvalStatus


      }


      const row = worksheet.addRow(rowData);
      const statusCell = row.getCell('status');
      switch (rowData.status?.toLowerCase()) {
        case 'pending':
          statusCell.font = { color: { argb: 'FFA500' }, bold: true }; // Orange
          break;
        case 'rejected':
          statusCell.font = { color: { argb: 'F63338' }, bold: true };
          break;
        case 'approved':
          statusCell.font = { color: { argb: '008000' }, bold: true };
          break;
        default:
          statusCell.font = { color: { argb: '000000' }, bold: true };
          break;
      }

      worksheet.columns.forEach((column, colIndex) => {
        const headerLength = column.header?.length || 0; // Length of header
        const cellLength = rowData[column.key]?.toString().length || 0; // Length of cell value
        columnMaxLengths[colIndex] = Math.max(columnMaxLengths[colIndex] || 0, headerLength, cellLength);
      });
    });

    // Apply column widths with padding
    worksheet.columns.forEach((column, colIndex) => {
      column.width = columnMaxLengths[colIndex] + 3; // Add padding for readability
    });

    // Style the header row and center alignment
    const headerRow = worksheet.getRow(1);
    headerRow.font = { bold: true };
    headerRow.eachCell((cell) => {
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '64B5F6' },
      };
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' },
      };
      cell.alignment = { horizontal: 'center', vertical: 'middle' };
    });

    worksheet.columns.forEach((column) => {
      if (column.key === 'TaskNameTitle' || column.key === 'taskLeaders') {
        column.width = 50;
        column.alignment = { wrapText: true, horizontal: 'center', vertical: 'middle' };
      } else if (column.key === 'description') {
        column.width = 100;
        column.alignment = { wrapText: true, horizontal: 'center', vertical: 'middle' };
      }
      else {
        column.alignment = { horizontal: 'center', vertical: 'middle' };
      }
    });

    // Apply wrapText for all rows
    worksheet.eachRow((row) => {
      row.eachCell((cell) => {
        cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true }; // Center alignment and wrap text
      });
    });

    // Freeze the header row
    worksheet.views = [{ state: 'frozen', ySplit: 1 }];

    const buffer = await workbook.xlsx.writeBuffer();
    return buffer;
  }

  async getEmployeeAttendance(body: getEmployeeAttendanceDto, loginuser) {
    const { fromMonth, toMonth, users } = body;
    const entityManager = getManager();
    const sqlQuery = `SELECT organization_id FROM user where id IN (${[...users]}) ;`;
    let orgId = await entityManager.query(sqlQuery);
    let organizationid = orgId[0].organization_id;

    const startingDateOfMonth = moment(fromMonth, 'MM-YYYY').startOf('month').format('YYYY-MM-DD');
    const lastDateOfMonth = moment(toMonth, 'MM-YYYY').endOf('month').format('YYYY-MM-DD');
    const user = await User.createQueryBuilder('user')
      .leftJoinAndSelect('user.profile', 'profile')
      .leftJoinAndSelect('user.role', 'role')
      .where('user.id IN (:...ids)', { ids: users })
      .getMany();
    const getAttendance = await createQueryBuilder(Attendance, 'attendance')
      .leftJoin('attendance.organization', 'organization')
      .where('attendance.userId IN (:...userIds)', { userIds: users })
      .andWhere('attendance.attendanceDate >= :fromMonth', { fromMonth: startingDateOfMonth })
      .andWhere('attendance.attendanceDate <= :toMonth', { toMonth: lastDateOfMonth })
      .getMany();
    const userWiseData = users.reduce((acc, userId) => {
      const userData = user.filter((item) => item.id === userId);
      acc[userId] = {
        Present: 0,
        Absent: 0,
        Leave: 0,
        PendingLeave: 0,
        ApprovedLeave: 0,
        RejectedLeave: 0,
        employeeId: userData[0].profile.employeeId,
        role: userData[0].role.name,
        username: userData[0].fullName,
      };
      return acc;
    }, {});

    // getAttendance.map((item) => {
    //   userWiseData[item.userId][item.type] = userWiseData[item.userId][item.type] + 1;
    // });

    getAttendance.map((item) => {
      if (item.type === 'Leave') {
        userWiseData[item.userId][item.type] += 1;
        // Check the status of the leave and increment accordingly
        if (item.status === 'PENDING') {
          userWiseData[item.userId].PendingLeave += 1;
        } else if (item.status === 'APPROVED' || !item.status) {
          userWiseData[item.userId].ApprovedLeave += 1;
        } else if (item.status === 'REJECTED') {
          userWiseData[item.userId].RejectedLeave += 1;
        }

      } else {
        // Increment the general count for Present, Absent, or Leave
        userWiseData[item.userId][item.type] += 1;
      }
    });

    let getEmployeeAttendance;
    const query = 'getEmployeeAttendance';
    this.eventEmitter.emit(Event_Actions.REPORT_GENERATED, {
      ...getEmployeeAttendance,
      query,
      body,
      organizationid,
      user: loginuser,
    });
    return userWiseData;
  }
  async getEmployeeAttendanceandTimesheet(body: getEmployeeAttendanceandTimesheetDto, loginuser) {
    const { fromDate, toDate, users, holidayscount, countOfSelectedDates } = body;
    const entityManager = getManager();

    // Query to get the organization ID based on user IDs
    const sqlQuery = `SELECT organization_id FROM user where id IN (${[...users]}) ;`;
    let orgId = await entityManager.query(sqlQuery);
    let organizationid = orgId[0].organization_id;

    const startingDateOfRange = moment(fromDate).startOf('day').format('YYYY-MM-DD');
    const lastDateOfRange = moment(toDate).endOf('day').format('YYYY-MM-DD');

    const user = await User.createQueryBuilder('user')
      .leftJoinAndSelect('user.profile', 'profile')
      .leftJoinAndSelect('user.role', 'role')
      .where('user.id IN (:...ids)', { ids: users })
      .getMany();
    // Get attendance data within the specified date range
    const getAttendance = await createQueryBuilder(Attendance, 'attendance')
      .leftJoin('attendance.organization', 'organization')
      .where('attendance.userId IN (:...userIds)', { userIds: users })
      .andWhere('attendance.attendanceDate >= :fromDate', { fromDate: startingDateOfRange })
      .andWhere('attendance.attendanceDate <= :toDate', { toDate: lastDateOfRange })
      .getMany();
    const userWiseData = users.reduce((acc, userId) => {
      const userData = user.filter((item) => item.id === userId);
      acc[userId] = {
        Present: 0,
        Absent: 0,
        Leave: 0,
        employeeId: userData[0].profile.employeeId,
        role: userData[0].role.name,
        username: userData[0].fullName,
        holidayscount: holidayscount,
        fromDate: fromDate,
        toDate: toDate,
        getAttendance: getAttendance,
        countOfSelectedDates: countOfSelectedDates,
      };
      return acc;
    }, {});

    // Map through the attendance records and update the counts
    getAttendance.map((item) => {
      userWiseData[item.userId][item.type] = userWiseData[item.userId][item.type] + 1;
    });

    let getEmployeeAttendance;
    const query = 'getEmployeeAttendanceTimesheet';
    this.eventEmitter.emit(Event_Actions.REPORT_GENERATED, {
      ...getEmployeeAttendance,
      query,
      body,
      getAttendance,
      organizationid,
      user: loginuser,
    });

    return userWiseData;
  }

  getSaturdayType = (date) => {
    const day = moment(date, 'YYYY-MM-DD'); // ✅ correct
    const dom = day.date();

    if (dom <= 7) return 'first-saturday';
    if (dom <= 14) return 'second-saturday';
    if (dom <= 21) return 'third-saturday';
    if (dom <= 28) return 'fourth-saturday';
    return 'fifth-saturday';
  };

  async exportEmployeeAttendanceandTimesheet(body, userId) {
    const { fromDate, toDate } = body;
    let user = await User.findOne({ where: { id: userId }, relations: ['organization'] });

    const usersData = await createQueryBuilder(User, 'user')
      .leftJoinAndSelect('user.role', 'role')
      .leftJoinAndSelect('user.profile', 'profile')
      .where('user.id in (:...ids)', { ids: body.users })
      .getMany();

    const attendanceData = await createQueryBuilder(Attendance, 'attendence')
      .where('attendence.userId in (:...ids)', { ids: body.users })
      .andWhere('attendence.attendanceDate >= :fromDate', { fromDate: body.fromDate })
      .andWhere('attendence.attendanceDate <= :toDate', { toDate: body.toDate })
      .getMany();

    const logHoursData = await createQueryBuilder(LogHour, 'loghour')
      .leftJoinAndSelect('loghour.user', 'user')
      .where('user.id IN (:...ids)', { ids: body.users })
      .andWhere('CAST(loghour.completedDate AS DATE) >= :fromDate', { fromDate: body.fromDate })
      .andWhere('CAST(loghour.completedDate AS DATE) <= :toDate', { toDate: body.toDate })
      .getMany();


    const daysDifference = moment(toDate).diff(moment(fromDate), 'days') + 1;
    let holidaysCount = 0;

    const workbook = new ExcelJS.Workbook();
    const summarySheet = workbook.addWorksheet('Summary');

    const formattedStartDate = moment(fromDate).format('DD/MM/YYYY');
    const formattedEndDate = moment(toDate).format('DD/MM/YYYY');

    summarySheet.mergeCells('A1:H1');
    const titleRow = summarySheet.getCell('A1');
    titleRow.value = 'Employee Attendance and Timesheet Report';
    titleRow.alignment = { horizontal: 'center' };
    titleRow.font = { bold: true, size: 14 };

    summarySheet.mergeCells('A2:H2');
    const periodRow = summarySheet.getCell('A2');
    periodRow.value = `Period ${formattedStartDate} to ${formattedEndDate}`;
    periodRow.alignment = { horizontal: 'center' };
    periodRow.font = { bold: true, size: 14, color: { argb: '0000FF' } };

    const headerRow = [
      'Employee ID',
      'Role',
      'User Name',
      'Total Days',
      // 'Holidays',
      'Holidays',
      'Holiday Presents',
      'Present',
      'Absent',
      'Leave',
      'Blanks',
    ];
    summarySheet.addRow(headerRow);
    headerRow.forEach((col, index) => {
      summarySheet.getCell(3, index + 1).font = { bold: true };
      summarySheet.getCell(3, index + 1).alignment = { horizontal: 'center', vertical: 'middle' };
    });

    const generateDateArray = (start, end) => {
      const dates = [];
      let currentDate = moment(start);
      const lastDate = moment(end);

      while (currentDate.isSameOrBefore(lastDate)) {
        dates.push(currentDate.format('DD-MM-YYYY'));
        currentDate.add(1, 'day');
      }

      return dates;
    };

    const orgPreferences: any = await OrganizationPreferences.findOne({
      where: { organization: user.organization.id },
    });

    let holidays: any[] = [];
    let weekendData: any[] = [];
    if (orgPreferences?.holidayPreferences) {
      const currentYear = moment().format('YYYY');
      holidays = orgPreferences.holidayPreferences.addholiday.filter(
        (h: any) => moment(h.date).format('YYYY') === currentYear,
      );
      weekendData = orgPreferences.holidayPreferences.updateweekend;
    }

    const holidaysArray = holidays.map((h) => moment(h.date).format('DD-MM-YYYY'));
    const weekendDataS = weekendData.map((w) => w.value);
    const holidayDays = orgPreferences?.holidayPreferences?.updateweekend?.map(
      (item) => item.label,
    );
    const holidaysDates = orgPreferences?.holidayPreferences?.addholiday?.map((item) =>
      moment(item.date).format('DD-MM-YYYY'),
    );

    const dateArray = generateDateArray(fromDate, toDate)
    let userData = {};
    for (const user of usersData) {
      userData[user.id] = {
        present: 0,
        absent: 0,
        leave: 0,
        holiday: 0,
        holidayPresent: 0,
        employeeId: user.profile.employeeId,
        role: user.role.name,
        username: user.fullName,
        blank: 0,
      };
    }
    const formatDuration = (milliseconds) => {
      const totalMinutes = Math.floor(milliseconds / 60000); // Convert milliseconds to minutes
      const hours = Math.floor(totalMinutes / 60); // Extract hours
      const minutes = totalMinutes % 60; // Remaining minutes
      return `${String(hours).padStart(2, '0')}:${String(minutes).padStart(2, '0')}`;
    };

    // for (const date of dateArray) {
    //   const dayName = moment(date, 'DD-MM-YYYY').format('dddd');
    //   for (const user of body.users) {
    //     const eachDateUser = {
    //       date: date,
    //       day: dayName,
    //     };
    //     const attendenceFilter = attendanceData.filter((item) => {
    //       return (
    //         item.attendanceDate === moment(date, 'DD-MM-YYYY').format('YYYY-MM-DD') &&
    //         item.userId === user
    //       );
    //     });
    //     // eachDateUser['status'] =
    //     //   holidayDays.includes(dayName) || holidaysDates.includes(date)
    //     //     ? 'Holiday'
    //     //     : attendenceFilter[0]?.type;
    //     eachDateUser['status'] =
    //       holidaysDates.includes(date) || // official holidays
    //       holidayDays.includes(dayName) || // weekends like Sunday
    //       (dayName === 'saturday' && weekendDataS.includes(this.getSaturdayType(date))) // 2nd/4th Saturday
    //         ? 'Holiday'
    //         : attendenceFilter[0]?.type || 'NotMention';
    //     eachDateUser['checkInTime'] = attendenceFilter[0]?.checkin_time
    //       ? moment(attendenceFilter[0]?.checkin_time, 'YYYY-MM-DD HH:mm:ss.SSSSSS').format(
    //           'hh:mm A',
    //         )
    //       : '00:00';
    //     eachDateUser['checkOutTime'] = attendenceFilter[0]?.checkout_time
    //       ? moment(attendenceFilter[0]?.checkout_time, 'YYYY-MM-DD HH:mm:ss.SSSSSS').format(
    //           'hh:mm A',
    //         )
    //       : '00:00';
    //     eachDateUser['checkInAddress'] =
    //       (attendenceFilter[0]?.checkInAddress as any)?.displayName || '';
    //     eachDateUser['checkOutAddress'] =
    //       (attendenceFilter[0]?.checkOutAddress as any)?.displayName || '';
    //     if (attendenceFilter[0]?.checkin_time && attendenceFilter[0]?.checkout_time) {
    //       const checkin = moment(attendenceFilter[0]?.checkin_time, 'YYYY-MM-DD HH:mm:ss.SSSSSS');
    //       const checkout = moment(attendenceFilter[0]?.checkout_time, 'YYYY-MM-DD HH:mm:ss.SSSSSS');
    //       const duration = moment.duration(checkout.diff(checkin));
    //       const hours = String(Math.floor(duration.asHours())).padStart(2, '0');
    //       const minutes = String(duration.minutes()).padStart(2, '0');
    //       const timeDifference = `${hours}:${minutes}`;
    //       eachDateUser['workingHours'] = timeDifference;
    //     }
    //     if (attendenceFilter[0]?.type && eachDateUser['status'] !== 'Holiday') {
    //       if (attendenceFilter[0]?.type === AttendanceStatus.Present) {
    //         userData[user]['present'] += 1;
    //       } else if (attendenceFilter[0]?.type === AttendanceStatus.Absent) {
    //         userData[user]['absent'] += 1;
    //       } else if (attendenceFilter[0]?.type === AttendanceStatus.Leave) {
    //         userData[user]['leave'] += 1;
    //       }
    //     }
    //     const loghourFilter = logHoursData.filter((item) => {
    //       const completedDateFormatted = moment(item.completedDate).format('DD-MM-YYYY');
    //       return item.user.id === user && completedDateFormatted === date;
    //     });
    //     let taskLogHour = 0;
    //     let generalLogHour = 0;
    //     let totalLogHour = 0;
    //     loghourFilter.map((item) => {
    //       const duration = Number(item.duration);
    //       if (item.type === LogHourType.TASK) {
    //         taskLogHour += duration;
    //         totalLogHour += duration;
    //       } else if (item.type === LogHourType.GENERAL) {
    //         totalLogHour += duration;
    //         generalLogHour += duration;
    //       }
    //     });
    //     eachDateUser['taskLogHour'] = formatDuration(taskLogHour);
    //     eachDateUser['generalLogHour'] = formatDuration(generalLogHour);
    //     eachDateUser['totalLogHour'] = formatDuration(totalLogHour);

    //     userData[user][date] = eachDateUser;
    //     if (user === body.users[0]) {
    //       if (eachDateUser['status'] === 'Holiday') {
    //         holidaysCount += 1;
    //       }
    //     }
    //   }
    // }

    for (const date of dateArray) {
      const dayName = moment(date, 'DD-MM-YYYY').format('dddd').toLowerCase();
      for (const user of body.users) {
        const eachDateUser = { date, day: dayName };

        const attendenceFilter = attendanceData.filter(
          (item) =>
            item.attendanceDate === moment(date, 'DD-MM-YYYY').format('YYYY-MM-DD') &&
            item.userId === user,
        );

        const isOfficialHoliday = holidaysDates.includes(date);
        const isWeekend = holidayDays?.map((d) => d.toLowerCase())?.includes(dayName);
        const dateStr = moment(date, 'DD-MM-YYYY').format('YYY-MM-DD');
        const isSaturdayHoliday =
          dayName === 'saturday' && weekendDataS.includes(this.getSaturdayType(dateStr));

        const isHoliday = isOfficialHoliday || isWeekend || isSaturdayHoliday;

        eachDateUser['status'] = isHoliday ? 'Holiday' : attendenceFilter[0]?.type;

        eachDateUser['checkInTime'] = attendenceFilter[0]?.checkin_time
          ? moment(attendenceFilter[0]?.checkin_time, 'YYYY-MM-DD HH:mm:ss.SSSSSS').format(
              'hh:mm A',
            )
          : '00:00';
        eachDateUser['checkOutTime'] = attendenceFilter[0]?.checkout_time
          ? moment(attendenceFilter[0]?.checkout_time, 'YYYY-MM-DD HH:mm:ss.SSSSSS').format(
              'hh:mm A',
            )
          : '00:00';
        eachDateUser['checkInAddress'] =
          (attendenceFilter[0]?.checkInAddress as any)?.displayName || '';
        eachDateUser['checkOutAddress'] =
          (attendenceFilter[0]?.checkOutAddress as any)?.displayName || '';

        if (attendenceFilter[0]?.checkin_time && attendenceFilter[0]?.checkout_time) {
          const checkin = moment(attendenceFilter[0]?.checkin_time);
          const checkout = moment(attendenceFilter[0]?.checkout_time);
          const duration = moment.duration(checkout.diff(checkin));
          eachDateUser['workingHours'] = `${String(Math.floor(duration.asHours())).padStart(
            2,
            '0',
          )}:${String(duration.minutes()).padStart(2, '0')}`;
        }

        // ✅ Attendance counting with holiday logic
        if (isHoliday) {
          if (attendenceFilter[0]?.type === AttendanceStatus.Present) {
            userData[user]['holidayPresent'] += 1;
          } else {
            userData[user]['holiday'] += 1;
          }
        } else {
          if (attendenceFilter[0]?.type === AttendanceStatus.Present) {
            userData[user]['present'] += 1;
          } else if (attendenceFilter[0]?.type === AttendanceStatus.Absent) {
            userData[user]['absent'] += 1;
          } else if (attendenceFilter[0]?.type === AttendanceStatus.Leave) {
            userData[user]['leave'] += 1;
          } else {
            userData[user]['blank'] += 1;
          }
        }

        // LogHours processing remains unchanged
        const loghourFilter = logHoursData.filter((item) => {
          return item.user.id === user && moment(item.completedDate).format('DD-MM-YYYY') === date;
        });

        let taskLogHour = 0,
          generalLogHour = 0,
          totalLogHour = 0;
        loghourFilter.forEach((item) => {
          const duration = Number(item.duration);
          if (item.type === LogHourType.TASK) {
            taskLogHour += duration;
            totalLogHour += duration;
          } else if (item.type === LogHourType.GENERAL) {
            generalLogHour += duration;
            totalLogHour += duration;
          }
        });

        eachDateUser['taskLogHour'] = formatDuration(taskLogHour);
        eachDateUser['generalLogHour'] = formatDuration(generalLogHour);
        eachDateUser['totalLogHour'] = formatDuration(totalLogHour);

        userData[user][date] = eachDateUser;

        // Count holidays for summary (first user only)
        if (user === body.users[0] && isHoliday) {
          holidaysCount += 1;
        }
      }
    }

    // const summaryRows = Object.values(userData).map((user) => {
    //   return [
    //     {
    //       text: user['employeeId'],
    //       hyperlink: `#'${user['username']}'!A1`,
    //     },
    //     {
    //       text: user['role'],
    //       hyperlink: `#'${user['username']}'!A1`,
    //     },
    //     {
    //       text: user['username'],
    //       hyperlink: `#'${user['username']?.slice(0, 31)}'!A1`,
    //     },
    //     {
    //       text: daysDifference,
    //       hyperlink: `#'${user['username']}'!A1`,
    //     },

    //     // 1 * holidaysCount,
    //     1 * user['present'],
    //     1 * user['absent'],
    //     1 * user['leave'],
    //     1 * user['blank'],
    //     1 * user['holiday'],
    //     1 * user['holidayPresent'],
    //   ];
    // });

    const makeSafeSheetName = (name: string, userId: string) => {
      // Remove invalid characters and limit to 31 chars
      let safeName = name.replace(/[:\\/?*\[\]]/g, '').slice(0, 25);
      return `${safeName}_${userId}`; // append user id to make unique
    };

    const summaryRows = Object.values(userData).map((user) => {
      const safeSheetName = makeSafeSheetName(user['username'], user['employeeId']);
      return [
        { text: user['employeeId'], hyperlink: `#'${safeSheetName}'!A1` },
        { text: user['role'], hyperlink: `#'${safeSheetName}'!A1` },
        { text: user['username'], hyperlink: `#'${safeSheetName}'!A1` },
        { text: daysDifference, hyperlink: `#'${safeSheetName}'!A1` },
        1 * user['holiday'],
        1 * user['holidayPresent'],
        1 * user['present'],
        1 * user['absent'],
        1 * user['leave'],
        1 * user['blank'],
      ];
    });
    summaryRows.forEach((row, index) => {
      const newRow = summarySheet.addRow(row);
      const rowIndex = 4 + index; // Starting from row 4 (adjust as per your sheet)

      newRow.eachCell((cell, colIndex) => {
        cell.alignment = { horizontal: 'center', vertical: 'middle' };

        // Check if this is the username column (e.g., column 3)
        if (colIndex === 3) {
          if (cell.value) {
            cell.font = { color: { argb: '0000FF' }, underline: true }; // Blue and underlined
          }
        }
      });
    });

    summarySheet.columns = [
      { width: 15 }, // Employee ID
      { width: 35 }, // Role
      { width: 25 }, // User Name
      { width: 15 }, // Working Days
      { width: 15 }, // Holidays
      { width: 15 }, // Present
      { width: 15 }, // Absent
      { width: 15 }, // Leave
      { width: 15 },
      { width: 15 },
    ];

    for (const user of body.users) {
      const userssData = userData[user];
      if (!userssData) continue;
      const userRows = dateArray.map((date) => {
        const dayName = userssData[date]['day'];
        const checkInTime = userssData[date]['checkInTime']
          ? userssData[date]['checkInTime']
          : '00:00';
        const checkOutTime = userssData[date]['checkOutTime']
          ? userssData[date]['checkOutTime']
          : '00:00';
        const checkInAddress = userssData[date]['checkInAddress'] || '';
        const checkOutAddress = userssData[date]['checkOutAddress'] || '';
        const datess = userssData[date]['date'];
        const description = userssData[date]['status'];
        const standardHours = `${
          orgPreferences?.holidayPreferences?.updateovertime?.hours?.value || '00'
        }:${orgPreferences?.holidayPreferences?.updateovertime?.minutes?.value || '00'}`;
        const userworkingHours = userssData[date]['workingHours']
          ? userssData[date]['workingHours']
          : '00:00';
        const usertaskLogHour = userssData[date]['taskLogHour']
          ? userssData[date]['taskLogHour']
          : '00:00';
        const usergeneralLogHour = userssData[date]['generalLogHour']
          ? userssData[date]['generalLogHour']
          : '00:00';
        const usertotalLogHour = userssData[date]['totalLogHour']
          ? userssData[date]['totalLogHour']
          : '00:00';
        return [
          datess,
          dayName,
          description || ' ',
          checkInTime || '00:00',
          checkInAddress,
          checkOutTime || '00:00',
          checkOutAddress,
          userworkingHours || '00:00',
          standardHours,
          usertaskLogHour,
          usergeneralLogHour,
          usertotalLogHour,
        ];
      });

      let rows = [];
      const headers = [
        'Date',
        'Day',
        'Description',
        'Check In Time',
        'Check In Address',
        'Check Out Time',
        'Check Out Address',
        'Working Hours (HH:MM)',
        'Standard Hours (HH:MM)',
        'Task Log Hours',
        'General Log Hours',
        'Total Log Hours',
      ];
      rows.push(headers);
      // const safeSheetName = `${userssData['username']?.slice(0, 25)}_${user}`;
      const safeSheetName = makeSafeSheetName(userssData['username'], userssData['employeeId']);
      const userSheet = workbook.addWorksheet(safeSheetName);

      userSheet.addRow(headers);
      userSheet.addRows(userRows);
      userSheet.getRow(1).eachCell((cell) => {
        cell.font = { bold: true };
        cell.alignment = { horizontal: 'center', vertical: 'middle' };
      });

      userSheet.eachRow({ includeEmpty: true }, (row, rowNumber) => {
        if (rowNumber > 1) {
          row.eachCell((cell) => {
            cell.alignment = { horizontal: 'center', vertical: 'middle' };
          });
        }
      });

      userSheet.columns = [
        { width: 15 }, // Date
        { width: 12 }, // Day
        { width: 20 }, // Description
        { width: 15 }, // Check In Time
        { width: 40 }, // Check In Address
        { width: 15 }, // Check Out Time
        { width: 40 }, // Check Out Address
        { width: 22 }, // Working Hours (HH:MM)
        { width: 22 }, // Standard Hours (HH:MM)
        { width: 20 }, // Task Log Hours
        { width: 20 }, // General Log Hours
        { width: 20 }, // Total Log Hours
      ];
    }

    const fileBuffer = await workbook.xlsx.writeBuffer();
    return fileBuffer;
  }

  getDateRangeFormatted(inputDate) {
    const startTime = `${inputDate} ${`00:00:00`}`;
    const endTime = `${inputDate} ${`23:59:59`}`;

    return {
      startTime,
      endTime,
    };
  }

  // async exportEmployeeLogHours(body: getEmployeeLogHoursDto) {
  //   let logHours = await this.getEmployeeLogHours(body);

  //   let rows = logHours.result.map((logHour) => {
  //     return {
  //       'Date': logHour.date,
  //       'General Log Hours': logHour.generalLogHours,
  //       'Task Log Hours': logHour.taskLogHours,
  //       'Total Log Hours': logHour.totalLogHours,
  //     };
  //   });

  //   const worksheet = xlsx.utils.json_to_sheet(rows);
  //   const workbook = xlsx.utils.book_new();
  //   xlsx.utils.book_append_sheet(workbook, worksheet, 'Employee Log Hours');
  //   let file = xlsx.write(workbook, { type: 'buffer' });
  //   return file;
  // }

  // async exportEmployeeLogHours(body: getEmployeeLogHoursDynamicDto) {
  //   const uniqueUsers = body.uniqueUsers;
  //   const uniqueDates = body.uniqueDates;
  //   const tableData = body.tableData;

  //   let rows = [];
  //   const headers = ['User', ...uniqueDates, 'Total'];
  //   rows.push(headers);

  //   let columnTotals = Array(uniqueDates.length).fill(0);
  //   uniqueUsers.forEach((user) => {
  //     let row = [user];
  //     let totalMinutes = 0;

  //     uniqueDates.forEach((date, index) => {
  //       if (tableData[user] && tableData[user][date]) {
  //         const [hours, minutes] = tableData[user][date].split(':').map(Number);
  //         const totalMinutesForDate = hours * 60 + minutes;
  //         row.push(tableData[user][date]);

  //         totalMinutes += totalMinutesForDate;
  //         columnTotals[index] += totalMinutesForDate;
  //       } else {
  //         row.push('00:00');
  //       }
  //     });

  //     const totalHours = Math.floor(totalMinutes / 60);
  //     const totalMinutesRemaining = totalMinutes % 60;
  //     const totalFormatted = `${String(totalHours).padStart(2, '0')}:${String(
  //       totalMinutesRemaining,
  //     ).padStart(2, '0')}`;

  //     row.push(totalFormatted);
  //     rows.push(row);
  //   });
  //   let totalRow = ['Total'];

  //   let totalMinutes = columnTotals.reduce((total, minutes) => total + minutes, 0); // Calculate the total minutes

  //   const totalHours = Math.floor(totalMinutes / 60);
  //   const totalMinutesRemaining = totalMinutes % 60;
  //   const totalFormatted = `${String(totalHours).padStart(2, '0')}:${String(
  //     totalMinutesRemaining,
  //   ).padStart(2, '0')}`;
  //   totalRow.push(
  //     ...columnTotals.map((minutes) => {
  //       const hours = Math.floor(minutes / 60);
  //       const minutesRemaining = minutes % 60;
  //       return `${String(hours).padStart(2, '0')}:${String(minutesRemaining).padStart(2, '0')}`;
  //     }),
  //   );
  //   totalRow.push(totalFormatted);
  //   rows.push(totalRow);
  //   totalRow.pop();

  //   const worksheet = xlsx.utils.aoa_to_sheet(rows);
  //   const workbook = xlsx.utils.book_new();
  //   xlsx.utils.book_append_sheet(workbook, worksheet, 'Employee Log Hours');
  //   let file = xlsx.write(workbook, { type: 'buffer' });

  //   return file;
  // }
  // async exportEmployeeLogHours(body: getEmployeeLogHoursDynamicDto) {
  //   const uniqueUsers = body.uniqueUsers;
  //   const uniqueDates = body.uniqueDates;
  //   const tableData = body.tableData;

  //   let rows = [];
  //   const headers = ['User', ...uniqueDates, 'Total'];
  //   rows.push(headers);

  //   let columnTotals = Array(uniqueDates.length).fill(0);
  //   uniqueUsers.forEach((user) => {
  //     let row: any = [user];
  //     let totalMinutes = 0;

  //     uniqueDates.forEach((date, index) => {
  //       if (tableData[user] && tableData[user][date]) {
  //         const [hours, minutes] = tableData[user][date].split(':').map(Number);
  //         const totalMinutesForDate = hours * 60 + minutes;
  //         row.push({ t: 'n', v: totalMinutesForDate / 1440 }); // Excel time is a fraction of a day

  //         totalMinutes += totalMinutesForDate;
  //         columnTotals[index] += totalMinutesForDate;
  //       } else {
  //         row.push({ t: 'n', v: 0 }); // Representing '00:00' as 0
  //       }
  //     });

  //     const totalHours = Math.floor(totalMinutes / 60);
  //     const totalMinutesRemaining = totalMinutes % 60;
  //     const totalFormatted = `${String(totalHours).padStart(2, '0')}:${String(
  //       totalMinutesRemaining,
  //     ).padStart(2, '0')}`;

  //     row.push({ t: 'n', v: totalMinutes / 1440 }); // Total as a fraction of a day
  //     rows.push(row);
  //   });

  //   let totalRow: any = ['Total'];

  //   let totalMinutes = columnTotals.reduce((total, minutes) => total + minutes, 0); // Calculate the total minutes

  //   const totalHours = Math.floor(totalMinutes / 60);
  //   const totalMinutesRemaining = totalMinutes % 60;
  //   const totalFormatted = `${String(totalHours).padStart(2, '0')}:${String(
  //     totalMinutesRemaining,
  //   ).padStart(2, '0')}`;

  //   totalRow.push(
  //     ...columnTotals.map((minutes) => {
  //       const hours = Math.floor(minutes / 60);
  //       const minutesRemaining = minutes % 60;
  //       return { t: 'n', v: (hours * 60 + minutesRemaining) / 1440 }; // Time as a fraction of a day
  //     }),
  //   );

  //   totalRow.push({ t: 'n', v: totalMinutes / 1440 }); // Total as a fraction of a day
  //   rows.push(totalRow);
  //   totalRow.pop();

  //   // Create a worksheet with specific cell formatting
  //   const worksheet = xlsx.utils.aoa_to_sheet(rows);

  //   // Set cell format for time (Excel treats fractions of a day as time)
  //   Object.keys(worksheet).forEach((key) => {
  //     if (key[0] !== '!') {
  //       worksheet[key].z = 'hh:mm'; // Set format for time
  //     }
  //   });

  //   const workbook = xlsx.utils.book_new();
  //   xlsx.utils.book_append_sheet(workbook, worksheet, 'Employee Log Hours');
  //   let file = xlsx.write(workbook, { type: 'buffer' });

  //   return file;
  // }
  async exportEmployeeLogHours(body: getEmployeeLogHoursDynamicDto) {
    const uniqueUsers = body.uniqueUsers;
    const uniqueDates = body.uniqueDates;
    const tableData = body.tableData;

    let rows = [];
    const headers = ['User', ...uniqueDates, 'Total Log Hours'];
    rows.push(headers);

    let columnTotals = Array(uniqueDates.length).fill(0);
    uniqueUsers.forEach((user) => {
      let row: any = [user];
      let totalMinutes = 0;

      uniqueDates.forEach((date, index) => {
        if (tableData[user] && tableData[user][date]) {
          const [hours, minutes] = tableData[user][date].split(':').map(Number);
          const totalMinutesForDate = hours * 60 + minutes;
          row.push({ t: 'n', v: totalMinutesForDate / 1440 });

          totalMinutes += totalMinutesForDate;
          columnTotals[index] += totalMinutesForDate;
        } else {
          row.push({ t: 'n', v: 0 });
        }
      });

      row.push({ t: 'n', v: totalMinutes / 1440 });
      rows.push(row);
    });

    let totalRow: any = ['Total'];

    let totalMinutes = columnTotals.reduce((total, minutes) => total + minutes, 0);

    const totalHours = Math.floor(totalMinutes / 60);
    const totalMinutesRemaining = totalMinutes % 60;

    totalRow.push(
      ...columnTotals.map((minutes) => {
        const hours = Math.floor(minutes / 60);
        const minutesRemaining = minutes % 60;
        return { t: 'n', v: (hours * 60 + minutesRemaining) / 1440 };
      }),
    );

    totalRow.push({ t: 'n', v: totalMinutes / 1440 });
    rows.push(totalRow);
    totalRow.pop();

    const worksheet = xlsx.utils.aoa_to_sheet(rows);

    Object.keys(worksheet).forEach((key) => {
      if (key[0] !== '!') {
        worksheet[key].z = 'hh:mm';
      }
    });

    const totalColumnIndex = rows[0].length - 1;
    for (let i = 1; i < rows.length; i++) {
      const cellAddress = xlsx.utils.encode_cell({ r: i, c: totalColumnIndex });
      if (worksheet[cellAddress]) {
        worksheet[cellAddress].z = '[h]:mm';
      }
    }

    const workbook = xlsx.utils.book_new();
    xlsx.utils.book_append_sheet(workbook, worksheet, 'Log Hours');
    let file = xlsx.write(workbook, { type: 'buffer' });

    return file;
  }

  async exportEmployeeAttendance(body: getEmployeeAttendanceDto, loginuser) {
    let usersData = await this.getEmployeeAttendance(body, loginuser);

    if (!Object.values(usersData).length) {
      throw new BadRequestException('No Data for Export !');
    }

    let rows: any = Object.values(usersData).map((user) => {
      return {
        'Employee ID': user['employeeId'],
        'Employee Role': user['role'],
        'Employee Name': getTitle(user['username']),
        'Working Days': user['totalWorkingDays'],
        'Holidays': user['Holiday'],
        'Holiday Presents': user['HolidayPresent'],
        'Days Present': 1 * user['Present'],
        'Days Absent': 1 * user['Absent'],
        'Days Leave': 1 * user['Leave'],
        'Blanks': user['NotMention'],
      };
    });

    //  Holiday: 0,
    //     HolidayPresent: 0,
    //     PendingLeave: 0,
    //     ApprovedLeave: 0,
    //     RejectedLeave: 0,
    //     NotMention: 0, // 👈 new counter
    //     employeeId: u?.profile.employeeId,
    //     role: u?.role.name,
    //     username: u?.fullName,
    //     totalDaysInMonth: allDates?.length,

    const worksheet = xlsx.utils.json_to_sheet(rows);
    const workbook = xlsx.utils.book_new();
    xlsx.utils.book_append_sheet(workbook, worksheet, 'Attendance');
    let file = xlsx.write(workbook, { type: 'buffer' });

    return file;
  }

  async getClientsReport(userId: number, body: getClientsReportDto) {
    const { fromDate, toDate, category, subCategory, status } = body;

    const entityManager = getManager();
    const query = 'getClientsReport';
    const sqlQuery = `SELECT organization_id FROM user where id = ${userId} ;`;
    let orgId = await entityManager.query(sqlQuery);
    let organizationid = orgId[0].organization_id;

    let user = await User.findOne({ where: { id: userId }, relations: ['organization'] });

    let clients = createQueryBuilder(Client, 'client')
      .leftJoin('client.organization', 'organization')
      .where('organization.id = :organizationId', { organizationId: user.organization.id });

    if (category) {
      clients.andWhere('client.category = :category', { category });
    }

    if (subCategory) {
      clients.andWhere('client.subCategory = :subCategory', { subCategory });
    }

    if (fromDate) {
      clients.andWhere('client.createdAt >= :fromDate', { fromDate });
    }

    if (toDate) {
      clients.andWhere('client.createdAt <= :toDate', {
        toDate: moment(toDate).add(1, 'days').format('YYYY-MM-DD'),
      });
    }

    // if (typeof active === 'boolean') {
    //   clients.andWhere('client.status = :active', { active });
    //   // clients.andWhere('client.active = :active', { active });
    // }
    if (status === 'Active') {
      clients.andWhere('client.status = :status', { status: UserStatus.ACTIVE });
    }
    if (status === 'Inactive') {
      clients.andWhere('client.status = :status', { status: UserStatus.INACTIVE });
    }

    if (status === 'Deleted') {
      clients.andWhere('client.status = :status', { status: UserStatus.DELETED });
    }

    // return clients.getMany();
    let response = await clients.getMany();

    let groupResponse = [];
    if (!category) {
      let clientsGroup = createQueryBuilder(ClientGroup, 'clientGroup')
        .leftJoin('clientGroup.organization', 'organization')
        .where('organization.id = :organizationId', { organizationId: user.organization.id });

      if (fromDate) {
        clientsGroup.andWhere('clientGroup.createdAt >= :fromDate', { fromDate });
      }

      if (toDate) {
        clientsGroup.andWhere('clientGroup.createdAt <= :toDate', {
          toDate: moment(toDate).add(1, 'days').format('YYYY-MM-DD'),
        });
      }
      if (status === 'Active') {
        clientsGroup.andWhere('clientGroup.status = :status', { status: UserStatus.ACTIVE });
      }
      if (status === 'Inactive') {
        clientsGroup.andWhere('clientGroup.status = :status', { status: UserStatus.INACTIVE });
      }

      if (status === 'Deleted') {
        clientsGroup.andWhere('clientGroup.status = :status', { status: UserStatus.DELETED });
      }

      groupResponse = await clientsGroup.getMany();
    }

    const totalData: any = [...response, ...groupResponse];
    totalData.sort(
      (a: any, b: any) => new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime(),
    );

    this.eventEmitter.emit(Event_Actions.REPORT_GENERATED, {
      ...totalData,
      query,
      body,
      organizationid,
      user,
    });

    return totalData;
  }
  async exportClientsReport(userId: number, body: getClientsReportDto) {
    let clients = await this.getClientsReport(userId, body);

    if (!clients.length) {
      throw new BadRequestException('No Data for Export !');
    }

    let rows = clients.map((client) => {
      function capitalizeFirstLetter(string) {
        return string.charAt(0).toUpperCase() + string.slice(1);
      }
      return {
        'Client ID / Client Group ID': client.clientId,
        'Client Category': getTitle(client?.category),
        'Client Sub-Category': getTitle(client?.subCategory),
        'Client Number / Client Group Number': client?.clientNumber,
        'Display Name': client?.displayName,
        'Trade Name': client?.tradeName,
        'Mobile #': `+${getCountryCode(client?.countryCode)} ${client?.mobileNumber}`,
        'Email ID': client?.email,
        'Status': capitalizeFirstLetter(client.status.toLowerCase()),
      };
    });

    const worksheet = xlsx.utils.json_to_sheet(rows);
    const workbook = xlsx.utils.book_new();
    xlsx.utils.book_append_sheet(workbook, worksheet, 'Clients');
    let file = xlsx.write(workbook, { type: 'buffer' });
    return file;
  }

  async getLeadsReport(userId: number, body: getLeadsReportDto) {
    const { createdAt, updatedAt, category, subCategory, status, search } = body;

    const entityManager = getManager();
    const query = 'getLeadsReport';
    const sqlQuery = `SELECT organization_id FROM user where id = ${userId} ;`;
    let orgId = await entityManager.query(sqlQuery);
    let organizationid = orgId[0].organization_id;

    let user = await User.findOne({ where: { id: userId }, relations: ['organization'] });

    let leads = createQueryBuilder(Lead, 'lead')
      .leftJoin('lead.organization', 'organization')
      .where('organization.id = :organizationId', { organizationId: user.organization.id });

    if (category) {
      leads.andWhere('lead.category = :category', { category });
    }

    if (subCategory) {
      leads.andWhere('lead.subCategory = :subCategory', { subCategory });
    }

    if (createdAt) {
      leads.andWhere('lead.createdAt >= :fromDate', { createdAt });
    }

    if (updatedAt) {
      leads.andWhere('lead.createdAt <= :toDate', {
        toDate: moment(updatedAt).add(1, 'days').format('YYYY-MM-DD'),
      });
    }

    if (status === 'Pending') {
      leads.andWhere('client.status = :status', { status: LeadStatusEnum.PENDING });
    }
    if (status === 'Converted') {
      leads.andWhere('client.status = :status', { status: LeadStatusEnum.CONVERTED });
    }

    if (search?.length) {
      leads.andWhere('lead.name like :search', { search: `%${search}%` });
    }

    let response = await leads.getMany();
    // this.eventEmitter.emit(Event_Actions.REPORT_GENERATED, {
    //   ...response,
    //   query,
    //   body,
    //   organizationid,
    //   user,
    // });

    return response;
  }

  async exportLeadsReport(userId: number, body: getLeadsReportDto) {
    // Fetch leads data
    const leads = await this.getLeadsReport(userId, body);
    if (!leads.length) throw new BadRequestException('No Data for Export');

    // Initialize a new workbook and worksheet
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Leads');

    // Define headers with keys and formatting
    const headers = [
      { header: 'S.No', key: 'serialNo' },
      { header: 'Category', key: 'category' },
      { header: 'Sub-Category', key: 'subCategory' },
      { header: 'Lead Name', key: 'leadName', width: 50, style: { alignment: { wrapText: true } } }, // Wrap text for Lead Name
      { header: 'Mobile #', key: 'mobile' },
      { header: 'Email Id', key: 'email' },
      { header: 'Created Date', key: 'createdDate' },
      { header: 'Status', key: 'status' },
    ];

    // Add headers to the worksheet
    worksheet.columns = headers;

    // Initialize an array to store the maximum lengths of each column (excluding 'Lead Name')
    const columnMaxLengths: number[] = Array(headers.length).fill(0);
    let serialCounter = 1; // Initialize a counter
    // Format and map leads data to rows
    leads.forEach((lead) => {
      function capitalizeFirstLetter(string: string) {
        return string.charAt(0).toUpperCase() + string.slice(1);
      }

      const category = CLIENT_CATEGORIES.find(item => item.value === lead?.category)?.label || lead?.category;


      const rowData = {
        serialNo: serialCounter++,// Assign and then increment the counter
        category: category,
        subCategory: getTitle(lead?.subCategory),
        leadName: lead.name,
        mobile: `+${getCountryCode(lead.countryCode)} ${lead?.mobileNumber}`,
        email: lead?.email,
        createdDate: formatDate(lead?.createdAt),
        status: capitalizeFirstLetter(lead.status.toLowerCase()),
      };

      // Add the row to the worksheet
      const row = worksheet.addRow(rowData);

      // Determine max column width (excluding 'Lead Name')
      worksheet.columns.forEach((column, index) => {
        const headerLength = column.header?.length || 0; // Get header length
        Object.keys(rowData).forEach((key) => {
          if (key === column.key && key !== 'leadName') {
            const cellLength = rowData[key].toString().length;
            columnMaxLengths[index] = Math.max(columnMaxLengths[index] || 0, cellLength, headerLength);
          }
        });
      });

      // Apply bold formatting to 'Status' column values
      row.getCell('status').font = { bold: true };

      // Apply wrapText to 'Lead Name' column
      row.getCell('leadName').alignment = { wrapText: true };

      // Apply conditional text color for 'Status' column
      if (rowData.status.toLowerCase() === 'converted') {
        row.getCell('status').font = { bold: true, color: { argb: 'FF00B050' } }; // Green text for 'converted'
      } else {
        row.getCell('status').font = { bold: true, color: { argb: 'FFFF0000' } }; // Red text for other statuses
      }
    });

    // Adjust column widths based on content length (excluding 'Lead Name')
    worksheet.columns.forEach((column, index) => {
      if (column.key !== 'leadName') {
        column.width = columnMaxLengths[index] + 1; // Add slight padding
      }
    });

    // Apply header styles and freeze top row
    const headerRow = worksheet.getRow(1);
    headerRow.font = { bold: true };
    headerRow.eachCell((cell) => {
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '64B5F6' }, // Blue background for header
      };
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' },
      };
    });
    worksheet.views = [{ state: 'frozen', ySplit: 1 }];

    // Generate and return the Excel file buffer
    const buffer = await workbook.xlsx.writeBuffer();
    return buffer;
  }



  async getDscReport(userId: number, body: getDscRegisterDto) {
    const { holderName, expiryDate, issuedDate, password, search } = body;

    const entityManager = getManager();
    const query = 'getLeadsReport';
    const sqlQuery = `SELECT organization_id FROM user where id = ${userId} ;`;
    let orgId = await entityManager.query(sqlQuery);
    let organizationid = orgId[0].organization_id;

    let user = await User.findOne({ where: { id: userId }, relations: ['organization', 'role'] });
    const ViewAll = user.role.permissions.some(
      (permission) => permission.slug === Permissions.VIEW_ALL_CLIENT_MANAGERS
    );
    const ViewAssigned = user.role.permissions.some(
      (permission) => permission.slug === Permissions.VIEW_ASSIGNED_CLIENT_MANAGERS
    );

    let dscregisters = createQueryBuilder(DscRegister, 'dscregister')
      .leftJoin('dscregister.organization', 'organization')
      .leftJoinAndSelect('dscregister.clients', 'clients')
      .leftJoin('clients.clientManagers', 'clientManagers')
      .leftJoinAndSelect('dscregister.clientGroups', 'clientGroups')
      .leftJoin('clientGroups.clientGroupManagers', 'clientGroupManagers')
      .where('organization.id = :organizationId', { organizationId: user.organization.id });

    if (holderName) {
      dscregisters.andWhere('dscregister.holderName = :holderName', { holderName });
    }

    if (!ViewAll && ViewAssigned) {
      dscregisters.andWhere(
        new Brackets(qb => {
          qb.where('clients.id IS NOT NULL AND clientManagers.id = :userId', { userId })
            .orWhere('clientGroups.id IS NOT NULL AND clientGroupManagers.id = :userId', { userId })
            .orWhere('clients.id IS NULL AND clientGroups.id IS NULL');
        })
      );
    } else if (!ViewAll && !ViewAssigned) {
      dscregisters.andWhere('clients.id IS NULL AND clientGroups.id IS NULL');
    }

    if (expiryDate) {
      dscregisters.andWhere('dscregister.expiryDate = :expiryDate', { expiryDate });
    }

    if (issuedDate) {
      dscregisters.andWhere('dscregister.issuedDate >= :fromDate', { issuedDate });
    }

    if (password) {
      dscregisters.andWhere('dscregister.password = :password', { password });
    }

    if (search?.length) {
      dscregisters.andWhere('dscregister.holderName like :search', { search: `%${search}%` });
    }

    // return clients.getMany();
    let response = await dscregisters.getMany();
    // this.eventEmitter.emit(Event_Actions.REPORT_GENERATED, {
    //   ...response,
    //   query,
    //   body,
    //   organizationid,
    //   user,
    // });

    return response;
  }
  async exportDscRegisterReport(userId: number, body: getDscRegisterDto) {
    const dscregisters = await this.getDscReport(userId, body);
    if (!dscregisters.length) {
      throw new BadRequestException('No Data for Export');
    }

    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('DSC Register');
    dscregisters.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());

    // Define headers for the worksheet
    const headers = [
      { header: 'S.No', key: 'serialNo' },
      { header: 'Client / Client Group', key: 'clientGroup' },
      { header: 'DSC Token #', key: 'tokenNumber' },
      { header: 'DSC Holder', key: 'holderName' },
      { header: 'Token Password', key: 'password' },
      { header: 'Certificate Expiry Date', key: 'expiryDate' },
      { header: 'Days Left to Expiry', key: 'calculateDaysLeft' },
      { header: 'Holder Designation', key: 'holderDesignation' },
      { header: 'Token Last Issued Date', key: 'issuedDate' },
      { header: 'Token Last Received Date', key: 'receivedDate' },
      { header: 'Mobile#', key: 'mobile' },
      { header: 'Email Id', key: 'email' },
      { header: 'PAN', key: 'panNumber' },
    ];

    // Add headers to the worksheet
    worksheet.columns = headers;
    let serialCounter = 1; // Initialize a counter
    // Format and map the data
    const formatDatedd = (dateString) => {
      if (!dateString || isNaN(new Date(dateString).getTime())) {
        return ""; // Return empty if the dateString is invalid or not provided
      }
      const date = new Date(dateString);
      const day = date.getDate().toString().padStart(2, '0');
      const month = (date.getMonth() + 1).toString().padStart(2, '0');
      const year = date.getFullYear();
      return `${day}-${month}-${year}`;
    };

    const calculateDaysLeft = (expiryDate: string): string => {
      if (!expiryDate || isNaN(new Date(expiryDate).getTime())) {
        return "No Expiry Date";
      }
      const currentDate = new Date();
      const expiry = new Date(expiryDate);
      const timeDifference = expiry.getTime() - currentDate.getTime();
      const daysLeft = Math.floor(timeDifference / (1000 * 60 * 60 * 24)) + 1;

      if (daysLeft > 0) {
        return `${daysLeft} ${daysLeft === 1 ? 'Day' : 'Days'} Left`;
      } else if (daysLeft === 0) {
        return "Expiring Today";
      } else {
        return `Expired (${Math.abs(daysLeft)} ${Math.abs(daysLeft) === 1 ? 'day' : 'days'})`;
      }
    };

    const formatDated = (dateString) => {
      if (!dateString || isNaN(new Date(dateString).getTime())) return '';
      const date = new Date(dateString);
      return `${date.getDate().toString().padStart(2, '0')}-${(date.getMonth() + 1)
        .toString()
        .padStart(2, '0')}-${date.getFullYear()}`;
    };

    dscregisters.forEach((dscregister) => {
      const daysLeft = calculateDaysLeft(dscregister?.expiryDate);

      // Add row with calculated days left
      const row = worksheet.addRow({
        serialNo: serialCounter++,// Assign and then increment the counter
        clientGroup: [...dscregister?.clients, ...dscregister?.clientGroups]
          .map((client) => client.displayName)
          .join(', '),
        tokenNumber: dscregister?.tokenNumber || ' ',
        holderName: dscregister?.holderName || ' ',
        password: dscregister?.password || ' ',
        expiryDate: formatDated(dscregister?.expiryDate) || ' ',
        calculateDaysLeft: daysLeft,
        holderDesignation: dscregister?.holderDesignation || ' ',
        issuedDate: formatDatedd(dscregister?.issuedDate) || " ",
        receivedDate: formatDatedd(dscregister?.receivedDate) || " ",
        panNumber: dscregister?.panNumber || ' ',
        mobile: `+${getCountryCode(dscregister.countryCode)} ${dscregister?.mobileNumber}` || " ",
        email: dscregister?.email || " ",
      });

      // Apply red color for expired dates
      if (daysLeft.includes("Expired")) {
        row.getCell('calculateDaysLeft').font = { color: { argb: 'FF0000' } }; // Red color
      }
    });

    // Style the header row
    const headerRow = worksheet.getRow(1);
    headerRow.font = { bold: true };
    headerRow.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };

    // Apply background color only for actual headers
    headers.forEach((_, index) => {
      const cell = headerRow.getCell(index + 1);
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '64B5F6' },
      };
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' },
      };
    });

    // Adjust column widths dynamically and apply alignment
    worksheet.columns.forEach((column) => {
      if (column.key === 'holderName' || column.key === 'clientGroup') {
        column.width = 50; // Fixed width for wrapped columns
        column.alignment = { wrapText: true, horizontal: 'center', vertical: 'middle' };
      } else {
        let maxLength = column.header.length; // Start with header length
        column.eachCell({ includeEmpty: true }, (cell) => {
          const cellValue = cell.value || '';
          const cellLength = cellValue.toString().length;
          if (cellLength > maxLength) {
            maxLength = cellLength;
          }
        });
        column.width = maxLength + 2; // Add padding for better readability
        column.alignment = { horizontal: 'center', vertical: 'middle' };
      }
    });

    // Apply alignment to all rows
    worksheet.eachRow((row) => {
      row.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
    });

    // Freeze the header row
    worksheet.views = [{ state: 'frozen', ySplit: 1 }];

    // Generate the Excel file as a buffer
    const buffer = await workbook.xlsx.writeBuffer();

    return buffer;
  }



  async getUsersReport(userId: number, body: getUsersDto) {
    const { fullName, mobileNumber, email, status, role, search } = body;

    const entityManager = getManager();
    const query = 'getUsersReport';
    const sqlQuery = `SELECT organization_id FROM user where id = ${userId} ;`;
    let orgId = await entityManager.query(sqlQuery);
    let organizationid = orgId[0].organization_id;

    let user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
    let roles = await Role.findOne({ where: { id: userId }, relations: ['organization'] });

    let users = createQueryBuilder(User, 'user')
      .leftJoin('user.organization', 'organization')
      .leftJoinAndSelect('user.role', 'role')
      .where('organization.id = :organizationId', { organizationId: user.organization.id })
      .andWhere('user.type = :type', { type: UserType.ORGANIZATION });

    if (fullName) {
      users.andWhere('user.fullName = :fullName', { fullName });
    }

    if (role?.length) {
      users.andWhere('role.name = :role', { role });
    }

    if (search?.length) {
      users.andWhere('user.fullName LIKE :search', {
        search: `%${search}%`,
      });
    }

    if (mobileNumber) {
      users.andWhere('user.mobileNumber = :mobileNumber', { mobileNumber });
    }

    if (email) {
      users.andWhere('user.email = :email', { email });
    }

    // return clients.getMany();
    let response = await users.getMany();
    this.eventEmitter.emit(Event_Actions.REPORT_GENERATED, {
      ...response,
      query,
      body,
      organizationid,
      user,
    });

    return response;
  }

  async exportUsersReport(userId: number, body: getUsersDto) {
    const newQuery = { ...body, offset: 0, limit: 100000000 };
    let users = await this.getUsersReport(userId, body);


    if (!users?.length) throw new BadRequestException('No Data for Export');

    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('User Invite');
    const headers = [
      { header: 'S.No', key: 'serialNo' },
      { header: 'User Name', key: 'fullName' },
      { header: 'Mobile #', key: 'mobileNumber' },
      { header: 'Email Id', key: 'email' },
      { header: 'Status', key: 'status' },


    ]


    worksheet.columns = headers;

    let serialCounter = 1; // Initialize a counter

    // Initialize array to store max lengths for each column
    const columnMaxLengths = Array(headers.length).fill(0);

    users.forEach((user) => {

      function capitalizeFirstLetter(string) {
        return string.charAt(0).toUpperCase() + string.slice(1);
      }
      const rowData = {
        serialNo: serialCounter++,
        fullName: user.fullName,
        mobileNumber: user.mobileNumber,
        email: user.email,
        status: capitalizeFirstLetter(user.status),

      }


      const row = worksheet.addRow(rowData);

      const statusCell = row.getCell('status');
      switch (rowData.status?.toLowerCase()) {
        case 'active':
          statusCell.font = { color: { argb: '008000' }, bold: true };
          break;
        default:
          statusCell.font = { color: { argb: 'F63338' }, bold: true };
          break;
      }
      worksheet.columns.forEach((column, colIndex) => {
        const headerLength = column.header?.length || 0; // Length of header
        const cellLength = rowData[column.key]?.toString().length || 0; // Length of cell value
        columnMaxLengths[colIndex] = Math.max(columnMaxLengths[colIndex] || 0, headerLength, cellLength);
      });
    });

    // Apply column widths with padding
    worksheet.columns.forEach((column, colIndex) => {
      column.width = columnMaxLengths[colIndex] + 3; // Add padding for readability
    });

    // Style the header row and center alignment
    const headerRow = worksheet.getRow(1);
    headerRow.font = { bold: true };
    headerRow.eachCell((cell) => {
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '64B5F6' },
      };
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' },
      };
      cell.alignment = { horizontal: 'center', vertical: 'middle' };
    });

    worksheet.columns.forEach((column) => {
      if (column.key === 'fullName') {
        column.width = 50;
        column.alignment = { wrapText: true, horizontal: 'center', vertical: 'middle' };
      } else {
        column.alignment = { horizontal: 'center', vertical: 'middle' };
      }
    });
    // Apply wrapText for all rows
    worksheet.eachRow((row) => {
      row.eachCell((cell) => {
        cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true }; // Center alignment and wrap text
      });
    });

    // Freeze the header row
    worksheet.views = [{ state: 'frozen', ySplit: 1 }];

    const buffer = await workbook.xlsx.writeBuffer();
    return buffer;
  }

  async getInviteUsersReport(userId: number, body: getUserInviteDto) {
    const { fullName, mobileNumber, email, status } = body;

    const entityManager = getManager();
    const query = 'getUsersReport';
    const sqlQuery = `SELECT organization_id FROM user where id = ${userId} ;`;
    let orgId = await entityManager.query(sqlQuery);
    let organizationid = orgId[0].organization_id;

    let user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
    let roles = await Role.findOne({ where: { id: userId }, relations: ['organization'] });

    let inviteuusers = createQueryBuilder(InvitedUser, 'inviteuser')
      .leftJoin('inviteuser.organization', 'organization')

      .where('organization.id = :organizationId', { organizationId: user.organization.id });

    if (fullName) {
      inviteuusers.andWhere('user.fullName = :fullName', { fullName });
    }

    if (mobileNumber) {
      inviteuusers.andWhere('user.mobileNumber = :mobileNumber', { mobileNumber });
    }

    if (email) {
      inviteuusers.andWhere('user.email = :email', { email });
    }

    // return clients.getMany();
    let response = await inviteuusers.getMany();
    this.eventEmitter.emit(Event_Actions.REPORT_GENERATED, {
      ...response,
      query,
      body,
      organizationid,
      user,
    });

    return response;
  }
  async exportInviteUsersReport(userId: number, body: getUserInviteDto) {
    const newQuery = { ...body, offset: 0, limit: 100000000 };
    let inviteuusers = await this.getInviteUsersReport(userId, newQuery);


    if (!inviteuusers?.length) throw new BadRequestException('No Data for Export');

    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('User Invite');
    const headers = [
      { header: 'S.No', key: 'serialNo' },
      { header: 'Full Name', key: 'fullName' },
      { header: 'Mobile Number', key: 'mobileNumber' },
      { header: 'Email Id', key: 'email' },
      { header: 'Status', key: 'status' },


    ]


    worksheet.columns = headers;

    let serialCounter = 1; // Initialize a counter

    // Initialize array to store max lengths for each column
    const columnMaxLengths = Array(headers.length).fill(0);

    inviteuusers.forEach((inviteuser) => {

      function capitalizeFirstLetter(string) {
        return string.charAt(0).toUpperCase() + string.slice(1);
      }
      const rowData = {
        serialNo: serialCounter++,
        fullName: inviteuser.fullName,
        mobileNumber: fullMobileWithCountry(inviteuser?.mobileNumber, inviteuser?.countryCode),
        email: inviteuser?.email,
        status: capitalizeFirstLetter(inviteuser.status.toLowerCase()),


      }


      const row = worksheet.addRow(rowData);

      const statusCell = row.getCell('status');
      switch (rowData.status?.toLowerCase()) {
        case 'pending':
          statusCell.font = { color: { argb: '149ECD' }, bold: true };
          break;
        case 'cancelled':
          statusCell.font = { color: { argb: 'F63338' }, bold: true };
          break;
        case 'joined':
          statusCell.font = { color: { argb: '008000' }, bold: true };
          break;
        default:
          statusCell.font = { color: { argb: '000000' }, bold: true };
          break;
      }
      worksheet.columns.forEach((column, colIndex) => {
        const headerLength = column.header?.length || 0; // Length of header
        const cellLength = rowData[column.key]?.toString().length || 0; // Length of cell value
        columnMaxLengths[colIndex] = Math.max(columnMaxLengths[colIndex] || 0, headerLength, cellLength);
      });
    });

    // Apply column widths with padding
    worksheet.columns.forEach((column, colIndex) => {
      column.width = columnMaxLengths[colIndex] + 3; // Add padding for readability
    });

    // Style the header row and center alignment
    const headerRow = worksheet.getRow(1);
    headerRow.font = { bold: true };
    headerRow.eachCell((cell) => {
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '64B5F6' },
      };
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' },
      };
      cell.alignment = { horizontal: 'center', vertical: 'middle' };
    });


    // Apply wrapText for all rows
    worksheet.eachRow((row) => {
      row.eachCell((cell) => {
        cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true }; // Center alignment and wrap text
      });
    });

    // Freeze the header row
    worksheet.views = [{ state: 'frozen', ySplit: 1 }];

    const buffer = await workbook.xlsx.writeBuffer();
    return buffer;
  }

  async getLoghoursReport(userId: number, body: getLoghoursDto) {
    const { duration, type, status } = body;

    const entityManager = getManager();
    const query = 'getLohoursreport';
    const sqlQuery = `SELECT organization_id FROM user where id = ${userId} ;`;
    let orgId = await entityManager.query(sqlQuery);
    let organizationid = orgId[0].organization_id;

    let user = await User.findOne({ where: { id: userId }, relations: ['organization'] });

    let loghours = createQueryBuilder(LogHour, 'loghour')
      .leftJoinAndSelect('loghour.user', 'user')
      .leftJoinAndSelect('loghour.client', 'client')
      .leftJoinAndSelect('loghour.task', 'task')

      .where('user.id = :userId', { userId: user.id })
      .andWhere('loghour.duration != :duration', { duration: 0 })
      .andWhere('task.parentTask IS NULL')
      .orderBy('loghour.completedDate', 'DESC');

    if (duration) {
      loghours.andWhere('loghour.duration = :duration', { duration });
    }

    if (type) {
      loghours.andWhere('loghour.type = :type', { type });
    }

    if (status) {
      loghours.andWhere('loghour.status = :status', { status });
    }

    if (body.fromDate && body.toDate) {
      const { startTime, endTime } = dateFormation(body.fromDate, body.toDate);
      if (body.fromDate) {
        loghours.andWhere('loghour.completedDate >= :fromDate', { fromDate: startTime });
      }

      if (body.toDate) {
        loghours.andWhere('loghour.completedDate <= :toDate', { toDate: endTime });
      }
    }

    // return clients.getMany();
    let response = await loghours.getMany();
    this.eventEmitter.emit(Event_Actions.REPORT_GENERATED, {
      ...response,
      query,
      body,
      organizationid,
      user,
    });

    return response;
  }
  async exportLoghoursReport(userId: number, body: getLoghoursDto) {
    let loghours = await this.getLoghoursReport(userId, body);

    let rows = loghours?.map((loghour) => {
      function formatDuration(durationInMillis) {
        const hours = Math.floor(durationInMillis / 3600000); // 1 hour = 3600000 milliseconds
        const minutes = Math.floor((durationInMillis % 3600000) / 60000); // 1 minute = 60000 milliseconds

        const formattedHours = hours.toString().padStart(2, '0');
        const formattedMinutes = minutes.toString().padStart(2, '0');

        return `${formattedHours}:${formattedMinutes}`;
      }

      const durationInMillis = 25200000; // Example duration in milliseconds
      const formattedDuration = formatDuration(durationInMillis);
      function capitalizeFirstLetter(string) {
        return string.charAt(0).toUpperCase() + string.slice(1);
      }

      return {
        'Date': loghour?.completedDate,
        'Client': loghour?.client?.displayName,
        'Type': capitalizeFirstLetter(loghour.type.toLowerCase()),
        'Task Id': loghour?.task?.taskNumber,
        'Task Name|Title': loghour?.task ? loghour?.task?.name : loghour?.title,
        'Duration': formatDuration(loghour?.duration),
      };
    });

    if (rows !== undefined && rows.length) {
      const worksheet = xlsx.utils.json_to_sheet(rows);
      const workbook = xlsx.utils.book_new();
      xlsx.utils.book_append_sheet(workbook, worksheet, 'loghour67');
      let file = xlsx.write(workbook, { type: 'buffer' });
      return file;
    } else {
      throw new BadRequestException('No Data for Export');
    }
  }

  //User Loghours export data
  async getUserLoghoursReport(userId: number, body: getLoghoursDto) {
    const { duration, type, status } = body;
    const entityManager = getManager();
    const query = 'getLohoursreport';
    const sqlQuery = `SELECT organization_id FROM user where id = ${userId} ;`;
    let orgId = await entityManager.query(sqlQuery);
    let organizationid = orgId[0].organization_id;

    let user = await User.findOne({ where: { id: userId }, relations: ['organization'] });

    let loghours = createQueryBuilder(LogHour, 'loghour')
      .leftJoinAndSelect('loghour.user', 'user')
      .leftJoinAndSelect('loghour.client', 'client')
      .leftJoinAndSelect('loghour.task', 'task')

      .where('user.id = :userId', { userId: user.id })
      .andWhere('loghour.duration != :duration', { duration: 0 })
      .andWhere('task.parentTask IS NULL')
      .orderBy('loghour.completedDate', 'DESC');

    // Filter log hours based on the type
    if (type === 'USER') {
      loghours.andWhere('loghour.type = :type', { type });
    }

    // Filter log hours based on the user ID if provided
    if (body.userId) {
      loghours.andWhere('loghour.user_id = :userId', { userId: body.userId });
    }

    if (duration) {
      loghours.andWhere('loghour.duration = :duration', { duration });
    }

    if (type) {
      loghours.andWhere('loghour.type = :type', { type });
    }

    if (status) {
      loghours.andWhere('loghour.status = :status', { status });
    }

    if (body.fromDate && body.toDate) {
      const { startTime, endTime } = dateFormation(body.fromDate, body.toDate);
      if (body.fromDate) {
        loghours.andWhere('loghour.completedDate >= :fromDate', { fromDate: startTime });
      }

      if (body.toDate) {
        loghours.andWhere('loghour.completedDate <= :toDate', { toDate: endTime });
      }
    }

    // return clients.getMany();
    let response = await loghours.getMany();
    this.eventEmitter.emit(Event_Actions.REPORT_GENERATED, {
      ...response,
      query,
      body,
      organizationid,
      user,
    });

    return response;
  }
  async exportUserLoghoursReport(userId: number, body: getLoghoursDto) {
    let loghours = await this.getUserLoghoursReport(userId, body);

    let rows = loghours?.map((loghour) => {
      function formatDuration(durationInMillis) {
        const hours = Math.floor(durationInMillis / 3600000); // 1 hour = 3600000 milliseconds
        const minutes = Math.floor((durationInMillis % 3600000) / 60000); // 1 minute = 60000 milliseconds

        const formattedHours = hours.toString().padStart(2, '0');
        const formattedMinutes = minutes.toString().padStart(2, '0');

        return `${formattedHours}:${formattedMinutes}`;
      }

      const durationInMillis = 25200000; // Example duration in milliseconds
      const formattedDuration = formatDuration(durationInMillis);
      function capitalizeFirstLetter(string) {
        return string.charAt(0).toUpperCase() + string.slice(1);
      }

      return {
        'Date': loghour?.completedDate,
        'Client': loghour?.client?.displayName,
        'Type': capitalizeFirstLetter(loghour.type.toLowerCase()),
        'Task Id': loghour?.task?.taskNumber,
        'Task Name | Title': loghour?.task ? loghour?.task?.name : loghour?.title,
        'Duration': formatDuration(loghour?.duration),
      };
    });

    if (rows !== undefined && rows.length) {
      const worksheet = xlsx.utils.json_to_sheet(rows);
      const workbook = xlsx.utils.book_new();
      xlsx.utils.book_append_sheet(workbook, worksheet, 'loghour12');
      let file = xlsx.write(workbook, { type: 'buffer' });
      return file;
    } else {
      throw new BadRequestException('No Data for Export');
    }
  }

  async getExpenditureReport(userId: number, body: FindExpenditureDto) {
    const { limit, offset } = body;

    let repo = createQueryBuilder(Expenditure, 'expenditure')
      .leftJoinAndSelect('expenditure.task', 'task')
      .leftJoinAndSelect('task.category', 'category')
      .leftJoinAndSelect('expenditure.client', 'client')
      .leftJoinAndSelect('expenditure.clientGroup', 'clientGroup')
      .leftJoinAndSelect('expenditure.user', 'user')
      .leftJoinAndSelect('user.imageStorage', 'imageStorage')

      .leftJoinAndSelect('expenditure.storage', 'storage')
      .orderBy('expenditure.createdAt', 'DESC');

    if (body.type === FindExpenditureQueryType.TASK) {
      repo.where('task.id = :taskId', { taskId: body.taskId });
    }

    if (body.type === FindExpenditureQueryType.USER) {
      repo.where('user.id = :userId', { userId });
    }

    if (body.type === FindExpenditureQueryType.SELF) {
      repo.where('user.id = :userId', { userId });
    }

    if (body.search) {
      repo.andWhere('(expenditure.particularName LIKE :search OR task.name LIKE :search)', {
        search: `%${body.search}%`,
      });
    }
    if (offset >= 0) {
      repo.skip(offset);
    }

    if (limit) {
      repo.take(limit);
    }

    let result = await repo.getManyAndCount();

    return {
      count: result[1],
      result: result[0],
    };
  }

  async exportExpenditureReport(userId: number, body: FindExpenditureDto) {
    let expenditures = await this.getExpenditureReport(userId, body);
    let rows = expenditures?.result?.map((expenditure) => {
      function capitalizeFirstLetter(string) {
        return string.charAt(0).toUpperCase() + string.slice(1);
      }

      return {
        'Expense Nature': capitalizeFirstLetter(expenditure?.type.toLowerCase()),
        'Date': expenditure?.date || null,
        'Client / Client Group': expenditure?.client ? expenditure?.client?.displayName : expenditure?.clientGroup?.displayName,
        'Expense Type': getExpenseType(expenditure?.taskExpenseType),
        'TASK ID': expenditure?.task?.taskNumber,
        'TASK NAME': expenditure?.task?.name,
        'Expense Title': expenditure?.particularName,
        'Amount': expenditure?.amount,
      };
    });

    if (rows !== undefined && rows.length) {
      const worksheet = xlsx.utils.json_to_sheet(rows);
      const workbook = xlsx.utils.book_new();
      xlsx.utils.book_append_sheet(workbook, worksheet, 'dscregister');
      let file = xlsx.write(workbook, { type: 'buffer' });
      return file;
    } else {
      throw new BadRequestException('No Data for Export');
    }
  }

  //User Expenditure export data
  async getUserExpenditureReport(userId: number, body: FindExpenditureDto) {
    const { limit, offset } = body;

    let repo = createQueryBuilder(Expenditure, 'expenditure')
      .leftJoinAndSelect('expenditure.task', 'task')
      .leftJoinAndSelect('task.category', 'category')

      .leftJoinAndSelect('expenditure.client', 'client')
      .leftJoinAndSelect('expenditure.clientGroup', 'clientGroup')
      .leftJoinAndSelect('expenditure.user', 'user')
      .leftJoinAndSelect('user.imageStorage', 'imageStorage')

      .leftJoinAndSelect('expenditure.storage', 'storage')
      .orderBy('expenditure.createdAt', 'DESC');

    if (body.type === FindExpenditureQueryType.TASK) {
      repo.where('task.id = :taskId', { taskId: body.taskId });
    }

    if (body.type === FindExpenditureQueryType.USER) {
      repo.where('user.id = :userId', { userId: body.userId });
    }

    if (body.type === FindExpenditureQueryType.SELF) {
      repo.where('user.id = :userId', { userId: body.userId });
    }

    if (body.search) {
      repo.andWhere('(expenditure.particularName LIKE :search OR task.name LIKE :search)', {
        search: `%${body.search}%`,
      });
    }
    if (offset >= 0) {
      repo.skip(offset);
    }

    if (limit) {
      repo.take(limit);
    }

    let result = await repo.getManyAndCount();

    return {
      count: result[1],
      result: result[0],
    };
  }

  async exportUserExpenditureReport(userId: number, body: FindExpenditureDto) {
    const newQuery = { ...body, offset: 0, limit: 100000000 };
    let expenditures = await this.getUserExpenditureReport(userId, body);


    if (!expenditures.result.length) throw new BadRequestException('No Data for Export');

    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Expenditure');
    const headers = [
      { header: 'S.No', key: 'serialNo' },
      { header: 'Expense Nature', key: 'expenseNature' },
      { header: 'Expense Type', key: 'expenseType' },
      { header: 'Date', key: 'date' },
      { header: 'Client / Client Group', key: 'client' },
      { header: 'Task ID', key: 'taskId' },
      { header: 'Task Name', key: 'taskName' },
      { header: 'Expense Title', key: 'expenseTitle' },
      { header: 'Amount', key: 'amount' },
      { header: 'Updated Date & Time', key: 'updatedAt' },
      { header: 'Status', key: 'status' },
      { header: 'Remarks', key: 'remarks' }

    ]


    worksheet.columns = headers;

    let serialCounter = 1; // Initialize a counter

    // Initialize array to store max lengths for each column
    const columnMaxLengths = Array(headers.length).fill(0);

    expenditures.result.forEach((expenditure) => {
      function capitalizeFirstLetter(string) {
        return string.charAt(0).toUpperCase() + string.slice(1);
      }
      const rowData = {
        serialNo: serialCounter++,
        expenseNature: capitalizeFirstLetter(expenditure?.type.toLowerCase()),
        expenseType: getExpenseType(expenditure?.taskExpenseType),
        date: expenditure?.date ? moment(expenditure.date).format('DD-MM-YYYY') : null,
        client: expenditure?.client
          ? expenditure?.client?.displayName
          : expenditure?.clientGroup?.displayName,
        taskId: expenditure?.task?.taskNumber,
        taskName: expenditure?.task?.name,
        expenseTitle: expenditure?.particularName,
        amount: 1 * expenditure?.amount,
        updatedAt: moment(expenditure?.updatedAt).format("DD-MM-YYYY HH:mm:ss"),
        status: expenditure?.status,
        remarks: expenditure?.remarks


      }


      const row = worksheet.addRow(rowData);
      const statusCell = row.getCell('status');
      switch (rowData.status?.toLowerCase()) {
        case 'pending':
          statusCell.font = { color: { argb: 'FFA500' }, bold: true }; // Orange
          break;
        case 'rejected':
          statusCell.font = { color: { argb: 'F63338' }, bold: true };
          break;
        case 'approved':
          statusCell.font = { color: { argb: '008000' }, bold: true };
          break;
        default:
          statusCell.font = { color: { argb: '000000' }, bold: true };
          break;
      }

      worksheet.columns.forEach((column, colIndex) => {
        const headerLength = column.header?.length || 0; // Length of header
        const cellLength = rowData[column.key]?.toString().length || 0; // Length of cell value
        columnMaxLengths[colIndex] = Math.max(columnMaxLengths[colIndex] || 0, headerLength, cellLength);
      });
    });

    // Apply column widths with padding
    worksheet.columns.forEach((column, colIndex) => {
      column.width = columnMaxLengths[colIndex] + 3; // Add padding for readability
    });

    // Style the header row and center alignment
    const headerRow = worksheet.getRow(1);
    headerRow.font = { bold: true };
    headerRow.eachCell((cell) => {
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '64B5F6' },
      };
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' },
      };
      cell.alignment = { horizontal: 'center', vertical: 'middle' };
    });

    worksheet.columns.forEach((column) => {
      if (column.key === 'taskName' || column.key === 'members' || column.key === 'taskLeaders') {
        column.width = 50;
        column.alignment = { wrapText: true, horizontal: 'center', vertical: 'middle' };
      } else if (column.key === 'remarks') {
        column.width = 100;
        column.alignment = { wrapText: true, horizontal: 'center', vertical: 'middle' };
      } else {
        column.alignment = { horizontal: 'center', vertical: 'middle' };
      }
    });

    // Apply wrapText for all rows
    worksheet.eachRow((row) => {
      row.eachCell((cell) => {
        cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true }; // Center alignment and wrap text
      });
    });

    // Freeze the header row
    worksheet.views = [{ state: 'frozen', ySplit: 1 }];

    const buffer = await workbook.xlsx.writeBuffer();
    return buffer;
  }

  async getClientsPageReport(userId: number, body: FindQueryDto) {
    const { limit, offset } = body;
    const { status: state, category, subCategory, monthAdded, search, labels, selectedIdList } = body;
    const month = new Date(monthAdded).getMonth() + 1;
    const year = new Date(monthAdded).getFullYear();

    let user = await User.findOne({ where: { id: userId }, relations: ['organization', 'role'] });
    let ViewAll = user.role.permissions.find(
      (permission) => permission.slug === Permissions.VIEW_ALL_CLIENT_MANAGERS,
    );
    let ViewAssigned = user.role.permissions.find(
      (permission) => permission.slug === Permissions.VIEW_ASSIGNED_CLIENT_MANAGERS,
    );
    let clients = getConnection()
      .createQueryBuilder(Client, 'client')
      .leftJoin('client.labels', 'labels')
      .leftJoin('client.clientManagers', 'clientManagers')
      .leftJoin('client.organization', 'organization')
      .orderBy('client.displayName', 'ASC')
      .leftJoinAndSelect('client.contactPersons', 'contactPersons')
      .where('organization.id = :organization', { organization: user.organization.id });
    // .andWhere('client.status != :status', { status: UserStatus.DELETED })
    if (ViewAssigned && !ViewAll) {
      clients.andWhere('clientManagers.id = :userId', { userId });
    }

    if (category?.length) {
      clients.andWhere('client.category in (:...category)', { category });
    }

    if (subCategory?.length) {
      clients.andWhere('client.subCategory in (:...subCategory)', { subCategory });
    }

    if (state?.length) {
      clients.andWhere('client.status in (:...state)', { state });
    } else {
      clients.andWhere('client.status != :status', { status: UserStatus.DELETED });
    }

    if (labels?.length) {
      clients.andWhere('labels.id in (:...labels)', { labels });
    }

    if (search) {
      const whereClause = `(
        client.displayName like '%${search}%' or
        client.email like '%${search}%' or
        client.mobileNumber like '%${search}%' or
        client.category like '%${search}%' or
        client.panNumber like '%${search}%' or
        client.tradeName like '%${search}%'
      )`;
      clients.andWhere(whereClause);
    }

    const sort = (typeof body?.sort === "string") ? JSON.parse(body.sort) : body?.sort;
    if (sort?.column) {
      const columnMap: Record<string, string> = {
        clientId: 'client.clientId',
        clientNumber: 'client.clientNumber',
        displayName: 'client.displayName',
        tradeName: 'client.tradeName',
        category: 'client.category',
        authorizedPerson: 'client.authorizedPerson',
        active: 'client.state',
      };
      const column = columnMap[sort.column] || sort.column;
      clients.orderBy(column, sort.direction.toUpperCase());
    } else {
      clients.orderBy('client.createdAt', 'DESC');
    };

    if (monthAdded) {
      clients.andWhere('MONTH(client.createdAt) = :month and YEAR(client.createdAt) = :year', {
        month,
        year,
      });
    }

    if (selectedIdList?.length > 0) {
      clients.andWhere('client.id in (:...selectedIdList)', { selectedIdList });
    } else {
      if (offset >= 0) {
        clients.skip(offset);
      }

      if (limit) {
        clients.take(limit);
      }
    };

    let result = await clients.getManyAndCount();

    return {
      count: result[1],
      result: result[0],
    };
  }

  // async exportClientsPageReport(userId: number, body: FindQueryDto) {
  //   let clients = await this.getClientsPageReport(userId, body);

  //   let rows = clients?.result?.map((client) => {
  //     function capitalizeFirstLetter(string) {
  //       return string.charAt(0).toUpperCase() + string.slice(1);
  //     }
  //     return {
  //       'Client Id': client.clientId,
  //       'GSTIN/PAN': client?.gstNumber ? client?.gstNumber : client.panNumber,
  //       'Display Name': client?.displayName,
  //       'Client Number': client?.clientNumber,
  //       'Trade Name': client?.tradeName,
  //       'Client Category': getTitle(client?.category),
  //       'Client Sub-Category': getTitle(client?.subCategory),
  //       'Mobile Number': `+${getCountryCode(client.countryCode)} ${client?.mobileNumber}`,
  //       'Email ID': client?.email,
  //       'Authorized Person': client?.authorizedPerson,
  //       'Status': capitalizeFirstLetter(client?.status.toLocaleLowerCase()),
  //     };
  //   });

  //   if (rows !== undefined && rows.length) {
  //     const worksheet = xlsx.utils.json_to_sheet(rows);
  //     const workbook = xlsx.utils.book_new();
  //     xlsx.utils.book_append_sheet(workbook, worksheet, 'Clients');
  //     let file = xlsx.write(workbook, { type: 'buffer' });
  //     return file;
  //   } else {
  //     throw new BadRequestException('No Data for Export');
  //   }
  // }

  async exportClientsPageReport(userId: number, body: FindQueryDto) {
    let clients = await this.getClientsPageReport(userId, body);

    const user = await User.findOne(userId)
    let rows = clients?.result?.map((client) => {
      let communicationAddress = '';
      let billingAddress = '';
      let billingState = '';
      if (client.address && typeof client.address === 'object') {
        communicationAddress = (client.address as any).communicationfulladdress ?? '';
        billingAddress = (client.address as any).billingfulladdress ?? '';
        billingState = client.issameaddress ? client.state : (client.address as any).billingState ?? '';
      }
      return {
        'id': client.id,
        'clientId': client.clientId || '',
        'clientNumber': client?.clientNumber || '',
        'category': categoryDisplayNames[client?.category] || '',
        'subCategory': subCategoryDisplayNames[client?.subCategory] || '',
        'displayName': client?.displayName || '',
        'countryCode': client?.countryCode ? getCountryLabel(client?.countryCode) : '',
        'mobileNumber': client?.mobileNumber ? client?.mobileNumber : '',
        'email': client?.email || '',
        'gstNumber': client?.gstNumber ? client?.gstNumber : '',
        'panNumber': client?.panNumber ? client?.panNumber : '',
        'tanNumber': client?.tanNumber || '',
        'authorizedPerson': client?.authorizedPerson || '',
        'designation': client?.designation || '',
        'alternateCountryCode': client?.alternateCountryCode ? getCountryLabel(client?.alternateCountryCode) : '',
        'alternateMobileNumber': client?.alternateMobileNumber || '',
        'dob': client?.dob ? moment(client.dob).format('DD-MM-YYYY') : '',
        'firstName': client?.firstName || '',
        'lastName': client?.lastName || '',
        'middleName': client?.middleName || '',
        'fullName': client?.fullName || '',
        'legalName': client?.legalName || '',
        'tradeName': client?.tradeName || '',
        'communicationAddress': communicationAddress,
        'state': client?.state || '',
        'billingAddress': billingAddress,
        'billingState': billingState
      };
    });

    if (rows !== undefined && rows.length) {
      const workbook = new ExcelJS.Workbook();
      const worksheet = workbook.addWorksheet('Clients');
      const dropdownSheet = workbook.addWorksheet('DropdownData');
      const markerSheet = workbook.addWorksheet('Metadata');
      const guidelinesSheet = workbook.addWorksheet('Guidelines');



      guidelinesSheet.getCell('C2').value = 'Client Updation Sheet - Guidelines';
      guidelinesSheet.getCell('C2').font = { bold: true, size: 12 };
      guidelinesSheet.getCell('B4').value = 'S.No';
      guidelinesSheet.getCell('B4').font = { bold: true };
      guidelinesSheet.getCell('C4').value = 'Editable Fields';
      guidelinesSheet.getCell('C4').font = { bold: true };
      guidelinesSheet.getCell('F4').value = 'S.No';
      guidelinesSheet.getCell('F4').font = { bold: true };
      guidelinesSheet.getCell('G4').value = 'Non-Editable Fields';
      guidelinesSheet.getCell('G4').font = { bold: true };


      const addSerialNumbers = (content, startingRow) => {
        content.forEach((item, index) => {
          guidelinesSheet.getCell(`B${startingRow + index}`).value = index + 1;
          guidelinesSheet.getCell(`C${startingRow + index}`).value = item;
        });
      };

      const addSerialNumbersNonEditable = (content, startingRow) => {
        content.forEach((item, index) => {
          guidelinesSheet.getCell(`F${startingRow + index}`).value = index + 1;
          guidelinesSheet.getCell(`G${startingRow + index}`).value = item;
        });
      };

      const addContentWithHeading = (heading, content, startingRow) => {

        guidelinesSheet.getCell(`B${startingRow + 1}`).value = 'S.No';
        guidelinesSheet.getCell(`B${startingRow + 1}`).font = { bold: true };
        guidelinesSheet.getCell(`C${startingRow + 1}`).value = heading;
        guidelinesSheet.getCell(`C${startingRow + 1}`).font = { bold: true };
        content.forEach((item, index) => {
          guidelinesSheet.getCell(`B${startingRow + 2 + index}`).value = index + 1;
          guidelinesSheet.getCell(`C${startingRow + 2 + index}`).value = item;
        });
        return startingRow + 2 + content.length;
      };

      addSerialNumbers(content1, 5);
      addSerialNumbersNonEditable(content2, 5);

      let nextRow = 5 + content1.length + 2;

      nextRow = addContentWithHeading("Mandatory Fields", content3, nextRow);

      nextRow += 2;

      nextRow = addContentWithHeading("Non-Mandatory Fields", content4, nextRow);

      nextRow += 2;

      addContentWithHeading("Notes", content5, nextRow);

      guidelinesSheet.getColumn('B').alignment = { horizontal: 'center', vertical: 'middle' };
      guidelinesSheet.getColumn('F').alignment = { horizontal: 'center', vertical: 'middle' };
      guidelinesSheet.getColumn('C').width = 100

      markerSheet.getColumn(1).values = ['EXPORTED EXCEL FOR CLIENT BULK UPDATE', user?.organization?.id, user?.fullName]
      dropdownSheet.state = 'hidden';
      markerSheet.state = 'hidden';

      const categories = [
        "Individual", "Hindu Undivided Family", "Partnership Firm",
        "Limited Liability Partnership", "Company", "Association of Persons",
        "Body of Individuals", "Trust", "Government", "Local Authority",
        "Artificial Juridical Person"
      ];

      const subCategories = {
        Company: ["Opc", "Private Limited", "Public Limited", "Government", "Section-8", "Foreign"],
        Trust: ["Public Trust", "Private Discretionary Trust"],
        Government: ["State", "Central"],
      };

      dropdownSheet.getColumn(1).values = ['', ...categories];
      dropdownSheet.getColumn(2).values = ['', ...(subCategories['Company'] || [])];
      dropdownSheet.getColumn(3).values = ['', ...(subCategories['Trust'] || [])];
      dropdownSheet.getColumn(4).values = ['', ...(subCategories['Government'] || [])];
      dropdownSheet.getColumn(5).values = countries.map((item) => item.label) || [];
      dropdownSheet.getColumn(6).values = STATES.map((item) => item.label) || [];
      worksheet.columns = [
        { header: 'id', key: 'id' },
        { header: 'Client Id', key: 'clientId', width: 10 },
        { header: 'Client File #', key: 'clientNumber', width: 15 },
        { header: 'Client Category *', key: 'category', width: 25 },
        { header: 'Sub Category *', key: 'subCategory', width: 25 },
        { header: 'Display Name *', key: 'displayName', width: 30 },
        { header: 'Country Code *', key: 'countryCode', width: 15 },
        { header: 'Mobile Number *', key: 'mobileNumber', width: 15 },
        { header: 'Email Id *', key: 'email', width: 32 },
        { header: 'GSTIN', key: 'gstNumber', width: 18 },
        { header: 'PAN', key: 'panNumber', width: 12 },
        { header: 'TAN', key: 'tanNumber', width: 30 },
        { header: 'Authorized Person', key: 'authorizedPerson', width: 30 },
        { header: 'Designation', key: 'designation', width: 15 },
        { header: 'A - Country Code', key: 'alternateCountryCode', width: 15 },
        { header: 'Alternate Mobile Number', key: 'alternateMobileNumber', width: 15 },
        { header: 'Date of Birth(DD-MM-YYYY)', key: 'dob', width: 12 },
        { header: 'First Name', key: 'firstName', width: 10 },
        { header: 'Last Name', key: 'lastName', width: 10 },
        { header: 'Middle Name', key: 'middleName', width: 10 },
        { header: 'Full Name', key: 'fullName', width: 30 },
        { header: 'Legal Name', key: 'legalName', width: 30 },
        { header: 'Trade Name', key: 'tradeName', width: 30 },
        { header: 'Communication Address', key: 'communicationAddress', width: 100 },
        { header: 'State / Union Territory', key: 'state', width: 15 },
        { header: 'Billing Address', key: 'billingAddress', width: 100 },
        { header: 'Billing State', key: 'billingState', width: 15 },
      ];

      worksheet.addRows(rows);

      for (let rowIndex = 2; rowIndex <= rows.length + 1; rowIndex++) {
        worksheet.getCell(`D${rowIndex}`).dataValidation = {
          type: 'list',
          allowBlank: true,
          formulae: [`DropdownData!$A$2:$A$${categories.length + 1}`],
          showErrorMessage: true,
          errorStyle: 'stop',
          errorTitle: 'Invalid Selection',
          error: 'Please select a valid category from the dropdown list.'
        };

        worksheet.getCell(`E${rowIndex}`).dataValidation = {
          type: 'list',
          allowBlank: true,
          formulae: [
            `INDIRECT(IF(D${rowIndex}="Company", "DropdownData!$B$2:$B$${subCategories.Company.length + 1}", 
                    IF(D${rowIndex}="Trust", "DropdownData!$C$2:$C$${subCategories.Trust.length + 1}", 
                    IF(D${rowIndex}="Government", "DropdownData!$D$2:$D$${subCategories.Government.length + 1}", ""))))`
          ],
          showErrorMessage: true,
          errorStyle: 'stop',
          errorTitle: 'Invalid Selection',
          error: 'Please select a valid sub category from the dropdown list.'
        };

        worksheet.getCell(`G${rowIndex}`).dataValidation = {
          type: 'list',
          allowBlank: true,
          formulae: [
            `DropdownData!$E$2:$E$${countries.length + 1}`
          ],
          showErrorMessage: true,
          errorStyle: 'stop',
          errorTitle: 'Invalid Selection',
          error: 'Please select a valid country from the dropdown list.'
        };

        worksheet.getCell(`O${rowIndex}`).dataValidation = {
          type: 'list',
          allowBlank: true,
          formulae: [
            `DropdownData!$E$2:$E$${countries.length + 1}`
          ],
          showErrorMessage: true,
          errorStyle: 'stop',
          errorTitle: 'Invalid Selection',
          error: 'Please select a valid country from the dropdown list.'
        };

        worksheet.getCell(`Y${rowIndex}`).dataValidation = {
          type: 'list',
          allowBlank: true,
          formulae: [
            `DropdownData!$F$2:$F$${STATES.length + 1}`
          ],
          showErrorMessage: true,
          errorStyle: 'stop',
          errorTitle: 'Invalid Selection',
          error: 'Please select a valid state from the dropdown list.'
        };

        worksheet.getCell(`AA${rowIndex}`).dataValidation = {
          type: 'list',
          allowBlank: true,
          formulae: [
            `DropdownData!$F$2:$F$${STATES.length + 1}`
          ],
          showErrorMessage: true,
          errorStyle: 'stop',
          errorTitle: 'Invalid Selection',
          error: 'Please select a valid state from the dropdown list.'
        };
      };

      worksheet.getColumn(1).hidden = true;

      worksheet.eachRow((row, rowNumber) => {
        row.eachCell((cell, colNumber) => {
          if (colNumber === 1 || colNumber === 2 || colNumber === 6 || colNumber === 10) {
            cell.protection = { locked: true };
          } else {
            cell.protection = { locked: false };
          }
        });
      });

      await worksheet.protect('vider@1818', {
        selectLockedCells: true,
        selectUnlockedCells: true,
        formatColumns: true,
        formatRows: true,
      });

      await guidelinesSheet.protect('vider@1818', {
        selectLockedCells: true,
        selectUnlockedCells: true,
        formatColumns: true,
        formatRows: true,
      });

      const buffer = await workbook.xlsx.writeBuffer();

      return buffer;
    } else {
      throw new BadRequestException('No Data for Export');
    }
  }


  async exportClientsGroupPageReport(userId: number, body: FindQueryDto) {
    const newQuery = { ...body, offset: 0, limit: 100000000 };
    let clientGroup = await this.groupservice.getAllClientGroups(userId, newQuery);

    if (!clientGroup.result.length) throw new BadRequestException('No Data for Export');
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Client Group');
    const headers = [
      { header: 'Group ID', key: 'groupId' },
      { header: 'Group#', key: 'groupNumber' },
      { header: 'GSTIN', key: 'gstIn' },
      { header: 'PAN', key: 'pan' },
      { header: 'Display Name', key: 'displayName' },
      { header: 'Mobile Number', key: 'mobileNumber' },
      { header: 'Email ID', key: 'emailId' },
      { header: 'Status', key: 'status' }
    ]

    worksheet.columns = headers;
    const columnMaxLengths = Array(headers.length).fill(0);

    clientGroup.result.forEach((client) => {
      function capitalizeFirstLetter(string) {
        return string.charAt(0).toUpperCase() + string.slice(1);
      }
      const rowData = {
        groupId: client.clientId || "",
        groupNumber: client?.clientNumber || "",
        gstIn: client?.gstNumber || client?.gstNumber || "",
        pan: client.panNumber || "",
        displayName: client?.displayName || "",
        mobileNumber: `+${getCountryCode(client.countryCode)} ${client?.mobileNumber}` || "",
        emailId: client?.email || " ",
        status: capitalizeFirstLetter(client?.status.toLocaleLowerCase()) || "",
      }


      const row = worksheet.addRow(rowData);
      const statusCell = row.getCell('status');
      if (rowData.status?.toUpperCase() === 'ACTIVE') {
        statusCell.font = { color: { argb: 'FF00B050' }, bold: true }; // Green for ACTIVE
      } else {
        statusCell.font = { color: { argb: 'FFFF0000' }, bold: true }; // Red for other statuses
      }

      worksheet.columns.forEach((column, colIndex) => {
        const headerLength = column.header?.length || 0;
        const cellLength = rowData[column.key]?.toString().length || 0;
        columnMaxLengths[colIndex] = Math.max(columnMaxLengths[colIndex] || 0, headerLength, cellLength);
      });
    });

    worksheet.columns.forEach((column, colIndex) => {
      column.width = columnMaxLengths[colIndex] + 2;
    });

    const headerRow = worksheet.getRow(1);
    headerRow.font = { bold: true };
    headerRow.eachCell((cell) => {
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '64B5F6' },
      };
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' },
      };
      cell.alignment = { horizontal: 'center', vertical: 'middle' };
    });

    worksheet.columns.forEach((column) => {
      if (column.key === 'displayName') {
        column.width = 50;
        column.alignment = { wrapText: true, horizontal: 'center', vertical: 'middle' };
      } else {
        column.alignment = { horizontal: 'center', vertical: 'middle' };
      }
    });

    worksheet.eachRow((row) => {
      row.eachCell((cell) => {
        cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
      });
    });

    worksheet.views = [{ state: 'frozen', ySplit: 1 }];

    const buffer = await workbook.xlsx.writeBuffer();
    return buffer;
  }
  async getTasksReport(userId: number, body: getTasksReport) {
    const entityManager = getManager();

    const query = 'getTasksReport';
    const sqlQuery = `SELECT organization_id FROM user where id = ${userId} ;`;
    let orgId = await entityManager.query(sqlQuery);
    let organizationid = orgId[0].organization_id;

    let user = await User.findOne({ where: { id: userId }, relations: ['organization'] });

    let tasks = createQueryBuilder(Task, 'task')
      .leftJoinAndSelect('task.organization', 'organization')
      .leftJoinAndSelect('task.members', 'members')
      .leftJoinAndSelect('task.taskLeader', 'taskLeader')
      .leftJoinAndSelect('members.imageStorage', 'imageStorage')
      .leftJoinAndSelect('taskLeader.imageStorage', 'leaderImageStorage')
      .leftJoinAndSelect('task.client', 'client')
      .leftJoinAndSelect('task.clientGroup', 'clientGroup')
      .leftJoinAndSelect('task.category', 'category')
      .leftJoinAndSelect('task.subCategory', 'subCategory')
      .where('task.organization.id = :organizationId', { organizationId: user.organization.id })
      .andWhere('(task.recurringStatus is null or task.recurringStatus = :recurringStatus)', {
        recurringStatus: TaskRecurringStatus.CREATED,
      })
      .andWhere('task.parentTask IS NULL');

    if (body.category?.length) {
      const categoryIds = body.category.map((category: any) => category?.id || category);
      tasks.andWhere('category.id IN (:...category)', { category: categoryIds });
    }

    if (body.subCategory?.length) {
      const subcategoryIds = body.subCategory.map(
        (subCategory: any) => subCategory?.id || subCategory,
      );
      tasks.andWhere('subCategory.id IN (:...subCategory)', { subCategory: subcategoryIds });
    }

    if (body.members?.length) {
      const memberIds = body.members.map((member: any) => member?.id || member);
      tasks.andWhere('members.id IN (:...members)', { members: memberIds });
    }

    if (body.taskLeader?.length) {
      const taskLeadermemberIds = body.taskLeader.map((member: any) => member?.id || member);
      tasks.andWhere('taskLeader.id IN (:...taskLeader)', { taskLeader: taskLeadermemberIds });
    }

    if (body.fromDate) {
      tasks.andWhere('task.taskStartDate >= :fromDate', { fromDate: body.fromDate });
    }

    if (body.toDate) {
      tasks.andWhere('task.taskStartDate <= :toDate', {
        toDate: body.toDate,
      });
    }

    if (body.priority) {
      tasks.andWhere('task.priority = :priority', { priority: body.priority });
    }

    if (body.billingType !== BillableType.ALL) {
      if (body.billingType === BillableType.BILLABLE) {
        tasks.andWhere('task.billable is true');
      } else {
        tasks.andWhere('task.billable is false');
      }
    }

    if (body.status) {
      tasks.andWhere('task.status = :status', { status: body.status });
    }

    if (body.clientType === "CLIENT_GROUP") {
      const clientId: any = body.client;
      tasks.andWhere('clientGroup.id = :clientGroup', { clientGroup: clientId });
    }

    if (body?.clientType !== 'CLIENT_GROUP' && body?.client) {
      const clientId: any = body?.client;
      tasks.andWhere('client.id = :client', { client: clientId });
    }

    if (body?.financialYear) {
      tasks.andWhere('task.financialYear=:financialYear', { financialYear: body.financialYear });
    }
    // if (body.members?.length) {
    //   tasks.andWhere('members.id IN (:...members)', { members: body.members });
    // }

    this.eventEmitter.emit(Event_Actions.REPORT_GENERATED, { query, body, organizationid, user });
    return tasks.getMany();
  }

  async getSubTasksReport(userId: number, body: getTasksReport) {
    const entityManager = getManager();

    const query = 'getTasksReport';
    const sqlQuery = `SELECT organization_id FROM user where id = ${userId} ;`;
    let orgId = await entityManager.query(sqlQuery);
    let organizationid = orgId[0].organization_id;

    let user = await User.findOne({ where: { id: userId }, relations: ['organization'] });

    let tasks = createQueryBuilder(Task, 'task')
      .select([
        'task.id',
        'task.taskNumber',
        'task.name',
        'task.dueDate',
        'task.priority',
        'task.status',
        'task.recurringStatus',
        'task.createdAt',
        'task.approvalStatus',
        'task.recurring',
        'task.billable',
        'task.createdDate',
        'task.paymentStatus',
        'task.feeAmount',
        'parentTask.taskNumber',
        'parentTask.name',
        'client.id',
        'client.displayName',
        'clientGroup.id',
        'clientGroup.displayName'
      ])
      .leftJoin('task.parentTask', 'parentTask')
      .leftJoin('task.organization', 'organization')
      .leftJoinAndSelect('task.members', 'members')
      .leftJoinAndSelect('members.imageStorage', 'imageStorage')
      .leftJoinAndSelect('task.client', 'client')
      .leftJoinAndSelect('task.clientGroup', 'clientGroup')
      .leftJoin('task.category', 'category')
      .leftJoin('task.subCategory', 'subCategory')
      .where('task.organization.id = :organizationId', { organizationId: user.organization.id })
      .andWhere('(task.recurringStatus is null or task.recurringStatus = :recurringStatus)', {
        recurringStatus: TaskRecurringStatus.CREATED,
      })
      .andWhere('task.parentTask IS NOT NULL');

    if (body.category?.length) {
      const categoryIds = body.category.map((category: any) => category?.id || category);
      tasks.andWhere('category.id IN (:...category)', { category: categoryIds });
    }

    if (body.subCategory?.length) {
      const subcategoryIds = body.subCategory.map(
        (subCategory: any) => subCategory?.id || subCategory,
      );
      tasks.andWhere('subCategory.id IN (:...subCategory)', { subCategory: subcategoryIds });
    }

    if (body.members?.length) {
      const memberIds = body.members.map((member: any) => member?.id || member);
      tasks.andWhere('members.id IN (:...members)', { members: memberIds });
    }

    if (body.fromDate) {
      tasks.andWhere('task.taskStartDate >= :fromDate', { fromDate: body.fromDate });
    }

    if (body.toDate) {
      tasks.andWhere('task.taskStartDate <= :toDate', {
        toDate: body.toDate,
      });
    }

    if (body.priority) {
      tasks.andWhere('task.priority = :priority', { priority: body.priority });
    }

    if (body.status) {
      tasks.andWhere('task.status = :status', { status: body.status });
    }

    if (body.clientType === "CLIENT_GROUP") {
      const clientId: any = body.client;
      tasks.andWhere('clientGroup.id = :clientGroup', { clientGroup: clientId });
    }

    if (body.clientType !== 'CLIENT_GROUP' && body.client) {
      const clientId: any = body.client;
      tasks.andWhere('client.id = :client', { client: clientId });
    }

    if (body?.financialYear) {
      tasks.andWhere('task.financialYear=:financialYear', { financialYear: body.financialYear });
    }

    this.eventEmitter.emit(Event_Actions.REPORT_GENERATED, { query, body, organizationid, user });
    return tasks.getMany();
  }

  async getCommentsReport(userId: number, body: getTasksReport) {
    const query = 'getTasksReport';
    let user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
    let organizationid = user.organization.id;

    let tasks = createQueryBuilder(Task, 'task')
      .leftJoinAndSelect('task.organization', 'organization')
      .leftJoinAndSelect('task.category', 'category')
      .leftJoinAndSelect('task.subCategory', 'subCategory')
      .leftJoinAndSelect('task.members', 'members')
      .leftJoinAndSelect('task.taskLeader', 'taskLeader')
      .leftJoinAndSelect('task.comments', 'comments')
      .leftJoinAndSelect('comments.user', 'user')
      .leftJoinAndSelect('task.client', 'client')
      .leftJoinAndSelect('task.clientGroup', 'clientGroup')
      .where('task.organization.id = :organizationId', { organizationId: user.organization.id })
      .andWhere('(task.recurringStatus is null or task.recurringStatus = :recurringStatus)', {
        recurringStatus: TaskRecurringStatus.CREATED,
      })
      .andWhere('task.status IN (:...status)', {
        status: [
          TaskStatusEnum.TODO,
          TaskStatusEnum.IN_PROGRESS,
          TaskStatusEnum.UNDER_REVIEW,
          TaskStatusEnum.ON_HOLD,
          TaskStatusEnum.COMPLETED,
        ],
      })
      .andWhere('task.parentTask IS NULL');

    if (body.members?.length) {
      const memberIds = body.members.map((member: any) => member?.id || member);
      tasks.andWhere('members.id IN (:...members)', { members: memberIds });
    }

    if (body.fromDate) {
      tasks.andWhere('task.taskStartDate >= :fromDate', { fromDate: body.fromDate });
    }

    if (body.toDate) {
      tasks.andWhere('task.taskStartDate <= :toDate', {
        toDate: body.toDate,
      });
    }

    if (body.clientType === "CLIENT_GROUP") {
      const clientId: any = body.client;
      tasks.andWhere('clientGroup.id = :clientGroup', { clientGroup: clientId });
    }

    if (body?.clientType !== 'CLIENT_GROUP' && body?.client) {
      const clientId: any = body?.client;
      tasks.andWhere('client.id = :client', { client: clientId });
    }

    this.eventEmitter.emit(Event_Actions.REPORT_GENERATED, { query, body, organizationid, user });
    return tasks.getMany();
  }

  async exportTasksReport(userId: number, body: getTasksReport) {
    let tasks = await this.getTasksReport(userId, body);

    if (!tasks.length) {
      throw new BadRequestException('No Data for Export !');
    }

    let rows = tasks.map((task) => {
      // Function to format date to "DD-MM-YYYY" format
      const formatDate = (dateString) => {
        const date = new Date(dateString);
        const day = date.getDate().toString().padStart(2, '0');
        const month = (date.getMonth() + 1).toString().padStart(2, '0');
        const year = date.getFullYear();
        return `${day}-${month}-${year}`;
      };
      return {
        'Client / Client Group': task?.client ? task?.client?.displayName : task?.clientGroup?.displayName,
        'Service Category': task?.category?.name,
        'Service Sub-Category': task?.subCategory?.name,
        'Task ID': task.taskNumber,
        'Task': task?.name,
        'Priority': getTitle(task?.priority),
        'Task Leader': task?.taskLeader.map((assignee) => assignee.fullName).join(', '),
        'Status': getTitle(task?.status),
        'Statutory Due Date': formatDate(task.dueDate),
        'User': task?.members.map((assignee) => assignee.fullName).join(', '),
      };
    });

    const worksheet = xlsx.utils.json_to_sheet(rows);
    const workbook = xlsx.utils.book_new();
    xlsx.utils.book_append_sheet(workbook, worksheet, 'Main Task');
    let file = xlsx.write(workbook, { type: 'buffer' });
    return file;
  }
  async exportSubTasksReport(userId: number, body: getTasksReport) {
    let tasks = await this.getSubTasksReport(userId, body);

    if (!tasks.length) {
      throw new BadRequestException('No Data for Export !');
    }
    let rows = tasks.map((task) => {
      // Function to format date to "DD-MM-YYYY" format
      const formatDate = (dateString) => {
        const date = new Date(dateString);
        const day = date.getDate().toString().padStart(2, '0');
        const month = (date.getMonth() + 1).toString().padStart(2, '0');
        const year = date.getFullYear();
        return `${day}-${month}-${year}`;
      };
      return {
        'Client / Client Group': task?.client ? task?.client?.displayName : task?.clientGroup?.displayName,
        'Sub Task ID': task?.taskNumber,
        'Sub Task Name': task?.name,
        'Main Task ID': task?.parentTask?.taskNumber,
        'Main Task Name': task?.parentTask?.name,
        'Statutory Due Date': formatDate(task?.dueDate),
        'priority': getTitle(task?.priority),
        'Status': getTitle(task?.status),
        'Members': task?.members.map((assignee) => assignee.fullName).join(', '),
      };
    });

    const worksheet = xlsx.utils.json_to_sheet(rows);
    const workbook = xlsx.utils.book_new();
    xlsx.utils.book_append_sheet(workbook, worksheet, 'Sub Task');
    let file = xlsx.write(workbook, { type: 'buffer' });
    return file;
  }

  async exportCommentsReport(userId: number, body: getTasksReport) {
    let tasks = await this.getCommentsReport(userId, body);

    if (!tasks.length) {
      throw new BadRequestException('No Data for Export !');
    }

    let rows = tasks.map((task) => {
      return {
        'Client / Client Group': task?.client ? task?.client?.displayName : task?.clientGroup?.displayName,
        'Service Category': task?.category?.name,
        'Service Sub-Category': task?.subCategory?.name,
        'Task ID': task.taskNumber,
        'Task': task?.name,
        'Status': getTitle(task?.status),
        'Task Leader': task?.taskLeader.map((assignee) => assignee.fullName).join(', '),
        'Last Comment': task?.comments?.length ? task?.comments[task?.comments.length - 1].text : "NA",
        'Last Commented by': task?.comments?.length ? task?.comments[task?.comments.length - 1].user?.fullName : "NA",
        'Commented at': task?.comments?.length ? moment(task?.comments[task?.comments.length - 1].updatedAt).format("DD-MM-YYYY HH:mm") : "NA",
        'User': task?.members.map((assignee) => assignee.fullName).join(', '),
      };
    });

    const worksheet = xlsx.utils.json_to_sheet(rows);
    const workbook = xlsx.utils.book_new();
    xlsx.utils.book_append_sheet(workbook, worksheet, 'Main Task');
    let file = xlsx.write(workbook, { type: 'buffer' });
    return file;
  }

  filterByDate(args: IFilterByDate) {
    const { query, dateFilterKey, entityKey = 'taskStartDate', tasks } = args;
    let today = this.getDates(DateKeys.TODAY);
    let yesterday = this.getDates(DateKeys.YESTERDAY);
    let thisWeekStart = this.getDates(DateKeys.WEEK_START);
    let thisWeekEnd = this.getDates(DateKeys.WEEK_END);
    let lastWeekStart = this.getDates(DateKeys.LAST_WEEK_START);
    let lastWeekEnd = this.getDates(DateKeys.LAST_WEEK_END);
    let thisMonthStart = this.getDates(DateKeys.MONTH_START);
    let thisMonthEnd = this.getDates(DateKeys.MONTH_END);
    let lastMonthStart = this.getDates(DateKeys.LAST_MONTH_START);
    let lastMonthEnd = this.getDates(DateKeys.LAST_MONTH_END);

    let whereStatement = '';

    const appendStatement = (statement: string) => {
      if (whereStatement) {
        whereStatement += ` or ${statement}`;
      } else {
        whereStatement += statement;
      }
    };

    if (query[dateFilterKey]?.includes(DateFilters.TODAY)) {
      const statement = `Date(task.${entityKey}) = '${today}'`;
      appendStatement(statement);
    }
    if (query[dateFilterKey]?.includes(DateFilters.YESTERDAY)) {
      const statement = `Date(task.${entityKey}) = '${yesterday}'`;
      appendStatement(statement);
    }

    if (query[dateFilterKey]?.includes(DateFilters.THIS_WEEK)) {
      const statement = `Date(task.${entityKey}) between '${thisWeekStart}' and '${thisWeekEnd}'`;
      appendStatement(statement);
    }

    if (query[dateFilterKey]?.includes(DateFilters.LAST_WEEK)) {
      const statement = `Date(task.${entityKey}) between '${lastWeekStart}' and '${lastWeekEnd}'`;
      appendStatement(statement);
    }

    if (query[dateFilterKey]?.includes(DateFilters.THIS_MONTH)) {
      const statemenet = `Date(task.${entityKey}) between '${thisMonthStart}' and '${thisMonthEnd}'`;
      appendStatement(statemenet);
    }

    if (query[dateFilterKey]?.includes(DateFilters.LAST_MONTH)) {
      const statement = `Date(task.${entityKey}) between '${lastMonthStart}' and '${lastMonthEnd}'`;
      appendStatement(statement);
    }

    if (query[dateFilterKey]?.includes(DateFilters.OVERDUE)) {
      const statement = `Date(task.${entityKey}) >= '${today}'`;
      appendStatement(statement);
    }

    if (query[dateFilterKey]?.includes(DateFilters.CUSTOM)) {
      let customDates = JSON.parse(query.customDates);
      const { startTime, endTime } = dateFormation(
        customDates[dateFilterKey].fromDate,
        customDates[dateFilterKey].toDate,
      );

      const statement = `Date(task.${entityKey})
       between '${startTime}'
       and '${endTime}'`;
      appendStatement(statement);
    }

    if (whereStatement) {
      tasks.andWhere(`(${whereStatement})`);
    }
  }

  getDates(key: DateKeys) {
    const today = moment().format('YYYY-MM-DD');
    const yesterday = moment().subtract(1, 'days').format('YYYY-MM-DD');
    const thisWeekStart = moment().startOf('week').format('YYYY-MM-DD');
    const thisWeekEnd = moment().endOf('week').format('YYYY-MM-DD');
    const lastWeekStart = moment().subtract(1, 'weeks').startOf('week').format('YYYY-MM-DD');
    const lastWeekEnd = moment().subtract(1, 'weeks').endOf('week').format('YYYY-MM-DD');
    const thisMonthStart = moment().startOf('month').format('YYYY-MM-DD');
    const thisMonthEnd = moment().endOf('month').format('YYYY-MM-DD');
    const lastMonthStart = moment().subtract(1, 'months').startOf('month').format('YYYY-MM-DD');
    const lastMonthEnd = moment().subtract(1, 'months').endOf('month').format('YYYY-MM-DD');

    switch (key) {
      case DateKeys.TODAY:
        return today;
      case DateKeys.YESTERDAY:
        return yesterday;
      case DateKeys.WEEK_START:
        return thisWeekStart;
      case DateKeys.WEEK_END:
        return thisWeekEnd;
      case DateKeys.LAST_WEEK_START:
        return lastWeekStart;
      case DateKeys.LAST_WEEK_END:
        return lastWeekEnd;
      case DateKeys.MONTH_START:
        return thisMonthStart;
      case DateKeys.MONTH_END:
        return thisMonthEnd;
      case DateKeys.LAST_MONTH_START:
        return lastMonthStart;
      case DateKeys.LAST_MONTH_END:
        return lastMonthEnd;
      default:
        return '';
    }
  }

  async getTasksPageReport(userId: number, body: FindTasksQuery) {
    const query = body;
    const entityManager = getManager();

    const sqlQuery = `SELECT organization_id FROM user where id = ${userId} ;`;
    let orgId = await entityManager.query(sqlQuery);
    let organizationid = orgId[0].organization_id;

    let user = await User.findOne({
      where: { id: userId },
      relations: ['organization', 'role', 'role.permissions'],
    });
    let allTasks = user.role.permissions.find(
      (permission) => permission.slug === Permissions.VIEW_ALL_TASKS,
    );

    let assignedTasks = user.role.permissions.find(
      (permission) => permission.slug === Permissions.VIEW_ASSIGNED_TASKS,
    );

    const organizationPreferences = await OrganizationPreferences.findOne({
      where: { organization: user.organization.id },
      order: { id: 'DESC' },
    });
    const completedDays = organizationPreferences?.taskPreferences?.['taskDate'] || 15;

    let all = allTasks ? true : false;
    let assigned = !all && assignedTasks ? true : false;

    let tasks = createQueryBuilder(Task, 'task')
      .leftJoinAndSelect('task.members', 'members')
      // .leftJoinAndSelect('task.approvals', 'approvals')
      .leftJoinAndSelect('task.client', 'client')
      .leftJoinAndSelect('task.clientGroup', 'clientGroup')
      .leftJoin('task.user', 'user')
      .leftJoinAndSelect('task.taskLeader', 'taskLeader')
      .leftJoinAndSelect('task.category', 'category')
      .leftJoinAndSelect('task.subCategory', 'subCategory')
      .where('task.organization.id = :organizationId', { organizationId: user.organization.id })
      .andWhere('(task.recurringStatus is null or task.recurringStatus = :recurringStatus)', {
        recurringStatus: TaskRecurringStatus.CREATED,
      })
      .andWhere('task.parentTask IS NULL');

    tasks
      .andWhere('(task.recurringStatus is null or task.recurringStatus = :recurringStatus)', {
        recurringStatus: TaskRecurringStatus.CREATED,
      })
      .andWhere(
        new Brackets((qb) =>
          qb
            .where('task.status IN (:...statuses)', {
              statuses: [
                TaskStatusEnum.TODO,
                TaskStatusEnum.IN_PROGRESS,
                TaskStatusEnum.ON_HOLD,
                TaskStatusEnum.UNDER_REVIEW,
              ],
            })
            .orWhere('task.status = :completedStatus', {
              completedStatus: TaskStatusEnum.COMPLETED,
            })
            .andWhere('task.statusUpdatedAt >= :todayDate', {
              todayDate: moment().subtract(parseInt(completedDays), 'days').toDate(),
            }),
        ),
      )
      .orderBy('task.createdAt', 'DESC');

    if (body?.completedOn && !body?.createdOn) {
      tasks.andWhere(`task.status=:status`, { status: TaskStatusEnum.COMPLETED });
    }

    if (assigned) {
      tasks.andWhere('members.id = :userId', { userId });
    }

    if (body?.client) {
      tasks.andWhere('task.client.id = :client', {
        client: body.client,
      });
    }

    if (body?.clientGroup) {
      tasks.andWhere('task.clientGroup.id = :clientGroup', {
        clientGroup: body.clientGroup,
      });
    }

    if (Array.isArray(body?.assignee) && body?.assignee?.length > 0) {
      tasks.andWhere('members.id in (:...assignee)', {
        assignee: body.assignee,
      });
    }

    if (body?.createdBy?.length) {
      tasks.andWhere('user.id in (:...createdBy)', {
        createdBy: body.createdBy,
      });
    }

    if (body?.status?.length) {

      const dueStatuses: string[] = query.status?.filter((status) => status.endsWith('_overdue')).map((status) => status.replace('_overdue', '')) || [];
      const nonDueStatuses: string[] = query.status?.filter((status) => !status.endsWith('_overdue')) || [];
      const hasCompletedOverdue = query.status?.includes('completed_overdue') || false;

      tasks.andWhere(
        new Brackets((qb) => {
          const today = moment().format('YYYY-MM-DD');
          let hasConditions = false;

          // Handle non-overdue statuses
          if (nonDueStatuses.length) {
            const includesCompleted = nonDueStatuses.includes(TaskStatusEnum.COMPLETED);
            const filteredNonDueStatuses = includesCompleted
              ? nonDueStatuses.filter((status) => status !== TaskStatusEnum.COMPLETED)
              : nonDueStatuses;

            if (filteredNonDueStatuses.length) {
              qb.where('task.status IN (:...nonDueStatuses) AND task.dueDate >= :today', {
                nonDueStatuses: filteredNonDueStatuses,
                today,
              });
              hasConditions = true;
            }

            if (includesCompleted) {
              const method = hasConditions ? 'orWhere' : 'where';
              qb[method]('task.status = :completedStatus AND Date(task.dueDate) >= Date(task.statusUpdatedAt)', {
                completedStatus: TaskStatusEnum.COMPLETED,
              });
              hasConditions = true;
            }
          }

          // Handle overdue statuses
          if (dueStatuses.length || hasCompletedOverdue) {
            const includesCompleted = dueStatuses.includes(TaskStatusEnum.COMPLETED);
            const filteredDueStatuses = includesCompleted
              ? dueStatuses.filter((status) => status !== TaskStatusEnum.COMPLETED)
              : dueStatuses;
            if (filteredDueStatuses.length) {
              const method = hasConditions ? 'orWhere' : 'where';
              qb[method]('task.status IN (:...dueStatuses) AND task.dueDate < :today', {
                dueStatuses: filteredDueStatuses,
                today,
              });
              hasConditions = true;
            }

            if (hasCompletedOverdue) {
              const method = hasConditions ? 'orWhere' : 'where';
              qb[method]('task.status = :completedOverdue AND task.dueDate < :today', {
                completedOverdue: 'completed',
                today,
              });
              hasConditions = true;
            } else if (includesCompleted) {
              const method = hasConditions ? 'orWhere' : 'where';
              qb[method]('task.status = :completedStatus AND task.dueDate < task.statusUpdatedAt', {
                completedStatus: TaskStatusEnum.COMPLETED,
              });
            }
          }
        }),
      );


      // let dueStatuses: any[] = query.status?.reduce((acc, status) => {
      //   if (status.endsWith('_overdue')) {
      //     acc.push(status.replace('_overdue', ''));
      //   }
      //   return acc;
      // }, []);

      // let nonDueStatuses: any[] = query.status?.filter((status) => !status.endsWith('_overdue'));
      // tasks.andWhere(
      //   new Brackets((qb) => {
      //     let hasAddedCondition = false;
      //     let complitedStatus = false;
      //     if (nonDueStatuses?.length) {
      //       if (nonDueStatuses.includes(TaskStatusEnum.COMPLETED)) {
      //         nonDueStatuses = nonDueStatuses.filter((item) => !TaskStatusEnum.COMPLETED === item);
      //         complitedStatus = true;
      //       }
      //       if (nonDueStatuses?.length) {
      //         qb.where('task.status IN (:...nonDueStatuses) AND task.dueDate >= :todayDate', {
      //           nonDueStatuses: nonDueStatuses,
      //           todayDate: moment().format('YYYY-MM-DD').toString(),
      //         });
      //       }

      //       if (complitedStatus) {
      //         qb.andWhere('task.status=:comStatus AND Date(task.dueDate) >= Date(task.statusUpdatedAt)', {
      //           comStatus: TaskStatusEnum.COMPLETED,
      //         });
      //       }
      //       hasAddedCondition = true;
      //     }

      //     let dueComplited = false;
      //     if (dueStatuses?.length) {
      //       if (dueStatuses.includes(TaskStatusEnum.COMPLETED)) {
      //         dueStatuses = dueStatuses.filter((item) => !TaskStatusEnum.COMPLETED === item);
      //         dueComplited = true;
      //       }

      //       if (hasAddedCondition) {
      //         if (dueStatuses?.length) {
      //           qb.orWhere('task.status IN (:...dueStatuses) AND task.dueDate < :todayDatee', {
      //             dueStatuses: dueStatuses,
      //             todayDatee: moment().format('YYYY-MM-DD').toString(),
      //           });
      //         }

      //         if (dueComplited) {
      //           qb.orWhere('task.status=:dueComplited AND task.dueDate < task.statusUpdatedAt', {
      //             dueComplited: TaskStatusEnum.COMPLETED,
      //           });
      //         }
      //       } else {
      //         if (dueStatuses?.length) {
      //           qb.where('task.status IN (:...dueStatuses) AND task.dueDate < :todayDatee', {
      //             dueStatuses: dueStatuses,
      //             todayDatee: moment().format('YYYY-MM-DD').toString(),
      //           });
      //         }

      //         if (dueComplited) {
      //           qb.andWhere('task.status=:dueComplited AND task.dueDate < task.statusUpdatedAt', {
      //             dueComplited: TaskStatusEnum.COMPLETED,
      //           });
      //         }
      //       }
      //     }
      //   }),
      // );
    }

    if (body?.priority?.length) {
      tasks.andWhere('task.priority IN (:...priorities)', { priorities: body?.priority });
    }

    if (body?.taskType?.length < 1) {
      if (body.taskType?.includes('recurring')) {
        tasks.andWhere('task.recurring is true');
      }

      if (body?.taskType?.includes('non_recurring')) {
        tasks.andWhere('task.recurring is false');
      }
    }

    if (body?.completedBy?.length) {
      tasks.andWhere('task.status = :status', { status: TaskStatusEnum.COMPLETED });
      tasks.andWhere(
        ` (select
          user_id from task_status ts
          where ts.task_id = task.id
          and ts.status = "completed"
          order by ts.created_at desc
          limit 1) in (:...completedBy)`,
        { completedBy: body.completedBy },
      );
    }

    if (body?.search) {
      tasks.andWhere(
        `(
          task.name like :search or
          task.taskNumber like :search
        )`,
        { search: '%' + body.search + '%' },
      );
    }

    // if (body.taskType?.includes('recurring')) {
    //   tasks.andWhere('task.recurring is true');
    // }

    // if (body.taskType?.includes('non_recurring')) {
    //   tasks.andWhere('task.recurring is false');
    // }

    this.filterByDate({
      query,
      tasks,
      entityKey: 'taskStartDate',
      dateFilterKey: DateFilterKeys.START_DATE,
    });

    this.filterByDate({
      query,
      tasks,
      entityKey: 'dueDate',
      dateFilterKey: DateFilterKeys.DUE_ON,
    });

    this.filterByDate({
      query,
      tasks,
      entityKey: 'createdAt',
      dateFilterKey: DateFilterKeys.CREATED_ON,
    });

    this.filterByDate({
      query,
      tasks,
      entityKey: 'statusUpdatedAt',
      dateFilterKey: DateFilterKeys.COMPLETED_ON,
    });

    try {
      return await tasks.getMany();
    } catch (error) {
      console.log('error', error);
    }
  }


  async exportTasksPageReport(userId: number, body: FindTasksQuery) {
    let tasks = [];
    if (body.taskValue === 'task') {
      tasks = await this.getTasksPageReport(userId, body);


    } else if (body.taskValue === 'sub-task') {
      const [datatask] = await this.tasksService.findSubTask(userId, body);
      tasks = datatask;
    } else if (body.taskValue === 'approval-task') {
      const taskData = await this.tasksService.findApprovalTasks(userId, body);
      tasks = taskData?.t || [];

    }

    if (!tasks.length) throw new BadRequestException('No Data for Export');

    const workbook = new ExcelJS.Workbook();
    const sheetName = body.taskValue === 'task' ? 'Main Task Report' : (body.taskValue === 'sub-task') ? 'Sub Task Report' : 'Approval Task Report';
    const worksheet = workbook.addWorksheet(sheetName);
    const headers = (body.taskValue === 'task')
      ? [
        { header: 'Client / Client Group', key: 'client' },
        { header: 'Task Id', key: 'taskId' },
        { header: 'Financial Year', key: 'financialYear' },
        { header: 'Task Name', key: 'taskName', width: 50 },
        { header: 'Status', key: 'status' },
        { header: 'Due Date', key: 'dueDate' },
        { header: 'Expected Completion Date', key: 'expectedCompletionDate' },
        { header: 'Priority', key: 'priority' },
        { header: 'Task Leaders', key: 'taskLeaders' },
        { header: 'Approval Level', key: 'approvalLevel' },
        { header: 'Members', key: 'members' },
      ]
      : (body.taskValue === 'approval-task') ? [
        { header: 'Client / Client Group', key: 'client' },
        { header: 'Task Id', key: 'taskId' },
        { header: 'Financial Year', key: 'financialYear' },
        { header: 'Task Name', key: 'taskName', width: 50 },
        { header: 'Status', key: 'status' },
        { header: 'Due Date', key: 'dueDate' },
        { header: 'Expected Completion Date', key: 'expectedCompletionDate' },
        { header: 'Priority', key: 'priority' },
        { header: 'Task Leaders', key: 'taskLeaders' },
        { header: 'Approval Level', key: 'approvalLevel' },
        { header: 'Members', key: 'members' },
      ] :



        [
          { header: 'Client / Client Group', key: 'client' },
          { header: 'Sub Task ID', key: 'subTaskId' },
          { header: 'Financial Year', key: 'financialYear' },
          { header: 'Sub Task Name', key: 'subTaskName' },
          { header: 'Status', key: 'status' },
          { header: 'Main Task ID', key: 'maintaskid' },
          { header: 'Main Task Name', key: 'mainTaskId' },
          { header: 'Statutory Due Date', key: 'dueDate' },
          { header: 'Priority', key: 'priority' },
          { header: 'Members', key: 'members' },
        ];

    worksheet.columns = headers;


    // Initialize array to store max lengths for each column
    const columnMaxLengths = Array(headers.length).fill(0);

    tasks.forEach((task) => {
      const rowData = body.taskValue === 'task'
        ? {
          client: task?.client?.displayName || task?.clientGroup?.displayName,
          taskId: task.taskNumber,
          financialYear: task.financialYear,
          taskName: task?.name,
          dueDate: formattedDate(task?.dueDate),
          expectedCompletionDate: formattedDate(task?.expectedCompletionDate)
            ? formattedDate(task?.expectedCompletionDate)
            : '',
          priority: getTitle(task?.priority),
          taskLeaders: task?.taskLeader.map((assignee) => assignee.fullName).join(', '),
          approvalLevel: task?.approvalStatus?.[0]?.status || '',
          status: getTitle(task?.status),
          members: task?.members.map((assignee) => assignee.fullName).join(', '),
        }
        : (body?.taskValue === 'approval-task') ? {
          client: task?.client?.displayName || task?.clientGroup?.displayName,
          taskId: task.taskNumber,
          financialYear: task.financialYear,
          taskName: task?.name,
          dueDate: formattedDate(task?.dueDate),
          expectedCompletionDate: formattedDate(task?.expectedCompletionDate)
            ? formattedDate(task?.expectedCompletionDate)
            : '',
          priority: getTitle(task?.priority),
          taskLeaders: task?.taskLeader?.map((assignee) => assignee.fullName).join(', '),
          approvalLevel: task?.approvalStatus?.[0]?.status || '',
          status: getTitle(task?.status),
          members: task?.members.map((assignee) => assignee.fullName).join(', '),
        } :
          {
            client: task?.client?.displayName || task?.clientGroup?.displayName,
            status: getTitle(task?.status),
            financialYear: task.financialYear,
            subTaskId: task?.taskNumber,
            subTaskName: task?.name,
            maintaskid: task?.parentTask?.taskNumber || '',
            mainTaskId: task?.parentTask?.name || '',
            dueDate: formattedDate(task?.dueDate),
            priority: getTitle(task?.priority),
            members: task?.members.map((assignee) => assignee.fullName).join(', '),
          };

      const row = worksheet.addRow(rowData);
      const priorityCell = row.getCell('priority');
      switch (rowData.priority?.toLowerCase()) {
        case 'high':
          priorityCell.font = { color: { argb: 'FB0505' }, bold: true };
          break;
        case 'medium':
          priorityCell.font = { color: { argb: 'F17F23' }, bold: true };
          break;
        case 'low':
          priorityCell.font = { color: { argb: '019335' }, bold: true };
          break;
        default:
          priorityCell.font = { color: { argb: '64B5F6' }, bold: true };
          break;
      }


      // Access the 'status' column cell
      const statusCell = row.getCell('status');

      if (rowData.status) {
        // Apply font color based on status
        switch (rowData.status.toLowerCase()) {
          case 'todo':
            statusCell.font = { color: { argb: '149ECD' }, bold: true };
            break;
          case 'in progress':
            statusCell.font = { color: { argb: 'F49752' }, bold: true };
            break;
          case 'on hold':
            statusCell.font = { color: { argb: 'F63338' }, bold: true };
            break;
          case 'under review':
            statusCell.font = { color: { argb: '653BBA' }, bold: true };
            break;
          case 'completed':
            statusCell.font = { color: { argb: '008000' }, bold: true };
            break;
          default:
            statusCell.font = { color: { argb: '000000' }, bold: true }; 
            break;
        }
      }


      // Calculate maximum width for each column
      worksheet.columns.forEach((column, colIndex) => {
        const headerLength = column.header?.length || 0; // Length of header
        const cellLength = rowData[column.key]?.toString().length || 0; // Length of cell value
        columnMaxLengths[colIndex] = Math.max(columnMaxLengths[colIndex] || 0, headerLength, cellLength);
      });
    });

    // Apply column widths with padding
    worksheet.columns.forEach((column, colIndex) => {
      column.width = columnMaxLengths[colIndex] + 2; // Add padding for readability
    });

    // Style the header row and center alignment
    const headerRow = worksheet.getRow(1);
    headerRow.font = { bold: true };
    headerRow.eachCell((cell) => {
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '64B5F6' },
      };
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' },
      };
      cell.alignment = { horizontal: 'center', vertical: 'middle' };
    });

    worksheet.columns.forEach((column) => {
      if (column.key === 'taskName' || column.key === 'client' || column.key === 'mainTaskId' || column.key === 'taskLeaders' || column.key === 'members' || column.key === 'subTaskName') {
        column.width = 50;
        column.alignment = { wrapText: true, horizontal: 'center', vertical: 'middle' }; // Enable text wrap
      } else {
        column.alignment = { horizontal: 'center', vertical: 'middle' }; // Apply center alignment for other columns
      }
    });

    // Apply wrapText for all rows
    worksheet.eachRow((row) => {
      row.eachCell((cell) => {
        cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true }; // Center alignment and wrap text
      });
    });

    // Freeze the header row
    worksheet.views = [{ state: 'frozen', ySplit: 1 }];

    const buffer = await workbook.xlsx.writeBuffer();
    return buffer;
  }




  async getUserTasksPageReport(userId: number, body: FindTasksQuery) {
    const query = body;
    const entityManager = getManager();
    const sqlQuery = `SELECT organization_id FROM user where id = ${userId} ;`;
    let orgId = await entityManager.query(sqlQuery);
    let organizationid = orgId[0].organization_id;

    let user = await User.findOne({
      where: { id: userId },
      relations: ['organization', 'role', 'role.permissions'],
    });
    let allTasks = user.role.permissions.find(
      (permission) => permission.slug === Permissions.VIEW_ALL_TASKS,
    );

    let assignedTasks = user.role.permissions.find(
      (permission) => permission.slug === Permissions.VIEW_ASSIGNED_TASKS,
    );

    let all = allTasks ? true : false;
    let assigned = !all && assignedTasks ? true : false;

    let tasks = createQueryBuilder(Task, 'task')
      .leftJoinAndSelect('task.members', 'members')
      .leftJoinAndSelect('task.client', 'client')
      .leftJoin('task.user', 'user')
      .leftJoinAndSelect('task.taskLeader', 'taskLeader')
      .leftJoinAndSelect('task.category', 'category')
      .leftJoinAndSelect('task.subCategory', 'subCategory')
      .where('task.organization.id = :organizationId', { organizationId: user.organization.id })
      .andWhere('(task.recurringStatus is null or task.recurringStatus = :recurringStatus)', {
        recurringStatus: TaskRecurringStatus.CREATED,
      })
      .andWhere(`task.status NOT IN ('${TaskStatusEnum.DELETED}','${TaskStatusEnum.TERMINATED}')`)
      .andWhere('task.parentTask IS NULL');

    tasks
      .andWhere('(task.recurringStatus is null or task.recurringStatus = :recurringStatus)', {
        recurringStatus: TaskRecurringStatus.CREATED,
      })
      .andWhere(
        new Brackets(
          (qb) =>
            qb
              .where('task.status IN (:...statuses)', {
                statuses: [
                  TaskStatusEnum.TODO,
                  TaskStatusEnum.IN_PROGRESS,
                  TaskStatusEnum.ON_HOLD,
                  TaskStatusEnum.UNDER_REVIEW,
                ],
              })
              .orWhere('task.status = :completedStatus', {
                completedStatus: TaskStatusEnum.COMPLETED,
              }),
          // .andWhere('task.updatedAt >= :todayDate', {
          //   todayDate: moment().subtract(15, 'days').toDate(),
          // }),
        ),
      )
      .orderBy('task.createdAt', 'DESC');

    tasks.andWhere('members.id = :userId', { userId });

    if (query.search) {
      tasks.andWhere('task.name like :search', { search: `%${query.search}%` });
    }

    if (query.client) {
      tasks.andWhere('client.id = :clientId', { clientId: query.client });
    }

    if (query.billable) {
      tasks.andWhere('task.billable is true');
    }

    try {
      return await tasks.getMany();
    } catch (error) {
      console.log('error', error);
    }
  }
  async exportUserTasksPageReport(userId: number, body: FindTasksQuery) {
    let tasks = await this.getUserTasksPageReport(userId, body);
    let rows = tasks?.map((task) => {
      const formatDate = (dateString) => {
        const date = new Date(dateString);
        const day = date.getDate().toString().padStart(2, '0');
        const month = (date.getMonth() + 1).toString().padStart(2, '0');
        const year = date.getFullYear();
        return `${day}-${month}-${year}`;
      };
      return {
        'Client': task?.client?.displayName,
        'Task Id': task.taskNumber,
        'Task Type': task.recurring ? 'Recurring' : 'Non-Recurring',
        'Task Name': task?.name,
        'Start Date': formatDate(task?.taskStartDate),
        'Statutory Due Date': formatDate(task.dueDate),
        'Status': getTitle(task?.status),
      };
    });

    if (rows !== undefined && rows.length) {
      const worksheet = xlsx.utils.json_to_sheet(rows);
      const workbook = xlsx.utils.book_new();
      xlsx.utils.book_append_sheet(workbook, worksheet, 'Clients');
      let file = xlsx.write(workbook, { type: 'buffer' });
      return file;
    } else {
      throw new BadRequestException('No Data for Export');
    }
  }

  //MANAGE USERS TASK REPORT
  async getUserCardTasksPageReport(userId: number, body: FindTasksQuery) {
    const query = body;
    const entityManager = getManager();

    const sqlQuery = `SELECT organization_id FROM user where id = ${userId} ;`;
    let orgId = await entityManager.query(sqlQuery);
    let organizationid = orgId[0].organization_id;

    let user = await User.findOne({
      where: { id: userId },
      relations: ['organization', 'role', 'role.permissions'],
    });
    let allTasks = user.role.permissions.find(
      (permission) => permission.slug === Permissions.VIEW_ALL_TASKS,
    );

    let assignedTasks = user.role.permissions.find(
      (permission) => permission.slug === Permissions.VIEW_ASSIGNED_TASKS,
    );

    let all = allTasks ? true : false;
    let assigned = !all && assignedTasks ? true : false;

    let tasks = createQueryBuilder(Task, 'task')
      .leftJoinAndSelect('task.members', 'members')
      .leftJoinAndSelect('task.client', 'client')
      .leftJoin('task.user', 'user')
      .leftJoinAndSelect('task.category', 'category')
      .leftJoinAndSelect('task.subCategory', 'subCategory')
      .where('task.organization.id = :organizationId', { organizationId: user.organization.id })
      .andWhere('(task.recurringStatus is null or task.recurringStatus = :recurringStatus)', {
        recurringStatus: TaskRecurringStatus.CREATED,
      })
      .andWhere('task.parentTask IS NULL');

    tasks
      .andWhere('(task.recurringStatus is null or task.recurringStatus = :recurringStatus)', {
        recurringStatus: TaskRecurringStatus.CREATED,
      })
      .andWhere(
        new Brackets(
          (qb) =>
            qb
              .where('task.status IN (:...statuses)', {
                statuses: [
                  TaskStatusEnum.TODO,
                  TaskStatusEnum.IN_PROGRESS,
                  TaskStatusEnum.ON_HOLD,
                  TaskStatusEnum.UNDER_REVIEW,
                ],
              })
              .orWhere('task.status = :completedStatus', {
                completedStatus: TaskStatusEnum.COMPLETED,
              }),
          // .andWhere('task.updatedAt >= :todayDate', {
          //   todayDate: moment().subtract(15, 'days').toDate(),
          // }),
        ),
      )
      .orderBy('task.createdAt', 'DESC');

    tasks.andWhere('members.id = :userId', { userId: body.userId });

    if (body?.completedOn && !body?.createdOn) {
      tasks.andWhere(`task.status=:status`, { status: TaskStatusEnum.COMPLETED });
    }

    if (assigned) {
      tasks.andWhere('members.id = :userId', { userId });
    }

    if (body?.client) {
      tasks.andWhere('task.client.id = :client', {
        client: body.client,
      });
    }

    if (Array.isArray(body?.assignee) && body?.assignee?.length > 0) {
      tasks.andWhere('members.id in (:...assignee)', {
        assignee: body.assignee,
      });
    }

    if (body?.createdBy?.length) {
      tasks.andWhere('user.id in (:...createdBy)', {
        createdBy: body.createdBy,
      });
    }

    if (body?.status?.length) {
      tasks.andWhere('task.status in (:...status)', {
        status: body.status,
      });
    }

    if (body?.priority?.length) {
      tasks.andWhere('task.priority IN (:...priorities)', { priorities: body?.priority });
    }

    if (body?.taskType?.length < 1) {
      if (body.taskType?.includes('recurring')) {
        tasks.andWhere('task.recurring is true');
      }

      if (body?.taskType?.includes('non_recurring')) {
        tasks.andWhere('task.recurring is false');
      }
    }

    if (body?.completedBy?.length) {
      tasks.andWhere('task.status = :status', { status: TaskStatusEnum.COMPLETED });
      tasks.andWhere(
        ` (select
        user_id from task_status ts
        where ts.task_id = task.id
        and ts.status = "completed"
        order by ts.created_at desc
        limit 1) in (:...completedBy)`,
        { completedBy: body.completedBy },
      );
    }

    if (body?.search) {
      tasks.andWhere(
        `(
        task.name like :search or
        task.taskNumber like :search
      )`,
        { search: '%' + body.search + '%' },
      );
    }

    // if (body.taskType?.includes('recurring')) {
    //   tasks.andWhere('task.recurring is true');
    // }

    // if (body.taskType?.includes('non_recurring')) {
    //   tasks.andWhere('task.recurring is false');
    // }

    this.filterByDate({
      query,
      tasks,
      entityKey: 'taskStartDate',
      dateFilterKey: DateFilterKeys.START_DATE,
    });

    this.filterByDate({
      query,
      tasks,
      entityKey: 'dueDate',
      dateFilterKey: DateFilterKeys.DUE_ON,
    });

    this.filterByDate({
      query,
      tasks,
      entityKey: 'createdAt',
      dateFilterKey: DateFilterKeys.CREATED_ON,
    });

    this.filterByDate({
      query,
      tasks,
      entityKey: 'updatedAt',
      dateFilterKey: DateFilterKeys.COMPLETED_ON,
    });

    try {
      return await tasks.getMany();
    } catch (error) {
      console.log('error', error);
    }
  }
  async exportUserCardTasksPageReport(userId: number, body: FindTasksQuery) {
    let tasks = await this.getUserCardTasksPageReport(userId, body);
    let rows = tasks?.map((task) => {
      return {
        'Client': task?.client?.displayName,
        'Task Id': task.taskNumber,
        'Task Type': task.recurring ? 'Recurring' : 'Non-Recurring',
        'Task Name': task?.name,
        'Start Date': formatDate(task?.taskStartDate),
        'Statutory Due Date': formatDate(task.dueDate),
        'Status': getTitle(task?.status),
      };
    });

    if (rows !== undefined && rows.length) {
      const worksheet = xlsx.utils.json_to_sheet(rows);
      const workbook = xlsx.utils.book_new();
      xlsx.utils.book_append_sheet(workbook, worksheet, 'upcoming tasks');
      let file = xlsx.write(workbook, { type: 'buffer' });
      return file;
    } else {
      throw new BadRequestException('No Data for Export');
    }
  }

  //Recurring tasks page report
  async getUserRecurringTasksPageReport(userId: number, body: FindTasksQuery) {
    const query = body;
    const entityManager = getManager();

    const sqlQuery = `SELECT organization_id FROM user where id = ${userId} ;`;
    let orgId = await entityManager.query(sqlQuery);
    let organizationid = orgId[0].organization_id;

    query.category = Array.isArray(query.category) ? query.category : [];
    query.subCategory = Array.isArray(query.subCategory) ? query.subCategory : [];
    query.clientCategory = Array.isArray(query.clientCategory) ? query.clientCategory : [];
    query.clientSubCategory = Array.isArray(query.clientSubCategory) ? query.clientSubCategory : [];
    query.assignee = Array.isArray(query.assignee) ? query.assignee : [];
    query.createdBy = Array.isArray(query.createdBy) ? query.createdBy : [];
    query.status = Array.isArray(query.status) ? query.status : [];
    query.priority = Array.isArray(query.priority) ? query.priority : [];
    query.financialYear = Array.isArray(query.financialYear) ? query.financialYear : [];
    query.tags = Array.isArray(query.tags) ? query.tags : [];
    query.completedBy = Array.isArray(query.completedBy) ? query.completedBy : [];

    let user = await User.findOne({
      where: { id: userId },
      relations: ['role', 'role.permissions'],
    });
    let allTasks = user.role.permissions.find(
      (permission) => permission.slug === Permissions.VIEW_ALL_TASKS,
    );

    let assignedTasks = user.role.permissions.find(
      (permission) => permission.slug === Permissions.VIEW_ASSIGNED_TASKS,
    );

    let all = allTasks ? true : false;
    let assigned = !all && assignedTasks ? true : false;

    let tasks = createQueryBuilder(Task, 'task')
      .select([
        'task.id',
        'task.taskNumber',
        'task.name',
        'task.dueDate',
        'task.priority',
        'task.status',
        'task.recurring',
        'task.taskStartDate',
        'task.financialYear',
        'task.frequency',
        'client.displayName',
        'client.id',
        'clientGroup.displayName',
        'clientGroup.id',
        'members.fullName',
        'taskMembers.fullName',
        'taskMembers.image',
        'taskLeader.fullName',
        'taskLeader.image',
        'leaderImageStorage.file',
        // 'approvals.status',
        'taskStatus.id',
        'subtasks.name',
        'recurringProfile',
        'imageStorage.file',
      ])
      .leftJoin('task.client', 'client')
      .leftJoin('task.clientGroup', 'clientGroup')
      .leftJoin('task.subTasks', 'subtasks')
      .leftJoin('task.members', 'members')
      // .leftJoin('task.approvals', 'approvals')
      .leftJoin('task.taskStatus', 'taskStatus')
      .leftJoin('task.members', 'taskMembers')
      .leftJoin('taskMembers.imageStorage', 'imageStorage')
      .leftJoin('task.taskLeader', 'taskLeader')
      .leftJoin('task.parentTask', 'parentTask')
      .leftJoin('taskLeader.imageStorage', 'leaderImageStorage')
      .leftJoin('task.recurringProfile', 'recurringProfile')
      .where('task.organization.id = :organization', { organization: user.organization.id })
      .andWhere('(task.recurringStatus = :recurringStatus)', {
        recurringStatus: TaskRecurringStatus.PENDING,
      })
      .andWhere(
        `task.status NOT IN ('${TaskStatusEnum.COMPLETED}', '${TaskStatusEnum.DELETED}', '${TaskStatusEnum.TERMINATED}')`,
      );

    if (assigned) {
      tasks.andWhere('taskMembers.id = :userId', { userId });
    }

    if (query['recurringType'] === 'recurring') {
      tasks.andWhere('task.recurring is true');
    } else if (query['recurringType'] === 'non-recurring') {
      tasks.andWhere('task.recurring is false');
    }

    if (query.allTasks) {
      if (query.allTasks === 'main_task') {
        tasks.andWhere('task.parentTask IS NULL');
      } else if (query.allTasks === 'sub_task') {
        tasks.andWhere('task.parentTask IS NOT NULL');
      }
    }

    if (query.client) {
      tasks.andWhere('task.client.id = :client', {
        client: query.client,
      });
    }

    if (query.category.length > 0) {
      tasks.andWhere('category.id in (:...categories)', {
        categories: query.category,
      });
    }

    if (query.subCategory.length > 0) {
      tasks.andWhere('subCategory.id in (:...subCategories)', {
        subCategories: query.subCategory,
      });
    }

    if (query.clientCategory.length > 0) {
      tasks.andWhere('client.category in (:...clientCategories)', {
        clientCategories: query.clientCategory,
      });
    }

    if (query.clientSubCategory.length > 0) {
      tasks.andWhere('client.subCategory in (:...clientSubCategories)', {
        clientSubCategories: query.clientSubCategory,
      });
    }

    if (query.assignee.length > 0) {
      tasks.andWhere('members.id in (:...assignee)', {
        assignee: query.assignee,
      });
    }

    if (query.createdBy.length > 0) {
      tasks.andWhere('user.id in (:...createdBy)', {
        createdBy: query.createdBy,
      });
    }

    if (query.status.length > 0) {
      tasks.andWhere('task.status in (:...status)', {
        status: query.status,
      });
    }

    if (query.priority.length > 0) {
      tasks.andWhere('task.priority in (:...priority)', {
        priority: query.priority,
      });
    }

    if (query.financialYear.length > 0) {
      tasks.andWhere('task.financialYear in (:...financialYear)', {
        financialYear: query.financialYear,
      });
    }

    if (query.tags.length > 0) {
      tasks.andWhere('labels.id in (:...labels)', {
        labels: query.tags,
      });
    }
    if (query?.billable?.length) {
      let billableType = [];
      if (query?.billable?.includes('billable')) {
        billableType.push(true);
      }
      if (query?.billable?.includes('nonbillable')) {
        billableType.push(false);
      }
      tasks.andWhere('task.billable in (:...billable)', {
        billable: billableType,
      });
    }
    if (query?.taskLeader?.length) {
      tasks.andWhere('taskLeader.id in (:...taskLeader)', {
        taskLeader: query.taskLeader,
      });
    }

    // if (query.completedOn) {
    //   tasks.andWhere('task.status = :status', { status: TaskStatusEnum.COMPLETED });
    //   tasks.andWhere(
    //     `Date((select
    //   max(created_at) from task_status ts
    //   where ts.task_id = task.id
    //   and ts.status = "completed"
    //   order by ts.created_at desc
    //   limit 1)) = :completedOn`,
    //     { completedOn: query.completedOn }, // Ensure you use query.completedOn here, not a hardcoded date
    //   );
    // }

    if (query.completedBy.length > 0) {
      tasks.andWhere('task.status = :status', { status: TaskStatusEnum.COMPLETED });
      tasks.andWhere(
        `(select
      user_id from task_status ts
      where ts.task_id = task.id
      and ts.status = "completed"
      order by ts.created_at desc
      limit 1) in (:...completedBy)`,
        { completedBy: query.completedBy },
      );
    }

    if (query.search) {
      tasks.andWhere(
        `(
      task.name like :search or
      client.displayName like :search or clientGroup.displayName like:search
    )`,
        { search: '%' + query.search + '%' },
      );
    }

    if (query.taskType?.length < 2) {
      if (query.taskType?.includes('recurring')) {
        tasks.andWhere('task.recurring is true');
      }

      if (query.taskType?.includes('non_recurring')) {
        tasks.andWhere('task.recurring is false');
      }
    }

    this.filterByDate({
      query,
      tasks,
      entityKey: 'taskStartDate',
      dateFilterKey: DateFilterKeys.START_DATE,
    });

    this.filterByDate({
      query,
      tasks,
      entityKey: 'dueDate',
      dateFilterKey: DateFilterKeys.DUE_ON,
    });

    this.filterByDate({
      query,
      tasks,
      entityKey: 'createdAt',
      dateFilterKey: DateFilterKeys.CREATED_ON,
    });

    try {
      let result = await tasks.getMany();
      return result;
    } catch (error) {
      console.log('error', error);
    }
  }
  // async exportUserRecurringTasksPageReport(userId: number, body: FindTasksQuery) {
  //   let tasks = await this.getUserRecurringTasksPageReport(userId, body);
  //   tasks = tasks?.sort((a, b) => new Date(a.taskStartDate).getTime() - new Date(b.taskStartDate).getTime());
  //   let rows = tasks?.map((task) => {
  //     return {
  //       'Client / Client Group': task?.client ? task?.client?.displayName : task?.clientGroup?.displayName,
  //       'FY': task?.financialYear || '',
  //       'Frequency': task?.frequency,
  //       'Task Name': task?.name,
  //       'Priority': task?.priority,
  //       'Start Date': formatDate(task?.taskStartDate),
  //       'Statutory Due Date': formatDate(task.dueDate),
  //       'Members': task?.members.map((assignee) => assignee.fullName).join(', '),
  //     };
  //   });

  //   if (rows !== undefined && rows.length) {
  //     const worksheet = xlsx.utils.json_to_sheet(rows);
  //     const workbook = xlsx.utils.book_new();
  //     xlsx.utils.book_append_sheet(workbook, worksheet, 'Upcoming Task');
  //     let file = xlsx.write(workbook, { type: 'buffer' });
  //     return file;
  //   } else {
  //     throw new BadRequestException('No Data for Export');
  //   }
  // }

  async exportUserRecurringTasksPageReport(userId: number, body: FindTasksQuery) {
    let tasks = await this.getUserRecurringTasksPageReport(userId, body);
    tasks = tasks?.sort((a, b) => new Date(a.taskStartDate).getTime() - new Date(b.taskStartDate).getTime());


    if (!tasks.length) throw new BadRequestException('No Data for Export');

    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Upcoming Task');
    const headers = [
      { header: 'S.No', key: 'serialNo' },
      { header: 'Client / Client Group', key: 'clientGroup' },
      { header: 'FY', key: 'fy' },
      { header: 'Frequency', key: 'frequency' },
      { header: 'Task Name', key: 'taskName' },
      { header: 'Priority', key: 'priority' },
      { header: 'Start Date', key: 'startDate' },
      { header: 'Statutory Due Date', key: 'dueDate' },
      { header: 'Members', key: 'members' },
    ]


    worksheet.columns = headers;
    let serialCounter = 1; // Initialize a counter


    // Initialize array to store max lengths for each column
    const columnMaxLengths = Array(headers.length).fill(0);

    tasks.forEach((task) => {
      const rowData = {
        serialNo: serialCounter++,// Assign and then increment the counter
        clientGroup: task?.client ? task?.client?.displayName : task?.clientGroup?.displayName,
        fy: task?.financialYear || '',
        frequency: task?.frequency || '',
        taskName: task?.name,
        priority: getTitle(task?.priority),
        startDate: formatDate(task?.taskStartDate),
        dueDate: formatDate(task?.dueDate),
        members: task?.members.map((assignee) => assignee.fullName).join(', '),
      }


      const row = worksheet.addRow(rowData);
      const priorityCell = row.getCell('priority');
      switch (rowData.priority?.toLowerCase()) {
        case 'high':
          priorityCell.font = { color: { argb: 'FB0505' }, bold: true };
          break;
        case 'medium':
          priorityCell.font = { color: { argb: 'F17F23' }, bold: true };
          break;
        case 'low':
          priorityCell.font = { color: { argb: '019335' }, bold: true };
          break;
        default:
          priorityCell.font = { color: { argb: '64B5F6' }, bold: true };
          break;
      }
      worksheet.columns.forEach((column, colIndex) => {
        const headerLength = column.header?.length || 0;
        const cellLength = rowData[column.key]?.toString().length || 0;
        columnMaxLengths[colIndex] = Math.max(columnMaxLengths[colIndex] || 0, headerLength, cellLength);
      });
    });

    worksheet.columns.forEach((column, colIndex) => {
      column.width = columnMaxLengths[colIndex] + 2;
    });

    const headerRow = worksheet.getRow(1);
    headerRow.font = { bold: true };
    headerRow.eachCell((cell) => {
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '64B5F6' },
      };
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' },
      };
      cell.alignment = { horizontal: 'center', vertical: 'middle' };
    });

    worksheet.columns.forEach((column) => {
      if (column.key === 'taskName' || column.key === 'members' || column.key === 'clientGroup') {
        column.width = 50;
        column.alignment = { wrapText: true, horizontal: 'center', vertical: 'middle' };
      } else {
        column.alignment = { horizontal: 'center', vertical: 'middle' };
      }
    });

    worksheet.eachRow((row) => {
      row.eachCell((cell) => {
        cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
      });
    });

    worksheet.views = [{ state: 'frozen', ySplit: 1 }];

    const buffer = await workbook.xlsx.writeBuffer();
    return buffer;
  }

  //Complete Taks page report

  async getCompletedTasksReport(userId: number, body: FindTasksQuery) {
    const query = body;
    const { limit, offset, search } = query;
    const user = await User.findOne({ where: { id: userId }, relations: ['organization'] });

    let tasks = await createQueryBuilder(Task, 'task')
      .select([
        'task',
        'organization.id',
        'client.id',
        'client.displayName',
        'clientGroup.id',
        'clientGroup.displayName',
        'clientGroup.type',
        'members.id',
        'members.fullName',
        'imageStorage',
        'taskLeader.id',
        'taskLeader.fullName',
        'leaderImageStorage',
        'service.id',
        'service.name',
      ])
      .leftJoin('task.organization', 'organization')
      .leftJoin('task.client', 'client')
      .leftJoin('task.clientGroup', 'clientGroup')
      .leftJoin('task.members', 'members')
      .leftJoin('members.imageStorage', 'imageStorage')
      .leftJoin('task.taskLeader', 'taskLeader')
      .leftJoin('taskLeader.imageStorage', 'leaderImageStorage')
      .leftJoin('task.service', 'service')
      .where('task.status = :status', { status: TaskStatusEnum.COMPLETED })
      .andWhere('(task.recurringStatus is null or task.recurringStatus = :recurringStatus)', {
        recurringStatus: TaskRecurringStatus.CREATED,
      })
      .andWhere('organization.id = :organizationId', { organizationId: user.organization.id });
    const sort = (typeof query?.sort === "string") ? JSON.parse(query.sort) : query?.sort;
    if (query.fromDate && query.toDate) {
      tasks.andWhere(`Date(task.status_updated_at) >= '${moment(query.fromDate).format(
        'YYYY-MM-DD',
      )}' and Date(task.status_updated_at) <= '${moment(query.toDate).format('YYYY-MM-DD')}'`);
    }

    if (query.financialYear) {
      tasks.andWhere('task.financialYear = :financialYear', {
        financialYear: query.financialYear,
      });
    }

    if (sort?.column) {
      const columnMap: Record<string, string> = {
        clientname: 'client.displayName',
        financialYear: 'task.financialYear',
        name: 'task.name',
        taskNumber: 'task.taskNumber',
        paymentStatus: 'task.paymentStatus',
        due_date: 'task.dueDate',
        statusUpdatedAt: 'task.statusUpdatedAt'
      };
      const column = columnMap[sort.column] || sort.column;
      tasks.orderBy(column, sort.direction.toUpperCase());
    } else {
      tasks.orderBy('task.statusUpdatedAt', 'DESC');
    }
    if (query.search) {
      tasks.andWhere(
        new Brackets((qb) => {
          qb.where('task.taskNumber LIKE :taskNumber', {
            taskNumber: `%${query.search}%`,
          });
          qb.orWhere('task.name LIKE :name', {
            name: `%${query.search}%`,
          });
          qb.orWhere('service.name LIKE :serviceName', {
            serviceName: `%${query.search}`,
          });
        }),
      );
    }
    if (offset >= 0) {
      tasks.skip(offset);
    }

    if (limit) {
      tasks.take(limit);
    }

    let result = await tasks.getManyAndCount();

    return {
      count: result[1],
      result: result[0],
    };
  }
  async exportCompletedTasksReport(userId: number, body: FindTasksQuery) {
    const newQuery = { ...body, offset: 0, limit: 100000000 };
    let tasks = await this.getCompletedTasksReport(userId, newQuery);


    if (!tasks?.result?.length) throw new BadRequestException('No Data for Export');

    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Completed Task');
    const headers = [
      { header: 'S.No', key: 'serialNo' },
      { header: 'Client / Client Group', key: 'clientGroup' },
      { header: 'Financial Year', key: 'financialYear' },
      { header: 'Task ID', key: 'taskNumber' },
      { header: 'Task Name', key: 'name' },
      { header: 'Billing Status', key: 'paymentStatus' },
      { header: 'Start Date', key: 'taskStartDate' },
      { header: 'Expected Completion Date', key: 'expectedCompletionDate' },
      { header: 'Statutory Due Date', key: 'dueDate' },
      { header: 'Status Updated on', key: 'statusUpdatedAt' },
      // { header: 'Task Leader', key: 'taskLeader' },
      { header: 'Members', key: 'members' },
    ]


    worksheet.columns = headers;

    let serialCounter = 1; // Initialize a counter

    // Initialize array to store max lengths for each column
    const columnMaxLengths = Array(headers.length).fill(0);

    tasks?.result.forEach((task) => {
      function capitalizeFirstLetter(string) {
        return string.charAt(0).toUpperCase() + string.slice(1);
      }
      const rowData = {
        serialNo: serialCounter++,
        clientGroup: task?.client ? task?.client?.displayName : task?.clientGroup?.displayName,
        financialYear: task?.financialYear,
        taskNumber: task?.taskNumber,
        name: task?.name,
        paymentStatus: Boolean(task?.billable) ? "Billed" : "Un-Billed",
        taskStartDate: formatDate(task?.taskStartDate),
        expectedCompletionDate: formatDate(task?.expectedCompletionDate),
        dueDate: formatDate(task.dueDate),
        statusUpdatedAt: task?.statusUpdatedAt ? moment(task?.statusUpdatedAt).format('DD-MM-YYYY HH:mm') : '',
        // taskLeader:  task?.taskLeader.map((assignee) => assignee.fullName).join(', '),
        members: task?.members.map((assignee) => assignee.fullName).join(', '),


      }


      const row = worksheet.addRow(rowData);

      worksheet.columns.forEach((column, colIndex) => {
        const headerLength = column.header?.length || 0; // Length of header
        const cellLength = rowData[column.key]?.toString().length || 0; // Length of cell value
        columnMaxLengths[colIndex] = Math.max(columnMaxLengths[colIndex] || 0, headerLength, cellLength);
      });
    });

    // Apply column widths with padding
    worksheet.columns.forEach((column, colIndex) => {
      column.width = columnMaxLengths[colIndex] + 3; // Add padding for readability
    });

    // Style the header row and center alignment
    const headerRow = worksheet.getRow(1);
    headerRow.font = { bold: true };
    headerRow.eachCell((cell) => {
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '64B5F6' },
      };
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' },
      };
      cell.alignment = { horizontal: 'center', vertical: 'middle' };
    });

    worksheet.columns.forEach((column) => {
      if (column.key === 'name' || column.key === 'members' || column.key === 'clientGroup') {
        column.width = 50;
        column.alignment = { wrapText: true, horizontal: 'center', vertical: 'middle' };
      } else {
        column.alignment = { horizontal: 'center', vertical: 'middle' };
      }
    });

    // Apply wrapText for all rows
    worksheet.eachRow((row) => {
      row.eachCell((cell) => {
        cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true }; // Center alignment and wrap text
      });
    });

    // Freeze the header row
    worksheet.views = [{ state: 'frozen', ySplit: 1 }];

    const buffer = await workbook.xlsx.writeBuffer();
    return buffer;
  }
  //Deleted Task page report
  async getDeletedTasksReport(userId: number, body: FindTasksQuery) {
    const query = body;
    const entityManager = getManager();

    const sqlQuery = `SELECT organization_id FROM user where id = ${userId} ;`;
    let orgId = await entityManager.query(sqlQuery);
    let organizationid = orgId[0].organization_id;

    let user = await User.findOne({
      where: { id: userId },
      relations: ['organization', 'role', 'role.permissions'],
    });
    let allTasks = user.role.permissions.find(
      (permission) => permission.slug === Permissions.VIEW_ALL_TASKS,
    );

    let assignedTasks = user.role.permissions.find(
      (permission) => permission.slug === Permissions.VIEW_ASSIGNED_TASKS,
    );

    let all = allTasks ? true : false;
    let assigned = !all && assignedTasks ? true : false;

    let tasks = await createQueryBuilder(Task, 'task')
      .leftJoinAndSelect('task.organization', 'organization')
      .leftJoinAndSelect('task.client', 'client')
      .leftJoinAndSelect('task.clientGroup', 'clientGroup')
      .leftJoinAndSelect('task.members', 'members')
      .leftJoinAndSelect('members.imageStorage', 'imageStorage')
      .where('task.status = :status', { status: TaskStatusEnum.DELETED })
      .andWhere('organization.id = :organizationId', { organizationId: user.organization.id });

    try {
      return await tasks.getMany();
    } catch (error) {
      console.log('error', error);
    }
  }
  async exportDeletedTasksReport(userId: number, body: FindTasksQuery) {
    const newQuery = { ...body, offset: 0, limit: 100000000 };
    let tasks = await this.tasksService.getDeletedTasks(userId, newQuery);

    if (!tasks.result.length) throw new BadRequestException('No Data for Export');

    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Deleted Tasks');
    const headers = [
      { header: 'S.No', key: 'serialNo' },
      { header: 'Client / Client Group', key: 'clientGroup' },
      { header: 'Financial Year', key: 'financialYear' },
      { header: 'Task ID', key: 'taskNumber' },
      { header: 'Task Name', key: 'name' },
      { header: 'Billing Status', key: 'paymentStatus' },
      { header: 'Start Date', key: 'taskStartDate' },
      // { header: 'Expected Completion Date', key: 'expectedCompletionDate' },
      { header: 'Statutory Due Date', key: 'dueDate' },
      { header: 'Status Updated on', key: 'statusUpdatedAt' },
      { header: 'Task Leader', key: 'taskLeader' },
      { header: 'Members', key: 'members' },
    ]


    worksheet.columns = headers;

    let serialCounter = 1; // Initialize a counter

    // Initialize array to store max lengths for each column
    const columnMaxLengths = Array(headers.length).fill(0);

    tasks.result.forEach((task) => {
      function capitalizeFirstLetter(string) {
        return string.charAt(0).toUpperCase() + string.slice(1);
      }
      const rowData = {
        serialNo: serialCounter++,
        clientGroup: task?.client ? task?.client?.displayName : task?.clientGroup?.displayName,
        financialYear: task.financialYear,
        taskNumber: task.taskNumber,
        name: task.name,
        paymentStatus: Boolean(task?.billable) ? "Billed" : "Un-Billed",
        taskStartDate: formatDate(task.taskStartDate) || '',
        // expectedCompletionDate: task.expectedCompletionDate ? task.expectedCompletionDate : '',
        dueDate: formatDate(task.dueDate),
        statusUpdatedAt: task?.statusUpdatedAt ? moment(task?.statusUpdatedAt).format('DD-MM-YYYY HH:mm') : '',
        taskLeader: task?.taskLeader.map((assignee) => assignee.fullName).join(', '),
        members: task?.members.map((assignee) => assignee.fullName).join(', '),


      }


      const row = worksheet.addRow(rowData);


      worksheet.columns.forEach((column, colIndex) => {
        const headerLength = column.header?.length || 0; // Length of header
        const cellLength = rowData[column.key]?.toString().length || 0; // Length of cell value
        columnMaxLengths[colIndex] = Math.max(columnMaxLengths[colIndex] || 0, headerLength, cellLength);
      });
    });

    // Apply column widths with padding
    worksheet.columns.forEach((column, colIndex) => {
      column.width = columnMaxLengths[colIndex] + 3; // Add padding for readability
    });

    // Style the header row and center alignment
    const headerRow = worksheet.getRow(1);
    headerRow.font = { bold: true };
    headerRow.eachCell((cell) => {
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '64B5F6' },
      };
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' },
      };
      cell.alignment = { horizontal: 'center', vertical: 'middle' };
    });

    worksheet.columns.forEach((column) => {
      if (column.key === 'name' || column.key === 'taskLeader' || column.key === 'members' || column.key === 'clientGroup') {
        column.width = 50;
        column.alignment = { wrapText: true, horizontal: 'center', vertical: 'middle' };
      } else {
        column.alignment = { horizontal: 'center', vertical: 'middle' };
      }
    });

    // Apply wrapText for all rows
    worksheet.eachRow((row) => {
      row.eachCell((cell) => {
        cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true }; // Center alignment and wrap text
      });
    });

    // Freeze the header row
    worksheet.views = [{ state: 'frozen', ySplit: 1 }];

    const buffer = await workbook.xlsx.writeBuffer();
    return buffer;
  }



  //Deleted clients report
  async getDeletedClientsPageReport(userId: number, body: FindQueryDto) {
    const { limit, offset } = body;
    const { status: state, category, subCategory, monthAdded, search, labels } = body;
    const month = new Date(monthAdded).getMonth() + 1;
    const year = new Date(monthAdded).getFullYear();

    let user = await User.findOne({ where: { id: userId }, relations: ['organization'] });

    let clients = getConnection()
      .createQueryBuilder(Client, 'client')
      .leftJoin('client.labels', 'labels')
      .leftJoin('client.organization', 'organization')
      .orderBy('client.displayName', 'ASC')
      .leftJoinAndSelect('client.contactPersons', 'contactPersons')
      .where('organization.id = :organization', { organization: user.organization.id })
      .andWhere('client.status = :status', { status: UserStatus.DELETED });

    const sort = (typeof body?.sort === "string") ? JSON.parse(body.sort) : body?.sort;
    if (sort?.column) {
      const columnMap: Record<string, string> = {
        clientId: 'client.clientId',
        displayName: 'client.displayName',
      };
      const column = columnMap[sort.column] || sort.column;
      clients.orderBy(column, sort.direction.toUpperCase());
    } else {
      clients.orderBy('client.createdAt', 'DESC');
    };
    let result = await clients.getManyAndCount();

    return {
      count: result[1],
      result: result[0],
    };
  }

  async exportDeletedClientsPageReport(userId: number, body: FindQueryDto) {
    const newQuery = { ...body, offset: 0, limit: 100000000 };
    let clients = await this.getDeletedClientsPageReport(userId, newQuery);


    if (!clients.result.length) throw new BadRequestException('No Data for Export');

    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Deleted Clients');
    const headers = [
      { header: 'S.No', key: 'serialNo' },
      { header: 'Client Id', key: 'clientId' },
      { header: 'GSTIN / PAN', key: 'gstpan' },
      { header: 'Category', key: 'category' },
      { header: 'Sub Category', key: 'subCategory' },
      { header: 'Client Name', key: 'displayName' },
      { header: 'Mobile #', key: 'mobileNumber' },
      { header: 'Email ID', key: 'email' },
      { header: 'Status Updated At', key: 'statusUpdatedAt' },
    ];

    worksheet.columns = headers;

    let serialCounter = 1; // Initialize a counter

    // Initialize array to store max lengths for each column
    const columnMaxLengths = Array(headers.length).fill(0);

    clients.result.forEach((client) => {
      function capitalizeFirstLetter(string) {
        return string.charAt(0).toUpperCase() + string.slice(1);
      }
      const rowData = {
        serialNo: serialCounter++,
        clientId: client.clientId,
        displayName: client?.displayName,
        mobileNumber: fullMobileWithCountry(client?.mobileNumber, client?.countryCode),
        email: client?.email,
        category: client?.category ? getTitle(client?.category) : "",
        subCategory: client?.subCategory ? getTitle(client?.subCategory) : "",
        gstpan: client?.gstNumber ? client?.gstNumber : client?.panNumber,
        statusUpdatedAt: moment(client?.updatedAt).format("DD-MM-YYYY h:mm a")
      }


      const row = worksheet.addRow(rowData);


      worksheet.columns.forEach((column, colIndex) => {
        const headerLength = column.header?.length || 0; // Length of header
        const cellLength = rowData[column.key]?.toString().length || 0; // Length of cell value
        columnMaxLengths[colIndex] = Math.max(columnMaxLengths[colIndex] || 0, headerLength, cellLength);
      });
    });

    // Apply column widths with padding
    worksheet.columns.forEach((column, colIndex) => {
      column.width = columnMaxLengths[colIndex] + 3; // Add padding for readability
    });

    // Style the header row and center alignment
    const headerRow = worksheet.getRow(1);
    headerRow.font = { bold: true };
    headerRow.eachCell((cell) => {
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '64B5F6' },
      };
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' },
      };
      cell.alignment = { horizontal: 'center', vertical: 'middle' };
    });

    worksheet.columns.forEach((column) => {
      if (column.key === 'displayName') {
        column.width = 50;
        column.alignment = { wrapText: true, horizontal: 'center', vertical: 'middle' };
      } else {
        column.alignment = { horizontal: 'center', vertical: 'middle' };
      }
    });

    // Apply wrapText for all rows
    worksheet.eachRow((row) => {
      row.eachCell((cell) => {
        cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true }; // Center alignment and wrap text
      });
    });

    // Freeze the header row
    worksheet.views = [{ state: 'frozen', ySplit: 1 }];

    const buffer = await workbook.xlsx.writeBuffer();
    return buffer;
  }

  async exportDeletedClientsGroupPageReport(userId: number, body: FindQueryDto) {
    const newQuery = { ...body, offset: 0, limit: 100000000 };
    let clients = await this.groupservice.findDeleted(userId, newQuery);

    if (!clients[0]?.length) throw new BadRequestException('No Data for Export');

    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Deleted Clients Group');
    const headers = [
      { header: 'S.No', key: 'serialNo' },
      { header: 'Client Group ID', key: 'clientId' },
      { header: 'Client Group #', key: 'clientNumber' },
      { header: 'GSTIN / PAN', key: 'gstpan' },
      { header: 'Group Name', key: 'displayName' },
      { header: 'Mobile #', key: 'mobileNumber' },
      { header: 'Email ID', key: 'email' },
      { header: 'Status Updated At', key: 'statusUpdatedAt' },
    ];

    worksheet.columns = headers;

    let serialCounter = 1;

    // Initialize array to store max lengths for each column
    const columnMaxLengths = Array(headers.length).fill(0);

    clients[0]?.forEach((client) => {
      function capitalizeFirstLetter(string) {
        return string.charAt(0).toUpperCase() + string.slice(1);
      }
      const rowData = {
        serialNo: serialCounter++,
        clientId: client.clientId,
        displayName: client?.displayName,
        mobileNumber: fullMobileWithCountry(client?.mobileNumber, client?.countryCode),
        email: client?.email,
        gstpan: client?.gstNumber ? client?.gstNumber : client?.panNumber,
        statusUpdatedAt: moment(client?.updatedAt).format("DD-MM-YYYY h:mm a"),
        clientNumber: client?.clientNumber,
      }


      const row = worksheet.addRow(rowData);


      worksheet.columns.forEach((column, colIndex) => {
        const headerLength = column.header?.length || 0; // Length of header
        const cellLength = rowData[column.key]?.toString().length || 0; // Length of cell value
        columnMaxLengths[colIndex] = Math.max(columnMaxLengths[colIndex] || 0, headerLength, cellLength);
      });
    });

    // Apply column widths with padding
    worksheet.columns.forEach((column, colIndex) => {
      column.width = columnMaxLengths[colIndex] + 3; // Add padding for readability
    });

    // Style the header row and center alignment
    const headerRow = worksheet.getRow(1);
    headerRow.font = { bold: true };
    headerRow.eachCell((cell) => {
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '64B5F6' },
      };
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' },
      };
      cell.alignment = { horizontal: 'center', vertical: 'middle' };
    });

    worksheet.columns.forEach((column) => {
      if (column.key === 'displayName') {
        column.width = 50;
        column.alignment = { wrapText: true, horizontal: 'center', vertical: 'middle' };
      } else {
        column.alignment = { horizontal: 'center', vertical: 'middle' };
      }
    });

    // Apply wrapText for all rows
    worksheet.eachRow((row) => {
      row.eachCell((cell) => {
        cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true }; // Center alignment and wrap text
      });
    });

    // Freeze the header row
    worksheet.views = [{ state: 'frozen', ySplit: 1 }];

    const buffer = await workbook.xlsx.writeBuffer();
    return buffer;
  }
  //Deleted users report
  async geDeletedtUsersReport(userId: number, body: getUsersDto) {
    const { fullName, mobileNumber, email, status, role, search } = body;

    const entityManager = getManager();
    const query = 'getDeletedUsersReport';

    // Fetch organization id
    const sqlQuery = `SELECT organization_id FROM user WHERE id = ${userId};`;
    const orgId = await entityManager.query(sqlQuery);
    const organizationid = orgId[0].organization_id;

    // Fetch user and organization data
    const user = await User.findOne({ where: { id: userId }, relations: ['organization'] });

    // Create query builder to fetch deleted users
    const deletedUsersQuery = createQueryBuilder(User, 'user')
      .leftJoin('user.organization', 'organization')
      .leftJoinAndSelect('user.role', 'role')
      .leftJoinAndSelect('user.profile', 'profile')
      .where('organization.id = :organizationId', { organizationId: user.organization.id })
      .andWhere('user.status = :status', { status: UserStatus.DELETED })
      .andWhere('user.type = :type', { type: UserType.ORGANIZATION }); // Filter deleted users

    // Execute query
    const deletedUsers = await deletedUsersQuery.getMany();

    // Emit event and return response
    this.eventEmitter.emit(Event_Actions.REPORT_GENERATED, {
      users: deletedUsers,
      query,
      body,
      organizationid,
      user,
    });

    return deletedUsers;
  }

  async getDeletedUsers(userId: number, query: any) {
    const { limit, offset } = query;
    let user = await User.findOne({
      where: { id: userId },
      relations: ['organization'],
    });

    let users = createQueryBuilder(User, 'user')
      .leftJoinAndSelect('user.profile', 'profile')
      .leftJoinAndSelect('user.organization', 'organization')
      .leftJoinAndSelect('user.role', 'role')
      .where('organization.id = :orgId', { orgId: user.organization.id })
      .andWhere('user.status = :status', { status: UserStatus.DELETED });

    const sort = typeof query?.sort === 'string' ? JSON.parse(query.sort) : query?.sort;
    if (sort?.column) {
      const columnMap: Record<string, string> = {
        employeeId: 'profile.employeeId',
        fullName: 'user.fullName',
      };
      const column = columnMap[sort.column] || sort.column;
      users.orderBy(column, sort.direction.toUpperCase());
    } else {
      users.orderBy('user.updatedAt', 'DESC');
    }
    if (query.search) {
      users.andWhere(
        new Brackets((qb) => {
          qb.where('user.fullName LIKE :fullName', {
            fullName: `%${query.search}%`,
          });
          qb.orWhere('user.email LIKE :email', {
            email: `%${query.search}%`,
          });
        }),
      );
    }

    if (offset >= 0) {
      users.skip(offset);
    }

    if (limit) {
      users.take(limit);
    }

    // let users = await User.findAndCount({
    //   where: {
    //     organization: { id: user.organization.id },
    //     status: UserStatus.DELETED,
    //   },
    //   relations: ['profile'],
    //   take: query.limit,
    //   skip: query.offset,
    // });
    let result = await users.getManyAndCount();

    return result;
  }

  async exportDeletedUsersReport(userId: number, body: getUsersDto) {
    const newQuery = { ...body, offset: 0, limit: 100000000 };
    const [users, totalCount]: [User[], number] = await this.getDeletedUsers(userId, body);
    console.log("data:", users)

    if (!users.length) throw new BadRequestException('No Data for Export');

    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Deleted Users');
    const headers = [
      { header: 'S.No', key: 'serialNo' },
      { header: 'User ID', key: 'employeeId' },
      { header: 'Full Name', key: 'fullName' },
      { header: 'Mobile#', key: 'mobileNumber' },
      { header: 'Email Id', key: 'email' },

    ]


    worksheet.columns = headers;

    let serialCounter = 1; // Initialize a counter

    // Initialize array to store max lengths for each column
    const columnMaxLengths = Array(headers.length).fill(0);

    users.forEach((user) => {
      function capitalizeFirstLetter(string) {
        return string.charAt(0).toUpperCase() + string.slice(1);
      }
      const rowData = {
        serialNo: serialCounter++,
        employeeId: user?.profile?.employeeId,
        fullName: user.fullName,
        mobileNumber: fullMobileWithCountry(user?.mobileNumber, user?.countryCode),
        email: user.email,


      }


      const row = worksheet.addRow(rowData);


      worksheet.columns.forEach((column, colIndex) => {
        const headerLength = column.header?.length || 0; // Length of header
        const cellLength = rowData[column.key]?.toString().length || 0; // Length of cell value
        columnMaxLengths[colIndex] = Math.max(columnMaxLengths[colIndex] || 0, headerLength, cellLength);
      });
    });

    // Apply column widths with padding
    worksheet.columns.forEach((column, colIndex) => {
      column.width = columnMaxLengths[colIndex] + 3; // Add padding for readability
    });

    // Style the header row and center alignment
    const headerRow = worksheet.getRow(1);
    headerRow.font = { bold: true };
    headerRow.eachCell((cell) => {
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '64B5F6' },
      };
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' },
      };
      cell.alignment = { horizontal: 'center', vertical: 'middle' };
    });

    worksheet.columns.forEach((column) => {
      if (column.key === 'fullName') {
        column.width = 50;
        column.alignment = { wrapText: true, horizontal: 'center', vertical: 'middle' };
      } else {
        column.alignment = { horizontal: 'center', vertical: 'middle' };
      }
    });

    // Apply wrapText for all rows
    worksheet.eachRow((row) => {
      row.eachCell((cell) => {
        cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true }; // Center alignment and wrap text
      });
    });

    // Freeze the header row
    worksheet.views = [{ state: 'frozen', ySplit: 1 }];

    const buffer = await workbook.xlsx.writeBuffer();
    return buffer;
  }

  //client pending taks page report
  async getClientPendingTasksReport(clientId: number, body: FindTasksQuery, userId: number) {
    const entityManager = getManager();

    const query = 'getTasksReport';
    const user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
    let organizationId = await Organization.findOne({ where: { id: user.organization.id } });

    let client = await Client.findOne({
      where: {
        id: clientId,
      },
      relations: ['organization'],
    });
    let tasks = await createQueryBuilder(Task, 'task')
      .leftJoinAndSelect('task.organization', 'organization')
      .leftJoinAndSelect('task.client', 'client')
      .leftJoinAndSelect('task.members', 'members')
      .where('task.client.id = :clientId', { clientId })
      .leftJoinAndSelect('members.imageStorage', 'imageStorage')
      .where('task.status = :status', { status: TaskStatusEnum.PENDING })
      .andWhere('organization.id = :organizationId', { organizationId: user.organization.id });

    this.eventEmitter.emit(Event_Actions.REPORT_GENERATED, { query, body, organizationId, user });
    return tasks.getMany();
  }
  async exportClientPendingTasksReport(clientId: number, body: FindTasksQuery, userId: number) {
    let tasks = await this.getClientPendingTasksReport(clientId, body, userId);
    let rows = tasks?.map((task) => {
      const formatDate = (dateString) => {
        const date = new Date(dateString);
        const day = date.getDate().toString().padStart(2, '0');
        const month = (date.getMonth() + 1).toString().padStart(2, '0');
        const year = date.getFullYear();
        return `${day}-${month}-${year}`;
      };
      return {
        'Task ID': task?.taskNumber,
        'Client': task?.client?.displayName,
        'Task Name': task?.name,
        'Due Date': formatDate(task.dueDate),
        'Priority': getTitle(task?.priority),
        'Status': getTitle(task?.status),
        'Members': task?.members.map((assignee) => assignee.fullName).join(', '),
      };
    });

    const worksheet = xlsx.utils.json_to_sheet(rows);
    const workbook = xlsx.utils.book_new();
    xlsx.utils.book_append_sheet(workbook, worksheet, 'Clients');
    let file = xlsx.write(workbook, { type: 'buffer' });
    return file;
  }

  async exportTaskDelayReport(body: any) {
    body.users = body?.users?.id;
    let tasks = await detailedoverduecompletedtasks(body);
    if (!tasks.length) {
      throw new BadRequestException('No Data for Export !');
    }
    let rows = tasks?.map((task) => {
      const formatDate = (dateString) => {
        const date = new Date(dateString);
        const day = date.getDate().toString().padStart(2, '0');
        const month = (date.getMonth() + 1).toString().padStart(2, '0');
        const year = date.getFullYear();
        return `${day}-${month}-${year}`;
      };
      return {
        'Client/Client Group': task?.client?.displayName,
        // 'Service Category': task?.category?.name,
        // 'Service Sub-Category': task?.subCategory?.name,
        'Task ID': task.taskNumber,
        'Task Name': task?.name,
        // 'Priority': getTitle(task?.priority),
        // 'Status': getTitle(task?.status),
        'Statutory Due Date': formatDate(task.dueDate),
        // 'Billing Type': task.billable ? 'Billbale' : 'Non-Billable',
        'Task Status': getTitle(task?.status),
        'Delayed by Days': task.overDueBy,
        'Employees': task?.members.map((assignee) => assignee.fullName).join(', '),
      };
    });

    const worksheet = xlsx.utils.json_to_sheet(rows);
    const workbook = xlsx.utils.book_new();
    xlsx.utils.book_append_sheet(workbook, worksheet, 'Task Missed Deadline');
    let file = xlsx.write(workbook, { type: 'buffer' });
    return file;
  }

  async exportErrorsReport(userId: number, body: any) {
    let rows = body.map((item) => {
      return {
        errors: item,
      };
    });

    const worksheet = xlsx.utils.json_to_sheet(rows);
    const workbook = xlsx.utils.book_new();
    xlsx.utils.book_append_sheet(workbook, worksheet, 'Clients');
    let file = xlsx.write(workbook, { type: 'buffer' });
    return file;
  }

  async exportClientImportErrors(userId: number, body: any) {
    const user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
    let rows = body.map((item) => {
      return {
        errors: item,
      };
    });

    const totalClientErrors = body.filter(item => !item.includes("TAN") && !item.includes("Trace") && !item.includes("Atom Pro"));
    const panCredentilas = body.filter(item => item.includes("Atom Pro") && item.includes("PAN"));
    const gstCredentials = body.filter(item => item.includes("GST") && item.includes("Atom Pro"));
    const tanCredentials = body.filter(item => (item.includes("TAN") || item.includes("Trace")) && !item.includes("Clients Added in Atom Pro"));

    const workbook = new ExcelJS.Workbook();
    const clientSheet = workbook.addWorksheet('Client');
    const panSheet = workbook.addWorksheet('PAN');
    const tanSheet = workbook.addWorksheet('TAN');
    const gstSheet = workbook.addWorksheet('GST');

    // Format the header row
    const headerRow = clientSheet.getRow(1);
    headerRow.eachCell((cell) => {
      cell.font = { bold: true, size: 14 };
      cell.alignment = { horizontal: 'center' };
    });

    const markerSheet = workbook.addWorksheet('Metadata');
    markerSheet.getColumn(1).values = [user?.organization?.id, user?.fullName];
    markerSheet.state = 'hidden';

    workbook.views = [
      {
        x: 0,
        y: 0,
        width: 20000,
        height: 5000,
        activeTab: 0,
        firstSheet: 0,
        visibility: 'visible',
      },
    ];

    const columns = [
      { header: 'import errors', key: 'error', width: 100 },
    ];

    clientSheet.columns = columns;
    panSheet.columns = columns;
    gstSheet.columns = columns;
    tanSheet.columns = columns;

    const addDataToSheet = (sheet: ExcelJS.Worksheet, data: any[], usernameKey: string) => {
      data.forEach((record) =>
        sheet.addRow({
          error: record,
        }),
      );
    };

    addDataToSheet(clientSheet, totalClientErrors, 'client');
    addDataToSheet(panSheet, panCredentilas, 'panNumber');
    addDataToSheet(tanSheet, tanCredentials, 'tanNumber');
    addDataToSheet(gstSheet, gstCredentials, 'userName');

    const styleSheet = (
      sheet: ExcelJS.Worksheet,
      editableColumns: number[],
      dataLength: number,
    ) => {
      sheet.views = [{ state: 'frozen', ySplit: 1 }];
      const headerRow = sheet.getRow(1);
      headerRow.eachCell((cell) => {
        cell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FF64B5F6' } };
        cell.font = { bold: true };
        cell.border = {
          top: { style: 'thin' },
          left: { style: 'thin' },
          bottom: { style: 'thin' },
          right: { style: 'thin' },
        };
      });

      sheet.getColumn(1).alignment = { wrapText: true };

      // Apply styling and protection to all rows up to dataLength
      for (let rowNum = 2; rowNum <= dataLength + 1; rowNum++) {
        const row = sheet.getRow(rowNum);
        row.eachCell({ includeEmpty: true }, (cell, colNum) => {
          cell.alignment = { horizontal: 'left', vertical: 'middle', wrapText: true };
          cell.border = {
            top: { style: 'thin' },
            left: { style: 'thin' },
            bottom: { style: 'thin' },
            right: { style: 'thin' },
          };
          cell.protection = { locked: !editableColumns.includes(colNum) };
          // Ensure editable columns have a default value if empty
          if (editableColumns.includes(colNum) && !cell.value) {
            cell.value = '';
          }
        });

        // Apply status formatting if applicable
        if (row.getCell(8).value) {
          const statusCell = row.getCell(8);
          statusCell.font = {
            color: { argb: statusCell.value === 'ENABLE' ? 'FF00B050' : 'FFFF0000' },
            bold: true,
          };
        }
      }

      sheet.protect('vider@1818', {
        selectLockedCells: true,
        selectUnlockedCells: true,
        formatColumns: true,
        formatRows: true,
      });
    };
    styleSheet(clientSheet, [7], totalClientErrors.length);
    styleSheet(panSheet, [7], panCredentilas.length);
    styleSheet(gstSheet, [7], gstCredentials.length);
    styleSheet(tanSheet, [7, 9, 10], tanCredentials.length);

    const buffer = await workbook.xlsx.writeBuffer();
    return buffer;
  }

  async getExtesnsionClients(userId: number, body: getClientsReportDto) {
    const { fromDate, toDate, category, subCategory, status } = body;

    const entityManager = getManager();
    const query = 'getClientsReport';
    const sqlQuery = `SELECT organization_id FROM user where id = ${userId} `;
    let orgId = await entityManager.query(sqlQuery);
    let organizationid = orgId[0].organization_id;

    let user = await User.findOne({ where: { id: userId }, relations: ['organization', 'role'] });

    let ViewAll = user.role.permissions.find(
      (permission) => permission.slug === Permissions.VIEW_ALL_CLIENT_MANAGERS,
    );
    let ViewAssigned = user.role.permissions.find(
      (permission) => permission.slug === Permissions.VIEW_ASSIGNED_CLIENT_MANAGERS,
    );

    let clients = createQueryBuilder(Client, 'client')
      .leftJoin('client.organization', 'organization')
      .leftJoin('client.clientManagers', 'clientManagers')
      .where('organization.id = :organizationId', { organizationId: user.organization.id });

    if (ViewAssigned && !ViewAll) {
      clients.andWhere('clientManagers.id = :userId', { userId });
    }

    if (category) {
      clients.andWhere('client.category = :category', { category });
    }

    if (subCategory) {
      clients.andWhere('client.subCategory = :subCategory', { subCategory });
    }

    if (fromDate) {
      clients.andWhere('client.createdAt >= :fromDate', { fromDate });
    }

    if (toDate) {
      clients.andWhere('client.createdAt <= :toDate', {
        toDate: moment(toDate).add(1, 'days').format('YYYY-MM-DD'),
      });
    }

    if (status === 'Active') {
      clients.andWhere('client.status = :status', { status: UserStatus.ACTIVE });
    }
    if (status === 'Inactive') {
      clients.andWhere('client.status = :status', { status: UserStatus.INACTIVE });
    }

    if (status === 'Deleted') {
      clients.andWhere('client.status = :status', { status: UserStatus.DELETED });
    }

    // return clients.getMany();
    if (ViewAll || ViewAssigned) {
      let response = await clients.getMany();
      return response;
    } else {
      return [];
    }
  }
}
