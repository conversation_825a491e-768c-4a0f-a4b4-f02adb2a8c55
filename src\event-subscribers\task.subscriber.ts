import { generateEmpoyeeId } from 'src/utils';
import {
  Connection,
  EntitySubscriberInterface,
  EventSubscriber,
  InsertEvent,
  Like,
  UpdateEvent,
  getManager,
} from 'typeorm';
import Task from '../modules/tasks/entity/task.entity';
import {
  getAdminEmailsBasedOnOrganizationId,
  getAdminIDsBasedOnOrganizationId,
  getUserDetails,
  insertINTONotificationUpdate,
  insertINTOnotificationForApproval,
} from 'src/utils/re-use';
import { User } from 'src/modules/users/entities/user.entity';
import { getAdminIds } from 'src/utils/re-use';
import { TaskRecurringStatus } from 'src/modules/tasks/dto/types';
import ViderWhatsappSessions from 'src/modules/whatsapp/entity/viderWhatsappSessions';
import { sendWhatsAppTextMessage } from 'src/modules/whatsapp/whatsapp.service';
import { sendnewMail } from 'src/emails/newemails';
import * as moment from 'moment';
import { fullMobileNumberWithCountry } from 'src/utils/validations/fullMobileWithCountry';
import { Organization } from 'src/modules/organization/entities/organization.entity';
import notify from 'src/notifications/notify';
// const { Kafka } = require('kafkajs')
// const { generateAuthToken } = require('aws-msk-iam-sasl-signer-js')

// async function oauthBearerTokenProvider(region) {
//    // Uses AWS Default Credentials Provider Chain to fetch credentials
//    const authTokenResponse = await generateAuthToken({ region });
//    return {
//        value: authTokenResponse.token
//    }
// }

@EventSubscriber()
export class TaskSubscriber implements EntitySubscriberInterface<Task> {
  constructor(private readonly connection: Connection) {
    connection.subscribers.push(this);
  }

  listenTo() {
    return Task;
  }

  entityManager = getManager();

  async beforeInsert(event: InsertEvent<Task>) {
    let orgId = event?.entity?.organization?.id;
    if (!event.entity.recurring && event.entity.recurringStatus === TaskRecurringStatus.CREATED) {
      let count = await this.connection.manager.getRepository(Task).count({
        where: {
          organization: {
            id: orgId,
          },
          taskNumber: Like('%NRT%'),
        },
      });
      function generateNRTId(id: number) {
        if (id < 10000) {
          return 'NRT' + id.toString().padStart(4, '0');
        }
        return 'NRT' + id;
      }
      event.entity.taskNumber = generateNRTId(count + 1);
    }
    if (event?.entity?.parentTask && event.entity.recurringStatus === TaskRecurringStatus.CREATED) {
      let subCount = await this.connection.manager.getRepository(Task).count({
        where: {
          organization: {
            id: orgId,
          },
          taskNumber: Like('%SBT%'),
        },
      });
      function generateSBTId(id: number) {
        if (id < 10000) {
          return 'SBT' + id.toString().padStart(4, '0');
        }
        return 'SBT' + id;
      }
      event.entity.taskNumber = generateSBTId(subCount + 1);
    }
  }

  getAllTaskMemberNames = async (taskMemeberIds) => {
    const getUserName = (userId) => `SELECT full_name FROM user where id= ${userId};`;

    const taskMemberNames = await Promise.all(
      taskMemeberIds.map(async (userId) => {
        const userName = await this.entityManager.query(getUserName(userId));
        return userName[0].full_name;
      }),
    );
    const allTaskmemberNames = taskMemberNames.join(', ');
    return allTaskmemberNames;
  };

  beforeTaskFeeAmount;

  async beforeUpdate(event: UpdateEvent<Task>) {
    this.beforeTaskFeeAmount = event.databaseEntity?.feeAmount;
  }

  async afterUpdate(event: UpdateEvent<Task>) {
    if (!event.entity.bulkUpdate) {
      const oldFeeAmount = this.beforeTaskFeeAmount;
      const updatedFeeAmount = parseInt(event.entity?.feeAmount);
      const taskName = event.entity?.name;
      const taskId = event.entity?.id;
      const taskNumber = event?.entity?.taskNumber;
      const userid = event.entity['userId'];
      if (
        userid &&
        event.entity.members &&
        event.entity.recurringStatus !== TaskRecurringStatus.PENDING
      ) {
        const getUserQuery = `SELECT full_name FROM user where id = ${userid};`;
        const getUser = await this.entityManager.query(getUserQuery);
        const userName = getUser[0]?.full_name;
        if (taskId) {
          const taskMembers = event.entity.members;
          const taskMemberIds = taskMembers.map((obj) => obj.id);
          const orgIdQuery = `SELECT organization_id, client_id, client_group_id FROM task WHERE id=${taskId}`;
          const getOrgId = await this.entityManager.query(orgIdQuery);
          const orgId = getOrgId[0]?.organization_id;
          const organization = await Organization.findOne({ id: orgId });


          const addressParts = [
            organization.buildingNo || '',
            organization.floorNumber || '',
            organization.buildingName || '',
            organization.street || '',
            organization.location || '',
            organization.city || '',
            organization.district || '',
            organization.state || ''
          ].filter(part => part && part.trim() !== '');
          const pincode = organization.pincode && organization.pincode.trim() !== '' ? ` - ${organization.pincode}` : '';

          const address = addressParts.join(', ') + pincode;
          const clientId = getOrgId[0]?.client_id;
          const clientGroupId = getOrgId[0]?.client_group_id;
          if (clientId) {
            const clientNameQuery = `SELECT display_name FROM client WHERE id=${clientId}`;
            const getClient = await this.entityManager.query(clientNameQuery);
            const clientName = getClient[0]?.display_name;
            const orgAdminIds = await getAdminIDsBasedOnOrganizationId(orgId);
            const uniqueUserIds = Array.from(new Set([...orgAdminIds, ...taskMemberIds]));
            const taskDueDateFormat = moment(event?.entity?.dueDate).format('DD-MM-YYYY');

            if (oldFeeAmount !== updatedFeeAmount && !Number.isNaN(updatedFeeAmount)) {
              const title = 'Fee';
              const key = 'FEE_PUSH';
              const body =
                oldFeeAmount === null
                  ? `The fee amount for "<strong>${taskName}</strong>" of <strong>${clientName}</strong> has been updated from 0 to "<strong>${updatedFeeAmount}</strong>" by <strong>${userName}</strong>.`
                  : `The fee amount for "<strong>${taskName}</strong>" of <strong>${clientName}</strong> has been updated from "<strong>${oldFeeAmount}</strong>" to "<strong>${updatedFeeAmount}</strong>" by <strong>${userName}</strong>.`;

              // insertINTOnotification(title, body, orgAdminIds, orgId);
              insertINTONotificationUpdate(title, body, uniqueUserIds, orgId, key, clientId);
              const organization = await Organization.findOne({ id: orgId });


              const addressParts = [
                organization.buildingNo || '',
                organization.floorNumber || '',
                organization.buildingName || '',
                organization.street || '',
                organization.location || '',
                organization.city || '',
                organization.district || '',
                organization.state || ''
              ].filter(part => part && part.trim() !== '');
              const pincode = organization.pincode && organization.pincode.trim() !== '' ? ` - ${organization.pincode}` : '';

              const address = addressParts.join(', ') + pincode;
              if (uniqueUserIds) {
                for (let user of uniqueUserIds) {
                  const taskUserDetails = await getUserDetails(user);
                  await sendnewMail({
                    id: taskUserDetails?.id,
                    key: 'FEE_MAIL',
                    email: taskUserDetails?.email,
                    data: {
                      taskUserName: taskUserDetails?.full_name,
                      taskName,
                      clientName,
                      taskId: event?.entity?.taskNumber,
                      feeAmount: updatedFeeAmount,
                      oldFeeAmount: oldFeeAmount,
                      userId: userid,
                      adress: address,
                      phoneNumber: organization?.mobileNumber,
                      mail: organization?.email,
                      legalName: organization?.tradeName || organization?.legalName

                    },
                    filePath: 'fee',
                    subject: `Fee Update: ${taskName} with ${clientName}`,
                  });
                  //whatsapp
                  try {
                    if (user !== undefined) {
                      const sessionValidation = await ViderWhatsappSessions.findOne({
                        where: { userId: user, status: 'ACTIVE' },
                      });
                      if (sessionValidation) {
                        const adminUserDetails = await getUserDetails(user);

                        const { full_name: userFullName, mobile_number: userPhoneNumber, id, organization_id } = adminUserDetails;
                        const key = 'FEE_WHATSAPP';
                        const whatsappMessageBody = `
               Hi ${userFullName}
               
               service fee for the ${taskName} of ${clientName} has been updated by ${userName}
               
               updated service fee: ${updatedFeeAmount}
               
               We hope this helps!`;
                        await sendWhatsAppTextMessage(
                          `91${userPhoneNumber}`,
                          whatsappMessageBody,
                          organization_id,
                          title,
                          id,
                          key,
                        );
                      }

                    }
                  } catch (error) {
                    console.error('Error sending User WhatsApp notification:', error);
                  }

                }
              }
            }
            if (event.entity) {
              const oldTaskMembers = event.entity?.['oldTaskMembers'];
              const updatedTaskMembers = event.entity?.members;
              if (Array.isArray(oldTaskMembers)) {
                const removedMembers = oldTaskMembers.reduce((acc, member) => {
                  if (
                    !Array.isArray(updatedTaskMembers) ||
                    !updatedTaskMembers.some((eachMember) => member.id === eachMember.id)
                  ) {
                    acc.push(member);
                  }
                  return acc;
                }, []);
                if (removedMembers.length > 0) {
                  const taskMemberIds = removedMembers.map((obj) => obj.id);
                  const presentTaskMemberIds = updatedTaskMembers.map((obj) => obj.id);
                  const allTaskMemberNames = await this.getAllTaskMemberNames(taskMemberIds);
                  const title = 'User Removed from already created Task';
                  const body = `"${allTaskMemberNames}" has been removed as task member from "${taskName}" of ${clientName} by ${userName}`;

                  const uniqueUserIds = Array.from(
                    new Set([...orgAdminIds, ...taskMemberIds, ...presentTaskMemberIds]),
                  );
                  // insertINTOnotification(title, body, uniqueUserIds, orgId);
                  const key = 'USER_REMOVED_FROM_ALREADY_CREATED_TASK_PUSH';
                  insertINTONotificationUpdate(title, body, uniqueUserIds, orgId, key);
                  let commaSeparatedNames = '';
                  if (event?.entity?.members) {
                    const fullNames = event?.entity?.members.map((member) => member.fullName);
                    commaSeparatedNames = fullNames.join(', ');
                  }

                  // const userListMails = await getAdminEmailsBasedOnOrganizationId(orgId);
                  for (let user of taskMemberIds) {
                    const taskUserDetails = await getUserDetails(user);
                    await sendnewMail({
                      id: taskUserDetails?.id,
                      key: 'FEE_MAIL',
                      email: taskUserDetails?.email,
                      data: {
                        taskId: taskNumber, // ok
                        taskName: taskName, //ok
                        clientName: clientName, // ok
                        adminName: taskUserDetails?.full_name, // ok
                        userName: userName,
                        serviceName: event?.entity?.name, // ok
                        dueDate: taskDueDateFormat, // ok
                        assignedUser: allTaskMemberNames, //ok
                        existingUsers: commaSeparatedNames, // ok
                        userId: userid,
                      },
                      filePath: 'user-removed-already-created-task',
                      subject: "You've Been Removed from a Task",
                    });
                    //  whatsapp
                    try {
                      if (taskUserDetails !== undefined) {
                        for (let user of event?.entity?.members) {
                          const sessionValidation = await ViderWhatsappSessions.findOne({
                            where: { userId: user?.id, status: 'ACTIVE' },
                          });
                          if (sessionValidation) {
                            const userDetails = await getUserDetails(user?.id);
                            const { mobile_number: userPhoneNumber, id } = userDetails;
                            const key = 'USER_REMOVED_FROM_ALREADY_CREATED_TASK_WHATSAPP';
                            const whatsappMessageBody = `
    
    ${allTaskMemberNames} has been removed from  ${taskName} 
    
    Task Details :
     Task Id   : ${taskId}
     Task Name : ${taskName}
     Task Due Date : ${event?.entity?.dueDate}
     Client Name : ${clientName}
    
    We hope this helps!
              `;
                            await sendWhatsAppTextMessage(
                              `91${userPhoneNumber}`,
                              whatsappMessageBody,
                              user?.id,
                              title,
                              id,
                              key,
                            );
                          }
                        }
                      }
                    } catch (error) {
                      console.error('Error sending User WhatsApp notification:', error);
                    }
                  }

                  // admins
                  for (let user of orgAdminIds) {
                    const taskUserDetails = await getUserDetails(user);
                    await sendnewMail({
                      id: taskUserDetails?.id,
                      key: 'USER_REMOVED_FROM_ALREADY_CREATED_TASK_MAIL',
                      email: taskUserDetails?.email,
                      data: {
                        taskId: taskNumber, // ok
                        taskName: taskName, //ok
                        clientName: clientName, // ok
                        adminName: taskUserDetails?.full_name, // ok
                        userName: userName,
                        serviceName: event?.entity?.name, // ok
                        dueDate: taskDueDateFormat, // ok
                        assignedUser: allTaskMemberNames, //ok
                        existingUsers: commaSeparatedNames, // ok
                        userId: userid,
                        adress: address,
                        phoneNumber: organization?.mobileNumber,
                        mail: organization?.email,
                        legalName: organization?.tradeName || organization?.legalName
                      },
                      filePath: 'user-removed-already-created-task',
                      subject: 'User Been Removed from a Task',
                    });
                  }
                }
              }
              if (event.entity) {
                const updatedTaskMembers = event.entity?.members;
                const oldTaskMembers = event.entity?.['oldTaskMembers'];
                if (Array.isArray(oldTaskMembers)) {
                  const removedMembers = oldTaskMembers.reduce((acc, member) => {
                    if (
                      !Array.isArray(updatedTaskMembers) ||
                      !updatedTaskMembers.some((eachMember) => member.id === eachMember.id)
                    ) {
                      acc.push(member);
                    }
                    return acc;
                  }, []);
                  if (removedMembers.length > 0) {
                    const taskMemberIds = removedMembers.map((obj) => obj.id);
                    const presentTaskMemberIds = updatedTaskMembers.map((obj) => obj.id);
                    const allTaskMemberNames = await this.getAllTaskMemberNames(taskMemberIds);
                    const title = 'User Removed from already created Task';
                    const body = `"${allTaskMemberNames}" has been removed as task member from "${taskName}" of ${clientName} by ${userName}`;

                    const uniqueUserIds = Array.from(
                      new Set([...orgAdminIds, ...taskMemberIds, ...presentTaskMemberIds]),
                    );
                    // insertINTOnotification(title, body, uniqueUserIds, orgId);
                    const key = 'USER_REMOVED_FROM_ALREADY_CREATED_TASK_PUSH';
                    insertINTONotificationUpdate(title, body, uniqueUserIds, orgId, key);
                    const organization = await Organization.findOne({ id: orgId });


                    const addressParts = [
                      organization.buildingNo || '',
                      organization.floorNumber || '',
                      organization.buildingName || '',
                      organization.street || '',
                      organization.location || '',
                      organization.city || '',
                      organization.district || '',
                      organization.state || ''
                    ].filter(part => part && part.trim() !== '');
                    const pincode = organization.pincode && organization.pincode.trim() !== '' ? ` - ${organization.pincode}` : '';

                    const address = addressParts.join(', ') + pincode;
                    let commaSeparatedNames = '';
                    if (event?.entity?.members) {
                      const fullNames = event?.entity?.members.map((member) => member.fullName);
                      commaSeparatedNames = fullNames.join(', ');
                    }

                    // const userListMails = await getAdminEmailsBasedOnOrganizationId(orgId);
                    for (let user of taskMemberIds) {
                      const taskUserDetails = await getUserDetails(user);
                      await sendnewMail({
                        id: taskUserDetails?.id,
                        key: 'USER_REMOVED_FROM_ALREADY_CREATED_TASK_MAIL',
                        email: taskUserDetails?.email,
                        data: {
                          taskId: taskNumber, // ok
                          taskName: taskName, //ok
                          clientName: clientName, // ok
                          adminName: taskUserDetails?.full_name, // ok
                          userName: userName,
                          serviceName: event?.entity?.name, // ok
                          dueDate: taskDueDateFormat, // ok
                          assignedUser: allTaskMemberNames, //ok
                          existingUsers: commaSeparatedNames, // ok
                          adress: address,
                          phoneNumber: organization?.mobileNumber,
                          mail: organization?.email,
                          legalName: organization?.tradeName || organization?.legalName
                        },
                        filePath: 'user-removed-already-created-task',
                        subject: "You've Been Removed from a Task",
                      });
                    }

                    // admins
                    for (let user of orgAdminIds) {
                      const taskUserDetails = await getUserDetails(user);
                      await sendnewMail({
                        id: taskUserDetails?.id,
                        key: 'USER_REMOVED_FROM_ALREADY_CREATED_TASK_MAIL',
                        email: taskUserDetails?.email,
                        data: {
                          taskId: taskNumber, // ok
                          taskName: taskName, //ok
                          clientName: clientName, // ok
                          adminName: taskUserDetails?.full_name, // ok
                          userName: userName,
                          serviceName: event?.entity?.name, // ok
                          dueDate: taskDueDateFormat, // ok
                          assignedUser: allTaskMemberNames, //ok
                          existingUsers: commaSeparatedNames, // ok
                          adress: address,
                          phoneNumber: organization?.mobileNumber,
                          mail: organization?.email,
                          legalName: organization?.tradeName || organization?.legalName
                        },
                        filePath: 'user-removed-already-created-task',
                        subject: 'User Been Removed from a Task',
                      });
                    }
                  }
                }
                if (Array.isArray(updatedTaskMembers) && Array.isArray(oldTaskMembers)) {
                  const oldListNumbers = oldTaskMembers.map((item) => item.id);
                  const addedMembers = [];
                  for (let i of updatedTaskMembers) {
                    if (oldListNumbers.includes(i.id)) {
                      // console.log("");
                    } else {
                      addedMembers.push(i);
                    }
                  }
                  if (addedMembers.length > 0) {
                    const taskMemberIds = addedMembers.map((obj) => obj.id);
                    const allTaskMemberNames = await this.getAllTaskMemberNames(taskMemberIds);
                    const presentTaskMemberIds = updatedTaskMembers.map((obj) => obj.id);

                    const title = 'User Added to already created Task';
                    const body = `<strong>${allTaskMemberNames}</strong> has been added as task member to "<strong>${taskName}</strong>" of <strong>${clientName}</strong> by <strong>${userName}</strong>`;
                    const uniqueUserIds = Array.from(
                      new Set([...orgAdminIds, ...taskMemberIds, ...presentTaskMemberIds]),
                    );
                    // insertINTOnotification(title, body, uniqueUserIds, orgId);
                    const key = 'USER_ADDED_TO_ALREADY_CREATED_TASK_PUSH';
                    insertINTONotificationUpdate(title, body, uniqueUserIds, orgId, key);
                    const organization = await Organization.findOne({ id: orgId });


                    const addressParts = [
                      organization.buildingNo || '',
                      organization.floorNumber || '',
                      organization.buildingName || '',
                      organization.street || '',
                      organization.location || '',
                      organization.city || '',
                      organization.district || '',
                      organization.state || ''
                    ].filter(part => part && part.trim() !== '');
                    const pincode = organization.pincode && organization.pincode.trim() !== '' ? ` - ${organization.pincode}` : '';

                    const address = addressParts.join(', ') + pincode;
                    let commaSeparatedNames = '';
                    if (event?.entity?.members) {
                      const fullNames = event?.entity?.members.map((member) => member.fullName);
                      commaSeparatedNames = fullNames.join(', ');
                    }

                    // const userListMails = await getAdminEmailsBasedOnOrganizationId(orgId);
                    for (let user of taskMemberIds) {
                      const taskUserDetails = await getUserDetails(user);
                      await sendnewMail({
                        id: taskUserDetails?.id,
                        key: 'USER_ADDED_TO_ALREADY_CREATED_TASK_MAIL',
                        email: taskUserDetails?.email,
                        data: {
                          taskId: taskNumber, // ok
                          taskName: taskName, //ok
                          clientName: clientName, // ok
                          adminName: taskUserDetails?.full_name, // ok
                          userName: userName,
                          serviceName: event?.entity?.name, // ok
                          dueDate: taskDueDateFormat, // ok
                          assignedUser: allTaskMemberNames, //ok
                          existingUsers: commaSeparatedNames, // ok
                          userId: userid,
                          adress: address,
                          phoneNumber: organization?.mobileNumber,
                          mail: organization?.email,
                          legalName: organization?.tradeName || organization?.legalName
                        },
                        filePath: 'user-added-to-already-created-task',
                        subject: 'User Added To Already Created Task',
                      });
                    }

                    for (let user of orgAdminIds) {
                      const taskUserDetails = await getUserDetails(user);
                      await sendnewMail({
                        id: taskUserDetails?.id,
                        key: 'USER_ADDED_TO_ALREADY_CREATED_TASK_MAIL',
                        email: taskUserDetails?.email,
                        data: {
                          taskId: taskNumber, // ok
                          taskName: taskName, //ok
                          clientName: clientName, // ok
                          adminName: taskUserDetails?.full_name, // ok
                          userName: userName,
                          serviceName: event?.entity?.name, // ok
                          dueDate: taskDueDateFormat, // ok
                          assignedUser: allTaskMemberNames, //ok
                          existingUsers: commaSeparatedNames, // ok
                          userId: userid,
                          adress: address,
                          phoneNumber: organization?.mobileNumber,
                          mail: organization?.email,
                          legalName: organization?.tradeName || organization?.legalName
                        },
                        filePath: 'user-added-to-already-created-task-admin',
                        subject: 'User Added To Already Created Task',
                      });
                      //  whatsapp
                      try {
                        if (taskUserDetails !== undefined) {
                          for (let user of event?.entity?.members) {
                            const sessionValidation = await ViderWhatsappSessions.findOne({
                              where: { userId: user?.id, status: 'ACTIVE' },
                            });
                            if (sessionValidation) {
                              const userDetails = await getUserDetails(user?.id);
                              const { mobile_number: userPhoneNumber, id } = userDetails;
                              const key = 'USER_ADDED_TO_ALREADY_CREATED_TASK_WHATSAPP';
                              const whatsappMessageBody = `
    
    ${allTaskMemberNames} has been added to ${taskName} 
    
    Task Details :
     Task Id   : ${taskId}
     Task Name : ${taskName}
     Task Due Date : ${event?.entity?.dueDate}
     Client Name : ${clientName}
    
    We hope this helps!
              `;
                              await sendWhatsAppTextMessage(
                                `91${userPhoneNumber}`,
                                whatsappMessageBody,
                                user?.id,
                                title,
                                id,
                                key,
                              );
                            }
                          }
                        }
                      } catch (error) {
                        console.error('Error sending User WhatsApp notification:', error);
                      }
                    }
                  }
                }
              }
            }
          }
          if (clientGroupId) {
            const clientNameQuery = `SELECT display_name FROM client_group WHERE id=${clientGroupId}`;
            const getClient = await this.entityManager.query(clientNameQuery);
            const clientName = getClient[0]?.display_name;
            const orgAdminIds = await getAdminIDsBasedOnOrganizationId(orgId);
            const uniqueUserIds = Array.from(new Set([...orgAdminIds, ...taskMemberIds]));
            const taskDueDateFormat = moment(event?.entity?.dueDate).format('DD-MM-YYYY');

            if (event.entity) {
              const updatedTaskMembers = event.entity?.members;
              const oldTaskMembers = event.entity?.['oldTaskMembers'];
              if (Array.isArray(oldTaskMembers)) {
                const removedMembers = oldTaskMembers.reduce((acc, member) => {
                  if (
                    !Array.isArray(updatedTaskMembers) ||
                    !updatedTaskMembers.some((eachMember) => member.id === eachMember.id)
                  ) {
                    acc.push(member);
                  }
                  return acc;
                }, []);
                if (removedMembers.length > 0) {
                  const taskMemberIds = removedMembers.map((obj) => obj.id);
                  const presentTaskMemberIds = updatedTaskMembers.map((obj) => obj.id);
                  const allTaskMemberNames = await this.getAllTaskMemberNames(taskMemberIds);
                  const title = 'User Removed from already created Task';
                  const body = `"${allTaskMemberNames}" has been removed as task member from "${taskName}" of ${clientName} by ${userName}`;

                  const uniqueUserIds = Array.from(
                    new Set([...orgAdminIds, ...taskMemberIds, ...presentTaskMemberIds]),
                  );
                  // insertINTOnotification(title, body, uniqueUserIds, orgId);
                  const key = 'USER_REMOVED_FROM_ALREADY_CREATED_TASK_PUSH';
                  insertINTONotificationUpdate(title, body, uniqueUserIds, orgId, key);
                }
              }
              if (event.entity) {
                const updatedTaskMembers = event.entity?.members;
                const oldTaskMembers = event.entity?.['oldTaskMembers'];
                if (Array.isArray(oldTaskMembers)) {
                  const removedMembers = oldTaskMembers.reduce((acc, member) => {
                    if (
                      !Array.isArray(updatedTaskMembers) ||
                      !updatedTaskMembers.some((eachMember) => member.id === eachMember.id)
                    ) {
                      acc.push(member);
                    }
                    return acc;
                  }, []);
                  if (removedMembers.length > 0) {
                    const taskMemberIds = removedMembers.map((obj) => obj.id);
                    const presentTaskMemberIds = updatedTaskMembers.map((obj) => obj.id);
                    const allTaskMemberNames = await this.getAllTaskMemberNames(taskMemberIds);
                    const title = 'User Removed from already created Task';
                    const body = `"${allTaskMemberNames}" has been removed as task member from "${taskName}" of ${clientName} by ${userName}`;

                    const uniqueUserIds = Array.from(
                      new Set([...orgAdminIds, ...taskMemberIds, ...presentTaskMemberIds]),
                    );
                    // insertINTOnotification(title, body, uniqueUserIds, orgId);
                    const key = 'USER_REMOVED_FROM_ALREADY_CREATED_TASK_PUSH';
                    insertINTONotificationUpdate(title, body, uniqueUserIds, orgId, key, taskId, clientId);
                  }
                }
                if (Array.isArray(updatedTaskMembers) && Array.isArray(oldTaskMembers)) {
                  const oldListNumbers = oldTaskMembers.map((item) => item.id);
                  const addedMembers = [];
                  for (let i of updatedTaskMembers) {
                    if (oldListNumbers.includes(i.id)) {
                      // console.log("");
                    } else {
                      addedMembers.push(i);
                    }
                  }
                  if (addedMembers.length > 0) {
                    const taskMemberIds = addedMembers.map((obj) => obj.id);
                    const allTaskMemberNames = await this.getAllTaskMemberNames(taskMemberIds);
                    const presentTaskMemberIds = updatedTaskMembers.map((obj) => obj.id);

                    const title = 'User Added to already created Task';
                    const body = `<strong>${allTaskMemberNames}</strong> has been added as task member to "<strong>${taskName}</strong>" of <strong>${clientName}</strong> by <strong>${userName}</strong>`;
                    const uniqueUserIds = Array.from(
                      new Set([...orgAdminIds, ...taskMemberIds, ...presentTaskMemberIds]),
                    );
                    // insertINTOnotification(title, body, uniqueUserIds, orgId);
                    const key = 'USER_ADDED_TO_ALREADY_CREATED_TASK_PUSH';
                    insertINTONotificationUpdate(title, body, uniqueUserIds, orgId, key, taskId, clientId);
                  }
                }
              }
            }
          }
        }
      }
    }
  }

  // async afterInsert(event: InsertEvent<Task>) {
  //   if (event.entity && event.entity.parentTask === undefined) {
  //     const { id: taskId, name: taskName, dueDate } = event.entity;
  //     const { client, clientGroup } = event.entity;
  //     if (client) {
  //       const { displayName: clientName } = client;
  //       const clientNumber = client?.clientId;
  //       const { members } = event.entity;
  //       const userIds: User[] = members.map((user: any) => user.id);
  //       const { user } = event.entity;
  //       if (user) {
  //         const { fullName: userName } = user;
  //         const taskMembersName = members.map((user) => user?.fullName);
  //         const names = taskMembersName.join(',');
  //         const { organization } = event.entity;
  //         const { id: orgId } = organization;
  //         const adminIds = await getAdminIds(orgId);

  //         const { taskLeader } = event.entity;

  //         // Concatenate user IDs and admin IDs
  //         const concatenatedArray: any = userIds.concat(adminIds);

  //         // Include Task Leaders if defined and not already in the list
  //         if (taskLeader && Array.isArray(taskLeader)) {
  //           taskLeader.forEach(leader => {
  //             if (!concatenatedArray.includes(leader.id)) {
  //               concatenatedArray.push(leader.id);
  //             }
  //           });
  //         }
  //         const expiryDateFormatted = moment(dueDate).format('DD-MM-YYYY');
  //         const usersList = [];
  //         concatenatedArray.forEach(function (element) {
  //           if (!usersList.includes(element)) {
  //             usersList.push(element);
  //           }
  //         });
  //         const adminBody = `<strong>${userName}</strong> have created "<strong>${taskName}</strong>" for <strong>${clientName}</strong> and assigned to "<strong>${names}</strong>.`;
  //         // insertINTOnotification(title, adminBody, usersList, orgId);
  //         const title = 'Task Created';
  //         const body = `<strong>${taskName}</strong>" for <strong>${clientName}</strong> has been created and assigned to <strong>${names}</strong>.`;
  //         const key = 'TASK_CREATED_PUSH';

  //         const { service } = event.entity;
  //         if (service && event.entity?.recurringStatus !== TaskRecurringStatus.PENDING) {
  //           console.log({ usersList })
  //           insertINTONotificationUpdate(title, body, usersList, orgId, key, taskId, clientNumber);
  //           await notify.taskCreatedPush({ taskName, clientName, names, usersList })


  //           const addressParts = [
  //             organization.buildingNo || '',
  //             organization.floorNumber || '',
  //             organization.buildingName || '',
  //             organization.street || '',
  //             organization.location || '',
  //             organization.city || '',
  //             organization.district || '',
  //             organization.state || ''
  //           ].filter(part => part && part.trim() !== '');
  //           const pincode = organization.pincode && organization.pincode.trim() !== '' ? ` - ${organization.pincode}` : '';

  //           const address = addressParts.join(', ') + pincode;
  //           const { name: serviceName } = service;
  //           const taskNumber = event?.entity?.taskNumber;
  //           for (let user of usersList) {
  //             const userDetails = await getUserDetails(user);
  //             await sendnewMail({
  //               id: userDetails?.id,
  //               key: 'TASK_CREATION_MAIL',
  //               email: userDetails?.email,
  //               data: {
  //                 taskId: taskNumber,
  //                 serviceName: serviceName,
  //                 clientName: clientName,
  //                 dueDate: expiryDateFormatted,
  //                 adminName: userDetails?.full_name,
  //                 userName: userName,
  //                 taskMembers: names,
  //                 userId: event.entity['userId'],
  //                 adress: address,
  //                 phoneNumber: organization?.mobileNumber,
  //                 mail: organization?.email,
  //                 legalName: organization?.tradeName || organization?.legalName
  //               },
  //               filePath: 'task-creation',
  //               subject: 'New Task Created',
  //             });
  //           }

  //           // Whatsapp Notification
  //           try {
  //             if (usersList) {
  //               const formattedDate = new Date(dueDate).toISOString().split('T')[0];
  //               for (let userId of usersList) {
  //                 const sessionValidation = await ViderWhatsappSessions.findOne({
  //                   where: { userId: userId, status: 'ACTIVE' },
  //                 });

  //                 if (sessionValidation) {
  //                   const adminUserDetails = await getUserDetails(userId);
  //                   const {
  //                     full_name: userFullName,
  //                     mobile_number: userPhoneNumber,
  //                     country_code: countryCode,
  //                   } = adminUserDetails;
  //                   const key = 'TASK_CREATED_WHATSAPP';
  //                   const whatsappMessageBody = `
  //   Hi ${userFullName}
  //   A new Task has been created in ATOM:

  //   Task name: ${taskName}
  //   Due date: ${formattedDate}
  //   Assigned by: ${userName}
  //   Client Name: ${clientName}
  //   Members: ${names}

  //   We hope this helps!
  //   `;

  //                   await sendWhatsAppTextMessage(
  //                     // `91${userPhoneNumber}`,
  //                     fullMobileNumberWithCountry(userPhoneNumber, countryCode),
  //                     whatsappMessageBody,
  //                     orgId,
  //                     title,
  //                     userId,
  //                     key,
  //                   );
  //                 }
  //               }
  //             }
  //           } catch (error) {
  //             console.error('Error sending Task WhatsApp notification:', error);
  //           }
  //         } else if (
  //           !service &&
  //           expiryDateFormatted &&
  //           usersList &&
  //           event.entity?.recurringStatus !== TaskRecurringStatus.PENDING
  //         ) {
  //           insertINTONotificationUpdate(title, body, usersList, orgId, key);
  //           const organization = await Organization.findOne({ id: orgId });


  //           const addressParts = [
  //             organization.buildingNo || '',
  //             organization.floorNumber || '',
  //             organization.buildingName || '',
  //             organization.street || '',
  //             organization.location || '',
  //             organization.city || '',
  //             organization.district || '',
  //             organization.state || ''
  //           ].filter(part => part && part.trim() !== '');
  //           const pincode = organization.pincode && organization.pincode.trim() !== '' ? ` - ${organization.pincode}` : '';

  //           const address = addressParts.join(', ') + pincode;
  //           const taskNumber = event?.entity?.taskNumber;
  //           for (let user of usersList) {
  //             const taskUserDetails = await getUserDetails(user);
  //             await sendnewMail({
  //               id: taskUserDetails?.id,
  //               key: 'TASK_CREATION_MAIL',
  //               email: taskUserDetails?.email,
  //               data: {
  //                 taskId: taskNumber,
  //                 serviceName: '',
  //                 clientName: clientName,
  //                 dueDate: expiryDateFormatted,
  //                 adminName: taskUserDetails?.full_name,
  //                 userName: userName,
  //                 taskMembers: names,
  //                 userId: event.entity['userId'],
  //                 adress: address,
  //                 phoneNumber: organization?.mobileNumber,
  //                 mail: organization?.email,
  //                 legalName: organization?.tradeName || organization?.legalName
  //               },
  //               filePath: 'task-creation',
  //               subject: 'New Task Created',
  //             });
  //           }

  //           // Whatsapp Notification
  //           try {
  //             if (usersList) {
  //               const formattedDate = new Date(dueDate).toISOString().split('T')[0];
  //               for (let userId of usersList) {
  //                 const sessionValidation = await ViderWhatsappSessions.findOne({
  //                   where: { userId: userId, status: 'ACTIVE' },
  //                 });

  //                 if (sessionValidation) {
  //                   const adminUserDetails = await getUserDetails(userId);
  //                   const {
  //                     full_name: userFullName,
  //                     mobile_number: userPhoneNumber,
  //                     country_code: countryCode,
  //                   } = adminUserDetails;
  //                   const key = 'TASK_CREATED_WHATSAPP';
  //                   const whatsappMessageBody = `
  //   Hi ${userFullName}
  //   A new Task has been created in ATOM:

  //   Task name: ${taskName}
  //   Due date: ${formattedDate}
  //   Assigned by: ${userName}
  //   Client Name: ${clientName}
  //   Members: ${names}

  //   We hope this helps!
  //   `;

  //                   await sendWhatsAppTextMessage(
  //                     // `91${userPhoneNumber}`,
  //                     fullMobileNumberWithCountry(userPhoneNumber, countryCode),
  //                     whatsappMessageBody,
  //                     orgId,
  //                     title,
  //                     userId,
  //                     key,
  //                   );
  //                 }
  //               }
  //             }
  //           } catch (error) {
  //             console.error('Error sending Task WhatsApp notification:', error);
  //           }
  //         }
  //       }
  //     }
  //     if (clientGroup) {
  //       const { displayName: clientName, id } = clientGroup;
  //       const { members } = event.entity;
  //       const userIds: User[] = members.map((user: any) => user.id);
  //       const { user } = event.entity;
  //       if (user) {
  //         const { fullName: userName } = user;
  //         const taskMembersName = members.map((user) => user?.fullName);
  //         const names = taskMembersName.join(',');
  //         const { organization } = event.entity;
  //         const { id: orgId } = organization;
  //         const adminIds = await getAdminIds(orgId);
  //         const concatenatedArray = userIds.concat(adminIds);
  //         const expiryDateFormatted = moment(dueDate).format('DD-MM-YYYY');
  //         const usersList = [];
  //         concatenatedArray.forEach(function (element) {
  //           if (!usersList.includes(element)) {
  //             usersList.push(element);
  //           }
  //         });
  //         const adminBody = `<strong>${userName}</strong> have created "<strong>${taskName}</strong>" for <strong>${clientName}</strong> and assigned to "<strong>${names}</strong>.`;
  //         // insertINTOnotification(title, adminBody, usersList, orgId);
  //         const title = 'Task Created';
  //         const body = `<strong>${taskName}</strong>" for <strong>${clientName}</strong> has been created and assigned to <strong>${names}</strong>.`;
  //         const key = 'TASK_CREATED_PUSH';

  //         const { service } = event.entity;
  //         if (service && event.entity?.recurringStatus !== TaskRecurringStatus.PENDING) {
  //           insertINTONotificationUpdate(title, body, usersList, orgId, key);

  //           // Whatsapp Notification
  //           try {
  //             if (usersList) {
  //               const formattedDate = new Date(dueDate).toISOString().split('T')[0];
  //               for (let userId of usersList) {
  //                 const sessionValidation = await ViderWhatsappSessions.findOne({
  //                   where: { userId: userId, status: 'ACTIVE' },
  //                 });

  //                 if (sessionValidation) {
  //                   const adminUserDetails = await getUserDetails(userId);
  //                   const {
  //                     full_name: userFullName,
  //                     mobile_number: userPhoneNumber,
  //                     country_code: countryCode,
  //                   } = adminUserDetails;
  //                   const key = 'TASK_CREATED_WHATSAPP';
  //                   const whatsappMessageBody = `
  //   Hi ${userFullName}
  //   A new Task has been created in ATOM:

  //   Task name: ${taskName}
  //   Due date: ${formattedDate}
  //   Assigned by: ${userName}
  //   Client Group Name: ${clientName}
  //   Members: ${names}

  //   We hope this helps!
  //   `;

  //                   await sendWhatsAppTextMessage(
  //                     // `91${userPhoneNumber}`,
  //                     fullMobileNumberWithCountry(userPhoneNumber, countryCode),
  //                     whatsappMessageBody,
  //                     orgId,
  //                     title,
  //                     userId,
  //                     key,
  //                   );
  //                 }
  //               }
  //             }
  //           } catch (error) {
  //             console.error('Error sending Task WhatsApp notification:', error);
  //           }

  //         } else if (
  //           !service &&
  //           expiryDateFormatted &&
  //           usersList &&
  //           event.entity?.recurringStatus !== TaskRecurringStatus.PENDING
  //         ) {
  //           insertINTONotificationUpdate(title, body, usersList, orgId, key);

  //           // Whatsapp Notification
  //           try {
  //             if (usersList) {
  //               const formattedDate = new Date(dueDate).toISOString().split('T')[0];
  //               for (let userId of usersList) {
  //                 const sessionValidation = await ViderWhatsappSessions.findOne({
  //                   where: { userId: userId, status: 'ACTIVE' },
  //                 });

  //                 if (sessionValidation) {
  //                   const adminUserDetails = await getUserDetails(userId);
  //                   const {
  //                     full_name: userFullName,
  //                     mobile_number: userPhoneNumber,
  //                     country_code: countryCode,
  //                   } = adminUserDetails;
  //                   const key = 'TASK_CREATED_WHATSAPP';
  //                   const whatsappMessageBody = `
  //   Hi ${userFullName}
  //   A new Task has been created in ATOM:

  //   Task name: ${taskName}
  //   Due date: ${formattedDate}
  //   Assigned by: ${userName}
  //   Client Group Name: ${clientName}
  //   Members: ${names}

  //   We hope this helps!
  //   `;

  //                   await sendWhatsAppTextMessage(
  //                     // `91${userPhoneNumber}`,
  //                     fullMobileNumberWithCountry(userPhoneNumber, countryCode),
  //                     whatsappMessageBody,
  //                     orgId,
  //                     title,
  //                     userId,
  //                     key,
  //                   );
  //                 }
  //               }
  //             }
  //           } catch (error) {
  //             console.error('Error sending Task WhatsApp notification:', error);
  //           }
  //         }
  //       }
  //     }
  //     //   const run = async () => {
  //     //     const kafka = new Kafka({
  //     //         clientId: 'my-app',
  //     //         brokers: [
  //     //    'b-2-public.vidermskcluster.b3wq8s.c2.kafka.ap-south-1.amazonaws.com:9198', 
  //     //    'b-1-public.vidermskcluster.b3wq8s.c2.kafka.ap-south-1.amazonaws.com:9198'
  //     //  ],
  //     //         ssl: true,
  //     //         sasl: {
  //     //             mechanism: 'oauthbearer',
  //     //             oauthBearerProvider: () => oauthBearerTokenProvider('ap-south-1')
  //     //         }
  //     //     })
  //     //  const admin = kafka.admin();
  //     //  try {
  //     //      console.log(`Creating topic: task-create-test`);
  //     //      await admin.createTopics({
  //     //        topics: [
  //     //          {
  //     //            topic: 'task-create-test',
  //     //            numPartitions: 3, // Specify the number of partitions for the topic
  //     //            replicationFactor: 2, // Specify the replication factor (must be <= number of brokers)
  //     //          },
  //     //        ],
  //     //      });
  //     //      console.log(`Topic task-create-test created successfully.`);
  //     //    } catch (error) {
  //     //      console.error(`Error creating topic, error`);
  //     //    } finally {
  //     //      await admin.disconnect();
  //     //    }

  //     //     const producer = kafka.producer()
  //     //    //  const consumer = kafka.consumer({ groupId: 'new-group' })

  //     //     // Producing
  //     //     await producer.connect()
  //     //     await producer.send({
  //     //         topic: 'task-create-test',
  //     //         messages: [
  //     //           {key:'Task Create Test',value: JSON.stringify({entityDetails: event.entity}) }
  //     //         ]

  //     //     })
  //     //      console.log(`Posted messages meta pending test created successfully.`);

  //     //  }

  //     //  run().catch(console.error)
  //   }
  // }
  async afterInsert(event: InsertEvent<Task>) {
    const task = event.entity;
    if (!task || task.parentTask !== undefined) return;

    let {
      id: taskId,
      name: taskName,
      dueDate,
      client,
      clientGroup,
      members,
      user,
      organization,
      taskLeader,
      service,
      recurringStatus,
      taskNumber
    } = task;

    const orgId = organization?.id;
    const clientName = client?.displayName || clientGroup?.displayName || '';
    const clientNumber = client?.clientId;
    user = null
    const userName = user?.fullName || 'System';
    const taskMembersName = members.map((u) => u?.fullName).filter(Boolean);
    const names = taskMembersName.join(', ');

    const userIds = members.map((u: any) => u.id);
    const adminIds = await getAdminIds(orgId);
    const allUserIds: any[] = Array.from(new Set([
      ...userIds,
      ...adminIds,
      ...(taskLeader?.map((u) => u.id) || [])
    ]));

    const expiryDateFormatted = moment(dueDate).format('DD-MM-YYYY');
    const title = 'Task Created';
    const key = 'TASK_CREATED_PUSH';
    const body = `<strong>${taskName}</strong> for <strong>${clientName}</strong> has been created and assigned to <strong>${names || 'no members'}</strong>.`;

    const addressParts = [
      organization.buildingNo,
      organization.floorNumber,
      organization.buildingName,
      organization.street,
      organization.location,
      organization.city,
      organization.district,
      organization.state,
    ].filter(Boolean);
    const pincode = organization.pincode ? ` - ${organization.pincode}` : '';
    const address = addressParts.join(', ') + pincode;

    insertINTONotificationUpdate(title, body, allUserIds, orgId, key, taskId, clientNumber);

    if (service && recurringStatus !== TaskRecurringStatus.PENDING) {
      await notify.taskCreatedPush({ taskName, clientName, names, usersList: allUserIds });
    }

    for (const userId of allUserIds) {
      const userDetails = await getUserDetails(userId);
      if (userDetails?.email) {
        await sendnewMail({
          id: userDetails.id,
          key: 'TASK_CREATION_MAIL',
          email: userDetails.email,
          data: {
            taskId: taskNumber,
            serviceName: service?.name || '',
            clientName,
            dueDate: expiryDateFormatted,
            adminName: userDetails?.full_name,
            userName,
            taskMembers: names,
            userId: task['userId'] || '',
            adress: address,
            phoneNumber: organization?.mobileNumber,
            mail: organization?.email,
            legalName: organization?.tradeName || organization?.legalName,
          },
          filePath: 'task-creation',
          subject: 'New Task Created',
        });
      }

      try {
        const session = await ViderWhatsappSessions.findOne({ where: { userId, status: 'ACTIVE' } });
        if (session) {
          const {
            full_name: fullName,
            mobile_number: phone,
            country_code: cc,
          } = userDetails;
          const formattedDate = new Date(dueDate).toISOString().split('T')[0];
          const msg = `
Hi ${fullName},
A new Task has been created in ATOM:

Task name: ${taskName}
Due date: ${formattedDate}
Assigned by: ${userName}
${client ? `Client Name: ${clientName}` : `Client Group: ${clientName}`}
Members: ${names || 'No members'}

We hope this helps!
`;

          await sendWhatsAppTextMessage(
            fullMobileNumberWithCountry(phone, cc),
            msg,
            orgId,
            title,
            userId,
            'TASK_CREATED_WHATSAPP'
          );
        }
      } catch (error) {
        console.error('Error sending WhatsApp notification:', error);
      }
    }
  }

}
