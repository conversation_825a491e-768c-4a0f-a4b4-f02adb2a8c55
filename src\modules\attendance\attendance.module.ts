import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

import { AttendanceController } from './attendance.controller';
import Attendance from './attendance.entity';
import { AttendanceService } from './attendance.service';
import { AttendanceSubscriber } from 'src/event-subscribers/attendance.subscriber';
import LeaveApproval from './leave-approval.entity';
import ApprovalService from './approval.service';
import { OrganizationPreferencesService } from '../organization-preferences/organization-preferences.service';

@Module({
  imports: [TypeOrmModule.forFeature([Attendance, LeaveApproval])],
  controllers: [AttendanceController],
  providers: [
    AttendanceService,
    AttendanceSubscriber,
    ApprovalService,
    OrganizationPreferencesService,
  ],
})
export class AttendanceModule {}
