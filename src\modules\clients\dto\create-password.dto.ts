import { IsNotEmpty, IsOptional } from 'class-validator';

export class CreateClientPasswordDto {
  @IsNotEmpty()
  website: string;

  @IsOptional()
  client: number;

  @IsOptional()
  clientGroup: number;

  @IsNotEmpty()
  websiteUrl: string;

  @IsNotEmpty()
  loginId: string;

  @IsNotEmpty()
  password: string;

  @IsOptional()
  atomProAdd: boolean;

  @IsOptional()
  tracesTan: string;
}

export class UpdateClientPasswordDto {
  @IsNotEmpty()
  website: string;

  @IsNotEmpty()
  websiteUrl: string;

  @IsNotEmpty()
  loginId: string;

  @IsNotEmpty()
  password: string;

  @IsOptional()
  atomProAdd: boolean;

  @IsOptional()
  isExistingAtomPro: any;

  @IsOptional()
  tracesTan: string;
}
