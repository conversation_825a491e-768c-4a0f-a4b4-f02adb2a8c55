import { Injectable, CanActivate, ExecutionContext, UnauthorizedException } from '@nestjs/common';

@Injectable()
export class CronAuthGuard implements CanActivate {
  private readonly validApiKey: string;

  constructor() {
    this.validApiKey = process.env.CRON_X_API_KEY;
  }

  canActivate(context: ExecutionContext): boolean {
    const request = context.switchToHttp().getRequest();
    const apiKey = request.headers['x-api-key'];

    if (!apiKey || apiKey !== this.validApiKey) {
      throw new UnauthorizedException('Invalid or missing CRON X-API-key');
    }

    return true;
  }
}