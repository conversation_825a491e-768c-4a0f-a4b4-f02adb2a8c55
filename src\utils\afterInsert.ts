
import { getManager } from 'typeorm/globals';

export async function taskUpdated(event: any) {
  // console.log('CommonSubscriber::afterInsert::', event.entity);

  // const entity = event.entity;

  // if (event?.metadata?.tableName == 'invited_user') {

  //   let user = await getOrgUserbyOrgID(entity.organization.id);

  // let notification = insertINTOnotification(event, user);
  // console.log('Success', notification);


}




// if (event.entity.action == 'CLIENT_UPDATED') {
//   let activity = new Activity();
//   let data = Activity
//     .createQueryBuilder()
//     .update(activity)
//     .set({
//       remarkType: "123"
//     })
//     .where("actorId = :id", { id: 95 })
//     .execute()

//   // let data = event.entity.createQueryBuilder().update(Activity).set(activity).where("actorId = :id", { id: 456 }).execute();

//   return data;

// }

async function getOrgUserbyOrgID(orgid: number) {
  let sql = `select u.id, u.full_name from user u 
    Left join role r on r.id = u.role_id 
    where u.organization_id = '${orgid}' and r.name = 'Admin'`;

  let orgUser = await getManager().query(sql);

  return orgUser;
}


async function insertINTOnotification(event: any, users: any) {
  let sql = `INSERT INTO notification (user_id, title, body) 
    VALUES ('95', 'User Invited', '{user name} have invited {invitee name}')`;

  const entityManager = getManager();
  let notification = await entityManager.query(sql);


  // let notifications = [];
  // for (let user of users) {
  //   let newNotification = new Notification();
  //   newNotification.title = 'User Invited';
  //   newNotification.body = `${user?.full_name} have sai invited 11:20 invited ${event?.entity?.fullName}`;
  //   newNotification.user = user.id;
  //   notifications.push(newNotification);
  // }
  // await Notification.save(notifications);
  // console.log('Success notification:::', notification);
}

// }
