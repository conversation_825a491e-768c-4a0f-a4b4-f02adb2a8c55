import { Injectable } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { Event_Actions } from 'src/event-listeners/actions';
import { User } from 'src/modules/users/entities/user.entity';
import AddCommentDto from '../dto/add-comment.dto';
import TaskComment from '../entity/comment.entity';
import Task from '../entity/task.entity';
import Activity, { ActivityType } from 'src/modules/activity/activity.entity';

@Injectable()
export class CommentsServie {
  constructor(private eventEmitter: EventEmitter2) {}

  async addComment(userId: number, taskId: number, data: AddCommentDto) {
    let task = await Task.findOne({ where: { id: taskId } });
    let user = await User.findOne({ where: { id: userId } });
    let taskComment = new TaskComment();
    taskComment.text = data.text;
    taskComment.task = task;
    taskComment.user = user;

    if (data.parentId) {
      taskComment.parentId = data.parentId;
    }

    await taskComment.save();

    let taskactivity = new Activity();
      taskactivity.action = taskComment.parentId ? Event_Actions.COMMENT_REPLIED :Event_Actions.COMMENT_ADDED;
      taskactivity.actorId = userId;
      taskactivity.type = ActivityType.TASK;
      taskactivity.typeId = taskId;
      taskactivity.remarks = taskComment.parentId ? `Comment Replied by ${user.fullName}` : `Comment Added by ${user.fullName}`;
    await taskactivity.save();

    this.eventEmitter.emit(Event_Actions.COMMENT_ADDED, {
      userName: user.fullName,
      taskId: task.id,
    });

    return taskComment;
  }

  async findComments(id: number) {
    let data = await TaskComment.find({
      where: {
        task: {
          id,
        },
      },
      relations: ['user', 'task'],
    });

    let comments = data.filter((comment) => comment.parentId === null);
    let replies = data.filter((comment) => comment.parentId !== null);
    let result = comments.map((comment) => {
      let filtered = replies.filter((reply) => reply.parentId === comment.id);
      return {
        ...comment,
        replies: filtered ? filtered : [],
      };
    });

    return result;
  }
}
