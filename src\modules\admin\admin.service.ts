import {
  BadRequestException,
  ConflictException,
  Injectable,
  InternalServerErrorException,
  NotFoundException,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import Category from 'src/modules/categories/categories.entity';
import Event from 'src/modules/events/event.entity';
import CreateFormDto from 'src/modules/forms/dto/create-form.dto';
import CreateValidationDto from 'src/modules/forms/dto/create-validation.dto';
import { Form, FormDocument } from 'src/modules/forms/schemas/form.schema';
import { Validation, ValidationDocument } from 'src/modules/forms/schemas/validation.schema';
import Label from 'src/modules/labels/label.entity';
import { Role } from 'src/modules/roles/entities/role.entity';
import CreateServiceDto from 'src/modules/services/dto/create-service.dto';
import { Checklist } from 'src/modules/services/entities/checklist.entity';
import Milestone from 'src/modules/services/entities/milestone.entity';
import { Service } from 'src/modules/services/entities/service.entity';
import StageOfWork from 'src/modules/services/entities/stage-of-work.entity';
import { SubTask } from 'src/modules/services/entities/subtask.entity';
import Task from 'src/modules/tasks/entity/task.entity';
import { Organization } from 'src/modules/organization/entities/organization.entity';
import { User, UserStatus, UserType } from 'src/modules/users/entities/user.entity';
import * as moment from 'moment';
import OrganizationPreferences from '../organization-preferences/entity/organization-preferences.entity';

import {
  Brackets,
  Check,
  createQueryBuilder,
  EntityManager,
  getConnection,
  getManager,
  getRepository,
  In,
  IsNull,
  MoreThan,
  Not,
  Equal,
} from 'typeorm';
import CreateCategoryDto from '../categories/dto/create-category.dto';
import { UpdateOrganizationProfileDto } from '../organization/dto/update-organization-profile.dto';
import State from '../states/state.entity';
import { AtomSuperAdminActivity } from '../atom-super-admin-activity/atom-super-admin-activity.entity';
import { Cron, CronExpression } from '@nestjs/schedule';
import QtmTemplate from './entities/quantumTemplateFeed.entity';
import QtmTemplateCat from './entities/quantumTemplateCategories';
import QtmTemplateSubcat from './entities/quantumTemplateSubCategories';
import CreateQuantumTemplateDto from './dto/createQuantumTemplate.dto';
import CreateAtomSuperAdminActivityDto from '../atom-super-admin-activity/dto/atom-super-admin-activity.dto';
import QtmCategories from './entities/quantumCategories';
import CreateQuantumCategoryDto from './dto/createQuantumCategories.dto';
import QtmLabel from './entities/documentLabels';
import { CreateDocumentLabelDto } from './dto/createDocumentLabelDto';
import DocumentLabels from './entities/documentLabels';
import { QtmSuperAdminActivity } from '../qtm-super-admin-activity/qtm-super-admin-activity.entity';
import CreateQtmSuperAdminActivityDto from '../qtm-super-admin-activity/dto/qtm-super-admin-activity.dto';
import QtmActivity from './entities/qtmActivity.entity';
import ChecklistItem from '../services/entities/checklist-item.entity';
import AtomToQtmrequests from '../quantum/entity/atm-qtm-requests.entity';
import AtomProLimitRequests from '../automation/entities/atomProLimitRequests.entity';
import { userType } from '../udin-task/udin-task.entity';
import axios from 'axios';
import AutomationServers from '../automation/entities/automationServers.entity';
import AutomationMachines from '../automation/entities/automation_machines.entity';
import Posters from '../poster/posters.entity';
import Storage, { StorageSystem, StorageType } from '../storage/storage.entity';
import { AwsService } from '../storage/upload.service';
import PosterEvents from '../poster/poster-events.entity';
import PosterEventTypes from '../poster/poster-event-types.entity';
import { PosterEventTypesDto } from './dto/poster-event-types.dto';
import { PosterEventsDto } from './dto/poster-events.dto';
import { Permission } from '../roles/entities/permission.entity';
import BroadcastEmailTemplates from '../communication/entity/broadcast-email-templates-entity';

@Injectable()
export class AdminService {
  constructor(
    @InjectModel(Form.name) private formModel: Model<FormDocument>,
    @InjectModel(Validation.name)
    private validationModel: Model<ValidationDocument>,
    private uploadService: AwsService,
  ) {}

  async getOrganizations(query) {
    const qb = createQueryBuilder(Organization, 'organization')
      .leftJoinAndSelect('organization.organizationPreferences', 'organizationPreferences')
      .distinct(true); // safe default in case of multiple joins

    if (query?.search) {
      const search = query.search.trim().toLowerCase();

      qb.andWhere(
        new Brackets((qb) => {
          qb.where('LOWER(organization.legalName) LIKE :search1', {
            search1: `${search}%`,
          });
          qb.orWhere('LOWER(organization.email) LIKE :search2', {
            search2: `${search}%`,
          });
          qb.orWhere('organization.mobileNumber LIKE :search3', {
            search3: `${search}%`,
          });
        }),
      );
    }

    if (query?.state) {
      qb.andWhere('organization.state = :state', { state: query.state });
    }

    // 📄 Pagination
    if (query?.limit) {
      qb.take(query.limit);
    }

    if (query?.offset) {
      qb.skip(query.offset);
    }

    // 📌 Order by newest organizations
    qb.orderBy('organization.id', 'DESC');

    // 🧾 Fetch data
    const [result, totalCount] = await qb.getManyAndCount();

    return {
      totalCount,
      result,
    };
  }

  async getOrganization(id: number) {
    let organization = await Organization.findOne({
      where: {
        id,
      },
    });
    return organization;
  }

  async getOrganizationUsers(id: number, query: any) {
    // let users = await User.find({ where: { organization: id, type: UserType.ORGANIZATION } });
    // return users;

    let users = await createQueryBuilder(User, 'user')
      .where('user.type = :userType', { userType: UserType.ORGANIZATION })
      .andWhere('user.organization.id = :orgId', { orgId: id });

    if (query?.status) {
      let status = query?.status;
      if (status === 'Active') {
        users.andWhere('user.status = :status', { status: UserStatus.ACTIVE });
      } else if (status === 'Inactive') {
        users.andWhere('user.status = :status', { status: UserStatus.INACTIVE });
      } else if (status === 'Deleted') {
        users.andWhere('user.status = :status', { status: UserStatus.DELETED });
      }
    }

    if (query?.search) {
      users.andWhere(
        new Brackets((qb) => {
          qb.where('user.fullName LIKE :search1', {
            search1: `%${query.search}%`,
          });
          qb.orWhere('user.mobileNumber LIKE :search2', {
            search2: `%${query.search}%`,
          });
          qb.orWhere('user.email LIKE :search3', {
            search3: `%${query.search}%`,
          });
        }),
      );
    }
    let result = await users.getManyAndCount();
    return {
      totalCount: result[1],
      result: result[0],
    };
  }

  async getAllOrganizationUsers(query) {
    const allUsers = await createQueryBuilder(User, 'user')
      .leftJoinAndSelect('user.organization', 'organization')
      .where('user.type = :userType', { userType: UserType.ORGANIZATION });

    if (query?.userType) {
      let status = query?.userType;
      if (status === 'Active') {
        allUsers.andWhere('user.status = :status', { status: UserStatus.ACTIVE });
      } else if (status === 'Inactive') {
        allUsers.andWhere('user.status = :status', { status: UserStatus.INACTIVE });
      } else if (status === 'Deleted') {
        allUsers.andWhere('user.status = :status', { status: UserStatus.DELETED });
      }
    }

    if (query.search) {
      allUsers.andWhere(
        new Brackets((qb) => {
          qb.where('user.fullName LIKE :search1', {
            search1: `%${query.search}%`,
          });
          qb.orWhere('user.mobileNumber LIKE :search2', {
            search2: `%${query.search}%`,
          });
          qb.orWhere('user.email LIKE :search3', {
            search3: `%${query.search}%`,
          });
          qb.orWhere('organization.legalName LIKE :search4', {
            search4: `%${query.search}%`,
          });
        }),
      );
    }

    allUsers.orderBy('user.id', 'DESC');

    if (query?.limit) {
      allUsers.take(query?.limit);
    }

    if (query?.offset > 0) {
      allUsers.skip(query?.offset);
    }

    let result = await allUsers.getManyAndCount();

    return {
      count: result[1],
      result: result[0],
    };
  }

  async getNotificationConfig(id: number) {
    const orgPreferences = await OrganizationPreferences.findOne({ where: { organization: id } });
    if (orgPreferences) {
      if (orgPreferences?.notificationConfig) {
        return orgPreferences?.notificationConfig;
      } else {
        return { whatsappPreferences: false };
      }
    } else {
      return { whatsappPreferences: false };
    }
  }

  async getQauntumConfig(id: number) {
    try {
      const orgPreferences = await OrganizationPreferences.findOne({ where: { organization: id } });
      if (orgPreferences) {
        if (orgPreferences?.quantumConfig) {
          return orgPreferences?.quantumConfig;
        } else {
          return { quantum: false };
        }
      } else {
        return { quantum: false };
      }
    } catch (err) {
      console.log('error coming while getting the getQauntumConfig', err);
    }
  }

  async updateQuantumConfig(id: number, data: any) {
    try {
      const orgPreferences = await OrganizationPreferences.findOne({ where: { organization: id } });
      if (orgPreferences) {
        orgPreferences.quantumConfig = data?.quantum;
        orgPreferences.lastUpdated = new Date();
        orgPreferences.save();
        return orgPreferences;
      } else {
        const organization = await Organization.findOne({ where: { id } });
        const organizationPreferences = new OrganizationPreferences();
        organizationPreferences.organization = organization;
        organizationPreferences.quantumConfig = data?.quantum;
        organizationPreferences.lastUpdated = new Date();
        organizationPreferences.save();
        return organizationPreferences;
      }
    } catch (err) {
      console.log('error coming while update the updateQuantumConfig', err);
    }
  }

  async getQuantumRequests(query) {
    try {
      const atomToQtmrequests = await createQueryBuilder(AtomToQtmrequests, 'atomToQtmrequests')
        .leftJoinAndSelect('atomToQtmrequests.user', 'user')
        .leftJoinAndSelect('user.organization', 'organization');

      if (query?.limit) {
        atomToQtmrequests.take(query?.limit);
      }

      if (query?.offset > 0) {
        atomToQtmrequests.skip(query?.offset);
      }
      atomToQtmrequests.orderBy('atomToQtmrequests.id', 'DESC');

      let result = await atomToQtmrequests.getManyAndCount();
      return {
        totalCount: result[1],
        result: result[0],
      };

      return atomToQtmrequests;
    } catch (error) {
      console.log('error occur while getting getQuantumRequests', error);
    }
  }

  async getAtomProMachines(query) {
    try {
      // Step 1: Get the total count without limit/offset
      const totalCountQuery = await createQueryBuilder(AutomationServers, 'automationServers')
        .select('COUNT(DISTINCT automationServers.ipAddress)', 'totalCount') // Total unique ipAddress count
        .getRawOne();

      const totalCount = parseInt(totalCountQuery.totalCount, 10) || 0; // Parse total count safely

      // Step 2: Get the paginated data
      const atomToQtmrequests = createQueryBuilder(AutomationServers, 'automationServers')
        .select([
          'automationServers.ip_address AS deviceName', // Grouped column
          'COUNT(*) AS machineCount', // Aggregated count
          `SUM(CASE 
          WHEN automationServers.serverStatus = 'busy' 
               AND automationServers.updated_at < NOW() - INTERVAL 1 HOUR
          THEN 1 ELSE 0 END) AS busySinceOneHour`,
        ])
        .groupBy('automationServers.ip_address'); // Group by ipAddress

      // AND updated_at < NOW() - INTERVAL 1 HOUR
      if (query?.limit) {
        atomToQtmrequests.take(query.limit); // Apply limit
      }

      if (query?.offset > 0) {
        atomToQtmrequests.skip(query.offset); // Apply offset
      }

      const result = await atomToQtmrequests.getRawMany(); // Fetch paginated data

      const typeQuery = `
      SELECT 
        SUM(CASE WHEN status = 'PENDING' THEN 1 ELSE 0 END) AS pendingCount,
        SUM(CASE WHEN status = 'INQUEUE' THEN 1 ELSE 0 END) AS inQueueCount
      FROM automation_machines
    `;

      const entityManager = getManager();
      const statusWiseDetails = await entityManager.query(typeQuery);

      const statusResult = statusWiseDetails[0]; // Extract the single object from the result list

      console.log(statusResult);

      // Step 3: Return totalCount and result
      return {
        statusResult,
        totalCount, // Total rows in the database
        result, // Paginated results
      };
    } catch (error) {
      console.log('Error occurred while getting getQuantumRequests', error);
      throw error; // Re-throw for better error handling upstream
    }
  }

  async updateMachineStatus(machineName: string) {
    try {
      // Fetch machines matching the condition
      const noOfMachines = await getRepository(AutomationMachines).find({
        where: { machineName, status: 'PENDING' },
      });

      if (noOfMachines.length > 0) {
        // Update machines that match the condition
        await getRepository(AutomationMachines)
          .createQueryBuilder()
          .update(AutomationMachines)
          .set({ machineName: null, status: 'INQUEUE' })
          .where('machineName = :machineName', { machineName })
          .andWhere('status = :status', { status: 'PENDING' })
          .execute();

        console.log(`Updated ${noOfMachines.length} machine(s).`);
      } else {
        console.log('No machines found with the given criteria.');
      }
    } catch (error) {
      console.error('Error updating machine status:', error);
    }
  }

  async deleteAtomProMachines(body: any) {
    try {
      const serverDetails = await AutomationServers.find({
        where: { ipAddress: body?.deviceName },
      });
      for (let machine of serverDetails) {
        try {
          // Fetch machines matching the condition
          const noOfMachines = await getRepository(AutomationMachines).find({
            where: { machineName: machine?.machineName, status: 'PENDING' },
          });

          if (noOfMachines.length > 0) {
            // Update machines that match the condition
            await getRepository(AutomationMachines)
              .createQueryBuilder()
              .update(AutomationMachines)
              .set({ machineName: null, status: 'INQUEUE' })
              .where('machineName = :machineName', { machineName: machine?.machineName })
              .andWhere('status = :status', { status: 'PENDING' })
              .execute();

            console.log(`Updated ${noOfMachines.length} machine(s).`);
          } else {
            console.log('No machines found with the given criteria.');
          }

          await machine.remove();
        } catch (error) {
          console.error('Error updating machine status:', error);
        }
      }
    } catch (error) {
      console.log('error in deleteAtomProMachines', error);
    }
  }

  async updateInqueueStatus() {
    try {
      await getRepository(AutomationMachines)
        .createQueryBuilder()
        .update(AutomationMachines)
        .set({ machineName: null, status: 'INQUEUE' })
        .where('status = :status', { status: 'PENDING' }) // Corrected to `.where`
        .execute();
    } catch (error) {
      console.log('Error Occur in updateInqueueStatus', error);
    }
  }

  async deleteServerRowsAndChangeStatus() {
    try {
      const serverDetails = await AutomationServers.find();
      if (serverDetails.length > 0) {
        await AutomationServers.remove(serverDetails);
        await this.updateInqueueStatus();
        return 'All rows deleted from the automation servers table.';
      } else {
        await this.updateInqueueStatus();
        return 'No rows found to delete.';
      }
    } catch (error) {
      console.log('Error occur in deleteServerRowsAndChangeStatus', error);
    }
  }

  async updateAutomationServerStatusToAvailable() {
    try {
      await getRepository(AutomationServers)
        .createQueryBuilder()
        .update(AutomationServers)
        .set({ serverStatus: 'available' })
        .where('serverStatus = :serverStatus', { serverStatus: 'busy' }) // Corrected to `.where`
        .execute();
    } catch (error) {
      console.log('Error occure in updateAutomationServerStatusToAvailable', error);
    }
  }

  async getAutomationConfig(id: number) {
    try {
      const orgPreferences = await OrganizationPreferences.findOne({ where: { organization: id } });
      if (orgPreferences) {
        if (orgPreferences?.automationConfig) {
          return orgPreferences;
        } else {
          return {
            automationConfig: {
              gstr: 'NO',
              gstrLimit: 100,
              incomeTax: 'NO',
              incomeTaxLimit: 300,
              tan: 'NO',
              tanLimit: 30,
            },
          };
        }
      } else {
        return {
          automationConfig: {
            gstr: 'NO',
            gstrLimit: 100,
            incomeTax: 'NO',
            incomeTaxLimit: 300,
            tan: 'NO',
            tanLimit: 30,
          },
        };
      }
    } catch (err) {
      console.log('error coming while getting the getQauntumConfig', err);
    }
  }

  async updateAutomationConfig(id: number, data: any) {
    try {
      const orgPreferences = await OrganizationPreferences.findOne({ where: { organization: id } });
      if (orgPreferences) {
        orgPreferences.automationConfig = data;
        orgPreferences.lastUpdated = new Date();
        orgPreferences.save();
        return orgPreferences;
      } else {
        const organization = await Organization.findOne({ where: { id } });
        const organizationPreferences = new OrganizationPreferences();
        organizationPreferences.organization = organization;
        organizationPreferences.automationConfig = data;
        organizationPreferences.lastUpdated = new Date();
        organizationPreferences.save();

        return organizationPreferences;
      }
    } catch (err) {
      console.log('error coming while update the updateQuantumConfig', err);
    }
  }

  async getAtomProLimitRequests() {
    try {
      const atomProRequests = await AtomProLimitRequests.find({
        relations: ['user'],
        order: {
          id: 'DESC',
        },
      });
      return atomProRequests;
    } catch (error) {
      console.log('error occur while getting getAtomProLimitRequests', error);
    }
  }

  async updateOrganizationLimitRequest(id: number, data: any) {
    try {
      const atomProRequest = await AtomProLimitRequests.findOne({ where: { id: id } });
      if (atomProRequest) {
        atomProRequest.respondStatus = true;
        await atomProRequest.save();
      }
    } catch (err) {
      console.log('error coming while update the updateQuantumConfig', err);
    }
  }

  async getAutomationScheduling(id) {
    try {
      let config: any = {
        method: 'get',
        maxBodyLength: Infinity,
        url: `${process.env.CAMUNDA_URL}/vider/quantum/api/automation/bulk/sync/${id}/incometax`,
        headers: {},
        data: '',
      };

      const incometaxResponse = await axios.request(config);

      let config1: any = {
        method: 'get',
        maxBodyLength: Infinity,
        url: `${process.env.CAMUNDA_URL}/vider/quantum/api/automation/bulk/sync/${id}/gstr`,
        headers: {},
        data: '',
      };
      const gstrResponse = await axios.request(config1);

      const gstrSchedule = await gstrResponse?.data;

      const incomeTaxSchedule = await incometaxResponse?.data;
      return { incomeTaxSchedule, gstrSchedule };
    } catch (error) {
      console.log('error occure while getting into getAutomationScheduling', error);
    }
  }

  async gstrEnableStatus(id) {
    try {
      let config: any = {
        method: 'put',
        maxBodyLength: Infinity,
        url: `${process.env.CAMUNDA_URL}/vider/quantum/api/automation/bulk/sync/${id}/enable/gstr`,
        headers: {},
        data: '',
      };
      const response = await axios.request(config);
    } catch (error) {
      console.log('Error occure while Updating incometaxEnableStatus');
    }
  }

  async gstrDisableStatus(id) {
    try {
      let config: any = {
        method: 'put',
        maxBodyLength: Infinity,
        url: `${process.env.CAMUNDA_URL}/vider/quantum/api/automation/bulk/sync/${id}/disable/gstr`,
        headers: {},
        data: '',
      };
      const response = await axios.request(config);
    } catch (error) {
      console.log('Error occure while Updating incometaxEnableStatus');
    }
  }

  async incometaxEnableStatus(id) {
    try {
      let config: any = {
        method: 'put',
        maxBodyLength: Infinity,
        url: `${process.env.CAMUNDA_URL}/vider/quantum/api/automation/bulk/sync/${id}/enable/incometax`,
        headers: {},
        data: '',
      };
      const response = await axios.request(config);
    } catch (error) {
      console.log('Error occure while Updating incometaxEnableStatus');
    }
  }

  async incometaxDisableStatus(id) {
    try {
      let config: any = {
        method: 'put',
        maxBodyLength: Infinity,
        url: `${process.env.CAMUNDA_URL}/vider/quantum/api/automation/bulk/sync/${id}/disable/incometax`,
        headers: {},
        data: '',
      };
      const response = await axios.request(config);
    } catch (error) {
      console.log('Error occure while Updating incometaxEnableStatus');
    }
  }

  async automationScheduling(data) {
    let data1 = JSON.stringify({
      modules: ['P', 'NAO', 'ANO', 'LB', 'OD'],
      orgId: data?.id,
      type: data?.type,
    });

    let config: any = {
      method: 'post',
      maxBodyLength: Infinity,
      url: `${process.env.CAMUNDA_URL}/vider/quantum/api/automation/bulk/sync`,
      headers: {
        'Content-Type': 'application/json',
      },
      data: data1,
    };

    const response = await axios.request(config);
    return response?.data;
  }

  async updateNotificationConfig(id: number, data: any) {
    const orgPreferences = await OrganizationPreferences.findOne({ where: { organization: id } });

    try {
      if (orgPreferences) {
        orgPreferences.notificationConfig = data?.whatsappPreferences;
        orgPreferences.lastUpdated = new Date();
        orgPreferences.save();
        return orgPreferences;
      } else {
        const organization = await Organization.findOne({ where: { id } });
        const organizationPreferences = new OrganizationPreferences();
        organizationPreferences.organization = organization;
        organizationPreferences.notificationConfig = data?.whatsappPreferences;
        organizationPreferences.lastUpdated = new Date();
        organizationPreferences.save();
        return organizationPreferences;
      }
    } catch (err) {
      console.log('error coming while update the NotificationConfig', err);
    }
  }

  async updateOrganization(id: number, data: UpdateOrganizationProfileDto) {
    let organization = await Organization.findOne({ where: { id: id } });

    if (!organization) {
      throw new NotFoundException('organization not found');
    }

    organization.config = JSON.stringify(data.config);

    await organization.save();

    return organization;
  }

  async createService(data: CreateServiceDto) {
    data.name = data.name.trim();

    let existingService = await Service.findOne({
      where: {
        name: data.name,
        category: { id: data.category },
        ...(data.subCategory && { subCategory: { id: data.subCategory } }),
        defaultOne: true,
      },
    });

    if (existingService) {
      throw new ConflictException(
        'Service already exists with the given Category, Subcategory and Name',
      );
    }

    let labelIds: number[] = [];
    let labelss: Label[] = [];

    if (data.labels) {
      labelIds = (data.labels as Label[]).map((label) => label.id);
      labelss = await Label.find({ where: { id: In(labelIds) } });
    }

    let category = await Category.findOne({
      where: {
        id: data.category,
      },
    });

    let subCategory = await Category.findOne({
      where: {
        id: data.subCategory,
      },
    });

    let service = new Service();
    service.name = data.name;
    service.description = data.description;
    service.hourlyPrice = data.hourlyPrice;
    service.totalPrice = data.totalPrice;
    service.category = category;
    service.subCategory = subCategory;
    service.checklists = data.checklists as Checklist[];
    service.milestones = data.milestones as Milestone[];
    service.stageOfWorks = data.stageOfWork as StageOfWork[];
    service.subTasks = data.subTasks as SubTask[];
    service.defaultOne = true;
    service.isActive = true;
    service.state = data.state;
    service.clientSubCategory = JSON.stringify(data.clientSubCategory);
    service.linkedServices = data.linkedServices?.length
      ? JSON.stringify(data.linkedServices)
      : null;
    service.content = data.content;
    const words = data.content.split(/\s+/).filter(Boolean).length;
    const averageWordsPerMinute = 200;
    const readTimeInMinutes = Math.ceil(words / averageWordsPerMinute);
    service.serviceReadTime = `${readTimeInMinutes} minutes`;
    service.prismServiceName = data.prismServiceName;
    service.prismImage = data.prismImage;
    service.prismChecklists = data.prismChecklists?.length
      ? JSON.stringify(data.prismChecklists)
      : null;
    service.serviceProcedure = data.serviceProcedure?.length
      ? JSON.stringify(data.serviceProcedure)
      : null;
    service.serviceFaqs = data.serviceFaqs?.length ? JSON.stringify(data.serviceFaqs) : null;
    service.subtaskServices = data.subtaskServices?.length
      ? JSON.stringify(data.subtaskServices)
      : null;
    service.prismSampleCertificate = data.prismSampleCertificate;
    service.prismProcessImage = data.prismProcessImage;
    service.applicationType = data.applicationType;
    service.prismYoutubeLink = data.prismYoutubeLink;
    service.postIsActive = data.postIsActive;
    service.prismPrice = data.prismPrice;
    service.prismDescription = data.prismDescription;
    service.labels = labelss;
    service.isActive = data.isActive;
    service.isRecurring = data.isRecurring;
    service.frequency = data.frequency;
    service.recurringFrequencyDetails = data.recurringFrequencyDetails;

    await service.save();
  }

  async updateService(id: number, data: any) {
    let service = await Service.findOne({ where: { id } });

    if (!service) {
      throw new NotFoundException('Service not found');
    }
    data.name = data.name.trim();
    let existingService = await Service.findOne({
      where: {
        name: data.name,
        id: Not(id),
        category: { id: data.category },
        ...(data.subCategory && { subCategory: { id: data.subCategory } }),
        defaultOne: true,
      },
    });

    let labelIds: number[] = [];
    let labelss: Label[] = [];

    if (data.labels) {
      labelIds = (data.labels as Label[]).map((label) => label.id);
      labelss = await Label.find({ where: { id: In(labelIds) } });
    }

    if (existingService) {
      throw new ConflictException(
        'Service already exists with the given Category, Subcategory and Name',
      );
    }

    let category = await Category.findOne({ where: { id: data.category } });
    let subCategory = await Category.findOne({ where: { id: data.subCategory } });

    for (const serviceChecklist of data.checklists) {
      if (serviceChecklist.checklistUpdated) {
        const updateChecklist = await Checklist.findOne({ where: { id: serviceChecklist.id } });
        updateChecklist.name = serviceChecklist.name;
        await updateChecklist.save();
      }
      if (serviceChecklist.itemUpdated) {
        for (const checklistItem of serviceChecklist.checklistItems) {
          if (checklistItem.itemUpdated) {
            const updatedChecklistItem = await ChecklistItem.findOne({
              where: { id: checklistItem.id },
            });
            updatedChecklistItem.name = checklistItem.name;
            updatedChecklistItem.description = checklistItem.description;
            updatedChecklistItem.checklist = serviceChecklist;
            await updatedChecklistItem.save();
          } else if (checklistItem.itemAdded) {
            const newChecklistItem = new ChecklistItem();
            newChecklistItem.name = checklistItem.name;
            newChecklistItem.description = checklistItem.description;
            newChecklistItem.checklist = serviceChecklist;
            await newChecklistItem.save();
          }
          const idsToDelete = serviceChecklist.checklistItems.map((item) => item.id);
          const recordsToDelete = await ChecklistItem.find({
            where: { checklist: serviceChecklist.id, id: Not(In(idsToDelete)) },
          });
          for (const record of recordsToDelete) {
            await record.remove();
          }
        }
      } else if (serviceChecklist.checklistAdded) {
        const newChecklist = new Checklist();
        newChecklist.name = serviceChecklist.name;
        newChecklist.service = service;
        const newChecklistRes = await newChecklist.save();
        for (const newAddedChecklistItem of serviceChecklist.checklistItems) {
          const newChecklistItemToAdd = new ChecklistItem();
          newChecklistItemToAdd.name = newAddedChecklistItem.name;
          newChecklistItemToAdd.description = newAddedChecklistItem.description;
          newChecklistItemToAdd.checklist = newChecklistRes;
          await newChecklistItemToAdd.save();
        }
      }
    }
    const idsToDelete = data.checklists.map((item) => item.id);
    const recordsToDelete = await Checklist.find({
      where: { service: id, id: Not(In(idsToDelete)) },
    });

    const recordsToDeleteIds = recordsToDelete.map((item) => item.id);

    const checklistItemsRecordsToDelete = await ChecklistItem.find({
      where: { checklist: In(recordsToDeleteIds) },
    });

    for (const chechlistItemRecord of checklistItemsRecordsToDelete) {
      await chechlistItemRecord.remove();
    }
    for (const record of recordsToDelete) {
      await record.remove();
    }
    // // SERVICE MILESTONES UPDATE, ADD AND DELETE
    for (const newData of data.stageOfWork) {
      const existingRecord = await StageOfWork.findOne({ where: { id: newData.id } });
      if (existingRecord && newData.isUpdated) {
        const stageOfWork = existingRecord;
        stageOfWork.name = newData.name;
        stageOfWork.description = newData.description;
        stageOfWork.attachmentFile = newData.attachmentFile;
        stageOfWork.extraAttributes = newData.extraAttributes;
        stageOfWork.referenceNumber = newData.referenceNumber;
        stageOfWork.referenceNumberValue = newData.referenceNumberValue;
        stageOfWork.type = newData.type;
        stageOfWork.service = service;
        await stageOfWork.save();
      } else if (!existingRecord) {
        const newStageOfWork = new StageOfWork();
        newStageOfWork.name = newData.name;
        newStageOfWork.description = newData.description;
        newStageOfWork.attachmentFile = newData.attachmentFile;
        newStageOfWork.extraAttributes = newData.extraAttributes;
        newStageOfWork.referenceNumber = newData.referenceNumber;
        newStageOfWork.referenceNumberValue = newData.referenceNumberValue;
        newStageOfWork.type = newData.type;
        newStageOfWork.service = service;
        await newStageOfWork.save();
      }
    }

    const sowIdsToDelete = data.stageOfWork.map((item) => item.id);
    const sowRecordsToDelete = await StageOfWork.find({
      where: { service: id, id: Not(In(sowIdsToDelete)) },
    });

    for (const sowRecord of sowRecordsToDelete) {
      await sowRecord.remove();
    }

    service.name = data.name;
    service.description = data.description;
    service.hourlyPrice = data.hourlyPrice;
    service.totalPrice = data.totalPrice;
    service.category = category;
    service.subCategory = subCategory;
    // service.checklists = data.checklists as Checklist[];
    service.version = service.version + 1;
    service.clientSubCategory = JSON.stringify(data.clientSubCategory);
    service.state = data.state;
    service.linkedServices = data?.linkedServices?.length
      ? JSON.stringify(data.linkedServices)
      : null;
    service.content = data.content;
    const words = data.content.split(/\s+/).filter(Boolean).length;
    const averageWordsPerMinute = 200;
    const readTimeInMinutes = Math.ceil(words / averageWordsPerMinute);
    service.serviceReadTime = `${readTimeInMinutes} minutes`;
    service.prismServiceName = data.prismServiceName;
    service.prismImage = data.prismImage;
    service.prismChecklists = data?.prismChecklists?.length
      ? JSON.stringify(data.prismChecklists)
      : null;
    service.serviceProcedure = data?.serviceProcedure?.length
      ? JSON.stringify(data.serviceProcedure)
      : null;
    service.serviceFaqs = data?.serviceFaqs?.length ? JSON.stringify(data.serviceFaqs) : null;
    service.subtaskServices = data?.subtaskServices?.length
      ? JSON.stringify(data.subtaskServices)
      : null;
    service.prismSampleCertificate = data.prismSampleCertificate;
    service.prismProcessImage = data.prismProcessImage;
    service.applicationType = data.applicationType;
    service.prismYoutubeLink = data.prismYoutubeLink;
    service.postIsActive = data.postIsActive;
    service.prismPrice = data.prismPrice;
    service.prismDescription = data.prismDescription;
    service.labels = labelss;
    service.isActive = data.isActive;
    service.isRecurring = data.isRecurring;
    service.frequency = data.frequency;
    service.recurringFrequencyDetails = data.recurringFrequencyDetails;
    await service.save();
    return service;
  }

  async update(id: number, data: CreateCategoryDto) {
    let category = await Category.findOne({ where: { id } });
    category.name = data.name;
    category.image = data.image;
    category.color = data.color;
    category.subCategories = data.subCategories;
    category.version = category.version + 1;
    await category.save();
    return category;
  }

  async getServices(query: any) {
    let services = createQueryBuilder(Service, 'service')
      .leftJoinAndSelect('service.category', 'category')
      .leftJoinAndSelect('service.subCategory', 'subCategory')
      .leftJoinAndSelect('service.organization', 'organization')
      .leftJoinAndSelect('service.labels', 'labels')
      .where('service.defaultOne = :defaultOne', {
        defaultOne: true,
      });

    if (query.search) {
      services.andWhere(
        new Brackets((qb) => {
          qb.where('service.name LIKE :search', {
            search: `%${query.search}%`,
          });
          qb.orWhere('category.name LIKE :search', {
            search: `%${query.search}%`,
          });
          qb.orWhere('subCategory.name LIKE :search', {
            search: `%${query.search}%`,
          });
          qb.orWhere('labels.name LIKE :search', {
            search: `%${query.search}%`,
          });
        }),
      );
    }

    if (query.category) {
      services.andWhere('category.id = :category', {
        category: query.category,
      });
    }

    if (query.subCategory) {
      services.andWhere('subCategory.id = :subCategory', {
        subCategory: query.subCategory,
      });
    }

    if (query.offset) {
      services.skip(query.offset);
    }

    if (query.limit) {
      services.take(query.limit);
    }

    let data = await services.getManyAndCount();

    return {
      totalCount: data[1],
      result: data[0],
    };
  }

  async createCategory(data: any) {
    try {
      let category = new Category();
      category.name = data.name;
      category.image = data.image;
      category.color = data.color;
      category.subCategories = data.subCategories;
      category.defaultOne = true;
      category.isActive = true;
      await category.save();
      return category;
    } catch (err) {
      console.log(err);
      throw new InternalServerErrorException(err);
    }
  }

  async createQuantumCategories(data: CreateQuantumCategoryDto) {
    try {
      let category = new QtmCategories();
      category.name = data.name;
      category.subCategories = data.subCategories;
      category.defaultOne = true;
      await category.save();
      return category;
    } catch (err) {
      console.log(err);
      throw new InternalServerErrorException(err);
    }
  }

  async getCategories() {
    let categories = await Category.find({
      relations: ['subCategories', 'parentCategory'],
      where: {
        defaultOne: true,
        parentCategory: IsNull(),
      },
    });
    return categories;
  }

  async getNewCategories() {
    let newCategories = await Category.createQueryBuilder('categories')
      .leftJoinAndSelect('categories.subCategories', 'subCategories')
      .leftJoinAndSelect('categories.parentCategory', 'parentCategory')
      .leftJoinAndSelect(AtomSuperAdminActivity, 'atom', 'categories.id = atom.typeId')
      .where(
        '(categories.defaultOne = :defaultOne AND atom.typeId IS NULL) OR (categories.defaultOne = :defaultOne AND categories.updatedAt > (SELECT MAX(atom.lastUpdated) FROM atom_super_admin_activity atom WHERE atom.typeId = categories.id))',
        { defaultOne: true },
      )
      .andWhere("atom.type = 'Categories'")
      .getMany();

    const uniqueTypeIds = await getManager()
      .createQueryBuilder(AtomSuperAdminActivity, 'atom')
      .select('DISTINCT(atom.typeId)', 'typeId')
      .where('atom.type = :type', { type: 'Categories' })
      .getRawMany();
    const typeIdsArray = uniqueTypeIds.map((row) => row.typeId);
    const categoriesWithSentToProduction = newCategories.map((category) => {
      const sentToProduction = typeIdsArray.includes(category.id);
      return { ...category, sentToProduction };
    });
    return categoriesWithSentToProduction;
  }

  async createAdminCategoryInProd(data: any) {
    const serviceResponse = {
      categoryResponse: [],
      subCategoryResponse: [],
    };
    let categoryInsertQuery = `INSERT INTO vider.category 
      (name,
      color,
      default_one,
      from_admin,
      version,
      is_active
      )
      VALUES (
        '${data.name}',
        '${data.color}',
        ${true},
        ${false},
        ${data.version},
        ${true}
      );`;
    const entityManager = getManager();
    const categoryInsertResponse = await entityManager.query(categoryInsertQuery);

    if (categoryInsertResponse) {
      serviceResponse.categoryResponse.push({
        type: 'Categories',
        typeId: data.id,
        prodTypeId: categoryInsertResponse.insertId,
      });
    } else {
      throw new Error(`Category '${data.name}' failed to Move to Production.`);
    }
    for (const subCategory of data.subCategories) {
      const subCategoryInsertQuery = `
        INSERT INTO vider.category 
            (name,
              parent_category_id,
              default_one,
              version)
        VALUES 
            ('${subCategory.name}',
              ${categoryInsertResponse.insertId},
             ${false},
             ${subCategory.version});
    `;
      const subCategoryInsertResponse = await entityManager.query(subCategoryInsertQuery);
      if (subCategoryInsertResponse) {
        serviceResponse.subCategoryResponse.push({
          type: 'Sub Categories',
          typeId: subCategory.id,
          prodTypeId: subCategoryInsertResponse.insertId,
        });
      } else {
        throw new Error(`Sub category '${subCategory.name}' failed to Move to Production.`);
      }
    }
    return serviceResponse;
  }

  async updateAdminCategoryInProd(data: any) {
    const serviceResponse = {
      categoryResponse: [],
      subCategoryResponse: [],
    };
    const updatedCatProdTypeId = await AtomSuperAdminActivity.findOne({
      where: {
        typeId: data.id,
        type: 'Categories',
      },
      order: {
        id: 'DESC',
      },
    });
    const updateCategoryQuery = `UPDATE vider.category SET
  name = '${data.name}',
  image = ${data.image ? `'${data.image}'` : null},
  color = '${data.color}',
  default_one =  ${true},
  from_admin =  ${false},
  version =  ${data.version}
  WHERE id = ${updatedCatProdTypeId.prodTypeId};`;
    const entityManager = getManager();
    const updateResponse = await entityManager.query(updateCategoryQuery);
    if (updateResponse) {
      serviceResponse.categoryResponse.push({
        type: 'Categories',
        typeId: data.id,
        prodTypeId: updatedCatProdTypeId.prodTypeId,
      });
    } else {
      throw new Error(`Category with id ${updatedCatProdTypeId.prodTypeId} does not exist.`);
    }
    const deletedSubCatResponse = await Category.findOne({
      where: {
        id: data.id,
      },
    });

    const prodSubCatQuery = `SELECT * FROM vider.category WHERE parent_category_id = ${updatedCatProdTypeId.prodTypeId} ;`;
    const prodSubCatResponse = await entityManager.query(prodSubCatQuery);
    const prodSubCatIds = prodSubCatResponse.map((item) => item.id);
    const preprodSubCatIds = data.subCategories.map((item) => item.id);
    const subcatProdTypeQ = await AtomSuperAdminActivity.find({
      select: ['prodTypeId'],
      where: {
        type: 'Sub Categories',
        typeId: In(preprodSubCatIds),
      },
    });
    const subcatProdTypeIds = subcatProdTypeQ.map((item) => item.prodTypeId);
    const deletedSubCatIds = prodSubCatIds.filter((id) => !subcatProdTypeIds.includes(id));

    if (deletedSubCatIds.length > 0) {
      for (let deletedId of deletedSubCatIds) {
        const deleteSubCatQuery = `UPDATE vider.category SET parent_category_id = ${null} WHERE id = ${deletedId};`;
        const deleteSubCatResponse = await entityManager.query(deleteSubCatQuery);
        if (deleteSubCatResponse) {
          serviceResponse.subCategoryResponse.push({
            type: 'Sub Categories',
            prodTypeId: deletedId,
          });
        }
      }
    }

    for (const subCategory of data.subCategories) {
      const updatedSubCatProdTypeId = await AtomSuperAdminActivity.findOne({
        where: {
          typeId: subCategory.id,
          type: 'Sub Categories',
        },
        order: {
          id: 'DESC',
        },
      });
      if (updatedSubCatProdTypeId) {
        const lastUpdatedDate = new Date(updatedSubCatProdTypeId.lastUpdated);
        const subCategoryDate = new Date(subCategory.updatedAt);
        if (updatedSubCatProdTypeId && lastUpdatedDate < subCategoryDate) {
          const subCategoryInsertQuery = `
          UPDATE vider.category SET 
              name = '${subCategory.name}',
              default_one =   ${false},
              version =   ${subCategory.version}
          WHERE id = ${updatedSubCatProdTypeId.prodTypeId};`;
          const subCategoryInsertResponse = await entityManager.query(subCategoryInsertQuery);
          if (subCategoryInsertResponse) {
            serviceResponse.subCategoryResponse.push({
              type: 'Sub Categories',
              typeId: subCategory.id,
              prodTypeId: updatedSubCatProdTypeId.prodTypeId,
            });
          } else {
            throw new Error(`Sub Category  '${subCategory.name}' failed to Update to production.`);
          }
        }
      }
      if (!updatedSubCatProdTypeId) {
        const subCategoryInsertQuery = `
        INSERT INTO vider.category 
            (name,
              parent_category_id,
              default_one,
              version)
        VALUES 
            ('${subCategory.name}',
              ${updatedCatProdTypeId.prodTypeId},
             ${false},
             ${subCategory.version});
    `;
        const subCategoryInsertResponse = await entityManager.query(subCategoryInsertQuery);
        if (subCategoryInsertResponse) {
          serviceResponse.subCategoryResponse.push({
            type: 'Sub Categories',
            typeId: subCategory.id,
            prodTypeId: subCategoryInsertResponse.insertId,
          });
        } else {
          throw new Error(`Sub Category  '${subCategory.name}' failed to Move to production.`);
        }
      }
    }
    return serviceResponse;
  }

  async createLabel(data: any) {
    let label = new Label();
    label.name = data.name;
    label.color = data.color;
    label.defaultOne = true;
    await label.save();
    return label;
  }

  async getLabels() {
    let labels = await Label.find({
      where: {
        defaultOne: true,
      },
    });
    return labels;
  }

  async getNewLabels() {
    const newLabels = await getConnection()
      .getRepository(Label)
      .createQueryBuilder('label')
      .where('label.default_one = :defaultOne', { defaultOne: true })
      .getMany();

    const typeIds = await getConnection()
      .getRepository(AtomSuperAdminActivity)
      .createQueryBuilder('atom_super_admin_activity')
      .where('atom_super_admin_activity.type = :activityType', { activityType: 'Label' })
      .getMany();

    const ids = typeIds.map((activity) => activity.typeId);
    const filteredServices = newLabels.filter((label) => !ids.includes(label.id));

    const allServices = [...filteredServices];

    return allServices;
  }

  async getNewEmailTemplates() {
    const newEmailTemplates = await BroadcastEmailTemplates.createQueryBuilder('emailTemplates')
      .leftJoinAndSelect('emailTemplates.label', 'label')
      .leftJoin(
        AtomSuperAdminActivity,
        'atom',
        'emailTemplates.id = atom.typeId AND atom.type = :type',
        {
          type: 'EmailTemplates',
        },
      )
      .where(
        '(emailTemplates.default = :default AND atom.typeId IS NULL) OR (emailTemplates.default = :default AND emailTemplates.updatedAt > (SELECT MAX(atom.lastUpdated) FROM atom_super_admin_activity atom WHERE atom.typeId = emailTemplates.id))',
        { default: 1 },
      )
      .getMany();

    const uniqueTypeIds = await getManager()
      .createQueryBuilder(AtomSuperAdminActivity, 'atom')
      .select('DISTINCT(atom.typeId)', 'typeId')
      .where('atom.type = :type', { type: 'EmailTemplate' })
      .getRawMany();

    const typeIdsArray = uniqueTypeIds.map((row) => row.typeId);
    const emailTemplatesWithSentToProduction = newEmailTemplates.map((emailTemplate) => {
      const sentToProduction = typeIdsArray.includes(emailTemplate.id);
      return { ...emailTemplate, sentToProduction };
    });

    return emailTemplatesWithSentToProduction;
  }

  async createOrUpdateAdminLabelInProd(body: any) {
    const serviceResponse = { labelResponse: [] };
    const query = `INSERT INTO vider.label (
      name,
      color,
      default_one
      ) 
      VALUES 
      (
        '${body.name}',
        '${body.color}',
        ${true}
    );`;
    const entityManager = getManager();
    const labelInsertResponse = await entityManager.query(query);
    serviceResponse.labelResponse.push({
      type: 'Label',
      typeId: body.id,
      prodTypeId: labelInsertResponse.insertId,
    });
    return serviceResponse;
  }

  async updateAdminlabelInProd(data: any) {
    try {
      const serviceResponse = { updatedLabelRespone: [] };
      const labelProd = await AtomSuperAdminActivity.findOne({
        where: {
          typeId: data.id,
          type: 'Label',
        },
        order: {
          id: 'DESC',
        },
      });
      const updateLabelQuery = `UPDATE vider.label
    SET
      name = '${data.name}',
      color = '${data.color}'
      WHERE id = ${labelProd.prodTypeId};
    `;
      const entityManager = getManager();
      await entityManager.query(updateLabelQuery);
      serviceResponse.updatedLabelRespone.push({
        type: 'Label',
        typeId: data.id,
        prodTypeId: labelProd.prodTypeId,
      });
    } catch (error) {
      console.error(error);
    }
  }

  async createRole(data: any) {
    const { name, description } = data;
    let existingRole = await Role.findOne({
      where: { name, defaultOne: true },
    });

    if (existingRole) throw new BadRequestException('Role already exists');

    let role = new Role();
    role.name = data.name;
    role.description = data.description;
    role.defaultRole = data.defaultRole;
    role.defaultOne = true;
    await role.save();
    return role;
  }

  async getRoles() {
    let roles = await Role.find({
      where: {
        defaultOne: true,
      },
    });
    return roles;
  }

  async getDefaultRoles(roleId) {
    let roles = await Role.findOne({
      where: { id: roleId, defaultOne: true },
      relations: ['permissions'],
    });
    return roles;
  }

  async getNewServices() {
    const serviceRepository = getRepository(Service);
    const newServices = await serviceRepository
      .createQueryBuilder('service')
      .leftJoinAndSelect('service.category', 'category')
      .leftJoinAndSelect('service.subCategory', 'subCategory')
      .leftJoinAndSelect('service.stageOfWorks', 'stageOfWorks')
      .leftJoinAndSelect('service.labels', 'labels')
      .leftJoinAndSelect(AtomSuperAdminActivity, 'atom', 'service.id = atom.typeId')
      .where(
        '(service.defaultOne = :defaultOne AND atom.typeId IS NULL) OR (service.defaultOne = :defaultOne AND service.updatedAt > (SELECT MAX(atom.lastUpdated) FROM atom_super_admin_activity atom WHERE atom.typeId = service.id))',
        { defaultOne: true },
      )
      .andWhere("atom.type = 'Services'")
      .getMany();

    const uniqueTypeIds = await getManager()
      .createQueryBuilder(AtomSuperAdminActivity, 'atom')
      .select('DISTINCT(atom.typeId)', 'typeId')
      .where('atom.type = :type', { type: 'Services' })
      .getRawMany();

    const typeIdsArray = uniqueTypeIds.map((row) => row.typeId);
    const servicesWithSentToProduction = newServices.map((service) => {
      const sentToProduction = typeIdsArray.includes(service.id);
      return { ...service, sentToProduction };
    });
    return servicesWithSentToProduction;
  }

  async getServiceChecklists(id: number) {
    let serviceChecklist = await createQueryBuilder(Service, 'service')
      .leftJoinAndSelect('service.checklists', 'checklist')
      .leftJoinAndSelect('checklist.checklistItems', 'checklistItems')
      .where('service.id = :serviceId', { serviceId: id })
      .getOne();
    return serviceChecklist;
  }

  async getNewRoles() {
    const structuredResult = await createQueryBuilder(Role, 'role')
      .leftJoinAndSelect('role.permissions', 'permissions')
      .leftJoinAndSelect(AtomSuperAdminActivity, 'atom', 'role.id = atom.typeId')
      .where(
        '(role.defaultOne = :defaultOne AND atom.typeId IS NULL) OR (role.defaultOne = :defaultOne AND role.lastUpdated > (SELECT MAX(atom.lastUpdated) FROM atom_super_admin_activity atom WHERE atom.typeId = role.id))',
        { defaultOne: true },
      )
      .andWhere("(atom.type = 'Roles')")
      .getMany();

    const rawResult = await createQueryBuilder(Role, 'role')
      .leftJoinAndSelect('role.permissions', 'permissions')
      .leftJoinAndSelect(AtomSuperAdminActivity, 'atom', 'role.id = atom.typeId')
      .where(
        '(role.defaultOne = :defaultOne AND  atom.type = "Roles"  AND role.lastUpdated > (SELECT MAX(atom_sub.last_updated) FROM atom_super_admin_activity atom_sub WHERE atom_sub.type_id = role.id ))',
        { defaultOne: true },
      )
      // .andWhere("(atom.type = 'Roles')")
      .select([
        'role.id as roleId',
        'role.name as roleName',
        'atom.id as atomSuperAdminActivityId',
        'atom.typeId as atomSuperAdminActivityTypeId',
        'atom.prodTypeId as atomSuperAdminActivityProdTypeId',
      ])
      .getRawMany();

    structuredResult.forEach((role: any) => {
      const roleId = role.id;
      const matchingRaw = rawResult.find((raw) => raw.roleId === roleId);
      if (matchingRaw) {
        role.atomSuperAdminActivityId = matchingRaw.atomSuperAdminActivityId;
        role.atomSuperAdminActivityTypeId = matchingRaw.atomSuperAdminActivityTypeId;
        role.atomSuperAdminActivityProdTypeId = matchingRaw.atomSuperAdminActivityProdTypeId;
      }
    });
    return structuredResult;
  }

  async createAdminEmailTemplateInProd(data: any) {
    const serviceResponse = { templateResponse: {} };

    const labelActivityRecords = [];
    if (data.label && data.label.id) {
      const labelId = data.label.id;

      const labelRecord = await AtomSuperAdminActivity.findOne({
        where: {
          type: 'Label',
          typeId: labelId,
        },
        order: {
          id: 'DESC',
        },
      });

      if (labelRecord) {
        labelActivityRecords.push(labelRecord);
      }
    }

    // Use first matched label for label_id insert (or handle differently if needed)
    const labelIdForInsert = labelActivityRecords[0]?.prodTypeId || null;

    const query = `INSERT INTO vider.broadcast_email_templates (
    title,
    subject,
    content,
    \`default\`,
    label_id
  ) VALUES (
    '${data.title.replace(/'/g, "''")}',
    '${data.subject.replace(/'/g, "''")}',
    '${data.content.replace(/'/g, "''")}',
    1,
    ${labelIdForInsert}
  );`;

    const entityManager = getManager();
    const templateInsertResponse = await entityManager.query(query);

    serviceResponse.templateResponse = {
      type: 'EmailTemplate',
      typeId: data.id,
      prodTypeId: templateInsertResponse.insertId,
    };

    return serviceResponse;
  }

  async updateAdminEmailTemplateInProd(data: any) {
    const serviceResponse = { updatedTemplateResponse: {} };
    const labelActivityRecords = [];

    // 1. Get latest label mapping from activity
    if (data.label && data.label.id) {
      const labelId = data.label.id;

      const labelRecord = await AtomSuperAdminActivity.findOne({
        where: {
          type: 'Label',
          typeId: labelId,
        },
        order: {
          id: 'DESC',
        },
      });

      if (labelRecord) {
        labelActivityRecords.push(labelRecord);
      }
    }

    const labelIdForUpdate = labelActivityRecords[0]?.prodTypeId || null;

    // 2. Get previously created prod template ID from activity log
    const existingProdRecord = await AtomSuperAdminActivity.findOne({
      where: {
        type: 'EmailTemplate',
        typeId: data.id,
      },
      order: {
        id: 'DESC',
      },
    });

    // 3. If not found, skip update
    if (!existingProdRecord) return serviceResponse;

    // 4. Escape string values
    const escapedTitle = data.title.replace(/'/g, "''");
    const escapedSubject = data.subject.replace(/'/g, "''");
    const escapedContent = data.content.replace(/'/g, "''");

    // 5. Construct update query
    const updateQuery = `
    UPDATE vider.broadcast_email_templates SET
      title = '${escapedTitle}',
      subject = '${escapedSubject}',
      content = '${escapedContent}',
      label_id = ${labelIdForUpdate},
      updated_at = NOW()
    WHERE id = ${existingProdRecord.prodTypeId};
  `;

    // 6. Run query
    const entityManager = getManager();
    const updateResponse = await entityManager.query(updateQuery);

    // 7. Build response
    if (updateResponse) {
      serviceResponse.updatedTemplateResponse = [
        {
          type: 'EmailTemplate',
          typeId: data.id,
          prodTypeId: existingProdRecord.prodTypeId,
        },
      ];
    }

    return serviceResponse;
  }

  async createAdminServiceInProd(data: any) {
    const serviceResponseData = {
      serviceRespone: [],
      serviceChecklistResponse: [],
      serviceCheckListItemsResponse: [],
      serviceMilestoneResponse: [],
      serviceStageofworkResponse: [],
    };

    const catActivityRecord = await AtomSuperAdminActivity.findOne({
      where: {
        type: 'Categories',
        typeId: data.category.id,
      },
      order: {
        id: 'DESC',
      },
    });
    const subCatActivityRecord = await AtomSuperAdminActivity.findOne({
      where: {
        type: 'Sub Categories',
        typeId: data.subCategory.id,
      },
      order: {
        id: 'DESC',
      },
    });

    const serviceQuery = `
        INSERT INTO vider.service 
        (name,
          description,
          default_one,
          hourly_price,
          total_price,
          category_id,
          sub_category_id,
          organization_id,
          client_sub_category,
          from_admin,
          admin_service_id,
          recurring_frequency,
          recurring_dates,
          state,
          version,
          is_active,
          linked_services,
          subtask_services,
          service_faqs,
          service_procedure,
          recurring_frequency_details,
          is_recurring
        ) VALUES ( 
          ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?
          );`;
    const entityManager = getManager();
    const savedServiceRecord = await entityManager.query(serviceQuery, [
      data.name,
      data.description || null,
      true,
      data.hourlyPrice || 0,
      data.totalPrice || 0,
      catActivityRecord.prodTypeId || null,
      subCatActivityRecord.prodTypeId || null,
      null,
      JSON.stringify(data.clientSubCategory),
      false,
      data.adminServiceId || null,
      data.recurringFrequency,
      data.recurringDates,
      data.state,
      data.version,
      data.isActive,
      data.linkedServices ? JSON.stringify(data.linkedServices) : null,
      data.subtaskServices ? JSON.stringify(data.subtaskServices) : null,
      data.serviceFaqs ? JSON.stringify(data.serviceFaqs) : null,
      data.serviceProcedure ? JSON.stringify(data.serviceProcedure) : null,
      data.recurringFrequencyDetails ? JSON.stringify(data.recurringFrequencyDetails) : null,
      data.isRecurring,
    ]);
    // const savedServiceRecord = await entityManager.query(serviceQuery1);
    const savedServiceIdQ = `SELECT id FROM vider.service WHERE id = ${savedServiceRecord.insertId};`;
    const savedServiceId = await entityManager.query(savedServiceIdQ);
    if (savedServiceRecord) {
      serviceResponseData.serviceRespone.push({
        type: 'Services',
        typeId: data.id,
        prodTypeId: savedServiceRecord.insertId,
      });
    }

    if (data.checklists) {
      for (const checklist of data.checklists) {
        const newChecklist = `INSERT INTO vider.service_checklist (
            name,
            service_id
          )
          VALUES (
            '${checklist.name.replace(/'/g, "''")}',
            ${savedServiceId[0].id}
          );`;
        const checkListResonse = await entityManager.query(newChecklist);
        if (checkListResonse) {
          serviceResponseData.serviceChecklistResponse.push({
            type: 'Checklist',
            typeId: checklist.id,
            prodTypeId: checkListResonse.insertId,
            refId: data.id,
          });
        }
        if (checklist.checklistItems.length > 0) {
          for (let subChecklistItems of checklist.checklistItems) {
            const checklistItemsinsertQ = `INSERT INTO vider.service_checklist_item (name,checklist_id,description) VALUES ('${subChecklistItems.name.replace(
              /'/g,
              "''",
            )}',${checkListResonse.insertId},'${subChecklistItems.description.replace(
              /'/g,
              "''",
            )}');`;
            const subChecklistItemsResponse = await entityManager.query(checklistItemsinsertQ);
            if (subChecklistItemsResponse) {
              serviceResponseData.serviceCheckListItemsResponse.push({
                type: 'Checklist Items',
                typeId: subChecklistItems.id,
                prodTypeId: subChecklistItemsResponse.insertId,
                refId: checklist.id,
              });
            } else {
              throw new Error(`Error in moving '${subChecklistItems.name}' to pre-production`);
            }
          }
        }
      }
    }

    if (data.stageOfWorks) {
      for (const stageOfWork of data.stageOfWorks) {
        const extraAttributes = JSON.stringify(stageOfWork.extraAttributes);
        const newStageofWork = `INSERT INTO vider.service_stageofwork (
            name,
            description,
            reference_number,
            reference_number_value,
            type,
            extra_attributes,
            attachment_file,
            service_id
          ) VALUES (
            '${stageOfWork.name.replace(/'/g, "''")}',
            '${stageOfWork.description.replace(/'/g, "''")}',
            ${stageOfWork.referenceNumber ? true : false},
            ${stageOfWork.referenceNumberValue || null},
            '${stageOfWork.type}',
            '${extraAttributes}',
            '${stageOfWork.attachmentFile}',
            ${savedServiceId[0].id}
          );`;
        const stageofworkResponse = await entityManager.query(newStageofWork);
        if (stageofworkResponse) {
          serviceResponseData.serviceStageofworkResponse.push({
            type: 'Stage of Work',
            typeId: stageOfWork.id,
            prodTypeId: stageofworkResponse.insertId,
          });
        } else {
          throw new Error(`Error in moving '${stageOfWork.name}' to pre-production`);
        }
      }
    }

    if (data.labels) {
      for (let serviceLabels of data.labels) {
        const labelActivityRecordCheck = await AtomSuperAdminActivity.findOne({
          where: { typeId: serviceLabels.id, type: 'Label' },
          order: { id: 'DESC' },
        });
        if (labelActivityRecordCheck) {
          const labelInsertQuery = `INSERT INTO vider.service_labels_label (label_id,service_id) VALUES (${labelActivityRecordCheck.prodTypeId}, ${savedServiceRecord.insertId});`;
          const labelInsertResponse = await entityManager.query(labelInsertQuery);
        }
      }
    }

    return serviceResponseData;
  }

  async updateAdminServiceInProd(data: any) {
    const serviceResponseData = {
      serviceRespone: [],
      serviceChecklistResponse: [],
      serviceCheckListItemsResponse: [],
      serviceMilestoneResponse: [],
      serviceStageofworkResponse: [],
    };

    const catActivityRecord = await AtomSuperAdminActivity.findOne({
      where: {
        type: 'Categories',
        typeId: data.category.id,
      },
      order: {
        id: 'DESC',
      },
    });
    const subCatActivityRecord = await AtomSuperAdminActivity.findOne({
      where: {
        type: 'Sub Categories',
        typeId: data.subCategory.id,
      },
      order: {
        id: 'DESC',
      },
    });
    const updatedServiceActivityRecord = await AtomSuperAdminActivity.findOne({
      where: {
        type: 'Services',
        typeId: data.id,
      },
      order: {
        id: 'DESC',
      },
    });

    const updateServiceQuery = `
    UPDATE vider.service
    SET
      name = ?,
      description = ?,
      default_one = ?,
      hourly_price = ?,
      total_price = ?,
      category_id = ?,
      sub_category_id = ?,
      organization_id = ?,
      client_sub_category = ?,
      from_admin = ?,
      admin_service_id = ?,
      recurring_frequency = ?,
      recurring_dates = ?,
      state = ?,
      version = ?,
      is_active = ?,
      linked_services = ?,
      subtask_services = ?,
      service_faqs = ?,
      service_procedure = ?,
      recurring_frequency_details = ?,
      is_recurring = ?,
      updated_at = ?
    WHERE id = ?;
  `;
    const entityManager = getManager();

    const updateServiceResponse = await entityManager.query(updateServiceQuery, [
      data.name,
      data.description,
      true,
      data.hourlyPrice,
      data.totalPrice,
      catActivityRecord.prodTypeId,
      subCatActivityRecord.prodTypeId || null,
      null,
      JSON.stringify(data.clientSubCategory),
      data.fromAdmin,
      data.adminServiceId,
      data.recurringFrequency,
      data.recurringDates,
      data.state,
      data.version,
      data.isActive,
      data.linkedServices ? JSON.stringify(data.linkedServices) : null,
      data.subtaskServices ? JSON.stringify(data.subtaskServices) : null,
      data.serviceFaqs ? JSON.stringify(data.serviceFaqs) : null,
      data.serviceProcedure ? JSON.stringify(data.serviceProcedure) : null,
      data.recurringFrequencyDetails ? JSON.stringify(data.recurringFrequencyDetails) : null,
      data.isRecurring,
      (data.updatedAt = new Date()),
      updatedServiceActivityRecord.prodTypeId,
    ]);

    if (updateServiceResponse) {
      serviceResponseData.serviceRespone.push({
        type: 'Services',
        typeId: data.id,
        prodTypeId: updatedServiceActivityRecord.prodTypeId,
      });
    } else {
      throw new Error(`Error in updating '${data.name}' Service with ${data.id}`);
    }

    // DELETE FUNCTIONALITY IN PROD FOR CHECKLIST
    const getProdChecklistsQuery = `SELECT * FROM vider.service_checklist where service_id= ${updatedServiceActivityRecord.prodTypeId};`;
    const prodChecklists = await entityManager.query(getProdChecklistsQuery);
    for (let prodChecklist of prodChecklists) {
      const checklistActivity = await AtomSuperAdminActivity.findOne({
        where: {
          type: 'Checklist',
          prodTypeId: prodChecklist.id,
        },
        order: {
          id: 'DESC',
        },
      });
      const checkForChecklist = data?.checklists?.find(
        (checklist) => checklist.id === checklistActivity?.typeId,
      );
      if (checkForChecklist === undefined) {
        const deleteQuery = `DELETE from vider.service_checklist where id= ${prodChecklist.id};`;
        await entityManager.query(deleteQuery);
      }

      // DELETE FUNCTIONALITY IN PROD FOR CHECKLIST ITEM
      const getProdChecklistItemsQuery = `SELECT * FROM vider.service_checklist_item where checklist_id = ${prodChecklist.id};`;
      const prodChecklistItems = await entityManager.query(getProdChecklistItemsQuery);

      for (const prodChecklistItem of prodChecklistItems) {
        const checklistItemActivity = await AtomSuperAdminActivity.findOne({
          where: {
            type: 'Checklist Items',
            prodTypeId: prodChecklistItem.id,
          },
          order: {
            id: 'DESC',
          },
        });
        const checklistItemExists = data.checklists.some((checklist) =>
          checklist.checklistItems.some((item) => item.id === checklistItemActivity?.typeId),
        );
        if (checklistItemExists === false) {
          const deleteChecklistItemQuery = `DELETE from vider.service_checklist_item where id = ${prodChecklistItem?.id}`;
          await entityManager.query(deleteChecklistItemQuery);
        }
      }
    }

    // Checklist Update Functionality
    if (data.checklists) {
      for (const checkList of data.checklists) {
        let checklistProdId: number;
        const checklistInAtomSuperAdminActivity = await AtomSuperAdminActivity.findOne({
          where: {
            type: 'Checklist',
            typeId: checkList.id,
          },
          order: {
            id: 'DESC',
          },
        });
        checklistProdId = checklistInAtomSuperAdminActivity?.prodTypeId;
        const escapedCheklistName = checkList.name.replace(/'/g, "''");
        if (
          checklistInAtomSuperAdminActivity &&
          moment(checklistInAtomSuperAdminActivity.lastUpdated).toISOString() < checkList.updatedAt
        ) {
          const updateQuery = `UPDATE vider.service_checklist
            SET
            name = '${escapedCheklistName}',
            updated_at = CURRENT_TIMESTAMP
            WHERE id = ${checklistInAtomSuperAdminActivity.prodTypeId};`;
          const updateProdChecklistResponse = await entityManager.query(updateQuery);
          if (updateProdChecklistResponse) {
            serviceResponseData.serviceChecklistResponse.push({
              type: 'Checklist',
              typeId: checkList.id,
              prodTypeId: checklistInAtomSuperAdminActivity.prodTypeId,
            });
          }
        } else if (checklistInAtomSuperAdminActivity === undefined) {
          const insertChecklist = `INSERT INTO vider.service_checklist (name,service_id) VALUES ('${escapedCheklistName}',${updatedServiceActivityRecord.prodTypeId});`;
          const insertChecklistInProdResponse = await entityManager.query(insertChecklist);
          checklistProdId = insertChecklistInProdResponse.insertId;
          if (insertChecklistInProdResponse) {
            serviceResponseData.serviceChecklistResponse.push({
              type: 'Checklist',
              typeId: checkList.id,
              prodTypeId: insertChecklistInProdResponse.insertId,
            });
          } else {
            throw new Error('Failed to insert checklist in production.');
          }
        }

        // Handle Checklist Items
        if (checkList.checklistItems.length > 0) {
          for (const checklistItem of checkList.checklistItems) {
            const { name, description } = checklistItem;
            const escapedCheklistItemName = name.replace(/'/g, "''");
            const escapedCheklistItemDescription = description.replace(/'/g, "''");

            const checklistItemInAtomSuperAdminActivity = await AtomSuperAdminActivity.findOne({
              where: {
                type: 'Checklist Items',
                typeId: checklistItem.id,
              },
              order: {
                id: 'DESC',
              },
            });
            if (
              checklistItemInAtomSuperAdminActivity &&
              moment(checklistItemInAtomSuperAdminActivity.lastUpdated).toISOString() <
                checklistItem.updatedAt
            ) {
              const updateQueryForChecklistItem = `UPDATE vider.service_checklist_item
                SET
                name = '${escapedCheklistItemName}',
                description = '${escapedCheklistItemDescription}',
                updated_at = CURRENT_TIMESTAMP
                WHERE id = ${checklistItemInAtomSuperAdminActivity.prodTypeId};`;
              const updateProdChecklistItemResponse = await entityManager.query(
                updateQueryForChecklistItem,
              );
              if (updateProdChecklistItemResponse) {
                serviceResponseData.serviceCheckListItemsResponse.push({
                  type: 'Checklist Items',
                  typeId: checklistItem.id,
                  prodTypeId: checklistItemInAtomSuperAdminActivity.prodTypeId,
                });
              }
            } else if (checklistItemInAtomSuperAdminActivity === undefined) {
              const insertChecklistItem = `INSERT INTO vider.service_checklist_item (name,description,checklist_id) VALUES ('${escapedCheklistItemName}','${description.replace(
                /'/g,
                "''",
              )}',${checklistProdId});`;
              const insertChecklistItemResponse = await entityManager.query(insertChecklistItem);
              if (insertChecklistItemResponse) {
                serviceResponseData.serviceCheckListItemsResponse.push({
                  type: 'Checklist Items',
                  typeId: checklistItem.id,
                  prodTypeId: insertChecklistItemResponse.insertId,
                });
              } else {
                throw new Error('Failed to insert checklist item in production.');
              }
            }
          }
        }
      }
    }

    //DELETE FUNCTIONALITY OF STAGE OF WORK (MILESTONES)//
    const existingSOWDataQuery = `SELECT * FROM vider.service_stageofwork WHERE service_id = ${updatedServiceActivityRecord.prodTypeId};`;
    const existingProdSOWDataResponse = await entityManager.query(existingSOWDataQuery);
    if (existingProdSOWDataResponse) {
      for (let existingStageofwork of existingProdSOWDataResponse) {
        const activityStageofworkRecord = await AtomSuperAdminActivity.findOne({
          where: { prodTypeId: existingStageofwork.id, type: 'Stage of Work' },
          order: { id: 'DESC' },
        });
        const checkForStageOfWork = data?.stageOfWorks?.find(
          (sow) => sow.id === activityStageofworkRecord?.typeId,
        );
        if (checkForStageOfWork === undefined) {
          const deleteQuery = `DELETE from vider.service_stageofwork where id= ${existingStageofwork.id};`;
          await entityManager.query(deleteQuery);
        }
      }
    }

    //UPDATE FUNCTIONALITY OF STAGE OF WORK (MILESTONES)
    for (let newStageofwork of data.stageOfWorks) {
      const activityRecordCheck = await AtomSuperAdminActivity.findOne({
        where: { typeId: newStageofwork.id, type: 'Stage of Work' },
        order: { id: 'DESC' },
      });
      if (
        activityRecordCheck &&
        moment(activityRecordCheck.lastUpdated).toISOString() < newStageofwork.updatedAt
      ) {
        const escapedName = newStageofwork.name.replace(/'/g, "''");
        const escapedDesc = newStageofwork.description.replace(/'/g, "''");
        const extraAttribute = newStageofwork.extraAttributes;
        const jsonied = JSON.stringify(extraAttribute);
        const updateStageofworkQuery = `UPDATE vider.service_stageofwork 
              SET 
              name = '${escapedName}',
              description = '${escapedDesc}', 
              reference_number = ${newStageofwork.reference_number ? true : false},
              reference_number_value = ${newStageofwork.reference_number_value || null},
              type = '${newStageofwork.type}',
              extra_attributes = '${jsonied}',
              attachment_file = ${newStageofwork.attachmentFile},
              updated_at = CURRENT_TIMESTAMP
              WHERE id = ${activityRecordCheck.prodTypeId};`;
        const updateStageofworkResponse = await entityManager.query(updateStageofworkQuery);
        if (updateStageofworkResponse) {
          serviceResponseData.serviceStageofworkResponse.push({
            type: 'Stage of Work',
            typeId: newStageofwork.id,
            prodTypeId: activityRecordCheck.prodTypeId,
          });
        }
      } else if (!activityRecordCheck) {
        let attachmentFileValue;

        if (newStageofwork.attachmentFile !== null && newStageofwork.attachmentFile !== undefined) {
          attachmentFileValue =
            newStageofwork.attachmentFile !== '' ? `'${newStageofwork.attachmentFile}'` : 'NULL';
        } else {
          attachmentFileValue = 'NULL';
        }
        const inserDataQuery = `INSERT INTO vider.service_stageofwork (name,description,reference_number,reference_number_value,type,extra_attributes,attachment_file,service_id) VALUES('${newStageofwork.name.replace(
          /'/g,
          "''",
        )}','${newStageofwork.description.replace(/'/g, "''")}',${
          newStageofwork.referenceNumber ? 'true' : 'false'
        },${newStageofwork.referenceNumberValue || null},'${newStageofwork.type}','${JSON.stringify(
          newStageofwork.extraAttributes,
        )}',${attachmentFileValue}, ${updatedServiceActivityRecord.prodTypeId});`;
        const insertResponse = await entityManager.query(inserDataQuery);
        serviceResponseData.serviceStageofworkResponse.push({
          type: 'Stage of Work',
          typeId: newStageofwork.id,
          prodTypeId: insertResponse.insertId,
        });
      }
    }

    //LABELS UPDATE//
    if (data.labels) {
      const labelsDeleteQuery = `DELETE FROM vider.service_labels_label WHERE service_id = ${updatedServiceActivityRecord.prodTypeId};`;
      const res = await entityManager.query(labelsDeleteQuery);
      for (let serviceLabels of data.labels) {
        const labelActivityRecordCheck = await AtomSuperAdminActivity.findOne({
          where: { typeId: serviceLabels.id, type: 'Label' },
          order: { id: 'DESC' },
        });
        if (labelActivityRecordCheck) {
          const labelInsertQuery = `INSERT INTO vider.service_labels_label (label_id,service_id) VALUES (${labelActivityRecordCheck.prodTypeId}, ${updatedServiceActivityRecord.prodTypeId});`;
          const labelInsertResponse = await entityManager.query(labelInsertQuery);
        }
      }
    }

    return serviceResponseData;
  }

  async createAdminServiceActivity(userId: number, data: CreateAtomSuperAdminActivityDto) {
    let user = await User.findOne({
      where: { id: userId },
      relations: ['organization'],
    });

    const { serviceResponse } = data;
    const responseTypes = Object.keys(serviceResponse);

    for (const responseType of responseTypes) {
      const responses = serviceResponse[responseType];

      for (const response of responses) {
        let atomSuperAdminActivity = new AtomSuperAdminActivity();

        atomSuperAdminActivity.type = response.type;
        atomSuperAdminActivity.typeId = response.typeId;
        atomSuperAdminActivity.updatedUserId = userId;
        atomSuperAdminActivity.toProduction = true;
        atomSuperAdminActivity.prodTypeId = response.prodTypeId;
        atomSuperAdminActivity.refId = response.refId;

        await atomSuperAdminActivity.save();
      }
    }
    return;
  }

  async createAdminRoleInProd(userId: number, data: any) {
    const entityManager = getManager();
    const checkQuery = `SELECT * FROM vider.role where name = "${data.name}" and default_one=true;`;
    const roleCheck = await entityManager.query(checkQuery);
    if (roleCheck && roleCheck.length > 0) throw new BadRequestException('Role already exists');

    const insertQuery = `
    INSERT INTO vider.role
    (
      name,
      description,
      default_one)
    VALUES
    (
      '${data.name}',
      '${data.description}',
      ${data.defaultOne});`;
    const response = await entityManager.query(insertQuery);
    return response;
  }

  async updateAdminRoleInProd(userId: number, data: any) {
    const entityManager = getManager();
    const existingRoleQuery = `SELECT * FROM vider.role where id = "${data.atomSuperAdminActivityProdTypeId}" and default_one=true;`;
    const existingRole = await entityManager.query(existingRoleQuery);
    const roleUpdateQuery = `
    UPDATE vider.role
    SET
      name= '${data.name}',
      description= '${data.description}'
    WHERE
      id = ${existingRole[0].id};`;
    const roleUpdateResponse = await entityManager.query(roleUpdateQuery);
    return roleUpdateResponse;
  }

  async createRolePermissionsInProd(prodId: number, data: any) {
    const entityManager = getManager();

    const rolePermissionResponse = [];
    data?.permissions?.forEach(async (eachPermission: any) => {
      const rolePermissionsQuery = `INSERT INTO 
      vider.role_permissions_permission
      (role_id,permission_id)
      VALUES(${prodId},${eachPermission.id});`;

      const response = await entityManager.query(rolePermissionsQuery);
      rolePermissionResponse.push(response);
    });
    return rolePermissionResponse;
  }

  async updateRolePermissionsInProd(prodId: number, data: any) {
    const entityManager = getManager();

    const deleteExistingRolePermissionsQuery = `DELETE FROM vider.role_permissions_permission 
      WHERE role_id = ${prodId};`;

    const deleteExistingRolePermissionsResponse = await entityManager.query(
      deleteExistingRolePermissionsQuery,
    );

    const rolePermissionResponse = [];
    data?.permissions?.forEach(async (eachPermission: any) => {
      const rolePermissionsQuery = `INSERT INTO 
      vider.role_permissions_permission
      (role_id,permission_id)
      VALUES(${prodId},${eachPermission.id});`;

      const response = await entityManager.query(rolePermissionsQuery);
      rolePermissionResponse.push(response);
    });
    return rolePermissionResponse;
  }

  async createAtomSuperAdminActivity(userId: number, data: CreateAtomSuperAdminActivityDto) {
    let user = await User.findOne({
      where: { id: userId },
      relations: ['organization'],
    });

    let atomSuperAdminActivity = new AtomSuperAdminActivity();
    atomSuperAdminActivity.type = data.type;
    atomSuperAdminActivity.typeId = data.id;
    atomSuperAdminActivity.updatedUserId = userId;
    atomSuperAdminActivity.toProduction = true;
    atomSuperAdminActivity.prodTypeId = data.prodTypeId;
    await atomSuperAdminActivity.save();

    return atomSuperAdminActivity;
  }

  async updateAtomSuperAdminActivity(userId: number, data: any) {
    let atomSuperAdminActivity = AtomSuperAdminActivity.findOne({
      where: { id: data?.atomSuperAdminActivityId },
    });
  }

  async getQuantumTemplate(id: number) {
    let quantumTemplate: any = await QtmTemplate.findOne({
      where: {
        id: id,
      },
    });
    return quantumTemplate;
  }

  async getQuantumTemplates() {
    let quantumTemplates = await QtmTemplate.find();
    return quantumTemplates;
  }

  async createQuantumTemplate(data: CreateQuantumTemplateDto) {
    const { metadata } = data;
    const updatedSections = (metadata as any).sections;

    const initialSeqData = updatedSections
      .reduce((acc: any, section: any) => {
        return [
          ...acc,
          ...section.metadataFields.map((eachQuestion: any) => {
            return {
              sequence: eachQuestion.sequence,
              fieldName: eachQuestion.fieldName,
            };
          }),
        ];
      }, [])
      .sort((a: any, b: any) => a.sequence - b.sequence);

    if (updatedSections && Array.isArray(updatedSections)) {
      updatedSections.forEach((section) => {
        if (section.metadataFields) {
          section.metadataFields.forEach((field: any) => {
            if (field.sequence) {
              delete field.sequence;
            }
          });
        }
      });
    }

    (metadata as any).sections = updatedSections;

    const newMetadata = {
      ...metadata,
      initialSequence: initialSeqData,
    };

    let quantumTemplateData = new QtmTemplate();
    quantumTemplateData.name = data.name;
    quantumTemplateData.templateCategoryId = data.templateCategoryId;
    quantumTemplateData.subCatId = data.subCatId;
    quantumTemplateData.labels = data.labels;
    quantumTemplateData.metadata = newMetadata;
    quantumTemplateData.description = data.description;
    quantumTemplateData.updatedBy = 'admin';
    quantumTemplateData.googleDrivelink = data.googleDrivelink;
    quantumTemplateData.version = 1;
    quantumTemplateData.excerpt = data.excerpt;
    quantumTemplateData.saved = false;
    quantumTemplateData.templateName = data.templateName;
    quantumTemplateData.price = data.price;
    quantumTemplateData.imageUrls = data.imageUrls;
    await quantumTemplateData.save();
    return quantumTemplateData;
  }

  async updateQuantumTemplate(id: number, data: CreateQuantumTemplateDto) {
    let quantumTemplateData = await QtmTemplate.findOne({ where: { id } });
    const { metadata } = data;
    const updatedSections = (metadata as any).sections;
    const initialSeqData = updatedSections
      .reduce((acc: any, section: any) => {
        return [
          ...acc,
          ...section.metadataFields.map((eachQuestion: any) => {
            return {
              sequence: eachQuestion.sequence,
              fieldName: eachQuestion.fieldName,
            };
          }),
        ];
      }, [])
      .sort((a: any, b: any) => a.sequence - b.sequence);

    if (updatedSections && Array.isArray(updatedSections)) {
      updatedSections.forEach((section) => {
        if (section.metadataFields) {
          section.metadataFields.forEach((field: any) => {
            if (field.sequence) {
              delete field.sequence;
            }
          });
        }
      });
    }

    (metadata as any).sections = updatedSections;

    const newMetadata = {
      ...metadata,
      initialSequence: initialSeqData,
    };

    if (!quantumTemplateData) {
      throw new NotFoundException('Template not found');
    }

    quantumTemplateData.name = data.name;
    quantumTemplateData.templateCategoryId = data.templateCategoryId;
    quantumTemplateData.subCatId = data.subCatId;
    quantumTemplateData.labels = data.labels;
    quantumTemplateData.metadata = newMetadata;
    quantumTemplateData.description = data.description;
    quantumTemplateData.updatedBy = 'admin';
    quantumTemplateData.googleDrivelink = data.googleDrivelink;
    quantumTemplateData.version = data.version + 1;
    quantumTemplateData.excerpt = data.excerpt;
    quantumTemplateData.saved = false;
    quantumTemplateData.templateName = data.templateName;
    quantumTemplateData.price = data.price;
    quantumTemplateData.imageUrls = data.imageUrls;
    await quantumTemplateData.save();
    return quantumTemplateData;
  }

  async deleteQuantumTemplate(id: number) {
    let quantumTemplateData = await QtmTemplate.findOne({ where: { id: id } });
    await quantumTemplateData.remove();
    return { success: true };
  }

  async getDocumentLabels() {
    let documentLabels = await QtmLabel.find();
    return documentLabels;
  }

  async createDocumentLabel(data: CreateDocumentLabelDto) {
    let documentLabel = new DocumentLabels();
    documentLabel.name = data.name;
    documentLabel.color = data.color;
    documentLabel.defaultOne = true;
    await documentLabel.save();
    return documentLabel;
  }

  async deleteDocumentLabel(id: number) {
    let documentLabel = await QtmLabel.delete(id);
    return documentLabel;
  }

  async getQuantumTemplateCategories() {
    let categories = await QtmCategories.find({
      relations: ['subCategories', 'parentCategory'],
      where: {
        defaultOne: true,
        parentCategory: IsNull(),
      },
    });
    return categories;
  }

  async deleteQuantumCategory(id: number) {
    let category = await QtmCategories.delete(id);
    return category;
  }

  async updateQuantumCategory(id: number, data: CreateQuantumCategoryDto) {
    let category = await QtmCategories.findOne({ where: { id } });
    category.name = data.name;
    category.subCategories = data.subCategories;
    category.version = category.version + 1;
    await category.save();
    return category;
  }

  async getQuantumTemplateSubCategories() {
    let quantumTemplateSubCategories = await QtmTemplateSubcat.find();
    return quantumTemplateSubCategories;
  }

  async createEvent(data: any, userId) {
    let event = new Event();
    event.title = data.title;
    event.date = data.date;
    event.startTime = data.startTime;
    event.endTime = data.endTime;
    event.notes = data.notes;
    event.location = data.location;
    event.reminder = data.reminder;
    event.defaultOne = true;
    event['userId'] = userId;
    event.eventType = data.eventType;
    await event.save();
    return event;
  }

  async getEvents(query: Date) {
    let events = createQueryBuilder(Event, 'event');
    if (query) {
      const startOfMonth = moment(`${query}`).startOf('month').format('YYYY-MM-DD');
      const endOfMonth = moment(query).endOf('month').format('YYYY-MM-DD');
      events
        .where(`Date(event.date) BETWEEN '${startOfMonth}' AND '${endOfMonth}'`)
        .andWhere('event.defaultOne=:true', { true: true });
    }
    return await events.getMany();
  }

  async getNewEvents() {
    const eventRepository = getRepository(Event);
    const newEvents = await eventRepository
      .createQueryBuilder('event')
      // .leftJoin(AtomSuperAdminActivity, 'atom', 'event.id = atom.typeId')
      .leftJoin(AtomSuperAdminActivity, 'atom', 'event.id = atom.typeId AND atom.type = :type', {
        type: 'Calendar Event',
      })
      .where(
        '(event.defaultOne = :defaultOne AND atom.typeId IS NULL) OR (event.defaultOne = :defaultOne AND event.updatedAt > (SELECT MAX(atom.lastUpdated) FROM atom_super_admin_activity atom WHERE atom.typeId = event.id))',
        { defaultOne: true },
      )
      // .andWhere("atom.type= 'Calendar Event'")
      .getMany();

    const uniqueTypeIds = await getManager()
      .createQueryBuilder(AtomSuperAdminActivity, 'atom')
      .select('DISTINCT(atom.typeId)', 'typeId')
      .where('atom.type = :type', { type: 'Calendar Event' })
      .getRawMany();

    const typeIdsArray = uniqueTypeIds.map((row) => row.typeId);
    const eventsWithSentToProduction = newEvents.map((event) => {
      const sentToProduction = typeIdsArray.includes(event.id);
      return { ...event, sentToProduction };
    });
    return eventsWithSentToProduction;
  }

  async createAdminEventInProd(data: any) {
    const serviceResponse = { eventResponse: [] };
    const escapedNotes = data.notes.replace(/'/g, "''");
    const insertEventToProd = `INSERT INTO vider.event
     (
      title, 
      date, 
      location, 
      notes, 
      type, 
      default_one
     )
     VALUES (
      '${data.title}',
      '${data.date}',
      '${data.location}',
      '${escapedNotes}',
      '${data.type}',
      ${true});
      `;
    const entityManager = getManager();
    const insertResponse = await entityManager.query(insertEventToProd);
    if (insertResponse) {
      serviceResponse.eventResponse.push({
        type: 'Calendar Event',
        typeId: data.id,
        prodTypeId: insertResponse.insertId,
      });
    }
    return serviceResponse;
  }

  async updateAdminEventInProd(data: any) {
    const serviceResponse = { updatedEventResponse: [] };
    const updatedEventProdTypeId = await AtomSuperAdminActivity.findOne({
      where: { typeId: data.id, type: 'Calendar Event' },
      order: { id: 'DESC' },
    });
    const escapedNotes = data.notes.replace(/'/g, "''");
    const updateEventQuery = `UPDATE vider.event SET
        title = '${data.title}',
        date = '${data.date}',
        location = '${data.location}',
        notes = '${escapedNotes}', 
        default_one = ${true}
        WHERE id = ${updatedEventProdTypeId.prodTypeId};
        `;
    const entityManager = getManager();
    const updateEventResponse = await entityManager.query(updateEventQuery);
    if (updateEventResponse) {
      serviceResponse.updatedEventResponse.push({
        type: 'Calendar Event',
        typeId: data.id,
        prodTypeId: updatedEventProdTypeId.prodTypeId,
      });
    }
    return serviceResponse;
  }

  async createForm(data: CreateFormDto) {
    try {
      let form = new this.formModel({
        ...data,
        defaultOne: true,
      });

      await form.save();

      return form;
    } catch (err) {
      console.log(err);
      throw new InternalServerErrorException(err);
    }
  }

  async cloneForm(id: string) {
    let form = await this.formModel.findById(id);

    let clonedForm = JSON.stringify(form, (key, value) => {
      if (key === '_id') {
        return undefined;
      }
      if (key === '__v') {
        return undefined;
      }
      return value;
    });

    let parsedForm = JSON.parse(clonedForm);
    let newForm = new this.formModel(parsedForm);

    await newForm.save();
    return newForm;
  }

  async getForms() {
    let forms = await this.formModel.find(
      {
        defaultOne: true,
      },
      {
        __v: 0,
        organizationId: 0,
      },
    );

    return forms;
  }

  async createFormValidation(data: CreateValidationDto) {
    const validation = new this.validationModel({
      ...data,
      defaultOne: true,
    });

    return validation.save();
  }

  async getFormValidations() {
    let data = await this.validationModel.find({
      defaultOne: true,
    });

    return data;
  }

  async getSuperAdminDashboard() {
    let organizations = await Organization.count();
    let totalUsers = await User.count({
      where: {
        isSuperUser: false,
      },
    });
    let tasks = await Task.count();
    let categories = await Category.count({
      where: {
        defaultOne: true,
      },
    });
    let services = await Service.count({
      defaultOne: true,
    });
    let labels = await Label.count({
      where: {
        defaultOne: true,
      },
    });
    let forms = await this.formModel.count({
      where: {
        defaultOne: true,
      },
    });

    const serviceRepository = getRepository(Service);
    const newServices = await serviceRepository
      .createQueryBuilder('service')
      .leftJoinAndSelect(AtomSuperAdminActivity, 'atom', 'service.id = atom.typeId')
      .where(
        '(service.defaultOne = :defaultOne AND atom.typeId IS NULL) OR (service.defaultOne = :defaultOne AND service.updatedAt > (SELECT MAX(atom.lastUpdated) FROM atom_super_admin_activity atom WHERE atom.typeId = service.id))',
        { defaultOne: true },
      )
      .andWhere("atom.type = 'Services'")
      .getMany();

    const uniqueTypeIds = await getManager()
      .createQueryBuilder(AtomSuperAdminActivity, 'atom')
      .select('DISTINCT(atom.typeId)', 'typeId')
      .where('atom.type = :type', { type: 'Services' })
      .getRawMany();

    const typeIdsArray = uniqueTypeIds.map((row) => row.typeId);
    const servicesWithSentToProduction = newServices.map((service) => {
      const sentToProduction = typeIdsArray.includes(service.id);
      return { ...service, sentToProduction };
    });

    let newCategories = await Category.createQueryBuilder('categories')
      .leftJoinAndSelect(AtomSuperAdminActivity, 'atom', 'categories.id = atom.typeId')
      .where(
        '(categories.defaultOne = :defaultOne AND atom.typeId IS NULL) OR (categories.defaultOne = :defaultOne AND categories.updatedAt > (SELECT MAX(atom.lastUpdated) FROM atom_super_admin_activity atom WHERE atom.typeId = categories.id))',
        { defaultOne: true },
      )
      .andWhere("atom.type = 'Categories'")
      .getMany();

    const uniqueCatTypeIds = await getManager()
      .createQueryBuilder(AtomSuperAdminActivity, 'atom')
      .select('DISTINCT(atom.typeId)', 'typeId')
      .where('atom.type = :type', { type: 'Categories' })
      .getRawMany();
    const catTypeIdsArray = uniqueCatTypeIds.map((row) => row.typeId);
    const categoriesWithSentToProduction = newCategories.map((category) => {
      const sentToProduction = catTypeIdsArray.includes(category.id);
      return { ...category, sentToProduction };
    });

    const eventRepository = getRepository(Event);
    const newEvents = await eventRepository
      .createQueryBuilder('event')
      .leftJoin(AtomSuperAdminActivity, 'atom', 'event.id = atom.typeId')
      .where(
        '(event.defaultOne = :defaultOne AND atom.typeId IS NULL) OR (event.defaultOne = :defaultOne AND event.updatedAt > (SELECT MAX(atom.lastUpdated) FROM atom_super_admin_activity atom WHERE atom.typeId = event.id))',
        { defaultOne: true },
      )
      .andWhere("atom.type= 'Calendar Event'")
      .getMany();

    const uniqueEventTypeIds = await getManager()
      .createQueryBuilder(AtomSuperAdminActivity, 'atom')
      .select('DISTINCT(atom.typeId)', 'typeId')
      .where('atom.type = :type', { type: 'Calendar Event' })
      .getRawMany();

    const eventTypeIdsArray = uniqueEventTypeIds.map((row) => row.typeId);
    const eventsWithSentToProduction = newEvents.map((event) => {
      const sentToProduction = eventTypeIdsArray.includes(event.id);
      return { ...event, sentToProduction };
    });

    const newLabels = await getConnection()
      .getRepository(Label)
      .createQueryBuilder('label')
      .where('label.default_one = :defaultOne', { defaultOne: true })
      .getMany();

    const typeIds = await getConnection()
      .getRepository(AtomSuperAdminActivity)
      .createQueryBuilder('atom_super_admin_activity')
      .where('atom_super_admin_activity.type = :activityType', { activityType: 'Label' })
      .getMany();

    const ids = typeIds.map((activity) => activity.typeId);
    const filteredServices = newLabels.filter((label) => !ids.includes(label.id));

    // Quantum count
    let organizationsQuantum = await createQueryBuilder(Organization, 'organization')
      .select(['organization.loggedIn'])
      .where('organization.loggedIn IS NOT NULL') // Check if loggedIn is not null
      .andWhere("JSON_UNQUOTE(JSON_EXTRACT(organization.loggedIn, '$.quantum')) = :quantum", {
        quantum: 'true',
      })
      .getCount();

    const quantumUserCount = await createQueryBuilder(User, 'user')
      .leftJoin('user.organization', 'organization') // Assuming there's a relationship between User and Organization
      .where('organization.loggedIn IS NOT NULL') // Check if loggedIn is not null
      .andWhere("JSON_UNQUOTE(JSON_EXTRACT(organization.loggedIn, '$.quantum')) = :quantum", {
        quantum: 'true',
      })
      .getCount();

    const createdDocuemntCount = await QtmActivity.count();
    const websiteDocumentsCount = await QtmActivity.count({
      where: {
        organizationId: 453,
      },
    });

    const qtmTemplates = await this.getNewQtmTemplates();
    const qtmCategories = await this.getNewQtmCategories();

    const totalPrismServices = await this.getAllServiceBlogDetails();

    return {
      totalOrganizations: organizations,
      totalUsers: totalUsers,
      totalTasks: tasks,
      totalCategories: categories,
      totalServices: services,
      totalLabels: labels,
      totalForms: forms,
      updatedServiceCount: servicesWithSentToProduction.filter(
        (service) => service.sentToProduction,
      ).length,
      newServiceCount: servicesWithSentToProduction.filter((service) => !service.sentToProduction)
        .length,
      updatedCategoryCount: categoriesWithSentToProduction.filter(
        (category) => category.sentToProduction,
      ).length,
      newCategoryCount: categoriesWithSentToProduction.filter(
        (category) => !category.sentToProduction,
      ).length,
      updatedEventCount: eventsWithSentToProduction.filter((event) => event.sentToProduction)
        .length,
      newEventCount: eventsWithSentToProduction.filter((event) => !event.sentToProduction).length,
      labelCount: filteredServices.length,
      organizationsQuantumCount: organizationsQuantum,
      quantumUserCount: quantumUserCount,
      docuemntCount: createdDocuemntCount,
      websiteDocumentsCount,
      updatedTemplateCount: qtmTemplates.filter((event) => event?.sentToProduction)?.length,
      newTemplateCount: qtmTemplates.filter((event) => !event?.sentToProduction)?.length,
      updatedCategoryCountQtm: qtmCategories.filter((event) => event?.sentToProduction)?.length,
      newCategoriesCountQtm: qtmCategories.filter((event) => !event?.sentToProduction)?.length,
      prismServicesLength: totalPrismServices.length,
    };
  }

  async getServiceBlogDetails(id: number) {
    let service = await createQueryBuilder(Service, 'service')
      .select([
        'service.id',
        'service.prismServiceName',
        'service.content',
        'service.linkedServices',
        'service.serviceReadTime',
        'service.prismYoutubeLink',
        'service.postIsActive',
        'service.prismChecklists',
        'service.prismPrice',
        'service.applicationType',
        'service.prismImage',
        'service.prismSampleCertificate',
        'service.prismProcessImage',
        'service.prismDescription',
      ])
      .where('service.id = :serviceId', { serviceId: id })
      .andWhere('service.applicationType = :applicationType', { applicationType: 'prism' })
      .andWhere('service.postIsActive = :postIsActive', { postIsActive: true })
      .getOne();
    return service;
  }

  async getAllServiceBlogDetails() {
    let service = await createQueryBuilder(Service, 'service')
      .select([
        'service.id',
        'service.prismServiceName',
        'service.content',
        'service.linkedServices',
        'service.serviceReadTime',
        'service.prismYoutubeLink',
        'service.postIsActive',
        'service.prismChecklists',
        'service.prismPrice',
        'service.applicationType',
        'service.prismImage',
        'service.prismSampleCertificate',
        'service.prismProcessImage',
        'service.prismDescription',
      ])
      .andWhere('service.applicationType = :applicationType', { applicationType: 'prism' })
      .andWhere('service.postIsActive = :postIsActive', { postIsActive: true })
      .getMany();
    return service;
  }
  async createQtmSuperAdminActivity(userId: number, data: CreateQtmSuperAdminActivityDto) {
    let user = await User.findOne({
      where: { id: userId },
      relations: ['organization'],
    });

    let qtmSuperAdminActivity = new QtmSuperAdminActivity();
    qtmSuperAdminActivity.type = data.type;
    qtmSuperAdminActivity.typeId = data.id;
    qtmSuperAdminActivity.updatedUserId = userId;
    qtmSuperAdminActivity.toProduction = true;
    qtmSuperAdminActivity.prodTypeId = data.prodTypeId;
    await qtmSuperAdminActivity.save();

    return qtmSuperAdminActivity;
  }

  async createQtmResponsesToQtmActivity(userId: number, data: CreateQtmSuperAdminActivityDto) {
    let user = await User.findOne({
      where: { id: userId },
      relations: ['organization'],
    });

    const { combinedResponses } = data;
    const responseTypes = Object.keys(combinedResponses);

    for (const responseType of responseTypes) {
      const responses = combinedResponses[responseType];

      for (const response of responses) {
        let atomSuperAdminActivity = new QtmSuperAdminActivity();

        atomSuperAdminActivity.type = response.type;
        atomSuperAdminActivity.typeId = response.typeId;
        atomSuperAdminActivity.updatedUserId = userId;
        atomSuperAdminActivity.toProduction = true;
        atomSuperAdminActivity.prodTypeId = response.prodTypeId;

        await atomSuperAdminActivity.save();
      }
    }
    return;
  }

  async createAdminQtmCategoryInProd(data: any) {
    const combinedResponses = {
      categoryResponse: [],
      subCategoryResponse: [],
    };
    let categoryInsertQuery = `INSERT INTO vider.qtm_categories 
      (id, name,
      default_one
      )
      VALUES (
        '${data.id}',
        '${data.name}',
        ${true}
      );`;
    const entityManager = getManager();
    const categoryInsertResponse = await entityManager.query(categoryInsertQuery);

    if (categoryInsertResponse) {
      combinedResponses.categoryResponse.push({
        type: 'Qtm Categories',
        typeId: data.id,
        prodTypeId: categoryInsertResponse.insertId,
      });
    } else {
      throw new Error(`Category '${data.name}' failed to Move to Production.`);
    }
    for (const subCategory of data.subCategories) {
      const subCategoryInsertQuery = `
        INSERT INTO vider.qtm_categories 
            (id,name,
              parent_category_id,
              default_one)
        VALUES 
            ('${subCategory.id}',
              '${subCategory.name}',
              ${categoryInsertResponse.insertId},
             ${false});
    `;
      const subCategoryInsertResponse = await entityManager.query(subCategoryInsertQuery);
      if (subCategoryInsertResponse) {
        combinedResponses.subCategoryResponse.push({
          type: 'Qtm Sub Categories',
          typeId: subCategory.id,
          prodTypeId: subCategoryInsertResponse.insertId,
        });
      } else {
        throw new Error(`Sub category '${subCategory.name}' failed to Move to Production.`);
      }
    }
    return combinedResponses;
  }

  async updateAdminQtmCategoryInProd(data: any) {
    const combinedResponses = {
      categoryResponse: [],
      subCategoryResponse: [],
    };
    const updatedCatProdTypeId = await QtmSuperAdminActivity.findOne({
      where: {
        typeId: data?.id,
        type: 'Qtm Categories',
      },
      order: {
        id: 'DESC',
      },
    });
    const updateCategoryQuery = `UPDATE vider.qtm_categories SET
  name = '${data?.name}',
  version =  ${data?.version}
  WHERE id = ${updatedCatProdTypeId?.prodTypeId};`;
    const entityManager = getManager();
    const updateResponse = await entityManager.query(updateCategoryQuery);
    if (updateResponse) {
      combinedResponses.categoryResponse.push({
        type: 'Qtm Categories',
        typeId: data.id,
        prodTypeId: updatedCatProdTypeId.prodTypeId,
      });
    } else {
      throw new Error(`Qtm Category with id ${updatedCatProdTypeId.prodTypeId} does not exist.`);
    }

    const prodSubCatQuery = `SELECT * FROM vider.qtm_categories WHERE parent_category_id = ${updatedCatProdTypeId.prodTypeId} ;`;
    const prodSubCatResponse = await entityManager.query(prodSubCatQuery);
    const prodSubCatIds = prodSubCatResponse.map((item) => item?.id);
    const preprodSubCatIds = data?.subCategories?.map((item) => item?.id);
    const subcatProdTypeQ = await QtmSuperAdminActivity.find({
      select: ['prodTypeId'],
      where: {
        type: 'Qtm Sub Categories',
        typeId: In(preprodSubCatIds),
      },
    });
    const subcatProdTypeIds = subcatProdTypeQ.map((item) => item.prodTypeId);
    const deletedSubCatIds = prodSubCatIds.filter((id) => !subcatProdTypeIds.includes(id));

    if (deletedSubCatIds?.length > 0) {
      for (let deletedId of deletedSubCatIds) {
        const deleteSubCatQuery = `DELETE FROM vider.qtm_categories WHERE id = ${deletedId};`;
        const deleteSubCatResponse = await entityManager.query(deleteSubCatQuery);
        if (deleteSubCatResponse) {
          combinedResponses.subCategoryResponse.push({
            type: 'Qtm Sub Categories',
            prodTypeId: deletedId,
          });
        }
      }
    }

    for (const subCategory of data.subCategories) {
      const updatedSubCatProdTypeId = await QtmSuperAdminActivity.findOne({
        where: {
          typeId: subCategory.id,
          type: 'Qtm Sub Categories',
        },
        order: {
          id: 'DESC',
        },
      });
      if (updatedSubCatProdTypeId) {
        const lastUpdatedDate = new Date(updatedSubCatProdTypeId.lastUpdated);
        const subCategoryDate = new Date(subCategory.updatedAt);
        if (updatedSubCatProdTypeId && lastUpdatedDate < subCategoryDate) {
          const subCategoryInsertQuery = `
          UPDATE vider.qtm_categories SET 
              name = '${subCategory.name}',
              default_one =   ${false},
              version =   ${subCategory.version}
          WHERE id = ${updatedSubCatProdTypeId.prodTypeId};`;
          const subCategoryInsertResponse = await entityManager.query(subCategoryInsertQuery);
          if (subCategoryInsertResponse) {
            combinedResponses.subCategoryResponse.push({
              type: 'Qtm Sub Categories',
              typeId: subCategory.id,
              prodTypeId: updatedSubCatProdTypeId.prodTypeId,
            });
          } else {
            throw new Error(
              `Qtm Sub Category  '${subCategory.name}' failed to Update to production.`,
            );
          }
        }
      }
      if (!updatedSubCatProdTypeId) {
        const subCategoryInsertQuery = `
        INSERT INTO vider.qtm_categories 
            (name,
              parent_category_id,
              default_one,
              version)
        VALUES 
            ('${subCategory.name}',
              ${updatedCatProdTypeId.prodTypeId},
             ${false},
             ${subCategory.version});
    `;
        const subCategoryInsertResponse = await entityManager.query(subCategoryInsertQuery);
        if (subCategoryInsertResponse) {
          combinedResponses.subCategoryResponse.push({
            type: 'Qtm Sub Categories',
            typeId: subCategory.id,
            prodTypeId: subCategoryInsertResponse.insertId,
          });
        } else {
          throw new Error(`Qtm Sub Category  '${subCategory.name}' failed to Move to production.`);
        }
      }
    }
    return combinedResponses;
  }

  async getNewQtmCategories() {
    let newCategories = await QtmCategories.createQueryBuilder('categories')
      .leftJoinAndSelect('categories.subCategories', 'subCategories')
      .leftJoinAndSelect('categories.parentCategory', 'parentCategory')
      .leftJoinAndSelect(QtmSuperAdminActivity, 'qtm', 'categories.id = qtm.typeId')
      .where(
        '(categories.defaultOne = :defaultOne AND qtm.typeId IS NULL) OR (categories.defaultOne = :defaultOne AND categories.updatedAt > (SELECT MAX(qtm.lastUpdated) FROM qtm_super_admin_activity qtm WHERE qtm.typeId = categories.id))',
        { defaultOne: true },
      )
      .andWhere("qtm.type = 'Qtm Categories'")
      .getMany();

    const uniqueTypeIds = await getManager()
      .createQueryBuilder(QtmSuperAdminActivity, 'qtm')
      .select('DISTINCT(qtm.typeId)', 'typeId')
      .where('qtm.type = :type', { type: 'Qtm Categories' })
      .getRawMany();

    const typeIdsArray = uniqueTypeIds.map((row) => row.typeId);
    const categoriesWithSentToProduction = newCategories.map((category) => {
      const sentToProduction = typeIdsArray.includes(category.id);
      return { ...category, sentToProduction };
    });
    return categoriesWithSentToProduction;
  }

  async createAdminQtmTemplateInProd(data: any) {
    const qtmTemplateInsertQuery = `
        INSERT INTO vider.qtm_template 
        (
            name,
            template_category_id,
            labels,
            metadata,
            description,
            updated_by,
            google_drivelink,
            version,
            excerpt,
            saved,
            template_name,
            sub_cat_id,
            image_urls,
            price
        )
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?);
    `;

    const entityManager = getManager();
    const qtmTemplateInsertResponse = await entityManager.query(qtmTemplateInsertQuery, [
      data.name,
      data.templateCategoryId,
      JSON.stringify(data.labels),
      JSON.stringify(data.metadata),
      data.description?.replace(/'/g, "''"), // Escape single quotes
      data.updatedBy,
      data.googleDrivelink,
      data.version,
      data.excerpt?.replace(/'/g, "''"), // Escape single quotes
      data.saved,
      data.templateName,
      data.subCatId,
      JSON.stringify(data.imageUrls),
      data.price,
    ]);

    return qtmTemplateInsertResponse;
  }

  async updateAdminQtmTemplateInProd(data: any) {
    const updatedTemplateProdTypeId = await QtmSuperAdminActivity.findOne({
      where: {
        typeId: data?.id,
        type: 'Qtm Templates',
      },
      order: {
        id: 'DESC',
      },
    });

    const qtmTemplateUpdateQuery = `
      UPDATE vider.qtm_template
      SET
          name = ?,
          template_category_id = ?,
          labels = ?,
          metadata = ?,
          description = ?,
          updated_by = ?,
          google_drivelink = ?,
          version = ?,
          excerpt = ?,
          saved = ?,
          template_name = ?,
          sub_cat_id = ?,
          image_urls = ?,
          price = ?
      WHERE id = ?;
  `;

    const entityManager = getManager();
    const qtmTemplateUpdateResponse = await entityManager.query(qtmTemplateUpdateQuery, [
      data.name,
      data.templateCategoryId,
      JSON.stringify(data.labels),
      JSON.stringify(data.metadata),
      data.description?.replace(/'/g, "''"), // Escape single quotes
      data.updatedBy,
      data.googleDrivelink,
      data.version,
      data.excerpt?.replace(/'/g, "''"), // Escape single quotes
      data.saved,
      data.templateName,
      data.subCatId,
      JSON.stringify(data.imageUrls),
      data.price,
      updatedTemplateProdTypeId?.prodTypeId,
    ]);

    return {
      prodTypeId: updatedTemplateProdTypeId?.prodTypeId,
      ...qtmTemplateUpdateResponse,
    };
  }

  async getNewQtmTemplates() {
    let newQtmTemplates = await QtmTemplate.createQueryBuilder('qtmTemplates')
      .leftJoinAndSelect(QtmSuperAdminActivity, 'qtm', 'qtmTemplates.id = qtm.typeId')
      .where(
        '(qtm.typeId IS NULL) OR (qtmTemplates.lastUpdated > (SELECT MAX(qtm.lastUpdated) FROM qtm_super_admin_activity qtm WHERE qtm.typeId = qtmTemplates.id))',
      )
      .andWhere("qtm.type = 'Qtm Templates'")
      .getMany();
    const uniqueTypeIds = await getManager()
      .createQueryBuilder(QtmSuperAdminActivity, 'qtm')
      .select('DISTINCT(qtm.typeId)', 'typeId')
      .where('qtm.type = :type', { type: 'Qtm Templates' })
      .getRawMany();

    const typeIdsArray = uniqueTypeIds.map((row) => row.typeId);
    const templatesWithSentToProduction = newQtmTemplates.map((template) => {
      const sentToProduction = typeIdsArray.includes(template.id);
      return { ...template, sentToProduction };
    });
    return templatesWithSentToProduction;
  }

  async getPosters(query) {
    const qb = Storage.createQueryBuilder('storage');

    if (query.eventType === 'DD') {
      qb.where('storage.event_id = :type', { type: query.type });
    } else {
      qb.where('storage.poster_events_id = :type', { type: query.type });
    }

    const posters = await qb.getMany();
    return posters;
  }

  async getNewPosterEvents() {
    const posterEventsRepository = getRepository(PosterEvents);

    const newPosterEvents = await posterEventsRepository
      .createQueryBuilder('posterEvents')
      .leftJoinAndSelect('posterEvents.posterEventTypes', 'posterEventTypes')
      .leftJoin(
        AtomSuperAdminActivity,
        'atom',
        'posterEvents.id = atom.typeId AND atom.type = :type',
        {
          type: 'Poster Events',
        },
      )
      // .leftJoinAndSelect('posterEvents.storage','storage')
      .where(
        'atom.typeId IS NULL OR posterEvents.updatedAt > (' +
          'SELECT MAX(atom.lastUpdated) FROM atom_super_admin_activity atom ' +
          'WHERE atom.typeId = posterEvents.id AND atom.type = :type)',
        { type: 'Poster Events' },
      )
      .getMany();

    const uniqueTypeIds = await getManager()
      .createQueryBuilder(AtomSuperAdminActivity, 'atom')
      .select('DISTINCT(atom.typeId)', 'typeId')
      .where('atom.type = :type', { type: 'Poster Events' })
      .getRawMany();

    const typeIdsArray = uniqueTypeIds.map((row) => row.typeId);
    const posterEventsWithSentToProduction = newPosterEvents.map((posterEvent) => {
      const sentToProduction = typeIdsArray.includes(posterEvent.id);
      return { ...posterEvent, sentToProduction };
    });
    console.log('uniqueTypeIds', uniqueTypeIds);

    return posterEventsWithSentToProduction;
  }

  async getNewPosterImages() {
    try {
      const type = 'Poster Image';
      const storageRepo = getRepository(Storage);

      const newPosterImages = await storageRepo
        .createQueryBuilder('storage')
        .leftJoinAndSelect('storage.posterEvents', 'posterEvents')
        .leftJoinAndSelect('posterEvents.posterEventTypes', 'posterEventTypes')
        .leftJoinAndSelect('storage.event', 'event')
        .leftJoin(
          AtomSuperAdminActivity,
          'atom',
          'atom.typeId = storage.id AND atom.type = :type',
          { type },
        )
        .where('(posterEvents.id IS NOT NULL OR event.id IS NOT NULL)')
        .andWhere('storage.storageSystem = :storageSystem', { storageSystem: 'AMAZON' })
        .andWhere('atom.typeId IS NULL')
        .getMany();

      return newPosterImages.map((posterImg) => ({
        ...posterImg,
        sentToProduction: false,
      }));
    } catch (error) {
      console.error('Error in getNewPosterImages:', error);
      return [];
    }
  }

  async createAdminPosterEventsInProd(data: { id: number }) {
    const serviceResponse = { posterEventsResponse: [] };

    const preprodEvent = await PosterEvents.findOne({
      where: { id: data.id },
      relations: ['posterEventTypes'],
    });

    if (!preprodEvent) throw new NotFoundException('Poster Event not found in preprod');

    const [prodEventType] = await getManager().query(
      `SELECT id FROM vider.poster_event_types WHERE name = ?`,
      [preprodEvent.posterEventTypes.name],
    );

    if (!prodEventType) {
      throw new NotFoundException('Matching Event Type not found in production');
    }

    const escapedName = preprodEvent.name.replace(/'/g, "''");

    const insertQuery = `
    INSERT INTO vider.poster_events (name, poster_event_types_id, created_at, updated_at)
    VALUES ('${escapedName}', ${prodEventType.id}, NOW(), NOW());
  `;

    const entityManager = getManager();
    const insertResponse = await entityManager.query(insertQuery);

    if (insertResponse) {
      serviceResponse.posterEventsResponse.push({
        type: 'Poster Events',
        typeId: preprodEvent.id,
        prodTypeId: insertResponse.insertId,
      });
    }

    return serviceResponse;
  }

  async updateAdminPosterEventsInProd(data: { id: number }) {
    const serviceResponse = { updatedPosterEventsResponse: [] };

    const preprodEvent = await PosterEvents.findOne({
      where: { id: data.id },
      relations: ['posterEventTypes'],
    });

    if (!preprodEvent) throw new NotFoundException('Poster Event not found in preprod');

    const existingProdRecord = await AtomSuperAdminActivity.findOne({
      where: { typeId: data.id, type: 'Poster Events' },
      order: { id: 'DESC' },
    });

    if (!existingProdRecord) return serviceResponse;

    const [prodEventType] = await getManager().query(
      `SELECT id FROM vider.poster_event_types WHERE name = ?`,
      [preprodEvent.posterEventTypes.name],
    );

    if (!prodEventType) {
      throw new NotFoundException('Matching Event Type not found in production');
    }

    const escapedName = preprodEvent.name.replace(/'/g, "''");

    const updateQuery = `
    UPDATE vider.poster_events SET
      name = '${escapedName}',
      poster_event_types_id = ${prodEventType.id},
      updated_at = NOW()
    WHERE id = ${existingProdRecord.prodTypeId};
  `;

    const entityManager = getManager();
    const updateResponse = await entityManager.query(updateQuery);

    if (updateResponse) {
      serviceResponse.updatedPosterEventsResponse.push({
        type: 'Poster Events',
        typeId: preprodEvent.id,
        prodTypeId: existingProdRecord.prodTypeId,
      });
    }

    return serviceResponse;
  }

  async saveAttachments(files: Express.Multer.File[], selectedType: string, eventType: string) {
    let errors: string[] = [];
    let attachments: Storage[] = [];

    // Fetch appropriate event based on eventType
    let existingPosterEvent: PosterEvents | null = null;
    let existingEvent: Event | null = null;

    if (eventType === 'DD') {
      existingEvent = await Event.findOne({ where: { id: selectedType } });
      if (!existingEvent) {
        throw new Error(`Event with ID ${selectedType} not found`);
      }
    } else {
      existingPosterEvent = await PosterEvents.findOne({ where: { id: selectedType } });
      if (!existingPosterEvent) {
        throw new Error(`PosterEvent with ID ${selectedType} not found`);
      }
    }

    for (let file of files) {
      const { buffer, mimetype, originalname, size } = file;

      // Check for duplicates based on eventType
      const existingAttachment = await Storage.find({
        where:
          eventType === 'DD'
            ? { name: originalname, event: selectedType }
            : { name: originalname, posterEvents: selectedType },
      });

      if (existingAttachment?.length) {
        errors.push(`File name ${originalname} already exists`);
        continue;
      }

      let key = `storage/admin/-${moment().valueOf()}/${originalname}`;
      let upload: any = await this.uploadService.upload(buffer, key, mimetype);

      let storage = new Storage();
      storage.name = originalname;
      storage.file = upload.Key;
      storage.fileSize = size;
      storage.fileType = mimetype;
      storage.type = StorageType.FILE;
      storage.storageSystem = StorageSystem.AMAZON;

      // Assign to appropriate relation
      if (eventType === 'DD') {
        storage.event = existingEvent;
      } else {
        storage.posterEvents = existingPosterEvent;
      }

      attachments.push(storage);
    }

    await Storage.save(attachments);
    return attachments;
  }

  async getPosterEventTypes(userId: number, query: any) {
    const poster = await createQueryBuilder(PosterEventTypes, 'posterEventTypes')
      .select(['posterEventTypes.id', 'posterEventTypes.name'])
      .getMany();

    return poster;
  }

  async createPosterEventType(userId: number, body: PosterEventTypesDto) {
    let existingEventType = await PosterEventTypes.findOne({
      where: { name: body.name },
    });

    if (existingEventType) {
      throw new Error('Event Type already exists');
    }

    let posterEventType = new PosterEventTypes();
    posterEventType.name = body.name;

    await posterEventType.save();
    return posterEventType;
  }

  async updatePosterEventType(id: number, data: PosterEventTypesDto) {
    let posterEventType = await PosterEventTypes.findOne({ where: { id } });
    posterEventType.name = data.name;

    await posterEventType.save();

    return posterEventType;
  }

  async deletePosterEventTypes(id: number) {
    let posterEventType = await PosterEventTypes.findOne({ where: { id } });
    if (!posterEventType) {
      throw new Error('Event Type not found');
    }

    await posterEventType.remove();
    return { message: 'Event Type deleted successfully' };
  }

  async getNewPosterEventTypes() {
    const posterEventTypesRepository = getRepository(PosterEventTypes);
    const newPosterEventTypes = await posterEventTypesRepository
      .createQueryBuilder('posterEventTypes')
      // .leftJoin(AtomSuperAdminActivity, 'atom', 'event.id = atom.typeId')
      .leftJoin(
        AtomSuperAdminActivity,
        'atom',
        'posterEventTypes.id = atom.typeId AND atom.type = :type',
        {
          type: 'Poster Event Types',
        },
      )
      .where(
        'atom.typeId IS NULL OR posterEventTypes.updatedAt > (' +
          'SELECT MAX(atom.lastUpdated) FROM atom_super_admin_activity atom ' +
          'WHERE atom.typeId = posterEventTypes.id AND atom.type = :type)',
        { type: 'Poster Event Types' },
      )
      .getMany();

    const uniqueTypeIds = await getManager()
      .createQueryBuilder(AtomSuperAdminActivity, 'atom')
      .select('DISTINCT(atom.typeId)', 'typeId')
      .where('atom.type = :type', { type: 'Poster Event Types' })
      .getRawMany();

    const typeIdsArray = uniqueTypeIds.map((row) => row.typeId);
    const posterEventTypesWithSentToProduction = newPosterEventTypes.map((posterEventTypes) => {
      const sentToProduction = typeIdsArray.includes(posterEventTypes.id);
      return { ...posterEventTypes, sentToProduction };
    });
    return posterEventTypesWithSentToProduction;
  }

  async createAdminPosterEventTypesInProd(data: any) {
    const serviceResponse = { posterEventTypesResponse: [] };
    const escapedName = data.name.replace(/'/g, "''");

    const insertPosterEventTypesToProd = `
    INSERT INTO vider.poster_event_types (name)
    VALUES ('${escapedName}');
  `;

    const entityManager = getManager();
    const insertResponse = await entityManager.query(insertPosterEventTypesToProd);

    if (insertResponse) {
      serviceResponse.posterEventTypesResponse.push({
        type: 'Poster Event Types',
        typeId: data.id,
        prodTypeId: insertResponse.insertId,
      });
    }

    return serviceResponse;
  }

  async updateAdminPosterEventTypesInProd(data: any) {
    const serviceResponse = { updatedPosterEventTypesResponse: [] };

    const updatedPosterEventTypesProdTypeId = await AtomSuperAdminActivity.findOne({
      where: { typeId: data.id, type: 'Poster Event Types' },
      order: { id: 'DESC' },
    });

    if (!updatedPosterEventTypesProdTypeId) return serviceResponse;

    const escapedName = data.name.replace(/'/g, "''");

    const updatePosterEventTypesQuery = `
    UPDATE vider.poster_event_types SET
      name = '${escapedName}'
    WHERE id = ${updatedPosterEventTypesProdTypeId.prodTypeId};
  `;

    const entityManager = getManager();
    const updatePosterEventTypesResponse = await entityManager.query(updatePosterEventTypesQuery);

    if (updatePosterEventTypesResponse) {
      serviceResponse.updatedPosterEventTypesResponse.push({
        type: 'Poster Event Types',
        typeId: data.id,
        prodTypeId: updatedPosterEventTypesProdTypeId.prodTypeId,
      });
    }

    return serviceResponse;
  }

  async getPosterEvents(userId: number, query: any) {
    const posterEventTypes = await createQueryBuilder(PosterEventTypes, 'posterEventTypes')
      .leftJoinAndSelect('posterEventTypes.events', 'events')
      .getMany();

    // Fetch events from `events` table where they should be shown under PosterEventTypes with name = Due Dates
    const matchedEvents = await createQueryBuilder(Event, 'event')
      .where('event.defaultOne = :default', { default: true })
      .select(['event.id AS id', 'event.title AS name', 'event.date AS date'])
      .getRawMany();

    // Find PosterEventType with name = Due Dates
    const dueDatesType = posterEventTypes.find((type) => type.name === 'Due Dates');

    if (dueDatesType) {
      // Override the events for name = Due Dates with matched `events` table entries
      (dueDatesType as any).events = matchedEvents.map((e) => {
        const dummyEvent = new PosterEvents();
        dummyEvent.id = e.id;
        dummyEvent.name = e.name;
        dummyEvent.date = e.date;
        return dummyEvent;
      });
    }

    return posterEventTypes;
  }

  // Create Poster Event
  async createPosterEvent(userId: number, body: PosterEventsDto) {
    const posterEventType = await PosterEventTypes.findOne({
      where: { id: body.posterEventTypeId },
    });

    if (!posterEventType) {
      throw new NotFoundException('Event type not found');
    }

    let posterEvent = new PosterEvents();
    posterEvent.name = body.name;
    posterEvent.date = body.date;
    posterEvent.posterEventTypes = posterEventType;

    await posterEvent.save();
    return posterEvent;
  }

  // Update Poster Event
  async updatePosterEvent(id: number, data: PosterEventsDto) {
    let posterEvent = await PosterEvents.findOne({ where: { id: id } });

    if (!posterEvent) {
      throw new NotFoundException('Event not found');
    }

    const posterEventType = await PosterEventTypes.findOne({
      where: { id: data.posterEventTypeId },
    });

    if (!posterEventType) {
      throw new NotFoundException('Event type not found');
    }

    posterEvent.name = data.name;
    posterEvent.date = data.date;
    posterEvent.posterEventTypes = posterEventType;

    await posterEvent.save();
    return posterEvent;
  }

  // Delete Poster Event
  async deletePosterEvents(id: number) {
    let posterEvent = await PosterEvents.findOne({ where: { id } });
    if (!posterEvent) {
      throw new NotFoundException('Event Type not found');
    }

    await posterEvent.remove();
    return { message: 'Event deleted successfully' };
  }

  async updateAdminRole(id: number, data) {
    const { permissions, name, active, description } = data;
    const role = await Role.findOne({ where: { id, defaultOne: true } });
    role.name = name;
    role.active = active;
    role.description = description;

    let existingRole = await Role.findOne({
      where: { name, defaultOne: true },
    });

    if (existingRole) throw new BadRequestException('Role already exists');

    if (permissions && permissions.length) {
      let perms = await Permission.find({ where: { id: In(permissions) } });
      role.permissions = perms;
    }
    await role.save();
    return role;
  }

  async createNewPosterImageInProd(data, userId) {
    const { posterEvents, event } = data;

    if (!posterEvents?.id && !event?.id) {
      throw new NotFoundException('Either Poster Event or Event must be present');
    }

    const entityManager = getManager();

    let activityRecord;

    if (posterEvents?.id) {
      activityRecord = await AtomSuperAdminActivity.findOne({
        where: { type: 'Poster Events', typeId: posterEvents.id },
        order: { id: 'DESC' },
      });
    } else if (event?.id) {
      activityRecord = await AtomSuperAdminActivity.findOne({
        where: { type: 'Calendar Event', typeId: event.id },
        order: { id: 'DESC' },
      });
    }

    if (activityRecord?.prodTypeId) {
      const isDueDates = posterEvents?.posterEventTypes?.name === 'Due Dates';
      const linkColumn = isDueDates || !!event?.id ? 'event_id' : 'poster_events_id';

      const storageInsertQuery = `
      INSERT INTO vider.storage (
        name, type, file_type, file, file_size, \`show\`, storage_system, ${linkColumn}, created_at, updated_at
      )
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW());
    `;

      const insertparams = [
        data.name,
        data.type,
        data.fileType,
        data.file,
        data.fileSize,
        data.show,
        data.storageSystem,
        activityRecord.prodTypeId,
      ];

      const insertRes = await entityManager.query(storageInsertQuery, insertparams);

      if (insertRes?.insertId) {
        const superAdminActivity = new AtomSuperAdminActivity();
        superAdminActivity.prodTypeId = insertRes.insertId;
        superAdminActivity.typeId = data?.id;
        superAdminActivity.type = 'Poster Image';
        superAdminActivity.toProduction = true;
        superAdminActivity.updatedUserId = userId;
        await superAdminActivity.save();
      }
    } else {
      const missingName = posterEvents?.name || event?.title || 'Unknown';
      throw new NotFoundException(`Poster Event or Event: "${missingName}" doesn't exist in Prod`);
    }
  }

  async createEmailTemplate(data: any, userId: any) {
    let broadcastEmailTemplates = new BroadcastEmailTemplates();
    broadcastEmailTemplates.title = data.title;
    broadcastEmailTemplates.label = data.tags;
    broadcastEmailTemplates.content = data.content;
    broadcastEmailTemplates.subject = data.subject;
    broadcastEmailTemplates.default = 1;

    await broadcastEmailTemplates.save();
    return broadcastEmailTemplates;
  }

  async getEmailTemplates(userId: string, query: any) {
    const broadcastEmailTemplatesQuery = BroadcastEmailTemplates.createQueryBuilder(
      'broadcastEmailTemplates',
    )
      .leftJoinAndSelect('broadcastEmailTemplates.label', 'label')
      .where('broadcastEmailTemplates.default = :default', { default: 1 })

      .orderBy('broadcastEmailTemplates.createdAt', 'DESC');

    // Add the search filter if provided
    if (query.search) {
      broadcastEmailTemplatesQuery.andWhere('broadcastEmailTemplates.title LIKE :search', {
        search: `%${query.search}%`,
      });
    }

    let emailTemplates = await broadcastEmailTemplatesQuery.getMany();

    // Extract unique labels from the email templates (assuming they are strings)
    const templateLabels = emailTemplates
      .map((template) => template.labels) // Get the label from each template
      .filter((label) => !!label); // Filter out any undefined or null labels

    if (templateLabels.length === 0) {
      return emailTemplates; // Return early if no labels are found
    }
    const labelColors = await Label.createQueryBuilder('label')

      .andWhere('label.name IN (:...templateLabels)', { templateLabels }) // Match template labels with label names
      .getMany();

    // Format the result by including the label color in the template response
    const formattedTemplates = emailTemplates.map((template) => {
      const matchingLabel = labelColors.find((label) => label.name === template.labels); // Find the matching label by name
      return {
        ...template,
        labelColor: matchingLabel ? matchingLabel.color : null, // Add the color if there's a match
      };
    });

    return formattedTemplates;
  }

  async getOneEmailTemplate(userId, id) {
    // Fetch the user with their organization relation
    // let user = await User.findOne({
    //   where: { id: userId },
    //   // relations: ['organization'],
    // });
    const getOneEmailTemplate = await BroadcastEmailTemplates.createQueryBuilder('EmailTemplate')
      // .leftJoinAndSelect('EmailTemplate.organization', 'organization')
      .leftJoinAndSelect('EmailTemplate.label', 'label')

      .where('EmailTemplate.id = :id', { id: id })
      .getOne();
    // if (getOneEmailTemplate?.organization?.id !== user?.organization?.id) {
    //   return undefined; // Return undefined if the organization does not match
    // }
    return getOneEmailTemplate;
  }

  async updateEmailTemplate(userId, id, body) {
    // Find the user and their organization
    // let user = await User.findOne({
    //   where: { id: userId },
    //   // relations: ['organization'],
    // });

    // if (!user) {
    //   throw new Error('User not found');
    // }

    // Find the ClientGroupBroadcast with the given id
    let emailTemplateData = await BroadcastEmailTemplates.findOne({
      where: { id: id },
      // relations: ['organization'],
    });

    if (!emailTemplateData) {
      throw new Error('emailTemplateData not found');
    }
    // const existingGroup = await BroadcastEmailTemplates.findOne({
    //   where: {
    //     title: body.title,
    //     // organization: { id: user.organization.id }, // Ensure it's within the same organization
    //     id: Not(id),
    //   },
    // });

    // If the group name exists, throw an error
    // if (existingGroup) {
    //   throw new BadRequestException('Email Title already exists');
    // }
    // Update the ClientGroupBroadcast with the new data
    emailTemplateData.title = body.title;
    emailTemplateData.labels = body.tags;
    emailTemplateData.content = body.content;
    emailTemplateData.label = body.tags;
    emailTemplateData.subject = body.subject;
    emailTemplateData.default = 1;

    // clientGroupData.clients = body.clients;
    // clientGroupData.organization = user.organization;

    // Save the updated ClientGroupBroadcast
    await emailTemplateData.save();

    return emailTemplateData;
  }
  async deleteEmailTemplate(ids: number, userId) {
    let clienGroup = await BroadcastEmailTemplates.delete(ids);
    return clienGroup;
  }
}
