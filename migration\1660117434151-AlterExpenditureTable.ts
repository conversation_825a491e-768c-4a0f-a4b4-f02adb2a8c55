import { MigrationInterface, QueryRunner } from 'typeorm';

export class AlterExpenditureTable1660117434151 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
        ALTER TABLE task_expenditure
        RENAME to expenditure,
        RENAME COLUMN type to task_expense_type,
        ADD COLUMN type enum('GENERAL','TASK') not null,
        ADD COLUMN client_id int null,
        ADD FOREIGN KEY (client_id) REFERENCES client(id) ON DELETE SET NULL
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}
