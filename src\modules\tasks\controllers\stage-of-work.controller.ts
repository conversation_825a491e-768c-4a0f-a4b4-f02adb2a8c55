import {
  Body,
  Delete,
  Get,
  Param,
  ParseIntPipe,
  Post,
  Put,
  Query,
  UseGuards,
  Request,

} from '@nestjs/common';
import AddStageOfWorkDto from '../dto/add-stage-of-work.dto';
import { TasksController } from './task.controller';
import { JwtAuthGuard } from 'src/modules/users/jwt/jwt-auth.guard';

export class StageOfWorkController extends TasksController {

  @UseGuards(JwtAuthGuard)
  @Post('/:taskId/stage-of-work')
  addMilestone(
    @Request() req: any,
    @Body() body: AddStageOfWorkDto,
    @Param('taskId', ParseIntPipe) taskId: number,
  ) {
    const { userId } = req.user;
    return this.stageOfWorkService.addStageOfWork(taskId, userId, body);
  }

  @Get('/stage-of-work')
  getMilestones(@Query('taskId', ParseIntPipe) taskId: number) {
    return this.stageOfWorkService.getStageOfWork(taskId);
  }

  @Get('/stage-of-work/collect-data')
  getCollectDataStageOfWork(@Query('taskId', ParseIntPipe) taskId: number) {
    return this.stageOfWorkService.getCollectDataStageOfWork(taskId);
  }


  @UseGuards(JwtAuthGuard)
  @Put('/stage-of-work/:id')
  updateMilestone(
    @Request() req: any,
    @Body() body: AddStageOfWorkDto,
    @Param('id', ParseIntPipe) id: number,
  ) {
    const { userId } = req.user;
    return this.stageOfWorkService.updateStageOfWork(id, userId, body);
  }

  @UseGuards(JwtAuthGuard)
  @Delete('/stage-of-work/:id')
  deleteMilestone(@Param('id', ParseIntPipe) id: number, @Request() req: any, @Query() query) {
    const { userId } = req.user;
    return this.stageOfWorkService.deleteStageOfWork(id, userId, query);
  }
}
