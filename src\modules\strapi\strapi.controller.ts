import { StrapiService } from './strapi.service';
import {
  Body,
  Controller,
  Delete,
  Get,
  InternalServerErrorException,
  Post,
  Put,
  Query,
  UploadedFile,
  UseInterceptors,
} from '@nestjs/common';

@Controller('strapi')
export class StrapiController {
  public service: StrapiService;

  public constructor(service: StrapiService) {
    this.service = service;
  }

  @Get('allblogs')
  async getAllBlogs() {
    return this.service.getAllBlogs();
  }

}