import { BadRequestException, Injectable } from '@nestjs/common';
import Client from 'src/modules/clients/entity/client.entity';
import { User } from 'src/modules/users/entities/user.entity';
import { GstrPromise } from '../entity/promise.entity';
import { <PERSON><PERSON>, CronExpression } from '@nestjs/schedule';
import { In, createQueryBuilder, getConnection, getManager, getRepository } from 'typeorm';
import axios from 'axios';
import GstrRegister, { RegistrationType } from '../entity/gstr-register.entity';
import * as moment from 'moment';
import { Organization } from 'src/modules/organization/entities/organization.entity';
import { ReturnsData } from '../entity/returns-data.entity';
import FindReturnsDto from '../dto/find-returns.dto';
import {
  getCurrentFinancialYear,
  getFinancialYearFromRtnPrd,
  incrementFinancialYear,
} from 'src/utils/datesFormation';
import SyncClinetsDto from '../dto/sync-clients.dto';
import { GstrRegisterService } from './gstr-register.service';
import ClientGroup from 'src/modules/client-group/client-group.entity';

@Injectable()
export class PromiseService {
  constructor(private gstrService: GstrRegisterService) {}

  async getReturns(data: FindReturnsDto, userId: number) {
    console.log('Data', data);
    for (const client of data.clientDetails) {
      const args: SyncClinetsDto = {
        financialYear: data.financialYear,
        clientId: client.clientId,
        gstNumber: client.gstNumber,
        clientGroupId: client.clientGroupId,
      };
      await this.syncSingleClientReturns(args);
    }
    return true;
  }

  async getSingleClientReturns(data: any, userId: number) {
    if (data?.clientId) {
      const existingGstrRegister = await GstrRegister.findOne({
        where: { client: data?.clientId },
      });
      if (existingGstrRegister) {
        throw new BadRequestException('Client Already Synced');
      }
    }

    if (data?.clientGroupId) {
      const existingGroupGstrRegister = await GstrRegister.findOne({
        where: { clientGroup: data?.clientGroupId },
      });
      if (existingGroupGstrRegister) {
        throw new BadRequestException('Client Already Synced');
      }
    }

    let user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
    let client = await Client.findOne({ id: data?.clientId });
    let clientGroup = await ClientGroup.findOne({ id: data?.clientGroupId });
    let gstrRegister = new GstrRegister();
    gstrRegister.client = client;
    gstrRegister.clientGroup = clientGroup;
    gstrRegister.registrationType = data.type;
    gstrRegister.organization = user.organization;
    await gstrRegister.save();

    if (!data.syncAll) {
      try {
        const headers = { Authorization: process.env.FYN_AUTH };
        const url = `${process.env.FYN_RETURNS}/${data.gstNumber}/${data.financialYear}`;
        const response = await axios.get(url, { headers });
        if (response?.data?.EFiledlist?.length > 0) {
          try {
            const returnsData = [];
            await getManager().transaction(async (transactionalEntityManager) => {
              for (const i of response?.data?.EFiledlist) {
                const returnData = new ReturnsData();
                returnData.gstrRegister = gstrRegister;
                returnData.financialYear = getFinancialYearFromRtnPrd(i.ret_prd);
                returnData.valid = i.valid;
                returnData.mof = i.mof;
                returnData.dof = i.dof;
                returnData.rtntype = i.rtntype;
                returnData.retPrd = i.ret_prd;
                returnData.arn = i.arn;
                returnData.status = i.status;
                returnsData.push(returnData);
              }
              if (returnsData.length > 0) {
                await transactionalEntityManager.insert(ReturnsData, returnsData);
              }
            });
          } catch (error) {
            console.error('Failed to save returns data:', error);
          }
        } else {
          console.log(response?.data?.error?.message);
        }
      } catch (e) {
        console.error(e);
      }
    } else {
      this.gstrService.syncClients({
        clients: [data.clientId],
        clientsGroup: [data.clienGroupId],
        financialYear: data.selectedYear,
      });
    }
  }

  async syncSingleClientReturns(data: SyncClinetsDto) {
    try {
      const headers = { Authorization: process.env.FYN_AUTH };
      const url = `${process.env.FYN_RETURNS}/${data.gstNumber}/${data.financialYear}`;
      const response = await axios.get(url, { headers });
      let gstrRegisterr;

      if (data.clientId) {
        gstrRegisterr = await GstrRegister.findOne({ where: { client: data.clientId } });
      }

      if (data.clientGroupId) {
        gstrRegisterr = await GstrRegister.findOne({ where: { clientGroup: data.clientGroupId } });
      }

      const existingReturns = (
        await ReturnsData.find({
          where: { gstrRegister: gstrRegisterr, financialYear: data.financialYear },
          select: ['arn'],
        })
      ).map((data) => data.arn);
      if (response?.data?.EFiledlist?.length) {
        const filterReturns: any[] = response?.data?.EFiledlist.filter(
          (r: any) => !existingReturns.includes(r.arn),
        );
        const returnsData = [];
        await getManager().transaction(async (transactionalEntityManager) => {
          for (const i of filterReturns) {
            const returnData = new ReturnsData();
            returnData.gstrRegister = gstrRegisterr;
            returnData.financialYear = getFinancialYearFromRtnPrd(i.ret_prd);
            returnData.valid = i.valid;
            returnData.mof = i.mof;
            returnData.dof = i.dof;
            returnData.rtntype = i.rtntype;
            returnData.retPrd = i.ret_prd;
            returnData.arn = i.arn;
            returnData.status = i.status;
            returnsData.push(returnData);
          }
          if (returnsData.length > 0) {
            await transactionalEntityManager.insert(ReturnsData, returnsData);
          }
        });
      }
      if (
        response?.data?.EFiledlist?.length &&
        existingReturns?.length &&
        response?.data?.EFiledlist?.length === existingReturns?.length
      ) {
      }
      return true;
    } catch (err) {
      console.log(err);
    }
  }

  async syncClientBasedOnTaskComplete(data: any, userId: number) {
    let existingGstrRegister: GstrRegister;
    if (data?.clientId) {
      existingGstrRegister = await GstrRegister.findOne({
        where: { client: data?.clientId },
      });
    }

    if (data?.clientGroupId) {
      existingGstrRegister = await GstrRegister.findOne({
        where: { clientGroup: data?.clientGroupId },
      });
    }

    if (existingGstrRegister) {
      const updatedOne = await this.syncSingleClientReturns(data);
      return updatedOne;
    } else {
      const newOne = await this.getSingleClientReturns(data, userId);
      return newOne;
    }
  }
}
