import { CanActivate, ExecutionContext, Injectable } from '@nestjs/common';
import { User } from 'src/modules/users/entities/user.entity';

@Injectable()
export class HasPermission implements CanActivate {
  permissions: Array<string>;

  constructor(permissions: Array<string>) {
    this.permissions = permissions;
  }

  canActivate(context: ExecutionContext): any {
    const request = context.switchToHttp().getRequest();
    return this.validateRequest(request);
  }

  async validateRequest(request: any) {
    let user = await User.findOne({
      where: {
        id: request.user.userId,
      },
      relations: ['role', 'role.permissions'],
    });

    if (user.role.defaultRole) return true;

    let hasPermission = user.role.permissions.some((permission) => {
      return this.permissions.includes(permission.slug);
    });

    if (hasPermission) return true;

    return false;
  }
}
