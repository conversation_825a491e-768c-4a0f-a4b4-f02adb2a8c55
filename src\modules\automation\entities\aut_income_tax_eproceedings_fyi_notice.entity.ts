import { Optional } from '@nestjs/common';
import Client from 'src/modules/clients/entity/client.entity';
import { Organization, StorageSystem } from 'src/modules/organization/entities/organization.entity';
import {
  BaseEntity,
  Column,
  CreateDateColumn,
  Entity,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import AutEProceedingFya from './aut_income_tax_eproceedings_fya.entity';
import AutEProceedingFyi from './aut_income_tax_eproceedings_fyi.entity';
import AutProceedingResponseFyi from './aut_income_tax_eproceedings_fyi_notice_response.entity';

@Entity()
class AutFyiNotice extends BaseEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  pan: string;

  @Column()
  proceedingReqId: string;

  @Column()
  eProceedingId: number;

  @Column()
  documentIdentificationNumber: string;

  @Column()
  proceedingName: string;

  @Column()
  proceedingType: string;

  @Column()
  proceedingStatus: string;

  @Column()
  itrType: string;

  @Column()
  assessmentYear: string;

  @Column()
  financialYear: string;

  @Column()
  noticeSection: string;

  @Column()
  description: string;

  @Column()
  issuedOn: string;

  @Column()
  responseDueDate: string;

  @Column()
  lastResponseSubmittedOn: string;

  @Column()
  documentReferenceId: string;

  @Column('json')
  noticeAttatchments: object;

  @Column('json')
  noticeLetters: object;

  @Column()
  remarks: string;

  @Column()
  remarkSubmittedOn: string;

  @Column()
  respType: string;

  @Column()
  proceedingLimitationDate: string;

  @Column()
  nameOfAssesse: string;

  @Column()
  organizationId: number;

  @Column({ type: 'enum', enum: StorageSystem, default: null })
  storageSystem: StorageSystem;

  @ManyToOne(() => Client, (client) => client.autfyinotice, { onDelete: 'SET NULL' })
  client: Client;

  @ManyToOne(() => AutEProceedingFyi, (autEProceedingFyi) => autEProceedingFyi.notices)
  eProceeding: AutEProceedingFyi;

  @OneToMany(
    () => AutProceedingResponseFyi,
    (autProceedingResponse) => autProceedingResponse.fyiNotice,
  )
  responses: AutProceedingResponseFyi[];

  @CreateDateColumn()
  createdAt: string;

  @UpdateDateColumn()
  updatedAt: string;
}

export default AutFyiNotice;
