import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  ParseIntPipe,
  Post,
  Put,
  Query,
  Req,
  UseGuards,
} from '@nestjs/common';
import { JwtAuthGuard } from 'src/modules/users/jwt/jwt-auth.guard';
import CreateLabelDto from './dto/create-label.dto';
import { LabelService } from './label.service';

@Controller('labels')
export class LabelController {
  constructor(private service: LabelService) {}

  @UseGuards(JwtAuthGuard)
  @Get()
  findAll(@Req() req: any, @Query() query: any) {
    const { userId } = req.user;
    return this.service.findAll(userId,query);
  }

  @UseGuards(JwtAuthGuard)
  @Post()
  create(@Body() body: CreateLabelDto, @Req() req: any) {
    const { userId } = req.user;
    return this.service.create(userId, body);
  }

  @Put('/:id')
  update(@Param('id', ParseIntPipe) id: number, @Body() body: CreateLabelDto) {
    return this.service.update(id, body);
  }

  @Delete('/:id')
  delete(@Param('id', ParseIntPipe) id: number) {
    return this.service.delete(id);
  }
}
