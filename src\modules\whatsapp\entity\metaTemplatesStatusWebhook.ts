import { BaseEntity, Column, CreateDateColumn, Entity, PrimaryColumn, PrimaryGeneratedColumn, UpdateDateColumn } from "typeorm";


@Entity()
class MetaTemplatesStatusWebhook extends BaseEntity {
    @PrimaryGeneratedColumn()
    id: number;


    @Column()
    templateName: string;

@Column()
    templateId: string;

 @Column()
 status : string;

 @Column()
 reason: string;
 
 @Column()
 whatsappBusinessId: string;
 
 @Column({ type: 'timestamp', nullable: true })
    createdAt: Date;

@UpdateDateColumn()
    updatedAt : Date;
    
}

export default MetaTemplatesStatusWebhook;