import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ClientGroupService } from './client-group.service';
import { ClientGroupController } from './client-group.controller';
import ClientGroup from './client-group.entity';
import { ClientGroupSubscriber } from 'src/event-subscribers/clientGroup.subscriber';

@Module({
  imports: [TypeOrmModule.forFeature([ClientGroup])],
  controllers: [ ClientGroupController],
  providers: [
    ClientGroupService,
    ClientGroupSubscriber,
  ],
})
export class ClientGroupModule { }
