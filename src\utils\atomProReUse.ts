import AutClientCredentials, {
  IncomeTaxStatus,
} from 'src/modules/automation/entities/aut_client_credentials.entity';
import { CreateClientPasswordDto } from 'src/modules/clients/dto/create-password.dto';
import Password from 'src/modules/clients/entity/password.entity';
import GstrCredentials, {
  GstrStatus,
} from 'src/modules/gstr-automation/entity/gstrCredentials.entity';
import OrganizationPreferences from 'src/modules/organization-preferences/entity/organization-preferences.entity';
import TanClientCredentials from 'src/modules/tan-automation/entity/tan-client-credentials.entity';

export const checkAtomProConfigIncomeTax = async (organizationId: number) => {
  const orgPreferences: any = await OrganizationPreferences.findOne({
    where: { organization: organizationId },
  });
  const checkIncomeTaxYes = (await orgPreferences?.automationConfig?.incomeTax) === 'YES';
  if (checkIncomeTaxYes) {
    const autClientCredential = await AutClientCredentials.count({
      where: { organizationId: organizationId, status: IncomeTaxStatus.ENABLE },
    });
    const organizationIncomeTaxLimit = orgPreferences?.automationConfig?.incomeTaxLimit;
    if (organizationIncomeTaxLimit > autClientCredential) {
      return true;
    } else {
      return 'Client Not Added in Atom Pro Income Tax due to Client Limit Reached';
    }
  } else {
    return 'Subscribe Atom Pro Income Tax to access for this Client';
  }
};

export const checkAtomProConfigGstr = async (organizationId: number) => {
  const orgPreferences: any = await OrganizationPreferences.findOne({
    where: { organization: organizationId },
  });
  const checkGstrYes = (await orgPreferences?.automationConfig?.gstr) === 'YES';
  if (checkGstrYes) {
    const gstrCredentialCount = await GstrCredentials.count({
      where: { organizationId: organizationId, status: GstrStatus.ENABLE },
    });
    const organizationGstrLimit = orgPreferences?.automationConfig?.gstrLimit;
    if (organizationGstrLimit > gstrCredentialCount) {
      return true;
    } else {
      return 'Client Not Added in Atom Pro Gstr due to Client Limit Reached';
    }
  } else {
    return 'Subscribe Atom Pro Gstr to access for this Client';
  }
};

export const createClientCredentials = async (data: any) => {
  const password = new Password();
  password.website = data.website.trim();
  password.websiteUrl = data.websiteUrl.trim();
  password.loginId = data.loginId.trim();
  password.password = data.password.trim();
  password.client = data.client;
  password['userId'] = data?.userId;
  password.isExistingAtomPro = data.isaddAtomPro;
  await password.save();
  return password;
};

export const createTraceClientCredentials = async (data: any) => {
  const password = new Password();
  password.website = data.website.trim();
  password.websiteUrl = data.websiteUrl.trim();
  password.loginId = data.loginId.trim();
  password.password = data.password.trim();
  password.client = data.client;
  password['userId'] = data?.userId;
  password.isExistingAtomPro = data.isaddAtomPro;
  password.tracesTan = data.tracesTan.trim();
  await password.save();
  return password;
};

export const checkAtomProConfigIncomeTaxTan = async (organizationId: number) => {
  const orgPreferences: any = await OrganizationPreferences.findOne({
    where: { organization: organizationId },
  });
  const checkIncomeTaxTanYes = (await orgPreferences?.automationConfig?.tan) === 'YES';
  if (checkIncomeTaxTanYes) {
    const tanClientCredential = await TanClientCredentials.count({
      where: { organizationId: organizationId, status: IncomeTaxStatus.ENABLE },
    });
    const organizationIncomeTaxLimit = orgPreferences?.automationConfig?.tanLimit;
    if (organizationIncomeTaxLimit > tanClientCredential) {
      return true;
    } else {
      return 'Client Not Added in Atom Pro Income Tax TAN due to Client Limit Reached';
    }
  } else {
    return 'Subscribe Atom Pro Income Tax TAN to access for this Client';
  }
};

export const updateClientCredentials = async (data: any,id:number) => {
  const password = await  Password.findOne({where:{id}});
  password.password = data.password.trim();
  password.client = data.client;
  password['userId'] = data?.userId;
  password.isExistingAtomPro = data.isaddAtomPro;
  await password.save();
  return password;
};
