import {
  Connection,
  EntitySubscriberInterface,
  EventSubscriber,
  InsertEvent,
  getManager,
} from 'typeorm';
import { sendnewMailToBusinessTeam } from 'src/emails/newemails';
import AtomToQtmrequests from 'src/modules/quantum/entity/atm-qtm-requests.entity';
import { fullMobileWithCountry } from 'src/utils/validations/fullMobileWithCountry';

@EventSubscriber()
export class QuantumSubscriber implements EntitySubscriberInterface<AtomToQtmrequests> {
  constructor(private readonly connection: Connection) {
    connection.subscribers.push(this);
  }

  listenTo() {
    return AtomToQtmrequests;
  }

  async beforeInsert(event: InsertEvent<AtomToQtmrequests>) {}

  async afterInsert(event: InsertEvent<AtomToQtmrequests>) {
    const entityManager = getManager();
    const {
      id,
      user: {
        id: userId,
        fullName,
        email: userEmail,
        mobileNumber: userMobileNumber,
        countryCode: userCountryCode,
        organization: {
          id: orgId,
          legalName,
          email: orgEmail,
          buildingNo,
          floorNumber,
          street,
          city,
          district,
          state,
          pincode,
          primaryContactFullName,
          mobileNumber: orgMobileNumber,
          countryCode: orgCountryCode,
        } = {},
      } = {},
    } = event.entity;

    const data = {
      organizationId: orgId,
      fullName,
      organizationName: legalName,
      userId: userId,
      orgEmail: orgEmail,
      orgMobileNumber: fullMobileWithCountry(orgMobileNumber, orgCountryCode),
      buildingNo,
      floorNumber,
      street,
      city,
      district,
      state,
      pincode,
      primaryContactFullName,
      mobileNumber: fullMobileWithCountry(userMobileNumber, userCountryCode),
      email: userEmail,
    };

    const mailOptionsForBusiness = {
      data: data,
      email: process.env.SOCIAL_EMAIL,
      filePath: 'quantum-request-business-team',
      subject: 'New Request for Quantum Subscription in ATOM',
      key: '',
      id: 0,
    };
    await sendnewMailToBusinessTeam(mailOptionsForBusiness);
  }
}
