import {
    BadRequestException,
    ConflictException,
    forwardRef,
    Inject,
    Injectable,
    InternalServerErrorException,
    UnprocessableEntityException,
} from '@nestjs/common';
import axios from 'axios';
import { User } from '../users/entities/user.entity';
import AuthToken, { AuthTokenType } from './auth-token.entity';
import FindOneDriveStorageDto from './find-onedrive-storage.dto';
import { IExisting, IUpload, StorageService } from '../storage/storage.service';
import Storage, { StorageSystem, StorageType } from '../storage/storage.entity';
import Client from '../clients/entity/client.entity';
import ClientGroup from '../client-group/client-group.entity';
import { v4 as uuidv4 } from 'uuid';
import { createQueryBuilder } from 'typeorm';
import { getName } from 'src/utils/FilterSpecialChars';
import Task from '../tasks/entity/task.entity';
import { AttachmentsService } from '../tasks/services/attachments.service';
import * as moment from 'moment';
import CollectData from '../collect-data/collect-data.entity';
import { Event_Actions } from 'src/event-listeners/actions';
import { EventEmitter2 } from '@nestjs/event-emitter';


const AUTH_URL = `https://accounts.google.com/o/oauth2/v2/auth`;
const AUTH_TOKEN_URL = `https://oauth2.googleapis.com/token`;
const DRIVE_URL = 'https://www.googleapis.com/drive/v3/files';
const GMAIL_API_URL = 'https://gmail.googleapis.com/gmail/v1/users/me';
const THREADS_API_URL = "https://gmail.googleapis.com/gmail/v1/users/me/threads";
const SEND_API_URL = "https://gmail.googleapis.com/gmail/v1/users/me/messages"

//DEVELOPER CREDENTIALS Anji
// const CLIENT_ID = "*************-oc0kpnca1b860pv6ler8n7pbdg9mesrp.apps.googleusercontent.com";
// const CLIENT_SECRET = "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";

//VIDER CREDENTIALS
const CLIENT_ID = '************-2t8p2h4ibfnor1pbf3r0f1qvp2asi0q4.apps.googleusercontent.com';
const CLIENT_SECRET = '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';

// const SCOPE_DRIVE = 'https://www.googleapis.com/auth/drive.readonly';
const SCOPE_DRIVE = 'https://www.googleapis.com/auth/drive';
const SCOPE_GMAIL = 'https://www.googleapis.com/auth/gmail.readonly';
const SCOPE_GMAIL_COMPOSE = 'https://www.googleapis.com/auth/gmail.compose';
const SCOPE_GMAIL_MODIFY = 'https://www.googleapis.com/auth/gmail.modify';
// const SCOPE1 = 'https://www.googleapis.com/auth/drive.file';
const GRANT_TYPE_AUTH = 'authorization_code';
const RESPONSE_TYPE = 'code';
const GRANT_TYPE_REFRESH = 'refresh_token'

@Injectable()
export class GoogleDriveStorageService {
    constructor(
        private eventEmitter: EventEmitter2,
        @Inject((forwardRef(() => AttachmentsService)))
        private attachmentsService: AttachmentsService,
        @Inject((forwardRef(() => StorageService)))
        private storageService: StorageService
    ) { }
    async saveToken(userId: number, body: any) {

        if (!body.code) {
            throw new BadRequestException('Code is required');
        }

        const user = await User.findOne({
            where: { id: userId },
            relations: ['organization'],
        });

        let existingToken = await AuthToken.findOne({
            where: {
                organizationId: user.organization.id,
                type: AuthTokenType.GOOGLE,
            },
        });

        if (existingToken) {
            await existingToken.remove();
        }

        const data = new URLSearchParams({
            client_id: CLIENT_ID,
            scope: `${SCOPE_DRIVE} ${SCOPE_GMAIL} ${SCOPE_GMAIL_COMPOSE} ${SCOPE_GMAIL_MODIFY}`,
            redirect_uri: body?.location === 'signup' ? `${process.env.WEBSITE_URL}/googledrive-auth-signup` : `${process.env.WEBSITE_URL}/googledrive-auth`,
            client_secret: CLIENT_SECRET,
            code: body.code,
            grant_type: GRANT_TYPE_AUTH,
            access_type: "offline",
            prompt: "consent",
        });

        try {
            let res = await axios({
                method: 'POST',
                url: AUTH_TOKEN_URL,
                headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                data,
            });

            let token = new AuthToken();
            token.accessToken = res.data.access_token;
            token.refreshToken = res.data.refresh_token;
            token.type = AuthTokenType.GOOGLE;
            token.organizationId = user.organization.id;
            await token.save();
            return token;
        } catch (err) {
            console.log('error', err);
            throw new InternalServerErrorException(err);
        }
    }

    async getItems(userId: number, query: FindOneDriveStorageDto) {

        let user = await User.findOne({
            where: { id: userId },
            relations: ['organization'],
        });

        let token = await AuthToken.findOne({
            where: {
                organizationId: user.organization.id,
                type: AuthTokenType.GOOGLE,
            },
        });

        const searchParams = new URLSearchParams({
            client_id: CLIENT_ID,
            scope: `${SCOPE_DRIVE} ${SCOPE_GMAIL} ${SCOPE_GMAIL_COMPOSE} ${SCOPE_GMAIL_MODIFY}`,
            response_type: RESPONSE_TYPE,
            redirect_uri: query.location === 'signup' ? `${process.env.WEBSITE_URL}/googledrive-auth-signup` : `${process.env.WEBSITE_URL}/googledrive-auth`,
            access_type: "offline",
            prompt: "consent", // Forces re-authorization and refresh token return

        });

        if (!token) {

            throw new UnprocessableEntityException({
                code: 'NO_TOKEN',
                message: 'User has not been authenticated with Google',
                authorizationUrl: `${AUTH_URL}?${searchParams.toString()}`,
            });
        }

        try {
            let response = await this.getData(token, query);
            return response;
        } catch (err) {
            let error = err.response.data?.error;
            if (err?.response?.data?.error?.status === 'UNAUTHENTICATED') {
                await this.refreshToken(token);
                let response = await this.getData(token, query);
                return response;
            } else {
                console.log("error", err?.response?.data?.error?.status)
            }
            throw new InternalServerErrorException(error);
        }
    }

    async getData(token: AuthToken, query: FindOneDriveStorageDto) {
        const DRIVE_API_URL = 'https://www.googleapis.com/drive/v3/files';
        const response = await axios.get(DRIVE_API_URL, {
            headers: {
                'Authorization': `Bearer ${token.accessToken}`,
            },
            params: {
                q: `'${query.id}' in parents`,
                fields: 'files(id, name, mimeType , webContentLink)',

            },
        });

        return response.data.files;
    }

    async getFolderSizeRecursive(folderId: string, accessToken: string): Promise<number> {
        const DRIVE_FOLDER_MIME = 'application/vnd.google-apps.folder';

        let total = 0;
        let pageToken: string | undefined;

        do {
            const query = `'${folderId}' in parents and trashed = false`;
            const url = `https://www.googleapis.com/drive/v3/files?q=${encodeURIComponent(query)}&fields=nextPageToken,files(id,size,mimeType)&pageSize=1000${pageToken ? `&pageToken=${pageToken}` : ''}`;

            const res = await axios.get(url, {
                headers: {
                    Authorization: `Bearer ${accessToken}`,
                },
            });

            for (const file of res.data.files ?? []) {
                if (file.mimeType === DRIVE_FOLDER_MIME) {
                    total += await this.getFolderSizeRecursive(file.id, accessToken);
                } else {
                    total += parseInt(file.size || '0');
                }
            }

            pageToken = res.data.nextPageToken;
        } while (pageToken);

        return total;
    }

    async getGoogleDriveStorageInfo(userId: number, folderId?: string) {
        const user = await User.findOne({
            where: { id: userId },
            relations: ['organization'],
        });

        if (!user) {
            throw new BadRequestException('User not found');
        }

        const token = await AuthToken.findOne({
            where: {
                organizationId: user.organization.id,
                type: AuthTokenType.GOOGLE,
            },
        });

        if (!token) {
            throw new UnprocessableEntityException(
                'No authentication token found. Please authenticate with Google Drive.',
            );
        }

        if (!folderId) {
            const rootFolder = await Storage.findOne({
                where: { organization: user.organization.id, name: 'Atom' },
            });
            folderId = rootFolder?.fileId;
        }

        try {
            // Get global storage info
            const aboutUrl = 'https://www.googleapis.com/drive/v3/about?fields=storageQuota';
            const aboutResponse = await axios.get(aboutUrl, {
                headers: {
                    Authorization: `Bearer ${token.accessToken}`,
                    'Content-Type': 'application/json',
                },
            });

            const quota = aboutResponse.data.storageQuota;
            const bytesToGB = (bytes: string | number) =>
                parseFloat((parseInt(bytes as string) / 1073741824).toFixed(2));

            // const totalStorageGB = bytesToGB(quota.limit);
            // const usedStorageGB = bytesToGB(quota.usage);
            // const usedInDriveGB = bytesToGB(quota.usageInDrive || 0);
            // const usedInTrashGB = bytesToGB(quota.usageInDriveTrash || 0);
            // const storageLeftGB = parseFloat((totalStorageGB - usedStorageGB).toFixed(2));
            const totalStorageGB = (quota.limit);
            const usedStorageGB = (quota.usage);
            const usedInDriveGB = (quota.usageInDrive || 0);
            const usedInTrashGB = (quota.usageInDriveTrash || 0);
            const storageLeftGB = parseFloat((totalStorageGB - usedStorageGB).toFixed(2));

            let folderStorageUsedGB = 0;
            // if (folderId) {
            //     const folderBytes = await this.getFolderSizeRecursive(folderId, token.accessToken);
            //     folderStorageUsedGB = (folderBytes);
            // }
            let orgStorage = await createQueryBuilder(Storage, 'storage')
                .select('SUM(storage.fileSize) as totalStorage')
                .leftJoin('storage.organization', 'organization')
                .where('organization.id = :orgId', { orgId: user.organization.id })
                .getRawOne();

            let clientStorage = await createQueryBuilder(Storage, 'storage')
                .select('SUM(storage.fileSize) as totalStorage')
                .leftJoin('storage.client', 'client')
                .leftJoin('storage.clientGroup', 'clientGroup')
                .leftJoin('client.organization', 'organization')
                .where('organization.id = :orgId', { orgId: user.organization.id })
                .getRawOne();

            return {
                totalStorageGB,
                usedStorageGB,
                usedInDriveGB,
                usedInTrashGB,
                storageLeftGB,
                ...(folderId && {
                    folderId,
                    folderStorageUsedGB,
                }),
                totalStorageUsed:
                    +clientStorage?.totalStorage + +orgStorage?.totalStorage
            };
        } catch (err) {
            const error = err?.response?.data?.error;
            if (error?.code === 401) {
                await this.refreshToken(token);
                return this.getGoogleDriveStorageInfo(userId, folderId);
            } else {
                throw new InternalServerErrorException(
                    error?.message || 'Failed to get Google Drive storage info',
                );
            }
        }
    }







    // async getGoogleDriveStorageInfo(userId: number, folderId?: string) {
    //     const user = await User.findOne({
    //         where: { id: userId },
    //         relations: ['organization'],
    //     });

    //     const rootFolderId = await Storage.findOne({ where: { organization: user.organization.id, name: 'Atom' } });
    //     console.log("rootFolderId", rootFolderId)
    //     folderId = rootFolderId?.fileId;


    //     if (!user) {
    //         throw new BadRequestException('User not found');
    //     }

    //     const token = await AuthToken.findOne({
    //         where: {
    //             organizationId: user.organization.id,
    //             type: AuthTokenType.GOOGLE,
    //         },
    //     });

    //     if (!token) {
    //         throw new UnprocessableEntityException(
    //             'No authentication token found. Please authenticate with Google Drive.',
    //         );
    //     }

    //     try {
    //         // Call Google Drive about endpoint
    //         const aboutUrl = 'https://www.googleapis.com/drive/v3/about?fields=storageQuota';
    //         const aboutResponse = await axios.get(aboutUrl, {
    //             headers: {
    //                 Authorization: `Bearer ${token.accessToken}`,
    //                 'Content-Type': 'application/json',
    //             },
    //         });

    //         const quota = aboutResponse.data.storageQuota;
    //         console.log("quota", quota)
    //         const bytesToGB = (bytes: string | number) =>
    //             parseFloat((parseInt(bytes as string) / 1073741824).toFixed(2)); // Convert bytes to GB

    //         const totalStorageGB = bytesToGB(quota.limit);
    //         const usedStorageGB = bytesToGB(quota.usage); // Total usage (Gmail + Photos + Drive + Trash)
    //         const usedInDriveGB = bytesToGB(quota.usageInDrive || 0); // Just Google Drive files
    //         const usedInTrashGB = bytesToGB(quota.usageInDriveTrash || 0); // Trash files
    //         const storageLeftGB = parseFloat((totalStorageGB - usedStorageGB).toFixed(2));

    //         let folderStorageUsedGB = 0;

    //         // Optional: get folder-specific usage
    //         if (folderId) {
    //             const filesUrl = `https://www.googleapis.com/drive/v3/files?q='${folderId}'+in+parents&fields=files(size)`;
    //             const filesResponse = await axios.get(filesUrl, {
    //                 headers: {
    //                     Authorization: `Bearer ${token.accessToken}`,
    //                     'Content-Type': 'application/json',
    //                 },
    //             });

    //             const folderStorageUsed = filesResponse.data.files.reduce(
    //                 (total, file) => total + (parseInt(file.size) || 0),
    //                 0,
    //             );

    //             folderStorageUsedGB = bytesToGB(folderStorageUsed);
    //         }

    //         return {
    //             totalStorageGB,        // Total quota
    //             // Trash usage
    //             usedStorageGB: usedInDriveGB + usedStorageGB + usedInTrashGB,         // Total usage across Gmail, Photos, Drive
    //             // Drive file usage
    //             storageLeftGB,         // Remaining available
    //             ...(folderId && {
    //                 folderId,
    //                 folderStorageUsedGB,
    //             }),
    //         };
    //     } catch (err) {
    //         const error = err?.response?.data?.error;
    //         if (error?.code === 401) {
    //             await this.refreshToken(token);
    //             return this.getGoogleDriveStorageInfo(userId, folderId);
    //         } else {
    //             throw new InternalServerErrorException(
    //                 error?.message || 'Failed to get Google Drive storage info',
    //             );
    //         }
    //     }
    // }


    async refreshToken(token: AuthToken) {
        try {
            const data = new URLSearchParams({
                client_id: CLIENT_ID,
                client_secret: CLIENT_SECRET,
                scope: `${SCOPE_DRIVE} ${SCOPE_GMAIL} ${SCOPE_GMAIL_COMPOSE} ${SCOPE_GMAIL_MODIFY}`,
                redirect_uri: `${process.env.WEBSITE_URL}/googledrive-auth`,
                grant_type: GRANT_TYPE_REFRESH,
                refresh_token: token.refreshToken
            });

            let res = await axios({
                method: 'POST',
                url: AUTH_TOKEN_URL,
                headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                data,
            });

            token.accessToken = res.data.access_token;
            token.refreshToken = res.data.refresh_token || token.refreshToken;
            await token.save();

        } catch (err) {

            let error = err.response.data?.error;
            console.log('refresh, error', error);
            throw new InternalServerErrorException(error);
        }
    }

    async reAuthorize() {
        const searchParams = new URLSearchParams({
            client_id: CLIENT_ID,
            scope: `${SCOPE_DRIVE} ${SCOPE_GMAIL} ${SCOPE_GMAIL_COMPOSE} ${SCOPE_GMAIL_MODIFY}`,
            response_type: RESPONSE_TYPE,
            redirect_uri: `${process.env.WEBSITE_URL}/googledrive-auth`,
            access_type: "offline",
            prompt: "consent",
        });

        return `${AUTH_URL}?${searchParams.toString()}`;
    };


    async getGmailMessages(userId: number, query) {
        const user = await User.findOne({
            where: { id: userId },
            relations: ['organization'],
        });

        const token = await AuthToken.findOne({
            where: {
                organizationId: user.organization.id,
                type: AuthTokenType.GOOGLE,
            },
        });

        if (!token) {
            const searchParams = new URLSearchParams({
                client_id: CLIENT_ID,
                scope: `${SCOPE_DRIVE} ${SCOPE_GMAIL} ${SCOPE_GMAIL_COMPOSE} ${SCOPE_GMAIL_MODIFY}`,
                response_type: RESPONSE_TYPE,
                redirect_uri: `${process.env.WEBSITE_URL}/googledrive-auth`,
                access_type: 'offline',
                prompt: "consent",
            });

            throw new UnprocessableEntityException({
                code: 'NO_TOKEN',
                message: 'User has not been authenticated with Google',
                authorizationUrl: `${AUTH_URL}?${searchParams.toString()}`,
            });
        }

        try {
            const response = await this.fetchGmailMessages(token, query);
            // console.log(response)
            return response;
        } catch (err) {
            const error = err.response?.data?.error;
            if (err?.response?.data?.error?.status === 'UNAUTHENTICATED') {
                await this.refreshToken(token);
                const response = await this.fetchGmailMessages(token, query);
                return response;
            } else {
                console.log('error', err?.response?.data?.error?.status);
                throw new InternalServerErrorException(error);
            }
        }
    }




    //new one

    async fetchGmailMessages(token, query) {

        try {
            // Fetch threads with query and pagination
            // console.log(query)
            const response = await axios.get(THREADS_API_URL, {
                headers: {
                    Authorization: `Bearer ${token.accessToken}`,
                },

                params: {
                    q: query.q,
                    maxResults: query.maxResults,
                    currentPage: query.currentPage,
                    pageToken: query.nextPageToken

                }
            });

            const { threads, nextPageToken, resultSizeEstimate } = response.data;

            if (!threads) return { messages: [], nextPageToken: null, totalThreads: 0 };

            // Fetch thread details
            const detailedMessages = await Promise.all(
                threads.map(async (thread) => {
                    const threadResponse = await axios.get(`${THREADS_API_URL}/${thread.id}`, {
                        headers: {
                            Authorization: `Bearer ${token.accessToken}`,
                        },
                    });

                    const threadData = threadResponse.data;
                    const messages = threadData.messages.map((msg) => {
                        const headers = msg.payload.headers;
                        const sender = headers.find((header) => header.name === "From")?.value || "Unknown";
                        const subject = headers.find((header) => header.name === "Subject")?.value || "No Subject";
                        const date = headers.find((header) => header.name === "Date")?.value || "Unknown Date";
                        const snippet = msg.snippet;

                        return { id: msg.id, sender, subject, date, snippet };
                    });

                    return {
                        threadId: threadData.id,
                        messageCount: threadData.messages.length,
                        messages,
                    };
                })
            );
            const totalPages = Math.ceil((resultSizeEstimate || 0) / query.maxResults);

            return {
                messages: detailedMessages,
                nextPageToken: nextPageToken || null,
                totalThreads: resultSizeEstimate || 0,
                currentPage: query.currentPage || 1,
                totalPages,
            };
        } catch (err) {

            if (err?.response?.data?.error?.status === "UNAUTHENTICATED") {
                await this.refreshToken(token);
                return await this.fetchGmailMessages(token, query);
            }
            console.error(err.response?.data || err.message);
            throw new InternalServerErrorException(err);
        }
    }

    async getGmailMessageDetails(userId: number, messageId: string) {
        const user = await User.findOne({
            where: { id: userId },
            relations: ['organization'],
        });

        const token = await AuthToken.findOne({
            where: {
                organizationId: user.organization.id,
                type: AuthTokenType.GOOGLE,
            },
        });

        if (!token) {
            const searchParams = new URLSearchParams({
                client_id: CLIENT_ID,
                scope: `${SCOPE_DRIVE} ${SCOPE_GMAIL} ${SCOPE_GMAIL_COMPOSE} ${SCOPE_GMAIL_MODIFY}`,
                response_type: RESPONSE_TYPE,
                redirect_uri: `${process.env.WEBSITE_URL}/googledrive-auth`,
                access_type: 'offline',
                prompt: "consent",
            });

            throw new UnprocessableEntityException({
                code: 'NO_TOKEN',
                message: 'User has not been authenticated with Google',
                authorizationUrl: `${AUTH_URL}?${searchParams.toString()}`,
            });
        }

        try {
            const response = await this.fetchGmailMessageDetails(token, messageId);
            return response;
        } catch (err) {
            const error = err.response?.data?.error;
            if (err?.response?.data?.error?.status === 'UNAUTHENTICATED') {
                await this.refreshToken(token);
                const response = await this.fetchGmailMessageDetails(token, messageId);
                return response;
            } else {
                console.log('error', err?.response?.data?.error?.status);
                throw new InternalServerErrorException(error);
            }
        }
    }

    //old one
    // Method to fetch detailed information of a specific Gmail message by messageId
    async fetchGmailMessageMetaDataDetails(token: AuthToken, threadId: string) {
        try {
            // Fetch detailed information for the message
            const msgResponse = await axios.get(`${THREADS_API_URL}/${threadId}`, {
                headers: {
                    Authorization: `Bearer ${token.accessToken}`,
                },
            });

            const msgData = msgResponse.data;
            const message = msgData.messages[0]
            let sender = message.payload.headers.find(header => header.name === 'From')?.value || 'Unknown';
            const subject = message.payload.headers.find(header => header.name === 'Subject')?.value || 'No Subject';
            const date = message.payload.headers.find(header => header.name === 'Date')?.value || 'Unknown Date';
            const snippet = message.snippet;
            const body = this.getMessageBody(message.payload);
            let messageIdHeader = message.payload.headers.find((header) => (header.name === 'Message-ID'));
            // if (!messageIdHeader) {
            //     messageIdHeader = message.payload.headers.find((header) => (header.name === 'Message-Id'));
            //     sender = message.payload.headers.find(header => header.name === 'To')?.value || 'Unknown';
            // };


            const messageId = messageIdHeader.value;
            return {
                id: message.id,
                threadId: message.threadId,
                sender,
                subject,
                date,
                snippet,
                body,
                messageId
            };

        } catch (err) {
            console.log('fetchGmailMessageDetails error', err);
            throw new InternalServerErrorException(err);
        }
    }

    //one one with attachemtns

    async fetchGmailMessageDetails(token: AuthToken, threadId: string) {
        try {
            // Fetch detailed information for the thread
            const msgResponse = await axios.get(`${THREADS_API_URL}/${threadId}`, {
                headers: {
                    Authorization: `Bearer ${token.accessToken}`,
                },
            });

            const msgData = msgResponse.data;

            // Extract details from each message in the thread
            const messagesDetails = await Promise.all(
                msgData.messages.map(async (message) => {
                    const headers = message.payload?.headers || [];
                    const sender = headers.find((header) => header.name === 'From')?.value || 'Unknown';
                    const subject = headers.find((header) => header.name === 'Subject')?.value || 'No Subject';
                    const date = headers.find((header) => header.name === 'Date')?.value || 'Unknown Date';
                    const snippet = message.snippet;
                    const body = this.getMessageBody(message.payload); // Extract message body

                    // Extract attachments
                    const attachments = [];
                    const parts = message.payload?.parts || [];
                    for (const part of parts) {
                        if (part.filename && part.body?.attachmentId) {
                            try {
                                const attachmentResponse = await axios.get(
                                    `https://www.googleapis.com/gmail/v1/users/me/messages/${message.id}/attachments/${part.body.attachmentId}`,
                                    {
                                        headers: {
                                            Authorization: `Bearer ${token.accessToken}`,
                                        },
                                    }
                                );
                                attachments.push({
                                    filename: part.filename,
                                    mimeType: part.mimeType,
                                    content: attachmentResponse.data.data,
                                    contentLength: attachmentResponse.data.data.length
                                    // Base64 encoded content
                                });
                            } catch (attachmentError) {
                                console.error("Failed to fetch attachment:", {
                                    messageId: message.id,
                                    attachmentId: part.body.attachmentId,
                                    error: attachmentError.response?.data || attachmentError.message,
                                });
                            }
                        }
                    }

                    return {
                        id: message.id,
                        sender,
                        subject,
                        date,
                        snippet,
                        body,
                        attachments,
                    };
                })
            );

            return {
                threadId: msgData.id,
                messages: messagesDetails,
            };
        } catch (err) {
            console.error('fetchGmailMessageDetails error', err.response?.data || err.message);
            throw new InternalServerErrorException(err);
        }
    }

    // Helper method to extract the body content from the email payload
    private getMessageBody(payload: any) {
        let body = '';

        // Check if the body exists in 'parts' or 'body' in the payload
        if (payload.body && payload.body.data) {
            body = Buffer.from(payload.body.data, 'base64').toString('utf-8');
        } else if (payload.parts) {
            // Check for body in parts (for multipart messages)
            payload.parts.forEach((part: any) => {
                if (part.mimeType === 'text/plain' && part.body.data) {
                    body = Buffer.from(part.body.data, 'base64').toString('utf-8');
                }
            });
        }

        return body;
    };

    async sendReplyToMessage(userId: number, messageId: string, body: any) {
        const user = await User.findOne({
            where: { id: userId },
            relations: ['organization'],
        });

        const token = await AuthToken.findOne({
            where: {
                organizationId: user.organization.id,
                type: AuthTokenType.GOOGLE,
            },
        });

        if (!token) {
            const searchParams = new URLSearchParams({
                client_id: CLIENT_ID,
                scope: `${SCOPE_DRIVE} ${SCOPE_GMAIL} ${SCOPE_GMAIL_COMPOSE} ${SCOPE_GMAIL_MODIFY}`,
                response_type: RESPONSE_TYPE,
                redirect_uri: `${process.env.WEBSITE_URL}/googledrive-auth`,
                access_type: 'offline',
                prompt: "consent",
            });

            throw new UnprocessableEntityException({
                code: 'NO_TOKEN',
                message: 'User has not been authenticated with Google',
                authorizationUrl: `${AUTH_URL}?${searchParams.toString()}`,
            });
        }

        try {
            const originalMessage = await this.fetchGmailMessageMetaDataDetails(token, messageId);

            const contnet = body.body
            // Construct the reply message
            const replyMessage = {
                raw: this.createRawMessage(
                    originalMessage.sender,
                    originalMessage.subject,
                    originalMessage.date,
                    contnet,
                    // messageId,
                    originalMessage.messageId
                ),
                threadId: messageId
            };


            // Send the reply
            const response = await axios.post(`${SEND_API_URL}/send`, replyMessage, {
                headers: {
                    Authorization: `Bearer ${token.accessToken}`,
                    'Content-Type': 'application/json',
                },
            });

            return response.data;
        } catch (err) {
            console.error(err);
            const error = err.response?.data?.error;
            if (err?.response?.data?.error?.status === 'UNAUTHENTICATED') {
                await this.refreshToken(token);
                const response = await this.sendReplyToMessage(userId, messageId, body);
                return response;
            } else {
                console.log('error', err?.response?.data?.error?.status);
                throw new InternalServerErrorException(error);
            }
        }
    }

    // Helper method to create the raw MIME message format required by Gmail API
    private createRawMessage(to: string, subject: string, date: string, body: string, inReplyTo: string): string {
        const message = [
            `Content-Type: text/html; charset="UTF-8"`,
            `MIME-Version: 1.0`,
            `Content-Transfer-Encoding: 7bit`,
            `To: ${to}`,
            `Subject: Re: ${subject}`,
            `In-Reply-To: ${inReplyTo}`,
            `References: ${inReplyTo}`,
            `Date: ${new Date().toUTCString()}`,
            '',
            body,
        ].join('\n');
        // Base64url encode the message
        return Buffer.from(message)
            .toString('base64')
            .replace(/\+/g, '-')
            .replace(/\//g, '_')
            .replace(/=+$/, '');
    }


    async sendNewEmail(userId: number, emailDetails: { to: string, subject: string, body: string }) {
        const user = await User.findOne({
            where: { id: userId },
            relations: ['organization'],
        });

        const token = await AuthToken.findOne({
            where: {
                organizationId: user.organization.id,
                type: AuthTokenType.GOOGLE,
            },
        });

        if (!token) {
            const searchParams = new URLSearchParams({
                client_id: CLIENT_ID,
                scope: `${SCOPE_DRIVE} ${SCOPE_GMAIL} ${SCOPE_GMAIL_COMPOSE} ${SCOPE_GMAIL_MODIFY}`,
                response_type: RESPONSE_TYPE,
                redirect_uri: `${process.env.WEBSITE_URL}/googledrive-auth`,
                access_type: 'offline',
                prompt: "consent",
            });

            throw new UnprocessableEntityException({
                code: 'NO_TOKEN',
                message: 'User has not been authenticated with Google',
                authorizationUrl: `${AUTH_URL}?${searchParams.toString()}`,
            });
        }

        try {
            // Construct the email
            const rawMessage = this.composeRawMessage(
                emailDetails.to,
                emailDetails.subject,
                new Date().toISOString(),
                emailDetails.body
            );

            const email = {
                raw: rawMessage,
            };

            // Send the email
            const response = await axios.post(`${SEND_API_URL}/send`, email, {
                headers: {
                    Authorization: `Bearer ${token.accessToken}`,
                    'Content-Type': 'application/json',
                },
            });

            return response.data;
        } catch (err) {
            console.error(err);
            const error = err.response?.data?.error;
            if (err?.response?.data?.error?.status === 'UNAUTHENTICATED') {
                await this.refreshToken(token);
                const response = await this.sendNewEmail(userId, emailDetails);
                return response;
            } else {
                throw new InternalServerErrorException(error);
            }
        }
    }
    private composeRawMessage(to: string, subject: string, date: string, body: string): string {
        const emailLines = [
            `To: ${to}`,
            `Subject: ${subject}`,
            `Date: ${date}`,
            `MIME-Version: 1.0`,
            `Content-Type: text/plain; charset="UTF-8"`,
            ``,
            body,
        ];

        const emailContent = emailLines.join('\r\n');
        return Buffer.from(emailContent).toString('base64').replace(/\+/g, '-').replace(/\//g, '_').replace(/=+$/, '');
    }


    async uploadFile(args: IUpload) {
        const { file, body, userId } = args;
        const { buffer, mimetype } = file;
        const user = await User.findOne({
            where: { id: userId },
            relations: ['organization'],
        });

        const token = await AuthToken.findOne({
            where: {
                organizationId: user.organization.id,
                type: AuthTokenType.GOOGLE,
            },
        });
        if (!token) {
            throw new UnprocessableEntityException('No authentication token found. Please authenticate with OneDrive.');
        }

        let storage = new Storage();
        storage.fileType = file.mimetype;
        storage.fileSize = file.size;
        storage.name = file.originalname;
        storage.type = StorageType.FILE;
        storage.uid = uuidv4();
        storage.storageSystem = StorageSystem.GOOGLE;
        storage.user = user;

        let existingFile = await this.existing({
            name: file.originalname,
            parent: body.folderId,
            type: body.type,
            clientId: body.clientId,
            clientGroupId: body.clientGroup,
            orgId: user.organization.id,
            roomId: body.roomId,
        });
        if (existingFile) {
            throw new ConflictException('File with this name already exists');
        }



        let key: string;
        let upload: any;
        let atomFolder: Storage;
        let clientFolder: Storage;
        let dispalayNameFolder: Storage;
        let orgFileId: any;


        try {

            if (body.type === 'client') {
                let client = await Client.findOne({ where: { id: body.clientId }, relations: ['organization'] });
                if (body.folderId) {
                    const folder = await Storage.findOne({ where: { uid: body.folderId } });
                    key = `${folder.fileId}:/${file.originalname}:`;
                } else {

                    dispalayNameFolder = await Storage.findOne({
                        where: {
                            name: client.displayName,
                            organization: client.organization.id,
                            show: false,
                            type: StorageType.FOLDER,
                            client: client
                        }
                    });

                    if (!dispalayNameFolder) {
                        atomFolder = await Storage.findOne({
                            where: {
                                name: "Atom",
                                organization: client.organization.id,
                                show: false
                            }
                        });
                        if (!atomFolder) {
                            const folderData = await this.createGoogleDriveFolder(userId, "Atom");
                            atomFolder = new Storage();
                            atomFolder.name = 'Atom';
                            atomFolder.organization = user.organization;
                            atomFolder.type = StorageType.FOLDER;
                            atomFolder.uid = uuidv4();
                            atomFolder.fileId = folderData.id;
                            atomFolder.show = false;
                            atomFolder.storageSystem = StorageSystem.GOOGLE;
                            atomFolder.authId = user.organization.id;
                            await atomFolder.save();
                        };
                        clientFolder = await Storage.findOne({
                            where: {
                                name: "Clients",
                                organization: user.organization.id,
                                show: false
                            }
                        });
                        if (!clientFolder) {
                            const folderData = await this.createGoogleDriveFolder(userId, "Clients", atomFolder.fileId);
                            clientFolder = new Storage();
                            clientFolder.name = "Clients";
                            clientFolder.organization = user.organization;
                            clientFolder.type = StorageType.FOLDER;
                            clientFolder.uid = uuidv4();
                            clientFolder.fileId = folderData.id;
                            clientFolder.show = false;
                            clientFolder.storageSystem = StorageSystem.GOOGLE;
                            clientFolder.authId = user.organization.id;
                            clientFolder.parent = atomFolder;
                            await clientFolder.save();
                        };
                        dispalayNameFolder = await Storage.findOne({
                            where: {
                                name: client.displayName,
                                organization: user.organization.id,
                                show: false,
                                type: StorageType.FOLDER,
                                client: client
                            }
                        });
                        if (!dispalayNameFolder) {
                            const name = getName(client.displayName);
                            const folderData = await this.createGoogleDriveFolder(userId, client.displayName, clientFolder?.fileId);
                            dispalayNameFolder = new Storage();
                            dispalayNameFolder.name = client.displayName;
                            dispalayNameFolder.organization = user.organization;
                            dispalayNameFolder.type = StorageType.FOLDER;
                            dispalayNameFolder.uid = uuidv4();
                            dispalayNameFolder.fileId = folderData.id;
                            dispalayNameFolder.show = false;
                            dispalayNameFolder.storageSystem = StorageSystem.GOOGLE;
                            dispalayNameFolder.authId = user.organization.id;
                            dispalayNameFolder.parent = clientFolder;
                            dispalayNameFolder.client = client;
                            await dispalayNameFolder.save();
                        };

                    };
                    key = `${dispalayNameFolder.fileId}:/${file.originalname}:`;
                };
                upload = await this.uploadToGoogleDrive(file.originalname, token, buffer, dispalayNameFolder.fileId, userId);
                storage.client = client;
            };

            if (body.type === 'clientGroup') {
                let clientGroup = await ClientGroup.findOne({ where: { id: body.clientGroup }, relations: ['organization'] });
                if (body.folderId) {
                    const folder = await Storage.findOne({ where: { uid: body.folderId } });
                    key = `${folder.fileId}:/${file.originalname}:`;
                } else {

                    dispalayNameFolder = await Storage.findOne({
                        where: {
                            name: clientGroup.displayName,
                            organization: clientGroup.organization.id,
                            show: false,
                            clientGroup: clientGroup,
                            type: StorageType.FOLDER
                        }
                    });

                    if (!dispalayNameFolder) {
                        atomFolder = await Storage.findOne({
                            where: {
                                name: "Atom",
                                organization: clientGroup.organization.id,
                                show: false,
                            }
                        });
                        if (!atomFolder) {
                            const folderData = await this.createGoogleDriveFolder(userId, "Atom");
                            atomFolder = new Storage();
                            atomFolder.name = 'Atom';
                            atomFolder.organization = user.organization;
                            atomFolder.type = StorageType.FOLDER;
                            atomFolder.uid = uuidv4();
                            atomFolder.fileId = folderData.id;
                            atomFolder.show = false;
                            atomFolder.storageSystem = StorageSystem.GOOGLE;
                            atomFolder.authId = user.organization.id;
                            await atomFolder.save();
                        };
                        clientFolder = await Storage.findOne({
                            where: {
                                name: "Clients",
                                organization: user.organization.id,
                                show: false
                            }
                        });
                        if (!clientFolder) {
                            const folderData = await this.createGoogleDriveFolder(userId, "Clients", atomFolder.fileId);
                            clientFolder = new Storage();
                            clientFolder.name = "Clients";
                            clientFolder.organization = user.organization;
                            clientFolder.type = StorageType.FOLDER;
                            clientFolder.uid = uuidv4();
                            clientFolder.fileId = folderData.id;
                            clientFolder.show = false;
                            clientFolder.storageSystem = StorageSystem.GOOGLE;
                            clientFolder.authId = user.organization.id;
                            clientFolder.parent = atomFolder;
                            await clientFolder.save();
                        };
                        dispalayNameFolder = await Storage.findOne({
                            where: {
                                name: clientGroup.displayName,
                                organization: user.organization.id,
                                show: false,
                                clientGroup: clientGroup,
                                type: StorageType.FOLDER
                            }
                        });
                        if (!dispalayNameFolder) {
                            const name = getName(clientGroup.displayName);
                            const folderData = await this.createGoogleDriveFolder(userId, name, clientFolder?.fileId);
                            dispalayNameFolder = new Storage();
                            dispalayNameFolder.name = name;
                            dispalayNameFolder.organization = user.organization;
                            dispalayNameFolder.type = StorageType.FOLDER;
                            dispalayNameFolder.uid = uuidv4();
                            dispalayNameFolder.fileId = folderData.id;
                            dispalayNameFolder.show = false;
                            dispalayNameFolder.storageSystem = StorageSystem.GOOGLE;
                            dispalayNameFolder.authId = user.organization.id;
                            dispalayNameFolder.parent = clientFolder;
                            dispalayNameFolder.clientGroup = clientGroup;
                            await dispalayNameFolder.save();
                        };

                    };
                    // key = `${dispalayNameFolder.fileId}:/${file.originalname.replace(/[<>:"\/\\|?*.]/g, '')}:`;
                    key = `${dispalayNameFolder.fileId}:/${file.originalname.replace(/[<>:"\/\\|?*]/g, '')
                        .replace(/\.(?=.*\.)/g, '')}:`;
                };
                upload = await this.uploadToGoogleDrive(file.originalname, token, buffer, dispalayNameFolder.fileId, userId);
                storage.clientGroup = clientGroup;
            };

            if (body.type === 'organization') {
                let orgFolder: Storage;
                if (body?.folderId) {
                    const parentFolder = await Storage.findOne({ where: { uid: body.folderId } });
                    // const oneDriveFolder = await this.oneDriveService.createOneDriveFolder(userId, body.name, parentFolder.fileId);
                    // storage.fileId = oneDriveFolder.id;
                    // storage.storageSystem = StorageSystem.MICROSOFT;
                    key = `${parentFolder.fileId}:/${file.originalname}:`;
                } else {
                    atomFolder = await Storage.findOne({
                        where: {
                            name: "Atom",
                            organization: user.organization.id,
                            show: false
                        }
                    });
                    if (!atomFolder) {
                        const folderData = await this.createGoogleDriveFolder(userId, "Atom");
                        atomFolder = new Storage();
                        atomFolder.name = 'Atom';
                        atomFolder.organization = user.organization;
                        atomFolder.type = StorageType.FOLDER;
                        atomFolder.uid = uuidv4();
                        atomFolder.fileId = folderData.id;
                        atomFolder.show = false;
                        atomFolder.storageSystem = StorageSystem.GOOGLE;
                        atomFolder.authId = user.organization.id;
                        await atomFolder.save();
                    };
                    orgFolder = await Storage.findOne({
                        where: {
                            name: "Organization Storage",
                            organization: user.organization.id,
                            show: false
                        }
                    });
                    if (!orgFolder) {
                        const folder = await this.createGoogleDriveFolder(userId, "Organization Storage", atomFolder.fileId);
                        orgFolder = new Storage();
                        orgFolder.name = "Organization Storage";
                        orgFolder.organization = user.organization;
                        orgFolder.type = StorageType.FOLDER;
                        orgFolder.uid = uuidv4();
                        orgFolder.fileId = folder.id;
                        orgFolder.show = false;
                        orgFolder.storageSystem = StorageSystem.GOOGLE;
                        orgFolder.authId = user.organization.id;
                        orgFolder.parent = atomFolder;
                        await orgFolder.save();
                    };
                    // key = `${orgFolder.fileId}:/${file.originalname.replace(/[<>:"\/\\|?*.]/g, '')}:`;
                    key = `${orgFolder.fileId}:/${file.originalname.replace(/[<>:"\/\\|?*]/g, '')
                        .replace(/\.(?=.*\.)/g, '')}:`;

                }
                // key = `root:/Atom/Organization Storage/${file.originalname}:`;
                let orgFileId = orgFolder.fileId;
                upload = await this.uploadToGoogleDrive(file.originalname, token, buffer, orgFolder.fileId, userId);
                storage.organization = user.organization;
            };

        } catch (err) {
            let error = err?.response?.data?.error;
            console.log(err);
            if (error.code === 'InvalidAuthenticationToken') {
                await this.refreshToken(token);
                upload = await this.uploadToGoogleDrive(file.originalname, token, buffer, orgFileId, userId);
            } else if (error.code === 'quotaLimitReached') {
                throw new ConflictException({
                    code: 'NO_STORAGE',
                    message: error.message,
                });
            }
        };
        storage.file = upload.file;
        storage.webUrl = upload.webUrl;
        storage.downloadUrl = upload["@microsoft.graph.downloadUrl"];
        storage.fileId = upload.id;
        storage.authId = user.organization.id;

        if (body.folderId) {
            let folder = await Storage.findOne({ where: { uid: body.folderId } });
            storage.parent = folder;
        };
        await storage.save();
        return storage;
    };

    async createGoogleDriveFolder(userId: number, folderName: string, parentFolderId?: string) {
        const user = await User.findOne({
            where: { id: userId },
            relations: ['organization'],
        });

        if (!user) {
            throw new BadRequestException('User not found');
        }

        const token = await AuthToken.findOne({
            where: {
                organizationId: user.organization.id,
                type: AuthTokenType.GOOGLE,
            },
        });

        if (!token) {
            throw new UnprocessableEntityException('No authentication token found. Please authenticate with Google Drive.');
        }

        try {
            const url = 'https://www.googleapis.com/drive/v3/files';
            const response = await axios({
                method: 'POST',
                url,
                headers: {
                    Authorization: `Bearer ${token.accessToken}`,
                    'Content-Type': 'application/json',
                },
                data: {
                    name: folderName.replace(/[<>:"\/\\|?*.]/g, ' '), // Remove invalid characters
                    mimeType: 'application/vnd.google-apps.folder',
                    parents: parentFolderId ? [parentFolderId] : [], // Set parent folder if provided
                },
            });

            return response.data;
        } catch (err) {
            const error = err?.response?.data?.error;
            if (error?.code === 401) { // Invalid token
                await this.refreshToken(token);
                return this.createGoogleDriveFolder(userId, folderName, parentFolderId); // Retry with refreshed token
            } else {
                throw new InternalServerErrorException(error?.message || 'Failed to create folder');
            }
        }
    }


    async existing(props: IExisting) {
        const { name, parent, type, orgId, clientId, clientGroupId, roomId } = props;

        let existing = createQueryBuilder(Storage, 'storage');

        let where = `storage.name = :name`;

        if (type === 'client') {
            existing.leftJoin('storage.client', 'client');
            where += ` and client.id = :clientId`;
        }

        if (type === 'clientGroup') {
            existing.leftJoin('storage.clientGroup', 'clientGroup');
            where += ` and clientGroup.id = :clientId`;
        }

        if (type === 'organization') {
            existing.leftJoin('storage.organization', 'organization');
            where += ` and organization.id = :orgId`;
        }
        if (type === 'chat') {
            existing.leftJoin('storage.room', 'room');
            where += ` and room.id = :roomId`;
        }

        if (parent) {
            existing.leftJoin('storage.parent', 'parent');
            where += ` and parent.uid = :parent`;
        }

        if (!parent) {
            where += ` and storage.parent is null`;
        }

        existing.where(`(${where})`, { name, parent, clientId, orgId, roomId });

        let result = await existing.getOne();
        return Boolean(result);
    };

    async uploadToGoogleDrive(fileName: string, token: AuthToken, fileBuffer, parentFolderId = "root", userId: number) {
        try {
            const metadata = {
                name: fileName,
                parents: parentFolderId !== "root" ? [parentFolderId] : [],
            };

            const boundary = `boundary-${Date.now()}`;
            const delimiter = `\r\n--${boundary}\r\n`;
            const closeDelimiter = `\r\n--${boundary}--`;

            const metadataPart = `Content-Type: application/json; charset=UTF-8\r\n\r\n${JSON.stringify(metadata)}`;
            const filePart = `Content-Type: application/octet-stream\r\n\r\n`;

            // Combine metadata and file data
            const multipartBody = Buffer.concat([
                Buffer.from(delimiter + metadataPart + delimiter + filePart),
                fileBuffer,
                Buffer.from(closeDelimiter),
            ]);

            const uploadUrl = "https://www.googleapis.com/upload/drive/v3/files?uploadType=multipart";

            const response = await axios.request({
                method: "POST",
                url: uploadUrl,
                headers: {
                    Authorization: `Bearer ${token.accessToken}`,
                    "Content-Type": `multipart/related; boundary=${boundary}`,
                },
                data: multipartBody,
            });
            const fileId = response?.data?.id;
            //Make the file publicly accessible
            await this.makeFilePublic(fileId, token.accessToken);


            // Fetch file details (like thumbnail URL)
            const fileDetails = await this.getGoogleDriveFileDetailsById(fileId, userId);

            return { ...response.data, file: fileDetails?.thumbnailLink };
        } catch (err) {
            const error = err?.response?.data?.error;
            console.log(err);

            if (error?.code === 401) {
                // Handle expired token
                await this.refreshToken(token);
                return await this.uploadToGoogleDrive(fileName, token, fileBuffer, parentFolderId, userId);
            } else if (error?.code === 403) {
                return "accessDenied";
            } else {
                throw new Error(error || "Failed to upload file");
            }
        }
    };
    async getGoogleDriveFileDetailsById(fileId: string, userId: any) {
        const user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
        if (!user) {
            throw new BadRequestException('User not found');
        }

        const token = await AuthToken.findOne({
            where: {
                organizationId: user.organization.id,
                type: AuthTokenType.GOOGLE,
            },
        });

        if (!token) {
            throw new UnprocessableEntityException('No authentication token found. Please authenticate with Google Drive.');
        }

        try {
            const url = `https://www.googleapis.com/drive/v3/files/${fileId}?fields=id,name,mimeType,thumbnailLink,webViewLink`;
            const headers = {
                'Authorization': `Bearer ${token.accessToken}`,
                'Content-Type': 'application/json',
            };

            const response = await axios.get(url, { headers });

            return response.data;
        } catch (err) {
            const error = err?.response?.data?.error;
            if (error?.code === 401) { // Invalid or expired token
                await this.refreshToken(token);
                return this.getGoogleDriveFileDetailsById(fileId, userId); // Retry with refreshed token
            } else if (error?.code === 404) { // File not found
                return { success: true };
            } else {
                throw new InternalServerErrorException(error?.message || 'Failed to retrieve file details');
            }
        }
    };

    async makeFilePublic(fileId: string, accessToken: string) {
        const permissionsUrl = `https://www.googleapis.com/drive/v3/files/${fileId}/permissions`;

        const body = {
            role: "reader",
            type: "anyone",
        };

        try {
            await axios.post(permissionsUrl, body, {
                headers: {
                    Authorization: `Bearer ${accessToken}`,
                    "Content-Type": "application/json",
                },
            });
        } catch (err) {
            console.error("Failed to make file public:", err.response?.data);
        }
    }


    async addAttachments(taskId: number, files: Express.Multer.File[], userId: number) {
        try {
            let task = await Task.findOne({
                where: { id: taskId },
                relations: ['client', 'category', 'subCategory', 'user', 'organization', 'clientGroup'],
            });

            let user = await User.findOne({
                where: { id: userId },
                relations: ['organization'],
            });

            let token = await AuthToken.findOne({
                where: {
                    organizationId: user.organization.id,
                    type: AuthTokenType.GOOGLE,
                },
            });


            let taskStorage = await this.attachmentsService.existingClientTaskStorage(task, StorageSystem.GOOGLE);
            let taskAttachments: Storage[] = [];
            let errors: string[] = [];

            for (let file of files) {
                const { buffer, mimetype, originalname, size } = file;
                const existingAttachement = await Storage.find({ where: { parent: taskStorage, name: originalname } })
                if (existingAttachement?.length) {
                    errors.push(`File name ${originalname} already exists`);
                    continue;
                }
                // let key = `${taskStorage.fileId}:/${file.originalname.replace(/[<>:"\/\\|?*.]/g, '')}:`;
                let key = `${taskStorage.fileId}:/${file.originalname.replace(/[<>:"\/\\|?*]/g, '')
                    .replace(/\.(?=.*\.)/g, '')}:`;
                let upload: any;
                try {
                    upload = await this.uploadToGoogleDrive(file.originalname, token, buffer, taskStorage?.fileId, userId);
                } catch (err) {
                    let error = err?.response?.data?.error;
                    if (error.code === 'InvalidAuthenticationToken') {
                        await this.refreshToken(token);
                        upload = await this.uploadToGoogleDrive(file.originalname, token, buffer, taskStorage?.fileId, userId);
                    } else if (error.code === 'quotaLimitReached') {
                        throw new ConflictException({
                            code: 'NO_STORAGE',
                            message: error.message,
                        });
                    };
                };
                let storage = new Storage();
                storage.name = originalname;
                storage.file = upload.file;
                storage.webUrl = upload.webUrl;
                storage.downloadUrl = upload["@microsoft.graph.downloadUrl"];
                storage.fileId = upload.id;
                storage.authId = user.organization.id;
                storage.task = task;
                storage.fileSize = size;
                storage.fileType = mimetype;
                storage.client = task.client;
                storage.clientGroup = task?.clientGroup;
                storage.type = StorageType.FILE;
                storage.parent = taskStorage;
                storage.storageSystem = StorageSystem.GOOGLE;
                storage.user = user;
                taskAttachments.push(storage);
            };

            await Storage.save(taskAttachments);

            if (errors?.length) {
                return { errors }
            } else {
                return {
                    success: true,
                };
            }


        } catch (err) {
            console.log(err);
            throw new InternalServerErrorException(err);
        }
    };
    async attachementsUpload(args: IUpload) {
        const { file, body, userId } = args;
        const { stageid } = body;
        const { buffer, mimetype } = file;
        const user = await User.findOne({
            where: { id: userId },
            relations: ['organization'],
        });

        let token = await AuthToken.findOne({
            where: {
                organizationId: user.organization.id,
                type: AuthTokenType.GOOGLE,
            },
        });
        const searchParams = new URLSearchParams({
            client_id: CLIENT_ID,
            scope: `${SCOPE_DRIVE} ${SCOPE_GMAIL} ${SCOPE_GMAIL_COMPOSE} ${SCOPE_GMAIL_MODIFY}`,
            response_type: RESPONSE_TYPE,
            redirect_uri: `${process.env.WEBSITE_URL}/googledrive-auth`,
        });

        if (!token) {
            throw new UnprocessableEntityException({
                code: 'NO_TOKEN',
                message: 'User has not been authenticated with Google',
                authorizationUrl: `${AUTH_URL}?${searchParams.toString()}`,
            });
        }

        let existingFile = await this.existing({
            name: file.originalname,
            parent: body.folderId,
            type: body.type,
            clientId: body.clientId,
            clientGroupId: null,
            orgId: user.organization.id,
            roomId: body.roomId,
        });

        if (body.type !== 'chat' && existingFile) {
            throw new ConflictException('File with this name already exists');
        }
        const { storageLimit, freeSpace } = await this.storageService.getOrgStorage(userId);
        if (!(freeSpace - +file.size > 0)) {
            throw new ConflictException(
                'We regret to inform you that the storage limit for your organization has been exceeded. As a result, no further data can be stored until the storage usage is reduced or additional storage capacity is allocated.',
            );
        }

        let key: string;
        let upload: any;
        let parentId: string;
        let kybStorage: Storage;

        try {
            if (body.type === 'client') {
                let finalFolder: Storage;
                let client = await Client.findOne({ where: { id: body.clientId }, relations: ['organization'] });
                let dispalayNameFolder: Storage;
                let atomFolder: Storage;
                let clientFolder: Storage;
                dispalayNameFolder = await Storage.findOne({
                    where: {
                        name: client.displayName,
                        organization: client.organization.id,
                        show: false,
                        type: StorageType.FOLDER,
                        client: client,
                    },
                });
                if (!dispalayNameFolder) {
                    atomFolder = await Storage.findOne({
                        where: {
                            name: "Atom",
                            organization: client.organization.id,
                            show: false,
                        },
                    });
                    if (!atomFolder) {
                        const folderData = await this.createGoogleDriveFolder(userId, "Atom");
                        atomFolder = new Storage();
                        atomFolder.name = 'Atom';
                        atomFolder.organization = user.organization;
                        atomFolder.type = StorageType.FOLDER;
                        atomFolder.uid = uuidv4();
                        atomFolder.fileId = folderData.id;
                        atomFolder.show = false;
                        atomFolder.storageSystem = StorageSystem.GOOGLE;
                        atomFolder.authId = user.organization.id;
                        await atomFolder.save();
                    }
                    clientFolder = await Storage.findOne({
                        where: {
                            name: "Clients",
                            organization: user.organization.id,
                            show: false,
                        },
                    });
                    if (!clientFolder) {
                        const folderData = await this.createGoogleDriveFolder(userId, "Clients", atomFolder.fileId);
                        clientFolder = new Storage();
                        clientFolder.name = "Clients";
                        clientFolder.organization = user.organization;
                        clientFolder.type = StorageType.FOLDER;
                        clientFolder.uid = uuidv4();
                        clientFolder.fileId = folderData.id;
                        clientFolder.show = false;
                        clientFolder.storageSystem = StorageSystem.GOOGLE;
                        clientFolder.authId = user.organization.id;
                        clientFolder.parent = atomFolder;
                        await clientFolder.save();
                    }
                    dispalayNameFolder = await Storage.findOne({
                        where: {
                            name: client.displayName,
                            organization: user.organization.id,
                            show: false,
                            type: StorageType.FOLDER,
                            client: client,
                        },
                    });
                    if (!dispalayNameFolder) {
                        const folderData = await this.createGoogleDriveFolder(userId, client.displayName, clientFolder?.fileId);
                        dispalayNameFolder = new Storage();
                        dispalayNameFolder.name = client.displayName;
                        dispalayNameFolder.organization = user.organization;
                        dispalayNameFolder.type = StorageType.FOLDER;
                        dispalayNameFolder.uid = uuidv4();
                        dispalayNameFolder.fileId = folderData.id;
                        dispalayNameFolder.show = false;
                        dispalayNameFolder.storageSystem = StorageSystem.GOOGLE;
                        dispalayNameFolder.authId = user.organization.id;
                        dispalayNameFolder.parent = clientFolder;
                        dispalayNameFolder.client = client;
                        await dispalayNameFolder.save();
                    }
                };
                finalFolder = dispalayNameFolder;
                if (body.kyb == "true") {
                    const clientId = body.clientId;
                    const storageType = user.organization.storageSystem;
                    kybStorage = await this.storageService.existingKybStorage(userId, clientId, undefined, storageType);
                    finalFolder = kybStorage;
                };

                key = `${finalFolder.fileId}:/${file.originalname.replace(/[<>:"\/\\|?*]/g, '').replace(/\.(?=.*\.)/g, '')}:`;
                const fileName = file.originalname.replace(/[<>:"\/\\|?*]/g, '').replace(/\.(?=.*\.)/g, '');
                parentId = finalFolder.fileId; // Set parentId
                upload = await this.uploadToGoogleDrive(fileName, token, buffer, parentId, userId);
            }

            if (body.type === 'clientGroup') {
                let finalFolder: Storage;
                let clientGroup = await ClientGroup.findOne({ where: { id: body.clientId }, relations: ['organization'] });
                let dispalayNameFolder: Storage;
                let atomFolder: Storage;
                let clientFolder: Storage;
                dispalayNameFolder = await Storage.findOne({
                    where: {
                        name: clientGroup.displayName,
                        organization: clientGroup.organization.id,
                        show: false,
                        type: StorageType.FOLDER,
                        clientGroup: clientGroup,
                    },
                });

                if (!dispalayNameFolder) {
                    atomFolder = await Storage.findOne({
                        where: {
                            name: "Atom",
                            organization: clientGroup.organization.id,
                            show: false,
                        },
                    });
                    if (!atomFolder) {
                        const folderData = await this.createGoogleDriveFolder(userId, "Atom");
                        atomFolder = new Storage();
                        atomFolder.name = 'Atom';
                        atomFolder.organization = user.organization;
                        atomFolder.type = StorageType.FOLDER;
                        atomFolder.uid = uuidv4();
                        atomFolder.fileId = folderData.id;
                        atomFolder.show = false;
                        atomFolder.storageSystem = StorageSystem.GOOGLE;
                        atomFolder.authId = user.organization.id;
                        await atomFolder.save();
                    }
                    clientFolder = await Storage.findOne({
                        where: {
                            name: "Clients",
                            organization: user.organization.id,
                            show: false,
                        },
                    });
                    if (!clientFolder) {
                        const folderData = await this.createGoogleDriveFolder(userId, "Clients", atomFolder.fileId);
                        clientFolder = new Storage();
                        clientFolder.name = "Clients";
                        clientFolder.organization = user.organization;
                        clientFolder.type = StorageType.FOLDER;
                        clientFolder.uid = uuidv4();
                        clientFolder.fileId = folderData.id;
                        clientFolder.show = false;
                        clientFolder.storageSystem = StorageSystem.GOOGLE;
                        clientFolder.authId = user.organization.id;
                        clientFolder.parent = atomFolder;
                        await clientFolder.save();
                    }
                    dispalayNameFolder = await Storage.findOne({
                        where: {
                            name: clientGroup.displayName,
                            organization: user.organization.id,
                            show: false,
                            type: StorageType.FOLDER,
                            clientGroup: clientGroup,
                        },
                    });
                    if (!dispalayNameFolder) {
                        const folderData = await this.createGoogleDriveFolder(userId, clientGroup.displayName, clientFolder?.fileId);
                        dispalayNameFolder = new Storage();
                        dispalayNameFolder.name = clientGroup.displayName;
                        dispalayNameFolder.organization = user.organization;
                        dispalayNameFolder.type = StorageType.FOLDER;
                        dispalayNameFolder.uid = uuidv4();
                        dispalayNameFolder.fileId = folderData.id;
                        dispalayNameFolder.show = false;
                        dispalayNameFolder.storageSystem = StorageSystem.GOOGLE;
                        dispalayNameFolder.authId = user.organization.id;
                        dispalayNameFolder.parent = clientFolder;
                        await dispalayNameFolder.save();
                    }
                }
                finalFolder = dispalayNameFolder;
                if (body?.kyb === 'true') {
                    const clientGroupId = body.clientId;
                    const storageType = user.organization.storageSystem;
                    kybStorage = await this.storageService.existingKybStorage(userId, undefined, clientGroupId, storageType);
                    finalFolder = kybStorage;
                }
                key = `${finalFolder.fileId}:/${file.originalname.replace(/[<>:"\/\\|?*]/g, '').replace(/\.(?=.*\.)/g, '')}:`;
                const fileName = file.originalname.replace(/[<>:"\/\\|?*]/g, '').replace(/\.(?=.*\.)/g, '');
                parentId = finalFolder.fileId; // Set parentId
                upload = await this.uploadToGoogleDrive(fileName, token, buffer, parentId, userId);
            }

            if (body.type === 'organization') {
                let atomFolder: Storage;
                let orgFolder: Storage;

                atomFolder = await Storage.findOne({
                    where: {
                        name: "Atom",
                        organization: user.organization.id,
                        show: false,
                    },
                });
                if (!atomFolder) {
                    const folderData = await this.createGoogleDriveFolder(userId, "Atom");
                    atomFolder = new Storage();
                    atomFolder.name = 'Atom';
                    atomFolder.organization = user.organization;
                    atomFolder.type = StorageType.FOLDER;
                    atomFolder.uid = uuidv4();
                    atomFolder.fileId = folderData.id;
                    atomFolder.show = false;
                    atomFolder.storageSystem = StorageSystem.GOOGLE;
                    atomFolder.authId = user.organization.id;
                    await atomFolder.save();
                }
                orgFolder = await Storage.findOne({
                    where: {
                        name: "Organization Storage",
                        organization: user.organization.id,
                        show: false,
                    },
                });
                if (!orgFolder) {
                    const folder = await this.createGoogleDriveFolder(userId, "Organization Storage", atomFolder.fileId);
                    orgFolder = new Storage();
                    orgFolder.name = "Organization Storage";
                    orgFolder.organization = user.organization;
                    orgFolder.type = StorageType.FOLDER;
                    orgFolder.uid = uuidv4();
                    orgFolder.fileId = folder.id;
                    orgFolder.show = false;
                    orgFolder.storageSystem = StorageSystem.GOOGLE;
                    orgFolder.authId = user.organization.id;
                    orgFolder.parent = atomFolder;
                    await orgFolder.save();
                }

                key = `${orgFolder.fileId}:/${file.originalname.replace(/[<>:"\/\\|?*]/g, '').replace(/\.(?=.*\.)/g, '')}:`;
                const fileName = file.originalname.replace(/[<>:"\/\\|?*]/g, '').replace(/\.(?=.*\.)/g, '');
                parentId = orgFolder.fileId; // Set parentId
                upload = await this.uploadToGoogleDrive(fileName, token, buffer, parentId, userId);
            }

            if (body.type === 'chat') {
                let atomFolder: Storage;
                let orgFolder: Storage;

                atomFolder = await Storage.findOne({
                    where: {
                        name: "Atom",
                        organization: user.organization.id,
                        show: false,
                    },
                });
                if (!atomFolder) {
                    const folderData = await this.createGoogleDriveFolder(userId, "Atom");
                    atomFolder = new Storage();
                    atomFolder.name = 'Atom';
                    atomFolder.organization = user.organization;
                    atomFolder.type = StorageType.FOLDER;
                    atomFolder.uid = uuidv4();
                    atomFolder.fileId = folderData.id;
                    atomFolder.show = false;
                    atomFolder.storageSystem = StorageSystem.GOOGLE;
                    atomFolder.authId = user.organization.id;
                    await atomFolder.save();
                }
                orgFolder = await Storage.findOne({
                    where: {
                        name: "Organization Storage",
                        organization: user.organization.id,
                        show: false,
                    },
                });
                if (!orgFolder) {
                    const folder = await this.createGoogleDriveFolder(userId, "Organization Storage", atomFolder.fileId);
                    orgFolder = new Storage();
                    orgFolder.name = "Organization Storage";
                    orgFolder.organization = user.organization;
                    orgFolder.type = StorageType.FOLDER;
                    orgFolder.uid = uuidv4();
                    orgFolder.fileId = folder.id;
                    orgFolder.show = false;
                    orgFolder.storageSystem = StorageSystem.GOOGLE;
                    orgFolder.authId = user.organization.id;
                    orgFolder.parent = atomFolder;
                    await orgFolder.save();
                }

                const fileName = file.originalname.replace(/[<>:"\/\\|?*]/g, '').replace(/\.(?=.*\.)/g, '');
                parentId = orgFolder.fileId; // Set parentId
                upload = await this.uploadToGoogleDrive(fileName, token, buffer, parentId, userId);
            }
        } catch (err) {
            let error = err?.response?.data?.error;
            if (error?.code === 'InvalidAuthenticationToken') {
                await this.refreshToken(token);
                const fileName = file.originalname.replace(/[<>:"\/\\|?*]/g, '').replace(/\.(?=.*\.)/g, '');
                upload = await this.uploadToGoogleDrive(fileName, token, buffer, parentId, userId); // Pass parentId and userId
            } else if (error?.code === 'quotaLimitReached') {
                throw new ConflictException({
                    code: 'NO_STORAGE',
                    message: error.message,
                });
            } else {
                throw err; // Re-throw unhandled errors
            }
        }

        return {
            key,
            upload: upload.file,
            webUrl: upload.webUrl,
            downloadUrl: upload["@microsoft.graph.downloadUrl"],
            fileId: upload.id,
            fileSize: file.size,
            fileType: file.mimetype,
            clientId: body.clientId,
            name: file.originalname,
            authId: user.organization.id,
            storageSystem: StorageSystem.GOOGLE,
            file: upload.file,
            stageid,
            ...(kybStorage && { parentId: kybStorage.id })
        };
    }

    async collectDataAddAttc(
        origin: string,
        collectId: any,
        taskId: number,
        files: Express.Multer.File[],
    ) {
        try {
            let task = await Task.findOne({
                where: { id: taskId },
                relations: ['client', 'category', 'subCategory', 'organization', 'user', 'clientGroup'],
            });

            let token = await AuthToken.findOne({
                where: {
                    organizationId: task.organization.id,
                    type: AuthTokenType.GOOGLE,

                }
            });

            let taskStorage = await this.attachmentsService.existingClientTaskStorage(task, StorageSystem.GOOGLE);

            let taskAttachments: Storage[] = [];
            const collectData = await CollectData.findOne({ where: { uid: collectId } })

            for (let file of files) {
                const { buffer, mimetype, originalname } = file;
                let key = `${taskStorage.fileId}:/${file.originalname.replace(/[<>:"\/\\|?*]/g, '')
                    .replace(/\.(?=.*\.)/g, '')}:`;
                let fileName = file.originalname.replace(/[<>:"\/\\|?*]/g, '')
                    .replace(/\.(?=.*\.)/g, '');
                let upload: any;

                try {
                    upload = await this.uploadToGoogleDrive(fileName, token, buffer, taskStorage.fileId, task.user.id);

                } catch (err) {
                    let error = err?.response?.data?.error;
                    if (error.code === 'InvalidAuthenticationToken') {
                        await this.refreshToken(token);
                        upload = await this.uploadToGoogleDrive(fileName, token, buffer, taskStorage.fileId, task.user.id);
                    } else if (error.code === 'quotaLimitReached') {
                        throw new ConflictException({
                            code: 'NO_STORAGE',
                            message: error.message,
                        });
                    }
                }

                let storage = new Storage();
                storage.name = originalname;
                storage.file = upload.file;
                storage.webUrl = upload.webUrl;
                storage.authId = task.organization.id;
                storage.downloadUrl = upload["@microsoft.graph.downloadUrl"];
                storage.fileId = upload.id;
                storage.task = task;
                storage.fileType = mimetype;
                storage.fileSize = file.size;
                storage.client = task.client;
                storage.clientGroup = task.clientGroup;
                storage.type = StorageType.FILE;
                storage.parent = taskStorage;
                storage.storageSystem = StorageSystem.GOOGLE;
                storage.collectId = collectData?.id;
                storage.origin = origin;
                taskAttachments.push(storage);
            };

            await Storage.save(taskAttachments);
            this.eventEmitter.emit(Event_Actions.COLLECT_DATA_FILES_UPLOAD, {
                origin,
                collectId,
                taskId,
                files: files.map(file => file.originalname)
            });

            return {
                success: true,
            };
        } catch (err) {
            console.log(err);
            throw new InternalServerErrorException(err);
        }
    };

    async deleteGoogleDriveFile(userId: number, fileId: string, orgId?: number) {
        if (!fileId) return null;

        const user = await User.findOne({
            where: { id: userId },
            relations: ['organization'],
        });

        if (!user && !orgId) {
            throw new BadRequestException('User not found');
        }

        const token = await AuthToken.findOne({
            where: {
                organizationId: user?.organization?.id || orgId,
                type: AuthTokenType.GOOGLE,
            },
        });

        if (!token) {
            throw new UnprocessableEntityException('No authentication token found. Please authenticate with Google Drive.');
        }

        try {
            const url = `https://www.googleapis.com/drive/v3/files/${fileId}`;
            await axios({
                method: 'DELETE',
                url,
                headers: {
                    Authorization: `Bearer ${token.accessToken}`,
                    'Content-Type': 'application/json',
                },
            });
            return { success: true };
        } catch (err) {
            const error = err?.response?.data?.error;
            if (error?.code === 401) { // Unauthorized - token might be expired
                await this.refreshToken(token);
                return await this.deleteGoogleDriveFile(userId, fileId);
            } else if (error?.code === 404) { // File not found
                return { success: true }; // Consider it deleted if it doesn't exist
            } else {
                throw new InternalServerErrorException(error?.message || 'Failed to delete file');
            }
        }
    }

    async copyFile(fileId: string, userId: number, newFileName?: string, destinationFolderId?: string) {
        const user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
        if (!user) {
            throw new BadRequestException('User not found');
        }

        const token = await AuthToken.findOne({
            where: {
                organizationId: user.organization.id,
                type: AuthTokenType.GOOGLE,
            },
        });

        if (!token) {
            throw new UnprocessableEntityException('No authentication token found. Please authenticate with Google Drive.');
        }

        let parentFolderId = destinationFolderId;
        if (!destinationFolderId) {
            try {
                const fileDetails = await this.getGoogleDriveFileDetailsById(fileId, userId);
                parentFolderId = fileDetails.parents?.[0]; // Use the parent folder of the original file
            } catch (error) {
                console.error('Error retrieving file details:', error.response ? error.response.data : error.message);
                return;
            }
        }

        try {
            const url = `https://www.googleapis.com/drive/v3/files/${fileId}/copy`;
            const headers = {
                'Authorization': `Bearer ${token.accessToken}`,
                'Content-Type': 'application/json',
            };

            const body: any = {
                name: `${newFileName ?? ''}-${moment().valueOf()}`
            };
            if (parentFolderId) {
                body.parents = [parentFolderId];
            }

            const response = await axios.post(url, body, { headers });
            const copiedFile = response.data;

            // Make the copied file public
            await this.makeFilePublic(copiedFile.id, token.accessToken);

            // Fetch file details (e.g., thumbnailLink)
            const fileDetails = await this.getGoogleDriveFileDetailsById(copiedFile.id, userId);

            return { ...copiedFile, file: fileDetails?.thumbnailLink };
        } catch (err) {
            const error = err?.response?.data?.error;
            console.log(err);
            if (error?.code === 401) {
                await this.refreshToken(token);
                return this.copyFile(fileId, userId, newFileName, destinationFolderId);
            } else {
                console.error('Error copying file:', error?.message || err.message);
                throw new InternalServerErrorException(error?.message || 'Failed to copy file');
            }
        }
    }



}
