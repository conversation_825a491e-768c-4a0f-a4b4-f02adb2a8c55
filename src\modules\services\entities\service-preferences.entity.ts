import {
    BaseEntity,
    Column,
    CreateDateColumn,
    Entity,
    PrimaryGeneratedColumn,
    UpdateDateColumn,
  } from 'typeorm';
  
  @Entity('service_preferences')
  export class ServicePreferences extends BaseEntity {
    @PrimaryGeneratedColumn()
    id: number;
  
    @Column()
    serviceId: number;
  
    @Column()
    organizationId: number;

    @CreateDateColumn()
    createdAt: string;
  
    @UpdateDateColumn()
    updatedAt: string;
  }
  
  export default ServicePreferences;