import { MiddlewareConsumer, Module, NestModule } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { MongooseModule } from '@nestjs/mongoose';
import { ScheduleModule } from '@nestjs/schedule';
import { TypeOrmModule } from '@nestjs/typeorm';
import { DscRegisterModule } from 'src/modules/dsc-register/dsc-register.module';
import { ExpenditureModule } from 'src/modules/expenditure/expenditure.module';
import { LeadsModule } from 'src/modules/leads/leads.module';
import { LogHoursModule } from 'src/modules/log-hours/log-hours.module';
import { OneDriveStorageModule } from 'src/modules/ondrive-storage/onedrive-storage.module';
import { ReportsModule } from 'src/modules/reports/reports.module';
import { StatsModule } from 'src/modules/stats/stats.module';
import db_config from '../config/db_config';
import mongo_config from '../config/mongo_config';
import { ActivityModule } from '../modules/activity/activity.module';
import { AdminModule } from '../modules/admin/admin.module';
import { BillingModule } from '../modules/billing/billing.module';
import { CategoriesModule } from '../modules/categories/categories.module';
import { ChatsModule } from '../modules/chats/chats.module';
import { ClientPanelModule } from '../modules/client-panel/client-panel.module';
import { ClientModule } from '../modules/clients/clients.module';
import { CommonModule } from '../modules/common/common.module';
import { EventsModule } from '../modules/events/events.module';
import { FormsModule } from '../modules/forms/forms.module';
import { GetStartedModule } from '../modules/get-started/get-started.module';
import { LabelModule } from '../modules/labels/label.module';
import { OrganizationsModule } from '../modules/organization/organization.module';
import { RecurringModule } from '../modules/recurring/recurring.module';
import { PermissionsModule } from '../modules/roles/permissions.module';
import { ServicesModule } from '../modules/services/services.module';
import { StatesModule } from '../modules/states/states.module';
import { StorageModule } from '../modules/storage/storage.module';
import { TasksModule } from '../modules/tasks/tasks.module';
import { UsersModule } from '../modules/users/users.module';
import { NotificationsModule } from '../notifications/notifications.module';
import { PushNotificationModule } from 'src/modules/notification-settings/notifications-prreferences.module';
import { StrapiModule } from 'src/modules/strapi/strapi.module';
import { SandboxModule } from 'src/modules/sandbox/sandbox.module';
import { GstrRegisterModule } from 'src/modules/gstr-register/gstr-register.module';
import { WebhookModule } from 'src/modules/webhook/webhook.module';
import { AtomSuperAdminActivityModule } from 'src/modules/atom-super-admin-activity/atom_super_admin_activity.module';
import { AttendanceModule } from 'src/modules/attendance/attendance.module';
import OrganizationPreferences from 'src/modules/organization-preferences/entity/organization-preferences.entity';
import { OrganizationPreferencesModule } from 'src/modules/organization-preferences/organization-preferences.module';
import { WalletModule } from 'src/modules/wallet/wallet.module';
import { WhatsappModule } from 'src/modules/whatsapp/whatsapp.module';
import { QtmSuperAdminActivityModule } from 'src/modules/qtm-super-admin-activity/qtm-super-admin-activity.module';
import { AtmQtmApprovalModule } from 'src/modules/atm-qtm-approval/atm-qtm-approval.module';
import { CollectDataModule } from 'src/modules/collect-data/collent-data.module';
import { KybModule } from 'src/modules/kyb/kyb.module';
import { QuantumModule } from 'src/modules/quantum/quantum.module';
import { BudgetedHoursModule } from 'src/modules/budgeted-hours/budgeted-hours.module';
import { AutomationModule } from 'src/modules/automation/automation.module';
import { CronActivityModule } from 'src/modules/cron-activity/cron-activity.module';
import { ClientGroupModule } from 'src/modules/client-group/client-group.module';
import { UdinTaskModule } from 'src/modules/udin-task/udin-task.module';
import { GstrAutomationModule } from 'src/modules/gstr-automation/gstr-automation.module';
import { CommunicationModule } from 'src/modules/communication/communication.module';
import { WellknownModule } from 'src/modules/wellknown/wellknown.module';
import { LoggerMiddleware } from './middleware';
import { RequestTimeMiddleware } from './timeLogger';
import { TanAutomationModule } from 'src/modules/tan-automation/tan-automation.module';
import { PosterModule } from 'src/modules/poster/poster.module';
import { MarketingWebhookModule } from 'src/modules/webhook-marketing/marketing-webhook.module';
import { DocumentCategoryModule } from 'src/modules/document-in-out/document-category.module';
import { DocumentInOutModule } from 'src/modules/document-in-out/document-in-out.module';
import { ViderAiModule } from 'src/modules/vider-ai/viderAi.module';
import { ChannelPartnerModule } from 'src/modules/channel-partners/channel-partner.module';


ConfigModule.forRoot({
  load: [db_config, mongo_config],
  expandVariables: true,
});

@Module({
  imports: [
    TypeOrmModule.forRoot(db_config()),
    MongooseModule.forRoot(mongo_config().MONGO_URI),
    EventEmitterModule.forRoot(),
    ScheduleModule.forRoot(),
    OrganizationsModule,
    GetStartedModule,
    UsersModule,
    PermissionsModule,
    CategoriesModule,
    ServicesModule,
    CommonModule,
    ClientModule,
    LeadsModule,
    StorageModule,
    LabelModule,
    TasksModule,
    RecurringModule,
    EventsModule,
    StatesModule,
    ActivityModule,
    NotificationsModule,
    FormsModule,
    ChatsModule,
    AdminModule,
    ClientPanelModule,
    BillingModule,
    ReportsModule,
    LogHoursModule,
    ExpenditureModule,
    OneDriveStorageModule,
    DscRegisterModule,
    StatsModule,
    PushNotificationModule,
    GstrRegisterModule,
    StrapiModule,
    SandboxModule,
    WebhookModule,
    // MarketingWebhookModule,
    WhatsappModule,
    AtomSuperAdminActivityModule,
    AttendanceModule,
    OrganizationPreferencesModule,
    WalletModule,
    QtmSuperAdminActivityModule,
    AtmQtmApprovalModule,
    CollectDataModule,
    KybModule,
    QuantumModule,
    BudgetedHoursModule,
    AutomationModule,
    CronActivityModule,
    ClientGroupModule,
    UdinTaskModule,
    GstrAutomationModule,
    CommunicationModule,
    WellknownModule,
    TanAutomationModule,
    PosterModule,
    DocumentCategoryModule,
    DocumentInOutModule,
    ViderAiModule,
    StorageModule,
    ChannelPartnerModule
  ],
})
export class AppModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer.apply(RequestTimeMiddleware).forRoutes('*');
  }
}
