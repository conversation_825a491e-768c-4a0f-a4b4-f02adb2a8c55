import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AtomSuperAdminActivity } from './atom-super-admin-activity.entity';
import { AtomSuperAdminActivityController } from './atom-super-admin-activity.controller';
import { AtomSuperAdminActivityService } from './atom-super-admin-activity.service';

@Module({
  imports: [
    TypeOrmModule.forFeature([
    AtomSuperAdminActivity
    ]),
  ],
  controllers: [AtomSuperAdminActivityController],
  providers: [AtomSuperAdminActivityService],
})
export class AtomSuperAdminActivityModule {}
