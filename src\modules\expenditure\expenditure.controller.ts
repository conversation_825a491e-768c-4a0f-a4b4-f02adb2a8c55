import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  ParseIntPipe,
  Patch,
  Post,
  Put,
  Query,
  Request,
  UseGuards,
} from '@nestjs/common';
import { JwtAuthGuard } from 'src/modules/users/jwt/jwt-auth.guard';
import AddExpenditureDto from './dto/add-expenditure.dto';
import FindExpenditureDto, { FindExpenditureQueryType } from './dto/find-expenditure.dto';
import { ExpenditureService } from './expenditure.service';
import { query } from 'express';

@Controller('expenditure')
export class ExpenditureController {
  constructor(private service: ExpenditureService) { }

  @UseGuards(JwtAuthGuard)
  @Post()
  add(@Request() req: any, @Body() body: AddExpenditureDto) {
    const { userId } = req.user;
    return this.service.add(userId, body);
  }

  @UseGuards(JwtAuthGuard)
  @Get()
  get(@Request() req: any, @Query() query: FindExpenditureDto) {
    const { userId } = req.user;
    let id = query.type === FindExpenditureQueryType.USER ? query.userId : userId;
    return this.service.find(+id, query);
  }

  @UseGuards(JwtAuthGuard)
  @Post('/export')
  export(@Request() req: any, @Body() body: any) {
    const { userId } = req.user;
    return this.service.export(userId, body);
  }

  @UseGuards(JwtAuthGuard)
  @Get('/organization')
  getOrgExpenditure(@Request() req: any, @Query() query: FindExpenditureDto) {
    const { userId } = req.user;
    return this.service.findOrgExpenditure(userId, query);
  }
  @UseGuards(JwtAuthGuard)
  @Post('/organization-export')
  exportOrgExpenditure(@Request() req: any, @Body() body: any) {
    const { userId } = req.user;
    return this.service.exportOrgExpenditure(userId, body);
  }

  @UseGuards(JwtAuthGuard)
  @Put('/status-change')
  async approvalsStatusChange(@Request() req: any, @Body() body) {
    const { userId } = req.user;
    return this.service.approvalsStatusChange(userId, body);
  }
  @UseGuards(JwtAuthGuard)
  @Put('/:id')
  update(@Request() req: any, @Body() body: Omit<AddExpenditureDto, 'taskId'>, @Param('id', ParseIntPipe) id: number) {
    const { userId } = req.user;
    return this.service.update(id, userId, body);
  }

  @UseGuards(JwtAuthGuard)
  @Put('/settings/:id')
  edit(
    @Request() req: any,
    @Body() body: AddExpenditureDto,
    @Param('id', ParseIntPipe) id: number,
  ) {
    const { userId } = req.user;
    return this.service.edit(id, body, userId);
  }

  @UseGuards(JwtAuthGuard)
  @Patch('/:id/approve')
  approveExpense(@Param('id', ParseIntPipe) id: number, @Request() req: any) {
    const { userId } = req.user;
    return this.service.approve(id, userId);
  }

  @UseGuards(JwtAuthGuard)
  @Patch('/:id/reject')
  rejectExpense(@Param('id', ParseIntPipe) id: number, @Body() body: { reason: string }, @Request() req: any) {
    const { userId } = req.user;
    return this.service.reject(id, body, userId);
  }
  @UseGuards(JwtAuthGuard)
  @Delete('/bulk-delete')
  bulkDelete(@Request() req: any, @Body() body: any) {
    const { userId } = req.user;
    return this.service.bulkDelete(userId, body);
  }

  @UseGuards(JwtAuthGuard)
  @Delete('/:id')
  delete(@Param('id', ParseIntPipe) id: number, @Request() req: any, @Query() query) {
    const { userId } = req.user;
    return this.service.delete(id, userId, query);
  }




}
