import { Injectable } from "@nestjs/common";
import CronActivity from "src/modules/cron-activity/cron-activity.entity";
import * as moment from 'moment';
import { Organization } from "src/modules/organization/entities/organization.entity";
import { User, UserStatus, UserType } from "src/modules/users/entities/user.entity";
import { Brackets, createQueryBuilder } from "typeorm";
import TanCommunicationInbox from "../entity/tan-communication-inbox.entity";
import { sendnewMail } from "src/emails/newemails";
import TanTempEproFyi from "../entity/tan_temp_epro_fyi.entity";
import TanTempEproFya from "../entity/tan_temp_epro_fya.entity";
import { Cron, CronExpression } from "@nestjs/schedule";
import { Permissions } from 'src/modules/events/permission';

@Injectable()

export class TanCronService {
// @Cron('0 30 3 * * *') // (EVERY DAY AT 03.30AM - Server Time) (09.00 AM)
  async handleTracesComminicationMail() {
    if (process.env.Cron_Running === 'true') {
      console.log("-------- TRACES NOTICE CRON EXECUTION STARTED ---------")
      const cronData = new CronActivity();
      cronData.cronType = 'Traces NOTICES';
      cronData.cronDate = moment().toDate().toString();
      cronData.startTime = moment().format('YYYY-MM-DD HH:mm:ss');
      const cornActivityID = await cronData.save();
      const errorList = [];
      try {
        const today = moment().format('DD-MMM-YYYY');
        const previousSeventhDay = moment().subtract(7, 'days').format('DD-MMM-YYYY');
        const totalOrganization = await Organization.createQueryBuilder('organization')
          .leftJoinAndSelect('organization.users', 'user')
          .where(
            "DATE_FORMAT(STR_TO_DATE(JSON_UNQUOTE(JSON_EXTRACT(config, '$.expirydate')), '%Y-%m-%d'), '%Y-%m-%d') >= :expirydate",
            { expirydate: moment().format('YYYY-MM-DD') },
          )
          .andWhere('user.status = :status', { status: 'active' })
          .andWhere('user.type = :type', { type: UserType.ORGANIZATION })
          .getMany();

        try {
          for (let organization of totalOrganization) {
            const users = organization?.users;

            if (users?.length > 0) {
              for (let user of users) {
                if (user.status === UserStatus.DELETED) return;

                const userData = await User.findOne({
                  where: { id: user.id },
                  relations: ['organization', 'role'],
                });

                const { role } = userData;
                const ViewAll = role.permissions.some(
                  (p) => p.slug === Permissions.VIEW_ALL_CLIENT_MANAGERS,
                );
                const ViewAssigned = role.permissions.some(
                  (p) => p.slug === Permissions.VIEW_ASSIGNED_CLIENT_MANAGERS,
                );

                let tracesRecordsArray = [];
                let fyiRecordsArray = [];
                let fyaRecordsArray = [];
                if (ViewAll || ViewAssigned) {

                  const tracesRecordsQuery = await createQueryBuilder(TanCommunicationInbox, 'tanCommunicationInbox')
                    .leftJoinAndSelect('tanCommunicationInbox.client', 'client')
                    .where('tanCommunicationInbox.organizationId = :orgId', { orgId: organization?.id })
                    .andWhere(

                      'STR_TO_DATE(tanCommunicationInbox.date, "%d-%b-%Y") BETWEEN STR_TO_DATE(:previousSeventhDay, "%d-%b-%Y") AND STR_TO_DATE(:today, "%d-%b-%Y")',
                      {
                        previousSeventhDay,
                        today,
                      },
                    )
                    .andWhere('client.status != :status', { status: UserStatus.DELETED });

                  if (!ViewAll) {
                    tracesRecordsQuery.andWhere(
                      (qb) => {
                        const subQuery = qb
                          .subQuery()
                          .select('1')
                          .from('client_client_managers_user', 'cm')
                          .where('cm.client_id = client.id')
                          .andWhere('cm.user_id = :userId')
                          .getQuery();
                        return `EXISTS (${subQuery})`;
                      },
                      { userId: userData.id },
                    );
                  }

                  const tracesRecords = await tracesRecordsQuery.getMany();
                  if (tracesRecords?.length > 0) {
                    tracesRecordsArray = tracesRecords.map((item) => ({
                      orgId: organization?.id,
                      id: item.id,
                      clientName: item?.client?.displayName,
                      category: item?.commCat ? item?.commCat : "--",
                      referenceId: item?.commRefNo ? item?.commRefNo : "--",
                      formType: item?.formType || '--',
                      quarter: item?.qt || '--',
                      fy: item?.fy === '0-01' ? '--' : item?.fy,
                      issuedOnDate: item?.date ? item?.date : '--',
                      type: item?.type ? item?.type : '--',
                    }));
                  }

                  const tanToday = moment().format('YYYY-MM-DD');
                  const tanPreviousSeventhDay = moment().subtract(7, 'days').format('YYYY-MM-DD');
                  const tanNextSeventhDay = moment().add(7, 'days').format('YYYY-MM-DD');
                    const fyiRecordsQuery = await createQueryBuilder(TanTempEproFyi, 'tanTempEproFyi')
                      .where('tanTempEproFyi.organizationId = :orgId', { orgId: organization?.id })
                      .andWhere(
                        new Brackets((qb) => {
                          qb.where(
                            'STR_TO_DATE(tanTempEproFyi.noticeSentDate, "%d-%m-%Y") BETWEEN :tanPreviousSeventhDay AND :tanToday',
                            {
                              tanPreviousSeventhDay,
                              tanToday,
                            },
                          ).orWhere(
                            'STR_TO_DATE(tanTempEproFyi.dateOfCompliance, "%d-%m-%Y") BETWEEN :tanToday AND :tanNextSeventhDay',
                            {
                              tanToday,
                              tanNextSeventhDay,
                            },
                          ).orWhere(
                            'STR_TO_DATE(tanTempEproFyi.dateResponseSubmitted, "%d-%m-%Y") BETWEEN :tanPreviousSeventhDay AND :tanToday',
                            {
                             tanPreviousSeventhDay,
                             tanToday,
                            },
                          );
                        }),
                      )
                      .leftJoinAndSelect('tanTempEproFyi.client', 'client');
  
  
                    const fyiRecords = await fyiRecordsQuery.getMany();
                    if (fyiRecords?.length > 0) {
                      fyiRecordsArray = fyiRecords.map((item) => ({
                        orgId: organization?.id,
                        id: item.id,
                        clientName: item?.client?.displayName,
                        panNumber: item?.pan,
                        proceedingName: item?.proceedingName,
                        din: item?.noticeDin || '-',
                        section:item?.noticeSection || '-',
                        ay: item?.ay === '0-01' ? 'NA' : item?.ay,
                        issuedOnDate: item?.noticeSentDate ? item?.noticeSentDate : '-',
                        responseDueDate: item?.dateOfCompliance ? item?.dateOfCompliance : '-',
                        responseSubmitted: item?.dateResponseSubmitted ? item?.dateResponseSubmitted : "-"
                      }));
                    }
  
                    const fyaRecordsQuery = await createQueryBuilder(TanTempEproFya, 'tanTempEproFya')
                      .where('tanTempEproFya.organizationId = :orgId', { orgId: organization?.id })
                      .andWhere(
                        new Brackets((qb) => {
                          qb.where(
                            'STR_TO_DATE(tanTempEproFya.noticeSentDate, "%d-%m-%Y") BETWEEN :tanPreviousSeventhDay AND :tanToday',
                            {
                              tanPreviousSeventhDay,
                              tanToday,
                            },
                          ).orWhere(
                            'STR_TO_DATE(tanTempEproFya.dateOfCompliance, "%d-%m-%Y") BETWEEN :tanToday AND :tanNextSeventhDay',
                            {
                              tanToday,
                              tanNextSeventhDay,
                            },
                          ).orWhere(
                            'STR_TO_DATE(tanTempEproFya.dateResponseSubmitted, "%d-%m-%Y") BETWEEN :tanPreviousSeventhDay AND :tanToday',
                            {
                             tanPreviousSeventhDay,
                             tanToday,
                            },
                          );
                        }),
                      )
                      .leftJoinAndSelect('tanTempEproFya.client', 'client');
                    
                    const orgId = organization?.id;
  
                    const organizations = await Organization.findOne({ id: orgId });
  
                    const addressParts = [
                      organizations.buildingNo || '',
                      organizations.floorNumber || '',
                      organizations.buildingName || '',
                      organizations.street || '',
                      organizations.location || '',
                      organizations.city || '',
                      organizations.district || '',
                      organizations.state || '',
                    ].filter((part) => part && part.trim() !== '');
                    const pincode =
                      organizations.pincode && organizations.pincode.trim() !== ''
                        ? ` - ${organizations.pincode}`
                        : '';
  
                    const address = addressParts.join(', ') + pincode;
  
                    const fyaRecords = await fyaRecordsQuery.getMany();
                    if (fyaRecords?.length > 0) {
                      fyaRecordsArray = fyaRecords.map((item) => ({
                        orgId: organization?.id,
                        id: item.id,
                        clientName: item?.client?.displayName,
                        panNumber: item?.pan,
                        proceedingName: item?.proceedingName,
                        section:item?.noticeSection || '-',
                        din: item?.noticeDin || '-',
                        ay: item?.ay === '0-01' ? 'NA' : item?.ay,
                        issuedOnDate: item?.noticeSentDate ? item?.noticeSentDate : '-',
                        responseDueDate: item?.dateOfCompliance ? item?.dateOfCompliance : '-',
                        responseSubmitted: item?.dateResponseSubmitted ? item?.dateResponseSubmitted : "-"
                      }));
                    }

                  if (tracesRecordsArray?.length > 0 || fyaRecordsArray?.length > 0 || fyiRecordsArray?.length > 0) {
                    const mailOptions = {
                      data: {
                        fyaRecordsArray,
                        fyiRecordsArray,
                        tracesRecordsArray,
                        userName: user?.fullName,
                        userId: user?.id,
                        websiteUrl: process.env.WEBSITE_URL,
                      },
                      email: user?.email,
                      filePath: 'traces-notice',
                      subject: 'TAN Traces Notices',
                      key: 'TRACES_NOTICE_MAIL',
                      id: user?.id,
                    };
                    await sendnewMail(mailOptions);
                  }
                }


              }
            }
          }
        } catch (error) {
          console.log(`Error in getting Income tax Notice records in cron:`, error);
        }
      } catch (error) {
        errorList.push(error.message);
        const getcornActivityID = await createQueryBuilder(CronActivity, 'cronActivity')
          .where('id = :id', { id: cornActivityID.id })
          .getOne();
        getcornActivityID.responseData = errorList.length ? errorList.join(',') : 'Success';
        getcornActivityID.endTime = moment().format('YYYY-MM-DD HH:mm:ss');
        await getcornActivityID.save();
        return console.log(error.message);
      }
      const getcornActivityID = await createQueryBuilder(CronActivity, 'cronActivity')
        .where('id = :id', { id: cornActivityID.id })
        .getOne();
      getcornActivityID.responseData = errorList.length ? errorList.join(',') : 'Success';
      getcornActivityID.endTime = moment().format('YYYY-MM-DD HH:mm:ss');
      await getcornActivityID.save();
      console.log('TRACES NOTICE CRON EXECUTION COMPLETED!!!!');
      return 'TRACES NOTICE CRON EXECUTION COMPLETED!!!!';

    }
  }
}