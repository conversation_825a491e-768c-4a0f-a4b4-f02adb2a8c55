// import { <PERSON><PERSON><PERSON>, <PERSON>NotEmpty, IsOptional } from 'class-validator';

// export class CreateDscApplyDto {
//   @IsNotEmpty({ message: 'Holder name is required' })
//   applicantName: string;

//   @IsNotEmpty()
//   client: number;

//   @IsNotEmpty({ message: 'Expiry is requred' })
//   expiryDate: string;

//   @IsNotEmpty({ message: 'Password is required' })
//   password: string;

//   // @IsNotEmpty({ message: 'Token number is required' })
//   @IsOptional()
//   tokenNumber: string;

//   // @IsNotEmpty({ message: 'Pan number is required' })
//   @IsOptional()
//   panNumber: string;

//   // @IsNotEmpty({ message: 'Holder designation is required' })
//   @IsOptional()
//   holderDesignation: string;

//   @IsOptional()
//   // @IsNotEmpty({ message: 'Mobile number is required' })
//   mobileNumber: string;

//   @IsOptional()
//   // @IsNotEmpty({ message: 'Email is required' })
//   // @IsEmail({}, { message: 'Email is invalid' })
//   email: string;
// }

// export class UpdateDscRegisterDto extends CreateDscRegisterDto {
//   @IsOptional()
//   client: number;
// }
