import { IsOptional, IsNumberString, IsNotEmpty } from 'class-validator';

export class FindInvoicesDto {
  @IsOptional()
  @IsNumberString()
  limit: number;

  @IsOptional()
  @IsNumberString()
  offset: number;

  @IsOptional()
  search: string;

  @IsOptional()
  clientId: string;

  @IsOptional()
  status: string;

  @IsOptional()
  screen: string;

  @IsOptional()
  billingEntity: []

  @IsOptional()
  sort: string;

  @IsOptional()
  fromDate: Date;

  @IsOptional()
  toDate: Date;

  @IsOptional()
  gst: boolean

  @IsOptional()
  type: string;
}

export class NextInvoiceNumberDto {
  @IsNotEmpty()
  @IsNumberString()
  billingEntity: number;

  @IsNotEmpty()
  type: string;
}
