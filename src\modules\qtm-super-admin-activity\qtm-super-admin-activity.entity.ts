import { BaseEntity, Column, CreateDateColumn, Entity, ManyToMany, OneToMany, PrimaryGeneratedColumn, UpdateDateColumn } from "typeorm";
import { User } from "../users/entities/user.entity";

@Entity('qtm_super_admin_activity')
export class QtmSuperAdminActivity extends BaseEntity {
    @PrimaryGeneratedColumn()
    id: number;

    @Column()
    type: string;

    @Column()
    typeId: number;

    @CreateDateColumn()
    createdAt: string;

    @UpdateDateColumn()
    lastUpdated: string;

    @Column()
    updatedUserId: number;

    @Column({ default: false })
    toProduction: boolean;

    @Column()
    prodTypeId: number;

    // @OneToMany(() => User, (user) => user.atomSuperAdminActivity)
    // users: User[];

}