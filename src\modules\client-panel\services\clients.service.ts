import {
  BadRequestException,
  ConsoleLogger,
  Injectable,
  InternalServerErrorException,
} from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { validate } from 'class-validator';
import { randomBytes } from 'crypto';
import * as _ from 'lodash';
import { Event_Actions } from 'src/event-listeners/actions';
import { User, UserStatus, UserType } from 'src/modules/users/entities/user.entity';
import { Not, createQueryBuilder, getConnection } from 'typeorm';
import * as xlsx from 'xlsx';
import axios from 'axios';
import * as moment from 'moment';
import Task from 'src/modules/tasks/entity/task.entity';
import { TaskRecurringStatus, TaskStatusEnum } from 'src/modules/tasks/dto/types';
import Storage from 'src/modules/storage/storage.entity';
import { StorageService } from 'src/modules/storage/storage.service';
import Activity, { ActivityType } from 'src/modules/activity/activity.entity';
import { getTitle } from 'src/utils';
import GstrRegister, { RegistrationType } from 'src/modules/gstr-register/entity/gstr-register.entity';
import AutClientCredentials, { syncStatus } from 'src/modules/automation/entities/aut_client_credentials.entity';
import parsePhoneNumberFromString from "libphonenumber-js";
import countries from 'src/utils/countries';
import mobileWithCountry from 'src/utils/validations/mobileWithCountry';
import { TAN_REGEX } from 'src/utils/validations/regex-pattrens';
import Client from 'src/modules/clients/entity/client.entity';
import BulkUpdateDto from 'src/modules/clients/dto/bulk-update.dto';
import CreateClientDto from 'src/modules/clients/dto/create-client.dto';
import FindQueryDto from 'src/modules/clients/dto/find-query.dto';
import { CategoryEnum, SubCategoryEnum } from 'src/modules/clients/dto/types';
import { UpdateClientDto } from 'src/modules/clients/dto/update-client.dto';
import Password from 'src/modules/clients/entity/password.entity';


@Injectable()
export class ClientService {
  // constructor(private eventEmitter: EventEmitter2, private storageService: StorageService) { }

  async create(userId: number, data: CreateClientDto) {
    try {
      let user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
      const displayName = (' ' + data.displayName)?.trim();

      let existingUser = await createQueryBuilder(Client, 'client')
        .leftJoin('client.organization', 'organization')
        .where('organization.id = :organization', { organization: user.organization.id })
        .andWhere('(client.displayName = :displayName)', {
          displayName: displayName,
        })
        .getOne();

      if (existingUser) {
        throw new BadRequestException(
          'Client with the given Display Name already Exists in your Organization',
        );
      }

      if (data?.clientNumber) {
        let existingNumber = await createQueryBuilder(Client, 'client')
          .leftJoin('client.organization', 'organization')
          .where('organization.id = :organization', { organization: user.organization.id })
          .andWhere('(client.clientNumber = :clientNumber)', {
            clientNumber: data?.clientNumber,
          })
          .getOne();

        if (existingNumber && data?.clientNumberDuplicate) {
          throw new BadRequestException('Client Number Exists');
        }
      }

      let clientManager = await User.findOne({ where: { id: data.clientManager } });
      if (!(' ' + data.displayName)?.trim().length) {
        throw new BadRequestException('Invalid Display Name');
      }
      if (data?.clientPortalAccess) {
        let existingClientPortalUser = await createQueryBuilder(Client, 'client')
          .leftJoinAndSelect('client.organization', 'organization')
          .where('organization.id != :organization', { organization: user.organization.id })
          .andWhere('client.email = :email', {
            email: data.email,
          })
          .andWhere('client.clientPortalAccess = :clientPortalAccess', { clientPortalAccess: data.clientPortalAccess })
          .getOne();

        if (existingClientPortalUser) {
          throw new BadRequestException(
            'The Email address provided already has Client Portal Access enabled in another organization.',
          );
        }
      }

      let newUser = new User();
      newUser.fullName = data.displayName.trim();
      newUser.email = data.email;
      newUser.password = null;
      newUser.mobileNumber = data.mobileNumber;
      newUser.organization = user.organization;
      newUser.type = UserType.CLIENT;


      let client = new Client();
      client.displayName = data.displayName.trim();
      client.email = data.email;
      client.mobileNumber = data.mobileNumber;
      client.category = data.category;
      client.subCategory = data.subCategory;
      client.tradeName = data.tradeName;
      client.legalName = data.legalName;
      client.constitutionOfBusiness = data.constitutionOfBusiness;
      client.placeOfSupply = data.placeOfSupply;
      client.firstName = data.firstName;
      client.lastName = data.lastName;
      client.fullName = data.fullName;
      client.designation = data.designation;
      client.gstNumber = data.gstNumber;
      client.middleName = data.middleName;
      client.panNumber = data.panNumber;
      client.gstVerified = data.gstVerified;
      client.panVerified = data.panVerified;
      client.authorizedPerson =
        data.authorizedPerson !== null && data.authorizedPerson !== undefined
          ? data.authorizedPerson.trim()
          : data.authorizedPerson;
      client.designation =
        data.designation !== null && data.designation !== undefined
          ? data.designation.trim()
          : data.designation;
      client.clientManager = clientManager;
      client.createdBy = user;
      client.organization = user.organization;
      client.user = newUser;
      client.clientPortalAccess = data.clientPortalAccess;
      client.issameaddress = data.issameaddress;
      client.clientNumber = data.clientNumber;
      client.localDirectoryPath = [];
      client.countryCode = data.countryCode;
      await client.save();

      // this.eventEmitter.emit(Event_Actions.CLIENT_CREATED, {
      //   userId,
      //   data: client,
      //   orgName: user?.organization?.legalName,
      //   isEmail: data?.isEmail,
      // });
      return client;
    } catch (err) {
      console.log(err);
      throw new InternalServerErrorException(err);
    }
  }

  async findAll(userId: number, query: FindQueryDto): Promise<{ count: number; result: Client[] }> {
    const { limit, offset } = query;
    const { status: state, category, subCategory, monthAdded, search, labels } = query;
    const month = new Date(monthAdded).getMonth() + 1;
    const year = new Date(monthAdded).getFullYear();

    let user = await User.findOne({ where: { id: userId }, relations: ['organization'] });

    let clients = getConnection()
      .createQueryBuilder(Client, 'client')
      .leftJoin('client.labels', 'labels')
      .leftJoin('client.organization', 'organization')
      .orderBy('client.displayName', 'ASC')
      .leftJoinAndSelect('client.contactPersons', 'contactPersons')
      .leftJoinAndSelect('client.clientImage', 'clientImage')
      .where('organization.id = :organization', { organization: user.organization.id });
    // .andWhere('client.status != :status', { status: UserStatus.DELETED })

    if (category?.length) {
      clients.andWhere('client.category in (:...category)', { category });
    }

    if (subCategory?.length) {
      clients.andWhere('client.subCategory in (:...subCategory)', { subCategory });
    }

    if (state?.length) {
      clients.andWhere('client.status in (:...state)', { state });
    } else {
      clients.andWhere('client.status != :status', { status: UserStatus.DELETED });
    }

    if (labels?.length) {
      clients.andWhere('labels.id in (:...labels)', { labels });
    }

    if (search) {
      const whereClause = `(
        client.displayName like '%${search}%' or
        client.email like '%${search}%' or
        client.mobileNumber like '%${search}%' or
        client.category like '%${search}%' or
        client.panNumber like '%${search}%' or
        client.tradeName like '%${search}%' or 
        client.clientNumber like '%${search}%'
      )`;
      clients.andWhere(whereClause);
    }

    if (monthAdded) {
      clients.andWhere('MONTH(client.createdAt) = :month and YEAR(client.createdAt) = :year', {
        month,
        year,
      });
    }

    if (offset >= 0) {
      clients.skip(offset);
    }

    if (limit) {
      clients.take(limit);
    }

    let result = await clients.getManyAndCount();

    return {
      count: result[1],
      result: result[0],
    };
  }

  async findOne(id: number, userId: number) {
    let user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
    const client = await createQueryBuilder(Client, 'client')
      .leftJoinAndSelect('client.clientManager', 'clientManager')
      .leftJoinAndSelect('client.clientImage', 'clientImage')
      .leftJoinAndSelect('client.contactPersons', 'contactPersons')
      .leftJoinAndSelect('client.labels', 'labels')
      .leftJoinAndSelect('client.recurringProfiles', 'recurringProfiles')
      .leftJoinAndSelect('recurringProfiles.tasks', 'recurringProfilesTasks')
      .where('client.id = :id', { id })
      .andWhere('client.organization.id = :organization', { organization: user.organization.id })
      .getOne();

    return client;
  }

  async update(userId: number, id: number, data: UpdateClientDto) {

    let user = await User.findOne({ where: { id: userId }, relations: ['organization'] });

    const clientDataBaseState = await Client.findOne({ where: { id } });

    if (!(' ' + data.displayName)?.trim().length) {
      throw new BadRequestException('Invalid Display Name');
    }

    let client = await Client.findOne({ where: { id }, relations: ['clientImage', 'user'] });
    const oldClientPortalAcces = client.clientPortalAccess;
    const oldClientStatus = client.status;

    if (data?.displayName !== undefined && data?.displayName !== "undefined") {
      if (data?.displayName.length !== client?.displayName.length) {
        const displayName = (' ' + data.displayName)?.trim();
        let existingUser = await createQueryBuilder(Client, 'client')
          .leftJoin('client.organization', 'organization')
          .where('organization.id = :organization', { organization: user.organization.id })
          .andWhere('(client.displayName = :displayName)', {
            displayName: displayName,
          })
          .getOne();
        if (existingUser) {
          throw new BadRequestException(
            'Client with the given display name already exists in your organization',
          );
        }
      }
    }
    if (clientDataBaseState?.clientPortalAccess !== data?.clientPortalAccess) {
      if (!data?.clientPortalAccess) {
        let existingClientPortalUser = await createQueryBuilder(Client, 'client')
          .leftJoinAndSelect('client.organization', 'organization')
          .where('organization.id != :organization', { organization: user.organization.id })
          .andWhere('client.email = :email', {
            email: data.email,
          })
          .andWhere('client.clientPortalAccess = :clientPortalAccess', { clientPortalAccess: data.clientPortalAccess })
          .getOne();

        if (existingClientPortalUser) {
          throw new BadRequestException(
            'The Email address provided already has Client Portal Access enabled in another organization.',
          );
        }
      }
    }

    if (data?.gstNumber) {
      let existingGstNumber = await Client.findOne({
        where: {
          gstNumber: data.gstNumber,
          organization: user.organization.id,
          id: Not(id),
        },
      });
      if (existingGstNumber) {
        throw new BadRequestException(
          'Duplicate GSTIN Detected! To ensure accurate data management, GSTINs must be unique for each client. Please double-check the entered GSTIN.',
        );
      }
    }

    let iStorage: Storage;

    if (data?.clientImage) {
      if (client?.clientImage?.id) {
        iStorage = await Storage.findOne({ where: { id: client?.clientImage?.id } });
        iStorage.fileType = data?.clientImage?.fileType;
        iStorage.fileSize = data?.clientImage?.fileSize;
        iStorage.name = data?.clientImage?.name;
        iStorage.file = data?.clientImage?.upload;
        iStorage.show = data?.clientImage?.show;
        client.clientImage = iStorage;
      } else {
        // const storage = await this.storageService.addAttachements(userId, data?.clientImage);
        // client.clientImage = storage;
      }
    } else {
      if (client?.clientImage?.id) {
        const existingPStorage = await Storage.findOne({ where: { id: client?.clientImage?.id } });
        await existingPStorage.remove();
        client.clientImage = null;
      }
    }

    if (client.user) {
      let clinetUser = await User.findOne({
        where: { id: client.user.id, type: UserType.CLIENT },
        relations: ['organization'],
      });
      clinetUser.fullName = data.displayName;
      clinetUser.organization = user.organization;
      clinetUser.mobileNumber = data.mobileNumber;
      if (client.email !== data.email) {
        clinetUser.email = data.email;
      }
      clinetUser.save();
    }

    client.displayName =
      data.displayName !== null && data.displayName !== undefined
        ? data.displayName.trim()
        : data.displayName;
    client.mobileNumber = data.mobileNumber;
    client.alternateMobileNumber = data.alternateMobileNumber;
    client.category = data.category;
    client.subCategory = data.subCategory;
    client.email = data.email;
    client.tradeName = data.tradeName;
    client.legalName = data.legalName;
    client.constitutionOfBusiness = data.constitutionOfBusiness;
    client.middleName = data.middleName;
    client.gstRegistrationDate = data.gstRegistrationDate;
    client.placeOfSupply = data.placeOfSupply;
    client.fullName = data.fullName;
    client.firstName = data.firstName;
    client.lastName = data.lastName;
    client.gstNumber = data.gstNumber;
    client.panNumber = data.panNumber;
    client.tanNumber = data.tanNumber;
    client.clientNumber = data.clientNumber;
    if (data?.gstNumber) {
      const gstCharAt13 = data.gstNumber.charAt(13);
      const extractedNumber = data.gstNumber.substring(2, data.gstNumber.length - 3);
      if (gstCharAt13 === 'D') {
        const pattren = TAN_REGEX.test(extractedNumber);
        if (pattren) {
          // client.tanNumber = data.tanNumber || extractedNumber;
          client.tanNumber = data.tanNumber;
        } else {
          // client.panNumber = data.panNumber || extractedNumber;
          client.panNumber = data.panNumber;
        }

        client.registrationType = RegistrationType.TAX_DEDUCTOR;
      } else if (gstCharAt13 === 'Z' || gstCharAt13 === 'C') {
        // client.panNumber = data.panNumber || extractedNumber;
        client.panNumber = data.panNumber;
        client.registrationType =
          gstCharAt13 === 'Z' ? RegistrationType.REGULAR_TAXPAYER : RegistrationType.TAX_COLLECTOR;
      }
    } else {
      client.tanNumber = data.tanNumber;
      client.panNumber = data.panNumber;
    }

    client.gstVerified = data.gstVerified;
    client.panVerified = data.panVerified;
    client.authorizedPerson =
      data.authorizedPerson !== null && data.authorizedPerson !== undefined
        ? data.authorizedPerson.trim()
        : data.authorizedPerson;
    client.designation =
      data.designation !== null && data.designation !== undefined
        ? data.designation.trim()
        : data.designation;
    client.dob = data.dob;
    client.buildingName = data.buildingName;
    client.street = data.street;
    client.city = data.city;
    client.state = data.state;
    client.pincode = data.pincode;
    client.labels = data.labels;
    client.localDirectoryPath = data.localDirectoryPath;
    client.notes = data.notes;
    client.status = data.status;
    client.image = data.image;
    client.clientPortalAccess = data.clientPortalAccess;
    client.address = data.address;
    client.issameaddress = data.issameaddress;
    client.countryCode = data.countryCode;

    // if (data.clientManager) {
    //   let clientManager = await User.findOne({ where: { id: data.clientManager } });
    //   client.clientManager = clientManager;
    // }
    // if (data.clientManager === null) {
    //   client.clientManager = null
    // }

    if (
      clientDataBaseState?.status !== UserStatus.INACTIVE &&
      data.status === UserStatus.INACTIVE
    ) {
      client.inactiveAt = new Date();
    }

    if (data.status === UserStatus.INACTIVE) {
      await createQueryBuilder(Task, 'task')
        .leftJoinAndSelect('task.client', 'client')
        .where('client.id = :id', { id: client.id })
        .andWhere('recurringStatus = :status', { status: TaskRecurringStatus.PENDING })
        .update()
        .set({
          recurringStatus: TaskRecurringStatus.TERMINATED,
          status: TaskStatusEnum.TERMINATED,
          restore: TaskStatusEnum.TODO,
          statusUpdatedAt: moment().format('YYYY-MM-DD HH:mm:ss.SSSSSS')
        })
        .execute();
    }

    await client.save();

    if (oldClientPortalAcces !== client.clientPortalAccess) {
      let activity = new Activity();
      activity.action = Event_Actions.CLIENT_PORTAL_ACCESS;
      activity.actorId = user.id;
      activity.type = ActivityType.CLIENT;
      activity.typeId = client.id;
      activity.remarks = `Client Portal Access ${client.clientPortalAccess ? "Enabled" : "Disabled"} by ${user.fullName}`;
      await activity.save();
    } else if (oldClientStatus !== client.status) {
      let activity = new Activity();
      activity.action = Event_Actions.CLIENT_STATUS_CHANGE;
      activity.actorId = user.id;
      activity.type = ActivityType.CLIENT;
      activity.typeId = client.id;
      activity.remarks = `Client Status Changed from ${oldClientStatus === UserStatus.ACTIVE ? "Active" : "Inactive"} to ${client.status === UserStatus.ACTIVE ? "Active" : "Inactive"} by ${user.fullName}`;
      await activity.save();
    } else {
      // this.eventEmitter.emit(Event_Actions.CLIENT_UPDATED, {
      //   userId,
      //   data: client,
      // });  
    }

    return client;
  }

  async findDeleted(userId: number, query: FindQueryDto) {
    const user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
    const clients = await Client.findAndCount({
      where: {
        organization: { id: user.organization.id },
        status: UserStatus.DELETED,
      },
      take: query.limit,
      skip: query.offset,
    });
    return clients;
  }

  async restoreClient(id: number, userId) {
    const user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
    const client = await Client.findOne({ where: { id } });
    client.status = UserStatus.ACTIVE;
    await client.save();
    let activity = new Activity();
    activity.action = Event_Actions.CLIENT_RESTORED;
    activity.actorId = user.id;
    activity.type = ActivityType.CLIENT;
    activity.typeId = client.id;
    activity.remarks = `Client Restored by ${user.fullName}`;
    await activity.save();

    return client;
  }

  async bulkDelete(ids: number[], userId: number) {
    const user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
    await createQueryBuilder(Task, 'task')
      .leftJoin('task.client', 'client')
      .where('client.id IN (:...ids)', { ids: ids })
      .andWhere('recurringStatus = :status', { status: TaskRecurringStatus.PENDING })
      .update(Task)
      .set({
        recurringStatus: TaskRecurringStatus.TERMINATED,
        status: TaskStatusEnum.TERMINATED,
        restore: TaskStatusEnum.TODO
      })
      .execute();


    await createQueryBuilder(Client, 'client')
      .whereInIds(ids)
      .update({ status: UserStatus.DELETED })
      .execute();

    for (let i of ids) {
      let activity = new Activity();
      activity.action = Event_Actions.CLIENT_DELETED;
      activity.actorId = user.id;
      activity.type = ActivityType.CLIENT;
      activity.typeId = i;
      activity.remarks = `Client Profile Deleted by ${user.fullName}`;
      await activity.save();
    }

    return { success: true };
  }

  async bulkUpdate(data: BulkUpdateDto, userId) {
    const user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
    if (data.status === 'INACTIVE') {
      await createQueryBuilder(Task, 'task')
        .leftJoinAndSelect('task.client', 'client')
        .where('client.id IN (:...ids)', { ids: data.ids })
        .andWhere('recurringStatus = :status', { status: TaskRecurringStatus.PENDING })
        .update(Task)
        .set({
          recurringStatus: TaskRecurringStatus.TERMINATED,
          status: TaskStatusEnum.TERMINATED,
          restore: TaskStatusEnum.TODO
        })
        .execute();
    }

    await createQueryBuilder(Client, 'client')
      .whereInIds(data.ids)
      .update({ status: data.status, inactiveAt: new Date() })
      .execute();

    for (let i of data.ids) {
      let activity = new Activity();
      activity.action = Event_Actions.CLIENT_STATUS_UPDATED;
      activity.actorId = user.id;
      activity.type = ActivityType.CLIENT;
      activity.typeId = i;
      activity.remarks = `Client Status Changed to ${getTitle(data.status.toLowerCase())} by ${user.fullName}`;
      await activity.save();
    }

    return { success: true };
  }

  getGstAddress = (address: any) => {
    let result = "";
    if (address?.bno) {
      result += `${address?.bno}, `;
    }
    if (address?.flno) {
      result += `${address?.flno}, `;
    }
    if (address?.bnm) {
      result += `${address.bnm}, `;
    }
    if (address?.st) {
      result += `${address.st}, `;
    }
    if (address?.locality) {
      result += `${address.locality}, `;
    }
    if (address?.loc) {
      result += `${address.loc}, `;
    }
    if (address?.dst) {
      result += `${address.dst}, `;
    }
    if (address?.stcd) {
      result += `${address.stcd} - `;
    }
    if (address?.pncd) {
      result += address.pncd;
    }
    return result;
  }


  async importClients(userId: number, file: Express.Multer.File) {
    const user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
    const workbook = xlsx.read(file.buffer, { cellDates: true });
    const sheet = workbook.Sheets[workbook.SheetNames[0]];
    const dataFromExcel = xlsx.utils.sheet_to_json(sheet);

    const result: Client[] = [];
    let errorsArray = [];
    let gstrRegisters = [];
    let credentilas = [];
    let clientCredentials = [];
    let addedArray = 0;
    let notAddedClient = 0;
    let validateError = 0;

    let alreadyAddedClients = 0;
    let rows = '';
    const gstinPattern = /^[0-9]{2}[a-zA-Z]{4}[a-zA-Z0-9]{1}[0-9]{4}[a-zA-Z]{1}[1-9A-Za-z]{1}[D,C,Z]{1}[0-9a-zA-Z]{1}$/;
    const PAN_REGEX = /^[A-Z]{3}[A,B,C,F,G,H,L,J,P,T]{1}[A-Z]{1}[0-9]{4}[A-Z]{1}$/;
    const newClientsDisplayName = [];
    const newClientGst = [];
    const newClientPan = [];

    if (dataFromExcel.length > 0) {
      let filteredData = dataFromExcel.filter((item) => {
        return (
          item['S.No'] ||
          item['Email Id *'] ||
          item['Mobile Number *'] ||
          item['Display Name *'] ||
          item['Client Category *'] ||
          item['Sub Category *']
        );
      });

      for (const [index, item] of filteredData.entries()) {
        const data: any = item;
        validateError = 0;

        if (
          data.hasOwnProperty('Client Category *') &&
          data['Client Category *'] != '' &&
          data.hasOwnProperty('Display Name *') &&
          data['Display Name *'] != '' &&
          data.hasOwnProperty('Mobile Number *') &&
          data['Mobile Number *'] != '' &&
          data.hasOwnProperty('Email Id *') &&
          data['Email Id *'] != '' &&
          data.hasOwnProperty('Country Code *') &&
          data['Country Code *'] != ''
        ) {
          let date = null;

          if (
            (' ' + data['Date of Birth(DD-MM-YYYY)'])?.trim() !== 'undefined' &&
            (' ' + data['Date of Birth(DD-MM-YYYY)'])?.trim() !== undefined
          ) {
            const dateStr = data['Date of Birth(DD-MM-YYYY)'];
            if (
              typeof dateStr === 'string' &&
              dateStr !== null &&
              dateStr !== undefined &&
              dateStr !== 'undefined'
            ) {
              const trimmedDateString = dateStr?.trim();
              date = moment(trimmedDateString).subtract(1, 'day').format('YYYY-MM-DD');
            } else if (
              typeof dateStr === 'object' &&
              dateStr !== null &&
              dateStr !== undefined &&
              dateStr !== 'undefined'
            ) {
              const dateObj = moment.utc(dateStr);
              const dateObjAdd = dateObj?.add(1, 'days');
              date = moment(dateObjAdd).subtract(1, 'day').format('YYYY-MM-DD');
            }
          }

          const client = new Client();
          client.createdBy = user;
          client.organization = user?.organization;
          client.category =
            CategoryEnum[
            data['Client Category *']?.toUpperCase().replace(/\s+/g, '_') as CategoryEnum
            ];
          client.subCategory =
            SubCategoryEnum[
            data['Sub Category *']?.toUpperCase().replace(/\s+/g, '_') as SubCategoryEnum
            ];

          client.displayName = (' ' + data['Display Name *'])?.trim();
          client.clientNumber = (' ' + data['Client Number'])?.trim() !== 'undefined' ? (' ' + data['Client Number'])?.trim() : null;
          client.mobileNumber = data['Mobile Number *'];
          client.email = (' ' + data['Email Id *'])?.trim();
          client.alternateMobileNumber =
            (' ' + data['Alternate Mobile Number'])?.trim() !== 'undefined'
              ? (' ' + data['Alternate Mobile Number'])?.trim()
              : null;
          client.dob =
            (' ' + data['Date of Birth(DD-MM-YYYY)'])?.trim() !== 'undefined' ? date : null;

          const panNumber = (' ' + data['PAN'])?.trim();
          const gstNumber = (' ' + data['GSTIN'])?.trim();
          const tanNumber = (' ' + data['TAN'])?.trim();
          const displayName = (' ' + data['Display Name *'])?.trim();
          const validateNumber = mobileWithCountry(data['Mobile Number *'], data['Country Code *']);
          if (validateNumber) {
            const errorDuplicate = `row ${index + 2} have mobile number does not match the selected country.`
            errorsArray = [...errorsArray, errorDuplicate];
            notAddedClient += 1;
            continue;
          }
          const countryCode = countries.find(c => c.label === data['Country Code *'])
          client.countryCode = countryCode.code;
          function getThree(duplicate1: string, duplicate2: string, duplicate3: string) {
            const errorDuplicate = `row ${index + 2
              } have ${duplicate1}  ${duplicate2}  ${duplicate3} have duplicate data`;
            errorsArray = [...errorsArray, errorDuplicate];
            notAddedClient += 1;
          }
          function getTwo(duplicate1: string, duplicate2: string) {
            const errorDuplicate = `row ${index + 2
              } have ${duplicate1}  ${duplicate2} have duplicate data`;
            errorsArray = [...errorsArray, errorDuplicate];
            notAddedClient += 1;
          }
          function getOne(duplicate1: string) {
            const errorDuplicate = `row ${index + 2} have ${duplicate1} have duplicate data`;
            errorsArray = [...errorsArray, errorDuplicate];
            notAddedClient += 1;
          }
          if (newClientsDisplayName.includes(displayName)) {
            if (newClientGst.includes(gstNumber)) {
              getTwo(displayName, gstNumber);
              continue;
            } else {
              getOne(displayName);
              continue;
            }
          } else if (newClientGst.includes(gstNumber)) {
            getOne(gstNumber);
            continue;
          }
          if (displayName !== 'undefined') {
            newClientsDisplayName.push(displayName);
          }
          if (gstNumber !== 'undefined') {
            newClientGst.push(gstNumber);
          }
          if (panNumber !== 'undefined') {
            newClientPan.push(panNumber);
          }

          let existingUser = await createQueryBuilder(Client, 'client')
            .leftJoin('client.organization', 'organization')
            .where('organization.id = :organization', { organization: user?.organization?.id })
            .andWhere('(client.displayName = :displayName OR client.gstNumber = :gstNumber)', {
              displayName: displayName,
              gstNumber: gstNumber,
            })
            .getOne();

          let duplicated = result.findIndex((items) => {
            if (displayName !== 'undefined') {
              if (gstNumber !== 'undefined') {
                return items?.displayName === displayName || items?.gstNumber === gstNumber;
              } else {
                return items?.displayName === displayName;
              }
            } else {
              return false;
            }
          });

          if (duplicated > -1) {
            // throw new BadRequestException(
            //   `row ${duplicated + 1} and row ${index + 1} have duplicate data`,
            // );
            const errorDuplicate = `row ${duplicated + 2} and row ${index + 2} have duplicate data`;
            errorsArray = [...errorsArray, errorDuplicate];
            notAddedClient += 1;
            continue;
          }

          await validate(client).then((errors) => {
            if (errors.length > 0) {
              // console.log('validation failed. errors: ', errors);
              // throw new BadRequestException(
              //   `Invalid ${_.startCase(errors[0].property)} in row ${index + 1}`,
              // );
              const errorDuplicate = `Invalid ${_.startCase(errors[0]?.property)} in row ${index + 2
                }`;
              errorsArray = [...errorsArray, errorDuplicate];
              notAddedClient += 1;
              validateError = 1;
            }
            //  else {
            //   console.log("errors.length",errors.length)
            //   result.push({ ...client } as Client);
            // }
          });
          if (validateError) {
            continue;
          }
          if (existingUser) {
            alreadyAddedClients += 1;
            const errorName = `row ${index + 2} have duplicate data i.e. Display Name, GST, PAN`;
            errorsArray = [...errorsArray, errorName];
            rows += `${index + 2},`;
          }

          if (tanNumber && tanNumber !== "undefined" && tanNumber !== "" && !TAN_REGEX.test(tanNumber)) {
            const errorDuplicate = `row ${index + 2
              } has Invalid TAN`;
            errorsArray = [...errorsArray, errorDuplicate];
            notAddedClient += 1;
            continue;
          }

          if (panNumber && panNumber !== "undefined" && panNumber !== "" && !PAN_REGEX.test(panNumber)) {
            const errorDuplicate = `row ${index + 2
              } has Invalid PAN`;
            errorsArray = [...errorsArray, errorDuplicate];
            notAddedClient += 1;
            continue;
          }

          if (gstNumber && gstNumber !== "undefined" && gstNumber !== "" && !gstinPattern.test(gstNumber)) {
            const errorDuplicate = `row ${index + 2
              } has Invalid GST`;
            errorsArray = [...errorsArray, errorDuplicate];
            notAddedClient += 1;
            continue;
          }

          if (!existingUser) {
            // throw new BadRequestException(
            //   `"${existingUser.email}" or "${existingUser.displayName}" already exists in your organization`,
            // );
            const a = gstNumber?.slice(2, gstNumber?.length - 3);
            if (gstNumber !== 'undefined') {
              if (panNumber !== 'undefined') {
                if (!(a === panNumber)) {
                  const errorName = `row ${index + 2
                    } - Gst ${gstNumber} and Pan ${panNumber} is not Matched`;
                  errorsArray = [...errorsArray, errorName];
                  notAddedClient += 1;
                  continue;
                  // throw new BadRequestException(
                  //   `Gst ${gstNumber} and Pan ${panNumber} is not Matched`,
                  // );
                }
              }
            }
            client.panVerified = false;
            client.panNumber =
              (' ' + data['PAN'])?.trim() !== 'undefined' ? (' ' + data['PAN'])?.trim() : null;
            client.tanNumber = (' ' + data['TAN'])?.trim() !== 'undefined' ? (' ' + data['TAN'])?.trim() : null;
            if (gstNumber !== 'undefined') {
              if (gstNumber.length === 15) {

                const gstData = async (gstNumber) => {
                  try {
                    const headers = {
                      Authorization: process.env.FYN_AUTH,
                    };
                    const url = `${process.env.FYN_URL}${gstNumber}`;
                    const response = await axios.get(url, { headers });
                    const res = { data: response.data };
                    return res;
                  } catch (error) {
                    console.error(error);
                  }
                };
                const gstDetailsData = await gstData(gstNumber);

                if (gstDetailsData.data.sts) {
                  const address: any = {
                    communicationfulladdress: this.getGstAddress(gstDetailsData?.data?.pradr?.addr),
                  };
                  address["billingfulladdress"] = address?.communicationfulladdress;
                  address["shippingfulladdress"] = address?.communicationfulladdress
                  client.issameaddress = true;
                  client.gstVerified = true;
                  client.gstNumber = gstDetailsData?.data?.gstin;
                  client.legalName = gstDetailsData?.data?.lgnm;
                  client.address = address;
                  client.gstRegistrationDate = gstDetailsData?.data?.rgdt || null;
                  client.tradeName = gstDetailsData?.data?.tradeNam;
                  client.constitutionOfBusiness = gstDetailsData?.data?.ctb;
                  client.placeOfSupply = gstDetailsData?.data?.pradr?.addr?.stcd;
                  client.buildingName = gstDetailsData?.data?.pradr?.addr?.bnm;
                  client.street = gstDetailsData?.data?.pradr?.addr?.st;
                  client.city = gstDetailsData?.data?.pradr?.addr?.dst;
                  client.state = gstDetailsData?.data?.pradr?.addr?.stcd;
                  client.pincode = gstDetailsData?.data?.pradr?.addr?.pncd;
                  const gstCharAt13 = client.gstNumber.charAt(13);
                  const extractedNumber = client.gstNumber.substring(2, client.gstNumber.length - 3);
                  if (client.gstNumber === 'D') {
                    const pattren = TAN_REGEX.test(extractedNumber);
                    if (pattren) {
                      client.tanNumber = client.tanNumber;
                    } else {
                      client.panNumber = client.panNumber;
                    }

                    client.registrationType = RegistrationType.TAX_DEDUCTOR;
                  } else if (gstCharAt13 === 'Z' || gstCharAt13 === 'C') {
                    client.panNumber = client.panNumber;
                    client.registrationType =
                      gstCharAt13 === 'Z' ? RegistrationType.REGULAR_TAXPAYER : RegistrationType.TAX_COLLECTOR;
                  }
                } else {
                  client.gstNumber = gstNumber;
                }
              } else {
                const errorName = `row ${index + 2} - GST ${gstNumber} is invalid Pattern`;
                errorsArray = [...errorsArray, errorName];
                notAddedClient += 1;
                continue;
              }
            } else {
              const address: any = {
                communicationfulladdress: data['Address']
              }
              if (data['Address']) {
                client.issameaddress = true;
                address["billingfulladdress"] = address?.communicationfulladdress;
                address["shippingfulladdress"] = address?.communicationfulladdress
                client.address = address;
              }
              client.constitutionOfBusiness = (' ' + data['Constitution of Business'])?.trim() !== "undefined" ? (' ' + data['Constitution of Business'])?.trim() : null;
              client.placeOfSupply = (' ' + data['State Jurisdiction / Place of Supply'])?.trim() !== "undefined" ? (' ' + data['State Jurisdiction / Place of Supply'])?.trim() : null;
              client.buildingName = (' ' + data['Building No. / Flat No.'])?.trim() !== "undefined" ? (' ' + data['Building No. / Flat No.'])?.trim() : null;
              client.street = (' ' + data['Road / Street'])?.trim() !== "undefined" ? (' ' + data['Road / Street'])?.trim() : null;
              client.city = (' ' + data['City / Town / Village'])?.trim() !== "undefined" ? (' ' + data['City / Town / Village'])?.trim() : null;
              client.state = (' ' + data['State / Union Territory'])?.trim() !== "undefined" ? (' ' + data['State / Union Territory'])?.trim() : null;
              client.pincode = (' ' + data['Pincode'])?.trim() !== "undefined" ? (' ' + data['Pincode'])?.trim() : null;
            }
            client.firstName =
              (' ' + data['First Name'])?.trim() !== 'undefined'
                ? (' ' + data['First Name'])?.trim()
                : null;
            client.middleName =
              (' ' + data['Middle Name'])?.trim() !== 'undefined'
                ? (' ' + data['Middle Name'])?.trim()
                : null;
            client.lastName =
              (' ' + data['Last Name'])?.trim() !== 'undefined'
                ? (' ' + data['Last Name'])?.trim()
                : null;
            client.fullName =
              (' ' + data['Full Name'])?.trim() !== 'undefined'
                ? (' ' + data['Full Name'])?.trim()
                : null;
            let newUser = new User();
            newUser.fullName = (' ' + data['Display Name *'])?.trim();
            newUser.email = (' ' + data['Email Id *'])?.trim();
            newUser.password = null;
            newUser.organization = user.organization;
            newUser.mobileNumber = data['Mobile Number *'];
            newUser.type = UserType.CLIENT;
            client.user = newUser;
            client['Income Tax ID (PAN)'] = data['Income Tax ID (PAN)'];
            client['Income Tax Password'] = data['Income Tax Password'];



            result.push({ ...client } as Client);
          }
        } else {
          // throw new BadRequestException(
          //   `Improper Client details, Please upload as per sample sheet`,
          // );
          const errorDuplicate = `Row ${index + 2
            } have Improper Client details, Mandatory Fields Missing.`;
          errorsArray = [...errorsArray, errorDuplicate];
          notAddedClient += 1;
          continue;
        }
      }
      if (result.length > 0) {
        try {
          for (const client of result) {
            client.localDirectoryPath = [];
            addedArray += 1;
            await Client.save(client);
            if (client.gstVerified) {
              let gstrRegister = new GstrRegister();
              gstrRegister.client = client;
              gstrRegister.registrationType = client.registrationType;
              gstrRegister.organization = user.organization;
              gstrRegisters.push(gstrRegister);
            };
            if (client['Income Tax Password'] && client["Income Tax ID (PAN)"]) {
              let credential = new Password();
              credential.website = 'Income Tax | e-Filing (PAN)';
              credential.websiteUrl = 'https://eportal.incometax.gov.in/iec/foservices/#/login';
              credential.loginId = client["Income Tax ID (PAN)"];
              credential.password = client['Income Tax Password'];
              credential.client = client;
              await credential.save()
              // credentilas.push(credential);
            };

            let randomPassword = randomBytes(16).toString('hex');
            // this.eventEmitter.emit(Event_Actions.CLIENT_CREATED, {
            //   userId,
            //   data: client,
            //   orgName: user?.organization?.legalName,
            //   isEmail: undefined,
            // });
          };
          // await GstrRegister.save(gstrRegisters);

          errorsArray = [...errorsArray, notAddedClient, addedArray];
          return errorsArray;
        } catch (err) {
          // throw new InternalServerErrorException(err);
          const errorDuplicate = String(new InternalServerErrorException(err));
          notAddedClient += 1;
          errorsArray = [...errorsArray, errorDuplicate, notAddedClient, addedArray];
          return errorsArray;
        }
      } else {
        // throw new BadRequestException(
        //   `All Clients are already Added and Atleast one new row required as per sample sheet`,
        // );
        const errorone = `All ${alreadyAddedClients} Clients (rows ${rows.slice(
          0,
          -1,
        )}) are already Added and Atleast one new row required as per sample sheet`;
        errorsArray = [...errorsArray, errorone, notAddedClient, addedArray];
        return errorsArray;
      }
    } else {
      throw new BadRequestException(`Atleast one row required as per sample sheet`);
    }
  }

  async getAllClients(id: number) {
    let user = await User.findOne({ where: { id: id }, relations: ['organization'] });

    let clients = getConnection()
      .createQueryBuilder(Client, 'client')
      .leftJoin('client.labels', 'labels')
      .leftJoin('client.organization', 'organization')
      .orderBy('client.displayName', 'ASC')
      .leftJoinAndSelect('client.contactPersons', 'contactPersons')
      .where('organization.id = :organization', { organization: user.organization.id })
      .getMany();

    return clients;
  };

  async gstData(gstNumber) {
    try {
      const headers = {
        Authorization: process.env.FYN_AUTH,
      };
      const url = `${process.env.FYN_URL}${gstNumber}`;
      const response = await axios.get(url, { headers });
      const res = { data: response.data };
      return res;
    } catch (error) {
      console.error(error);
    }
  };
}
