import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  ParseIntPipe,
  Post,
  Put,
  Query,
  Req,
  UseGuards,
} from '@nestjs/common';
import { JwtAuthGuard } from 'src/modules/users/jwt/jwt-auth.guard';
import { GstrDashboardService } from '../service/dashboard.services';

@Controller('gstr-dashboard')
export class GstrDashboardController {
  constructor(private service: GstrDashboardService) {}

  @UseGuards(JwtAuthGuard)
  @Get('not-ord')
  getNoticeOrdersDateCount(@Req() req: any, @Query() query: any) {
    const { userId } = req.user;
    return this.service.getNoticeOrdersDateCount(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Get('add-not-ord')
  getAdditionalNoticeOrdersDateCount(@Req() req: any, @Query() query: any) {
    const { userId } = req.user;
    return this.service.getAdditionalNoticeOrdersDateCount(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Get('/config-status')
  getIncometaxConfigStatus(@Req() req: any, @Query() query: any) {
    const { userId } = req.user;
    return this.service.getGstrConfigStatus(userId, query.startDates);
  }

  @UseGuards(JwtAuthGuard)
  @Get('verification')
  clientCheck(@Req() req: any, @Query() query: any) {
    const { userId } = req.user;
    return this.service.clientCheck(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Post('/exportGstrInvalid')
  async exportGstrInvalid(@Req() req: any, @Body() body: any) {
    const { userId } = req?.user;
    const query = body;
    return this.service.exportGstrInvalid(userId, query);
  }

    @UseGuards(JwtAuthGuard)
  @Get('add-not-ord-stats')
  getAdditionalNoticesStatCount(@Req() req: any, @Query() query: any) {
    const { userId } = req.user;
    return this.service.getAdditionalNoticesStatCount(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Get('demand-stats')
  outstandingDemandStats(@Req() req:any, @Query() query:any){
    const {userId} = req.user;
    return this.service.outstandingDemandStats(userId,query);
  }

  @UseGuards(JwtAuthGuard)
  @Get('additional-notice-stats')
  getAdditionalNoticeStats(@Req() req:any, @Query() query:any){
    const {userId} = req.user;
    return this.service.getAdditionalNoticeStats(userId, query)
  }
}
