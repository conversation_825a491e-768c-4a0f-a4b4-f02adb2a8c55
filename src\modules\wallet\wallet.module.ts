import { Modu<PERSON> } from "@nestjs/common";
import { TypeOrmModule } from "@nestjs/typeorm";
import { Wallets } from "./entities/wallets.entity";
import { WalletsTransactions } from "./entities/wallets-transactions.entity";
import { WalletActivity } from "./entities/wallet-activity.entity";
import { WalletController } from "./controllers/wallet.controller";
import { WalletService } from "./services/wallet.service";
import { WalletsTransactionsSubscriber } from "src/event-subscribers/wallets-transactions.subscriber";


@Module({
    imports: [
        TypeOrmModule.forFeature([Wallets,
            WalletsTransactions,
            WalletActivity])
    ],
    controllers: [
        WalletController
    ],
    providers: [
        WalletService,
        WalletsTransactionsSubscriber
        
    ]
})

export class WalletModule { }