import {
  BadRequestException,
  Body,
  Controller,
  Delete,
  Get,
  Param,
  ParseIntPipe,
  Post,
  Put,
  Query,
  Request,
  UseGuards,
} from '@nestjs/common';
import { IsNotEmpty, IsOptional } from 'class-validator';
import { User } from 'src/modules/users/entities/user.entity';
import { JwtAuthGuard } from 'src/modules/users/jwt/jwt-auth.guard';
import { createQueryBuilder, In } from 'typeorm';
import { Permission } from './entities/permission.entity';
import { Role } from './entities/role.entity';
import { Cron, CronExpression } from '@nestjs/schedule';

class RoleDto {
  @IsNotEmpty()
  name: string;

  @IsOptional()
  description: string;
}

class UpdateRoleDto {
  @IsOptional()
  name: string;

  @IsOptional()
  description: string;

  @IsOptional()
  active: boolean;

  @IsOptional()
  permissions: Array<number>;
}

@Controller('roles')
export class RolesController {
  @Get('/all')
  async getAllRoles() {
    let roles = await Role.find();
    return roles;
  }

  @UseGuards(JwtAuthGuard)
  @Get()
  async getRoles(@Request() req: any, @Query() query: any) {
    const { userId } = req.user;
    let user = await User.findOne(userId);
    let roles = createQueryBuilder(Role, 'roles')
      .leftJoin('roles.organization', 'organization')
      .where('organization.id = :organization', { organization: user.organization.id });
      const sort = (typeof query?.sort === "string") ? JSON.parse(query.sort) : query?.sort;
      if (sort?.column) {
          const columnMap: Record<string, string> = {
            name: 'roles.name',
            createdAt: 'roles.createdAt',
          };
          const column = columnMap[sort.column] || sort.column;
          roles.orderBy(column, sort.direction.toUpperCase());
      } 
    if (query.offset >= 0) {
      roles.skip(query.offset);
    }

    if (query.limit) {
      roles.take(query.limit);
    }
    return roles.getManyAndCount();
  }
  @UseGuards(JwtAuthGuard)
  @Get(':roleId')
  async getRole(@Request() req: any,@Param('roleId', ParseIntPipe) roleId: number) {
    const { userId } = req.user;
    let user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
    let role = await Role.findOne({ where: { id: roleId,  organization: user?.organization?.id }, relations: ['permissions'] });
    return role;
  }

  @UseGuards(JwtAuthGuard)
  @Post()
  async createRole(@Request() req: any, @Body() { name, description }: RoleDto) {
    const { userId } = req.user;
    let user = await User.findOne({ where: { id: userId }, relations: ['organization'] });

    let existingRole = await Role.findOne({
      where: { name, organization: user.organization.id },
    });

    if (existingRole) throw new BadRequestException('Role already exists');

    const role = new Role();
    role.organization = user.organization;
    role.name = name;
    role.description = description;
    await role.save();
    return role;
  }

  @UseGuards(JwtAuthGuard)
  @Put(':id')
  async updateRole(@Request() req: any, @Param() { id }, @Body() { name, description, permissions, active }: UpdateRoleDto) {
    const { userId } = req.user;
    let user = await User.findOne({ where: { id: userId }, relations: ['organization'] });

    const role = await Role.findOne(id);
    role.name = name;
    role.description = description;
    role.active = active;

    let existingRole = await Role.findOne({
      where: { name, organization: user?.organization?.id },
    });
    if (existingRole) throw new BadRequestException('Role already exists');

    if (permissions && permissions.length) {
      let perms = await Permission.find({ where: { id: In(permissions) } });
      role.permissions = perms;
    }

    await role.save();
    return role;
  }

  @Delete(':id')
  async deleteRole(@Param('id', ParseIntPipe) id: number) {
    let role = await Role.findOne({ where: { id } });
    await role.remove();
    return 'success';
  }

  // @Cron(CronExpression.EVERY_HOUR)
  // async sendCron(){
  //   const role = await createQueryBuilder(Role, 'role')
  //     .leftJoinAndSelect('role.permissions','permissions')
  //     // .leftJoinAndSelect('role.organization','organization')
  //     // .where('organization.id = 934')
  //     .getMany();
  //     let roleCount = 0;
  //     console.log(role,"role",role.length);
  //   for(let i of role){
  //     const permissions = i.permissions.map(item => item.id);
  //     if(permissions.includes(375)){
  //     } else {
  //       roleCount += 1;
  //       console.log(i.id,i.name);
  //       permissions.push(375);
  //       let perms = await Permission.find({ where: { id: In(permissions) } });
  //       i.permissions = perms;
  //       await i.save();
  //     }
  //   }
  //   console.log(roleCount,"roleCount");
  // }
}
