import {
  BadRequestException,
  Injectable,
  InternalServerErrorException,
} from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { validate } from 'class-validator';
import { randomBytes } from 'crypto';
import * as _ from 'lodash';
import { Event_Actions } from 'src/event-listeners/actions';
import { User, UserStatus, UserType } from 'src/modules/users/entities/user.entity';
import { Brackets, In, Not, createQueryBuilder, getConnection, getManager } from 'typeorm';
import * as xlsx from 'xlsx';
import BulkUpdateDto from '../dto/bulk-update.dto';
import CreateClientDto from '../dto/create-client.dto';
import FindQueryDto from '../dto/find-query.dto';
import { CategoryEnum, SubCategoryEnum } from '../dto/types';
import { UpdateClientDto } from '../dto/update-client.dto';
import Client from '../entity/client.entity';
import axios from 'axios';
import * as moment from 'moment';
import Task from 'src/modules/tasks/entity/task.entity';
import { TaskRecurringStatus, TaskStatusEnum } from 'src/modules/tasks/dto/types';
import Storage, { StorageSystem } from 'src/modules/storage/storage.entity';
import { StorageService } from 'src/modules/storage/storage.service';
import Activity, { ActivityType } from 'src/modules/activity/activity.entity';
import { getTitle } from 'src/utils';
import GstrRegister, {
  RegistrationType,
} from 'src/modules/gstr-register/entity/gstr-register.entity';
import Password, { IsExistingAtomPro } from '../entity/password.entity';
import AutClientCredentials, {
  IncomeTaxStatus,
  syncStatus,
} from 'src/modules/automation/entities/aut_client_credentials.entity';
import parsePhoneNumberFromString from 'libphonenumber-js';
import countries from 'src/utils/countries';
import mobileWithCountry from 'src/utils/validations/mobileWithCountry';
import ClientGroup from 'src/modules/client-group/client-group.entity';
import { TAN_REGEX } from 'src/utils/validations/regex-pattrens';
import { OneDriveStorageService } from 'src/modules/ondrive-storage/onedrive-storage.service';
import isValidString from 'src/utils/validations/displayName';
import OrganizationPreferences from 'src/modules/organization-preferences/entity/organization-preferences.entity';
import GstrCredentials, {
  GstrStatus,
} from 'src/modules/gstr-automation/entity/gstrCredentials.entity';

import { ClientPermission } from '../entity/client-permission.entity';
import checkGstrUsername from 'src/utils/validations/atom-pro/gstrUserName';
import checkGstrPassword from 'src/utils/validations/atom-pro/gstrPassword';
import checkPanNumber, { checkTanNumber } from 'src/utils/validations/atom-pro/panNumber';
import { checkAtomProConfigGstr, checkAtomProConfigIncomeTax, checkAtomProConfigIncomeTaxTan } from 'src/utils/atomProReUse';
import e from 'express';
import { BharathStorageService } from 'src/modules/storage/bharath-storage.service';
import { CLIENT_CATEGORIES } from 'src/utils/clientExport';
import TanClientCredentials from 'src/modules/tan-automation/entity/tan-client-credentials.entity';
import { Permissions } from 'src/modules/events/permission';
import checkTracesUserId from 'src/utils/validations/atom-pro/tracesUserId';
import checkTracesPassword from 'src/utils/validations/atom-pro/tracesPassword';
import { GoogleDriveStorageService } from 'src/modules/ondrive-storage/googledrive-storage.service';
import { ImportStatus } from '../entity/import-data.entity';
import ImportData from '../entity/import-data.entity';
import { Notification } from 'src/notifications/notification.entity';
import { getAdminEmailssBasedOnOrganizationId, getAdminIDsBasedOnOrganizationId } from 'src/utils/re-use';
import { sendnewMail } from 'src/emails/newemails';

@Injectable()
export class ClientService {
  constructor(
    private eventEmitter: EventEmitter2,
    private storageService: StorageService,
    private oneDriveService: OneDriveStorageService,
    private googleDriveService: GoogleDriveStorageService,
    private bharathService: BharathStorageService,
  ) { }

  async create(userId: number, data: CreateClientDto) {
    try {
      let user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
      const displayName = (' ' + data.displayName)?.trim();

      let existingUser = await createQueryBuilder(Client, 'client')
        .leftJoin('client.organization', 'organization')
        .where('organization.id = :organization', { organization: user.organization.id })
        .andWhere('(client.displayName = :displayName)', {
          displayName: displayName,
        })
        .getOne();

      if (existingUser) {
        throw new BadRequestException(
          'Client with the given Display Name already Exists in your Organization',
        );
      }

      if (data?.clientNumber) {
        let existingNumber = await createQueryBuilder(Client, 'client')
          .leftJoin('client.organization', 'organization')
          .where('organization.id = :organization', { organization: user.organization.id })
          .andWhere('(client.clientNumber = :clientNumber)', {
            clientNumber: data?.clientNumber,
          })
          .getOne();

        if (existingNumber && data?.clientNumberDuplicate) {
          throw new BadRequestException('Client Number Exists');
        }
      }
      let clientManagers = await createQueryBuilder(User, 'user')
        .where('user.id IN (:...ids)', { ids: data.clientManagers })
        .getMany();

      if (!(' ' + data.displayName)?.trim().length) {
        throw new BadRequestException('Invalid Display Name');
      }

      if (data?.clientPortalAccess) {
        let existingClientPortalUser = await createQueryBuilder(Client, 'client')
          .leftJoinAndSelect('client.organization', 'organization')
          .where('organization.id != :organization', { organization: user.organization.id })
          .andWhere('client.email = :email', {
            email: data.email,
          })
          .andWhere('client.clientPortalAccess = :clientPortalAccess', {
            clientPortalAccess: data.clientPortalAccess,
          })
          .getOne();

        if (existingClientPortalUser) {
          throw new BadRequestException(
            'The Email address provided already has Client Portal Access enabled in another organization.',
          );
        }
      }

      let newUser = new User();
      newUser.fullName = data.displayName.trim();
      newUser.email = data.email;
      newUser.password = null;
      newUser.mobileNumber = data.mobileNumber;
      newUser.organization = user.organization;
      (newUser['userId'] = userId), (newUser.type = UserType.CLIENT);

      let client = new Client();
      client.displayName = data.displayName.trim();
      client.email = data.email;
      client.mobileNumber = data.mobileNumber;
      client.category = data.category;
      client.subCategory = data.subCategory;
      client.tradeName = data.tradeName;
      client.legalName = data.legalName;
      client.constitutionOfBusiness = data.constitutionOfBusiness;
      client.placeOfSupply = data.placeOfSupply;
      client.firstName = data.firstName;
      client.lastName = data.lastName;
      client.fullName = data.fullName;
      client.designation = data.designation;
      client.gstNumber = data.gstNumber;
      client.middleName = data.middleName;
      client.panNumber = data.panNumber;
      client.gstVerified = data.gstVerified;
      client.panVerified = data.panVerified;
      client.authorizedPerson =
        data.authorizedPerson !== null && data.authorizedPerson !== undefined
          ? data.authorizedPerson.trim()
          : data.authorizedPerson;
      client.designation =
        data.designation !== null && data.designation !== undefined
          ? data.designation.trim()
          : data.designation;
      client.clientManagers = clientManagers;
      client.createdBy = user;
      client.organization = user.organization;
      client.user = newUser;
      client.clientPortalAccess = data.clientPortalAccess;
      client.issameaddress = data.issameaddress;
      client.clientNumber = data.clientNumber;
      client.localDirectoryPath = [];
      client.countryCode = data.countryCode;
      if (data?.clientPortalAccess) {
        const getClientDefaultPermissions = await createQueryBuilder(
          ClientPermission,
          'clientPermission',
        )
          .where('clientPermission.defaultOne = :defaultValue', { defaultValue: 1 })
          .getMany();
        client.permissions = getClientDefaultPermissions;
        this.eventEmitter.emit(Event_Actions.CLIENT_PORTAL_ACCESS_UPDATED, {
          userId,
          user: user,
          data: client,
        });
      }

      client['userId'] = user.id;
      await client.save();

      if (data['clientGroup']) {
        let clientGroup = await createQueryBuilder(ClientGroup, 'clientGroup')
          .leftJoinAndSelect('clientGroup.clients', 'clients')
          .where('clientGroup.id = :id', { id: data['clientGroup'] })
          .getOne();

        clientGroup.clients = [...clientGroup.clients, client]
        clientGroup.save();
      }

      this.eventEmitter.emit(Event_Actions.CLIENT_CREATED, {
        userId,
        data: client,
        orgName: user?.organization?.legalName,
        isEmail: data?.isEmail,
      });
      return client;
    } catch (err) {
      console.log(err);
      throw new InternalServerErrorException(err);
    }
  }

  async findAll(userId: number, query: FindQueryDto): Promise<{ count: number; result: Client[] }> {
    const { limit, offset } = query;
    const { status: state, category, subCategory, monthAdded, search, labels, dscId } = query;
    const month = new Date(monthAdded).getMonth() + 1;
    const year = new Date(monthAdded).getFullYear();

    let user = await User.findOne({ where: { id: userId }, relations: ['organization', 'role'] });

    let ViewAll = user.role.permissions.find(
      (permission) => permission.slug === Permissions.VIEW_ALL_CLIENT_MANAGERS,
    );
    let ViewAssigned = user.role.permissions.find(
      (permission) => permission.slug === Permissions.VIEW_ASSIGNED_CLIENT_MANAGERS,
    );

    let clients = getConnection()
      .createQueryBuilder(Client, 'client')
      .leftJoin('client.labels', 'labels')
      .leftJoin('client.clientManagers', 'clientManagers')
      .leftJoin('client.organization', 'organization')
      .leftJoinAndSelect('client.dscRegisters', 'dscRegister')
      .orderBy('client.displayName', 'ASC')
      .leftJoinAndSelect('client.contactPersons', 'contactPersons')
      .leftJoinAndSelect('client.clientImage', 'clientImage')
      .where('organization.id = :organization', { organization: user.organization.id })

    if (ViewAssigned && !ViewAll) {
      clients.andWhere('clientManagers.id = :userId', { userId });
    }

    if (category?.length) {
      clients.andWhere('client.category in (:...category)', { category });
    }

    if (subCategory?.length) {
      clients.andWhere('client.subCategory in (:...subCategory)', { subCategory });
    }

    if (dscId) {

      clients.andWhere('client.id NOT IN (SELECT dsrc.client_id FROM dsc_register_clients_client dsrc JOIN dsc_register dsc ON dsc.id = dsrc.dsc_register_id WHERE dsc.id = :dscR)', { dscR: dscId });
    }


    if (state?.length) {
      clients.andWhere('client.status in (:...state)', { state });
    } else {
      clients.andWhere('client.status != :status', { status: UserStatus.DELETED });
    }

    if (labels?.length) {
      clients.andWhere('labels.id in (:...labels)', { labels });
    }
    if (search) {
      const whereClause = `(
        client.displayName like '%${search}%' or
        client.email like '%${search}%' or
        client.mobileNumber like '%${search}%' or
        client.category like '%${search}%' or
        client.panNumber like '%${search}%' or
        client.gstNumber like '%${search}%' or
        client.tradeName like '%${search}%' or 
        client.clientNumber like '%${search}%'
      )`;
      clients.andWhere(whereClause);
    };



    if (monthAdded) {
      clients.andWhere('MONTH(client.createdAt) = :month and YEAR(client.createdAt) = :year', {
        month,
        year,
      });
    }
    const sort = (typeof query?.sort === "string") ? JSON.parse(query.sort) : query?.sort;
    if (sort?.column) {
      const columnMap: Record<string, string> = {
        clientId: 'client.clientId',
        clientNumber: 'client.clientNumber',
        displayName: 'client.displayName',
        tradeName: 'client.tradeName',
        category: 'client.category',
        authorizedPerson: 'client.authorizedPerson',
        active: 'client.state',
      };
      const column = columnMap[sort.column] || sort.column;
      clients.orderBy(column, sort.direction.toUpperCase());
    } else {
      clients.orderBy('client.createdAt', 'DESC');
    };
    if (offset >= 0) {
      clients.skip(offset);
    }

    if (limit) {
      clients.take(limit);
    }
    let result = await clients.getManyAndCount();

    return {
      count: result[1],
      result: result[0],
    };
  }

  async findAllClientManagerClient(userId: number, query: any) {
    let user = await User.findOne({ where: { id: query?.selectedUser }, relations: ['organization', 'role'] });

    let clients = createQueryBuilder(Client, 'client')
      .leftJoin('client.organization', 'organization')
      .where('organization.id = :organization', { organization: user.organization.id })
      .andWhere('client.status != :status', { status: UserStatus.DELETED })
      .andWhere(qb => {
        const subQuery = qb
          .subQuery()
          .select('1')
          .from('client_client_managers_user', 'cm')
          .where('cm.client_id = client.id')
          .andWhere('cm.user_id = :userId')
          .getQuery();

        return `NOT EXISTS (${subQuery})`;
      }, { userId: query?.selectedUser })
      .orderBy('client.displayName', 'ASC');

    let result = await clients.getManyAndCount();

    return {
      count: result[1],
      result: result[0],
    };
  }

  async findAllClientGroupManagerClient(userId: number, query: any) {
    let user = await User.findOne({ where: { id: userId }, relations: ['organization', 'role'] });

    let clients = getConnection()
      .createQueryBuilder(ClientGroup, 'clientGroup')
      .leftJoin('clientGroup.organization', 'organization')
      .orderBy('clientGroup.displayName', 'ASC')
      .where('organization.id = :organization', { organization: user.organization.id })
      .andWhere(qb => {
        const subQuery = qb
          .subQuery()
          .select('1')
          .from('client_group_client_group_managers_user', 'cm')
          .where('cm.client_group_id = clientGroup.id')
          .andWhere('cm.user_id = :userId')
          .getQuery();

        return `NOT EXISTS (${subQuery})`;
      }, { userId: query?.selectedUser })
      .andWhere('clientGroup.status != :status', { status: UserStatus.DELETED });

    let result = await clients.getManyAndCount();

    return {
      count: result[1],
      result: result[0],
    };
  }

  async getWithoutClients(userId: number, query): Promise<{ count: number; result: Client[] }> {
    const { limit, offset } = query;
    const { status: state, category, subCategory, monthAdded, search, labels } = query;
    const month = new Date(monthAdded).getMonth() + 1;
    const year = new Date(monthAdded).getFullYear();

    let user = await User.findOne({ where: { id: userId }, relations: ['organization'] });

    let clients = getConnection()
      .createQueryBuilder(Client, 'client')
      .leftJoin('client.labels', 'labels')
      .leftJoin('client.groupClients', 'groupClients')
      .leftJoin('client.organization', 'organization')
      .orderBy('client.displayName', 'ASC')
      .leftJoinAndSelect('client.contactPersons', 'contactPersons')
      .leftJoinAndSelect('client.clientImage', 'clientImage')
      .where('organization.id = :organization', { organization: user.organization.id })
      .andWhere('groupClients.id is NULL');

    const sort = (typeof query?.sort === "string") ? JSON.parse(query.sort) : query?.sort;
    if (sort?.column) {
      const columnMap: Record<string, string> = {
        clientId: 'client.clientId',
        clientNumber: 'client.clientNumber',
        displayName: 'client.displayName',
        category: 'client.category',
        active: 'client.status'
      };
      const column = columnMap[sort.column] || sort.column;
      clients.orderBy(column, sort.direction.toUpperCase());
    } else {
      clients.orderBy('client.createdAt', 'DESC');
    }
    if (category?.length) {
      clients.andWhere('client.category in (:...category)', { category });
    }

    if (subCategory?.length) {
      clients.andWhere('client.subCategory in (:...subCategory)', { subCategory });
    }

    if (state?.length) {
      clients.andWhere('client.status in (:...state)', { state });
    } else {
      clients.andWhere('client.status != :status', { status: UserStatus.DELETED });
    }

    if (labels?.length) {
      clients.andWhere('labels.id in (:...labels)', { labels });
    }

    if (search) {
      const whereClause = `(
        client.displayName like '%${search}%' or
        client.email like '%${search}%' or
        client.mobileNumber like '%${search}%' or
        client.category like '%${search}%' or
        client.panNumber like '%${search}%' or
        client.tradeName like '%${search}%' or 
        client.clientNumber like '%${search}%'
      )`;
      clients.andWhere(whereClause);
    }

    if (monthAdded) {
      clients.andWhere('MONTH(client.createdAt) = :month and YEAR(client.createdAt) = :year', {
        month,
        year,
      });
    }

    if (offset >= 0) {
      clients.skip(offset);
    }

    if (limit) {
      clients.take(limit);
    }

    let result = await clients.getManyAndCount();

    return {
      count: result[1],
      result: result[0],
    };
  }

  async getNonClientGroupClient(userId: number, query: FindQueryDto) {
    const { search } = query;

    let user = await User.findOne({ where: { id: userId }, relations: ['organization'] });

    let clients = getConnection()
      .createQueryBuilder(Client, 'client')
      .leftJoinAndSelect('client.groupClients', 'groupClients')
      .leftJoin('client.organization', 'organization')
      .orderBy('client.displayName', 'ASC')
      .where('organization.id = :organization', { organization: user.organization.id })
      .andWhere('groupClients.id IS NULL')
      .andWhere('client.status != :status', { status: UserStatus.DELETED });

    if (search) {
      const whereClause = `(
        client.displayName like '%${search}%' or
        client.email like '%${search}%' or
        client.mobileNumber like '%${search}%' or
        client.category like '%${search}%' or
        client.panNumber like '%${search}%' or
        client.tradeName like '%${search}%' or 
        client.clientNumber like '%${search}%'
      )`;
      clients.andWhere(whereClause);
    }

    let result = await clients.getManyAndCount();

    return {
      count: result[1],
      result: result[0],
    };
  }

  async getTaskCreateClientAll(userId: number, query: FindQueryDto) {
    const { limit, offset } = query;
    const { status: state, category, subCategory, monthAdded, search, labels } = query;
    const month = new Date(monthAdded).getMonth() + 1;
    const year = new Date(monthAdded).getFullYear();

    let user = await User.findOne({ where: { id: userId }, relations: ['organization', 'role'] });
    let ViewAll = user.role.permissions.find(
      (permission) => permission.slug === Permissions.VIEW_ALL_CLIENT_MANAGERS,
    );
    let ViewAssigned = user.role.permissions.find(
      (permission) => permission.slug === Permissions.VIEW_ASSIGNED_CLIENT_MANAGERS,
    );

    let clients = getConnection()
      .createQueryBuilder(Client, 'client')
      .leftJoin('client.clientManagers', 'clientManagers')
      .leftJoin('client.labels', 'labels')
      .leftJoin('client.organization', 'organization')
      .orderBy('client.displayName', 'ASC')
      .leftJoinAndSelect('client.contactPersons', 'contactPersons')
      .leftJoinAndSelect('client.clientImage', 'clientImage')
      .where('organization.id = :organization', { organization: user.organization.id });

    if (query.clientManagers) {
      if (ViewAssigned && !ViewAll) {
        clients.andWhere('clientManagers.id = :userId', { userId });
      }
    }

    if (category?.length) {
      clients.andWhere('client.category in (:...category)', { category });
    }

    if (subCategory?.length) {
      clients.andWhere('client.subCategory in (:...subCategory)', { subCategory });
    }

    if (state?.length) {
      clients.andWhere('client.status in (:...state)', { state });
    } else {
      clients.andWhere('client.status != :status', { status: UserStatus.DELETED });
    }

    if (labels?.length) {
      clients.andWhere('labels.id in (:...labels)', { labels });
    }

    if (search) {
      const whereClause = `(
        client.displayName like '%${search}%' or
        client.email like '%${search}%' or
        client.mobileNumber like '%${search}%' or
        client.category like '%${search}%' or
        client.panNumber like '%${search}%' or
        client.tradeName like '%${search}%' or 
        client.clientNumber like '%${search}%'
      )`;
      clients.andWhere(whereClause);
    }

    if (monthAdded) {
      clients.andWhere('MONTH(client.createdAt) = :month and YEAR(client.createdAt) = :year', {
        month,
        year,
      });
    }

    if (offset >= 0) {
      clients.skip(offset);
    }

    if (limit) {
      clients.take(limit);
    }

    let result = await clients.getManyAndCount();

    let clientsGroup = getConnection()
      .createQueryBuilder(ClientGroup, 'clientGroup')
      .leftJoin('clientGroup.clientGroupManagers', 'clientGroupManagers')
      .leftJoinAndSelect('clientGroup.organization', 'organization')
      .leftJoinAndSelect('clientGroup.clients', 'clients')
      .leftJoinAndSelect('clientGroup.clientGroupImage', 'clientGroupImage')
      .where('organization.id = :organization', { organization: user.organization.id })
      .andWhere('clientGroup.status != :status', { status: UserStatus.DELETED });

    if (search) {
      const whereClause = `(
        clientGroup.displayName like '%${search}%' or
        client.email like '%${search}%' or
        client.mobileNumber like '%${search}%' or
        client.category like '%${search}%' or
        client.panNumber like '%${search}%' or
        client.tradeName like '%${search}%' or 
        client.clientNumber like '%${search}%'
      )`;
      clientsGroup.andWhere(whereClause);
    }

    if (query.clientManagers) {
      if (ViewAssigned && !ViewAll) {
        clientsGroup.andWhere('clientGroupManagers.id = :userId', { userId });
      }
    }

    let groupresult = await clientsGroup.getManyAndCount();

    const data = [...result[0], ...groupresult[0]]
    data.sort((a, b) => {
      if (a.displayName < b.displayName) {
        return -1;
      }
      if (a.displayName > b.displayName) {
        return 1;
      }
      return 0;
    });

    return {
      count: result[1],
      result: data,
    };
  }

  async getImportData(userId: number) {

    let user = await User.findOne({ where: { id: userId }, relations: ['organization', 'role'] });

    let importData = getConnection()
      .createQueryBuilder(ImportData, 'importData')
      .leftJoin('importData.organization', 'organization')
      .where('organization.id = :organization', { organization: user.organization.id });

    let result = await importData.getOne();
    if (result) {
      result.responseData = result?.responseData ? JSON.parse(result.responseData) : null;
    }
    return result;
  }

  async getTaskCreateClientAllNew(userId: number, query: FindQueryDto) {
    const { limit = 10, page = 1, search, clientManagers, status, category, subCategory, labels, monthAdded } = query;
    const bool = clientManagers === 'false' ? false : true;
    const user = await User.findOne({ where: { id: userId }, relations: ['organization', 'role'] });
    const ViewAll = user.role.permissions.some(p => p.slug === Permissions.VIEW_ALL_CLIENT_MANAGERS);
    const ViewAssigned = user.role.permissions.some(p => p.slug === Permissions.VIEW_ASSIGNED_CLIENT_MANAGERS);

    const baseClientQuery = getConnection()
      .createQueryBuilder(Client, 'client')
      .leftJoin('client.clientManagers', 'clientManagers')
      // .leftJoin('client.labels', 'labels')
      .leftJoin('client.organization', 'organization')
      // .leftJoinAndSelect('client.contactPersons', 'contactPersons')
      // .leftJoinAndSelect('client.clientImage', 'clientImage')
      .select([
        'client.id',
        'client.displayName',
        'client.legalName',
        'client.buildingName',
        'client.street',
        'client.city',
        'client.state',
        'client.pincode',
        'client.email',
        'client.mobileNumber',
        'client.countryCode',
        'client.gstNumber',
        'client.address',
        'client.gstVerified',
      ])
      .where('organization.id = :organization', { organization: user.organization.id });

    if (bool && ViewAssigned && !ViewAll) {
      baseClientQuery.andWhere('clientManagers.id = :userId', { userId });
    };
    if (status?.length) baseClientQuery.andWhere('client.status IN (:...status)', { status });
    else baseClientQuery.andWhere('client.status != :status', { status: UserStatus.DELETED });

    if (category?.length) baseClientQuery.andWhere('client.category IN (:...category)', { category });
    if (subCategory?.length) baseClientQuery.andWhere('client.subCategory IN (:...subCategory)', { subCategory });
    // if (labels?.length) baseClientQuery.andWhere('labels.id IN (:...labels)', { labels });

    if (search) {
      baseClientQuery.andWhere(new Brackets(qb => {
        qb.where('client.displayName LIKE :search', { search: `%${search}%` })
        // .orWhere('client.email LIKE :search', { search: `%${search}%` })
        // .orWhere('client.mobileNumber LIKE :search', { search: `%${search}%` });
      }));
    }

    if (monthAdded) {
      const month = new Date(monthAdded).getMonth() + 1;
      const year = new Date(monthAdded).getFullYear();
      baseClientQuery.andWhere('MONTH(client.createdAt) = :month AND YEAR(client.createdAt) = :year', { month, year });
    }

    const clientsData = await baseClientQuery
      .orderBy('client.displayName', 'ASC')
      .take(limit)
      .skip(limit * page)
      .getMany();


    const clietCount = await baseClientQuery.getCount();

    const cgQuery =
      getConnection().
        createQueryBuilder(ClientGroup, 'clientGroup')
        .leftJoin('clientGroup.clientGroupManagers', 'clientGroupManagers')
        .leftJoin('clientGroup.organization', 'organization')
        .leftJoinAndSelect('clientGroup.clients', 'clients')
        .select([
          'clientGroup.id',
          'clientGroup.displayName',
          'clientGroup.legalName',
          'clientGroup.buildingName',
          'clientGroup.street',
          'clientGroup.city',
          'clientGroup.state',
          'clientGroup.pincode',
          'clientGroup.email',
          'clientGroup.mobileNumber',
          'clientGroup.countryCode',
          'clientGroup.gstNumber',
          'clientGroup.type',
          'clientGroup.address',
          'clientGroup.gstVerified',
          'clients'
        ])
        // .leftJoinAndSelect('clientGroup.clientGroupImage', 'clientGroupImage')
        .where('organization.id = :organization', { organization: user.organization.id })
        .andWhere('clientGroup.status != :status', { status: UserStatus.DELETED })
        .andWhere('clientGroup.displayName LIKE :search', { search: `%${search}%` })
        .take(limit)
        .skip(limit * page)
    const groupsData = await cgQuery.getMany();
    const clientGroupCount = await cgQuery.getCount()





    // const result = [...clientsData, ...groupsData].sort((a, b) => a.displayName.localeCompare(b.displayName));
    const result = [...clientsData, ...groupsData];

    return {
      result,
      totalCount: clietCount + clientGroupCount,
    };
  }


  async getStorageClientAll(userId: number, query: FindQueryDto) {
    const { limit, offset } = query;
    const { status: state, category, subCategory, monthAdded, search, labels } = query;
    const month = new Date(monthAdded).getMonth() + 1;
    const year = new Date(monthAdded).getFullYear();

    let user = await User.findOne({ where: { id: userId }, relations: ['organization', 'role'] });

    let ViewAll = user.role.permissions.find(
      (permission) => permission.slug === Permissions.VIEW_ALL_CLIENT_MANAGERS,
    );
    let ViewAssigned = user.role.permissions.find(
      (permission) => permission.slug === Permissions.VIEW_ASSIGNED_CLIENT_MANAGERS,
    );

    let clients = getConnection()
      .createQueryBuilder(Client, 'client')
      .leftJoin('client.labels', 'labels')
      .leftJoin('client.organization', 'organization')
      .leftJoin('client.clientManagers', 'clientManagers')
      .orderBy('client.displayName', 'ASC')
      .leftJoinAndSelect('client.contactPersons', 'contactPersons')
      .leftJoinAndSelect('client.clientImage', 'clientImage')
      .where('organization.id = :organization', { organization: user.organization.id });

    if (ViewAssigned && !ViewAll) {
      clients.andWhere('clientManagers.id = :userId', { userId });
    }

    if (category?.length) {
      clients.andWhere('client.category in (:...category)', { category });
    }

    if (subCategory?.length) {
      clients.andWhere('client.subCategory in (:...subCategory)', { subCategory });
    }

    if (state?.length) {
      clients.andWhere('client.status in (:...state)', { state });
    } else {
      clients.andWhere('client.status != :status', { status: UserStatus.DELETED });
    }

    if (labels?.length) {
      clients.andWhere('labels.id in (:...labels)', { labels });
    }

    if (search) {
      const whereClause = `(
        client.displayName like '%${search}%' or
        client.email like '%${search}%' or
        client.mobileNumber like '%${search}%' or
        client.category like '%${search}%' or
        client.panNumber like '%${search}%' or
        client.tradeName like '%${search}%' or 
        client.clientNumber like '%${search}%'
      )`;
      clients.andWhere(whereClause);
    }

    if (monthAdded) {
      clients.andWhere('MONTH(client.createdAt) = :month and YEAR(client.createdAt) = :year', {
        month,
        year,
      });
    }

    if (offset >= 0) {
      clients.skip(offset);
    }

    if (limit) {
      clients.take(limit);
    }

    let result = await clients.getManyAndCount();

    let clientsGroup = getConnection()
      .createQueryBuilder(ClientGroup, 'clientGroup')
      .leftJoinAndSelect('clientGroup.organization', 'organization')
      .leftJoinAndSelect('clientGroup.clients', 'clients')
      .leftJoin('clientGroup.clientGroupManagers', 'clientGroupManagers')
      .leftJoinAndSelect('clientGroup.clientGroupImage', 'clientGroupImage')
      .where('organization.id = :organization', { organization: user.organization.id })
      .andWhere('clientGroup.status != :status', { status: UserStatus.DELETED });

    if (ViewAssigned && !ViewAll) {
      clientsGroup.andWhere('clientGroupManagers.id = :userId', { userId });
    }

    if (search) {
      const whereClause = `(
        clientGroup.displayName like '%${search}%' or
        client.email like '%${search}%' or
        client.mobileNumber like '%${search}%' or
        client.category like '%${search}%' or
        client.panNumber like '%${search}%' or
        client.tradeName like '%${search}%' or 
        client.clientNumber like '%${search}%'
      )`;
      clients.andWhere(whereClause);
    }

    let groupresult = await clientsGroup.getManyAndCount();

    const data = [...result[0], ...groupresult[0]]
    data.sort((a, b) => {
      if (a.displayName < b.displayName) {
        return -1;
      }
      if (a.displayName > b.displayName) {
        return 1;
      }
      return 0;
    });

    return {
      count: result[1],
      result: data,
    };
  }

  async findOne(id: number, userId: number) {
    let user = await User.findOne({ where: { id: userId }, relations: ['organization', 'role'] });

    const clientQuery = await createQueryBuilder(Client, 'client')
      .leftJoinAndSelect('client.groupClients', 'groupClients')
      .leftJoinAndSelect('client.clientManagers', 'clientManagers')
      .leftJoinAndSelect('client.clientImage', 'clientImage')
      .leftJoinAndSelect('client.contactPersons', 'contactPersons')
      .leftJoinAndSelect('client.labels', 'labels')
      .leftJoinAndSelect('client.recurringProfiles', 'recurringProfiles')
      .leftJoinAndSelect('recurringProfiles.tasks', 'recurringProfilesTasks')
      .where('client.id = :id', { id })
      .andWhere('client.organization.id = :organization', { organization: user.organization.id });

    let ViewAll = user.role.permissions.find(
      (permission) => permission.slug === Permissions.VIEW_ALL_CLIENT_MANAGERS,
    );
    let ViewAssigned = user.role.permissions.find(
      (permission) => permission.slug === Permissions.VIEW_ASSIGNED_CLIENT_MANAGERS,
    );

    if (ViewAssigned && !ViewAll) {
      clientQuery.andWhere((qb) => {
        const subQuery = qb
          .subQuery()
          .select('1')
          .from('client_client_managers_user', 'cm')
          .where('cm.client_id = client.id')
          .andWhere('cm.user_id = :userId')
          .getQuery();
        return `EXISTS (${subQuery})`;
      }, { userId: user.id });
    }

    const client = await clientQuery.getOne();
    return client;
  }

  async update(userId: number, id: number, data: UpdateClientDto) {
    let user = await User.findOne({ where: { id: userId }, relations: ['organization'] });

    const clientDataBaseState = await Client.findOne({ where: { id } });

    if (!(' ' + data.displayName)?.trim().length) {
      throw new BadRequestException('Invalid Display Name');
    }

    let client = await Client.findOne({
      where: { id },
      relations: ['clientImage', 'user', 'organization'],
    });
    const oldClientPortalAcces = client.clientPortalAccess;
    const oldClientStatus = client.status;
    const oldClientDisplayName = client.displayName;

    if (data?.displayName !== undefined && data?.displayName !== 'undefined') {
      if (data?.displayName.length !== client?.displayName.length) {
        const displayName = (' ' + data.displayName)?.trim();
        let existingUser = await createQueryBuilder(Client, 'client')
          .leftJoin('client.organization', 'organization')
          .where('organization.id = :organization', { organization: user.organization.id })
          .andWhere('(client.displayName = :displayName)', {
            displayName: displayName,
          })
          .getOne();
        if (existingUser) {
          throw new BadRequestException(
            'Client with the given display name already exists in your organization',
          );
        }
      }
    }
    if (clientDataBaseState?.clientPortalAccess !== data?.clientPortalAccess) {
      if (data?.clientPortalAccess) {
        let existingClientPortalUser = await createQueryBuilder(Client, 'client')
          .leftJoinAndSelect('client.organization', 'organization')
          .where('organization.id != :organization', { organization: user.organization.id })
          .andWhere('client.email = :email', {
            email: data.email,
          })
          .andWhere('client.clientPortalAccess = :clientPortalAccess', {
            clientPortalAccess: data.clientPortalAccess,
          })
          .getOne();

        if (existingClientPortalUser) {
          throw new BadRequestException(
            'The Email address provided already has Client Portal Access enabled in another organization.',
          );
        }
      }
    }

    if (data?.gstNumber) {
      let existingGstNumber = await Client.findOne({
        where: {
          gstNumber: data.gstNumber,
          organization: user.organization.id,
          id: Not(id),
        },
      });
      if (existingGstNumber) {
        throw new BadRequestException(
          'Duplicate GSTIN Detected! To ensure accurate data management, GSTINs must be unique for each client. Please double-check the entered GSTIN.',
        );
      }
    }


    if (client.user) {
      let clinetUser = await User.findOne({
        where: { id: client.user.id, type: UserType.CLIENT },
        relations: ['organization'],
      });
      clinetUser.fullName = data.displayName;
      clinetUser.organization = user.organization;
      clinetUser.mobileNumber = data.mobileNumber;
      if (client.email !== data.email) {
        clinetUser.email = data.email;
      }
      clinetUser.save();
    }

    client.displayName =
      data.displayName !== null && data.displayName !== undefined
        ? data.displayName.trim()
        : data.displayName;
    client.mobileNumber = data.mobileNumber;
    client.alternateMobileNumber = data.alternateMobileNumber;
    client.category = data.category;
    client.subCategory = data.subCategory;
    client.email = data.email;
    client.tradeName = data.tradeName;
    client.legalName = data.legalName;
    client.constitutionOfBusiness = data.constitutionOfBusiness;
    client.middleName = data.middleName;
    client.gstRegistrationDate = data.gstRegistrationDate;
    client.placeOfSupply = data.placeOfSupply;
    client.fullName = data.fullName;
    client.firstName = data.firstName;
    client.lastName = data.lastName;
    client.gstNumber = data.gstNumber;
    client.panNumber = data.panNumber;
    client.tanNumber = data.tanNumber;
    client.clientNumber = data.clientNumber;
    if (data.clientManagers) {
      let clientManagers = await createQueryBuilder(User, 'user')
        .where('user.id IN (:...ids)', { ids: data.clientManagers })
        .getMany();
      client.clientManagers = clientManagers;
    }
    if (data?.gstNumber) {
      const gstCharAt13 = data.gstNumber.charAt(13);
      const extractedNumber = data.gstNumber.substring(2, data.gstNumber.length - 3);
      if (gstCharAt13 === 'D') {
        const pattren = TAN_REGEX.test(extractedNumber);
        if (pattren) {
          // client.tanNumber = data.tanNumber || extractedNumber;
          client.tanNumber = data.tanNumber;
        } else {
          // client.panNumber = data.panNumber || extractedNumber;
          client.panNumber = data.panNumber;
        }

        client.registrationType = RegistrationType.TAX_DEDUCTOR;
      } else if (gstCharAt13 === 'Z' || gstCharAt13 === 'C') {
        // client.panNumber = data.panNumber || extractedNumber;
        client.panNumber = data.panNumber;
        client.registrationType =
          gstCharAt13 === 'Z' ? RegistrationType.REGULAR_TAXPAYER : RegistrationType.TAX_COLLECTOR;
      }
    } else {
      client.tanNumber = data.tanNumber;
      client.panNumber = data.panNumber;
    }

    client.gstVerified = data.gstVerified;
    client.panVerified = data.panVerified;
    client.authorizedPerson =
      data.authorizedPerson !== null && data.authorizedPerson !== undefined
        ? data.authorizedPerson.trim()
        : data.authorizedPerson;
    client.designation =
      data.designation !== null && data.designation !== undefined
        ? data.designation.trim()
        : data.designation;
    client.dob = data.dob;
    client.buildingName = data.buildingName;
    client.street = data.street;
    client.city = data.city;
    client.state = data.state;
    client.pincode = data.pincode;
    client.labels = data.labels;
    client.localDirectoryPath = data.localDirectoryPath;
    client.notes = data.notes;
    client.status = data.status;
    client.image = data.image;
    client.clientPortalAccess = data.clientPortalAccess;
    client.address = data.address;
    client.issameaddress = data.issameaddress;
    client.countryCode = data.countryCode;
    client.alternateCountryCode = data?.alternateCountryCode;
    // client.incomeTaxAudit = data.incomeTaxAudit;
    // client.gstAnnualForm = data.gstAnnualForm;
    client['userId'] = user.id;

    if (
      clientDataBaseState?.status !== UserStatus.INACTIVE &&
      data.status === UserStatus.INACTIVE
    ) {
      client.inactiveAt = new Date();
    }

    if (data.status === UserStatus.INACTIVE) {
      await createQueryBuilder(Task, 'task')
        .leftJoinAndSelect('task.client', 'client')
        .where('client.id = :id', { id: client.id })
        .andWhere('recurringStatus = :status', { status: TaskRecurringStatus.PENDING })
        .update()
        .set({
          recurringStatus: TaskRecurringStatus.TERMINATED,
          status: TaskStatusEnum.TERMINATED,
          restore: TaskStatusEnum.TODO,
          statusUpdatedAt: moment().format('YYYY-MM-DD HH:mm:ss.SSSSSS')
        })
        .execute();
    }

    if (oldClientPortalAcces !== client.clientPortalAccess) {
      if (client.clientPortalAccess) {
        if (!client.user && client.user === null) {
          let portalPassword = null;
          const passwordQuery = await createQueryBuilder(User, 'user')
            .leftJoinAndSelect('user.organization', 'organization')
            .where('user.type = :type', { type: UserType.CLIENT })
            .andWhere('organization.id = :id', { id: user.organization.id })
            .andWhere('user.email = :email', { email: client.email })
            .getMany();

          for (let i of passwordQuery) {
            if (i.password) {
              portalPassword = i.password;
            }
          }

          let newUser = new User();
          newUser.fullName = data.displayName.trim();
          newUser.email = data.email;
          newUser.password = portalPassword;
          newUser.mobileNumber = data.mobileNumber;
          newUser.organization = user.organization;
          newUser.type = UserType.CLIENT;
          client.user = newUser;
        } else if (!client?.user?.password) {
          let portalPassword = null;

          const passwordQuery = await createQueryBuilder(User, 'user')
            .leftJoinAndSelect('user.organization', 'organization')
            .where('user.type = :type', { type: UserType.CLIENT })
            .andWhere('organization.id = :id', { id: user.organization.id })
            .andWhere('user.email = :email', { email: client.email })
            .getMany();

          for (let i of passwordQuery) {
            if (i.password) {
              portalPassword = i.password;
            }
          }

          const oldUser = await createQueryBuilder(User, 'user')
            .where('id = :id', { id: client?.user?.id })
            .getOne();

          oldUser.password = portalPassword;
          oldUser.save();
        }
        const getClientDefaultPermissions = await createQueryBuilder(
          ClientPermission,
          'clientPermission',
        )
          .where('clientPermission.defaultOne = :default', { default: true })
          .getMany();

        client.permissions = getClientDefaultPermissions;
      }
    }
    await client.save();

    if (oldClientPortalAcces !== client.clientPortalAccess) {
      let activity = new Activity();
      activity.action = Event_Actions.CLIENT_PORTAL_ACCESS;
      activity.actorId = user.id;
      activity.type = ActivityType.CLIENT;
      activity.typeId = client.id;
      activity.remarks = `Client Portal Access ${client.clientPortalAccess ? 'Enabled' : 'Disabled'
        } by ${user.fullName}`;
      await activity.save();
      if (client.clientPortalAccess) {
        this.eventEmitter.emit(Event_Actions.CLIENT_PORTAL_ACCESS_UPDATED, {
          userId,
          user: user,
          data: client,
        });
      }
    } else if (oldClientStatus !== client.status) {
      let activity = new Activity();
      activity.action = Event_Actions.CLIENT_STATUS_CHANGE;
      activity.actorId = user.id;
      activity.type = ActivityType.CLIENT;
      activity.typeId = client.id;
      activity.remarks = `Client Status Changed from ${oldClientStatus === UserStatus.ACTIVE ? 'Active' : 'Inactive'
        } to ${client.status === UserStatus.ACTIVE ? 'Active' : 'Inactive'} by ${user.fullName}`;
      await activity.save();
    } else {
      this.eventEmitter.emit(Event_Actions.CLIENT_UPDATED, {
        userId,
        data: client,
      });
    }

    if (oldClientDisplayName !== client.displayName) {
      if (client?.organization?.storageSystem === 'MICROSOFT') {
        const existingFolder = await Storage.findOne({
          where: {
            name: oldClientDisplayName,
            show: false,
            organization: client.organization,
          },
        });
        if (existingFolder) {
          const updateResponse = await this.oneDriveService.updateItemName(
            userId,
            existingFolder?.fileId,
            client.displayName,
          );
          if (updateResponse) {
            existingFolder.name = client.displayName;
            await existingFolder.save();
          }
        }
      }
    }

    return client;
  }

  async addDomainRecord(data: any) {
    const API_KEY = 'eo1w7MAwHfV1_3BHLmkXCbF5cM7TNb7XbDf';
    const API_SECRET = 'GUoZ5T3c5GdmXANBQYcUxf';
    const DOMAIN = 'vider.in';

    const payload = {
      type: 'A',
      name: data.name,
      data: '35.154.183.11'
    }

    try {
      const response = await axios.put(
        `https://api.godaddy.com/v1/domains/${DOMAIN}/records/${payload.type}/${payload.name}`,
        [payload],
        {
          headers: {
            'Authorization': `sso-key ${API_KEY}:${API_SECRET}`,
            'Content-Type': 'application/json'
          }
        }
      );

      return response.data;
    } catch (error: any) {
      console.error('Error updating record:', error.response?.data || error.message);
      throw new InternalServerErrorException(error);
    }
  }


  async findDeleted(userId: number, query: FindQueryDto) {
    const { limit, offset } = query;
    const user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
    // const clients = await Client.findAndCount({
    //   where: {
    //     organization: { id: user.organization.id },
    //     status: UserStatus.DELETED,
    //   },
    //   take: query.limit,
    //   skip: query.offset,
    // });
    // return clients;
    let clients = createQueryBuilder(Client, 'client')
      .leftJoinAndSelect('client.organization', 'organization')
      .leftJoinAndSelect('client.clientManagers', 'clientManagers')
      .where('organization.id = :orgId', { orgId: user.organization.id })
      .andWhere('client.status = :status', { status: UserStatus.DELETED });
    const sort = (typeof query?.sort === "string") ? JSON.parse(query.sort) : query?.sort;
    if (sort?.column) {
      const columnMap: Record<string, string> = {
        clientId: 'client.clientId',
        displayName: 'client.displayName',
      };
      const column = columnMap[sort.column] || sort.column;
      clients.orderBy(column, sort.direction.toUpperCase());
    } else {
      clients.orderBy('client.createdAt', 'DESC');
    };
    if (query.search) {
      clients.andWhere(
        new Brackets((qb) => {
          qb.where('client.displayName LIKE :displayName', {
            displayName: `%${query.search}%`,
          });
          qb.orWhere('client.email LIKE :email', {
            email: `%${query.search}%`,
          });
          qb.orWhere('client.clientId LIKE :clientId', {
            clientId: `%${query.search}%`,
          });
          qb.orWhere('client.gstNumber LIKE :gstNumber', {
            gstNumber: `%${query.search}%`,
          });
          qb.orWhere('client.mobileNumber LIKE :mobileNumber', {
            mobileNumber: `%${query.search}%`,
          });
          qb.orWhere('client.panNumber LIKE :panNumber', {
            panNumber: `%${query.search}%`,
          });
        }),
      );
    }

    if (offset >= 0) {
      clients.skip(offset);
    }

    if (limit) {
      clients.take(limit);
    }

    let result = await clients.getManyAndCount();
    return result;
  }

  async restoreClient(id: number, userId: number, body: any) {
    const errorList = [];
    const successList = [];
    const user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
    const client = await Client.findOne({ where: { id } });
    client.status = UserStatus.ACTIVE;
    client['userId'] = user.id;
    if (body?.clientManagers) {
      client['clientManagers'] = body?.clientManagers;
    }
    await client.save();
    let activity = new Activity();
    activity.action = Event_Actions.CLIENT_RESTORED;
    activity.actorId = user.id;
    activity.type = ActivityType.CLIENT;
    activity.typeId = client.id;
    activity.remarks = `Client Restored by ${user.fullName}`;
    await activity.save();

    // atom pro status change
    if (body?.restoreAtomProClients) {
      //check income tax atom pro
      const checkIncomeTax = await AutClientCredentials.findOne({ where: { clientId: id } });
      if (checkIncomeTax) {
        const atomProConfig = await checkAtomProConfigIncomeTax(user?.organization?.id);
        if (atomProConfig === true) {
          checkIncomeTax.status = IncomeTaxStatus.ENABLE;
          await checkIncomeTax.save();
          successList.push('Client Added in Atom Pro Income Tax');
        } else {
          errorList.push(atomProConfig);
        }
      }

      // check gstr credentials and enable
      const checkGstr = await GstrCredentials.findOne({ where: { clientId: id } });
      if (checkGstr) {
        const atomProConfig = await checkAtomProConfigGstr(user?.organization?.id);
        if (atomProConfig === true) {
          checkGstr.status = GstrStatus.ENABLE;
          await checkGstr.save();
          successList.push('Client Added in Atom Pro Gstr');
        } else {
          errorList.push(atomProConfig);
        }
      }
      //checks for TAN credentilas
      const checkIncomeTaxTan = await TanClientCredentials.findOne({ where: { clientId: id } });
      if (checkIncomeTaxTan) {
        const atomProTanConfig = await checkAtomProConfigIncomeTaxTan(user?.organization?.id);
        if (atomProTanConfig === true) {
          checkIncomeTax.status = IncomeTaxStatus.ENABLE;
          await checkIncomeTax.save();
          successList.push('Client Added in Atom Pro Income Tax TAN');
        } else {
          errorList.push(atomProTanConfig);
        }
      }
    }

    return { client, errorList, successList };
  }

  async restoreBulkClient(userId: number, body: any) {
    const errorList = [];
    const successList = [];
    const user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
    let clientIds = body?.ids;
    for (let i of body?.ids) {
      const client = await Client.findOne({ where: { id: i } });
      client.status = UserStatus.ACTIVE;
      client['userId'] = user.id;
      if (body?.activeInactiveDataa?.length) {
        client['clientManagers'] = body?.activeInactiveDataa;
      }
      await client.save();
      let activity = new Activity();
      activity.action = Event_Actions.CLIENT_RESTORED;
      activity.actorId = user.id;
      activity.type = ActivityType.CLIENT;
      activity.typeId = client.id;
      activity.remarks = `Client Restored by ${user.fullName}`;
      await activity.save();

      // atom pro status change
      if (body?.restoreAtomProClients) {
        //check income tax atom pro
        const checkIncomeTax = await AutClientCredentials.findOne({ where: { clientId: i } });
        if (checkIncomeTax) {
          const atomProConfig = await checkAtomProConfigIncomeTax(user?.organization?.id);
          if (atomProConfig === true) {
            checkIncomeTax.status = IncomeTaxStatus.ENABLE;
            await checkIncomeTax.save();
            successList.push('Client Added in Atom Pro Income Tax');
          } else {
            errorList.push(atomProConfig);
          }
        }

        // check gstr credentials and enable
        const checkGstr = await GstrCredentials.findOne({ where: { clientId: i } });
        if (checkGstr) {
          const atomProConfig = await checkAtomProConfigGstr(user?.organization?.id);
          if (atomProConfig === true) {
            checkGstr.status = GstrStatus.ENABLE;
            await checkGstr.save();
            successList.push('Client Added in Atom Pro Gstr');
          } else {
            errorList.push(atomProConfig);
          }
        }
        //checks for TAN credentilas
        const checkIncomeTaxTan = await TanClientCredentials.findOne({ where: { clientId: i } });
        if (checkIncomeTaxTan) {
          const atomProTanConfig = await checkAtomProConfigIncomeTaxTan(user?.organization?.id);
          if (atomProTanConfig === true) {
            checkIncomeTax.status = IncomeTaxStatus.ENABLE;
            await checkIncomeTax.save();
            successList.push('Client Added in Atom Pro Income Tax TAN');
          } else {
            errorList.push(atomProTanConfig);
          }
        }
      }
    }

    return { clientIds, errorList, successList };
  }

  async bulkDelete(ids: number[], userId: number) {
    const user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
    let clientIds = [];

    const pendingTasks = await createQueryBuilder(Task, 'task')
      .leftJoinAndSelect('task.client', 'client')
      .where('task.status IN (:...statuses)', {
        statuses: [
          TaskStatusEnum.TODO,
          TaskStatusEnum.IN_PROGRESS,
          TaskStatusEnum.ON_HOLD,
          TaskStatusEnum.UNDER_REVIEW,
        ],
      })
      .andWhere('client.id IN (:...ids)', { ids })
      .getMany();

    pendingTasks.forEach(item => {
      if (!clientIds.includes(item?.client?.displayName)) {
        clientIds.push(item?.client?.displayName);
      }
    })

    if (clientIds.length > 0) {
      return { errorsList: clientIds };
    }

    await createQueryBuilder(Task, 'task')
      .leftJoin('task.client', 'client')
      .where('client.id IN (:...ids)', { ids: ids })
      .andWhere('recurringStatus = :status', { status: TaskRecurringStatus.PENDING })
      .update(Task)
      .set({
        recurringStatus: TaskRecurringStatus.TERMINATED,
        status: TaskStatusEnum.TERMINATED,
        restore: TaskStatusEnum.TODO,
        statusUpdatedAt: moment().format('YYYY-MM-DD HH:mm:ss.SSSSSS')
      })
      .execute();
    await createQueryBuilder(Client, 'client')
      .whereInIds(ids)
      .update({ status: UserStatus.DELETED })
      .execute();
    for (let i of ids) {
      let activity = new Activity();
      activity.action = Event_Actions.CLIENT_DELETED;
      activity.actorId = user.id;
      activity.type = ActivityType.CLIENT;
      activity.typeId = i;
      activity.remarks = `Client Profile Deleted by ${user.fullName}`;
      await activity.save();

      if (i) {
        const checkIncomeTax = await AutClientCredentials.findOne({ where: { clientId: i } });
        if (checkIncomeTax) {
          checkIncomeTax.status = IncomeTaxStatus.DISABLE;
          await checkIncomeTax.save();
        }
        const checkGstr = await GstrCredentials.findOne({ where: { clientId: i } });
        if (checkGstr) {
          checkGstr.status = GstrStatus.DISABLE;
          await checkGstr.save();
        }
      }
    }

    return { success: true };
  }

  async bulkUpdate(data: BulkUpdateDto, userId) {
    const user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
    if (data.status === 'INACTIVE') {
      await createQueryBuilder(Task, 'task')
        .leftJoinAndSelect('task.client', 'client')
        .where('client.id IN (:...ids)', { ids: data.ids })
        .andWhere('recurringStatus = :status', { status: TaskRecurringStatus.PENDING })
        .update(Task)
        .set({
          recurringStatus: TaskRecurringStatus.TERMINATED,
          status: TaskStatusEnum.TERMINATED,
          restore: TaskStatusEnum.TODO,
          statusUpdatedAt: moment().format('YYYY-MM-DD HH:mm:ss.SSSSSS')
        })
        .execute();
    }

    await createQueryBuilder(Client, 'client')
      .whereInIds(data.ids)
      .update({ status: data.status, inactiveAt: new Date() })
      .execute();

    for (let i of data.ids) {
      let activity = new Activity();
      activity.action = Event_Actions.CLIENT_STATUS_UPDATED;
      activity.actorId = user.id;
      activity.type = ActivityType.CLIENT;
      activity.typeId = i;
      activity.remarks = `Client Status Changed to ${getTitle(data.status.toLowerCase())} by ${user.fullName
        }`;
      await activity.save();
    }

    return { success: true };
  }

  getGstAddress = (address: any) => {
    let result = '';
    if (address?.bno) {
      result += `${address?.bno}, `;
    }
    if (address?.flno) {
      result += `${address?.flno}, `;
    }
    if (address?.bnm) {
      result += `${address.bnm}, `;
    }
    if (address?.st) {
      result += `${address.st}, `;
    }
    if (address?.locality) {
      result += `${address.locality}, `;
    }
    if (address?.loc) {
      result += `${address.loc}, `;
    }
    if (address?.dst) {
      result += `${address.dst}, `;
    }
    if (address?.stcd) {
      result += `${address.stcd} - `;
    }
    if (address?.pncd) {
      result += address.pncd;
    }
    return result;
  };

  async importClients(userId: number, file: Express.Multer.File) {
    const user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
    let users = await User.find({
      where: {
        organization: { id: user.organization.id },
        status: UserStatus.ACTIVE,
        type: UserType.ORGANIZATION,
      },
      relations: ['organization'],
    });
    const workbook = xlsx.read(file.buffer, { cellDates: true });
    const sheet = workbook.Sheets[workbook.SheetNames[0]];
    const dataFromExcel = xlsx.utils.sheet_to_json(sheet);


    if (dataFromExcel.length > 0) {
      this.importClientsPartTwo(userId, dataFromExcel, user, users)
      return { success: true, message: 'Import started. Please check the logs for details.' };
    } else {
      throw new BadRequestException(`Atleast one row required as per sample sheet`);
    }
  }

  async importClientsPartTwo(userId, dataFromExcel, user, users) {

    const ImportClientRow = await createQueryBuilder(ImportData, 'importClientRow')
      .where('importClientRow.organization = :organization', { organization: user.organization.id })
      .getOne();

    if (!ImportClientRow) {
      const importClient = new ImportData();
      importClient.organization = user.organization;
      importClient.status = ImportStatus.PENDING;
      importClient.responseData = null;
      await importClient.save();
    } else {
      ImportClientRow.organization = user.organization;
      ImportClientRow.status = ImportStatus.PENDING;
      ImportClientRow.responseData = null;
      await ImportClientRow.save();
    }

    const result: Client[] = [];
    let errorsArray = [];
    let gstrRegisters = [];
    let addedArray = 0;
    let notAddedClient = 0;
    let validateError = 0;
    let totalRows = 0;
    let alreadyAddedClients = 0;
    let incomeTaxCredentials = 0;
    let addedIncomeTaxClients = 0;
    let notaddedIncomeTaxClients = 0;
    let GstrCredential = 0;
    let addedGstrClients = 0;
    let notaddedGstrClients = 0;
    let TanTraceCredentials = 0;
    let addedTanClients = 0;
    let notaddedTanClients = 0;
    let rows = '';
    const gstinPattern =
      /^[0-9]{2}[a-zA-Z]{4}[a-zA-Z0-9]{1}[0-9]{4}[a-zA-Z]{1}[1-9A-Za-z]{1}[D,C,Z]{1}[0-9a-zA-Z]{1}$/;
    const PAN_REGEX = /^[A-Z]{3}[A,B,C,F,G,H,L,J,P,T]{1}[A-Z]{1}[0-9]{4}[A-Z]{1}$/;
    const newClientsDisplayName = [];
    const newClientGst = [];
    const newClientPan = [];


    let filteredData = dataFromExcel.filter((item) => {
      return (
        item['S.No'] ||
        item['Email Id *'] ||
        item['Mobile Number *'] ||
        item['Display Name *'] ||
        item['Client Category *'] ||
        item['Sub Category *'] ||
        item['Client Manager *']
      );
    });

    for (const [index, item] of filteredData.entries()) {
      const data: any = item;
      validateError = 0;

      if (
        data.hasOwnProperty('Client Category *') &&
        data['Client Category *'] != '' &&
        data.hasOwnProperty('Display Name *') &&
        data['Display Name *'] != '' &&
        data.hasOwnProperty('Mobile Number *') &&
        data['Mobile Number *'] != '' &&
        data.hasOwnProperty('Email Id *') &&
        data['Email Id *'] != '' &&
        data.hasOwnProperty('Country Code *') &&
        data['Country Code *'] != '' &&
        data.hasOwnProperty('Client Manager *') &&
        data['Client Manager *'] != ''
      ) {
        let date = null;

        if (
          (' ' + data['Date of Birth(DD-MM-YYYY)'])?.trim() !== 'undefined' &&
          (' ' + data['Date of Birth(DD-MM-YYYY)'])?.trim() !== undefined
        ) {
          const dateStr = data['Date of Birth(DD-MM-YYYY)'];
          if (
            typeof dateStr === 'string' &&
            dateStr !== null &&
            dateStr !== undefined &&
            dateStr !== 'undefined'
          ) {
            const trimmedDateString = dateStr?.trim();
            date = moment(trimmedDateString).subtract(1, 'day').format('YYYY-MM-DD');
          } else if (
            typeof dateStr === 'object' &&
            dateStr !== null &&
            dateStr !== undefined &&
            dateStr !== 'undefined'
          ) {
            const dateObj = moment.utc(dateStr);
            const dateObjAdd = dateObj?.add(1, 'days');
            date = moment(dateObjAdd).subtract(1, 'day').format('YYYY-MM-DD');
          }
        }
        totalRows += 1;
        const client = new Client();
        client.createdBy = user;
        client.organization = user?.organization;
        client.category =
          CategoryEnum[
          data['Client Category *']?.toUpperCase().replace(/\s+/g, '_') as CategoryEnum
          ];
        client.subCategory =
          SubCategoryEnum[
          data['Sub Category *']?.toUpperCase().replace(/\s+/g, '_') as SubCategoryEnum
          ];

        if (!isValidString(data['Display Name *'])) {
          const errorDuplicate = `row ${index + 2
            } has Invalid Characters(<, >, :, ", /, \\, |, ?, *, or .) in Display Name .`;
          errorsArray = [...errorsArray, errorDuplicate];
          notAddedClient += 1;
          continue;
        }
        client.displayName = (' ' + data['Display Name *'])?.trim();
        client.clientNumber =
          (' ' + data['Client Number'])?.trim() !== 'undefined'
            ? (' ' + data['Client Number'])?.trim()
            : null;
        client.mobileNumber = data['Mobile Number *'];
        client.email = (' ' + data['Email Id *'])?.trim();

        const alternateMobileNumber: any =
          (' ' + data['Alternate Mobile Number'])?.trim() !== 'undefined'
            ? (' ' + data['Alternate Mobile Number'])?.trim()
            : null;

        const alternateCountryCode: any =
          (' ' + data['A - Country Code'])?.trim() !== 'undefined'
            ? (' ' + data['A - Country Code'])?.trim()
            : null;

        if (alternateMobileNumber && alternateCountryCode) {
          const validateANumber = mobileWithCountry(alternateMobileNumber, alternateCountryCode);
          if (!validateANumber) {
            const countryCode = countries.find((c) => c.label === alternateCountryCode);
            client.alternateMobileNumber = alternateMobileNumber;
            client.alternateCountryCode = countryCode?.code;
          }
        }

        client.dob =
          (' ' + data['Date of Birth(DD-MM-YYYY)'])?.trim() !== 'undefined' ? date : null;
        client.authorizedPerson =
          (' ' + data['Authorized Person'])?.trim() !== 'undefined'
            ? (' ' + data['Authorized Person'])?.trim()
            : null;
        client.designation =
          (' ' + data['Designation'])?.trim() !== 'undefined'
            ? (' ' + data['Designation'])?.trim()
            : null;

        if ((' ' + data['Client Manager *'])?.trim() === "ALL") {
          client.clientManagers = users;
        } else if (data['Client Manager *'] && (' ' + data['Client Manager *'])?.trim() !== "" && (' ' + data['Client Manager *'])?.trim() !== "ALL") {
          const clientManagers = data['Client Manager *'].split(",")?.map(item => item?.trim());
          const filteredUsers = users.filter(item => clientManagers.includes(item.fullName))
          if (filteredUsers.length !== clientManagers.length) {
            const errorDuplicate = `row ${index + 2
              } has Wrong Client Managers Names, Please Check It !.`;
            errorsArray = [...errorsArray, errorDuplicate];
            notAddedClient += 1;
            continue;
          }
          client.clientManagers = filteredUsers;
        } else {
          const errorDuplicate = `row ${index + 2
            } doesn't Have Client Managers, it is empty !.`;
          errorsArray = [...errorsArray, errorDuplicate];
          notAddedClient += 1;
          continue;
        }
        const panNumber = (' ' + data['PAN'])?.trim();
        const gstNumber = (' ' + data['GSTIN'])?.trim();
        const tanNumber = (' ' + data['TAN'])?.trim();
        const displayName = (' ' + data['Display Name *'])?.trim();
        const validateNumber = mobileWithCountry(data['Mobile Number *'], data['Country Code *']);
        if (validateNumber) {
          const errorDuplicate = `row ${index + 2
            } have mobile number does not match the selected country.`;
          errorsArray = [...errorsArray, errorDuplicate];
          notAddedClient += 1;
          continue;
        }

        const countryCode = countries.find((c) => c.label === data['Country Code *']);
        client.countryCode = countryCode.code;
        function getThree(duplicate1: string, duplicate2: string, duplicate3: string) {
          const errorDuplicate = `row ${index + 2
            } have ${duplicate1}  ${duplicate2}  ${duplicate3} have duplicate data`;
          errorsArray = [...errorsArray, errorDuplicate];
          notAddedClient += 1;
        }
        function getTwo(duplicate1: string, duplicate2: string) {
          const errorDuplicate = `row ${index + 2
            } have ${duplicate1}  ${duplicate2} have duplicate data`;
          errorsArray = [...errorsArray, errorDuplicate];
          notAddedClient += 1;
        }
        function getOne(duplicate1: string) {
          const errorDuplicate = `row ${index + 2} have ${duplicate1} have duplicate data`;
          errorsArray = [...errorsArray, errorDuplicate];
          notAddedClient += 1;
        }
        if (newClientsDisplayName.includes(displayName)) {
          if (newClientGst.includes(gstNumber)) {
            getTwo(displayName, gstNumber);
            continue;
          } else {
            getOne(displayName);
            continue;
          }
        } else if (newClientGst.includes(gstNumber)) {
          getOne(gstNumber);
          continue;
        }
        if (displayName !== 'undefined') {
          newClientsDisplayName.push(displayName);
        }
        if (gstNumber !== 'undefined') {
          newClientGst.push(gstNumber);
        }
        if (panNumber !== 'undefined') {
          newClientPan.push(panNumber);
        }

        let existingUser = await createQueryBuilder(Client, 'client')
          .leftJoin('client.organization', 'organization')
          .where('organization.id = :organization', { organization: user?.organization?.id })
          .andWhere('(client.displayName = :displayName OR client.gstNumber = :gstNumber)', {
            displayName: displayName,
            gstNumber: gstNumber,
          })
          .getOne();

        let duplicated = result.findIndex((items) => {
          if (displayName !== 'undefined') {
            if (gstNumber !== 'undefined') {
              return items?.displayName === displayName || items?.gstNumber === gstNumber;
            } else {
              return items?.displayName === displayName;
            }
          } else {
            return false;
          }
        });

        if (duplicated > -1) {
            // throw new BadRequestException(
            //   `row ${duplicated + 1} and row ${index + 1} have duplicate data`,
            // );
          const errorDuplicate = `row ${duplicated + 2} and row ${index + 2} have duplicate data`;
          errorsArray = [...errorsArray, errorDuplicate];
          notAddedClient += 1;
          continue;
        }

        await validate(client).then((errors) => {
          if (errors.length > 0) {
            const errorDuplicate = `Invalid ${_.startCase(errors[0]?.property)} in row ${index + 2
              }`;
            errorsArray = [...errorsArray, errorDuplicate];
            notAddedClient += 1;
            validateError = 1;
          }
        });
        if (validateError) {
          continue;
        }
        if (existingUser) {
          alreadyAddedClients += 1;
          const errorName = `row ${index + 2} have duplicate data i.e. Display Name, GST, PAN`;
          errorsArray = [...errorsArray, errorName];
          rows += `${index + 2},`;
        }

        if (
          tanNumber &&
          tanNumber !== 'undefined' &&
          tanNumber !== '' &&
          !TAN_REGEX.test(tanNumber)
        ) {
          const errorDuplicate = `row ${index + 2} has Invalid TAN`;
          errorsArray = [...errorsArray, errorDuplicate];
          notAddedClient += 1;
          continue;
        }

        if (
          panNumber &&
          panNumber !== 'undefined' &&
          panNumber !== '' &&
          !PAN_REGEX.test(panNumber)
        ) {
          const errorDuplicate = `row ${index + 2} has Invalid PAN`;
          errorsArray = [...errorsArray, errorDuplicate];
          notAddedClient += 1;
          continue;
        }

        if (
          gstNumber &&
          gstNumber !== 'undefined' &&
          gstNumber !== '' &&
          !gstinPattern.test(gstNumber)
        ) {
          const errorDuplicate = `row ${index + 2} has Invalid GST`;
          errorsArray = [...errorsArray, errorDuplicate];
          notAddedClient += 1;
          continue;
        }
        // if (gstrUsername && gstrUsername !== "undefined" && gstrUsername !== "" && !GSTR_USERNAME_REGEX.test(gstrUsername)) {
        //   const errorDuplicate = `row ${index + 2
        //     } has Invalid GST ID`;
        //   errorsArray = [...errorsArray, errorDuplicate];
        //   notAddedClient += 1;
        //   continue;
        // }

        // if (gstrPassword && gstrPassword !== "undefined" && gstrPassword !== "" && !GSTR_PASSWORD_REGEX.test(gstrPassword)) {
        //   const errorDuplicate = `row ${index + 2
        //     } has Invalid GST Password`;
        //   errorsArray = [...errorsArray, errorDuplicate];
        //   notAddedClient += 1;
        //   continue;
        // }
        if (!existingUser) {
          const a = gstNumber?.slice(2, gstNumber?.length - 3);
          if (gstNumber !== 'undefined') {
            if (panNumber !== 'undefined') {
              if (!(a === panNumber)) {
                const errorName = `row ${index + 2
                  } - Gst ${gstNumber} and Pan ${panNumber} is not Matched`;
                errorsArray = [...errorsArray, errorName];
                notAddedClient += 1;
                continue;
              }
            }
          }
          client.panVerified = false;
          client.panNumber =
            (' ' + data['PAN'])?.trim() !== 'undefined' ? (' ' + data['PAN'])?.trim() : null;
          client.tanNumber =
            (' ' + data['TAN'])?.trim() !== 'undefined' ? (' ' + data['TAN'])?.trim() : null;
          if (gstNumber !== 'undefined') {
            if (gstNumber.length === 15) {
              const gstData = async (gstNumber) => {
                try {
                  const headers = {
                    Authorization: process.env.FYN_AUTH,
                  };
                  const url = `${process.env.FYN_URL}${gstNumber}`;
                  const response = await axios.get(url, { headers });
                  const res = { data: response.data };
                  return res;
                } catch (error) {
                  console.error(error);
                }
              };
              const gstDetailsData = await gstData(gstNumber);

              if (gstDetailsData?.data?.sts) {
                const address: any = {
                  communicationfulladdress: this.getGstAddress(gstDetailsData?.data?.pradr?.addr),
                };
                address['billingfulladdress'] = address?.communicationfulladdress;
                address['shippingfulladdress'] = address?.communicationfulladdress;
                client.issameaddress = true;
                client.gstVerified = true;
                client.gstNumber = gstDetailsData?.data?.gstin;
                client.legalName = gstDetailsData?.data?.lgnm;
                client.address = address;
                client.gstRegistrationDate = gstDetailsData?.data?.rgdt || null;
                client.tradeName = gstDetailsData?.data?.tradeNam;
                client.constitutionOfBusiness = gstDetailsData?.data?.ctb;
                client.placeOfSupply = gstDetailsData?.data?.pradr?.addr?.stcd;
                client.buildingName = gstDetailsData?.data?.pradr?.addr?.bnm;
                client.street = gstDetailsData?.data?.pradr?.addr?.st;
                client.city = gstDetailsData?.data?.pradr?.addr?.dst;
                client.state = gstDetailsData?.data?.pradr?.addr?.stcd;
                client.pincode = gstDetailsData?.data?.pradr?.addr?.pncd;
                const gstCharAt13 = client.gstNumber.charAt(13);
                const extractedNumber = client.gstNumber.substring(
                  2,
                  client.gstNumber.length - 3,
                );
                if (client.gstNumber === 'D') {
                  const pattren = TAN_REGEX.test(extractedNumber);
                  if (pattren) {
                    client.tanNumber = client.tanNumber;
                  } else {
                    client.panNumber = client.panNumber;
                  }

                  client.registrationType = RegistrationType.TAX_DEDUCTOR;
                } else if (gstCharAt13 === 'Z' || gstCharAt13 === 'C') {
                  client.panNumber = client.panNumber;
                  client.registrationType =
                    gstCharAt13 === 'Z'
                      ? RegistrationType.REGULAR_TAXPAYER
                      : RegistrationType.TAX_COLLECTOR;
                }
              } else {
                client.gstNumber = gstNumber;
              }
            } else {
              const errorName = `row ${index + 2} - GST ${gstNumber} is invalid Pattern`;
              errorsArray = [...errorsArray, errorName];
              notAddedClient += 1;
              continue;
            }
          } else {
            const address: any = {
              communicationfulladdress: data['Address'],
            };
            if (data['Address']) {
              client.issameaddress = true;
              address['billingfulladdress'] = address?.communicationfulladdress;
              address['shippingfulladdress'] = address?.communicationfulladdress;
              client.address = address;
            }
            client.constitutionOfBusiness =
              (' ' + data['Constitution of Business'])?.trim() !== 'undefined'
                ? (' ' + data['Constitution of Business'])?.trim()
                : null;
            client.placeOfSupply =
              (' ' + data['State Jurisdiction / Place of Supply'])?.trim() !== 'undefined'
                ? (' ' + data['State Jurisdiction / Place of Supply'])?.trim()
                : null;
            client.buildingName =
              (' ' + data['Building No. / Flat No.'])?.trim() !== 'undefined'
                ? (' ' + data['Building No. / Flat No.'])?.trim()
                : null;
            client.street =
              (' ' + data['Road / Street'])?.trim() !== 'undefined'
                ? (' ' + data['Road / Street'])?.trim()
                : null;
            client.city =
              (' ' + data['City / Town / Village'])?.trim() !== 'undefined'
                ? (' ' + data['City / Town / Village'])?.trim()
                : null;
            client.state =
              (' ' + data['State / Union Territory'])?.trim() !== 'undefined'
                ? (' ' + data['State / Union Territory'])?.trim()
                : null;
            client.pincode =
              (' ' + data['Pincode'])?.trim() !== 'undefined'
                ? (' ' + data['Pincode'])?.trim()
                : null;
          }
          client.firstName =
            (' ' + data['First Name'])?.trim() !== 'undefined'
              ? (' ' + data['First Name'])?.trim()
              : null;
          client.middleName =
            (' ' + data['Middle Name'])?.trim() !== 'undefined'
              ? (' ' + data['Middle Name'])?.trim()
              : null;
          client.lastName =
            (' ' + data['Last Name'])?.trim() !== 'undefined'
              ? (' ' + data['Last Name'])?.trim()
              : null;
          client.fullName =
            (' ' + data['Full Name'])?.trim() !== 'undefined'
              ? (' ' + data['Full Name'])?.trim()
              : null;
          let newUser = new User();
          newUser.fullName = (' ' + data['Display Name *'])?.trim();
          newUser.email = (' ' + data['Email Id *'])?.trim();
          newUser.password = null;
          newUser.organization = user.organization;
          newUser.mobileNumber = data['Mobile Number *'];
          newUser.type = UserType.CLIENT;
          (newUser['userId'] = userId), (client.user = newUser);
          client['Income Tax PAN'] = data['Income Tax PAN'];
          client['Income Tax PAN Password'] = data['Income Tax PAN Password'];
          client['Income Tax (PAN) - ATOM Pro'] = data['Income Tax (PAN) - ATOM Pro'];
          client['userId'] = user.id;
          client['GST Password'] = data['GST Password'];
          client['GST Username'] = data['GST Username'];
          client['GST - ATOM Pro'] = data['GST - ATOM Pro'];
          client['Income Tax TAN'] = data['Income Tax TAN'];
          client['Income Tax TAN Password'] = data['Income Tax TAN Password'];
          client['Income Tax (TAN) - ATOM Pro'] = data['Income Tax (TAN) - ATOM Pro'];
          client['Traces User Id'] = data['Traces User Id'];
          client['Traces Password'] = data['Traces Password'];
          client['Traces TAN'] = data['Traces TAN'];
          client['rowNumber'] = index + 2;
          result.push({ ...client } as Client);
        }
      } else {
        const errorDuplicate = `Row ${index + 2
          } have Improper Client details, Mandatory Fields Missing.`;
        errorsArray = [...errorsArray, errorDuplicate];
        notAddedClient += 1;
        continue;
      }
    }
    if (result.length > 0) {
      try {
        for (const client of result) {
          const organizationPreferences: any = await OrganizationPreferences.findOne({
            where: { organization: user?.organization },
          });

          const organizationIncomeTaxLimit =
            organizationPreferences?.automationConfig?.incomeTaxLimit;
          const organizationGstrLimit = organizationPreferences?.automationConfig?.gstrLimit;

          const incomeTaxAccess = organizationPreferences?.automationConfig?.incomeTax === 'YES';
          const gstrAccess = organizationPreferences?.automationConfig?.gstr === 'YES';
          const tanAccess = organizationPreferences?.automationConfig?.tan === 'YES';
          const tanLimit = organizationPreferences?.automationConfig?.tanLimit;
          client.localDirectoryPath = [];
          addedArray += 1;
          await Client.save(client);
          console.log(client['rowNumber']);
          if (client.gstVerified) {
            let gstrRegister = new GstrRegister();
            gstrRegister.client = client;
            gstrRegister.registrationType = client.registrationType;
            gstrRegister.organization = user.organization;
            gstrRegisters.push(gstrRegister);
          }

          let createCredentials = async (userId, data) => {
            const client = await Client.findOne({ where: { id: data.client?.id } });
            let isLoginIDExists = await Password.findOne({ where: { client: client, loginId: data.loginId.trim() } });
            if (isLoginIDExists) {
              console.log("LoginId already Exists!!!");
            }
            const password = new Password();
            password.website = data.website.trim();
            password.websiteUrl = data.websiteUrl.trim();
            password.loginId = data.loginId.trim();
            password.password = data.password.trim();
            password.client = client;
            password['userId'] = userId;
            password.isExistingAtomPro = data.isExistingAtomPro;
            await password.save();
            return password;
          };

          let createTanAndTracesCredentials = async (userId: number, tanDetails: any, traceDetails: any) => {
            const client = await Client.findOne({ where: { id: tanDetails?.client?.id } });
            let tanPassword: Password, tracePassword: Password;

            await getManager().transaction(async (transactionalEntityManager) => {

              // Create TAN credentials within the transaction
              tanPassword = new Password();
              tanPassword.website = tanDetails.website.trim();
              tanPassword.websiteUrl = tanDetails.websiteUrl.trim();
              tanPassword.loginId = tanDetails.loginId;
              tanPassword.password = tanDetails.password;
              tanPassword.client = client;
              tanPassword['userId'] = userId;
              tanPassword.isExistingAtomPro = tanDetails.isExistingAtomPro;
              await transactionalEntityManager.save(tanPassword);


              // Create Traces credentials within the transaction
              tracePassword = new Password();
              tracePassword.website = traceDetails.website.trim();
              tracePassword.websiteUrl = traceDetails.websiteUrl.trim();
              tracePassword.loginId = traceDetails.loginId;
              tracePassword.password = traceDetails.password;
              tracePassword.tracesTan = tanDetails?.loginId;
              tracePassword.client = client;
              tracePassword['userId'] = userId;
              tracePassword.isExistingAtomPro = traceDetails.isExistingAtomPro;
              await transactionalEntityManager.save(tracePassword);
            });

            return { tanPassword, tracePassword };
          }
          if (client['Income Tax PAN Password'] && client['Income Tax PAN']) {
            const incomeTaxPan = client['Income Tax PAN'];
            const checkValidPan = checkPanNumber(incomeTaxPan);
            const details = {
              website: 'Income Tax | e-Filing (PAN)',
              websiteUrl: 'https://eportal.incometax.gov.in/iec/foservices/#/login',
              loginId: String(client['Income Tax PAN']),
              password: String(client['Income Tax PAN Password']),
              client: client,
              isExistingAtomPro: IsExistingAtomPro.NO,
            };
            if (checkValidPan) {
              const errorDuplicate = `Row ${client['rowNumber']} Atom Pro Income Tax Invalid PAN, Client Name: ${client.displayName}, PAN: ${incomeTaxPan}`;
              errorsArray = [...errorsArray, errorDuplicate];
              notaddedIncomeTaxClients += 1;
              await createCredentials(userId, details);
            } else {
              if (client['Income Tax (PAN) - ATOM Pro'] === 'Yes') {
                incomeTaxCredentials += 1;
                // insert into income client tax Table
                const autClientCredential = await AutClientCredentials.count({
                  where: {
                    organizationId: user?.organization?.id,
                    status: IncomeTaxStatus.ENABLE,
                  },
                });

                const existingClient = await AutClientCredentials.findOne({
                  where: {
                    clientId: client,
                  },
                });

                if (existingClient) {
                  const errorDuplicate = `Row ${client['rowNumber']} This client is already existing in Atom Pro Income Tax ClientName: ${client.displayName}, PAN: ${incomeTaxPan}`;
                  errorsArray = [...errorsArray, errorDuplicate];
                  notaddedIncomeTaxClients += 1;

                  await createCredentials(userId, details);
                } else {
                  const existingRecord = await AutClientCredentials.findOne({
                    where: { organizationId: user?.organization?.id, panNumber: details.loginId },
                  });

                  if (existingRecord) {
                    const errorDuplicate = `Row ${client['rowNumber']} This PAN is already existing in Atom Pro Income Tax, ClientName: ${client.displayName}, PAN: ${incomeTaxPan}`;
                    errorsArray = [...errorsArray, errorDuplicate];
                    notaddedIncomeTaxClients += 1;
                    await createCredentials(userId, details);
                  } else {
                    if (incomeTaxAccess) {
                      if (organizationIncomeTaxLimit > autClientCredential) {
                        details.isExistingAtomPro = IsExistingAtomPro.YES;
                        const passwordData: any = await createCredentials(userId, details);
                        const clientCredentials = new AutClientCredentials();
                        clientCredentials.panNumber = details.loginId;
                        clientCredentials.password = details.password;
                        clientCredentials.client = client;
                        clientCredentials.organizationId = user?.organization?.id;
                        clientCredentials.syncStatus = syncStatus.NOTSYNC;
                        clientCredentials.status = IncomeTaxStatus.ENABLE;
                        clientCredentials.passwordId = passwordData?.id;
                        await clientCredentials.save();
                        addedIncomeTaxClients += 1;
                      } else {
                        const errorDuplicate = `Row ${client['rowNumber']} Client Not Added in Atom Pro Income Tax due to Client Limit Reached, ClientName: ${client.displayName}, PAN: ${details.loginId}`;
                        errorsArray = [...errorsArray, errorDuplicate];
                        notaddedIncomeTaxClients += 1;
                        await createCredentials(userId, details);
                      }
                    } else {
                      const errorDuplicate = `Row ${client['rowNumber']} Subscribe Atom Pro Income Tax to access for this Client, Client Name: ${client.displayName}, PAN : ${details.loginId}`;
                      errorsArray = [...errorsArray, errorDuplicate];
                      notaddedIncomeTaxClients += 1;
                      await createCredentials(userId, details);
                    }
                  }
                }
              } else {
                await createCredentials(userId, details);
              }
            }
          }

          if (client['GST Password'] && client['GST Username']) {
            const gstrUsername = client['GST Username'];
            const gstrPassword = client['GST Password'];
            const checkValidGstrUserName = checkGstrUsername(gstrUsername);
            const checkValidGstrPassword = checkGstrPassword(gstrPassword);
            const details = {
              website: 'GST | e-Filing',
              websiteUrl: 'https://services.gst.gov.in/services/login',
              loginId: String(client['GST Username']),
              password: String(client['GST Password']),
              client: client,
              isExistingAtomPro: IsExistingAtomPro.NO,
            };
            if (checkValidGstrUserName) {
              const errorDuplicate = `Row ${client['rowNumber']} Atom Pro GST User Id Invalid, ClientName: ${client.displayName}, UserID: ${gstrUsername}`;
              errorsArray = [...errorsArray, errorDuplicate];
              notaddedGstrClients += 1;
              await createCredentials(userId, details);
            } else {
              if (checkValidGstrPassword) {
                const errorDuplicate = `Row ${client['rowNumber']} Atom Pro GST Password Invalid, ClientName: ${client.displayName}, UserID: ${gstrUsername}`;
                errorsArray = [...errorsArray, errorDuplicate];
                notaddedGstrClients += 1;
                await createCredentials(userId, details);
              } else {
                // client['GST - ATOM Pro'] = data['GST - ATOM Pro'];
                if (client['GST - ATOM Pro'] === 'Yes') {
                  GstrCredential += 1;
                  // insert into income client tax Table
                  const gstrCredentialsCount = await GstrCredentials.count({
                    where: { organizationId: user?.organization?.id, status: GstrStatus.ENABLE },
                  });

                  const existingClinet = await GstrCredentials.findOne({
                    where: {
                      client: client,
                    },
                  });

                  if (existingClinet) {
                    const errorDuplicate = `Row ${client['rowNumber']} This client is already existing in Atom Pro GST, ClientName: ${client.displayName}, UserID: ${gstrUsername}`;
                    errorsArray = [...errorsArray, errorDuplicate];
                    notaddedGstrClients += 1;

                    await createCredentials(userId, details);
                  } else {
                    const existingRecord = await GstrCredentials.findOne({
                      where: {
                        organizationId: user?.organization?.id,
                        userName: details.loginId,
                      },
                    });

                    if (existingRecord) {
                      const errorDuplicate = `Row ${client['rowNumber']} This User Id is already existing in Atom Pro GST, ClientName: ${client.displayName}, UserID: ${gstrUsername}`;
                      errorsArray = [...errorsArray, errorDuplicate];
                      notaddedGstrClients += 1;
                      await createCredentials(userId, details);
                    } else {
                      if (gstrAccess) {
                        if (organizationGstrLimit > gstrCredentialsCount) {
                          details.isExistingAtomPro = IsExistingAtomPro.YES;
                          const passwordData: any = await createCredentials(userId, details);
                          const gstrCredentials = new GstrCredentials();
                          gstrCredentials.userName = details.loginId;
                          gstrCredentials.password = details.password;
                          gstrCredentials.userId = user?.id;
                          gstrCredentials.client = client;
                          gstrCredentials.organizationId = user?.organization?.id;
                          gstrCredentials.status = GstrStatus.ENABLE;
                          gstrCredentials.passwordId = passwordData?.id;
                          gstrCredentials.save();

                          addedGstrClients += 1;
                        } else {
                          const errorDuplicate = `Row ${client['rowNumber']} Client Not Added in Atom Pro GST due to Client Limit Reached, ClientName: ${client.displayName}, Id : ${gstrUsername}`;
                          errorsArray = [...errorsArray, errorDuplicate];
                          notaddedGstrClients += 1;
                          await createCredentials(userId, details);
                        }
                      } else {
                        const errorDuplicate = `Row ${client['rowNumber']} Subscribe Atom Pro GST to access for this Client, Client Name: ${client.displayName}, UserID : ${gstrUsername}`;
                        errorsArray = [...errorsArray, errorDuplicate];
                        notaddedGstrClients += 1;
                        await createCredentials(userId, details);
                      }
                    }
                  }
                } else {
                  await createCredentials(userId, details);
                }
              }
            }
          }

          if (client['Income Tax TAN'] || client['Traces TAN']) {
            let incomeTaxTan = (' ' + client['Income Tax TAN'])?.trim() !== 'undefined' ? client['Income Tax TAN'] : null;
            let incomeTaxTanPassword = (' ' + client['Income Tax TAN Password'])?.trim() !== 'undefined' ? client['Income Tax TAN Password'] : null;
            let tracesUserId = (' ' + client['Traces User Id'])?.trim() !== 'undefined' ? client['Traces User Id'] : null;
            let tracesPassword = (' ' + client['Traces Password'])?.trim() !== 'undefined' ? client['Traces Password'] : null;
            let tracesTan = (' ' + client['Traces TAN'])?.trim() !== 'undefined' ? client['Traces TAN'] : null;
            let isTanAddAtomPro = (' ' + client['Income Tax (TAN) - ATOM Pro'])?.trim() !== 'undefined' ? client['Income Tax (TAN) - ATOM Pro'] : null;

            if (isTanAddAtomPro === 'Yes') {
              TanTraceCredentials += 1;
            }

            //Case 1: Only Income Tax TAN data available without Traces.
            if (incomeTaxTan && !tracesTan) {
              if (!incomeTaxTanPassword) {
                const errorDuplicate = `Row ${client['rowNumber']} Mandatory Fields missing for this TAN, ClientName: ${client.displayName}, TAN: ${incomeTaxTan}`;
                errorsArray = [...errorsArray, errorDuplicate];
                notaddedTanClients += 1;
              } else {
                const tanDetails = {
                  website: 'Income Tax | e-Filing (TAN)',
                  websiteUrl: 'https://eportal.incometax.gov.in/iec/foservices/#/login',
                  loginId: incomeTaxTan,
                  password: incomeTaxTanPassword,
                  client: client,
                  isExistingAtomPro: IsExistingAtomPro.NO,
                };
                const traceDetails = {
                  website: 'Income Tax | Traces (Tax Deductor)',
                  websiteUrl: 'https://www.tdscpc.gov.in/app/login.xhtml?usr=Ded',
                  loginId: null,
                  password: null,
                  client: client,
                  isExistingAtomPro: IsExistingAtomPro.NO
                };
                const checkValidTan = checkTanNumber(incomeTaxTan);
                if (checkValidTan) {
                  const errorDuplicate = `Row ${client['rowNumber']} Income Tax Invalid TAN, Client Name: ${client.displayName}, TAN: ${incomeTaxTan}`;
                  errorsArray = [...errorsArray, errorDuplicate];
                  notaddedTanClients += 1;
                  // await createTanAndTracesCredentials(userId,tanDetails,traceDetails);   
                } else {
                  let tanExistingDetails = await Password.findOne({ where: { client: client, loginId: incomeTaxTan, website: 'Income Tax | e-Filing (TAN)' } });
                  let tracesExistingDetails = await Password.findOne({ where: { client: client, tracesTan: incomeTaxTan, website: 'Income Tax | Traces (Tax Deductor)' } });

                  if (tanExistingDetails || tracesExistingDetails) {
                    const errorDuplicate = `Row ${client['rowNumber']} Income Tax TAN or Traces already exists, Client Name: ${client.displayName}, TAN: ${incomeTaxTan}`;
                    errorsArray = [...errorsArray, errorDuplicate];
                    notaddedTanClients += 1;
                    // await createTanAndTracesCredentials(userId,tanDetails,traceDetails);
                  } else {
                    try {
                      if (isTanAddAtomPro === 'Yes') {
                        let clientIsExisting = await TanClientCredentials.findOne({ where: { clientId: client } });
                        const tanClilentCredentialsCount = await TanClientCredentials.count({ where: { organizationId: user?.organization?.id, status: IncomeTaxStatus.ENABLE } });
                        if (clientIsExisting) {
                          const errorDuplicate = `Row ${client['rowNumber']} This client is already existing in Atom Pro TAN ClientName: ${client.displayName}, TAN: ${incomeTaxTan}`;
                          errorsArray = [...errorsArray, errorDuplicate];
                          notaddedTanClients += 1;
                          // await createTanAndTracesCredentials(userId,tanDetails,traceDetails);
                        } else {
                          let tanIsExisting = await TanClientCredentials.findOne({ where: { organizationId: user?.organization?.id, tanNumber: incomeTaxTan } });
                          if (tanIsExisting) {
                            const errorDuplicate = `Row ${client['rowNumber']} This TAN is already existing in Atom Pro Income Tax, ClientName: ${client.displayName}, TAN: ${incomeTaxTan}`;
                            errorsArray = [...errorsArray, errorDuplicate];
                            notaddedTanClients += 1;
                            // await createTanAndTracesCredentials(userId,tanDetails,traceDetails);
                          } else {
                            if (tanAccess) {
                              if (tanLimit > tanClilentCredentialsCount) {
                                tanDetails.isExistingAtomPro = IsExistingAtomPro.YES;
                                traceDetails.isExistingAtomPro = IsExistingAtomPro.YES;
                                let { tanPassword, tracePassword } = await createTanAndTracesCredentials(userId, tanDetails, traceDetails);
                                const clientCredentials = new TanClientCredentials();
                                clientCredentials.tanNumber = incomeTaxTan;
                                clientCredentials.password = incomeTaxTanPassword;
                                clientCredentials.client = client;
                                clientCredentials.organizationId = user?.organization?.id;
                                clientCredentials.status = IncomeTaxStatus.ENABLE;
                                clientCredentials.passwordId = tanPassword?.id;
                                clientCredentials.traceUserId = null;
                                clientCredentials.tracePassword = null;
                                await clientCredentials.save();
                                addedTanClients += 1;
                              } else {
                                const errorDuplicate = `Row ${client['rowNumber']} Client Not Added in Atom Pro Income Tax due to Client Limit Reached, ClientName: ${client.displayName}, TAN: ${incomeTaxTan}`;
                                errorsArray = [...errorsArray, errorDuplicate];
                                notaddedTanClients += 1;
                                await createTanAndTracesCredentials(userId, tanDetails, traceDetails);
                              }
                            } else {
                              const errorDuplicate = `Row ${client['rowNumber']} Subscribe Atom Pro Income Tax to access for this Client, Client Name: ${client.displayName}, TAN: ${incomeTaxTan}`;
                              errorsArray = [...errorsArray, errorDuplicate];
                              notaddedTanClients += 1;
                              await createTanAndTracesCredentials(userId, tanDetails, traceDetails);
                            }
                          }
                        }
                      } else {
                        await createTanAndTracesCredentials(userId, tanDetails, traceDetails);
                      }
                    } catch (error) {
                      errorsArray.push(`Row ${client['rowNumber']} Failed to create credentials for TAN and Traces, ClientName: ${client.displayName}, Error: ${error.message}`);
                    }
                  }
                }
              }
            }
            //Case 2: Only Traces Data avaliable without Income Tax TAN data.
            else if (!incomeTaxTan && tracesTan) {
              if (!tracesPassword || !tracesUserId) {
                const errorDuplicate = `Row ${client['rowNumber']} Traces Details missing for this client, ClientName: ${client.displayName}, Traces: ${tracesTan}`;
                errorsArray = [...errorsArray, errorDuplicate];
                notaddedTanClients += 1;
              } else {
                const tanDetails = {
                  website: 'Income Tax | e-Filing (TAN)',
                  websiteUrl: 'https://eportal.incometax.gov.in/iec/foservices/#/login',
                  loginId: tracesTan,
                  password: null,
                  client: client,
                  isExistingAtomPro: IsExistingAtomPro.NO,
                };
                const traceDetails = {
                  website: 'Income Tax | Traces (Tax Deductor)',
                  websiteUrl: 'https://www.tdscpc.gov.in/app/login.xhtml?usr=Ded',
                  loginId: tracesUserId,
                  password: tracesPassword,
                  client: client,
                  isExistingAtomPro: IsExistingAtomPro.NO
                }
                const checkValidTan = checkTanNumber(tracesTan);
                if (checkValidTan) {
                  const errorDuplicate = `Row ${client['rowNumber']} Income Tax Invalid TAN, Client Name: ${client.displayName}, TAN: ${tracesTan}`;
                  errorsArray = [...errorsArray, errorDuplicate];
                  notaddedTanClients += 1;
                  // await createTanAndTracesCredentials(userId,tanDetails,traceDetails)   
                } else {
                  let tanExistingDetails = await Password.findOne({ where: { client: client, loginId: tracesTan, website: 'Income Tax | e-Filing (TAN)' } });
                  let tracesExistingDetails = await Password.findOne({ where: { client: client, tracesTan: tracesTan, website: 'Income Tax | Traces (Tax Deductor)' } });

                  if (tanExistingDetails || tracesExistingDetails) {
                    const errorDuplicate = `Row ${client['rowNumber']} Income Tax TAN or Traces already exists, Client Name: ${client.displayName}, TAN: ${tracesTan}`;
                    errorsArray = [...errorsArray, errorDuplicate];
                    notaddedTanClients += 1;
                    // await createTanAndTracesCredentials(userId,tanDetails,traceDetails)     
                  } else {
                    const checkValidTracesUserId = checkTracesUserId(tracesUserId);
                    const checkValidTracesPassword = checkTracesPassword(tracesPassword);
                    if (checkValidTracesUserId) {
                      const errorDuplicate = `Row ${client['rowNumber']} Trace User Id id Invalid, Client Name: ${client.displayName}, Trace User Id: ${tracesUserId}`;
                      errorsArray = [...errorsArray, errorDuplicate];
                      notaddedTanClients += 1;
                    }
                    if (checkValidTracesPassword) {
                      const errorDuplicate = `Row ${client['rowNumber']} Trace Password is Invalid, Client Name: ${client.displayName}, Trace Password: ${tracesPassword}`;
                      errorsArray = [...errorsArray, errorDuplicate];
                      notaddedTanClients += 1;
                    } else {
                      try {
                        if (isTanAddAtomPro === 'Yes') {
                          let clientIsExisting = await TanClientCredentials.findOne({ where: { clientId: client } });
                          const tanClilentCredentialsCount = await TanClientCredentials.count({ where: { organizationId: user?.organization?.id, status: IncomeTaxStatus.ENABLE } });
                          if (clientIsExisting) {
                            const errorDuplicate = `Row ${client['rowNumber']} This client is already existing in Atom Pro Income Tax ClientName: ${client.displayName}, TAN: ${tracesTan}`;
                            errorsArray = [...errorsArray, errorDuplicate];
                            notaddedTanClients += 1;
                            // await createTanAndTracesCredentials(userId,tanDetails,traceDetails);
                          } else {
                            let tanIsExisting = await TanClientCredentials.findOne({ where: { organizationId: user?.organization?.id, tanNumber: tracesTan } });
                            if (tanIsExisting) {
                              const errorDuplicate = `Row ${client['rowNumber']} This TAN is already existing in Atom Pro Income Tax, ClientName: ${client.displayName}, TAN: ${tracesTan}`;
                              errorsArray = [...errorsArray, errorDuplicate];
                              notaddedTanClients += 1;
                              // await createTanAndTracesCredentials(userId,tanDetails,traceDetails);
                            } else {
                              if (tanAccess) {
                                if (tanLimit > tanClilentCredentialsCount) {
                                  tanDetails.isExistingAtomPro = IsExistingAtomPro.YES;
                                  traceDetails.isExistingAtomPro = IsExistingAtomPro.YES;
                                  let { tanPassword, tracePassword } = await createTanAndTracesCredentials(userId, tanDetails, traceDetails);
                                  const clientCredentials = new TanClientCredentials();
                                  clientCredentials.tanNumber = tracesTan;
                                  clientCredentials.password = incomeTaxTanPassword;
                                  clientCredentials.client = client;
                                  clientCredentials.organizationId = user?.organization?.id;
                                  clientCredentials.status = IncomeTaxStatus.ENABLE;
                                  clientCredentials.passwordId = tanPassword?.id;
                                  clientCredentials.traceUserId = tracesUserId;
                                  clientCredentials.tracePassword = tracesPassword;
                                  await clientCredentials.save();
                                  addedTanClients += 1;
                                } else {
                                  const errorDuplicate = `Row ${client['rowNumber']} Client Not Added in Atom Pro Income Tax due to Client Limit Reached, ClientName: ${client.displayName}, TAN: ${tracesTan}`;
                                  errorsArray = [...errorsArray, errorDuplicate];
                                  notaddedTanClients += 1;
                                  await createTanAndTracesCredentials(userId, tanDetails, traceDetails);
                                }
                              } else {
                                const errorDuplicate = `Row ${client['rowNumber']} Subscribe Atom Pro Income Tax to access for this Client, Client Name: ${client.displayName}, TAN: ${tracesTan}`;
                                errorsArray = [...errorsArray, errorDuplicate];
                                notaddedTanClients += 1;
                                await createTanAndTracesCredentials(userId, tanDetails, traceDetails);
                              }
                            }
                          }
                        } else {
                          await createTanAndTracesCredentials(userId, tanDetails, traceDetails);
                        }
                      } catch (error) {
                        errorsArray.push(`Row ${client['rowNumber']} Failed to create credentials for TAN and Traces, ClientName: ${client.displayName}, Error: ${error.message}`);
                      }
                    }

                  }
                }
              }
            }
            //Case 3: If Both TAN and Traces data are available.
            else if (incomeTaxTan && tracesTan) {
              if (incomeTaxTan !== tracesTan) {
                const errorDuplicate = `Row ${client['rowNumber']} Income Tax TAN doesn't match with Trace TAN, ClientName: ${client.displayName}, Traces: ${tracesTan}`;
                errorsArray = [...errorsArray, errorDuplicate];
                notaddedTanClients += 1;
              } else {
                const tanDetails = {
                  website: 'Income Tax | e-Filing (TAN)',
                  websiteUrl: 'https://eportal.incometax.gov.in/iec/foservices/#/login',
                  loginId: incomeTaxTan,
                  password: incomeTaxTanPassword,
                  client: client,
                  isExistingAtomPro: IsExistingAtomPro.NO,
                };
                const traceDetails = {
                  website: 'Income Tax | Traces (Tax Deductor)',
                  websiteUrl: 'https://www.tdscpc.gov.in/app/login.xhtml?usr=Ded',
                  loginId: tracesUserId,
                  password: tracesPassword,
                  client: client,
                  isExistingAtomPro: IsExistingAtomPro.NO
                }
                const checkValidTan = checkTanNumber(tracesTan);
                if (checkValidTan) {
                  const errorDuplicate = `Row ${client['rowNumber']} Income Tax Invalid TAN, Client Name: ${client.displayName}, TAN: ${incomeTaxTan}`;
                  errorsArray = [...errorsArray, errorDuplicate];
                  notaddedTanClients += 1;
                  // await createTanAndTracesCredentials(userId,tanDetails,traceDetails)
                } else {
                  let tanExistingDetails = await Password.findOne({ where: { client: client, loginId: incomeTaxTan, website: 'Income Tax | e-Filing (TAN)' } });
                  let tracesExistingDetails = await Password.findOne({ where: { client: client, tracesTan: incomeTaxTan, website: 'Income Tax | Traces (Tax Deductor)' } });

                  if (tanExistingDetails || tracesExistingDetails) {
                    const errorDuplicate = `Row ${client['rowNumber']} Income Tax TAN or Traces already exists, Client Name: ${client.displayName}, TAN: ${incomeTaxTan}`;
                    errorsArray = [...errorsArray, errorDuplicate];
                    notaddedTanClients += 1;
                    // await createTanAndTracesCredentials(userId,tanDetails,traceDetails)     
                  } else {
                    const checkValidTracesUserId = checkTracesUserId(tracesUserId);
                    const checkValidTracesPassword = checkTracesPassword(tracesPassword);
                    if (checkValidTracesUserId) {
                      const errorDuplicate = `Row ${client['rowNumber']} Trace User Id id Invalid, Client Name: ${client.displayName}, Trace User Id: ${tracesUserId}`;
                      errorsArray = [...errorsArray, errorDuplicate];
                      notaddedTanClients += 1;
                    }
                    if (checkValidTracesPassword) {
                      const errorDuplicate = `Row ${client['rowNumber']} Trace Password is Invalid, Client Name: ${client.displayName}, Trace Password: ${tracesPassword}`;
                      errorsArray = [...errorsArray, errorDuplicate];
                      notaddedTanClients += 1;
                    } else {
                      try {
                        if (isTanAddAtomPro === 'Yes') {
                          let clientIsExisting = await TanClientCredentials.findOne({ where: { clientId: client } });
                          const tanClilentCredentialsCount = await TanClientCredentials.count({ where: { organizationId: user?.organization?.id, status: IncomeTaxStatus.ENABLE } });
                          if (clientIsExisting) {
                            const errorDuplicate = `Row ${client['rowNumber']} This client is already existing in Atom Pro Income Tax ClientName: ${client.displayName}, TAN: ${incomeTaxTan}`;
                            errorsArray = [...errorsArray, errorDuplicate];
                            notaddedTanClients += 1;
                            // await createTanAndTracesCredentials(userId,tanDetails,traceDetails);
                          } else {
                            let tanIsExisting = await TanClientCredentials.findOne({ where: { organizationId: user?.organization?.id, tanNumber: incomeTaxTan } });
                            if (tanIsExisting) {
                              const errorDuplicate = `Row ${client['rowNumber']} This TAN is already existing in Atom Pro Income Tax, ClientName: ${client.displayName}, TAN: ${incomeTaxTan}`;
                              errorsArray = [...errorsArray, errorDuplicate];
                              notaddedTanClients += 1;
                              // await createTanAndTracesCredentials(userId,tanDetails,traceDetails);
                            } else {
                              if (tanAccess) {
                                if (tanLimit > tanClilentCredentialsCount) {
                                  tanDetails.isExistingAtomPro = IsExistingAtomPro.YES;
                                  traceDetails.isExistingAtomPro = IsExistingAtomPro.YES;
                                  let { tanPassword, tracePassword } = await createTanAndTracesCredentials(userId, tanDetails, traceDetails);
                                  const clientCredentials = new TanClientCredentials();
                                  clientCredentials.tanNumber = incomeTaxTan;
                                  clientCredentials.password = incomeTaxTanPassword;
                                  clientCredentials.client = client;
                                  clientCredentials.organizationId = user?.organization?.id;
                                  clientCredentials.status = IncomeTaxStatus.ENABLE;
                                  clientCredentials.passwordId = tanPassword?.id;
                                  clientCredentials.traceUserId = tracesUserId;
                                  clientCredentials.tracePassword = tracesPassword;
                                  await clientCredentials.save();
                                  addedTanClients += 1;
                                } else {
                                  const errorDuplicate = `Row ${client['rowNumber']} Client Not Added in Atom Pro Income Tax due to Client Limit Reached, ClientName: ${client.displayName}, TAN: ${tracesTan}`;
                                  errorsArray = [...errorsArray, errorDuplicate];
                                  notaddedTanClients += 1;
                                  await createTanAndTracesCredentials(userId, tanDetails, traceDetails);
                                }
                              } else {
                                const errorDuplicate = `Row ${client['rowNumber']} Subscribe Atom Pro Income Tax to access for this Client, Client Name: ${client.displayName}, TAN: ${tracesTan}`;
                                errorsArray = [...errorsArray, errorDuplicate];
                                notaddedTanClients += 1;
                                await createTanAndTracesCredentials(userId, tanDetails, traceDetails);
                              }
                            }
                          }
                        } else {
                          await createTanAndTracesCredentials(userId, tanDetails, traceDetails);
                        }
                      } catch (error) {
                        errorsArray.push(`Row ${client['rowNumber']} Failed to create credentials for TAN and Traces, ClientName: ${client.displayName}, Error: ${error.message}`);
                      }
                    }
                  }
                }
              }
            }

          }
          else if (client['Income Tax (TAN) - ATOM Pro']?.trim()) {
            const errorDuplicate = `Row ${client['rowNumber']} Income Tax TAN or Trace TAN is not provided, Client Name: ${client.displayName}`;
            errorsArray = [...errorsArray, errorDuplicate];
            notaddedTanClients += 1;
            TanTraceCredentials += 1;
          }

          let randomPassword = randomBytes(16).toString('hex');
          this.eventEmitter.emit(Event_Actions.CLIENT_CREATED, {
            userId,
            data: client,
            orgName: user?.organization?.legalName,
            isEmail: undefined,
          });
        }

        errorsArray = [
          ...errorsArray,
          notaddedTanClients,
          incomeTaxCredentials,
          TanTraceCredentials,
          GstrCredential,
          totalRows,
          addedIncomeTaxClients,
          addedGstrClients,
          addedTanClients,
          alreadyAddedClients,
          notAddedClient,
          addedArray,
        ];

        const ImportClientRow = await createQueryBuilder(ImportData, 'importClientRow')
          .where('importClientRow.organization = :organization', { organization: user.organization.id })
          .getOne();
        ImportClientRow.status = ImportStatus.COMPLETE;
        ImportClientRow.responseData = JSON.stringify(errorsArray);
        await ImportClientRow.save();

        const adminIds = await getAdminEmailssBasedOnOrganizationId(user.organization.id);
        for (let i of adminIds) {
          let newNotification = new Notification();
          newNotification.title = 'Imported Clients';
          newNotification.body = `Your client list has been successfully imported. ${addedArray} clients added.`;
          newNotification.user = i.id;
          newNotification.status = 'unread';
          await newNotification.save();
          const mailOptions = {
            data: {
              notAddedClient,
              addedArray,
              totalRows,
              date: moment().tz('Asia/Kolkata').format('DD-MM-YYYY HH:mm:ss'),
              userName: i.full_name,
              userId: user?.id,
              alreadyAdded: alreadyAddedClients
            },
            email: user.organization?.email,
            filePath: 'import-data',
            subject: 'Bulk Client Import Status Report',
            key: 'TO_ADMIN',
            id: user?.id,
          };
          const msg = await sendnewMail(mailOptions);
        }

        return errorsArray;
      } catch (err) {
        const errorDuplicate = String(new InternalServerErrorException(err));
        notAddedClient += 1;
        errorsArray = [
          ...errorsArray,
          errorDuplicate,
          notaddedTanClients,
          incomeTaxCredentials,
          TanTraceCredentials,
          GstrCredential,
          totalRows,
          addedIncomeTaxClients,
          addedGstrClients,
          addedTanClients,
          alreadyAddedClients,
          notAddedClient,
          addedArray,
        ];
        const ImportClientRow = await createQueryBuilder(ImportData, 'importClientRow')
          .where('importClientRow.organization = :organization', { organization: user.organization.id })
          .getOne();
        ImportClientRow.status = ImportStatus.COMPLETE;
        ImportClientRow.responseData = JSON.stringify(errorsArray);
        await ImportClientRow.save();

        const adminIds = await getAdminEmailssBasedOnOrganizationId(user.organization.id);
        for (let i of adminIds) {
          let newNotification = new Notification();
          newNotification.title = 'Imported Clients';
          newNotification.body = `Your client list has been successfully imported. ${addedArray} clients added.`;
          newNotification.user = i;
          newNotification.status = 'unread';
          await newNotification.save();
          const mailOptions = {
            data: {
              notAddedClient,
              addedArray,
              totalRows,
              date: moment().tz('Asia/Kolkata').format('DD-MM-YYYY HH:mm:ss'),
              userName: i.full_name,
              userId: user?.id,
              alreadyAdded: alreadyAddedClients
            },
            email: user.organization?.email,
            filePath: 'import-data',
            subject: 'Bulk Client Import Status Report',
            key: 'TO_ADMIN',
            id: user?.id,
          };
          const msg = await sendnewMail(mailOptions);
        }
        return errorsArray;
      }
    } else {
      const errorone = `All Clients are already Added and Atleast one new row required as per sample sheet`;
      errorsArray = [
        ...errorsArray,
        errorone,
        notaddedTanClients,
        incomeTaxCredentials,
        TanTraceCredentials,
        GstrCredential,
        totalRows,
        addedIncomeTaxClients,
        addedGstrClients,
        addedTanClients,
        alreadyAddedClients,
        notAddedClient,
        addedArray,
      ];
      const ImportClientRow = await createQueryBuilder(ImportData, 'importClientRow')
        .where('importClientRow.organization = :organization', { organization: user.organization.id })
        .getOne();
      ImportClientRow.status = ImportStatus.COMPLETE;
      ImportClientRow.responseData = JSON.stringify(errorsArray);
      await ImportClientRow.save();

      const adminIds = await getAdminEmailssBasedOnOrganizationId(user.organization.id);
      for (let i of adminIds) {
        let newNotification = new Notification();
        newNotification.title = 'Imported Clients';
        newNotification.body = `Your client list has been successfully imported. ${addedArray} clients added.`;
        newNotification.user = i;
        newNotification.status = 'unread';
        await newNotification.save();
        const mailOptions = {
          data: {
            notAddedClient,
            addedArray,
            totalRows,
            date: moment().tz('Asia/Kolkata').format('DD-MM-YYYY'),
            userName: i.full_name,
            userId: user?.id,
            alreadyAdded: alreadyAddedClients
          },
          email: user.organization?.email,
          filePath: 'import-data',
          subject: 'Bulk Client Import Status Report',
          key: 'TO_ADMIN',
          id: user?.id,
        };
        const msg = await sendnewMail(mailOptions);
      }

      return errorsArray;
    }
  }

  async getAllClients(id: number) {
    let user = await User.findOne({ where: { id: id }, relations: ['organization'] });

    let clients = getConnection()
      .createQueryBuilder(Client, 'client')
      .leftJoin('client.labels', 'labels')
      .leftJoin('client.organization', 'organization')
      .orderBy('client.displayName', 'ASC')
      .leftJoinAndSelect('client.contactPersons', 'contactPersons')
      .where('organization.id = :organization', { organization: user.organization.id })
      .getMany();

    return clients;
  }

  async gstData(gstNumber) {
    try {
      const headers = {
        Authorization: process.env.FYN_AUTH,
      };
      const url = `${process.env.FYN_URL}${gstNumber}`;
      const response = await axios.get(url, { headers });
      const res = { data: response.data };
      return res;
    } catch (error) {
      console.error(error);
    }
  }

  async updateImage(id: number, userId: number, data: any) {
    let client = null;
    let clientGroup = null;
    if (data?.clientType) {
      clientGroup = await ClientGroup.findOne({ where: { id: id }, relations: ['clientGroupImage'] });
    } else {
      client = await Client.findOne({ where: { id: id }, relations: ['clientImage'] });
    }
    let iStorage: Storage;
    if (data?.name) {
      if (client?.clientImage?.id) {
        if (data.name !== client?.clientImage?.name) {
          if (client?.clientImage?.storageSystem === StorageSystem.AMAZON) {
            this.storageService.deleteAwsFile(client?.clientImage?.file);
          } else if (client?.clientImage?.storageSystem === StorageSystem.MICROSOFT) {
            this.oneDriveService.deleteOneDriveFile(userId, client?.clientImage?.fileId);
          } else if (client?.clientImage?.storageSystem === StorageSystem.BHARATHCLOUD) {
            this.bharathService.deleteB3File(userId, client?.clientImage?.file)
          }
        }
        iStorage = await Storage.findOne({ where: { id: client?.clientImage?.id } });
        iStorage.fileType = data?.fileType;
        iStorage.fileSize = data?.fileSize;
        iStorage.name = data?.name;
        iStorage.file = data?.upload;
        iStorage.authId = client?.organization.id;
        iStorage.filePath = data?.filePath;
        iStorage.show = data?.show;
      } else if (clientGroup?.clientGroupImage?.id) {
        if (data.name !== clientGroup?.clientGroupImage?.name) {
          if (clientGroup?.clientGroupImage?.storageSystem === StorageSystem.AMAZON) {
            this.storageService.deleteAwsFile(clientGroup?.clientGroupImage?.file);
          } else if (clientGroup?.clientGroupImage?.storageSystem === StorageSystem.MICROSOFT) {
            this.oneDriveService.deleteOneDriveFile(userId, clientGroup?.clientGroupImage?.fileId);
          } else if (clientGroup?.clientGroupImage?.storageSystem === StorageSystem.BHARATHCLOUD) {
            this.bharathService.deleteB3File(userId, clientGroup?.clientGroupImage?.file)
          }
        }
        iStorage = await Storage.findOne({ where: { id: clientGroup?.clientGroupImage?.id } });
        iStorage.fileType = data?.fileType;
        iStorage.fileSize = data?.fileSize;
        iStorage.name = data?.name;
        iStorage.file = data?.upload;
        iStorage.show = data?.show;
        iStorage.authId = client?.organization.id;
        iStorage.filePath = data?.filePath;
      } else {
        iStorage = await this.storageService.addAttachements(userId, data);
      }
    } else {
      if (client?.clientImage?.id) {
        const existingPStorage = await Storage.findOne({ where: { id: client?.clientImage?.id } });
        const existingStorage = await existingPStorage.remove();
        if (existingStorage) {
          if (existingStorage.storageSystem === StorageSystem.AMAZON) {
            this.storageService.deleteAwsFile(existingStorage.file);
          } else if (existingStorage.storageSystem === StorageSystem.MICROSOFT) {
            this.oneDriveService.deleteOneDriveFile(userId, existingStorage.fileId);
          } else if (existingStorage.storageSystem === StorageSystem.BHARATHCLOUD) {
            this.bharathService.deleteB3File(userId, existingStorage.file);
          }
        }
        if (clientGroup?.clientGroupImage?.id) {
          const existingPStorage = await Storage.findOne({ where: { id: clientGroup?.clientGroupImage?.id } });
          const existingStorage = await existingPStorage.remove();
          if (existingStorage) {
            if (existingStorage.storageSystem === StorageSystem.AMAZON) {
              this.storageService.deleteAwsFile(existingStorage.file);
            } else if (existingStorage.storageSystem === StorageSystem.MICROSOFT) {
              this.oneDriveService.deleteOneDriveFile(userId, existingStorage.fileId);
            } else if (existingStorage.storageSystem === StorageSystem.BHARATHCLOUD) {
              this.bharathService.deleteB3File(userId, existingStorage.file)
            }
          }
        }
        clientGroup.clientGroupImage = null;
      }
    }

    if (iStorage) {
      if (client) {
        iStorage.clientImage = client;
      }
      if (clientGroup) {
        iStorage.clientGroupImage = clientGroup;
      }
      await iStorage.save();
    }

  }

  async bulkUpdateClients(userId: number, file: Express.Multer.File) {
    const user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
    const workbook = xlsx.read(file.buffer, { cellDates: true });
    const sheet = workbook.Sheets[workbook.SheetNames[0]];
    const dataFromExcel: Client[] = xlsx.utils.sheet_to_json(sheet, { defval: null });

    const metaDataSheet = workbook.Sheets['Metadata'];


    if (!metaDataSheet) {
      throw new BadRequestException("Please upload the valid exported excel.");
    };
    const markerText = metaDataSheet['A1']?.v;
    const organizationIdFromExcel = metaDataSheet['A2']?.v;
    if (organizationIdFromExcel !== user?.organization?.id) {
      throw new BadRequestException("Organization doesn't matched. Please upload exported excel from your organization.");
    };

    function removeEmptyStrings(data: any): any {
      return Object.fromEntries(
        Object.entries(data).filter(([_, value]) => value !== '' && value !== 'null')
      );
    }

    const result: Client[] = [];
    let errorsArray = [];
    let updatedClients = 0;
    let notUpdatedClients = 0;
    let validateError = 0;
    let rows = '';
    const PAN_REGEX = /^[A-Z]{3}[A,B,C,F,G,H,L,J,P,T]{1}[A-Z]{1}[0-9]{4}[A-Z]{1}$/;
    const GSTIN_REGEX =
      /^[0-9]{2}[a-zA-Z]{4}[a-zA-Z0-9]{1}[0-9]{4}[a-zA-Z]{1}[1-9A-Za-z]{1}[D,C,Z]{1}[0-9a-zA-Z]{1}$/;
    const requiredFields = [
      'Client Category *',
      'Display Name *',
      'Mobile Number *',
      'Email Id *',
      'Country Code *'
    ];

    if (dataFromExcel.length > 0) {
      let filteredData = dataFromExcel.filter((item) => {
        return (
          item['S.No'] ||
          item['Email Id *'] ||
          item['Mobile Number *'] ||
          item['Display Name *'] ||
          item['Client Category *'] ||
          item['Sub Category *']
        );
      });


      const areMandotoryFieldsValid = (data, requiredFields) => {
        return requiredFields.every(field => {
          return data.hasOwnProperty(field) &&
            data[field] !== '' &&
            data[field] !== null &&
            data[field] !== 'null';
        });
      };

      const isSubCategoryValid = (clientCat: string, clientSubCat: string | null): boolean => {
        const category = CLIENT_CATEGORIES.find((cat) => cat.value === clientCat);

        if (!category) return false;

        if (!category.subCategories) {
          return clientSubCat === null;
        }

        return category.subCategories.some((subCat) => subCat.value === clientSubCat);
      }

      const checkCellValidation = (value: any) => {
        let validation = false;
        if ((' ' + value)?.trim() !== 'undefined' &&
          (' ' + value)?.trim() !== undefined && (' ' + value)?.trim() !== 'null' && (' ' + value)?.trim() !== null && (' ' + value)?.trim() !== '') {
          validation = true;
        }
        return validation;
      }

      for (const [index, item] of filteredData.entries()) {
        const existingClient = await Client.findOne(item['id']);
        const client = existingClient;

        const data: any = item;
        validateError = 0;

        if (areMandotoryFieldsValid(data, requiredFields)) {
          let date = null;
          if (checkCellValidation(data['Date of Birth(DD-MM-YYYY)'])) {
            const dateStr = data['Date of Birth(DD-MM-YYYY)'];
            if (
              typeof dateStr === 'string' &&
              dateStr !== null &&
              dateStr !== undefined &&
              dateStr !== 'undefined'
            ) {
              const trimmedDateString = dateStr?.trim();
              date = moment(trimmedDateString, 'DD-MM-YYYY', true).format('YYYY-MM-DD');
            } else if (
              typeof dateStr === 'object' &&
              dateStr !== null &&
              dateStr !== undefined &&
              dateStr !== 'undefined'
            ) {
              const dateObj = moment.utc(dateStr);
              const dateObjAdd = dateObj?.add(1, 'days');
              date = moment(dateObjAdd).format('YYYY-MM-DD');
            }
          }

          if (data['Client Category *']) {
            const clientCat =
              CategoryEnum[
              data['Client Category *']?.toUpperCase().replace(/\s+/g, '_') as CategoryEnum
              ];
            const clientSubCat =
              SubCategoryEnum[
                data['Sub Category *']?.toUpperCase().replace(/\s+/g, '_') as SubCategoryEnum
              ] === undefined
                ? null
                : SubCategoryEnum[
                data['Sub Category *']?.toUpperCase().replace(/\s+/g, '_') as SubCategoryEnum
                ];

            if (isSubCategoryValid(clientCat, clientSubCat)) {
              client.category =
                CategoryEnum[
                data['Client Category *']?.toUpperCase().replace(/\s+/g, '_') as CategoryEnum
                ];

              client.subCategory =
                SubCategoryEnum[
                  data['Sub Category *']?.toUpperCase().replace(/\s+/g, '_') as SubCategoryEnum
                ] === undefined
                  ? null
                  : SubCategoryEnum[
                  data['Sub Category *']?.toUpperCase().replace(/\s+/g, '_') as SubCategoryEnum
                  ];
            } else {
              const errorDuplicate = `row ${index + 2
                } sub category doesn't match with the category or sub category is not provided.`;
              errorsArray = [...errorsArray, errorDuplicate];
              notUpdatedClients += 1;
              continue;
            }
          }



          if (!isValidString(data['Display Name *'])) {
            const errorDuplicate = `row ${index + 2
              } has Invalid Characters(<, >, :, ", /, \\, |, ?, *, or .) in Display Name .`;
            errorsArray = [...errorsArray, errorDuplicate];
            notUpdatedClients += 1;
            continue;
          }
          client.displayName = (' ' + data['Display Name *'])?.trim();
          client.clientNumber =
            checkCellValidation(data['Client Number'])
              ? (' ' + data['Client Number'])?.trim()
              : null;
          client.mobileNumber = data['Mobile Number *'];
          client.email = (' ' + data['Email Id *'])?.trim();

          const alternateMobileNumber: any =
            checkCellValidation(data['Alternate Mobile Number'])
              ? (' ' + data['Alternate Mobile Number'])?.trim()
              : null;


          const alternateCountryCode: any =
            checkCellValidation(data['A - Country Code'])
              ? (' ' + data['A - Country Code'])?.trim()
              : null;

          if (alternateMobileNumber && alternateCountryCode) {
            const validateANumber = mobileWithCountry(alternateMobileNumber, alternateCountryCode);
            if (!validateANumber) {
              const countryCode = countries.find((c) => c.label === alternateCountryCode);
              client.alternateMobileNumber = alternateMobileNumber;
              client.alternateCountryCode = countryCode?.code;
            }
          }

          client.dob =
            checkCellValidation(data['Date of Birth(DD-MM-YYYY)']) ? date : null;
          client.authorizedPerson =
            checkCellValidation(data['Authorized Person'])
              ? (' ' + data['Authorized Person'])?.trim()
              : null;
          client.designation =
            checkCellValidation(data['Designation'])
              ? (' ' + data['Designation'])?.trim()
              : null;

          const gstNumber = checkCellValidation(data['GSTIN']) ? (' ' + data['GSTIN'])?.trim() : null;
          const panNumber = checkCellValidation(data['PAN']) ? (' ' + data['PAN'])?.trim() : null;
          const tanNumber = checkCellValidation(data['TAN']) ? (' ' + data['TAN'])?.trim() : null;
          const validateNumber = mobileWithCountry(data['Mobile Number *'], data['Country Code *']);
          if (validateNumber) {
            const errorDuplicate = `row ${index + 2
              } have mobile number does not match the selected country.`;
            errorsArray = [...errorsArray, errorDuplicate];
            notUpdatedClients += 1;
            continue;
          }

          const countryCode = countries.find((c) => c.label === data['Country Code *']);
          client.countryCode = countryCode.code;

          await validate(client).then((errors) => {
            if (errors.length > 0) {
              const errorDuplicate = `Invalid ${_.startCase(errors[0]?.property)} in row ${index + 2
                }`;
              errorsArray = [...errorsArray, errorDuplicate];
              notUpdatedClients += 1;
              validateError = 1;
            }
          });

          if (validateError) {
            continue;
          }

          if (
            tanNumber &&
            tanNumber !== 'undefined' &&
            tanNumber !== '' &&
            !TAN_REGEX.test(tanNumber)
          ) {
            const errorDuplicate = `row ${index + 2} has Invalid TAN`;
            errorsArray = [...errorsArray, errorDuplicate];
            notUpdatedClients += 1;
            continue;
          }

          if (
            panNumber &&
            panNumber !== 'undefined' &&
            panNumber !== '' &&
            !PAN_REGEX.test(panNumber)
          ) {
            const errorDuplicate = `row ${index + 2} has Invalid PAN`;
            errorsArray = [...errorsArray, errorDuplicate];
            notUpdatedClients += 1;
            continue;
          }
          if (
            panNumber &&
            panNumber !== 'undefined' &&
            panNumber !== '' &&
            PAN_REGEX.test(panNumber)
          ) {
            if (gstNumber && gstNumber !== 'undefined' && gstNumber !== '' && GSTIN_REGEX.test(gstNumber)) {
              const gstPan = gstNumber?.slice(2, gstNumber?.length - 3);
              if (!(gstPan === panNumber)) {
                const errorName = `row ${index + 2
                  } - Gst ${gstNumber} and Pan ${panNumber} is not Matched`;
                errorsArray = [...errorsArray, errorName];
                notUpdatedClients += 1;
                continue;
              }
            }
          }
          client.panNumber = panNumber;
          client.tanNumber = tanNumber;
          const address: any = {
            communicationfulladdress: data['Communication Address'],
          };

          //ADDRESS CHECK GST VERIFIED OR NOT
          if (!existingClient?.gstVerified) {
            if (checkCellValidation(data['Communication Address']) && checkCellValidation(data['Billing Address'])) {
              client.issameaddress = data['Communication Address'] === data['Billing Address'] ? true : false;
              address['billingfulladdress'] = checkCellValidation(data['Billing Address']) ? (' ' + data['Billing Address'])?.trim() : null;
              address['communicationfulladdress'] = checkCellValidation(' ' + data['Communication Address']) ? (' ' + data['Communication Address'])?.trim() : null;
              address['billingState'] = checkCellValidation(data['Billing State']) ? (' ' + data['Billing State'])?.trim() : null;
              client.address = address;
            } else if (checkCellValidation(data['Communication Address']) && !checkCellValidation(data['Billing Address'])) {
              client.issameaddress = true;
              address['billingfulladdress'] = checkCellValidation(data['Communication Address']) ? (' ' + data['Communication Address'])?.trim() : null;
              address['communicationfulladdress'] = checkCellValidation(' ' + data['Communication Address']) ? (' ' + data['Communication Address'])?.trim() : null;
              address['billingState'] = checkCellValidation(data['State / Union Territory']) ? (' ' + data['State / Union Territory'])?.trim() : null;
              client.address = address;
            } else {
              client.issameaddress = false;
              address['billingfulladdress'] = checkCellValidation(data['Billing Address']) ? (' ' + data['Billing Address'])?.trim() : null;
              address['communicationfulladdress'] = checkCellValidation(' ' + data['Communication Address']) ? (' ' + data['Communication Address'])?.trim() : null;
              address['billingState'] = checkCellValidation(data['Billing State']) ? (' ' + data['Billing State'])?.trim() : null;
              client.address = address;
            }
            client.state =
              checkCellValidation(data['State / Union Territory'])
                ? (' ' + data['State / Union Territory'])?.trim()
                : null;
            client.firstName =
              checkCellValidation(data['First Name'])
                ? (' ' + data['First Name'])?.trim()
                : null;
            client.middleName =
              checkCellValidation(data['Middle Name'])
                ? (' ' + data['Middle Name'])?.trim()
                : null;
            client.lastName =
              checkCellValidation(data['Last Name'])
                ? (' ' + data['Last Name'])?.trim()
                : null;
            client.fullName =
              checkCellValidation(data['Full Name'])
                ? (' ' + data['Full Name'])?.trim()
                : null;
            client.tradeName =
              checkCellValidation(data['Trade Name'])
                ? (' ' + data['Trade Name'])?.trim()
                : null;
            client.legalName =
              checkCellValidation(data['Legal Name'])
                ? (' ' + data['Legal Name'])?.trim()
                : null;
          } else {
            if (client?.issameaddress) {

              if (client.address && typeof client.address === 'object') {
                address['billingfulladdress'] = (client.address as any).billingfulladdress ?? '';
                address['communicationfulladdress'] = (client.address as any).communicationfulladdress ?? '';
                address['billingState'] = client.issameaddress ? client.state : (client.address as any).billingState ?? '';
                client.address = address;
              }

            }
          };
          client['rowNumber'] = index + 2;

          result.push({ ...client } as Client);

        } else {
          const errorDuplicate = `Row ${index + 2
            } have Improper Client details, Mandatory Fields Missing.`;
          errorsArray = [...errorsArray, errorDuplicate];
          notUpdatedClients += 1;
          continue;
        }
      }
      if (result?.length > 0) {
        try {
          for (const client of result) {
            updatedClients += 1;
            let a = removeEmptyStrings(client);
            await Client.save(a);

            // this.eventEmitter.emit(Event_Actions.CLIENT_UPDATED, {
            //   userId,
            //   data: client,
            //   orgName: user?.organization?.legalName,
            //   isEmail: undefined,
            // });
          }
          errorsArray = [
            ...errorsArray,
            notUpdatedClients,
            updatedClients,
          ];
          return errorsArray;
        } catch (err) {
          // throw new InternalServerErrorException(err);
          const errorDuplicate = String(new InternalServerErrorException(err));
          notUpdatedClients += 1;
          errorsArray = [
            ...errorsArray,
            errorDuplicate,
            notUpdatedClients,
            updatedClients,
          ];
          return errorsArray;
        }
      } else {
        errorsArray = [
          ...errorsArray,
          notUpdatedClients,
          updatedClients,
        ];
        return errorsArray;
      }
    } else {
      throw new BadRequestException(`Atleast one row required as per sample sheet`);
    }

  }
}
