import {
  Body,
  Controller,
  Get,
  Param,
  ParseIntPipe,
  Post,
  Put,
  Query,
  Request,
  UseGuards,
} from '@nestjs/common';
import { JwtAuthGuard } from 'src/modules/users/jwt/jwt-auth.guard';
import { DocumentCategoryService } from './doucment-category.service';

@Controller('document-category')
export class DocumentInOutController {
  constructor(private service: DocumentCategoryService) { }

  @UseGuards(JwtAuthGuard)
  @Post()
  async create(@Body() body: any, @Request() req: any) {
    const { userId } = req.user;
    return this.service.create(userId, body);
  }

  @UseGuards(JwtAuthGuard)
  @Get()
  async get(@Request() req: any, @Query() query: any) {
    const { userId } = req.user;
    return this.service.get(userId, query);
  }

  // @UseGuards(JwtAuthGuard)
  // @Put('/:id')
  // async update(@Request() req: any, @Param('id', ParseIntPipe) id: number, @Body() body: CreateLeadDto) {
  //   const { userId } = req.user;
  //   return this.service.update(id, body, userId);
  // }

  @UseGuards(JwtAuthGuard)
  @Post('/delete')
  delete(@Body() body: any) {
    return this.service.delete(body.ids);
  }
}
