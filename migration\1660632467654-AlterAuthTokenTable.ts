import { MigrationInterface, QueryRunner } from 'typeorm';

export class AlterAuthTokenTable1660632467654 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
        ALTER TABLE auth_token
        MODIFY COLUMN access_token text not null,
        MODIFY COLUMN refresh_token text not null
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}
