import { BaseEntity, BeforeInsert, Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

@Entity()
export class Permission extends BaseEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  label: string;

  @Column()
  parentLabel: string;

  @Column()
  name: string;

  @Column({ unique: true })
  slug: string;

  @BeforeInsert()
  async slugify() {
    this.slug = `${this.label.toLowerCase().replace(/ /g, '_')}_${this.name
      .toLowerCase()
      .replace(/ /g, '_')}`;
  }
}
