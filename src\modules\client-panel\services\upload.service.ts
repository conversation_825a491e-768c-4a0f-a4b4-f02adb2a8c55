import { Injectable, BadRequestException } from '@nestjs/common';
import { S3 } from 'aws-sdk';

@Injectable()
export class AwsService {
  async upload(buffer: Buffer, key: string, contentType = '') {
    try {
      const bucketS3 = process.env.AWS_BUCKET_NAME;
      const upload = await this.uploadS3(buffer, bucketS3, key, contentType);
      return upload;
    } catch (err) {
      throw new BadRequestException(err);
    }
  }

  async get(key: string) {
    try {
      const bucketS3 = process.env.AWS_BUCKET_NAME;
      const upload = await this.getFileFromS3(bucketS3, key);
      return upload;
    } catch (err) {
      throw new BadRequestException(err);
    }
  }

  async getFileFromS3(bucket, key) {
    const s3 = this.getS3();
    const params = {
      Bucket: bucket,
      Key: key,
    };
    return new Promise((resolve, reject) => {
      s3.getObject(params, (err, data) => {
        if (err) {
          console.error(err);
          reject(err.message);
        }
        resolve(data);
      });
    });
  }

  async uploadS3(file: Buffer, bucket, key, contentType) {
    const s3 = this.getS3();
    const params = {
      Bucket: bucket,
      Key: key,
      Body: file,
      ContentType: contentType,
    };
    return new Promise((resolve, reject) => {
      s3.upload(params, (err, data) => {
        if (err) {
          console.error(err);
          reject(err.message);
        }
        resolve(data);
      });
    });
  }

  getS3() {
    return new S3({
      accessKeyId: process.env.AWS_ACCESS_KEY_ID_VIDER,
      secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY_VIDER,
      region: process.env.AWS_REGION,
    });
  }

  async copyS3Object(sourceKey: string, newKey: string) {
    try {
      const bucketS3 = process.env.AWS_BUCKET_NAME;
      const s3 = this.getS3();

      const copyParams = {
        Bucket: bucketS3,
        CopySource: `${bucketS3}/${sourceKey}`,
        Key: newKey
      };
      await new Promise((resolve, reject) => {
        s3.copyObject(copyParams, (err, data) => {
          if (err) {
            console.error(err);
            reject(err.message);
          };
          resolve(data);
        });
      });
      return { success: true, newKey };
    } catch (err) {
      throw new BadRequestException(err);
    }
  }

  async delete(key: string) {
    if (!key) return null;
    try {
      const s3 = this.getS3();
      const params = {
        Bucket: process.env.AWS_BUCKET_NAME,
        Key: key
      };
      return new Promise((resolve, reject) => {
        s3.deleteObject(params, (err, data) => {
          if (err) {
            console.error(err);
            reject(err.message);
          }
          resolve(data);
          return { success: true }
        })
      })
    } catch (err) {
      console.error(err);
      // throw new BadRequestException(err);
    }
  }
}
