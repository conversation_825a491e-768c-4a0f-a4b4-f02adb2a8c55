import {
  FeeType,
  PriorityEnum,
  RecurringFrequency,
} from 'src/modules/tasks/dto/types';

export interface TaskData {
  name: string;
  client: number;
  category: number;
  categoryName: string;
  subCategory: number;
  taskLeader: number;
  feeType: FeeType;
  feeAmount: number;
  description: string;
  members: number[];
  labels: number[];
  dueDate?: string | null;
  priority: PriorityEnum;
  financialYear: string;
  budgetedhours: string;
  bhallocation: string;
  frequency: RecurringFrequency;
  dueDay: number;
  customDates: Array<{ startDate: string; dueDate: string; expectedCompletionDate: string }>;
  checklists: Array<any>;
  milestones: Array<any>;
  stageOfWorks: Array<any>;
  subTasks: Array<any>;
}
