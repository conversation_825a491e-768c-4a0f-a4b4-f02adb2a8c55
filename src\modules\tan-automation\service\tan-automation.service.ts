import { BadRequestException, Injectable, InternalServerErrorException } from '@nestjs/common';
import { User } from 'src/modules/users/entities/user.entity';
import TanClientCredentials from '../entity/tan-client-credentials.entity';
import { Brackets, create<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, get<PERSON>onnection, get<PERSON>anager, getRepository, <PERSON><PERSON>han } from 'typeorm';
import { Organization } from 'src/modules/organization/entities/organization.entity';
import { UserStatus } from 'src/modules/client-group/client-group.entity';
import {
  IncomeTaxStatus,
  syncStatus,
} from 'src/modules/automation/entities/aut_client_credentials.entity';
import Client from 'src/modules/clients/entity/client.entity';
import AutomationMachines from 'src/modules/automation/entities/automation_machines.entity';
import TanProfile from '../entity/tan-profile.entity';
import TanEChallan from '../entity/tan-e-challan.entity';
import TanIncomeTaxForms from '../entity/tan-income-tax-forms.entity';
import TanMyCas from '../entity/tan-my-cas.entity';
import { dateFormation } from 'src/utils/datesFormation';
import <PERSON><PERSON><PERSON><PERSON><PERSON> from '../entity/tan-key-person.entity';
import OrganizationPreferences from 'src/modules/organization-preferences/entity/organization-preferences.entity';
import Password, { IsExistingAtomPro } from 'src/modules/clients/entity/password.entity';
import { Permissions } from 'src/modules/tasks/permission';

import { createClientCredentials, createTraceClientCredentials, updateClientCredentials } from 'src/utils/atomProReUse';
import TanCommunicationInbox from '../entity/tan-communication-inbox.entity';
import * as ExcelJS from 'exceljs';
import {
  calculateAdvanceYr,
  calculateAssessmentYear,
  calculateAssessmentYear1,
} from 'src/utils/re-use';
import { formatDate, getTitle } from 'src/utils';
import * as moment from 'moment';
import { capitalize, orderBy } from 'lodash';
import TanTempEproFya from '../entity/tan_temp_epro_fya.entity';
import TanTempEproFyi from '../entity/tan_temp_epro_fyi.entity';
import TraceOutstandingDemand from '../entity/trace-outstanding-deman.entity';

const categoryLabels = {
  individual: 'Individual',
  huf: 'Hindu Undivided Family',
  partnership_firm: 'Partnership Firm',
  llp: 'Limited Liability Partnership',
  company: 'Company',
  opc: 'OPC',
  public: 'Public Limited',
  government: 'Government',
  sec_8: 'Section-8',
  foreign: 'Foreign',
  aop: 'Association of Persons',
  boi: 'Body of Individuals',
  trust: 'Trust',
  public_trust: 'Public Trust',
  private_discretionary_trust: 'Private Discretionary Trust',
  state: 'State',
  central: 'Central',
  local_authority: 'Local Authority',
  artificial_judicial_person: 'Artificial Juridical Person',
};

@Injectable()
export class TanAutomationService {
  async findAll(userId: number, query: any) {
    try {
      const { offset, limit, search } = query;
      const user = await User.findOne({ where: { id: userId }, relations: ['organization', 'role'] });
      let ViewAll = user.role.permissions.find(
        (permission) => permission.slug === Permissions.VIEW_ALL_CLIENT_MANAGERS,
      );
      let ViewAssigned = user.role.permissions.find(
        (permission) => permission.slug === Permissions.VIEW_ASSIGNED_CLIENT_MANAGERS,
      );
      const tanClients = createQueryBuilder(TanClientCredentials, 'tanClientCredentials')
        .leftJoinAndSelect('tanClientCredentials.client', 'client')
        .leftJoin('client.clientManagers', 'clientManagers')
        .where('tanClientCredentials.organizationId = :orgId', { orgId: user?.organization?.id })
        .andWhere('client.status != :status', { status: UserStatus.DELETED })
        .andWhere(
          new Brackets((qb) => {
            qb.where('tanClientCredentials.status IS NULL').orWhere(
              'tanClientCredentials.status = :enabledStatus',
              { enabledStatus: IncomeTaxStatus.ENABLE },
            );
          }),
        );

      if (search) {
        tanClients.andWhere(
          new Brackets((qb) => {
            qb.where('client.displayName LIKE :search', {
              search: `%${query.search}%`,
            });
            qb.orWhere('tanClientCredentials.tanNumber LIKE :search', {
              search: `%${query.search}%`,
            });
          }),
        );
      }

      if (ViewAssigned && !ViewAll) {
        tanClients.andWhere('clientManagers.id = :userId', { userId });
      }
      const sort = (typeof query?.sort === "string") ? JSON.parse(query.sort) : query?.sort;
      if (sort?.column) {
        const columnMap: Record<string, string> = {
          client: 'client.clientId',
          Category: 'client.category',
          displayName: 'client.displayName',
          tanNumber: 'tanClientCredentials.tanNumber',


        };
        const column = columnMap[sort.column] || sort.column;
        tanClients.orderBy(column, sort.direction.toUpperCase());
      } else {
        tanClients.orderBy('tanClientCredentials.createdAt', 'DESC');
      };
      if (offset >= 0) {
        tanClients.skip(offset);
      }

      if (limit) {
        tanClients.take(limit);
      }

      const result = await tanClients.getManyAndCount();
      return {
        count: result[1],
        data: result[0],
      };
    } catch (error) {
      console.log('error occur while getting tan clients', error);
    }
  }

  async exportIncomeTaxTanClients(userId: number, query: any) {
    const newQuery = { ...query, offset: 0, limit: ********* };
    let clients = await this.findAll(userId, newQuery);

    if (!clients.data.length) throw new BadRequestException('No Data for Export');

    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Inc Tax Clients');
    const headers = [
      { header: 'S.No', key: 'serialNo' },
      { header: 'Client ID', key: 'clientId' },
      { header: 'Category', key: 'category' },
      { header: 'Client Name', key: 'displayName' },
      { header: 'TAN', key: 'tan' },
      { header: 'Password', key: 'password' },
    ];

    worksheet.columns = headers;
    let serialCounter = 1; // Initialize a counter

    // Initialize array to store max lengths for each column
    const columnMaxLengths = Array(headers.length).fill(0);

    clients.data.forEach((client) => {
      const rowData = {
        serialNo: serialCounter++,
        clientId: client?.client?.clientId,
        category: getTitle(client?.client?.category),
        displayName: client.client.displayName,
        tan: client?.tanNumber,
        password: client?.password,
      };

      const row = worksheet.addRow(rowData);
      worksheet.columns.forEach((column, colIndex) => {
        const headerLength = column.header?.length || 0; // Length of header
        const cellLength = rowData[column.key]?.toString().length || 0; // Length of cell value
        columnMaxLengths[colIndex] = Math.max(
          columnMaxLengths[colIndex] || 0,
          headerLength,
          cellLength,
        );
      });
    });

    // Apply column widths with padding
    worksheet.columns.forEach((column, colIndex) => {
      column.width = columnMaxLengths[colIndex] + 3; // Add padding for readability
    });

    // Style the header row and center alignment
    const headerRow = worksheet.getRow(1);
    headerRow.font = { bold: true };
    headerRow.eachCell((cell) => {
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '64B5F6' },
      };
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' },
      };
      cell.alignment = { horizontal: 'center', vertical: 'middle' };
    });

    worksheet.columns.forEach((column) => {
      if (column.key === 'displayName') {
        column.width = 50;
        column.alignment = { wrapText: true, horizontal: 'center', vertical: 'middle' };
      } else {
        column.alignment = { horizontal: 'center', vertical: 'middle' };
      }
    });

    // Apply wrapText for all rows
    worksheet.eachRow((row) => {
      row.eachCell((cell) => {
        cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true }; // Center alignment and wrap text
      });
    });

    // Freeze the header row
    worksheet.views = [{ state: 'frozen', ySplit: 1 }];

    const buffer = await workbook.xlsx.writeBuffer();
    return buffer;
  }

  async addClientTanCredentials(userId: number, body: any) {
    try {
      let user = await User.findOne({
        where: { id: userId },
        relations: ['organization'],
      });

      const credential = await TanClientCredentials.findOne({
        where: { organizationId: user?.organization?.id, tanNumber: body?.tanNumber },
      });

      const organizationPreferences: any = await OrganizationPreferences.findOne({
        where: { organization: user.organization },
      });

      if (organizationPreferences) {
        const organizationLimit = organizationPreferences?.automationConfig?.tanLimit;
        if (organizationLimit) {
          const tanClientCredentialCount = await TanClientCredentials.count({
            where: { organizationId: user.organization.id, status: IncomeTaxStatus.ENABLE },
          });

          if (organizationLimit >= tanClientCredentialCount) {
            if (credential) {
              throw new BadRequestException(
                'Specified TAN Number Utilize your organization already',
              );
            } else {
              const client = await Client.findOne({ where: { id: body?.selectedClient?.id } });

              const tanPassword = (body?.password ?? '').trim();
              const traceUserId = (body?.traceUserId ?? '').trim();
              const tracePassword = (body?.tracePassword ?? '').trim();
              const details = {
                website: 'Income Tax | e-Filing (TAN)',
                websiteUrl: 'https://eportal.incometax.gov.in/iec/foservices/#/login',
                loginId: body?.tanNumber,
                password: body?.password === "" ? null : body?.password,
                client: client,
                isaddAtomPro: IsExistingAtomPro.YES,
                userId,
                traceUserId: body?.traceUserId,
                tracePassword: body?.tracePassword,
              };
              const traceDetails = {
                website: 'Income Tax | Traces (Tax Deductor)',
                websiteUrl: 'https://www.tdscpc.gov.in/app/login.xhtml?usr=Ded',
                loginId: body?.traceUserId === '' ? null : body?.traceUserId,
                password: body?.tracePassword === '' ? null : body?.tracePassword,
                tracesTan: body?.tanNumber,
                client: client,
                isaddAtomPro: IsExistingAtomPro.YES,
                userId,
              };

              let password = null;
              const passwordCheck = await Password.findOne({where:{client,website:details?.website},order:{createdAt:'DESC'}});
              if(passwordCheck){
                 password = await updateClientCredentials(details, passwordCheck?.id);
              }else{
                 password = await createClientCredentials(details);
              }

              const passwordTraceCheck = await Password.findOne({where:{client,website:traceDetails?.website},order:{createdAt:'DESC'}});

              if(passwordTraceCheck){
                 const tracePassword = await updateClientCredentials(traceDetails, passwordTraceCheck?.id);
              }else{
                const tracePassword: any = await createTraceClientCredentials(traceDetails);
              }

              const clientCredentials = new TanClientCredentials();
              clientCredentials.tanNumber = body?.tanNumber;
              clientCredentials.password = body?.password === "" ? null : body?.password;
              clientCredentials.client = client;
              clientCredentials.organizationId = user?.organization?.id;
              clientCredentials.passwordId = password?.id;
              clientCredentials.status = IncomeTaxStatus.ENABLE;
              clientCredentials.traceUserId = body?.traceUserId === '' ? null : body?.traceUserId;
              clientCredentials.tracePassword = body?.tracePassword === '' ? null : body?.tracePassword;
              await clientCredentials.save();
            }
          } else {
            throw new BadRequestException('Maximum Income tax TAN Client Count Reached');
          }
        }
      }
    } catch (error) {
      console.log('Error occur while add the incomeTax tan client credentials', error);
      throw new InternalServerErrorException(error);
    }
  }

  async updateClientTanCredentials(id: any, body: any, userId) {
    let queryRunner = getConnection().createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();
    try {
      const clientCredential = await queryRunner.manager.findOne(TanClientCredentials, {
        where: { id },
        relations: ['client'],
      });
      if (clientCredential.passwordId) {
        const password = await queryRunner.manager.findOne(Password, {
          where: { id: clientCredential.passwordId },
          relations: ['client'],
        });

        password.password = body.password;
        password['userId'] = userId;
        await queryRunner.manager.save(password);

        const tracePassword = await queryRunner.manager.findOne(Password, {
          where: { client: password.client, tracesTan: password.loginId },
          relations: ['client'],
        });

        if (tracePassword) {
          tracePassword.loginId = body.traceUserId;
          tracePassword.password = body.tracePassword;
          tracePassword['userId'] = userId;
          await queryRunner.manager.save(tracePassword);
        } else {
          throw new BadRequestException('Traces Not Found with this TAN');
        }
      }
      if (clientCredential) {
        clientCredential.password = body.password;
        clientCredential.traceUserId = body.traceUserId;
        clientCredential.tracePassword = body.tracePassword;
        await queryRunner.manager.save(clientCredential);

        await queryRunner.commitTransaction();
        return clientCredential;
      }
    } catch (error) {
      await queryRunner.rollbackTransaction();
      console.log('Error occured while updating the income Tax TAN client credentials', error);
      throw error;
    } finally {
      await queryRunner.release();
    }
  }
  async getAllClients(userId: number, data: any) {
    let user = await User.findOne({
      where: { id: userId },
      relations: ['organization'],
    });

    const entityManager = getManager();
    let query = `
    SELECT id, display_name as displayName, status
    FROM client
    WHERE organization_id = ${user?.organization.id}
    AND status != 'DELETED'
    AND id NOT IN (
        SELECT client_id
        FROM tan_client_credentials
        WHERE organization_id = ${user?.organization.id}
        AND client_id IS NOT NULL  
    )
    `;

    if (data?.search) {
      query += ` AND display_name LIKE '%${data?.search}%'`;
    }
    if (data?.limit) {
      query += ` LIMIT ${data?.limit}`;
      if (data?.page) {
        query += ` OFFSET ${data?.page}`;
      }
    }

    let clients = await entityManager.query(query);
    return clients;
  }

  async getIncomeTaxProfile(userId: number, id: number) {
    try {
      const user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
      const checkClientCredentials = await TanClientCredentials.findOne({
        where: { id: id, organizationId: user.organization.id },
      });
      if (checkClientCredentials) {
        const checkStatus = await AutomationMachines.findOne({
          where: { autoCredentials: id, status: 'PENDING' },
        });

        if (checkStatus) {
          // console.log(checkStatus);
        }

        const lastCompletedMachine = await AutomationMachines.findOne({
          where: { tanCredentials: id, status: 'COMPLETED', type: 'TAN' },
          order: {
            id: 'DESC',
          }, // Assuming you have a createdAt field indicating creation timestamp
        });

        const lastCompletedTracesMachine = await AutomationMachines.findOne({
          where: { tanCredentials: id, status: 'COMPLETED', type: 'TRACES' },
          order: {
            id: 'DESC',
          }, // Assuming you have a createdAt field indicating creation timestamp
        });
        try {
          const clientCredential = await TanClientCredentials.findOne({
            where: { id },
            relations: ['client'],
          });

          if (clientCredential) {
            const profileDetails = await TanProfile.findOne({
              where: { clientId: clientCredential?.client?.id },
            });
            const keyPersonDetails = await TanKeyPerson.find({
              where: { clientId: clientCredential?.client?.id },
            });
            return {
              profileDetails,
              lastCompletedMachine,
              checkClientCredentials: true,
              keyPersonDetails,
              clientCredential,
              lastCompletedTracesMachine
            };
          }
        } catch (error) {
          console.log('error occured while fetching income tax client credentials', error);
        }
      } else {
        {
          checkClientCredentials;
        }
      }
    } catch (error) {
      console.log('error occure while getting getIncomeTaxProfile', error);
    }
  }

  async clientEChallan(userId: number, query: any, id: number) {
    const user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
    const { limit, offset, search, assessmentYear, sortValue } = query;
    try {
      const clientCredential = await TanClientCredentials.findOne({
        where: { id, organizationId: user?.organization?.id },
        relations: ['client'],
      });

      if (clientCredential) {
        const challanDetails = createQueryBuilder(TanEChallan, 'tanEchallan')
          .select([
            'tanEchallan.id',
            'tanEchallan.assessmentYear',
            'tanEchallan.acin',
            'tanEchallan.minorDesc',
            'tanEchallan.minorHead',
            'tanEchallan.totalAmt',
            'tanEchallan.paymentTime',
            'tanEchallan.basicTax',
            'tanEchallan.surCharge',
            'tanEchallan.eduCess',
            'tanEchallan.interest',
            'tanEchallan.penalty',
            'tanEchallan.others',
            'tanEchallan.natureOfPayment',
            'client.id',
            'client.displayName',
            'tanClientCredentials.id',
          ])
          .leftJoin('tanEchallan.tanClientCredentials', 'tanClientCredentials')
          .leftJoin('tanClientCredentials.client', 'client')
          .where('tanClientCredentials.id =:id', { id: id });

        challanDetails.orderBy('tanEchallan.paymentTime', 'DESC');

        if (search) {
          challanDetails.andWhere(
            new Brackets((qb) => {
              qb.where('tanEchallan.acin LIKE :acin', {
                acin: `%${search}%`,
              });
              qb.orWhere('tanEchallan.minorDesc LIKE :minorDesc', {
                minorDesc: `%${search}%`,
              });
              qb.orWhere('tanEchallan.minorHead LIKE :minorHead', {
                minorHead: `%${search}%`,
              });
              qb.orWhere('tanEchallan.natureOfPayment LIKE :natureOfPayment', {
                natureOfPayment: `%${search}%`,
              });
            }),
          );
        }

        if (assessmentYear) {
          if (assessmentYear === 'null') {
            challanDetails.andWhere('tanEchallan.assessmentYear = 0');
          } else {
            challanDetails.andWhere('tanEchallan.assessmentYear like :as', {
              as: `%${assessmentYear}%`,
            });
          }
        }

        if (sortValue) {
          switch (sortValue) {
            case 'AMOUNT_DESC':
              challanDetails.orderBy('tanEchallan.totalAmt', 'DESC');
              break;
            case 'AMOUNT_ASC':
              challanDetails.orderBy('tanEchallan.totalAmt', 'ASC');
              break;
            case 'DATE_NEWEST':
              challanDetails.orderBy('tanEchallan.paymentTime', 'DESC');
              break;
            case 'DATE_OLDEST':
              challanDetails.orderBy('tanEchallan.paymentTime', 'ASC');
              break;
            default:
              break;
          }
        }

        const sort = (typeof query?.sort === "string") ? JSON.parse(query.sort) : query?.sort;
        if (sort?.column) {
          const columnMap: Record<string, string> = {
            id: 'tanEchallan.assessmentYear',
            minorDesc: 'tanEchallan.minorDesc',
            natureOfPayment: 'tanEchallan.natureOfPayment',
            paymentTime: 'tanEchallan.paymentTime',
            acin: 'tanEchallan.acin',
            totalAmt: 'tanEchallan.totalAmt'
          };
          const column = columnMap[sort.column] || sort.column;
          challanDetails.orderBy(column, sort.direction.toUpperCase());
        } else {
          challanDetails.orderBy('tanEchallan.paymentTime', 'DESC');
        };
        if (offset) {
          challanDetails.skip(offset);
        }

        if (limit) {
          challanDetails.take(limit);
        }

        let result = await challanDetails.getManyAndCount();
        return {
          count: result[1],
          result: result[0],
          accessDenied: true,
        };
      } else {
        return { accessDenied: false };
      }
    } catch (error) {
      console.log('error occured while fetching income tax client e-Challan in TAN', error);
    }
  }

  async exportTanClientChallan(userId: number, query: any) {
    const newQuery = { ...query, offset: 0, limit: ********* };
    const id = query.incometaxid;
    let clients = await this.clientEChallan(userId, newQuery, id);

    if (!clients.result.length) throw new BadRequestException('No Data for Export');

    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Inc Tax Challans');
    const headers = [
      { header: 'S.No', key: 'serialNo' },
      { header: 'AY', key: 'ay' },
      { header: 'Type of Payment', key: 'typeOfPayment' },
      { header: 'Nature of Payment', key: 'natureOfPayment' },
      { header: 'Date of Payment', key: 'dateOfPayment' },
      { header: 'Alternate CIN', key: 'cin' },
      { header: 'BSR Code', key: 'bsr' },
      { header: 'Challan #', key: 'challan' },
      { header: 'Tax (₹)', key: 'tax' },
      { header: 'Surcharge (₹)', key: 'surCharge' },
      { header: 'Cess (₹)', key: 'cess' },
      { header: 'Interest (₹)', key: 'interest' },
      { header: 'Penality (₹)', key: 'penalty' },
      { header: 'Others (₹)', key: 'others' },
      { header: 'Total (₹)', key: 'total' },
    ];

    worksheet.columns = headers;
    let serialCounter = 1; // Initialize a counter

    // Initialize array to store max lengths for each column
    const columnMaxLengths = Array(headers.length).fill(0);

    clients.result.forEach((client) => {
      const rowData = {
        serialNo: serialCounter++,
        ay: client?.assessmentYear,
        typeOfPayment: client?.minorDesc,
        dateOfPayment: formatDate(client?.paymentTime),
        natureOfPayment: client?.natureOfPayment,
        cin: client?.acin,
        bsr: client?.acin?.slice(0, 7),
        challan: client?.acin?.toString().slice(-5),
        tax: client?.basicTax,
        surCharge: client?.surCharge,
        cess: client?.eduCess,
        interest: client?.interest,
        penalty: client?.penalty,
        others: client?.others,
        total: client?.totalAmt,
      };

      const row = worksheet.addRow(rowData);
      worksheet.columns.forEach((column, colIndex) => {
        const headerLength = column.header?.length || 0; // Length of header
        const cellLength = rowData[column.key]?.toString().length || 0; // Length of cell value
        columnMaxLengths[colIndex] = Math.max(
          columnMaxLengths[colIndex] || 0,
          headerLength,
          cellLength,
        );
      });
    });

    // Apply column widths with padding
    worksheet.columns.forEach((column, colIndex) => {
      column.width = columnMaxLengths[colIndex] + 3; // Add padding for readability
    });

    // Style the header row and center alignment
    const headerRow = worksheet.getRow(1);
    headerRow.font = { bold: true };
    headerRow.eachCell((cell) => {
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '64B5F6' },
      };
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' },
      };
      cell.alignment = { horizontal: 'center', vertical: 'middle' };
    });

    worksheet.columns.forEach((column) => {
      if (column.key === 'displayName') {
        column.width = 50;
        column.alignment = { wrapText: true, horizontal: 'center', vertical: 'middle' };
      } else {
        column.alignment = { horizontal: 'center', vertical: 'middle' };
      }
    });

    // Apply wrapText for all rows
    worksheet.eachRow((row) => {
      row.eachCell((cell) => {
        cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true }; // Center alignment and wrap text
      });
    });

    // Freeze the header row
    worksheet.views = [{ state: 'frozen', ySplit: 1 }];

    const buffer = await workbook.xlsx.writeBuffer();
    return buffer;
  }

  async findEchallan(userId: number, id: number) {
    try {
      const user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
      const incomeTaxEchallan = await TanEChallan.findOne({
        where: { id, organizationId: user?.organization?.id },
      });
      return incomeTaxEchallan;
    } catch (error) {
      console.log('error occur while getting findForm', error);
    }
  }

  async findEchallans(userId: number, query: any) {
    try {
      const { limit, offset, assessmentYear, clientCategory, sortValue, search } = query;
      let user = await User.findOne({
        where: { id: userId },
        relations: ['organization', 'role'],
      });

      let ViewAll = user.role.permissions.find(
        (permission) => permission.slug === Permissions.VIEW_ALL_CLIENT_MANAGERS,
      );
      let ViewAssigned = user.role.permissions.find(
        (permission) => permission.slug === Permissions.VIEW_ASSIGNED_CLIENT_MANAGERS,
      );

      let eChallans = createQueryBuilder(TanEChallan, 'tanEChallan')
        .select([
          'tanEChallan.id',
          'tanEChallan.tan',
          'tanEChallan.assessmentYear',
          'tanEChallan.acin',
          'tanEChallan.minorDesc',
          'tanEChallan.minorHead',
          'tanEChallan.totalAmt',
          'tanEChallan.paymentTime',
          'tanEChallan.basicTax',
          'tanEChallan.surCharge',
          'tanEChallan.eduCess',
          'tanEChallan.interest',
          'tanEChallan.penalty',
          'tanEChallan.others',
          'tanEChallan.natureOfPayment',
          'client.id',
          'client.displayName',
          'tanClientCredentials.id',
          'clientManagers.id'
        ])
        .leftJoin('tanEChallan.tanClientCredentials', 'tanClientCredentials')
        .leftJoin('tanClientCredentials.client', 'client')
        .leftJoin('client.clientManagers', 'clientManagers')
        .where('tanEChallan.organizationId = :id', { id: user?.organization?.id })
        .andWhere('client.status != :status', { status: UserStatus.DELETED })
        .andWhere('tanClientCredentials.status != :disStatus', {
          disStatus: IncomeTaxStatus.DISABLE,
        });
      const sort = (typeof query?.sort === "string") ? JSON.parse(query.sort) : query?.sort;
      if (sort?.column) {
        const columnMap: Record<string, string> = {
          id: 'tanEChallan.assessmentYear',
          name: 'client.displayName',
          minorDesc: 'tanEChallan.minorDesc',
          natureOfPayment: 'tanEChallan.natureOfPayment',
          paymentTime: 'tanEChallan.paymentTime',
          acin: 'tanEChallan.acin',
          totalAmt: 'tanEChallan.totalAmt'
        };
        const column = columnMap[sort.column] || sort.column;
        eChallans.orderBy(column, sort.direction.toUpperCase());
      } else {
        eChallans.orderBy('tanEChallan.paymentTime', 'DESC');
      };
      // eChallans.orderBy('tanEChallan.paymentTime', 'DESC');


      if (ViewAssigned && !ViewAll) {
        eChallans.andWhere('clientManagers.id = :userId', { userId });
      }

      if (search) {
        eChallans.andWhere(
          new Brackets((qb) => {
            qb.where('tanEChallan.tan LIKE :pansearch', {
              pansearch: `%${search}%`,
            });
            qb.orWhere('client.displayName LIKE :namesearch', {
              namesearch: `%${search}%`,
            });
          }),
        );
      }

      if (assessmentYear) {
        if (assessmentYear === 'null') {
          eChallans.andWhere('tanEChallan.assessmentYear = 0');
        } else {
          eChallans.andWhere('tanEChallan.assessmentYear like :as', { as: `%${assessmentYear}%` });
        }
      }

      if (clientCategory) {
        eChallans.andWhere(`tanEChallan.minorDesc like :clientCategory`, {
          clientCategory: clientCategory,
        });
      }

      if (sortValue) {
        switch (sortValue) {
          case 'AMOUNT_DESC':
            eChallans.orderBy('tanEChallan.totalAmt', 'DESC');
            break;
          case 'AMOUNT_ASC':
            eChallans.orderBy('tanEChallan.totalAmt', 'ASC');
            break;
          case 'DATE_NEWEST':
            eChallans.orderBy('tanEChallan.paymentTime', 'DESC');
            break;
          case 'DATE_OLDEST':
            eChallans.orderBy('tanEChallan.paymentTime', 'ASC');
            break;
          default:
            break;
        }
      }

      if (offset >= 0) {
        eChallans.skip(offset);
      }

      if (limit) {
        eChallans.take(limit);
      }

      let result = await eChallans.getManyAndCount();
      return {
        count: result[1],
        result: result[0],
      };
    } catch (error) {
      console.log('error occured while fetching getIncomeTaxEChallans in TAN', error);
    }
  }
  async exportTanIncomeTaxChallans(userId: number, query: any) {
    const newQuery = { ...query, offset: 0, limit: ********* };
    let clients = await this.findEchallans(userId, newQuery);

    if (!clients.result.length) throw new BadRequestException('No Data for Export');

    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Inc Tax Challans');
    const headers = [
      { header: 'S.No', key: 'serialNo' },
      { header: 'AY', key: 'ay' },
      { header: 'Type of Payment', key: 'typeOfPayment' },
      { header: 'Nature of Payment', key: 'natureOfPayment' },
      { header: 'Date of Payment', key: 'dateOfPayment' },
      { header: 'Alternate CIN', key: 'cin' },
      { header: 'BSR Code', key: 'bsr' },
      { header: 'Challan #', key: 'challan' },
      { header: 'Tax (₹)', key: 'tax' },
      { header: 'Surcharge (₹)', key: 'surCharge' },
      { header: 'Cess (₹)', key: 'cess' },
      { header: 'Interest (₹)', key: 'interest' },
      { header: 'Penality (₹)', key: 'penalty' },
      { header: 'Others (₹)', key: 'others' },
      { header: 'Total (₹)', key: 'total' },
    ];

    worksheet.columns = headers;
    let serialCounter = 1; // Initialize a counter

    // Initialize array to store max lengths for each column
    const columnMaxLengths = Array(headers.length).fill(0);

    clients.result.forEach((client) => {
      const rowData = {
        serialNo: serialCounter++,
        ay: client?.assessmentYear,
        typeOfPayment: client?.minorDesc,
        dateOfPayment: formatDate(client?.paymentTime),
        natureOfPayment: client?.natureOfPayment,
        cin: client?.acin,
        bsr: client?.acin?.slice(0, 7),
        challan: client?.acin?.toString().slice(-5),
        tax: client?.basicTax,
        surCharge: client?.surCharge,
        cess: client?.eduCess,
        interest: client?.interest,
        penalty: client?.penalty,
        others: client?.others,
        total: client?.totalAmt,
      };

      const row = worksheet.addRow(rowData);
      worksheet.columns.forEach((column, colIndex) => {
        const headerLength = column.header?.length || 0; // Length of header
        const cellLength = rowData[column.key]?.toString().length || 0; // Length of cell value
        columnMaxLengths[colIndex] = Math.max(
          columnMaxLengths[colIndex] || 0,
          headerLength,
          cellLength,
        );
      });
    });

    // Apply column widths with padding
    worksheet.columns.forEach((column, colIndex) => {
      column.width = columnMaxLengths[colIndex] + 3; // Add padding for readability
    });

    // Style the header row and center alignment
    const headerRow = worksheet.getRow(1);
    headerRow.font = { bold: true };
    headerRow.eachCell((cell) => {
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '64B5F6' },
      };
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' },
      };
      cell.alignment = { horizontal: 'center', vertical: 'middle' };
    });

    worksheet.columns.forEach((column) => {
      if (column.key === 'displayName') {
        column.width = 50;
        column.alignment = { wrapText: true, horizontal: 'center', vertical: 'middle' };
      } else {
        column.alignment = { horizontal: 'center', vertical: 'middle' };
      }
    });

    // Apply wrapText for all rows
    worksheet.eachRow((row) => {
      row.eachCell((cell) => {
        cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true }; // Center alignment and wrap text
      });
    });

    // Freeze the header row
    worksheet.views = [{ state: 'frozen', ySplit: 1 }];

    const buffer = await workbook.xlsx.writeBuffer();
    return buffer;
  }
  async findForm(userId: number, id: number) {
    try {
      const user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
      const incomeTaxForm = await TanIncomeTaxForms.findOne({
        where: { id, organizationId: user?.organization?.id },
        relations: ['tanClientCredentials', 'tanClientCredentials.tanProfile'],
      });
      let myCaDetails;
      if (incomeTaxForm?.caMembershipNo) {
        myCaDetails = await TanMyCas.findOne({
          where: { caMembershipNum: incomeTaxForm?.caMembershipNo },
        });
      }
      return { ...incomeTaxForm, myCaDetails };
    } catch (error) {
      console.log('error occur while getting findForm in TAN', error);
    }
  }

  async getClientform(id: number, query: any, userId: number) {
    const user = await User.findOne({ where: { id: userId }, relations: ['organization', 'role'] });
    const { limit, offset } = query;
    try {
      const clientCredential = await TanClientCredentials.findOne({
        where: { id, organizationId: user?.organization?.id },
        relations: ['client'],
      });

      if (clientCredential) {
        const formDetails = createQueryBuilder(TanIncomeTaxForms, 'tanIncomeTaxForms')
          .leftJoinAndSelect('tanIncomeTaxForms.tanClientCredentials', 'tanClientCredentials')
          .leftJoinAndSelect('tanClientCredentials.client', 'client')

          .where('tanClientCredentials.id =:id', { id: id });

        if (offset) {
          formDetails.skip(offset);
        }

        if (limit) {
          formDetails.take(limit);
        }
        const sort = (typeof query?.sort === "string") ? JSON.parse(query.sort) : query?.sort;
        if (sort?.column) {
          const columnMap: Record<string, string> = {
            id: 'tanIncomeTaxForms.refYear',
            financialQuarter: 'tanIncomeTaxForms.financialQuarter',
            formDesc: 'tanIncomeTaxForms.formDesc',
            ackDt: 'tanIncomeTaxForms.ackDt',
            tempAckNo: 'tanIncomeTaxForms.tempAckNo',
            filingTypeCd: 'tanIncomeTaxForms.filingTypeCd',


          };
          const column = columnMap[sort.column] || sort.column;
          formDetails.orderBy(column, sort.direction.toUpperCase());
        } else {
          // formDetails.orderBy('tanIncomeTaxForms.createdAt', 'DESC');
        };

        formDetails.addSelect(
          `COALESCE(
            STR_TO_DATE(NULLIF(tanIncomeTaxForms.ackDt, 'NA'), '%d-%b-%Y'),'0000-01-01'
          )`,
          'issueDateOrder',
        );

        formDetails.addOrderBy('issueDateOrder', 'DESC');

        let result = await formDetails.getManyAndCount();
        return {
          count: result[1],
          result: result[0],
          accessDenied: true,
        };
      } else {
        return { accessDenied: false };
      }
    } catch (error) {
      console.log('error occured while fetching income tax client forms in TAN', error);
    }
  }

  async exportTanClientForm(userId: number, query: any) {
    const id = query.incometaxid;
    let forms: any = await this.getClientform(id, query, userId);

    if (!forms.result.length) throw new BadRequestException('No Data for Export');

    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Inc Tax Forms');
    const headers = [
      { header: 'S.No', key: 'serialNo' },
      { header: 'FY', key: 'fy' },
      { header: 'AY', key: 'ay' },
      { header: 'Filing Type', key: 'filingType' },
      { header: 'Form Name', key: 'formName' },
      { header: 'Acknowledgement #', key: 'acknowledgeNum' },
      { header: 'Date of Filing', key: 'dateOfFiling' },
      { header: 'UDIN', key: 'udin' },
    ];

    worksheet.columns = headers;

    let serialCounter = 1; // Initialize a counter

    // Initialize array to store max lengths for each column
    const columnMaxLengths = Array(headers.length).fill(0);

    forms.result.forEach((form) => {
      const rowData = {
        serialNo: serialCounter++, // Assign and then increment the counter
        fy: calculateAssessmentYear(form?.refYear),
        ay: calculateAdvanceYr(form?.refYear),
        filingType: form?.filingTypeCd,
        formName: form?.formDesc,
        acknowledgeNum: form?.ackNum,
        dateOfFiling: form?.ackDt,
        udin: form?.udinNum,
      };

      const row = worksheet.addRow(rowData);
      worksheet.columns.forEach((column, colIndex) => {
        const headerLength = column.header?.length || 0; // Length of header
        const cellLength = rowData[column.key]?.toString().length || 0; // Length of cell value
        columnMaxLengths[colIndex] = Math.max(
          columnMaxLengths[colIndex] || 0,
          headerLength,
          cellLength,
        );
      });
    });

    // Apply column widths with padding
    worksheet.columns.forEach((column, colIndex) => {
      column.width = columnMaxLengths[colIndex] + 3; // Add padding for readability
    });

    // Style the header row and center alignment
    const headerRow = worksheet.getRow(1);
    headerRow.font = { bold: true };
    headerRow.eachCell((cell) => {
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '64B5F6' },
      };
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' },
      };
      cell.alignment = { horizontal: 'center', vertical: 'middle' };
    });

    worksheet.columns.forEach((column) => {
      if (column.key === 'taskName' || column.key === 'members' || column.key === 'taskLeaders') {
        column.width = 50;
        column.alignment = { wrapText: true, horizontal: 'center', vertical: 'middle' };
      } else {
        column.alignment = { horizontal: 'center', vertical: 'middle' };
      }
    });

    // Apply wrapText for all rows
    worksheet.eachRow((row) => {
      row.eachCell((cell) => {
        cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true }; // Center alignment and wrap text
      });
    });

    // Freeze the header row
    worksheet.views = [{ state: 'frozen', ySplit: 1 }];

    const buffer = await workbook.xlsx.writeBuffer();
    return buffer;
  }

  async findAllForms(userId: number, query: any) {
    try {
      const { limit, offset } = query;
      let user = await User.findOne({
        where: { id: userId },
        relations: ['organization', 'role'],
      });

      let ViewAll = user.role.permissions.find(
        (permission) => permission.slug === Permissions.VIEW_ALL_CLIENT_MANAGERS,
      );
      let ViewAssigned = user.role.permissions.find(
        (permission) => permission.slug === Permissions.VIEW_ASSIGNED_CLIENT_MANAGERS,
      );

      let forms = createQueryBuilder(TanIncomeTaxForms, 'forms')
        .leftJoinAndSelect('forms.tanClientCredentials', 'tanClientCredentials')
        .leftJoinAndSelect('tanClientCredentials.client', 'client')
        .leftJoin('client.clientManagers', 'clientManagers')
        .where('forms.organizationId = :id', { id: user?.organization?.id })
        .andWhere('client.status != :status', { status: UserStatus.DELETED })
        .andWhere('tanClientCredentials.status != :disStatus', {
          disStatus: IncomeTaxStatus.DISABLE,
        });

      if (ViewAssigned && !ViewAll) {
        forms.andWhere('clientManagers.id = :userId', { userId });
      }

      if (query.search) {
        forms.andWhere('client.displayName LIKE :search', { search: `%${query.search}%` });
        forms.orWhere('tanClientCredentials.tanNumber LIKE :search', {
          search: `%${query.search}%`,
        });
      }

      if (query.formCode) {
        forms.andWhere('forms.formDesc = :formCd', {
          formCd: query.formCode,
        });
      }

      if (query.filingType) {
        if (query.filingType === 'Original/Regular') {
          forms.andWhere('forms.filingTypeCd IN (:...filingType)', {
            filingType: ['Original', 'Regular'],
          });
        } else if (query.filingType === 'Revised/Correction') {
          forms.andWhere('forms.filingTypeCd IN (:...filingType)', {
            filingType: ['Revised', 'Correction'],
          });
        }
      }

      if (query.financialQuarter) {
        if(query.financialQuarter === "NA"){
          forms.andWhere('forms.financialQuarter = :finQ',{
            finQ: 'Not applicable'
          });
        }else{
          forms.andWhere('forms.financialQuarter = :finQuarter', {
            finQuarter: query.financialQuarter,
          });
        }
      }

      if (query.financialYear) {
        forms.andWhere('forms.refYear = :finY AND forms.refYearType = :financialYearType', {
          finY: query.financialYear,
          financialYearType: 'FY',
        });
      }

      if (query.clientCategory) {
        forms.andWhere(`client.category = :clientCategory`, {
          clientCategory: query.clientCategory,
        });
      }

      if (query.udinStat) {
        switch (query.udinStat) {
          case 'UDIN_APPLICABLE':
            forms.andWhere('forms.isUdinApplicable is true');
            break;
          case 'UDIN_NOT_APPLICABLE':
            forms.andWhere('forms.isUdinApplicable is false');
            break;
          case 'UDIN_COMPLETED':
            forms.andWhere('forms.udinNum is not null');
            break;
          case 'UDIN_PENDING':
            forms.andWhere('forms.udinNum is null').andWhere('forms.isUdinApplicable is true');
          default:
            break;
        }
      }
      const sort = (typeof query?.sort === "string") ? JSON.parse(query.sort) : query?.sort;
      if (sort?.column) {
        const columnMap: Record<string, string> = {
          id: 'forms.refYear',
          financialQuarter: 'forms.financialQuarter',
          forms: 'client.displayName',
          formDesc: 'forms.formDesc',
          ackDt: 'forms.ackDt',
          tempAckNo: 'forms.tempAckNo',
          filingTypeCd: 'forms.filingTypeCd',


        };
        const column = columnMap[sort.column] || sort.column;
        forms.orderBy(column, sort.direction.toUpperCase());
      } else {
        // forms.orderBy('forms.createdAt', 'DESC');
      };
      if (offset >= 0) {
        forms.skip(offset);
      }

      if (limit) {
        forms.take(limit);
      }

      forms.addSelect(
        `COALESCE(
          STR_TO_DATE(NULLIF(forms.ackDt, 'NA'), '%d-%b-%Y'),'0000-01-01'
        )`,
        'issueDateOrder',
      );
      forms.addOrderBy('issueDateOrder', 'DESC');

      let result = await forms.getManyAndCount();
      return {
        count: result[1],
        result: result[0],
      };
    } catch (error) {
      console.log('error occur while getting findAll', error);
    }
  }
  async exportTanIncomeTaxForms(userId: number, query: any) {
    const id = query.incometaxid;
    let forms: any = await this.findAllForms(userId, query);

    if (!forms.result.length) throw new BadRequestException('No Data for Export');

    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Inc Tax Forms');
    const headers = [
      { header: 'S.No', key: 'serialNo' },
      { header: 'FY', key: 'fy' },
      { header: 'AY', key: 'ay' },
      { header: 'Filing Type', key: 'filingType' },
      { header: 'Form Name', key: 'formName' },
      { header: 'Acknowledgement #', key: 'acknowledgeNum' },
      { header: 'Date of Filing', key: 'dateOfFiling' },
      { header: 'UDIN', key: 'udin' },
    ];

    worksheet.columns = headers;

    let serialCounter = 1; // Initialize a counter

    // Initialize array to store max lengths for each column
    const columnMaxLengths = Array(headers.length).fill(0);

    forms.result.forEach((form) => {
      const rowData = {
        serialNo: serialCounter++, // Assign and then increment the counter
        fy: calculateAssessmentYear(form?.refYear),
        ay: calculateAdvanceYr(form?.refYear),
        filingType: form?.filingTypeCd,
        formName: form?.formDesc,
        acknowledgeNum: form?.ackNum,
        dateOfFiling: form?.ackDt,
        udin: form?.udinNum,
      };

      const row = worksheet.addRow(rowData);
      worksheet.columns.forEach((column, colIndex) => {
        const headerLength = column.header?.length || 0; // Length of header
        const cellLength = rowData[column.key]?.toString().length || 0; // Length of cell value
        columnMaxLengths[colIndex] = Math.max(
          columnMaxLengths[colIndex] || 0,
          headerLength,
          cellLength,
        );
      });
    });

    // Apply column widths with padding
    worksheet.columns.forEach((column, colIndex) => {
      column.width = columnMaxLengths[colIndex] + 3; // Add padding for readability
    });

    // Style the header row and center alignment
    const headerRow = worksheet.getRow(1);
    headerRow.font = { bold: true };
    headerRow.eachCell((cell) => {
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '64B5F6' },
      };
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' },
      };
      cell.alignment = { horizontal: 'center', vertical: 'middle' };
    });

    worksheet.columns.forEach((column) => {
      if (column.key === 'taskName' || column.key === 'members' || column.key === 'taskLeaders') {
        column.width = 50;
        column.alignment = { wrapText: true, horizontal: 'center', vertical: 'middle' };
      } else {
        column.alignment = { horizontal: 'center', vertical: 'middle' };
      }
    });

    // Apply wrapText for all rows
    worksheet.eachRow((row) => {
      row.eachCell((cell) => {
        cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true }; // Center alignment and wrap text
      });
    });

    // Freeze the header row
    worksheet.views = [{ state: 'frozen', ySplit: 1 }];

    const buffer = await workbook.xlsx.writeBuffer();
    return buffer;
  }
  async getActivityLogData(id: any, query: any, userId: number) {
    try {
      const user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
      const clientCredential = await TanClientCredentials.findOne({
        where: { id, organizationId: user?.organization?.id },
        relations: ['client'],
      });
      if (clientCredential) {
        const autActivity = await createQueryBuilder(AutomationMachines, 'autActivity')
          .leftJoinAndSelect('autActivity.user', 'user')
          .where('autActivity.tanCredentials =:id', { id: id });
        autActivity.andWhere('autActivity.type =:type', { type: 'TAN' });

        if (query.fromDate && query.toDate) {
          const { startTime, endTime } = dateFormation(query.fromDate, query.toDate);
          autActivity
            .andWhere('Date(autActivity.created_at) >= :startTime', { startTime })
            .andWhere('Date(autActivity.created_at) <= :endTime', { endTime });
        }

        autActivity.orderBy('autActivity.id', 'DESC');
        let result = await autActivity.getMany();
        return { result, accessDenied: true };
      } else {
        return { accessDenied: false };
      }
    } catch (error) {
      console.error('Error fetching activity log data:', error);
      return null; // Or throw an error
    }
  }

  async getclientReport(userId: number, query: any) {
    try {
      const { limit, offset, status, remarks } = query;
      let user = await User.findOne({
        where: { id: userId },
        relations: ['organization', 'role'],
      });

      let ViewAll = user.role.permissions.find(
        (permission) => permission.slug === Permissions.VIEW_ALL_CLIENT_MANAGERS,
      );
      let ViewAssigned = user.role.permissions.find(
        (permission) => permission.slug === Permissions.VIEW_ASSIGNED_CLIENT_MANAGERS,
      );

      const entityManager = getRepository(AutomationMachines);

      let sql = await entityManager
        .createQueryBuilder('automationMachines')
        .leftJoinAndSelect('automationMachines.tanCredentials', 'tanCredentials')
        .leftJoinAndSelect('tanCredentials.client', 'client')
        // .leftJoin('client.clientManagers', 'clientManagers')
        .where('tanCredentials.organizationId = :id', { id: user.organization.id })
        .andWhere('client.status != :status', { status: UserStatus.DELETED })
        .andWhere('tanCredentials.status != :disStatus', { disStatus: IncomeTaxStatus.DISABLE });

      if (status) {
        sql = sql.andWhere('automationMachines.status = :status', { status });
      }

      // if (ViewAssigned && !ViewAll) {
      //   sql.andWhere('clientManagers.id = :userId', { userId });
      // }

      if (remarks) {
        sql = sql.andWhere('automationMachines.remarks = :remarks', { remarks });
      }

      sql = sql
        .andWhere((qb) => {
          const subQuery = qb
            .subQuery()
            .select('MAX(innerAutomationMachines.id)', 'maxId')
            .from(AutomationMachines, 'innerAutomationMachines')
            .leftJoin('innerAutomationMachines.tanCredentials', 'innerTanCredentials')
            .where('innerTanCredentials.organizationId = :id', { id: user.organization.id })
            .andWhere('innerAutomationMachines.type = :type', { type: 'TAN' })
            .groupBy('innerTanCredentials.id')
            .getQuery();
          return 'automationMachines.id IN ' + subQuery;
        })
        // .orderBy('automationMachines.id', 'DESC')
        .limit(limit)
        .offset(offset);
      const sort = (typeof query?.sort === "string") ? JSON.parse(query.sort) : query?.sort;
      if (sort?.column) {
        const columnMap: Record<string, string> = {
          id: 'client.clientId',
          displayName: 'client.displayName',
          tanNumber: 'tanCredentials.tanNumber',
          status: 'automationMachines.status',
          remarks: 'automationMachines.remarks',
          createdAt: 'automationMachines.createdAt'

        };
        const column = columnMap[sort.column] || sort.column;
        sql.orderBy(column, sort.direction.toUpperCase());
      } else {
        sql.orderBy('automationMachines.createdAt', 'DESC');
      };
      const result = await sql.getManyAndCount();
      return {
        data: result[0],
        count: result[1],
      };
    } catch (error) {
      console.log('error occur while getting  getclientReport', error);
    }
  }
  async exportTanSyncStatus(userId: number, query: any) {
    const id = query.incometaxId;
    let traces = await this.getclientReport(userId, query);

    if (!traces?.data?.length) throw new BadRequestException('No Data for Export');

    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Income Tax (TAN)');
    const headers = [
      { header: 'S.No', key: 'serialNo' },
      { header: 'Client Id', key: 'clientId' },
      { header: 'Client Name', key: 'clientName' },
      { header: 'TAN', key: 'tan' },
      { header: 'Password', key: 'password' },
      { header: 'Status', key: 'status' },
      { header: 'Remarks', key: 'remarks' },
      { header: 'Last Sync', key: 'lastSync' }
    ];

    worksheet.columns = headers;

    let serialCounter = 1; // Initialize a counter

    // Initialize array to store max lengths for each column
    const columnMaxLengths = Array(headers.length).fill(0);

    traces.data.forEach((trace) => {
      const rowData = {
        serialNo: serialCounter++, // Auto-incrementing S.No
        clientId: trace?.tanCredentials?.client?.clientId || '',
        clientName: trace?.tanCredentials?.client?.displayName || '',
        tan: trace?.tanCredentials?.tanNumber || '',
        password: trace?.tanCredentials?.password || " ",
        status: capitalize(trace?.status) || '',
        remarks: trace?.remarks || '',
        lastSync: trace?.createdAt ? moment(trace.createdAt).format("DD-MM-YYYY h:mm a") : '',
      };
      const row = worksheet.addRow(rowData);
      const statusCell = row.getCell('status');

      if (rowData.status === 'Pending') {
        statusCell.font = {
          color: { argb: '800080' }, // Purple
          bold: true,
        };
      } else if (rowData.status === 'Completed') {
        statusCell.font = {
          color: { argb: '228B22' }, // Green
          bold: true,
        };
      } else if (rowData.status === 'Inqueue') {
        statusCell.font = {
          color: { argb: 'FF8C00' }, // Orange
          bold: true,
        };
      }




      worksheet.columns.forEach((column, colIndex) => {
        const headerLength = column.header?.length || 0; // Length of header
        const cellLength = rowData[column.key]?.toString().length || 0; // Length of cell value
        columnMaxLengths[colIndex] = Math.max(
          columnMaxLengths[colIndex] || 0,
          headerLength,
          cellLength,
        );
      });
    });

    // Apply column widths with padding
    worksheet.columns.forEach((column, colIndex) => {
      column.width = columnMaxLengths[colIndex] + 3; // Add padding for readability
    });

    // Style the header row and center alignment
    const headerRow = worksheet.getRow(1);
    headerRow.font = { bold: true };
    headerRow.eachCell((cell) => {
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '64B5F6' },
      };
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' },
      };
      cell.alignment = { horizontal: 'center', vertical: 'middle' };
    });

    worksheet.columns.forEach((column) => {
      if (column.key === 'taskName') {
        column.width = 50;
        column.alignment = { wrapText: true, horizontal: 'center', vertical: 'middle' };
      } else {
        column.alignment = { horizontal: 'center', vertical: 'middle' };
      }
    });

    // Apply wrapText for all rows
    worksheet.eachRow((row) => {
      row.eachCell((cell) => {
        cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true }; // Center alignment and wrap text
      });
    });

    // Freeze the header row
    worksheet.views = [{ state: 'frozen', ySplit: 1 }];

    const buffer = await workbook.xlsx.writeBuffer();
    return buffer;
  }
  async exportTanIncomeTaxReports(userId: number, query: any) {
    let reports: any = await this.getclientReport(userId, query);

    if (!reports.data.length) throw new BadRequestException('No Data for Export');

    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Inc Tax Forms');
    const headers = [
      { header: 'S.No', key: 'serialNo' },
      { header: 'Client Name', key: 'clientName' },
      { header: 'TAN', key: 'tan' },
      { header: 'Password', key: 'password' },
      { header: 'Last Sync Time', key: 'lastSync' },
      { header: 'Status', key: 'status' },
      { header: 'Remarks', key: 'remarks' },
    ];

    worksheet.columns = headers;

    let serialCounter = 1; // Initialize a counter
    const columnMaxLengths = Array(headers.length).fill(0);

    reports.data.forEach((report) => {
      const rowData = {
        serialNo: serialCounter++, // Assign and then increment the counter
        clientName: report?.tanCredentials?.client?.displayName,
        tan: report?.tanCredentials?.tanNumber,
        password: report?.tanCredentials?.password,
        lastSync: report?.createdAt ? moment(report.createdAt).format('DD-MM-YYYY h:mm a') : null,
        status: report?.status,
        remarks: report?.remarks,
      };

      const row = worksheet.addRow(rowData);
      worksheet.columns.forEach((column, colIndex) => {
        const headerLength = column.header?.length || 0; // Length of header
        const cellLength = rowData[column.key]?.toString().length || 0; // Length of cell value
        columnMaxLengths[colIndex] = Math.max(
          columnMaxLengths[colIndex] || 0,
          headerLength,
          cellLength,
        );
      });
    });

    // Apply column widths with padding
    worksheet.columns.forEach((column, colIndex) => {
      column.width = columnMaxLengths[colIndex] + 3; // Add padding for readability
    });

    // Style the header row and center alignment
    const headerRow = worksheet.getRow(1);
    headerRow.font = { bold: true };
    headerRow.eachCell((cell) => {
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '64B5F6' },
      };
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' },
      };
      cell.alignment = { horizontal: 'center', vertical: 'middle' };
    });

    worksheet.columns.forEach((column) => {
      if (column.key === 'taskName' || column.key === 'members' || column.key === 'taskLeaders') {
        column.width = 50;
        column.alignment = { wrapText: true, horizontal: 'center', vertical: 'middle' };
      } else {
        column.alignment = { horizontal: 'center', vertical: 'middle' };
      }
    });

    // Apply wrapText for all rows
    worksheet.eachRow((row) => {
      row.eachCell((cell) => {
        cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true }; // Center alignment and wrap text
      });
    });

    // Freeze the header row
    worksheet.views = [{ state: 'frozen', ySplit: 1 }];

    const buffer = await workbook.xlsx.writeBuffer();
    return buffer;
  }

  async getclientAutoStatus(id: number, userId: number) {
    try {
      const user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
      const clientCredentials = await TanClientCredentials.findOne({
        where: { id, organizationId: user?.organization?.id },
      });

      if (clientCredentials) {
        const lastCompletedMachine = await AutomationMachines.findOne({
          where: { tanCredentials: id, type: 'TAN' },
          order: {
            id: 'DESC',
          },
          relations: ['tanCredentials', 'tanCredentials.client'],
        });
        let totalInqueueCount = 0;
        if (lastCompletedMachine?.status === 'INQUEUE') {
          totalInqueueCount = await AutomationMachines.count({
            where: { status: 'INQUEUE', type: 'TAN', id: LessThan(lastCompletedMachine.id) },
          });
        }
        return { lastCompletedMachine, accessDenied: true, totalInqueueCount };
      } else {
        return { accessDenied: false };
      }
    } catch (error) {
      console.log('error occur while getting getclientAutoStatus', error);
    }
  }

  async findMycas(userId: number, query: any) {
    try {
      const { limit, offset, assessmentYear, clientCategory, sortValue, search, filingValue } =
        query;
      let user = await User.findOne({
        where: { id: userId },
        relations: ['organization', 'role'],
      });
      let ViewAll = user.role.permissions.find(
        (permission) => permission.slug === Permissions.VIEW_ALL_CLIENT_MANAGERS,
      );
      let ViewAssigned = user.role.permissions.find(
        (permission) => permission.slug === Permissions.VIEW_ASSIGNED_CLIENT_MANAGERS,
      );
      let myCasDetails = createQueryBuilder(TanMyCas, 'tanMycas')
        .select([
          'tanMycas.id',
          'tanMycas.panNumber',
          'tanMycas.caName',
          'tanMycas.caMembershipNum',
          'tanMycas.caStatus',
          'tanMycas.assignedDate',
          'tanMycas.filingType',
          'tanMycas.filingStatus',
          'tanMycas.formTypeCd',
          'tanMycas.isWithdrawable',
          'tanMycas.formStatus',
          'tanMycas.transactionNo',
          'tanMycas.assessmentYear',
          'tanMycas.financialYear',
          'tanMycas.udinNumber',
          'client.id',
          'client.displayName',
          'tanClientCredentials.id',
          'tanClientCredentials.tanNumber',
        ])
        .leftJoin('tanMycas.tanClientCredentials', 'tanClientCredentials')
        .leftJoin('tanClientCredentials.client', 'client')
        .leftJoin('client.clientManagers', 'clientManagers')
        .where('tanMycas.organizationId = :id', { id: user?.organization?.id })
        .andWhere('client.status != :status', { status: UserStatus.DELETED })
        .andWhere('tanClientCredentials.status != :disStatus', {
          disStatus: IncomeTaxStatus.DISABLE,
        });

      myCasDetails.orderBy('tanMycas.assignedDate', 'DESC');

      if (search) {
        myCasDetails.andWhere(
          new Brackets((qb) => {
            qb.where('tanMycas.tanNumber LIKE :panSearch', {
              panSearch: `%${search}%`,
            });
            qb.orWhere('client.displayName LIKE :nameSearch', {
              nameSearch: `%${search}%`,
            });
            qb.orWhere('tanMycas.caName LIKE :caNameSearch', {
              caNameSearch: `%${search}%`,
            });
            qb.orWhere('tanMycas.caMembershipNum LIKE :membershipSearch', {
              membershipSearch: `%${search}%`,
            });
          }),
        );
      }

      if (assessmentYear) {
        if (assessmentYear === 'null') {
          myCasDetails.andWhere('tanMycas.assessmentYear = 0');
        } else {
          myCasDetails.andWhere('tanMycas.assessmentYear like :as', { as: `%${assessmentYear}%` });
        }
      }

      if (ViewAssigned && !ViewAll) {
        myCasDetails.andWhere('clientManagers.id = :userId', { userId });
      }

      if (clientCategory) {
        myCasDetails.andWhere(`tanMycas.formTypeCd like :clientCategory`, {
          clientCategory: clientCategory,
        });
      }

      if (sortValue) {
        switch (sortValue) {
          case 'DATE_NEWEST':
            myCasDetails.orderBy('tanMycas.assignedDate', 'DESC');
            break;
          case 'DATE_OLDEST':
            myCasDetails.orderBy('tanMycas.assignedDate', 'ASC');
            break;
          case 'UDIN_COMPLETED':
            myCasDetails.andWhere('tanMycas.udinNumber is not null');
            break;
          case 'UDIN_PENDING':
            myCasDetails.andWhere('tanMycas.udinNumber is null');
          default:
            break;
        }
      }

      if (filingValue) {
        if (filingValue === 'NA') {
          myCasDetails.andWhere('tanMycas.filingType IS NULL');
        } else {
          myCasDetails.andWhere('tanMycas.filingType LIKE :filingValue', {
            filingValue: `%${filingValue}%`,
          });
        }
      }

      if (offset >= 0) {
        myCasDetails.skip(offset);
      }

      if (limit) {
        myCasDetails.take(limit);
      }

      let result = await myCasDetails.getManyAndCount();
      return {
        count: result[1],
        result: result[0],
      };
    } catch (error) {
      console.log('error occured while fetching findMycas in TAN', error);
    }
  }
  async exportTanIncomeTaxMycas(userId: number, query: any) {
    let mycas: any = await this.findMycas(userId, query);

    if (!mycas.result.length) throw new BadRequestException('No Data for Export');

    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Inc Tax My CA');
    const headers = [
      { header: 'S.No', key: 'serialNo' },
      { header: 'AY', key: 'fy' },
      { header: 'CA Name', key: 'caName' },
      { header: 'CA Membership #', key: 'caMembership' },
      { header: 'Filing Type', key: 'filingType' },
      { header: 'Form Name', key: 'formName' },
      { header: 'Assigned Date', key: 'assaignedDate' },
      { header: 'Transaction ID', key: 'transactionId' },
      { header: 'UDIN', key: 'udin' },
    ];

    worksheet.columns = headers;
    let serialCounter = 1; // Initialize a counter
    // Initialize array to store max lengths for each column
    const columnMaxLengths = Array(headers.length).fill(0);

    mycas.result.forEach((myca) => {
      const rowData = {
        serialNo: serialCounter++, // Assign and then increment the counter
        fy: calculateAssessmentYear(myca?.refYear),
        caName: myca?.caName,
        caMembership: myca?.caMembershipNum,
        filingType: myca?.filingType,
        formName: myca?.formTypeCd,
        assaignedDate: formatDate(myca?.assaignedDate),
        transactionId: myca?.transactionNo,
        udin: myca?.udinNumber,
      };

      const row = worksheet.addRow(rowData);
      worksheet.columns.forEach((column, colIndex) => {
        const headerLength = column.header?.length || 0; // Length of header
        const cellLength = rowData[column.key]?.toString().length || 0; // Length of cell value
        columnMaxLengths[colIndex] = Math.max(
          columnMaxLengths[colIndex] || 0,
          headerLength,
          cellLength,
        );
      });
    });

    // Apply column widths with padding
    worksheet.columns.forEach((column, colIndex) => {
      column.width = columnMaxLengths[colIndex] + 3; // Add padding for readability
    });

    // Style the header row and center alignment
    const headerRow = worksheet.getRow(1);
    headerRow.font = { bold: true };
    headerRow.eachCell((cell) => {
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '64B5F6' },
      };
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' },
      };
      cell.alignment = { horizontal: 'center', vertical: 'middle' };
    });

    worksheet.columns.forEach((column) => {
      if (column.key === 'taskName') {
        column.width = 50;
        column.alignment = { wrapText: true, horizontal: 'center', vertical: 'middle' };
      } else {
        column.alignment = { horizontal: 'center', vertical: 'middle' };
      }
    });

    // Apply wrapText for all rows
    worksheet.eachRow((row) => {
      row.eachCell((cell) => {
        cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true }; // Center alignment and wrap text
      });
    });

    // Freeze the header row
    worksheet.views = [{ state: 'frozen', ySplit: 1 }];

    const buffer = await workbook.xlsx.writeBuffer();
    return buffer;
  }
  async getMycaFormTypes(userId: number) {
    try {
      let user = await User.findOne({
        where: { id: userId },
        relations: ['organization'],
      });

      const formTypeData = await createQueryBuilder(TanMyCas, 'tanMyCas')
        .select('tanMyCas.formTypeCd', 'formType')
        .where('tanMyCas.organizationId = :id', { id: user.organization.id })
        .groupBy('tanMyCas.formTypeCd')
        .getRawMany();

      const filteredSections = formTypeData
        .map((section) => section.formType)
        .filter((section) => section !== '' && section !== null);

      return filteredSections;
    } catch (error) {
      console.error('Error occurred while fetching form types in TAN:', error);
      throw error;
    }
  }

  async clientMycas(userId: number, query: any, id: number) {
    const user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
    const { limit, offset } = query;
    try {
      const clientCredential = await TanClientCredentials.findOne({
        where: { id, organizationId: user?.organization?.id },
        relations: ['client'],
      });

      if (clientCredential) {
        const myCasDetails = await createQueryBuilder(TanMyCas, 'tanMycas')
          .select([
            'tanMycas.id',
            'tanMycas.panNumber',
            'tanMycas.caName',
            'tanMycas.caMembershipNum',
            'tanMycas.caStatus',
            'tanMycas.assignedDate',
            'tanMycas.filingType',
            'tanMycas.filingStatus',
            'tanMycas.formTypeCd',
            'tanMycas.isWithdrawable',
            'tanMycas.formStatus',
            'tanMycas.transactionNo',
            'tanMycas.assessmentYear',
            'tanMycas.financialYear',
            'tanMycas.udinNumber',
            'client.id',
            'client.displayName',
            'tanClientCredentials.id',
          ])
          .leftJoin('tanMycas.tanClientCredentials', 'tanClientCredentials')
          .leftJoin('tanClientCredentials.client', 'client')
          .where('tanClientCredentials.id =:id', { id: id });

        myCasDetails.orderBy('tanMycas.assignedDate', 'DESC');

        if (offset) {
          myCasDetails.skip(offset);
        }

        if (limit) {
          myCasDetails.take(limit);
        }

        let result = await myCasDetails.getManyAndCount();
        return {
          count: result[1],
          result: result[0],
          accessDenied: true,
        };
      } else {
        return { accessDenied: false };
      }
    } catch (error) {
      console.log('error occured while fetching income tax client clientMycas in TAN', error);
    }
  }
  async exportTanClientMyCas(userId: number, query: any) {
    const id = query.incometaxid;
    let mycas: any = await this.clientMycas(userId, query, id);

    if (!mycas.result.length) throw new BadRequestException('No Data for Export');

    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Inc Tax My CA');
    const headers = [
      { header: 'S.No', key: 'serialNo' },
      { header: 'FY', key: 'fy' },
      { header: 'CA Name', key: 'caName' },
      { header: 'CA Membership #', key: 'caMembership' },
      { header: 'Filing Type', key: 'filingType' },
      { header: 'Form Name', key: 'formName' },
      { header: 'Assigned Date', key: 'assaignedDate' },
      { header: 'Transaction ID', key: 'transactionId' },
      { header: 'UDIN', key: 'udin' },
    ];

    worksheet.columns = headers;

    let serialCounter = 1; // Initialize a counter

    // Initialize array to store max lengths for each column
    const columnMaxLengths = Array(headers.length).fill(0);

    mycas.result.forEach((myca) => {
      const rowData = {
        serialNo: serialCounter++, // Assign and then increment the counter
        fy: calculateAssessmentYear(myca?.refYear),
        caName: myca?.caName,
        caMembership: myca?.caMembershipNum,
        filingType: myca?.filingType,
        formName: myca?.formTypeCd,
        assaignedDate: formatDate(myca?.assaignedDate),
        transactionId: myca?.transactionNo,
        udin: myca?.udinNumber,
      };

      const row = worksheet.addRow(rowData);
      worksheet.columns.forEach((column, colIndex) => {
        const headerLength = column.header?.length || 0; // Length of header
        const cellLength = rowData[column.key]?.toString().length || 0; // Length of cell value
        columnMaxLengths[colIndex] = Math.max(
          columnMaxLengths[colIndex] || 0,
          headerLength,
          cellLength,
        );
      });
    });

    // Apply column widths with padding
    worksheet.columns.forEach((column, colIndex) => {
      column.width = columnMaxLengths[colIndex] + 3; // Add padding for readability
    });

    // Style the header row and center alignment
    const headerRow = worksheet.getRow(1);
    headerRow.font = { bold: true };
    headerRow.eachCell((cell) => {
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '64B5F6' },
      };
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' },
      };
      cell.alignment = { horizontal: 'center', vertical: 'middle' };
    });

    worksheet.columns.forEach((column) => {
      if (column.key === 'taskName') {
        column.width = 50;
        column.alignment = { wrapText: true, horizontal: 'center', vertical: 'middle' };
      } else {
        column.alignment = { horizontal: 'center', vertical: 'middle' };
      }
    });

    // Apply wrapText for all rows
    worksheet.eachRow((row) => {
      row.eachCell((cell) => {
        cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true }; // Center alignment and wrap text
      });
    });

    // Freeze the header row
    worksheet.views = [{ state: 'frozen', ySplit: 1 }];

    const buffer = await workbook.xlsx.writeBuffer();
    return buffer;
  }

  async getClientTraceCommunications(id: number, query: any, userId: number) {
    const user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
    const { limit, offset } = query;
    try {
      const clientCredential = await TanClientCredentials.findOne({
        where: { id, organizationId: user?.organization?.id },
        relations: ['client'],
      });

      if (clientCredential) {
        const traceData = createQueryBuilder(TanCommunicationInbox, 'tanTraces')
          .leftJoinAndSelect('tanTraces.tanClientCredentials', 'tanClientCredentials')
          .leftJoinAndSelect('tanClientCredentials.client', 'client')
          .where('tanClientCredentials.id =:id', { id: id });

        const sort = (typeof query?.sort === "string") ? JSON.parse(query.sort) : query?.sort;
        if (sort?.column) {
          const columnMap: Record<string, string> = {
            date: 'tanTraces.date',
            id: 'tanTraces.id',
            qt: 'tanTraces.qt',
            formType: 'tanTraces.formType',
            commCat: 'tanTraces.commCat',
            description: 'tanTraces.description',
            type: 'tanTraces.type',
          };
          const column = columnMap[sort.column] || sort.column;
          traceData.orderBy(column, sort.direction.toUpperCase());
        } else {
          // traceData.orderBy('tanTraces.createdAt', 'DESC');
        };
        if (offset) {
          traceData.skip(offset);
        }

        if (limit) {
          traceData.take(limit);
        }

        traceData.addSelect(
          `COALESCE(
            STR_TO_DATE(NULLIF(tanTraces.date, 'NA'), '%d-%b-%Y'),'0000-01-01'		
          )`,
          'issueDateOrder',
        );

        traceData.addOrderBy('issueDateOrder', 'DESC');

        let result = await traceData.getManyAndCount();
        return {
          count: result[1],
          result: result[0],
          accessDenied: true,
        };
      } else {
        return { accessDenied: false };
      }
    } catch (error) {
      console.log('error occured while fetching client Trace Communication data in TAN', error);
    }
  }
  async exportClientTanTracesInbox(userId: number, query: any) {
    const id = query.incometaxId;
    let traces = await this.getClientTraceCommunications(id, query, userId);

    if (!traces?.result?.length) throw new BadRequestException('No Data for Export');

    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Traces Inbox');
    const headers = [
      { header: 'S.No', key: 'serialNo' },
      { header: 'Date', key: 'date' },
      { header: 'Reference ID', key: 'referenceID' },
      { header: 'FY', key: 'fy' },
      { header: 'Quarter', key: 'quarter' },
      { header: 'Form Type', key: 'formType' },
      { header: 'Category', key: 'category' },
      { header: 'Description', key: 'description' },
      { header: 'Action', key: 'type' }

    ];

    worksheet.columns = headers;

    let serialCounter = 1; // Initialize a counter

    // Initialize array to store max lengths for each column
    const columnMaxLengths = Array(headers.length).fill(0);

    traces.result.forEach((trace) => {
      const rowData = {
        serialNo: serialCounter++, // Auto-incremented serial number
        date: trace?.date || '', // Make sure it's in desired format (e.g., format it with moment.js if needed)
        referenceID: trace?.commRefNo || '',
        fy: trace?.fy || '',
        quarter: trace?.qt || '',
        formType: trace?.formType || '',
        category: trace?.commCat || '',
        description: trace?.description || '',
        type: trace?.type || '',
      };
      const row = worksheet.addRow(rowData);
      const typeCell = row.getCell('type'); // Get the cell for the "Type" column
      if (rowData.type === 'Required') {
        typeCell.font = {
          color: { argb: '800080' },
          bold: true, // Bold text
        };
      } else if (rowData.type === 'Not Required') {
        typeCell.font = {
          color: { argb: '008080' },
          bold: true, // Bold text
        };
      }



      worksheet.columns.forEach((column, colIndex) => {
        const headerLength = column.header?.length || 0; // Length of header
        const cellLength = rowData[column.key]?.toString().length || 0; // Length of cell value
        columnMaxLengths[colIndex] = Math.max(
          columnMaxLengths[colIndex] || 0,
          headerLength,
          cellLength,
        );
      });
    });

    // Apply column widths with padding
    worksheet.columns.forEach((column, colIndex) => {
      column.width = columnMaxLengths[colIndex] + 3; // Add padding for readability
    });

    // Style the header row and center alignment
    const headerRow = worksheet.getRow(1);
    headerRow.font = { bold: true };
    headerRow.eachCell((cell) => {
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '64B5F6' },
      };
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' },
      };
      cell.alignment = { horizontal: 'center', vertical: 'middle' };
    });

    worksheet.columns.forEach((column) => {
      if (column.key === 'category' || column.key === 'description') {
        column.width = 50;
        column.alignment = { wrapText: true, horizontal: 'center', vertical: 'middle' };
      } else {
        column.alignment = { horizontal: 'center', vertical: 'middle' };
      }
    });

    // Apply wrapText for all rows
    worksheet.eachRow((row) => {
      row.eachCell((cell) => {
        cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true }; // Center alignment and wrap text
      });
    });

    // Freeze the header row
    worksheet.views = [{ state: 'frozen', ySplit: 1 }];

    const buffer = await workbook.xlsx.writeBuffer();
    return buffer;
  }

  async findAllTraceCommunication(userId: number, query: any) {
    try {
      const { limit, offset } = query;
      let user = await User.findOne({
        where: { id: userId },
        relations: ['organization', 'role'],
      });

      let ViewAll = user.role.permissions.find(
        (permission) => permission.slug === Permissions.VIEW_ALL_CLIENT_MANAGERS,
      );
      let ViewAssigned = user.role.permissions.find(
        (permission) => permission.slug === Permissions.VIEW_ASSIGNED_CLIENT_MANAGERS,
      );

      let tracesData = createQueryBuilder(TanCommunicationInbox, 'tanTraces')
        .leftJoinAndSelect('tanTraces.tanClientCredentials', 'tanClientCredentials')
        .leftJoinAndSelect('tanClientCredentials.client', 'client')
        .leftJoin('client.clientManagers', 'clientManagers')
        .where('tanTraces.organizationId = :id', { id: user?.organization?.id })
        .andWhere('client.status != :status', { status: UserStatus.DELETED })
        .andWhere('tanClientCredentials.status != :disStatus', {
          disStatus: IncomeTaxStatus.DISABLE,
        });

      if (query.search) {
        tracesData.andWhere('client.displayName LIKE :search', { search: `%${query.search}%` });
        tracesData.orWhere('tanClientCredentials.tanNumber LIKE :search', {
          search: `%${query.search}%`,
        });
      }

      if (ViewAssigned && !ViewAll) {
        tracesData.andWhere('clientManagers.id = :userId', { userId });
      }

      if (query.formCode) {
        tracesData.andWhere('tanTraces.formType = :formCd', {
          formCd: query.formCode,
        });
      }

      if (query.filingType) {
        tracesData.andWhere('tanTraces.type = :filingType', {
          filingType: query.filingType,
        });
      }

      if (query.financialQuarter) {
        tracesData.andWhere('tanTraces.qt = :finQuarter', {
          finQuarter: query.financialQuarter,
        });
      }

      if (query.financialYear) {
        tracesData.andWhere('tanTraces.fy = :finY', {
          finY: query.financialYear,
        });
      };

      if (query.clientCategory) {
        tracesData.andWhere(`client.category = :clientCategory`, {
          clientCategory: query.clientCategory,
        });
      }

      if (query.interval) {
        const today = moment().format('DD-MMM-YYYY');
        let fromDate;
        if (query.interval === 'last1week') {
          fromDate = moment().subtract(7, 'days').format('DD-MMM-YYYY');
        } else if (query.interval === 'last15days') {
          fromDate = moment().subtract(15, 'days').format('DD-MMM-YYYY');
        } else if (query.interval === 'last1month') {
          fromDate = moment().subtract(30, 'days').format('DD-MMM-YYYY');
        }

        tracesData.andWhere(
          'STR_TO_DATE(tanTraces.date, "%d-%b-%Y") BETWEEN STR_TO_DATE(:fromDate, "%d-%b-%Y") AND STR_TO_DATE(:today, "%d-%b-%Y")',
          {
            fromDate,
            today,
          },
        );
      }





      if (offset >= 0) {
        tracesData.skip(offset);
      }

      if (limit) {
        tracesData.take(limit);
      }

      tracesData.addSelect(
        `COALESCE(
          STR_TO_DATE(NULLIF(tanTraces.date, 'NA'), '%d-%b-%Y'),'0000-01-01'		
        )`,
        'issueDateOrder',
      );
      tracesData.addOrderBy('issueDateOrder', 'DESC');

      let result = await tracesData.getManyAndCount();
      return {
        count: result[1],
        result: result[0],
      };
    } catch (error) {
      console.log('error occur while getting findAll tan traces communiaction', error);
    }
  }
  async exportClientTracesInbox(userId: number, query: any) {
    let traces: any = await this.findAllTraceCommunication(userId, query);

    if (!traces.result.length) throw new BadRequestException('No Data for Export');

    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Traces Inbox');
    const headers = [
      { header: 'S.No', key: 'serialNo' },
      { header: 'Client Name', key: 'clientName' },
      { header: 'TAN', key: 'tan' },
      { header: 'Date', key: 'date' },
      { header: 'Reference ID', key: 'referenceID' },
      { header: 'FY', key: 'fy' },
      { header: 'Quarter', key: 'quarter' },
      { header: 'Form Type', key: 'formType' },
      { header: 'Category', key: 'category' },
      { header: 'Description', key: 'description' },
      { header: 'Action', key: 'type' }

    ];

    worksheet.columns = headers;

    let serialCounter = 1; // Initialize a counter

    // Initialize array to store max lengths for each column
    const columnMaxLengths = Array(headers.length).fill(0);

    traces.result.forEach((trace) => {
      const rowData = {
        serialNo: serialCounter++, // Auto-incremented serial number
        clientName: trace?.tanClientCredentials?.client?.displayName || '',
        tan: trace?.tanClientCredentials?.tanNumber || '',
        date: trace?.date || '', // Make sure it's in desired format (e.g., format it with moment.js if needed)
        referenceID: trace?.commRefNo || '',
        fy: trace?.fy || '',
        quarter: trace?.qt || '',
        formType: trace?.formType || '',
        category: trace?.commCat || '',
        description: trace?.description || '',
        type: trace?.type || '',
      };
      const row = worksheet.addRow(rowData);
      const typeCell = row.getCell('type'); // Get the cell for the "Type" column
      if (rowData.type === 'Required') {
        typeCell.font = {
          color: { argb: '800080' },
          bold: true, // Bold text
        };
      } else if (rowData.type === 'Not Required') {
        typeCell.font = {
          color: { argb: '008080' },
          bold: true, // Bold text
        };
      }



      worksheet.columns.forEach((column, colIndex) => {
        const headerLength = column.header?.length || 0; // Length of header
        const cellLength = rowData[column.key]?.toString().length || 0; // Length of cell value
        columnMaxLengths[colIndex] = Math.max(
          columnMaxLengths[colIndex] || 0,
          headerLength,
          cellLength,
        );
      });
    });

    // Apply column widths with padding
    worksheet.columns.forEach((column, colIndex) => {
      column.width = columnMaxLengths[colIndex] + 3; // Add padding for readability
    });

    // Style the header row and center alignment
    const headerRow = worksheet.getRow(1);
    headerRow.font = { bold: true };
    headerRow.eachCell((cell) => {
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '64B5F6' },
      };
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' },
      };
      cell.alignment = { horizontal: 'center', vertical: 'middle' };
    });

    worksheet.columns.forEach((column) => {
      if (column.key === 'clientName' || column.key === 'category' || column.key === 'description') {
        column.width = 50;
        column.alignment = { wrapText: true, horizontal: 'center', vertical: 'middle' };
      } else {
        column.alignment = { horizontal: 'center', vertical: 'middle' };
      }
    });

    // Apply wrapText for all rows
    worksheet.eachRow((row) => {
      row.eachCell((cell) => {
        cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true }; // Center alignment and wrap text
      });
    });

    // Freeze the header row
    worksheet.views = [{ state: 'frozen', ySplit: 1 }];

    const buffer = await workbook.xlsx.writeBuffer();
    return buffer;
  }

  async getActivityLogTracesData(id: any, query: any, userId: number) {
    try {
      const user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
      const clientCredential = await TanClientCredentials.findOne({
        where: { id, organizationId: user?.organization?.id },
        relations: ['client'],
      });
      if (clientCredential) {
        const autActivity = await createQueryBuilder(AutomationMachines, 'autActivity')
          .leftJoinAndSelect('autActivity.user', 'user')
          .where('autActivity.tanCredentials =:id', { id: id });

        autActivity.andWhere('autActivity.type =:type', { type: 'TRACES' });

        if (query.fromDate && query.toDate) {
          const { startTime, endTime } = dateFormation(query.fromDate, query.toDate);
          autActivity
            .andWhere('Date(autActivity.created_at) >= :startTime', { startTime })
            .andWhere('Date(autActivity.created_at) <= :endTime', { endTime });
        }

        autActivity.orderBy('autActivity.id', 'DESC');
        let result = await autActivity.getMany();
        return { result, accessDenied: true };
      } else {
        return { accessDenied: false };
      }
    } catch (error) {
      console.error('Error fetching activity log data:', error);
      return null; // Or throw an error
    }
  }

  async getclientAutoTracesStatus(id: number, userId: number) {
    try {
      const user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
      const clientCredentials = await TanClientCredentials.findOne({
        where: { id, organizationId: user?.organization?.id },
      });

      if (clientCredentials) {
        const lastCompletedMachine = await AutomationMachines.findOne({
          where: { tanCredentials: id, type: 'TRACES' },
          order: {
            id: 'DESC',
          },
          relations: ['tanCredentials', 'tanCredentials.client'],
        });
        let totalInqueueCount = 0;
        if (lastCompletedMachine?.status === 'INQUEUE') {
          totalInqueueCount = await AutomationMachines.count({
            where: { status: 'INQUEUE', type: 'TRACES', id: LessThan(lastCompletedMachine.id) },
          });
        }
        return { lastCompletedMachine, accessDenied: true, totalInqueueCount };
      } else {
        return { accessDenied: false };
      }
    } catch (error) {
      console.log('error occur while getting getclientAutoStatus', error);
    }
  }

  async getTraceReport(userId: number, query: any) {
    try {
      const { limit, offset, status, remarks } = query;
      let user = await User.findOne({
        where: { id: userId },
        relations: ['organization', 'role'],
      });
      const entityManager = getRepository(AutomationMachines);

      let ViewAll = user.role.permissions.find(
        (permission) => permission.slug === Permissions.VIEW_ALL_CLIENT_MANAGERS,
      );
      let ViewAssigned = user.role.permissions.find(
        (permission) => permission.slug === Permissions.VIEW_ASSIGNED_CLIENT_MANAGERS,
      );

      let sql = await entityManager
        .createQueryBuilder('automationMachines')
        .leftJoinAndSelect('automationMachines.tanCredentials', 'tanCredentials')
        .leftJoinAndSelect('tanCredentials.client', 'client')
        // .leftJoin('client.clientManagers', 'clientManagers')
        .where('tanCredentials.organizationId = :id', { id: user.organization.id })
        .andWhere('automationMachines.type = :type', { type: 'TRACES' })
        .andWhere('client.status != :status', { status: UserStatus.DELETED })
        .andWhere('tanCredentials.status != :disStatus', { disStatus: IncomeTaxStatus.DISABLE });

      if (status) {
        sql = sql.andWhere('automationMachines.status = :status', { status });
      }

      if (remarks) {
        sql = sql.andWhere('automationMachines.remarks = :remarks', { remarks });
      }

      // if (ViewAssigned && !ViewAll) {
      //   sql.andWhere('clientManagers.id = :userId', { userId });
      // }

      sql = sql
        .andWhere((qb) => {
          const subQuery = qb
            .subQuery()
            .select('MAX(innerAutomationMachines.id)', 'maxId')
            .from(AutomationMachines, 'innerAutomationMachines')
            .leftJoin('innerAutomationMachines.tanCredentials', 'innerTanCredentials')
            .where('innerTanCredentials.organizationId = :id', { id: user.organization.id })
            .andWhere('innerAutomationMachines.type = :type', { type: 'TRACES' })
            .groupBy('innerTanCredentials.id')
            .getQuery();
          return 'automationMachines.id IN ' + subQuery;
        })
        // .orderBy('automationMachines.id', 'DESC')
        .limit(limit)
        .offset(offset);
      const sort = (typeof query?.sort === "string") ? JSON.parse(query.sort) : query?.sort;
      if (sort?.column) {
        const columnMap: Record<string, string> = {
          id: 'client.clientId',
          displayName: 'client.displayName',
          tanNumber: 'tanCredentials.tanNumber',
          status: 'automationMachines.status',
          remarks: 'automationMachines.remarks',
          createdAt: 'automationMachines.createdAt'

        };
        const column = columnMap[sort.column] || sort.column;
        sql.orderBy(column, sort.direction.toUpperCase());
      } else {
        sql.orderBy('automationMachines.createdAt', 'DESC');
      };
      const result = await sql.getManyAndCount();
      return {
        data: result[0],
        count: result[1],
      };
    } catch (error) {
      console.log('error occur while getting  getclientReport', error);
    }
  }
  async exportTanTraceSyncStatus(userId: number, query: any) {
    const id = query.incometaxId;
    let traces = await this.getTraceReport(userId, query);

    if (!traces?.data?.length) throw new BadRequestException('No Data for Export');

    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Traces');
    const headers = [
      { header: 'S.No', key: 'serialNo' },
      { header: 'Client Id', key: 'clientId' },
      { header: 'Client Name', key: 'clientName' },
      { header: 'Traces User Id', key: 'traceUserId' },
      { header: 'Password', key: 'password' },
      { header: 'TAN', key: 'tan' },

      { header: 'Status', key: 'status' },
      { header: 'Remarks', key: 'remarks' },
      { header: 'Last Sync', key: 'lastSync' }
    ];

    worksheet.columns = headers;

    let serialCounter = 1; // Initialize a counter

    // Initialize array to store max lengths for each column
    const columnMaxLengths = Array(headers.length).fill(0);

    traces.data.forEach((trace) => {
      const rowData = {
        serialNo: serialCounter++, // Auto-incrementing S.No
        clientId: trace?.tanCredentials?.client?.clientId || '',
        clientName: trace?.tanCredentials?.client?.displayName || '',
        traceUserId: trace?.tanCredentials?.traceUserId || " ",
        tan: trace?.tanCredentials?.tanNumber || '',
        password: trace?.tanCredentials?.password || " ",
        status: capitalize(trace?.status) || '',
        remarks: trace?.remarks || '',
        lastSync: trace?.createdAt ? moment(trace.createdAt).format("DD-MM-YYYY h:mm a") : '',
      };
      const row = worksheet.addRow(rowData);
      const statusCell = row.getCell('status');

      if (rowData.status === 'Pending') {
        statusCell.font = {
          color: { argb: '800080' }, // Purple
          bold: true,
        };
      } else if (rowData.status === 'Completed') {
        statusCell.font = {
          color: { argb: '228B22' }, // Green
          bold: true,
        };
      } else if (rowData.status === 'Inqueue') {
        statusCell.font = {
          color: { argb: 'FF8C00' }, // Orange
          bold: true,
        };
      }




      worksheet.columns.forEach((column, colIndex) => {
        const headerLength = column.header?.length || 0; // Length of header
        const cellLength = rowData[column.key]?.toString().length || 0; // Length of cell value
        columnMaxLengths[colIndex] = Math.max(
          columnMaxLengths[colIndex] || 0,
          headerLength,
          cellLength,
        );
      });
    });

    // Apply column widths with padding
    worksheet.columns.forEach((column, colIndex) => {
      column.width = columnMaxLengths[colIndex] + 3; // Add padding for readability
    });

    // Style the header row and center alignment
    const headerRow = worksheet.getRow(1);
    headerRow.font = { bold: true };
    headerRow.eachCell((cell) => {
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '64B5F6' },
      };
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' },
      };
      cell.alignment = { horizontal: 'center', vertical: 'middle' };
    });

    worksheet.columns.forEach((column) => {
      if (column.key === 'clientName' || column.key === 'category' || column.key === 'description') {
        column.width = 50;
        column.alignment = { wrapText: true, horizontal: 'center', vertical: 'middle' };
      } else {
        column.alignment = { horizontal: 'center', vertical: 'middle' };
      }
    });

    // Apply wrapText for all rows
    worksheet.eachRow((row) => {
      row.eachCell((cell) => {
        cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true }; // Center alignment and wrap text
      });
    });

    // Freeze the header row
    worksheet.views = [{ state: 'frozen', ySplit: 1 }];

    const buffer = await workbook.xlsx.writeBuffer();
    return buffer;
  }


  async findFyaTempNotices(userId: number, query: any) {
    try {
      const { limit, offset, fromDate, toDate } = query;
      let user = await User.findOne({
        where: { id: userId },
        relations: ['organization'],
      });
      let epro = createQueryBuilder(TanTempEproFya, 'tanTempEproFya')
        .leftJoinAndSelect('tanTempEproFya.client', 'client')
        .leftJoinAndSelect('client.tanClientCredentials', 'tanClientCredentials')
        .where('tanTempEproFya.organizationId = :id', { id: user?.organization?.id })
        .andWhere('client.status != :status', { status: UserStatus.DELETED })
        .andWhere('tanClientCredentials.status != :disStatus', {
          disStatus: IncomeTaxStatus.DISABLE,
        });

      if (fromDate && toDate) {
        const formattedFromDate = new Date(fromDate)
          .toLocaleDateString('en-GB')
          .split('/')
          .join('-');
        const formattedToDate = new Date(toDate).toLocaleDateString('en-GB').split('/').join('-');

        epro.andWhere(
          new Brackets((qb) => {
            qb.where("STR_TO_DATE(tanTempEproFya.noticeSentDate, '%d-%m-%Y') BETWEEN STR_TO_DATE(:fromDate, '%d-%m-%Y') AND STR_TO_DATE(:toDate, '%d-%m-%Y')", { fromDate: formattedFromDate, toDate: formattedToDate },)
            qb.orWhere("STR_TO_DATE(tanTempEproFya.dateOfCompliance, '%d-%m-%Y') BETWEEN STR_TO_DATE(:fromDate, '%d-%m-%Y') AND STR_TO_DATE(:toDate, '%d-%m-%Y')", { fromDate: formattedFromDate, toDate: formattedToDate },);
            qb.orWhere("STR_TO_DATE(tanTempEproFya.proceedingLimitationDate, '%d-%m-%Y') BETWEEN STR_TO_DATE(:fromDate, '%d-%m-%Y') AND STR_TO_DATE(:toDate, '%d-%m-%Y')", { fromDate: formattedFromDate, toDate: formattedToDate },)
            qb.orWhere("STR_TO_DATE(tanTempEproFya.dateResponseSubmitted, '%d-%m-%Y') BETWEEN STR_TO_DATE(:fromDate, '%d-%m-%Y') AND STR_TO_DATE(:toDate, '%d-%m-%Y')", { fromDate: formattedFromDate, toDate: formattedToDate },)
            qb.orWhere("STR_TO_DATE(tanTempEproFya.proceedingConcludedDate, '%d-%m-%Y') BETWEEN STR_TO_DATE(:fromDate, '%d-%m-%Y') AND STR_TO_DATE(:toDate, '%d-%m-%Y')", { fromDate: formattedFromDate, toDate: formattedToDate },)
          }),
        );
      }

      if (query.search) {
        epro.andWhere(
          new Brackets((qb) => {
            qb.where('tanTempEproFya.pan LIKE :pansearch', {
              pansearch: `%${query.search}%`,
            });
            qb.orWhere('client.displayName LIKE :namesearch', {
              namesearch: `%${query.search}%`,
            });
            qb.orWhere('tanTempEproFya.proceedingName LIKE :prosearch', {
              prosearch: `%${query.search}%`,
            });
          }),
        );
      }

      if (query.section) {
        if (query.section === 'null') {
          epro.andWhere('tanTempEproFya.noticeSection is NULL');
        } else {
          epro.andWhere('tanTempEproFya.noticeSection like :sectionsearch', {
            sectionsearch: `%${query.section}%`,
          });
        }
      }

      if (query.assessmentYear) {
        if (query.assessmentYear === 'null') {
          epro.andWhere('tanTempEproFya.ay = 0');
        } else {
          epro.andWhere('tanTempEproFya.ay like :as', {
            as: `%${query.assessmentYear}%`,
          });
        }
      }

      if (query.clientCategory) {
        epro.andWhere(`client.category = :clientCategory`, {
          clientCategory: query.clientCategory,
        });
      }

      if (query.caseStatus) {
        epro.andWhere('tanTempEproFya.proceedingStatus LIKE :caseStatus', {
          caseStatus: query?.caseStatus,
        });
      }

      epro
        .addSelect(`STR_TO_DATE(tanTempEproFya.noticeSentDate, '%d-%m-%Y')`, 'parsedDate')
        .orderBy('parsedDate', 'DESC');

      if (offset) {
        epro.skip(offset);
      }

      if (limit) {
        epro.take(limit);
      }

      let result = await epro.getManyAndCount();
      return {
        count: result[1],
        result: result[0],
      };
    } catch (error) {
      console.log('error occured while fetching getIncomeTaxEproceedings', error);
    }
  }

  async findFyiTempNotices(userId: number, query: any) {
    try {
      const { limit, offset, fromDate, toDate } = query;
      let user = await User.findOne({
        where: { id: userId },
        relations: ['organization'],
      });
      let epro = createQueryBuilder(TanTempEproFyi, 'tanTempEproFyi')
        .leftJoinAndSelect('tanTempEproFyi.client', 'client')
        .leftJoinAndSelect('client.tanClientCredentials', 'tanClientCredentials')
        .where('tanTempEproFyi.organizationId = :id', { id: user?.organization?.id })
        .andWhere('client.status != :status', { status: UserStatus.DELETED })
        .andWhere('tanClientCredentials.status != :disStatus', {
          disStatus: IncomeTaxStatus.DISABLE,
        });

      if (fromDate && toDate) {
        const formattedFromDate = new Date(fromDate)
          .toLocaleDateString('en-GB')
          .split('/')
          .join('-');
        const formattedToDate = new Date(toDate).toLocaleDateString('en-GB').split('/').join('-');

        epro.andWhere(
          new Brackets((qb) => {
            qb.where("STR_TO_DATE(tanTempEproFyi.noticeSentDate, '%d-%m-%Y') BETWEEN STR_TO_DATE(:fromDate, '%d-%m-%Y') AND STR_TO_DATE(:toDate, '%d-%m-%Y')", { fromDate: formattedFromDate, toDate: formattedToDate },)
            qb.orWhere("STR_TO_DATE(tanTempEproFyi.dateOfCompliance, '%d-%m-%Y') BETWEEN STR_TO_DATE(:fromDate, '%d-%m-%Y') AND STR_TO_DATE(:toDate, '%d-%m-%Y')", { fromDate: formattedFromDate, toDate: formattedToDate },);
            qb.orWhere("STR_TO_DATE(tanTempEproFyi.proceedingLimitationDate, '%d-%m-%Y') BETWEEN STR_TO_DATE(:fromDate, '%d-%m-%Y') AND STR_TO_DATE(:toDate, '%d-%m-%Y')", { fromDate: formattedFromDate, toDate: formattedToDate },)
            qb.orWhere("STR_TO_DATE(tanTempEproFyi.dateResponseSubmitted, '%d-%m-%Y') BETWEEN STR_TO_DATE(:fromDate, '%d-%m-%Y') AND STR_TO_DATE(:toDate, '%d-%m-%Y')", { fromDate: formattedFromDate, toDate: formattedToDate },)
            qb.orWhere("STR_TO_DATE(tanTempEproFyi.proceedingConcludedDate, '%d-%m-%Y') BETWEEN STR_TO_DATE(:fromDate, '%d-%m-%Y') AND STR_TO_DATE(:toDate, '%d-%m-%Y')", { fromDate: formattedFromDate, toDate: formattedToDate },)
          }),
        );
      }

      if (query.caseStatus) {
        epro.andWhere('tanTempEproFyi.proceedingStatus LIKE :caseStatus', {
          caseStatus: query?.caseStatus,
        });
      }

      if (query.search) {
        epro.andWhere(
          new Brackets((qb) => {
            qb.where('tanTempEproFyi.pan LIKE :pansearch', {
              pansearch: `%${query.search}%`,
            });
            qb.orWhere('client.displayName LIKE :namesearch', {
              namesearch: `%${query.search}%`,
            });
            qb.orWhere('tanTempEproFyi.proceedingName LIKE :prosearch', {
              prosearch: `%${query.search}%`,
            });
          }),
        );
      }

      if (query.section) {
        if (query.section === 'null') {
          epro.andWhere('tanTempEproFyi.noticeSection is NUll');
        } else {
          epro.andWhere('tanTempEproFyi.noticeSection like :sectionsearch', {
            sectionsearch: `%${query.section}%`,
          });
        }
      }

      if (query.assessmentYear) {
        if (query.assessmentYear === 'null') {
          epro.andWhere('tanTempEproFyi.ay = 0');
        } else {
          epro.andWhere('tanTempEproFyi.ay like :as', {
            as: `%${query.assessmentYear}%`,
          });
        }
      }

      if (query.clientCategory) {
        epro.andWhere(`client.category = :clientCategory`, {
          clientCategory: query.clientCategory,
        });
      }

      epro
        .addSelect(`STR_TO_DATE(tanTempEproFyi.noticeSentDate, '%d-%m-%Y')`, 'parsedDate')
        .orderBy('parsedDate', 'DESC');

      if (offset) {
        epro.skip(offset);
      }

      if (limit) {
        epro.take(limit);
      }
      let result = await epro.getManyAndCount();
      return {
        count: result[1],
        result: result[0],
      };
    } catch (error) {
      console.log('error occured while fetching getIncomeTaxFyiEproceedings', error);
    }
  }

  async getExcelFyaSections(userId: number) {
    try {
      let user = await User.findOne({
        where: { id: userId },
        relations: ['organization'],
      });

      const sectionsData = await createQueryBuilder(TanTempEproFya, 'tanTempEproFya')
        .select('tanTempEproFya.noticeSection', 'noticeSection')
        .where('tanTempEproFya.organizationId = :id', { id: user.organization.id })
        .groupBy('tanTempEproFya.noticeSection')
        .getRawMany();

      const filteredSections = sectionsData
        .map((section) => section.noticeSection)
        .filter((section) => section !== '' && section !== null && section !== 'null');

      return filteredSections;
    } catch (error) {
      console.error('Error occurred while fetching excel-fya-sections:', error);
      throw error;
    }
  }

  async getExcelFyiSections(userId: number) {
    try {
      let user = await User.findOne({
        where: { id: userId },
        relations: ['organization'],
      });

      const sectionsData = await createQueryBuilder(TanTempEproFyi, 'tanTempEproFyi')
        .select('tanTempEproFyi.noticeSection', 'noticeSection')
        .where('tanTempEproFyi.organizationId = :id', { id: user.organization.id })
        .groupBy('tanTempEproFyi.noticeSection')
        .getRawMany();

      const filteredSections = sectionsData
        .map((section) => section.noticeSection)
        .filter((section) => section !== '' && section !== null && section !== 'null');

      return filteredSections;
    } catch (error) {
      console.error('Error occurred while fetching excel-fyi-sections:', error);
      throw error;
    }
  }

  async getClientExcelProceedingFyi(id: number, query: any, userId: number) {
    const user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
    const { limit, offset, fromDate, toDate } = query;
    try {
      const clientCredential = await TanClientCredentials.findOne({
        where: { id, organizationId: user?.organization?.id },
        relations: ['client'],
      });
      if (clientCredential) {
        let clientEpro = createQueryBuilder(TanTempEproFyi, 'tanTempEproFyi')
          .leftJoinAndSelect('tanTempEproFyi.client', 'client')
          .where('client.id = :id', { id: clientCredential?.client?.id });
        // clientEpro.orderBy('incTempEproFyi.noticeSentDate', 'DESC');

        if (fromDate && toDate) {
          const formattedFromDate = new Date(fromDate)
            .toLocaleDateString('en-GB')
            .split('/')
            .join('-');
          const formattedToDate = new Date(toDate).toLocaleDateString('en-GB').split('/').join('-');

          clientEpro.andWhere(
            `STR_TO_DATE(tanTempEproFyi.noticeSentDate, '%d-%m-%Y') BETWEEN STR_TO_DATE(:fromDate, '%d-%m-%Y') AND STR_TO_DATE(:toDate, '%d-%m-%Y') 
                        OR STR_TO_DATE(tanTempEproFyi.dateOfCompliance, '%d-%m-%Y') BETWEEN STR_TO_DATE(:fromDate, '%d-%m-%Y') AND STR_TO_DATE(:toDate, '%d-%m-%Y')`,
            { fromDate: formattedFromDate, toDate: formattedToDate },
          );
        }

        if (query.caseStatus) {
          clientEpro.andWhere('tanTempEproFyi.proceedingStatus LIKE :caseStatus', {
            caseStatus: query?.caseStatus,
          });
        }

        if (query.search) {
          clientEpro.andWhere('tanTempEproFyi.proceedingName LIKE :prosearch', {
            prosearch: `%${query.search}%`,
          });
        }

        if (query.section) {
          if (query.section === 'null') {
            clientEpro.andWhere('tanTempEproFyi.noticeSection is NULL');
          } else {
            clientEpro.andWhere('tanTempEproFyi.noticeSection like :sectionsearch', {
              sectionsearch: `%${query.section}%`,
            });
          }
        }

        if (query.assessmentYear) {
          if (query.assessmentYear === 'null') {
            clientEpro.andWhere('tanTempEproFyi.ay = 0');
          } else {
            clientEpro.andWhere('tanTempEproFyi.ay like :as', {
              as: `%${query.assessmentYear}%`,
            });
          }
        }

        if (offset >= 0) {
          clientEpro.skip(offset);
        }

        if (limit) {
          clientEpro.take(limit);
        }

        clientEpro
          .addSelect(`STR_TO_DATE(tanTempEproFyi.noticeSentDate, '%d-%m-%Y')`, 'parsedDate')
          .orderBy('parsedDate', 'DESC');

        let result = await clientEpro.getManyAndCount();
        return {
          count: result[1],
          result: result[0],
          accessDenied: true,
        };
      } else {
        return { accessDenied: false };
      }
    } catch (error) {
      console.log('Error occurred while fetching income tax excel-fyi', error);
    }
  }

  async getClientExcelProceedingFya(id: number, query: any, userId: number) {
    const { limit, offset, fromDate, toDate } = query;
    const user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
    try {
      const clientCredential = await TanClientCredentials.findOne({
        where: { id, organizationId: user?.organization?.id },
        relations: ['client'],
      });
      if (clientCredential) {
        const clientEpro = createQueryBuilder(TanTempEproFya, 'tanTempEproFya')
          .leftJoinAndSelect('tanTempEproFya.client', 'client')
          .where('client.id =:id', { id: clientCredential?.client?.id });

        clientEpro
          .addSelect(`STR_TO_DATE(tanTempEproFya.noticeSentDate, '%d-%m-%Y')`, 'parsedDate')
          .orderBy('parsedDate', 'DESC');

        if (fromDate && toDate) {
          const formattedFromDate = new Date(fromDate)
            .toLocaleDateString('en-GB')
            .split('/')
            .join('-');
          const formattedToDate = new Date(toDate).toLocaleDateString('en-GB').split('/').join('-');

          clientEpro.andWhere(
            `STR_TO_DATE(tanTempEproFya.noticeSentDate, '%d-%m-%Y') BETWEEN STR_TO_DATE(:fromDate, '%d-%m-%Y') AND STR_TO_DATE(:toDate, '%d-%m-%Y') 
                        OR STR_TO_DATE(tanTempEproFya.dateOfCompliance, '%d-%m-%Y') BETWEEN STR_TO_DATE(:fromDate, '%d-%m-%Y') AND STR_TO_DATE(:toDate, '%d-%m-%Y')`,
            { fromDate: formattedFromDate, toDate: formattedToDate },
          );
        }

        if (query.caseStatus) {
          clientEpro.andWhere('tanTempEproFya.proceedingStatus LIKE :caseStatus', {
            caseStatus: query?.caseStatus,
          });
        }

        if (query.search) {
          clientEpro.andWhere('tanTempEproFya.proceedingName LIKE :prosearch', {
            prosearch: `%${query.search}%`,
          });
        }

        if (query.section) {
          if (query.section === 'null') {
            clientEpro.andWhere('tanTempEproFya.noticeSection is NULL');
          } else {
            clientEpro.andWhere('tanTempEproFya.noticeSection like :sectionsearch', {
              sectionsearch: `%${query.section}%`,
            });
          }
        }

        if (query.assessmentYear) {
          if (query.assessmentYear === 'null') {
            clientEpro.andWhere('tanTempEproFya.ay = 0');
          } else {
            clientEpro.andWhere('tanTempEproFya.ay like :as', {
              as: `%${query.assessmentYear}%`,
            });
          }
        }

        if (offset >= 0) {
          clientEpro.skip(offset);
        }

        if (limit) {
          clientEpro.take(limit);
        }

        let result = await clientEpro.getManyAndCount();
        return {
          count: result[1],
          result: result[0],
          accessDenied: true,
        };
      } else {
        return { accessDenied: false };
      }
    } catch (error) {
      console.log('error occured while fetching income tax excel-fya', error);
    }
  }

  async getExcelCombinedNotices(userId: number, query: any) {
    try {
      const { limit, offset, search, interval, column, assessmentYear, sort } = query;
      const userRepository = getRepository(User);
      const user = await userRepository.findOne({
        where: { id: userId },
        relations: ['organization', 'role'],
      });

      if (!user?.organization?.id) {
        return { count: 0, result: [] };
      }

      let ViewAll = user.role.permissions.find(
        (permission) => permission.slug === Permissions.VIEW_ALL_CLIENT_MANAGERS,
      );
      let ViewAssigned = user.role.permissions.find(
        (permission) => permission.slug === Permissions.VIEW_ASSIGNED_CLIENT_MANAGERS,
      );

      const organizationId = user.organization.id;

      let fyaNoticeQuery = createQueryBuilder(TanTempEproFya, 'tanTempEproFya')
        .leftJoinAndSelect('tanTempEproFya.client', 'client')
        .leftJoin('client.clientManagers', 'clientManagers')
        .leftJoin('client.tanClientCredentials', 'tanClientCredentials')
        .where('tanTempEproFya.organizationId = :id', { id: organizationId })
        .andWhere('client.status != :status', { status: UserStatus.DELETED })
        .andWhere('tanClientCredentials.status != :status', { status: "DISABLE" })
        .addSelect("'FYA'", 'eProType');

      let fyiNoticeQuery = createQueryBuilder(TanTempEproFyi, 'tanTempEproFyi')
        .leftJoinAndSelect('tanTempEproFyi.client', 'client')
        .leftJoin('client.clientManagers', 'clientManagers')
        .leftJoin('client.tanClientCredentials', 'tanClientCredentials')
        .where('tanTempEproFyi.organizationId = :id', { id: organizationId })
        .andWhere('client.status != :status', { status: UserStatus.DELETED })
        .andWhere('tanClientCredentials.status != :status', { status: "DISABLE" })
        .addSelect("'FYI'", 'eProType');

      if (search) {
        fyaNoticeQuery.andWhere(
          'client.displayName LIKE :search OR tanTempEproFya.noticeDin LIKE :search',
          {
            search: `%${search}%`,
          },
        );
        fyiNoticeQuery.andWhere(
          'client.displayName LIKE :search OR tanTempEproFyi.noticeDin LIKE :search',
          {
            search: `%${search}%`,
          },
        );
      }

      if (ViewAssigned && !ViewAll) {
        fyaNoticeQuery.andWhere('clientManagers.id = :userId', { userId });
      }
      if (ViewAssigned && !ViewAll) {
        fyiNoticeQuery.andWhere('clientManagers.id = :userId', { userId });
      }

      if (assessmentYear) {
        fyaNoticeQuery.andWhere('tanTempEproFya.ay = :assessmentYear', { assessmentYear });
        fyiNoticeQuery.andWhere('tanTempEproFyi.ay = :assessmentYear', { assessmentYear });
      }

      const applyConditions = (table: any, alias: string, column) => {
        if (column === 'dateOfCompliance') {
          if (interval && column) {
            const now = moment().startOf('day'); // Current date without time
            let futureDate;

            if(interval === 'today'){
              futureDate = now.clone().add(1,'day')
            } else if (interval === 'last1week') {
              futureDate = now.clone().add(7, 'days');
            } else if (interval === 'last15days') {
              futureDate = now.clone().add(15, 'days');
            } else if (interval === 'last1month') {
              futureDate = now.clone().add(1, 'month');
            } else if (interval === 'last1year') {
              futureDate = now.clone().add(1, 'year');
            }

            if (futureDate) {
              table.andWhere(
                `STR_TO_DATE(${alias}.dateOfCompliance, '%d-%m-%Y') BETWEEN :now AND :futureDate`,
                {
                  now: now.format('YYYY-MM-DD'),
                  futureDate: futureDate.format('YYYY-MM-DD'),
                },
              );
            }
          }
        }
        if (column === 'noticeSentDate') {
          if (interval && column) {
            const now = moment().startOf('day'); // Current date without time
            let pastDate;

            if(interval === 'today'){
              pastDate = now;
            } else if (interval === 'last1week') {
              pastDate = now.clone().subtract(7, 'days');
            } else if (interval === 'last15days') {
              pastDate = now.clone().subtract(15, 'days');
            } else if (interval === 'last1month') {
              pastDate = now.clone().subtract(1, 'month');
            } else if (interval === 'last1year') {
              pastDate = now.clone().subtract(1, 'year');
            }

            if (pastDate) {
              table.andWhere(
                `STR_TO_DATE(${alias}.noticeSentDate, '%d-%m-%Y') BETWEEN :pastDate AND :now`,
                {
                  pastDate: pastDate.format('YYYY-MM-DD'),
                  now: now.format('YYYY-MM-DD'),
                },
              );
            }
          }
        }
        if (column === 'dateResponseSubmitted') {
          if (interval && column) {
            const now = moment().startOf('day'); // Current date without time
            let futureDate;

            if(interval === 'today'){
              futureDate = now.add(1,'day');
            } else if (interval === 'last1week') {
              futureDate = now.clone().add(7, 'days');
            } else if (interval === 'last15days') {
              futureDate = now.clone().add(15, 'days');
            } else if (interval === 'last1month') {
              futureDate = now.clone().add(1, 'month');
            } else if (interval === 'last1year') {
              futureDate = now.clone().add(1, 'year');
            }

            if (futureDate) {
              table.andWhere(
                `STR_TO_DATE(${alias}.dateResponseSubmitted, '%d-%m-%Y') BETWEEN :now AND :futureDate`,
                {
                  now: now.format('YYYY-MM-DD'),
                  futureDate: futureDate.format('YYYY-MM-DD'),
                },
              );
            }
          }
        }
      };

      const applyFormAndToDateFilter = (table: any, alias: string, column) => {
        const fromDate = moment(query.fromDate).startOf('day').format('YYYY-MM-DD');
        const toDate = moment(query.toDate).endOf('day').format('YYYY-MM-DD');

        if (column === 'all') {
          table.where((qb) => {
            qb.where(
              `STR_TO_DATE(${alias}.dateOfCompliance,"%d-%m-%Y") BETWEEN :fromDate AND :toDate`,
              { fromDate, toDate },
            )
              .orWhere(
                `STR_TO_DATE(${alias}.noticeSentDate, "%d-%m-%Y") BETWEEN :fromDate AND :toDate`,
                { fromDate, toDate },
              )
              .orWhere(
                `STR_TO_DATE(${alias}.dateResponseSubmitted, "%d-%m-%Y") BETWEEN :fromDate AND :toDate`,
                {
                  fromDate,
                  toDate,
                },
              );
          });
        } else {
          if (column) {
            table.andWhere(
              `STR_TO_DATE(${alias}.${column},"%d-%m-%Y") BETWEEN :fromDate AND :toDate`,
              {
                fromDate,
                toDate,
              },
            );
          }
        }
      };

      if (query.fromDate && query.toDate) {
        applyFormAndToDateFilter(fyaNoticeQuery, 'tanTempEproFya', column);
        applyFormAndToDateFilter(fyiNoticeQuery, 'tanTempEproFyi', column);
      } else {
        applyConditions(fyaNoticeQuery, 'tanTempEproFya', column);
        applyConditions(fyiNoticeQuery, 'tanTempEproFyi', column);
      }

      if (column && sort) {
        fyaNoticeQuery
          .addSelect(`STR_TO_DATE(tanTempEproFya.${sort}, '%d-%m-%Y')`, 'formattedDate')
          .orderBy('formattedDate', 'DESC');

        fyiNoticeQuery
          .addSelect(`STR_TO_DATE(tanTempEproFyi.${sort}, '%d-%m-%Y')`, 'formattedDate')
          .orderBy('formattedDate', 'DESC');
      }

      // Pagination
      fyaNoticeQuery.skip(offset).take(limit);
      fyiNoticeQuery.skip(offset).take(limit);

      const [fyaNotices, fyaCount] = await fyaNoticeQuery.getManyAndCount();
      const [fyiNotices, fyiCount] = await fyiNoticeQuery.getManyAndCount();

      const formattedFyaNotices = fyaNotices.map((notice) => ({ ...notice, eproType: 'FYA' }));
      const formattedFyiNotices = fyiNotices.map((notice) => ({ ...notice, eproType: 'FYI' }));

      return {
        count: fyaCount + fyiCount,
        result: [...formattedFyaNotices, ...formattedFyiNotices],
      };
    } catch (error) {
      console.log('error occur while getting getCombinedNotices', error);
    }
  }

   async findAllDemands(userId: number, query: any) {
    try {
      const { limit, offset } = query;
      let user = await User.findOne({
        where: { id: userId },
        relations: ['organization', 'role'],
      });


      let ViewAll = user.role.permissions.find(
        (permission) => permission.slug === Permissions.VIEW_ALL_CLIENT_MANAGERS,
      );
      let ViewAssigned = user.role.permissions.find(
        (permission) => permission.slug === Permissions.VIEW_ASSIGNED_CLIENT_MANAGERS,
      );

      let demands = createQueryBuilder(TraceOutstandingDemand, 'demands')
        .leftJoinAndSelect('demands.tanClientCredentials', 'tanClientCredentials')
        .leftJoinAndSelect('tanClientCredentials.client', 'client')
        .leftJoin('client.clientManagers', 'clientManagers')
        .where('demands.organizationId = :id', { id: user?.organization?.id })
        .andWhere('client.status != :status', { status: UserStatus.DELETED })
        .andWhere('tanClientCredentials.status != :disStatus', {
          disStatus: IncomeTaxStatus.DISABLE,
        })
         .andWhere(
          new Brackets((qb) => {
            qb.where('demands.isInPortal != :portalStatus', { portalStatus: 'NO' }).orWhere(
              'demands.isInPortal IS NULL',
            );
          }),
        );;

      if (ViewAssigned && !ViewAll) {
        demands.andWhere('clientManagers.id = :userId', { userId });
      }

      if (query.search) {
       demands.andWhere(new Brackets((qb)=>{
          qb.where('client.displayName LIKE :search', { search: `%${query.search}%` });
          qb.orWhere('tanClientCredentials.tanNumber LIKE :tanSearch',{tanSearch : `%${query.search}%`});
      }))
      }

      if (query.formCode) {
          demands.andWhere('demands.formType = :formtyp', {
            formtyp: query.formCode
          });
      }

      if (query.quarter) {
        demands.andWhere('demands.quarter = :finQuarter', {
          finQuarter: query.quarter,
        });
      }

      if (query.financialYear) {
        demands.andWhere('demands.financialYear = :finY', {
          finY: query.financialYear,
        });
      }

      if (query.clientCategory) {
        demands.andWhere(`client.category = :clientCategory`, {
          clientCategory: query.clientCategory,
        });
      }


      const sort = (typeof query?.sort === "string") ? JSON.parse(query.sort) : query?.sort;
      if (sort?.column) {
          const columnMap: Record<string, string> = {
            id: 'demands.id',
            quarter: 'demands.quarter',
            name:'client.displayName',
            financialYear:'demands.financialYear',
            formType: 'demands.formType',
            netPayableAmount:'demands.netPayableAmount',
           
          };
          const column = columnMap[sort.column] || sort.column;
          demands.orderBy(column, sort.direction.toUpperCase());
      } else {
        demands.orderBy('demands.financialYear', 'DESC');
      };
      if (offset >= 0) {
        demands.skip(offset);
      }

      if (limit) {
        demands.take(limit);
      }

    
      let result = await demands.getManyAndCount();
      return {
        count: result[1],
        result: result[0],
      };
    } catch (error) {
      console.log('error occur while getting findAllDemands in tan', error);
    }
  }

   async findDemand(userId: number, id: number) {
    try {
      const user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
      const tracesDemand = await TraceOutstandingDemand.findOne({
        where: { id, organizationId: user?.organization?.id },
        relations: ['tanClientCredentials','tanClientCredentials.client'],
      });
      
      return tracesDemand ;
    } catch (error) {
      console.log('error occur while getting findForm in TAN', error);
    }
  }

   async getClientDemand(id: number, query: any, userId: number) {
    const user = await User.findOne({ where: { id: userId }, relations: ['organization', 'role'] });
    const { limit, offset } = query;
    try {
      const clientCredential = await TanClientCredentials.findOne({
        where: { id, organizationId: user?.organization?.id },
        relations: ['client'],
      });

      if (clientCredential) {
        const demandDetails = createQueryBuilder(TraceOutstandingDemand, 'demands')
          .leftJoinAndSelect('demands.tanClientCredentials', 'tanClientCredentials')
          .leftJoinAndSelect('tanClientCredentials.client', 'client')
          .where('tanClientCredentials.id =:id', { id: id })
           .andWhere(
            new Brackets((qb) => {
              qb.where('demands.isInPortal != :portalStatus', { portalStatus: 'NO' }).orWhere(
                'demands.isInPortal IS NULL',
              );
            }),
          );

        if (offset) {
          demandDetails.skip(offset);
        }

        if (limit) {
          demandDetails.take(limit);
        }
        const sort = (typeof query?.sort === "string") ? JSON.parse(query.sort) : query?.sort;
        if (sort?.column) {
            const columnMap: Record<string, string> = {
              financialYear: 'demands.financialYear',
              quarter: 'demands.quarter',
              formType: 'demands.formType',
              netPayableAmount:'demands.netPayableAmount',
            };
            const column = columnMap[sort.column] || sort.column;
            demandDetails.orderBy(column, sort.direction.toUpperCase());
        } else {
          demandDetails.orderBy('demands.financialYear', 'DESC');
        };
  

        let result = await demandDetails.getManyAndCount();
        return {
          count: result[1],
          result: result[0],
          accessDenied: true,
        };
      } else {
        return { accessDenied: false };
      }
    } catch (error) {
      console.log('error occured while fetching income tax client demands in TAN', error);
    }
  }

   async exportDemands(userId: number, query: any) {
      const newQuery = { ...query, offset: 0, limit: ********* };
      const { result: demands } = await this.findAllDemands(userId, newQuery);
  
      if (!demands.length) {
        throw new BadRequestException('No Data for Export');
      }
  
      const workbook = new ExcelJS.Workbook();
      const worksheet = workbook.addWorksheet('GSTR Demands');
  
      const headers = [
        { header: 'S.No', key: 'serialNo' },
        { header: 'Client Name', key: 'clientName' },
        { header: 'Category', key: 'category' },
        { header: 'Financal Year', key: 'financialYear' },
        { header: 'Quarter', key: 'quarter' },
        { header: 'Form Type', key: 'formType' },
        { header: 'Net Payable Rounded Off', key: 'netPayableAmount'}
  
      ];
  
      worksheet.columns = headers;
  
      const columnMaxLengths = Array(headers.length).fill(0);
      let serialCounter = 1;

        const numberFields = ['netPayableAmount'];


  
      for (const demand of demands) {
        const rowData: any = {
          serialNo: serialCounter++,
          clientName: demand?.tanClientCredentials?.client?.displayName || '-',
          category: categoryLabels[demand?.tanClientCredentials?.client?.category] || '-',
          financialYear: demand?.financialYear || '-',
          quarter: demand?.quarter || '-',
          formType: demand?.formType || '-',
          netPayableAmount:demand?.netPayableAmount || "-"
        };
  
        for (const key of numberFields) {
          const val = demand?.[key];
          rowData[key] = typeof val === 'number' ? val : val ? Number(val) : null;
        }
  
        const row = worksheet.addRow(rowData);
  
        // Apply number formatting
        for (const key of numberFields) {
          const colIndex = headers.findIndex(h => h.key === key) + 1;
          const cell = row.getCell(colIndex);
          if (rowData[key] !== null && rowData[key] !== undefined) {
            cell.numFmt = '₹#,##,##0.00';
          }
        }
  
        // Calculate max column length for autosizing
        worksheet.columns.forEach((column, colIndex) => {
          const headerLength = column.header?.toString().length || 0;
          const cellLength = rowData[column.key]?.toString().length || 0;
          columnMaxLengths[colIndex] = Math.max(
            columnMaxLengths[colIndex],
            headerLength,
            cellLength
          );
        });
      }
  
      // Set column widths
      worksheet.columns.forEach((column, colIndex) => {
        column.width = columnMaxLengths[colIndex] + 3;
      });
  
      // Style header row
      const headerRow = worksheet.getRow(1);
      headerRow.font = { bold: true };
      headerRow.eachCell((cell) => {
        cell.fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: '64B5F6' }
        };
        cell.border = {
          top: { style: 'thin' },
          left: { style: 'thin' },
          bottom: { style: 'thin' },
          right: { style: 'thin' }
        };
        cell.alignment = { horizontal: 'center', vertical: 'middle' };
      });
  
     
      // Style all data cells with borders
    worksheet.eachRow((row, rowNumber) => {
      row.eachCell((cell) => {
        cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
        cell.border = {
          top: { style: 'thin' },
          left: { style: 'thin' },
          bottom: { style: 'thin' },
          right: { style: 'thin' }
        };
      });
    });
  

  
      worksheet.views = [{ state: 'frozen', ySplit: 1 }];
  
      const buffer = await workbook.xlsx.writeBuffer();
      return buffer;
    }

        async exportClientDemand(userId: number, query: any) {
        const newQuery = { ...query, offset: 0, limit: ********* };
        const id = query.incometaxid;
        const { result: demands } = await this.getClientDemand(id,newQuery,userId);
    
        if (!demands.length) {
          throw new BadRequestException('No Data for Export');
        }
    
        const workbook = new ExcelJS.Workbook();
        const worksheet = workbook.addWorksheet('GSTR Demands');
    
        const headers = [
          { header: 'S.No', key: 'serialNo' },
          { header: 'Financal Year', key: 'financialYear' },
          { header: 'Quarter', key: 'quarter' },
          { header: 'Form Type', key: 'formType' },
          { header: 'Net Payable Rounded Off', key: 'netPayableAmount'}
    
        ];
    
        worksheet.columns = headers;
    
        const columnMaxLengths = Array(headers.length).fill(0);
        let serialCounter = 1;
    
        const numberFields = ['netPayableAmount'];
    
        for (const demand of demands) {
          const rowData: any = {
            serialNo: serialCounter++,
            financialYear: demand?.financialYear || '-',
            quarter: demand?.quarter || '-',
            formType: demand?.formType || '-',
            netPayableAmount:demand?.netPayableAmount || "-"
          };
    
          for (const key of numberFields) {
            const val = demand?.[key];
            rowData[key] = typeof val === 'number' ? val : val ? Number(val) : null;
          }
    
          const row = worksheet.addRow(rowData);
    
          // Apply number formatting
          for (const key of numberFields) {
            const colIndex = headers.findIndex(h => h.key === key) + 1;
            const cell = row.getCell(colIndex);
            if (rowData[key] !== null && rowData[key] !== undefined) {
              cell.numFmt = '₹#,##,##0.00';
            }
          }
    
    
          // Calculate max column length for autosizing
          worksheet.columns.forEach((column, colIndex) => {
            const headerLength = column.header?.toString().length || 0;
            const cellLength = rowData[column.key]?.toString().length || 0;
            columnMaxLengths[colIndex] = Math.max(
              columnMaxLengths[colIndex],
              headerLength,
              cellLength
            );
          });
        }
    
        // Set column widths
        worksheet.columns.forEach((column, colIndex) => {
          column.width = columnMaxLengths[colIndex] + 3;
        });
    
        // Style header row
        const headerRow = worksheet.getRow(1);
        headerRow.font = { bold: true };
        headerRow.eachCell((cell) => {
          cell.fill = {
            type: 'pattern',
            pattern: 'solid',
            fgColor: { argb: '64B5F6' }
          };
          cell.border = {
            top: { style: 'thin' },
            left: { style: 'thin' },
            bottom: { style: 'thin' },
            right: { style: 'thin' }
          };
          cell.alignment = { horizontal: 'center', vertical: 'middle' };
        });

        // Style all data cells with borders
      worksheet.eachRow((row, rowNumber) => {
        row.eachCell((cell) => {
       
          cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
          cell.border = {
            top: { style: 'thin' },
            left: { style: 'thin' },
            bottom: { style: 'thin' },
            right: { style: 'thin' }
          };
        });
      });
    
  
    
        worksheet.views = [{ state: 'frozen', ySplit: 1 }];
    
        const buffer = await workbook.xlsx.writeBuffer();
        return buffer;
      }
}
