import { MigrationInterface, QueryRunner } from 'typeorm';

export class AlterContactPersonTable1655212882320
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE contact_person
                               MODIFY COLUMN dsc_available boolean not null default false`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}
