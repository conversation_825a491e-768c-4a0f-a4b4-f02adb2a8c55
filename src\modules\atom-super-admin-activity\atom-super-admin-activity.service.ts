import { Injectable, NotFoundException } from '@nestjs/common';
import CreateAtomSuperAdminActivityDto from './dto/atom-super-admin-activity.dto';
import { User } from '../users/entities/user.entity';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { Event_Actions } from 'src/event-listeners/actions';
import { AtomSuperAdminActivity } from './atom-super-admin-activity.entity';
import { createQueryBuilder, getManager, Not } from 'typeorm';
import { Cron, CronExpression } from '@nestjs/schedule';
import { Role } from '../roles/entities/role.entity';
import { type } from 'os';
import { Checklist } from '../services/entities/checklist.entity';
import { Service } from '../services/entities/service.entity';

@Injectable()
export class AtomSuperAdminActivityService {
    constructor(private eventEmitter: EventEmitter2) { }

    // @Cron(CronExpression.EVERY_5_SECONDS)
    async create(userId: number, data: CreateAtomSuperAdminActivityDto) {
        let user = await User.findOne({
            where: { id: userId },
            relations: ['organization'],
        });

        let atomSuperAdminActivity = new AtomSuperAdminActivity()
        atomSuperAdminActivity.type = data.type
        atomSuperAdminActivity.typeId = data.id
        atomSuperAdminActivity.updatedUserId = userId
        atomSuperAdminActivity.toProduction = true
        await atomSuperAdminActivity.save();

        return atomSuperAdminActivity;
    }

    // @Cron(CronExpression.EVERY_5_SECONDS)
    async update(userId: number, id: number, data: CreateAtomSuperAdminActivityDto) {
        let atomSuperAdminActivity = await AtomSuperAdminActivity.findOne({ where: { id: 1 } });
        atomSuperAdminActivity.type = "Role"
        atomSuperAdminActivity.typeId = 0
        atomSuperAdminActivity.toProduction = true
        // atomSuperAdminActivity.lastUpdated = new Date().toISOString();
        await atomSuperAdminActivity.save()
        return atomSuperAdminActivity
    }
    // @Cron(CronExpression.EVERY_5_SECONDS)
    async deleteMultiple(ids: number[]) {
        const deletedRoles = [954, 953, 952, 951, 949, 948, 947, 946, 945];
      
        for (const id of deletedRoles) {
          const role = await AtomSuperAdminActivity.findOne(id);
      
          if (!role) {
            throw new NotFoundException(`AtomSuperAdminActivity with ID ${id} not found`);
          }
      
          await role.remove();
        //   deletedRoles.push(role);
        }
      
        return deletedRoles;
      }      

}
