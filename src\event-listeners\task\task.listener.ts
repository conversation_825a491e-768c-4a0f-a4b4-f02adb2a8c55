import { Injectable } from '@nestjs/common';
import { OnEvent } from '@nestjs/event-emitter';
import notify, { sendNotification } from 'src/notifications/notify';
import { TaskStatusEnum } from 'src/modules/tasks/dto/types';
import TaskStatus from 'src/modules/tasks/entity/task-status.entity';
import { User } from 'src/modules/users/entities/user.entity';
import { createQueryBuilder } from 'typeorm';
import Activity, { ActivityType } from '../../modules/activity/activity.entity';
import Task from '../../modules/tasks/entity/task.entity';
import { Event_Actions } from '../actions';
import ApprovalProcedures from 'src/modules/atm-qtm-approval/entities/approval-procedures.entity';
import {
  getUserDetails,
  insertINTONotificationUpdate,
  insertINTOnotification,
} from 'src/utils/re-use';
import Client from 'src/modules/clients/entity/client.entity';
import { Organization } from 'src/modules/organization/entities/organization.entity';
import ViderWhatsappSessions from 'src/modules/whatsapp/entity/viderWhatsappSessions';
import { sendWhatsAppTextMessage } from 'src/modules/whatsapp/whatsapp.service';
import { getTitle } from 'src/utils';
import * as moment from 'moment';
import ClientGroup from 'src/modules/client-group/client-group.entity';
import { fullMobileNumberWithCountry } from 'src/utils/validations/fullMobileWithCountry';

interface CreateTask {
  actor?: string;
  userId: number;
  clientId: number;
  task: Task;
  user: User;
  clients: any;
  newStatus: any;
  oldTaskStatus: any;
  newTaskStatus: any;
}

@Injectable()
export class TaskListener {
  @OnEvent(Event_Actions.TASK_CREATED, { async: true })
  async handleTaskCreation(event: any) {
    try {
      let user = await User.findOne({ where: { id: event?.userId } });
      const { userId, clientId, task } = event;

      let activity = new Activity();
      activity.action = Event_Actions.TASK_CREATED;
      activity.actorId = user.id;
      activity.type = ActivityType.CLIENT;
      activity.typeId = clientId;

      activity.remarks = `"${task?.name}" Task created by ${user.fullName}`;
      await activity.save();

      let taskStatus = new TaskStatus();
      taskStatus.restore = TaskStatusEnum.TODO;
      taskStatus.status = TaskStatusEnum.TODO;
      taskStatus.task = task;
      taskStatus.user = user;

      await taskStatus.save();
    } catch (err) {
      console.log(err);
    }
  }

  @OnEvent(Event_Actions.TASK_CREATED, { async: true })
  async sendNotificationOnTaskCreation(event: CreateTask) {
    const { task } = event;
    try {
      let { taskData, memberIds, orgAdmin, clientManager } = await this.getTaskData(570);

      let userIds = [...memberIds];

      if (orgAdmin) {
        userIds.push(orgAdmin.id);
      }

      if (clientManager) {
        userIds.push(clientManager.id);
      }

      let notification = {
        title: `Task created`,
        body: `Task "${taskData.name}" for the client  "${taskData.client.displayName}" has been created by ${taskData.user.fullName}`,
      };

      // await sendNotification(userIds, notification);
    } catch (err) {
      console.log(err);
    }
  }

  @OnEvent(Event_Actions.TASK_CREATED, { async: true })
  async sendEmailOnTaskCreation(event: CreateTask) {
    // const { task } = event;
    // try {
    // let { taskData, memberIds, orgAdmin, clientManager } = await this.getTaskData(task?.id);
    // console.log(memberIds)
    // let userIds = [...memberIds];
    // if (orgAdmin) {
    //   userIds.push(orgAdmin.id);
    // }
    // if (clientManager) {
    //   userIds.push(clientManager.id);
    // }
    // let notification = {
    //   title: `Task created`,
    //   body: `Task "${taskData.name}" for the client  "${taskData.client.displayName}" has been created by ${taskData.user.fullName}`,
    // };
    // // await sendNotification(userIds, notification);
    // await notify.taskCreated({ taskData, userIds });
    //
    // } catch (err) {
    // console.log(err);
    // }
  }

  @OnEvent(Event_Actions.TASK_STATUS_UPDATED, { async: true })
  async handleTaskStatusUpdate(event: CreateTask) {
    try {
      const { userId, clientId, task, newStatus, oldTaskStatus } = event;
      let user = await User.findOne({ where: { id: userId } });

      let activity = new Activity();
      activity.action = Event_Actions.TASK_STATUS_UPDATED;
      activity.actorId = user.id;
      activity.type = ActivityType.CLIENT;
      activity.typeId = task?.client?.id;
      activity.remarks = `"${task?.taskNumber}" Task moved from ${getTitle(
        oldTaskStatus,
      )} to ${getTitle(task?.status)} by ${user.fullName}`;
      await activity.save();

      let taskactivity = new Activity();
      taskactivity.action = Event_Actions.TASK_STATUS_UPDATED;
      taskactivity.actorId = user.id;
      taskactivity.type = ActivityType.TASK;
      taskactivity.typeId = task.id;
      taskactivity.remarks = `"${task?.taskNumber}" Task moved from ${getTitle(
        oldTaskStatus,
      )} to ${getTitle(task?.status)} by ${user.fullName}`;
      await taskactivity.save();

      let userIds = task?.members?.map((member: any) => member.id);

      let notification = {
        title: 'Task Status Updated',
        body: `${user.fullName} has moved ${task?.name}" to ${task?.status}`,
      };

      // await sendNotification(userIds, notification);

      await notify.taskStatusUpdated({
        taskName: task.name,
        taskStatus: task.status,
        userFullname: user.fullName,
        userIds,
      });
      //Todo: task done send espo mail
      if (task.status === TaskStatusEnum.COMPLETED) {
        // console.log('status-done-in-list');
      }
    } catch (err) {
      console.log(err);
    }
  }

  @OnEvent(Event_Actions.TASK_TERMINATED, { async: true })
  async handleTaskTermination(event: CreateTask) {
    try {
      const { userId, clientId, task, oldTaskStatus } = event;
      let user = await User.findOne(userId);
      let activity = new Activity();
      activity.action = Event_Actions.TASK_TERMINATED;
      activity.actorId = user.id;
      activity.type = ActivityType.CLIENT;
      activity.typeId = clientId;
      activity.remarks = `"${task.taskNumber}" Task terminated by ${user.fullName} from "${getTitle(
        oldTaskStatus,
      )}"`;
      await activity.save();

      let taskactivity = new Activity();
      taskactivity.action = Event_Actions.TASK_TERMINATED;
      taskactivity.actorId = user.id;
      taskactivity.type = ActivityType.TASK;
      taskactivity.typeId = task.id;
      taskactivity.remarks = `"${task.taskNumber}" Task terminated by ${
        user.fullName
      } from "${getTitle(oldTaskStatus)}" `;
      await taskactivity.save();

      let taskStatus = new TaskStatus();
      taskStatus.restore = taskStatus.status;
      taskStatus.status = TaskStatusEnum.TERMINATED;
      taskStatus.task = task;
      taskStatus.user = user;
      await taskStatus.save();
    } catch (err) {
      console.log(err);
    }
  }

  @OnEvent(Event_Actions.TASK_RESTORED, { async: true })
  async handleTaskRestoration(event: CreateTask) {
    try {
      const { userId, clientId, task, oldTaskStatus, newTaskStatus } = event;
      let user = await User.findOne(userId);
      let activity = new Activity();
      activity.action = Event_Actions.TASK_RESTORED;
      activity.actorId = user.id;
      activity.type = ActivityType.CLIENT;
      activity.typeId = clientId;
      activity.remarks = `"${task.taskNumber}" Task "${newTaskStatus}" Restored by ${
        user.fullName
      } to "${getTitle(oldTaskStatus)}"`;
      await activity.save();

      let taskactivity = new Activity();
      taskactivity.action = Event_Actions.TASK_RESTORED;
      taskactivity.actorId = user.id;
      taskactivity.type = ActivityType.TASK;
      taskactivity.typeId = task.id;
      taskactivity.remarks = `"${task.taskNumber}" Task "${newTaskStatus}" Restored by ${
        user.fullName
      } to "${getTitle(oldTaskStatus)}"`;
      await taskactivity.save();

      let taskStatus = new TaskStatus();
      taskStatus.restore = taskStatus.status;
      taskStatus.status = TaskStatusEnum.TERMINATED;
      taskStatus.task = task;
      taskStatus.user = user;
      await taskStatus.save();
    } catch (err) {
      console.log(err);
    }
  }

  @OnEvent(Event_Actions.TASK_DELETED, { async: true })
  async handleTaskDeletion(event: CreateTask) {
    try {
      const { userId, clientId, task, oldTaskStatus } = event;
      let user = await User.findOne(userId);
      let activity = new Activity();
      activity.action = Event_Actions.TASK_DELETED;
      activity.actorId = user.id;
      activity.type = ActivityType.CLIENT;
      activity.typeId = clientId;
      activity.remarks = `"${task?.taskNumber}" Task deleted from ${getTitle(oldTaskStatus)} by ${
        user.fullName
      }`;
      await activity.save();

      let taskactivity = new Activity();
      taskactivity.action = Event_Actions.TASK_DELETED;
      taskactivity.actorId = user.id;
      taskactivity.type = ActivityType.TASK;
      taskactivity.typeId = task.id;
      taskactivity.remarks = `"${task?.taskNumber}" Task deleted from ${getTitle(
        oldTaskStatus,
      )} by ${user.fullName}`;
      await taskactivity.save();

      let taskStatus = new TaskStatus();
      taskStatus.restore = taskStatus.status;
      taskStatus.status = TaskStatusEnum.DELETED;
      taskStatus.task = task;
      taskStatus.user = user;
      await taskStatus.save();

      let { orgAdmin, clientManager } = await this.getTaskData(task.id);

      let userIds = [];

      if (orgAdmin) userIds.push(orgAdmin.id);

      if (clientManager) userIds.push(clientManager.id);

      if (!userIds.length) return;

      let notification = {
        title: 'Task Deleted',
        body: `${user.fullName} has deleted a task '${task.name}'`,
      };

      // await sendNotification(userIds, notification);
    } catch (err) {
      console.log(err);
    }
  }

  async getTaskData(taskId: number) {
    try {
      let taskData = await createQueryBuilder(Task, 'task')
        .leftJoinAndSelect('task.parentTask', 'parentTask')
        .leftJoinAndSelect('task.organization', 'organization')
        .leftJoinAndSelect('task.client', 'client')
        .leftJoinAndSelect('task.clientGroup', 'clientGroup')
        .leftJoinAndSelect('client.clientManager', 'clientManager')
        .leftJoinAndSelect('task.user', 'user')
        .leftJoinAndSelect('task.members', 'members')
        .leftJoinAndSelect('organization.users', 'users')
        .leftJoinAndSelect('users.role', 'role')
        .where('task.id = :taskId', { taskId })
        .getOne();

      let memberIds = taskData?.members.map((member: User) => member?.id);

      let orgAdmin = taskData?.organization?.users.find((user: User) => user?.role?.defaultRole);

      let clientManager = taskData?.client?.clientManager;

      return {
        taskData,
        memberIds,
        orgAdmin,
        clientManager,
      };
    } catch (err) {
      console.log(err);
    }
  }

  @OnEvent(Event_Actions.TASK_STATUS_TO_UNDER_REVIEW, { async: true })
  async sendPushNOtToFirstLevelUser(event: any) {
    try {
      const { task }: { task: Task } = event;
      const {
        name: taskName,
        client,
        clientGroup,
        members,
      }: { name: string; client: Client; clientGroup: ClientGroup; members: User[] } = task;

      let clientName;

      if(client){
        clientName = client?.displayName;
      }

      if(clientGroup){
        clientName = clientGroup?.displayName;
      }

      const { user }: { user: User } = task;
      const { fullName: requesterName, mobileNumber: approverNumber } = user;
      const { approvalProcedures } = task;
      const { id } = approvalProcedures;
      const approvalsData = await ApprovalProcedures.findOne({
        where: {
          id,
        },
        relations: ['approval', 'approval.approvalLevels', 'approval.approvalLevels.user'],
      });
      const firstApprovalLevelArray = approvalsData.approval.approvalLevels[0]?.user;
      const { id: userID, organization, fullName: approverName } = firstApprovalLevelArray;
      const { id: orgId } = organization;
      const key = 'TASK_APPROVAL_PUSH';
      const title = 'Task Received for Review';
      const body = `<strong>${requesterName}</strong> has requested you to review <strong>${taskName}</strong> of <strong>${clientName}</strong>`;
      let activity = new Activity();
      activity.action = Event_Actions.TASK_RECEIVED_FOR_REVIEW;
      activity.actorId = user.id;
      activity.type = ActivityType.TASK;
      activity.typeId = task?.id;
      activity.remarks = `${event?.user?.fullName} requested ${approverName}'s approval for this task`;
      await activity.save();
      const users: any = [firstApprovalLevelArray.id];
      // insertINTOnotification(title, body, users, orgId);
      insertINTONotificationUpdate(title, body, users, orgId, key);

      //for task-members

      const tmtitle = 'Task Send for Approval';
      const tmusers: any[] = members.map((user) => user.id);

      const tmbody = `<strong>${requesterName}</strong> has requested <strong>${approverName}</strong> to review <strong>${taskName}</strong> of <strong>${clientName}</strong>`;

      insertINTONotificationUpdate(tmtitle, tmbody, tmusers, orgId, key);
        // whatsapp
        try {
  
            for (let userId of [firstApprovalLevelArray.id]) {
              const sessionValidation = await ViderWhatsappSessions.findOne({
                where: { userId: userId,status:'ACTIVE' },
              });
              if (sessionValidation) {


                const loggedinuserDetails = await getUserDetails(userId);
                const { full_name: userFullName, mobile_number: userPhoneNumber } = loggedinuserDetails;
                const key = 'TASK_APPROVAL_WHATSAPP';
                const whatsappMessageBody = `
Hi ${userFullName},
Please Review the Task "${taskName}" and Approve / Reject

Task details:
Task ID: ${task?.taskNumber}
Task Name: ${task?.name}
Task Due Date: ${task?.dueDate}
Client Name:${clientName}
    
You may Approve or Reject the Task.
    
Thanks,
The ATOM Team `;
            await sendWhatsAppTextMessage(
              fullMobileNumberWithCountry(
                firstApprovalLevelArray.mobileNumber,
                firstApprovalLevelArray.countryCode,
              ),
              whatsappMessageBody,
              orgId,
              title,
              702,
              key,
            );
          }
        }
      } catch (error) {
        console.error('Error sending Client WhatsApp notification:', error);
      }
    } catch (e) {
      console.log(e);
    }
  }

  @OnEvent(Event_Actions.CAMUNDA_TASK_APPROVAL, { async: true })
  async sendPushNotToNextLevelUser(event: any) {
    const { nextLevelDetails, task }: { nextLevelDetails: any; task: Task } = event;
    const {
      name: taskName,
      client,
      clientGroup,
      members,
    }: { name: string; client: Client; clientGroup: ClientGroup; members: User[] } = task;

    let clientname;

    if(client){
      clientname = client?.displayName
    }

    if(clientGroup){
      clientname = clientGroup?.displayName
    }

    let taskStatus = await TaskStatus.findOne({
      where: {
        task: task.id,
      },
      relations: ['user'],
      order: {
        createdAt: 'DESC',
      },
    });

    const { user }: { user: User } = taskStatus;
    const { fullName: requesterName } = user;
    const { name, assignee, assigneeRoles, assigneeName, assigneeGroups } = nextLevelDetails[0];
    const orgId = parseInt(assigneeGroups.match(/\d+/)[0]);
    const key = 'TASK_APPROVAL_PUSH';
    const title = 'Task Received for Review';
    const body = `${requesterName} has requested you to review ${taskName} of ${clientname}`;
    let activity = new Activity();
    activity.action = Event_Actions.TASK_RECEIVED_FOR_REVIEW;
    activity.actorId = user.id;
    activity.type = ActivityType.TASK;
    activity.typeId = task?.id;
    activity.remarks = `${event?.user?.fullName} requested ${assigneeName}'s approval for this task`;
    await activity.save();
    const users: any[] = [parseInt(assignee)];
    insertINTONotificationUpdate(title, body, users, orgId, key);

    try {
      if (users !== undefined) {
          const sessionValidation = await ViderWhatsappSessions.findOne({
            where: { userId: users ,status:'ACTIVE'},
          });
          if (sessionValidation) {
           const adminUserDetails = await getUserDetails(users);

            const { full_name: userFullName, mobile_number: userPhoneNumber,id ,organization_id} = adminUserDetails;
            const key = 'TASK_APPROVAL_WHATSAPP';
            const whatsappMessageBody = `
Hi ${userFullName}
 ${requesterName} has requested you to review the task "${taskName}" of "${clientname}" and Approve / Reject
  
  Task details:
  Task ID: ${task?.taskNumber}
  Task Name: ${task?.name}
  Task Due Date: ${task?.dueDate}
  Client Name: ${clientname}
  
You may Approve or Reject the Task.

Thanks,
We hope this helps!`;
            await sendWhatsAppTextMessage(
              `91${userPhoneNumber}`,
              whatsappMessageBody,
              organization_id,
              title,
              id,
              key,
            );
          }
        
      }
    } catch (error) {
      console.error('Error sending User WhatsApp notification:', error);
    }
    //for task members
    const tmtitle = 'Task Send for Approval';
    const tmusers: any[] = members.map((user) => user.id);

    const tmbody = `<strong>${requesterName}</strong> has requested <strong>${assigneeName}</strong> to review <strong>${taskName}</strong> of <strong>${clientname}</strong>`;

    insertINTONotificationUpdate(tmtitle, tmbody, tmusers, orgId, key);
    // whatsapp
    try {
      const sessionValidation = await ViderWhatsappSessions.findOne({
        where: { userId: parseInt(assignee),status:'ACTIVE' },
      });

      if (sessionValidation) {
        const approverDetails = await getUserDetails(parseInt(assignee));
        const {
          full_name: approverFullName,
          mobile_number: approverPhoneNumber,
          country_code: approverCountryCode,
        } = approverDetails;
        const whatsappKey = 'requested for review approval-nextlevel';
        const whatsappMessageBody = `
  Hi ${approverFullName},
  
  ${requesterName} has requested you to review the task "${taskName}" of "${clientname}" and Approve / Reject
  
  Task details:
  Task ID: ${task?.taskNumber}
  Task Name: ${task?.name}
  Task Due Date: ${task?.dueDate}
  Client Name: ${clientname}
  
  You may Approve or Reject the Task.
  
  Thanks,
  The ATOM Team`;

        await sendWhatsAppTextMessage(
          // `91${approverPhoneNumber}`,
          fullMobileNumberWithCountry(approverPhoneNumber, approverCountryCode),
          whatsappMessageBody,
          orgId,
          title,
          user?.id,
          whatsappKey,
        );
      }
    } catch (error) {
      console.error('Error sending Client WhatsApp notification:', error);
    }
  }

  @OnEvent(Event_Actions.CAMUNDA_TASK_DECLINE, { async: true })
  async sendPushToTaskMembers(event: any) {
    const { task, user }: { task: Task; user: User } = event;
    const { fullName: approverName } = user;
    const {
      organization,
      approvalProcedures,
      name: taskName,
      client,
      clientGroup,
      members,
    }: {
      organization: Organization;
      approvalProcedures: ApprovalProcedures;
      name: string;
      client: Client;
      clientGroup: ClientGroup;
      members: User[];
    } = task;
    const { id: orgId } = organization;

    let clientName;

    if(client){
      clientName = client?.displayName;
    }

    if(clientGroup){
      clientName = clientGroup?.displayName;
    }

    const users: any[] = members.map((user) => user.id);
    let taskStatus = await TaskStatus.findOne({
      where: {
        task: task.id,
      },
      relations: ['user'],
      order: {
        createdAt: 'DESC',
      },
    });
    const { user: requser }: { user: User } = taskStatus;
    const { fullName: requesterName } = requser;
    const key = 'TASK_APPROVAL_PUSH';
    const title = 'Task Rejected';
    const body = `<strong>${approverName}</strong> has rejected <strong>${taskName}</strong> of <strong>${clientName}</strong> sent for review by <strong>${requesterName}</strong>`;
    insertINTONotificationUpdate(title, body, users, orgId, key);
    // whatsapp
    try {
      for (const member of members) {
        const sessionValidation = await ViderWhatsappSessions.findOne({
          where: { userId: member?.id,status:'ACTIVE' },
        });
        if (sessionValidation) {
          // const loggedinuserDetails = await getUserDetails(member.id);
          // const { full_name: userFullName, mobile_number: userPhoneNumber } = loggedinuserDetails;
          const { fullName: userFullName, mobileNumber: userPhoneNumber, countryCode } = member;

          const key = 'TASK_APPROVAL_WHATSAPP';
          const whatsappMessageBody = `
  Hi ${userFullName},
  There's an update on the task "${taskName}" for client "${clientName}".

  The task has been rejected by the ${approverName}.
  
  Task details:
  Task ID: ${task?.taskNumber}
  Task Name: ${task?.name}
  Task Due Date: ${task?.dueDate}
  Client Name: ${clientName}
  
  You may check the task for more details.
  
  Thanks,
  The ATOM Team`;

          await sendWhatsAppTextMessage(
            // `91${userPhoneNumber}`,
            fullMobileNumberWithCountry(userPhoneNumber, countryCode),
            whatsappMessageBody,
            orgId,
            title,
            member?.id,
            key,
          );
        }
      }
    } catch (error) {
      console.error('Error sending WhatsApp notification:', error);
    }
  }

  @OnEvent(Event_Actions.CAMUNDA_TASK_APPROVAL_COMPLETE, { async: true })
  async allApprovalsComplete(event: any) {
    const { task }: { task: Task } = event;
    if(task?.id){
      const taskId = task.id;
      const {
        name: taskName,
        client,
        members,
        clientGroup,
        organization,
      }: { name: string; client: Client; clientGroup: ClientGroup; members: User[]; organization: Organization } = task;
  
      let clientName;
  
      if(client){
        clientName = client?.displayName;
      }
  
      if(clientGroup){
        clientName = clientGroup?.displayName
      }
  
      let taskStatus = await TaskStatus.findOne({
        where: {
          task: task.id,
        },
        relations: ['user'],
        order: {
          createdAt: 'DESC',
        },
      });
      const { user: requser }: { user: User } = taskStatus;
      const { fullName: requesterName } = requser;
      const key = 'TASK_APPROVAL_PUSH';
      const title = 'Task Approved';
      const body = `<strong>${taskName}</strong> of <strong>${clientName}</strong> has been approved by everyone as requested by <strong>${requesterName}</strong>`;
      const users: any[] = members.map((user) => user.id);
      const taskdata= await Task.findOne({where:{id: taskId}, relations:["taskLeader"]})
                  const taskLeader:any= taskdata?.taskLeader;
                  if (taskLeader && Array.isArray(taskLeader)) {
                    taskLeader.forEach(leader => {
                        if (!users.includes(leader.id)) {
                          users.push(leader.id);
                        }
                    });
                }
      const { id: orgId } = organization;
      insertINTONotificationUpdate(title, body, users, orgId, key);
      try {
        for (const member of members) {
          const sessionValidation = await ViderWhatsappSessions.findOne({
            where: { userId: member.id,status:'ACTIVE' },
          });
          if (sessionValidation) {
  
            const loggedinuserDetails = await getUserDetails(member.id);
  
            // const { full_name: userFullName, mobile_number: userPhoneNumber } = loggedinuserDetails;
            const { fullName: userFullName, mobileNumber: userPhoneNumber, countryCode } = member;
  
            const key = 'TASK_APPROVAL_WHATSAPP';
            const whatsappMessageBody = `
    Hi ${userFullName},
    There's an update on the task "${taskName}" for client "${clientName}".
    
    The task has been approved by everyone.
    
    Task details:
    Task ID: ${task?.taskNumber}
    Task Name: ${task?.name}
    Task Due Date: ${task?.dueDate}
    Client Name: ${clientName}
    
    As everyone approved your task, you can now update the status to "DONE".
    
    Thanks,
    The ATOM Team`;
  
            await sendWhatsAppTextMessage(
              // `91${userPhoneNumber}`,
              fullMobileNumberWithCountry(userPhoneNumber, countryCode),
              whatsappMessageBody,
              orgId,
              title,
              member?.id,
              key,
            );
          }
        }
      } catch (error) {
        console.error('Error sending WhatsApp notification:', error);
      }  
    }
  }

  @OnEvent(Event_Actions.PRIORITY_UPDATED, { async: true })
  async handleTaskPrioirty(event: any) {
    try {
      const { user, oldPriority, task } = event;

      let activity = new Activity();
      activity.action = Event_Actions.PRIORITY_UPDATED;
      activity.actorId = user.id;
      activity.type = ActivityType.TASK;
      activity.typeId = task.id;

      activity.remarks = `"${task?.taskNumber}" Task Priority Changed from "${getTitle(
        oldPriority,
      )}" to "${getTitle(task.priority)}" by ${user.fullName}`;
      await activity.save();
    } catch (err) {
      console.log(err);
    }
  }

  @OnEvent(Event_Actions.START_DATE_UPDATED, { async: true })
  async handleTaskStartDate(event: any) {
    try {
      const { user, oldStartDate, task } = event;

      let activity = new Activity();
      activity.action = Event_Actions.START_DATE_UPDATED;
      activity.actorId = user.id;
      activity.type = ActivityType.TASK;
      activity.typeId = task.id;

      activity.remarks = `"${task?.taskNumber}" Task Start Date Changed from ${moment(
        oldStartDate,
      ).format('DD-MM-YYYY')} to ${moment(task.taskStartDate).format('DD-MM-YYYY')} by ${
        user.fullName
      }`;
      await activity.save();
    } catch (err) {
      console.log(err);
    }
  }

  @OnEvent(Event_Actions.STATUTORY_DUE_DATE_CHANGED, { async: true })
  async handleTaskStatutoryDate(event: any) {
    try {
      const { user, oldDueDate, task } = event;

      let activity = new Activity();
      activity.action = Event_Actions.STATUTORY_DUE_DATE_CHANGED;
      activity.actorId = user.id;
      activity.type = ActivityType.TASK;
      activity.typeId = task.id;

      activity.remarks = `"${task?.taskNumber}" Task Statutory Due Date Changed from ${moment(
        oldDueDate,
      ).format('DD-MM-YYYY')} to ${moment(task.dueDate).format('DD-MM-YYYY')} by ${user.fullName}`;
      await activity.save();
    } catch (err) {
      console.log(err);
    }
  }

  @OnEvent(Event_Actions.EXPECTED_COMPLETEION_DATE_CHANGED, { async: true })
  async handleTaskExpectedDate(event: any) {
    try {
      const { user, oldExpectedDate, task } = event;

      let activity = new Activity();
      activity.action = Event_Actions.EXPECTED_COMPLETEION_DATE_CHANGED;
      activity.actorId = user.id;
      activity.type = ActivityType.TASK;
      activity.typeId = task.id;

      activity.remarks = `"${task?.taskNumber}" Task Expected Completion Date Changed ${
        oldExpectedDate ? `from ${moment(oldExpectedDate).format('DD-MM-YYYY')}` : ''
      } to ${moment(task.expectedCompletionDate).format('DD-MM-YYYY')} by ${user.fullName}`;
      await activity.save();
    } catch (err) {
      console.log(err);
    }
  }

  @OnEvent(Event_Actions.BILLING_TYPE_CHANGED, { async: true })
  async handleTaskBllingType(event: any) {
    try {
      const { user, oldBillingType, task } = event;

      let activity = new Activity();
      activity.action = Event_Actions.BILLING_TYPE_CHANGED;
      activity.actorId = user.id;
      activity.type = ActivityType.TASK;
      activity.typeId = task.id;

      activity.remarks = `"${task?.taskNumber}" Task Billing Type Changed from ${
        oldBillingType ? 'Billable' : 'Non-Billable'
      } to ${task.billable ? 'Billable' : 'Non-Billable'} by ${user.fullName}`;
      await activity.save();
    } catch (err) {
      console.log(err);
    }
  }

  @OnEvent(Event_Actions.FEE_TYPE_UPDATED, { async: true })
  async handleTaskFeeType(event: any) {
    try {
      const { user, oldFeeType, task } = event;

      let activity = new Activity();
      activity.action = Event_Actions.FEE_TYPE_UPDATED;
      activity.actorId = user.id;
      activity.type = ActivityType.TASK;
      activity.typeId = task.id;

      activity.remarks = `"${task?.taskNumber}" Task Fee Type Updated from ${getTitle(
        oldFeeType?.toLowerCase(),
      )} to ${getTitle(task?.feeType?.toLowerCase())} by ${user.fullName}`;
      await activity.save();
    } catch (err) {
      console.log(err);
    }
  }

  @OnEvent(Event_Actions.FEE_UPDATED, { async: true })
  async handleTaskFee(event: any) {
    try {
      const { user, oldfee, task } = event;

      let activity = new Activity();
      activity.actorId = user.id;
      activity.type = ActivityType.TASK;
      activity.typeId = task.id;
      activity.action = Event_Actions.FEE_UPDATED;
      activity.remarks = `"${task?.taskNumber}" Task Fee Changed Rs. ${
        oldfee ? oldfee : 0
      } to Rs. ${task.feeAmount ? task.feeAmount : 0} by ${user.fullName}`;
      await activity.save();
    } catch (err) {
      console.log(err);
    }
  }

  @OnEvent(Event_Actions.DESCRIPTION_UPDATED, { async: true })
  async handleTaskDescription(event: any) {
    try {
      const { user, oldDescription, task } = event;

      let activity = new Activity();
      activity.actorId = user.id;
      activity.type = ActivityType.TASK;
      activity.typeId = task.id;
      if (task.description === '<p><br></p>') {
        activity.action = Event_Actions.DESCRIPTION_REMOVED;
        activity.remarks = `"${task?.taskNumber}" Task Description Removed by ${user.fullName}`;
      } else {
        activity.action = Event_Actions.DESCRIPTION_UPDATED;
        activity.remarks = `"${task?.taskNumber}" Task Description Updated by ${user.fullName}`;
      }
      await activity.save();
    } catch (err) {
      console.log(err);
    }
  }

  @OnEvent(Event_Actions.LABELS_UPDATED, { async: true })
  async handleTaskLabels(event: any) {
    try {
      const { user, oldLablesName, newLablesName, task } = event;

      let activity = new Activity();
      activity.actorId = user.id;
      activity.type = ActivityType.TASK;
      activity.typeId = task.id;
      activity.action = Event_Actions.LABELS_UPDATED;
      activity.remarks = `Past Labels: ${
        oldLablesName?.length ? oldLablesName.join(', ') : 'NA'
      }\nCurrent Labels: ${newLablesName?.length ? newLablesName.join(', ') : 'NA'}`;
      await activity.save();
    } catch (err) {
      console.log(err);
    }
  }

  @OnEvent(Event_Actions.DIRECTORY_UPDATED, { async: true })
  async handleTaskDirectory(event: any) {
    try {
      const { user, oldDirectory, newDirectory, task } = event;

      let activity = new Activity();
      activity.actorId = user.id;
      activity.type = ActivityType.TASK;
      activity.typeId = task.id;
      activity.action = Event_Actions.DIRECTORY_UPDATED;
      activity.remarks = `"${task?.taskNumber}" Task Directory${
        newDirectory ? ` Updated to ${newDirectory}` : ' Removed'
      } by ${user.fullName}`;
      await activity.save();
    } catch (err) {
      console.log(err);
    }
  }
}
