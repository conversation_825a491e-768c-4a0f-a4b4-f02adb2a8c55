import {
  Injectable,
  NotFoundException,
  BadRequestException,
  InternalServerErrorException,
} from '@nestjs/common';
import {
  Brackets,
  create<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  More<PERSON>han,
} from 'typeorm';
import { ChannelPartner } from '../entity/channel-partner.entity';
import { CouponCode } from '../entity/coupon-code.entity';
import { ChannelPartnerSignup, leadStatusEnum } from '../entity/channel-partner-signup.entity';

@Injectable()
export class ChannelPartnerService {
  async getAllPartners() {
    const channelPartners = await ChannelPartner.find({ order: { createdAt: 'DESC' } });
    return channelPartners;
  }

  async getActivePartners() {
    const channelPartners = await ChannelPartner.find({
      where: { isActive: true },
      order: { createdAt: 'DESC' },
    });
    return channelPartners;
  }

  async createPartner(dto: any) {
    const partner = ChannelPartner.create(dto);
    return await ChannelPartner.save(partner);
  }

  async updatePartner(id: number, dto: any) {
    const partner = await ChannelPartner.findOne({ id });
    if (!partner) throw new NotFoundException('Channel partner not found');
    Object.assign(partner, dto);
    return await ChannelPartner.save(partner);
  }

  async updatePartnerToogle(id: number, dto: any) {
    const partner = await ChannelPartner.findOne({ id });
    if (!partner) throw new NotFoundException('Channel partner not found');
    partner.isActive = dto.isActive;
    return await ChannelPartner.save(partner);
  }


  async getAllCoupons() {
    const couponCodes = await CouponCode.find({
      relations: ['channelPartner'],
      order: { createdAt: 'DESC' },
    });
    return couponCodes;
  }

  async createCoupon(dto: any) {
    try {
      const partner = await ChannelPartner.findOne({
        id: dto.channelPartnerId,
      });

      if (!partner) throw new BadRequestException('Invalid partner ID');

      const coupon = CouponCode.create({
        ...dto,
        channelPartner: partner,
      });

      return await CouponCode.save(coupon);
    } catch (error) {
      if (error.code === 'ER_DUP_ENTRY') {
        throw new BadRequestException("Can't have duplicate coupon code");
      }
      throw new InternalServerErrorException('Failed to create coupon');
    }
  }

  async updateCoupon(id: number, dto: any) {
    try {
      const coupon = await CouponCode.findOne({
        where: { id },
        relations: ['channelPartner'],
      });

      if (!coupon) throw new NotFoundException('Coupon not found');

      if (dto.channelPartnerId) {
        const partner = await ChannelPartner.findOne({
          id: dto.channelPartnerId,
        });
        if (!partner) throw new BadRequestException('Invalid partner ID');
        coupon.channelPartner = partner;
      }

      Object.assign(coupon, dto);
      return CouponCode.save(coupon);
    } catch (error) {
      if (error.code === 'ER_DUP_ENTRY') {
        throw new BadRequestException("Can't have duplicate coupon code");
      }
      throw new InternalServerErrorException('Failed to create coupon');
    }
  }


  // Validate Coupon (Signup Flow)
  async validateCoupon(dto: any) {
    const { couponCode, channelPartnerId } = dto;
    const now = new Date();

    const coupon = await CouponCode.findOne({
      where: { code: couponCode },
      relations: ['channelPartner'],
    });

    if (!coupon) {
      throw new BadRequestException('Invalid coupon code');
    }

    if (channelPartnerId && coupon.channelPartner?.id !== channelPartnerId) {
      throw new BadRequestException(`Coupon belongs to partner "${coupon.channelPartner?.name}"`);
    }

    if (new Date(coupon.validFrom) > now || new Date(coupon.validTo) < now) {
      throw new BadRequestException('Coupon is expired or not yet valid');
    }

    return {
      message: 'Coupon is valid',
      couponId: coupon.id,
      partnerId: coupon.channelPartner?.id,
    };
  }

  async getSignUps(query: any) {
    const { limit, offset, search, status } = query;

    const signUpRecords = createQueryBuilder(ChannelPartnerSignup, 'signUps')
      .leftJoinAndSelect('signUps.channelPartner', 'channelPartner')
      .leftJoinAndSelect('signUps.coupon', 'coupon')
      .leftJoinAndSelect('signUps.user', 'user')
      .leftJoinAndSelect('user.organization', 'organization');

    if (search) {
      signUpRecords.where(
        new Brackets((qb) => {
          qb.where('user.fullName LIKE :search1', {
            search1: `%${query.search}%`,
          });
          qb.orWhere('coupon.code LIKE :search2', {
            search2: `%${query.search}%`,
          });
          qb.orWhere('channelPartner.name LIKE :search3', {
            search3: `%${query.search}%`,
          });
        }),
      );
    }

    if (status) {
      signUpRecords.andWhere('signUps.leadStatus = :leadStatus', { leadStatus: status });
    }

    if (offset >= 0) {
      signUpRecords.skip(offset);
    }

    if (limit) {
      signUpRecords.take(limit);
    }

    const result = await signUpRecords.getManyAndCount();

    return {
      count: result[1],
      result: result[0],
    };
  }

  async updateSignUpStatus(id: number, data: any) {
    const { status } = data;

    const signUpRecord = await ChannelPartnerSignup.findOne({ id });

    if (signUpRecord) {
      signUpRecord.leadStatus = leadStatusEnum[status];
      await signUpRecord.save();
    }
    return;
  }

  async getPartnerAnalytics(id: number) {
    const partner = await ChannelPartner.findOne({ where: { id } });
    const today = new Date();

    const [activeCoupons, expiredCoupons] = await Promise.all([
      CouponCode.count({
        where: {
          channelPartner: { id },
          validFrom: LessThan(today),
          validTo: MoreThan(today),
        },
      }),

      CouponCode.count({
        where: {
          channelPartner: { id },
          validTo: LessThan(today),
        },
      }),
    ]);

    const totalUsers = await ChannelPartnerSignup.count({ where: { channelPartner: { id } } });

    const leadCounts = await ChannelPartnerSignup.createQueryBuilder('signup')
      .select('signup.leadStatus', 'leadStatus')
      .addSelect('COUNT(*)', 'count')
      .where('signup.channelPartner.id = :id', { id })
      .groupBy('signup.leadStatus')
      .getRawMany();

    return {
      partner: {
        name: partner.name,
        status: partner.isActive,
        createdAt: partner.createdAt,
        updatedAt: partner.updatedAt,
      },
      coupons: {
        active: activeCoupons,
        expired: expiredCoupons,
      },
      users: totalUsers,
      leadStatusCounts: leadCounts,
    };
  }
}
