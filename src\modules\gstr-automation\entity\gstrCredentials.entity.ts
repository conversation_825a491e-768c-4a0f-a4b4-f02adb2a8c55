import Client from 'src/modules/clients/entity/client.entity';
import {
  BaseEntity,
  Column,
  CreateDateColumn,
  Entity,
  JoinColumn,
  ManyToOne,
  OneToMany,
  OneToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import AutomationMachines from 'src/modules/automation/entities/automation_machines.entity';
import Password from 'src/modules/clients/entity/password.entity';
import GstrProfile from './gstrProfile.entity';
import { decrypt, encrypt } from 'src/utils/encryption';
import { GstrOutstandingDemand } from './gstrDemands.entity';
import { GstrLedgerBalance } from './gstrLedgersBalance.entity';

export enum GstrStatus {
  ENABLE = 'ENABLE',
  DISABLE = 'DISABLE',
}

@Entity()
class GstrCredentials extends BaseEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  userName: string;

  // @Column()
    @Column({
    transformer: {
      to: (value: string) => (value ? encrypt(value) : value),
      from: (value: string) => (value ? decrypt(value) : value),
    },
  })
  password: string;

  @Column()
  userId: number;

  @Column()
  clientId: number;

  @Column({ type: 'enum', enum: GstrStatus, default: GstrStatus.ENABLE })
  status: GstrStatus;

  @Column()
  passwordId: number;

  @OneToMany(() => AutomationMachines, (automationMachines) => automationMachines.gstrCredentials)
  gstrCredentials: AutomationMachines[];

  @ManyToOne(() => Client, (client) => client.gstrCredentials, { onDelete: 'SET NULL' })
  client: Client;

  @OneToOne(() => Password, (password) => password.gstrCredentials, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'password_id' })
  passwordI: Password;

  @OneToOne(()=> GstrProfile, (gstrProfile)=> gstrProfile.gstrCredentials)
  profile:GstrProfile;

  @OneToMany(()=> GstrOutstandingDemand, (gstrOutstandingDemand)=> gstrOutstandingDemand.gstrCredentials)
  gstrOutstandingDemand:GstrOutstandingDemand;

  @OneToMany(()=> GstrLedgerBalance, (gstrLedgersBalance) => gstrLedgersBalance.gstrCredentials)
  gstrLedgersBalance:GstrLedgerBalance;

  @Column()
  organizationId: number;

  @Column()
  remarks:string;

  @CreateDateColumn()
  createdAt: string;

  @UpdateDateColumn()
  updatedAt: string;
}

export default GstrCredentials;
