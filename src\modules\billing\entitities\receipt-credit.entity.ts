import Client from 'src/modules/clients/entity/client.entity';
import {
  BaseEntity,
  Column,
  CreateDateColumn,
  Entity,
  JoinColumn,
  ManyToOne,
  OneToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import Receipt from './receipt.entity';
import { BillingEntity } from 'src/modules/organization/entities/billing-entity.entity';
import ClientGroup from 'src/modules/client-group/client-group.entity';

export enum CreditType {
  CREDIT = 'CREDIT',
  DEBIT = 'DEBIT',
}

export enum ReceiptCreditStatus {
  CREATED = 'CREATED',
  CANCELLED = 'CANCELLED',
}

@Entity('receipt_credit')
class ReceiptCredit extends BaseEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ type: 'decimal', precision: 19, scale: 2 })
  amount: number;

  @Column({ type: 'enum', enum: CreditType, })
  type: CreditType;

  @Column({ type: 'enum', enum: ReceiptCreditStatus })
  status: ReceiptCreditStatus;

  @ManyToOne(() => Client, (client) => client.receiptCredits)
  client: Client;

  @ManyToOne(() => ClientGroup, (clientGroup) => clientGroup.receiptCredits)
  clientGroup: ClientGroup;

  @ManyToOne(() => BillingEntity, (billingEntity) => billingEntity.receiptCredits)
  billingEntity: BillingEntity;

  @OneToOne(() => Receipt, (receipt) => receipt.receiptCredit)
  @JoinColumn()
  receipt: Receipt;

  @UpdateDateColumn()
  updatedAt: string;

  @CreateDateColumn()
  createdAt: string;
}

export default ReceiptCredit;
