import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import AutProfileDetails from './entities/aut-profile-details.entity';
import { AutProfileDetailsController } from './controllers/aut-profile-details.controller';
import { AutProfileDetailsService } from './services/aut-profile-details.service';
import { AutIncomeTaxFormsController } from './controllers/income-tax-forms.controller';
import { AutIncomeTaxFormsService } from './services/income-tax-forms.services';
import AutIncomeTaxForms from './entities/aut_income_tax_forms.entity';
import AutClientCredentials from './entities/aut_client_credentials.entity';
import AutIncometaxReturns from './entities/aut_incometax_returns.entity';
import AutOutstandingDemand from './entities/aut-outstanding-demand.entity';
import AutFyaNotice from './entities/aut_income_tax_eproceedings_fya_notice.entity';
import AutEProceedingFya from './entities/aut_income_tax_eproceedings_fya.entity';
import AutEProceedingFyi from './entities/aut_income_tax_eproceedings_fyi.entity';
import AutFyiNotice from './entities/aut_income_tax_eproceedings_fyi_notice.entity';
import AutJurisdictionDetails from './entities/aut-jurisdiction-details.entity';
import AutoIncomeTaxForms from './entities/aut_income_tax_forms.entity';
import AutActivity from './entities/aut_activity.entity';
import AutomationMachines from './entities/automation_machines.entity';
import { ClientPasswordService } from '../clients/services/client-passwords.service';
import { ClientService } from '../clients/services/clients.service';
import { StorageService } from '../storage/storage.service';
import { AwsService } from '../storage/upload.service';
import { OneDriveStorageController } from '../ondrive-storage/onedrive-storage.controller';
import { GoogleDriveStorageService } from '../ondrive-storage/googledrive-storage.service';
import { AttachmentsService } from '../tasks/services/attachments.service';
import { OneDriveStorageService } from '../ondrive-storage/onedrive-storage.service';
import AutUpdateTracker from './entities/aut_update_tracker.entity';
import { AutDashboardService } from './services/dashboard.services';
import { AutDashboardController } from './controllers/dashboard.controller';
import AutProceedingResponseFyi from './entities/aut_income_tax_eproceedings_fyi_notice_response.entity';
import AutProceedingResponseFya from './entities/aut_income_tax_eproceedings_fyi_notice_response_fya.entity';
import AtomProLimitRequests from './entities/atomProLimitRequests.entity';
import { IncomeTaxConfigService } from './services/config.services';
import { IncomeTaxConfigController } from './controllers/config.controller';
import { IncomeTaxCronJobService } from './services/incometax-cron-job-service';
import { BharathCloudService } from '../storage/bharath-upload.service';
import { BharathStorageService } from '../storage/bharath-storage.service';
import AutEChallan from './entities/aut_incometax_e-challan.entity';
import AutMyCas from './entities/aut_incometax_my-cas.entity';
import AutomationMachinesArchive from './entities/automation_machines_archive.entity';
import { AutClientCredentialsSubscriber } from 'src/event-subscribers/AutClientCredentials.subscriber';
import AutDemandResponse from './entities/aut-demand-resposne';
import AutomationServers from './entities/automationServers.entity';
import IncTempEproFyi from './entities/inc_temp_epro_fyi.entity';
import IncTempEproFya from './entities/inc_temp_epro_fya.entity';
import AutomationBulkSync from './entities/automation_bulk_sync';
import { PanCronController } from './controllers/pan-cron.controller';
import { AttachmentFyaService } from './services/attachments-fya.service';
import { AttachmentFyaController } from './controllers/attachments-fya.controller';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      AutProfileDetails,
      AutIncomeTaxForms,
      AutClientCredentials,
      AutIncometaxReturns,
      AutOutstandingDemand,
      AutEProceedingFya,
      AutFyaNotice,
      AutEProceedingFyi,
      AutFyiNotice,
      AutJurisdictionDetails,
      AutoIncomeTaxForms,
      AutActivity,
      AutomationMachines,
      AutUpdateTracker,
      AutProceedingResponseFyi,
      AutProceedingResponseFya,
      AtomProLimitRequests,
      AutEChallan,
      AutMyCas,
      AutomationMachinesArchive,
      AutDemandResponse,
      AutomationServers,
      IncTempEproFyi,
      IncTempEproFya,
      AutomationBulkSync,
    ]),
  ],
  controllers: [
    AutProfileDetailsController,
    AutIncomeTaxFormsController,
    AutDashboardController,
    IncomeTaxConfigController,
    PanCronController,
    AttachmentFyaController,
  ],
  providers: [
    AutProfileDetailsService,
    AutIncomeTaxFormsService,
    ClientPasswordService,
    ClientService,
    StorageService,
    AwsService,
    OneDriveStorageService,
    AttachmentsService,
    BharathStorageService,
    BharathCloudService,
    GoogleDriveStorageService,
    AutDashboardService,
    IncomeTaxConfigService,
    IncomeTaxCronJobService,
    AutClientCredentialsSubscriber,

    AttachmentFyaService,
  ],
})
export class AutomationModule {}
