import { Organization } from 'src/modules/organization/entities/organization.entity';
import {
  BaseEntity,
  Column,
  Entity,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import DocumentInOut from './document-in-out.entity';
import DocumentsData from './documents-data.entity';

export enum LeadStatusEnum {
  PENDING = 'PENDING',
  CONVERTED = 'CONVERTED',
}

@Entity()
class DocumentCategory extends BaseEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  name: string;

  @Column()
  type: string;

  @ManyToOne(() => Organization, (organization) => organization.documentCategory)
  organization: Organization;

  @OneToMany(() => DocumentInOut, (documentInOut) => documentInOut.keptAt)
  documentInOut: DocumentInOut[];

  @OneToMany(() => DocumentsData, (documentData) => documentData.documentCategory)
  documentData: DocumentsData[];
}

export default DocumentCategory;
