import { Injectable } from '@nestjs/common';
import { User } from 'src/modules/users/entities/user.entity';
import { createQueryBuilder } from 'typeorm';
import CreateLabelDto from 'src/modules/labels/dto/create-label.dto';
import Label from 'src/modules/labels/label.entity';

@Injectable()
export class LabelService {
  async findAll(userId: number, query: any) {
    let user = await User.findOne({
      where: { id: userId },
      relations: ['organization'],
    });

    let labels = await createQueryBuilder(Label, 'label')
      .leftJoinAndSelect('label.organization', 'organization')
      .where('organization.id = :orgId', { orgId: user.organization?.id })
      .orWhere('label.defaultOne is true')

    if (query.offset >= 0) {
      labels.skip(query.offset);
    }
    if (query.limit) {
      labels.take(query.limit);
    }  
    return labels.getManyAndCount();
  }

}
