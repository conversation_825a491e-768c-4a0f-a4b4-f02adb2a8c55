import {
  BaseEntity,
  Column,
  <PERSON>tity,
  ManyToOne,
  PrimaryGeneratedColumn,
} from 'typeorm';
import Checklist from './checklist.entity';

export enum ChecklistItemStatus {
  PENDING = 'PENDING',
  DONE = 'DONE',
}

@Entity()
class ChecklistItem extends BaseEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  name: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({
    type: 'enum',
    enum: ChecklistItemStatus,
    default: ChecklistItemStatus.PENDING,
  })
  status: ChecklistItemStatus;

  @ManyToOne(() => Checklist, (checklist) => checklist.checklistItems, {
    onDelete: 'CASCADE',
  })
  checklist: Checklist;
}

export default ChecklistItem;
