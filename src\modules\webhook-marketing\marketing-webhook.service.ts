// webhook.service.ts
import { Injectable } from '@nestjs/common';
import MarketingWebhookPayload from './entity/marketingWebhookPayload.entity';

@Injectable()
export class MarketingWebhookService {
  async getWebhook() {
    return 'iam webhook';
  }

  async receiveWebhook(data: any) {
    if (data['hub.verify_token'] !== '') {
      return data;
    }
  }

  processPayload(payload: any) {
    // Your logic to process and store the webhook payload goes here
    // For example, you can store it in a database
    // Replace the following code with your actual database storage logic
    // Example (using TypeORM as you mentioned in your previous code):

    // Import the WebhookPayload entity
    // import { WebhookPayload } from './entity/WebhookPayload';

    // Save the payload to the database using TypeORM
    const newWebhookPayload = new MarketingWebhookPayload();
    newWebhookPayload.payload = payload;
    newWebhookPayload.save().then((savedPayload) => {});
  }
}
