import { SelectQueryBuilder } from 'typeorm';
import Task from '../entity/task.entity';
import FindTasksQuery from './find-query.dto';

export interface UpdateStatusBody {
  status: TaskStatusEnum;
  restore: TaskStatusEnum;
  sourceItemsOrder: number[];
  destinationItemsOrder: number[];
}

export enum TaskStatusEnum {
  TODO = 'todo',
  IN_PROGRESS = 'in_progress',
  ON_HOLD = 'on_hold',
  UNDER_REVIEW = 'under_review',
  TERMINATED = 'terminated',
  DELETED = 'deleted',
  COMPLETED = 'completed',
  PENDING = 'pending',
}

export enum TaskRecurringStatus {
  PENDING = 'pending',
  CREATED = 'created',
  TERMINATED = 'terminated'
}

export enum PaymentStatusEnum {
  BILLED = 'BILLED',
  ESTIMATED = 'ESTIMATED',
  UNBILLED = 'UNBILLED',
}


export enum ProformaTaskStatus {
  GENEREATED = 'GENERATED',
  NOT_GENERATED = 'NOT_GENERATED',
}

export enum TaskStatus {
  TODO = 'todo',
  IN_PROGRESS = 'in_progress',
  ON_HOLD = 'on_hold',
  UNDER_REVIEW = 'under_review',
}

export enum RecurringFrequency {
  CUSTOM = 'custom',
  MONTHLY = 'monthly',
  QUARTERLY = 'quarterly',
  HALF_YEARLY = 'half_yearly',
  YEARLY = 'yearly',
}

export enum PriorityEnum {
  NONE = 'none',
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
}

export enum DateFilters {
  TODAY = 'today',
  YESTERDAY = 'yesterday',
  THIS_WEEK = 'this_week',
  LAST_WEEK = 'last_week',
  THIS_MONTH = 'this_month',
  LAST_MONTH = 'last_month',
  OVERDUE = 'overdue',
  CUSTOM = 'custom',
}

export enum DateFilterKeys {
  START_DATE = 'startDate',
  DUE_ON = 'dueOn',
  CREATED_ON = 'createdOn',
  COMPLETED_ON = 'completedOn',
  EXPECTED_COMPLETION_DATE = 'expectedCompletionDate'
}

export interface IFilterByDate {
  query: FindTasksQuery;
  dateFilterKey: DateFilterKeys;
  entityKey?: string;
  tasks: SelectQueryBuilder<Task>;
}

export enum QueryTypeEnum {
  USER = 'USER',
  SELF = 'SELF',
  ORGANIZATION = 'ORGANIZATION',
}

export enum DateKeys {
  TODAY = 'today',
  YESTERDAY = 'yesterday',
  WEEK_START = 'week_tart',
  WEEK_END = 'week_End',
  LAST_WEEK_START = 'last_week_start',
  LAST_WEEK_END = 'last_week_end',
  MONTH_START = 'month_start',
  MONTH_END = 'month_end',
  LAST_MONTH_START = 'last_month_start',
  LAST_MONTH_END = 'last_month_end',
}

export enum FeeType {
  HOURLY = 'HOURLY',
  TOTAL = 'TOTAL',
}

export enum ServiceType {
  CUSTOM = 'custom',
  STANDARD = 'standard',
}

export enum TaskType {
  NON_RECURRING = 'non_recurring',
  RECURRING = 'recurring',
}

export enum ExpenditureType {
  PURE_AGENT = 'PURE_AGENT',
  ADDITIONAL_CHARGES = 'ADDITIONAL',
}

export enum ExpenditureStatus {
  PENDING = 'PENDING',
  APPROVED = 'APPROVED',
  REJECTED = 'REJECTED',
}
