import {
  Connection,
  EntitySubscriberInterface,
  EventSubscriber,
  InsertEvent,
} from 'typeorm';
import { InvitedUser } from '../modules/users/entities/invited-user.entity';
import { insertINTOnotification, getAdminIDsBasedOnOrganizationId, getUserNamewithUserId } from 'src/utils/re-use';

@EventSubscriber()
export class InviteUserSubscriber implements EntitySubscriberInterface<InvitedUser> {
  constructor(private readonly connection: Connection) {
    connection.subscribers.push(this);
  }

  listenTo() {
    return InvitedUser;
  }

  async beforeInsert(event: InsertEvent<InvitedUser>) {
  }

  async afterInsert(event: InsertEvent<InvitedUser>) {
    const { organization, fullName } = event?.entity
    const { id } = organization
    const userName = await getUserNamewithUserId(event?.entity['userId'])
    const usersList = await getAdminIDsBasedOnOrganizationId(id)
    const title = "Organization User Invited";
    const body = `<strong>${userName}</strong> have invited <strong>${fullName}</strong>.`;
    insertINTOnotification(title, body, usersList, id)
  }
}
