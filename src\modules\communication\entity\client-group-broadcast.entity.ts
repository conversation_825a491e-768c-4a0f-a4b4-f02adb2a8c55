import Client from 'src/modules/clients/entity/client.entity';
import { Organization } from 'src/modules/organization/entities/organization.entity';
import Storage from 'src/modules/storage/storage.entity';
import {
  BaseEntity,
  Column,
  CreateDateColumn,
  Entity,
  JoinColumn,
  JoinTable,
  ManyToMany,
  ManyToOne,
  OneToMany,
  OneToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import BroadcastActivity from './broadcast-activity.entity';
import Label from 'src/modules/labels/label.entity';


@Entity()
class ClientGroupBroadcast extends BaseEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  groupName: string;

  // @Column()
  // labels: string;
  @OneToMany(() => BroadcastActivity, (broadcastActivity) => broadcastActivity.clientGroup)
  broadcastActivities: BroadcastActivity[];

  @Column({ nullable: true })
  description: string;
  
  @CreateDateColumn()
  createdAt: string;

  @UpdateDateColumn()
  updatedAt: string;

   @ManyToOne(() => Organization, (organization) => organization.clientGroupBroadcast)
  organization: Organization;


  @OneToOne(() => Label, (label) => label.clientGroupBroadcast)
  @JoinColumn()
  label: Label;
  

  @ManyToMany(() => Client, (client) => client.clientGroupBroadcast)
  @JoinTable()
  clients: Client[];

}

export default ClientGroupBroadcast;
