import { Body, Controller, Delete, Get, Param, ParseIntPipe, Post, Request, UseGuards } from '@nestjs/common';
import { JwtAuthGuard } from 'src/modules/users/jwt/jwt-auth.guard';
import AddPinDto from '../dto/add-pin.dto copy';
import { ClientPinsService } from '../services/client-pins.service';

@UseGuards(JwtAuthGuard)
@Controller('client-pins')
export class ClientPinsController {
  constructor(private clientPinsService: ClientPinsService) {}

  @Post()
  addPin(@Body() body: AddPinDto, @Request() req: any) {
    const { userId } = req.user;
    return this.clientPinsService.addPin(userId, body);
  }

  @Get()
  getPins(@Request() req: any) {
    const { userId } = req.user;
    return this.clientPinsService.getPins(userId);
  }

  @Delete(':id')
  deletePin(@Param('id', ParseIntPipe) id: number) {
    return this.clientPinsService.deletePin(id);
  }
}
