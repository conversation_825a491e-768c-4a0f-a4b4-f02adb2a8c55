import { MigrationInterface, QueryRunner } from 'typeorm';

export class AlterActivityTable1661755796591 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE activity
             ADD COLUMN remarkType varchar(255) NULL,
             MODIFY COLUMN type enum('clients','user','form','task') NOT NULL
            `,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}
