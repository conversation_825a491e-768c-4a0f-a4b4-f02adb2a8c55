import {
  Body,
  Controller,
  Get,
  Post,
  Put,
  Query,
  Req,
  UseGuards,
  Request,
  Res,
} from '@nestjs/common';
import { JwtAuthGuard } from 'src/modules/users/jwt/jwt-auth.guard';
import { CreatePosterDto } from './dto/create-poster.dto';
import { PosterService } from './poster.service';
import { Response } from 'express';

@Controller('poster')
export class PosterController {
  constructor(private service: PosterService) {}

  

  @UseGuards(JwtAuthGuard)
  @Get('/poster-config')
  getEvents(@Req() request: any, @Query() query: any) {
    const { userId } = request.user;
    return this.service.getPosterConfig(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Put('/logo')
  async updateImage(@Request() req: any, @Body() body: any) {
    const { userId } = req.user;
    return this.service.updateImage(userId, body);
  }

  @UseGuards(JwtAuthGuard)
  @Post('/config')
  createPoster(@Body() body: CreatePosterDto, @Req() request: any) {
    const { userId } = request.user;
    return this.service.create(userId, body);
  }

  @UseGuards(JwtAuthGuard)
  @Get('/poster-event-types')
  getPosterEventTypes() {
    return this.service.getPosterEventTypes();
  }

  @UseGuards(JwtAuthGuard)
  @Get('/poster-events')
  getPosterEventsByType(@Query('typeName') typeName: string) {
    return this.service.getPosterEventsByType(typeName);
  }

  @UseGuards(JwtAuthGuard)
  @Post('/poster-preview/template1')
  async createTemplate1(@Body() body: any, @Req() request: any) {
    const { userId } = request.user;
    const result = await this.service.createTemplate1(userId, body);
    return { template1: result };
  }

  @UseGuards(JwtAuthGuard)
  @Post('/poster-preview/template2')
  async createTemplate2(@Body() body: any, @Req() request: any) {
    const { userId } = request.user;
    const result = await this.service.createTemplate2(userId, body);
    return { template2: result };
  }

  @UseGuards(JwtAuthGuard)
  @Post('/poster-preview/template3')
  async createTemplate3(@Body() body: any, @Req() request: any) {
    const { userId } = request.user;
    const result = await this.service.createTemplate3(userId, body);
    return { template3: result };
  }

  @Get('/posters')
  getPosters(@Query() query: any) {
    return this.service.getPosters(query);
  }
}
