import { IsArray, IsNotEmpty, IsOptional } from 'class-validator';

export class UpdateProfileDto {
  @IsOptional()
  fullName: string;

  @IsOptional()
  mobileNumber: string;

  @IsOptional()
  workEmail: string;

  @IsOptional()
  fatherName: string;

  @IsOptional()
  id: number;

  @IsOptional()
  address: string;

  @IsOptional()
  image: string;

  @IsOptional()
  dob: string;

  @IsOptional()
  mfaEnabled: boolean;

  @IsOptional()
  @IsArray()
  specializations: Array<string>;

  @IsOptional()
  bankAccountNumber: string;

  @IsOptional()
  bankAccountHolderName: string;

  @IsOptional()
  bankName: string;

  @IsOptional()
  bankIfscCode: string;

  @IsOptional()
  aadharNumber: string;

  @IsOptional()
  aadharCard: string;

  @IsOptional()
  panNumber: string;

  @IsOptional()
  panCard: string;

  @IsOptional()
  drivingLicenseNumber: string;

  @IsOptional()
  drivingLicense: string;

  @IsOptional()
  role: number;

  @IsOptional()
  addharStorage: any;

  @IsOptional()
  panStorage: any;

  @IsOptional()
  profileSign: any;


  @IsOptional()
  drivingLicenseStorage: any;

  @IsOptional()
  imageStorage: any;

  @IsOptional()
  countryCode: any;

  @IsOptional()
  authorisedDesignation: string

  @IsOptional()
  authorisedName: string
}
