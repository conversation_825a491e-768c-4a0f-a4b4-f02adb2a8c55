import {
  Body,
  Controller,
  Get,
  Param,
  ParseIntPipe,
  Patch,
  Query,
  Req,
  UseGuards,
} from '@nestjs/common';
import { RecurringService } from './recurring.service';
import { JwtAuthGuard } from '../users/jwt/jwt-auth.guard';

@Controller('recurring-profile')
export class RecurringController {
  constructor(private service: RecurringService) {}

  @Get()
  async getRecurringProfiles(@Query() query: any) {
    return this.service.getRecurringProfiles(query);
  }

  @UseGuards(JwtAuthGuard)
  @Get('task')
  async getRecurringProfilesTask(@Query() query: any, @Req() req: any) {
    const { userId } = req?.user;
    return this.service.getRecurringProfilesTask(query, userId);
  }

  @UseGuards(JwtAuthGuard)
  @Patch('/tasks/:id')
  bulkUpdate(@Req() req: any, @Param() param, @Body() body: any) {
    const { userId } = req?.user;
    return this.service.bulkUpdateTask(userId, param?.id, body);
  }
}
