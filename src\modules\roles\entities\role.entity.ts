import { Organization } from 'src/modules/organization/entities/organization.entity';
import { User } from 'src/modules/users/entities/user.entity';
import {
  BaseEntity,
  Column,
  CreateDateColumn,
  Entity,
  JoinTable,
  ManyToMany,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { Permission } from './permission.entity';
import OrgApprovalLevel from 'src/modules/atm-qtm-approval/entities/org-approvals-level.entity';

@Entity()
export class Role extends BaseEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  name: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  @CreateDateColumn()
  createdAt: string;

  @UpdateDateColumn()
  lastUpdated: string;

  @Column({ default: true })
  active: boolean;

  @Column({ default: false })
  defaultOne: boolean;

  @Column({ default: false })
  defaultRole: boolean;

  @ManyToMany(() => Permission, {
    eager: true,
  })
  @JoinTable()
  permissions: Permission[];

  @ManyToOne(() => Organization, (organization) => organization.users, {
    eager: true,
  })
  organization: Organization;

  @OneToMany(() => User, (user) => user.role)
  users: User[];

  @OneToMany(() => OrgApprovalLevel, (appLevel) => appLevel.role)
  approvalLevels: OrgApprovalLevel[];


  async givePermissionTo(permissionName: string) {
    let permission = await Permission.findOne({ name: permissionName });

    let existingPermission = this.permissions.filter((p) => p.name == permissionName).length != 0;
    if (!existingPermission) {
      this.permissions.push(permission);
    }

    await this.save();
    let role = await Role.findOne(this.id);
    return role;
  }

  async revokePermissionTo(permissionName: string) {
    let permissions = this.permissions.filter((p) => p.name != permissionName);
    this.permissions = permissions;
    await this.save();
    return this;
  }

  async getAllPermissions(): Promise<Permission[]> {
    return this.permissions;
  }

  async getPermissionNames(): Promise<string[]> {
    return this.permissions.map((p) => p.name);
  }

  async can(permissionName: any): Promise<boolean> {
    return this.permissions.filter((p) => p.name == permissionName).length > 0;
  }
}
