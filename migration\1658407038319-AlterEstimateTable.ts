import { MigrationInterface, QueryRunner } from 'typeorm';

export class AlterEstimateTable1658407038319 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
        ALTER TABLE invoice_particular
        MODIFY COLUMN cgst enum('NON_TAXABLE','OUT_OF_SCOPE','NON_GST_SUPPLY','GST0','GST5','GST12','GST18','GST28') NULL,
        MODIFY COLUMN igst enum('NON_TAXABLE','OUT_OF_SCOPE','NON_GST_SUPPLY','GST0','GST5','GST12','GST18','GST28') NULL,
        MODIFY COLUMN sgst enum('NON_TAXABLE','OUT_OF_SCOPE','NON_GST_SUPPLY','GST0','GST5','GST12','GST18','GST28') NULL
   `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}
