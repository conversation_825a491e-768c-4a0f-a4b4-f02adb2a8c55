import { ConflictException, Injectable } from '@nestjs/common';
import { User } from 'src/modules/users/entities/user.entity';
import { UpdateOrganizationProfileDto } from '../dto/update-organization-profile.dto';
import * as ejs from 'ejs';
import * as nodemailer from 'nodemailer';
import { Notification } from 'src/notifications/notification.entity';
import { Organization, OrganizationCategory, StorageSystem } from '../entities/organization.entity';
import Storage from 'src/modules/storage/storage.entity';
import { StorageService } from 'src/modules/storage/storage.service';
import { OneDriveStorageService } from 'src/modules/ondrive-storage/onedrive-storage.service';
import { BharathCloudService } from 'src/modules/storage/bharath-upload.service';
import { getAdminEmailsBasedOnOrganizationId } from 'src/utils/re-use';
import BroadcastActivity, {
  Status,
} from 'src/modules/communication/entity/broadcast-activity.entity';
import { GoogleDriveStorageService } from 'src/modules/ondrive-storage/googledrive-storage.service';

@Injectable()
export class OrganizationsService {
  constructor(
    private storageService: StorageService,
    private oneDriveService: OneDriveStorageService,
    private googleDriveService: GoogleDriveStorageService,
    private bharathService: BharathCloudService
  ) { }
  async getOrganizationProfile(userId: number) {
    let user = await User.findOne({
      where: { id: userId },
      relations: [
        'organization',
        'organization.orgGstStorage',
        'organization.orgPanStorage',
        'organization.udinUsers',
      ],
    });
    return user.organization;
  }

  async getsmtp(userId: number) {
    let user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
    let organization = await Organization.findOne({ where: { id: user.organization.id } });
    return organization.smtp;
  }

  async updateOrganizationProfile(userId: number, body: UpdateOrganizationProfileDto) {
    let user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
    let organization = await Organization.findOne({
      where: { id: user.organization.id },
      relations: ['orgGstStorage', 'orgPanStorage'],
    });
    organization.legalName =
      body.legalName !== null && body.legalName !== undefined
        ? body.legalName.trim()
        : body.legalName;
    organization.tradeName =
      body.tradeName !== null && body.tradeName !== undefined
        ? body.tradeName.trim()
        : body.tradeName;
    organization.constitutionOfBusiness =
      body.constitutionOfBusiness !== null && body.constitutionOfBusiness !== undefined
        ? body.constitutionOfBusiness.trim()
        : body.constitutionOfBusiness;
    organization.placeOfSupply = body.placeOfSupply;
    organization.firstName = body.firstName;
    organization.lastName = body.lastName;
    organization.middleName = body.middleName;
    organization.mobileNumber = body.mobileNumber;
    organization.alternateMobileNumber = body.alternateMobileNumber;
    organization.gstVerified = body.gstVerified;
    organization.gstNumber = body.gstNumber;
    organization.gstAttachment = body.gstAttachment;
    organization.udinUsers = body.udinUsers;
    let gstStorage: Storage;
    if (body?.orgGstStorage) {
      if (organization?.orgGstStorage?.id) {
        if (body.orgGstStorage.name !== organization?.orgGstStorage?.name) {
          if (organization?.orgGstStorage.storageSystem === StorageSystem.AMAZON) {
            this.storageService.deleteAwsFile(organization?.orgGstStorage?.file);
          } else if (organization?.orgGstStorage.storageSystem === StorageSystem.MICROSOFT) {
            this.oneDriveService.deleteOneDriveFile(userId, organization?.orgGstStorage?.fileId);
          } else if (organization?.orgGstStorage.storageSystem === StorageSystem.GOOGLE) {
            this.googleDriveService.deleteGoogleDriveFile(userId, organization?.orgGstStorage?.fileId);
          } else if (organization?.orgGstStorage.storageSystem === StorageSystem.BHARATHCLOUD) {
            this.bharathService.deleteFile(user.organization.id, organization?.orgGstStorage?.file)
          }
        }
        gstStorage = await Storage.findOne({ where: { id: organization?.orgGstStorage?.id } });
        gstStorage.fileType = body?.orgGstStorage.fileType;
        gstStorage.fileSize = body?.orgGstStorage.fileSize;
        gstStorage.name = body?.orgGstStorage.name;
        gstStorage.file = body?.orgGstStorage.upload;
        gstStorage.show = body?.orgGstStorage.show;
        gstStorage.filePath = body?.orgGstStorage.filePath;
      } else {
        gstStorage = await this.storageService.addAttachements(userId, body?.orgGstStorage);
      }
    } else {
      if (organization?.orgGstStorage?.id) {
        const existingAStorage = await Storage.findOne({
          where: { id: organization?.orgGstStorage?.id },
        });
        await existingAStorage.remove();
        if (existingAStorage) {
          if (existingAStorage.storageSystem === StorageSystem.AMAZON) {
            this.storageService.deleteAwsFile(existingAStorage.file)
          } else if (existingAStorage.storageSystem === StorageSystem.MICROSOFT) {
            this.oneDriveService.deleteOneDriveFile(userId, existingAStorage.fileId);
          } else if (existingAStorage.storageSystem === StorageSystem.GOOGLE) {
            this.googleDriveService.deleteGoogleDriveFile(userId, existingAStorage.fileId);
          } else if (existingAStorage.storageSystem === StorageSystem.BHARATHCLOUD) {
            this.bharathService.deleteFile(user.organization.id, existingAStorage.file)
          }
        }
        organization.orgGstStorage = null;
      }
    }

    let panStorage: Storage;
    if (body?.orgPanStorage) {
      if (organization?.orgPanStorage?.id) {
        if (body.orgPanStorage.name !== organization?.orgPanStorage?.name) {
          if (organization?.orgPanStorage.storageSystem === StorageSystem.AMAZON) {
            this.storageService.deleteAwsFile(organization?.orgPanStorage?.file);
          } else if (organization?.orgPanStorage.storageSystem === StorageSystem.MICROSOFT) {
            this.oneDriveService.deleteOneDriveFile(userId, organization?.orgPanStorage?.fileId);
          } else if (organization?.orgPanStorage.storageSystem === StorageSystem.GOOGLE) {
            this.googleDriveService.deleteGoogleDriveFile(userId, organization?.orgPanStorage?.fileId);
          }
        }
        panStorage = await Storage.findOne({ where: { id: organization?.orgPanStorage?.id } });
        panStorage.fileType = body?.orgPanStorage.fileType;
        panStorage.fileSize = body?.orgPanStorage.fileSize;
        panStorage.name = body?.orgPanStorage.name;
        panStorage.file = body?.orgPanStorage.upload;
        panStorage.show = body?.orgPanStorage.show;
        panStorage.authId = user?.organization?.id;
        panStorage.filePath = body?.orgPanStorage.filePath;
      } else {
        panStorage = await this.storageService.addAttachements(userId, body?.orgPanStorage);
      }
    } else {
      if (organization?.orgPanStorage?.id) {
        const existingAStorage = await Storage.findOne({
          where: { id: organization?.orgPanStorage?.id },
        });
        const existingStorage = await existingAStorage.remove();
        if (existingStorage) {
          if (existingStorage.storageSystem === StorageSystem.AMAZON) {
            this.storageService.deleteAwsFile(existingStorage.file);
          } else if (existingStorage.storageSystem === StorageSystem.MICROSOFT) {
            this.oneDriveService.deleteOneDriveFile(userId, existingStorage.fileId);
          } else if (existingStorage.storageSystem === StorageSystem.GOOGLE) {
            this.googleDriveService.deleteGoogleDriveFile(userId, existingStorage.fileId);
          }

          else if (existingStorage.storageSystem === StorageSystem.BHARATHCLOUD) {
            this.bharathService.deleteFile(user.organization.id, existingStorage.file);
          }
        }
        organization.orgPanStorage = null;
      }
    }
    organization.panVerified = body.panVerified;
    organization.panNumber = body.panNumber;
    organization.panAttachment = body.panAttachment;
    organization.buildingNo = body.buildingNumber;
    organization.district = body.district;
    organization.location = body.location;
    organization.floorNumber = body.floorNumber;
    organization.buildingName =
      body.buildingName !== undefined && body.buildingName !== null
        ? body.buildingName.trim()
        : body.buildingName;
    organization.street =
      body.street !== undefined && body.street !== null ? body.street.trim() : body.street;
    organization.state = body.state;
    organization.city =
      body.city !== undefined && body.city !== null ? body.city.trim() : body.city;
    organization.pincode = body.pincode;
    organization.website =
      body.website !== undefined && body.website !== null ? body.website.trim() : body.website;
    organization.primaryContactFullName =
      body.primaryContactFullName !== undefined && body.primaryContactFullName !== null
        ? body.primaryContactFullName.trim()
        : body.primaryContactFullName;
    organization.primaryContactMobileNumber = body.primaryContactMobileNumber;
    organization.primaryContactEmail = body.primaryContactEmail;
    organization.primaryContactDesignation = body.primaryContactDesignation;
    organization.registrationDate = body.registrationDate;
    organization.registrationNumber =
      body.registrationNumber !== null && body.registrationNumber !== undefined
        ? body.registrationNumber.trim()
        : body.registrationNumber;
    organization.config = JSON.stringify(body.config);
    organization.loggedIn = JSON.stringify(body.loggedIn);
    organization.category = body.category as OrganizationCategory;
    organization.countryCode = body?.countryCode;
    organization.alternateCountryCode = body?.alternateCountryCode;
    organization.primaryContactCountryCode = body?.primaryContactCountryCode;
    const org = await organization.save();
    if (gstStorage) {
      gstStorage.orgGstStorage = org;
      await gstStorage.save();
    }

    if (panStorage) {
      panStorage.orgPanStorage = org;
      await panStorage.save();
    }
    return organization;
  }

  async updateSmtp(userId: number, body: any) {
    let defaultsmtp: any = {
      host: 'email-smtp.ap-south-1.amazonaws.com',
      port: 587,
      auth: {
        user: 'AKIA5GHOVJDTRJ3PAQ6E',
        pass: 'BFt/gc++ytmTt24jK/317ARm7RQPk9eS12ThV1hZ5Jgc',
      },
    };
    let user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
    let organization = await Organization.findOne({ where: { id: user.organization.id } });
    let smtp = organization.smtp;
    if (body.service === 'Gmail') {
      let mailWithType = body.user + body.service;
      smtp = {
        ...smtp,
        [mailWithType]: {
          service: body.service,
          auth: {
            user: body.user,
            pass: body.pass,
          },
          name: body.name,
        },
      };
    } else if (body.service === 'yahoo') {
      let mailWithType = body.user + body.service;
      smtp = {
        ...smtp,
        [mailWithType]: {
          host: 'smtp.mail.yahoo.com',
          port: 465,
          secure: true,
          auth: {
            user: body.user,
            pass: body.pass,
          },
          service: body.service,
          name: body.name,
        },
      };
    } else if (body.service === 'outlook') {
      let mailWithType = body.user + body.service;
      smtp = {
        ...smtp,
        [mailWithType]: {
          host: 'smtp.office365.com',
          port: 587,
          secure: false,
          auth: {
            user: body.user,
            pass: body.pass,
          },
          name: body.name,
          service: body.service,
        },
      };
    } else {
      smtp = null;
    }

    if (organization.smtp && Object.keys(organization.smtp).length >= 8) {
      throw new ConflictException("You can add only 8 SMTP's for Organzaition !");
    }

    organization.smtp = smtp;
    if (organization.config) {
      organization.config = JSON.stringify({
        demo: organization.config['demo'],
        userslimit: organization.config['userslimit'],
        subscriptionmode: organization.config['subscriptionmode'],
        expirydate: organization.config['expirydate'],
      });
    }
    organization.loggedIn = JSON.stringify(body.loggedIn);

    if (!organization.othersSmtp && smtp) {
      organization.othersSmtp = [
        `${body.user + body.service}`,
        smtp[`${body.user + body.service}`],
      ];
    }

    if (!organization.smtp) {
      const broadcastActivity = await BroadcastActivity.createQueryBuilder('broadcastActivity')
        .leftJoinAndSelect('broadcastActivity.organization', 'organization')
        .where('organization.id = :organizationId', { organizationId: user.organization.id })
        .andWhere('broadcastActivity.Status = :status', { status: Status.Ready })
        .getMany();

      if (broadcastActivity.length) {
        throw new ConflictException("Broadcast have Ready Status, Can't Change SMTP !");
      }
    }

    if (!organization.smtp) {
      organization.broadcastSmtp = null;
      organization.clientSmtp = null;
      organization.billingSmtp = null;
      organization.iproSmtp = null;
      organization.othersSmtp = null;
    }

    function sendMail() {
      return new Promise(async (resolve, reject) => {
        if (body.service === 'outlook' || body.service === 'Gmail' || body.service === 'yahoo') {
          let mailSmtp = { ...smtp[`${body.user + body.service}`] };
          let filePath = 'smtp';
          let html = {};
          let data = { email: body.user };
          let templatePath = `src/emails/templates/${filePath}.ejs`;
          let templateStr = ejs.fileLoader(templatePath);
          let template = ejs.compile(templateStr.toString());
          html = filePath == '' ? data : template(data);

          const customMailOptions = {
            from:
              body.service === 'outlook' || body.service === 'Gmail' || body.service === 'yahoo'
                ? body.name
                  ? `"${body.name}" <${body.user}>`
                  : body.user
                : '<EMAIL>',
            to: user.email,
            subject: 'Organization SMTP Updated',
            html: html,
          };

          if (body.service === 'outlook' || body.service === 'Gmail' || body.service === 'yahoo') {
            delete mailSmtp['name'];
          }

          if (body.service === 'outlook' || body.service === 'yahoo') {
            delete mailSmtp['service'];
          }
          const sendingSmtp =
            body.service === 'outlook' || body.service === 'Gmail' || body.service === 'yahoo'
              ? mailSmtp
              : defaultsmtp;
          const customtransporter = nodemailer.createTransport(sendingSmtp);
          customtransporter.sendMail(customMailOptions, function (error: any, info: any) {
            if (error) {
              console.log(error);
              reject(error);
              return error;
            } else {
              console.log(info.response);
              resolve(info.response);
              return error;
            }
          });
          let newNotification = new Notification();
          newNotification.title = "Organization SMTP's Updated";
          newNotification.body = `Organization SMTP's has been updated with the Email ID : ${body.user ? body.user : data?.email
            }.`;
          newNotification.user = user;
          newNotification.status = 'unread';
          await newNotification.save();
        } else {
          let filePath = 'smtp';
          let html = {};
          let data = { email: '<EMAIL>' };
          let templatePath = `src/emails/templates/${filePath}.ejs`;
          let templateStr = ejs.fileLoader(templatePath);
          let template = ejs.compile(templateStr.toString());
          html = filePath == '' ? data : template(data);

          let viderMailOptions = {
            from: {
              name: 'Vider',
              address: process.env.FROM_EMAIL,
            },
            to: user.email,
            subject: 'Organization SMTP Updated',
            html: html,
          };
          const customtransporter = nodemailer.createTransport(defaultsmtp);

          customtransporter.sendMail(viderMailOptions, function (error: any, info: any) {
            if (error) {
              console.log(error);
              reject(error);
              return error;
            } else {
              console.log(info.response);
              resolve(info.response);
              return error;
            }
          });
          let newNotification = new Notification();
          newNotification.title = 'New Organization SMTP Added';
          newNotification.body = `New Organization SMTP has been updated with the Email ID : ${body?.user ? body?.user : data?.email
            }.`;
          newNotification.user = user;
          newNotification.status = 'unread';
          await newNotification.save();
        }
      });
    }
    await sendMail();

    await organization.save();

    return organization;
  }

  async updateSmtpMail(userId: number, body: any) {
    let defaultsmtp: any = {
      host: 'email-smtp.ap-south-1.amazonaws.com',
      port: 587,
      auth: {
        user: 'AKIA5GHOVJDTRJ3PAQ6E',
        pass: 'BFt/gc++ytmTt24jK/317ARm7RQPk9eS12ThV1hZ5Jgc',
      },
    };

    let user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
    let organization = await Organization.findOne({ where: { id: user.organization.id } });
    let smtp = organization.smtp;

    const keyToRemove = Object.keys(body)[0];
    const deletedSmtp = smtp[keyToRemove];

    delete smtp[keyToRemove];
    organization.broadcastSmtp =
      (organization.broadcastSmtp ? organization.broadcastSmtp[0] : null) === keyToRemove
        ? null
        : organization.broadcastSmtp;
    organization.clientSmtp =
      (organization.clientSmtp ? organization.clientSmtp[0] : null) === keyToRemove
        ? null
        : organization.clientSmtp;
    organization.billingSmtp =
      (organization.billingSmtp ? organization.billingSmtp[0] : null) === keyToRemove
        ? null
        : organization.billingSmtp;
    organization.iproSmtp =
      (organization.iproSmtp ? organization.iproSmtp[0] : null) === keyToRemove
        ? null
        : organization.iproSmtp;
    organization.othersSmtp =
      (organization.othersSmtp ? organization.othersSmtp[0] : null) === keyToRemove
        ? null
        : organization.othersSmtp;

    organization.smtp = smtp;
    if (organization.config) {
      organization.config = JSON.stringify({
        demo: organization.config['demo'],
        userslimit: organization.config['userslimit'],
        subscriptionmode: organization.config['subscriptionmode'],
        expirydate: organization.config['expirydate'],
      });
    }
    organization.loggedIn = JSON.stringify(body.loggedIn);

    if (Object.keys(organization.smtp).length === 0) {
      throw new ConflictException(
        'If you are in Custom SMTP Option, then one Custom SMTP email is Mandatory !',
      );
    }
    const adminMails = await getAdminEmailsBasedOnOrganizationId(organization?.id);
    function sendMail() {
      return new Promise(async (resolve, reject) => {
        let mailSmtp = { ...smtp[`${body.user}`] };
        let filePath = 'removed-smtp';
        let html = {};
        let data = { email: deletedSmtp.auth.user, service: deletedSmtp.service };
        let templatePath = `src/emails/templates/${filePath}.ejs`;
        let templateStr = ejs.fileLoader(templatePath);
        let template = ejs.compile(templateStr.toString());
        html = filePath == '' ? data : template(data);
        if (adminMails.length) {
          for (let i = 0; i < adminMails.length; i++) {
            let viderMailOptions = {
              from: {
                name: 'Vider',
                address: process.env.FROM_EMAIL,
              },
              to: adminMails[i].email,
              subject: 'Organization SMTP Removed',
              html: html,
            };

            const customtransporter = nodemailer.createTransport(defaultsmtp);
            customtransporter.sendMail(viderMailOptions, function (error: any, info: any) {
              if (error) {
                console.log(error);
                reject(error);
                return error;
              } else {
                console.log(info.response);
                resolve(info.response);
                return error;
              }
            });
          }
        }
        let newNotification = new Notification();
        newNotification.title = 'Organization SMTP Removed';
        newNotification.body = `Organization SMTP has been Removed with the Email ID : ${deletedSmtp.auth.user ? deletedSmtp.auth.user : data?.email
          }.`;
        newNotification.user = user;
        newNotification.status = 'unread';
        await newNotification.save();
      });
    }
    await sendMail();

    await organization.save();

    return organization;
  }

  async updateSmtpPreferences(userId: number, body: any) {
    let defaultsmtp: any = {
      host: 'email-smtp.ap-south-1.amazonaws.com',
      port: 587,
      auth: {
        user: 'AKIA5GHOVJDTRJ3PAQ6E',
        pass: 'BFt/gc++ytmTt24jK/317ARm7RQPk9eS12ThV1hZ5Jgc',
      },
    };

    let user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
    let organization = await Organization.findOne({ where: { id: user.organization.id } });
    if (organization.config) {
      organization.config = JSON.stringify({
        demo: organization.config['demo'],
        userslimit: organization.config['userslimit'],
        subscriptionmode: organization.config['subscriptionmode'],
        expirydate: organization.config['expirydate'],
      });
    }
    organization.loggedIn = JSON.stringify(body.loggedIn);

    if (organization?.broadcastSmtp?.[0] !== body?.broadcastSmtp?.[0]) {
      const broadcastActivity = await BroadcastActivity.createQueryBuilder('broadcastActivity')
        .leftJoinAndSelect('broadcastActivity.organization', 'organization')
        .where('organization.id = :organizationId', { organizationId: user.organization.id })
        .andWhere('broadcastActivity.Status = :status', { status: Status.Ready })
        .getMany();

      if (broadcastActivity.length) {
        throw new ConflictException(
          "Broadcast have Ready Status, Can't Change Now Bradcast SMTP !",
        );
      }
    }
    organization.broadcastSmtp = body.broadcastSmtp;
    organization.clientSmtp = body.clientSmtp;
    organization.iproSmtp = body.iproSmtp;
    organization.billingSmtp = body.billingSmtp;
    organization.othersSmtp = body.othersSmtp;

    if (!organization.othersSmtp) {
      throw new ConflictException(
        'If you are in Custom SMTP Option, All Other Notifications email is Mandatory !',
      );
    }

    const adminMails = await getAdminEmailsBasedOnOrganizationId(organization?.id);

    function sendMail() {
      return new Promise(async (resolve, reject) => {
        let filePath = 'smtp-preferences';
        let html = {};
        let data = { email: body.user };
        let templatePath = `src/emails/templates/${filePath}.ejs`;
        let templateStr = ejs.fileLoader(templatePath);
        let template = ejs.compile(templateStr.toString());
        html = filePath == '' ? data : template(data);

        if (adminMails.length) {
          for (let i = 0; i < adminMails.length; i++) {
            let viderMailOptions = {
              from: {
                name: 'Vider',
                address: process.env.FROM_EMAIL,
              },
              to: adminMails[i].email,
              subject: 'Organization SMTP Preferences Updated',
              html: html,
            };

            const customtransporter = nodemailer.createTransport(defaultsmtp);

            customtransporter.sendMail(viderMailOptions, function (error: any, info: any) {
              if (error) {
                console.log(error);
                reject(error);
                return error;
              } else {
                console.log(info.response);
                resolve(info.response);
                return error;
              }
            });
          }
        }
        let newNotification = new Notification();
        newNotification.title = 'Organization SMTP Preferences Updated';
        newNotification.body = `Organization SMTP Preferences has been updated. Your new SMTP server configuration Activated.`;
        newNotification.user = user;
        newNotification.status = 'unread';
        await newNotification.save();
      });
    }
    await sendMail();

    await organization.save();

    return organization;
  }

  async getudinUsers(userId: number) {
    let user = await User.findOne({
      where: { id: userId },
      relations: ['organization', 'organization.udinUsers'],
    });
    return user.organization.udinUsers;
  }

  async updateStorageStatus(userId: number, body: any) {
    const user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
    await Organization.update(user.organization.id, { hasStorage: true });
    return true;
  }
}
