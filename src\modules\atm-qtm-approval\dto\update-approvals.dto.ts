import {
  <PERSON>E<PERSON>,
  IsNotEmpty,
  IsNumberString,
  Is<PERSON>ptional,
  ValidateIf,
} from 'class-validator';
import { ApprovalType } from './find-approvals.dto';

export class UpdateTaskApprovalsDto {
  @IsNotEmpty()
  @IsNumberString()
  taskId: number;

  @ValidateIf((o) => o.type === ApprovalType.IPRO)
  @IsNumberString()
  @IsNotEmpty()
  iproId: number;

  // @IsNotEmpty()
  @IsOptional()
  approvalHierarchyId: number;

  @IsOptional()
  removeApproval: boolean;
}

export class UpdateIproApprovalsDto {
  @IsNotEmpty()
  iproId: string;

  // @IsNotEmpty()
  @IsOptional()
  approvalHierarchyId: number;
}

export class UpdateEstimateApprovals {
  @IsNotEmpty()
  estimateId: number;

  // @IsNotEmpty()
  @IsOptional()
  approvalHierarchyId: number;
}

export class UpdateInvoiceApprovals {
  @IsNotEmpty()
  invoiceId: number;

  // @IsNotEmpty()
  @IsOptional()
  approvalHierarchyId: number;
}
