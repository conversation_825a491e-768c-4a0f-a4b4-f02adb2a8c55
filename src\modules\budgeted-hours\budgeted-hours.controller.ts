import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  ParseIntPipe,
  Post,
  Put,
  Query,
  Req,
  Request,
  UseGuards,
} from '@nestjs/common';
import { JwtAuthGuard } from 'src/modules/users/jwt/jwt-auth.guard';
import { BudgetedHoursService } from './budgeted-hours.service';

@Controller('budgeted-hours')
export class BudgetedHoursController {
  constructor(private readonly BudgetedHoursService: BudgetedHoursService) { }

  @Get()
  getTaskBudgetedHours(@Query('taskId', ParseIntPipe) taskId: number) {
    return this.BudgetedHoursService.find(taskId);
  }

  @UseGuards(JwtAuthGuard)
  @Put()
  updateBudgetedHours(
    @Body() body: any,
    @Request() req: any,
  ) {
    const { userId } = req?.user;
    return this.BudgetedHoursService.update(body, userId);
  }

  @UseGuards(JwtAuthGuard)
  @Get('/efficiency')
  timesheetReport(@Request() req: any, @Query() query) {
    const { userId } = req.user;
    return this.BudgetedHoursService.timesheetReport(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Post('/efficiency-export')
  async exportLogHoursTimesheetReport(@Req() req: any, @Body() body: any) {
    const { userId } = req.user;
    return this.BudgetedHoursService.exportLogHoursTimesheetReport(userId, body);
  }
}
