import { Optional } from '@nestjs/common';
import Client from 'src/modules/clients/entity/client.entity';
import {
  BaseEntity,
  Column,
  CreateDateColumn,
  Entity,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import AutEProceedingFya from './aut_income_tax_eproceedings_fya.entity';
import AutProceedingResponseFya from './aut_income_tax_eproceedings_fyi_notice_response_fya.entity';
import Storage from 'src/modules/storage/storage.entity';

export enum StorageSystem {
  AMAZON = 'AMAZON',
  MICROSOFT = 'MICROSOFT',
  GOOGLE = 'GOOGLE',
  BHARATHCLOUD = 'BHARATHCLOUD',
}

@Entity()
class AutFyaNotice extends BaseEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  pan: string;

  @Column()
  proceedingReqId: string;

  @Column()
  nameOfAssesse: string;

  @Column()
  eProceedingId: number;

  @Column()
  documentIdentificationNumber: string;

  @Column()
  proceedingName: string;

  @Column()
  itrType: string;

  @Column()
  assesmentYear: string;

  @Column()
  financialYear: string;

  @Column()
  noticeSection: string;

  @Column()
  description: string;

  @Column()
  issuedOn: string;

  @Column()
  responseDueDate: string;

  @Column()
  manualDueDate: string;

  @Column()
  lastResponseSubmittedOn: string;

  @Column()
  documentReferenceId: string;

  @Column('json')
  noticeAttatchments: object;

  @Column('json')
  noticeLetters: object;

  @Column()
  remark: string;

  @Column()
  remarkSubmittedOn: string;

  @Column()
  respType: String;

  @Column()
  organizationId: number;

  @Column()
  createdType: string;

  @Column({ type: 'enum', enum: StorageSystem, default: null })
  storageSystem: StorageSystem;

  @ManyToOne(() => Client, (client) => client.autfyanotice, { onDelete: 'SET NULL' })
  client: Client;

  @ManyToOne(() => AutEProceedingFya, (autEProceedingFya) => autEProceedingFya.notices)
  eProceeding: AutEProceedingFya;

  @OneToMany(
    () => AutProceedingResponseFya,
    (autProceedingResponse) => autProceedingResponse.fyaNotice,
  )
  responses: AutProceedingResponseFya[];

  @OneToMany(() => Storage, (storage) => storage.autFyaNotice, {
    cascade: true,
  })
  storage: Storage[];

  @CreateDateColumn()
  createdAt: string;

  @UpdateDateColumn()
  updatedAt: string;
}

export default AutFyaNotice;
