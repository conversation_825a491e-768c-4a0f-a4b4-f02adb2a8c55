import { BadRequestException, Injectable, InternalServerErrorException, UnprocessableEntityException } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import Client from 'src/modules/clients/entity/client.entity';
import Password from 'src/modules/clients/entity/password.entity';
import { FormType } from 'src/modules/forms/dto/types';
import { Form, FormDocument } from 'src/modules/forms/schemas/form.schema';
import Task from 'src/modules/tasks/entity/task.entity';
import { User, UserType } from 'src/modules/users/entities/user.entity';
import { Brackets, createQueryBuilder, In } from 'typeorm';
import { ClientLoginDto } from './dto/client-loging.dto';
import { ClientVerifyEmailDto } from './dto/client-verifymail.dto';
import * as bcrypt from 'bcrypt';
import Kyb from '../kyb/kyb.entity';
import { TaskRecurringStatus, TaskStatusEnum } from '../tasks/dto/types';
import { ChangePasswordDto, ClientForgotPasswordDto, ForgotPasswordDto, ResetPasswordDto } from '../users/dto/forgot-password.dto';
import email from 'src/emails/email';
import { BlackList } from '../users/entities/black-list.entity';
import * as moment from 'moment';
import UdinTask from '../udin-task/udin-task.entity';

@Injectable()
export class ClientPanelService {
  constructor(
    private jwtService: JwtService,
    @InjectModel(Form.name) private formModel: Model<FormDocument>,
  ) { }

  async clientLogin(data: ClientLoginDto) {
    const user = await User.findOne({
      where: {
        email: data.username,
        type: In([UserType.CLIENT]),
      },
      relations: ['client'],
    });

    if (!user) {
      throw new UnprocessableEntityException('Invalid credentials');
    }

    if (!user?.client.clientPortalAccess) {
      throw new UnprocessableEntityException('Client portal access is disabled');
    }

    let passwordVerified = await user.verifyPassword(data.password);
    if (!passwordVerified) {
      throw new UnprocessableEntityException('Invalid Password');
    }

    const payload = { email: user.email, userId: user.id };
    let token = this.jwtService.sign(payload);

    return {
      access_token: token,
      clientId: user.client.id,
    };
  }

  async clientVerifyEmail(data: ClientVerifyEmailDto) {
    const user = await User.findOne({
      where: {
        email: data.username,
        type: In([UserType.CLIENT]),
      },
      relations: ['client'],
    });

    if (!user) {
      throw new UnprocessableEntityException('Invalid email');
    }

    if (!user?.client.clientPortalAccess) {
      throw new UnprocessableEntityException('Client portal access is disabled');
    }

    if (!user?.password) {
      return { password: null };
    }

    return true;
  }

  async clientSetPassword(data: ClientLoginDto) {
    const user = await User.findOne({
      where: {
        email: data.username,
        type: In([UserType.CLIENT]),
      },
      relations: ['client'],
    });

    user.password = await bcrypt.hash(data.password, 10);
    await user.save();

    const payload = { email: data.username, userId: user.id };
    return { access_token: this.jwtService.sign(payload) };
  }

  async forgotPassword(data: ClientForgotPasswordDto) {
    let user: any = await User.findOne({ where: { email: data.email, type: UserType.CLIENT } });

    if (!user) {
      throw new BadRequestException("user with the given email doesn't exist");
    }
    let name = '';
    if (user) {
      name = user.fullName;
    }

    let token = this.jwtService.sign({ email: data.email });
    let url = `https://client.vider.in/reset-password?token=${token}`;

    await email.resetPassword({ email: data.email, name: name, link: url });

    return { success: true };
  }

  async resetPassword(data: ResetPasswordDto) {
    try {
      const decodedPassword = bcrypt.hashSync(data.password, 10)
      let blackToken = await BlackList.findOne({ where: { token: data.token } });

      if (blackToken) {
        throw new BadRequestException('Token expired');
      }

      let decoded = this.jwtService.verify(data.token);
      let user = await User.findOne({
        where: { email: decoded.email, type: UserType.CLIENT },
      });
      user.password = decodedPassword;

      await user.save();
      let blacklist = new BlackList();
      blacklist.token = data.token;
      await blacklist.save();

      return user;
    } catch (err) {
      console.log(err);
      throw new InternalServerErrorException(err);
    }
  }

  async changePassword(id: number, data: ChangePasswordDto) {
    let user = await User.findOneOrFail(id);
    let isValidPassword = await user.verifyPassword(data.oldPassword);

    if (!isValidPassword) {
      throw new UnprocessableEntityException('The old password is wrong.');
    }

    let hashPassword = await bcrypt.hash(data.newPassword, 10);
    user.password = hashPassword;
    await user.save();
    return { success: true };
  }

  async getClientIproForms(userId: number) {
    let user = await createQueryBuilder(User, 'user')
      .leftJoinAndSelect('user.client', 'client')
      .leftJoinAndSelect('user.clientUser', 'clientUser')
      .leftJoinAndSelect('clientUser.client', 'clientUserClient')
      .where('user.id = :id', { id: userId })
      .andWhere('user.type = :type', { type: UserType.CLIENT })
      .getOne();

    let clientId = user.type === UserType.CLIENT ? user?.client?.id : user?.clientUser?.client?.id;

    let forms = await this.formModel.find({ clientId, type: FormType.TASK });

    let result = [];

    for (let form of forms) {
      let task = await Task.findOne({
        where: { id: form.taskId },
        relations: ['category'],
      });

    }

    return result;
  }

  async getClientKybForms(userId: number, query: any) {
    const kybs = await Kyb.find({ where: { client: query.clientId }, relations: ['storage'] });
    return kybs;
  }

  async getClientTasks(userId: number, query: any) {
    let tasks = createQueryBuilder(Task, 'task')
      .select([
        'task.id',
        'task.taskNumber',
        'task.name',
        'task.dueDate',
        'task.priority',
        'task.status',
        'task.recurringStatus',
        'task.createdAt',
        'task.approvalStatus',
        'task.recurring',
        'task.billable',
        'task.createdDate',
        'task.paymentStatus',
        'task.feeAmount',
      ])
      .leftJoinAndSelect('task.members', 'members')
      .leftJoinAndSelect('task.taskLeader', 'taskLeader')
      .leftJoinAndSelect('members.imageStorage', 'imageStorage')
      .leftJoinAndSelect('taskLeader.imageStorage', 'leaderImageStorage')
      .leftJoinAndSelect('task.client', 'client')
      .leftJoin('task.user', 'user')
      .leftJoinAndSelect('task.approvalProcedures', 'approvalProcedures')
      .leftJoinAndSelect('approvalProcedures.approval', 'approval')
      .leftJoinAndSelect('approval.approvalLevels', 'approvalLevels')
      .leftJoinAndSelect('approvalLevels.user', 'approvalLevelsUsers')
      .where('client.id = :clientId', { clientId: Number(query.client) })
      .andWhere('task.parentTask IS NULL')
      .andWhere('(task.recurringStatus is null or task.recurringStatus = :recurringStatus)', {
        recurringStatus: TaskRecurringStatus.CREATED,
      })
      .orderBy('task.createdDate', 'DESC')
      .addOrderBy('task.id', 'DESC');

    if (query.search) {
      tasks.andWhere(
        `(
          task.name like :search or
          task.taskNumber like :search or
          client.displayName like :search
        )`,
        { search: '%' + query.search + '%' },
      );
    }

    if (query?.removeCompleted === "true") {
      tasks.andWhere('task.status IN (:...statuses)', {
        statuses: [
          TaskStatusEnum.TODO,
          TaskStatusEnum.IN_PROGRESS,
          TaskStatusEnum.ON_HOLD,
          TaskStatusEnum.UNDER_REVIEW,
        ],
      })
    } else {
      tasks.andWhere(
        new Brackets((qb) =>
          qb
            .where('task.status IN (:...statuses)', {
              statuses: [
                TaskStatusEnum.TODO,
                TaskStatusEnum.IN_PROGRESS,
                TaskStatusEnum.ON_HOLD,
                TaskStatusEnum.UNDER_REVIEW,
              ],
            })
            .orWhere('task.status = :completedStatus', {
              completedStatus: TaskStatusEnum.COMPLETED,
            })
            .andWhere('task.statusUpdatedAt >= :todayDate', {
              todayDate: moment().subtract(15, 'days').toDate(),
            }),
        ),
      )
    }

    if (query.offset) {
      tasks.skip(query.offset);
    }

    if (query.limit) {
      tasks.take(query.limit);
    }

    let result = await tasks.getManyAndCount();

    return result;
  }

  async getClientProfile(userId: number) {
    let user = await createQueryBuilder(User, 'user')
      .leftJoinAndSelect('user.client', 'client')
      .leftJoinAndSelect('user.clientUser', 'clientUser')
      .leftJoinAndSelect('clientUser.client', 'clientUserClient')
      .where('user.id = :id', { id: userId })
      .andWhere('user.type = :type', { type: UserType.CLIENT })
      .getOne();

    let clientId = user?.type === UserType.CLIENT ? user?.client?.id : user?.clientUser?.client.id;

    let client = await createQueryBuilder(Client, 'client')
      .leftJoinAndSelect('client.organization', 'organization')
      .leftJoinAndSelect('client.clientManager', 'clientManager')
      .leftJoinAndSelect('client.contactPersons', 'clientUsers')
      .leftJoinAndSelect('client.clientImage', 'clientImage')
      .leftJoinAndSelect('client.labels', 'labels')
      .where('client.id = :id', { id: clientId })
      .getOne();

    return client;
  }

  async getSingleClientProfile(userId: number, query: any) {
    let client = await createQueryBuilder(Client, 'client')
      .leftJoinAndSelect('client.organization', 'organization')
      .leftJoinAndSelect('client.clientManager', 'clientManager')
      .leftJoinAndSelect('client.contactPersons', 'clientUsers')
      .leftJoinAndSelect('client.clientImage', 'clientImage')
      .leftJoinAndSelect('client.labels', 'labels')
      .where('client.id = :id', { id: query.clientId })
      .getOne();

    return client;
  }

  async getClientCredentials(userId: number, query: any) {
    let credentials = await createQueryBuilder(Password, 'password')
      .leftJoinAndSelect('password.client', 'client')
      .where('client.id = :id', { id: query.clientId })
      .getMany();

    return credentials;
  }

  async getMyProfile(userId: number) {
    let user = await createQueryBuilder(User, 'user')
      .where('user.id = :id', { id: userId })
      .andWhere('user.type = :type', { type: UserType.CLIENT })
      .getOne();
    return user;
  }

  async getClientPortalClients(userId: number, query: any) {
    let client = await createQueryBuilder(Client, 'client')
      .leftJoinAndSelect('client.organization', 'organization')
      .where('client.email = :email', { email: query.email })
      .andWhere('client.clientPortalAccess IS TRUE')
      .andWhere('organization.id = :orgId', { orgId: Number(query.organizationId) })
      .getMany();

    return client;
  }

  async getudinUsers(userId: number, query: any) {
    let user = await Client.findOne({
      where: { id: query.clientId },
      relations: ['organization', 'organization.udinUsers'],
    });
    return user.organization.udinUsers;
  }

  async getUdinTask(query, userId) {
    const user = await Client.findOne({ where: { id: query.clientId }, relations: ['organization'] });
    const udinTask = await UdinTask.findOne({
      where: { task: { id: query.taskId }, organization: { id: user.organization.id } },
      relations: ['user']
    });
    return udinTask;
  }
}
