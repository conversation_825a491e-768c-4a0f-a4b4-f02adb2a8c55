import {
  <PERSON><PERSON>num, <PERSON>NotEmpty, IsOptional, IsString, IsNumberString,
  ValidateIf
} from 'class-validator';

export enum FindUserTasksQueryType {
  USER = 'USER',
  SELF = 'SELF',
}

class FindUserTasksDto {
  @IsEnum(FindUserTasksQueryType)
  @IsNotEmpty()
  type: FindUserTasksQueryType;
  @IsOptional()
  pageSize: any;

  @IsOptional()
  sort: any;

  @IsOptional()
  @IsString()
  page: number;

  @IsOptional()
  @IsString()
  offset: number;

  @IsOptional()
  @IsNumberString()
  limit: number;

  @ValidateIf((o) => o.queryType === FindUserTasksQueryType.USER)
  @IsNotEmpty()
  userId: number;

  @IsOptional()
  search: string;

  @IsOptional()
  client: string;

  @IsOptional()
  clientGroup: string;

  @IsOptional()
  billable: string;

  @IsOptional()
  tab: string;

  @IsOptional()
  status: string;

  @IsOptional()
  priority: string;

  @IsOptional()
  fromStartDate: string;

  @IsOptional()
  toStartDate: string;

  @IsOptional()
  fromDueDate: string;

  @IsOptional()
  toDueDate: string;

  @IsOptional()
  fromExpectedDate: string;

  @IsOptional()
  toExpectedDate: string;
  @IsOptional()
  dueDate: string;


  @IsOptional()
  taskValue: string;

  @IsOptional()
  subtask: string;

  @IsOptional()
  clientId: number;

  @IsOptional()
  clientType: string;
}

export default FindUserTasksDto;
