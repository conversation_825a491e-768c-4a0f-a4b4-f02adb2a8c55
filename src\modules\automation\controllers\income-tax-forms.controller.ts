import {
  BadRequestException,
  Body,
  Controller,
  Delete,
  Get,
  Param,
  ParseIntPipe,
  Post,
  Put,
  Query,
  Req,
  UseGuards,
} from '@nestjs/common';
import { JwtAuthGuard } from 'src/modules/users/jwt/jwt-auth.guard';
import { AutIncomeTaxFormsService } from '../services/income-tax-forms.services';
import { ImportPasswordsDto } from '../dto/import-passwords.dto';
import { AttachmentFyaService } from '../services/attachments-fya.service';
@Controller('incometax')
export class AutIncomeTaxFormsController {
  constructor(
    private service: AutIncomeTaxFormsService,
    protected attachmentFyaService: AttachmentFyaService,
  ) { }

  @UseGuards(JwtAuthGuard)
  @Get('forms')
  findAll(@Req() req: any, @Query() query: any) {
    const { userId } = req.user;
    return this.service.findAll(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Post('/export-forms')
  async exportIncometaxForms(@Req() req: any, @Body() body: any) {
    const { userId } = req?.user;
    const query = body;
    return this.service.exportIncometaxForms(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Get('form/:id')
  findForm(@Req() req: any, @Param('id', ParseIntPipe) id: number) {
    const { userId } = req.user;
    return this.service.findForm(userId, id);
  }

  @UseGuards(JwtAuthGuard)
  @Post('credentials')
  addClientAutCredentials(@Body() body: any, @Req() req: any) {
    const { userId } = req.user;
    return this.service.addClientAutCredentials(userId, body);
  }

  @UseGuards(JwtAuthGuard)
  @Get('credentials')
  getClientAutCredentials(@Req() req: any, @Query() query: any) {
    const { userId } = req.user;
    return this.service.getClientAutCredentials(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Post('/export-credentials')
  async exportClientAutCredentials(@Req() req: any, @Body() body: any) {
    const { userId } = req?.user;
    const query = body;
    return this.service.exportClientAutCredentials(userId, query);
  }

  @Get('credential/:id')
  getClientCredential(@Param('id', ParseIntPipe) id: number) {
    return this.service.getClientCredential(id);
  }

  @UseGuards(JwtAuthGuard)
  @Get('clients')
  getAllClients(@Req() req: any, @Query() query: any) {
    const { userId } = req.user;
    return this.service.getAllClients(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Get('profile/:id')
  getIncomeTaxProfile(@Param('id', ParseIntPipe) id: number, @Req() req: any) {
    const { userId } = req.user;
    return this.service.getIncomeTaxProfile(userId, id);
  }

  @Get('jurisdiction/:id')
  getIncomeTaxJurisdiction(@Param('id', ParseIntPipe) id: number) {
    return this.service.getIncomeTaxJurisdiction(id);
  }

  @UseGuards(JwtAuthGuard)
  @Get('clientform/:id')
  getClientform(@Param('id', ParseIntPipe) id: number, @Query() query: any, @Req() req: any) {
    const { userId } = req.user;
    return this.service.getClientform(id, query, userId);
  }

  @UseGuards(JwtAuthGuard)
  @Post('/clientformexport')
  async exportClientForm(@Req() req: any, @Body() body: any) {
    const { userId } = req?.user;
    const query = body;
    return this.service.exportClientForm(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Get('returns')
  findReturns(@Req() req: any, @Query() query: any) {
    const { userId } = req.user;
    return this.service.getIncomeTaxReturns(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Post('/incometax-returns')
  async exportIncomeTaxReturns(@Req() req: any, @Body() body: any) {
    const { userId } = req?.user;
    const query = body;
    return this.service.exportIncomeTaxReturns(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Get('return/:id')
  findReturn(@Req() req: any, @Param('id', ParseIntPipe) id: number) {
    const { userId } = req.user;
    return this.service.findReturn(userId, id);
  }

  @UseGuards(JwtAuthGuard)
  @Get('clientreturn/:id')
  getClientReturn(@Param('id', ParseIntPipe) id: number, @Query() query: any, @Req() req: any) {
    const { userId } = req.user;
    return this.service.getClientReturn(id, query, userId);
  }

  @UseGuards(JwtAuthGuard)
  @Post('/clientreturn/clientreturnexport')
  async exportIncomeTaxClientReturnsexport(@Req() req: any, @Body() body: any) {
    const { userId } = req?.user;
    const query = body;
    return this.service.exportIncomeTaxClientReturnsexport(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Get('demands')
  findDemands(@Req() req: any, @Query() query: any) {
    const { userId } = req.user;
    return this.service.getIncomeTaxDemands(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Post('/export-demands')
  async exportIncomeTaxDemands(@Req() req: any, @Body() body: any) {
    const { userId } = req?.user;
    const query = body;
    return this.service.exportIncomeTaxDemands(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Get('demand/:id')
  findDemand(@Req() req: any, @Param('id', ParseIntPipe) id: number) {
    const { userId } = req.user;
    return this.service.findDemand(userId, id);
  }

  @UseGuards(JwtAuthGuard)
  @Get('clientdemand/:id')
  getClientDemand(@Param('id', ParseIntPipe) id: number, @Query() query: any, @Req() req: any) {
    const { userId } = req.user;
    return this.service.getClientDemand(id, query, userId);
  }

  @UseGuards(JwtAuthGuard)
  @Post('/export-clientdemand')
  async exportclientDemand(@Req() req: any, @Body() body: any) {
    const { userId } = req?.user;
    const query = body;
    return this.service.exportclientDemand(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Get('action')
  findEproceedings(@Req() req: any, @Query() query: any) {
    const { userId } = req.user;
    return this.service.getIncomeTaxEproceedings(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Post('/export-action')
  async exportIncomeTaxEproceedings(@Req() req: any, @Body() body: any) {
    const { userId } = req?.user;
    const query = body;
    return this.service.exportIncomeTaxEproceedings(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Get('information')
  findFYIEproceedings(@Req() req: any, @Query() query: any) {
    const { userId } = req.user;
    return this.service.getIncomeTaxFyiEproceedings(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Post('/export-information')
  async exportIncomeTaxFyiEproceedings(@Req() req: any, @Body() body: any) {
    const { userId } = req?.user;
    const query = body;
    return this.service.exportIncomeTaxFyiEproceedings(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Get('proceeding/:id')
  findEproceeding(@Req() req: any, @Param('id', ParseIntPipe) id: number) {
    const { userId } = req.user;
    return this.service.findEproceeding(userId, id);
  }

  @UseGuards(JwtAuthGuard)
  @Get('information-pro/:id')
  findEproceedingFyi(@Req() req: any, @Param('id', ParseIntPipe) id: number) {
    const { userId } = req.user;
    return this.service.findEproceedingFyi(userId, id);
  }

  @Get('clientproceeding/:id')
  getClientEproceeding(@Param('id', ParseIntPipe) id: number) {
    return this.service.getClientEproceeding(id);
  }

  //VIEW NOTICE
  @UseGuards(JwtAuthGuard)
  @Get('proceeding/notice-action/:id')
  getFyaNotice(@Req() req: any, @Param('id', ParseIntPipe) id: number) {
    const { userId } = req.user;
    return this.service.getFyaNotice(userId, id);
  }

  @UseGuards(JwtAuthGuard)
  @Get('proceeding/notice-information/:id')
  getFyiNotice(@Req() req: any, @Param('id', ParseIntPipe) id: number) {
    const { userId } = req.user;
    return this.service.getFyiNotice(userId, id);
  }

  //Client Proceeding For Your Action
  @UseGuards(JwtAuthGuard)
  @Get('clientproceedingfya/:id')
  getClientProceedingFya(
    @Param('id', ParseIntPipe) id: number,
    @Query() query: any,
    @Req() req: any,
  ) {
    const { userId } = req.user;
    return this.service.getClientProceedingFya(id, query, userId);
  }

  @UseGuards(JwtAuthGuard)
  @Post('/clientfyareport')
  async exportClientproceedingFya(@Req() req: any, @Body() body: any) {
    const { userId } = req?.user;
    const query = body;
    return this.service.exportClientproceedingFya(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Get('sections-fya')
  getFyaSections(@Req() req: any) {
    const { userId } = req.user;
    return this.service.getFyaSections(userId);
  }

  @UseGuards(JwtAuthGuard)
  @Get('sections-fyi')
  getFyiSections(@Req() req: any) {
    const { userId } = req.user;
    return this.service.getFyiSections(userId);
  }

  @UseGuards(JwtAuthGuard)
  @Get('sections-demands')
  getDemandsSections(@Req() req: any) {
    const { userId } = req.user;
    return this.service.getDemandsSections(userId);
  }

  @UseGuards(JwtAuthGuard)
  @Get('clientproceedingfyi/:id')
  getClientProceedingFyi(
    @Param('id', ParseIntPipe) id: number,
    @Query() query: any,
    @Req() req: any,
  ) {
    const { userId } = req.user;
    return this.service.getClientProceedingFyi(id, query, userId);
  }

  @UseGuards(JwtAuthGuard)
  @Post('/clientfyireport')
  async exportClientproceedingFyi(@Req() req: any, @Body() body: any) {
    const { userId } = req?.user;
    const query = body;
    return this.service.exportClientproceedingFyi(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Get('automationAuth')
  getAuthToken() {
    return this.service.getAuthToken();
  }

  @UseGuards(JwtAuthGuard)
  @Put('credentials/:id')
  update(@Param('id', ParseIntPipe) id: number, @Body() body, @Req() req: any) {
    const { userId } = req.user;
    return this.service.updateClientAutCredentials(id, body, userId);
  }

  @UseGuards(JwtAuthGuard)
  @Post('activity')
  sendActivityData(@Body() body: any, @Req() req: any) {
    const { userId } = req.user;
    return this.service.sendActivityData(userId, body);
  }

  @UseGuards(JwtAuthGuard)
  @Get('activity/:id')
  getActivityLogData(@Req() req: any, @Param('id', ParseIntPipe) id: number, @Query() query: any) {
    const { userId } = req.user;
    return this.service.getActivityLogData(id, query, userId);
  }

  @UseGuards(JwtAuthGuard)
  @Get('activityArchive/:id')
  getActivityArchiveData(
    @Req() req: any,
    @Param('id', ParseIntPipe) id: number,
    @Query() query: any,
  ) {
    const { userId } = req.user;
    return this.service.getActivityArchiveLogData(id, query, userId);
  }

  @UseGuards(JwtAuthGuard)
  @Get('checkNoOfSync')
  checkAutomationInOrganization(@Req() req: any) {
    const { userId } = req.user;
    return this.service.checkAutomationInOrganization(userId);
  }

  @UseGuards(JwtAuthGuard)
  @Post('import')
  async importCredentials(@Req() req: any, @Body() body: any) {
    const { userId } = req.user;
    return this.service.importCredentialsss(userId, body);
  }

  @UseGuards(JwtAuthGuard)
  @Get('scheduling')
  async organizationScheduling(@Req() req: any) {
    const { userId } = req.user;
    return this.service.organizationScheduling(userId);
  }

  @UseGuards(JwtAuthGuard)
  @Get('clientechallan/:id')
  clientEChallan(@Param('id', ParseIntPipe) id: number, @Query() query: any, @Req() req: any) {
    const { userId } = req.user;
    return this.service.clientEChallan(userId, query, id);
  }

  @UseGuards(JwtAuthGuard)
  @Post('/clientechallanexport')
  async exportClientEchallan(@Req() req: any, @Body() body: any) {
    const { userId } = req?.user;
    const query = body;
    return this.service.exportClientEchallan(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Get('e-challan/:id')
  findEchallan(@Req() req: any, @Param('id', ParseIntPipe) id: number) {
    const { userId } = req.user;
    return this.service.findEchallan(userId, id);
  }

  @UseGuards(JwtAuthGuard)
  @Get('e-challans')
  findEchallans(@Req() req: any, @Query() query: any) {
    const { userId } = req.user;
    return this.service.findEchallans(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Post('/incometaxechallanexport')
  async exportIncometaxEchallan(@Req() req: any, @Body() body: any) {
    const { userId } = req?.user;
    const query = body;
    return this.service.exportIncometaxEchallan(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Get('mycas/:id')
  clientMycas(@Param('id', ParseIntPipe) id: number, @Query() query: any, @Req() req: any) {
    const { userId } = req.user;
    return this.service.clientMycas(userId, query, id);
  }

  @UseGuards(JwtAuthGuard)
  @Post('/mycas/mycasexport')
  async exportClientMycas(@Req() req: any, @Body() body: any) {
    const { userId } = req?.user;
    const query = body;
    return this.service.exportClientMycas(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Get('my-cas')
  findMycas(@Req() req: any, @Query() query: any) {
    const { userId } = req.user;
    return this.service.findMycas(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Post('/mycas/incometaxmycaexport')
  async exportIncometaxMyCas(@Req() req: any, @Body() body: any) {
    const { userId } = req?.user;
    const query = body;
    return this.service.exportIncometaxMyCas(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Get('form-types')
  getMycaFormTypes(@Req() req: any) {
    const { userId } = req.user;
    return this.service.getMycaFormTypes(userId);
  }

  @UseGuards(JwtAuthGuard)
  @Get('temp-notice-fya')
  findFyaTempNotices(@Req() req: any, @Query() query: any) {
    const { userId } = req.user;
    return this.service.findFyaTempNotices(userId, query);
  }
  @UseGuards(JwtAuthGuard)
  @Post('/tempproceedingTempfyA-export')
  async exportIncomeTaxTempEproceedingsFyA(@Req() req: any, @Body() body: any) {
    const { userId } = req?.user;
    const query = body;
    return this.service.exportIncomeTaxTempEproceedingsFyA(userId, query);
  }
  @UseGuards(JwtAuthGuard)
  @Get('temp-notice-fyi')
  findFyiTempNotices(@Req() req: any, @Query() query: any) {
    const { userId } = req.user;
    return this.service.findFyiTempNotices(userId, query);
  }
  @UseGuards(JwtAuthGuard)
  @Post('/tempproceedingTempfyI-export')
  async exportIncomeTaxTempEproceedingsFyi(@Req() req: any, @Body() body: any) {
    const { userId } = req?.user;
    const query = body;
    return this.service.exportIncomeTaxTempEproceedingsFyi(userId, query);
  }
  @UseGuards(JwtAuthGuard)
  @Get('clientproceedingfyi-excel/:id')
  getClientExcelProceedingFyi(
    @Param('id', ParseIntPipe) id: number,
    @Query() query: any,
    @Req() req: any,
  ) {
    const { userId } = req.user;
    return this.service.getClientExcelProceedingFyi(id, query, userId);
  }
  @UseGuards(JwtAuthGuard)
  @Post('/clientproceedingfyI-export')
  async exportClientTempNoticeFYI(@Req() req: any, @Body() body: any) {
    const { userId } = req?.user;
    const query = body;
    return this.service.exportClientTempNoticeFYI(userId, query);
  }
  @UseGuards(JwtAuthGuard)
  @Get('clientproceedingfya-excel/:id')
  getClientExcelProceedingFya(
    @Param('id', ParseIntPipe) id: number,
    @Query() query: any,
    @Req() req: any,
  ) {
    const { userId } = req.user;
    return this.service.getClientExcelProceedingFya(id, query, userId);
  }

  @UseGuards(JwtAuthGuard)
  @Post('/clientproceedingfya-export')
  async exportClientTempNoticeFYA(@Req() req: any, @Body() body: any) {
    const { userId } = req?.user;
    const query = body;
    return this.service.exportClientTempNoticeFYA(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Get('sections-fya-excel')
  getExcelFyaSections(@Req() req: any) {
    const { userId } = req.user;
    return this.service.getExcelFyaSections(userId);
  }

  @UseGuards(JwtAuthGuard)
  @Get('sections-fyi-excel')
  getExcelFyiSections(@Req() req: any) {
    const { userId } = req.user;
    return this.service.getExcelFyiSections(userId);
  }
}
