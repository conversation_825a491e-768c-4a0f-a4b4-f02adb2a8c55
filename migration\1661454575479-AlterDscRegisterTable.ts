import { MigrationInterface, QueryRunner } from 'typeorm';

export class AlterDscRegisterTable1661454575479 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE dsc_register
       MODIFY COLUMN received_date datetime null,
       MODIFY COLUMN issued_date datetime null;
      `,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}
