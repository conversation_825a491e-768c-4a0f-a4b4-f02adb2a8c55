import { User } from 'src/modules/users/entities/user.entity';
import {
  CreateDateColumn,
  JoinC<PERSON>umn,
  OneToOne,
  UpdateDateColumn,
} from 'typeorm';
import { BaseEntity, Entity, PrimaryGeneratedColumn, Column } from 'typeorm';

@Entity()
export class Token extends BaseEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @OneToOne(() => User, { onDelete: 'CASCADE' })
  @JoinColumn()
  user: User;

  @Column()
  token: string;

  @CreateDateColumn()
  createdAt: string;

  @UpdateDateColumn()
  updatedAt: string;
}
