import { Injectable } from '@nestjs/common';
import { OnEvent } from '@nestjs/event-emitter';
import { Event_Actions } from './actions';
import { getManager } from 'typeorm';
import { getUserDetails, insertINTONotificationUpdate } from 'src/utils/re-use';
import { sendnewMail } from 'src/emails/newemails';
import { sendWhatsAppTextMessage } from 'src/modules/whatsapp/whatsapp.service';
import ViderWhatsappSessions from 'src/modules/whatsapp/entity/viderWhatsappSessions';
import { Organization } from 'src/modules/organization/entities/organization.entity';
const moment = require('moment');

@Injectable()
export default class ExportListeners {
  @OnEvent(Event_Actions.EXPORT_INVOICES)
  async handleExportInvoices(event: any) {
    try {
      const entityManager = getManager();
      const { user } = event;
      const { fullName, organization } = user;
      const { id } = organization;
      if (id) {
        const roleQuery = `SELECT id FROM role where organization_id = ${id} and name = "Admin";`;
        let getRole = await entityManager.query(roleQuery);
        const role_id = getRole[0]?.id;
        if (role_id === null || role_id === 'undefined' || role_id === undefined) {
          throw new Error('Role is Not Defined');
        }
        const getUserQuery = `select id from user where organization_id=${id} and role_id = ${role_id}`;
        let getUserId = await entityManager.query(getUserQuery);
        const userIDs = getUserId.map((row: any) => row.id);
        async function insertINTOnotifications() {
          const key = 'EXPORTED_INVOICES_PUSH';
          let title = 'Exported Invoices';
          let body = `Invoices list has been exported by <strong>${fullName}</strong>.`;
          //   insertINTOnotification(title, body, userIDs, id);
          insertINTONotificationUpdate(title, body, userIDs, id, key);
          const organization = await Organization.findOne({ id: id });

          const addressParts = [
            organization.buildingNo || '',
            organization.floorNumber || '',
            organization.buildingName || '',
            organization.street || '',
            organization.location || '',
            organization.city || '',
            organization.district || '',
            organization.state || '',
          ].filter((part) => part && part.trim() !== '');
          const pincode =
            organization.pincode && organization.pincode.trim() !== ''
              ? ` - ${organization.pincode}`
              : '';

          const address = addressParts.join(', ') + pincode;
          for (let a of userIDs) {
            let getEmailQuery = `SELECT id, email,full_name FROM user where id = ${a};`;
            let getEmail = await entityManager.query(getEmailQuery);
            let mail = getEmail[0]?.email;
            let id = getEmail[0]?.id;
            let fullname = getEmail[0]?.full_name;
            let data = {
              username: fullname,
              userName: fullName,
              invType: 'Invoices',
              userId: user.id,
              adress: address,
              phoneNumber: organization?.mobileNumber,
              mail: organization?.email,
              legalName: organization?.tradeName || organization?.legalName,
            };
            let IData = {
              id,
              key: 'EXPORT_INVOICES_MAIL',
              data: data,
              subject: `Invoices List exported`,
              email: mail,
              filePath: 'export-invoices',
            };
            await sendnewMail(IData);
            const title = 'Invoice Exported';
            try {
              if (userIDs !== undefined) {
                const sessionValidation = await ViderWhatsappSessions.findOne({
                  where: { userId: userIDs, status: 'ACTIVE' },
                });
                if (sessionValidation) {
                  const adminUserDetails = await getUserDetails(userIDs);

                  const {
                    full_name: userFullName,
                    mobile_number: userPhoneNumber,
                    id,
                    organization_id,
                  } = adminUserDetails;
                  const key = 'EXPORT_INVOICES_WHATSAPP';
                  const whatsappMessageBody = `
 Hi ${userFullName}

 ${fullName} have exported the list of invoices
 check whether he is permitted to do so!

 We hope this helps!`;
                  await sendWhatsAppTextMessage(
                    `91${userPhoneNumber}`,
                    whatsappMessageBody,
                    organization_id,
                    title,
                    id,
                    key,
                  );
                }
              }
            } catch (error) {
              console.error('Error sending User WhatsApp notification:', error);
            }
          }
        }
        insertINTOnotifications();
      }
    } catch (err) {
      console.log(err);
    }
  }
  @OnEvent(Event_Actions.EXPORT_PROFORMA_INVOICES)
  async handleExportProformaInvoices(event: any) {
    try {
      const entityManager = getManager();
      const { user } = event;
      const { fullName, organization } = user;
      const { id } = organization;
      if (id) {
        const roleQuery = `SELECT id FROM role where organization_id = ${id} and name = "Admin";`;
        let getRole = await entityManager.query(roleQuery);
        const role_id = getRole[0]?.id;
        if (role_id === null || role_id === 'undefined' || role_id === undefined) {
          throw new Error('Role is Not Defined');
        }
        const getUserQuery = `select id from user where organization_id=${id} and role_id = ${role_id}`;
        let getUserId = await entityManager.query(getUserQuery);
        const userIDs = getUserId.map((row: any) => row.id);
        async function insertINTOnotifications() {
          const key = 'EXPORTED_PROFORMA_INVOICES_PUSH';
          let title = 'Exported Proforma Invoices';
          let body = `Proforma Invoices list has been exported by <strong>${fullName}</strong>.`;
          //   insertINTOnotification(title, body, userIDs, id);
          insertINTONotificationUpdate(title, body, userIDs, id, key);
          const organization = await Organization.findOne({ id: id });

          const addressParts = [
            organization.buildingNo || '',
            organization.floorNumber || '',
            organization.buildingName || '',
            organization.street || '',
            organization.location || '',
            organization.city || '',
            organization.district || '',
            organization.state || '',
          ].filter((part) => part && part.trim() !== '');
          const pincode =
            organization.pincode && organization.pincode.trim() !== ''
              ? ` - ${organization.pincode}`
              : '';

          const address = addressParts.join(', ') + pincode;
          try {
            if (userIDs !== undefined) {
              const sessionValidation = await ViderWhatsappSessions.findOne({
                where: { userId: userIDs, status: 'ACTIVE' },
              });
              if (sessionValidation) {
                const adminUserDetails = await getUserDetails(userIDs);

                const {
                  full_name: userFullName,
                  mobile_number: userPhoneNumber,
                  id,
                  organization_id,
                } = adminUserDetails;
                const key = 'EXPORT_PROFORMA_INVOICES_WHATSAPP';
                const whatsappMessageBody = `
Hi ${userFullName}
   
Proforma Invoices list has been exported by ${fullName}.
We hope this helps!`;
                await sendWhatsAppTextMessage(
                  `91${userPhoneNumber}`,
                  whatsappMessageBody,
                  organization_id,
                  title,
                  id,
                  key,
                );
              }
            }
          } catch (error) {
            console.error('Error sending User WhatsApp notification:', error);
          }

          for (let a of userIDs) {
            let getEmailQuery = `SELECT id, email,full_name FROM user where id = ${a};`;
            let getEmail = await entityManager.query(getEmailQuery);
            let mail = getEmail[0]?.email;
            let id = getEmail[0]?.id;
            let fullname = getEmail[0]?.full_name;
            let data = {
              username: fullname,
              userName: fullName,
              invType: 'Proforma Invoices',
              userId: user.id,
              adress: address,
              phoneNumber: organization?.mobileNumber,
              mail: organization?.email,
              legalName: organization?.tradeName || organization?.legalName,
            };
            let IData = {
              id,
              key: 'EXPORT_PROFORMA_INVOICES_MAIL',
              data: data,
              subject: `Profora Invoices List exported`,
              email: mail,
              filePath: 'export-invoices',
            };
            await sendnewMail(IData);
          }
        }
        insertINTOnotifications();
      }
    } catch (err) {
      console.log(err);
    }
  }

  @OnEvent(Event_Actions.INVOICE_DOWNLOADED)
  async handleDownloadInvoices(event: any) {
    try {
      const entityManager = getManager();
      const { invoiceId, userId } = event;
      if (userId) {
        const getUserQuery = `select full_name,organization_id from user where id=${userId}`;
        let getUser = await entityManager.query(getUserQuery);
        const fullName = getUser[0].full_name;
        const organizationId = getUser[0].organization_id;
        const getRoleQuery = `SELECT id FROM role where organization_id = ${organizationId} and name = "Admin";`;
        let getRole = await entityManager.query(getRoleQuery);
        const role_id = getRole[0]?.id;
        if (role_id === null || role_id === 'undefined' || role_id === undefined) {
          throw new Error('Role is Not Defined');
        }
        const getUserQry = `select id from user where organization_id=${organizationId} and role_id = ${role_id}`;
        let getUserId = await entityManager.query(getUserQry);
        const userIDs = getUserId.map((row) => row.id);
        const getInvocieQuery = `SELECT * FROM invoice where id = ${invoiceId};`;
        let getInvocie = await entityManager.query(getInvocieQuery);
        const { invoice_number, invoice_due_date, invoice_date, grand_total, client_id } =
          getInvocie[0];
        const getClientQuery = `SELECT * FROM client where id = ${client_id};`;
        let getClient = await entityManager.query(getClientQuery);
        const { display_name } = getClient[0];
        async function insertINTOnotifications() {
          const key = 'INVOICE_DOWNLOADED_PUSH';
          let title = 'Invoice Downloaded';
          let body = `<strong>${fullName}</strong> have downloaded an invoice <strong>${invoice_number}</strong> of <strong>${display_name}</strong>.`;
          //   insertINTOnotification(title, body, userIDs, organizationId);
          insertINTONotificationUpdate(title, body, userIDs, organizationId, key);
          const organization = await Organization.findOne({ id: organizationId });

          const addressParts = [
            organization.buildingNo || '',
            organization.floorNumber || '',
            organization.buildingName || '',
            organization.street || '',
            organization.location || '',
            organization.city || '',
            organization.district || '',
            organization.state || '',
          ].filter((part) => part && part.trim() !== '');
          const pincode =
            organization.pincode && organization.pincode.trim() !== ''
              ? ` - ${organization.pincode}`
              : '';

          const address = addressParts.join(', ') + pincode;
          if (userIDs) {
            for (let user of userIDs) {
              const userDetails = await getUserDetails(user);
              await sendnewMail({
                id: userDetails?.id,
                key: 'INVOICES_DOWNLOADED_MAIL',
                email: userDetails?.email,
                data: {
                  userId: userId,
                  taskUserName: userDetails?.full_name,
                  clientName: display_name,
                  invoiceNumber: invoice_number,
                  invoiceDate: moment(invoice_date).format('DD-MM-YYYY'),
                  adress: address,
                  phoneNumber: organization?.mobileNumber,
                  mail: organization?.email,
                  legalName: organization?.tradeName || organization?.legalName,
                },
                filePath: 'invoices-download',
                subject: `Invoices Downloaded: ${invoice_number} of ${display_name}`,
              });
              const title = 'Invoice Cancelled';
              try {
                if (userIDs !== undefined) {
                  const sessionValidation = await ViderWhatsappSessions.findOne({
                    where: { userId: userIDs, status: 'ACTIVE' },
                  });
                  if (sessionValidation) {
                    const adminUserDetails = await getUserDetails(userIDs);

                    const {
                      full_name: userFullName,
                      mobile_number: userPhoneNumber,
                      id,
                      organization_id,
                    } = adminUserDetails;
                    const key = 'INVOICES_DOWNLOADED_WHATSAPP';
                    const whatsappMessageBody = `
 Hi ${userFullName}
An invoice ${invoice_number} of ${display_name} have been downloaded by ${fullName}

Invoice number: ${invoice_number}
Client name: ${display_name}
Invoice amount: ${grand_total}
Invoice Due date: ${invoice_due_date}

We hope this helps!`;
                    await sendWhatsAppTextMessage(
                      `91${userPhoneNumber}`,
                      whatsappMessageBody,
                      organization_id,
                      title,
                      id,
                      key,
                    );
                  }
                }
              } catch (error) {
                console.error('Error sending User WhatsApp notification:', error);
              }
            }
          }
        }
        insertINTOnotifications();
      }
    } catch (err) {
      console.log(err);
    }
  }
  @OnEvent(Event_Actions.PROFORMA_INVOICE_DOWNLOADED)
  async handleDownloadProformaInvoices(event: any) {
    try {
      const entityManager = getManager();
      const { invoiceId, userId } = event;
      if (userId) {
        const getUserQuery = `select full_name,organization_id from user where id=${userId}`;
        let getUser = await entityManager.query(getUserQuery);
        const fullName = getUser[0].full_name;
        const organizationId = getUser[0].organization_id;
        const getRoleQuery = `SELECT id FROM role where organization_id = ${organizationId} and name = "Admin";`;
        let getRole = await entityManager.query(getRoleQuery);
        const role_id = getRole[0]?.id;
        if (role_id === null || role_id === 'undefined' || role_id === undefined) {
          throw new Error('Role is Not Defined');
        }
        const getUserQry = `select id from user where organization_id=${organizationId} and role_id = ${role_id}`;
        let getUserId = await entityManager.query(getUserQry);
        const userIDs = getUserId.map((row) => row.id);
        const getInvocieQuery = `SELECT * FROM proforma_invoice where id = ${invoiceId};`;
        let getInvocie = await entityManager.query(getInvocieQuery);
        const {
          invoice_number,
          invoice_due_date,
          invoice_date,
          grand_total,
          client_id,
          client_group_id,
        } = getInvocie[0];
        const getClientQuery = `SELECT * FROM client where id = ${client_id};`;
        const getClientGroupQuery = `SELECT * FROM client where id = ${client_group_id};`;

        let getClient = await entityManager.query(getClientQuery);
        let getClientGroup = await entityManager.query(getClientGroupQuery);
        if (getClient[0]) {
          const { display_name } = getClient[0];
          const { display_name: clientGroupName } = getClientGroup[0];
          async function insertINTOnotifications() {
            const key = 'PROFORMA_INVOICE_DOWNLOADED_PUSH';
            let title = 'Proforma Invoice Downloaded';
            let body = `<strong>${fullName}</strong> have downloaded an proforma invoice <strong>${invoice_number}</strong> of <strong>${display_name}</strong>.`;
            //   insertINTOnotification(title, body, userIDs, organizationId);
            insertINTONotificationUpdate(title, body, userIDs, organizationId, key);
            const organization = await Organization.findOne({ id: organizationId });

            const addressParts = [
              organization.buildingNo || '',
              organization.floorNumber || '',
              organization.buildingName || '',
              organization.street || '',
              organization.location || '',
              organization.city || '',
              organization.district || '',
              organization.state || '',
            ].filter((part) => part && part.trim() !== '');
            const pincode =
              organization.pincode && organization.pincode.trim() !== ''
                ? ` - ${organization.pincode}`
                : '';

            const address = addressParts.join(', ') + pincode;
            try {
              if (userIDs !== undefined) {
                const sessionValidation = await ViderWhatsappSessions.findOne({
                  where: { userId: userIDs, status: 'ACTIVE' },
                });
                if (sessionValidation) {
                  const adminUserDetails = await getUserDetails(userIDs);

                  const {
                    full_name: userFullName,
                    mobile_number: userPhoneNumber,
                    id,
                    organization_id,
                  } = adminUserDetails;
                  const key = 'PROFORMA_INVOICES_CREATED_WHATSAPP';
                  const whatsappMessageBody = `
      Hi ${userFullName}
     
     ${fullName} has downloaded  proforma invoice ${invoice_number} of ${
                    display_name || clientGroupName
                  }.
      We hope this helps!`;
                  await sendWhatsAppTextMessage(
                    `91${userPhoneNumber}`,
                    whatsappMessageBody,
                    organization_id,
                    title,
                    id,
                    key,
                  );
                }
              }
            } catch (error) {
              console.error('Error sending User WhatsApp notification:', error);
            }

            if (userIDs) {
              for (let user of userIDs) {
                const userDetails = await getUserDetails(user);
                await sendnewMail({
                  id: userDetails?.id,
                  key: 'PROFORMA_INVOICES_DOWNLOADED_MAIL',
                  email: userDetails?.email,
                  data: {
                    userId: userId,
                    taskUserName: userDetails?.full_name,
                    clientName: display_name,
                    invoiceNumber: invoice_number,
                    invoiceDate: moment(invoice_date).format('DD-MM-YYYY'),
                    adress: address,
                    phoneNumber: organization?.mobileNumber,
                    mail: organization?.email,
                    legalName: organization?.tradeName || organization?.legalName,
                  },
                  filePath: 'invoices-download',
                  subject: `Proforma Invoices Downloaded: ${invoice_number} of ${display_name}`,
                });
              }
            }
          }
          insertINTOnotifications();
        }
      }
    } catch (err) {
      console.log(err);
    }
  }

  @OnEvent(Event_Actions.REPORT_GENERATED)
  async handleReportGenerated(event: any) {
    try {
      const entityManager = getManager();
      const { organizationid, query, user } = event;
      const orgId = Number(organizationid);
      let reportName = '';
      if (query === 'statuswisetasks') {
        reportName = 'Status Wise Tasks Report';
      } else if (query === 'userbasedmasterreport') {
        reportName = 'User Based Master Report';
      } else if (query === 'overduetasks') {
        reportName = 'Over Due Tasks Report';
      } else if (query === 'detailedoverduetasks') {
        reportName = 'Detail Overdue Tasks Report';
      } else if (query === 'highestnumberoftaskscompletion') {
        reportName = 'Highest Number of Tasks Completion Report';
      } else if (query === 'taskscompletedtobilledtasks') {
        reportName = 'Tasks Completed To Billed Tasks Report';
      } else if (query === 'taskscompletedtounbilledtasks') {
        reportName = 'Tasks Completed To UnBilled Tasks Report';
      } else if (query === 'ClientTasksCompletedToBilled') {
        reportName = 'Client Wise Tasks Completed To Billed Report';
      } else if (query === 'ClientTasksCompletedToUnBilled') {
        reportName = 'Client Wise Tasks Completed To UnBilled Report';
      } else if (query === 'invoiceoverduereports') {
        reportName = 'Invoice Overdue Reports';
      } else if (query === 'balancedueforinvoicesraisedreport') {
        reportName = 'Balanced Due for Invoices Raised Report';
      } else if (query === 'receiptmanagementreport') {
        reportName = 'Receipt Issued Report';
      } else if (query === 'getLogHours') {
        reportName = 'Log Hours Report';
      } else if (query === 'getEmployeeLogHours') {
        reportName = 'Employee Log Hours Report';
      } else if (query === 'getClientsReport') {
        reportName = 'Clients Report';
      } else if (query === 'getTasksReport') {
        reportName = 'Tasks Report';
      } else if (query === 'getLogHoursFee') {
        reportName = 'Time to Value Report';
      } else if (query === 'getOnHoldTasks') {
        reportName = 'On Hold Tasks';
      } else if (query === 'getEmployeeAttendance') {
        reportName = 'Employee Attendance Report';
      } else if (query === 'proformaInvoiceReport') {
        reportName = 'Proforma Invoice Report';
      }
      if (
        query === 'clientslistinvoice' ||
        query === 'clientdashboardamountreceived' ||
        query === 'clientdashboardinvoiceunbilled' ||
        query === 'clientdashboardinvoicebilled' ||
        query === 'clientdashboardamountdue' ||
        query === 'clientpureagentreceivedanddue' ||
        query === 'clientdashboardactivitylog' ||
        query === 'usertasksbyorgid' ||
        query === 'taskhrmsdetails' ||
        query === 'upcommingtasks' ||
        query === 'servicecategorystatusbytasks' ||
        query === 'allcategoryandsubcategory' ||
        query === 'allcategoryandsubcategory' ||
        query === 'allusersefficiency' ||
        query === 'clientinvoiceunbilled' ||
        query === 'clientinvoicebilled' ||
        query === 'clientinvoicebilling' ||
        query === 'clientinvoicereceipts' ||
        query === 'clientinvoicereceipts'
      ) {
        return;
      }
      const getUserQuery = `select full_name from user where id = ${user.id}`;
      let getUser = await entityManager.query(getUserQuery);
      const fullName = getUser[0]?.full_name;
      const getRoleQuery = `SELECT id FROM role where organization_id = ${orgId} and name = "Admin";`;
      let getRole = await entityManager.query(getRoleQuery);
      const role_id = getRole[0]?.id;
      const getUserqury = `select id from user where organization_id=${orgId} and role_id = ${role_id}`;
      let getUsers = await entityManager.query(getUserqury);
      const userIDs = getUsers.map((row) => row.id);
      async function insertINTOnotifications() {
        const key = 'GENERATED_PUSH';
        let title = `${reportName} Generated`;
        let body = `<strong>${reportName}</strong> generated by <strong>${fullName}</strong>.`;
        // insertINTOnotification(title, body, userIDs, orgId);
        insertINTONotificationUpdate(title, body, userIDs, orgId, key);
        //whatsapp
        const organization = await Organization.findOne({ id: orgId });

        const addressParts = [
          organization.buildingNo || '',
          organization.floorNumber || '',
          organization.buildingName || '',
          organization.street || '',
          organization.location || '',
          organization.city || '',
          organization.district || '',
          organization.state || '',
        ].filter((part) => part && part.trim() !== '');
        const pincode =
          organization.pincode && organization.pincode.trim() !== ''
            ? ` - ${organization.pincode}`
            : '';
        const address = addressParts.join(', ') + pincode;
        try {
          if (userIDs !== undefined) {
            const sessionValidation = await ViderWhatsappSessions.findOne({
              where: { userId: userIDs, status: 'ACTIVE' },
            });
            if (sessionValidation) {
              const adminUserDetails = await getUserDetails(userIDs);

              const {
                full_name: userFullName,
                mobile_number: userPhoneNumber,
                id,
                organization_id,
              } = adminUserDetails;
              const key = 'REPORT_GENERATED_WHATSAPP';
              const whatsappMessageBody = `
        Hi ${userFullName}
        report generated
        
        We hope this helps!`;
              await sendWhatsAppTextMessage(
                `91${userPhoneNumber}`,
                whatsappMessageBody,
                organization_id,
                title,
                id,
                key,
              );
            }
          }
        } catch (error) {
          console.error('Error sending User WhatsApp notification:', error);
        }

        for (let a of userIDs) {
          let getEmailQuery = `SELECT id, email,full_name FROM user where id = ${a};`;
          let getEmail = await entityManager.query(getEmailQuery);
          let mail = getEmail[0]?.email;
          let fullname = getEmail[0]?.full_name;
          let id = getEmail[0]?.id;
          let data = {
            username: fullname,
            userName: fullName,
            reportName: `${reportName}`,
            userId: user.id,
            adress: address,
            phoneNumber: organization?.mobileNumber,
            mail: organization?.email,
            legalName: organization?.tradeName || organization?.legalName,
          };
          let IData = {
            id,
            key: 'REPORT_GENERATED_MAIL',
            data: data,
            subject: `${reportName}`,
            email: mail,
            filePath: 'report-generated',
          };
          await sendnewMail(IData);
        }
      }
      insertINTOnotifications();
    } catch (err) {
      console.log(err);
    }
  }
}
