import {
  AfterLoad,
  BaseEntity,
  Column,
  CreateDateColumn,
  Entity,
  JoinColumn,
  ManyToOne,
  OneToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { Service } from './service.entity';
import Storage from 'src/modules/storage/storage.entity';

export enum StageOfWorkType {
  STAGE_OF_WORK = 'STAGE_OF_WORK',
  DELIVERABLES = 'DELIVERABLES',
}

export enum AttributeType {
  REFERENCE_NUMBER = 'REFERENCE_NUMBER',
  ATTACHMENT = 'ATTACHMENT',
}

export interface IExtraAttributes {
  type: AttributeType;
  title: string;
  value: any;
}

@Entity('service_stageofwork')
class ServiceStageOfWork extends BaseEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  name: string;

  @Column({ type: 'enum', enum: StageOfWorkType })
  type: StageOfWorkType;

  @Column({ nullable: true })
  description: string;

  @Column({ default: false })
  referenceNumber: boolean;

  @Column({ nullable: true })
  referenceNumberValue: string;

  @Column({ type: 'json' })
  extraAttributes: Array<IExtraAttributes>;

  @OneToOne(() => Storage, (storage) => storage.serviceStageOfWork, { cascade: true })

  storage: Storage;

  @Column()
  attachmentFile: string;

  attachmentFileUrl: string;

  @ManyToOne(() => Service, (service) => service.stageOfWorks)
  service: Service;

  @CreateDateColumn()
  createdAt: string;

  @UpdateDateColumn()
  updatedAt: string;

  @AfterLoad()
  renderUrl() {
    if (this.attachmentFile) {
      this.attachmentFileUrl = `${process.env.AWS_BASE_URL}/${this.attachmentFile}`;
    }
  }
}

export default ServiceStageOfWork;
