import { BaseEntity, Column, CreateDateColumn, Entity, PrimaryColumn, PrimaryGeneratedColumn, UpdateDateColumn } from "typeorm";

@Entity()
class WhatsappConversation extends BaseEntity {
    @PrimaryGeneratedColumn()
    id: number;

    @Column()
    userId: number;

    @Column()
    organizationId: number;

   @Column({ type: 'timestamp', nullable: true })
    createdAt: Date;

    @Column()
    message:string;

    @Column()
    direction:string;

    @Column()
    type:string;

    @Column()
    link:string;

    @Column()
    mobileNumber: string;

    @Column({ type: 'timestamp', nullable: true })
    windowTime: Date;

    @Column()
    templateName : string;

    @Column()
    msgType: string
  
    
    
}

export default WhatsappConversation;