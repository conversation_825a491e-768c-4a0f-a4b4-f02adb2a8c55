import { <PERSON><PERSON><PERSON>, <PERSON>NotEmpty, IsOptional } from 'class-validator';
import Client from 'src/modules/clients/entity/client.entity';
import { Organization } from 'src/modules/organization/entities/organization.entity';

export class ImportDscRegisterDto {
  @IsNotEmpty({ message: 'Holder name is required' })
  holderName: string;

  @IsOptional()
  organization: Organization;

  @IsNotEmpty({ message: 'Expiry is requred' })
  expiryDate: string;

  @IsNotEmpty({ message: 'Password is required' })
  password: string;

  @IsOptional()
  tokenNumber: string;

  @IsOptional()
  panNumber: string;

  @IsOptional()
  holderDesignation: string;

  @IsOptional()
  mobileNumber: string;

  @IsNotEmpty({ message: 'Select Country' })
  countryCode: string;

  @IsOptional()
  clients: Client[];

  @IsOptional()
  @IsEmail({}, { message: 'Email is invalid' })
  email: string;
}