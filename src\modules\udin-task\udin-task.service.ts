import { BadRequestException, Injectable } from '@nestjs/common';
import { Brackets, In, createQuery<PERSON>uilder, getManager } from 'typeorm';
import Task from '../tasks/entity/task.entity';
import { User, UserType } from '../users/entities/user.entity';
import UdinTask, { UdinTaskStatus, userType } from './udin-task.entity';
import { TaskRecurringStatus, TaskStatusEnum } from '../tasks/dto/types';
import * as moment from 'moment';
import * as xlsx from 'xlsx';
import { identity } from 'lodash';
import { formatDate } from 'src/utils';
import Activity, { ActivityType } from '../activity/activity.entity';
import { Event_Actions } from 'src/event-listeners/actions';
import * as ExcelJS from 'exceljs';
import { Permissions } from '../events/permission';
import { dateFormation } from 'src/utils/datesFormation';



@Injectable()
export class UdinTaskService {

  async getUdinTasks(query, userId) {
    const user = await User.findOne({ where: { id: userId }, relations: ['organization', 'role'] });
    const ViewAll = user.role.permissions.some(
      (permission) => permission.slug === Permissions.VIEW_ALL_CLIENT_MANAGERS
    );
    const ViewAssigned = user.role.permissions.some(
      (permission) => permission.slug === Permissions.VIEW_ASSIGNED_CLIENT_MANAGERS
    );
    const udinTasks = await createQueryBuilder(UdinTask, 'udinTask')
      .leftJoinAndSelect('udinTask.client', 'udinClient')
      .leftJoin('udinClient.clientManagers', 'clientManagers')
      .leftJoinAndSelect('udinTask.clientGroup', 'udinClientGroup')
      .leftJoin('udinClientGroup.clientGroupManagers', 'clientGroupManagers')
      .leftJoinAndSelect('udinTask.task', 'task')
      .leftJoinAndSelect('task.parentTask', 'parentTask')
      .leftJoinAndSelect('task.client', 'client')
      .leftJoin('client.clientManagers', 'taskClientManagers')
      .leftJoinAndSelect('task.clientGroup', 'clientGroup')
      .leftJoin('clientGroup.clientGroupManagers', 'taskClientGroupManagers')
      .leftJoinAndSelect('task.members', 'members')
      .leftJoinAndSelect('udinTask.user', 'udinUser')
      .leftJoinAndSelect('udinTask.organization', 'organization')
      .where(new Brackets(qb => {
        qb.where('udinTask.task IS NULL')
          .orWhere('udinTask.task IS NOT NULL')
          .orWhere('udinTask.userType = :userType', { userType: UserType.NON_ORGANIZATION });
      }))
      .andWhere(new Brackets(qb => {
        qb.where('udinTask.task IS NULL')
          .orWhere(new Brackets(innerQb => {
            innerQb.where('task.recurringStatus = :recurringStatus', { recurringStatus: TaskRecurringStatus.CREATED });
          }));
      }))
      .andWhere(new Brackets(qb => {
        qb.where('task.id IS NULL')
          .orWhere(`task.status NOT IN ('${TaskStatusEnum.DELETED}', '${TaskStatusEnum.TERMINATED}')`);
      }))

      .andWhere('udinTask.udinTaskStatus = :udinStatus', { udinStatus: UdinTaskStatus.ACTIVE })
      .andWhere('organization.id = :orgId', { orgId: user.organization.id })
      .orderBy('task.id', 'DESC')
      .addOrderBy('udinTask.id', 'DESC');


    udinTasks
      .andWhere(
        new Brackets((qb) =>
          qb
            .where('task.status IN (:...statuses)', {
              statuses: [
                TaskStatusEnum.TODO,
                TaskStatusEnum.IN_PROGRESS,
                TaskStatusEnum.ON_HOLD,
                TaskStatusEnum.UNDER_REVIEW,
                TaskStatusEnum.COMPLETED
              ],
            })
            .andWhere('task.recurringStatus NOT IN (:recStatus)', { recStatus: 'pending' })
            .orWhere('task.id IS NULL')
        ),
      );


    if (!ViewAll && ViewAssigned) {
      udinTasks.andWhere(
        new Brackets(qb => {
          qb.where(new Brackets(innerQb => {
            innerQb.where('udinClient.id IS NOT NULL')
              .andWhere('clientManagers.id = :userId', { userId });
          }))
            .orWhere(new Brackets(innerQb => {
              innerQb.where('udinClientGroup.id IS NOT NULL')
                .andWhere('clientGroupManagers.id = :userId', { userId });
            }))
            .orWhere(new Brackets(innerQb => {
              innerQb.where('client.id IS NOT NULL')
                .andWhere('taskClientManagers.id = :userId', { userId });
            }))
            .orWhere(new Brackets(innerQb => {
              innerQb.where('clientGroup.id IS NOT NULL')
                .andWhere('taskClientGroupManagers.id = :userId', { userId });
            }))
            .orWhere(new Brackets(innerQb => {
              innerQb.where('udinClient.id IS NULL')
                .andWhere('udinClientGroup.id IS NULL')
                .andWhere('client.id IS NULL')
                .andWhere('clientGroup.id IS NULL');
            }));
        })
      );
    } else if (!ViewAll && !ViewAssigned) {
      udinTasks.andWhere(
        new Brackets(qb => {
          qb.where('udinClient.id IS NULL')
            .andWhere('udinClientGroup.id IS NULL')
            .andWhere('client.id IS NULL')
            .andWhere('clientGroup.id IS NULL');
        })
      );
    }


    if (query.selectedTaskType === 'task') {
      udinTasks.andWhere('task.parentTask IS NULL');
    }

    if (query.selectedTaskType === 'sub-task') {
      udinTasks.andWhere('task.parentTask IS NOT NULL');
    }

    if (query?.type?.includes('withoutAtom')) {
      udinTasks.andWhere('udinTask.userType = :userType', { userType: UserType.NON_ORGANIZATION });
    }

    if (query?.type?.includes('withAtom')) {
      udinTasks.andWhere('udinTask.userType = :userType', { userType: UserType.ORGANIZATION })
        .andWhere('udinTask.task IS NOT NULL')
        .andWhere(new Brackets(qb => {
          qb.where('task.recurringStatus IS NULL')
            .orWhere('task.recurringStatus = :recurringStatus', { recurringStatus: TaskRecurringStatus.CREATED });
        }));
    }


    if (query?.assignee?.length) {
      udinTasks.andWhere('udinUser.id IN (:...assignee)', {
        assignee: query.assignee,
      });
    }

    if (query.search) {
      udinTasks.andWhere(
        `(
          task.name LIKE :search OR
          task.taskNumber LIKE :search OR
          client.displayName LIKE :search OR
          udinTask.udinNumber LIKE :search OR
          udinTask.name LIKE :search OR
          udinClient.displayName LIKE :search
        )`,
        { search: `%${query.search}%` },
      );
    }

    if (query.offset) {
      udinTasks.skip(query.offset);
    }

    if (query.limit) {
      udinTasks.take(query.limit);
    }
    const sort = (typeof query?.sort === "string") ? JSON.parse(query.sort) : query?.sort;
    if (sort?.column) {
      const columnMap: Record<string, string> = {
        taskNumber: 'task.taskNumber',
        date: 'udinTask.date',
        name: 'task.name',
        client: 'client.displayName',
        fullName: 'user.fullName',
        udinNumber: 'udinTask.udinNumber',
      };
      const column = columnMap[sort.column] || sort.column;
      udinTasks.orderBy(column, sort.direction.toUpperCase());
    } else {
      udinTasks.orderBy('udinTask.date', 'DESC');
    }
    let result = await udinTasks.getManyAndCount();
    return result;
  }


  async exportUdinTasksPageReport(userId: number, query: any) {
    // Fetch UDIN tasks data
    let udintasks = await this.getUdinTasks(query, userId);

    if (!udintasks || !udintasks.length) {
      throw new BadRequestException('No Data for Export');
    }

    // Sort tasks based on the date (if available)

    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('UDIN Register');

    // Define headers for the worksheet
    const headers = [
      { header: 'S.No', key: 'serialNo' },
      { header: 'Client / Client Group', key: 'clientGroup' },
      { header: 'Task ID', key: 'taskId' },
      { header: 'Task Name/Title', key: 'taskName' },
      { header: 'UDIN', key: 'udin' },
      { header: 'Date of Signing', key: 'dateOfSigning' },
      { header: 'Professional Name', key: 'professionalName' },
    ];

    // Add headers to the worksheet
    worksheet.columns = headers;
    let serialCounter = 1; // Initialize a counter

    // Format and map the data
    const formatDate = (dateString: string): string => {
      if (!dateString || isNaN(new Date(dateString).getTime())) {
        return "";
      }
      const date = new Date(dateString);
      const day = date.getDate().toString().padStart(2, '0');
      const month = (date.getMonth() + 1).toString().padStart(2, '0');
      const year = date.getFullYear();
      return `${day}-${month}-${year}`;
    };

    const rows = udintasks[0].map((udintask: any) => {
      return {
        serialNo: serialCounter++,// Assign and then increment the counter
        clientGroup: udintask?.client
          ? udintask?.client?.displayName
          : (udintask?.clientGroup?.displayName ||
            (udintask?.task?.client
              ? udintask?.task?.client?.displayName
              : udintask?.task?.clientGroup?.displayName)) || " ",
        taskId: udintask?.task?.taskNumber || " ",
        taskName: udintask?.task?.name || udintask?.name || " ",
        udin: udintask?.udinNumber || " ",
        dateOfSigning: formatDate(udintask?.date) || " ",
        professionalName: udintask?.user?.fullName || " ",
      };
    });

    // Add rows to the worksheet
    rows.forEach(row => worksheet.addRow(row));

    // Style the header row
    const headerRow = worksheet.getRow(1);
    headerRow.font = { bold: true };
    headerRow.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };

    // Apply background color only for actual headers
    headers.forEach((_, index) => {
      const cell = headerRow.getCell(index + 1);
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '64B5F6' },
      };
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' },
      };
    });

    // Adjust column widths dynamically and apply alignment
    worksheet.columns.forEach((column) => {
      if (column.key === 'clientGroup' || column.key === 'taskName') {
        column.width = 50;
        column.alignment = { wrapText: true, horizontal: 'center', vertical: 'middle' };
      } else {
        let maxLength = column.header.length;
        column.eachCell({ includeEmpty: true }, (cell) => {
          const cellValue = cell.value || '';
          const cellLength = cellValue.toString().length;
          if (cellLength > maxLength) {
            maxLength = cellLength;
          }
        });
        column.width = maxLength + 2;
        column.alignment = { horizontal: 'center', vertical: 'middle' };
      }
    });

    // Apply alignment to all rows
    worksheet.eachRow((row) => {
      row.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
    });

    // Freeze the header row
    worksheet.views = [{ state: 'frozen', ySplit: 1 }];

    // Generate the Excel file as a buffer
    const buffer = await workbook.xlsx.writeBuffer();

    return buffer;
  }



  async getUdinTask(query, userId) {
    const user = await User.findOne({ where: { id: userId }, relations: ['organization'] });

    const udinTask = await UdinTask.findOne({
      where: { task: { id: query.taskId }, organization: { id: user.organization.id } },
      relations: ['user']
    });

    return udinTask;
  }


  async update(body: any, userId: number) {
    let user = await User.findOne({
      where: { id: userId },
      relations: ['organization'],
    });
    const udinTask = await createQueryBuilder(UdinTask, 'udinTask')
      .leftJoinAndSelect('udinTask.task', 'task')
      .leftJoinAndSelect('udinTask.user', 'user')
      .leftJoinAndSelect('udinTask.organization', 'organization')
      .where('task.id = :taskId', { taskId: body?.taskId })
      .andWhere('organization.id = :org', { org: user.organization.id })
      .getOne();

    const oldDate = udinTask?.date;
    const oldNumber = udinTask?.udinNumber;
    const oldUser = udinTask?.user;

    const task = await Task.findOne({ where: { id: body?.taskId } }); 

    if (body?.isUdin && udinTask?.task) {
      udinTask.task = task;
      udinTask.date = body?.udinDate;
      udinTask.udinNumber = body?.udinNumber;
      udinTask.user = body?.udinUser;
      await udinTask.save();
     
      let activity = new Activity();
      activity.action = Event_Actions.UDIN_UPDATED;
      activity.actorId = user.id;
      activity.type = ActivityType.TASK;
      activity.typeId = udinTask.task ? udinTask?.task?.id : task.id;
      activity.remarks = `UDIN Certificate Details updated from "${oldUser?.fullName || 'NA'} | ${oldNumber || "NA"} | ${moment(oldDate).format("DD-MM-YYYY") || "NA"} " to "${udinTask?.user?.fullName} | ${udinTask.udinNumber} | ${moment(udinTask.date).format("DD-MM-YYYY")}" by ${user?.fullName}`;
      await activity.save();
    }
    if (body?.isUdin && !udinTask?.task) {
      let udinTask = new UdinTask();
      udinTask.task = task;
      udinTask.user = body?.udinUser;
      udinTask.date = body?.udinDate;
      udinTask.organization = user.organization;
      udinTask.udinNumber = body?.udinNumber;
      await udinTask.save();
      
        task.isUdin = true;
        await task.save();
      
       
    
      let activity = new Activity();
      activity.action = Event_Actions.UDIN_ADDED;
      activity.actorId = user.id;
      activity.type = ActivityType.TASK;
      activity.typeId = udinTask.task ? udinTask?.task?.id : task.id;
      activity.remarks = `UDIN Certificate Details "${udinTask.user.fullName} | ${udinTask.udinNumber} | ${moment(udinTask.date).format("DD-MM-YYYY")}" added by ${user.fullName}`;
      await activity.save();
    }

    if (!body?.isUdin && udinTask?.task) {
      await udinTask.remove();
     
        task.isUdin = false;
        await task.save();
      
      
      if (udinTask?.udinNumber) {
        let activity = new Activity();
        activity.action = Event_Actions.UDIN_REMOVED;
        activity.actorId = user.id;
        activity.type = ActivityType.TASK;
        activity.typeId = udinTask.task ? udinTask?.task?.id : task.id;
        activity.remarks = `UDIN Certificate Details "${udinTask.user.fullName} | ${udinTask.udinNumber} | ${moment(udinTask.date).format("DD-MM-YYYY")}" removed by ${user?.fullName}`;
        await activity.save();
      }
      return null;
    }
    if (!body?.isUdin && !udinTask?.task) {
       task.isUdin = false;
        await task.save();
    }
    return udinTask;
  }

  async createUdinTask(userId: number, body: any) {
    const user = await User.findOne({ where: { id: userId }, relations: ['organization'], });
    let newUdinTask = new UdinTask();
    newUdinTask.date = body?.udinDate;
    newUdinTask.udinNumber = body?.udinNumber;
    newUdinTask.organization = user.organization;
    newUdinTask.user = body?.user;
    newUdinTask.client = body?.client?.type === "CLIENT_GROUP" ? null : body?.client;
    newUdinTask.clientGroup = body?.client?.type === "CLIENT_GROUP" ? body?.client : null;
    newUdinTask.name = body?.name;
    newUdinTask.userType = userType.NON_ORGANIZATION;
    newUdinTask.udinTaskStatus = UdinTaskStatus.ACTIVE;
    await newUdinTask.save();
    return newUdinTask;
  }

  async updateUdinTask(userId: number, id: number, body: any) {
    const user = await User.findOne({ where: { id: userId }, relations: ['organization'], });
    const udinTask = await UdinTask.findOne({ where: { id } });
    let newUdinTask = udinTask;
    newUdinTask.date = body?.udinDate;
    newUdinTask.udinNumber = body?.udinNumber;
    newUdinTask.organization = user.organization;
    newUdinTask.user = body?.user;
    newUdinTask.client = body?.client?.type === "CLIENT_GROUP" ? null : body?.client;
    newUdinTask.clientGroup = body?.client?.type === "CLIENT_GROUP" ? body?.client : null;
    newUdinTask.name = body?.name;
    newUdinTask.userType = userType.NON_ORGANIZATION;
    newUdinTask.udinTaskStatus = UdinTaskStatus.ACTIVE;
    await newUdinTask.save();
    return newUdinTask;
  }


}
