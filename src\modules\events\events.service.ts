import { Injectable } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { Event_Actions } from 'src/event-listeners/actions';
import Client from 'src/modules/clients/entity/client.entity';
import Task from 'src/modules/tasks/entity/task.entity';
import { User } from 'src/modules/users/entities/user.entity';
import { createQueryBuilder, getConnection } from 'typeorm';
import { CreateEventDto, EventTypeEnum } from './dto/create-event.dto';
import Event from './event.entity';
import * as moment from 'moment';
import { Permissions } from './permission';
import Activity, { ActivityType } from '../activity/activity.entity';
import { sendClientWhatsAppTemplateMessage, sendWhatsAppTemplateMessage } from '../whatsapp/whatsapp.service';
import { getUserDetails } from 'src/utils/re-use';
import { Cron, CronExpression } from '@nestjs/schedule';
import ClientGroup from '../client-group/client-group.entity';
import puppeteer from 'puppeteer';
import PosterConfig from '../poster/poster-config.entity';
import * as sharp from 'sharp';
import axios from 'axios';
import Storage, { StorageSystem, StorageType } from '../storage/storage.entity';
import { AwsService } from '../storage/upload.service';
import { OneDriveStorageService } from '../ondrive-storage/onedrive-storage.service';
import { BharathCloudService } from '../storage/bharath-upload.service';
import { StorageService } from '../storage/storage.service';
@Injectable()
export class EventsService {
  constructor(
    private eventEmitter: EventEmitter2,
    private awsService: AwsService,
    private oneDriveService: OneDriveStorageService,
    private bharathService: BharathCloudService,
    private storageService: StorageService,
  ) { }

  async create(userId: number, body: CreateEventDto) {
    let user = await User.findOne({
      where: { id: userId },
      relations: ['organization'],
    });

    let event = new Event();
    event.title = body.title.trim();
    event.type = body.type;
    event.location = body.location.trim();
    event.date = body.date;
    event.startTime = body.startTime;
    event.endTime = body.endTime;
    event.reminder = body.reminder;
    event.notes = body.notes.trim();
    event.user = user;
    event.organization = user.organization;
    event['userId'] = userId;

    if (body.type === EventTypeEnum.TASK) {
      let client = await Client.findOne({ where: { id: body.client } });
      let clientGroup = await ClientGroup.findOne({ where: { id: body.clientGroup } });
      let task = await Task.findOne({ where: { id: body.task }, relations: ['clientGroup'] });
      let members = await User.findByIds(body.members);
      event.client = client;
      event.clientGroup = clientGroup;
      event.clientGroup = task.clientGroup;
      event.task = task;
      event.members = members;
      event.whatsappEnabled = body.whatsappEnabled ?? false;

    }

    await event.save();
    if (body.type === EventTypeEnum.TASK) {
      let taskactivity = new Activity();
      taskactivity.action = Event_Actions.EVENT_CREATED;
      taskactivity.actorId = userId;
      taskactivity.type = ActivityType.TASK;
      taskactivity.typeId = body.task;
      taskactivity.remarks = `Event "${event.title}" Created by ${user.fullName}`;
      await taskactivity.save();
    }

    //Testing event-created
    let orgName = user.organization.legalName;

    this.eventEmitter.emit(Event_Actions.EVENT_CREATED, { eventData: event, user, orgName });

    return event;
  }

  async update(id: number, data: CreateEventDto, userId) {
    let user = await User.findOne({
      where: { id: userId },
      relations: ['organization'],
    });
    let event = await Event.findOne({ where: { id }, relations: ['task'] });
    event.title = data.title.trim();
    event.location = data.location.trim();
    event.date = data.date;
    event.startTime = data.startTime;
    event.endTime = data.endTime;
    event.reminder = data.reminder;
    event.notes = data.notes.trim();
    event.eventType = data.eventType;

    if (data.client) {
      let client = await Client.findOne({ where: { id: data.client } });
      event.client = client;
    }

    if (data.task) {
      let task = await Task.findOne({ where: { id: data.task } });
      event.task = task;
    }

    if (data.members) {
      let members = await User.findByIds(data.members);
      event.members = members;
    }
    event['userId'] = userId;
    await event.save();
    if (data.task) {
      let taskactivity = new Activity();
      taskactivity.action = Event_Actions.EVENT_UPDATED;
      taskactivity.actorId = userId;
      taskactivity.type = ActivityType.TASK;
      taskactivity.typeId = data.task;
      taskactivity.remarks = `Event "${event.title}" Updated by ${user.fullName}`;
      await taskactivity.save();
    }
    return event;
  }

  async delete(id: number, userId) {
    let user = await User.findOne({
      where: { id: userId },
      relations: ['organization'],
    });
    let event = await Event.findOne({ where: { id }, relations: ['task'] });
    event['userId'] = userId;
    await event.remove();
    if (event.task) {
      let taskactivity = new Activity();
      taskactivity.action = Event_Actions.EVENT_DELETED;
      taskactivity.actorId = userId;
      taskactivity.type = ActivityType.TASK;
      taskactivity.typeId = event.task.id;
      taskactivity.remarks = `Event "${event.title}" Deleted by ${user.fullName}`;
      await taskactivity.save();
    }
    return event;
  }

  async getEvents(userId: number, query: any) {
    let user = await User.findOne(userId, { relations: ['organization'] });

    let userDetails = await User.findOne({
      where: { id: userId },
      relations: ['role', 'role.permissions'],
    });

    let eventPermission = userDetails?.role?.permissions?.find(
      (permission) => permission.slug === Permissions.VIEW_CALENDAR,
    );
    if (eventPermission || !userDetails?.role) {
      let events = createQueryBuilder(Event, 'event')
        .leftJoinAndSelect('event.task', 'task')
        .leftJoinAndSelect('event.client', 'client')
        .leftJoinAndSelect('event.clientGroup', 'clientGroup')
        .leftJoinAndSelect('event.user', 'user')
        .leftJoinAndSelect('event.organization', 'organization')
        .leftJoinAndSelect('event.members', 'members');

      if (query.taskId) {
        events.where('task.id = :taskId', { taskId: query.taskId });
      }

      if (!query.taskId) {
        events.where('organization.id = :orgId', { orgId: user.organization.id });
      }

      if (query.startDates) {
        const startOfMonth = moment(`${query.startDates}`).startOf('month').format('YYYY-MM-DD');
        const endOfMonth = moment(query.startDates).endOf('month').format('YYYY-MM-DD');
        events.andWhere(`Date(event.date) BETWEEN '${startOfMonth}' AND '${endOfMonth}'`);
      }

      if (query.members) {
        const members = Array.isArray(query.members) ? query.members : [query.members];
        events.andWhere('members.id IN (:...members)', { members });
      }

      return await events.getMany();
    }
    return [];
  }

  async getDefaultEvents(query: Date) {
    let events = createQueryBuilder(Event, 'event');
    if (query) {
      const startOfMonth = moment(`${query}`).startOf('month').format('YYYY-MM-DD');
      const endOfMonth = moment(query).endOf('month').format('YYYY-MM-DD');
      events
        .where(`Date(event.date) BETWEEN '${startOfMonth}' AND '${endOfMonth}'`)
        .andWhere('event.defaultOne=:true', { true: true });
    }
    return await events.getMany();
  }

  async generateComplianceCalendar(userId: number, monthParam: string): Promise<string> {
    if (!monthParam) {
      throw new Error('Month parameter is required in YYYY-MM format');
    }

    const now = moment(monthParam, 'YYYY-MM', true);
    if (!now.isValid()) {
      throw new Error('Invalid month format. Use YYYY-MM format.');
    }

    const month = now.format('MMMM');
    const year = now.format('YYYY');

    const user = await User.findOne({
      where: { id: userId },
      relations: ['organization'],
    });

    if (!user) throw new Error('User not found');

    const poster = await createQueryBuilder(PosterConfig, 'poster')
      .leftJoinAndSelect('poster.organization', 'organization')
      .leftJoinAndSelect('poster.orgLogo', 'orgLogo')
      .where('organization.id = :orgId', { orgId: user.organization.id })
      .getOne();

    const firmName = poster?.firmName || user?.organization?.legalName || '';

    const logoStorage = poster?.orgLogo;
    const hasLogo = !!logoStorage?.file;

    let logoBuffer: Buffer | null = null;

    if (hasLogo) {
      try {
        const size = 80; // final logo size

        // Rounded square mask
        const roundedSquareMask = Buffer.from(`
      <svg width="${size}" height="${size}">
        <rect width="${size}" height="${size}" rx="10" ry="10" fill="white"/>
      </svg>
    `);

        if (logoStorage.storageSystem === StorageSystem.AMAZON) {
          const logoData: any = await this.awsService.getFileFromS3(
            process.env.AWS_BUCKET_NAME,
            logoStorage.file,
          );

          if (logoData?.Body) {
            logoBuffer = await sharp(logoData.Body)
              .resize(size, size)
              .composite([{ input: roundedSquareMask, blend: 'dest-in' }])
              .png()
              .toBuffer();
          }

        } else if (logoStorage.storageSystem === StorageSystem.MICROSOFT) {
          const metadata: any = await this.oneDriveService.getMetadataByFileId(
            userId,
            logoStorage.fileId,
          );
          const downloadUrl = metadata['@microsoft.graph.downloadUrl'];
          if (!downloadUrl) throw new Error('Download URL not found in metadata');

          const response = await axios.get(downloadUrl, { responseType: 'arraybuffer' });

          logoBuffer = await sharp(response.data)
            .resize(size, size)
            .composite([{ input: roundedSquareMask, blend: 'dest-in' }])
            .png()
            .toBuffer();
        }
      } catch (err) {
        console.warn('Logo retrieval failed, skipping logo.');
      }
    }


    let logoBase64: string | null = null;
    if (logoBuffer) {
      logoBase64 = logoBuffer.toString('base64');
    }

    const startOfMonth = now.startOf('month').format('YYYY-MM-DD');
    const endOfMonth = now.endOf('month').format('YYYY-MM-DD');

    const events = await Event.createQueryBuilder('event')
      .where('event.date BETWEEN :start AND :end', {
        start: startOfMonth,
        end: endOfMonth,
      })
      .andWhere('event.defaultOne = :defaultOne', { defaultOne: 1 })
      .orderBy('event.date', 'ASC')
      .getMany();

    events.sort((a, b) => {
      const aStar = a.title.startsWith('*') ? 1 : 0;
      const bStar = b.title.startsWith('*') ? 1 : 0;

      if (aStar !== bStar) return aStar - bStar; 

      return moment(a.date).diff(moment(b.date)); 
    });

    const htmlContent = `
  <html>
    <head>
      <style>
        body {
          font-family: Arial, sans-serif;
          padding: 0px;
          background: #fff;
        }
        h2 {
          text-align: center;
          margin-bottom: 30px;
          margin-top: 0px;
        }
        table {
          width: 100%;
          border-collapse: collapse;
          margin-bottom: 30px;
          table-layout: fixed;
        }
        th,
        td {
          border: 1px solid #ccc;
          padding: 10px;
          font-size: 13px;
          text-align: justify;
          word-wrap: break-word;
        }
        th {
          background-color: rgb(33, 77, 171);
          color: rgb(255, 255, 255);
        }
        .footer {
          text-align: center;
          font-weight: 200px;
          margin-top: 20px;
        }
        .due-date { width: 10%; }
        .event-type { width: 10%; }
        .title { width: 40%; }
        .description { width: 40%; }

        /* Row colors by event type - brighter shades */
        .gst-type { background-color: #b2dfdb; }        /* Teal pastel */
        .income-tax-type { background-color: #ffccbc; } /* Soft coral */
        .mca-type { background-color: #c5e1a5; }        /* Fresh green */
        .epfo-type { background-color: #d1c4e9; }       /* Soft lavender */
        .esic-type { background-color: #fff59d; }       /* Warm yellow */
        thead th {
          font-size: 18px;
          font-weight: 700;
          padding: 10px;
          text-align: center;
        }
        tbody td {
          text-align: center;
          padding: 8px;
        }


      </style>
    </head>
    <body>
      <h2>Compliance Calendar for ${month} ${year}</h2>
      <table>
        <thead>
          <tr>
            <th class="due-date">Date</th>
            <th class="event-type">Event Type</th>
            <th class="title">Title</th>
            <th class="description">Description</th>
          </tr>
        </thead>
        <tbody>
          ${events.length === 0
        ? `<tr><td colspan="4">No events found for this month.</td></tr>`
        : events
          .map((e) => {
            let rowClass = '';
            switch (e.eventType) {
              case 'GST': rowClass = 'gst-row'; break;
              case 'Income Tax': rowClass = 'income-tax-row'; break;
              case 'MCA': rowClass = 'mca-row'; break;
              case 'EPFO': rowClass = 'epfo-row'; break;
              case 'ESIC': rowClass = 'esic-row'; break;
              default: rowClass = '';
            }

            return `
              <tr>
                <td class="due-date">
                  ${moment(e.date).format('DD MMM YYYY')} <br/>
                  (${moment(e.date).format('dddd')})
                </td>
                <td class="event-type ${e.eventType === 'GST' ? 'gst-type' :
                e.eventType === 'Income Tax' ? 'income-tax-type' :
                  e.eventType === 'MCA' ? 'mca-type' :
                    e.eventType === 'EPFO' ? 'epfo-type' :
                      e.eventType === 'ESIC' ? 'esic-type' : ''
              }">${e.eventType || '-'}</td>
              <td class="title">${e.title}</td>
                <td class="description">${e.notes ? e.notes.replace(/<[^>]+>/g, '') : '-'}</td>
              </tr>
            `;
          })
          .join('')
      }
        </tbody>
      </table>
      <h2 class="footer">-----  The End  -----</h2>
    </body>
  </html>
  `;

    const browser = await puppeteer.launch({
      headless: true,
      userDataDir: '/tmp/puppeteer-profile',
      args: ['--no-sandbox', '--disable-setuid-sandbox'],
    });
    const page = await browser.newPage();
    await page.setContent(htmlContent, { waitUntil: 'networkidle0' });

    const headerTemplate = `
  <div style="
    width: 100%;
    height: 120px;
    background-color: #003366 !important;  /* Dark Blue */
    -webkit-print-color-adjust: exact; 
    color: #ffffff;
    display: flex;
    flex-direction: column;
    justify-content: center;
    margin-top:-20px;
    padding: 5px 40px;
    box-sizing: border-box;
  ">
    <!-- Small 'Name' label on top -->
    <div style="font-size: 14px; font-weight: normal; text-align: left;">
      Organisation Name
    </div>

    <!-- Firm Name below -->
    <div style="font-size: 30px; font-weight: bold; text-align: left; margin-top: 5px;">
      ${firmName}
    </div>

    <!-- Logo on the right if present -->
    ${logoBase64
        ? `<img src="data:image/png;base64,${logoBase64}" 
              style="height:90px; width:90px; border-radius:10px; position: absolute; right: 20px; top: 15px;" />`
        : ''
      }
  </div>
`;
    const pdfBuffer = await page.pdf({
      format: 'A4',
      printBackground: true,
      landscape: true,
      displayHeaderFooter: true,
      headerTemplate: headerTemplate,
      footerTemplate: `
      <div style="font-size:12px; width:100%; text-align:center; margin-bottom:10px;">
        Page <span class="pageNumber"></span> of <span class="totalPages"></span>
      </div>
    `,
      margin: {
        top: '140px',
        bottom: '60px',
        left: '30px',
        right: '30px',
      },
    });

    await browser.close();

    const buffer = Buffer.from(pdfBuffer);
    return `data:application/pdf;base64,${buffer.toString('base64')}`;
  }

  @Cron(CronExpression.EVERY_MINUTE)
  async generalEventReminder() {
    if (process.env.Cron_Running === 'true') {
      async function getEvents() {
        const query = `
            SELECT *,
       CASE 
           WHEN reminder = 'before_1_day' THEN DATE_FORMAT(DATE_ADD(CONCAT(date, ' ', TIME(start_time)), INTERVAL -1 DAY), '%M %d, %Y %h:%i %p')
           WHEN reminder = 'before_2_hours' THEN DATE_FORMAT(DATE_ADD(CONCAT(date, ' ', TIME(start_time)), INTERVAL -2 HOUR), '%M %d, %Y %h:%i %p')
           WHEN reminder = 'before_1_hour' THEN DATE_FORMAT(DATE_ADD(CONCAT(date, ' ', TIME(start_time)), INTERVAL -1 HOUR), '%M %d, %Y %h:%i %p')
           WHEN reminder = 'before_30_minutes' THEN DATE_FORMAT(DATE_ADD(CONCAT(date, ' ', TIME(start_time)), INTERVAL -30 MINUTE), '%M %d, %Y %h:%i %p')
           WHEN reminder = 'before_15_minutes' THEN DATE_FORMAT(DATE_ADD(CONCAT(date, ' ', TIME(start_time)), INTERVAL -15 MINUTE), '%M %d, %Y %h:%i %p')
     ELSE NULL
       END AS formatted_reminder_time
  FROM event
  WHERE event.type = 'EVENT' AND event.is_sent_whatsapp = false
  HAVING STR_TO_DATE(formatted_reminder_time, '%M %d, %Y %h:%i %p') BETWEEN NOW() - INTERVAL 1 MINUTE AND NOW();
        `;

        try {
          const connection = getConnection();
          const events = await connection.query(query);
          return events;
        } catch (error) {
          console.error('Error fetching events:', error);
          return [];
        }
      }

      const events = await getEvents();
      const connection = getConnection();
      for (const event of events) {
        try {
          const userId = event?.user_id;
          const userDetails = await getUserDetails(userId);
          const {
            full_name: userFullName,
            mobile_number: userPhoneNumber,
            organization_id: orgId,
          } = userDetails;
          const eventReminder =
            event.reminder === 'before_30_minutes'
              ? '30 minutes'
              : event.reminder === 'before_1_hour'
                ? '1 hour'
                : event.reminder === 'before_2_hours'
                  ? '2 hours'
                  : event.reminder === 'before_1_day'
                    ? '1 day'
                    : event.reminder === 'before_15_minutes'
                      ? '15 minutes'
                      : null;
          // const eventDate = moment(event?.date).format("MMMM DD, YYYY")
          // const formattedStartTime = moment(event?.start_time, "YYYY-MM-DD HH:mm:ss").format("hh:mm a");
          // const formattedEndTime = moment(event?.end_time, "YYYY-MM-DD HH:mm:ss").format("hh:mm a");
          const eventDate = moment(event?.date).utcOffset('+05:30').format('MMMM DD, YYYY');
          const formattedStartTime = moment(event?.start_time, 'YYYY-MM-DD HH:mm:ss')
            .utcOffset('+05:30')
            .format('hh:mm a');
          const formattedEndTime = moment(event?.end_time, 'YYYY-MM-DD HH:mm:ss')
            .utcOffset('+05:30')
            .format('hh:mm a');
          const timeRange = `${formattedStartTime} - ${formattedEndTime}`;
          const Time = `${eventDate},${timeRange}`;
          const eventDescriptionWithTags = event?.notes;
          const eventNotes = eventDescriptionWithTags.replace(/<[^>]+>/g, '');

          console.log(`hi ${userFullName} reminder to ${userPhoneNumber},${event.id}`);

          const templateName = 'general_event_reminder';
          const eventTitle = event?.title;
          const reminderTime = eventReminder;
          const eventTime = Time;
          const location = event?.location;
          const eventDescription = event?.notes ? eventNotes : '-';
          const whatsappOptions = {
            to: userPhoneNumber,
            orgId: orgId,
            title: 'Event Reminder',
            name: templateName,
            header: [
              {
                type: 'text',
                text: userFullName,
              },
            ],
            body: [eventTitle, reminderTime, eventTime, location, eventDescription],
            userId,
          };
          console.log(eventTitle, reminderTime, eventTime, location, eventDescription);
          await sendWhatsAppTemplateMessage(whatsappOptions);
          const updateQuery = `
            UPDATE event
            SET is_sent_whatsapp = true
            WHERE id = ${event.id};
        `;
          await connection.query(updateQuery, [event.id]);
        } catch (error) {
          console.error(`Error sending reminder for event ${event.id}:`, error);
        }
      }
    }
  }

  @Cron(CronExpression.EVERY_MINUTE)
  async taskEventReminder() {
    if (process.env.Cron_Running === 'true') {
      async function getEvents() {
        const query = `
      SELECT event.* , client.display_name as clientName,client_group.display_name as clientGroupName, task.name as taskName,
      CASE 
          WHEN reminder = 'before_1_day' THEN DATE_FORMAT(DATE_ADD(CONCAT(date, ' ', TIME(start_time)), INTERVAL -1 DAY), '%M %d, %Y %h:%i %p')
  WHEN reminder = 'before_2_hours' THEN DATE_FORMAT(DATE_ADD(CONCAT(date, ' ', TIME(start_time)), INTERVAL -2 HOUR), '%M %d, %Y %h:%i %p')
  WHEN reminder = 'before_1_hour' THEN DATE_FORMAT(DATE_ADD(CONCAT(date, ' ', TIME(start_time)), INTERVAL -1 HOUR), '%M %d, %Y %h:%i %p')
  WHEN reminder = 'before_30_minutes' THEN DATE_FORMAT(DATE_ADD(CONCAT(date, ' ', TIME(start_time)), INTERVAL -30 MINUTE), '%M %d, %Y %h:%i %p')
  WHEN reminder = 'before_15_minutes' THEN DATE_FORMAT(DATE_ADD(CONCAT(date, ' ', TIME(start_time)), INTERVAL -15 MINUTE), '%M %d, %Y %h:%i %p')
  
          ELSE NULL
      END AS formatted_reminder_time
  FROM event
  LEFT JOIN task ON event.task_id = task.id
  LEFT JOIN client ON event.client_id = client.id
  LEFT JOIN client_group ON event.client_group_id = client_group.id
  WHERE event.type = 'TASK' AND (event.is_sent_whatsapp = false OR event.is_sent_whatsapp_client = false)
  HAVING STR_TO_DATE(formatted_reminder_time, '%M %d, %Y %h:%i %p') BETWEEN NOW() - INTERVAL 1 MINUTE AND NOW();
      `;

        try {
          const connection = getConnection();
          const events = await connection.query(query);
          return events;
        } catch (error) {
          console.error('Error fetching events:', error);
          return [];
        }
      }

      const events = await getEvents();
      const connection = getConnection();

      for (const event of events) {
        const eventMembers = await Event.findOne({
          where: { id: event.id },
          relations: ['members'],
        });
        const names = eventMembers?.members?.map((user) => user.fullName).join(', ');
        const members = eventMembers?.members;
        const eventReminder =
          event.reminder === 'before_30_minutes'
            ? '30 minutes'
            : event.reminder === 'before_1_hour'
              ? '1 hour'
              : event.reminder === 'before_2_hours'
                ? '2 hours'
                : event.reminder === 'before_1_day'
                  ? '1 day'
                  : event.reminder === 'before_15_minutes'
                    ? '15 minutes'
                    : null;
        if (members) {
          for (let user of members) {
            try {
              const userId = event?.user_id;
              const userDetails = await getUserDetails(userId);
              const {
                full_name: userFullName,
                mobile_number: userPhoneNumber,
                organization_id: orgId,
              } = userDetails;

              const eventDate = moment(event?.date).utcOffset('+05:30').format('MMMM DD, YYYY');
              const formattedStartTime = moment(event?.start_time, 'YYYY-MM-DD HH:mm:ss')
                .utcOffset('+05:30')
                .format('hh:mm a');
              const formattedEndTime = moment(event?.end_time, 'YYYY-MM-DD HH:mm:ss')
                .utcOffset('+05:30')
                .format('hh:mm a');
              const timeRange = `${formattedStartTime} - ${formattedEndTime}`;
              const Time = `${eventDate},${timeRange}`;
              const eventDescriptionWithTags = event?.notes;
              const eventNotes = eventDescriptionWithTags.replace(/<[^>]+>/g, '');

              console.log(`hi ${userFullName} reminder to ${userPhoneNumber},${event.id}`);
              console.log(event.clientName, event.clientGroupName);
              const templateName = 'task_event_reminderr';
              const eventTitle = event?.title;
              const reminderTime = eventReminder;
              const eventTime = Time;
              const location = event?.location;
              const eventDescription = event?.notes ? eventNotes : '-';
              const clientTemplateName = 'eventreminderclient';
              const clientMobileNumber = event?.client?.mobile_number || event?.client_group?.mobile_number;
              const clientFullName = event?.client?.display_name || event?.client_group?.display_name
              const whatsappOptions = {
                to: clientMobileNumber,
                name: clientTemplateName,
                header: [
                  {
                    type: 'text',
                    text: clientFullName
                  },
                ],
                body: [reminderTime,
                  eventTime,
                  location,
                  eventTitle,
                  event.clientName ? event.clientName : event.clientGroupName,
                  event.taskName,
                  names,
                  eventDescription],
                title: 'Task event reminder-client',
                userId: event?.user?.id,
                orgId: event?.organization?.id,
                key: 'TASK_EVENT_REMINDER_WHATSAPP',
              };

              // const whatsappOptions = {
              //   to: clientMobileNumber,
              //   orgId: orgId,
              //   title: 'Event Reminder Client',
              //   name: clientTemplateName,
              //   header: [
              //     {
              //       type: 'text',
              //       text: clientFullName,
              //     },
              //   ],
              //   body: [
              //     reminderTime,
              //     eventTime,
              //     location,
              //     eventTitle,
              //     event.clientName ? event.clientName : event.clientGroupName,
              //     event.taskName,
              //     names,
              //     eventDescription,
              //   ],
              //   userId,
              // };
              console.log(
                reminderTime,
                eventTime,
                location,
                eventTitle,
                event.clientName,
                event.taskName,
                names,
                eventDescription,
              );
              await sendClientWhatsAppTemplateMessage(whatsappOptions);
              const updateQuery = `
            UPDATE event
            SET is_sent_whatsapp = true
            WHERE id = ${event.id};
        `;
              await connection.query(updateQuery, [event.id]);
            } catch (error) {
              console.error(`Error sending reminder for event ${event.id}:`, error);
            }
            // === CLIENT / CLIENT GROUP WHATSAPP REMINDER ===
            try {
              const eventDate = moment(event?.date).utcOffset('+05:30').format('MMMM DD, YYYY');
              const formattedStartTime = moment(event?.start_time, 'YYYY-MM-DD HH:mm:ss')
                .utcOffset('+05:30')
                .format('hh:mm a');
              const formattedEndTime = moment(event?.end_time, 'YYYY-MM-DD HH:mm:ss')
                .utcOffset('+05:30')
                .format('hh:mm a');
              const timeRange = `${formattedStartTime} - ${formattedEndTime}`;
              const Time = `${eventDate},${timeRange}`;
              const eventDescriptionWithTags = event?.notes;
              const eventNotes = eventDescriptionWithTags?.replace(/<[^>]+>/g, '');
              const eventDescription = eventNotes || '-';

              const eventTitle = event?.title;
              const reminderTime = eventReminder;
              const eventTime = Time;
              const location = event?.location;
              const taskName = event?.taskName;
              const orgId = event?.organization_id;

              const clientTemplateName = 'eventreminderclient';

              let clientName = '';
              let clientNumber = '';
              let userId = null;

              if (!event.is_sent_whatsapp_client) {
                if (event.client_id) {
                  const [client] = await connection.query(
                    `SELECT display_name, mobile_number, country_code, id FROM client WHERE id = ${event.client_id}`
                  );
                  if (client?.mobile_number) {
                    clientName = client.display_name;
                    clientNumber = client.country_code + client.mobile_number;
                    userId = client.id;
                  }
                } else if (event.client_group_id) {
                  const [groupClient] = await connection.query(
                    `SELECT client.display_name, client.mobile_number, client.country_code, client.id 
         FROM client 
         JOIN client_group_clients_client cgcc ON cgcc.client_id = client.id
         WHERE cgcc.client_group_id = ${event.client_group_id} LIMIT 1`
                  );
                  if (groupClient?.mobile_number) {
                    clientName = groupClient.display_name;
                    clientNumber = groupClient.country_code + groupClient.mobile_number;
                    userId = groupClient.id;
                  }
                }

                if (clientNumber) {
                  const whatsappOptions = {
                    to: clientNumber,
                    orgId: orgId,
                    title: 'Event Reminder',
                    name: clientTemplateName,
                    header: [
                      {
                        type: 'text',
                        text: clientName,
                      },
                    ],
                    body: [
                      reminderTime,
                      eventTime,
                      location,
                      eventTitle,
                      clientName,
                      taskName,
                      names,
                      eventDescription,
                    ],
                    userId,
                  };

                  console.log(`📤 Sending client WhatsApp to: ${clientName} (${clientNumber})`);
                  await sendWhatsAppTemplateMessage(whatsappOptions);

                  // ✅ Update flag
                  const updateClientQuery = `
        UPDATE event
        SET is_sent_whatsapp_client = true
        WHERE id = ${event.id};
      `;
                  await connection.query(updateClientQuery);
                }
              }
            } catch (error) {
              console.error(`❌ Error sending WhatsApp to client/group for event ${event.id}:`, error);
            }

          }
        }

      }

      // for client 

    }
  }
}
