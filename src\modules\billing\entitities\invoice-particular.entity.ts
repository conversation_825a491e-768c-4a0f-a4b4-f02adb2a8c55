import {
  BaseEntity,
  Column,
  CreateDateColumn,
  Entity,
  ManyToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { Invoice } from './invoice.entity';
import { ProformaInvoice } from './proforma-invoice.entity';

export enum TAX_TYPE {
  NON_TAXABLE = 'NON_TAXABLE',
  OUT_OF_SCOPE = 'OUT_OF_SCOPE',
  NON_GST_SUPPLY = 'NON_GST_SUPPLY',
  GST0 = 'GST0',
  GST0_1 = 'GST0_1',
  GST0_25 = 'GST0_25',
  GST1 = 'GST1',
  GST1_5 = 'GST1_5',
  GST3 = 'GST3',
  GST5 = 'GST5',
  GST6 = 'GST6',
  GST7_5 = 'GST7_5',
  GST12 = 'GST12',
  GST18 = 'GST18',
  GST28 = 'GST28',
}

export enum InvoiceType {
  INVOICE = 'INVOICE',
  PROFORMA = 'PROFORMA',
};

@Entity()
class InvoiceParticular extends BaseEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @ManyToOne(() => Invoice, (invoice) => invoice.particulars)
  invoice: Invoice;

  @ManyToOne(() => ProformaInvoice, (proformaInvoice) => proformaInvoice.particulars)
  proformaInvoice: ProformaInvoice;

  @Column()
  name: string;

  @Column()
  hsn: string;

  @Column()
  units: number;

  // @Column({ type: 'bigint' })
  // rate: number;

  @Column({ type: 'decimal', precision: 19, scale: 2 })
  rate: number;

  @Column()
  discountType: 'PERCENT' | 'AMOUNT';

  @Column()
  discount: number;

  // @Column({ type: 'bigint' })
  // amount: number;

  @Column({ type: 'decimal', precision: 19, scale: 2 })
  amount: number;



  @Column({ type: 'enum', enum: TAX_TYPE })
  gst: TAX_TYPE;

  @Column({ nullable: true })
  taskId: number;

  @Column({ type: 'enum', enum: InvoiceType, default: InvoiceType.INVOICE })
  type: InvoiceType;

  @CreateDateColumn()
  createdAt: string;

  @UpdateDateColumn()
  updatedAt: string;


}

export default InvoiceParticular;
