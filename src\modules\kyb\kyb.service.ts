import { Injectable, InternalServerErrorException } from '@nestjs/common';
import { User } from 'src/modules/users/entities/user.entity';
import Client from '../clients/entity/client.entity';
import Kyb from './kyb.entity';
import Storage, { StorageSystem } from '../storage/storage.entity';
import { StorageService } from '../storage/storage.service';
import ClientGroup from '../client-group/client-group.entity';
import { createQueryBuilder } from 'typeorm';
import Activity, { ActivityType } from '../activity/activity.entity';
import { Event_Actions } from 'src/event-listeners/actions';
import { getTitle } from 'src/utils';
import { OneDriveStorageService } from '../ondrive-storage/onedrive-storage.service';
import { BharathStorageService } from '../storage/bharath-storage.service';
import { GoogleDriveStorageService } from '../ondrive-storage/googledrive-storage.service';

@Injectable()
export class KybService {
    constructor(private storageService: StorageService,
        private oneDriveService: OneDriveStorageService,
        private googleDriveService: GoogleDriveStorageService,
        private bharathService: BharathStorageService

    ) { }
    async addKyb(userId: number, data: any) {
        try {
            let storage: Storage;
            let user = await User.findOne({ where: { id: userId } });
            let client = await Client.findOne({ where: { id: data?.client } });
            let clientGroup = await ClientGroup.findOne({ where: { id: data?.clientGroup } });
            let kyb = new Kyb();
            kyb.documentName = data.documentName;
            kyb.documentNumber = data.documentNumber;
            if (data?.storage) {
                storage = await this.storageService.addAttachements(userId, data.storage);
            }

            kyb.client = client;
            kyb.clientGroup = clientGroup;
            kyb.user = user;
            const ky = await kyb.save();
            if (storage) {
                storage.kyb = ky;
                await storage.save();
            }
            let activity = new Activity();
            activity.action = Event_Actions.KYB_INFO_ADDED;
            activity.actorId = user.id;
            activity.type = kyb?.client ? ActivityType.CLIENT : ActivityType.CLIENT_GROUP;
            activity.typeId = kyb?.client ? kyb?.client?.id : kyb?.clientGroup?.id;
            activity.remarks = `"${getTitle(kyb.documentName)}" KYB Info added by ${user.fullName}`;
            await activity.save();


            return kyb;
        } catch (e) {
            console.log(e);
            throw new InternalServerErrorException(e);
        }
    }

    async getKybs(userId: number, query: any) {
        const user = await User.findOne({ where: { id: userId }, relations: ['organization'] });

        const clientGroup = await createQueryBuilder(ClientGroup, 'clientGroup')
            .leftJoinAndSelect('clientGroup.organization', 'organization')
            .leftJoinAndSelect('clientGroup.clients', 'clients')
            .where('clientGroup.id = :id', { id: query?.clientGroupId })
            .andWhere('organization.id = :organization', { organization: user.organization.id })
            .getOne();

        // const clientGroupIDs = clientGroup?.clients.map(item => item.id);

        const clientGroupIDs = clientGroup?.clients?.map(item => item.id).length ? clientGroup?.clients?.map(item => item.id) : [];

        let kybs = await createQueryBuilder(Kyb, 'kyb')
            .leftJoinAndSelect('kyb.client', 'client')
            .leftJoinAndSelect('kyb.clientGroup', 'clientGroup')
            .leftJoinAndSelect('kyb.storage', 'storage')
            .leftJoinAndSelect('kyb.documentsData', 'documentsData');

        if (query?.clientId) {
            kybs.where('client.id = :id', { id: query?.clientId })
        }

        if (query?.clientGroupId) {
            kybs.orWhere('clientGroup.id = :id', { id: query?.clientGroupId })
            if (clientGroupIDs?.length) {
                kybs.orWhere('client.id IN (:...clientGroupIDs)', { clientGroupIDs: clientGroupIDs });
            }
        }
        const data = await kybs.getMany();
        return data
    }

    async updateKyb(userId: number, id: number, data: any) {
        try {
            const user = await User.findOne({ where: { id: userId } });

            const kyb = await Kyb.findOne({ where: { id }, relations: ['storage', 'client', 'clientGroup'] });
            let storage: Storage;
            if (data?.storage) {
                if (kyb?.storage?.id) {
                    if (data.storage.name !== kyb?.storage?.name) {
                        if (kyb?.storage.storageSystem === StorageSystem.AMAZON) {
                            this.storageService.deleteAwsFile(kyb?.storage?.file);
                        } else if (kyb?.storage.storageSystem === StorageSystem.MICROSOFT) {
                            this.oneDriveService.deleteOneDriveFile(userId, kyb?.storage?.fileId);
                        } else if (kyb?.storage.storageSystem === StorageSystem.GOOGLE) {
                            this.googleDriveService.deleteGoogleDriveFile(userId, kyb?.storage?.fileId)
                        }
                        else if (kyb?.storage.storageSystem === StorageSystem.BHARATHCLOUD) {
                            this.bharathService.deleteB3File(userId, kyb?.storage?.file)
                        }

                    }
                    storage = await Storage.findOne({ where: { id: kyb.storage.id } });
                    storage.fileType = data.storage.fileType;
                    storage.fileSize = data.storage.fileSize;
                    storage.name = data.storage.name;
                    storage.file = data.storage.upload;
                    storage.fileId = data.storage.fileId;
                    storage.webUrl = data.storage.webUrl;
                    storage.downloadUrl = data.storage.downloadUrl;
                    storage.authId = user.organization.id;
                    storage.storageSystem = data.storage.storageSystem;
                    kyb.storage = storage;
                } else {
                    storage = await this.storageService.addAttachements(userId, data.storage);
                    kyb.storage = storage;
                }
            } else {
                if (kyb?.storage?.id) {
                    const existingStorage = await Storage.findOne({ where: { id: kyb?.storage?.id } });
                    await existingStorage.remove();
                    if (existingStorage) {
                        if (existingStorage.storageSystem === StorageSystem.AMAZON) {
                            this.storageService.deleteAwsFile(kyb.storage.file)
                        } else if (existingStorage.storageSystem === StorageSystem.MICROSOFT) {
                            this.oneDriveService.deleteOneDriveFile(userId, kyb.storage.fileId);
                        } else if (existingStorage.storageSystem === StorageSystem.GOOGLE) {
                            this.googleDriveService.deleteGoogleDriveFile(userId, kyb.storage.fileId);
                        }
                        else if (existingStorage.storageSystem === StorageSystem.BHARATHCLOUD) {
                            this.bharathService.deleteB3File(user.organization.id, kyb.storage.file)
                        }
                    }
                    kyb.storage = null;
                }
            }

            kyb.documentName = data.documentName;
            kyb.documentNumber = data.documentNumber;
            kyb.user = user;

            await kyb.save();

            let activity = new Activity();
            activity.action = Event_Actions.KYB_INFO_UPDATED;
            activity.actorId = user.id;
            activity.type = kyb?.client ? ActivityType.CLIENT : ActivityType.CLIENT_GROUP;
            activity.typeId = kyb?.client ? kyb.client.id : kyb?.clientGroup?.id;
            activity.remarks = `"${getTitle(kyb.documentName)}" KYB Info Updated by ${user.fullName}`;
            await activity.save();
            return kyb;
        } catch (err) {
            console.log(err);
            throw new InternalServerErrorException(err);
        }
    }

    async delete(id: number, userId) {
        const user = await User.findOne({ where: { id: userId } });
        const kyb = await Kyb.findOne({ where: { id }, relations: ['client', 'storage', 'clientGroup'] });
        if (kyb?.storage) {
            const { storageSystem, file, fileId } = kyb.storage;
            await kyb.remove(); // Remove the record once before handling the storage-specific logic

            switch (storageSystem) {
                case StorageSystem.MICROSOFT:
                    await this.oneDriveService.deleteOneDriveFile(userId, fileId);
                    break;
                case StorageSystem.AMAZON:
                    await this.storageService.deleteAwsFile(file);
                    break;
                case StorageSystem.BHARATHCLOUD:
                    await this.bharathService.deleteB3File(userId, file);
                    break;
                case StorageSystem.GOOGLE:
                    await this.googleDriveService.deleteGoogleDriveFile(userId, fileId);
                    break;
                default:
                    console.error('Unsupported storage system', storageSystem);
            }
        } else {
            await kyb.remove();
        }

        let activity = new Activity();
        activity.action = Event_Actions.KYB_INFO_DELETED;
        activity.actorId = user.id;
        activity.type = kyb?.client ? ActivityType.CLIENT : ActivityType.CLIENT_GROUP;
        activity.typeId = kyb?.client ? kyb.client.id : kyb.clientGroup.id;
        activity.remarks = `"${getTitle(kyb.documentName)}" KYB Info Deleted by ${user.fullName}`;
        await activity.save();

        return { success: true }
    }


}