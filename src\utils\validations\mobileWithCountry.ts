import parsePhoneNumberFromString from "libphonenumber-js";
import countries from "../countries";

function mobileWithCountry(phone: Number, country: any) {
    let countryCode = countries.find((c) => c.label === country);
    const phoneNumber = parsePhoneNumberFromString(`+${countryCode.phone}${phone}`, country);
    if (phoneNumber.isValid()) {
        return false;
    } else {
        return true;
    };
};

export default mobileWithCountry;
