import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  ParseIntPipe,
  Patch,
  Post,
  Put,
  Query,
  Req,
  UseGuards,
} from '@nestjs/common';
import { JwtAuthGuard } from 'src/modules/users/jwt/jwt-auth.guard';
import CreateServiceDto from './dto/create-service.dto';
import FindServicesDto from './dto/find-services.dto';
import ImportServicesDto from './dto/import-services.dto';
import { ServicesService } from './services.service';
import BulkUpdateDto from './dto/bulk-update.dto';
import BulkDeleteDto from './dto/bulk-delete-dto';
import { query } from 'express';

@Controller('services')
export class ServicesController {
  constructor(private service: ServicesService) {}

  @UseGuards(JwtAuthGuard)
  @Get()
  get(@Req() req: any, @Query() query: FindServicesDto) {
    const { userId } = req.user;
    return this.service.findAll(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Get('/dashboard')
  getServiceTasksDetails(@Req() req: any, @Query() query: any) {
    const { userId } = req.user;
    return this.service.getServiceTasksDetails(userId, query);
  }
  @UseGuards(JwtAuthGuard)
  @Post('/servicesdashboard-export')
  getServicesDashboardexport(@Req() req: any, @Body() body: FindServicesDto) {
    const { userId } = req.user;
    return this.service.getServicesDashboardexport(userId, body);
  }
  @UseGuards(JwtAuthGuard)
  @Post('/services-export')
  exportservicesReport(@Req() req: any, @Body() body: FindServicesDto) {
    const { userId } = req.user;
    return this.service.exportservicesReport(userId, body);
  }

  @UseGuards(JwtAuthGuard)
  @Get('/preferred-services')
  getPreferedServices(@Req() req: any, @Query() query: FindServicesDto) {
    const { userId } = req.user;
    return this.service.getPreferedServices(userId, query);
  }

  @Get('/default')
  getDefaultServices(@Query() query: FindServicesDto) {
    return this.service.findDefaultServices(query);
  }

  @UseGuards(JwtAuthGuard)
  @Get('/:id')
  getOne(@Req() req: any, @Param() param: any) {
    const { userId } = req.user;
    return this.service.findOne(userId, param.id);
  }

  @UseGuards(JwtAuthGuard)
  @Post()
  create(@Body() body: CreateServiceDto, @Req() req: any) {
    const { userId } = req.user;
    return this.service.create(userId, body);
  }

  @UseGuards(JwtAuthGuard)
  @Post('/import')
  importServices(@Req() req: any, @Body() body: ImportServicesDto) {
    const { userId } = req.user;
    return this.service.importServices(userId, body);
  }

  @UseGuards(JwtAuthGuard)
  @Put('/:id')
  update(@Req() req: any, @Body() body: CreateServiceDto, @Param('id', ParseIntPipe) id: number) {
    const { userId } = req.user;
    return this.service.update(userId, id, body);
  }

  @Delete('/:id')
  delete(@Param('id', ParseIntPipe) id: number) {
    return this.service.delete(id);
  }

  @UseGuards(JwtAuthGuard)
  @Post('/:id/clone')
  clonseService(@Req() req: any, @Param('id', ParseIntPipe) id: number, @Body() body: any) {
    const { userId } = req.user;
    return this.service.cloneService(userId, id, body);
  }

  @UseGuards(JwtAuthGuard)
  @Post('/update-admin-services')
  updateAdminService(@Req() req: any) {
    const { userId } = req.user;
    return this.service.updateAdminServices(userId);
  }

  @UseGuards(JwtAuthGuard)
  @Patch('/bulk-update')
  bulkUpdate(@Req() req: any, @Body() body: BulkUpdateDto) {
    const { userId } = req.user;
    return this.service.bulkUpdate(userId, body);
  }

  @UseGuards(JwtAuthGuard)
  @Post('/bulk-delete')
  bulkDelete(@Req() req: any, @Body() body: BulkDeleteDto) {
    const { userId } = req.user;
    return this.service.bulkDelete(userId, body.ids);
  }

  // @UseGuards(JwtAuthGuard)
  // @Post('/bulk-clone')
  // bulkClone(@Req() req: any,@Body() body: BulkDeleteDto) {
  //   const {userId} = req.user;
  //   return this.service.bulkClone(userId,body.ids);
  // }

  @UseGuards(JwtAuthGuard)
  @Post('/:id/preference')
  servicePreference(
    @Req() req: any,
    @Param('id', ParseIntPipe) id: number,
    @Body('action') action: string,
  ) {
    const { userId } = req.user;
    return this.service.servicePreference(userId, id, action);
  }

  @UseGuards(JwtAuthGuard)
  @Post('/milestone/:id')
  addMilestone(@Req() req: any, @Param('id', ParseIntPipe) id: number, @Body() body: any) {
    const { userId } = req.user;
    return this.service.addMilestone(id, userId, body);
  }

  @UseGuards(JwtAuthGuard)
  @Put('/milestone/:id')
  updateMilestone(@Req() req: any, @Param('id', ParseIntPipe) id: number, @Body() body: any) {
    const { userId } = req.user;
    return this.service.updateMilestone(id, userId, body);
  }

  @UseGuards(JwtAuthGuard)
  @Delete('/milestone/:id')
  deleteMilestone(@Req() req: any, @Param('id', ParseIntPipe) id: number) {
    const { userId } = req.user;
    return this.service.deleteMilestone(id, userId);
  }

  @UseGuards(JwtAuthGuard)
  @Post('favorite')
  addFavorite(@Req() req: any, @Body() body: any) {
    const { userId } = req.user;
    return this.service.addFavorite(userId, body);
  }

  @UseGuards(JwtAuthGuard)
  @Post('remove-favorite')
  removeFavorite(@Req() req: any, @Body() body: any) {
    const { userId } = req.user;
    return this.service.removeFavorite(userId, body);
  }
}
