import { MigrationInterface, QueryRunner } from 'typeorm';

export class AlterClientTable1661831090075 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            ALTER TABLE client
            DROP COLUMN deleted,
            DROP COLUMN active,
            ADD COLUMN status enum('ACTIVE', 'INACTIVE', 'DELETED') NOT NULL DEFAULT 'ACTIVE';
      `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}
