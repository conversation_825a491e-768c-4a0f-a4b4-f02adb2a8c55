import { BaseEntity, <PERSON>umn, <PERSON><PERSON>ty, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, OneToOne, PrimaryGeneratedColumn } from "typeorm";
import Storage from "../storage/storage.entity";

@Entity()
class Posters extends BaseEntity {

    @PrimaryGeneratedColumn()
    id: number;

    @Column()
    type: string;

    @OneToOne(() => Storage, (storage) => storage.poster, { cascade: true })
    @JoinColumn()
    storage: Storage;


}

export default Posters;