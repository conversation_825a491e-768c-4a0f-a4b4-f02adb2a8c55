import { Injectable, BadRequestException } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { S3 } from 'aws-sdk';
import CloudCredentials from './cloud-credentials.entity';

@Injectable()
export class BharathCloudService {
    async upload(orgId: number, buffer: Buffer, key: string, contentType = '') {
        try {
            const upload = await this.uploadB3(orgId, buffer, key, contentType);
            return upload;
        } catch (err) {
            throw new BadRequestException(err);
        }
    }

    async get(orgId: number, key: string) {
        try {
            const upload = await this.getFileFromS3(orgId, key);
            return upload;
        } catch (err) {
            throw new BadRequestException(err);
        }
    }

    async getFileFromS3(orgId: number, key: string) {
        const cloudCredentilas = await CloudCredentials.findOne({ where: { organizationId: orgId } });
        const { accessKey, secretKey, endPoint, bucketName } = cloudCredentilas;
        const s3 = this.getB3(accessKey, secretKey, endPoint);
        const params = {
            Bucket: bucketName,
            Key: key,
        };
        return new Promise((resolve, reject) => {
            s3.getObject(params, (err, data) => {
                if (err) {
                    console.error(err);
                    reject(err.message);
                }
                resolve(data);
            });
        });
    }

    async uploadB3(orgId: number, file: Buffer, key: string, contentType: string) {
        const cloudCredentilas = await CloudCredentials.findOne({ where: { organizationId: orgId } });
        const { accessKey, secretKey, endPoint, bucketName } = cloudCredentilas;
        const s3 = this.getB3(accessKey, secretKey, endPoint);
        const params = {
            Bucket: bucketName,
            Key: key,
            Body: file,
            ContentType: contentType,
        };
        // console.log({ params });
        return new Promise((resolve, reject) => {
            s3.upload(params, (err, data) => {
                if (err) {
                    console.error(err);
                    reject(err.message);
                };
                // console.log(data);
                resolve(data);
            });
        });
    }

    async deleteFile(orgId: number, key: string,) {
        const cloudCredentilas = await CloudCredentials.findOne({ where: { organizationId: orgId } });
        const { accessKey, secretKey, endPoint, bucketName } = cloudCredentilas;
        if (!key) return null;
        try {
            const s3 = this.getB3(accessKey, secretKey, endPoint);
            const params = {
                Bucket: bucketName,
                Key: key
            };
            return new Promise((resolve, reject) => {
                s3.deleteObject(params, (err, data) => {
                    if (err) {
                        console.error(err);
                        reject(err.message);
                    }
                    resolve(data);
                    return { success: true }
                })
            })
        } catch (err) {
            console.error(err);
            // throw new BadRequestException(err);
        }
    }

    async deleteFolder(orgId: number, folderKey: string) {
        if (!folderKey) return null;
        const cloudCredentilas = await CloudCredentials.findOne({ where: { organizationId: orgId } });
        const { accessKey, secretKey, endPoint } = cloudCredentilas;

        const s3 = this.getB3(accessKey, secretKey, endPoint);

        // Recursive helper function to delete objects
        const deleteAllObjectsInFolder = async (folderKey: string) => {
            try {
                // Step 1: List all objects within the folder
                const listedObjects = await s3
                    .listObjectsV2({
                        Bucket: process.env.BHARATH_BUCKET_NAME,
                        Prefix: folderKey,
                    })
                    .promise();

                if (listedObjects.Contents.length === 0) {
                    return;
                }

                // Step 2: Delete listed objects
                const deleteParams = {
                    Bucket: process.env.BHARATH_BUCKET_NAME,
                    Delete: { Objects: [] as any[] },  // Array to hold objects to delete
                };

                listedObjects.Contents.forEach(({ Key }) => {
                    deleteParams.Delete.Objects.push({ Key });
                });

                await s3.deleteObjects(deleteParams).promise();

                // If there are more objects, recursively delete the next batch
                if (listedObjects.IsTruncated) {
                    await deleteAllObjectsInFolder(folderKey);
                }

                return { success: true };
            } catch (err) {
                console.error('Error deleting folder:', err);
                throw new Error(err);
            }
        };

        // Start deletion process
        return deleteAllObjectsInFolder(folderKey);
    }


    getB3(accessKeyId: string, secretAccessKey: string, endpoint: string) {
        return new S3({
            accessKeyId,
            secretAccessKey,
            endpoint,
            s3ForcePathStyle: true,
        });
    }

    async copyS3Object(orgId: number, sourceKey: string, newKey: string) {
        try {
            const cloudCredentilas = await CloudCredentials.findOne({ where: { organizationId: orgId } });
            const { accessKey, secretKey, endPoint, bucketName } = cloudCredentilas;
            const s3 = this.getB3(accessKey, secretKey, endPoint);

            const copyParams = {
                Bucket: bucketName,
                CopySource: `${bucketName}/${sourceKey}`,
                Key: newKey
            };
            await new Promise((resolve, reject) => {
                s3.copyObject(copyParams, (err, data) => {
                    if (err) {
                        console.error(err);
                        reject(err.message);
                    }
                    resolve(data);
                });
            });
            return { success: true, newKey };
        } catch (err) {
            throw new BadRequestException(err);
        }
    };

    async listBuckets(orgId: number) {
        try {
            const cloudCredentilas = await CloudCredentials.findOne({ where: { organizationId: orgId } });
            const { accessKey, secretKey, endPoint, bucketName } = cloudCredentilas;
            const s3 = this.getB3(accessKey, secretKey, endPoint);

            return new Promise((resolve, reject) => {
                s3.listBuckets((err, data) => {
                    if (err) {
                        console.error(err);
                        reject(err.message);
                    }
                    resolve(data);
                    return { success: true }
                })
            })
        } catch (err) {
            console.error(err);
            // throw new BadRequestException(err);
        }

    };

    async createS3Folder(orgId: number, folderName: string): Promise<any> {
        const cloudCredentilas = await CloudCredentials.findOne({ where: { organizationId: orgId } });
        const { accessKey, secretKey, endPoint, bucketName } = cloudCredentilas;
        const s3 = this.getB3(accessKey, secretKey, endPoint);

        const params = {
            Bucket: bucketName,
            Key: `${folderName}/`,
            Body: '',
        };

        return new Promise((resolve, reject) => {
            s3.putObject(params, (err, data) => {
                if (err) {
                    console.error('Error creating folder:', err);
                    reject(err.message);
                } else {
                    // console.log(data);
                    resolve(data);
                }
            });
        });
    }

    async listObjects(orgId: number, folderKey: string = ''): Promise<any> {
        const cloudCredentials = await CloudCredentials.findOne({ where: { organizationId: orgId } });
        const { accessKey, secretKey, endPoint, bucketName } = cloudCredentials;
        const s3 = this.getB3(accessKey, secretKey, endPoint);

        const params = {
            Bucket: bucketName,
            Delimiter: '/', // Ensures that only immediate children are returned
            Prefix: folderKey // If empty, it retrieves root-level objects; if provided, it retrieves sub-objects
        };

        return new Promise((resolve, reject) => {
            s3.listObjectsV2(params, (err, data) => {
                if (err) {
                    console.error('Error listing objects:', err);
                    reject(err.message);
                } else {
                    resolve(data);
                }
            });
        });
    }

}