import parsePhoneNumberFromString from 'libphonenumber-js';
import countries from '../countries';

export function fullMobileNumberWithCountry(phone: any, country: any) {
  let countryCode = countries.find((c) => c.code === country);
  const phoneNumber = parsePhoneNumberFromString(`+${countryCode.phone}${phone}`, country);
  return phoneNumber?.number || '';
}

export const fullMobileWithCountry = (phone: any, country: any) => {
  let countryCode = countries.find((c) => c.code === country);
  const phoneNumber = parsePhoneNumberFromString(`+${countryCode?.phone}${phone}`, country);

  const mobileNumber = phoneNumber?.number
    ? `+${phoneNumber?.countryCallingCode} ${phoneNumber?.nationalNumber}`
    : '';

  return mobileNumber;
};
