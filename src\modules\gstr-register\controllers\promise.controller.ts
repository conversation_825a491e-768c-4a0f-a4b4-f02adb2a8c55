import { Body, Controller, Post, UseGuards, Request, Get } from '@nestjs/common';
import { PromiseService } from '../services/promise.service';
import { JwtAuthGuard } from 'src/modules/users/jwt/jwt-auth.guard';
import FindReturnsDto from '../dto/find-returns.dto';
import SyncClinetsDto from '../dto/sync-clients.dto';

@Controller('promise')
export class PromiseController {
  constructor(private service: PromiseService) {}

  @UseGuards(JwtAuthGuard)
  @Post()
  createPromiseClient(@Body() body: FindReturnsDto, @Request() req: any) {
    const { userId } = req.user;
    return this.service.getReturns(body, userId);
  }

  @UseGuards(JwtAuthGuard)
  @Post('/client-sync')
  syncSingleClientReturns(@Body() body: SyncClinetsDto) {
    return this.service.syncSingleClientReturns(body);
  }

  @UseGuards(JwtAuthGuard)
  @Post('/client')
  getSingleClientReturns(@Body() body: any, @Request() req: any) {
    const { userId } = req.user;
    return this.service.getSingleClientReturns(body, userId);
  }
}
