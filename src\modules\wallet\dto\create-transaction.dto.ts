import { <PERSON><PERSON><PERSON>, <PERSON>NotEmpty, IsOptional } from "class-validator";
import { TransactionType, Type } from "../entities/wallets-transactions.entity";


class CreateTransactionDto {
    @IsNotEmpty()
    amount: number;

    @IsOptional()
    description: string;

    @IsNotEmpty()
     @IsEnum(Type)
    type: Type;

    @IsNotEmpty()
    @IsEnum(TransactionType)
    transactionType: TransactionType;


};

export default CreateTransactionDto