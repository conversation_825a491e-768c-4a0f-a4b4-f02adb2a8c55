import { Mo<PERSON><PERSON> } from "@nestjs/common";
import { TypeOrmModule } from "@nestjs/typeorm";
import { WhatsappController } from "./whatsapp.controller";
import { WhatsappService } from "./whatsapp.service";
import ViderWhatsappSessions from "./entity/viderWhatsappSessions";
import WhatsappAudit from "./entity/whatsappAudit";
import WhatsappRequests from "./entity/whatsappRequests";
import WhatsappConversation from "./entity/whatsappConversation";
import CreateMetaTemplate from "./entity/createMetaTemplate";
import MetaTemplatesStatusWebhook from "./entity/metaTemplatesStatusWebhook";

@Module({
    imports: [
        TypeOrmModule.forFeature([
            ViderWhatsappSessions,
            WhatsappAudit,
            WhatsappRequests,
            WhatsappConversation,
            CreateMetaTemplate,
            MetaTemplatesStatusWebhook
        ])
    ],
    controllers: [WhatsappController],
    providers: [WhatsappService]
})

export class WhatsappModule { }
