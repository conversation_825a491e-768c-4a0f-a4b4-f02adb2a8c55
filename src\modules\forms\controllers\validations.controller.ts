import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Put,
  Request,
  UseGuards,
} from '@nestjs/common';
import { JwtAuthGuard } from 'src/modules/users/jwt/jwt-auth.guard';
import CreateValidationDto from '../dto/create-validation.dto';
import { ValidataionsService } from '../services/validations.service';

@Controller('form-validations')
export class ValidationsController {
  constructor(private service: ValidataionsService) {}

  @UseGuards(JwtAuthGuard)
  @Post()
  async createValidation(@Request() req, @Body() body: CreateValidationDto) {
    const { userId } = req.user;
    return this.service.create(userId, body);
  }

  @UseGuards(JwtAuthGuard)
  @Get()
  async getValidations(@Request() req) {
    const { userId } = req.user;
    return this.service.get(userId);
  }

  @Get('/default')
  async getDefaultValidations() {
    return this.service.getDefaultValidations();
  }

  @UseGuards(JwtAuthGuard)
  @Post('/import')
  async importValidations(@Request() req, @Body() body: any) {
    const { userId } = req.user;
    return this.service.importValidations(userId, body);
  }

  @UseGuards(JwtAuthGuard)
  @Put('/:id')
  async updateValidation(
    @Param('id') id: string,
    @Body() body: CreateValidationDto,
  ) {
    return this.service.update(id, body);
  }

  @UseGuards(JwtAuthGuard)
  @Delete('/:id')
  async deleteValidation(@Param('id') id: string) {
    return this.service.delete(id);
  }
}
