import { MigrationInterface, QueryRunner } from 'typeorm';

export class AlterInvoiceTable1658928200236 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
    ALTER TABLE invoice
        DROP COLUMN bank_name,
        DROP COLUMN bank_account_number,
        DROP COLUMN bank_branch,
        DROP COLUMN bank_ifsc_code,
        DROP COLUMN total_taxable_amount,
        DROP COLUMN tds_percent,
        DROP COLUMN tds_amount,
        DROP COLUMN other_charges,
        DROP COLUMN additional_charges,
        ADD total_charges int not null,
        ADD adjustment int not null,
        ADD place_of_supply varchar(255) not null,
        ADD status enum('DRAFT', 'APPROVAL_PENDING', 'APPROVED', 'CANCELLED', 'EMAIL_SENT', 'PARTIALLY_PAID', 'PAID') default 'DRAFT',
        ADD created_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
        ADD updated_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        ADD approval_hierarchy_id int null,
        ADD billing_entity_id int null,
        ADD billing_entity_address_id int null,
        ADD bank_details_id int null,
        ADD FOREIGN KEY (billing_entity_id) REFERENCES billing_entity(id) ON DELETE SET NULL,
        ADD FOREIGN KEY (billing_entity_address_id) REFERENCES invoice_address(id) ON DELETE SET NULL,
        ADD FOREIGN KEY (bank_details_id) REFERENCES invoice_bank_details(id) ON DELETE SET NULL
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}
