import {
  Connection,
  EntitySubscriberInterface,
  EventSubscriber,
  InsertEvent,
  UpdateEvent,
  getManager,
} from 'typeorm';
import Password from 'src/modules/clients/entity/password.entity';
import { getLoginUser } from 'src/utils/re-use';
import { User } from 'src/modules/users/entities/user.entity';
import Client from 'src/modules/clients/entity/client.entity';
import AutClientCredentials, { syncStatus } from 'src/modules/automation/entities/aut_client_credentials.entity';
import { BadRequestException } from '@nestjs/common';
import { set } from 'lodash';
import GstrCredentials from 'src/modules/gstr-automation/entity/gstrCredentials.entity';


let password: string;
let website: string
let websiteUrl: string
let loginId: string
@EventSubscriber()
export class ClientPasswordSubscriber implements EntitySubscriberInterface<Password> {
  constructor(private readonly connection: Connection) {
    connection.subscribers.push(this);
  }

  listenTo() {
    return Password;
  }


  async afterInsert(event: InsertEvent<Password>) {
    const password = (event.entity);
    if(password.client){
      const client = await Client.findOne({
        where: { id: password.client.id },
        relations: ['organization', 'createdBy']
      });
      if (password.website === 'Income Tax | e-Filing') {


        const existingClinet = await AutClientCredentials.findOne({
          where: {
            client: password.client.id
          }
        });
        const existingRecord = await AutClientCredentials.findOne({
          where: { organizationId: client?.organization?.id, panNumber: password?.loginId },
        });

        if (!existingClinet && !existingRecord) {
          const clientCredentials = new AutClientCredentials();
          clientCredentials.panNumber = password.loginId.trim();
          clientCredentials.password = password?.password.trim();
          clientCredentials.client = client;
          clientCredentials.organizationId = client?.organization?.id;
          clientCredentials.syncStatus = syncStatus.NOTSYNC;
          await clientCredentials.save();
        };

      } else if (password.website === 'GST | e-Filing') {
        //TODO: Add Credentilas to Ato  Pro Income Tax
        const credential = await GstrCredentials.findOne({
          where: { organizationId: client?.organization?.id, userName: password.loginId },
        });
        const existingClinet = await GstrCredentials.findOne({
          where: {
            client: password.client.id
          }
        });
        if (!credential && !existingClinet) {
          const gstrCredentials = new GstrCredentials();
          gstrCredentials.userName = password.loginId.trim();;
          gstrCredentials.password = password?.password.trim();
          gstrCredentials.userId = client.createdBy.id;
          gstrCredentials.clientId = client.id;
          gstrCredentials.organizationId = client?.organization?.id;
          gstrCredentials.save();
        }
      };
    };
  };
};
