import { User, UserStatus } from 'src/modules/users/entities/user.entity';
import TanIncomeTaxForms from '../entity/tan-income-tax-forms.entity';
import {
  Any,
  Brackets,
  createQueryBuilder,
  getConnection,
  getManager,
  getRepository,
  In,
} from 'typeorm';
import TanClientCredentials, { IncomeTaxStatus } from '../entity/tan-client-credentials.entity';
import Client from 'src/modules/clients/entity/client.entity';
import AutomationMachines from 'src/modules/automation/entities/automation_machines.entity';
import OrganizationPreferences from 'src/modules/organization-preferences/entity/organization-preferences.entity';
import { difference, forEach } from 'lodash';
import TanProfile from '../entity/tan-profile.entity';
import * as xlsx from 'xlsx';
import { Permissions } from 'src/modules/tasks/permission';

import {
  getAllotmentQuarter,
  getBehindQuarter,
  getCurrentQuarter,
  getFinancialQuarter,
  getFormValidQuarters,
  getQuarterFromMonth,
  getQuartersBetween,
  getQuartersBetweenFinancialYear,
  tanFormTypes,
} from '../tan-utils';
import { <PERSON>ron, CronExpression } from '@nestjs/schedule';
import Password from 'src/modules/clients/entity/password.entity';
import { BadRequestException } from '@nestjs/common';
import * as moment from 'moment';
import TanTempEproFya from '../entity/tan_temp_epro_fya.entity';
import TanTempEproFyi from '../entity/tan_temp_epro_fyi.entity';
import TanCommunicationInbox from '../entity/tan-communication-inbox.entity';


export class TanDashboardService {
  async getFormsUdinAnalytics(userId: number, assesmentYear: any) {
    const user = await User.findOne(userId, { relations: ['organization', 'role'] });

    let ViewAll = user.role.permissions.find(
      (permission) => permission.slug === Permissions.VIEW_ALL_CLIENT_MANAGERS,
    );
    let ViewAssigned = user.role.permissions.find(
      (permission) => permission.slug === Permissions.VIEW_ASSIGNED_CLIENT_MANAGERS,
    );

    const formsData = createQueryBuilder(TanIncomeTaxForms, 'tanForms')
      .leftJoin('tanForms.tanClientCredentials', 'tanClientCredentials')
      .leftJoin('tanClientCredentials.client', 'client')
      .leftJoin('client.clientManagers', 'clientManagers')
      .where('tanForms.organizationId = :organizationId', {
        organizationId: user?.organization?.id,
      })
      .andWhere('client.status != :status', { status: UserStatus.DELETED })
      .andWhere('tanClientCredentials.status != :disStatus', {
        disStatus: IncomeTaxStatus.DISABLE,
      });

    if (ViewAssigned && !ViewAll) {
      formsData.andWhere('clientManagers.id = :userId', { userId });
    }

    if (assesmentYear) {
      formsData.andWhere('tanForms.refYear = :finY AND tanForms.refYearType = :financialYearType', {
        finY: assesmentYear,
        financialYearType: 'FY',
      });
    }

    const result = await formsData.getMany();

    const analytics = {
      totalForms: result.length,
      originalForms: {
        totalOriginalForms: 0,
        udinCompleted: 0,
        udinPending: 0,
        udinNotApplicable: 0,
      },
      revisedFroms: {
        totalRevisedForms: 0,
        udinCompleted: 0,
        udinPending: 0,
        udinNotApplicable: 0,
      },
      notApplicableForms: {
        totalNotApplicableForms: 0,
        udinCompleted: 0,
        udinPending: 0,
        udinNotApplicable: 0,
      },
    };

    result.forEach((form) => {
      if (form?.filingTypeCd === 'Original' || form?.filingTypeCd === 'Regular') {
        analytics.originalForms.totalOriginalForms++;

        if (form?.isUdinApplicable) {
          if (form?.udinNum) {
            analytics.originalForms.udinCompleted++;
          } else {
            analytics.originalForms.udinPending++;
          }
        } else {
          analytics.originalForms.udinNotApplicable++;
        }
      } else if (form?.filingTypeCd === 'Revised' || form?.filingTypeCd === 'Correction') {
        analytics.revisedFroms.totalRevisedForms++;
        if (form?.isUdinApplicable) {
          if (form?.udinNum) {
            analytics.revisedFroms.udinCompleted++;
          } else {
            analytics.revisedFroms.udinPending++;
          }
        } else {
          analytics.revisedFroms.udinNotApplicable++;
        }
      } else {
        analytics.notApplicableForms.totalNotApplicableForms++;
        if (form?.isUdinApplicable) {
          if (form?.udinNum) {
            analytics.notApplicableForms.udinCompleted++;
          } else {
            analytics.notApplicableForms.udinPending++;
          }
        } else {
          analytics.notApplicableForms.udinNotApplicable++;
        }
      }
    });

    return analytics;
  }

  async incometaxClientCheck(userId, queryy) {
    const { search, offset, limit } = queryy;
    const user = await User.findOne({
      where: { id: userId },
      relations: ['organization', 'role'],
    });
    
    let ViewAll = user.role.permissions.find(
      (permission) => permission.slug === Permissions.VIEW_ALL_CLIENT_MANAGERS,
    );
    let ViewAssigned = user.role.permissions.find(
      (permission) => permission.slug === Permissions.VIEW_ASSIGNED_CLIENT_MANAGERS,
    );
    const entityManager = getRepository(TanClientCredentials);

    const query = await entityManager
      .createQueryBuilder('tanCredentials')
      .leftJoinAndSelect('tanCredentials.client', 'client')
      .leftJoin('client.clientManagers', 'clientManagers')
      .where('tanCredentials.organizationId = :id', { id: user.organization.id })
      .andWhere('client.status != :status', { status: UserStatus.DELETED })
      .andWhere('tanCredentials.status = :inStatus', { inStatus: IncomeTaxStatus.ENABLE })
      .andWhere('LOWER(tanCredentials.tanRemarks) LIKE :remarks', {
        remarks: 'Invalid Password, Please retry.',
      });
     
    if (search) {
      query.andWhere(
        new Brackets((qb) => {
          qb.where('tanCredentials.tan_number LIKE :search', {
            search: `%${search}%`,
          });
          qb.orWhere('client.displayName LIKE :namesearch', {
            namesearch: `%${search}%`,
          });
        }),
      );
    }

    if (ViewAssigned && !ViewAll) {
      query.andWhere('clientManagers.id = :userId', { userId });
    }

    if (offset) {
      query.skip(offset);
    }

    if (limit) {
      query.take(limit);
    }

    const tracesQuery = await entityManager
    .createQueryBuilder('tanCredentials')
    .leftJoinAndSelect('tanCredentials.client', 'client')
    .where('tanCredentials.organizationId = :id', { id: user.organization.id })
    .andWhere('client.status != :status', { status: UserStatus.DELETED })
    .andWhere('tanCredentials.status = :inStatus', { inStatus: IncomeTaxStatus.ENABLE })
    .andWhere('LOWER(tanCredentials.traceRemarks) LIKE :remarks', {
      remarks: 'Invalid details',
    });
   
    if (search) {
      tracesQuery.andWhere(
        new Brackets((qb) => {
          qb.where('tanCredentials.tan_number LIKE :search', {
            search: `%${search}%`,
          });
          qb.orWhere('client.displayName LIKE :namesearch', {
            namesearch: `%${search}%`,
          });
        }),
      );
    }

    if (offset) {
      tracesQuery.skip(offset);
    }

    if (limit) {
      tracesQuery.take(limit);
    }


    const [filteredRows, totalCount] = await query.getManyAndCount();
    const [filteredRowss,totalTracesCount] = await tracesQuery.getManyAndCount();

    // const totalClients = await AutClientCredentials.count({
    //   where: { organizationId: user.organization.id },
    // });

    const totalClients = await createQueryBuilder(TanClientCredentials, 'credentials')
      .leftJoin('credentials.client', 'client')
      .where('credentials.organizationId = :organizationId', {
        organizationId: user?.organization?.id,
      })
      .andWhere('client.status != :status', { status: UserStatus.DELETED })
      .andWhere('credentials.status = :inStatus', { inStatus: IncomeTaxStatus.ENABLE })
      .getCount();

      const totalClientsWithTraces = await createQueryBuilder(TanClientCredentials,'credentials')
      .leftJoin('credentials.client','client')
      .where('credentials.organizationId = :organizationId',{
        organizationId:user?.organization?.id,
      })
      .andWhere('client.status != :status',{status:UserStatus.DELETED})
      .andWhere('credentials.status = :inStatus', { inStatus: IncomeTaxStatus.ENABLE })
      .andWhere('credentials.traceUserId IS NOT NULL')
      .getCount();

    // const uniquePansCount = await Client.count({ where: { organization: user.organization } });

    const client = await createQueryBuilder(Client, 'client')
      .select('COUNT(DISTINCT client.tan_number)', 'count')
      .where('client.organization_id = :orgId', { orgId: user.organization?.id })
      .andWhere('client.tan_number IS NOT NULL')
      .getRawOne();

    const count = parseInt(client.count);
    const result = {
      filteredRows,
      totalClients,
      count: filteredRows.length,
      uniquePansCount: count,
      totalCount,
      totalClientsWithTraces,
      traceCount:filteredRowss.length,
      totalTracesCount,
      totalData:[...filteredRows,...filteredRowss]
    };
    return result;
  }

  async exportTanInvalid(userId: number, query: any) {
    let invalidRows = await this.incometaxClientCheck(userId, query);
    let rows = invalidRows?.filteredRows.map((invalidRow: any) => {
      return {
        'Category': invalidRow?.tanCredentials?.client?.category,
        'Client Name': invalidRow?.tanCredentials?.client?.displayName,
        'Client ID': invalidRow?.tanCredentials?.client?.clientId,
        'TAN': invalidRow?.tanCredentials?.tanNumber,
        'Remarks': invalidRow?.remarks,
      };
    });

    if (rows !== undefined && rows.length) {
      const worksheet = xlsx.utils.json_to_sheet(rows);
      const workbook = xlsx.utils.book_new();
      xlsx.utils.book_append_sheet(workbook, worksheet, 'Inc Tax Returns');
      let file = xlsx.write(workbook, { type: 'buffer' });
      return file;
    } else {
      throw new BadRequestException('No Data for Export');
    }
  }

  async getIncometaxConfigStatus(userId: any, query: Date) {
    let user = await User.findOne(userId, { relations: ['organization'] });
    const organizationPref: any = await OrganizationPreferences.findOne({
      where: { organization: user?.organization },
    });
    if (organizationPref) {
      const organizationLimit = organizationPref?.automationConfig?.tanLimit || 50;
      let clientCredentials = createQueryBuilder(TanClientCredentials, 'clientCredentials')
        .leftJoinAndSelect('clientCredentials.client', 'client')
        .where('clientCredentials.organizationId = :id', { id: user?.organization?.id })
        .andWhere('client.status != :status', { status: UserStatus.DELETED })
        .andWhere(
          new Brackets((qb) => {
            qb.where('clientCredentials.status IS NULL').orWhere(
              'clientCredentials.status = :enabledStatus',
              { enabledStatus: IncomeTaxStatus.ENABLE },
            );
          }),
        );
      let result = (await clientCredentials.getCount()) || 0;

      const abc = {
        totalLimit: organizationLimit,
        difference: organizationLimit - result,
        presentClients: result,
      };
      return abc;
    }
  }

  async getFormsAnalytics(userId: number, financialYear: any) {
    const user = await User.findOne(userId, { relations: ['organization', 'role'] });
    let ViewAll = user.role.permissions.find(
      (permission) => permission.slug === Permissions.VIEW_ALL_CLIENT_MANAGERS,
    );
    let ViewAssigned = user.role.permissions.find(
      (permission) => permission.slug === Permissions.VIEW_ASSIGNED_CLIENT_MANAGERS,
    );
    const tanClientsQUery = await createQueryBuilder(TanClientCredentials, 'tanClientCredentials')
      .leftJoinAndSelect(
        'tanClientCredentials.tanForms',
        'tanForms',
        'tanForms.formDesc IN (:...forms) AND tanForms.filingTypeCd = :filingTypeCd AND tanForms.refYear = :finYear',
        {
          forms: ['Form 24Q', 'Form 26Q', 'Form 27Q', 'Form 27EQ'],
          filingTypeCd: 'Regular',
          finYear: financialYear,
        },
      )
      .leftJoin('tanClientCredentials.client', 'client')
      .leftJoin('client.clientManagers', 'clientManagers')
      .leftJoinAndSelect('tanClientCredentials.tanProfile', 'tanProfile')
      .select([
        'tanClientCredentials.id',
        'tanForms.id',
        'tanForms.formDesc',
        'tanForms.financialQuarter',
        'tanForms.refYear',
        'tanProfile.dateOfAllotment',
        'client.id',
        'tanProfile.tanNumber',
      ])
      .where('tanClientCredentials.organizationId = :organizationId', {
        organizationId: user?.organization?.id,
      })
      .andWhere('client.status != :status', { status: UserStatus.DELETED })
      .andWhere('tanClientCredentials.status != :disStatus', {
        disStatus: IncomeTaxStatus.DISABLE,
      });
      if (ViewAssigned && !ViewAll) {
        tanClientsQUery.andWhere('clientManagers.id = :userId', { userId });
      }
  
      const tanClients = await tanClientsQUery.getMany();

    const forms = ['Form 24Q', 'Form 26Q', 'Form 27Q', 'Form 27EQ'];
    const quarters = ['Q1', 'Q2', 'Q3', 'Q4'];
    const analytics = {};

    forms.forEach((form) => {
      analytics[form] = { filed: 0, notFiled: 0, byQuarter: {} };
      quarters.forEach((quarter) => {
        analytics[form].byQuarter[quarter] = { filed: 0, notFiled: 0 };
      });
    });

    const financialYearStart = new Date(`${financialYear}-04-01`);
    const financialYearEnd = new Date(`${parseInt(financialYear) + 1}-03-31`);

    const today = new Date();
    const currentYearStart = new Date(`${today.getFullYear}-04-01`);
    const currentQuarter = `Q${Math.floor((today.getMonth() + 1) / 3)}`;
    const currentFinancialYear =
      today.getMonth() + 1 >= 4
        ? `${today.getFullYear()}-${today.getFullYear() + 1}`
        : `${today.getFullYear() - 1}-${today.getFullYear()}`;
    const currentQuarterIndex = quarters.indexOf(currentQuarter) + 1;

    tanClients.forEach((client) => {
      const dateOfAllotment = client.tanProfile[0]?.dateOfAllotment;
      if (!dateOfAllotment) return;

      const allotmentDate = new Date(dateOfAllotment);
      const allotmentYear = allotmentDate.getFullYear();
      //  const allotmentQuarter = Math.floor((allotmentDate.getMonth() + 1) / 3);
      const allotmentQuarter = parseInt(getQuarterFromMonth(allotmentDate.getMonth() + 1));

      let startQuarter = 1;
      let endQuarter = 4;

      if (allotmentDate < financialYearStart) {
        // console.log(`Financial year:${financialYear} is more than Allotment Date:${allotmentDate}`)
        startQuarter = 1;
        endQuarter = 4;
      } else if (allotmentDate > financialYearEnd) {
        // console.log(`Financial year:${financialYear} is less than Allotment Date:${allotmentDate} so we are skipping`)
        return;
      } else if (allotmentYear === today.getFullYear() && allotmentDate < currentYearStart) {
        // console.log(`Current Financial year:${today.getFullYear()} is same as  Allotment Date:${allotmentDate}`)
        startQuarter = allotmentQuarter;
        endQuarter = Math.min(4, currentQuarterIndex - 1);
      } else {
        // console.log(`Financial year:${financialYear} is same as  Allotment date:${allotmentDate}`)
        startQuarter = allotmentQuarter;
        endQuarter = 4;
      }

      quarters.slice(startQuarter - 1, endQuarter).forEach((quarter) => {
        if (client.tanForms.length === 0) {
          forms.forEach((form) => {
            analytics[form].notFiled++;
            analytics[form].byQuarter[quarter].notFiled++;
          });
        } else {
          forms.forEach((form) => {
            const formExists = client.tanForms.some(
              (tanForm) =>
                tanForm.formDesc === form &&
                tanForm.financialQuarter === quarter &&
                tanForm.refYear === financialYear,
            );

            if (formExists) {
              analytics[form].filed++;
              analytics[form].byQuarter[quarter].filed++;
            } else {
              analytics[form].notFiled++;
              analytics[form].byQuarter[quarter].notFiled++;
            }
          });
        }
      });
    });

    return analytics;
  }

  async getFormsNavigateAnalytics(userId: number, query: any) {
    if (!query?.financialYear) {
      return;
    }
    const { financialYear, financialQuarter, formCd } = query;
    const user = await User.findOne(userId, { relations: ['organization', 'role'] });
    let ViewAll = user.role.permissions.find(
      (permission) => permission.slug === Permissions.VIEW_ALL_CLIENT_MANAGERS,
    );
    let ViewAssigned = user.role.permissions.find(
      (permission) => permission.slug === Permissions.VIEW_ASSIGNED_CLIENT_MANAGERS,
    );

    const tanClients = await createQueryBuilder(TanClientCredentials, 'tanClientCredentials')
      .leftJoinAndSelect(
        'tanClientCredentials.tanForms',
        'tanForms',
        'tanForms.formDesc IN (:...forms) AND tanForms.filingTypeCd = :filingTypeCd AND tanForms.refYear = :financialYear',
        {
          forms: formCd ? [formCd] : ['Form 24Q', 'Form 26Q', 'Form 27Q', 'Form 27EQ'],
          filingTypeCd: 'Regular',
          financialYear: query?.financialYear,
        },
      )
      .leftJoin('tanClientCredentials.client', 'client')
      .leftJoin('client.clientManagers', 'clientManagers')
      .leftJoinAndSelect('tanClientCredentials.tanProfile', 'tanProfile')
      .select([
        'tanClientCredentials.id',
        'tanForms.id',
        'tanForms.formDesc',
        'tanForms.financialQuarter',
        'tanForms.refYear',
        'tanForms.ackDt',
        'tanForms.tempAckNo',
        'tanForms.filingTypeCd',
        'tanProfile.dateOfAllotment',
        'client.id',
        'client.displayName',
        'clientManagers.id',
        'tanProfile.tanNumber',
      ])
      .where('tanClientCredentials.organizationId = :organizationId', {
        organizationId: user?.organization?.id,
      })
      .andWhere('client.status != :status', { status: UserStatus.DELETED })
      .andWhere('tanClientCredentials.status != :disStatus', {
        disStatus: IncomeTaxStatus.DISABLE,
      });
    
      if (ViewAssigned && !ViewAll) {
        tanClients.andWhere('clientManagers.id = :userId', { userId });
      }

    const results = await tanClients.getMany();

    const forms = formCd ? [formCd] : ['Form 24Q', 'Form 26Q', 'Form 27Q', 'Form 27EQ'];

    const formsData = [];
    const today = new Date();
    const currentQuarterIndex = getFinancialQuarter(today);
    const currentYear = today.getFullYear();

    results.forEach((client) => {
      const dateOfAllotment = client.tanProfile[0]?.dateOfAllotment;
      if (!dateOfAllotment) return;

      const allotmentDate = new Date(dateOfAllotment);
      let startYear = allotmentDate.getFullYear();
      let startQuarterIndex = parseInt(getQuarterFromMonth(allotmentDate.getMonth() + 1));
      if (allotmentDate.getMonth() + 1 < 4) {
        startYear--;
        startQuarterIndex = 4;
      }

      let endQuarterIndex = currentQuarterIndex - 1;
      let endYear = query?.financialYear;
      if (endQuarterIndex === 0) {
        endQuarterIndex = 4;
        endYear -= 1;
      }
      const validQuarters = getQuartersBetweenFinancialYear(
        allotmentDate,
        parseInt(financialYear),
        currentYear,
        currentQuarterIndex,
      );

      validQuarters.forEach(({ year, quarter }) => {
        forms.forEach((form) => {
          const formExists = client.tanForms.some(
            (tanForm) =>
              tanForm.formDesc === form &&
              tanForm.refYear === year.toString() &&
              tanForm.financialQuarter === quarter &&
              tanForm.filingTypeCd === 'Regular',
          );
          if (formExists) {
            const filedForm = client.tanForms.find(
              (tanForm) =>
                tanForm.formDesc === form &&
                tanForm.refYear === year.toString() &&
                tanForm.financialQuarter === quarter,
            );
            const filedRecord = {
              tanClientId: client.id,
              tanNumber: client.tanProfile[0]?.tanNumber,
              clientName: client?.client?.displayName,
              dateOfAllotment: client.tanProfile[0]?.dateOfAllotment,
              formDesc: form,
              financialQuarter: quarter,
              refYear: year,
              ackDt: filedForm?.ackDt,
              tempAckNo: filedForm?.tempAckNo,
              filingStatus: 'Filed',
            };
            formsData.push(filedRecord);
          } else {
            const notFiledRecord = {
              tanClientId: client.id,
              formDesc: form,
              financialQuarter: quarter,
              refYear: year,
              tanNumber: client.tanProfile[0]?.tanNumber,
              clientName: client?.client?.displayName,
              dateOfAllotment: client.tanProfile[0]?.dateOfAllotment,
              ackDt: null,
              tempAckNo: null,
              filingStatus: 'Not Filed',
            };
            formsData.push(notFiledRecord);
          }
        });
      });
    });

    const filteredData = formsData.filter((record) => {
      const matchesFormCd = !query.formCd || record.formDesc.includes(query.formCd);
      const matchesFinancialQuarter =
        !query.financialQuarter || record.financialQuarter === query.financialQuarter;
      const matchesFinancialYear =
        !query.financialYear || record.refYear.toString() === query.financialYear;
      const matchesFilingStatus =
        !query.filingStatus || record.filingStatus.toString() === query.filingStatus;
      const matchesSearch =
        !query.search ||
        record.tanNumber?.includes(query.search) ||
        record.clientName?.toLowerCase().includes(query.search.toLowerCase());

      return (
        matchesFormCd &&
        matchesFinancialQuarter &&
        matchesFinancialYear &&
        matchesSearch &&
        matchesFilingStatus
      );
    });

    const limit = query.limit ? parseInt(query.limit, 10) : 10;
    const offset = query.offset ? parseInt(query.offset, 10) : 0;

    const paginatedData = filteredData.slice(offset, offset + limit);

    return {
      totalRecords: filteredData.length,
      data: paginatedData,
    };
  }

  async getFormsCorrectionAnalytics(userId: number, financialYear: any) {
    const user = await User.findOne(userId, { relations: ['organization', 'role'] });

    let ViewAll = user.role.permissions.find(
      (permission) => permission.slug === Permissions.VIEW_ALL_CLIENT_MANAGERS,
    );
    let ViewAssigned = user.role.permissions.find(
      (permission) => permission.slug === Permissions.VIEW_ASSIGNED_CLIENT_MANAGERS,
    );

    const correctionFormsQuery = await createQueryBuilder(TanIncomeTaxForms, 'tanForms')
      .leftJoinAndSelect('tanForms.tanClientCredentials', 'tanClientCredentials')
      .leftJoin('tanClientCredentials.client', 'client')
      .leftJoin('client.clientManagers', 'clientManagers')
      .select([
        'tanClientCredentials.id',
        'tanForms.id',
        'tanForms.formDesc',
        'tanForms.financialQuarter',
        'tanForms.refYear',
        'client.id',
        'clientManagers.id'
      ])
      .where('tanForms.organizationId = :organizationId', {
        organizationId: user?.organization?.id,
      })
      .andWhere('client.status != :status', { status: UserStatus.DELETED })
      .andWhere('tanClientCredentials.status != :disStatus', {
        disStatus: IncomeTaxStatus.DISABLE,
      })
      .andWhere(
        'tanForms.formDesc IN (:...forms) AND tanForms.filingTypeCd = :filingTypeCd AND tanForms.refYear = :finYear',
        {
          forms: ['Form 24Q', 'Form 26Q', 'Form 27Q', 'Form 27EQ'],
          filingTypeCd: 'Correction',
          finYear: financialYear,
        },
      );
      if (ViewAssigned && !ViewAll) {
        correctionFormsQuery.andWhere('clientManagers.id = :userId', { userId });
      }
      const correctionForms = await correctionFormsQuery.getMany();

    const forms = ['Form 24Q', 'Form 26Q', 'Form 27Q', 'Form 27EQ'];
    const quarters = ['Q1', 'Q2', 'Q3', 'Q4'];
    const analytics = {};

    forms.forEach((form) => {
      analytics[form] = { byQuarter: {} };
      quarters.forEach((quarter) => {
        analytics[form].byQuarter[quarter] = { filed: 0 };
      });
    });

    correctionForms.forEach((formData) => {
      const { formDesc, financialQuarter } = formData;
      if (forms.includes(formDesc) && quarters.includes(financialQuarter)) {
        analytics[formDesc].byQuarter[financialQuarter].filed += 1;
      }
    });

    return analytics;
  }

    async getExcelNoticeDates(
      organizationId: number,
      interval: '1week' | '15days' | '1month' | '1year' | 'today',
      dateColumn: 'notice_sent_date',
      query: any,
      ViewAll: any,
      ViewAssigned: any,
      userId: any
    ): Promise<number> {
      try {
        if (!organizationId) {
          throw new Error('Organization not found for the user.');
        }
  
        let intervalQuery = '';
        switch (interval) {
          case 'today':
            intervalQuery = 'INTERVAL 1 DAY';
            break;
          case '1week':1
            intervalQuery = 'INTERVAL 1 WEEK';
            break;
          case '15days':
            intervalQuery = 'INTERVAL 15 DAY';
            break;
          case '1month':
            intervalQuery = 'INTERVAL 1 MONTH';
            break;
          case '1year':
            intervalQuery = 'INTERVAL 1 YEAR';
            break;
          default:
            throw new Error('Invalid interval');
        }
  
        let sql = `
        SELECT COUNT(*) AS count 
        FROM tan_temp_epro_fya 
        LEFT JOIN  client ON tan_temp_epro_fya.client_id = client.id
        LEFT JOIN tan_client_credentials ON tan_client_credentials.client_id = client.id
        WHERE 
          STR_TO_DATE(${dateColumn}, '%d-%m-%Y') BETWEEN DATE_SUB(CURDATE(), ${intervalQuery}) AND CURDATE()
          AND tan_temp_epro_fya.organization_id = ${organizationId}
          AND (client.status != 'DELETED')
          AND (tan_client_credentials.status != 'DISABLE')
        `;
  
        if (query.assessmentYear && query.assessmentYear !== '') {
          sql += ` AND ay = '${query.assessmentYear}'`;
        }
        if (ViewAssigned && !ViewAll) {
          sql += `
          AND EXISTS (
            SELECT 1
            FROM client_client_managers_user cm
            WHERE cm.client_id = client.id
            AND cm.user_id = ${userId}
          )
        `;
        }
  
        const result = await getManager().query(sql);
        return parseInt(result[0]?.count, 10);
      } catch (error) {
        console.error(`Error fetching ${dateColumn} dates:`, error);
        throw error;
      }
    }

    async getExcelNoticeFyaResponseDueDates(
      organizationId: number,
      interval: '1week' | '15days' | '1month' | '1year' | 'today',
      dateColumn: 'date_of_compliance',
      query: any,
      ViewAll: any,
      ViewAssigned: any,
      userId: any
    ): Promise<number> {
      try {
        if (!organizationId) {
          throw new Error('Organization not found for the user.');
        }
  
        let intervalQuery = '';
        switch (interval) {
          case 'today':
            intervalQuery = 'INTERVAL 1 DAY';
            break;
          case '1week':
            intervalQuery = 'INTERVAL 1 WEEK';
            break;
          case '15days':
            intervalQuery = 'INTERVAL 15 DAY';
            break;
          case '1month':
            intervalQuery = 'INTERVAL 1 MONTH';
            break;
          case '1year':
            intervalQuery = 'INTERVAL 1 YEAR';
            break;
          default:
            throw new Error('Invalid interval');
        }
  
        let sql = `
        SELECT COUNT(*) AS count 
        FROM tan_temp_epro_fya 
        LEFT JOIN  client ON tan_temp_epro_fya.client_id = client.id
        LEFT JOIN tan_client_credentials ON tan_client_credentials.client_id = client.id
        WHERE 
          STR_TO_DATE(${dateColumn}, '%d-%m-%Y') BETWEEN  CURDATE() AND DATE_ADD(CURDATE(), ${intervalQuery})
          AND tan_temp_epro_fya.organization_id = ${organizationId}
          AND (client.status != 'DELETED')
          AND (tan_client_credentials.status != 'DISABLE')
        `;
  
        if (query.assessmentYear && query.assessmentYear !== '') {
          sql += ` AND ay = '${query.assessmentYear}'`;
        }
  
        if (ViewAssigned && !ViewAll) {
          sql += `
          AND EXISTS (
            SELECT 1
            FROM client_client_managers_user cm
            WHERE cm.client_id = client.id
            AND cm.user_id = ${userId}
          )
        `;
        }
  
        const result = await getManager().query(sql);
        return parseInt(result[0]?.count, 10);
      } catch (error) {
        console.error(`Error fetching ${dateColumn} dates:`, error);
        throw error;
      }
    }

    async getExcelNoticeFyiDates(
      organizationId: number,
      interval: '1week' | '15days' | '1month' | '1year' | 'today',
      dateColumn: 'notice_sent_date',
      query: any,
      ViewAll: any,
      ViewAssigned: any,
      userId: any
    ): Promise<number> {
      try {
        if (!organizationId) {
          throw new Error('Organization not found for the user.');
        }
  
        let intervalQuery = '';
        switch (interval) {
          case 'today':
            intervalQuery = 'INTERVAL 1 DAY';
            break;
          case '1week':
            intervalQuery = 'INTERVAL 1 WEEK';
            break;
          case '15days':
            intervalQuery = 'INTERVAL 15 DAY';
            break;
          case '1month':
            intervalQuery = 'INTERVAL 1 MONTH';
            break;
          case '1year':
            intervalQuery = 'INTERVAL 1 YEAR';
            break;
          default:
            throw new Error('Invalid interval');
        }
  
        let sql = `
        SELECT COUNT(*) AS count 
        FROM tan_temp_epro_fyi 
        LEFT JOIN  client ON tan_temp_epro_fyi.client_id = client.id
        LEFT JOIN tan_client_credentials ON tan_client_credentials.client_id = client.id
        WHERE 
          STR_TO_DATE(${dateColumn}, '%d-%m-%Y') BETWEEN  DATE_SUB(CURDATE(), ${intervalQuery}) AND CURDATE()
          AND tan_temp_epro_fyi.organization_id = ${organizationId}
          AND (client.status != 'DELETED')
          AND (tan_client_credentials.status != 'DISABLE')
        `;
  
        if (query.assessmentYear && query.assessmentYear !== '') {
          sql += ` AND ay = '${query.assessmentYear}'`;
        }
  
        if (ViewAssigned && !ViewAll) {
          sql += `
          AND EXISTS (
            SELECT 1
            FROM client_client_managers_user cm
            WHERE cm.client_id = client.id
            AND cm.user_id = ${userId}
          )
        `;
        }
  
        const result = await getManager().query(sql);
        return parseInt(result[0]?.count, 10);
      } catch (error) {
        console.error(`Error fetching ${dateColumn} dates:`, error);
        throw error;
      }
    }
  
    async getExcelNoticeFyiResponseDueDates(
      organizationId: number,
      interval: '1week' | '15days' | '1month' | '1year' | 'today',
      dateColumn: 'date_of_compliance',
      query: any,
      ViewAll: any,
      ViewAssigned: any,
      userId: any
    ): Promise<number> {
      try {
        if (!organizationId) {
          throw new Error('Organization not found for the user.');
        }
  
        let intervalQuery = '';
        switch (interval) {
          case 'today':
            intervalQuery = 'INTERVAL 1 DAY';
            break;
          case '1week':
            intervalQuery = 'INTERVAL 1 WEEK';
            break;
          case '15days':
            intervalQuery = 'INTERVAL 15 DAY';
            break;
          case '1month':
            intervalQuery = 'INTERVAL 1 MONTH';
            break;
          case '1year':
            intervalQuery = 'INTERVAL 1 YEAR';
            break;
          default:
            throw new Error('Invalid interval');
        }
  
        let sql = `
        SELECT COUNT(*) AS count 
        FROM tan_temp_epro_fyi 
        LEFT JOIN  client ON tan_temp_epro_fyi.client_id = client.id
        LEFT JOIN tan_client_credentials ON tan_client_credentials.client_id = client.id
        WHERE 
          STR_TO_DATE(${dateColumn}, '%d-%m-%Y') BETWEEN CURDATE() AND DATE_ADD(CURDATE(), ${intervalQuery}) 
          AND tan_temp_epro_fyi.organization_id = ${organizationId}
          AND (client.status != 'DELETED')
          AND (tan_client_credentials.status != 'DISABLE')
        `;
  
        if (query.assessmentYear && query.assessmentYear !== '') {
          sql += ` AND ay = '${query.assessmentYear}'`;
        }
  
        if (ViewAssigned && !ViewAll) {
          sql += `
          AND EXISTS (
            SELECT 1
            FROM client_client_managers_user cm
            WHERE cm.client_id = client.id
            AND cm.user_id = ${userId}
          )
        `;
        }
  
        const result = await getManager().query(sql);
        return parseInt(result[0]?.count, 10);
      } catch (error) {
        console.error(`Error fetching ${dateColumn} dates:`, error);
        throw error;
      }
    }

    async getExcelCombinedNoticesCount(userId: number, query: any) {
      const user = await User.findOne({ where: { id: userId }, relations: ['organization', 'role'] });
      let ViewAll = user.role.permissions.find(
        (permission) => permission.slug === Permissions.VIEW_ALL_CLIENT_MANAGERS,
      );
      let ViewAssigned = user.role.permissions.find(
        (permission) => permission.slug === Permissions.VIEW_ASSIGNED_CLIENT_MANAGERS,
      );
      const organizationId = user?.organization?.id;
  
      if (organizationId) {
        // FYA Notices
        const fyaTodayIssued = await this.getExcelNoticeDates(
          organizationId,
          'today',
          'notice_sent_date',
          query,
          ViewAll,
          ViewAssigned,
          userId
        );
        const fyaLast1WeekIssued = await this.getExcelNoticeDates(
          organizationId,
          '1week',
          'notice_sent_date',
          query,
          ViewAll,
          ViewAssigned,
          userId
        );
        const fyaLast15DaysIssued = await this.getExcelNoticeDates(
          organizationId,
          '15days',
          'notice_sent_date',
          query,
          ViewAll,
          ViewAssigned,
          userId
        );
        const fyaLast1MonthIssued = await this.getExcelNoticeDates(
          organizationId,
          '1month',
          'notice_sent_date',
          query,
          ViewAll,
          ViewAssigned,
          userId
        );
  
        const fyaTodayDue = await this.getExcelNoticeFyaResponseDueDates(
          organizationId,
          'today',
          'date_of_compliance',
          query,
          ViewAll,
          ViewAssigned,
          userId
        );
        const fyaLast1WeekDue = await this.getExcelNoticeFyaResponseDueDates(
          organizationId,
          '1week',
          'date_of_compliance',
          query,
          ViewAll,
          ViewAssigned,
          userId
        );
        const fyaLast15DaysDue = await this.getExcelNoticeFyaResponseDueDates(
          organizationId,
          '15days',
          'date_of_compliance',
          query,
          ViewAll,
          ViewAssigned,
          userId
        );
        const fyaLast1MonthDue = await this.getExcelNoticeFyaResponseDueDates(
          organizationId,
          '1month',
          'date_of_compliance',
          query,
          ViewAll,
          ViewAssigned,
          userId
        );
  
        // FYI Notices
        const fyiTodayIssued = await this.getExcelNoticeFyiDates(
          organizationId,
          'today',
          'notice_sent_date',
          query,
          ViewAll,
          ViewAssigned,
          userId
        );
        const fyiLast1WeekIssued = await this.getExcelNoticeFyiDates(
          organizationId,
          '1week',
          'notice_sent_date',
          query,
          ViewAll,
          ViewAssigned,
          userId
        );
        const fyiLast15DaysIssued = await this.getExcelNoticeFyiDates(
          organizationId,
          '15days',
          'notice_sent_date',
          query,
          ViewAll,
          ViewAssigned,
          userId
        );
        const fyiLast1MonthIssued = await this.getExcelNoticeFyiDates(
          organizationId,
          '1month',
          'notice_sent_date',
          query,
          ViewAll,
          ViewAssigned,
          userId
        );
  
        const fyiTodayDue = await this.getExcelNoticeFyiResponseDueDates(
          organizationId,
          'today',
          'date_of_compliance',
          query,
          ViewAll,
          ViewAssigned,
          userId
        );
        const fyiLast1WeekDue = await this.getExcelNoticeFyiResponseDueDates(
          organizationId,
          '1week',
          'date_of_compliance',
          query,
          ViewAll,
          ViewAssigned,
          userId
        );
        const fyiLast15DaysDue = await this.getExcelNoticeFyiResponseDueDates(
          organizationId,
          '15days',
          'date_of_compliance',
          query,
          ViewAll,
          ViewAssigned,
          userId
        );
        const fyiLast1MonthDue = await this.getExcelNoticeFyiResponseDueDates(
          organizationId,
          '1month',
          'date_of_compliance',
          query,
          ViewAll,
          ViewAssigned,
          userId
        );
  
        return {
          issueData: {
            last1WeekIssued: fyaLast1WeekIssued + fyiLast1WeekIssued,
            last15DaysIssued: fyaLast15DaysIssued + fyiLast15DaysIssued,
            last1MonthIssued: fyaLast1MonthIssued + fyiLast1MonthIssued,
            todayIssued: fyaTodayIssued + fyiTodayIssued
          },
          responseDueData: {
            last1WeekDue: fyaLast1WeekDue + fyiLast1WeekDue,
            last15DaysDue: fyaLast15DaysDue + fyiLast15DaysDue,
            last1MonthDue: fyaLast1MonthDue + fyiLast1MonthDue,
            todayDue : fyaTodayDue + fyiTodayDue
          },
        };
      } else {
        return {
          issueData: {
            last1WeekIssued: 0,
            last15DaysIssued: 0,
            last1MonthIssued: 0,
            todayIssued: 0
          },
          responseDueData: {
            last1WeekDue: 0,
            last15DaysDue: 0,
            last1MonthDue: 0,
            todayDue: 0
          },
        };
      }
    }

    async getTraceNoticeDates(
      organizationId: number,
      interval: '1week' | '15days' | '1month' | '1year' | 'today',
      dateColumn: 'date',
      query: any,
      ViewAll: any,
      ViewAssigned: any,
      userId: any
    ): Promise<number> {
      try {
        if (!organizationId) {
          throw new Error('Organization not found for the user.');
        }
  
        let intervalQuery = '';
        switch (interval) {
          case 'today':
            intervalQuery = 'INTERVAL 1 DAY';
            break;
          case '1week':
            intervalQuery = 'INTERVAL 1 WEEK';
            break;
          case '15days':
            intervalQuery = 'INTERVAL 15 DAY';
            break;
          case '1month':
            intervalQuery = 'INTERVAL 1 MONTH';
            break;
          case '1year':
            intervalQuery = 'INTERVAL 1 YEAR';
            break;
          default:
            throw new Error('Invalid interval');
        }
  
        let sql = `
        SELECT COUNT(*) AS count 
        FROM tan_communication_inbox 
        LEFT JOIN  client ON tan_communication_inbox.client_id = client.id
        LEFT JOIN tan_client_credentials ON tan_client_credentials.client_id = client.id
        WHERE 
          STR_TO_DATE(${dateColumn}, "%d-%b-%Y") BETWEEN DATE_SUB(CURDATE(), ${intervalQuery}) AND CURDATE()
          AND tan_communication_inbox.organization_id = ${organizationId}
          AND (client.status != 'DELETED')
          AND (tan_client_credentials.status != 'DISABLE')
        `;
  
        if (query.assessmentYear && query.assessmentYear !== '') {
          sql += ` AND ay = '${query.assessmentYear}'`;
        }
        if (ViewAssigned && !ViewAll) {
          sql += `
          AND EXISTS (
            SELECT 1
            FROM client_client_managers_user cm
            WHERE cm.client_id = client.id
            AND cm.user_id = ${userId}
          )
        `;
        }
  
        const result = await getManager().query(sql);
        return parseInt(result[0]?.count, 10);
      } catch (error) {
        console.error(`Error fetching ${dateColumn} dates:`, error);
        throw error;
      }
    }
  
    async tracesNotice(userId: number, query: any) {
      const user = await User.findOne({ where: { id: userId }, relations: ['organization', 'role'] });
      let ViewAll = user.role.permissions.find(
        (permission) => permission.slug === Permissions.VIEW_ALL_CLIENT_MANAGERS,
      );
      let ViewAssigned = user.role.permissions.find(
        (permission) => permission.slug === Permissions.VIEW_ASSIGNED_CLIENT_MANAGERS,
      );
      const organizationId = user?.organization?.id;
      const today = moment().format('YYYY-MM-DD');
      const previousSeventhDay = moment().subtract(6, 'days').format('YYYY-MM-DD');
  
      const previousFifteenththDay = moment().subtract(15, 'days').format('YYYY-MM-DD');
  
      const previousThirtythDay = moment().subtract(30, 'days').format('YYYY-MM-DD');
  
      if (organizationId) {

         const todayIssued = await this.getTraceNoticeDates(
          organizationId,
          'today',
          'date',
          query,
          ViewAll,
          ViewAssigned,
          userId
        );
        const Last1WeekIssued = await this.getTraceNoticeDates(
          organizationId,
          '1week',
          'date',
          query,
          ViewAll,
          ViewAssigned,
          userId
        );
        const Last15DaysIssued = await this.getTraceNoticeDates(
          organizationId,
          '15days',
          'date',
          query,
          ViewAll,
          ViewAssigned,
          userId
        );
        const Last1MonthIssued = await this.getTraceNoticeDates(
          organizationId,
          '1month',
          'date',
          query,
          ViewAll,
          ViewAssigned,
          userId
        );
  
        return {
          issueData: {
            last1WeekIssued: Last1WeekIssued,
            last15DaysIssued: Last15DaysIssued, 
            last1MonthIssued: Last1MonthIssued ,
            todayIssued: todayIssued
          },
        };
      } else {
        return {
          issueData: {
            last1WeekIssued: 0,
            last15DaysIssued: 0,
            last1MonthIssued: 0,
            todayIssued : 0
          },
        };
      }
    }

    async getExcelFyaEvents(userId, query: Date) {
      let user = await User.findOne(userId, { relations: ['organization'] });
      let fyaNotice = createQueryBuilder(TanTempEproFya, 'tanTempEproFya')
        .leftJoinAndSelect('tanTempEproFya.client', 'client')
        .leftJoinAndSelect('client.tanClientCredentials', 'tanClientCredentials')
        .where('tanTempEproFya.organizationId = :organizationId', {
          organizationId: user?.organization?.id,
        })
        .andWhere('client.status != :status', { status: UserStatus.DELETED })
        .andWhere('tanClientCredentials.status != :disStatus', {
          disStatus: IncomeTaxStatus.DISABLE,
        });
      if (query) {
        const startOfMonth = moment(`${query}`).startOf('month').format('YYYY-MM-DD');
        const endOfMonth = moment(query).endOf('month').format('YYYY-MM-DD');
        // fyaNotice.andWhere(`Date(fyaNotice.issuedOn) BETWEEN '${startOfMonth}' AND '${endOfMonth}'`);
        fyaNotice.andWhere(
          `STR_TO_DATE(tanTempEproFya.noticeSentDate, '%d-%m-%Y') BETWEEN '${startOfMonth}' AND '${endOfMonth}'`,
        );
      }
  
      let fyiNotice = createQueryBuilder(TanTempEproFyi, 'tanTempEproFyi')
        .leftJoinAndSelect('tanTempEproFyi.client', 'client')
        .leftJoinAndSelect('client.tanClientCredentials', 'tanClientCredentials')
        .where('tanTempEproFyi.organizationId = :organizationId', {
          organizationId: user?.organization?.id,
        })
        .andWhere('client.status != :status', { status: UserStatus.DELETED })
        .andWhere('tanClientCredentials.status != :disStatus', {
          disStatus: IncomeTaxStatus.DISABLE,
        });
      if (query) {
        const startOfMonth = moment(`${query}`).startOf('month').format('YYYY-MM-DD');
        const endOfMonth = moment(query).endOf('month').format('YYYY-MM-DD');
        // fyiNotice.andWhere(`Date(fyiNotice.issuedOn) BETWEEN '${startOfMonth}' AND '${endOfMonth}'`);
        fyiNotice.andWhere(
          `STR_TO_DATE(tanTempEproFyi.noticeSentDate, '%d-%m-%Y') BETWEEN '${startOfMonth}' AND '${endOfMonth}'`,
        );
      }
      const result = await fyaNotice.getMany();
      const result2 = await fyiNotice.getMany();
  
      const fyaNotices = result.map((notice) => ({ ...notice, type: 'FYA' }));
      const fyiNotices = result2.map((notice) => ({ ...notice, type: 'FYI' }));
      const result1and2 = [...fyaNotices, ...fyiNotices];
      return result1and2;
    }
  
    async getExcelResponseDueEvents(userId, query: Date) {
      let user = await User.findOne(userId, { relations: ['organization'] });
      let fyaNotice = createQueryBuilder(TanTempEproFya, 'tanTempEproFya')
        .leftJoinAndSelect('tanTempEproFya.client', 'client')
        .leftJoinAndSelect('client.tanClientCredentials', 'tanClientCredentials')
        .where('tanTempEproFya.organizationId = :organizationId', {
          organizationId: user?.organization?.id,
        })
        .andWhere('client.status != :status', { status: UserStatus.DELETED })
        .andWhere('tanClientCredentials.status != :disStatus', {
          disStatus: IncomeTaxStatus.DISABLE,
        });
  
      if (query) {
        const startOfMonth = moment(`${query}`).startOf('month').format('YYYY-MM-DD');
        const endOfMonth = moment(query).endOf('month').format('YYYY-MM-DD');
        fyaNotice.andWhere(
          // `Date(fyaNotice.responseDueDate) BETWEEN '${startOfMonth}' AND '${endOfMonth}'`,
          `STR_TO_DATE(tanTempEproFya.dateOfCompliance, '%d-%m-%Y') BETWEEN '${startOfMonth}' AND '${endOfMonth}'`,
        );
      }
  
      let fyiNotice = createQueryBuilder(TanTempEproFyi, 'tanTempEproFyi')
        .leftJoinAndSelect('tanTempEproFyi.client', 'client')
        .leftJoinAndSelect('client.tanClientCredentials', 'tanClientCredentials')
        .where('tanTempEproFyi.organizationId = :organizationId', {
          organizationId: user?.organization?.id,
        })
        .andWhere('client.status != :status', { status: UserStatus.DELETED })
        .andWhere('tanClientCredentials.status != :disStatus', {
          disStatus: IncomeTaxStatus.DISABLE,
        });
      if (query) {
        const startOfMonth = moment(`${query}`).startOf('month').format('YYYY-MM-DD');
        const endOfMonth = moment(query).endOf('month').format('YYYY-MM-DD');
        fyiNotice.andWhere(
          `STR_TO_DATE(tanTempEproFyi.dateOfCompliance, '%d-%m-%Y') BETWEEN '${startOfMonth}' AND '${endOfMonth}'`,
        );
        // .andWhere(`Date(fyiNotice.responseDueDate) BETWEEN '${startOfMonth}' AND '${endOfMonth}'`)
        // .andWhere('fyiNotice.remarkSubmittedOn IS NUll');
      }
      const result = await fyaNotice.getMany();
      const result2 = await fyiNotice.getMany();
      const fyaNotices = result.map((notice) => ({ ...notice, type: 'FYA' }));
      const fyiNotices = result2.map((notice) => ({ ...notice, type: 'FYI' }));
      const result1and2 = [...fyaNotices, ...fyiNotices];
  
      return result1and2;
    }

    async getTracesEvents(userId, query: Date) {
      let user = await User.findOne(userId, { relations: ['organization'] });
      let fyaNotice = createQueryBuilder(TanCommunicationInbox, 'tanCommunicationInbox')
        .leftJoinAndSelect('tanCommunicationInbox.client', 'client')
        .leftJoinAndSelect('client.tanClientCredentials', 'tanClientCredentials')
        .where('tanCommunicationInbox.organizationId = :organizationId', {
          organizationId: user?.organization?.id,
        })
        .andWhere('client.status != :status', { status: UserStatus.DELETED })
        .andWhere('tanClientCredentials.status != :disStatus', {
          disStatus: IncomeTaxStatus.DISABLE,
        });
      if (query) {
        const startOfMonth = moment(`${query}`).startOf('month').format('YYYY-MM-DD');
        const endOfMonth = moment(query).endOf('month').format('YYYY-MM-DD');
        // fyaNotice.andWhere(`Date(fyaNotice.issuedOn) BETWEEN '${startOfMonth}' AND '${endOfMonth}'`);
        fyaNotice.andWhere(
          `STR_TO_DATE(tanCommunicationInbox.date, "%d-%b-%Y") BETWEEN '${startOfMonth}' AND '${endOfMonth}'`,
        );
      }
  
     
      const result = await fyaNotice.getMany();
  
      const tracesNotices = result.map((notice) => ({ ...notice, type: 'TRACES' }));
      const result1and2 = [...tracesNotices];
      return result1and2;
    }
  async incometaxTanClientCheck(userId, queryy) {
    const { search, offset, limit } = queryy;
    const user = await User.findOne({
      where: { id: userId },
      relations: ['organization'],
    });
    const entityManager = getRepository(AutomationMachines);

    // Directly use a raw SQL query to get the desired result
    const query = await entityManager
      .createQueryBuilder('automationMachines')
      .leftJoinAndSelect('automationMachines.tanCredentials', 'tanCredentials')
      .leftJoinAndSelect('tanCredentials.client', 'client')
      .where('tanCredentials.organizationId = :id', { id: user.organization.id })
      .andWhere('automationMachines.type = :type',{type:'TAN'})
      .andWhere('client.status != :status', { status: UserStatus.DELETED })
      .andWhere('tanCredentials.status = :inStatus', { inStatus: IncomeTaxStatus.ENABLE })
      .andWhere('LOWER(automationMachines.remarks) LIKE :remarks', {
        remarks: 'Invalid Password, Please retry.',
      })
      .andWhere((qb) => {
        const subQuery = qb
          .subQuery()
          .select('MAX(innerAutomationMachines.id)', 'maxId')
          .from(AutomationMachines, 'innerAutomationMachines')
          .leftJoin('innerAutomationMachines.tanCredentials', 'innerAutoCredentials')
          .where('innerAutoCredentials.organizationId = :id', { id: user.organization.id })
          .andWhere("innerAutomationMachines.type = 'TAN'") 
          .groupBy('innerAutoCredentials.id')
          .getQuery();
        return 'automationMachines.id IN ' + subQuery;
      });
    if (search) {
      query.andWhere(
        new Brackets((qb) => {
          qb.where('tanCredentials.tan_number LIKE :search', {
            search: `%${search}%`,
          });
          qb.orWhere('client.displayName LIKE :namesearch', {
            namesearch: `%${search}%`,
          });
        }),
      );
    }

    if (offset) {
      query.skip(offset);
    }

    if (limit) {
      query.take(limit);
    }

    


    const [filteredRows, totalCount] = await query.getManyAndCount();

    // const totalClients = await AutClientCredentials.count({
    //   where: { organizationId: user.organization.id },
    // });

    const totalClients = await createQueryBuilder(TanClientCredentials, 'credentials')
      .leftJoin('credentials.client', 'client')
      .where('credentials.organizationId = :organizationId', {
        organizationId: user?.organization?.id,
      })
      .andWhere('client.status != :status', { status: UserStatus.DELETED })
      .andWhere('credentials.status = :inStatus', { inStatus: IncomeTaxStatus.ENABLE })
      .getCount();

    // const uniquePansCount = await Client.count({ where: { organization: user.organization } });

    const client = await createQueryBuilder(Client, 'client')
      .select('COUNT(DISTINCT client.tan_number)', 'count')
      .where('client.organization_id = :orgId', { orgId: user.organization?.id })
      .andWhere('client.tan_number IS NOT NULL')
      .getRawOne();

    const count = parseInt(client.count);
    const result = {
      filteredRows,
      totalClients,
      count: filteredRows.length,
      uniquePansCount: count,
      totalCount,
    };
    return result;
  }

  async tracesClientCheck(userId, queryy) {
    const { search, offset, limit } = queryy;
    const user = await User.findOne({
      where: { id: userId },
      relations: ['organization'],
    });
    const entityManager = getRepository(AutomationMachines);


    const tracesQuery = await entityManager
    .createQueryBuilder('automationMachines')
    .leftJoinAndSelect('automationMachines.tanCredentials', 'tanCredentials')
    .leftJoinAndSelect('tanCredentials.client', 'client')
    .where('tanCredentials.organizationId = :id', { id: user.organization.id })
    .andWhere('automationMachines.type = :type',{type:'TRACES'})
    .andWhere('client.status != :status', { status: UserStatus.DELETED })
    .andWhere('tanCredentials.status = :inStatus', { inStatus: IncomeTaxStatus.ENABLE })
    .andWhere('LOWER(automationMachines.remarks) LIKE :remarks', {
      remarks: 'Invalid details',
    })
    .andWhere((qb) => {
      const subQuery = qb
        .subQuery()
        .select('MAX(innerAutomationMachines.id)', 'maxId')
        .from(AutomationMachines, 'innerAutomationMachines')
        .leftJoin('innerAutomationMachines.tanCredentials', 'innerAutoCredentials')
        .where('innerAutoCredentials.organizationId = :id', { id: user.organization.id })
        .andWhere("innerAutomationMachines.type = 'TRACES'") 
        .groupBy('innerAutoCredentials.id')
        .getQuery();
      return 'automationMachines.id IN ' + subQuery;
    });
    if (search) {
      tracesQuery.andWhere(
        new Brackets((qb) => {
          qb.where('tanCredentials.tan_number LIKE :search', {
            search: `%${search}%`,
          });
          qb.orWhere('client.displayName LIKE :namesearch', {
            namesearch: `%${search}%`,
          });
        }),
      );
    }

    if (offset) {
      tracesQuery.skip(offset);
    }

    if (limit) {
      tracesQuery.take(limit);
    }


    const [filteredRows,totalCount] = await tracesQuery.getManyAndCount();

    // const totalClients = await AutClientCredentials.count({
    //   where: { organizationId: user.organization.id },
    // });


      const totalClientsWithTraces = await createQueryBuilder(TanClientCredentials,'credentials')
      .leftJoin('credentials.client','client')
      .where('credentials.organizationId = :organizationId',{
        organizationId:user?.organization?.id,
      })
      .andWhere('client.status != :status',{status:UserStatus.DELETED})
      .andWhere('credentials.status = :inStatus', { inStatus: IncomeTaxStatus.ENABLE })
      .andWhere('credentials.traceUserId IS NOT NULL')
      .getCount();

    // const uniquePansCount = await Client.count({ where: { organization: user.organization } });

    const client = await createQueryBuilder(Client, 'client')
      .select('COUNT(DISTINCT client.tan_number)', 'count')
      .where('client.organization_id = :orgId', { orgId: user.organization?.id })
      .andWhere('client.tan_number IS NOT NULL')
      .getRawOne();

    const count = parseInt(client.count);
    const result = {
      uniquePansCount: count,
      filteredRows,
      totalClientsWithTraces,
      totalCount,
    };
    return result;
  }
  
}
