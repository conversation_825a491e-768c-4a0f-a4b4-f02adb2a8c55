import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  ParseIntPipe,
  Post,
  Put,
  Query,
  Req,
  Request,
  UploadedFile,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { JwtAuthGuard } from 'src/modules/users/jwt/jwt-auth.guard';

import { DscRegisterService } from './dsc-register.service';
import { CreateDscRegisterDto, UpdateDscRegisterDto } from './dto/create-dsc-register.dto';
import { FindDscRegisterDto } from './dto/find-dsc-register.dto';
import IssueOrReceiveDto from './dto/issue-receive.dto';
import { FileInterceptor } from '@nestjs/platform-express';
import { DeleteDscClientsDto } from './dto/delete-dsc-clients.dto';
import { AddDscClientsDto } from './dto/add-dsc-clients.dto';

@Controller('dsc-register')
export class DscRegisterController {
  constructor(private service: DscRegisterService) { }

  @UseGuards(JwtAuthGuard)
  @Post()
  async create(@Body() body: CreateDscRegisterDto, @Request() req: any) {
    const { userId } = req.user;
    return this.service.create(userId, body);
  }

  @UseGuards(JwtAuthGuard)
  @Get()
  async get(@Request() req: any, @Query() query: FindDscRegisterDto) {
    const { userId } = req.user;
    return this.service.find(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Post('/client-dsc-register-export')
  async exportClientDscReport(@Req() req: any, @Body() body: any) {
    const query = body;
    const { userId } = req.user;
    const clientId = body.clientId;
    return this.service.exportClientDscReport(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Post('/clientgroup-dsc-register-export')
  async getClientgroupDscRegistersexport(@Req() req: any, @Body() body: any) {
    const query = body;
    const { userId } = req.user;
    const clientId = body.clientId;
    return this.service.getClientgroupDscRegistersexport(userId, query);
  }


  @UseGuards(JwtAuthGuard)
  @Get('/:id/details')
  async getOne(@Request() req: any, @Param('id', ParseIntPipe) id: number, @Query() query: any) {
    const { userId } = req.user;
    return this.service.getOne(id, userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Put('/:id')
  async update(@Param('id', ParseIntPipe) id: number, @Request() req: any, @Body() body: UpdateDscRegisterDto) {
    const { userId } = req.user;
    return this.service.update(id, body, userId);
  }

  @UseGuards(JwtAuthGuard)
  @Post('/clients-delete')
  async deleteClients(@Request() req: any, @Body() body: DeleteDscClientsDto) {
    const { userId } = req.user;
    return this.service.deleteClients(userId, body)
  };

  @UseGuards(JwtAuthGuard)
  @Post('/clients-add')
  async addClients(@Request() req: any, @Body() body: AddDscClientsDto) {
    const { userId } = req.user;
    return this.service.addClients(userId, body)
  }


  @UseGuards(JwtAuthGuard)
  @Delete('/:id')
  async delete(@Param('id', ParseIntPipe) id: number, @Request() req: any) {
    const { userId } = req.user;
    return this.service.delete(id, userId);
  }

  @UseGuards(JwtAuthGuard)
  @Post('/bulk-delete')
  async bulkDelete(@Request() req: any, @Body() body: any,) {
    const { userId } = req.user;
    return this.service.bulkDelete(userId, body);
  }

  @UseGuards(JwtAuthGuard)
  @Post('/:id/issue-receive')
  async issueOrRecive(
    @Req() req: any,
    @Param('id', ParseIntPipe) id: number,
    @Body() body: IssueOrReceiveDto,
  ) {
    const { userId } = req.user;
    return this.service.issueOrReceive(userId, id, body);
  }

  @UseGuards(JwtAuthGuard)
  @Get('/clients')
  async getAssigned(@Request() req: any) {
    const { userId } = req.user;
    return this.service.findClients(userId);
  }

  // @UseGuards(JwtAuthGuard)
  // @Post('/import')
  // @UseInterceptors(FileInterceptor('file'))
  // async importDscRegiter(@UploadedFile() file: Express.Multer.File, @Request() req: any) {
  //   const { userId } = req.user;
  //   return this.service.importDscRegisters(userId, file);
  // }
  @UseGuards(JwtAuthGuard)
  @Post('/import')
  // @UseInterceptors(FileInterceptor('file'))
  async importDscRegiter(@Body() body: any, @Request() req: any) {
    const { userId } = req.user;
    return this.service.importDscRegisters(userId, body);
  }

  @UseGuards(JwtAuthGuard)
  @Post('/apply-dsc')
  async applyDsc(@Body() body: any, @Request() req: any) {
    const { userId } = req.user;
    return this.service.applyDsc(userId, body);
  }

  @UseGuards(JwtAuthGuard)
  @Get('/get-applied-dsc')
  async getAppliedDsc(@Request() req: any) {
    const { userId } = req.user;
    return this.service.getAppliedDsc(userId);
  }

  @UseGuards(JwtAuthGuard)
  @Get('/get-applied-dsc-admin')
  async getAppliedDscAdmin(@Request() req: any) {
    const { userId } = req.user;
    return this.service.getAppliedDscAdmin(userId);
  }

  @Get('/get-applied-dsc/:id')
  async getOneAppliedDsc(@Request() req: any, @Param('id', ParseIntPipe) id: number,) {
    return this.service.getOneAppliedDsc(id);
  }

  @Put('/update-applyDsc/:id')
  async updateApplyDsc(@Param('id', ParseIntPipe) id: number, @Request() req: any, @Body() body) {
    return this.service.updateApplyDsc(id, body);

  }

}
