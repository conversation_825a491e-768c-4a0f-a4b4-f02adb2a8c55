import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddServiceStageOfWorkTable1657355603188
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `create table service_stageofwork (
            id int not null auto_increment,
            name varchar(255) not null,
            description varchar(255) null,
            reference_number boolean default false,
            reference_number_value varchar(255) null,
            type enum('STAGE_OF_WORK','DELIVERABLES') not null,
            extra_attributes json null,
            service_id int null,
            created_at datetime(6) default current_timestamp(6),
            updated_at datetime(6) default current_timestamp(6),
            primary key (id),
            CONSTRAINT FK_service_stageofwork_service_id FOREIGN KEY (service_id) REFERENCES service (id) ON DELETE SET NULL
        );`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}
