import {
  Connection,
  EntitySubscriberInterface,
  EventSubscriber,
  InsertEvent,
  UpdateEvent,
  getManager,
} from 'typeorm';
import CollectData from 'src/modules/collect-data/collect-data.entity';
// import UserId from 'src/modules/users/jwt/jwt.strategy';
import { getUserDetails, getUserNamewithUserId, insertINTOnotification } from 'src/utils/re-use';
import {
  sendClientWhatsAppTemplateMessage,
  sendWhatsAppTemplateMessage,
  sendWhatsAppTextMessage,
} from 'src/modules/whatsapp/whatsapp.service';
import * as moment from 'moment';
import Task from 'src/modules/tasks/entity/task.entity';
import { sendnewMail } from 'src/emails/newemails';
import OrganizationPreferences from 'src/modules/organization-preferences/entity/organization-preferences.entity';

let clientOldDetails: object = {};
@EventSubscriber()
export class CollectDataSubscriber implements EntitySubscriberInterface<CollectData> {
  constructor(private readonly connection: Connection) {
    connection.subscribers.push(this);
  }

  listenTo() {
    return CollectData;
  }
  // async afterInsert(event: InsertEvent<CollectData>) {
  //   const entityManager = getManager();
  //   const { user, task, client, whatsappCheck, uid, id, listOfFiles, createdAt, emailCheck } = event.entity;
  //   const userName = await getUserNamewithUserId(user.id);
  //   const fileslist: any = listOfFiles;
  //   const taskDetails = await Task.findOne({
  //     where: {
  //       id: task.id,
  //     },
  //     relations: ['members'],
  //   });
  //   // Assuming taskDetails is the object you provided
  //   const memberFullNames = [];
  //   if (taskDetails && taskDetails.members && taskDetails.members.length > 0) {
  //     for (const member of taskDetails.members) {
  //       memberFullNames.push(member.fullName);
  //     }
  //   }


  //   const getuserQuery = `SELECT full_name, organization_id FROM user where id=${id}`;
  //   const linkExpiryDate = moment(createdAt, 'DD-MM-YYYY').add(5, 'days').format('DD-MM-YYYY');
  //   const listofDocs = fileslist?.fileNames;
  //   //Mail Notification
  //   const key = 'CREATION_COLLECT_DATA_MAIL'
  //   if (emailCheck && key === 'CREATION_COLLECT_DATA_MAIL' && client) {

  //     const address = `${user.organization.buildingNo || " " ? user.organization.buildingNo || " " + ', ' : ''}${user.organization.floorNumber || " " ? user.organization.floorNumber || " " + ', ' : ''}${user.organization.buildingName || " " ? user.organization.buildingName + ', ' : ''}${user.organization.street ? user.organization.street + ', ' : ''}${user.organization.location ? user.organization.location + ', ' : ''}${user.organization.city ? user.organization.city + ', ' : ''}${user.organization.district ? user.organization.district + ', ' : ''}${user.organization.state ? user.organization.state + ', ' : ''}${user.organization.pincode ? user.organization.pincode : ''}`;

  //     const mailOptions = {
  //       id: user?.id,
  //       key: 'CREATION_COLLECT_DATA_MAIL',
  //       email: client?.email, // Include the email property
  //       clientMail: 'ORGANIZATION_CLIENT_EMAIL',
  //       data: {
  //         clientName: client?.displayName,
  //         TaskId: task?.taskNumber,
  //         TaskName: task?.name,
  //         AccessLink: `${process.env.WEBSITE_URL}/collect-data/${uid}`,
  //         TaskExpiryDate: linkExpiryDate,
  //         legalName: user?.organization?.tradeName || user?.organization?.legalName,
  //         adress: address,
  //         phoneNumber: user?.organization?.mobileNumber,
  //         mail: user?.organization?.email,
  //         website: user?.organization?.website,
  //         documents: listofDocs,
  //         userId: user.id
  //       },

  //       filePath: 'collect-data-created',
  //       subject: 'Secure Documents Upload Requested for Service',
  //     };

  //     const orgPreferences: any = await OrganizationPreferences.findOne({ where: { organization: user.organization.id } })
  //     const clientPreferences = orgPreferences?.clientPreferences?.email;
  //     if (clientPreferences && clientPreferences[key]) {
  //       // await sendnewMail(mailOptions);
  //     }
  //   }
  // }

  // async afterUpdate(event: UpdateEvent<CollectData>) {
  //   const entityManager = getManager();
  //   const { user, client, whatsappCheck, listOfFiles, uid, id, createdAt, emailCheck, name } = event.entity;
  //   const userName = await getUserNamewithUserId(user.id);
  //   const updatedName = event.entity.name;
  //   const filesList: any = listOfFiles;
  //   const collectData = await CollectData.findOne({
  //     where: {
  //       id: event.entity.id
  //     }, relations: ["task"]
  //   });

  //   const taskDetails = await Task.findOne({
  //     where: {
  //       id: collectData.task.id,
  //     },
  //     relations: ['members', 'organization'],
  //   });
  //   // Assuming taskDetails is the object you provided
  //   const memberFullNames = [];
  //   if (taskDetails && taskDetails.members && taskDetails.members?.length > 0) {
  //     for (const member of taskDetails?.members) {
  //       memberFullNames.push(member?.fullName);
  //     }
  //   }

  //   const linkExpiryDate = moment(createdAt, 'DD-MM-YYYY').add(5, 'days').format('DD-MM-YYYY');
  //   const listofDocs = filesList?.fileNames;
  //   //Mail Notification
  //   const key = 'UPDATION_COLLECT_DATA_MAIL'

  //   if (emailCheck && key === 'UPDATION_COLLECT_DATA_MAIL' && client) {
  //     const address = `${taskDetails?.organization?.buildingNo || " " ? taskDetails?.organization?.buildingNo || " " + ', ' : ''}${taskDetails?.organization?.floorNumber || " " ? taskDetails?.organization?.floorNumber || " " + ', ' : ''}${taskDetails?.organization?.buildingName || " " ? taskDetails?.organization?.buildingName + ', ' : ''}${taskDetails?.organization?.street ? taskDetails?.organization?.street + ', ' : ''}${taskDetails?.organization?.location ? taskDetails?.organization?.location + ', ' : ''}${taskDetails?.organization?.city ? taskDetails?.organization?.city + ', ' : ''}${taskDetails?.organization?.district ? taskDetails?.organization?.district + ', ' : ''}${taskDetails?.organization?.state ? taskDetails?.organization?.state + ', ' : ''}${taskDetails?.organization?.pincode ? taskDetails?.organization?.pincode : ''}`;
  //     let formattedFiles = '';
  //     for (let i = 0; i < listofDocs.length; i++) {
  //       formattedFiles += `${i + 1}. ${listofDocs[i]}`;
  //     }

  //     const mailOptions = {
  //       id: user.id,
  //       key: 'UPDATION_COLLECT_DATA_MAIL',
  //       email: client.email, // Include the email property
  //       clientMail: 'ORGANIZATION_CLIENT_EMAIL',
  //       data: {
  //         clientName: client.displayName,
  //         TaskId: collectData?.task?.taskNumber,
  //         TaskName: updatedName,
  //         AccessLink: `${process.env.WEBSITE_URL}/collect-data/${uid}`,
  //         TaskExpiryDate: linkExpiryDate,
  //         legalName: taskDetails?.organization?.tradeName || taskDetails?.organization?.legalName,
  //         adress: address,
  //         phoneNumber: taskDetails?.organization?.mobileNumber,
  //         mail: taskDetails?.organization?.email,
  //         website: taskDetails?.organization?.website,
  //         documents: listofDocs,
  //         userId: user.id,
  //       },

  //       filePath: 'collect-data-updated',
  //       subject: 'Updated Secure Documents Upload Link',
  //     };

  //     const orgPreferences: any = await OrganizationPreferences.findOne({ where: { organization: taskDetails?.organization?.id } })
  //     const clientPreferences = orgPreferences?.clientPreferences?.email;
  //     if (clientPreferences && clientPreferences[key]) {
  //       await sendnewMail(mailOptions);
  //     }
  //   }
  // }
}
