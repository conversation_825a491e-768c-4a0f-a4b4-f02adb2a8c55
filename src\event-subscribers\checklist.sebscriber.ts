import {
  Connection,
  EntitySubscriberInterface,
  EventSubscriber,
  InsertEvent,
  UpdateEvent,
  getManager,
} from 'typeorm';
import ChecklistItem from 'src/modules/tasks/entity/checklist-item.entity';
import {
  getClientIdBasedOnTaskId,
  getUserDetails,
  getUser<PERSON>s,
  getUserNamewithUserId,
  insertINTONotificationUpdate,
} from 'src/utils/re-use';
import { sendnewMail } from 'src/emails/newemails';
import Task from 'src/modules/tasks/entity/task.entity';
import Checklist from 'src/modules/tasks/entity/checklist.entity';
import { User } from 'src/modules/users/entities/user.entity';
import { Organization } from 'src/modules/organization/entities/organization.entity';

@EventSubscriber()
export class ChecklistSubscriber implements EntitySubscriberInterface<Checklist> {
  constructor(private readonly connection: Connection) {
    connection.subscribers.push(this);
  }

  listenTo() {
    return Checklist;
  }

  async afterInsert(event: InsertEvent<Checklist>) {
    const { name, task, id, checklistItems } = event.entity;
    if(task){
      const{id:taskId}=task;
      const userID = event.entity['userId'];
      let user = await User.findOne({
        where: {
          id: userID,
        },
      });
      if (name && task && id && checklistItems) {
        if (task?.id) {
          const taskMembersUserIds = await getUserIDs(task?.id);
          const getClient = await getClientIdBasedOnTaskId(task?.id);
          if (user?.['id'] && getClient && taskMembersUserIds) {
            const userName = await getUserNamewithUserId(user.id);
            if(getClient?.client){
            const { displayName: clientName,clientId } = getClient?.client;
            const { id: organizationId } = getClient?.organization;
            const { taskNumber, name: taskName } = task;
            const key = 'CHECKLIST_ADDED_PUSH';
            // lilender notification added an expenditure for client 10's "Form 26QB: TDS on Sale of Immovable Property"
            const title = 'Checklist Created';
            const body = `<strong>${userName}</strong> added an Checklist for <strong>${clientName}</strong>'s <strong>${taskName}<strong>`;
            insertINTONotificationUpdate(title, body, taskMembersUserIds, organizationId, key,taskId,clientId);
            const organization = await Organization.findOne({ id: organizationId });


    const addressParts = [
      organization.buildingNo || '',
      organization.floorNumber || '',
      organization.buildingName || '',
      organization.street || '',
      organization.location || '',
      organization.city || '',
      organization.district || '',
      organization.state || ''
    ].filter(part => part && part.trim() !== '');
    const pincode = organization.pincode && organization.pincode.trim() !== '' ? ` - ${organization.pincode}` : '';

    const address = addressParts.join(', ') + pincode;
            // mail notification
            for (let userId of taskMembersUserIds) {
              const userDetails = await getUserDetails(userId);
              await sendnewMail({
                id: userDetails?.id,
                key: 'CHECKLIST_ADDED_MAIL',
                email: userDetails?.email,
                data: {
                  taskUserName: userDetails?.full_name,
                  taskName: taskName,
                  clientName: clientName,
                  taskId: taskNumber,
                  checkList: name,
                  userId: user.id,
                },
                filePath: 'checklist-added',
                subject: `Task Update: new checklist added`,
              });
            }
            }
          }
        }
      }  
    }
  }
}
