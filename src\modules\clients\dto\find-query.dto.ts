import { IsNotEmpty, IsOptional } from 'class-validator';

class FindQueryDto {
  @IsOptional()
  category: string[];

  @IsOptional()
  sort: string;

  @IsOptional()
  labels: number[];

  @IsOptional()
  subCategory: string[];

  @IsOptional()
  monthAdded: string;

  @IsOptional()
  clientManagers: string;

  @IsOptional()
  offset: number;

  @IsOptional()
  limit: number;

  @IsOptional()
  search: string;

  @IsOptional()
  status: string[];

  @IsOptional()
  page: number;

  @IsOptional()
  dscId: number
  selectedIdList: number[];

}

export default FindQueryDto;
