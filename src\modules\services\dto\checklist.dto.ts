import { Type } from 'class-transformer';
import {
  IsArray,
  IsNotEmpty,
  IsOptional,
  ValidateNested,
} from 'class-validator';

class ChecklistItem {
  @IsNotEmpty()
  name: string;

  @IsOptional()
  description: string;
}

class ChecklistDto {
  @IsNotEmpty()
  name: string;

  @IsOptional()
  @Type(() => ChecklistItem)
  @IsArray()
  @ValidateNested()
  checklistItems: Array<ChecklistItem>;
}

export default ChecklistDto;
