import {
  AfterInsert,
  AfterLoad,
  AfterUpdate,
  BaseEntity,
  Column,
  Entity,
  JoinColumn,
  ManyToOne,
  OneToOne,
  PrimaryGeneratedColumn,
} from 'typeorm';
import { BillingEntity } from './billing-entity.entity';
import { Organization } from './organization.entity';
import Storage from 'src/modules/storage/storage.entity';


export enum AccountType {
  SAVINGS = 'savings',
  CURRENT = 'current',
  CASH_CREDIT = 'cash_credit',
  OVERDRAFT = 'overdraft'
}

@Entity()
export class BankAccount extends BaseEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  bankName: string;

  @Column()
  branchName: string;

  @Column()
  accountNumber: string;

  @Column()
  ifscCode: string;

  @Column({ nullable: true })
  upiId: string;

  @OneToOne(() => Storage, (storage) => storage.upiStorage, { cascade: true, onDelete: "CASCADE" })
  upiStorage: Storage;

  @Column({ nullable: true })
  upiAttachment: string;

  upiAttachmentUrl: string;

  @Column({ default: false })
  default: boolean;

  @Column({ nullable: true })
  accountName: string;

  @Column({ type: 'enum', enum: AccountType, })
  accountType: AccountType;

  @ManyToOne(() => Organization, (organization) => organization.bankAccounts, { nullable: true })
  organization: Organization;

  @ManyToOne(() => BillingEntity, (billingEntity) => billingEntity.bankAccounts, {
    nullable: true,
    onDelete: 'CASCADE',
  })
  billingEntity: BillingEntity;

  @AfterInsert()
  @AfterUpdate()
  @AfterLoad()
  renderUrl() {
    if (this.upiAttachment) {
      this.upiAttachmentUrl = `${process.env.AWS_BASE_URL}/${this.upiAttachment}`;
    }
  }
}
