import { BadRequestException, ConflictException, Injectable, InternalServerErrorException } from "@nestjs/common";
import { InvoiceStatus, ProformaInvoice, ProformaStatus } from "../entitities/proforma-invoice.entity";
import { CreateInvoiceDto, GENERATED_NUMBER_TYPE } from "../dto/create-invoice.dto";
import { User } from "src/modules/users/entities/user.entity";
import { BillingEntity } from "src/modules/organization/entities/billing-entity.entity";
import Client from "src/modules/clients/entity/client.entity";
import InvoiceAddress from "../entitities/invoice-address.entity";
import InvoiceParticular from "../entitities/invoice-particular.entity";
import InvoiceOtherParticular from "../entitities/invoice-other-particular.entity";
import Storage, { StorageSystem, StorageType } from "src/modules/storage/storage.entity";
import InvoiceBankDetails from "../entitities/invoice-bank-details.entity";
import { OneDriveStorageService } from "src/modules/ondrive-storage/onedrive-storage.service";
import { v4 as uuidv4 } from 'uuid';
import { AwsService } from "src/modules/storage/upload.service";
import { Brackets, createQueryBuilder, getManager, In, IsNull, Not } from "typeorm";
import Task from "src/modules/tasks/entity/task.entity";
import { PaymentStatusEnum, ProformaTaskStatus, TaskRecurringStatus } from "src/modules/tasks/dto/types";
import { ReceiptParticularStatus } from "../entitities/receipt-particular.entity";
import { GetUnbilledTasksDto } from "../dto/get-unbilled.dto";
import { EventEmitter2 } from "@nestjs/event-emitter";
import puppeteer from 'puppeteer';
import { Event_Actions } from "src/event-listeners/actions";
import { Invoice } from "../entitities/invoice.entity";
import * as xlsx from 'xlsx';
import Activity, { ActivityType } from "src/modules/activity/activity.entity";
import ClientGroup from "src/modules/client-group/client-group.entity";
import { FindInvoicesDto } from "../dto/find-invoices.dto";
import { dateFormation } from "src/utils/datesFormation";
import * as moment from "moment";
import { Permissions } from "src/modules/events/permission";
import { getTotalGst, TAX_TYPE_VALUE } from "../totalCalculations";
import * as ExcelJS from 'exceljs'
import { GoogleDriveStorageService } from "src/modules/ondrive-storage/googledrive-storage.service";
import { sanitizeFileNameForAWS } from "src/modules/storage/validations";

interface QueryConditions {
    id: number;
    [key: string]: number | string; // This allows additional properties with string keys and number or string values
}


@Injectable()
export class ProformaService {
    constructor(
        private oneDriveService: OneDriveStorageService,
        private googleDriveService: GoogleDriveStorageService,
        private awsService: AwsService,
        private eventEmitter: EventEmitter2,
    ) { }
    async create(userId: number, body: CreateInvoiceDto) {
        const proforma = new ProformaInvoice();
        let existingInvoice = await ProformaInvoice.findOne({
            where: {
                invoiceNumber: body.estimateNumber,
                billingEntity: body.billingEntity
            },
        });

        if (existingInvoice) {
            throw new ConflictException('Invoice number already exists');
        }
        proforma.invoiceNumber = body.estimateNumber;
        proforma.invoiceUser = body.invoiceUser;

        let user = await User.findOne({
            where: { id: userId },
            relations: ['organization'],
        });
        let billingEntity = await BillingEntity.findOne({
            where: { id: body.billingEntity },
        });

        let client;
        let clientGroup;
        if (body.clientType === "CLIENT_GROUP") {
            clientGroup = await ClientGroup.findOne({ where: { id: body.client } });
        }
        if (body.clientType !== "CLIENT_GROUP") {
            client = await Client.findOne({ where: { id: body.client } });
        }

        let billingEntityAddress = new InvoiceAddress();
        Object.assign(billingEntityAddress, body.billingEntityAddress);

        let billingAddress = new InvoiceAddress();
        Object.assign(billingAddress, body.billingAddress);

        let shippingAddress = new InvoiceAddress();
        // body.shippingAddress.state = 'Telangana';
        Object.assign(shippingAddress, body.shippingAddress);

        let taskIds = [];
        let particulars: InvoiceParticular[] = [];
        body.particulars.forEach((particular) => {
            let invoiceParticular = new InvoiceParticular();
            Object.assign(invoiceParticular, particular);
            delete invoiceParticular.id;
            !billingEntity.hasGst && delete invoiceParticular.gst;
            !billingEntity.hasGst && delete invoiceParticular.hsn;
            particulars.push(invoiceParticular);
            if (particular.taskId) {
                taskIds.push(particular.taskId);
            }
        });

        const invoiceTasks = await Task.find({
            where: {
                id: In(taskIds),
                proformaInvoiceId: Not(IsNull()),
            },
        });


        if (invoiceTasks.length) {
            throw new InternalServerErrorException(`Task Id ${invoiceTasks.map((task) => task.taskNumber).join(', ')} already Billed`);
        }
        let otherParticulars: InvoiceOtherParticular[] = [];
        body.otherParticulars.forEach((otherParticular) => {
            otherParticular['taskExpenseType'] = 'PURE_AGENT';
            const otherParticularClone = { ...otherParticular };
            delete otherParticularClone['id'];
            let invoiceOtherParticular = new InvoiceOtherParticular();
            Object.assign(invoiceOtherParticular, otherParticularClone);

            otherParticulars.push(invoiceOtherParticular);
        });
        let bankDetails
            = new InvoiceBankDetails();
        bankDetails.accountNumber = body?.bankDetails?.accountNumber;
        bankDetails.bankName = body?.bankDetails?.bankName;
        bankDetails.branchName = body?.bankDetails?.branchName;
        bankDetails.ifscCode = body?.bankDetails?.ifscCode;
        bankDetails.upiId = body?.bankDetails?.upiId;
        bankDetails.accountName = body?.bankDetails?.accountName;
        bankDetails.accountType = body?.bankDetails?.accountType;
        let storage: Storage;
        const existingStorage = await Storage.findOne({ where: { id: body.bankDetails?.upiAttachmentId } });
        if (existingStorage?.storageSystem === StorageSystem.MICROSOFT) {
            const newFile = await this.oneDriveService.copyFile(existingStorage?.fileId, userId, existingStorage?.name);
            storage = new Storage();
            storage.uid = uuidv4();
            storage.name = newFile?.name;
            storage.type = StorageType.FILE;
            storage.fileType = newFile?.file?.mimeType;
            storage.file = newFile?.['@microsoft.graph.downloadUrl'];
            storage.downloadUrl = newFile?.['@microsoft.graph.downloadUrl'];
            storage.fileId = newFile?.id;
            storage.fileSize = existingStorage?.fileSize;
            storage.show = false;
            storage.storageSystem = StorageSystem.MICROSOFT;
            storage.webUrl = newFile?.webUrl;
            storage.authId = user.organization.id;
            storage.organization = user.organization;
            await storage.save();
        } else if (existingStorage?.storageSystem === StorageSystem.GOOGLE) {
            const newFile = await this.googleDriveService.copyFile(existingStorage?.fileId, userId, existingStorage?.name);
            storage = new Storage();
            storage.uid = uuidv4();
            storage.name = newFile?.name;
            storage.type = StorageType.FILE;
            storage.fileType = newFile?.file?.mimeType;
            storage.file = newFile?.file;
            storage.fileId = newFile?.id;
            storage.fileSize = existingStorage?.fileSize;
            storage.show = false;
            storage.storageSystem = StorageSystem.GOOGLE;
            storage.webUrl = newFile?.webUrl;
            storage.authId = user.organization.id;
            storage.organization = user.organization;
            await storage.save();
        }
        else if (existingStorage?.storageSystem === StorageSystem.AMAZON) {
            const newFile = await this.awsService.copyS3Object(existingStorage?.file, `${existingStorage?.file}-${sanitizeFileNameForAWS(body.estimateNumber)}-${moment().valueOf()}`);
            storage = new Storage();
            storage.uid = uuidv4();
            storage.name = existingStorage?.name;
            storage.type = StorageType.FILE;
            storage.fileType = existingStorage?.fileType;
            storage.file = newFile.newKey;
            storage.fileSize = existingStorage?.fileSize;
            storage.show = false;
            storage.storageSystem = StorageSystem.AMAZON;
            storage.authId = user.organization.id;
            storage.organization = user.organization;
            await storage.save();

        };
        proforma.organization = user.organization;
        proforma.billingEntity = billingEntity;
        proforma.client = client;
        proforma.clientGroup = clientGroup;
        proforma.billingEntityAddress = billingEntityAddress;
        proforma.billingAddress = billingAddress;
        proforma.shippingAddress = shippingAddress;
        proforma.bankDetails = bankDetails;
        proforma.invoiceDate = body.invoiceDate;
        proforma.invoiceDueDate = body.invoiceDueDate;
        proforma.terms = body.terms;
        proforma.placeOfSupply = body.placeOfSupply;
        proforma.termsAndConditionsCopy = body.termsAndConditionsCopy;
        proforma.particulars = particulars;
        proforma.otherParticulars = otherParticulars;
        proforma.subTotal = body.subTotal;
        proforma.adjustment = body.adjustment;
        proforma.narration = body.narration;
        proforma.totalGstAmount = body.totalGstAmount;
        proforma.totalCharges = body.totalCharges;
        proforma.roundOff = body.roundOff;
        proforma.grandTotal = body.grandTotal;
        proforma.whatsappCheck = body.whatsappCheck;
        proforma.emailCheck = body.emailCheck;
        proforma.status = ProformaStatus.CREATED;
        proforma.divideTax = body.divideTax;
        proforma.hasTds = body.hasTds;
        if (body.hasTds) {
            proforma.tdsSection = body.tdsSection;
            proforma.tdsRate = body.tdsRate;
            proforma.tdsView = body.tdsView;
        };
        proforma['userId'] = user.id;
        if (body.divideTax) {
            proforma.supplyType = body.supplyType;
        }
        if (body?.signatureUser) {
            const signatureUser = await User.findOne(body?.signatureUser);
            proforma.signatureUser = signatureUser;
        } else {
            proforma.signatureUser = null;
        }
        const p = await proforma.save();
        if (storage) {
            storage.invoiceBankAttachement = p.bankDetails;
            await storage.save();
        };


        let activity = new Activity();
        activity.action = Event_Actions.PROFORMA_INVOICE_CREATED;
        activity.actorId = user.id;
        activity.type = client ? ActivityType.BILLING : ActivityType.BILLING_CLIENT_GROUP;
        activity.typeId = client ? client?.id : clientGroup?.id;
        activity.remarks = `Proforma Invoice "${proforma.invoiceNumber}" Created by ${user.fullName}`;
        await activity.save();

        if (particulars && particulars.length > 0) {
            const taskIds = particulars.map((particular) => particular.taskId);
            await createQueryBuilder(Task, 'task')
                .where('task.id IN (:...ids)', { ids: taskIds })
                .update({ proformaStatus: ProformaTaskStatus.GENEREATED, proformaInvoiceId: '' + proforma.id })
                .execute();
        }
        return proforma;
    };

    async get(userId: number, query: FindInvoicesDto) {
        let user = await User.findOne({
            where: { id: userId },
            relations: ['organization', 'role'],
        });
        const ViewAll = user.role.permissions.some(
            (permission) => permission.slug === Permissions.VIEW_ALL_CLIENT_MANAGERS
        );
        const ViewAssigned = user.role.permissions.some(
            (permission) => permission.slug === Permissions.VIEW_ASSIGNED_CLIENT_MANAGERS
        );

        let invoices = createQueryBuilder(ProformaInvoice, 'invoices')
            .select(['invoices.invoiceNumber',
                'invoices.billingEntity',
                'invoices.id',
                'invoices.status',
                'invoices.createdAt',
                'invoices.grandTotal',
                'invoices.subTotal',
                'invoices.invoiceDate',
                'invoices.invoiceDueDate',
                'invoices.divideTax',
                'invoices.narration',
                'invoices.placeOfSupply',
                'invoices.totalCharges',
                'billingEntity.tradeName',
                'billingEntity.hasGst',
                'billingEntity.locationOfSupply',
                'client.displayName',
                'client.gstNumber',
                'client.address',
                'organization.id',
                'clientGroup.displayName',
                'clientGroup.gstNumber',
                'clientGroup.address',
                // 'particulars.gst',
                // 'particulars.discount',
                // 'particulars.discountType',
                // 'particulars.rate',
                // 'particulars.units',
                // 'particulars.hsn',
                // 'particulars.amount',
            ])
            .leftJoinAndSelect('invoices.particulars', 'particulars')
            .leftJoinAndSelect('invoices.otherParticulars', 'otherParticulars')
            .leftJoin('invoices.billingEntity', 'billingEntity')
            .leftJoin('invoices.client', 'client')
            .leftJoin('invoices.clientGroup', 'clientGroup')
            .leftJoin('client.clientManagers', 'clientManagers')
            .leftJoin('clientGroup.clientGroupManagers', 'clientGroupManagers')
            .leftJoin('invoices.organization', 'organization')
            .where('organization.id = :orgId', { orgId: user.organization.id });

        if (query?.billingEntity?.length) {
            invoices.andWhere('billingEntity.id IN (:...billingEntity)', { billingEntity: query.billingEntity })
        }
        if (query.fromDate && query.toDate) {
            const { startTime, endTime } = dateFormation(query.fromDate, query.toDate);
            invoices.andWhere('invoices.invoiceDate BETWEEN :startDate AND :endDate', {
                startDate: startTime,
                endDate: endTime,
            });
        }

        if (!ViewAll && ViewAssigned) {
            invoices.andWhere(
                new Brackets(qb => {
                    qb.where('client.id IS NOT NULL AND clientManagers.id = :userId', { userId })
                        .orWhere('clientGroup.id IS NOT NULL AND clientGroupManagers.id = :userId', { userId })
                        .orWhere('client.id IS NULL AND clientGroup.id IS NULL');
                })
            );
        } else if (!ViewAll && !ViewAssigned) {
            invoices.andWhere('client.id IS NULL AND clientGroup.id IS NULL');
        }

        if (query.search) {
            invoices = invoices.andWhere(
                '(invoices.invoiceNumber LIKE :search OR client.displayName LIKE :search  OR clientGroup.displayName LIKE :search)',
                {
                    search: `%${query.search}%`,
                },
            );
        };
        if (query.status && query.status !== '') {
            invoices.andWhere(
                '(invoices.status = :status)',
                {
                    status: query.status,
                },
            );
        }
        const sort = (typeof query?.sort === "string") ? JSON.parse(query.sort) : query?.sort;
        if (sort?.column) {
            const columnMap: Record<string, string> = {
                invoiceDate: 'invoices.invoiceDate',
                invoiceDueDate: 'invoices.invoiceDueDate',
                grandTotal: 'invoices.grandTotal',
                tradeName: 'billingEntity.tradeName',
                displayName: 'client.displayName',
                status: 'invoices.status'
            };
            const column = columnMap[sort.column] || sort.column;
            invoices.orderBy(column, sort.direction.toUpperCase());
        } else {
            invoices.orderBy('invoices.createdAt', 'DESC');
        };

        if (query.offset) {
            invoices.skip(query.offset);
        }
        if (query.limit) {
            invoices.take(query.limit);
        }

        let data = await invoices.getManyAndCount();

        return {
            totalCount: data[1],
            result: data[0],
        };
    };

    async getProformaInvoice(estimateId: number, query: any) {
        let queryConditions: QueryConditions = {
            id: estimateId
        };
        if (query.orgId) {
            queryConditions.organization = query.orgId;
        }

        if (query.clientId) {
            queryConditions.client = query.clientId;
        }

        let invoice = await ProformaInvoice.findOne({
            where: queryConditions,
            relations: [
                'billingEntity',
                'billingEntityAddress',
                'billingAddress',
                'shippingAddress',
                'client',
                'clientGroup',
                'particulars',
                'otherParticulars',
                'bankDetails',
                'bankDetails.invoiceBankAttachement',
                'organization',
                'billingEntity.logStorage',
                'billingEntity.signatureStorage',
                'signatureUser',
                'signatureUser.profile',
                'signatureUser.profile.profileSign',
            ],
        });
        return invoice || "Un-Authorized";
    };

    async downloadProformaInvoicewithoutEmittor(proformaId: number) {
        let url = `${process.env.WEBSITE_URL}/billing/proforma/${proformaId}/preview?fromApi=true`;
        // let url = `http://localhost:3000/billing/invoices/${invoiceId}/preview?fromApi=true`;
        const options = {
            executablePath: '/usr/bin/chromium-browser',
            headless: true,
            args: ['--no-sandbox', '--disable-setuid-sandbox'],
        };
        try {
            const browser = await puppeteer.launch(options);
            const page = await browser.newPage();
            await page.setCacheEnabled(false);
            await page.setUserAgent(
                'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/78.0.3904.108 Safari/537.36',
            );

            const maxRetries = 3;
            let retries = 0;
            let loaded = false;

            while (retries < maxRetries && !loaded) {
                try {
                    await page.goto(url, { waitUntil: 'networkidle2', timeout: 60000 }); // 60 seconds timeout

                    // Check for a specific element that indicates the page has fully loaded
                    await page.waitForFunction(
                        'document.querySelector("body").innerText.includes("Powered by")',
                        { timeout: 60000 }, // Adjust timeout as needed
                    );
                    await page.waitForFunction(
                        'Array.from(document.images).every(img => img.complete && img.naturalHeight !== 0)',
                        { timeout: 60000 }, // Adjust timeout as needed
                    );

                    loaded = true;
                } catch (error) {
                    retries += 1;
                    console.log(`Retrying to load the page (${retries}/${maxRetries})`);
                    if (retries >= maxRetries) {
                        throw error;
                    }
                }
            }

            await page.addStyleTag({ content: '#freshworks-container { display: none; }' });
            await page.addStyleTag({ content: '.hide { display: none }' });
            await page.emulateMediaType('print');

            const pdf = await page.pdf({
                format: 'A4',
                printBackground: true,
                margin: {
                    top: '0px',
                    right: '0px',
                    bottom: '0px',
                    left: '0px',
                },
                scale: 0.6,
            });

            await browser.close();
            return Buffer.from(pdf);
        } catch (error) {
            console.error('Failed to download the invoice:', error);
            throw new InternalServerErrorException('Failed to download the invoice');
        }
    }
    async updateProformaInvoice(invoiceid: number, body: any, userId: any) {
        let user = await User.findOne({
            where: { id: userId },
            relations: ['organization'],
        });

        let invoice = await ProformaInvoice.findOne({
            where: { id: invoiceid },
            relations: [
                'billingEntity',
                'billingEntityAddress',
                'billingAddress',
                'shippingAddress',
                'client',
                'clientGroup',
                'particulars',
                'otherParticulars',
                'bankDetails',
                'bankDetails.invoiceBankAttachement'
            ],
        });

        let storage: Storage;
        // if (body?.bankDetails?.upiAttachmentId) {
        //     const attachement = await Storage.findOne({ where: { id: body?.bankDetails?.upiAttachmentId } });
        //     if (invoice?.bankDetails?.invoiceBankAttachement?.fileUrl) {
        //         if (invoice?.bankDetails?.invoiceBankAttachement?.storageSystem === StorageSystem.AMAZON) {
        //             await this.awsService.delete(invoice?.bankDetails?.invoiceBankAttachement?.file);
        //             storage = invoice.bankDetails.invoiceBankAttachement;
        //             if (invoice?.bankDetails?.invoiceBankAttachement && attachement) {
        //                 const newFile = await this.awsService.copyS3Object(attachement.file, `${attachement?.file}-${body.estimateNumber}`);
        //                 storage.name = attachement?.name;
        //                 storage.fileType = attachement?.fileType;
        //                 storage.file = newFile?.newKey;
        //                 storage.fileSize = attachement?.fileSize;
        //                 await storage.save();
        //             } else if (attachement) {
        //                 const newFile = await this.awsService.copyS3Object(attachement?.file, `${attachement?.file}-${body.estimateNumber}`);
        //                 storage = new Storage();
        //                 storage.uid = uuidv4();
        //                 storage.name = attachement?.name;
        //                 storage.type = StorageType.FILE;
        //                 storage.fileType = attachement?.fileType;
        //                 storage.file = newFile.newKey;
        //                 storage.fileSize = attachement?.fileSize;
        //                 storage.show = false;
        //                 storage.storageSystem = StorageSystem.AMAZON;
        //                 storage.authId = user.organization.id;
        //                 storage.organization = user.organization;
        //                 await storage.save();
        //             }
        //         } else if (invoice?.bankDetails?.invoiceBankAttachement?.storageSystem === StorageSystem.MICROSOFT) {
        //             await this.oneDriveService.deleteOneDriveFile(userId, invoice?.bankDetails?.invoiceBankAttachement?.fileId);
        //             storage = invoice.bankDetails.invoiceBankAttachement;
        //             if (invoice?.bankDetails?.invoiceBankAttachement && attachement) {
        //                 const newFile = await this.oneDriveService.copyFile(attachement?.fileId, userId);
        //                 storage.name = newFile?.name;
        //                 storage.fileType = newFile?.file?.mimeType;
        //                 storage.file = newFile?.['@microsoft.graph.downloadUrl'];
        //                 storage.downloadUrl = newFile?.['@microsoft.graph.downloadUrl'];
        //                 storage.fileId = newFile?.id;
        //                 storage.fileSize = attachement?.fileSize;
        //                 storage.webUrl = newFile?.webUrl;
        //                 await storage.save();

        //             } else if (attachement) {
        //                 const newFile = await this.oneDriveService.copyFile(attachement?.fileId, userId);
        //                 storage = new Storage();
        //                 storage.uid = uuidv4();
        //                 storage.name = attachement?.name;
        //                 storage.type = StorageType.FILE;
        //                 storage.fileType = attachement?.fileType;
        //                 storage.file = newFile?.['@microsoft.graph.downloadUrl'];
        //                 storage.downloadUrl = newFile?.['@microsoft.graph.downloadUrl'];
        //                 storage.fileId = newFile?.id;
        //                 storage.fileSize = attachement?.fileSize;
        //                 storage.show = false;
        //                 storage.storageSystem = StorageSystem.MICROSOFT;
        //                 storage.webUrl = newFile?.webUrl;
        //                 storage.authId = user.organization.id;
        //                 storage.organization = user.organization;
        //                 await storage.save();
        //             }
        //         }
        //     } else {
        //         storage = invoice.bankDetails.invoiceBankAttachement;
        //         if (attachement && user?.organization?.storageSystem == StorageSystem.AMAZON) {
        //             const newFile = await this.awsService.copyS3Object(attachement?.file, `${attachement?.file}-${body.estimateNumber}`);
        //             storage = new Storage();
        //             storage.uid = uuidv4();
        //             storage.name = attachement?.name;
        //             storage.type = StorageType.FILE;
        //             storage.fileType = attachement?.fileType;
        //             storage.file = newFile.newKey;
        //             storage.fileSize = attachement?.fileSize;
        //             storage.show = false;
        //             storage.storageSystem = StorageSystem.AMAZON;
        //             storage.authId = user.organization.id;
        //             storage.organization = user.organization;
        //             await storage.save();
        //         } else if (attachement && user?.organization?.storageSystem == StorageSystem.MICROSOFT) {
        //             const newFile = await this.oneDriveService.copyFile(attachement?.fileId, userId);
        //             storage = new Storage();
        //             storage.uid = uuidv4();
        //             storage.name = attachement?.name;
        //             storage.type = StorageType.FILE;
        //             storage.fileType = attachement?.fileType;
        //             storage.file = newFile?.['@microsoft.graph.downloadUrl'];
        //             storage.downloadUrl = newFile?.['@microsoft.graph.downloadUrl'];
        //             storage.fileId = newFile?.id;
        //             storage.fileSize = attachement?.fileSize;
        //             storage.show = false;
        //             storage.storageSystem = StorageSystem.MICROSOFT;
        //             storage.webUrl = newFile?.webUrl;
        //             storage.authId = user.organization.id;
        //             storage.organization = user.organization;
        //             await storage.save();
        //         }
        //     }
        // };
        if (body?.bankDetails?.upiAttachmentId) {
            const attachement = await Storage.findOne({ where: { id: body?.bankDetails?.upiAttachmentId } });

            if (invoice?.bankDetails?.invoiceBankAttachement?.fileUrl) {

                if (invoice?.bankDetails?.invoiceBankAttachement?.storageSystem === StorageSystem.AMAZON) {
                    await this.awsService.deleteFile(invoice?.bankDetails?.invoiceBankAttachement?.file);
                    storage = invoice.bankDetails.invoiceBankAttachement;

                    if (invoice?.bankDetails?.invoiceBankAttachement && attachement) {
                        const newFile = await this.awsService.copyS3Object(attachement.file, `${attachement?.file}-${sanitizeFileNameForAWS(body.estimateNumber)}-${moment().valueOf()}`);
                        storage.name = attachement?.name;
                        storage.fileType = attachement?.fileType;
                        storage.file = newFile?.newKey;
                        storage.fileSize = attachement?.fileSize;
                        await storage.save();
                    } else if (attachement) {
                        const newFile = await this.awsService.copyS3Object(attachement?.file, `${attachement?.file}-${sanitizeFileNameForAWS(body.estimateNumber)}-${moment().valueOf()}`);
                        storage = new Storage();
                        storage.uid = uuidv4();
                        storage.name = attachement?.name;
                        storage.type = StorageType.FILE;
                        storage.fileType = attachement?.fileType;
                        storage.file = newFile.newKey;
                        storage.fileSize = attachement?.fileSize;
                        storage.show = false;
                        storage.storageSystem = StorageSystem.AMAZON;
                        storage.authId = user.organization.id;
                        storage.organization = user.organization;
                        await storage.save();
                    }
                } else if (invoice?.bankDetails?.invoiceBankAttachement?.storageSystem === StorageSystem.MICROSOFT) {
                    await this.oneDriveService.deleteOneDriveFile(userId, invoice?.bankDetails?.invoiceBankAttachement?.fileId);
                    storage = invoice.bankDetails.invoiceBankAttachement;
                    if (invoice?.bankDetails?.invoiceBankAttachement && attachement) {
                        const newFile = await this.oneDriveService.copyFile(attachement?.fileId, userId, attachement?.name);
                        storage.name = newFile?.name;
                        storage.fileType = newFile?.file?.mimeType;
                        storage.file = newFile?.['@microsoft.graph.downloadUrl'];
                        storage.downloadUrl = newFile?.['@microsoft.graph.downloadUrl'];
                        storage.fileId = newFile?.id;
                        storage.fileSize = attachement?.fileSize;
                        storage.webUrl = newFile?.webUrl;
                        await storage.save();

                    } else if (attachement) {
                        const newFile = await this.oneDriveService.copyFile(attachement?.fileId, userId, attachement?.name);
                        storage = new Storage();
                        storage.uid = uuidv4();
                        storage.name = attachement?.name;
                        storage.type = StorageType.FILE;
                        storage.fileType = attachement?.fileType;
                        storage.file = newFile?.['@microsoft.graph.downloadUrl'];
                        storage.downloadUrl = newFile?.['@microsoft.graph.downloadUrl'];
                        storage.fileId = newFile?.id;
                        storage.fileSize = attachement?.fileSize;
                        storage.show = false;
                        storage.storageSystem = StorageSystem.MICROSOFT;
                        storage.webUrl = newFile?.webUrl;
                        storage.authId = user.organization.id;
                        storage.organization = user.organization;
                        await storage.save();
                    }
                } else if (invoice?.bankDetails?.invoiceBankAttachement?.storageSystem === StorageSystem.GOOGLE) {
                    await this.googleDriveService.deleteGoogleDriveFile(userId, invoice?.bankDetails?.invoiceBankAttachement?.fileId);
                    storage = invoice.bankDetails.invoiceBankAttachement;
                    if (invoice?.bankDetails?.invoiceBankAttachement && attachement) {
                        const newFile = await this.googleDriveService.copyFile(attachement?.fileId, userId, attachement?.name);
                        storage.name = newFile?.name;
                        storage.fileType = newFile?.file?.mimeType;
                        storage.file = newFile?.file;
                        storage.fileId = newFile?.id;
                        storage.fileSize = attachement?.fileSize;
                        storage.webUrl = newFile?.webUrl;
                        await storage.save();
                    }
                }
            } else {
                storage = invoice.bankDetails.invoiceBankAttachement;
                if (attachement && user?.organization?.storageSystem == StorageSystem.AMAZON) {
                    const newFile = await this.awsService.copyS3Object(attachement?.file, `${attachement?.file}-${sanitizeFileNameForAWS(body.estimateNumber)}-${moment().valueOf()}`);
                    storage = new Storage();
                    storage.uid = uuidv4();
                    storage.name = attachement?.name;
                    storage.type = StorageType.FILE;
                    storage.fileType = attachement?.fileType;
                    storage.file = newFile.newKey;
                    storage.fileSize = attachement?.fileSize;
                    storage.show = false;
                    storage.storageSystem = StorageSystem.AMAZON;
                    storage.authId = user.organization.id;
                    storage.organization = user.organization;
                    await storage.save();
                } else if (attachement && user?.organization?.storageSystem == StorageSystem.MICROSOFT) {
                    const newFile = await this.oneDriveService.copyFile(attachement?.fileId, userId, attachement?.name);
                    storage = new Storage();
                    storage.uid = uuidv4();
                    storage.name = attachement?.name;
                    storage.type = StorageType.FILE;
                    storage.fileType = attachement?.fileType;
                    storage.file = newFile?.file;
                    storage.fileId = newFile?.id;
                    storage.fileSize = attachement?.fileSize;
                    storage.show = false;
                    storage.storageSystem = StorageSystem.MICROSOFT;
                    storage.webUrl = newFile?.webUrl;
                    storage.authId = user.organization.id;
                    storage.organization = user.organization;
                    await storage.save();

                } else if (attachement && user?.organization?.storageSystem == StorageSystem.GOOGLE) {
                    const newFile = await this.googleDriveService.copyFile(attachement?.fileId, userId, attachement?.name);
                    storage = new Storage();
                    storage.uid = uuidv4();
                    storage.name = attachement?.name;
                    storage.type = StorageType.FILE;
                    storage.fileType = attachement?.fileType;
                    storage.file = newFile?.file;
                    storage.fileId = newFile?.id;
                    storage.fileSize = attachement?.fileSize;
                    storage.show = false;
                    storage.storageSystem = StorageSystem.GOOGLE;
                    storage.webUrl = newFile?.webUrl;
                    storage.authId = user.organization.id;
                    storage.organization = user.organization;
                    await storage.save();
                }
            }
        };

        if (!body.particulars.length) {
            throw new BadRequestException(`Service Description Should Not Be Empty`);
        }
        if (!body.estimateNumber) {
            throw new BadRequestException('Invoice Number Should Not Empty');
        }
        let existingInvoiceNumber = await ProformaInvoice.find({
            where: {
                billingEntity: invoice?.billingEntity?.id,
                invoiceNumber: body?.estimateNumber,
                id: Not(invoiceid),
            },
        });
        if (existingInvoiceNumber?.length) {
            throw new BadRequestException(`Invoice Number Already Exists`);
        }

        const getPastTasks = invoice.particulars.map((item) => {
            if (item['taskId']) {
                return item['taskId'];
            }
        });
        const getNewTasks = body.particulars.map((item) => {
            if (item['taskId']) {
                return item['taskId'];
            }
        });
        const filteredTasksId = getPastTasks.filter((item) => !getNewTasks.includes(item));
        for (let item of filteredTasksId) {
            if (item !== undefined) {
                let task = await Task.findOne({ where: { id: item } });
                task.proformaStatus = ProformaTaskStatus.NOT_GENERATED;
                task.proformaInvoiceId = null;
                task['userId'] = user.id;
                task.save();
            }
        };

        let billingEntity = await BillingEntity.findOne({
            where: { id: body.billingEntity },
        });

        let client = await Client.findOne({ where: { id: body.client } });

        let billingEntityAddress = new InvoiceAddress();
        Object.assign(billingEntityAddress, body.billingEntityAddress);

        let billingAddress = new InvoiceAddress();
        Object.assign(billingAddress, body.billingAddress);

        let shippingAddress = new InvoiceAddress();
        const bodyShippingAddress = body?.shippingAddress?.state
            ? body.shippingAddress.state
            : 'Telangana';
        Object.assign(shippingAddress, bodyShippingAddress);

        let taskIds = [];
        let particulars: InvoiceParticular[] = [];
        body.particulars.forEach((particular) => {
            let invoiceParticular = new InvoiceParticular();
            Object.assign(invoiceParticular, particular);
            if (!billingEntity.hasGst) {
                invoiceParticular.gst = null;
                invoiceParticular.hsn = ''; // Correctly remove the `hsn` property
            }
            particulars.push(invoiceParticular);
            if (particular.taskId) {
                taskIds.push(particular.taskId);
            }
        });

        let otherParticulars: InvoiceOtherParticular[] = [];
        body.otherParticulars.forEach((otherParticular: any) => {
            otherParticular['taskExpenseType'] = 'PURE_AGENT';
            let invoiceOtherParticular = new InvoiceOtherParticular();
            Object.assign(invoiceOtherParticular, otherParticular);
            otherParticulars.push(invoiceOtherParticular);
        });
        let bankDetails = new InvoiceBankDetails();
        Object.assign(bankDetails, body.bankDetails);
        // invoice.organization = user.organization;
        invoice.billingEntity = billingEntity;
        invoice.client = client;
        invoice.billingEntityAddress = billingEntityAddress;
        // invoice.billingAddress = billingAddress;
        // invoice.shippingAddress = shippingAddress;
        invoice.bankDetails = bankDetails;
        invoice.invoiceDate = body.invoiceDate;
        invoice.invoiceDueDate = body.invoiceDueDate;
        invoice.terms = body.terms;
        invoice.placeOfSupply = body.placeOfSupply;
        // invoice.termsAndConditions = body.termsAndConditions;
        invoice.termsAndConditionsCopy = body.termsAndConditionsCopy;
        invoice.particulars = particulars;
        invoice.otherParticulars = otherParticulars;
        invoice.subTotal = body.subTotal;
        invoice.adjustment = body.adjustment;
        invoice.narration = body.narration;
        invoice.totalGstAmount = body.totalGstAmount;
        invoice.totalCharges = body.totalCharges;
        invoice.roundOff = body.roundOff;
        invoice.grandTotal = body.grandTotal;
        invoice.invoiceNumber = body.estimateNumber;
        invoice.divideTax = body.divideTax;
        invoice.hasTds = body.hasTds;
        if (body.hasTds) {
            invoice.tdsSection = body.tdsSection;
            invoice.tdsRate = body.tdsRate;
            invoice.tdsView = body.tdsView;
        } else {
            invoice.tdsSection = null;
            invoice.tdsRate = null;
            invoice.tdsView = false;
        }
        if (body?.signatureUser) {
            const signatureUser = await User.findOne(body?.signatureUser);
            invoice.signatureUser = signatureUser;
        } else {
            invoice.signatureUser = null;
        }
        invoice['userId'] = user.id;
        if (invoice?.status === ProformaStatus.CLOSED) {
            if (getNewTasks?.length) {
                const isClosed = await Task.find({ where: { id: In(getNewTasks), paymentStatus: PaymentStatusEnum.BILLED, } });
                if (!isClosed?.length) {
                    invoice.status = ProformaStatus.CREATED;
                }
            } else {
                invoice.status = ProformaStatus.CREATED;
            }
        }
        const i = await invoice.save();

        if (storage) {
            storage.invoiceBankAttachement = i.bankDetails;
            await storage.save();

        }
        let activity = new Activity();
        activity.action = Event_Actions.PROFORMA_INVOICE_UPDATED;
        activity.actorId = user.id;
        activity.type = client ? ActivityType.BILLING : ActivityType.BILLING_CLIENT_GROUP;
        activity.typeId = client ? client?.id : invoice?.clientGroup?.id;
        activity.remarks = `Proforma Invoice "${invoice.invoiceNumber}" Updated by ${user.fullName}`;
        await activity.save();

        if (particulars && particulars.length > 0) {
            const taskIds = particulars.map((particular) => particular.taskId);
            await createQueryBuilder(Task, 'task')
                .where('task.id IN (:...ids)', { ids: taskIds })
                .update({ proformaStatus: ProformaTaskStatus.GENEREATED, proformaInvoiceId: '' + invoice.id })
                .execute();
        };

        return invoice;
    };

    async getProformaTasks(query: GetUnbilledTasksDto) {
        let tasks = createQueryBuilder(Task, 'task')
            .leftJoinAndSelect('task.client', 'client')
            .leftJoinAndSelect('task.clientGroup', 'clientGroup')
            .leftJoinAndSelect('task.category', 'category')
            .leftJoinAndSelect('task.members', 'members')
            .leftJoinAndSelect('members.imageStorage', 'imageStorage')
            .leftJoinAndSelect('task.expenditure', 'expenditure');

        if (query.client) {
            tasks.where('client.id = :clientId', { clientId: query.client })
        }

        if (query.clientGroup) {
            tasks.where('clientGroup.id = :clientGroupId', { clientGroupId: query.clientGroup })
        }

        tasks.andWhere('task.status NOT IN (:status)', { status: ['deleted', 'terminated'] })
            .andWhere('task.parentTask IS NULL')
            .andWhere('(task.recurringStatus is null or task.recurringStatus = :recurringStatus)', {
                recurringStatus: TaskRecurringStatus.CREATED,
            })
            .andWhere('task.proformaStatus != :profomaStatus', { profomaStatus: 'GENERATED' })
            .andWhere('task.paymentStatus !=:paymentStatus', { paymentStatus: 'BILLED' })
            .andWhere('task.billable IS TRUE');

        if (query.search) {
            tasks = tasks.andWhere('task.name LIKE :search', {
                search: `%${query.search}%`,
            });
        }
        const sort = (typeof query?.sort === "string") ? JSON.parse(query.sort) : query?.sort;
        if (sort?.column) {
            const columnMap: Record<string, string> = {
                feeAmount: 'task.feeAmount',
                taskNumber: 'task.taskNumber',
                name: 'category.name',
                status: 'task.status',
                additionalexpenditure: 'task.feeAmount',



            };
            const column = columnMap[sort.column] || sort.column;

            tasks.orderBy(column, sort.direction.toUpperCase());
        } else {
            tasks.orderBy('task.id', "ASC");
        }



        if (query.offset >= 0) {
            tasks.skip(query.offset);
        }

        if (query.limit) {
            tasks.take(query.limit);
        }

        let data = await tasks.getManyAndCount();

        return {
            totalCount: data[1],
            result: data[0],
        };

    };

    async downloadProformaInvoice(invoiceId: number, body: any) {
        let url = `${process.env.WEBSITE_URL}/billing/proforma/${invoiceId}/preview?fromApi=true`;
        // let url = `http://localhost:3000/billing/invoices/${invoiceId}/preview?fromApi=true`;
        const options = {
            executablePath: '/usr/bin/chromium-browser',
            headless: true,
            args: [
                '--no-sandbox',
                '--disable-setuid-sandbox',
            ],
        };
        try {
            const browser = await puppeteer.launch(options);
            const page = await browser.newPage();
            await page.setCacheEnabled(false);
            await page.setUserAgent(
                'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/78.0.3904.108 Safari/537.36',
            );

            const maxRetries = 3;
            let retries = 0;
            let loaded = false;

            while (retries < maxRetries && !loaded) {
                try {
                    await page.goto(url, { waitUntil: 'networkidle2', timeout: 60000 }); // 60 seconds timeout

                    // Check for a specific element that indicates the page has fully loaded
                    await page.waitForFunction(
                        'document.querySelector("body").innerText.includes("Powered by")',
                        { timeout: 60000 } // Adjust timeout as needed
                    );
                    await page.waitForFunction(
                        'Array.from(document.images).every(img => img.complete && img.naturalHeight !== 0)',
                        { timeout: 60000 } // Adjust timeout as needed
                    );

                    loaded = true;
                } catch (error) {
                    retries += 1;
                    console.log(`Retrying to load the page (${retries}/${maxRetries})`);
                    if (retries >= maxRetries) {
                        throw error;
                    }
                }
            }

            await page.addStyleTag({ content: '#freshworks-container { display: none; }' });
            await page.addStyleTag({ content: '.hide { display: none }' });

            // await page.addStyleTag({ content: '@media print { .myDiv { break-inside: avoid; } }' });

            // await page.addStyleTag({
            //   content: '@page { size: auto; }',
            // });

            // await page.emulateMediaType('screen');
            this.eventEmitter.emit(Event_Actions.PROFORMA_INVOICE_DOWNLOADED, { invoiceId, userId: body.userId });
            await page.emulateMediaType('print');

            const pdf = await page.pdf({
                format: 'A4',
                printBackground: true,
                margin: {
                    top: '0px',
                    right: '0px',
                    bottom: '0px',
                    left: '0px',
                },
                scale: 0.6,
            });

            await browser.close();
            return Buffer.from(pdf);;
        } catch (error) {
            console.error('Failed to download the invoice:', error);
            throw new InternalServerErrorException('Failed to download the invoice');
        }
    };

    async convert(userId: number, body: CreateInvoiceDto) {
        let invoice = new Invoice();
        let existingInvoice = await Invoice.findOne({
            where: {
                invoiceNumber: body.estimateNumber,
                billingEntity: body.billingEntity
            },
        });
        if (existingInvoice) {
            throw new InternalServerErrorException('Invoice number already exists');
        }
        invoice.invoiceNumber = body.estimateNumber;
        invoice.invoiceUser = body.invoiceUser;

        let user = await User.findOne({
            where: { id: userId },
            relations: ['organization'],
        });

        let billingEntity = await BillingEntity.findOne({
            where: { id: body.billingEntity },
        });

        let client = null;
        let clientGroup = null;
        if (body?.clientType !== "CLIENT_GROUP") {
            client = await Client.findOne({ where: { id: body.client } });
        }
        if (body?.clientType === "CLIENT_GROUP") {
            clientGroup = await ClientGroup.findOne({ where: { id: body.client } });
        }
        let billingEntityAddress = new InvoiceAddress();
        Object.assign(billingEntityAddress, body.billingEntityAddress);
        let billingAddress = new InvoiceAddress();
        const { id, ...billingAddressData } = body.billingAddress;
        Object.assign(billingAddress, billingAddressData);

        let shippingAddress = new InvoiceAddress();
        // body.shippingAddress.state = 'Telangana';
        Object.assign(shippingAddress, body.shippingAddress);

        let taskIds = [];
        let particulars: InvoiceParticular[] = [];
        body.particulars.forEach((particular: any) => {
            let invoiceParticular = new InvoiceParticular();
            Object.assign(invoiceParticular, particular);
            delete invoiceParticular.id;
            delete invoiceParticular.proformaInvoice;
            particulars.push(invoiceParticular);
            if (particular.taskId) {
                taskIds.push(particular.taskId);
            };
        });

        let otherParticulars: InvoiceOtherParticular[] = [];
        body.otherParticulars.forEach((otherParticular) => {
            otherParticular['taskExpenseType'] = 'PURE_AGENT';
            const otherParticularClone = { ...otherParticular };
            delete otherParticularClone['id'];
            delete otherParticularClone['proformaInvoice']
            let invoiceOtherParticular = new InvoiceOtherParticular();
            Object.assign(invoiceOtherParticular, otherParticularClone);
            otherParticulars.push(invoiceOtherParticular);
        });
        let bankDetails = new InvoiceBankDetails();
        bankDetails.accountNumber = body?.bankDetails?.accountNumber;
        bankDetails.bankName = body?.bankDetails?.bankName;
        bankDetails.branchName = body?.bankDetails?.branchName;
        bankDetails.ifscCode = body?.bankDetails?.ifscCode;
        bankDetails.upiId = body?.bankDetails?.upiId;
        let storage: Storage;
        const existingStorage = await Storage.findOne({ where: { id: body.bankDetails?.upiAttachmentId } });
        if (existingStorage?.storageSystem === StorageSystem.MICROSOFT) {
            const newFile = await this.oneDriveService.copyFile(existingStorage?.fileId, userId, existingStorage?.name);
            storage = new Storage();
            storage.uid = uuidv4();
            storage.name = newFile?.name;
            storage.type = StorageType.FILE;
            storage.fileType = newFile?.file?.mimeType;
            storage.file = newFile?.['@microsoft.graph.downloadUrl'];
            storage.downloadUrl = newFile?.['@microsoft.graph.downloadUrl'];
            storage.fileId = newFile?.id;
            storage.fileSize = existingStorage?.fileSize;
            storage.show = false;
            storage.storageSystem = StorageSystem.MICROSOFT;
            storage.webUrl = newFile?.webUrl;
            storage.authId = user.organization.id;
            storage.organization = user.organization;
            await storage.save();
        } else if (existingStorage?.storageSystem === StorageSystem.AMAZON) {
            const newFile = await this.awsService.copyS3Object(existingStorage?.file, `${existingStorage?.file}-${sanitizeFileNameForAWS(body.estimateNumber)}-${moment().valueOf()}`);
            storage = new Storage();
            storage.uid = uuidv4();
            storage.name = existingStorage?.name;
            storage.type = StorageType.FILE;
            storage.fileType = existingStorage?.fileType;
            storage.file = newFile.newKey;
            storage.fileSize = existingStorage?.fileSize;
            storage.show = false;
            storage.storageSystem = StorageSystem.AMAZON;
            storage.authId = user.organization.id;
            storage.organization = user.organization;
            await storage.save();

        } else if (existingStorage?.storageSystem === StorageSystem.GOOGLE) {
            const newFile = await this.googleDriveService.copyFile(existingStorage?.fileId, userId, existingStorage?.name);
            storage = new Storage();
            storage.uid = uuidv4();
            storage.name = newFile?.name;
            storage.type = StorageType.FILE;
            storage.fileType = newFile?.file?.mimeType;
            storage.file = newFile?.file;
            storage.fileId = newFile?.id;
            storage.fileSize = existingStorage?.fileSize;
            storage.show = false;
            storage.storageSystem = StorageSystem.GOOGLE;
            storage.webUrl = newFile?.webUrl;
            storage.authId = user.organization.id;
            storage.organization = user.organization;
            await storage.save();

        }
        // invoice.approvalHierarchyId = body.approvalHierarchyId;
        invoice.organization = user.organization;
        invoice.billingEntity = billingEntity;
        invoice.client = client;
        invoice.clientGroup = clientGroup;
        invoice.billingEntityAddress = billingEntityAddress;
        invoice.billingAddress = billingAddress;
        // invoice.shippingAddress = shippingAddress;
        invoice.bankDetails = bankDetails;
        invoice.invoiceDate = body.invoiceDate;
        invoice.invoiceDueDate = body.invoiceDueDate;
        invoice.terms = body.terms;
        invoice.placeOfSupply = body.placeOfSupply;
        // invoice.termsAndConditions = body.termsAndConditions;
        invoice.termsAndConditionsCopy = body.termsAndConditionsCopy;
        invoice.particulars = particulars;
        invoice.otherParticulars = otherParticulars;
        invoice.subTotal = body.subTotal;
        invoice.adjustment = body.adjustment;
        invoice.narration = body.narration;
        invoice.totalGstAmount = body.totalGstAmount;
        invoice.totalCharges = body.totalCharges;
        invoice.roundOff = body.roundOff;
        invoice.grandTotal = body.grandTotal;
        invoice.whatsappCheck = body.whatsappCheck;
        invoice.emailCheck = body.emailCheck;
        invoice.divideTax = body.divideTax;
        invoice.hasTds = body.hasTds;
        if (body.hasTds) {
            invoice.tdsSection = body.tdsSection;
            invoice.tdsRate = body.tdsRate;
            invoice.tdsView = body.tdsView;
        };
        if (body.divideTax) {
            invoice.supplyType = body.supplyType;
        }
        invoice['userId'] = user.id;
        if (body.submitForApproval) {
            invoice.status = InvoiceStatus.APPROVAL_PENDING;
        }
        const i = await invoice.save();
        if (storage) {
            storage.invoiceBankAttachement = i.bankDetails;
            await storage.save();
        };
        const proforma = await ProformaInvoice.findOne(body.proformaId);
        proforma.status = ProformaStatus.CONVERTED;
        await proforma.save();
        let activity = new Activity();
        activity.action = Event_Actions.PROFORMA_INVOICE_CONVERTED;
        activity.actorId = user.id;
        activity.type = invoice.client ? ActivityType.BILLING : ActivityType.BILLING_CLIENT_GROUP;
        activity.typeId = invoice.client ? invoice.client.id : invoice.clientGroup.id;
        activity.remarks = `Proforma Invoice "${proforma.invoiceNumber}" Converted by ${user.fullName}`;
        await activity.save();
        if (particulars && particulars.length > 0) {
            const taskIds = particulars.map((particular) => particular.taskId);
            await createQueryBuilder(Task, 'task')
                .where('task.id IN (:...ids)', { ids: taskIds })
                .update({ paymentStatus: PaymentStatusEnum.BILLED, invoiceId: '' + invoice.id })
                .execute();
        }

        return invoice;

    };

    async cancelProformaInvoice(estimateId: number, userId) {

        let user = await User.findOne({
            where: { id: userId },
            relations: ['organization'],
        });

        let invoice = await ProformaInvoice.findOne({
            where: { id: estimateId },
            relations: ['particulars', 'otherParticulars', 'client', 'clientGroup'],
        });
        invoice.status = ProformaStatus.CANCELLED;

        if (invoice.particulars && invoice.particulars.length > 0) {
            invoice.particulars.forEach(async (particular) => {
                if (particular.taskId) {
                    let task = await Task.findOne({ where: { id: particular['taskId'] } });
                    task.proformaStatus = ProformaTaskStatus.NOT_GENERATED;
                    task.proformaInvoiceId = null;
                    task['userId'] = user.id;
                    await task.save();
                }
            });
        }
        invoice['userId'] = user.id;
        await invoice.save();

        let activity = new Activity();
        activity.action = Event_Actions.PROFORMA_INVOICE_CANCELLED;
        activity.actorId = user.id;
        activity.type = invoice.client ? ActivityType.BILLING : ActivityType.BILLING_CLIENT_GROUP;
        activity.typeId = invoice.client ? invoice.client?.id : invoice.clientGroup?.id;
        activity.remarks = `Proforma Invoice "${invoice.invoiceNumber}" Cancelled by ${user.fullName}`;
        await activity.save();


        return invoice;
    };

    // async export(userId: number, body: FindInvoicesDto) {
    //     let user = await User.findOne({
    //         where: { id: userId },
    //         relations: ['organization'],
    //     });

    //     let invoicesData = await this.get(userId, body);
    //     let invoices = invoicesData.result;
    //     // if (!invoices.length) throw new BadRequestException('No Data for Export');
    //     // let rows = invoices.map((invoice) => {
    //     //     let addressLine1 = '';
    //     //     if (invoice?.client?.address && invoice?.client?.address['billingfulladdress']) {
    //     //         addressLine1 = invoice?.client?.address['billingfulladdress'];
    //     //     }
    //     //     if (invoice?.clientGroup?.address && invoice?.clientGroup?.address['billingfulladdress']) {
    //     //         addressLine1 = invoice?.clientGroup?.address['billingfulladdress'];
    //     //     }
    //     //     return {
    //     //         'Invoice Number': invoice?.invoiceNumber,
    //     //         'Invoice Date': moment(invoice?.invoiceDate).format('DD-MM-YYYY'),
    //     //         'Invoice Due Date': moment(invoice?.invoiceDueDate).format('DD-MM-YYYY'),
    //     //         'Billing Entity': invoice?.billingEntity?.tradeName,
    //     //         'Client / Client Group': invoice?.client ? invoice?.client?.displayName : invoice?.clientGroup?.displayName,
    //     //         'GSTIN': invoice?.client ? invoice?.client?.gstNumber : invoice?.clientGroup?.gstNumber,
    //     //         'Address': addressLine1,
    //     //         'State': invoice?.client ? invoice?.client?.address
    //     //             ? invoice?.client?.state
    //     //                 ? invoice?.client?.['state']
    //     //                 : ''
    //     //             : '' : invoice?.clientGroup?.address
    //     //             ? invoice?.clientGroup?.state
    //     //                 ? invoice?.clientGroup?.['state']
    //     //                 : ''
    //     //             : '',
    //     //         'CGST Value': (getTotalGst(invoice.particulars))?.toFixed(2),
    //     //         'Taxable Value': invoice?.subTotal,
    //     //         'Invoice Value': invoice?.grandTotal,
    //     //         'Status': invoice?.status?.toLowerCase(),
    //     //         'Narration': invoice?.narration
    //     //     };
    //     // });

    //     if (!invoices.length) throw new BadRequestException('No Data for Export');

    //     let rows = invoices.flatMap((invoice) => {
    //         let addressLine1 = '';
    //         if (invoice?.client?.address?.['billingfulladdress']) {
    //             addressLine1 = invoice.client.address['billingfulladdress'];
    //         }
    //         if (invoice?.clientGroup?.address?.['billingfulladdress']) {
    //             addressLine1 = invoice.clientGroup.address['billingfulladdress'];
    //         }

    //         const sameState = invoice.billingEntity?.locationOfSupply === invoice.placeOfSupply.split('-')[1];
    //         const hasGst = invoice.billingEntity.hasGst;
    //         const divideTax = invoice.divideTax;
    //         return invoice.particulars.map((particular) => ({
    //             'Invoice Number': invoice.invoiceNumber,
    //             'Invoice Date': moment(invoice.invoiceDate).format('DD-MM-YYYY'),
    //             'Invoice Due Date': moment(invoice.invoiceDueDate).format('DD-MM-YYYY'),
    //             'Billing Entity': invoice.billingEntity?.tradeName,
    //             'Client / Client Group': invoice.client ? invoice.client.displayName : invoice.clientGroup?.displayName,
    //             'GSTIN': invoice.client ? invoice.client.gstNumber : invoice.clientGroup?.gstNumber,
    //             'Address': addressLine1,
    //             'State': invoice.client ? invoice.client.state ?? '' : invoice.clientGroup?.state ?? '',
    //             'HSN': particular.hsn,
    //             'Invoice Value (₹)': 1 * invoice.grandTotal,
    //             'Rate (%)': TAX_TYPE_VALUE[particular.gst],
    //             'Taxable Value (₹)': 1 * invoice.subTotal,
    //             'IGST (₹)': ((hasGst && !sameState) || divideTax) ? getTotalGst([particular]) : 0,
    //             'CGST (₹)': (hasGst && sameState && !divideTax) ? Number((getTotalGst([particular]) / 2).toFixed(2)) : 0,
    //             'SGST (₹)': (hasGst && sameState && !divideTax) ? Number((getTotalGst([particular]) / 2).toFixed(2)) : 0,
    //             'Pure Agent': 1 * invoice.totalCharges,
    //             'Status': invoice.status.charAt(0).toUpperCase() + invoice.status.slice(1).toLowerCase(),
    //             'Narration': invoice.narration
    //         }));
    //     });


    //     const args = {
    //         user: user,
    //     };

    //     this.eventEmitter.emit(Event_Actions.EXPORT_PROFORMA_INVOICES, { ...args });

    //     const worksheet = xlsx.utils.json_to_sheet(rows);
    //     const workbook = xlsx.utils.book_new();
    //     xlsx.utils.book_append_sheet(workbook, worksheet, 'Invoices');
    //     let file = xlsx.write(workbook, { type: 'buffer' });

    //     return file;
    // }

    //FIRST TYPE


    async exportA(userId: number, body: FindInvoicesDto) {
        const newQuery = { ...body, offset: 0, limit: ********* };

        let user = await User.findOne({
            where: { id: userId },
            relations: ['organization'],
        });

        let invoicesData = await this.get(userId, newQuery);
        let invoices = invoicesData.result;

        if (!invoices?.length) throw new BadRequestException('No Data for Export');

        const workbook = new ExcelJS.Workbook();
        const worksheet = workbook.addWorksheet('Invoices', {
            views: [{ state: 'frozen', ySplit: 1 }]
        });

        // Define column headers
        worksheet.columns = [
            { header: 'S.No', key: 'serialNo' },
            { header: 'Invoice #', key: 'invoiceNo' },
            { header: 'Invoice Date', key: 'invoiceDate' },
            { header: 'Billing Entity', key: 'billingEntity' },
            { header: 'Client / Client Group', key: 'client' },
            { header: 'GSTIN', key: 'GSTIN' },
            { header: 'Address', key: 'address' },
            { header: 'State', key: 'state' },
            { header: 'Invoice Due Date', key: 'invoiceDueDate' },
            { header: 'HSN', key: 'hsn' },
            { header: 'GST Rate (%)', key: 'gstRate' },
            { header: 'Taxable Value (₹)', key: 'taxableValue' },
            { header: 'IGST (₹)', key: 'igst' },
            { header: 'CGST (₹)', key: 'cgst' },
            { header: 'SGST (₹)', key: 'sgst' },
            { header: 'Invoice Value (₹)', key: 'invoiceValue' },
            { header: 'Narration', key: 'narration' },
            { header: 'Status', key: 'status' },


        ];

        let serialCounter = 1;
        const columnMaxLengths = Array(worksheet.columns.length).fill(0);
        invoices.forEach((invoice) => {
            let addressLine1 = '';
            if (invoice?.client?.address?.['billingfulladdress']) {
                addressLine1 = invoice.client.address['billingfulladdress'];
            }
            if (invoice?.clientGroup?.address?.['billingfulladdress']) {
                addressLine1 = invoice.clientGroup.address['billingfulladdress'];
            }

            const sameState = invoice.billingEntity?.locationOfSupply === invoice.placeOfSupply.split('-')[1];
            const hasGst = invoice.billingEntity.hasGst;
            const divideTax = invoice.divideTax;

            const otherParticular = invoice?.otherParticulars?.length
                ? {
                    amount: invoice.otherParticulars.reduce((sum, item) => sum + item.amount * 1, 0),
                    hsn: '0',
                    gst: 'GST0',
                }
                : null;

            const rowData = [
                ...invoice.particulars,
                ...(otherParticular ? [otherParticular] : [])
            ].map((particular) => (
                {

                    serialNo: serialCounter++,
                    invoiceNo: invoice.invoiceNumber,
                    invoiceDate: moment(invoice.invoiceDate).format('DD-MM-YYYY'),
                    billingEntity: invoice.billingEntity?.tradeName || '',
                    client: invoice.client?.displayName || invoice.clientGroup?.displayName || '',
                    GSTIN: invoice.client ? invoice.client.gstNumber : invoice.clientGroup?.gstNumber,
                    address: addressLine1,
                    state: invoice.client ? invoice?.client?.address?.['billingState'] : invoice?.clientGroup?.address?.['billingState'],
                    invoiceDueDate: moment(invoice.invoiceDueDate).format('DD-MM-YYYY'),
                    hsn: particular.hsn,
                    gstRate: TAX_TYPE_VALUE[particular.gst],
                    taxableValue: particular.amount * 1,
                    igst: ((hasGst && !sameState) || divideTax) ? getTotalGst([particular]) : 0,
                    cgst: (hasGst && sameState && !divideTax) ? Number((getTotalGst([particular]) / 2).toFixed(2)) : 0,
                    sgst: (hasGst && sameState && !divideTax) ? Number((getTotalGst([particular]) / 2).toFixed(2)) : 0,
                    invoiceValue: 1 * invoice.grandTotal,
                    narration: invoice.narration,
                    status: invoice.status.charAt(0).toUpperCase() + invoice.status.slice(1).toLowerCase()

                }));


            rowData.forEach((data) => {
                const row = worksheet.addRow(Object.values(data)); // Add row from object values
                const statusIndex = Object.keys(data).indexOf('status') + 1; // Get the column index of 'status'
                const statusCell = row.getCell(statusIndex); // Get the correct cell for 'status'

                if (data.status) {
                    let color;
                    switch (data.status.toLowerCase()) {
                        case 'created': color = '149ECD'; break;
                        case 'invoiced': color = '149ECD'; break;
                        case 'in progress': color = 'F49752'; break;
                        case 'cancelled': color = 'F63338'; break;
                        case 'converted': color = 'F49752'; break;
                        case 'overdue': color = 'F63338'; break;
                        case 'partially paid': color = 'F49752'; break;
                        case 'cancelled': color = 'F63338'; break;
                        case 'closed': color = '008000'; break;
                        case 'paid': color = '008000'; break;
                        default: color = '000000'; break; // Default black
                    }
                    statusCell.font = { color: { argb: color }, bold: true };
                }
            });
        });

        // Auto adjust column widths
        worksheet.columns.forEach((column) => {
            let maxLength = column.header.length;
            worksheet.eachRow((row) => {
                const cellValue = row.getCell(column.key as string).value;
                const cellLength = cellValue ? cellValue.toString().length : 0;
                maxLength = Math.max(maxLength, cellLength);
            });
            column.width = maxLength + 3; // Add padding
        });

        // Apply styles to header row
        const headerRow = worksheet.getRow(1);
        headerRow.font = { bold: true };
        headerRow.eachCell((cell) => {
            cell.fill = {
                type: 'pattern',
                pattern: 'solid',
                fgColor: { argb: '64B5F6' },
            };
            cell.border = {
                top: { style: 'thin' },
                left: { style: 'thin' },
                bottom: { style: 'thin' },
                right: { style: 'thin' },
            };
            cell.alignment = { horizontal: 'center', vertical: 'middle' };
        });
        worksheet.columns.forEach((column) => {
            if (column.key === 'billingEntity' || column.key === 'address' || column.key === 'client') {
                column.width = 50;
                column.alignment = { wrapText: true, horizontal: 'center', vertical: 'middle' };
            } else if (column.key === 'narration') {
                column.width = 100;
                column.alignment = { wrapText: true, horizontal: 'center', vertical: 'middle' };
            } else {
                column.alignment = { horizontal: 'center', vertical: 'middle' };
            }
        });

        // Apply alignment and wrapText for all rows
        worksheet.eachRow((row, rowNumber) => {
            row.eachCell((cell) => {
                cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
            });
        });

        // Freeze the header row
        worksheet.views = [{ state: 'frozen', ySplit: 1 }];

        // Write workbook to buffer
        const file = await workbook.xlsx.writeBuffer();

        return { file, type: 'Type-A' };
    }

    async exportB(userId: number, body: FindInvoicesDto) {
        const newQuery = { ...body, offset: 0, limit: ********* };

        let user = await User.findOne({
            where: { id: userId },
            relations: ['organization'],
        });

        let invoicesData = await this.get(userId, newQuery);
        let invoices = invoicesData.result;

        if (!invoices?.length) throw new BadRequestException('No Data for Export');

        const workbook = new ExcelJS.Workbook();
        const worksheet = workbook.addWorksheet('Invoices', {
            views: [{ state: 'frozen', ySplit: 1 }]
        });

        // Define column headers
        worksheet.columns = [
            { header: 'S.No', key: 'serialNo' },
            { header: 'Invoice #', key: 'invoiceNo' },
            { header: 'Invoice Date', key: 'invoiceDate' },
            { header: 'Billing Entity', key: 'billingEntity' },
            { header: 'Client / Client Group', key: 'client' },
            { header: 'GSTIN', key: 'GSTIN' },
            { header: 'Address', key: 'address' },
            { header: 'State', key: 'state' },
            { header: 'Invoice Due Date', key: 'invoiceDueDate' },
            // { header: 'HSN', key: 'hsn'},
            { header: 'GST Rate (%)', key: 'gstRate' },
            { header: 'Taxable Value (₹)', key: 'taxableValue' },
            { header: 'IGST (₹)', key: 'igst' },
            { header: 'CGST (₹)', key: 'cgst' },
            { header: 'SGST (₹)', key: 'sgst' },
            { header: 'Invoice Value (₹)', key: 'invoiceValue' },
            { header: 'Narration', key: 'narration' },
            { header: 'Status', key: 'status' },


        ];

        let serialCounter = 1;
        const columnMaxLengths = Array(worksheet.columns.length).fill(0);

        invoices.forEach((invoice) => {
            let addressLine1 = '';
            if (invoice?.client?.address?.['billingfulladdress']) {
                addressLine1 = invoice.client.address['billingfulladdress'];
            }
            if (invoice?.clientGroup?.address?.['billingfulladdress']) {
                addressLine1 = invoice.clientGroup.address['billingfulladdress'];
            }

            const sameState = invoice.billingEntity?.locationOfSupply === invoice.placeOfSupply.split('-')[1];
            const hasGst = invoice.billingEntity.hasGst;
            const divideTax = invoice.divideTax;
            const filterZeroParticulars = invoice.particulars.filter(p => (!hasGst || p.gst == 'GST0' || p.gst == null));
            const nonZeroParticulars = invoice.particulars.filter(p => (p.gst != 'GST0' && p.gst != null && hasGst));
            const combaineWithRates: any = Object.values(
                nonZeroParticulars.reduce((acc, item) => {
                    const gst = item.gst;
                    const amount = 1 * item.amount;
                    if (acc[gst]) {
                        acc[gst].amount += amount;
                    } else {
                        acc[gst] = { gst, amount };
                    }
                    return acc;
                }, {})
            );

            let otherParticular =
                (invoice.otherParticulars?.length || filterZeroParticulars?.length) ? {
                    amount: [...invoice?.otherParticulars, ...filterZeroParticulars].reduce((sum, item) => (sum + (item.amount) * 1), 0),
                    hsn: '0',
                    gst: 'GST0',
                } : null;
            const rowData = [
                ...combaineWithRates,
                ...(otherParticular ? [otherParticular] : [])
            ].map((particular) => {
                return {
                    serialNo: serialCounter++,
                    invoiceNo: invoice.invoiceNumber,
                    invoiceDate: moment(invoice.invoiceDate).format('DD-MM-YYYY'),
                    billingEntity: invoice.billingEntity?.tradeName || '',
                    client: invoice.client?.displayName || invoice.clientGroup?.displayName || '',
                    GSTIN: invoice.client ? invoice.client.gstNumber : invoice.clientGroup?.gstNumber,
                    address: addressLine1,
                    state: invoice.client ? invoice?.client?.address?.['billingState'] : invoice.clientGroup?.address?.['billingState'],
                    invoiceDueDate: moment(invoice.invoiceDueDate).format('DD-MM-YYYY'),
                    // hsn: particular.hsn,
                    gstRate: TAX_TYPE_VALUE[particular.gst],
                    taxableValue: particular.amount * 1,
                    igst: ((hasGst && !sameState) || divideTax) ? getTotalGst([particular]) : 0,
                    cgst: (hasGst && sameState && !divideTax) ? Number((getTotalGst([particular]) / 2).toFixed(2)) : 0,
                    sgst: (hasGst && sameState && !divideTax) ? Number((getTotalGst([particular]) / 2).toFixed(2)) : 0,
                    invoiceValue: 1 * invoice.grandTotal,
                    narration: invoice.narration,
                    status: invoice.status.charAt(0).toUpperCase() + invoice.status.slice(1).toLowerCase()
                };
            });



            rowData.forEach((data) => {
                const row = worksheet.addRow(Object.values(data)); // Add row from object values
                const statusIndex = Object.keys(data).indexOf('status') + 1; // Get the column index of 'status'
                const statusCell = row.getCell(statusIndex); // Get the correct cell for 'status'

                if (data.status) {
                    let color;
                    switch (data.status.toLowerCase()) {
                        case 'created': color = '149ECD'; break;
                        case 'invoiced': color = '149ECD'; break;
                        case 'in progress': color = 'F49752'; break;
                        case 'cancelled': color = 'F63338'; break;
                        case 'converted': color = 'F49752'; break;
                        case 'overdue': color = 'F63338'; break;
                        case 'partially paid': color = 'F49752'; break;
                        case 'cancelled': color = 'F63338'; break;
                        case 'closed': color = '008000'; break;
                        case 'paid': color = '008000'; break;
                        default: color = '000000'; break; // Default black
                    }
                    statusCell.font = { color: { argb: color }, bold: true };
                }
            });
        });

        // Auto adjust column widths
        worksheet.columns.forEach((column) => {
            let maxLength = column.header.length;
            worksheet.eachRow((row) => {
                const cellValue = row.getCell(column.key as string).value;
                const cellLength = cellValue ? cellValue.toString().length : 0;
                maxLength = Math.max(maxLength, cellLength);
            });
            column.width = maxLength + 3; // Add padding
        });

        // Apply styles to header row
        const headerRow = worksheet.getRow(1);
        headerRow.font = { bold: true };
        headerRow.eachCell((cell) => {
            cell.fill = {
                type: 'pattern',
                pattern: 'solid',
                fgColor: { argb: '64B5F6' },
            };
            cell.border = {
                top: { style: 'thin' },
                left: { style: 'thin' },
                bottom: { style: 'thin' },
                right: { style: 'thin' },
            };
            cell.alignment = { horizontal: 'center', vertical: 'middle' };
        });
        worksheet.columns.forEach((column) => {
            if (column.key === 'billingEntity' || column.key === 'address' || column.key === 'client') {
                column.width = 50;
                column.alignment = { wrapText: true, horizontal: 'center', vertical: 'middle' };
            } else if (column.key === 'narration') {
                column.width = 100;
                column.alignment = { wrapText: true, horizontal: 'center', vertical: 'middle' };
            } else {
                column.alignment = { horizontal: 'center', vertical: 'middle' };
            }
        });

        // Apply alignment and wrapText for all rows
        worksheet.eachRow((row, rowNumber) => {
            row.eachCell((cell) => {
                cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
            });
        });

        // Freeze the header row
        worksheet.views = [{ state: 'frozen', ySplit: 1 }];

        // Write workbook to buffer
        const file = await workbook.xlsx.writeBuffer();

        return { file, type: 'Type-B' };
    }




}