import { JwtAuthGuard } from 'src/modules/users/jwt/jwt-auth.guard';
import { AutProfileDetailsService } from '../services/aut-profile-details.service';
import { Controller, UseGuards } from '@nestjs/common/decorators/core';
import { Body, Get, Param, Post, Query, Req } from '@nestjs/common/decorators/http';
import { ParseIntPipe } from '@nestjs/common';

@Controller('automation')
export class AutProfileDetailsController {
  constructor(private service: AutProfileDetailsService) {}

  @UseGuards(JwtAuthGuard)
  @Get()
  findAll(@Req() req: any, @Query() query: any) {
    const { userId } = req.user;
    return this.service.findAll(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Post()
  create(@Body() body: any, @Req() req: any) {
    const { userId } = req.user;
    return this.service.create(userId, body);
  }

  // @Put('/:id')
  // update(@Param('id', ParseIntPipe) id: number, @Body() body: CreateLabelDto) {
  //   return this.service.update(id, body);
  // }

  // @Delete('/:id')
  // delete(@Param('id', ParseIntPipe) id: number) {
  //   return this.service.delete(id);
  // }

  @UseGuards(JwtAuthGuard)
  @Post('auto-machine/:id')
  createMachine(@Param('id', ParseIntPipe) id: number, @Body() body: any, @Req() req: any) {
    const { userId } = req.user;
    return this.service.createMachine(userId, id, body);
  }

  @UseGuards(JwtAuthGuard)
  @Get('clientAutoStatus/:id')
  getclientAutoStatus(@Param('id', ParseIntPipe) id: number, @Req() req: any) {
    const { userId } = req.user;
    return this.service.getclientAutoStatus(id, userId);
  }

  @UseGuards(JwtAuthGuard)
  @Post('bulkSync')
  bulkAutomationSync(@Body() body: any, @Req() req: any) {
    const { userId } = req.user;
    return this.service.bulkAutomationSync(userId, body);
  }

  @UseGuards(JwtAuthGuard)
  @Get('reports')
  getclientReport(@Req() req: any, @Query() query: any) {
    const { userId } = req.user;
    return this.service.getclientReport(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Post('/export-reports')
  async exportclientReport(@Req() req: any, @Body() body: any) {
    const { userId } = req?.user;
    const query= body;
    return this.service.exportclientReport(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Get('new-updates')
  getIncometexUpdates(@Req() req: any, @Query() query: any) {
    const { userId } = req.user;
    return this.service.getIncometexUpdates(userId,query);
  }

  @UseGuards(JwtAuthGuard)
  @Get('update/:id')
  findForm(@Req() req: any, @Param('id', ParseIntPipe) id: number) {
    const { userId } = req.user;
    return this.service.getUpdatedItem(userId, id);
  }

  // @UseGuards(JwtAuthGuard)
  // @Get('fya-notice')
  // getFyaNotices(@Req() req: any, @Query() query: any) {
  //   const { userId } = req.user;
  //   return this.service.getFyaNotices(userId, query);
  // }

  @UseGuards(JwtAuthGuard)
  @Get('eproceeding-notice')
  getFyiNotices(@Req() req: any, @Query() query: any) {
    const { userId } = req.user;
    return this.service.getCombinedNotices(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Post('/export-eproceeding-notice')
  async exportCombinedNotices(@Req() req: any, @Body() body: any) {
    const { userId } = req?.user;
    const query= body;
    return this.service.exportCombinedNotices(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Post('atomProCredentials/:id')
  addCredentialsToAtomPro(@Param('id', ParseIntPipe) id: number, @Req() req: any) {
    const { userId } = req.user;
    return this.service.addCredentialsToAtomPro(userId, id);
  }

  @UseGuards(JwtAuthGuard)
  @Post('limitRequest')
  sendAtomProLimitRequest(@Req() req: any) {
    const { userId } = req.user;
    return this.service.sendAtomProLimitRequest(userId);
  }

  @UseGuards(JwtAuthGuard)
  @Post('sendAtomProRequest')
  sendAtomProRequest(@Req() req: any, @Body() body: any) {
    const { userId } = req.user;
    return this.service.sendAtomProRequest(userId, body);
  }

  @UseGuards(JwtAuthGuard)
  @Get('eproceeding-notice-excel')
  getExcelFyiNotices(@Req() req: any, @Query() query: any) {
    const { userId } = req.user;
    return this.service.getExcelCombinedNotices(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Post('/exportEproceedingExcelNotice-export')
  async exportEproceedingExcelNotice(@Req() req: any, @Body() body: any) {
    const { userId } = req?.user;
    const query= body;
    return this.service.exportEproceedingExcelNotice(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Post('sync-fail')
  syncFailedModules(@Req() req: any, @Body() body: any) {
    const { userId } = req.user;
    return this.service.syncFailedModules(userId);
  }
}
