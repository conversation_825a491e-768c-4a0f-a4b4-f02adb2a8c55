import { Injectable } from '@nestjs/common';
import { OnEvent } from '@nestjs/event-emitter';
import Client from 'src/modules/clients/entity/client.entity';
import email from 'src/emails/email';
import { sendNotification } from 'src/notifications/notify';
import { User } from 'src/modules/users/entities/user.entity';
import notify from 'src/notifications/notify';
import Activity, { ActivityType } from '../../modules/activity/activity.entity';
import { Event_Actions } from '../actions';
import { sendnewMail } from 'src/emails/newemails';
import OrganizationPreferences from 'src/modules/organization-preferences/entity/organization-preferences.entity';
import { Organization } from 'src/modules/organization/entities/organization.entity';

interface CreateEvent {
  userId: number;
  data: Client;
  password?: string;
  orgName: string;
  isEmail: boolean;
}

interface CreateClientUser {
  fullName: string;
  email: string;
  password: string;
}

@Injectable()
export class ClientListener {
  @OnEvent(Event_Actions.CLIENT_CREATED, { async: true })
  async addToActivity(event: CreateEvent) {
    try {
      let user = await User.findOne({ where: { id: event.userId } });
      let activity = new Activity();
      activity.action = Event_Actions.CLIENT_CREATED;
      activity.actorId = user.id;
      activity.type = ActivityType.CLIENT;
      activity.typeId = event.data.id;
      activity.remarks = `"${event.data.clientId}" Client Profile Created by ${user.fullName}`;
      await activity.save();

      if(event.data.clientPortalAccess){
        let activity = new Activity();
        activity.action = Event_Actions.CLIENT_PORTAL_ACCESS;
        activity.actorId = user.id;
        activity.type = ActivityType.CLIENT;
        activity.typeId = event.data.id;
        activity.remarks = `Client Portal Access Enabled by ${user.fullName}`;
        await activity.save();
      }
    } catch (e) {
      console.log(e);
    }
  }

  @OnEvent(Event_Actions.CLIENT_UPDATED, { async: true })
  async updateActivity(event: CreateEvent) {
    try {
      let user = await User.findOne(event.userId);
      let activity = new Activity();
      activity.action = Event_Actions.CLIENT_PROFILE_UPDATED;
      activity.actorId = user.id;
      activity.type = ActivityType.CLIENT;
      activity.typeId = event.data.id;
      activity.remarks = `Client Profile Updated by ${user.fullName}`;
      await activity.save();
    } catch (err) {
      console.log(err);
    }
  }

  @OnEvent(Event_Actions.CLIENT_CREATED, { async: true })
  async sendWelcomeAndCredentials(event: CreateEvent) {
    const { data, password } = event;

    if (!data.clientPortalAccess) return;

    // try {
    //   await email.clientCreated({
    //     email: data.email,
    //     password: password,
    //     fullName: data.displayName,
    //     orgName: event.orgName,
    //   });
    // } catch (e) {
    //   console.log(e);
    // }
  }

  @OnEvent(Event_Actions.CLIENT_CREATED, { async: true })
  async sendNotificationToClientManager(event: CreateEvent) {
    const { data } = event;
    try {
      let client = await Client.findOne({
        where: { id: data.id },
        relations: ['clientManagers'],
      });

      // if (!client.clientManager) return;

      // let notification = {
      //   title: `${client?.displayName}'s Profile has been Created`,
      //   body: `Lets manage ${client?.displayName}'s information, profile, tasks, passwords, storage all at one place `,
      // };

      // await sendNotification([client.clientManager.id], notification);
      await notify.clientCreated(client);
    } catch (e) {
      console.log(e);
    }
  }

  // @OnEvent(Event_Actions.CLIENT_UPDATED, { async: true })
  // async handleClientUpdate(event: CreateEvent) {
  //   try {
  //     const { data } = event;
  //     let client = await Client.findOne({
  //       where: { id: data.id },
  //       relations: ['clientManager'],
  //     });

  //     if (!client.clientManager) return;

  //     let notification = {
  //       title: `Client Profile Update`,
  //       body: `<b>${client?.displayName}</b>'s Profile has been updated`,
  //     };

  //     await sendNotification([client.clientManager.id], notification);
  //   } catch (e) {
  //     console.log(e);
  //   }
  // }

  @OnEvent(Event_Actions.CLIENT_USER_CREATED, { async: true })
  async sendCredentialsToClientUser(event: CreateClientUser) {
    try {
      // await email.clientCreated({
      //   email: event.email,
      //   password: event.password,
      //   fullName: event.fullName,
      //   orgName: event.orgName,
      // });
    } catch (e) {
      console.log(e);
    }
  }

  @OnEvent(Event_Actions.CLIENT_PORTAL_ACCESS_UPDATED, { async: true })
  async UpdatedClientPortalAccess(event: any) {
    const organization = await Organization.findOne({ id: event.user.organization.id });
            const addressParts = [
          organization.buildingNo || '',
          organization.floorNumber || '',
          organization.buildingName || '',
          organization.street || '',
          organization.location || '',
          organization.city || '',
          organization.district || '',
          organization.state || ''
        ].filter(part => part && part.trim() !== '');
        const pincode = organization.pincode && organization.pincode.trim() !== '' ? ` - ${organization.pincode}` : '';
    
        const address = addressParts.join(', ') + pincode;
    try {
      const data = {
        displayname: event.data.displayName,
        userId: event.user.id,
        userEmail: event.user.email,
        adress: address,
        phoneNumber: organization?.mobileNumber,
        mail: organization?.email,
        legalName: organization?.tradeName || organization?.legalName

      }
      const key = 'CLIENT_PORTAL_ACCESS_MAIL'
      const mailOptions = {
        id: event.user.id,
        key: key,
        email: event.data.email,
        clientMail: 'ORGANIZATION_CLIENT_EMAIL',
        data: data,
        filePath: 'client-portal-access',
        subject: `Welcome to Your ATOM Client Portal!`,
      };

      const orgPreferences: any = await OrganizationPreferences.findOne({ where: { organization: event.user.organization.id } })
      const clientPreferences = orgPreferences?.clientPreferences?.email;
      if (clientPreferences && clientPreferences[key]) {
        await sendnewMail(mailOptions);
      }
    } catch (e) {
      console.log(e);
    }
  }
}
