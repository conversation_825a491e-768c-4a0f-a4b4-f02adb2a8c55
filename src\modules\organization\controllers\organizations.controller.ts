import { Body, Controller, Get, Param, ParseIntPipe, Patch, Put, Request, UseGuards } from '@nestjs/common';
import { JwtAuthGuard } from 'src/modules/users/jwt/jwt-auth.guard';
import { UpdateOrganizationProfileDto } from '../dto/update-organization-profile.dto';
import { OrganizationsService } from '../services/orgnizations.service';

@UseGuards(JwtAuthGuard)
@Controller('organization')
export class OrganizationsController {
  constructor(private service: OrganizationsService) { }

  @Get()
  async getOrganizationProfile(@Request() request: any) {
    let { userId } = request.user;
    return this.service.getOrganizationProfile(userId);
  }

  @Patch()
  async updateOrganizationProfile(
    @Request() request: any,
    @Body() body: UpdateOrganizationProfileDto,
  ) {
    let { userId } = request.user;
    return this.service.updateOrganizationProfile(userId, body);
  }

  @Get("/smtp")
  async getSmtp(@Request() request: any) {
    let { userId } = request.user;
    return this.service.getsmtp(userId);
  }

  @Patch("/smtp")
  async updateSmtp(
    @Request() request: any,
    @Body() body: any,
  ) {
    let { userId } = request.user;
    return this.service.updateSmtp(userId, body);
  }

  @Patch("/smtp/mail")
  async updateSmtpMail(
    @Request() request: any,
    @Body() body: any,
  ) {
    let { userId } = request.user;
    return this.service.updateSmtpMail(userId, body);
  }

  @Patch("/smtp/preferences")
  async updateSmtpPreferences(
    @Request() request: any,
    @Body() body: any,
  ) {
    let { userId } = request.user;
    return this.service.updateSmtpPreferences(userId, body);
  }

  @Patch("/storage-status")
  async updateStorageStatus(
    @Request() request: any,
    @Body() body: any) {
    let { userId } = request.user;
    return this.service.updateStorageStatus(userId, body);
  }


  @Get('/udin-users')
  async getudinUsers(@Request() request: any) {
    let { userId } = request.user;
    return this.service.getudinUsers(userId);
  }
}
