import { User, UserStatus, UserType } from 'src/modules/users/entities/user.entity';
import { create<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, get<PERSON>anager, getRepository } from 'typeorm';
import { Notification } from 'src/notifications/notification.entity';
import NotificationPreferences from 'src/modules/notification-settings/notifications-preferences.entity';
import Client from 'src/modules/clients/entity/client.entity';
import Task from 'src/modules/tasks/entity/task.entity';
import OrganizationPreferences from 'src/modules/organization-preferences/entity/organization-preferences.entity';
import axios from 'axios';
import { InternalServerErrorException } from '@nestjs/common';
import ClientGroup from 'src/modules/client-group/client-group.entity';

const itrTypes = {
  '1': 'ITR-1',
  '2': 'ITR-2',
  '2A': 'ITR-2A',
  '3': 'ITR-3',
  '4': 'ITR-4',
  '4S': 'ITR-4S',
  '5': 'ITR-5',
  '6': 'ITR-6',
  '7': 'ITR-7',
};
export const getFormType = (type) => {
  return itrTypes[type] || 'Unknown Type'; // Default to 'Unknown Type' if not found
};

const filingTypes = {
  O: 'Original Return',
  D: 'Defective Return',
  R: 'Revised Return',
  T: 'Rectification Return',
  U: 'Updated Return',
};

export const getFilingType = (type) => {
  const abc = filingTypes[type];
  return abc;
};
export const getTitle = (key: string) => {
  key = key || '';
  return key
    ?.split('_')
    .map((item) => item.charAt(0).toUpperCase() + item.slice(1))
    .join(' ');
};
export const getExpenseType = (key: string) => {
  if (key === 'PURE_AGENT') return 'Pure Agent';
  else if (key === 'ADDITIONAL') return 'Additional Charges';
  else return '---';
};

export const formattedDate = (dateString) => {
  if (!dateString || isNaN(new Date(dateString).getTime())) {
    return ''; // Return empty if the dateString is invalid or not provided
  }
  const date = new Date(dateString);
  const day = date.getDate().toString().padStart(2, '0');
  const month = (date.getMonth() + 1).toString().padStart(2, '0');
  const year = date.getFullYear();
  return `${day}-${month}-${year}`;
};
export const getTimesheetDuration = (duration: number | null | undefined): string => {
  if (!duration || isNaN(duration)) {
    return '00:00'; // Default to "00:00" if duration is not valid
  }
  const hours = Math.floor(duration / 1000 / 60 / 60);
  const minutes = Math.floor((duration / 1000 / 60) % 60);
  return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;
};

export const getTotalLogHours = (generalLog, taskLog) => {
  if (!generalLog && !taskLog) return '00:00';

  // Convert to total minutes
  const generalMinutes = Math.floor((generalLog || 0) / 60000);
  const taskMinutes = Math.floor((taskLog || 0) / 60000);

  // Sum up total minutes
  const totalMinutes = generalMinutes + taskMinutes;
  const hours = Math.floor(totalMinutes / 60); // Extract hours
  const minutes = totalMinutes % 60; // Remaining minutes

  // Return formatted HH:MM string
  return `${String(hours).padStart(2, '0')}:${String(minutes).padStart(2, '0')}`;
};
export const formattedDateMonthYear = (date) => {
  const d = new Date(date);
  const day = String(d.getDate()).padStart(2, '0'); // Ensures two digits for day
  const month = String(d.getMonth() + 1).padStart(2, '0'); // Ensures two digits for month
  const year = d.getFullYear(); // Extracts the full year
  return `${day}-${month}-${year}`;
};

const verificationStatus = {
  Y: 'Verified',
  N: 'Not Verified', //red
  X: 'NA',
  empty: 'None',
};
export const getverificationStatus = (type) => {
  const abc = verificationStatus[type];
  return abc;
};

export const generateAssessmentYear = (assessmentYear) => {
  if (!assessmentYear || assessmentYear === '0' || assessmentYear === '') {
    return 'NA';
  } else if (!isNaN(assessmentYear) && assessmentYear.length === 4) {
    const year = parseInt(assessmentYear);
    const nextYear = (year + 1).toString().slice(-2);
    return `${year}-${nextYear}`;
  } else {
    return assessmentYear;
  }
};

// Utility function to calculate and format the assessment year
export function calculateAssessmentYear(startYear: any) {
  // Convert startYear to a number and calculate the end year
  const endYear = parseInt(startYear, 10) + 1;
  // Format the assessment year in 'YYYY-YY' format
  const formattedAssessmentYear = `${startYear}-${endYear.toString().slice(2)}`;
  // Return the formatted string with 'AY'
  return ` ${formattedAssessmentYear}`;
}

export function calculateAdvanceYr(startYear: any) {
  // Extract the first four digits (start year)
  const baseYear = parseInt(startYear.toString().slice(0, 4), 10);
  // Increment to get the next year
  const nextYear = baseYear + 1;
  // Format the assessment year in 'YYYY-YY' format
  const formattedAssessmentYear = `${nextYear}-${(nextYear + 1).toString().slice(2)}`;
  // Return the formatted string
  return formattedAssessmentYear;
}

// Utility function to calculate and format the assessment year or fiscal year
export function calculateAssessmentYear1(startYear, offset = 0) {
  // Convert startYear to a number and adjust the year by the offset
  const adjustedStartYear = parseInt(startYear, 10) + offset;
  const endYear = adjustedStartYear + 1;
  // Format the assessment year in 'YYYY-YY' format
  const formattedAssessmentYear = `${adjustedStartYear}-${endYear.toString().slice(2)}`;
  // Return the formatted string with 'AY'
  return `${formattedAssessmentYear}`;
}

// Utility function to format the amount with commas without the Indian Rupee symbol
export function formatAmount(amount) {
  // Check if the amount is a number, if not, parse it
  const parsedAmount = typeof amount === 'number' ? amount : parseFloat(amount);
  // Format the amount using the Indian locale without the currency style
  return new Intl.NumberFormat('en-IN').format(parsedAmount);
}

export const getUserIDs = async (taskId: number): Promise<User[]> => {
  const entityManager = getManager();
  const sql = `select user_id from task_members_user where task_id=${taskId}`;
  let members = await entityManager.query(sql);
  const userIDs: User[] = members.map((row: { user_id: any }) => row.user_id);
  return userIDs;
};

export const getAllTaskMemberNames = async (taskMemeberIds) => {
  const entityManager = getManager();

  const getUserName = (userId) => `SELECT full_name FROM user where id= ${userId};`;

  const taskMemberNames = await Promise.all(
    taskMemeberIds.map(async (userId) => {
      const userName = await entityManager.query(getUserName(userId));
      return userName[0].full_name;
    }),
  );
  const allTaskmemberNames = taskMemberNames.join(', ');
  return allTaskmemberNames;
};

export const getAllTaskMemberDetails = async (taskMemeberIds) => {
  const entityManager = getManager();
  const taskMemList = [];
  for (let i of taskMemeberIds) {
    const taskMemQuery = `SELECT full_name,email from user where id=${i}`;
    const taskMem = await entityManager.query(taskMemQuery);
    taskMemList.push(taskMem);
  }
  return taskMemList;
};

export const checkUserTosendMorningMsg = async (userId: number): Promise<boolean> => {
  const user = await User.findOne({
    where: { id: userId, status: UserStatus.ACTIVE },
    relations: ['organization'],
  });

  if (!user) {
    return false;
  }

  const orgId = user.organization?.id;

  const hasWhatsappPreferences = await createQueryBuilder(OrganizationPreferences, 'orgPrefs')
    .where('orgPrefs.organization = :organizationId', { organizationId: orgId })
    .andWhere(
      'JSON_EXTRACT(orgPrefs.notificationConfig, "$.whatsappPreferences") = :whatsappPreferences',
      { whatsappPreferences: true },
    )
    .getMany();

  if (!hasWhatsappPreferences) {
    return false;
  }

  const userPreferenceCheck = await NotificationPreferences.findOne({
    where: { user: userId, whatsappConfig: true },
  });

  return !!userPreferenceCheck;
};
// export const getAdminIDsBasedOnOrganizationId = async (organizationId: number) => {
//   const entityManager = getManager()
//   //get organization admin role id
//   const query1 = `SELECT id from role where organization_id=${organizationId} and name='Admin'`
//   const getRoleId = await entityManager.query(query1)
//   if (getRoleId.length >= 1) {
//     const roleId = getRoleId[0].id
//     // get admin list
//     const userId = `SELECT id FROM user where organization_id=${organizationId} and role_id = ${roleId}`
//     //console.log(user_id)
//     const getOrganizationId = await entityManager.query(userId)
//     const userList = []
//     for (let i of getOrganizationId) {
//       userList.push(i.id)
//     }
//     return userList
//   }
// }

export async function insertINTOnotificationForApproval(
  title: string,
  body: string,
  users: User[],
): Promise<void> {
  let notifications = [];
  for (let a of users) {
    let newNotification = new Notification();
    newNotification.title = title;
    newNotification.body = body;
    newNotification.user = a;
    notifications.push(newNotification);
  }
  await Notification.save(notifications);
  // console.log('Success notification');
}

export const getAdminEmailssBasedOnOrganizationId = async (organizationId: number) => {
  const entityManager = getManager();
  const getRoleQuery = `SELECT id from role where organization_id=${organizationId} and name='Admin'`;
  const getRoleId = await entityManager.query(getRoleQuery);
  const roleId = getRoleId[0].id;

  const user_id = `SELECT id,email,full_name FROM user where organization_id=${organizationId} and role_id = ${roleId} and type = 'ORGANIZATION'`;
  const get_organization_id = await entityManager.query(user_id);
  const user_list = [];
  for (let i of get_organization_id) {
    user_list.push(i);
  }
  return user_list;
};

export const getAdminsBasedonUserId = async (userId: number) => {
  const entity_manager = getManager();
  const user_id = `SELECT organization_id FROM user where id=${userId} and type = 'ORGANIZATION'`;
  const get_organization_id = await entity_manager.query(user_id);
  const orgId = get_organization_id[0].organization_id;
  getAdminEmailssBasedOnOrganizationId(orgId);
};

export const getAdminEmailsBasedOnOrganizationId = async (organizationId: number) => {
  const entity_manager = getManager();
  const getRoleQuery = `SELECT id from role where organization_id=${organizationId} and name='Admin'`;
  const get_role_id = await entity_manager.query(getRoleQuery);
  if (get_role_id.length >= 1) {
    const role_id = get_role_id[0].id;
    const admin_details = `SELECT id, email, full_name FROM user where organization_id=${organizationId} and role_id = ${role_id} and type = 'ORGANIZATION'`;
    const get_organization_details = await entity_manager.query(admin_details);
    const admin_list = [];
    for (let i of get_organization_details) {
      admin_list.push(i);
    }
    return admin_list;
  }
};

export const getTaskUserIDs = async (taskId: number): Promise<User[]> => {
  const entityManager = getManager();
  const getTaskMemberQuery = `select user_id from task_members_user where task_id=${taskId}`;
  let members = await entityManager.query(getTaskMemberQuery);
  const userIDs: User[] = members.map((row: { user_id: any }) => row.user_id);
  return userIDs;
};

export const getUserDetails = async (userId) => {
  const userQuery = `SELECT * FROM user WHERE id = ${userId}`;
  const entityManager = getManager();
  const userDetails = await entityManager.query(userQuery);
  return userDetails[0];
};

export async function insertINTOnotification(
  title: string,
  body: string,
  users: any,
  orgId: number,
  source = null,
): Promise<void> {
  const titleName = title.split(' ');
  titleName.push('PUSH');
  const titleName2 = titleName.join('_').toUpperCase();
  let newTitle = undefined;
  if (titleName.includes('Generated')) {
    newTitle = 'GENERATED_PUSH';
  }
  const entityManager = getManager();
  let notifications = [];
  if (Array.isArray(users) && users.length > 0) {
    for (let a of users) {
      let newNotification = new Notification();
      const getUserPermissions = `SELECT * FROM notification_preferences where user_id = ${a} and organization_id = ${orgId};`;
      const getData = await entityManager.query(getUserPermissions);
      let push = undefined;
      if (getData.length !== 0) {
        [{ push }] = getData;
      }
      if (push !== undefined) {
        const parsePush = JSON.parse(push);
        const notificationConstants = Object.keys(parsePush);
        if (
          notificationConstants.includes(titleName2) ||
          notificationConstants.includes(newTitle)
        ) {
          newNotification.title = title;
          newNotification.body = body;
          newNotification.user = a;
          newNotification.status = 'read';
          newNotification.sourceId = source;
          notifications.push(newNotification);
        }
      } else {
        newNotification.title = title;
        newNotification.body = body;
        newNotification.user = a;
        newNotification.status = 'read';
        newNotification.sourceId = source;
        notifications.push(newNotification);
      }
    }
  }
  // await Notification.save(notifications);
  // console.log('Success notification');
}

export async function insertINTONotificationUpdate(
  title: string,
  body: string,
  users: User[],
  orgId: number,
  key: string,
  source = null,
  additional_source = null,
): Promise<void> {
  try {
    if (key === 'ATTENDANCE_PUSH' || key === 'EXPENDITURE_PUSH') {
      for (let userid of users) {
        const userIdIs: any = userid;
        const userData = await User.findOne({ id: userIdIs });
        let newNotification = new Notification();
        newNotification.title = title;
        newNotification.body = body;
        newNotification.user = userData;
        newNotification.status = 'unread';
        newNotification.sourceId = source;
        newNotification.additional_source = additional_source;
        await newNotification.save();
      }
    } else if (users) {
      for (let userid of users) {
        const userPresenentCheck = await NotificationPreferences.findOne({
          where: { user: userid },
        });
        if (userPresenentCheck) {
          if (userPresenentCheck?.push) {
            const keys = Object.keys(userPresenentCheck?.push);
            const check = keys.includes(key);
            const userIdIs: any = userid;
            if (check) {
              const userData = await User.findOne({ id: userIdIs });
              let newNotification = new Notification();
              newNotification.title = title;
              newNotification.body = body;
              newNotification.user = userData;
              newNotification.status = 'unread';
              newNotification.sourceId = source;
              newNotification.additional_source = additional_source;
              await newNotification.save();
            }
          }
        }
      }
    }
  } catch (err) {
    console.log(err);
  }
}

export async function getUserNamewithUserId(userId: number) {
  const entityManager = getManager();
  const userQuery = `SELECT full_name FROM user where id=${userId}`;
  const getUserName = await entityManager.query(userQuery);
  const userName = getUserName[0].full_name;
  return userName;
}

export async function getAdminEmailssWithClientId(clientId: number) {
  const entityManager = getManager();
  const query = `SELECT u.id, u.email, u.full_name
  FROM client AS c
  INNER JOIN organization AS o
    ON c.organization_id = o.id
  INNER JOIN role AS r
    ON o.id = r.organization_id
  INNER JOIN user AS u
    ON r.id = u.role_id AND u.organization_id = o.id
  WHERE c.id=${clientId} AND r.name = 'Admin'`;
  const getAdmins = await entityManager.query(query);
  const userList = [];
  for (let i of getAdmins) {
    userList.push({
      id: i.id,
      email: i.email,
      fullName: i.full_name,
    });
  }
  return userList;
}

export async function getOrgNameBasedonClientId(clientId: number) {
  const entityManager = getManager();
  const getClientQuery = `SELECT organization_id FROM client WHERE id=${clientId}`;
  const orgIdCall = await entityManager.query(getClientQuery);
  const orgId = orgIdCall[0].organization_id;
  const getOrganizationQuery = `SELECT primary_contact_full_name FROM organization WHERE id=${orgId}`;
  const orgNameCall = await entityManager.query(getOrganizationQuery);
  const orgName = orgNameCall[0].primary_contact_full_name;
  return orgName;
}

export async function getAdminIdsWithClientId(clientId: number) {
  const entityManager = getManager();
  const getClientQuery = `SELECT organization_id FROM client WHERE id=${clientId}`;
  const getId = await entityManager.query(getClientQuery);
  const org = getId[0]?.organization_id;
  const getRoleQuery = `SELECT id from role where organization_id=${org} and name='Admin'`;
  const getRoleId = await entityManager.query(getRoleQuery);
  const role_id = getRoleId[0].id;
  const getUserQuery = `SELECT id FROM user where organization_id=${org} and role_id = ${role_id} and type = 'ORGANIZATION'`;
  const getUser = await entityManager.query(getUserQuery);
  const userList = [];
  for (let i of getUser) {
    userList.push(i.id);
  }
  return userList;
}

export async function getAdminIdsWithClientGroupId(clientId: number) {
  const entityManager = getManager();
  const getClientQuery = `SELECT organization_id FROM client_group WHERE id=${clientId}`;
  const getId = await entityManager.query(getClientQuery);
  const org = getId[0]?.organization_id;
  const getRoleQuery = `SELECT id from role where organization_id=${org} and name='Admin'`;
  const getRoleId = await entityManager.query(getRoleQuery);
  const role_id = getRoleId[0].id;
  const getUserQuery = `SELECT id FROM user where organization_id=${org} and role_id = ${role_id} and type = 'ORGANIZATION'`;
  const getUser = await entityManager.query(getUserQuery);
  const userList = [];
  for (let i of getUser) {
    userList.push(i.id);
  }
  return userList;
}

export const getTaskLeaderDetails = async (taskId: number) => {
  const entityManager = getManager();
  // const taskleaderQuery = `SELECT * FROM user WHERE id=${taskId}`;
  const taskLeaderQuery = `
   SELECT u.*
    FROM task t
    LEFT JOIN task_task_leader_user tlu ON t.id = tlu.task_id
    LEFT JOIN user u ON u.id = tlu.user_id
    WHERE t.id = ${taskId};
  `;
  const taskLeaderDetails = await entityManager.query(taskLeaderQuery);
  return taskLeaderDetails;
};

export const getLoginUser = async (userId: number) => {
  const entityManager = getManager();
  const userDetailsQuery = `SELECT * FROM user WHERE id=${userId}`;
  const userDetails = await entityManager.query(userDetailsQuery);
  return userDetails;
};

// export const getUserIDs = async (taskId: number): Promise<User[]> => {
//   const entityManager = getManager()
//   const sql = `select user_id from task_members_user where task_id=${taskId}`;
//   let members = await entityManager.query(sql);
//   const userIDs: User[] = members.map((row: { user_id: any; }) => row.user_id);
//   //console.log(userIDs);
//   return userIDs;
// };

// export async function insertINTOnotification(title: string, body: string, users: User[], orgId: number): Promise<void> {
//   const titleName = title.split(" ");
//   titleName.push("PUSH")
//   const titleName2 = titleName.join("_").toUpperCase();

//   const entityManager = getManager()
//   let notifications = [];
//   for (let a of users) {
//     let newNotification = new Notification();
//     const getUserPermissions = `SELECT * FROM notification_preferences where user_id = ${a} and organization_id = ${orgId};`
//     const getData = await entityManager.query(getUserPermissions);
//     let push = undefined;
//     if (getData.length !== 0) {
//       [{ push }] = getData;
//     };
//     if (push !== undefined) {
//       console.log(push);
//       const parsePush = JSON.parse(push);

//       const notificationConstants = Object.keys(parsePush);

//       if (notificationConstants.includes(titleName2)) {
//         console.log( titleName2)
//         newNotification.title = title;
//         newNotification.body = body;
//         newNotification.user = a;
//         notifications.push(newNotification);
//       }
//     } else {
//       newNotification.title = title;
//       newNotification.body = body;
//       newNotification.user = a;
//       notifications.push(newNotification);

//     }

//   }
//   await Notification.save(notifications);
//   console.log('Success notification1');
// }

export async function getAdminIds(orgId: number) {
  const Admins = 'Admin';
  const adminsql = 'SELECT id FROM role WHERE organization_id = ? AND name = ?';
  const entityManager = getManager();
  const admin = await entityManager.query(adminsql, [orgId, Admins]);
  const [{ id: adminId }] = admin || '';
  const adminNameSql = 'SELECT id from user where role_id= ?';
  const adminName = await entityManager.query(adminNameSql, [adminId]);
  const fullAdminArray: User[] = adminName.map((name) => name.id);
  return fullAdminArray;
}

// export async function insertINTOnotificationForApproval(title: string, body: () => string, users: User[]): Promise<void> {

//   let notifications = [];
//   for (let a of users) {
//     let newNotification = new Notification();
//     newNotification.title = title;
//     newNotification.body = body()
//     newNotification.user = a;
//     notifications.push(newNotification);
//   }
//   await Notification.save(notifications);
//   console.log('Success notification');
// }

// export async function getAdminIdsWithClientId(clientId: number) {
//   const entityManager = getManager()
//   // get organization id
//   const query = `SELECT u.id
//   FROM client AS c
//   INNER JOIN organization AS o
//     ON c.organization_id = o.id
//   INNER JOIN role AS r
//     ON o.id = r.organization_id
//   INNER JOIN user AS u
//     ON r.id = u.role_id AND u.organization_id = o.id
//   WHERE c.id=${clientId} AND r.name = 'Admin'`
//   const getAdmins = await entityManager.query(query)
//   const userList = []
//   for (let i of getAdmins) {
//     userList.push(i.id)
//   }

//   return userList
// }

// export async function getAdminEmailssWithClientId(clientId: number) {
//   const entityManager = getManager()
//   // get organization id

//   const query = `SELECT u.email, u.full_name
//   FROM client AS c
//   INNER JOIN organization AS o
//     ON c.organization_id = o.id
//   INNER JOIN role AS r
//     ON o.id = r.organization_id
//   INNER JOIN user AS u
//     ON r.id = u.role_id AND u.organization_id = o.id
//   WHERE c.id=${clientId} AND r.name = 'Admin'`
//   const getAdmins = await entityManager.query(query)

//   const userList = []
//   for (let i of getAdmins) {
//     userList.push({
//       email: i.email,
//       fullName: i.full_name
//     })
//   };

//   return userList
// }

// export async function getOrgNameBasedonClientId(clientId: number) {
//   const entityManager = getManager()
//   // get organization id

//   const query = `SELECT organization_id FROM client WHERE id=${clientId}`
//   const orgIdCall = await entityManager.query(query)
//   const orgId = orgIdCall[0].organization_id

//   const query1 = `SELECT primary_contact_full_name FROM organization WHERE id=${orgId}`
//   const orgNameCall = await entityManager.query(query1)
//   const orgName = orgNameCall[0].primary_contact_full_name

//   return orgName

// }

export const getClientIdBasedOnTaskId = async (taskId: number) => {
  const client = await Task.findOne({
    where: { id: taskId },
    relations: ['client', 'organization', 'clientGroup'],
  });
  return client;
};

export const getAllOrganizationUsersBasedOnClientId = async (clientId: number) => {
  const client = await Client.findOne({ where: { id: clientId }, relations: ['organization'] });
  const orgId = client?.organization?.id;
  if (orgId) {
    try {
      const allUsers = await User.find({
        where: { organization: orgId, type: UserType.ORGANIZATION },
      });
      return allUsers;
    } catch (error) {
      console.error(error);
    }
  }
};

export const getAllOrganizationUsersBasedOnClientGroupId = async (clientId: number) => {
  const client = await ClientGroup.findOne({
    where: { id: clientId },
    relations: ['organization'],
  });
  const orgId = client?.organization?.id;
  if (orgId) {
    try {
      const allUsers = await User.find({
        where: { organization: orgId, type: UserType.ORGANIZATION },
      });
      return allUsers;
    } catch (error) {
      console.error(error);
    }
  }
};

export const getAllOrganizationUsersBasedOnOrganizationId = async (orgId: number) => {
  if (orgId) {
    try {
      const allUsers = await User.find({
        where: { organization: orgId, type: UserType.ORGANIZATION },
      });
      return allUsers;
    } catch (error) {
      console.error(error);
    }
  }
};

export const getAllOrganizationUsersBasedOnUserId = async (userId: number) => {
  const user = await User.findOne({ where: { id: userId } });
  const orgId = user?.organization?.id;
  if (orgId) {
    try {
      const allUsers = await User.find({
        where: { organization: orgId, type: UserType.ORGANIZATION },
      });
      return allUsers;
    } catch (error) {
      console.error(error);
    }
  }
};

export const getAdminIDsBasedOnOrganizationId = async (organizationId: number) => {
  const entityManager = getManager();
  if (organizationId !== undefined) {
    const query1 = `SELECT id from role where organization_id=${organizationId} and name='Admin'`;
    const getRoleId = await entityManager.query(query1);

    if (getRoleId[0] !== undefined) {
      const roleId = getRoleId[0].id;
      const userId = `SELECT id FROM user where organization_id=${organizationId} and role_id = ${roleId}`;
      const getOrganizationId = await entityManager.query(userId);
      const userList = [];
      for (let i of getOrganizationId) {
        userList.push(i.id);
      }
      return userList;
    } else {
      const userId = `SELECT id FROM user where organization_id=${organizationId} and type = 'ORGANIZATION'`;
      const getOrganizationId = await entityManager.query(userId);
    }
  }
};

// export const getAdminEmailsBasedOnOrganizationId = async (organizationId: number) => {
//   const entityManager = getManager()
//   if (organizationId !== undefined) {
//     const query1 = `SELECT id from role where organization_id=${organizationId} and name='Admin'`
//     const getRoleId = await entityManager.query(query1)

//     if (getRoleId[0] !== undefined) {
//       const roleId = getRoleId[0].id

//       const user_id = `SELECT email,full_name FROM user where organization_id=${organizationId} and role_id = ${roleId}`

//       const getOrganizationId = await entityManager.query(user_id)

//       const userList = []
//       for (let i of getOrganizationId) {
//         userList.push({
//           email: i.email,
//           fullName: i.full_name
//         })
//       }

//       return userList
//     } else {
//       const userId = `SELECT id FROM user where organization_id=${organizationId}`
//       const getOrganizationId = await entityManager.query(userId)

//     }

//   }

// }

export const fetchRedirectUrl = async (url: string) => {
  try {
    const response = await axios.get(url);
    if (response.status === 200) {
      const finalUrl = response.request.res.responseUrl;
      // console.log(response?.request?.res);
      // console.log(finalUrl);
      return finalUrl;
    } else {
      return '';
      // console.error('Failed to fetch redirect URL');
    }
  } catch (error) {
    console.error('Error fetching redirect URL:', error);
  }
};

export const getFileDetailsById = async (fileId: string, token: any) => {
  try {
    const url = `https://graph.microsoft.com/v1.0/me/drive/items/${fileId}?expand=thumbnails`;
    const headers = {
      'Authorization': `Bearer ${token.accessToken}`,
      'Content-Type': 'application/json',
    };

    const response = await axios.get(url, { headers });

    return response.data;
  } catch (error) {
    console.error('Error:', error.response.data);
    throw new InternalServerErrorException('Failed to get file details');
  }
};

export function convertMillisecondsToHours(milliseconds: number, precision: number = 2): string {
  const hours = milliseconds / (1000 * 60 * 60);
  return `${hours.toFixed(precision)} hrs`;
}

export function convertMillisecToHrsAndMins(milliseconds: number): string {
  const totalMinutes = Math.floor(milliseconds / (1000 * 60));
  const hours = Math.floor(totalMinutes / 60);
  const minutes = totalMinutes % 60;

  const paddedHours = hours.toString().padStart(2, '0');
  const paddedMinutes = minutes.toString().padStart(2, '0');

  return `${paddedHours}:${paddedMinutes} hrs`;
}

export function convertMillisecToDateHrsAndMins(milliseconds: number): string {
  const totalMinutes = Math.floor(milliseconds / (1000 * 60));
  const hours = Math.floor(totalMinutes / 60);
  const minutes = totalMinutes % 60;

  const paddedHours = hours.toString().padStart(2, '0');
  const paddedMinutes = minutes.toString().padStart(2, '0');

  return `${paddedHours}:${paddedMinutes} hrs`;
}
