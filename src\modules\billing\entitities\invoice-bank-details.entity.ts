import { AccountType } from 'src/modules/organization/entities/bank-account.entity';
import Storage from 'src/modules/storage/storage.entity';
import { BaseEntity, Column, Entity, OneToOne, PrimaryGeneratedColumn } from 'typeorm';

@Entity()
class InvoiceBankDetails extends BaseEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  bankName: string;

  @Column()
  branchName: string;

  @Column()
  accountNumber: string;

  @Column()
  ifscCode: string;

  @Column({ nullable: true })
  upiId: string;

  @Column({ nullable: true })
  accountName: string;

  @Column({ type: 'enum', enum: AccountType, })
  accountType: AccountType;

  @Column({ nullable: true })
  upiAttachment: string;

  @OneToOne(() => Storage, (storage) => storage.invoiceBankAttachement, { cascade: true })
  invoiceBankAttachement: Storage;
}

export default InvoiceBankDetails;
