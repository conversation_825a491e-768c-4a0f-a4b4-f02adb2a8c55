import { Organization } from 'src/modules/organization/entities/organization.entity';
import {
  BaseEntity,
  Column,
  CreateDateColumn,
  Entity,
  ManyToOne,
  OneToMany,
  OneToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import ClientGroupBroadcast from '../communication/entity/client-group-broadcast.entity';
import { BroadcastChannel } from 'worker_threads';
import BroadcastEmailTemplates from '../communication/entity/broadcast-email-templates-entity';

@Entity()
class Label extends BaseEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  name: string;

  @Column()
  color: string;

  @Column({ default: false })
  defaultOne: boolean;

  @ManyToOne(() => Organization, (organization) => organization.labels)
  organization: Organization;

  @CreateDateColumn()
  createdAt: string;

  @UpdateDateColumn()
  updatedAt: string;

  @OneToOne(() => ClientGroupBroadcast, (clientGroupBroadcast) => clientGroupBroadcast.label)
  clientGroupBroadcast: ClientGroupBroadcast;

  @OneToOne(() => BroadcastEmailTemplates, (broadcastEmailTemplates) => broadcastEmailTemplates.label)
  broadcastEmailTemplates: BroadcastEmailTemplates;

}

export default Label;
