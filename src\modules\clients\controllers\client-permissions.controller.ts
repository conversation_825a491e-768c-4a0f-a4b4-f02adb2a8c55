import {
    Body,
    Controller,
    Delete,
    Get,
    Param,
    ParseIntPipe,
    Post,
    Put,
    Query,
    Request,
    UseGuards,
} from '@nestjs/common';
import { JwtAuthGuard } from 'src/modules/users/jwt/jwt-auth.guard';
import { ClientPermissionsService } from '../services/client-permissions.service';
import { User } from 'src/modules/users/entities/user.entity';
import Client from '../entity/client.entity';
import { ClientPermission } from '../entity/client-permission.entity';
import { createQueryBuilder, In } from 'typeorm';

@Controller('client-permissions')
export class ClientPermissionsController {
    constructor(private service: ClientPermissionsService) { }

    @UseGuards(JwtAuthGuard)
    @Get('/permissions')
    get(@Request() req: any, @Query() query: any) {
        const { userId } = req.user;
        return this.service.getClientPermissions(userId, query);
    }

    @UseGuards(JwtAuthGuard)
    @Put(':id')
    async updateRole(@Request() req: any, @Param() { id }, @Body() { name, description, permissions, active }) {  
      const client = await Client.findOne(id);

      let perms = await ClientPermission.find({ where: { id: In(permissions) } });
      client.permissions = perms;
  
      await client.save();
      return client;
    }  

    @UseGuards(JwtAuthGuard)
    @Get(':roleId')
    async getRole(@Request() req: any,@Param('roleId', ParseIntPipe) roleId: number) {
      const { userId } = req.user;
      let user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
      let client = await Client.findOne({ where: { id: roleId,  organization: user?.organization?.id }, relations: ['permissions'] });
      return client;
    }

}