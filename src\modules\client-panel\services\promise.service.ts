import { BadRequestException, Injectable } from "@nestjs/common";
import Client from "src/modules/clients/entity/client.entity";
import { User } from "src/modules/users/entities/user.entity";
// import { GstrPromise } from "../entity/promise.entity";
import { <PERSON><PERSON>, CronExpression } from "@nestjs/schedule";
import { In, createQueryBuilder, getConnection, getManager, getRepository } from "typeorm";
import axios from "axios";
// import GstrRegister, { RegistrationType } from "../entity/gstr-register.entity";
import * as moment from 'moment';
import { Organization } from "src/modules/organization/entities/organization.entity";
// import { ReturnsData } from "../entity/returns-data.entity";
// import FindReturnsDto from "../dto/find-returns.dto";
import { getCurrentFinancialYear, incrementFinancialYear } from "src/utils/datesFormation";
// import SyncClinetsDto from "../dto/sync-clients.dto";
import { GstrRegisterService } from "./gstr-register.service";
import FindReturnsDto from "src/modules/gstr-register/dto/find-returns.dto";
import SyncClinetsDto from "src/modules/gstr-register/dto/sync-clients.dto";
import GstrRegister, { RegistrationType } from "src/modules/gstr-register/entity/gstr-register.entity";
import { ReturnsData } from "src/modules/gstr-register/entity/returns-data.entity";


@Injectable()
export class PromiseService {
    constructor(private gstrService: GstrRegisterService) { }

    async getReturns(data: FindReturnsDto, userId: number) {
        let user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
        const results = [];
        async function fetchWithRetry(item, financialYear) {
            const headers = { Authorization: process.env.FYN_AUTH };
            const url = `${process.env.FYN_RETURNS}/${item.gstNumber}/${financialYear}`;
            try {
                const response = await axios.get(url, { headers });
                const reqReturn = response?.data?.EFiledlist?.find((r) => {
                    const month = r.ret_prd.slice(0, 2);
                    const year = r.ret_prd.slice(-4);
                    const [startYear, endYear] = item?.fixedFy.split('-').map(y => parseInt(y, 10));
                    const normalizedEndYear = endYear < 100 ? 2000 + endYear : endYear;
                    const comparisonYear = parseInt(item?.month) >= 4 && parseInt(item?.month) <= 12 ? startYear : normalizedEndYear;
                    if (item.type === RegistrationType.REGULAR_TAXPAYER) {
                        if (item.rtntype === "GSTR4" || item.rtntype === "GSTR9" || item.rtntype === "GSTR9C") {
                            return r.rtntype === item.rtntype;
                        } else if (item.rtntype === "CMP08") {
                            return month === item?.quarter && r.rtntype === item.rtntype && comparisonYear === parseInt(year);
                        } else {
                            return month === item?.month && r.rtntype === item.rtntype && comparisonYear === parseInt(year);
                        }
                    } else {
                        return month === item?.month && r.rtntype === item.rtntype && comparisonYear === parseInt(year);
                    }
                });
                if (response?.data?.EFiledlist?.length > 0) {
                    const count = await ReturnsData.count({
                        where: { gstrRegister: item.id, financialYear: financialYear }
                    });

                    if (count === 0) {
                        try {
                            const returnsData = [];
                            await getManager().transaction(async transactionalEntityManager => {
                                for (const i of response?.data?.EFiledlist) {
                                    const returnData = new ReturnsData();
                                    returnData.gstrRegister = item.id;
                                    returnData.financialYear = financialYear;
                                    returnData.valid = i.valid;
                                    returnData.mof = i.mof;
                                    returnData.dof = i.dof;
                                    returnData.rtntype = i.rtntype;
                                    returnData.retPrd = i.ret_prd;
                                    returnData.arn = i.arn;
                                    returnData.status = i.status;
                                    returnsData.push(returnData);
                                }
                                if (returnsData.length > 0) {
                                    await transactionalEntityManager.insert(ReturnsData, returnsData);
                                };
                            });

                        } catch (error) {
                            console.error("Failed to save returns data:", error);
                        }

                    } else if (count === response?.data?.EFiledlist.length) {
                    }
                    else {
                        const incomingARNs = response.data.EFiledlist.map(item => item.arn);
                        const existingARNs = await ReturnsData.find({
                            select: ["arn"],
                            where: { financialYear: financialYear, arn: In(incomingARNs) }
                        });
                        const existingARNSet = new Set(existingARNs.map(item => item.arn));
                        const newRecords = response.data.EFiledlist.filter(item => !existingARNSet.has(item.arn));
                        if (newRecords.length > 0) {
                            try {
                                const returnsData = [];
                                await getManager().transaction(async transactionalEntityManager => {
                                    for (const i of newRecords) {
                                        const returnData = new ReturnsData();
                                        returnData.gstrRegister = item.id;
                                        returnData.financialYear = financialYear;
                                        returnData.valid = i.valid;
                                        returnData.mof = i.mof;
                                        returnData.dof = i.dof;
                                        returnData.rtntype = i.rtntype;
                                        returnData.retPrd = i.ret_prd;
                                        returnData.arn = i.arn;
                                        returnData.status = i.status;
                                        returnsData.push(returnData);
                                    }
                                    if (returnsData.length > 0) {
                                        await transactionalEntityManager.insert(ReturnsData, returnsData);
                                    }
                                });

                            } catch (error) {
                                console.error("Failed to save returns data:", error);
                            }

                        }
                    }
                } else {
                }
                if (!reqReturn && financialYear < getCurrentFinancialYear() && financialYear !== getCurrentFinancialYear()) {
                    const nextYear = incrementFinancialYear(financialYear);
                    return fetchWithRetry(item, nextYear);
                } else if (!reqReturn && financialYear === getCurrentFinancialYear()) {
                    return null
                }
                else {
                    return reqReturn;
                }
            } catch (e) {
                console.error(`Failed to fetch data for GST number: ${item.gstNumber} in year: ${financialYear}`, e);
                throw e; // Depending on your error handling, you might want to continue or stop.
            }
        };
        const gstrRegisterIds = data.clientDetails.map(item => item.id);
        let comparisonYear: number
        if (data.month) {
            const [startYear, endYear] = data.financialYear.split('-').map(y => parseInt(y, 10));
            const normalizedEndYear = endYear < 100 ? 2000 + endYear : endYear;
            if (data.month === "03") {
                comparisonYear = parseInt(data.month) >= 4 && parseInt(data.month) <= 12 ? startYear : normalizedEndYear - 1;

            } else {
                comparisonYear = parseInt(data.month) >= 4 && parseInt(data.month) <= 12 ? startYear : normalizedEndYear;
            }
        }

        let getrClients = getConnection()
            .createQueryBuilder(GstrRegister, 'gstrRegister')
            .addSelect(['gstrRegister.id'])
        if (["GSTR1", "GSTR2X", "GSTR3B", "GSTR7", "GSTR8"].includes(data.rtntype)) {
            getrClients.innerJoin('gstrRegister.returnsData', 'returnsData',
                'returnsData.financialYear >= :financialYear AND returnsData.rtntype=:rtntype AND  LEFT(ret_prd, 2) =:selectedMonth AND RIGHT(ret_prd, 4)=:comparisonYear',
                {
                    financialYear: data.financialYear,
                    rtntype: data.rtntype,
                    selectedMonth: data.month,
                    comparisonYear: comparisonYear
                })
        } else if (["CMP08"].includes(data.rtntype)) {
            getrClients.innerJoin('gstrRegister.returnsData', 'returnsData',
                'returnsData.financialYear >= :financialYear AND returnsData.rtntype=:rtntype AND  LEFT(ret_prd, 2) =:selectedQuarter AND RIGHT(ret_prd, 4)=:comparisonYear',
                {
                    financialYear: data.financialYear,
                    rtntype: data.rtntype,
                    selectedQuarter: data.quarter,
                    comparisonYear: comparisonYear
                })
        } else if (["GSTR4", "GSTR9", "GSTR9C"].includes(data.rtntype)) {
            getrClients.innerJoin('gstrRegister.returnsData', 'returnsData',
                'returnsData.financialYear >=:financialYear AND returnsData.rtntype=:rtntype AND RIGHT(ret_prd,4)=:comparisonYear',
                {
                    financialYear: data.financialYear,
                    rtntype: data.rtntype,
                    comparisonYear: comparisonYear
                }
            )
        }

        // .innerJoin('gstrRegister.returnsData', 'returnsData',
        //     'returnsData.financialYear >= :financialYear AND returnsData.rtntype=:rtntype AND  LEFT(ret_prd, 2) =:selectedMonth ',
        //     {
        //         financialYear: data.financialYear,
        //         rtntype: data.rtntype, selectedMonth: data.month
        //     })
        getrClients.where('gstrRegister.organization = :organization', { organization: user.organization.id })
            .andWhere('gstrRegister.id IN (:...gstrRegisterIds)', { gstrRegisterIds })
            .andWhere('gstrRegister.registrationType = :type', { type: data.type });
        let result = await getrClients.getMany();

        // console.log(result);
        const existingGstRegistersSet = new Set(result.map(item => item.id));
        const newGstrRecords = data.clientDetails.filter(item => !existingGstRegistersSet.has(item.id));

        for (const item of newGstrRecords) {
            const result = await fetchWithRetry({ ...item, rtntype: data.rtntype, type: data.type, month: data.month, fixedFy: data.financialYear }, data.financialYear);
            results.push(result);
        }
        return results;

    }

    async getSingleClientReturns(data: any, userId: number) {
        const existingGstrRegister = await GstrRegister.findOne({ where: { client: data?.clientId } });
        if (existingGstrRegister) {
            throw new BadRequestException('Client Already Synced');
        }

        let user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
        let client = await Client.findOne({ id: data?.clientId });
        let gstrRegister = new GstrRegister();
        gstrRegister.client = client;
        gstrRegister.registrationType = data.type;
        gstrRegister.organization = user.organization;
        await gstrRegister.save();

        if (!data.syncAll) {
            try {
                const headers = { Authorization: process.env.FYN_AUTH };
                const url = `${process.env.FYN_RETURNS}/${data.gstNumber}/${data.financialYear}`;
                const response = await axios.get(url, { headers });
                if (response?.data?.EFiledlist?.length > 0) {
                    try {
                        const returnsData = [];
                        await getManager().transaction(async transactionalEntityManager => {
                            for (const i of response?.data?.EFiledlist) {
                                const returnData = new ReturnsData();
                                returnData.gstrRegister = gstrRegister;
                                returnData.financialYear = data.financialYear;
                                returnData.valid = i.valid;
                                returnData.mof = i.mof;
                                returnData.dof = i.dof;
                                returnData.rtntype = i.rtntype;
                                returnData.retPrd = i.ret_prd;
                                returnData.arn = i.arn;
                                returnData.status = i.status;
                                returnsData.push(returnData);
                            }
                            if (returnsData.length > 0) {
                                await transactionalEntityManager.insert(ReturnsData, returnsData);
                            }
                        });

                    } catch (error) {
                        console.error("Failed to save returns data:", error);
                    }

                } else {
                    console.log(response?.data?.error?.message)
                }

            } catch (e) {
                console.error(e);
            }

        } else {
            this.gstrService.syncClients({ clients: [data.clientId], financialYear: data.selectedYear, })
        }

    }

    async syncSingleClientReturns(data: any, userId: number) {
        const gstRegister = await GstrRegister.findOne(data.clientId);

        const [startYear, end] = data.financialYear.split('-');
        const endYear = `20${end}`;
        const months = ['04', '05', '06', '07', '08', '09', '10', '11', '12', '01', '02', '03'];
        const quarters = ['06', '09', '12', '03'];
        const years = ['03'];

        const financialYearMonths = months.map(month => {
            if (month === '01' || month === '02' || month === '03') {
                return month + endYear;
            } else {
                return month + startYear;
            }
        });

        const financialYearQuarter = quarters.map(quarter => {
            if (quarter === '03') {
                return quarter + endYear;
            } else {
                return quarter + startYear;
            }
        });

        const financialYearYers = years.map(year => {
            return year + endYear;
        });


        const gstrRegister = await GstrRegister.findOne({ where: { client: data.clientId } });
        let result = [];
        let difference: string[];
        if (['GSTR1', 'GSTR2X', 'GSTR3B', 'GSTR7', 'GSTR8'].includes(data.rtntype)) {
            difference = financialYearMonths.filter(item => !data.filledMonths.includes(item));
            for (const month of difference) {
                result = await fetchWithRetry({ id: gstrRegister.id, rtntype: data.rtntype, type: data.type, month, gstNumber: data.gstNumber, fixedFy: data.financialYear }, data.financialYear);
            }
        } else if (['CMP08'].includes(data.rtntype)) {
            difference = financialYearQuarter.filter(item => !data.filledMonths.includes(item));
            for (const month of difference) {
                result = await fetchWithRetry({ id: gstrRegister.id, rtntype: data.rtntype, type: data.type, month, gstNumber: data.gstNumber, fixedFy: data.financialYear }, data.financialYear);
            }
        } else if (['GSTR9', 'GSTR9C', 'GSTR4'].includes(data.rtntype)) {
            difference = financialYearYers
            for (const month of difference) {
                result = await fetchWithRetry({ id: gstrRegister.id, rtntype: data.rtntype, type: data.type, month, gstNumber: data.gstNumber, fixedFy: data.financialYear }, data.financialYear);
            }
        }

        return result;

        async function fetchWithRetry(item, financialYear) {
            const headers = { Authorization: process.env.FYN_AUTH };
            const url = `${process.env.FYN_RETURNS}/${item.gstNumber}/${financialYear}`;
            try {
                const response = await axios.get(url, { headers });
                const retPrds = response?.data?.EFiledlist.map((item: any) => item.ret_prd);
                difference = difference.filter(item => !retPrds.includes(item));
                const reqReturn = response?.data?.EFiledlist?.find((r) => {
                    const month = r.ret_prd.slice(0, 2);
                    const year = r.ret_prd.slice(-4);
                    const [startYear, endYear] = item?.fixedFy.split('-').map(y => parseInt(y, 10));
                    const normalizedEndYear = endYear < 100 ? 2000 + endYear : endYear;
                    const comparisonYear = parseInt(item?.month) >= 4 && parseInt(item?.month) <= 12 ? startYear : normalizedEndYear;
                    if (item.type === RegistrationType.REGULAR_TAXPAYER) {
                        if (item.rtntype === "GSTR4" || item.rtntype === "GSTR9" || item.rtntype === "GSTR9C") {
                            return r.rtntype === item.rtntype;
                        } else if (item.rtntype === "CMP08") {
                            return month === item?.quarter && r.rtntype === item.rtntype && comparisonYear === parseInt(year);
                        } else {
                            return month === item?.month && r.rtntype === item.rtntype && comparisonYear === parseInt(year);
                        }
                    } else {
                        return month === item?.month && r.rtntype === item.rtntype && comparisonYear === parseInt(year);
                    }
                });
                if (response?.data?.EFiledlist?.length > 0) {
                    const count = await ReturnsData.count({
                        where: { gstrRegister: item.id, financialYear: financialYear }
                    });

                    if (count === 0) {
                        try {
                            const returnsData = [];
                            await getManager().transaction(async transactionalEntityManager => {
                                for (const i of response?.data?.EFiledlist) {
                                    const returnData = new ReturnsData();
                                    returnData.gstrRegister = item.id;
                                    returnData.financialYear = financialYear;
                                    returnData.valid = i.valid;
                                    returnData.mof = i.mof;
                                    returnData.dof = i.dof;
                                    returnData.rtntype = i.rtntype;
                                    returnData.retPrd = i.ret_prd;
                                    returnData.arn = i.arn;
                                    returnData.status = i.status;
                                    returnsData.push(returnData);
                                }
                                if (returnsData.length > 0) {
                                    await transactionalEntityManager.insert(ReturnsData, returnsData);

                                }
                            });


                        } catch (error) {
                            console.error("Failed to save returns data:", error);
                        }

                    } else if (count === response?.data?.EFiledlist.length) {
                    }
                    else {
                        const incomingARNs = response.data.EFiledlist.map(item => item.arn);
                        const existingARNs = await ReturnsData.find({
                            select: ["arn"],
                            where: { financialYear: financialYear, arn: In(incomingARNs) }
                        });
                        const existingARNSet = new Set(existingARNs.map(item => item.arn));
                        const newRecords = response.data.EFiledlist.filter(item => !existingARNSet.has(item.arn));
                        if (newRecords.length > 0) {
                            try {
                                const returnsData = [];
                                await getManager().transaction(async transactionalEntityManager => {
                                    for (const i of newRecords) {
                                        const returnData = new ReturnsData();
                                        returnData.gstrRegister = item.id;
                                        returnData.financialYear = financialYear;
                                        returnData.valid = i.valid;
                                        returnData.mof = i.mof;
                                        returnData.dof = i.dof;
                                        returnData.rtntype = i.rtntype;
                                        returnData.retPrd = i.ret_prd;
                                        returnData.arn = i.arn;
                                        returnData.status = i.status;
                                        returnsData.push(returnData);
                                    }
                                    if (returnsData.length > 0) {
                                        await transactionalEntityManager.insert(ReturnsData, returnsData);
                                    }
                                });

                            } catch (error) {
                                console.error("Failed to save returns data:", error);
                            }
                        }
                    }
                } else {
                    console.log(response?.data?.error?.message);
                }
                if (!reqReturn && financialYear !== getCurrentFinancialYear()) {
                    const nextYear = incrementFinancialYear(financialYear);
                    return fetchWithRetry(item, nextYear);
                } else if (!reqReturn && financialYear !== getCurrentFinancialYear()) {
                    return null
                }
                else {
                    return reqReturn;
                }
            } catch (e) {
                console.error(`Failed to fetch data for GST number: ${item.gstNumber} in year: ${financialYear}`, e);
                throw e;
            };
        };
    };


}