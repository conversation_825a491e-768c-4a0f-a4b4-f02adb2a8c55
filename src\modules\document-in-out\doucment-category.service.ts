import { BadRequestException, ConflictException, Injectable } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { Event_Actions } from 'src/event-listeners/actions';
import { User } from 'src/modules/users/entities/user.entity';
import { createQueryBuilder, getRepository } from 'typeorm';
import Client from '../clients/entity/client.entity';
import DocumentCategory from './entity/document-category.entity';

@Injectable()
export class DocumentCategoryService {
  constructor(private eventEmitter: EventEmitter2) { }

  async create(userId: number, data: any) {
    let user = await User.findOne({
      where: { id: userId },
      relations: ['organization'],
    });
    data.name = data.name.trim();
    let documentCategoryExists = await createQueryBuilder(DocumentCategory, 'documentCategory')
      .leftJoin('documentCategory.organization', 'organization')
      .where('documentCategory.name = :name', { name: data.name })
      .andWhere('organization.id = :id', { id: user.organization.id })
      .getMany();
    if (documentCategoryExists.length) {
      throw new BadRequestException('Document Category is Already Exists !');
    }
    const documentCategory = new DocumentCategory();
    documentCategory.name = (' ' + data['name'])?.trim();
    documentCategory.type = data['type'];
    documentCategory.organization = user.organization;
    documentCategory['userId'] = user.id;

    await documentCategory.save();
    return documentCategory;
  }

  async get(userId: number, query: any) {
    const user = await User.findOne({
      where: { id: userId },
      relations: ['organization'],
    });

    const documentCategory = createQueryBuilder(DocumentCategory, 'documentCategory')
      .leftJoin('documentCategory.organization', 'organization')
      .where('organization.id = :organizationId', { organizationId: user.organization.id });

    if (query.search) {
      documentCategory.andWhere('lead.name like :search OR lead.email LIKE :search OR lead.mobileNumber LIKE :search', {
        search: `%${query.search}%`,
      });
    }

    let result = await documentCategory.getMany();
    return result;
  }

  // async update(id: number, data: CreateLeadDto, userId: number) {
  //   const lead = await Lead.findOne({ where: { id } });
  //   if (data.name.toLowerCase() !== lead.name.toLowerCase()) {
  //     let user = await User.findOne({
  //       where: { id: userId },
  //       relations: ['organization'],
  //     });

  //     data.name = data.name.trim();
  //     const status = "CONVERTED"
  //     if (data.status !== status.toLowerCase()) {
  //       let existingService = await Lead.findOne({
  //         where: {
  //           name: data.name, organization: user.organization
  //         },
  //       });
  //       if (existingService) {
  //         throw new ConflictException(
  //           'Lead with given display name already exists in the organization',
  //         );
  //       }

  //       let existingUser = await createQueryBuilder(Client, 'client')
  //         .leftJoin('client.organization', 'organization')
  //         .where('organization.id = :organization', { organization: user.organization.id })
  //         .andWhere('(client.displayName = :displayName)', {
  //           displayName: data.name,
  //         })
  //         .getOne();
  //       if (existingUser) {
  //         throw new ConflictException(
  //           'Lead | Client with the given Display Name already Exists in your Organization',
  //         );
  //       }
  //     }
  //   }
  //   lead.name = data.name;
  //   lead.email = data.email.trim();
  //   lead.mobileNumber = data.mobileNumber;
  //   lead.category = data.category;
  //   lead.subCategory = data.subCategory;
  //   lead.description = data.description.trim();
  //   lead.status = data.status;
  //   lead.countryCode = data.countryCode;
  //   lead['userId'] = userId;
  //   await lead.save();
  //   return lead;
  // }

  async delete(ids: number[]) {
    await getRepository(DocumentCategory)
    .createQueryBuilder()
    .delete()
    .where('id IN (:...ids)', { ids: ids })
    .execute();
    
    return { success: true };
  }
}
