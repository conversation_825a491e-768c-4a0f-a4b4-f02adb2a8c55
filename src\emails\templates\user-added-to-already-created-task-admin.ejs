<!DOCTYPE html>
<html>
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width,initial-scale=1" />
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Montserrat:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap"
      rel="stylesheet"
    />
  </head>

  <body style="margin: 0; padding: 0">
    <div style="background-color: aliceblue" align="center">
      <table
        style="
          background: #ffffff;
          width: 1200px;
          font-family: Montserrat;
          color: #121212;
          padding: 40px;
          margin: auto;
          width: 50%;
        "
      >
        <!-- <tr align="center">
          <td>
            <img
              src="https://vider.in/images/atom-modify.png"
              alt="vider"
              style="height: auto; width: 50%; display: block"
            />
          </td>
        </tr> -->
        <tr align="center">
          <td>
            <img
              src="https://vider.in/images/task-creation-atom.jpg"
              alt=""
              style="margin-top: 0px; width: 70%; height: auto; display: block"
            />
          </td>
        </tr>
        <tr>
          <td>
            <hr style="border: #1c34ff 2px solid" />
          </td>
        </tr>
        <tr>
          <td>
            <h1
              style="
                margin-top: 20px;
                height: auto;
                display: block;
                font-size: 35px;
                color: #182f53;
              "
            >
              Hello <%= adminName%>,
            </h1>
          </td>
        </tr>
        <tr>
          <td>
            <p style="margin-top: 5px; font-size: 17px; color: #121212">
              <strong><%= userName %></strong> added to an existing task
              <strong><%= taskName%></strong> for <strong><%= clientName%></strong>.
            </p>
            <p style="font-size: 17px; color: #121212">Here are the details: Task Details:</p>
            <p style="font-size: 17px; color: #121212">1. <strong>Task ID</strong>: <%=taskId%></p>
            <p style="font-size: 17px; color: #121212">
              2. <strong>Service Name</strong>: <%= serviceName%>
            </p>
            <p style="font-size: 17px; color: #121212">
              3. <strong>Client Name</strong>: <%=clientName%>
            </p>
            <p style="font-size: 17px; color: #121212">
              4. <strong>Due Date</strong>: <%= dueDate%>
            </p>
            <p style="font-size: 17px; color: #121212">
              5. <strong>Newly Added Users:</strong> <%= assignedUser%>
            </p>
            <p style="font-size: 17px; color: #121212">
              6. <strong>Existing Users :</strong> <%= existingUsers%>
            </p>
          </td>
        </tr>
        <tr>
          <td>
            <p style="font-size: 17px">
              Please review the information. Ensure you are updated with your emails to manage the
              task efficiently.
            </p>
          </td>
        </tr>
        <tr>
          <td>
            <p style="font-size: 17px">
              If you have any questions or concerns regarding this task, feel free to reach out to
              the assigned user or the task creator. Manage your responsibilities smarter, faster,
              and better!
            </p>
          </td>
        </tr>
           <tr>
        <td>
          <p style="margin-top: 3px; font-size: 17px">

            <span style="font-weight: bold; color: #aa1adc;">Disclaimer:</span>
            Please be aware that the preceding message is an automatically generated email originating from our system,
            and the associated mailbox is not actively monitored for incoming replies. Therefore, we kindly request that
            you refrain from attempting to respond directly to this notification. For any inquiries or assistance,
            please contact us via the mobile number or email address provided below. Thank you for your understanding.
        </td>
      </tr>

      <tr>
        <td>
          <hr style="border: #1C34FF 3px solid;" />
        </td>
      </tr>
    <tr>
        <td align="center">
          <h2><%= legalName %></h2>
         
        </td>
      </tr>
    <tr>  
        <td align="center">
          <table style="width: 210px">
      
        <tr>
          <td>
            <p style="margin-top: 3px; font-size: 17px">
  
              <span style="font-weight: bold">Address:</span>
              <%= adress %></td>
        </tr>
        <td align="center">
          <table style="width: 700px;">
            <tr align="center">
              <td align="center">
                <p style="margin-top: 3px; font-size: 17px">
                  
                </p>
              </td>
            </tr>
            <tr>
              <td align="center">
                <table style="margin: auto;">
                  <tr>
                    <td align="left" style="padding-right: 5px;">
                      <p style="margin-top: 3px; font-size: 17px;"><strong>Contact:</strong> <%=phoneNumber %></p>
                    </td>
                    <td align="right" style="padding-right: 10px;">
                      <p style="margin-top: 3px; font-size: 17px;">
                        |<strong> Email:</strong> <%=mail %></p>
                    </td>
                  </tr>
                </table>
              </td>
            </tr>
            <!-- <tr>
              <td align="center">
                <h2 style="margin: 0 auto;">Powered By: <img style="height: 30px;" src="https://jss-vider.s3.ap-south-1.amazonaws.com/vider_Logo.jpg" alt="" /></h2>
              </td>
            </tr>  -->
          </table>
        </td>
      </tr>
    </table>
  </div>
</body>

</html>