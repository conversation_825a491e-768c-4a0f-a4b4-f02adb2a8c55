import { Injectable } from '@nestjs/common';
import AddStageOfWorkDto from '../dto/add-stage-of-work.dto';
import StageOfWork from '../entity/stage-of-work.entity';
import Task from '../entity/task.entity';
import Storage, { StorageSystem } from 'src/modules/storage/storage.entity';
import { StorageService } from 'src/modules/storage/storage.service';
import Activity, { ActivityType } from 'src/modules/activity/activity.entity';
import { Event_Actions } from 'src/event-listeners/actions';
import { User } from 'src/modules/users/entities/user.entity';
import { OneDriveStorageService } from 'src/modules/ondrive-storage/onedrive-storage.service';
import { BharathStorageService } from 'src/modules/storage/bharath-storage.service';
import { GoogleDriveStorageService } from 'src/modules/ondrive-storage/googledrive-storage.service';

@Injectable()
export class StageOfWorkService {
  constructor(private storageService: StorageService,
    private oneDriveService: OneDriveStorageService,
    private googleDriveService: GoogleDriveStorageService,
    private bharathService: BharathStorageService

  ) { }
  async addStageOfWork(taskId: number, userId: number, data: AddStageOfWorkDto) {
    let user = await User.findOne({
      where: {
        id: userId,
      },
    });
    let storage: Storage;
    let task = await Task.findOne(taskId);
    let stageOfWork = new StageOfWork();
    stageOfWork.name = data.name.trim();
    stageOfWork.description = data.description.trim();
    stageOfWork.type = data.type;
    stageOfWork.referenceNumber = data.referenceNumber;
    stageOfWork.extraAttributes = data.extraAttributes;
    stageOfWork.attachmentFile = data.attachmentFile;
    if (data.storage) {
      storage = await this.storageService.addAttachements(userId, data.storage);
      // stageOfWork.storage = storage;
    }
    stageOfWork.task = task;
    const sw = await stageOfWork.save();
    if (storage) {
      storage.stageOfWork = sw;
      await storage.save();
    }

    let taskactivity = new Activity();
    taskactivity.action = Event_Actions.MILESTONE_CREATED;
    taskactivity.actorId = userId;
    taskactivity.type = ActivityType.TASK;
    taskactivity.typeId = taskId;
    taskactivity.remarks = `MileStone "${stageOfWork.name}" Created by ${user.fullName}`;
    await taskactivity.save();

    return stageOfWork;
  }

  async updateStageOfWork(id: number, userId: number, data: AddStageOfWorkDto) {

    let user = await User.findOne({
      where: {
        id: userId,
      },
    });

    let stageOfWork = await StageOfWork.findOne({
      where: {
        id,
      },
      relations: ['storage']
    });
    let storage: Storage;
    if (data?.storage) {
      if (stageOfWork?.storage?.id) {
        if (data?.storage.name !== stageOfWork?.storage?.name) {
          if (stageOfWork?.storage.storageSystem === StorageSystem.AMAZON) {
            this.storageService.deleteAwsFile(stageOfWork?.storage?.file)
          } else if (stageOfWork?.storage?.storageSystem === StorageSystem.MICROSOFT) {
            this.oneDriveService.deleteOneDriveFile(userId, stageOfWork?.storage?.fileId);
          } else if (stageOfWork?.storage?.storageSystem === StorageSystem.BHARATHCLOUD) {
            this.bharathService.deleteB3File(userId, stageOfWork?.storage?.file)
          }
        };

        storage = await Storage.findOne({ where: { id: stageOfWork?.storage?.id } });
        storage.fileType = data?.storage.fileType;
        storage.fileSize = data?.storage.fileSize;
        storage.name = data?.storage.name;
        storage.file = data?.storage.upload;
        storage.storageSystem = data?.storage?.storageSystem
        storage.webUrl = data?.storage?.webUrl;
        storage.downloadUrl = data?.storage?.download;
        storage.fileId = data?.storage?.fileId;
        storage.authId = user.organization.id;
        // stageOfWork.storage = storage;
      } else {

        storage = await this.storageService.addAttachements(userId, data?.storage);
        // stageOfWork.storage = storage;
      }
    } else {
      if (stageOfWork?.storage?.id) {
        const existingStorage = await Storage.findOne({ where: { id: stageOfWork?.storage?.id } });
        const existing = await existingStorage.remove();
        if (existing) {
          if (existing.storageSystem === StorageSystem.AMAZON) {
            this.storageService.deleteAwsFile(existing.file)
          } else if (existing.storageSystem === StorageSystem.MICROSOFT) {
            this.oneDriveService.deleteOneDriveFile(userId, existing.fileId);
          } else if (existing.storageSystem === StorageSystem.BHARATHCLOUD) {
            this.bharathService.deleteB3File(userId, existing.file)
          }
        }
        stageOfWork.storage = null;
      }
    }
    stageOfWork.name = data.name.trim();
    stageOfWork.description = data.description.trim();
    stageOfWork.type = data.type;
    stageOfWork.referenceNumber = data.referenceNumber;
    stageOfWork.referenceNumberValue = data.referenceNumberValue;
    stageOfWork.extraAttributes = data.extraAttributes;
    stageOfWork.status = data.status;
    stageOfWork.attachmentFile = data.attachmentFile;
    const sw = await stageOfWork.save();
    if (storage) {
      storage.stageOfWork = sw;
      await storage.save();
    }

    let taskactivity = new Activity();
    taskactivity.action = Event_Actions.MILESTONE_UPDATED;
    taskactivity.actorId = userId;
    taskactivity.type = ActivityType.TASK;
    taskactivity.typeId = data['taskId'];
    taskactivity.remarks = `MileStone "${stageOfWork.name}" Updated by ${user.fullName}`;
    await taskactivity.save();

    return sw;
  }

  async getStageOfWork(id: number) {
    let stageOfWorks = await StageOfWork.find({
      where: {
        task: {
          id,
        },
      }, relations: ['storage']
    });

    return stageOfWorks;
  }

  async getCollectDataStageOfWork(id: number) {
    let stageOfWorks = await StageOfWork.find({
      where: {
        task: {
          id,
        },
      }
    });

    return stageOfWorks;
  }

  async deleteStageOfWork(id: number, userId, query) {
    let user = await User.findOne({
      where: {
        id: userId,
      },
    });

    let stageOfWork = await StageOfWork.findOne({
      where: {
        id,
      },
      relations: ['storage']
    });
    const deleteWork = await stageOfWork.remove();
    if (deleteWork) {
      if (deleteWork.storage?.storageSystem == StorageSystem.MICROSOFT) {
        const fileId = deleteWork.storage.fileId;
        await this.oneDriveService.deleteOneDriveFile(userId, fileId);
      } else if (deleteWork.storage?.storageSystem == StorageSystem.AMAZON) {
        await this.storageService.deleteAwsFile(deleteWork.storage.file);
      } else if (deleteWork.storage?.storageSystem === StorageSystem.BHARATHCLOUD) {
        await this.bharathService.deleteB3File(userId, deleteWork.storage.file)
      } else if (deleteWork.storage?.storageSystem == StorageSystem.GOOGLE) {
        await this.googleDriveService.deleteGoogleDriveFile(userId, deleteWork.storage.file);
      }
    }
    let taskactivity = new Activity();
    taskactivity.action = Event_Actions.MILESTONE_DELETED;
    taskactivity.actorId = userId;
    taskactivity.type = ActivityType.TASK;
    taskactivity.typeId = query['taskId'];
    taskactivity.remarks = `MileStone "${stageOfWork.name}" Deleted by ${user.fullName}`;
    await taskactivity.save();

    return stageOfWork;
  }
}
