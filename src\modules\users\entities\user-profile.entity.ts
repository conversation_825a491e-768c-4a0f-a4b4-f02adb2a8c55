import Storage from 'src/modules/storage/storage.entity';
import {
  AfterLoad,
  BaseEntity,
  Column,
  CreateDateColumn,
  Entity,
  JoinColumn,
  OneToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';

enum GenderEnum {
  MALE = 'MALE',
  FEMALE = 'FEMALE',
}

@Entity()
export class UserProfile extends BaseEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  employeeId: string;

  @Column({ nullable: true })
  workEmail: string;

  @Column({ type: 'datetime' })
  dateOfJoining: string;

  @Column({ type: 'date', nullable: true })
  dob: string;

  @Column({ nullable: true })
  fatherName: string;

  @Column({ type: 'enum', nullable: true, enum: GenderEnum })
  gender: GenderEnum;

  @Column({ type: 'text', nullable: true })
  address: string;

  @Column({ type: 'simple-array', nullable: true })
  specializations: string[];

  @Column({ nullable: true })
  RegistrationNumber: string;

  @Column({ nullable: true })
  aadharNumber: string;

  @OneToOne(() => Storage, (storage) => storage.addharStorage, { cascade: true })
  addharStorage: Storage;

  @OneToOne(() => Storage, (storage) => storage.profileSign, { cascade: true })
  @JoinColumn()
  profileSign: Storage;

  @Column({ nullable: true })
  aadharCard: string;

  @OneToOne(() => Storage, (storage) => storage.panStorage, { cascade: true })
  panStorage: Storage;


  @Column({ nullable: true })
  panNumber: string;


  @Column({ nullable: true })
  panCard: string;

  @OneToOne(() => Storage, (storage) => storage.drivingLicenseStorage, { cascade: true })
  drivingLicenseStorage: Storage;

  @Column({ nullable: true })
  drivingLicenseNumber: string;

  @Column({ nullable: true })
  drivingLicense: string;

  @Column({ nullable: true })
  bankAccountNumber: string;

  @Column({ nullable: true })
  bankAccountHolderName: string;

  @Column({ nullable: true })
  bankName: string;

  @Column({ nullable: true })
  bankIfscCode: string;

  @Column({ nullable: true })
  authorisedName: string;

  @Column({ nullable: true })
  authorisedDesignation: string;

  @CreateDateColumn()
  createdAt: string;

  @UpdateDateColumn()
  updatedAt: string;

  aadharCardUrl: string;

  panCardUrl: string;

  drivingLicenseUrl: string;

  @AfterLoad()
  updateUrls() {
    if (this.aadharCard) {
      this.aadharCardUrl = `${process.env.AWS_BASE_URL}/${this.aadharCard}`;
    }
    if (this.panCard) {
      this.panCardUrl = `${process.env.AWS_BASE_URL}/${this.panCard}`;
    }
    if (this.drivingLicense) {
      this.drivingLicenseUrl = `${process.env.AWS_BASE_URL}/${this.drivingLicense}`;
    }
  }
}
