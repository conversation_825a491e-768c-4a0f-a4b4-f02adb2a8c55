import { BadRequestException, Injectable, InternalServerErrorException } from '@nestjs/common';
import { User, UserStatus, UserType } from 'src/modules/users/entities/user.entity';
import { Brackets, createQueryBuilder, getConnection } from 'typeorm';
import AutClientCredentials, { IncomeTaxStatus } from '../entities/aut_client_credentials.entity';

import GstrCredentials, {
  GstrStatus,
} from 'src/modules/gstr-automation/entity/gstrCredentials.entity';
import { checkAtomProConfigGstr, checkAtomProConfigIncomeTax } from 'src/utils/atomProReUse';
import { Organization } from 'src/modules/organization/entities/organization.entity';
import Password, { IsExistingAtomPro } from 'src/modules/clients/entity/password.entity';
import axios from 'axios';
import * as xlsx from 'xlsx';
import TanClientCredentials from 'src/modules/tan-automation/entity/tan-client-credentials.entity';
import * as ExcelJS from 'exceljs';
import checkGstrPassword from 'src/utils/validations/atom-pro/gstrPassword';
import GetIncometaxClientDto from '../dto/get-incometax-client.dto';
import Task from 'src/modules/tasks/entity/task.entity';
import { TaskRecurringStatus, TaskStatusEnum } from 'src/modules/tasks/dto/types';
import checkTracesPassword from 'src/utils/validations/atom-pro/tracesPassword';
import checkTracesUserId from 'src/utils/validations/atom-pro/tracesUserId';
import { checkTanNumber } from 'src/utils/validations/atom-pro/panNumber';
import AutEProceedingFya from '../entities/aut_income_tax_eproceedings_fya.entity';
import AutFyaNotice from '../entities/aut_income_tax_eproceedings_fya_notice.entity';
import { sendnewMailToBusinessTeam } from 'src/emails/newemails';
import * as moment from 'moment';
import { Console, error } from 'console';
import AutProceedingResponseFyi from '../entities/aut_income_tax_eproceedings_fyi_notice_response.entity';
import AutProceedingResponseFya, {
  CreatedType,
} from '../entities/aut_income_tax_eproceedings_fyi_notice_response_fya.entity';
import Storage from 'src/modules/storage/storage.entity';
import { AttachmentFyaService } from './attachments-fya.service';
import OrganizationPreferences from 'src/modules/organization-preferences/entity/organization-preferences.entity';
import { getTitle } from 'src/utils';

@Injectable()
export class IncomeTaxConfigService {
  constructor(private attachmentFyaService: AttachmentFyaService) { }
  async incomeTaxAtomClient(userId: number, id: number) {
    const incomeTaxClient = await AutClientCredentials.findOne({
      where: { id: id },
      relations: ['client'],
    });
    return incomeTaxClient?.client;
  }

  async disableIncomeTaxClient(userId: number, body: any) {
    const ids = body.ids;
    for (let incomeTaxId of ids) {
      const incomeTaxClient = await AutClientCredentials.findOne({ where: { id: incomeTaxId } });
      if (incomeTaxClient) {
        incomeTaxClient.status = IncomeTaxStatus.DISABLE;
        await incomeTaxClient.save();
      }
    }
  }

  async disableIncomeTaxSingleClient(id: number, userId: number) {
    const incomeTaxClient = await AutClientCredentials.findOne({ where: { id: id } });
    if (incomeTaxClient) {
      incomeTaxClient.status = IncomeTaxStatus.DISABLE;
      await incomeTaxClient.save();
    }
  }

  async getDeletedIncomeTaxClients(userId: number, query) {
    const { limit, offset } = query;
    const user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
    const organizationId = user?.organization?.id;
    if (organizationId) {
      let deletedClients = createQueryBuilder(AutClientCredentials, 'clientCredentials')
        .leftJoinAndSelect('clientCredentials.client', 'client')
        .where('clientCredentials.organizationId = :organizationId', {
          organizationId: organizationId,
        })
        .andWhere('client.status != :status', { status: UserStatus.DELETED })
        .andWhere('clientCredentials.status = :disStatus', { disStatus: IncomeTaxStatus.DISABLE });

      const sort = typeof query?.sort === 'string' ? JSON.parse(query.sort) : query?.sort;
      if (sort?.column) {
        const columnMap: Record<string, string> = {
          displayName: 'client.displayName',
        };
        const column = columnMap[sort.column] || sort.column;
        deletedClients.orderBy(column, sort.direction.toUpperCase());
      } else {
        deletedClients.orderBy('clientCredentials.updatedAt', 'DESC');
      }

      if (query.search) {
        deletedClients.andWhere(
          new Brackets((qb) => {
            qb.where('clientCredentials.panNumber LIKE :panNumber', {
              panNumber: `%${query.search}%`,
            });
            qb.orWhere('client.displayName LIKE :namesearch', {
              namesearch: `%${query.search}%`,
            });
            qb.orWhere('client.clientId LIKE :namesearch', {
              namesearch: `%${query.search}%`,
            });
          }),
        );
      }
      if (offset >= 0) {
        deletedClients.skip(offset);
      }

      if (limit) {
        deletedClients.take(limit);
      }

      let result = await deletedClients.getManyAndCount();
      return {
        count: result[1],
        result: result[0],
      };
    }
  }

  async exportuserUpcomingLeaderTasks(userId: number, query: any) {
    const newQuery = { ...query, offset: 0, limit: ********* };
    let clients = await this.getDeletedIncomeTaxClients(userId, newQuery);

    if (!clients.result.length) throw new BadRequestException('No Data for Export');

    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Deleted Income Tax Clients');
    const headers = [
      { header: 'S.No', key: 'serialNo' },
      { header: 'Client Id', key: 'clientId' },
      { header: 'Client #', key: 'clientNumber' },
      { header: 'PAN', key: 'panNumber' },
      { header: 'Category', key: 'category' },
      { header: 'Sub Category', key: 'subCategory' },
      { header: 'Client Name', key: 'displayName' },
      { header: 'Status Updated At', key: 'statusUpdatedAt' },
    ];

    worksheet.columns = headers;

    let serialCounter = 1; // Initialize a counter

    // Initialize array to store max lengths for each column
    const columnMaxLengths = Array(headers.length).fill(0);

    clients.result.forEach((client) => {
      function capitalizeFirstLetter(string) {
        return string.charAt(0).toUpperCase() + string.slice(1);
      }
      const rowData = {
        serialNo: serialCounter++,
        clientId: client?.client?.clientId,
        clientNumber: client?.client?.clientNumber,
        category: client?.client?.category ? getTitle(client?.client?.category) : "",
        subCategory: client?.client?.subCategory ? getTitle(client?.client?.subCategory) : "",
        displayName: client?.client?.displayName,
        panNumber: client?.panNumber,
        statusUpdatedAt: moment(client?.updatedAt).format("DD-MM-YYYY h:mm a")
      };

      const row = worksheet.addRow(rowData);

      worksheet.columns.forEach((column, colIndex) => {
        const headerLength = column.header?.length || 0; // Length of header
        const cellLength = rowData[column.key]?.toString().length || 0; // Length of cell value
        columnMaxLengths[colIndex] = Math.max(
          columnMaxLengths[colIndex] || 0,
          headerLength,
          cellLength,
        );
      });
    });

    // Apply column widths with padding
    worksheet.columns.forEach((column, colIndex) => {
      column.width = columnMaxLengths[colIndex] + 3; // Add padding for readability
    });

    // Style the header row and center alignment
    const headerRow = worksheet.getRow(1);
    headerRow.font = { bold: true };
    headerRow.eachCell((cell) => {
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '64B5F6' },
      };
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' },
      };
      cell.alignment = { horizontal: 'center', vertical: 'middle' };
    });

    worksheet.columns.forEach((column) => {
      if (column.key === 'displayName') {
        column.width = 50;
        column.alignment = { wrapText: true, horizontal: 'center', vertical: 'middle' };
      } else {
        column.alignment = { horizontal: 'center', vertical: 'middle' };
      }
    });

    // Apply wrapText for all rows
    worksheet.eachRow((row) => {
      row.eachCell((cell) => {
        cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true }; // Center alignment and wrap text
      });
    });

    // Freeze the header row
    worksheet.views = [{ state: 'frozen', ySplit: 1 }];

    const buffer = await workbook.xlsx.writeBuffer();
    return buffer;
  }

  async enableIncometaxClient(id: number, userId: number) {
    const user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
    const organizationId = user?.organization?.id;
    const incomeTaxClient = await AutClientCredentials.findOne({ where: { id: id } });
    if (incomeTaxClient) {
      const checkIncomeTax = await checkAtomProConfigIncomeTax(organizationId);
      incomeTaxClient.status = IncomeTaxStatus.ENABLE;
      if (checkIncomeTax === true) {
        await incomeTaxClient.save();
      } else {
        throw new BadRequestException(checkIncomeTax);
      }
    }
  }

  async enableBulkIncometaxClient(userId: number, body: any) {
    const user = await User.findOne({
      where: { id: userId },
      relations: ['organization'],
    });

    const organizationId = user?.organization?.id;
    if (!organizationId) {
      throw new BadRequestException('Organization not found for this user');
    }

    // Fetch org preferences only once
    const orgPreferences: any = await OrganizationPreferences.findOne({
      where: { organization: organizationId },
    });

    const isIncomeTaxEnabled = orgPreferences?.automationConfig?.incomeTax === 'YES';
    if (!isIncomeTaxEnabled) {
      throw new BadRequestException(
        'Subscribe Atom Pro Income Tax to enable clients for this organization',
      );
    }

    const incomeTaxClientCount = await AutClientCredentials.count({
      where: { organizationId, status: IncomeTaxStatus.ENABLE },
    });

    const organizationIncomeTaxLimit =
      orgPreferences?.automationConfig?.incomeTaxLimit || 0;

    // check if bulk enable will exceed the limit
    const clientsToEnable = body?.incomeTaxClients?.length || 0;
    if (incomeTaxClientCount + clientsToEnable > organizationIncomeTaxLimit) {
      throw new BadRequestException(
        `Cannot enable clients. You can only enable up to ${organizationIncomeTaxLimit} Income Tax clients in Atom Pro.`,
      );
    }

    // If within limit, enable all
    for (let i of body?.incomeTaxClients) {
      const incomeTaxClient = await AutClientCredentials.findOne({
        where: { id: i.id },
      });
      if (incomeTaxClient) {
        incomeTaxClient.status = IncomeTaxStatus.ENABLE;
        await incomeTaxClient.save();
      }
    }
  }

  async disableAtomProGstrClient(userId: number, body: any) {
    const ids = body.ids;
    for (let gstrId of ids) {
      const gstrClient = await GstrCredentials.findOne({ where: { id: gstrId } });
      if (gstrClient) {
        gstrClient.status = GstrStatus.DISABLE;
        await gstrClient.save();
      }
    }
  }

  async disableGstrSingleClient(id: number, userId: number) {
    const gstrClient = await GstrCredentials.findOne({ where: { id: id } });
    if (gstrClient) {
      gstrClient.status = GstrStatus.DISABLE;
      await gstrClient.save();
    }
  }

  async getDeletedGstrClients(userId: number, query) {
    const { limit, offset } = query;
    const user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
    const organizationId = user?.organization?.id;
    if (organizationId) {
      let deletedClients = createQueryBuilder(GstrCredentials, 'gstrCredentials')
        .leftJoinAndSelect('gstrCredentials.client', 'client')
        .where('gstrCredentials.organizationId = :organizationId', {
          organizationId: organizationId,
        })
        .andWhere('client.status != :status', { status: UserStatus.DELETED })
        .andWhere('gstrCredentials.status = :disStatus', { disStatus: GstrStatus.DISABLE });

      if (query.search) {
        deletedClients.andWhere(
          new Brackets((qb) => {
            qb.where('gstrCredentials.userName LIKE :userName', {
              userName: `%${query.search}%`,
            });
            qb.orWhere('client.displayName LIKE :namesearch', {
              namesearch: `%${query.search}%`,
            });
          }),
        );
      }

      if (offset >= 0) {
        deletedClients.skip(offset);
      }

      if (limit) {
        deletedClients.take(limit);
      }

      let result = await deletedClients.getManyAndCount();
      return {
        count: result[1],
        result: result[0],
      };
    }
  }

  async enableGstrClient(id: number, userId: number) {
    const user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
    const organizationId = user?.organization?.id;
    const gstrClient = await GstrCredentials.findOne({ where: { id: id } });
    if (gstrClient) {
      const checkGstr = await checkAtomProConfigGstr(organizationId);
      gstrClient.status = GstrStatus.ENABLE;
      if (checkGstr === true) {
        await gstrClient.save();
      } else {
        throw new BadRequestException(checkGstr);
      }
    }
  }

  async getBulkSyncStatus(userId: number) {
    try {
      const user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
      let data = '';

      let config: any = {
        method: 'get',
        maxBodyLength: Infinity,
        url: `${process.env.CAMUNDA_URL}/vider/quantum/api/automation/bulk/sync/${user?.organization?.id}/incometax`,
        headers: {},
        data: data,
      };

      const response = await axios.request(config);
      return JSON.stringify(response?.data);
    } catch (error) {
      console.log('error occure while getting into getBulkSyncStatus', error.message);
    }
  }

  async updateEnableStatus(userId: number) {
    try {
      const user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
      let config: any = {
        method: 'put',
        maxBodyLength: Infinity,
        url: `${process.env.CAMUNDA_URL}/vider/quantum/api/automation/bulk/sync/${user?.organization?.id}/enable/incometax`,
        headers: {},
        data: '',
      };
      const response = await axios.request(config);
    } catch (error) {
      console.log('error while updateEnableStatus', error);
    }
  }

  async updateDisableStatus(userId: number) {
    try {
      const user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
      let config: any = {
        method: 'put',
        maxBodyLength: Infinity,
        url: `${process.env.CAMUNDA_URL}/vider/quantum/api/automation/bulk/sync/${user?.organization?.id}/disable/incometax`,
        headers: {},
        data: '',
      };
      const response = await axios.request(config);
    } catch (error) {
      console.log('error while updateEnableStatus', error?.message);
    }
  }

  async organizationScheduling(userId, body) {
    const { periodicity, day, hour, minute, weekDay } = body;

    try {
      const user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
      const organizarionId = user.organization.id;

      const amPm = hour >= 12 ? 'pm' : 'am'; // Determine AM/PM
      const adjustedHour = hour > 12 ? hour - 12 : hour; // Convert to 12-hour format if needed

      // Prepare the originalBody object based on the provided periodicity
      const originalBody = [
        {
          periodicity: periodicity || 'DAILY', // Default to 'DAILY' if not provided
          days: periodicity === 'WEEKLY' ? (weekDay ? weekDay.toUpperCase() : null) : null,
          daysInstance: {}, // Can add more logic if required for weekly scheduling
          hour: adjustedHour, // Using 12-hour format
          minute: minute,
          seconds: 0,
          amPm: amPm, // "am" or "pm" based on the hour
          dayOfMonth: periodicity === 'MONTHLY' && day ? day : 0, // Set day if 'MONTHLY'
          month: 0, // Assuming no month field for DAILY/WEEKLY
          intervalMinutes: 0, // Assuming no intervalMinutes for now
        },
      ];
      let data = JSON.stringify({
        modules: ['OD', 'EP'],
        orgId: organizarionId,
        type: 'INCOMETAX',
        schedules: originalBody,
      });

      let config: any = {
        method: 'post',
        maxBodyLength: Infinity,
        url: `${process.env.CAMUNDA_URL}/vider/quantum/api/automation/bulk/sync`,
        headers: {
          'X-USER-ID': userId,
          'Content-Type': 'application/json',
        },
        data: data,
      };

      const response = await axios.request(config);
      return JSON.stringify(response?.data);
    } catch (error) {
      console.log('error occur while organizationScheduling', error);
    }
  }

  async changeAutomationPasswordGstrStatus(orgId: number) {
    const organizations = await Organization.find();
    for (let org of organizations) {
      const gstrClients = await GstrCredentials.find({
        where: { organizationId: org.id },
        relations: ['client'],
      });
      for (let gstr of gstrClients) {
        const createClientCredentials = async (data: any) => {
          const password = new Password();
          password.website = data.website.trim();
          password.websiteUrl = data.websiteUrl.trim();
          password.loginId = data.loginId.trim();
          password.password = data.password.trim();
          password.client = data.client;
          password.isExistingAtomPro = data.isaddAtomPro;
          await password.save();
          return password;
        };

        const details = {
          website: 'GST | e-Filing',
          websiteUrl: 'https://services.gst.gov.in/services/login',
          loginId: gstr.userName,
          password: gstr?.password,
          client: gstr.client,
          isaddAtomPro: IsExistingAtomPro.YES,
        };
        if (gstr.client.status === UserStatus.DELETED) {
          if (gstr.passwordId) {
            gstr.status = GstrStatus.DISABLE;
            await gstr.save();

            const passwordData = await Password.findOne({ where: { id: gstr.passwordId } });
            passwordData.isExistingAtomPro = IsExistingAtomPro.YES;
            await passwordData.save();
          } else {
            const passwordData: any = await createClientCredentials(details);
            gstr.passwordId = passwordData?.id;
            gstr.status = GstrStatus.DISABLE;
            await gstr.save();
          }
        } else {
          if (gstr.passwordId) {
            gstr.status = GstrStatus.ENABLE;
            await gstr.save();

            const passwordData = await Password.findOne({ where: { id: gstr.passwordId } });
            passwordData.isExistingAtomPro = IsExistingAtomPro.YES;
            await passwordData.save();
          } else {
            const passwordData: any = await createClientCredentials(details);
            gstr.passwordId = passwordData?.id;
            gstr.status = GstrStatus.ENABLE;
            await gstr.save();
          }
        }
      }
    }
  }

  async changeAutomationPasswordIncomeTAxStatus(orgId: number) {
    const organizations = await Organization.find();
    for (let org of organizations) {
      const incomeTaxClients = await AutClientCredentials.find({
        where: { organizationId: org.id },
        relations: ['client'],
      });

      for (let incomeTax of incomeTaxClients) {
        const createClientCredentials = async (data: any) => {
          const password = new Password();
          password.website = data.website.trim();
          password.websiteUrl = data.websiteUrl.trim();
          password.loginId = data.loginId.trim();
          password.password = data.password.trim();
          password.client = data.client;
          password.isExistingAtomPro = data.isaddAtomPro;
          await password.save();
          return password;
        };

        const details = {
          website: 'Income Tax | e-Filing (PAN)',
          websiteUrl: 'https://eportal.incometax.gov.in/iec/foservices/#/login',
          loginId: incomeTax.panNumber,
          password: incomeTax?.password,
          client: incomeTax.client,
          isaddAtomPro: IsExistingAtomPro.YES,
        };
        if (incomeTax.client.status === UserStatus.DELETED) {
          if (incomeTax.passwordId) {
            incomeTax.status = IncomeTaxStatus.DISABLE;
            await incomeTax.save();

            const passwordData = await Password.findOne({ where: { id: incomeTax.passwordId } });
            passwordData.isExistingAtomPro = IsExistingAtomPro.YES;
            await passwordData.save();
          } else {
            const passwordData: any = await createClientCredentials(details);
            incomeTax.passwordId = passwordData?.id;
            incomeTax.status = IncomeTaxStatus.DISABLE;
            await incomeTax.save();
          }
        } else {
          if (incomeTax.passwordId) {
            incomeTax.status = IncomeTaxStatus.ENABLE;
            await incomeTax.save();

            const passwordData = await Password.findOne({ where: { id: incomeTax.passwordId } });
            passwordData.isExistingAtomPro = IsExistingAtomPro.YES;
            await passwordData.save();
          } else {
            const passwordData: any = await createClientCredentials(details);
            incomeTax.passwordId = passwordData?.id;
            incomeTax.status = IncomeTaxStatus.ENABLE;
            await incomeTax.save();
          }
        }
      }
    }
  }

  async exportAtomProCredentilas(userId: number) {
    const user = await User.findOne({ where: { id: userId }, relations: ['organization'] });

    const panCredentilasQuery = createQueryBuilder(AutClientCredentials, 'autClientCredentials')
      .leftJoinAndSelect('autClientCredentials.client', 'client')
      .where('autClientCredentials.organizationId = :organizationId', {
        organizationId: user?.organization?.id,
      })
      .andWhere('client.status != :status', { status: UserStatus.DELETED });

    const gstCredentilasQuery = createQueryBuilder(GstrCredentials, 'gstCredentials')
      .leftJoinAndSelect('gstCredentials.client', 'client')
      .where('gstCredentials.organizationId = :organizationId', {
        organizationId: user?.organization?.id,
      })
      .andWhere('client.status != :status', { status: UserStatus.DELETED });

    const tanCredentilasQuery = createQueryBuilder(TanClientCredentials, 'tanCredentials')
      .leftJoinAndSelect('tanCredentials.client', 'client')
      .where('tanCredentials.organizationId = :organizationId', {
        organizationId: user?.organization?.id,
      })
      .andWhere('client.status != :status', { status: UserStatus.DELETED });

    const panCredentilas = await panCredentilasQuery.getMany();
    const gstCredentials = await gstCredentilasQuery.getMany();
    const tanCredentials = await tanCredentilasQuery.getMany();

    const workbook = new ExcelJS.Workbook();
    const guidelinesSheet = workbook.addWorksheet('Guidelines');
    const panSheet = workbook.addWorksheet('PAN');
    const tanSheet = workbook.addWorksheet('TAN');
    const gstSheet = workbook.addWorksheet('GST');

    const guidelines = [
      ['Guidelines for updating client credentials which are added in ATOM Pro'],
      ['S.No', 'Description'],
      [
        1,
        'Exported sheet from the organization will only be accepted in re-uploading for Bulk Update',
      ],
      [2, 'Do not delete/hide any column'],
      [3, 'Only the password field is editable & Traces User Id'],
      [
        4,
        'If there is any change/update in the password, make sure that it cannot be the same as the last three previous passwords',
      ],
      [
        5,
        'PAN passwords should be of 8 to 14 characters and should comprise at least one UPPER CASE letter, lower case letter, number, and special character',
      ],
      [
        6,
        'TAN passwords should be of 8 to 14 characters and should comprise at least one UPPER CASE letter, lower case letter, number, and special character',
      ],
      [
        7,
        `Traces password should be of 8 to 15 characters, cannot be the same as user id, should contain both alphabets and numbers (a-z, A-Z, 0-9) and should have at least one letter in Upper Case, Special Characters Allowed: Space ' & " , ;`,
      ],
      [
        8,
        'GST password should be of 8 to 15 characters, should comprise at least one alphabet, UPPER CASE letter, lower case letter, number and special character',
      ],
    ];
    guidelines.forEach((row) => {
      guidelinesSheet.addRow(row);
    });

    // Format the header row
    const headerRow = guidelinesSheet.getRow(1);
    headerRow.eachCell((cell) => {
      cell.font = { bold: true, size: 14 };
      cell.alignment = { horizontal: 'center' };
    });

    // Merge the title row for better visibility
    guidelinesSheet.mergeCells('A1:B1');
    guidelinesSheet.getRow(1).height = 25;
    guidelinesSheet.columns = [
      { key: 'sno', width: 10, alignment: { vertical: 'middle', horizontal: 'center' } },
      { key: 'description', width: 100, alignment: { vertical: 'middle', wrapText: true } },
    ];

    // Adjust row height for multiline text
    guidelinesSheet.eachRow((row, rowNumber) => {
      if (rowNumber > 1) {
        row.height = 20; // Adjust row height
      }
    });

    const markerSheet = workbook.addWorksheet('Metadata');
    markerSheet.getColumn(1).values = [user?.organization?.id, user?.fullName];
    markerSheet.state = 'hidden';

    workbook.views = [
      {
        x: 0,
        y: 0,
        width: 20000,
        height: 5000,
        activeTab: 1,
        firstSheet: 0,
        visibility: 'visible',
      },
    ];

    const columns = [
      { header: 'credentialId', key: 'id', width: 10, hidden: true },
      { header: 'Client Record Id', key: 'clientRecordId', hidden: true },
      { header: 'Client ID', key: 'clientId', width: 10 },
      { header: 'Client #', key: 'clientNumber', width: 15 },
      { header: 'Client Name', key: 'clientName', width: 25 },
      { header: 'Username', key: 'username', width: 20 },
      { header: 'Password', key: 'password', width: 20 },
      { header: 'Sync Status', key: 'status', width: 15 },
    ];

    const otherColumns = [
      ...columns,
      { header: 'Remarks', key: 'remarks', width: 150 },
    ]

    const tanColumns = [
      ...columns,
      { header: 'Traces User ID', key: 'tracesUserId', width: 20 },
      { header: 'Traces Password', key: 'tracesPassword', width: 20 },
      { header: 'TAN Remark', key: 'tanRemarks', width:150},
      { header: 'Trace Remark', key: 'traceRemarks', width:150}
    ];


    panSheet.columns = otherColumns;
    gstSheet.columns = otherColumns;
    tanSheet.columns = tanColumns;

    const addDataToSheet = (sheet: ExcelJS.Worksheet, data: any[], usernameKey: string) => {
      if (usernameKey === 'tanNumber') {
        data.forEach((record) =>
          sheet.addRow({
            id: record.id,
            clientRecordId: record.client?.id,
            clientId: record.client?.clientId,
            clientNumber: record.client?.clientNumber,
            clientName: record.client?.displayName,
            status: record.status,
            username: record[usernameKey],
            password: record.password,
            tracesUserId: record.traceUserId,
            tracesPassword: record.tracePassword,
            tanRemarks: record.tanRemarks,
            traceRemarks: record.traceRemarks
          }),
        );
      } else {
        data.forEach((record) =>
          sheet.addRow({
            id: record.id,
            clientRecordId: record.client?.id,
            clientId: record.client?.clientId,
            clientNumber: record.client?.clientNumber,
            clientName: record.client?.displayName,
            status: record.status,
            username: record[usernameKey],
            password: record.password,
            remarks:record.remarks
          }),
        );
      }
    };

    addDataToSheet(panSheet, panCredentilas, 'panNumber');
    addDataToSheet(tanSheet, tanCredentials, 'tanNumber');
    addDataToSheet(gstSheet, gstCredentials, 'userName');

    const styleSheet = (
      sheet: ExcelJS.Worksheet,
      editableColumns: number[],
      dataLength: number,
      type:string
    ) => {
      sheet.views = [{ state: 'frozen', ySplit: 1 }];
      const headerRow = sheet.getRow(1);
      headerRow.eachCell((cell) => {
        cell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FF64B5F6' } };
        cell.font = { bold: true };
        cell.border = {
          top: { style: 'thin' },
          left: { style: 'thin' },
          bottom: { style: 'thin' },
          right: { style: 'thin' },
        };
      });

      sheet.columns.forEach((column) => {
        column.width = column.header?.length < 20 ? 20 : column.header.length;
      });

     

      sheet.getColumn(5).width = 50;
      sheet.getColumn(5).alignment = { wrapText: true };
      if(type==='PAN' || type === 'GST'){
        sheet.getColumn(9).width = 50;
        sheet.getColumn(9).alignment = { wrapText: true };
      }else if(type==='TAN'){
        sheet.getColumn(11).width = 50;
        sheet.getColumn(11).alignment = { wrapText: true };
        sheet.getColumn(12).width = 50;
        sheet.getColumn(12).alignment = { wrapText: true };
      }
     

      // Apply styling and protection to all rows up to dataLength
      for (let rowNum = 2; rowNum <= dataLength + 1; rowNum++) {
        const row = sheet.getRow(rowNum);
        row.eachCell({ includeEmpty: true }, (cell, colNum) => {
          cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
          cell.border = {
            top: { style: 'thin' },
            left: { style: 'thin' },
            bottom: { style: 'thin' },
            right: { style: 'thin' },
          };
          cell.protection = { locked: !editableColumns.includes(colNum) };
          // Ensure editable columns have a default value if empty
          if (editableColumns.includes(colNum) && !cell.value) {
            cell.value = '';
          }
        });

        // Apply status formatting if applicable
        if (row.getCell(8).value) {
          const statusCell = row.getCell(8);
          statusCell.font = {
            color: { argb: statusCell.value === 'ENABLE' ? 'FF00B050' : 'FFFF0000' },
            bold: true,
          };
        }
      }

      sheet.protect('vider@1818', {
        selectLockedCells: true,
        selectUnlockedCells: true,
        formatColumns: true,
        formatRows: true,
      });
    };

    styleSheet(panSheet, [7], panCredentilas.length,"PAN");
    styleSheet(gstSheet, [7], gstCredentials.length,"GST");
    styleSheet(tanSheet, [7, 9, 10], tanCredentials.length,"TAN");
    const buffer = await workbook.xlsx.writeBuffer();
    return buffer;
  }

  async bulkUpdateCredentials(userId: number, file: Express.Multer.File) {
    const user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
    const workbook = xlsx.read(file.buffer, { cellDates: true });
    const sheet = workbook.Sheets[workbook.SheetNames[0]];
    const dataFromExcel = xlsx.utils.sheet_to_json(sheet, { defval: null });

    const panSheet = workbook.Sheets['PAN'];
    const tanSheet = workbook.Sheets['TAN'];
    const gstSheet = workbook.Sheets['GST'];

    const panData: any = panSheet ? xlsx.utils.sheet_to_json(panSheet, { defval: null }) : [];
    const gstData: any = gstSheet ? xlsx.utils.sheet_to_json(gstSheet, { defval: null }) : [];
    const tanData: any = tanSheet ? xlsx.utils.sheet_to_json(tanSheet, { defval: null }) : [];

    const metaDataSheet = workbook.Sheets['Metadata'];

    if (!metaDataSheet) {
      throw new BadRequestException('Please upload the valid exported excel.');
    }
    const organizationIdFromExcel = metaDataSheet['A1']?.v;
    if (organizationIdFromExcel !== user?.organization?.id) {
      throw new BadRequestException(
        "Organization doesn't matched. Please upload exported excel from your organization.",
      );
    }

    const results = {
      panSuccessCount: 0,
      panErrors: [] as string[],
      gstSuccessCount: 0,
      gstErrors: [] as string[],
      tanSuccessCount: 0,
      tracesSuccessCount: 0,
      tanErrors: [] as string[],
    };

    const addError = (errors: string[], record: any, rowId: number, message: string) => {
      errors.push(`Error at row ${rowId} for  ${record['Client Name']}: ${message}`);
    };

    for (const [index, record] of panData.entries()) {
      const {
        credentialId,
        'Username': username,
        'Password': password,
        'Client Record Id': clientRecordId,
      } = record;

      if (username && password && credentialId) {
        const credential = await AutClientCredentials.findOne({ where: { id: credentialId } });
        if (credential) {
          const panClient = credential;
          (panClient.password = password), await panClient.save();
          const clientPassword = await Password.findOne({
            where: {
              client: clientRecordId,
              website: 'Income Tax | e-Filing (PAN)',
              isExistingAtomPro: IsExistingAtomPro.YES,
            },
          });
          if (clientPassword) {
            const panPassword = clientPassword;
            panPassword.password = password;
            panPassword.save();
            results.panSuccessCount++;
          }
        }
      } else {
        addError(results.panErrors, record, index + 1, 'Mandatory Fields are missing');
        continue;
      }
    }

    for (const [index, record] of gstData.entries()) {
      const {
        credentialId,
        'Username': username,
        'Password': password,
        'Client Record Id': clientRecordId,
      } = record;

      if (username && password && credentialId) {
        const credential = await GstrCredentials.findOne({ where: { id: credentialId } });
        const gstpasswordCheck = checkGstrPassword(password);
        if (credential) {
          if (gstpasswordCheck) {
            addError(results.gstErrors, record, index + 1, 'GST Password is In-valid');
            continue;
          }
          const gstClient = credential;
          (gstClient.password = password), await gstClient.save();
          const clientPassword = await Password.findOne({
            where: {
              client: clientRecordId,
              website: 'GST | e-Filing',
              isExistingAtomPro: IsExistingAtomPro.YES,
            },
          });
          if (clientPassword) {
            const gstPassword = clientPassword;
            gstPassword.password = password;
            gstPassword.save();
            results.gstSuccessCount++;
          }
        }
      } else {
        addError(results.gstErrors, record, index + 1, 'Mandatory Fields are missing');
        continue;
      }
    }

    for (const [index, record] of tanData.entries()) {
      const {
        credentialId,
        'Username': username,
        'Password': password,
        'Client Record Id': clientRecordId,
        'Traces User ID': tracesUserId,
        'Traces Password': tracesPassword,
      } = record;
      const validTan = checkTanNumber(username);
      const validTracesPassword = checkTracesPassword(tracesPassword);
      const validTracesUserId = checkTracesUserId(tracesUserId);
      if (validTan) {
        addError(results.tanErrors, record, index + 1, `TAN: ${username} is invalid`);
        continue;
      }
      if (validTracesPassword) {
        addError(
          results.tanErrors,
          record,
          index + 1,
          `Trace Password: ${tracesPassword} is invalid`,
        );
        continue;
      }
      if (validTracesUserId) {
        addError(results.tanErrors, record, index + 1, `Trace User Id: ${tracesUserId} is invalid`);
        continue;
      }

      if (username && credentialId) {
        let queryRunner = getConnection().createQueryRunner();
        await queryRunner.connect();
        await queryRunner.startTransaction();

        try {
          const credential = await queryRunner.manager.findOne(TanClientCredentials, {
            where: { id: credentialId },
          });

          if (credential) {
            const tanClient = credential;
            (tanClient.password = password),
              (tanClient.traceUserId = tracesUserId),
              (tanClient.tracePassword = tracesPassword);
            await queryRunner.manager.save(tanClient);

            const clientPassword = await queryRunner.manager.findOne(Password, {
              where: {
                client: clientRecordId,
                website: 'Income Tax | e-Filing (TAN)',
                isExistingAtomPro: IsExistingAtomPro.YES,
              },
            });
            const clientTracePassword = await queryRunner.manager.findOne(Password, {
              where: { client: clientRecordId, tracesTan: username },
            });
            if (clientPassword) {
              const tanPassword = clientPassword;
              tanPassword.password = password;
              await queryRunner.manager.save(tanPassword);
              results.tanSuccessCount++;
            } else {
              throw new BadRequestException('Password Record not found by this TAN');
            }
            if (clientTracePassword) {
              const tracePassword = clientTracePassword;
              tracePassword.loginId = tracesUserId;
              tracePassword.password = tracesPassword;
              await queryRunner.manager.save(tracePassword);
              results.tracesSuccessCount++;
            } else {
              throw new BadRequestException('Trace Records not found by this TAN');
            }
          } else {
            throw new BadRequestException('TAN Credentials not found');
          }
          await queryRunner.commitTransaction();
        } catch (error) {
          await queryRunner.rollbackTransaction();
          console.error('Error updating credentials:', error);
          throw error;
        } finally {
          await queryRunner.release();
        }
      } else {
        addError(results.tanErrors, record, index + 1, 'Mandatory Fields are missing');
        continue;
      }
    }

    return results;
  }

  async exportErrorsReport(userId: number, body: any) {
    const rows = Object.entries(body).flatMap(([key, errors]: [string, string[]]) =>
      errors.map((error) => ({
        category: key.toUpperCase(),
        error,
      })),
    );

    if (rows.length === 0) {
      throw new Error('No errors to export');
    }

    const worksheet = xlsx.utils.json_to_sheet(rows);
    const workbook = xlsx.utils.book_new();
    xlsx.utils.book_append_sheet(workbook, worksheet, 'Errors');

    const file = xlsx.write(workbook, { type: 'buffer' });

    return file;
  }

  async getIncometaxClientTasks(userId: number, query: GetIncometaxClientDto) {
    try {
      const { limit, offset } = query;
      const user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
      const tasks = createQueryBuilder(Task, 'task')
        .leftJoinAndSelect('task.client', 'client')
        .leftJoinAndSelect('task.clientGroup', 'clientGroup')
        .leftJoinAndSelect('task.category', 'category')
        .leftJoin('task.subCategory', 'subCategory')
        .leftJoinAndSelect('task.members', 'members')
        .where('task.organization.id = :organization', { organization: user.organization.id })
        .andWhere('(task.recurringStatus is null or task.recurringStatus = :recurringStatus)', {
          recurringStatus: TaskRecurringStatus.CREATED,
        })
        .andWhere(
          new Brackets((qb) =>
            qb.where('task.status IN (:...statuses)', {
              statuses: [
                TaskStatusEnum.TODO,
                TaskStatusEnum.IN_PROGRESS,
                TaskStatusEnum.ON_HOLD,
                TaskStatusEnum.UNDER_REVIEW,
                TaskStatusEnum.COMPLETED,
              ],
            }),
          ),
        )
        .andWhere('task.parentTask IS NULL');
      if (query?.clientId) {
        tasks.andWhere('client.id = :clientId', { clientId: query?.clientId });
      }

      if (query?.categoryId) {
        tasks.andWhere('category.id = :categoryId', { categoryId: query.categoryId });
      }

      if (query?.subCategoryId) {
        tasks.andWhere('subCategory.id = :subCategoryId', { subCategoryId: query.subCategoryId });
      }

      if (offset >= 0) {
        tasks.skip(offset);
      }

      if (limit) {
        tasks.take(limit);
      }

      let result = await tasks.getManyAndCount();
      return {
        count: result[1],
        data: result[0],
      };
    } catch (error) {
      console.log('Error Occur getIncometaxClientTasks', error?.message);
    }
  }

  async createFyaItem(userId: number, body: any) {
    try {
      const user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
      if (body?.autCreadentials) {
        const autCreadentials = await AutClientCredentials.findOne({
          where: { id: body?.autCreadentials },
          relations: ['client'],
        });

        const proceedingFya = new AutEProceedingFya();
        proceedingFya.proceedingName = body?.proceedingName;
        proceedingFya.assesmentYear = body?.assessmentYear.split('-')[0];
        proceedingFya.financialYear = String(Number(body?.assessmentYear.split('-')[0]) - 1);
        proceedingFya.noticeName = body?.section;
        proceedingFya.pan = body?.pannumber;
        proceedingFya.client = autCreadentials?.client;
        proceedingFya.organizationId = user?.organization?.id;
        proceedingFya.type = body?.type;
        proceedingFya.createdType = 'MANUAL';
        await proceedingFya.save();
      }
    } catch (error) {
      console.log('Error occur while CreateFyaItem', error?.message);
      throw new BadRequestException('Error Occur while Create FYA Proceeding', error?.message);
    }
  }

  async updateFyaItem(userId: number, body: any) {
    try {
      const user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
      if (body?.id) {
        const proceedingFya = await AutEProceedingFya.findOne({
          where: { id: body?.id, organizationId: user?.organization?.id },
        });
        if (proceedingFya) {
          proceedingFya.type = body?.type;
          proceedingFya.proceedingName = body?.proceedingName;
          proceedingFya.assesmentYear = body?.assesmentYear;
          proceedingFya.financialYear = body?.financialYear;
          proceedingFya.noticeName = body?.noticeName;
          await proceedingFya.save();
        }
      }
    } catch (error) {
      console.log('Error occur while CreateFyaItem', error?.message);
      throw new BadRequestException('Error Occur while Update FYA Proceeding', error?.message);
    }
  }

  async deleteFyaItem(userId: number, id: number) {
    try {
      const user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
      if (id) {
        await AutFyaNotice.delete({ eProceedingId: id, organizationId: user?.organization?.id });
        await AutEProceedingFya.delete({ id: id, organizationId: user?.organization?.id });
        return true;
      }
    } catch (error) {
      throw new BadRequestException('Error Occur while deleting Proceeding', error?.message);
    }
  }

  async createFyaNotice(userId: number, body: any) {
    try {
      const proceding = await AutEProceedingFya.findOne({
        where: { id: body?.proceedingId },
        relations: ['client'],
      });

      const fyaNoticeExists = await AutFyaNotice.find({
        where: { eProceedingId: body?.proceedingId, nameOfAssesse: body?.nameOfAssesse },
      });

      const fyaNoticeExist2 = await AutFyaNotice.find({
        where: { eProceedingId: body?.proceedingId },
      });

      if (fyaNoticeExists?.length !== fyaNoticeExist2?.length) {
        throw new BadRequestException('Name Of Assesse is not match with existing notices');
      }

      const fyaNotice = new AutFyaNotice();
      fyaNotice.proceedingName = proceding?.proceedingName;
      fyaNotice.nameOfAssesse = body?.nameOfAssesse;
      fyaNotice.issuedOn = body?.issuedOn;
      fyaNotice.responseDueDate = body?.responseDueDate;
      fyaNotice.documentIdentificationNumber = body?.din;
      fyaNotice.description = body?.description;
      fyaNotice.assesmentYear = proceding?.assesmentYear;
      fyaNotice.financialYear = proceding?.financialYear;
      fyaNotice.client = proceding?.client;
      fyaNotice.createdType = 'MANUAL';
      fyaNotice.pan = body?.pan;
      fyaNotice.eProceedingId = proceding?.id;
      fyaNotice.organizationId = proceding?.organizationId;
      fyaNotice.noticeSection = proceding?.noticeName;
      await fyaNotice.save();
    } catch (error) {
      console.log('Error Occur while CreateFyaNotice', error?.message);
      throw new InternalServerErrorException(error);
    }
  }

  async updateFyaNotice(userId: number, body: any) {
    try {
      const user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
      if (body?.id) {
        const fyaNotice = await AutFyaNotice.findOne({
          where: { id: body?.id, organizationId: user?.organization?.id },
        });
        if (fyaNotice) {
          fyaNotice.issuedOn = body?.issuedOn;
          fyaNotice.responseDueDate = body?.responseDueDate;
          fyaNotice.documentIdentificationNumber = body?.documentIdentificationNumber;
          fyaNotice.description = body?.description;
          fyaNotice.nameOfAssesse = body?.nameOfAssesse;
          fyaNotice.pan = body?.pan;
          await fyaNotice.save();
        }
      }
    } catch (error) {
      console.log('Error occur while CreateFyaItem', error?.message);
      throw new BadRequestException('Error Occur while Update FYA Proceeding', error?.message);
    }
  }

  async deleteFyaNotice(userId: number, id: number) {
    try {
      const user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
      if (id) {
        const fyaNotice = await AutFyaNotice.findOne({
          where: { id: id, organizationId: user?.organization?.id },
        });
        if (fyaNotice) {
          const responses = await AutProceedingResponseFya.find({
            where: { fyaNotice: fyaNotice },
          });
          if (responses) {
            for (let response of responses) {
              if (response) {
                const storageItems = await Storage.find({
                  where: { autProceedingResponseFya: response },
                });
                if (storageItems && storageItems.length > 0) {
                  for (const item of storageItems) {
                    await this.attachmentFyaService.deleteStorageFile(item.id, user.id);
                  }
                }

                if (response) {
                  await response.remove();
                }
              }
            }
          }

          await fyaNotice.remove();
          return true;
        }
      }
    } catch (error) {
      console.log(error);
      throw new BadRequestException('Error Occur while deleting Notice', error?.message);
    }
  }

  async getExpiringOrganizations() {
    try {
      const today = moment().format('YYYY-MM-DD');
      const lastMonthDate = moment().subtract(30, 'days').format('YYYY-MM-DD');
      const nextSeventhDay = moment().add(7, 'days').format('YYYY-MM-DD');
      const unexpiredOrganizations = await Organization.createQueryBuilder('organization')
        .leftJoinAndSelect('organization.users', 'user')
        .where(
          "DATE_FORMAT(STR_TO_DATE(JSON_UNQUOTE(JSON_EXTRACT(config, '$.expirydate')), '%Y-%m-%d'), '%Y-%m-%d') between :today AND :nextSeventhDay",
          { today, nextSeventhDay },
        )
        .andWhere('user.type = :type', { type: UserType.ORGANIZATION })
        .getMany();

      const expiredOrganizations = await Organization.createQueryBuilder('organization')
        .leftJoinAndSelect('organization.users', 'user')
        .where(
          "DATE_FORMAT(STR_TO_DATE(JSON_UNQUOTE(JSON_EXTRACT(config, '$.expirydate')), '%Y-%m-%d'), '%Y-%m-%d') between :lastMonthDate AND :today",
          { lastMonthDate, today },
        )
        .andWhere('user.type = :type', { type: UserType.ORGANIZATION })
        .getMany();

      try {
        const oneWeekAboutToExpireArray = [];
        const monthAgoExpiredArray = [];
        for (let organization of unexpiredOrganizations) {
          const config: any = organization?.config;
          const expiryDate = moment(config.expirydate, 'YYYY-MM-DD');
          const orgData = {
            id: organization?.id,
            name: organization?.legalName,
            mobileNumber: organization?.mobileNumber,
            email: organization?.email,
            expiryDate: expiryDate,
            createdAt: organization?.createdAt,
          };
          oneWeekAboutToExpireArray.push(orgData);
        }
        for (let organization of expiredOrganizations) {
          const config: any = organization?.config;
          const expiryDate = moment(config.expirydate, 'YYYY-MM-DD');
          const orgData = {
            id: organization?.id,
            name: organization?.legalName,
            mobileNumber: organization?.mobileNumber,
            email: organization?.email,
            expiryDate: expiryDate,
            createdAt: organization?.createdAt,
          };
          monthAgoExpiredArray.push(orgData);
        }
        for (let email of [
          '<EMAIL>',
          '<EMAIL>',
          '<EMAIL>',
          '<EMAIL>',
        ]) {
          const mailOptions = {
            data: {
              oneWeekAboutToExpireArray,
              monthAgoExpiredArray,
            },
            email: email,
            filePath: 'org-expire-business-team',
            subject: 'Expiring and Expired Organization Report',
            key: 'ORG_EXP_MAIL',
            id: 0,
          };
          const msg = await sendnewMailToBusinessTeam(mailOptions);
        }
        return 'Cron Executed and mails sent Successfully!';
      } catch (error) {
        console.log(`Error in getting expiring organization records in cron:`, error);
      }
    } catch (error) {
      return console.log('getExpiringOrganizations ERROR', error);
    }
  }

  async createNoticeResponse(userId: number, body: any) {
    try {
      const user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
      const fyaNotice: any = await AutFyaNotice.findOne({ where: { id: body.noticeId } });
      if (fyaNotice) {
        const response = new AutProceedingResponseFya();
        response.responseType = body?.responseType;
        response.remarks = body?.remarks;
        response.submittedOn = body?.submittedOn;
        response.noticeId = fyaNotice?.documentIdentificationNumber;
        response.clientId = body?.clientId;
        response.organizationId = user?.organization?.id;
        response.createdType = CreatedType.MANUAL;
        response.fyaNotice = fyaNotice;
        await response.save();
        return true;
      }
    } catch (error) {
      console.log('Error Occur while CreateFyaNotice', error?.message);
    }
  }

  async updateNoticeResponse(userId: number, body: any) {
    try {
      const user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
      if (body?.id) {
        const response = await AutProceedingResponseFya.findOne({
          where: { id: body?.id, organizationId: user?.organization?.id },
        });
        if (response) {
          response.responseType = body?.responseType;
          response.remarks = body?.remarks;
          response.submittedOn = body?.submittedOn;
          await response.save();
        }
      }
    } catch (error) {
      console.log('Error occur while Update Notice Response', error?.message);
      throw new BadRequestException('Error Occur while Update Notice Response', error?.message);
    }
  }

  async manualDueDate(userId: number, body: any) {
    try {
      if (body?.type === 'FYA') {
        const fyaNotice = await AutFyaNotice.findOne({
          where: { id: body?.noticeId },
        });
        if (fyaNotice) {
          fyaNotice.manualDueDate = body?.dueDate;
          await fyaNotice.save();
          return true;
        }
      }
    } catch (error) {
      console.log('Error Occur while manualDueDate', error?.message);
    }
  }

  async deleteNoticeResponse(userId: number, id: number) {
    try {
      const user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
      if (id) {
        const response = await AutProceedingResponseFya.findOne({
          where: { id: id, organizationId: user?.organization?.id },
        });
        if (response) {
          const storageItems = await Storage.find({
            where: { autProceedingResponseFya: response },
          });
          if (storageItems && storageItems.length > 0) {
            for (const item of storageItems) {
              await this.attachmentFyaService.deleteStorageFile(item.id, user.id);
            }
          }

          if (response) {
            await response.remove();
            return true;
          }
        }
      }
    } catch (error) {
      console.log(error);
      throw new BadRequestException('Error Occur while deleting Notice', error?.message);
    }
  }
}
