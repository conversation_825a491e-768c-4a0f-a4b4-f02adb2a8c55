export interface FindQueryProps {
  category: string[];
  subCategory: string[] | null;
  monthAdded: string;
  offset: number;
  limit: number;
  search: string;
}

export enum CategoryEnum {
  INDIVIDUAL = 'individual',
  HINDU_UNDIVIDED_FAMILY = 'huf',
  PARTNERSHIP_FIRM = 'partnership_firm',
  LIMITED_LIABILITY_PARTNERSHIP = 'llp',
  COMPANY = 'company',
  TRUST = 'trust',
  SOCIETY = 'society',
  ASSOCIATION_OF_PERSONS = 'aop',
  BODY_OF_INDIVIDUALS = 'boi',
  COROPORATIONS = 'corporations',
  GOVERNMENT = 'government',
  ARTIFICIAL_JURIDICAL_PERSON = 'artificial_judicial_person',
  LOCAL_AUTHORITY = 'local_authority',
}

export enum SubCategoryEnum {
  NONE = '',
  INDIAN_FIRM = 'indian',
  FOREIGN_FIRM = 'foreign',
  INDIAN = 'indian',
  FOREIGN = 'foreign',
  PRIVATE_LIMITED = 'private',
  PUBLIC_LIMITED = 'public',
  GOVERNMENT = 'government',
  OPC = 'opc',
  'SECTION-8' = 'sec_8',
  PUBLIC_TRUST = 'public_trust',
  PRIVATE_DISCRETIONARY_TRUST = 'private_discretionary_trust',
  COOPERATIVE_SOCIETY = 'cooperative_society',
  STATE = 'state',
  CENTRAL = 'central',
}
