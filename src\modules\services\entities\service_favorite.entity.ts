// favorite.entity.ts
import {
  Entity,
  PrimaryGeneratedColumn,
  ManyToOne,
  CreateDateColumn,
  BaseEntity,
  Column,
} from 'typeorm';
import { Service } from './service.entity';
import { Organization } from 'src/modules/organization/entities/organization.entity';

@Entity('service_favorite')
export class ServiceFavorite extends BaseEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  organizationId: number;

  @ManyToOne(() => Service, (service) => service.favorites, { onDelete: 'CASCADE' })
  service: Service;

  @CreateDateColumn()
  createdAt: Date;
}
