import { IsNotEmpty, IsString, IsNumber, IsBoolean, IsDateString, IsOptional } from 'class-validator';

class CreateQtmSuperAdminActivityDto {
  @IsNotEmpty()
  @IsString()
  type: string;

  @IsNotEmpty()
  @IsNumber()
  id: number;

  @IsOptional()
  @IsDateString()
  createdAt: string;

  @IsOptional()
  @IsDateString()
  lastUpdated: string;

  @IsOptional()
  @IsNumber()
  updatedUserId: number;

  @IsNotEmpty()
  @IsBoolean()
  toProduction: boolean;

  @IsOptional()
  combinedResponses:any;

  @IsNumber()
  prodTypeId: number;

}

export default CreateQtmSuperAdminActivityDto;
