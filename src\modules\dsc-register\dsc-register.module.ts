import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { DscRegisterController } from './ds-register.controller';
import DscActivity from './entity/dsc-activity.entity';
import DscRegister from './entity/dsc-register.entity';
import { DscRegisterService } from './dsc-register.service';
import { DscSubscriber } from 'src/event-subscribers/dscactivity.subscriber';
import { DscAddSubscriber } from 'src/event-subscribers/dscAdd.subscriber';
import { DscDeleteSubscriber } from 'src/event-subscribers/dscDelete.subscriber';
import DscApply from './entity/dsc-apply.entity';
import { StorageService } from '../storage/storage.service';
import { AwsService } from '../storage/upload.service';
import { OneDriveStorageService } from '../ondrive-storage/onedrive-storage.service';
import { AttachmentsService } from '../tasks/services/attachments.service';
import { BharathStorageService } from '../storage/bharath-storage.service';
import { BharathCloudService } from '../storage/bharath-upload.service';
import { GoogleDriveStorageService } from '../ondrive-storage/googledrive-storage.service';

@Module({
  imports: [TypeOrmModule.forFeature([DscRegister, DscActivity, DscApply])],
  controllers: [DscRegisterController],
  providers: [DscRegisterService,
    DscAddSubscriber,
    DscDeleteSubscriber,
    DscSubscriber,
    StorageService,
    AwsService,
    OneDriveStorageService,
    AttachmentsService,
    BharathStorageService,
    BharathCloudService,
    GoogleDriveStorageService
  ],
})
export class DscRegisterModule { }
