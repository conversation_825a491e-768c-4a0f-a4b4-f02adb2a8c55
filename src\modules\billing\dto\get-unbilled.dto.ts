import { IsOptional, IsNotEmpty, IsNumberString } from 'class-validator';

export class GetUnbilledTasksDto {
  @IsOptional()
  search: string;

  @IsOptional()
  offset: number;

  @IsOptional()
  limit: number;

  @IsOptional()
  client: number;

  @IsOptional()
  clientGroup: number;

  @IsOptional()
  isinvoiced: boolean;

  @IsOptional()
  clientType: string | null;

  @IsOptional()
  sort: string
}
