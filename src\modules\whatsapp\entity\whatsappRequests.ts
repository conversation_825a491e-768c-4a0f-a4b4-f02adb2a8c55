import { <PERSON><PERSON> } from "aws-sdk/clients/robomaker";
import { BaseEntity, Column, CreateDateColumn, Entity, PrimaryColumn, PrimaryGeneratedColumn, UpdateDateColumn } from "typeorm";

export enum UserStatus {
    ACTIVE = 'ACTIVE',
    INACTIVE = 'INACTIVE',
    DELETED = 'DELETED',
  }
@Entity()
class WhatsappRequests extends BaseEntity {
    @PrimaryGeneratedColumn()
    id: number;

    @Column()
    mobileNumber: string;

    @Column()
    message: string;

    @Column({ type: 'timestamp', nullable: true })
    createdTimestamp: Date;

    @UpdateDateColumn()
    statusUpdatedTimestamp : Date;

   }

export default WhatsappRequests;