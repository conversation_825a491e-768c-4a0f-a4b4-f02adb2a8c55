import { Optional } from '@nestjs/common';
import Client from 'src/modules/clients/entity/client.entity';
import { Organization } from 'src/modules/organization/entities/organization.entity';
import {
  BaseEntity,
  Column,
  CreateDateColumn,
  Entity,
  ManyToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import TanClientCredentials from './tan-client-credentials.entity';

@Entity()
class TanCommunicationInbox extends BaseEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  commRefNo: string;

  @Column()
  commCat: string;

  @Column()
  fy: string;

  @Column()
  hidFy: string;

  @Column()
  qt: string;

  @Column()
  hidQt: string;

  @Column()
  formType: string;

  @Column()
  date: string;

  @Column()
  isRead: string;

  @Column()
  description: string;

  @Column()
  commMstrId: string;

  @Column()
  commInbId: string;

  @Column()
  category: string;

  @Column()
  comCatId: string;

  @Column()
  certNum: string;

  @CreateDateColumn()
  createdAt: string;

  @UpdateDateColumn()
  updatedAt: string;

  @Column()
  organizationId: number;

  @Column()
  clientId: number;

  @Column()
  type: string;

  @ManyToOne(() => Client, (client) => client.tanCommunicationInbox, { onDelete: 'SET NULL' })
  client: Client;

  @ManyToOne(() => TanClientCredentials, (tanClientCredentials) => tanClientCredentials.tanTraces, {
    onDelete: 'SET NULL',
  })
  tanClientCredentials: TanClientCredentials;
}

export default TanCommunicationInbox;
