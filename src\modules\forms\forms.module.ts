import { Modu<PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { FormListner } from 'src/event-listeners/form.listener';
import { FormsController } from './controllers/form.controller';
import { ValidationsController } from './controllers/validations.controller';
import {
  EsignTransaction,
  EsignTransactionSchema,
} from './schemas/esign-transaction.schema';
import {
  FormActivity,
  FormActivitySchema,
} from './schemas/form-activity.schema';
import { Form, FormSchema } from './schemas/form.schema';
import { Validation, ValidationSchema } from './schemas/validation.schema';
import { FormsService } from './services/forms.service';
import { ValidataionsService } from './services/validations.service';
import { AwsService } from 'src/modules/storage/upload.service';
import { FormsSubscriber } from 'src/event-subscribers/forms.subscriber';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: Form.name, schema: FormSchema },
      {
        name: Validation.name,
        schema: ValidationSchema,
      },
      { name: FormActivity.name, schema: FormActivitySchema },
      { name: EsignTransaction.name, schema: EsignTransactionSchema },
    ]),
  ],
  controllers: [FormsController, ValidationsController],
  providers: [AwsService, FormsService, ValidataionsService,FormsSubscriber, FormListner],
})
export class FormsModule {}
