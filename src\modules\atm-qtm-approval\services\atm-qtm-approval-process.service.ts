import { BadRequestException, ConflictException, Injectable, NotFoundException } from '@nestjs/common';
import CreateApprovalProcessDto from '../dto/create-approval-aprocess.dto';
import { In, createQueryBuilder } from 'typeorm';
import { User } from 'src/modules/users/entities/user.entity';
import OrgApprovals from '../entities/org-approvals.entity';
import { Role } from 'src/modules/roles/entities/role.entity';
import OrgApprovalLevel from '../entities/org-approvals-level.entity';
import CreareProcedureDto from '../dto/create-procedure.dto';
// import QtmTemplateCat from "src/modules/camunda/entity/qtm-template-cat.entity";
import ApprovalProcedures, { APPROVALS_PROCEDURE_STATUS } from '../entities/approval-procedures.entity';
import { Organization } from 'src/modules/organization/entities/organization.entity';
import Task from 'src/modules/tasks/entity/task.entity';
import { TaskRecurringStatus, TaskStatusEnum } from 'src/modules/tasks/dto/types';
import { set } from 'lodash';

@Injectable()
export class AtmQtmApprovalService {

  async findProcedure(userId: number, id: number) {
    let user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
    const result = await ApprovalProcedures.findOne({
      where: {
        id: id,
        organization: user?.organization?.id
      },
      relations: ['approval', 'approval.approvalLevels', 'approval.approvalLevels.role', 'approval.approvalLevels.user'],
    });

    return result;
  }


  async updateProcedure(id: number, body: CreareProcedureDto, userId: number) {
    return;
    let procedure = await ApprovalProcedures.findOne({
      where: {
        id: id,
      },
    });
    let user = await User.findOne({ where: { id: userId } });

    if (body.name.trim() !== procedure.name.trim()) {
      let oldApprovalProcedures = await ApprovalProcedures.findOne({ where: { name: body.name.trim(), organization: user.organization } });
      if (oldApprovalProcedures) {
        throw new BadRequestException("Given Name Already Exists in your Organization");
      }
    }

    const approval = await OrgApprovals.findOne({
      where: {
        id: body,
      },
    });

    procedure.name = body.name.trim();
    procedure.description = body.description ? body.description.trim() : body.description;
    procedure.module = body.module;
    // procedure.category = body.category;
    procedure.approval = approval;
    await procedure.save();
  }

  async createProcedure(body: CreareProcedureDto, userId: number) {
    const user = await User.findOne({
      where: {
        id: userId,
      },
    });
    let oldprocedure = await ApprovalProcedures.findOne({
      where: {
        name: body.name.trim(),
        organization: user.organization
      },
    });

    if (oldprocedure) {
      throw new BadRequestException("Given Approval Name Already Exists in your Organization");
    }

    const orgApprovals = new OrgApprovals();
    orgApprovals.name = body.name.trim();
    orgApprovals.user = user;
    orgApprovals.organization = user.organization;
    let approvalLevels = [];
    for (let level of body.approvalLevels) {
      let approvalLevel = new OrgApprovalLevel();
      let role = await Role.findOne({ where: { id: level.roleId } });
      let user = await User.findOne({ where: { id: level.userId } });
      approvalLevel.role = role;
      approvalLevel.user = user;
      approvalLevel.level = level.level;
      approvalLevels.push(approvalLevel);
    }
    orgApprovals.approvalLevels = approvalLevels;
    let procedure = new ApprovalProcedures();
    procedure.name = body?.name.trim();
    procedure.description = body?.description ? body?.description.trim() : body?.description;
    procedure.module = body.module;
    procedure.approval = orgApprovals;
    procedure.organization = user.organization;
    procedure.user = user;
    await procedure.save();
    return { success: true };
  }

  async getProcedures(userId: number) {
    const user = await User.findOne({
      where: {
        id: userId,
      },
    });

    const result = await ApprovalProcedures.find({
      where: {
        organization: user.organization.id,
        status: APPROVALS_PROCEDURE_STATUS.CREATED
      },

      relations: ['approval',
        'approval.approvalLevels',
        'approval.approvalLevels.user',
        'approval.approvalLevels.user.imageStorage',
      ],
      order: { createdAt: 'DESC' }
    });
    return result;
  }

  async deleteProcedure(id: number) {
    const procedure = await ApprovalProcedures.findOne({
      where: { id },
      relations: ['approval'],
    });


    const pendingTasks = await Task.find({
      where: {
        approvalProcedures: procedure,
        status: In([TaskStatusEnum.TODO,
        TaskStatusEnum.IN_PROGRESS, TaskStatusEnum.ON_HOLD, TaskStatusEnum.UNDER_REVIEW]),
        recurringStatus: TaskRecurringStatus.CREATED
      },
      relations: ['approvalProcedures']
    });
    if (pendingTasks.length > 0) {
      return pendingTasks?.map(task => task.taskNumber)
      throw new BadRequestException('Approval has Pending Tasks')
    };
    // const pendingRecurringTasks = await Task.find({
    //   where: {
    //     approvalProcedures: procedure,
    //     recurringStatus: TaskRecurringStatus.PENDING
    //   }
    // });
    await Task.update(
      {
        approvalProcedures: procedure,
        recurringStatus: TaskRecurringStatus.PENDING,
      },
      { approvalProcedures: null, processInstanceId: null, approvalStatus: null }
    );

    await Task.update(
      {
        approvalProcedures: procedure,
        status: In([TaskStatusEnum.DELETED, TaskStatusEnum.TERMINATED])
      },
      { approvalProcedures: null, processInstanceId: null, approvalStatus: null }
    )


    const approval = procedure.approval;
    const count = await ApprovalProcedures.count({
      where: { approval: { id: approval.id } },
    });
    // await ApprovalProcedures.remove(procedure);
    procedure.status = APPROVALS_PROCEDURE_STATUS.DELETED;
    await procedure.save();
    if (count === 1) {
      approval.status = APPROVALS_PROCEDURE_STATUS.DELETED;
      await OrgApprovals.remove(approval);
    }
    return { success: true, message: 'Deleted successfully' };
  }
}
