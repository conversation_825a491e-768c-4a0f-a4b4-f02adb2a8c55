import { IsNotEmpty, IsOptional } from 'class-validator';
import { RegistrationType } from '../entity/gstr-register.entity';

class FindComplianceDto {
    @IsNotEmpty()
    selectedYear: string;

    @IsOptional()
    selectedMonth: string;

    @IsOptional()
    selectedQuarter: string;

    @IsOptional()
    type: RegistrationType;

    @IsOptional()
    rtntype: string;

    @IsOptional()
    clientId: number;

    @IsOptional()
    clientGroupId: number;

}

export default FindComplianceDto;