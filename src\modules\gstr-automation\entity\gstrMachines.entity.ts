import { Optional } from '@nestjs/common';
import { Organization } from 'src/modules/organization/entities/organization.entity';
import {
  BaseEntity,
  Column,
  CreateDateColumn,
  Entity,
  ManyToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { User } from 'src/modules/users/entities/user.entity';

@Entity()
class GstrMachines extends BaseEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  machineName: string;

  @Column()
  status: string;

  @Column()
  gstrCredentialsId: number;

  @Column()
  userId: number;

  @Column()
  remarks: string;

  @Column('json')
  completeModules: object;

  @CreateDateColumn()
  createdAt: string;

  @UpdateDateColumn()
  updatedAt: string;
}

export default GstrMachines;
