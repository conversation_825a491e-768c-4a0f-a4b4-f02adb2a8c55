import {
  BadRequestException,
  Injectable,
  InternalServerErrorException,
} from '@nestjs/common';
import axios from 'axios';

@Injectable()
export class StrapiService {

  private readonly apiUrl = 'http://ec2-13-234-30-250.ap-south-1.compute.amazonaws.com:1337/api/'; // Replace with your Strapi API URL
  private readonly token = {
    accessToken: '46f7627949496b15b36aadbbf590db7a079ddfafe33ea2db0b6e25ca525eff20099c19d999cd556972ca316d37cb7a283ad1db9ad5e466d74720b59600769e7393a14bd7023f1da6e9cdc97251a91fd6964caf0d7bdc648ac5e6e0f70ba6074ca776c009b0a1f02ef18738c9a6d291af5403d8d697083a7c54f85265edfcfe87'
  };

  constructor() { }

  async getAllBlogs() {
    let res = await axios({
      method: 'GET',
      url: `${this.apiUrl}prismusers`,
      headers: {
        'Authorization': `Bearer ${this.token.accessToken}`,
        'Content-Type': 'application/json',
      },
      params: {},
    });

    return res.data;

  }



}
