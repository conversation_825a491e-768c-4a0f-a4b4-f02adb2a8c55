import { Service } from 'src/modules/services/entities/service.entity';
import Task from 'src/modules/tasks/entity/task.entity';
import { Organization } from 'src/modules/organization/entities/organization.entity';
import {
  AfterLoad,
  BaseEntity,
  Column,
  CreateDateColumn,
  Entity,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';

@Entity()
class Category extends BaseEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  name: string;

  @Column({ nullable: true })
  image: string;

  @Column({ nullable: true })
  color: string;

  @Column({ default: false })
  defaultOne: boolean;

  @Column({ default: false })
  isActive: boolean;

  @ManyToOne(() => Category, (category) => category.subCategories, { onDelete: 'CASCADE' })
  parentCategory: Category;

  @OneToMany(() => Category, (category) => category.parentCategory, { cascade: true })
  subCategories: Category[];

  @OneToMany(() => Task, (task) => task.category)
  tasks: Task[];

  @OneToMany(() => Service, (service) => service.category)
  services: Service[];

  @ManyToOne(() => Organization, (organization) => organization.categories)
  organization: Organization;

  @Column({ default: false })
  fromAdmin: boolean;

  @Column({ nullable: true })
  adminCategoryId: number;

  @CreateDateColumn()
  createdAt: string;

  @UpdateDateColumn()
  updatedAt: string;

  @Column({ default: 1 })
  version: number;

  imageUrl: string;

  @AfterLoad()
  renderUrl() {
    if (this.image) {
      this.imageUrl = `${process.env.AWS_BASE_URL}/${this.image}`;
    }
  }

}

export default Category;
