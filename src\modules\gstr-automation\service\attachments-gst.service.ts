import {
  ConflictException,
  forwardRef,
  Inject,
  Injectable,
  InternalServerErrorException,
} from '@nestjs/common';
import { StorageSystem } from 'src/modules/organization/entities/organization.entity';
import { User } from 'src/modules/users/entities/user.entity';
import Storage, { StorageType } from 'src/modules/storage/storage.entity';
import { v4 as uuidv4 } from 'uuid';

import { AwsService } from 'src/modules/storage/upload.service';
import { StorageService } from 'src/modules/storage/storage.service';
import * as moment from 'moment';
import { OneDriveStorageService } from 'src/modules/ondrive-storage/onedrive-storage.service';
import AuthToken, { AuthTokenType } from 'src/modules/ondrive-storage/auth-token.entity';
import Activity, { ActivityType } from 'src/modules/activity/activity.entity';
import { Event_Actions } from 'src/event-listeners/actions';
import { BharathStorageService } from 'src/modules/storage/bharath-storage.service';
import { getName } from 'src/utils/FilterSpecialChars';
import { GoogleDriveStorageService } from 'src/modules/ondrive-storage/googledrive-storage.service';
import notice from 'src/modules/automation/entities/aut_income_tax_eproceedings_fya_notice.entity';
import GstrNoticeOrders from '../entity/noticeOrders.entity';
import GstrAdditionalNoticeOrders from '../entity/gstrAdditionalOrdersAndNotices.entity';

@Injectable()
export class AttachmentGstService {
  constructor(
    private uploadService: AwsService,
    private bharahServce: BharathStorageService,
    @Inject(forwardRef(() => StorageService))
    private storageService: StorageService,
    @Inject(forwardRef(() => OneDriveStorageService))
    private oneDriveStorageService: OneDriveStorageService,
    @Inject(forwardRef(() => GoogleDriveStorageService))
    private googleDriveStorageService: GoogleDriveStorageService,
  ) {}

  async saveAttachment(noticeId: number, files: Express.Multer.File[], userId, type) {
    let user = await User.findOne({
      where: { id: userId },
      relations: ['organization'],
    });
    const storageSystem = user?.organization?.storageSystem;
    if (storageSystem === StorageSystem.AMAZON) {
      return await this.addAttachment(noticeId, files, userId, type);
    } else if (storageSystem === StorageSystem.MICROSOFT) {
      return await this.addOneDriveAttachments(noticeId, files, userId, type);
    } else if (storageSystem === StorageSystem.GOOGLE) {
      return await this.addGoogleAttachments(noticeId, files, userId, type);
    }
  }

  async addAttachment(noticeId: number, files: Express.Multer.File[], userId: number, type) {
    try {
      let notice;
      if (type === 'NoticeAndOrder') {
        notice = await GstrNoticeOrders.findOne({
          where: { id: noticeId },
          relations: ['client'],
        });
      } else if (type === 'AdditionalNotice') {
        notice = await GstrAdditionalNoticeOrders.findOne({
          where: { id: noticeId },
          relations: ['client'],
        });
      }

      let user = await User.findOne({
        where: { id: userId },
        relations: ['organization'],
      });

      let existingStorage = await this.existingGstrStorage(notice, user, type);
      let attachments: Storage[] = [];
      let errors: string[] = [];

      for (let file of files) {
        const { buffer, mimetype, originalname, size } = file;
        const existingAttachement = await Storage.find({
          where: { parent: existingStorage, name: originalname },
        });
        if (existingAttachement?.length) {
          errors.push(`File name ${originalname} already exists`);
          continue;
        }

        const { freeSpace } = await this.storageService.getOrgStorage(userId);
        if (!(freeSpace - file.size >= 0)) {
          throw new ConflictException(
            'We regret to inform you that the storage limit for your organization has been exceeded. As a result, no further data can be stored until the storage usage is reduced or additional storage capacity is allocated.',
          );
        }
        let key = `storage/automation/${notice?.client?.id}/ ${file.originalname}`;

        let upload: any = await this.uploadService.upload(buffer, key, mimetype);
        let storage = new Storage();
        storage.name = originalname;
        storage.file = upload.Key;
        storage.fileSize = size;
        storage.fileType = mimetype;
        storage.client = notice.client;
        storage.type = StorageType.FILE;
        storage.parent = existingStorage;
        storage.user = user;
        storage.storageSystem = StorageSystem.AMAZON;
        if (type === 'NoticeAndOrder') {
          storage.gstrNoticeOrders = notice;
        } else if (type === 'AdditionalNotice') {
          storage.gstrAdditionalNoticeOrders = notice;
        }
        attachments.push(storage);
      }

      await Storage.save(attachments);

      for (let i of attachments) {
        let collectactivity = new Activity();
        collectactivity.action = Event_Actions.ATTACHEMENT_ADDED;
        collectactivity.actorId = user.id;
        collectactivity.type = ActivityType.CLIENT;
        collectactivity.typeId = notice?.client?.id;
        collectactivity.remarks = `Atom Pro Attachement "${i.name}" Added by ${user.fullName}`;
        await collectactivity.save();
      }
      if (errors?.length) {
        return { errors };
      } else {
        return {
          success: true,
        };
      }
    } catch (error) {
      console.log(error);
      throw new InternalServerErrorException(error);
    }
  }

  async getFY(year) {
    // console.log("Current_year", year);
    if (!year) return 'Others';
    const nextYear = Number(year) + 1;
    const shortNextYear = nextYear.toString().slice(-2);
    return `AY ${year}-${shortNextYear}`;
  }

  async convertFYString(fyString) {
    if (!fyString || fyString === 'null' || fyString === 'undefined') return 'Others';
    const [startYear, endYear] = fyString.split('-');
    const shortEndYear = endYear.slice(-2);
    return `FY ${startYear}-${shortEndYear}`;
  }

  async capitalizeFirstLetterOfEachWord(str) {
    return str
      .split(' ')
      .map((word) => {
        return word.charAt(0).toUpperCase() + word.slice(1).toLowerCase();
      })
      .join(' ');
  }

  async existingGstrStorage(notice: any, user: User, type: string, storageType?: StorageSystem) {
    try {
      if (!notice?.client) return null;
      let atomFolder: Storage;
      let clientFolder: Storage;
      let displayNameFolder: Storage;
      let atomProFolder: Storage;
      let gstFolder: Storage;
      let categoryFolder: Storage;
      let subCategoryFolder: Storage;
      let fyFolder: Storage;
      const category =
        type === 'NoticeAndOrder' ? 'Notice And Orders' : 'Additional Notices And Orders';
      const subCategory = type === 'NoticeAndOrder' ? notice?.type : notice?.caseTypeName;

      if (storageType && storageType === StorageSystem.MICROSOFT) {
        atomFolder = await Storage.findOne({
          where: {
            name: 'Atom',
            organization: user?.organization,
            show: false,
          },
        });

        if (!atomFolder) {
          const folderData = await this.oneDriveStorageService.createOneDriveFolder(
            user?.id,
            'Atom',
          );
          atomFolder = new Storage();
          atomFolder.name = 'Atom';
          atomFolder.organization = user?.organization;
          atomFolder.type = StorageType.FOLDER;
          atomFolder.uid = uuidv4();
          atomFolder.fileId = folderData.id;
          atomFolder.show = false;
          atomFolder.storageSystem = StorageSystem.MICROSOFT;
          atomFolder.authId = user?.organization?.id;
          await atomFolder.save();
        }
        clientFolder = await Storage.findOne({
          where: {
            name: 'Clients',
            organization: user?.organization,
            show: false,
          },
        });

        if (!clientFolder) {
          const folderData = await this.oneDriveStorageService.createOneDriveFolder(
            user?.id,
            'Clients',
            atomFolder.fileId,
          );
          clientFolder = new Storage();
          clientFolder.name = 'Clients';
          clientFolder.organization = user?.organization;
          clientFolder.type = StorageType.FOLDER;
          clientFolder.uid = uuidv4();
          clientFolder.fileId = folderData.id;
          clientFolder.show = false;
          clientFolder.storageSystem = StorageSystem.MICROSOFT;
          clientFolder.authId = user?.organization.id;
          await clientFolder.save();
        }
        displayNameFolder = await Storage.findOne({
          where: {
            name: notice?.client?.displayName,
            organization: user?.organization,
            show: false,
            type: StorageType.FOLDER,
          },
        });

        if (!displayNameFolder) {
          const folderData = await this.oneDriveStorageService.createOneDriveFolder(
            user?.id,
            notice.client.displayName,
            clientFolder?.fileId,
          );
          displayNameFolder = new Storage();
          displayNameFolder.name = notice.client?.displayName;

          displayNameFolder.organization = user.organization;
          displayNameFolder.type = StorageType.FOLDER;
          displayNameFolder.uid = uuidv4();
          displayNameFolder.fileId = folderData.id;
          displayNameFolder.show = false;
          displayNameFolder.storageSystem = StorageSystem.MICROSOFT;
          displayNameFolder.authId = user.organization.id;
          if (notice?.client) {
            displayNameFolder.client = notice.client;
          }
          await displayNameFolder.save();
        }
      } else if (storageType && storageType === StorageSystem.GOOGLE) {
        atomFolder = await Storage.findOne({
          where: {
            name: 'Atom',
            organization: user.organization.id,
            show: false,
            type: StorageType.FOLDER,
          },
        });
        if (!atomFolder) {
          const folderData = await this.googleDriveStorageService.createGoogleDriveFolder(
            user?.id,
            'Atom',
          );
          atomFolder = new Storage();
          atomFolder.name = 'Atom';
          atomFolder.organization = user.organization;
          atomFolder.type = StorageType.FOLDER;
          atomFolder.uid = uuidv4();
          atomFolder.fileId = folderData.id;
          atomFolder.show = false;
          atomFolder.storageSystem = StorageSystem.GOOGLE;
          atomFolder.authId = user.organization.id;
          await atomFolder.save();
        }
        clientFolder = await Storage.findOne({
          where: {
            name: 'Clients',
            organization: user.organization.id,
            show: false,
            type: StorageType.FOLDER,
          },
        });
        if (!clientFolder) {
          const folderData = await this.googleDriveStorageService.createGoogleDriveFolder(
            user?.id,
            'Clients',
            atomFolder.fileId,
          );
          clientFolder = new Storage();
          clientFolder.name = 'Clients';
          clientFolder.organization = user.organization;
          clientFolder.type = StorageType.FOLDER;
          clientFolder.uid = uuidv4();
          clientFolder.fileId = folderData.id;
          clientFolder.show = false;
          clientFolder.storageSystem = StorageSystem.GOOGLE;
          clientFolder.authId = user.organization.id;
          await clientFolder.save();
        }
        displayNameFolder = await Storage.findOne({
          where: {
            name: notice.client.displayName,
            organization: user.organization.id,
            show: false,
            type: StorageType.FOLDER,
            client: notice.client,
          },
        });
        if (!displayNameFolder) {
          const folderData = await this.googleDriveStorageService.createGoogleDriveFolder(
            user?.id,
            notice.client.displayName,
            clientFolder?.fileId,
          );
          displayNameFolder = new Storage();
          displayNameFolder.name = notice.client.displayName;

          displayNameFolder.organization = user.organization;
          displayNameFolder.type = StorageType.FOLDER;
          displayNameFolder.uid = uuidv4();
          displayNameFolder.fileId = folderData.id;
          displayNameFolder.show = false;
          displayNameFolder.storageSystem = StorageSystem.GOOGLE;
          displayNameFolder.authId = user.organization.id;
          if (notice?.client) {
            displayNameFolder.client = notice.client;
          }
          await displayNameFolder.save();
        }
      }

      if (notice.client) {
        atomProFolder = await Storage.findOne({
          where: {
            name: 'Atom Pro',
            client: { id: notice?.client?.id },
            show: true,
          },
        });
      }
      if (!atomProFolder) {
        atomProFolder = new Storage();
        atomProFolder.name = 'Atom Pro';
        atomProFolder.client = notice.client;
        // atomProFolder.clientGroup = notice?.clientGroup;
        atomProFolder.type = StorageType.FOLDER;
        atomProFolder.uid = uuidv4();
        atomProFolder.show = true;
        atomProFolder.authId = notice.organizationId;
        if (storageType && storageType === StorageSystem.MICROSOFT) {
          const folderData = await this.oneDriveStorageService.createOneDriveFolder(
            user?.id,
            'Atom Pro',
            displayNameFolder?.fileId,
          );
          atomProFolder.fileId = folderData.id;
          atomProFolder.storageSystem = StorageSystem.MICROSOFT;
        } else if (storageType && storageType === StorageSystem.GOOGLE) {
          const folderData = await this.googleDriveStorageService.createGoogleDriveFolder(
            user?.id,
            'Atom Pro',
            displayNameFolder?.fileId,
          );
          atomProFolder.fileId = folderData.id;
          atomProFolder.storageSystem = StorageSystem.GOOGLE;
        }
        await atomProFolder.save();
      }

      gstFolder = await Storage.findOne({
        where: {
          name: 'GST',
          client: { id: notice?.client?.id },
          show: true,
          parent: { id: atomProFolder?.id },
        },
      });

      if (!gstFolder) {
        gstFolder = new Storage();
        gstFolder.name = 'GST';
        gstFolder.client = notice.client;
        gstFolder.type = StorageType.FOLDER;
        gstFolder.uid = uuidv4();
        gstFolder.show = true;
        gstFolder.authId = notice.organizationId;
        gstFolder.parent = atomProFolder;
        if (storageType && storageType === StorageSystem.MICROSOFT) {
          const folderData = await this.oneDriveStorageService.createOneDriveFolder(
            user?.id,
            'GST',
            atomProFolder?.fileId,
          );
          gstFolder.fileId = folderData.id;
          gstFolder.storageSystem = StorageSystem.MICROSOFT;
        } else if (storageType && storageType === StorageSystem.GOOGLE) {
          const folderData = await this.googleDriveStorageService.createGoogleDriveFolder(
            user?.id,
            'GST',
            atomProFolder?.fileId,
          );
          gstFolder.fileId = folderData.id;
          gstFolder.storageSystem = StorageSystem.GOOGLE;
        }
        await gstFolder.save();
      }

      categoryFolder = await Storage.findOne({
        where: {
          name: await this.capitalizeFirstLetterOfEachWord(category),
          client: { id: notice?.client?.id },
          show: true,
          parent: { id: gstFolder?.id },
        },
      });

      if (!categoryFolder) {
        categoryFolder = new Storage();
        categoryFolder.name = await this.capitalizeFirstLetterOfEachWord(category);
        categoryFolder.client = notice.client;
        categoryFolder.type = StorageType.FOLDER;
        categoryFolder.uid = uuidv4();
        categoryFolder.show = true;
        categoryFolder.authId = notice.organizationId;
        categoryFolder.parent = gstFolder;
        if (storageType && storageType === StorageSystem.MICROSOFT) {
          const folderData = await this.oneDriveStorageService.createOneDriveFolder(
            user?.id,
            await this.capitalizeFirstLetterOfEachWord(category),
            gstFolder?.fileId,
          );
          categoryFolder.fileId = folderData.id;
          categoryFolder.storageSystem = StorageSystem.MICROSOFT;
        } else if (storageType && storageType === StorageSystem.GOOGLE) {
          const folderData = await this.googleDriveStorageService.createGoogleDriveFolder(
            user?.id,
            await this.capitalizeFirstLetterOfEachWord(category),
            gstFolder?.fileId,
          );
          categoryFolder.fileId = folderData.id;
          categoryFolder.storageSystem = StorageSystem.GOOGLE;
        }
        await categoryFolder.save();
      }

      subCategoryFolder = await Storage.findOne({
        where: {
          name: await this.capitalizeFirstLetterOfEachWord(subCategory),
          client: { id: notice?.client?.id },
          show: true,
          parent: { id: categoryFolder?.id },
        },
      });

      if (!subCategoryFolder) {
        subCategoryFolder = new Storage();
        subCategoryFolder.name = await this.capitalizeFirstLetterOfEachWord(subCategory);
        subCategoryFolder.client = notice.client;
        subCategoryFolder.type = StorageType.FOLDER;
        subCategoryFolder.uid = uuidv4();
        subCategoryFolder.show = true;
        subCategoryFolder.authId = notice.organizationId;
        subCategoryFolder.parent = categoryFolder;
        if (storageType && storageType === StorageSystem.MICROSOFT) {
          const folderData = await this.oneDriveStorageService.createOneDriveFolder(
            user?.id,
            await this.capitalizeFirstLetterOfEachWord(subCategory),
            categoryFolder?.fileId,
          );
          subCategoryFolder.fileId = folderData.id;
          subCategoryFolder.storageSystem = StorageSystem.MICROSOFT;
        } else if (storageType && storageType === StorageSystem.GOOGLE) {
          const folderData = await this.googleDriveStorageService.createGoogleDriveFolder(
            user?.id,
            await this.capitalizeFirstLetterOfEachWord(subCategory),
            categoryFolder?.fileId,
          );
          subCategoryFolder.fileId = folderData.id;
          subCategoryFolder.storageSystem = StorageSystem.GOOGLE;
        }
        await subCategoryFolder.save();
      }

      if (type === 'NoticeAndOrder') {
        return subCategoryFolder;
      }

      fyFolder = await Storage.findOne({
        where: {
          name: await this.convertFYString(notice?.fy),
          client: { id: notice?.client?.id },
          show: true,
          parent: { id: subCategoryFolder?.id },
        },
      });
      if (!fyFolder) {
        fyFolder = new Storage();
        fyFolder.name = await this.convertFYString(notice?.fy);
        fyFolder.client = notice.client;
        // fyFolder.clientGroup = notice?.clientGroup;
        fyFolder.type = StorageType.FOLDER;
        fyFolder.uid = uuidv4();
        fyFolder.show = true;
        fyFolder.authId = notice.organizationId;
        fyFolder.parent = subCategoryFolder;
        if (storageType && storageType === StorageSystem.MICROSOFT) {
          const folderData = await this.oneDriveStorageService.createOneDriveFolder(
            user?.id,
            await this.convertFYString(notice?.fy),
            gstFolder?.fileId,
          );
          fyFolder.fileId = folderData.id;
          fyFolder.storageSystem = StorageSystem.MICROSOFT;
        } else if (storageType && storageType === StorageSystem.GOOGLE) {
          const folderData = await this.googleDriveStorageService.createGoogleDriveFolder(
            user?.id,
            await this.convertFYString(notice?.fy),
            gstFolder?.fileId,
          );
          fyFolder.fileId = folderData.id;
          fyFolder.storageSystem = StorageSystem.GOOGLE;
        }
        await fyFolder.save();
      }

      return fyFolder;
    } catch (error) {
      console.log(error);
      throw new InternalServerErrorException(error);
    }
  }

  async addOneDriveAttachments(
    noticeId: number,
    files: Express.Multer.File[],
    userId: number,
    type,
  ) {
    try {
      let notice;
      if (type === 'NoticeAndOrder') {
        notice = await GstrNoticeOrders.findOne({
          where: { id: noticeId },
          relations: ['client'],
        });
      } else if (type === 'AdditionalNotice') {
        notice = await GstrAdditionalNoticeOrders.findOne({
          where: { id: noticeId },
          relations: ['client'],
        });
      }
      let user = await User.findOne({
        where: { id: userId },
        relations: ['organization'],
      });

      let token = await AuthToken.findOne({
        where: {
          organizationId: user.organization.id,
          type: AuthTokenType.MICROSFT,
        },
      });
      let existingStorage = await this.existingGstrStorage(
        notice,
        user,
        type,
        StorageSystem.MICROSOFT,
      );
      let taskAttachments: Storage[] = [];
      let errors: string[] = [];

      for (let file of files) {
        const { buffer, mimetype, originalname, size } = file;
        const existingAttachement = await Storage.find({
          where: { parent: existingStorage, name: originalname },
        });
        if (existingAttachement?.length) {
          errors.push(`File name ${originalname} already exists`);
          continue;
        }
        // let key = `${taskStorage.fileId}:/${file.originalname.replace(/[<>:"\/\\|?*.]/g, '')}:`;
        let key = `${existingStorage.fileId}:/${file.originalname
          .replace(/[<>:"\/\\|?*]/g, '')
          .replace(/\.(?=.*\.)/g, '')}:`;
        let upload: any;
        try {
          upload = await this.oneDriveStorageService.upload(
            buffer,
            key,
            mimetype,
            token,
            file,
            userId,
          );
        } catch (err) {
          let error = err?.response?.data?.error;
          if (error.code === 'InvalidAuthenticationToken') {
            await this.oneDriveStorageService.refreshToken(token);
            upload = await this.oneDriveStorageService.upload(
              buffer,
              key,
              mimetype,
              token,
              file,
              userId,
            );
          } else if (error.code === 'quotaLimitReached') {
            throw new ConflictException({
              code: 'NO_STORAGE',
              message: error.message,
            });
          }
        }

        let storage = new Storage();
        storage.name = originalname;
        storage.file = upload.file;
        storage.webUrl = upload.webUrl;
        storage.downloadUrl = upload['@microsoft.graph.downloadUrl'];
        storage.fileId = upload.id;
        storage.authId = user.organization.id;
        storage.fileSize = size;
        storage.fileType = mimetype;
        storage.client = notice.client;
        storage.type = StorageType.FILE;
        storage.parent = existingStorage;
        storage.storageSystem = StorageSystem.MICROSOFT;
        storage.user = user;
        if (type === 'NoticeAndOrder') {
          storage.gstrNoticeOrders = notice;
        } else if (type === 'AdditionalNotice') {
          storage.gstrAdditionalNoticeOrders = notice;
        }
        taskAttachments.push(storage);
      }

      await Storage.save(taskAttachments);

      if (errors?.length) {
        return { errors };
      } else {
        return {
          success: true,
        };
      }
    } catch (err) {
      console.log(err);
      throw new InternalServerErrorException(err);
    }
  }

  async addGoogleAttachments(noticeId: number, files: Express.Multer.File[], userId: number, type) {
    try {
      let notice;
      if (type === 'NoticeAndOrder') {
        notice = await GstrNoticeOrders.findOne({
          where: { id: noticeId },
          relations: ['client'],
        });
      } else if (type === 'AdditionalNotice') {
        notice = await GstrAdditionalNoticeOrders.findOne({
          where: { id: noticeId },
          relations: ['client'],
        });
      }

      let user = await User.findOne({
        where: { id: userId },
        relations: ['organization'],
      });

      let token = await AuthToken.findOne({
        where: {
          organizationId: user.organization.id,
          type: AuthTokenType.GOOGLE,
        },
      });
      let existingStorage = await this.existingGstrStorage(
        notice,
        user,
        type,
        StorageSystem.GOOGLE,
      );
      let taskAttachments: Storage[] = [];
      let errors: string[] = [];

      for (let file of files) {
        const { buffer, mimetype, originalname, size } = file;
        const existingAttachement = await Storage.find({
          where: { parent: existingStorage, name: originalname },
        });
        if (existingAttachement?.length) {
          errors.push(`File name ${originalname} already exists`);
          continue;
        }
        // let key = `${taskStorage.fileId}:/${file.originalname.replace(/[<>:"\/\\|?*.]/g, '')}:`;
        let key = `${existingStorage.fileId}:/${file.originalname
          .replace(/[<>:"\/\\|?*]/g, '')
          .replace(/\.(?=.*\.)/g, '')}:`;
        let upload: any;
        try {
          upload = await this.googleDriveStorageService?.uploadToGoogleDrive(
            file.originalname,
            token,
            buffer,
            existingStorage?.fileId,
            userId,
          );
        } catch (err) {
          let error = err?.response?.data?.error;
          if (error.code === 'InvalidAuthenticationToken') {
            await this.googleDriveStorageService?.refreshToken(token);
            upload = await this.googleDriveStorageService.uploadToGoogleDrive(
              file.originalname,
              token,
              buffer,
              existingStorage?.fileId,
              userId,
            );
          } else if (error.code === 'quotaLimitReached') {
            throw new ConflictException({
              code: 'NO_STORAGE',
              message: error.message,
            });
          }
        }
        let storage = new Storage();
        storage.name = originalname;
        storage.file = upload.file;
        storage.webUrl = upload.webUrl;
        storage.downloadUrl = upload['@microsoft.graph.downloadUrl'];
        storage.fileId = upload.id;
        storage.authId = user.organization.id;
        storage.fileSize = size;
        storage.fileType = mimetype;
        storage.client = notice?.client;
        storage.type = StorageType.FILE;
        storage.parent = existingStorage;
        storage.storageSystem = StorageSystem.GOOGLE;
        storage.user = user;
        if (type === 'NoticeAndOrder') {
          storage.gstrNoticeOrders = notice;
        } else if (type === 'AdditionalNotice') {
          storage.gstrAdditionalNoticeOrders = notice;
        }
        taskAttachments.push(storage);
      }

      await Storage.save(taskAttachments);

      if (errors?.length) {
        return { errors };
      } else {
        return {
          success: true,
        };
      }
    } catch (err) {
      console.log(err);
      throw new InternalServerErrorException(err);
    }
  }

  async deleteStorageFile(storageId: number, userId: number) {
    try {
      await this.storageService.removeFile(storageId, userId);
    } catch (err) {
      console.log(err);
      throw new InternalServerErrorException(err);
    }
  }
}
