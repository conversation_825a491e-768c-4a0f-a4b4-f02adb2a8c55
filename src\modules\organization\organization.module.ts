import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { BankAccountsController } from './controllers/bank-accounts.controller';
import { BillingEntitesController } from './controllers/billing-entity.controller';
import { LicensesController } from './controllers/licenses.controller';
import { OrganizationsController } from './controllers/organizations.controller';
import { TeamsController } from './controllers/teams.controller';
import { BankAccount } from './entities/bank-account.entity';
import { BillingEntity } from './entities/billing-entity.entity';
import { OrganizationLicense } from './entities/organization-license.entity';
import { Organization } from './entities/organization.entity';
import Team from './entities/team.entity';
import { BankAccountsService } from './services/bank-accounts.service';
import { BillingEntitiesService } from './services/billing-entities.service';
import { LicensesService } from './services/licenses.service';
import { OrganizationsService } from './services/orgnizations.service';
import { TeamsService } from './services/teams.service';
import { OrganizationSubscriber } from 'src/event-subscribers/organization-subscriber';
import { OrganizationWalletSubscriber } from 'src/event-subscribers/organization-wallet.subscriber';
import { AwsService } from '../storage/upload.service';
import { StorageService } from '../storage/storage.service';
import { OneDriveStorageService } from '../ondrive-storage/onedrive-storage.service';
import { AttachmentsService } from '../tasks/services/attachments.service';
import { BharathStorageService } from '../storage/bharath-storage.service';
import { BharathCloudService } from '../storage/bharath-upload.service';
import { GoogleDriveStorageService } from '../ondrive-storage/googledrive-storage.service';

@Module({
  imports: [
    TypeOrmModule.forFeature([Organization, Team, OrganizationLicense, BillingEntity, BankAccount]),
  ],
  controllers: [
    LicensesController,
    OrganizationsController,
    BillingEntitesController,
    BankAccountsController,
    TeamsController,
  ],
  providers: [
    OrganizationsService,
    TeamsService,
    LicensesService,
    BillingEntitiesService,
    BankAccountsService,
    OrganizationSubscriber,
    OrganizationWalletSubscriber,
    AwsService,
    BharathStorageService,
    BharathCloudService,
    StorageService,
    AttachmentsService,
    OneDriveStorageService,
    GoogleDriveStorageService,
  ],
})
export class OrganizationsModule {}
