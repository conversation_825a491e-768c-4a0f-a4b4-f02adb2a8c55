import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateReceiptTable1659002827581 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
        CREATE TABLE receipt (
            id int NOT NULL AUTO_INCREMENT,
            PRIMARY KEY (id),
            receipt_number varchar(255) NOT NULL,
            receipt_date date NOT NULL,
            type enum('INVOICE', 'TASK', 'ADVANCE') NOT NULL,
            amount decimal(19,2) NOT NULL,
            tds varchar(255) NULL,
            tds_amount decimal(19,2) NULL,
            payment_mode enum('CASH', 'CHEQUE', 'BANK_TRANSFER', 'CREDIT_CARD', 'UPI') NOT NULL,
            payement_date date NOT NULL,
            reference_number varchar(255) NOT NULL,
            created_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
            updated_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            client_id int NULL,
            organization_id int NULL,
            billing_entity_id int NULL,
            FOREIGN KEY (client_id) REFERENCES client(id) ON DELETE SET NULL,                                                    
            FOREIGN KEY (organization_id) REFERENCES organization(id) ON DELETE SET NULL,
            FOREIGN KEY (billing_entity_id) REFERENCES billing_entity(id) ON DELETE SET NULL
        )            
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}
