import { Optional } from '@nestjs/common';
import Client from 'src/modules/clients/entity/client.entity';
import { Organization, StorageSystem } from 'src/modules/organization/entities/organization.entity';
import {
  BaseEntity,
  Column,
  CreateDateColumn,
  Entity,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import AutFyiNotice from './aut_income_tax_eproceedings_fyi_notice.entity';

@Entity()
class AutProceedingResponseFyi extends BaseEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @ManyToOne(() => AutFyiNotice, (autFyiNotice) => autFyiNotice.responses)
  fyiNotice: AutFyiNotice;

  @Column()
  responseType: string;

  @Column('json')
  attachments: object

  @Column()
  remarks: string;

  @Column()
  submittedOn: string;

  @Column()
  organizationId: number;

  @Column()
  clientId: number;

  @Column()
  noticeId:number;

  @Column({ type: 'enum', enum: StorageSystem, default: StorageSystem.AMAZON })
  storageSystem: StorageSystem;


}

export default AutProceedingResponseFyi;
