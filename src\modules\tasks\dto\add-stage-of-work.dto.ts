import {
  Is<PERSON><PERSON>y,
  IsBoolean,
  IsEnum,
  IsNotEmpty,
  IsOptional,
  IsString,
} from 'class-validator';
import {
  IExtraAttributes,
  StageOfWorkStatus,
  StageOfWorkType,
} from '../entity/stage-of-work.entity';

class AddStageOfWorkDto {
  @IsNotEmpty()
  @IsString()
  name: string;

  @IsOptional()
  @IsString()
  description: string;

  @IsNotEmpty()
  @IsBoolean()
  referenceNumber: boolean;

  @IsOptional()
  referenceNumberValue: string;

  @IsNotEmpty()
  @IsEnum(StageOfWorkType)
  type: StageOfWorkType;

  @IsOptional()
  @IsArray()
  extraAttributes: Array<IExtraAttributes>;

  @IsOptional()
  attachmentFile: string;

  @IsOptional()
  @IsEnum(StageOfWorkStatus)
  status: StageOfWorkStatus;

  @IsOptional()
  storage: any;
}

export default AddStageOfWorkDto;
