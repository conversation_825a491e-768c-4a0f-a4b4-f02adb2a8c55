import { Optional } from '@nestjs/common';
import { Organization } from 'src/modules/organization/entities/organization.entity';
import {
  BaseEntity,
  Column,
  CreateDateColumn,
  Entity,
  ManyToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';

@Entity()
class AutJurisdictionDetails extends BaseEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  pan:string;

  @Column()
  areaCode: string;

  @Column()
  aoType: string;

  @Column()
  rangeCode: number;

  @Column()
  aoNumber: number;

  @Column()
  jurisdiction: string;

  @Column()
  emailId: string;

  @Column()
  buildingName: string;

  @ManyToOne(() => Organization, (organization) => organization.labels)
  organization: Organization;


  @Column()
  clientId: number;

  @CreateDateColumn()
  createdAt: string;

  @UpdateDateColumn()
  updatedAt: string;
}

export default AutJurisdictionDetails;
