import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import Kyb from './kyb.entity';
import { KybService } from './kyb.service';
import { KybController } from './kyb.controller';
import { StorageService } from '../storage/storage.service';
import { AwsService } from '../storage/upload.service';
import { OneDriveStorageService } from '../ondrive-storage/onedrive-storage.service';
import { AttachmentsService } from '../tasks/services/attachments.service';
import { BharathStorageService } from '../storage/bharath-storage.service';
import { BharathCloudService } from '../storage/bharath-upload.service';
import { GoogleDriveStorageService } from '../ondrive-storage/googledrive-storage.service';

@Module({
    imports: [TypeOrmModule.forFeature([Kyb])],
    controllers: [KybController],
    providers: [KybService,
        StorageService,
        AwsService,
        OneDriveStorageService,
        AttachmentsService,
        BharathStorageService,
        BharathCloudService,
        GoogleDriveStorageService
    ],
})

export class KybModule { }