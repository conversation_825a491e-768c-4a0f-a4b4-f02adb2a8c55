import Client from 'src/modules/clients/entity/client.entity';
import { Organization } from 'src/modules/organization/entities/organization.entity';
import {
  BaseEntity,
  Column,
  CreateDateColumn,
  Entity,
  JoinColumn,
  ManyToOne,
  OneToMany,
  OneToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import TanEChallan from './tan-e-challan.entity';
import TanIncomeTaxForms from './tan-income-tax-forms.entity';
import TanMyCas from './tan-my-cas.entity';
import TanProfile from './tan-profile.entity';
import AutomationMachines from 'src/modules/automation/entities/automation_machines.entity';
import TanKeyPersonEntity from './tan-key-person.entity';
import TanKeyPerson from './tan-key-person.entity';
import Password from 'src/modules/clients/entity/password.entity';
import TanCommunicationInbox from './tan-communication-inbox.entity';
import { decrypt, encrypt } from 'src/utils/encryption';
import TraceOutstandingDemand from './trace-outstanding-deman.entity';

export enum IncomeTaxStatus {
  ENABLE = 'ENABLE',
  DISABLE = 'DISABLE',
}

@Entity()
class TanClientCredentials extends BaseEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  tanNumber: string;

  // @Column()
   @Column({
      transformer: {
        to: (value: string) => (value ? encrypt(value) : value),
        from: (value: string) => (value ? decrypt(value) : value),
      },
    })
  password: string;

  @Column()
  passwordId: number;

  @Column({ type: 'enum', enum: IncomeTaxStatus, default: IncomeTaxStatus.ENABLE })
  status: IncomeTaxStatus;

  @OneToMany(() => AutomationMachines, (automationMachines) => automationMachines.tanCredentials)
  tanCredentials: AutomationMachines[];

  @ManyToOne(() => Client, (client) => client.tanClientCredentials, { onDelete: 'SET NULL' })
  client: Client;

  @OneToMany(() => TanProfile, (tanProfile) => tanProfile.tanClientCredentials, {
    onDelete: 'SET NULL',
  })
  tanProfile: TanProfile;

  @OneToMany(() => TanEChallan, (tanEChallan) => tanEChallan.tanClientCredentials, {
    onDelete: 'SET NULL',
  })
  tanChallan: TanEChallan;

  @OneToMany(() => TanMyCas, (tanMyCas) => tanMyCas.tanClientCredentials, {
    onDelete: 'SET NULL',
  })
  tanMycas: TanMyCas;

  @OneToMany(() => TanIncomeTaxForms, (tanForms) => tanForms.tanClientCredentials, {
    onDelete: 'SET NULL',
  })
  tanForms: TanIncomeTaxForms[];

  @OneToMany(() => TanKeyPerson, (tanKeyPerson) => tanKeyPerson.tanClientCredentials, {
    onDelete: 'SET NULL',
  })
  tanKeyPerson: TanKeyPerson;

  @OneToOne(() => Password, (password) => password.tanClientCredentials, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'password_id' })
  passwordI: Password;

  @OneToMany(() => TanCommunicationInbox, (tanTraces) => tanTraces.tanClientCredentials, {
    onDelete: 'SET NULL',
  })
  tanTraces: TanCommunicationInbox[];

   @OneToMany(() => TraceOutstandingDemand, (tandemands) => tandemands.tanClientCredentials, {
    onDelete: 'SET NULL',
  })
  traceOutstandingDemands: TanIncomeTaxForms[];

  @Column()
  clientId: number;

  @Column()
  organizationId: number;

  @Column()
  traceUserId: string;

  // @Column()
   @Column({
      transformer: {
        to: (value: string) => (value ? encrypt(value) : value),
        from: (value: string) => (value ? decrypt(value) : value),
      },
    })
  tracePassword: string;

  @Column()
  tanRemarks:string;

  @Column()
  traceRemarks:string;

  @CreateDateColumn()
  createdAt: string;

  @UpdateDateColumn()
  updatedAt: string;
}

export default TanClientCredentials;
