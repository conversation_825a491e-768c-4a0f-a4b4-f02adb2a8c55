import { Body, Controller, Get, Patch, Post, Query, Req, UseGuards, Request, Put, Delete, Param, ParseIntPipe, UseInterceptors, UploadedFiles, UploadedFile } from '@nestjs/common';
import { JwtAuthGuard } from 'src/modules/users/jwt/jwt-auth.guard';
import { ClientLoginDto } from './dto/client-loging.dto';
import { ClientPanelService } from './client-panel.service';
import { ClientVerifyEmailDto } from './dto/client-verifymail.dto';
import FindTasksQuery from '../tasks/dto/find-query.dto';
import { ChangePasswordDto, ClientForgotPasswordDto, ForgotPasswordDto, ResetPasswordDto } from '../users/dto/forgot-password.dto';
import { LabelService } from './services/label.service';
import { TasksService } from './services/tasks.service';
import { CategoriesService } from './services/categories.service';
import { UsersService } from 'src/modules/client-panel/services/users.service';
import { StorageService } from './services/storage.service';
import CreateCollectDataDto from '../collect-data/dto/create-collect-data.dto';
import { FileInterceptor, FilesInterceptor } from '@nestjs/platform-express';
import { CollectDataService } from './services/collect-data.service';
import { EventsService } from './services/events.service';
import { ChecklistsService } from './services/checklists.service';
import { CommentsServie } from './services/comments.service';
import { AttachmentsService } from './services/attachments.service';
import { MilestonesService } from '../tasks/services/milestones.service';
import { StageOfWorkService } from './services/stage-of-work.service';
import { TotalStorageDto } from '../storage/dto/total-storage.dto';
import { IUploadBody } from '../storage/storage.controller';
import { FindStorageDto } from '../storage/dto/FindStorage.dto';
import { GetUserLogHoursDto, GetUserLogHoursStatsDto, UserLogHoursType } from '../log-hours/dto/get-user-log-hours.dto';
import { LogHoursService } from './services/loghours.service';
import { BudgetedHoursService } from './services/budgeted-hours.service';
import { ApprovalLevelService } from '../atm-qtm-approval/services/approval-level.service';
import { QuantumService } from './services/quantum.service';
import { StatsService } from './services/stats.service';
import { IQueryWeeklyLogHoursDto } from '../stats/stats.controller';
import { DscRegisterService } from './services/dsc-register.service';
import { FindDscRegisterDto } from '../dsc-register/dto/find-dsc-register.dto';
import { RecurringService } from './services/recurring.service';
import { InvoiceService } from './services/invoice.service';
import { FindInvoicesDto } from '../billing/dto/find-invoices.dto';
import { ReceiptsService } from './services/receipts.service';
import { ClientService } from './services/clients.service';
import {
  allcategoryandsubcategory,
  allusersefficiency,
  servicecategorystatusbytasks,
  detailedoverduetasks,
  userbasedmasterreport,
  upcommingtasks,
  taskhrmsdetails,
  usertasksbyorgid,
  statuswisetasks,
  overduetasks,
  highestnumberoftaskscompletion,
  clientinvoiceunbilled,
  clientinvoicebilled,
  clientinvoicebilling,
  clientinvoicereceipts,
  clientslistinvoice,
  clientdashboardactivitylog,
  clientpureagentreceivedanddue,
  clientdashboardamountdue,
  clientdashboardamountreceived,
  clientdashboardinvoiceunbilled,
  clientdashboardinvoicebilled,
  ClientTasksCompletedToBilled,
  ClientTasksCompletedToUnBilled,
  balancedueforinvoicesraisedreport,
  invoiceoverduereports,
  receiptmanagementreport,
  taskscompletedtobilledtasks,
  taskscompletedtounbilledtasks,
  detailedoverduecompletedtasks,
} from 'src/utils/sqlqueries';
import { createQueryBuilder, getManager } from 'typeorm';
import { KybService } from './services/kyb.service';
import { AutProfileDetailsService } from './services/aut-profile-details.service';
import { AutDashboardService } from './services/dashboard.services';
import { AutIncomeTaxFormsService } from './services/income-tax-forms.services';
import { GstrClientService } from './services/gstr-client.service';
import { GstrService } from './services/notices.service';
import FindQueryDto from '../gstr-register/dto/find-query.dto';
import FindComplianceDto from '../gstr-register/dto/find-compliance.dto';
import CreateGstrDto from '../gstr-register/dto/create-gstrs.dto';
import { GstrRegisterService } from './services/gstr-register.service';
import FindReturnsDto from '../gstr-register/dto/find-returns.dto';
import { PromiseService } from './services/promise.service';
import Client from '../clients/entity/client.entity';
import FindExpenditureDto, { FindExpenditureQueryType } from '../expenditure/dto/find-expenditure.dto';
import { ProformaService } from './services/proforma.service';
import { CreateInvoiceDto } from '../billing/dto/create-invoice.dto';
import { CreateBillingEntityDto } from '../organization/dto/create-billing-entity.dto';
import { FindBillingEntityDto } from '../organization/dto/find-billing-entities.dto';
import { BillingEntitiesService } from './services/billing-entities.service';
import AddExpenditureDto from '../expenditure/dto/add-expenditure.dto';
import { ExpenditureService } from './services/expenditure.service';

@Controller('client-panel')
export class ClientPanelController {

  constructor(
    private service: ClientPanelService,
    private labelService: LabelService,

    private categoriesService: CategoriesService,
    private usersService: UsersService,
    public storageService: StorageService,
    public collectDataService: CollectDataService,
    public kybService: KybService,
    private eventsService: EventsService,

    protected tasksService: TasksService,
    protected checklistsService: ChecklistsService,
    protected commentsService: CommentsServie,
    protected attachmentsService: AttachmentsService,
    protected milestoneService: MilestonesService,
    protected stageOfWorkService: StageOfWorkService,

    private readonly logHoursService: LogHoursService,
    private readonly budgetedHoursService: BudgetedHoursService,

    private approvalLevelService: ApprovalLevelService,
    private quantumService: QuantumService,
    private statsService: StatsService,
    private DSCService: DscRegisterService,
    private recurringService: RecurringService,
    private invoiceService: InvoiceService,
    private receiptService: ReceiptsService,
    private clientService: ClientService,
    private autProfileDetailsService: AutProfileDetailsService,
    private autDashboardService: AutDashboardService,
    private autIncomeTaxFormsService: AutIncomeTaxFormsService,
    private gstrClientService: GstrClientService,
    private gstrService: GstrService,
    private gstrRegisterService: GstrRegisterService,
    private promiseService: PromiseService,
    private proformaService: ProformaService,
    private billingService: BillingEntitiesService,
    private expenditureService: ExpenditureService,
  ) { }

  @Post('/signin')
  login(@Body() body: ClientLoginDto) {
    return this.service.clientLogin(body);
  }

  @Post('/verifyemail')
  verifyEmail(@Body() body: ClientVerifyEmailDto) {
    return this.service.clientVerifyEmail(body);
  }

  @Post('/setpassword')
  setPassword(@Body() body: ClientLoginDto) {
    return this.service.clientSetPassword(body);
  }

  @Post('/forgot-password')
  async forgotPassword(@Body() body: ClientForgotPasswordDto) {
    return this.service.forgotPassword(body);
  }

  @Post('/reset-password')
  async resetPassword(@Body() body: ResetPasswordDto) {
    return this.service.resetPassword(body);
  }

  @UseGuards(JwtAuthGuard)
  @Patch('/change-password')
  async changePassword(@Request() req: any, @Body() body: ChangePasswordDto) {
    return this.service.changePassword(req.user.userId, body);
  }

  @UseGuards(JwtAuthGuard)
  @Get('/tasks')
  getClientTasks(@Req() req, @Query() query: any) {
    let { userId } = req.user;
    return this.service.getClientTasks(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Get('/client-profile')
  getClientProfile(@Req() req) {
    let { userId } = req.user;
    return this.service.getClientProfile(userId);
  }

  @UseGuards(JwtAuthGuard)
  @Get('/client-profile-single')
  getSingleClientProfile(@Req() req, @Query() query: any) {
    let { userId } = req.user;
    return this.service.getSingleClientProfile(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Get('/credentials')
  getClientCredentials(@Req() req, @Query() query: any) {
    let { userId } = req.user;
    return this.service.getClientCredentials(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Get('/my-profile')
  getMyProfile(@Req() req) {
    let { userId } = req.user;
    return this.service.getMyProfile(userId);
  }

  @UseGuards(JwtAuthGuard)
  @Get('/clients')
  getAllClients(@Req() req, @Query() query: any) {
    let { userId } = req.user;
    return this.service.getClientPortalClients(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Get('/labels')
  findAlllabels(@Req() req: any, @Query() query: any) {
    const { userId } = req.user;
    return this.labelService.findAll(userId, query);
  }

  @Get('/tasks/stage-of-work')
  getStageOfWork(@Query('taskId', ParseIntPipe) taskId: number) {
    return this.stageOfWorkService.getStageOfWork(taskId);
  }

  @Get('/tasks/milestones')
  getMilestones(@Query('taskId', ParseIntPipe) taskId: number) {
    return this.milestoneService.getMilestones(taskId);
  }

  @Get('/tasks/attachments')
  getAttachments(@Query('taskId', ParseIntPipe) taskId: number) {
    return this.attachmentsService.findAttachments(taskId);
  }

  @Get('/tasks/comments')
  getComments(@Query('taskId', ParseIntPipe) taskId: number) {
    return this.commentsService.findComments(taskId);
  }

  @Get('/tasks/checklists')
  getChecklists(@Query('taskId', ParseIntPipe) taskId: number) {
    return this.checklistsService.findChecklists(taskId);
  }

  @UseGuards(JwtAuthGuard)
  @Get('/tasks/task-details/:id')
  getOne(@Request() req: any, @Param('id', ParseIntPipe) id: number, @Query() query: any) {
    const { userId } = req.user;
    return this.tasksService.findOne(id, userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Get('/categories')
  findAllcategories(@Req() req: any) {
    const { userId } = req.user;
    return this.categoriesService.findAll(userId);
  }

  @UseGuards(JwtAuthGuard)
  @Get('/users')
  async get(@Request() req: any) {
    const { userId } = req.user;
    return this.usersService.get(userId);
  }

  @UseGuards(JwtAuthGuard)
  @Post('/kyb')
  add(@Request() req: any, @Body() body: any) {
    const { userId } = req.user;
    return this.kybService.addKyb(userId, body);
  }

  @UseGuards(JwtAuthGuard)
  @Get('/kyb')
  getKybs(@Request() req: any, @Query() query: any) {
    const { userId } = req.user;
    return this.kybService.getKybs(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Put('/kyb/:id')
  updateKyb(@Request() req: any, @Body() body: any, @Param('id', ParseIntPipe) id: number) {
    const { userId } = req.user;
    return this.kybService.updateKyb(userId, id, body);
  }

  @Delete('/kyb/:id')
  delete(@Param('id', ParseIntPipe) id: number) {
    return this.kybService.delete(id);
  }

  @Get('/collect-data/:id')
  async getcollectdata(@Param('id') id: string) {
    return this.collectDataService.findOne(id);
  }

  @Get('/collect-data/task/:id')
  async getcollectdataTask(@Param('id', ParseIntPipe) id: number) {
    return this.collectDataService.findOneTask(id);
  }

  @Get('/collect-data/attachments/:id')
  async getcollectdataAttachments(@Param('id') id: string) {
    return this.collectDataService.getAttachments(id);
  }

  @UseGuards(JwtAuthGuard)
  @Put('/collect-data/:id')
  async updatecollectdataData(@Req() req: any, @Param('id', ParseIntPipe) id: number, @Body() body: CreateCollectDataDto) {
    const { userId } = req.user;
    return this.collectDataService.updateData(id, userId, body);
  }

  @UseGuards(JwtAuthGuard)
  @Post('/collect-data')
  async createcollectdata(@Req() req: any, @Body() body: CreateCollectDataDto) {
    const { userId } = req.user;
    return this.collectDataService.create(userId, body);
  }

  @Post('/collect-data/:taskId/:collectId/:origin/attachments')
  @UseInterceptors(FilesInterceptor('files'))
  addAttachmentscollectdata(
    @UploadedFiles() files: Express.Multer.File[],
    @Param('taskId', ParseIntPipe) taskId: number,
    @Param('collectId') collectId: any,
    @Param('origin') origin: string,
  ) {
    return this.collectDataService.addAttachments(origin, collectId, taskId, files);
  }

  @UseGuards(JwtAuthGuard)
  @Get('/events')
  getEvents(@Req() request: any, @Query() query: any) {
    const { userId } = request.user;
    return this.eventsService.getEvents(userId, query);
  }

  @Get('/events/default')
  getDefaultEvents(@Query() query: any) {
    return this.eventsService.getDefaultEvents(query.startDates);
  }

  @UseGuards(JwtAuthGuard)
  @Get('/storage')
  async getInitialStorage(@Query() query: FindStorageDto, @Request() req: any) {
    const { userId } = req.user;
    return this.storageService.findStorage(query, userId);
  }

  @Get('/storage/tree')
  async getStorageTree(@Query() { clientId }: { clientId: number }) {
    return this.storageService.getStorageTree(clientId);
  }

  @UseGuards(JwtAuthGuard)
  @Post('/storage/upload-files')
  @UseInterceptors(FileInterceptor('file'))
  uploadFile(
    @UploadedFile() file: Express.Multer.File,
    @Body() body: IUploadBody,
    @Request() req: any,
  ) {
    const { userId } = req.user;
    return this.storageService.uploadFile({ file, body, userId });
  }

  @UseGuards(JwtAuthGuard)
  @Post('/storage/upload-file')
  @UseInterceptors(FileInterceptor('file'))
  attachementsUpload(
    @UploadedFile() file: Express.Multer.File,
    @Body() body: IUploadBody,
    @Request() req: any,
  ) {
    const { userId } = req.user;
    return this.storageService.attachementsUpload({ file, body, userId });
  }

  @Get('/storage/total-storage')
  async getTotalStorage(@Query() query: TotalStorageDto) {
    return this.storageService.getTotalStorage(query);
  }

  @UseGuards(JwtAuthGuard)
  @Get('/storage/org-total')
  async getOrgTotal(@Req() req: any) {
    const { userId } = req.user;
    return this.storageService.getOrgStorage(userId);
  }

  @Get('/storage/:id')
  async getAutOrgStorage(@Param('id', ParseIntPipe) id: number) {
    return this.storageService.getAutOrgStorage(id);
  }

  @Get('/log-hours')
  getTaskLogHours(@Query('taskId', ParseIntPipe) taskId: number) {
    return this.logHoursService.find(taskId);
  }

  @UseGuards(JwtAuthGuard)
  @Get('/log-hours/user')
  getUserLogHours(@Request() req: any, @Query() query: GetUserLogHoursDto) {
    const { userId } = req.user;
    const id = query.type === UserLogHoursType.USER ? query.userId : userId;
    return this.logHoursService.findUserLogHours(id, query);
  }

  @UseGuards(JwtAuthGuard)
  @Get('/log-hours/user/stats')
  getUserLogHoursStats(@Request() req: any, @Query() query: GetUserLogHoursStatsDto) {
    const { userId } = req.user;
    const id = query.type === UserLogHoursType.USER ? query.userId : userId;
    return this.logHoursService.getUserLogHourStats(id, query);
  }

  @Get('/budgeted-hours')
  getTaskBudgetedHours(@Query('taskId', ParseIntPipe) taskId: number) {
    return this.budgetedHoursService.find(taskId);
  }

  @UseGuards(JwtAuthGuard)
  @Get('/approval-level')
  find(@Req() req: any) {
    const { userId } = req.user;
    return this.approvalLevelService.find(userId);
  }
  @UseGuards(JwtAuthGuard)
  @Get('/approval-level/tasks')
  findTasks(@Query() query) {
    return this.approvalLevelService.findTasks(query);
  }


  @UseGuards(JwtAuthGuard)
  @Get('/quantum/config')
  async getQtmqtmConfig(@Req() request: any) {
    const { userId } = request.user;
    return this.quantumService.getQtmqtmConfig(userId);
  }

  @UseGuards(JwtAuthGuard)
  @Get('/stats/task-analytics')
  async taskAnalytics(@Req() request: any, @Query() query: any) {
    let { userId } = request.user;
    return this.statsService.getTaskAnalytics(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Get('/stats/tasks-due-this-week')
  async tasksDueThisWeek(@Req() request: any, @Query() query: any) {
    let { userId } = request.user;
    return this.statsService.getTasksDueThisWeek(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Get('/stats/tasks-by-category')
  async getTasksByCategory(@Req() request: any, @Query() query: any) {
    let { userId } = request.user;
    return this.statsService.getTasksByCategory(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Post('/stats/tasks-by-category-export')
  async getTasksByCategoryExport(@Req() request: any, @Body() body: any) {
    const query = body;
    let { userId } = request.user;
    return this.statsService.getTasksByCategoryExport(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Get('/stats/tasks-by-client-category')
  async getTasksByClientCategory(@Req() request: any, @Query() query: any) {
    let { userId } = request.user;
    return this.statsService.getTasksByClientCategory(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Get('/stats/tasks-by-client-category-export')
  async getTasksByClientCategoryExport(@Req() request: any, @Body() body: any) {
    const query = body;
    let { userId } = request.user;
    return this.statsService.getTasksByClientCategoryExport(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Get('/stats/tasks-by-service')
  async getTasksByService(@Req() request: any, @Query() query: any) {
    let { userId } = request.user;
    return this.statsService.getTasksByService(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Post('/stats/tasks-by-service-export')
  async exportTasksByServiceReport(@Req() request: any, @Body() body: any) {
    const query = body;
    let { userId } = request.user;
    return this.statsService.exportTasksByServiceReport(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Get('/stats/over-due-tasks')
  async getOverDueTasks(@Req() request: any, @Query() query: any) {
    let { userId } = request.user;
    return this.statsService.getOverDueTasks(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Post('/stats/over-due-tasks-export')
  async exportOverDueTasksReport(@Req() request: any, @Body() body: any) {
    const query = body;
    let { userId } = request.user;
    return this.statsService.exportOverDueTasksReport(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Get('/stats/client-analytics')
  async getClientAnalytics(@Req() request: any) {
    let { userId } = request.user;
    return this.statsService.getClientAnalytics(userId);
  }

  @UseGuards(JwtAuthGuard)
  @Get('/stats/client-billing-analytics')
  async getClientBillingAnalytics(@Query() query: any, @Req() request: any) {
    let { userId } = request.user;
    return this.statsService.getClientBillingAnalytics(query, userId);
  }

  @UseGuards(JwtAuthGuard)
  @Get('/stats/due-dsc-registers')
  async getDueDscRegister(@Req() request: any, @Query() query: any) {
    let { userId } = request.user;
    return this.statsService.getDueDscRegisters(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Get('/stats/clients-by-category')
  async getClientsByCategory(@Req() request: any) {
    let { userId } = request.user;
    return this.statsService.getClientsByCategory(userId);
  }

  @UseGuards(JwtAuthGuard)
  @Post('/stats/clients-by-category-export')
  async getClientsByCategoryExport(@Req() request: any) {
    let { userId } = request.user;
    return this.statsService.getClientsByCategoryExport(userId);
  }

  @UseGuards(JwtAuthGuard)
  @Get('/stats/total-log-hours')
  async getTotalLogHours(@Req() request: any, @Query() query: any) {
    let { userId } = request.user;
    return this.statsService.getTotalLogHours(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Get('/stats/weekly-log-hours')
  async getWeeklyLogHours(@Req() request: any, @Query() query: IQueryWeeklyLogHoursDto) {
    let { userId } = request.user;
    return this.statsService.getWeeklyLogHours(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Get('/stats/employee-tasks-by-status')
  async getEmployeeTasksByStatus(@Req() request: any, @Query() query: any) {
    let { userId } = request.user;
    return this.statsService.getEmployeeTasksByStatus(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Post('/stats/employee-tasks-by-status-report')
  async exportEmployeeTasksByStatusReport(@Req() request: any, @Body() body: any) {
    const query = body;
    let { userId } = request.user;
    return this.statsService.exportEmployeeTasksByStatusReport(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Get('/stats/employee-attendance')
  async getEmployeeAttendance(@Req() request: any, @Query() query: any) {
    let { userId } = request.user;
    return this.statsService.getEmployeeAttendance(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Get('/stats/service-analytics')
  async serviceAnalytics(@Req() request: any, @Query() query: any) {
    let { userId } = request.user;
    return this.statsService.getServiceAnalytics(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Get('/quantum/createdTemplates')
  async getCreatedTemplates(@Req() request: any, @Query() query: any) {
    const { userId } = request.user;
    return this.quantumService.getCreatedTemplates(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Get('/dsc-register')
  async getDSCRegister(@Request() req: any, @Query() query: FindDscRegisterDto) {
    const { userId } = req.user;
    return this.DSCService.find(userId, query);
  }

  @Get('/recurring-profile')
  async getRecurringProfiles(@Query('clientId', ParseIntPipe) clientId: number) {
    return this.recurringService.getRecurringProfiles(clientId);
  }

  @Get('/tasks/completed-tasks')
  getCompletedTasks(
    @Query('clientId', ParseIntPipe) clientId: number,
    @Query() query: FindTasksQuery,
  ) {
    return this.tasksService.getCompletedTasks(clientId, query);
  }

  @Get('/tasks/terminated-tasks')
  getTerminatedTasks(
    @Query('clientId', ParseIntPipe) clientId: number,
    @Query() query: FindTasksQuery,
  ) {
    return this.tasksService.getTerminatedTasks(clientId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Get('/tasks/deleted-tasks')
  getDeletedTasks(@Query() query: FindTasksQuery, @Req() req: any) {
    const { userId } = req.user;
    return this.tasksService.getDeletedTasks(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Get('/invoices/client-panel/:clientid')
  getClientPortalInvoices(@Param('clientid', ParseIntPipe) clientid: number, @Req() request, @Query() query: FindInvoicesDto) {
    const { userId } = request.user;
    return this.invoiceService.getClientPortalInvoices(clientid, query);
  }

  @UseGuards(JwtAuthGuard)
  @Get('/receipts/client-panel/:clientId')
  getClientPortalAll(@Param('clientId', ParseIntPipe) id: number, @Req() req: any, @Query() query: any) {
    return this.receiptService.getClientPortalAll(id, query);
  }

  @UseGuards(JwtAuthGuard)
  @Get('/client/:id/details')
  getClientOne(@Request() req: any, @Param('id', ParseIntPipe) id: number) {
    const { userId } = req.user;
    return this.clientService.findOne(id, userId);
  }

  @Post('/common/queryapi')
  async getquerysqlAPI(@Body() payload: any) {
    const user={id:0};
    const ViewAll = false;
    const ViewAssigned = false;
    if (payload.query == 'usertasksbyorgid') {
      payload.sql = usertasksbyorgid(payload);
    }
    if (payload.query == 'taskhrmsdetails') {
      payload.sql = taskhrmsdetails(payload);
    }
    if (payload.query == 'upcommingtasks') {
      payload.sql = upcommingtasks(payload);
    }
    if (payload.query == 'statuswisetasks') {
      payload.sql = statuswisetasks(payload);
    }
    if (payload.query == 'servicecategorystatusbytasks') {
      payload.category = payload.category.name;
      payload.sql = servicecategorystatusbytasks(payload);
    }
    if (payload.query == 'allcategoryandsubcategory') {
      payload.users = payload.users.id;
      payload.sql = allcategoryandsubcategory(payload);
    }
    if (payload.query == 'userbasedmasterreport') {
      payload.users = payload.users.id;
      payload.sql = userbasedmasterreport(payload);
    }
    if (payload.query == 'overduetasks') {
      payload.sql = overduetasks(payload);
    }
    if (payload.query == 'detailedoverduetasks') {
      payload.users = payload.users.id;
      payload.sql = detailedoverduetasks(payload);
    }
    if (payload.query == 'highestnumberoftaskscompletion') {
      payload.sql = highestnumberoftaskscompletion(payload);
    }
    if (payload.query == 'allusersefficiency') {
      payload.sql = allusersefficiency(payload);
    }

    if (payload.query == 'clientinvoiceunbilled') {
      payload.sortQuery = '';
      if (payload.sort?.column && payload?.sort?.direction) {
        payload.sortQuery = 'ORDER BY ' + `${payload.sort.column} ${payload.sort.direction}`;
      }
      payload.sql = clientinvoiceunbilled(payload);
    }
    if (payload.query == 'clientinvoicebilled') {

      payload.sql = clientinvoicebilled(payload);
    }
    if (payload.query == 'clientinvoicebilling') {
      payload.sql = clientinvoicebilling(payload);
    }
    if (payload.query == 'clientinvoicereceipts') {
      payload.sql = clientinvoicereceipts(payload);
    }
    if (payload.query == 'clientdashboardamountreceived') {
      payload.sql = clientdashboardamountreceived(payload);
    }
    if (payload.query == 'clientdashboardamountdue') {
      payload.sql = clientdashboardamountdue(payload);
    }
    if (payload.query == 'clientpureagentreceivedanddue') {
      payload.sql = clientpureagentreceivedanddue(payload);
    }
    if (payload.query == 'clientdashboardactivitylog') {
      payload.sql = clientdashboardactivitylog(payload);
    }
    if (payload.query == 'clientslistinvoice') {
      payload.sortQuery = '';
      if (payload.sort?.column && payload?.sort?.direction) {
        payload.sortQuery = 'ORDER BY ' + `${payload.sort.column} ${payload.sort.direction}`;
      }
      payload.sql = clientslistinvoice(payload, user, ViewAll, ViewAssigned);
    }
    if (payload.query == 'clientdashboardinvoiceunbilled') {
      payload.sql = clientdashboardinvoiceunbilled(payload);
    }
    if (payload.query == 'clientdashboardinvoicebilled') {
      payload.sql = clientdashboardinvoicebilled(payload);
    }
    if (payload.query == 'taskscompletedtobilledtasks') {
      if (payload.billingEntity) {
        payload.billingEntity = payload.billingEntity.join(',');
      }
      payload.sql = taskscompletedtobilledtasks(payload);
    }
    if (payload.query == 'taskscompletedtounbilledtasks') {
      payload.sql = taskscompletedtounbilledtasks(payload);
    }
    if (payload.query == 'ClientTasksCompletedToBilled') {
      payload.clients = payload.clients.id;
      if (payload.billingEntity) {
        payload.billingEntity = payload.billingEntity.join(',');
      }
      payload.sql = ClientTasksCompletedToBilled(payload);
    }

    if (payload.query == 'ClientTasksCompletedToUnBilled') {
      payload.clients = payload.clients.id;
      payload.sql = ClientTasksCompletedToUnBilled(payload);
    }
    if (payload.query == 'balancedueforinvoicesraisedreport') {
      if (payload.billingEntity) {
        payload.billingEntity = payload.billingEntity.join(',');
      }
      payload.sql = balancedueforinvoicesraisedreport(payload);
    }
    if (payload.query == 'invoiceoverduereports') {
      if (payload.billingEntity) {
        payload.billingEntity = payload.billingEntity.join(',');
      }
      payload.sql = invoiceoverduereports(payload);
    }
    if (payload.query == 'receiptmanagementreport') {
      if (payload.billingEntity) {
        payload.billingEntity = payload.billingEntity.join(',');
      }
      payload.sql = receiptmanagementreport(payload);
    }

    let data = [];

    if (payload.query == 'detailedoverduecompletedtasks') {
      payload.users = payload?.users?.id;
      data = await detailedoverduecompletedtasks(payload);
    } else {
      data = await getManager().query(payload.sql);
    }

    return data;
  }

  @Get('/invoices/:id/preview')
  getInvoice(@Param('id', ParseIntPipe) id: number, @Query() query: any) {
    return this.invoiceService.getInvoice(id, query);
  }

  @Get('/receipts/:id/preview')
  getReceipt(@Param('id', ParseIntPipe) id: number, @Query() query: any) {
    return this.receiptService.getReceiptPreview(id, query);
  }

  @UseGuards(JwtAuthGuard)
  @Get('/users/profile')
  async getProfile(@Request() req: any) {
    const { userId } = req.user;
    return this.usersService.getProfile(userId);
  }

  @UseGuards(JwtAuthGuard)
  @Get('/dsc-register/:id/details')
  async getOneDSC(@Request() req: any, @Param('id', ParseIntPipe) id: number, @Query() query: any) {
    const { userId } = req.user;
    return this.DSCService.getOne(id, userId, query);
  }

  @Post('/invoices/:id/download')
  async downloadEstimate(@Param('id', ParseIntPipe) id: number) {
    return this.invoiceService.downloadInvoice(id);
  }

  @Post('/receipts/:id/download')
  async downloadReceipt(@Param('id', ParseIntPipe) id: number) {
    return this.receiptService.downloadReceipt(id);
  };

  @UseGuards(JwtAuthGuard)
  @Get('automation')
  findAll(@Req() req: any, @Query() query: any) {
    const { userId } = req.user;
    return this.autProfileDetailsService.findAll(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Post('automation')
  create(@Body() body: any, @Req() req: any) {
    const { userId } = req.user;
    return this.autProfileDetailsService.create(userId, body);
  }

  @UseGuards(JwtAuthGuard)
  @Post('automation/auto-machine/:id')
  createMachine(@Param('id', ParseIntPipe) id: number, @Body() body: any, @Req() req: any) {
    const { userId } = req.user;
    return this.autProfileDetailsService.createMachine(userId, id, body);
  }

  @Get('automation/clientAutoStatus/:id')
  getclientAutoStatus(@Param('id', ParseIntPipe) id: number) {
    return this.autProfileDetailsService.getclientAutoStatus(id);
  }

  @UseGuards(JwtAuthGuard)
  @Post('automation/bulkSync')
  bulkAutomationSync(@Body() body: any, @Req() req: any) {
    const { userId } = req.user;
    return this.autProfileDetailsService.bulkAutomationSync(userId, body);
  }

  @UseGuards(JwtAuthGuard)
  @Get('automation/reports')
  getclientReport(@Req() req: any, @Query() query: any) {
    const { userId } = req.user;
    return this.autProfileDetailsService.getclientReport(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Get('automation/new-updates')
  getIncometexUpdates(@Req() req: any, @Query() query: any) {
    const { userId } = req.user;
    return this.autProfileDetailsService.getIncometexUpdates(userId);
  }

  @UseGuards(JwtAuthGuard)
  @Get('automation/update/:id')
  getUpdatedItem(@Req() req: any, @Param('id', ParseIntPipe) id: number) {
    const { userId } = req.user;
    return this.autProfileDetailsService.getUpdatedItem(userId, id);
  }

  @UseGuards(JwtAuthGuard)
  @Get('automation/eproceeding-notice')
  getFyiNotices(@Req() req: any, @Query() query: any) {
    const { userId } = req.user;
    return this.autProfileDetailsService.getCombinedNotices(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Get('incometax-dashboard')
  findIncometaxDashboardAll(@Req() req: any, @Query() query: any) {
    const { userId } = req.user;
    return this.autDashboardService.findCount(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Get('incometax-dashboard/returnStatus')
  statusWiseReturnsCount(@Req() req: any, @Query() query: any) {
    const { userId } = req.user;
    return this.autDashboardService.statusWiseReturnsCount(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Get('incometax-dashboard/returnType')
  typeWiseReturnsCount(@Req() req: any, @Query() query: any) {
    const { userId } = req.user;
    return this.autDashboardService.typeWiseReturnsCount(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Get('incometax-dashboard/returnVerification')
  verificationWiseReturnsCount(@Req() req: any, @Query() query: any) {
    const { userId } = req.user;
    return this.autDashboardService.verificationWiseReturnsCount(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Get('incometax-dashboard/demandData')
  demandRaisedfilterDates(@Req() req: any) {
    const { userId } = req.user;
    return this.autDashboardService.demandRaisedfilterDates(userId);
  }

  @UseGuards(JwtAuthGuard)
  @Get('incometax-dashboard/eProceedingNotices')
  eProccedidingFyiNotice(@Req() req: any, @Query() query: any) {
    const { userId } = req.user;
    return this.autDashboardService.getCombinedNoticesCount(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Get('incometax-dashboard/verification')
  incometaxClientCheck(@Req() req: any, @Query() query: any) {
    const { userId } = req.user;
    return this.autDashboardService.incometaxClientCheck(userId);
  }

  @UseGuards(JwtAuthGuard)
  @Get('incometax-dashboard/fya-events')
  getFyaEvents(@Req() req: any, @Query() query: any) {
    const { userId } = req.user;
    return this.autDashboardService.getFyaEvents(userId, query.startDates);
  }

  @UseGuards(JwtAuthGuard)
  @Get('incometax-dashboard/response-due-events')
  getResponseDueEvents(@Req() req: any, @Query() query: any) {
    const { userId } = req.user;
    return this.autDashboardService.getResponseDueEvents(userId, query.startDates);
  }

  @UseGuards(JwtAuthGuard)
  @Get('incometax/forms')
  findAllIncomeTax(@Req() req: any, @Query() query: any) {
    const { userId } = req.user;
    return this.autIncomeTaxFormsService.findAll(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Get('incometax/form/:id')
  findForm(@Req() req: any, @Param('id', ParseIntPipe) id: number) {
    const { userId } = req.user;
    return this.autIncomeTaxFormsService.findForm(userId, id);
  }

  @UseGuards(JwtAuthGuard)
  @Post('incometax/credentials')
  addClientAutCredentials(@Body() body: any, @Req() req: any) {
    const { userId } = req.user;
    return this.autIncomeTaxFormsService.addClientAutCredentials(userId, body);
  }

  @UseGuards(JwtAuthGuard)
  @Get('incometax/credentials')
  getClientAutCredentials(@Req() req: any, @Query() query: any) {
    const { userId } = req.user;
    return this.autIncomeTaxFormsService.getClientAutCredentials(userId, query);
  }

  @Get('incometax/credential/:id')
  getClientCredential(@Param('id', ParseIntPipe) id: number, @Query() query: any) {
    return this.autIncomeTaxFormsService.getClientCredential(id, query);
  }

  @UseGuards(JwtAuthGuard)
  @Get('incometax/clients')
  getAllClientsIncomeTax(@Req() req: any) {
    const { userId } = req.user;
    return this.autIncomeTaxFormsService.getAllClients(userId);
  }

  @Get('incometax/profile/:id')
  getIncomeTaxProfile(@Param('id', ParseIntPipe) id: number) {
    return this.autIncomeTaxFormsService.getIncomeTaxProfile(id);
  }

  @Get('incometax/jurisdiction/:id')
  getIncomeTaxJurisdiction(@Param('id', ParseIntPipe) id: number) {
    return this.autIncomeTaxFormsService.getIncomeTaxJurisdiction(id);
  }

  @Get('incometax/clientform/:id')
  getClientform(@Param('id', ParseIntPipe) id: number, @Query() query: any) {
    return this.autIncomeTaxFormsService.getClientform(id, query);
  }

  @UseGuards(JwtAuthGuard)
  @Get('incometax/returns')
  findReturns(@Req() req: any, @Query() query: any) {
    const { userId } = req.user;
    return this.autIncomeTaxFormsService.getIncomeTaxReturns(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Get('incometax/return/:id')
  findReturn(@Req() req: any, @Param('id', ParseIntPipe) id: number) {
    const { userId } = req.user;
    return this.autIncomeTaxFormsService.findReturn(userId, id);
  }

  @Get('incometax/clientreturn/:id')
  getClientReturn(@Param('id', ParseIntPipe) id: number, @Query() query: any) {
    return this.autIncomeTaxFormsService.getClientReturn(id, query);
  }
  @UseGuards(JwtAuthGuard)
  @Get('incometax/demands')
  findDemands(@Req() req: any, @Query() query: any) {
    const { userId } = req.user;
    return this.autIncomeTaxFormsService.getIncomeTaxDemands(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Get('incometax/demand/:id')
  findDemand(@Req() req: any, @Param('id', ParseIntPipe) id: number) {
    const { userId } = req.user;
    return this.autIncomeTaxFormsService.findDemand(userId, id);
  }

  @Get('incometax/clientdemand/:id')
  getClientDemand(@Param('id', ParseIntPipe) id: number, @Query() query: any) {
    return this.autIncomeTaxFormsService.getClientDemand(id, query);
  }

  @UseGuards(JwtAuthGuard)
  @Get('incometax/action')
  findEproceedings(@Req() req: any, @Query() query: any) {
    const { userId } = req.user;
    return this.autIncomeTaxFormsService.getIncomeTaxEproceedings(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Get('incometax/information')
  findFYIEproceedings(@Req() req: any, @Query() query: any) {
    const { userId } = req.user;
    return this.autIncomeTaxFormsService.getIncomeTaxFyiEproceedings(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Get('incometax/proceeding/:id')
  findEproceeding(@Req() req: any, @Param('id', ParseIntPipe) id: number) {
    const { userId } = req.user;
    return this.autIncomeTaxFormsService.findEproceeding(userId, id);
  }

  @UseGuards(JwtAuthGuard)
  @Get('incometax/information-pro/:id')
  findEproceedingFyi(@Req() req: any, @Param('id', ParseIntPipe) id: number) {
    const { userId } = req.user;
    return this.autIncomeTaxFormsService.findEproceedingFyi(userId, id);
  }

  @Get('incometax/clientproceeding/:id')
  getClientEproceeding(@Param('id', ParseIntPipe) id: number) {
    return this.autIncomeTaxFormsService.getClientEproceeding(id);
  }

  //VIEW NOTICE
  @UseGuards(JwtAuthGuard)
  @Get('incometax/proceeding/notice-action/:id')
  getFyaNotice(@Req() req: any, @Param('id', ParseIntPipe) id: number) {
    const { userId } = req.user;
    return this.autIncomeTaxFormsService.getFyaNotice(userId, id);
  }

  @UseGuards(JwtAuthGuard)
  @Get('incometax/proceeding/notice-information/:id')
  getFyiNotice(@Req() req: any, @Param('id', ParseIntPipe) id: number) {
    const { userId } = req.user;
    return this.autIncomeTaxFormsService.getFyiNotice(userId, id);
  }

  //Client Proceeding For Your Action
  @Get('incometax/clientproceedingfya/:id')
  getClientProceedingFya(@Param('id', ParseIntPipe) id: number, @Query() query: any) {
    return this.autIncomeTaxFormsService.getClientProceedingFya(id, query);
  }

  @UseGuards(JwtAuthGuard)
  @Get('incometax/sections-fya')
  getFyaSections(@Req() req: any) {
    const { userId } = req.user;
    return this.autIncomeTaxFormsService.getFyaSections(userId);
  }

  @UseGuards(JwtAuthGuard)
  @Get('incometax/sections-fyi')
  getFyiSections(@Req() req: any) {
    const { userId } = req.user;
    return this.autIncomeTaxFormsService.getFyiSections(userId);
  }

  @UseGuards(JwtAuthGuard)
  @Get('incometax/sections-demands')
  getDemandsSections(@Req() req: any) {
    const { userId } = req.user;
    return this.autIncomeTaxFormsService.getDemandsSections(userId);
  }

  @Get('incometax/clientproceedingfyi/:id')
  getClientProceedingFyi(@Param('id', ParseIntPipe) id: number, @Query() query: any) {
    return this.autIncomeTaxFormsService.getClientProceedingFyi(id, query);
  }

  @UseGuards(JwtAuthGuard)
  @Get('incometax/automationAuth')
  getAuthToken() {
    return this.autIncomeTaxFormsService.getAuthToken();
  }

  @Put('incometax/credentials/:id')
  update(@Param('id', ParseIntPipe) id: number, @Body() body) {
    return this.autIncomeTaxFormsService.updateClientAutCredentials(id, body);
  }

  @UseGuards(JwtAuthGuard)
  @Post('incometax/activity')
  sendActivityData(@Body() body: any, @Req() req: any) {
    const { userId } = req.user;
    return this.autIncomeTaxFormsService.sendActivityData(userId, body);
  }

  @Get('incometax/activity/:id')
  getActivityLogData(@Req() req: any, @Param('id', ParseIntPipe) id: number, @Query() query: any) {
    return this.autIncomeTaxFormsService.getActivityLogData(id, query);
  }

  @UseGuards(JwtAuthGuard)
  @Get('incometax/checkNoOfSync')
  checkAutomationInOrganization(@Req() req: any) {
    const { userId } = req.user;
    return this.autIncomeTaxFormsService.checkAutomationInOrganization(userId);
  };

  @UseGuards(JwtAuthGuard)
  @Post('incometax/import')
  async importCredentials(@Req() req: any, @Body() body: any) {
    const { userId } = req.user;
    return this.autIncomeTaxFormsService.importCredentials(userId, body);
  };

  @UseGuards(JwtAuthGuard)
  @Get('incometax/scheduling')
  async organizationScheduling(@Req() req: any) {
    const { userId } = req.user;
    return this.autIncomeTaxFormsService.organizationScheduling(userId);
  }

  @UseGuards(JwtAuthGuard)
  @Get('gstr-dashboard/not-ord')
  getNoticeOrdersDateCount(@Req() req: any, @Query() query: any) {
    const { userId } = req.user;
    return this.autDashboardService.getNoticeOrdersDateCount(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Get('gstr-dashboard/add-not-ord')
  getAdditionalNoticeOrdersDateCount(@Req() req: any, @Query() query: any) {
    const { userId } = req.user;
    return this.autDashboardService.getAdditionalNoticeOrdersDateCount(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Get('gstr-client')
  async getGstrClients(@Request() req, @Query() query: any) {
    const { userId } = req.user;
    return this.gstrClientService.getGstrClients(userId, query);
  }

  @Get('gstr-client/credential/:id')
  getGstrClientCredential(@Param('id', ParseIntPipe) id: number, @Query() query: any) {
    return this.gstrClientService.getGstrClientCredential(id, query);
  }

  @UseGuards(JwtAuthGuard)
  @Get('gstr-client/clients')
  async getAtomClients(@Request() req: any) {
    const { userId } = req.user;
    return this.gstrClientService.getAtomClients(userId);
  }

  @UseGuards(JwtAuthGuard)
  @Post('gstr-client/credentials')
  addGstrCredentials(@Body() body: any, @Req() req: any) {
    const { userId } = req.user;
    return this.gstrClientService.addGstrCredentials(userId, body);
  }

  @UseGuards(JwtAuthGuard)
  @Put('gstr-client/credentials/:id')
  updateGstrCredentials(@Param('id', ParseIntPipe) id: number, @Body() body) {
    return this.gstrClientService.updateGstrCredentials(id, body);
  }

  @UseGuards(JwtAuthGuard)
  @Post('gstr-client/gstr-request/:id')
  createGsrRequest(@Req() req: any, @Param('id', ParseIntPipe) id: number) {
    const { userId } = req.user;
    return this.gstrClientService.createGsrRequest(userId, id);
  }

  @UseGuards(JwtAuthGuard)
  @Post('gstr-client/bulkSync')
  bulkGstrSync(@Body() body: any, @Req() req: any) {
    const { userId } = req.user;
    return this.gstrClientService.bulkGstrSync(userId, body);
  }

  @UseGuards(JwtAuthGuard)
  @Get('gstr/order-notices/:id')
  async getOrderNotices(@Request() req: any, @Param('id', ParseIntPipe) id: number) {
    const { userId } = req.user;
    return this.gstrService.getOrderNotices(userId, id);
  }

  @UseGuards(JwtAuthGuard)
  @Get('gstr/order-notice/:id')
  async getOrderNotice(@Request() req: any, @Param('id', ParseIntPipe) id: number) {
    const { userId } = req.user;
    return this.gstrService.getOrderNotice(userId, id);
  }

  @UseGuards(JwtAuthGuard)
  @Get('gstr/additional-notice/:id')
  async getGstrAdditionalDeails(@Request() req: any, @Param('id', ParseIntPipe) id: number) {
    const { userId } = req.user;
    return this.gstrService.getGstrAdditionalDeailss(userId, id);
  }

  @UseGuards(JwtAuthGuard)
  @Get('gstr/gstr-profile/:id')
  async getGstrProfile(@Request() req: any, @Param('id', ParseIntPipe) id: number) {
    const { userId } = req.user;
    return this.gstrService.getGstrProfile(userId, id);
  }

  @UseGuards(JwtAuthGuard)
  @Get('gstr/gstr-compliance/:id')
  async getGstrClientCompliance(@Request() req: any, @Param('id', ParseIntPipe) id: number) {
    const { userId } = req.user;
    return this.gstrService.getGstrClientCompliance(userId, id);
  }

  @UseGuards(JwtAuthGuard)
  @Get('gstr/gstr-add')
  async getAddNoticeAndOrders(@Request() req: any, @Query() query: any) {
    const { userId } = req.user;
    return this.gstrService.getAddNoticeAndOrders(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Get('gstr/gstr-notices-orders')
  async getNoticeAndOrders(@Request() req: any, @Query() query: any) {
    const { userId } = req.user;
    return this.gstrService.getNoticeAndOrders(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Get('gstr/reports')
  getGstrReport(@Request() req: any, @Query() query: any) {
    const { userId } = req.user;
    return this.gstrService.getGstrReport(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Get('gstr/additional/:id')
  async getGstrAdditionalNoticeOrders(@Request() req: any, @Param('id', ParseIntPipe) id: number) {
    const { userId } = req.user;
    return this.gstrService.getGstrAdditionalNoticeOrders(userId, id);
  }

  @UseGuards(JwtAuthGuard)
  @Get('gstr')
  getAll(@Request() req: any, @Query() query: FindQueryDto) {
    const { userId } = req.user;
    return this.gstrRegisterService.findAll(userId, query);
  };

  @UseGuards(JwtAuthGuard)
  @Post('gstr')
  createGstr(@Body() body: CreateGstrDto, @Request() req: any) {
    const { userId } = req.user;
    return this.gstrRegisterService.create(body, userId)
  };

  @UseGuards(JwtAuthGuard)
  @Get('gstr/clients')
  async getAssigned(@Request() req: any, @Query() query: any) {
    const { userId } = req.user;
    return this.gstrRegisterService.findClients(userId, query);
  };

  @UseGuards(JwtAuthGuard)
  @Get('gstr/compliance')
  async getCompliance(@Request() req: any, @Query() query: FindComplianceDto) {
    return this.gstrRegisterService.getCompliance(query);
  }

  @UseGuards(JwtAuthGuard)
  @Post('promise')
  createPromiseClient(@Body() body: FindReturnsDto, @Request() req: any) {
    const { userId } = req.user;
    return this.promiseService.getReturns(body, userId)
  }

  @UseGuards(JwtAuthGuard)
  @Post('promise/client-sync')
  syncSingleClientReturns(@Body() body: any, @Request() req: any) {
    const { userId } = req.user;
    return this.promiseService.syncSingleClientReturns(body, userId)
  }

  @UseGuards(JwtAuthGuard)
  @Post('promise/client')
  getSingleClientReturns(@Body() body: any, @Request() req: any) {
    const { userId } = req.user;
    return this.promiseService.getSingleClientReturns(body, userId)
  };

  @UseGuards(JwtAuthGuard)
  @Get('client-permissions/mine/:id')
  async getMyPermissions(@Param('id', ParseIntPipe) id: number, @Request() req) {
    let data = await createQueryBuilder(Client, 'client')
      .leftJoinAndSelect('client.permissions', 'permissions')
      .where('client.id = :id', { id: id })
      .getOne();
    return data.permissions;
  }

  @UseGuards(JwtAuthGuard)
  @Get('tasks/expenditure')
  getExpenditure(@Request() req: any, @Query() query: FindExpenditureDto) {
    const { userId } = req.user;
    let id = query.type === FindExpenditureQueryType.USER ? query.userId : userId;
    return this.tasksService.findExpenditure(+id, query);
  }

  @UseGuards(JwtAuthGuard)
  @Get('client-panel/udin-users')
  async getudinUsers(@Request() request: any, @Query() query: any) {
    let { userId } = request.user;
    return this.service.getudinUsers(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Get("udin-task/:id")
  getUdinTask(@Request() req: any, @Query() query: any, @Param('id', ParseIntPipe) taskId: number) {
    const { userId } = req?.user;
    return this.service.getUdinTask(query, userId);
  }

  @UseGuards(JwtAuthGuard)
  @Post('proforma')
  async createProforma(@Body() body: CreateInvoiceDto, @Req() request: any) {
    const { userId } = request.user;
    return this.proformaService.create(userId, body);
  };

  @UseGuards(JwtAuthGuard)
  @Get('proforma')
  async getProformas(@Req() request: any, @Query() query: FindInvoicesDto) {
    const { userId } = request.user;
    return this.proformaService.get(userId, query);
  }

  @Get('proforma/:id/preview')
  getInvoiceProforma(@Param('id', ParseIntPipe) id: number, @Query() query: any) {
    return this.proformaService.getProformaInvoice(id, query);
  };

  @UseGuards(JwtAuthGuard)
  @Get('proforma/performa-tasks')
  getClientProformaTasks(@Query() query: any) {
    return this.proformaService.getProformaTasks(query);
  };

  @Post('proforma/:id/download')
  async downloadEstimateProforma(@Param('id', ParseIntPipe) id: number, @Body() body: any) {
    return this.proformaService.downloadProformaInvoice(id, body);
  };

  @Post('proforma/:id/downloadwithoutemittor')
  async downloadEstimatewithoutEmittor(@Param('id', ParseIntPipe) id: number) {
    return this.proformaService.downloadProformaInvoicewithoutEmittor(id);
  }
  @UseGuards(JwtAuthGuard)
  @Post('proforma/convert')
  convertProformaInvoice(@Body() body: CreateInvoiceDto, @Req() request: any) {
    const { userId } = request.user;
    return this.proformaService.convert(userId, body);
  };

  @UseGuards(JwtAuthGuard)
  @Post('proforma/:id/cancel')
  async cancelEstimate(@Param('id', ParseIntPipe) id: number, @Req() req: any) {
    const { userId } = req.user;
    return this.proformaService.cancelProformaInvoice(id, userId);
  };

  @UseGuards(JwtAuthGuard)
  @Post('proforma/export')
  async exportInvoices(@Req() req: any, @Body() body: any) {
    const { userId } = req.user;
    if (body.type == 'Type-A') {
      return this.proformaService.exportA(userId, body);
    } else if (body.type == 'Type-B') {
      return this.proformaService.exportB(userId, body);
    }
  }

  @Post('billing-entities')
  async createBillingEntities(@Request() req: any, @Body() body: CreateBillingEntityDto) {
    const { userId } = req.user;
    return this.billingService.create(userId, body);
  }

  @UseGuards(JwtAuthGuard)
  @Get('billing-entities')
  async getBillingEntities(@Request() req: any, @Query() query: FindBillingEntityDto) {
    const { userId } = req.user;
    return this.billingService.get(userId, query);
  }
  
  @Get('billing-entities/active')
  async getActiveBillingEntity(@Request() req: any) {
    const { userId } = req.user;
    return this.billingService.getActive(userId);
  }

  @UseGuards(JwtAuthGuard)
  @Get('billing-entities/:id')
  async getOneBillingEntity(@Request() req: any, @Param('id', ParseIntPipe) id: number) {
    const { userId } = req.user;
    return this.billingService.getOne(id, userId);
  }

  @Patch('billing-entities/:id')
  async updateBillingEntity(@Request() req: any, @Param('id', ParseIntPipe) id: number, @Body() body: any) {
    const { userId } = req.user;
    return this.billingService.update(id, userId, body);
  }

  @Patch('billing-entities/default/:id')
  async defaultBillingEntity(@Request() req: any, @Param('id', ParseIntPipe) id: number) {
    const { userId } = req.user;
    return this.billingService.default(id, userId);

  }

  @Patch('billing-entities/status-change/:id')
  async changeStatusBillingEntity(@Request() req: any, @Param('id', ParseIntPipe) id: number, @Body() body: any) {

    const { userId } = req.user;
    return this.billingService.changeStatus(id, userId, body);

  }

  @Delete('billing-entities/:id')
  async deleteBillingEntity(@Param('id', ParseIntPipe) id: number) {
    return this.billingService.delete(id);
  }

  @UseGuards(JwtAuthGuard)
  @Get('expenditure')
  getExpenditureBilling(@Request() req: any, @Query() query: FindExpenditureDto) {
    const { userId } = req.user;
    let id = query.type === FindExpenditureQueryType.USER ? query.userId : userId;
    return this.expenditureService.find(+id, query);
  }

  @UseGuards(JwtAuthGuard)
  @Post('expenditure/export')
  exportExpenditure(@Request() req: any, @Body() body: any) {
    const { userId } = req.user;
    return this.expenditureService.export(userId, body);
  }

  @UseGuards(JwtAuthGuard)
  @Get('expenditure/organization')
  getOrgExpenditure(@Request() req: any, @Query() query: FindExpenditureDto) {
    const { userId } = req.user;
    return this.expenditureService.findOrgExpenditure(userId, query);
  }
  @UseGuards(JwtAuthGuard)
  @Post('expenditure/organization-export')
  exportOrgExpenditure(@Request() req: any, @Body() body: any) {
    const { userId } = req.user;
    return this.expenditureService.exportOrgExpenditure(userId, body);
  }
}
