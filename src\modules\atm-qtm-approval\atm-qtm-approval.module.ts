import { Modu<PERSON> } from "@nestjs/common";
import { TypeOrmModule } from "@nestjs/typeorm";
import { AtmQtmApprovalController } from "./controllers/atm-qtm-approval-process.controller";
import { AtmQtmApprovalService } from "./services/atm-qtm-approval-process.service";
import OrgApprovalLevel from "./entities/org-approvals-level.entity";
import OrgApprovals from "./entities/org-approvals.entity";
import ApprovalProcedures from "./entities/approval-procedures.entity";
import { ApprovalLevelController } from "./controllers/approval-level.controller";
import { ApprovalLevelService } from "./services/approval-level.service";

@Module({
    imports: [
        TypeOrmModule.forFeature([
            OrgApprovalLevel,
            OrgApprovals,
            ApprovalProcedures

        ])
    ],
    controllers: [AtmQtmApprovalController, ApprovalLevelController],
    providers: [AtmQtmApprovalService, ApprovalLevelService],

})

export class AtmQtmApprovalModule { }