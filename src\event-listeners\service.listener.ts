import { Injectable } from '@nestjs/common';
import { OnEvent } from '@nestjs/event-emitter';
import { sendNotification } from 'src/notifications/notify';
import { User } from 'src/modules/users/entities/user.entity';
import { createQueryBuilder } from 'typeorm';
import { Event_Actions } from './actions';

interface CreateEvent {
  userId: number;
  serviceName: string;
  serviceId: number;
}

@Injectable()
export class ServiceListener {
  @OnEvent(Event_Actions.SERVICE_ADDED, { async: true })
  async handleServiceCreate(event: CreateEvent) {
    try {
      const { userId, serviceName } = event;
      let user = await createQueryBuilder(User, 'user')
        .leftJoinAndSelect('user.organization', 'organization')
        .leftJoinAndSelect('organization.users', 'users')
        .leftJoinAndSelect('users.role', 'role')
        .where('user.id = :id', { id: userId })
        .getOne();

      let orgAdmin = user.organization.users.find(
        (user: User) => user.role.defaultRole,
      );

      if (!orgAdmin) return;

      let userIds = [orgAdmin.id];

      let notification = {
        title: 'Service Added',
        body: `'${serviceName}' Service has been created by ${user.fullName}`,
      };

      await sendNotification(userIds, notification);
    } catch (err) {
      console.log(err);
    }
  }

  @OnEvent(Event_Actions.SERVICE_UPDATED, { async: true })
  async handleServiceUpdate(event: CreateEvent) {
    try {
      const { userId, serviceName } = event;
      let user = await createQueryBuilder(User, 'user')
        .leftJoinAndSelect('user.organization', 'organization')
        .leftJoinAndSelect('organization.users', 'users')
        .leftJoinAndSelect('users.role', 'role')
        .where('user.id = :id', { id: userId })
        .getOne();

      let orgAdmin = user.organization.users.find(
        (user: User) => user.role.defaultRole,
      );

      if (!orgAdmin) return;

      let userIds = [orgAdmin.id];

      let notification = {
        title: 'Service Updated',
        body: `'${serviceName}' Service has been updated by ${user.fullName}`,
      };

      await sendNotification(userIds, notification);
    } catch (err) {
      console.log(err);
    }
  }
}
