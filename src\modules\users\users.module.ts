import { Module } from '@nestjs/common';
import { JwtModule } from '@nestjs/jwt';
import { PassportModule } from '@nestjs/passport';
import { TypeOrmModule } from '@nestjs/typeorm';
import { UserListener } from 'src/event-listeners/user.listener';
import { User } from 'src/modules/users/entities/user.entity';
import { UsersController } from './controllers/users.controller';
import { BlackList } from './entities/black-list.entity';
import { InvitedUser } from './entities/invited-user.entity';
import { UserProfile } from './entities/user-profile.entity';
import { jwtConstants } from './jwt/constants';
import { JwtStrategy } from './jwt/jwt.strategy';
import { LocalStrategy } from './jwt/local.strategy';
import { UsersService } from './services/users.service';
import { UserProfileSubscriber } from 'src/event-subscribers/profile.subscriber';
import { UserSubscriber } from 'src/event-subscribers/user.subscriber';

import { UserStatusSubscriber } from 'src/event-subscribers/userStatus.subscriber';
// import { UserSubscriber } from 'src/event-subscribers/user.subscriber';
import { InviteUserSubscriber } from 'src/event-subscribers/inviteuser.subscriber';
import { UserWalletSubscriber } from 'src/event-subscribers/user-wallet.subscriber';
import { CamundaUserSubscriber } from 'src/event-subscribers/camunda-subscribers/camunda-user.subscriber';
import { StorageService } from '../storage/storage.service';
import { AwsService } from '../storage/upload.service';
import { OneDriveStorageService } from '../ondrive-storage/onedrive-storage.service';
import { AttachmentsService } from '../tasks/services/attachments.service';
import { OrgChartService } from './services/org-chart.service';
import { OrganizationHierarchy } from './entities/organization-hierarchy';
import { OrgChartController } from './controllers/org-chat.controller';
import { BharathCloudService } from '../storage/bharath-upload.service';
import { BharathStorageService } from '../storage/bharath-storage.service';
import { GoogleDriveStorageService } from '../ondrive-storage/googledrive-storage.service';

@Module({
  imports: [
    TypeOrmModule.forFeature([User, UserProfile, InvitedUser, BlackList, OrganizationHierarchy]),
    PassportModule.register({
      defaultStrategy: 'jwt',
    }),
    JwtModule.register({
      secret: jwtConstants.secret,
      signOptions: { expiresIn: '7d' },
    }),
  ],
  controllers: [UsersController, OrgChartController],
  providers: [
    UsersService,
    LocalStrategy,
    JwtStrategy,
    UserListener,
    UserProfileSubscriber,
    UserSubscriber,
    UserStatusSubscriber,
    InviteUserSubscriber,
    UserWalletSubscriber,
    CamundaUserSubscriber,
    StorageService,
    AwsService,
    AttachmentsService,
    OneDriveStorageService,
    BharathStorageService,
    BharathCloudService,
    OrgChartService,
    GoogleDriveStorageService

  ],

  exports: [UsersService],
})
export class UsersModule { }
