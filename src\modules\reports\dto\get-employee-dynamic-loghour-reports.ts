import { ArrayMinSize, IsArray, IsEnum, IsNotEmpty, IsObject, IsOptional } from 'class-validator';
import { LogHourType } from 'src/modules/log-hours/log-hour.entity';

class getEmployeeLogHoursDynamicDto {
  @IsNotEmpty()
  @IsArray()
  @ArrayMinSize(1)
  uniqueUsers: Array<string>;

  @IsNotEmpty()
  @IsArray()
  @ArrayMinSize(1)
  uniqueDates: Array<string>;

  @IsNotEmpty()
  @IsObject()  
  tableData: Object;

  @IsNotEmpty()
  @IsArray()
  @ArrayMinSize(1)
  users: Array<number>;

  @IsNotEmpty()
  fromDate: string;

  @IsNotEmpty()
  toDate: string;
  
  @IsOptional()
  @IsEnum(LogHourType)
  type: LogHourType;
  

}

export default getEmployeeLogHoursDynamicDto;
