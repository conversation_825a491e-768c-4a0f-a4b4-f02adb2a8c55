{"name": "vider_server", "version": "1.0.39", "description": "CA Management tool", "author": "v<PERSON><PERSON> kumar", "private": true, "license": "UNLICENSED", "proxy": "http://localhost:5000", "scripts": {"buildmajor": "npm version major", "buildminor": "npm version minor", "buildpatch": "npm version patch", "prebuild": "<PERSON><PERSON><PERSON> dist", "build": "nest build", "build:local": "npm --no-git-tag-version version minor && git add . && git commit -m %npm_package_version% && git push && nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "typeorm": "typeorm-ts-node-commonjs"}, "dependencies": {"@nestjs/common": "^8.0.0", "@nestjs/config": "^1.0.2", "@nestjs/core": "^8.0.0", "@nestjs/jwt": "^8.0.0", "@nestjs/mapped-types": "*", "@nestjs/mongoose": "^9.0.3", "@nestjs/passport": "^8.0.1", "@nestjs/platform-express": "^8.0.0", "@nestjs/schedule": "^1.0.1", "@nestjs/typeorm": "^8.0.2", "@types/nodemailer": "^6.4.4", "aws-msk-iam-sasl-signer-js": "^1.0.0", "aws-sdk": "^2.1000.0", "axios": "^0.26.0", "bcrypt": "^5.0.1", "class-transformer": "^0.5.1", "class-validator": "^0.13.1", "compression": "^1.7.4", "crypto": "^1.0.1", "ejs": "^3.1.6", "exceljs": "^4.4.0", "firebase-admin": "^13.4.0", "hbs": "^4.2.0", "kafkajs": "^2.2.4", "libphonenumber-js": "^1.11.3", "lodash": "^4.17.21", "moment": "^2.29.4", "moment-timezone": "^0.5.48", "mongoose": "^6.2.9", "mysql2": "^2.3.0", "nodemailer": "^6.7.2", "os": "^0.1.2", "passport": "^0.5.0", "passport-jwt": "^4.0.0", "passport-local": "^1.0.0", "pdf-lib": "^1.17.1", "puppeteer": "^23.10.4", "reflect-metadata": "^0.1.13", "rimraf": "^3.0.2", "rxjs": "^7.2.0", "sharp": "^0.30.7", "socket.io": "^4.5.1", "strapi-sdk-javascript": "^0.3.3", "typeorm": "^0.2.38", "typeorm-naming-strategies": "^2.0.0", "uuid": "^8.3.2", "xlsx": "^0.17.4"}, "devDependencies": {"@nestjs/cli": "^8.0.0", "@nestjs/event-emitter": "^1.0.0", "@nestjs/platform-socket.io": "^8.4.5", "@nestjs/schematics": "^8.0.0", "@nestjs/serve-static": "^2.2.2", "@nestjs/testing": "^8.4.6", "@nestjs/websockets": "^8.4.5", "@types/cron": "^1.7.3", "@types/ejs": "^3.1.0", "@types/express": "^4.17.13", "@types/jest": "^27.0.1", "@types/lodash": "^4.14.182", "@types/multer": "^1.4.7", "@types/node": "^16.0.0", "@types/supertest": "^2.0.11", "@typescript-eslint/eslint-plugin": "^4.28.2", "@typescript-eslint/parser": "^4.28.2", "eslint": "^7.30.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^3.4.0", "jest": "^27.0.6", "prettier": "^2.3.2", "supertest": "^6.1.3", "ts-jest": "^27.0.3", "ts-loader": "^9.2.3", "ts-node": "^10.0.0", "tsconfig-paths": "^3.10.1", "typescript": "^4.3.5"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}