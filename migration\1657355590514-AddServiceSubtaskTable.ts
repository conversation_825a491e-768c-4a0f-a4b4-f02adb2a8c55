import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddServiceSubtaskTable1657355590514 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `create table service_subtask (
            id int not null auto_increment,
            name varchar(255) not null,
            description varchar(255) null,
            service_id int null,
            created_at datetime(6) default current_timestamp(6),
            updated_at datetime(6) default current_timestamp(6),
            primary key (id),
            CONSTRAINT FK_service_subtask_service_id FOREIGN KEY (service_id) REFERENCES service (id) ON DELETE SET NULL
        );`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}
