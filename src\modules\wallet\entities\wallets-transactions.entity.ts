import { BaseEntity, Column, CreateDateColumn, Entity, PrimaryGeneratedColumn, UpdateDateColumn } from "typeorm";

export enum Type {
    CREDIT = 'CREDIT',
    DEBIT = 'DEBIT',
}

export enum TransactionType {
    FEES = 'FEES',
    AMOUNT = 'AMOUNT',
    GST = 'GST',

}



@Entity()
export class WalletsTransactions extends BaseEntity {
    @PrimaryGeneratedColumn()
    id: number;

    @Column({ type: 'decimal', precision: 19, scale: 2, nullable: true }) //ReceiptParticular
    amount: number;

    @Column({ type: 'text', nullable: true }) //Role Table
    description: string;

    @Column({ type: 'enum', enum: Type })
    type: Type;

    @Column({ type: 'enum', enum: TransactionType })
    transactionType: TransactionType;

    @CreateDateColumn()
    createdAt: string;

    @UpdateDateColumn()
    updatedAt: string;

    @Column()
    updatedBy: number;

    @Column()
    organization_id: number;







}