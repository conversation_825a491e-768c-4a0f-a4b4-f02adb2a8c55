import {
  BaseEntity,
  Column,
  CreateDateColumn,
  Entity,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import ChecklistItem from './checklist-item.entity';
import { Service } from './service.entity';

@Entity('service_checklist')
export class Checklist extends BaseEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  name: string;

  @ManyToOne(() => Service, (service) => service.checklists)
  service: Service;

  @OneToMany(() => ChecklistItem, (checklistItem) => checklistItem.checklist, { cascade: true })
  checklistItems: ChecklistItem[];

  @CreateDateColumn()
  createdAt: string;

  @UpdateDateColumn()
  updatedAt: string;
}
