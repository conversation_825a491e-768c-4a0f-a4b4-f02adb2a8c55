import { BadRequestException } from '@nestjs/common';
import { User, UserStatus } from 'src/modules/users/entities/user.entity';
import {
  Brackets,
  createQ<PERSON><PERSON><PERSON>uilder,
  getConnection,
  getRepository,
  <PERSON><PERSON>han,
  Like,
} from 'typeorm';
import AutProfileDetails from '../entities/aut-profile-details.entity';
import AutomationMachines, { TypeEnum } from '../entities/automation_machines.entity';
import AutUpdateTracker from '../entities/aut_update_tracker.entity';
import AutFyaNotice from '../entities/aut_income_tax_eproceedings_fya_notice.entity';
import AutFyiNotice from '../entities/aut_income_tax_eproceedings_fyi_notice.entity';
import axios from 'axios';
import Password, { IsExistingAtomPro } from 'src/modules/clients/entity/password.entity';
import GstrCredentials, {
  GstrStatus,
} from 'src/modules/gstr-automation/entity/gstrCredentials.entity';
import OrganizationPreferences from 'src/modules/organization-preferences/entity/organization-preferences.entity';
import AutClientCredentials, {
  IncomeTaxStatus,
  syncStatus,
} from '../entities/aut_client_credentials.entity';
import checkGstrUsername from 'src/utils/validations/atom-pro/gstrUserName';
import checkGstrPassword from 'src/utils/validations/atom-pro/gstrPassword';
import checkPanNumber from 'src/utils/validations/atom-pro/panNumber';
import AtomProLimitRequests from '../entities/atomProLimitRequests.entity';
import * as xlsx from 'xlsx';
import { calculateAssessmentYear, generateAssessmentYear } from 'src/utils/re-use';
import { capitalize } from 'lodash';
import * as moment from 'moment';
import { Permissions } from 'src/modules/events/permission';
import * as ExcelJS from 'exceljs';
import IncTempEproFya from '../entities/inc_temp_epro_fya.entity';
import IncTempEproFyi from '../entities/inc_temp_epro_fyi.entity';
import { Injectable } from '@nestjs/common/decorators/core';

@Injectable()
export class AutProfileDetailsService {
  async findAll(userId: number, query: any) {
    let user = await User.findOne({
      where: { id: userId },
      relations: ['organization'],
    });

    const parsedQuery = JSON.parse(query.query || '{}');
    if (parsedQuery) {
      const results = await AutProfileDetails.find({
        where: {
          name: Like(`%${parsedQuery.search}%`), // Use Like to perform a case-insensitive search
        },
      });
      return results;
    } else {
      return AutProfileDetails.find();
    }
  }

  async create(userId: number, data: any) {
    let user = await User.findOne({
      where: { id: userId },
      relations: ['organization'],
    });

    // let label = new AutProfileDetails();
    // label.name = data.name;
    // label.pan = data.color;
    // label.organization = user.organization;
    // await label.save();
    // return label;
  }

  //   async update(id: number, data: CreateLabelDto) {
  //     let label = await Label.findOne(id);
  //     label.name = data.name;
  //     label.color = data.color;
  //     await label.save();
  //     return label;
  //   }

  //   async delete(id: number) {
  //     let label = await Label.findOne({ where: { id } });
  //     await label.remove();
  //     return { succcess: true };
  //   }

  async createMachine(userId: number, id: any, data: any) {
    // const checkAutomation = await AutomationMachines.findOne({
    //   where: { autoCredentials: id, status: 'PENDING' },
    // });
    // if (checkAutomation) {
    //   return 'waiting';
    // } else {
    //   // {"currentIndex":1,"machines":["AUTOMATION-VIDER-1","AUTOMATION-VIDER-2","AUTOMATION-VIDER-3","AUTOMATION-VIDER-4","AUTOMATION-VIDER-5"]}
    //   return this.addIncomeTaxAutomationsTOMachineTable(userId, id, data?.requests);
    // }

    let data1 = [
      {
        modules: data?.requests,
        autoCredentialsId: id,
        type: TypeEnum.INCOMETAX,
      },
    ];
    return this.sendSingleIncometaxAutomationRequestToCamunda(userId, data1);
  }

  async sendSingleIncometaxAutomationRequestToCamunda(userId, data) {
    try {
      let config: any = {
        method: 'post',
        maxBodyLength: Infinity,
        url: `${process.env.CAMUNDA_URL}/vider/quantum/api/automation`,
        headers: {
          'X-USER-ID': userId,
          'Content-Type': 'application/json',
        },
        data: JSON.stringify(data),
      };
      const response = await axios(config);

      const responseData = response?.data;
      return responseData[JSON.stringify(data[0].autoCredentialsId)];
      if (
        responseData[JSON.stringify(data[0].autoCredentialsId)] ===
        'There is already an active request present'
      ) {
        return 'There is already an active request present';
      } else {
        return true;
      }
    } catch (error) {
    }
  }

  async sendIncometaxAutomationRequestToCamunda(userId: number, data: any) {
    let config: any = {
      method: 'post',
      maxBodyLength: Infinity,
      url: `${process.env.CAMUNDA_URL}/vider/quantum/api/automation`,
      headers: {
        'X-USER-ID': userId,
        'Content-Type': 'application/json',
      },
      data: data,
    };

    axios
      .request(config)
      .then((response) => {
        // console.log(JSON.stringify(response.data));
      })
      .catch((error) => {
        console.log(error);
      });
  }

  async addIncomeTaxAutomationsTOMachineTable(userId: number, autoClientId: any, modules: any) {
    let user = await User.findOne({
      where: { id: userId },
    });
    let selectMachine = 'AUTOMATION-TEST-143';
    // const file = fs.readFileSync('machine.json', 'utf8');
    // const indexData = JSON.parse(file);
    // const index = indexData.currentIndex;
    // const machines = indexData.machines;
    // if (index === machines.length - 1 || machines.length <= index) {
    //   selectMachine = machines[0];
    //   fs.writeFileSync('machine.json', JSON.stringify({ currentIndex: 0, machines }));
    // } else {
    //   selectMachine = machines[index + 1];
    //   fs.writeFileSync('machine.json', JSON.stringify({ currentIndex: index + 1, machines }));
    // }

    const automationMachines = new AutomationMachines();
    automationMachines.autoCredentials = autoClientId;
    automationMachines.modules = modules;
    // automationMachines.machineName = 'AUTOMATION-VIDER';
    automationMachines.machineName = selectMachine;
    automationMachines.type = TypeEnum.INCOMETAX;
    automationMachines.status = 'PENDING';
    automationMachines.user = user;
    await automationMachines.save();
    return true;
  }

  async bulkAutomationSync(userId: number, data: any) {
    try {
      // let alreadyPrecentList = [];
      // if (data?.selectedIds) {
      //   for (let autoClient of data?.selectedIds) {
      //     const checkAutomation = await AutomationMachines.findOne({
      //       where: { autoCredentials: autoClient.id, status: 'PENDING' },
      //     });
      //     if (checkAutomation) {
      //       alreadyPrecentList.push(autoClient);
      //     } else {
      //       this.addIncomeTaxAutomationsTOMachineTable(userId, autoClient?.id, data?.requests);
      //     }
      //   }
      // }

      // return alreadyPrecentList;
      if (data?.selectedIds) {
        let abc = [];
        for (let autoClient of data?.selectedIds) {
          abc.push({
            modules: data?.requests,
            autoCredentialsId: autoClient?.id,
            type: TypeEnum.INCOMETAX,
          });
        }

        this.sendIncometaxAutomationRequestToCamunda(userId, JSON.stringify(abc));
      }
    } catch (error) {
    }
  }

  async getclientAutoStatus(id: number, userId: number) {
    try {
      const user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
      const clientCredentials = await AutClientCredentials.findOne({
        where: { id, organizationId: user?.organization?.id },
      });

      if (clientCredentials) {
        const lastCompletedMachine = await AutomationMachines.findOne({
          where: { autoCredentials: id },
          order: {
            id: 'DESC',
          },
          relations: ['autoCredentials', 'autoCredentials.client'],
        });
        let totalInqueueCount = 0;
        if (lastCompletedMachine?.status === 'INQUEUE') {
          totalInqueueCount = await AutomationMachines.count({
            where: { status: 'INQUEUE', id: LessThan(lastCompletedMachine.id), type: 'INCOMETAX' },
          });
        }
        return { lastCompletedMachine, accessDenied: true, totalInqueueCount };
      } else {
        return { accessDenied: false };
      }
    } catch (error) {
    }
  }

  async getclientReport(userId: number, query: any) {
    try {
      const { limit, offset, status, remarks } = query;
      let user = await User.findOne({
        where: { id: userId },
        relations: ['organization', 'role'],
      });
      const entityManager = getRepository(AutomationMachines);

      let sql = await entityManager
        .createQueryBuilder('automationMachines')
        .leftJoinAndSelect('automationMachines.autoCredentials', 'autoCredentials')
        .leftJoinAndSelect('autoCredentials.client', 'client')
        .where('autoCredentials.organizationId = :id', { id: user.organization.id })
        .andWhere('client.status != :status', { status: UserStatus.DELETED })
        .andWhere('autoCredentials.status != :disStatus', { disStatus: IncomeTaxStatus.DISABLE });
      // .andWhere('automationMachines.remarks != :remarks', { remarks: 'Success' }) // Exclude rows with remarks 'Success'
      // .andWhere((qb) => {
      //   const subQuery = qb
      //     .subQuery()
      //     .select('MAX(innerAutomationMachines.id)', 'maxId')
      //     .from(AutomationMachines, 'innerAutomationMachines')
      //     .leftJoin('innerAutomationMachines.autoCredentials', 'innerAutoCredentials')
      //     .where('innerAutoCredentials.organizationId = :id', { id: user.organization.id })
      //     .groupBy('innerAutoCredentials.id')
      //     .getQuery();
      //   return 'automationMachines.id IN ' + subQuery;
      // })
      // .orderBy('automationMachines.id', 'DESC')
      // .limit(limit)
      // .offset(offset)
      // .getManyAndCount();

      if (status) {
        sql = sql.andWhere('automationMachines.status = :status', { status });
      }

      if (remarks) {
        if (remarks === 'Success with Error') {
          sql = sql.andWhere('LOWER(automationMachines.remarks) = :remarks', {
            remarks: remarks.toLowerCase(),
          });
        }
        sql = sql.andWhere('automationMachines.remarks = :remarks', { remarks });
      }

      sql = sql
        .andWhere((qb) => {
          const subQuery = qb
            .subQuery()
            .select('MAX(innerAutomationMachines.id)', 'maxId')
            .from(AutomationMachines, 'innerAutomationMachines')
            .leftJoin('innerAutomationMachines.autoCredentials', 'innerAutoCredentials')
            .where('innerAutoCredentials.organizationId = :id', { id: user.organization.id })
            .groupBy('innerAutoCredentials.id')
            .getQuery();
          return 'automationMachines.id IN ' + subQuery;
        })
        .orderBy('automationMachines.id', 'DESC')
        .limit(limit)
        .offset(offset);
      const sort = typeof query?.sort === 'string' ? JSON.parse(query.sort) : query?.sort;
      if (sort?.column) {
        const columnMap: Record<string, string> = {
          id: 'client.displayName',
          pan: 'autoCredentials.panNumber',
          status: 'automationMachines.status',
          remarks: 'automationMachines.remarks',
          createdAt: 'automationMachines.createdAt',
        };
        const column = columnMap[sort.column] || sort.column;
        sql.orderBy(column, sort.direction.toUpperCase());
      }
      const result = await sql.getManyAndCount();
      return {
        data: result[0],
        count: result[1],
      };
    } catch (error) {
      console.log('error occur while getting  getclientReport', error);
    }
  }

  async exportclientReport(userId: number, query: any) {
    const newQuery = { ...query, offset: 0, limit: ********* };
    let clientreports = await this.getclientReport(userId, newQuery);
    if (!clientreports.data.length) throw new BadRequestException('No Data for Export');
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Inc Tax Sync Status');
    const headers = [
      { header: 'S.No', key: 'serialNo' },
      { header: 'Client Name', key: 'clientName' },
      { header: 'PAN', key: 'pan' },
      { header: 'Password', key: 'password' },
      { header: 'Last Sync', key: 'lastSync' },
      { header: 'Status', key: 'status' },
      { header: 'Remarks', key: 'remarks' },
    ];

    worksheet.columns = headers;
    const columnMaxLengths = Array(headers.length).fill(0);
    let serialCounter = 1; // Initialize a counter
    clientreports.data.forEach((clientreport) => {
      const formatDateTime1 = (dateString: any) => {
        if (!dateString) return '-'; // Handle cases where date is missing
        const date = new Date(dateString);
        return date.toLocaleString(); // Format to local date and time
      };

      const rowData = {
        serialNo: serialCounter++, // Assign and then increment the counter
        clientName: clientreport?.autoCredentials?.client?.displayName,
        pan: clientreport?.autoCredentials['panNumber'],
        password: clientreport?.autoCredentials['password'],
        lastSync: formatDateTime1(clientreport?.createdAt),
        status: capitalize(clientreport?.status),
        remarks: clientreport?.remarks,
      };

      const row = worksheet.addRow(rowData);

      worksheet.columns.forEach((column, colIndex) => {
        const headerLength = column.header?.length || 0;
        const cellLength = rowData[column.key]?.toString().length || 0;
        columnMaxLengths[colIndex] = Math.max(
          columnMaxLengths[colIndex] || 0,
          headerLength,
          cellLength,
        );
      });
    });

    worksheet.columns.forEach((column, colIndex) => {
      column.width = columnMaxLengths[colIndex] + 3;
    });

    const headerRow = worksheet.getRow(1);
    headerRow.font = { bold: true };
    headerRow.eachCell((cell) => {
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '64B5F6' },
      };
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' },
      };
      cell.alignment = { horizontal: 'center', vertical: 'middle' };
    });

    worksheet.columns.forEach((column) => {
      if (column.key === 'clientName') {
        column.width = 50;
        column.alignment = { wrapText: true, horizontal: 'center', vertical: 'middle' };
      } else {
        column.alignment = { horizontal: 'center', vertical: 'middle' };
      }
    });

    worksheet.eachRow((row) => {
      row.eachCell((cell) => {
        cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
      });
    });

    worksheet.views = [{ state: 'frozen', ySplit: 1 }];

    const buffer = await workbook.xlsx.writeBuffer();
    return buffer;
  }

  async getIncometexUpdates(userId: number, query: any) {
    try {
      let user = await User.findOne({
        where: { id: userId },
        relations: ['organization', 'role'],
      });

      let ViewAll = user.role.permissions.find(
        (permission) => permission.slug === Permissions.VIEW_ALL_CLIENT_MANAGERS,
      );
      let ViewAssigned = user.role.permissions.find(
        (permission) => permission.slug === Permissions.VIEW_ASSIGNED_CLIENT_MANAGERS,
      );

      let autUpdateTracker = createQueryBuilder(AutUpdateTracker, 'autUpdateTracker')
        .leftJoinAndSelect('autUpdateTracker.client', 'client')
        .leftJoin('client.clientManagers', 'clientManagers')
        .leftJoinAndSelect('client.autClientCredentials', 'autClientCredentials')
        .where('autUpdateTracker.organizationId = :organizationId', {
          organizationId: user?.organization?.id,
        })
        .andWhere('autUpdateTracker.isChange = :isChange', { isChange: true })
        .andWhere('client.status != :status', { status: UserStatus.DELETED })
        .andWhere('autClientCredentials.status != :disStatus', {
          disStatus: IncomeTaxStatus.DISABLE,
        });

      if (ViewAssigned && !ViewAll) {
        autUpdateTracker.andWhere('clientManagers.id = :userId', { userId });
      }
      const sort = typeof query?.sort === 'string' ? JSON.parse(query.sort) : query?.sort;
      if (sort?.column) {
        const columnMap: Record<string, string> = {
          displayName: 'client.displayName',
        };
        const column = columnMap[sort.column] || sort.column;
        autUpdateTracker.orderBy(column, sort.direction.toUpperCase());
      }
      const result = await autUpdateTracker.getMany();
      // const autUpdates = await AutUpdateTracker.find({
      //   where: { organizationId: user?.organization?.id, isChange: true },
      //   relations: ['client'],
      // });
      return result;
    } catch (error) {      
      console.log('error occur while getting  getIncometexUpdates', error);

    }
  }

  async getUpdatedItem(userId: any, id: number) {
    try {
      const user = await User.findOne({ where: { id: userId }, relations: ['organization'] });

      let updateTracker = getConnection()
        .createQueryBuilder(AutUpdateTracker, 'autUpdateTracker')
        .leftJoinAndSelect('autUpdateTracker.client', 'client')
        .leftJoin('client.organization', 'organization')
        .where('autUpdateTracker.id = :id', { id: id })
        .andWhere('organization.id = :organization', { organization: user.organization.id })
        .getOne();

      // const updateTracker = await AutUpdateTracker.findOne({ where: { id }, relations: ['client'] });
      return updateTracker;
    } catch (error) {
    }
  }

  async getCombinedNotices(userId: number, query: any) {
    try {
      const { limit, offset, search, interval, column, assessmentYear, sort } = query;
      const userRepository = getRepository(User);
      const user = await userRepository.findOne({
        where: { id: userId },
        relations: ['organization', 'role'],
      });

      if (!user?.organization?.id) {
        return { count: 0, result: [] };
      }

      let ViewAll = user.role.permissions.find(
        (permission) => permission.slug === Permissions.VIEW_ALL_CLIENT_MANAGERS,
      );
      let ViewAssigned = user.role.permissions.find(
        (permission) => permission.slug === Permissions.VIEW_ASSIGNED_CLIENT_MANAGERS,
      );

      const organizationId = user.organization.id;

      let fyaNoticeQuery = createQueryBuilder(AutFyaNotice, 'fyaNotice')
        .leftJoinAndSelect('fyaNotice.client', 'client')
        .leftJoinAndSelect('fyaNotice.responses', 'responses')
        .leftJoinAndSelect('fyaNotice.eProceeding', 'eProceeding')
        .leftJoin('client.autClientCredentials', 'autClientCredentials')
        .where('fyaNotice.organizationId = :id', { id: organizationId })
        .andWhere('client.status != :status', { status: UserStatus.DELETED })
        .andWhere('autClientCredentials.status != :status', { status: 'DISABLE' });

      let fyiNoticeQuery = createQueryBuilder(AutFyiNotice, 'fyiNotice')
        .leftJoinAndSelect('fyiNotice.client', 'client')
        .leftJoinAndSelect('fyiNotice.responses', 'responses')
        .leftJoinAndSelect('fyiNotice.eProceeding', 'eProceeding')
        .leftJoin('client.autClientCredentials', 'autClientCredentials')
        .where('fyiNotice.organizationId = :id', { id: organizationId })
        .andWhere('client.status != :status', { status: UserStatus.DELETED })
        .andWhere('autClientCredentials.status != :status', { status: 'DISABLE' });

      const applyConditions = (table: any, alias: string, column) => {
        if (column === 'responseDueDate') {
          if (interval && column) {
            const now = moment().startOf('day'); // Current date without time
            let futureDate;

            if(interval === 'today'){
              futureDate = now.clone().add(1,'day');
            } else if (interval === 'last1week') {
              futureDate = now.clone().add(7, 'days');
            } else if (interval === 'last15days') {
              futureDate = now.clone().add(15, 'days');
            } else if (interval === 'last1month') {
              futureDate = now.clone().add(1, 'month');
            } else if (interval === 'last1year') {
              futureDate = now.clone().add(1, 'year');
            }

            // if (futureDate) {
            //   table.andWhere(`${alias}.${column} BETWEEN :now AND :futureDate`, {
            //     now: now.format('YYYY-MM-DD'),
            //     futureDate: futureDate.format('YYYY-MM-DD'),
            //   });
            // }
            // if (futureDate && alias === 'fyaNotice') {
            //   table.andWhere(`${alias}.manualDueDate BETWEEN :now AND :futureDate`, {
            //     now: now.format('YYYY-MM-DD'),
            //     futureDate: futureDate.format('YYYY-MM-DD'),
            //   });
            // }

            const nowStr = now.format('YYYY-MM-DD');
            const futureStr = futureDate.format('YYYY-MM-DD');
            if (alias === 'fyaNotice') {
              // OR condition between responseDueDate and manualDueDate
              table.andWhere(
                `(${alias}.${column} BETWEEN :now AND :futureDate OR ${alias}.manualDueDate BETWEEN :now AND :futureDate)`,
                { now: nowStr, futureDate: futureStr },
              );
            } else {
              // Just check responseDueDate
              table.andWhere(`${alias}.${column} BETWEEN :now AND :futureDate`, {
                now: nowStr,
                futureDate: futureStr,
              });
            }
          }
        }

        if (interval && column && alias && column === 'remarkSubmittedOn') {
          const now = moment().startOf('day');
          let dateCondition;

          if(interval === 'today'){
            dateCondition = now;
          } else if (interval === 'last15days') {
            dateCondition = now.clone().subtract(15, 'days');
          } else if (interval === 'last1month') {
            dateCondition = now.clone().subtract(1, 'month');
          } else if (interval === 'last1week') {
            dateCondition = now.clone().subtract(7, 'days');
          }

          if (dateCondition) {
            const responseAlias = alias === 'fyaNotice' ? 'responses' : 'responses';
            table.andWhere(`${responseAlias}.submitted_on >= :dateCondition`, {
              dateCondition: dateCondition.format('YYYY-MM-DD'),
            });
          }
        }

        if (
          interval &&
          column &&
          alias &&
          column !== 'responseDueDate' &&
          column !== 'remarkSubmittedOn' &&
          column !== 'all'
        ) {
          let dateRange = null;

          if(interval === 'today'){
            dateRange = moment().startOf('day').format('YYYY-MM-DD');
          } else if (interval === 'last15days') {
            dateRange = moment().subtract(15, 'days').startOf('day').format('YYYY-MM-DD');
          } else if (interval === 'last1month') {
            dateRange = moment().subtract(1, 'months').startOf('day').format('YYYY-MM-DD');
          } else if (interval === 'last1week') {
            dateRange = moment().subtract(7, 'days').startOf('day').format('YYYY-MM-DD');
          }

          if (dateRange) {
            table.andWhere(`DATE(${alias}.${column}) >= :dateRange`, { dateRange });
          }
        }
      };

      const applyFormAndToDateFilter = (table: any, alias: string, column) => {
        const fromDate = moment(query.fromDate).startOf('day').format('YYYY-MM-DD');
        const toDate = moment(query.toDate).endOf('day').format('YYYY-MM-DD');

        if (column === 'all') {
          table.where((qb) => {
            qb.where(`${alias}.responseDueDate BETWEEN :fromDate AND :toDate`, { fromDate, toDate })
              .orWhere(`${alias}.issuedOn BETWEEN :fromDate AND :toDate`, { fromDate, toDate })
              .orWhere(`${alias}.remarkSubmittedOn BETWEEN :fromDate AND :toDate`, {
                fromDate,
                toDate,
              });
          });
        } else {
          if (column) {
            if (column === 'remarkSubmittedOn') {
              table.andWhere('responses.submitted_on BETWEEN :fromDate AND :toDate', {
                fromDate,
                toDate,
              });
            } else {
              table.andWhere(`DATE(${alias}.${column}) BETWEEN :fromDate AND :toDate`, {
                fromDate,
                toDate,
              });
            }
          }
        }
      };

      if (query.fromDate && query.toDate) {
        applyFormAndToDateFilter(fyaNoticeQuery, 'fyaNotice', column);
        applyFormAndToDateFilter(fyiNoticeQuery, 'fyiNotice', column);
      } else {
        applyConditions(fyaNoticeQuery, 'fyaNotice', column);
        applyConditions(fyiNoticeQuery, 'fyiNotice', column);
      }

      if (assessmentYear) {
        fyaNoticeQuery.andWhere('fyaNotice.assesmentYear =:assessmentYear', { assessmentYear });
        fyiNoticeQuery.andWhere('fyiNotice.assessmentYear =:assessmentYear', { assessmentYear });
      }

      if (search) {
        fyaNoticeQuery.andWhere(
          new Brackets((qb) => {
            qb.where('client.displayName LIKE :search', {
              search: `%${query.search}%`,
            });
            qb.orWhere('autClientCredentials.panNumber LIKE :search', {
              search: `%${query.search}%`,
            });
          }),
        );

        fyiNoticeQuery.andWhere(
          new Brackets((qb) => {
            qb.where('client.displayName LIKE :search', {
              search: `%${query.search}%`,
            });
            qb.orWhere('autClientCredentials.panNumber LIKE :search', {
              search: `%${query.search}%`,
            });
          }),
        );
      }

      if (ViewAssigned && !ViewAll) {
        fyaNoticeQuery.andWhere(
          (qb) => {
            const subQuery = qb
              .subQuery()
              .select('1')
              .from('client_client_managers_user', 'cm')
              .where('cm.client_id = client.id')
              .andWhere('cm.user_id = :userId')
              .getQuery();
            return `EXISTS (${subQuery})`;
          },
          { userId: user.id },
        );
      }
      if (ViewAssigned && !ViewAll) {
        fyiNoticeQuery.andWhere(
          (qb) => {
            const subQuery = qb
              .subQuery()
              .select('1')
              .from('client_client_managers_user', 'cm')
              .where('cm.client_id = client.id')
              .andWhere('cm.user_id = :userId')
              .getQuery();
            return `EXISTS (${subQuery})`;
          },
          { userId: user.id },
        );
      }

      if (sort) {
        if (sort === 'responseDueDate') {
          fyaNoticeQuery.orderBy(
            'COALESCE(STR_TO_DATE(NULLIF(fyaNotice.responseDueDate, "NA"), "%Y-%m-%d"), "0000-01-01")',
            'DESC',
          );
          fyiNoticeQuery.orderBy(
            'COALESCE(STR_TO_DATE(NULLIF(fyiNotice.responseDueDate, "NA"), "%Y-%m-%d"), "0000-01-01")',
            'DESC',
          );
        } else if (sort === 'issuedOn') {
          fyaNoticeQuery.orderBy(
            'COALESCE(STR_TO_DATE(NULLIF(fyaNotice.issuedOn, "NA"), "%Y-%m-%d"), "0000-01-01")',
            'DESC',
          );
          fyiNoticeQuery.orderBy(
            'COALESCE(STR_TO_DATE(NULLIF(fyiNotice.issuedOn, "NA"), "%Y-%m-%d"), "0000-01-01")',
            'DESC',
          );
        } else if (sort === 'remarkSubmittedOn') {
          fyaNoticeQuery.orderBy(
            'COALESCE(STR_TO_DATE(NULLIF(responses.submittedOn, "NA"), "%Y-%m-%d"), "0000-01-01")',
            'DESC',
          );
          fyiNoticeQuery.orderBy(
            'COALESCE(STR_TO_DATE(NULLIF(responses.submittedOn, "NA"), "%Y-%m-%d"), "0000-01-01")',
            'DESC',
          );
        } else {
          fyaNoticeQuery.orderBy('fyaNotice.assesmentYear', 'DESC');
          fyiNoticeQuery.orderBy('fyiNotice.assessmentYear', 'DESC');
        }
      } else {
        fyaNoticeQuery.orderBy('fyaNotice.assesmentYear', 'DESC');
        fyiNoticeQuery.orderBy('fyiNotice.assessmentYear', 'DESC');
      }

      const [fyaResult, fyiResult] = await Promise.all([
        // fyaNoticeQuery.getManyAndCount(),
        fyaNoticeQuery.getRawMany(),
        // fyiNoticeQuery.getManyAndCount(),
        fyiNoticeQuery.getRawMany(),
      ]);

      const mapFields = (notices: any[], type: string) => {
        return notices.map((notice) => ({
          id: notice[`${type}Notice_id`],
          pan: notice[`${type}Notice_pan`],
          clientName: notice.client_display_name,
          documentIdentificationNumber: notice[`${type}Notice_document_identification_number`],
          assessmentYear:
            notice[`${type}Notice_assessment_year`] || notice[`${type}Notice_assesment_year`],
          proceedingName: notice[`${type}Notice_proceeding_name`],
          issuedOn: notice[`${type}Notice_issued_on`],
          responseDueDate: notice[`${type}Notice_response_due_date`],
          responseSubmittedOn: notice.responses_submitted_on,
          eProceedingType: notice.eProceeding_type,
          createdType: notice?.[`${type}Notice_created_type`],
          manualDueDate: notice?.[`${type}Notice_manual_due_date`],
          type,
        }));
      };

      // const fyaNoticesWithType = fyaResult.map((notice) => ({ ...notice, type: 'FYA' }));
      // const fyiNoticesWithType = fyiResult.map((notice) => ({ ...notice, type: 'FYI' }));
      const fyaNoticesWithType = mapFields(fyaResult, 'fya');
      const fyiNoticesWithType = mapFields(fyiResult, 'fyi');
      let combinedNotices = [...fyaNoticesWithType, ...fyiNoticesWithType];

      const paginatedNotices = combinedNotices.slice(offset, offset + limit);
      const totalCount = fyaResult.length + fyiResult.length;

      return {
        count: totalCount,
        result: paginatedNotices,
      };
    } catch (error) {
      console.log('error occur while getting getCombinedNotices', error);
    }
  }

  async exportCombinedNotices(userId: number, query: any) {
    const exportQuery = { ...query, offset: 0, limit: ********* };

    let notices = await this.getCombinedNotices(userId, exportQuery);
    if (!notices.result.length) throw new BadRequestException('No Data for Export');
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('e-Proceedings');
    const headers = [
      { header: 'S.No', key: 'serialNo' },
      { header: 'AY', key: 'assessmentYear' },
      { header: 'e-Proceeding Type', key: 'proceedingType' },
      { header: 'Type', key: 'type' },
      { header: 'Client Name', key: 'clientName' },
      { header: 'PAN', key: 'pan' },
      { header: 'DIN', key: 'documentIdentificationNumber' },

      { header: 'Proceeding Name', key: 'proceedingName' },
      { header: 'Issued On', key: 'issuedOn' },
      { header: 'Response Due Date', key: 'responseDueDate' },
      { header: 'Response Submitted On', key: 'responseSubmittedOn' },
    ];

    worksheet.columns = headers;
    const columnMaxLengths = Array(headers.length).fill(0);
    let serialCounter = 1; // Initialize a counter
    if (!notices.result.length) throw new BadRequestException('No Data for Export');
    notices.result.forEach((notice) => {
      const rowData = {
        serialNo: serialCounter++, // Assign and then increment the counter
        type: notice?.type?.toUpperCase(),
        proceedingType: notice?.eProceedingType,
        clientName: notice?.clientName,
        pan: notice?.pan,
        documentIdentificationNumber: notice?.documentIdentificationNumber,
        assessmentYear: generateAssessmentYear(notice?.assessmentYear),
        proceedingName: notice?.proceedingName,
        issuedOn: notice?.issuedOn ? moment(notice?.issuedOn).format('DD-MM-YYYY') : null,
        responseDueDate: notice?.responseDueDate ? moment(notice?.responseDueDate).format('DD-MM-YYYY') : null,
        responseSubmittedOn: notice?.responseSubmittedOn ? moment( notice?.responseSubmittedOn).format('DD-MM-YYYY') : null,
      };

      const row = worksheet.addRow(rowData);

      // Apply conditional coloring for the "Type" column
      const typeCell = row.getCell('proceedingType'); // Get the cell for the "Type" column
      if (rowData.proceedingType === 'Self') {
        typeCell.font = {
          color: { argb: '4B0082' },
          bold: true, // Bold text
        };
      } else if (rowData.proceedingType === 'Other') {
        typeCell.font = {
          color: { argb: 'FF8C00' },
          bold: true, // Bold text
        };
      }
      worksheet.columns.forEach((column, colIndex) => {
        const headerLength = column.header?.length || 0;
        const cellLength = rowData[column.key]?.toString().length || 0;
        columnMaxLengths[colIndex] = Math.max(
          columnMaxLengths[colIndex] || 0,
          headerLength,
          cellLength,
        );
      });
    });

    worksheet.columns.forEach((column, colIndex) => {
      column.width = columnMaxLengths[colIndex] + 2;
    });

    const headerRow = worksheet.getRow(1);
    headerRow.font = { bold: true };
    headerRow.eachCell((cell) => {
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '64B5F6' },
      };
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' },
      };
      cell.alignment = { horizontal: 'center', vertical: 'middle' };
    });

    worksheet.columns.forEach((column) => {
      if (column.key === 'clientName') {
        column.width = 50;
        column.alignment = { wrapText: true, horizontal: 'center', vertical: 'middle' };
      } else {
        column.alignment = { horizontal: 'center', vertical: 'middle' };
      }
    });
    worksheet.eachRow((row) => {
      row.eachCell((cell) => {
        cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
      });
    });

    worksheet.views = [{ state: 'frozen', ySplit: 1 }];

    const buffer = await workbook.xlsx.writeBuffer();
    return buffer;
  }

  async addCredentialsToAtomPro(userId: number, passwordId: number) {
    const user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
    const password = await Password.findOne({ where: { id: passwordId }, relations: ['client'] });
    if (password) {
      const websiteUrl = password?.websiteUrl;
      if (websiteUrl === 'https://services.gst.gov.in/services/login') {
        const checkGstrUserName = checkGstrUsername(password.loginId);
        const checkPassword = checkGstrPassword(password.password);
        if (checkGstrUserName) {
          throw new BadRequestException('Gstr User Name Is Invalid');
        } else {
          if (checkPassword) {
            throw new BadRequestException('Gstr Password Is Invalid');
          } else {
            const existingClinet = await GstrCredentials.findOne({
              where: {
                client: password.client,
              },
            });

            if (existingClinet) {
              throw new BadRequestException('Client is already existing in Atom Pro Gstr');
            } else {
              const checkCredential = await GstrCredentials.findOne({
                where: { organizationId: user.organization.id, userName: password.loginId },
              });

              if (checkCredential) {
                throw new BadRequestException('This User Id is already existing in Atom Pro GSTR');
              } else {
                const gstrCredentialsCount = await GstrCredentials.count({
                  where: { organizationId: user.organization.id, status: GstrStatus.ENABLE },
                });
                const organizationPreferences: any = await OrganizationPreferences.findOne({
                  where: { organization: user?.organization },
                });

                const organizationGstrLimit = organizationPreferences?.automationConfig?.gstrLimit;
                const gstrAccess = organizationPreferences?.automationConfig?.gstr === 'YES';
                if (gstrAccess) {
                  if (organizationGstrLimit > gstrCredentialsCount) {
                    const gstrCredentials = new GstrCredentials();
                    gstrCredentials.userName = String(password.loginId);
                    gstrCredentials.password = String(password.password);
                    gstrCredentials.userId = user?.id;
                    gstrCredentials.clientId = password.client.id;
                    gstrCredentials.organizationId = user?.organization?.id;
                    await gstrCredentials.save();

                    //update password added status
                    password.isExistingAtomPro = IsExistingAtomPro.YES;
                    password['userId'] = userId;
                    await password.save();
                    return 'Atom Pro GSTR Client added Successfully';
                  } else {
                    throw new BadRequestException(
                      'Client Not Added in Atom Pro GSTR due to Client Limit Reached',
                    );
                  }
                } else {
                  throw new BadRequestException(
                    'Subscribe Atom Pro GSTR to access for this Client',
                  );
                }
              }
            }
          }
        }
      } else if (websiteUrl === 'https://eportal.incometax.gov.in/iec/foservices/#/login') {
        const checkPan = checkPanNumber(password.loginId);
        if (checkPan) {
          throw new BadRequestException('Income Tax PAN Invalid');
        } else {
          const autClientCredential = await AutClientCredentials.count({
            where: { organizationId: user?.organization?.id, status: IncomeTaxStatus.ENABLE },
          });
          const existingClinet = await AutClientCredentials.findOne({
            where: {
              client: password.client,
            },
          });

          if (existingClinet) {
            throw new BadRequestException('This client is already existing in Atom Pro Income Tax');
          } else {
            const existingRecord = await AutClientCredentials.findOne({
              where: { organizationId: user?.organization?.id, password: password.loginId },
            });

            if (existingRecord) {
              throw new BadRequestException('This PAN is already existing in Atom Pro Income Tax');
            } else {
              const organizationPreferences: any = await OrganizationPreferences.findOne({
                where: { organization: user?.organization },
              });
              const isIncomeTaxAccess =
                organizationPreferences?.automationConfig?.incomeTax === 'YES';

              if (isIncomeTaxAccess) {
                const organizationIncomeTaxLimit =
                  organizationPreferences?.automationConfig?.incomeTaxLimit;
                if (organizationIncomeTaxLimit > autClientCredential) {
                  const clientCredentials = new AutClientCredentials();
                  clientCredentials.panNumber = String(password.loginId).trim();
                  clientCredentials.password = String(password?.password).trim();
                  clientCredentials.client = password.client;
                  clientCredentials.organizationId = user?.organization?.id;
                  clientCredentials.syncStatus = syncStatus.NOTSYNC;
                  await clientCredentials.save();

                  //update password added status
                  password.isExistingAtomPro = IsExistingAtomPro.YES;
                  password['userId'] = userId;
                  await password.save();

                  return 'Atom Pro Income Tax Client added Successfully';
                } else {
                  throw new BadRequestException(
                    'Client Not Added in Atom Pro Income Tax due to Client Limit Reached',
                  );
                }
              } else {
                throw new BadRequestException(
                  'Subscribe Atom Pro Income Tax to access for this Client',
                );
              }
            }
          }
        }
      }
    }
  }

  async sendAtomProLimitRequest(userId: number) {
    // try {
    const user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
    if (user) {
      const checkRequest = await AtomProLimitRequests.findOne({
        where: { organizationId: user?.organization.id, respondStatus: false },
      });

      if (checkRequest) {
        throw new BadRequestException(
          'Atom Pro client request is already sent in this organization.',
        );
      } else {
        const atomProLimitRequests = new AtomProLimitRequests();
        atomProLimitRequests.user = user;
        atomProLimitRequests.organizationId = user?.organization?.id;
        atomProLimitRequests.respondStatus = false;
        await atomProLimitRequests.save();
      }
    }
    // } catch (error) {
    // console.log('error occur while getting sendAtomProLimitRequest', error);
    // }
  }

  async sendAtomProRequest(userId: number, body: any) {
    try {
      const user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
      if (user) {
        // const checkRequest = await AtomProLimitRequests.findOne({
        //   where: { organizationId: user?.organization.id, respondStatus: false },
        // });

        // if (checkRequest) {
        //   throw new BadRequestException(
        //     'Atom Pro client request is already sent in this organization.',
        //   );
        // } else {
        const atomProLimitRequests = new AtomProLimitRequests();
        atomProLimitRequests.user = user;
        atomProLimitRequests.organizationId = user?.organization?.id;
        atomProLimitRequests.respondStatus = false;
        atomProLimitRequests.requestTypes = body.selected;
        await atomProLimitRequests.save();
        // console.log('atomProLimitRequests', atomProLimitRequests);
        // }
      }
    } catch (error) {
    }
  }

  async getExcelCombinedNotices(userId: number, query: any) {
    try {
      const { limit, offset, search, interval, column, assessmentYear, sort } = query;
      const userRepository = getRepository(User);
      const user = await userRepository.findOne({
        where: { id: userId },
        relations: ['organization', 'role'],
      });

      if (!user?.organization?.id) {
        return { count: 0, result: [] };
      }

      let ViewAll = user.role.permissions.find(
        (permission) => permission.slug === Permissions.VIEW_ALL_CLIENT_MANAGERS,
      );
      let ViewAssigned = user.role.permissions.find(
        (permission) => permission.slug === Permissions.VIEW_ASSIGNED_CLIENT_MANAGERS,
      );

      const organizationId = user.organization.id;

      let fyaNoticeQuery = createQueryBuilder(IncTempEproFya, 'incTempEproFya')
        .leftJoinAndSelect('incTempEproFya.client', 'client')
        .leftJoin('client.clientManagers', 'clientManagers')
        .leftJoin('client.autClientCredentials', 'autClientCredentials')
        .where('incTempEproFya.organizationId = :id', { id: organizationId })
        .andWhere('client.status != :status', { status: UserStatus.DELETED })
        .andWhere('autClientCredentials.status != :status', { status: 'DISABLE' })
        .addSelect("'FYA'", 'eProType');

      let fyiNoticeQuery = createQueryBuilder(IncTempEproFyi, 'incTempEproFyi')
        .leftJoinAndSelect('incTempEproFyi.client', 'client')
        .leftJoin('client.clientManagers', 'clientManagers')
        .leftJoin('client.autClientCredentials', 'autClientCredentials')
        .where('incTempEproFyi.organizationId = :id', { id: organizationId })
        .andWhere('client.status != :status', { status: UserStatus.DELETED })
        .andWhere('autClientCredentials.status != :status', { status: 'DISABLE' })
        .addSelect("'FYI'", 'eProType');

      if (search) {
        fyaNoticeQuery.andWhere(
          'client.displayName LIKE :search OR incTempEproFya.noticeDin LIKE :search',
          {
            search: `%${search}%`,
          },
        );
        fyiNoticeQuery.andWhere(
          'client.displayName LIKE :search OR incTempEproFyi.noticeDin LIKE :search',
          {
            search: `%${search}%`,
          },
        );
      }

      if (ViewAssigned && !ViewAll) {
        fyaNoticeQuery.andWhere('clientManagers.id = :userId', { userId });
      }
      if (ViewAssigned && !ViewAll) {
        fyiNoticeQuery.andWhere('clientManagers.id = :userId', { userId });
      }

      if (assessmentYear) {
        fyaNoticeQuery.andWhere('incTempEproFya.ay = :assessmentYear', { assessmentYear });
        fyiNoticeQuery.andWhere('incTempEproFyi.ay = :assessmentYear', { assessmentYear });
      }

      const applyConditions = (table: any, alias: string, column) => {
        if (column === 'dateOfCompliance') {
          if (interval && column) {
            const now = moment().startOf('day'); // Current date without time
            let futureDate;

            if(interval === 'today'){
              futureDate = now.clone().add(1,'day');
            } else if (interval === 'last1week') {
              futureDate = now.clone().add(7, 'days');
            } else if (interval === 'last15days') {
              futureDate = now.clone().add(15, 'days');
            } else if (interval === 'last1month') {
              futureDate = now.clone().add(1, 'month');
            } else if (interval === 'last1year') {
              futureDate = now.clone().add(1, 'year');
            }

            if (futureDate) {
              table.andWhere(
                `STR_TO_DATE(${alias}.dateOfCompliance, '%d-%m-%Y') BETWEEN :now AND :futureDate`,
                {
                  now: now.format('YYYY-MM-DD'),
                  futureDate: futureDate.format('YYYY-MM-DD'),
                },
              );
            }
          }
        }
        if (column === 'noticeSentDate') {
          if (interval && column) {
            const now = moment().startOf('day'); // Current date without time
            let pastDate;

             if(interval === 'today'){
              pastDate = now.clone().add(0,'day');
            } else if (interval === 'last1week') {
              pastDate = now.clone().subtract(7, 'days');
            } else if (interval === 'last15days') {
              pastDate = now.clone().subtract(15, 'days');
            } else if (interval === 'last1month') {
              pastDate = now.clone().subtract(1, 'month');
            } else if (interval === 'last1year') {
              pastDate = now.clone().subtract(1, 'year');
            }

            if (pastDate) {
              table.andWhere(
                `STR_TO_DATE(${alias}.noticeSentDate, '%d-%m-%Y') BETWEEN :pastDate AND :now`,
                {
                  pastDate: pastDate.format('YYYY-MM-DD'),
                  now: now.format('YYYY-MM-DD'),
                },
              );
            }
          }
        }
        if (column === 'dateResponseSubmitted') {
          if (interval && column) {
            const now = moment().startOf('day'); // Current date without time
            let futureDate;

             if(interval === 'today'){
              futureDate = now.clone().add(1,'day');
            } else if (interval === 'last1week') {
              futureDate = now.clone().add(7, 'days');
            } else if (interval === 'last15days') {
              futureDate = now.clone().add(15, 'days');
            } else if (interval === 'last1month') {
              futureDate = now.clone().add(1, 'month');
            } else if (interval === 'last1year') {
              futureDate = now.clone().add(1, 'year');
            }

            if (futureDate) {
              table.andWhere(
                `STR_TO_DATE(${alias}.dateResponseSubmitted, '%d-%m-%Y') BETWEEN :now AND :futureDate`,
                {
                  now: now.format('YYYY-MM-DD'),
                  futureDate: futureDate.format('YYYY-MM-DD'),
                },
              );
            }
          }
        }
      };

      const applyFormAndToDateFilter = (table: any, alias: string, column) => {
        const fromDate = moment(query.fromDate).startOf('day').format('YYYY-MM-DD');
        const toDate = moment(query.toDate).endOf('day').format('YYYY-MM-DD');

        if (column === 'all') {
          table.where((qb) => {
            qb.where(
              `STR_TO_DATE(${alias}.dateOfCompliance,"%d-%m-%Y") BETWEEN :fromDate AND :toDate`,
              { fromDate, toDate },
            )
              .orWhere(
                `STR_TO_DATE(${alias}.noticeSentDate, "%d-%m-%Y") BETWEEN :fromDate AND :toDate`,
                { fromDate, toDate },
              )
              .orWhere(
                `STR_TO_DATE(${alias}.dateResponseSubmitted, "%d-%m-%Y") BETWEEN :fromDate AND :toDate`,
                {
                  fromDate,
                  toDate,
                },
              );
          });
        } else {
          if (column) {
            table.andWhere(
              `STR_TO_DATE(${alias}.${column},"%d-%m-%Y") BETWEEN :fromDate AND :toDate`,
              {
                fromDate,
                toDate,
              },
            );
          }
        }
      };

      if (query.fromDate && query.toDate) {
        applyFormAndToDateFilter(fyaNoticeQuery, 'incTempEproFya', column);
        applyFormAndToDateFilter(fyiNoticeQuery, 'incTempEproFyi', column);
      } else {
        applyConditions(fyaNoticeQuery, 'incTempEproFya', column);
        applyConditions(fyiNoticeQuery, 'incTempEproFyi', column);
      }

      if (column && sort) {
        fyaNoticeQuery
          .addSelect(`STR_TO_DATE(incTempEproFya.${sort}, '%d-%m-%Y')`, 'formattedDate')
          .orderBy('formattedDate', 'DESC');

        fyiNoticeQuery
          .addSelect(`STR_TO_DATE(incTempEproFyi.${sort}, '%d-%m-%Y')`, 'formattedDate')
          .orderBy('formattedDate', 'DESC');
      }

      // Pagination
      fyaNoticeQuery.skip(offset).take(limit);
      fyiNoticeQuery.skip(offset).take(limit);

      const [fyaNotices, fyaCount] = await fyaNoticeQuery.getManyAndCount();
      const [fyiNotices, fyiCount] = await fyiNoticeQuery.getManyAndCount();

      const formattedFyaNotices = fyaNotices.map((notice) => ({ ...notice, eproType: 'FYA' }));
      const formattedFyiNotices = fyiNotices.map((notice) => ({ ...notice, eproType: 'FYI' }));

      return {
        count: fyaCount + fyiCount,
        result: [...formattedFyaNotices, ...formattedFyiNotices],
      };
    } catch (error) {
      console.log('error occur while getting getCombinedNotices', error);
    }
  }
  async exportEproceedingExcelNotice(userId: number, query: any) {
    const newQuery = { ...query, offset: 0, limit: ********* };
    let clientreports = await this.getExcelCombinedNotices(userId, newQuery);
    if (!clientreports.result.length) throw new BadRequestException('No Data for Export');
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('e-Proceeding Excel');
    const headers = [
      { header: 'S.No', key: 'serialNo' },
      { header: 'AY', key: 'ay' },
      { header: 'e-Pro Type', key: 'eProType' },
      { header: 'Client Name', key: 'clientName' },
      { header: 'PAN', key: 'pan' },
      { header: 'DIN', key: 'din' },
      { header: 'Proceeding Name', key: 'proceedingName' },
      { header: 'Notice Sent Date', key: 'noticeSentDate' },
      { header: 'Date of Compliance', key: 'dateOfCompliance' },
      { header: 'Date of Response Submitted', key: 'responseSubmitted' },
      { header: 'Type', key: 'type' },
    ];

    worksheet.columns = headers;
    const columnMaxLengths = Array(headers.length).fill(0);
    let serialCounter = 1; // Initialize a counter
    clientreports.result.forEach((clientReport) => {
      const formatDateTime1 = (dateString: any) => {
        if (!dateString) return '-'; // Handle cases where date is missing
        const date = new Date(dateString);
        return date.toLocaleString(); // Format to local date and time
      };

      const rowData = {
        serialNo: serialCounter++, // Assign and then increment the counter
        ay: clientReport?.ay,
        eProType: clientReport?.eproType || '',
        clientName: clientReport?.client?.displayName || '',
        pan: clientReport?.pan || '',
        din: clientReport?.noticeDin || '',
        proceedingName: clientReport?.proceedingName || '',
        noticeSentDate: clientReport?.noticeSentDate || '',
        dateOfCompliance: clientReport?.dateOfCompliance || '',
        responseSubmitted: clientReport?.dateResponseSubmitted || '',
        type: capitalize(clientReport?.type) || '',
      };

      const row = worksheet.addRow(rowData);

      worksheet.columns.forEach((column, colIndex) => {
        const headerLength = column.header?.length || 0;
        const cellLength = rowData[column.key]?.toString().length || 0;
        columnMaxLengths[colIndex] = Math.max(
          columnMaxLengths[colIndex] || 0,
          headerLength,
          cellLength,
        );
      });
    });

    worksheet.columns.forEach((column, colIndex) => {
      column.width = columnMaxLengths[colIndex] + 3;
    });

    const headerRow = worksheet.getRow(1);
    headerRow.font = { bold: true };
    headerRow.eachCell((cell) => {
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '64B5F6' },
      };
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' },
      };
      cell.alignment = { horizontal: 'center', vertical: 'middle' };
    });

    worksheet.columns.forEach((column) => {
      if (column.key === 'clientName') {
        column.width = 50;
        column.alignment = { wrapText: true, horizontal: 'center', vertical: 'middle' };
      } else {
        column.alignment = { horizontal: 'center', vertical: 'middle' };
      }
    });

    worksheet.eachRow((row) => {
      row.eachCell((cell) => {
        cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
      });
    });

    worksheet.views = [{ state: 'frozen', ySplit: 1 }];

    const buffer = await workbook.xlsx.writeBuffer();
    return buffer;
  }

  async syncFailedModules(userId: number) {
    try {
      let user = await User.findOne({
        where: { id: userId },
        relations: ['organization'],
      });
      const entityManager = getRepository(AutomationMachines);

      let sql = await entityManager
        .createQueryBuilder('automationMachines')
        .leftJoinAndSelect('automationMachines.autoCredentials', 'autoCredentials')
        .leftJoinAndSelect('autoCredentials.client', 'client')
        .where('autoCredentials.organizationId = :id', { id: user.organization.id })
        .andWhere('client.status != :status', { status: UserStatus.DELETED })
        .andWhere('autoCredentials.status != :disStatus', { disStatus: IncomeTaxStatus.DISABLE })
        .andWhere('automationMachines.remarks = :remarks', { remarks: 'Success with error' })
        .andWhere((qb) => {
          const subQuery = qb
            .subQuery()
            .select('MAX(innerAutomationMachines.id)', 'maxId')
            .from(AutomationMachines, 'innerAutomationMachines')
            .leftJoin('innerAutomationMachines.autoCredentials', 'innerAutoCredentials')
            .where('innerAutoCredentials.organizationId = :id', { id: user.organization.id })
            .groupBy('innerAutoCredentials.id')
            .getQuery();
          return 'automationMachines.id IN ' + subQuery;
        })
        .orderBy('automationMachines.id', 'DESC');

      const result = await sql.getManyAndCount();
      let syncCount = 0;
      try {
        if (result?.[0]?.length > 0) {
          let abc = [];
          for (let machineRecord of result[0]) {
            if (machineRecord?.failedModules && machineRecord?.remarks === 'Success with error') {
              syncCount++;
              abc.push({
                modules: machineRecord?.failedModules,
                autoCredentialsId: machineRecord?.autoCredentials?.id,
                type: TypeEnum.INCOMETAX,
              });
            }
          }
          this.sendIncometaxAutomationRequestToCamunda(userId, JSON.stringify(abc));
        }
      } catch (error) {
        console.log('error occur while bulkAutomationSync', error);
      }

      return {
        count: syncCount,
      };
    } catch (error) {
      console.log('error occur while getting  getclientReport', error);
    }
  }
}
