import { MigrationInterface, QueryRunner } from 'typeorm';

export class AlterServiceTable1662461925196 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
        ALTER TABLE service
        ADD COLUMN recurring_frequency enum('custom', 'monthly','quarterly','half_yearly','yearly') null,
        ADD COLUMN recurring_dates json null;
      `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}
