import { <PERSON><PERSON><PERSON> } from "@nestjs/common";
import { TypeOrmModule } from "@nestjs/typeorm";
import { ChannelPartner } from "./entity/channel-partner.entity";
import { CouponCode } from "./entity/coupon-code.entity";
import { ChannelPartnerSignup } from "./entity/channel-partner-signup.entity";
import { ChannelPartnerController } from "./controllers/channel-partner.controller";
import { ChannelPartnerService } from "./services/channel-partner.service";

@Module({
    imports:[TypeOrmModule.forFeature([ChannelPartner,CouponCode,ChannelPartnerSignup])],
    controllers:[ChannelPartnerController],
    providers:[ChannelPartnerService],

})

export class ChannelPartnerModule{}