import Client from 'src/modules/clients/entity/client.entity';
import {
  BaseEntity,
  Column,
  CreateDateColumn,
  Entity,
  ManyToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import TanClientCredentials from './tan-client-credentials.entity';

@Entity()
class TanEChallan extends BaseEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  tan: string;

  @Column()
  name: string;

  @Column()
  assessmentYear: string;

  @Column()
  financialYear: string;

  @Column()
  majorDesc: string;

  @Column()
  minorDesc: string;

  @Column()
  majorHead: string;

  @Column()
  minorHead: string;

  @Column()
  amtInWords: string;

  @Column()
  cin: string;

  @Column()
  acin: string;

  @Column()
  paymentModeDesc: string;

  @Column()
  bankName: string;

  @Column()
  brn: string;

  @Column()
  paymentTime: string;

  @Column()
  bsrCode: string;

  @Column()
  tenderDt: string;

  @Column()
  challanNum: string;

  @Column()
  subPymntMode: string;

  @Column()
  basicTax: number;

  @Column()
  surCharge: number;

  @Column()
  eduCess: number;

  @Column()
  interest: number;

  @Column()
  penalty: number;

  @Column()
  others: number;

  @Column()
  totalAmt: number;

  @Column()
  organizationId: number;

  @Column()
  clientId: number;

  // @ManyToOne(()=> Client, (client) => client.tanChallan ,{ onDelete: 'SET NULL' })
  // client:Client;

  @ManyToOne(
    () => TanClientCredentials,
    (tanClientCredentials) => tanClientCredentials.tanChallan,
    { onDelete: 'SET NULL' },
  )
  tanClientCredentials: TanClientCredentials;

  @Column()
  natureOfPayment:string;

  @Column()
  sectionDesc:string;

  @CreateDateColumn()
  createdAt: string;

  @UpdateDateColumn()
  updatedAt: string;
}

export default TanEChallan;
