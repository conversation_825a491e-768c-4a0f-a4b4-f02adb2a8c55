import { <PERSON>du<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ClientListener } from 'src/event-listeners/client/client.listener';
import { LeadListener } from 'src/event-listeners/lead.listener';
import { NotificationsService } from 'src/notifications/notifications.service';
import { ClientPasswordController } from './controllers/client-passwords.controller';
import { ClientController } from './controllers/clients.controller';
import { ContactPersonController } from './controllers/contact-persons.controller';
import Client from './entity/client.entity';
import ContactPerson from './entity/contact-person.entity';
import Password from './entity/password.entity';
import { ClientSubscriber } from '../../event-subscribers/client.subscriber';
import { ClientPasswordService } from './services/client-passwords.service';
import { ClientService } from './services/clients.service';
import { ContactPersonService } from './services/contact-persons.service';
import ClientPin from './entity/client-pin.entity';
import { ClientPinsController } from './controllers/client-pins.controller';
import { ClientPinsService } from './services/client-pins.service';
// import { PasswordSubscriber } from 'src/event-subscribers/clientpassword.subscriber';/
import { PasswordSubscriber } from 'src/event-subscribers/clientcreadentials.subscribers';
import { AwsService } from '../storage/upload.service';
import { StorageService } from '../storage/storage.service';
import { OneDriveStorageService } from '../ondrive-storage/onedrive-storage.service';
import { AttachmentsService } from '../tasks/services/attachments.service';
import { ClientPasswordSubscriber } from 'src/event-subscribers/clientpassword.subscriber';
import { ClientPermissionsController } from './controllers/client-permissions.controller';
import { ClientPermission } from './entity/client-permission.entity';
import { ClientPermissionsService } from './services/client-permissions.service';
import { BharathStorageService } from '../storage/bharath-storage.service';
import { BharathCloudService } from '../storage/bharath-upload.service';
import { GoogleDriveStorageService } from '../ondrive-storage/googledrive-storage.service';
import ImportData from './entity/import-data.entity';


@Module({
  imports: [TypeOrmModule.forFeature([Client, ContactPerson, Password, ClientPin, ClientPermission, ImportData])],
  controllers: [ClientController, ContactPersonController, ClientPasswordController, ClientPinsController, ClientPermissionsController],
  providers: [
    ClientService,
    ContactPersonService,
    ClientPasswordService,
    ClientListener,
    NotificationsService,
    LeadListener,
    ClientSubscriber,
    ClientPinsService,
    PasswordSubscriber,
    // ClientPasswordSubscriber,
    StorageService,
    AwsService,
    BharathStorageService,
    BharathCloudService,
    AttachmentsService,
    OneDriveStorageService,
    ClientPermissionsService,
    GoogleDriveStorageService
  ],
})
export class ClientModule { }
