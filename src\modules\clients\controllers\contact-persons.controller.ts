import { Body, Controller, Delete, Param, ParseIntPipe, Post, Put, Req, UseGuards } from '@nestjs/common';
import { ContactPersonService } from '../services/contact-persons.service';
import CreateConctactPersonDto from '../dto/create-contact-person.dto';
import { JwtAuthGuard } from 'src/modules/users/jwt/jwt-auth.guard';

@Controller('contact-persons')
export class ContactPersonController {
  constructor(private service: ContactPersonService) {}

  @UseGuards(JwtAuthGuard)
  @Post()
  create(@Body() body: CreateConctactPersonDto, @Req() req: any) {
    const { userId } = req.user;
    return this.service.create(body, userId);
  }

  @UseGuards(JwtAuthGuard)
  @Put('/:id')
  update(@Param('id', ParseIntPipe) id: number, @Body() body: CreateConctactPersonDto, @Req() req: any) {
    const { userId } = req.user;
    return this.service.update(id, body, userId);
  }

  @UseGuards(JwtAuthGuard)
  @Delete('/:id')
  delete(@Param('id', ParseIntPipe) id: number, @Req() req: any) {
    const { userId } = req.user;
    return this.service.delete(id, userId);
  }
}
