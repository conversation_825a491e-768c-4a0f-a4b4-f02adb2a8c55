import {
  Connection,
  EntitySubscriberInterface,
  EventSubscriber,
  InsertEvent,
} from 'typeorm';
import { InvitedUser } from 'src/modules/users/entities/invited-user.entity';
import { insertINTOnotification, getAdminIDsBasedOnOrganizationId, getUserNamewithUserId } from 'src/utils/re-use';

@EventSubscriber()
export class InviteUserSubscriber implements EntitySubscriberInterface<InvitedUser> {
  constructor(private readonly connection: Connection) {
    connection.subscribers.push(this);
  }

  listenTo() {
    return InvitedUser;
  }

  async beforeInsert(event: InsertEvent<InvitedUser>) {
  }

  async afterInsert(event: InsertEvent<InvitedUser>) {
  }
}
