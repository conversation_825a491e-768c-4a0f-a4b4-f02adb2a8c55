import { <PERSON><PERSON><PERSON>, <PERSON>NotEmpty, <PERSON>Optional, IsString } from 'class-validator';
import { LicenseTypes } from './types';

export class CreateOrganizationLicenseDto {
  @IsNotEmpty()
  @IsString()
  name: string;

  @IsOptional()
  billingEntityId: number;

  @IsNotEmpty()
  @IsEnum(LicenseTypes)
  type: LicenseTypes;

  @IsNotEmpty()
  @IsString()
  licenseNumber: string;

  @IsOptional()
  attachment: string;

  @IsOptional()
  storage: any;
}
