import { BaseEntity, Column, CreateDateColumn, Entity, ManyToOne, PrimaryGeneratedColumn, UpdateDateColumn } from "typeorm";
import GstrRegister from "./gstr-register.entity";



@Entity()
export class ReturnsData extends BaseEntity {


    @PrimaryGeneratedColumn()
    id: number;

    @ManyToOne(() => GstrRegister, (gstrRegister) => gstrRegister.returnsData)
    gstrRegister: GstrRegister;

    @Column({ nullable: true })
    financialYear: string;

    @Column({ nullable: true })
    valid: string;

    @Column({ nullable: true })
    mof: string;

    @Column({ nullable: true })
    dof: string;

    @Column({ nullable: true })
    rtntype: string;

    @Column({ nullable: true })
    retPrd: string;

    @Column({ nullable: true })
    arn: string;

    @Column({ nullable: true })
    status: string;

    @Column('json', { array: true })
    data: object[];

    @CreateDateColumn()
    createdAt: string;

    @UpdateDateColumn()
    updatedAt: string;
}