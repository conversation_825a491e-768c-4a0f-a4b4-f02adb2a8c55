import { IsNotEmpty, IsOptional } from 'class-validator';

class CreateConctactPersonDto {
  @IsNotEmpty()
  name: string;

  @IsOptional()
  image: string;

  @IsNotEmpty()
  role: 'admin' | 'staff' | 'accountant';

  @IsNotEmpty()
  mobile: string;

  @IsNotEmpty()
  email: string;

  @IsOptional()
  dscAvailable: boolean;

  @IsOptional()
  dscExpiryDate: string;

  @IsNotEmpty()
  client: number;

  @IsOptional()
  countryCode: string;

}

export default CreateConctactPersonDto;
