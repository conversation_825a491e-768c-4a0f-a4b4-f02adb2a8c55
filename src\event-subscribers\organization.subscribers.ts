import {
  Connection,
  EntitySubscriberInterface,
  EventSubscriber,
  InsertEvent,
  getManager
} from 'typeorm';
import { Organization } from 'src/modules/organization/entities/organization.entity';
import { sendnewMail } from 'src/emails/newemails';


@EventSubscriber()
export class OrganizationSubscriber implements EntitySubscriberInterface<Organization> {
  constructor(private readonly connection: Connection) {
    connection.subscribers.push(this);
  }

  listenTo() {
    return Organization;
  }

  async beforeInsert(event: InsertEvent<Organization>) {
  }

  async afterInsert(event: InsertEvent<Organization>) {
    const entityManager = getManager()
    const { email, id, legalName } = event?.entity
    const data = {
      organizationName: legalName,
      email: email,
    }
    const jsonData = JSON.stringify(data);
    const mailOptions = {
      data: data,
      email: email,
      filePath: 'organization-signup',
      subject: 'Organization Registration - Vider',
      key: "",
      id: 0  
    };
    // await sendnewMail(mailOptions);
  }
}