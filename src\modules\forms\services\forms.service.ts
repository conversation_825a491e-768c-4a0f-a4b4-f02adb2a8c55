import { BadRequestException, Injectable, InternalServerErrorException } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { InjectModel } from '@nestjs/mongoose';
import axios from 'axios';
import { Response } from 'express';
import { createWriteStream, readFileSync, writeFile } from 'fs';
import * as http from 'https';
import { Model } from 'mongoose';
import { Event_Actions } from 'src/event-listeners/actions';
import { ActivityType } from 'src/modules/activity/activity.entity';
import { AwsService } from 'src/modules/storage/upload.service';
import Task from 'src/modules/tasks/entity/task.entity';
import { User } from 'src/modules/users/entities/user.entity';
import { AddFieldDto } from '../dto/add-field.dto';
import { AddPageDto } from '../dto/add-page.dto';
import CloneFormDto from '../dto/clone-form.dto';
import CreateFormDto, { UpdateFormDto } from '../dto/create-form.dto';
import { EsignDto } from '../dto/esign.dto';
import { FindFormsDto } from '../dto/find.dto';
import ImportFormsDto from '../dto/import-forms.dto';
import { FormType } from '../dto/types';
import { UpdateFieldDto } from '../dto/update-field.dto';
import { UpdatePageDto } from '../dto/update-page.dto';
import { EsignTransaction, EsignTransactionDocument } from '../schemas/esign-transaction.schema';
import { Field } from '../schemas/field.schema';
import { FormActivity, FormActivityDocument } from '../schemas/form-activity.schema';
import { Form, FormDocument } from '../schemas/form.schema';
import { Page } from '../schemas/page.schema';

@Injectable()
export class FormsService {
  constructor(
    @InjectModel(Form.name) private formModel: Model<FormDocument>,
    @InjectModel(FormActivity.name)
    private formActivityModel: Model<FormActivityDocument>,
    @InjectModel(EsignTransaction.name)
    private esignTransactionModel: Model<EsignTransactionDocument>,
    private eventEmitter: EventEmitter2,
    private uploadService: AwsService,
  ) {}

  async createForm(userId: number, data: CreateFormDto) {
    try {
      let user = await User.findOne(userId, { relations: ['organization'] });

      let form = new this.formModel({
        ...data,
        organizationId: user.organization.id,
      });

      await form.save();

      this.eventEmitter.emit(Event_Actions.FORM_CREATED, {
        userId: userId,
        formId: form.id,
        formName: form.name,
        formType: form.type,
        clientId: form.clientId,
      });

      return form;
    } catch (err) {
      console.log(err);
      throw new InternalServerErrorException(err);
    }
  }

  async updateForm(userId: number, id: string, data: UpdateFormDto) {
    try {
      let form = await this.formModel.findById(id);
      form.set(data);

      await form.save();

      this.eventEmitter.emit(Event_Actions.FORM_UPDATED, {
        userId: userId,
        formId: form.id,
        formName: form.name,
        formType: form.type,
      });

      return form;
    } catch (err) {
      console.log(err);
      throw new InternalServerErrorException(err);
    }
  }

  async cloneForm(userId: number, id: string, body: CloneFormDto) {
    let form = await this.formModel.findById(id);

    let clonedForm = JSON.stringify(form, (key, value) => {
      if (key === '_id') {
        return undefined;
      }
      if (key === '__v') {
        return undefined;
      }
      return value;
    });

    let parsedForm = JSON.parse(clonedForm);

    let newForm = new this.formModel(parsedForm);

    if (body.type === FormType.KYB) {
      newForm.type = FormType.KYB;
      newForm.clientId = +body.clientId;
    }

    if (body.type === FormType.TASK) {
      let task = await Task.findOne(body.taskId, { relations: ['client','clientGroup'] });
      newForm.type = FormType.TASK;
      newForm.taskId = task.id;
      newForm.clientId = task.client?.id;
    }

    await newForm.save();

    this.eventEmitter.emit(Event_Actions.FORM_CREATED, {
      userId: userId,
      formId: newForm.id,
      formName: newForm.name,
      formType: newForm.type,
      clientId: newForm.clientId,
    });

    return newForm;
  }

  async getAllForms(userId: number, query: FindFormsDto) {
    let user = await User.findOne(userId, { relations: ['organization'] });

    let options: any = {
      organizationId: user.organization.id,
    };

    if (query.type === FormType.KYB) {
      options.type = FormType.KYB;
      options.clientId = query.clientId;
    }

    if (query.type === FormType.TASK) {
      options.taskId = query.taskId;
    }

    if (query.type === FormType.TEMPLATE) {
      options.type = FormType.TEMPLATE;
    }

    let forms = await this.formModel.find(options, {
      __v: 0,
      organizationId: 0,
    });

    return forms;
  }

  async getDefaultForms() {
    let forms = await this.formModel.find({
      type: FormType.TEMPLATE,
      defaultOne: true,
    });

    return forms;
  }

  async getForm(formId: string) {
    let form = await this.formModel.findById(formId);

    if (!form) {
      throw new BadRequestException('Form not found');
    }

    return form;
  }

  async importForms(userId: number, data: ImportFormsDto) {
    try {
      let user = await User.findOne({
        where: {
          id: userId,
        },
        relations: ['organization'],
      });

      let forms = await this.formModel.find({
        _id: {
          $in: data.forms,
        },
      });

      for (let form of forms) {
        let clonedForm = JSON.stringify(form, (key, value) => {
          if (key === '_id') {
            return undefined;
          }
          if (key === '__v') {
            return undefined;
          }
          return value;
        });

        let parsedForm = JSON.parse(clonedForm);
        let newForm = new this.formModel(parsedForm);
        newForm.organizationId = user.organization.id;
        newForm.defaultOne = false;
        await newForm.save();
      }

      return {
        message: 'Forms imported',
      };
    } catch (e) {
      console.log(e);
      throw new InternalServerErrorException(e);
    }
  }

  async deleteForm(userId: number, formId: string) {
    let form = await this.formModel.findById(formId);

    if (!form) {
      throw new BadRequestException('Form not found');
    }

    await form.remove();

    this.eventEmitter.emit(Event_Actions.FORM_DELETED, {
      userId: userId,
      formId: form.id,
      formName: form.name,
    });

    return form;
  }

  async addPage(userId: number, formId: string, data: AddPageDto) {
    let form = await this.formModel.findById(formId);

    if (!form) {
      throw new BadRequestException('Form not found');
    }

    form.pages.push(data as Page);

    await form.save();

    this.eventEmitter.emit(Event_Actions.FORM_UPDATED, {
      userId: userId,
      formId: form.id,
      formName: form.name,
    });

    return form;
  }

  async clonePage(userId: number, formId: string, pageId: string) {
    let form = await this.formModel.findById(formId);
    let page = form.pages.find((page) => page.id === pageId);

    let clonePage = JSON.stringify(page, (key, value) => {
      if (key === '_id') {
        return undefined;
      }
      if (key === '__v') {
        return undefined;
      }
      return value;
    });

    let parsedPage = JSON.parse(clonePage);

    form.pages.push(parsedPage);

    await form.save();

    this.eventEmitter.emit(Event_Actions.FORM_UPDATED, {
      userId: userId,
      formId: form.id,
      formName: form.name,
    });

    return form;
  }

  async updatePage(userId: number, formId: string, pageId: string, data: UpdatePageDto) {
    let form = await this.formModel.findById(formId);

    if (!form) {
      throw new BadRequestException('Form not found');
    }

    let page = form.pages.find((p) => p._id?.toString() === pageId);

    if (!page) {
      throw new BadRequestException('Page not found');
    }

    if (data.name) {
      page.name = data.name;
    }

    if (data.fields) {
      page.fields = data.fields;
    }

    await form.save();

    this.eventEmitter.emit(Event_Actions.FORM_UPDATED, {
      userId: userId,
      formId: form.id,
      formName: form.name,
    });

    return form;
  }

  async deletePage(formId: string, pageId: string) {
    let form = await this.formModel.findById(formId);

    if (!form) {
      throw new BadRequestException('Form not found');
    }

    let page = form.pages.find((p) => p._id?.toString() === pageId);

    if (!page) {
      throw new BadRequestException('Page not found');
    }

    page.remove();

    await form.save();

    return form;
  }

  async addField(userId: number, formId: string, pageId: string, data: AddFieldDto) {
    try {
      let form = await this.formModel.findById(formId);

      if (!form) {
        throw new BadRequestException('Form not found');
      }

      let page = form.pages.find((p) => p._id?.toString() === pageId);

      if (!page) {
        throw new BadRequestException('Page not found');
      }

      page.fields.push(data as Field);

      await form.save();

      this.eventEmitter.emit(Event_Actions.FORM_UPDATED, {
        userId: userId,
        formId: form.id,
        formName: form.name,
      });

      return form;
    } catch (e) {
      throw new BadRequestException(e.message);
    }
  }

  async updateField(formId: string, pageId: string, fieldId: string, data: UpdateFieldDto) {
    let form = await this.formModel.findById(formId);

    if (!form) {
      throw new BadRequestException('Form not found');
    }

    let page = form.pages.find((p) => p._id?.toString() === pageId);

    if (!page) {
      throw new BadRequestException('Page not found');
    }

    let field = page.fields.find((f) => f._id?.toString() === fieldId);

    if (!field) {
      throw new BadRequestException('Field not found');
    }

    Object.assign(field, data);

    return form.save();
  }

  async deleteField(formId: string, pageId: string, fieldId: string) {
    let form = await this.formModel.findById(formId);

    if (!form) {
      throw new BadRequestException('Form not found');
    }

    let page = form.pages.find((p) => p._id?.toString() === pageId);

    if (!page) {
      throw new BadRequestException('Form Page not found');
    }

    let field = page.fields.find((f) => f._id?.toString() === fieldId);

    if (!field) {
      throw new BadRequestException('Form Field not found');
    }

    field.remove();

    return form.save();
  }

  async getActivity(formId: string) {
    let activity = await this.formActivityModel.find({
      type: ActivityType.FORM,
      typeId: formId,
    });

    return activity;
  }

  async esignDocument(userId: number, formId: string, fieldId: string, body: EsignDto) {
    let user = await User.findOne({ where: { id: userId } });

    let form = await this.formModel.findById(formId);
    let field: Field;

    form.pages.forEach((p) => {
      let f = p.fields.find((f) => f._id.toString() === fieldId);
      if (f) field = f;
    });

    if (!field) {
      throw new BadRequestException('Field not found');
    }

    let documentUrl = field.signatureDocument[0]?.url;
    let document = createWriteStream('document.pdf');

    http.get(documentUrl, function (response) {
      response.pipe(document);
    });

    document.on('error', function (err) {
      console.log(err);
      throw new InternalServerErrorException('Internal server error');
    });

    let esignModel = this.esignTransactionModel;
    let that = this;

    let generateParams = () => {
      return new Promise((resolve, reject) => {
        document.on('finish', onFinish);

        async function onFinish() {
          let referenceNumber = Date.now();

          try {
            let res = await axios({
              method: 'POST',
              url: process.env.EMSIGNER_CRYPTO_SERVER_URL + '/encrypt',
              data: that.prepareData(field, user.fullName, body, referenceNumber),
            });

            let esignTransaction = new esignModel();
            esignTransaction.referenceNumber = referenceNumber;
            esignTransaction.uniqueSessionKey = res.data.uniqueSessionKey;
            esignTransaction.formId = formId;
            esignTransaction.fieldId = fieldId;
            esignTransaction.signatureName = user.fullName;
            await esignTransaction.save();

            resolve(res.data);
          } catch (e) {
            reject(e);
          }
        }
      });
    };

    try {
      let response = await generateParams();
      return response;
    } catch (e) {
      console.log(e);
      throw new InternalServerErrorException('Internal server error');
    }
  }

  prepareData(field: Field, signatureName: string, body: EsignDto, referenceNumber: number) {
    let file = readFileSync('document.pdf');

    let data: any = {
      authToken: '59d8464b-bdbc-452e-8e73-5f5a269e1358',
      referenceNumber: referenceNumber,
      fileType: 'PDF',
      file: file.toString('base64'),
      name: signatureName,
      signatureType: 0,
      signatureMode: '8,12',
      coSign: field?.coSign,
      preview: field?.preview,
      successUrl: body.successUrl,
      failureUrl: body.failureUrl,
      cancelUrl: body.cancelUrl,
    };

    if (field.signatureSelectionMode === 'AUTOMATIC') {
      data.selectPage = field?.selectPage;
      data.signaturePosition = field?.signaturePosition;
    }

    if (field.selectPage === 'SPECIFY') {
      data.pageNumbers = data?.pageNumbers || '';
    }

    if (field.signatureSelectionMode === 'MANUAL') {
      data.selectPage = 'PAGE LEVEL';
      data.pageLevelCoordinates = field?.pageLevelCoordinates;
    }

    return data;
  }

  async esignSuccess(res: Response, body: any) {
    let status = body.ReturnStatus;
    let returnValue = body.Returnvalue;
    let referenceNumber = body.Referencenumber;

    if (status === 'Failure') {
      res.render('esign-failure', {});
      return;
    }

    let uploadService = this.uploadService;
    let formModel = this.formModel;

    try {
      let esignTransaction = await this.esignTransactionModel.findOne({
        referenceNumber: parseInt(referenceNumber),
      });

      let response = await axios({
        method: 'POST',
        url: process.env.EMSIGNER_CRYPTO_SERVER_URL + '/decrypt',
        data: {
          uniqueSessionKey: esignTransaction.uniqueSessionKey,
          signedData: returnValue,
        },
      });

      writeFile('signed-document.pdf', response.data, 'base64', onFinish);

      async function onFinish(err: any) {
        if (err) {
          throw new InternalServerErrorException('Internal server error');
        }

        let formId = esignTransaction.formId;
        let fieldId = esignTransaction.fieldId;

        let form = await formModel.findById(formId);

        let field: Field;

        form.pages.forEach((p: any) => {
          let f = p.fields.find((f: any) => f._id.toString() === fieldId);
          if (f) field = f;
        });

        if (!field) {
          throw new InternalServerErrorException('Internal server error');
        }

        let signedFile: Buffer = readFileSync('signed-document.pdf');
        let key = `esign-documents/${formId}/${fieldId}/${Date.now()}`;

        let upload: any = await uploadService.upload(signedFile, key, 'application/pdf');

        let data = {
          name: 'signed-document.pdf',
          url: upload.Location,
          type: 'application/pdf',
        };

        field.value = data;

        await form.save();

        res.render('esign-success', {});
      }
    } catch (e) {
      console.log(e);
      throw new InternalServerErrorException('Internal server error');
    }
  }
}
