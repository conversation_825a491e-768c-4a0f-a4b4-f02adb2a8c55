import { Optional } from '@nestjs/common';
import Client from 'src/modules/clients/entity/client.entity';
import { Organization } from 'src/modules/organization/entities/organization.entity';
import {
  BaseEntity,
  Column,
  CreateDateColumn,
  Entity,
  ManyToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import AutClientCredentials from './aut_client_credentials.entity';

export enum StorageSystem {
  AMAZON = 'AMAZON',
  MICROSOFT = 'MICROSOFT',
  GOOGLE = 'GOOGLE',
}

@Entity()
class AutIncometaxReturns extends BaseEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  formtypeCd: string;

  @Column()
  filingTypeCd: string;

  @Column()
  incmtaxSecCd: string;

  @Column()
  assmentYear: string;

  @Column()
  ackNum: string;

  @Column()
  submitBy: string;

  @Column()
  submitUserId: string;

  @Column()
  ackDt: string;

  @Column()
  verDt: string;

  @Column()
  submitTmstmp: string;

  @Column()
  demandAmt: string;

  @Column()
  refundAmt: string;

  @Column()
  verStatus: string;

  @Column('json')
  itrPanDetlList: object;

  @Column('json')
  storageFiles: object;

  @Column()
  organizationId: number;

  @Column({ type: 'enum', enum: StorageSystem, default: null })
  storageSystem: StorageSystem;


  @ManyToOne(() => Client, (client) => client.autIncometaxReturns, { onDelete: 'SET NULL' })
  client: Client;

  @ManyToOne(
    () => AutClientCredentials,
    (autClientCredentials) => autClientCredentials.autReturns,
    { onDelete: 'SET NULL' },
  )
  autClientCredentials: AutClientCredentials;

  @Column()
  timeLineDate:string;

  @Column()
  timeLineDesc:string;
}

export default AutIncometaxReturns;
