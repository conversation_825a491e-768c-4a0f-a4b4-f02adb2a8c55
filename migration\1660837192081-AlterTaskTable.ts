import { MigrationInterface, QueryRunner } from 'typeorm';

export class AlterTaskTable1660837192081 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
        ALTER TABLE task
        ADD COLUMN recurring_profile_id int null,
        ADD FOREIGN KEY (recurring_profile_id) REFERENCES recurring_profile(id) ON DELETE SET NULL
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}
