import { Injectable } from '@nestjs/common';
import { OnEvent } from '@nestjs/event-emitter';
import Client from 'src/modules/clients/entity/client.entity';
import { sendNotification } from 'src/notifications/notify';
import { User } from 'src/modules/users/entities/user.entity';
import { Event_Actions } from '../actions';

interface CreateCredential {
  userId: number;
  clientId: number;
}

@Injectable()
export class CredentialsListener {
  @OnEvent(Event_Actions.CREDENTIAL_CREATED, { async: true })
  async handleCredentialCreate(event: CreateCredential) {
    try {
      const { userId, clientId } = event;

      let user = await User.findOne({
        where: {
          id: userId,
        },
      });

      let client = await Client.findOne({
        where: {
          id: clientId,
        },
        relations: ['clientManager'],
      });

      if (!client.clientManager) return;

      let notification = {
        title: `Client Credentials Added`,
        body: `The credentials for client '${client?.displayName}' has been added by ${user?.fullName}`,
      };

      await sendNotification([client.clientManager.id], notification);
    } catch (err) {
      console.log(err);
    }
  }

  @OnEvent(Event_Actions.CREDENTIAL_UPDATED, { async: true })
  async handleCredentialUpdate(event: CreateCredential) {
    try {
      const { userId, clientId } = event;

      let user = await User.findOne({
        where: {
          id: userId,
        },
      });

      let client = await Client.findOne({
        where: {
          id: clientId,
        },
        relations: ['clientManager'],
      });

      if (!client.clientManager) return;

      let notification = {
        title: `Client Credentials Updated`,
        body: `The credentials for client '${client?.displayName}' has been updated by ${user?.fullName}`,
      };

      await sendNotification([client.clientManager.id], notification);
    } catch (err) {
      console.log(err);
    }
  }
}
