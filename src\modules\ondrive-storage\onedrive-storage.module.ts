import { Module, forwardRef } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import AuthToken from './auth-token.entity';
import { OneDriveStorageController } from './onedrive-storage.controller';
import { OneDriveStorageService } from './onedrive-storage.service';
import { GoogleDriveStorageController } from './googledrive-storage.controller';
import { GoogleDriveStorageService } from './googledrive-storage.service';
import { StorageService } from '../storage/storage.service';
import { AwsService } from '../storage/upload.service';
import { StorageModule } from '../storage/storage.module';
import { AttachmentsService } from '../tasks/services/attachments.service';
import { TasksModule } from '../tasks/tasks.module';
import { BharathStorageService } from '../storage/bharath-storage.service';
import { BharathCloudService } from '../storage/bharath-upload.service';

@Module({
  imports: [TypeOrmModule.forFeature([AuthToken]),
  ],
  controllers: [OneDriveStorageController,
    GoogleDriveStorageController],
  providers: [
    OneDriveStorageService,
    GoogleDriveStorageService,
    AttachmentsService,
    StorageService,
    AwsService,
    BharathStorageService,
    BharathCloudService,

  ],
})
export class OneDriveStorageModule { }
