import {
  Connection,
  EntitySubscriberInterface,
  EventSubscriber,
  InsertEvent,
  UpdateEvent,
  getManager,
} from 'typeorm';
import Expenditure from 'src/modules/expenditure/expenditure.entity';
import { getUserDetails, getUserIDs, insertINTONotificationUpdate } from 'src/utils/re-use';
import ViderWhatsappSessions from 'src/modules/whatsapp/entity/viderWhatsappSessions';
import { sendWhatsAppTextMessage } from 'src/modules/whatsapp/whatsapp.service';
import { sendnewMail } from 'src/emails/newemails';
import { Organization } from 'src/modules/organization/entities/organization.entity';

@EventSubscriber()
export class TaskExpenditureSubscriber implements EntitySubscriberInterface<Expenditure> {
  constructor(private readonly connection: Connection) {
    connection.subscribers.push(this);
  }

  listenTo() {
    return Expenditure;
  }

  async beforeInsert(event: InsertEvent<Expenditure>) { }

  async afterInsert(event: InsertEvent<Expenditure>) {
    const { task, taskExpenseType, amount } = event.entity;
    if (task !== undefined) {
      const taskId = task?.id;
      const orgQuery = `SELECT organization_id FROM task where id = ${taskId};`;
      const entityManager = getManager();
      const getOrgId = await entityManager.query(orgQuery);
      const orgId = getOrgId[0]?.organization_id;
      const taskName = task?.name;
      const { client, clientGroup } = event.entity;
      const clientName = client ? client.displayName : clientGroup?.displayName;
      const clientNumber = client?.clientId;
      const title = 'Task Expenditure';
      const { user } = event.entity;
      const userName = user?.fullName;
      const body = `<strong>${userName}</strong> added an expenditure to "<strong>${taskName}</strong>" of <strong>${clientName}</strong>'.`;
      const userIDs = await getUserIDs(taskId);
      // insertINTOnotification(title, body, userIDs, orgId);
      const organization = await Organization.findOne({ id: orgId });


    const addressParts = [
      organization.buildingNo || '',
      organization.floorNumber || '',
      organization.buildingName || '',
      organization.street || '',
      organization.location || '',
      organization.city || '',
      organization.district || '',
      organization.state || ''
    ].filter(part => part && part.trim() !== '');
    const pincode = organization.pincode && organization.pincode.trim() !== '' ? ` - ${organization.pincode}` : '';

    const address = addressParts.join(', ') + pincode;
      const key = 'TASK_EXPENDITURE_PUSH';
      //COMMENTD FOR APPROVALS 
      // insertINTONotificationUpdate(title, body, userIDs, orgId, key,taskId,clientNumber);
      if (event?.entity?.id) {
        for (let user of userIDs) {
          const taskUserDetails = await getUserDetails(user);
          await sendnewMail({
            id: taskUserDetails?.id,
            key: 'TASK_EXPENDITURE_MAIL',
            email: taskUserDetails?.email,
            data: {
              taskUserName: taskUserDetails?.full_name,
              taskName,
              clientName,
              userName,
              taskId: event?.entity?.task?.taskNumber,
              expenditureType: event?.entity?.type,
              amount: event?.entity?.amount,
              userId: event?.entity['userId'],
              adress: address,
                  phoneNumber: organization?.mobileNumber,
                  mail: organization?.email,
                  legalName: organization?.tradeName || organization?.legalName

            },
            filePath: 'task-expenditure',
            subject: `Task expenditure has been added`,
          });
        }
      }

      //Whatsapp Notification
      //       try {
      //         if (userIDs) {
      //           for (let userId of userIDs) {
      //             const sessionValidation = await ViderWhatsappSessions.findOne({
      //               where: { userId: userId },
      //             });
      //             if (sessionValidation) {
      //               const adminUserDetails = await getUserDetails(userId);
      //               const { full_name: userFullName, mobile_number: userPhoneNumber } = adminUserDetails;
      //               const whatsappMessageBody = `
      // Hi *${userFullName}*
      // An Expenditure has been added to the task "*${taskName}*":

      // *Expenditure name:* ${taskExpenseType}
      // *Amount:* ${amount}
      // *Added by:* ${userName}

      // We hope this helps!
      // `;
      //               await sendWhatsAppTextMessage(`91${userPhoneNumber}`, whatsappMessageBody,orgId,);
      //             }
      //           }
      //         }
      //       } catch (error) {
      //         console.error('Error sending Expenditure WhatsApp notification:', error);
      //       }
    }
  }

  exitingTaskExpenditure = 0;
  async beforeUpdate(event: UpdateEvent<Expenditure>) {
    this.exitingTaskExpenditure = event.databaseEntity?.amount;
  }

  // async afterUpdate(event: UpdateEvent<Expenditure>) {
  // const entityManager = getManager()
  //   const { user } = event.entity;
  //   const userName = user.fullName;

  //   const updatedTaskExpenditure = event.entity.amount
  //   const getTaskIdQuery = `SELECT task_id FROM expenditure where id=${event.entity.id};`
  // const taskId = await entityManager.query(getTaskIdQuery)

  //   const userIds = await getUserIDs(taskId[0].task_id)
  //   const title = "Task Expenditure Updated"
  //   const body = `<strong>${userName}</strong> has updated the amount from "<strong>${this.exitingTaskExpenditure}</strong>" to "<strong>${updatedTaskExpenditure}</strong>".`
  //   insertINTOnotification(title,body,userIds)

  // }
}
