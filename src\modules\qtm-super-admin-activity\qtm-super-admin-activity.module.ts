import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
// import { AtomSuperAdminActivity } from './qtm-super-admin-activity.entity';
import { QtmSuperAdminActivityController } from './qtm-super-admin-activity.controller';
import { QtmSuperAdminActivityService } from './qtm-super-admin-activity.service';
import { QtmSuperAdminActivity } from './qtm-super-admin-activity.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([
    QtmSuperAdminActivity
    ]),
  ],
  controllers: [QtmSuperAdminActivityController],
  providers: [QtmSuperAdminActivityService],
})
export class QtmSuperAdminActivityModule {}
