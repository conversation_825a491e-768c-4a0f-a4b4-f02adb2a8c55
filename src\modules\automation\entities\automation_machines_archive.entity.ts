import { Optional } from '@nestjs/common';
import { Organization } from 'src/modules/organization/entities/organization.entity';
import {
  BaseEntity,
  Column,
  CreateDateColumn,
  Entity,
  ManyToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import AutClientCredentials from './aut_client_credentials.entity';
import { User } from 'src/modules/users/entities/user.entity';
import GstrCredentials from 'src/modules/gstr-automation/entity/gstrCredentials.entity';

export enum TypeEnum {
  INCOMETAX = 'INCOMETAX',
  GSTR = 'GSTR',
}

@Entity()
class AutomationMachinesArchive extends BaseEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  machineName: string;

  @ManyToOne(
    () => AutClientCredentials,
    (autClientCredentials) => autClientCredentials.autoCredentials,
    { onDelete: 'SET NULL' },
  )
  autoCredentials: AutClientCredentials;

  @ManyToOne(() => GstrCredentials, (gstrCredentials) => gstrCredentials.gstrCredentials, {
    onDelete: 'SET NULL',
  })
  gstrCredentials: GstrCredentials;

  @Column('json')
  modules: object;

  @Column()
  status: string;

  @ManyToOne(() => User, (user) => user.automationMachines)
  user: User;

  @Column()
  remarks: string;

  @Column()
  createdAt: string;

  @Column()
  updatedAt: string;

  @Column('json')
  completeModules: object;

  @Column({ default: TypeEnum.INCOMETAX, type: 'enum', enum: TypeEnum })
  type: TypeEnum;
}

export default AutomationMachinesArchive;
