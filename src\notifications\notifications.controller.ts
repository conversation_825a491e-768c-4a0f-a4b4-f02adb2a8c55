import { Body, Controller, Get, Patch, Post, Query, Request, UseGuards } from '@nestjs/common';
import { JwtAuthGuard } from 'src/modules/users/jwt/jwt-auth.guard';
import { CreateTokenDto } from './dto/create-token.dto';
import { NotificationsService } from './notifications.service';
import UpdateNotificationsDto from './dto/update.dto';

@Controller('notifications')
export class NotificationsController {
  constructor(private service: NotificationsService) {}

  @UseGuards(JwtAuthGuard)
  @Post('/token')
  saveToken(@Request() request: any, @Body() body: CreateTokenDto) {
    const { userId } = request.user;
    return this.service.saveToken(userId, body);
  }

  @UseGuards(JwtAuthGuard)
  @Get()
  getNotifications(@Request() request: any, @Query() query) {
    const { userId } = request.user;
    return this.service.getAll(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Get('count')
  getNotificationsCount(@Request() request: any, @Query() query) {
    const { userId } = request.user;
    return this.service.getAllCount(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Patch('/update')
  async updateNotifications(@Request() req: any, @Body() body: UpdateNotificationsDto) {
    const { userId } = req.user;

    return this.service.update(userId, body);
  }

  @UseGuards(JwtAuthGuard)
  @Get('/notificationReminder')
 getNotificationReminderData(@Request() request: any, @Query() query) {
    const { userId } = request.user;
    return this.service.getNotificationReminderData(userId, query);
  }
  @UseGuards(JwtAuthGuard)
  @Post('/sendReminderNotification')
  sendReminderNotification(@Request() request: any, @Body() body) {
    const { userId } = request.user;
    return this.service.sendReminderNotification(userId, body);
  }

  @UseGuards(JwtAuthGuard)
  @Get('/mailReminderSent')
 getMailReminderSent(@Request() request: any, @Query() query) {
    const { userId } = request.user;
    return this.service.getMailRemindersSent(userId, query);
  }
}
