import { Organization } from 'src/modules/organization/entities/organization.entity';
import { User } from 'src/modules/users/entities/user.entity';
import {
  BaseEntity,
  Column,
  CreateDateColumn,
  Entity,
  ManyToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { CategoryEnum, SubCategoryEnum } from '../clients/dto/types';

export enum LeadStatusEnum {
  PENDING = 'PENDING',
  CONVERTED = 'CONVERTED',
}

@Entity()
class Lead extends BaseEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  name: string;

  @Column({ nullable: true })
  email: string;

  @Column()
  mobileNumber: string;

  @Column({ nullable: true })
  description: string;

  @Column({ type: 'enum', enum: CategoryEnum })
  category: CategoryEnum;

  @Column({ enum: SubCategoryEnum, type: 'enum', nullable: true })
  subCategory: SubCategoryEnum;

  @Column({ type: 'enum', enum: LeadStatusEnum, default: LeadStatusEnum.PENDING })
  status: LeadStatusEnum;

  @Column({ nullable: true })
  countryCode: string;

  @ManyToOne(() => Organization, (organization) => organization.leads)
  organization: Organization;

  @ManyToOne(() => User, (user) => user.leads)
  user: User;



  @CreateDateColumn()
  createdAt: string;

  @UpdateDateColumn()
  updatedAt: string;
}

export default Lead;
