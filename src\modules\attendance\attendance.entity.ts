import { Organization } from 'src/modules/organization/entities/organization.entity';
import {
  BaseEntity,
  Column,
  CreateDateColumn,
  Entity,
  JoinTable,
  ManyToMany,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { User } from '../users/entities/user.entity';
import { EXPENDITURE_STATUS, ExpenditureStatus } from '../expenditure/dto/types';

export enum AttendanceStatus {
  Present = 'Present',
  Absent = 'Absent',
  Leave = 'Leave',
  HalfDay = 'HalfDay',
  Overtime = 'Overtime',
  Holiday = 'Holiday',
}

@Entity({ name: 'attendance' })
class Attendance extends BaseEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @ManyToOne(() => Organization, (organization) => organization, { onDelete: 'CASCADE' })
  organization: Organization;

  @Column({ type: 'int' })
  userId: number;

  @ManyToOne(() => User, (user) => user.Attendance)
  user: User;
  @ManyToOne(() => User, (user) => user.attendanceCreatedBy)
  createdBy: User;

  // @Column({ type: 'int' })
  // created_by: number;

  @ManyToOne(() => User, (user) => user.attendanceReviewer)
  reviewer: User;

  @Column()
  reviewedAt: string;

  @ManyToMany(() => User, (user) => user.assignedAttendance)
  @JoinTable()
  managers: User[];

  @Column({ type: 'date' })
  attendanceDate: string;


  @Column({
    type: 'enum',
    enum: AttendanceStatus,
    default: AttendanceStatus.Present,
  })
  type: AttendanceStatus;

  @Column()
  checkin_time: string;

  @Column()
  checkout_time: string;

  @Column()
  hours_logged: string;

  @UpdateDateColumn()
  last_updated: string

  @CreateDateColumn()
  createdAt: string;

  @Column({ type: 'int' })
  last_updated_by: string;

  @Column({ type: 'varchar' })
  description: string;

  @Column({ type: 'varchar' })
  approvalDescription: string;

  @Column('json')
  checkInCoordinates: object;

  @Column('json')
  checkInAddress: object;

  @Column('json')
  checkOutAddress: object;

  @Column('json')
  checkOutCoordinates: object;


  @Column({
    type: 'enum',
    enum: EXPENDITURE_STATUS,
    default: null,
  })
  status: EXPENDITURE_STATUS;

  @Column({ nullable: true })
  leaveRequestId: string;

  @Column()
  requestedAt: string;
}
export default Attendance;
