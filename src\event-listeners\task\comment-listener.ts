import { Injectable } from '@nestjs/common';
import { OnEvent } from '@nestjs/event-emitter';
import { createQueryBuilder } from 'typeorm';
import Task from '../../modules/tasks/entity/task.entity';
import { Event_Actions } from '../actions';

interface AddComment {
  userName: string;
  taskId: number;
}

@Injectable()
export class CommentListener {
  @OnEvent(Event_Actions.COMMENT_ADDED, { async: true })
  async handleAddComment(event: AddComment) {
    try {
      const { userName, taskId } = event;
      let task = await createQueryBuilder(Task, 'task')
        .leftJoinAndSelect('task.members', 'members')
        .where('task.id = :id', { id: taskId })
        .getOne();

      let userIds = [];

      task.members.forEach((member: any) => {
        userIds.push(member.id);
      });

      let notification = {
        title: 'Comment Added',
        body: `${userName} has commented in Task '${task.name}'`,
      };

      // await sendNotification(userIds, notification);
    } catch (e) {
      console.log(e);
    }
  }
}
