import {
    Body,
    Controller,
    Get,
    Param,
    ParseIntPipe,
    Post,
    Query,
    Req,
    UseGuards,
} from '@nestjs/common';
import { JwtAuthGuard } from 'src/modules/users/jwt/jwt-auth.guard';
import FindOneDriveStorageDto from './find-onedrive-storage.dto';
import { GoogleDriveStorageService } from './googledrive-storage.service';

@Controller('googledrive')
export class GoogleDriveStorageController {
    constructor(private readonly service: GoogleDriveStorageService) { }

    @UseGuards(JwtAuthGuard)
    @Get()
    getItems(@Req() req: any, @Query() query: FindOneDriveStorageDto) {
        const { userId } = req.user;
        return this.service.getItems(userId, query);
    }

    @UseGuards(JwtAuthGuard)
    @Get('/gmail')
    getGmailItems(@Req() req: any, @Query() query: any) {
        const { userId } = req.user;
        return this.service.getGmailMessages(userId,
            query
        );
    }
    @UseGuards(JwtAuthGuard)
    @Get('/gmail/:id')
    getMailDetails(@Req() req: any, @Param('id') id: any) {
        const { userId } = req.user;
        return this.service.getGmailMessageDetails(userId, id);
    };

    @UseGuards(JwtAuthGuard)
    @Post('/send-gmail/:id')
    sendGmail(@Req() req: any, @Param('id') id: any, @Body() body: any) {
        const { userId } = req.user;
        return this.service.sendReplyToMessage(userId, id, body);
    };

    @UseGuards(JwtAuthGuard)
    @Post('/compose-mail')
    composeMail(@Req() req: any, @Body() body: any) {
        const { userId } = req.user;
        return this.service.sendNewEmail(userId, body)

    }


    @UseGuards(JwtAuthGuard)
    @Post('save-token')
    saveToken(@Req() req: any, @Body() body: any) {
        const { userId } = req.user;
        return this.service.saveToken(userId, body);
    }

    @UseGuards(JwtAuthGuard)
    @Post('re-authorize')
    reAuthorize(@Req() req: any) {
        return this.service.reAuthorize();
    }

    @UseGuards(JwtAuthGuard)
    @Get('/storage-info')
    getGoogleDriveStorageInfo(@Req() req: any, @Query('folderId') folderId?: string) {
        const { userId } = req.user;
        return this.service.getGoogleDriveStorageInfo(userId, folderId);
    }
}
