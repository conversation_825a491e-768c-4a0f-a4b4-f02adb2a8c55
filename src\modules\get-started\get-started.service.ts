import { Injectable } from '@nestjs/common';
import { User } from 'src/modules/users/entities/user.entity';
import { UpdateGetStarted } from './dto/update-get-started.dto';
import { GetStarted } from './get-started.entity';
import { GetStartedStatus } from './types';

@Injectable()
export class GetStartedService {
  async getGetStarted(userId: number) {
    let user = await User.findOne({
      where: { id: userId },
      relations: ['organization'],
    });

    let getStarted = await GetStarted.findOne({
      where: { organization: { id: user.organization.id } },
    });

    if (!getStarted) {
      getStarted = new GetStarted();
      getStarted.organization = user.organization;
      await getStarted.save();
    }

    return getStarted;
  }

  async  updateGetStarted(userId: number, data: UpdateGetStarted) {
    let user = await User.findOne({
      where: { id: userId },
      relations: ['organization'],
    });

    let getStarted = await GetStarted.findOne({
      where: { organization: { id: user.organization.id } },
    });

    if (data.importClients) {
      getStarted.importClients = data.importClients;
    }

    if (data.importTasks) {
      getStarted.importTasks = data.importTasks;
    }

    if (data.importDsc) {
      getStarted.importDsc = data.importDsc;
    }

    if (data.importUsers) {
      getStarted.importUsers = data.importUsers;
    }

    if (data.selectServices) {
      getStarted.selectServices = data.selectServices;
    }

    if (data.createUser) {
      getStarted.createUser = data.createUser;
    }

    if (data.status) {
      getStarted.status = data.status;
    }

    const { importClients, createUser, selectServices,importUsers, importTasks, importDsc } = getStarted;
    let completed = importClients && importUsers && importTasks && importDsc;

    if (completed) {
      getStarted.status = GetStartedStatus.COMPLETED;
    }

    await getStarted.save();
    return getStarted;
  }
}
