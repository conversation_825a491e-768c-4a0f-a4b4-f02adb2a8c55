import Client from 'src/modules/clients/entity/client.entity';
import { Organization } from 'src/modules/organization/entities/organization.entity';
import Storage from 'src/modules/storage/storage.entity';
import { User } from 'src/modules/users/entities/user.entity';
import {
  BaseEntity,
  Column,
  CreateDateColumn,
  Entity,
  JoinColumn,
  JoinTable,
  ManyToMany,
  ManyToOne,
  OneToMany,
  OneToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import BroadcastActivity from './broadcast-activity.entity';
import Label from 'src/modules/labels/label.entity';
@Entity()
class BroadcastEmailTemplates extends BaseEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  title: string;

  @Column()
  labels: string;
  
  @CreateDateColumn()
  createdAt: string;

  @OneToMany(() => BroadcastActivity, (broadcastActivity) => broadcastActivity.template)
  broadcastActivities: BroadcastActivity[];

  @Column()
  content: string;

  @Column()
  subject: string;
  
  @Column()
  default: number;

  @UpdateDateColumn()
  updatedAt: string;

   @ManyToOne(() => Organization, (organization) => organization.broadcastEmailTemplates)
  organization: Organization;

  @OneToOne(() => User, (user) => user.collectData)
  @JoinColumn()
  user: User;

  @OneToOne(() => Label, (label) => label.broadcastEmailTemplates)
  @JoinColumn()
  label: Label;

}

export default BroadcastEmailTemplates;
