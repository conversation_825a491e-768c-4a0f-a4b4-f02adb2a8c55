import { BaseEntity, BeforeInsert, Column, Entity, ManyToMany, PrimaryGeneratedColumn } from 'typeorm';
import Client from './client.entity';

@Entity()
export class ClientPermission extends BaseEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  label: string;

  @Column()
  parentLabel: string;

  @Column()
  name: string;

  @Column({ unique: true })
  slug: string;

  @ManyToMany(() => Client, (client) => client.permissions)
  permissions: Client[];

  @Column({ default: false })
  defaultOne: boolean;

  @BeforeInsert()
  async slugify() {
    this.slug = `${this.label.toLowerCase().replace(/ /g, '_')}_${this.name
      .toLowerCase()
      .replace(/ /g, '_')}`;
  }
}
