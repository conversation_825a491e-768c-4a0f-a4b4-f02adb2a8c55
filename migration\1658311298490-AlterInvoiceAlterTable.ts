import { MigrationInterface, QueryRunner } from 'typeorm';

export class AlterInvoiceAlterTable1658311298490 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
        ALTER TABLE invoice_address
        DROP COLUMN business_name,
        DROP COLUMN type,
        DROP COLUMN address,
        DROP COLUMN gst_treatment,
        DROP COLUMN gst_in,
        ADD legal_name varchar(255) null,
        ADD building_name varchar(255) null,
        ADD street varchar(255) null
     `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}
