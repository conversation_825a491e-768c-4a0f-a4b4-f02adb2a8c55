import { BaseEntity, Column, CreateDateColumn, Entity, ManyToOne, PrimaryGeneratedColumn, UpdateDateColumn } from 'typeorm';
import { Checklist } from './checklist.entity';

@Entity('service_checklist_item')
class ChecklistItem extends BaseEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  name: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  @ManyToOne(() => Checklist, (checklist) => checklist.checklistItems, { onDelete: 'CASCADE' })
  checklist: Checklist;

  @CreateDateColumn()
  createdAt: string;

  @UpdateDateColumn()
  updatedAt: string;
}

export default ChecklistItem;
