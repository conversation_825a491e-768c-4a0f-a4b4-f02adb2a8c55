import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  ParseIntPipe,
  Post,
  Put,
  Request,
  UseGuards,
} from '@nestjs/common';
import { JwtAuthGuard } from 'src/modules/users/jwt/jwt-auth.guard';
import { CreateTeamDto } from '../dto/create-team.dto';
import { TeamsService } from '../services/teams.service';

@Controller('teams')
export class TeamsController {
  constructor(private service: TeamsService) {}

  @UseGuards(JwtAuthGuard)
  @Post()
  async create(@Request() req: any, @Body() body: CreateTeamDto) {
    const { userId } = req.user;
    return this.service.create(userId, body);
  }

  @UseGuards(JwtAuthGuard)
  @Get()
  async get(@Request() req: any) {
    const { userId } = req.user;
    return this.service.get(userId);
  }

  @UseGuards(JwtAuthGuard)
  @Get('/:teamId')
  async getOne(@Param('teamId', ParseIntPipe) teamId: number) {
    return this.service.getOne(teamId);
  }

  @UseGuards(JwtAuthGuard)
  @Post('/:teamId/remove-member')
  async removeFromTeam(
    @Param('teamId', ParseIntPipe) teamId: number,
    @Body() body: { userId: number },
  ) {
    return this.service.removeFromTeam(teamId, body);
  }

  @UseGuards(JwtAuthGuard)
  @Put(':teamId')
  async update(
    @Param('teamId', ParseIntPipe) teamId: number,
    @Body() body: CreateTeamDto,
  ) {
    return this.service.update(teamId, body);
  }

  @UseGuards(JwtAuthGuard)
  @Delete(':teamId')
  async delete(@Param('teamId', ParseIntPipe) teamId: number) {
    return this.service.delete(teamId);
  }
}
