import {
  BadRequestException,
  ConflictException,
  Injectable,
  InternalServerErrorException,
} from '@nestjs/common';
import { User } from 'src/modules/users/entities/user.entity';
import { createQueryBuilder, IsNull } from 'typeorm';
import Category from 'src/modules/categories/categories.entity';
import CreateCategoryDto from 'src/modules/categories/dto/create-category.dto';
import ImportCategoriesDto from 'src/modules/categories/dto/import-categories.dto';

@Injectable()
export class CategoriesService {
  async findAll(userId: number) {
    let user = await User.findOne({
      where: { id: userId },
      relations: ['organization'],
    });

    let categories = await createQueryBuilder(Category, 'category')
      .leftJoinAndSelect('category.subCategories', 'subCategories')
      .leftJoinAndSelect('category.organization', 'organization')
      .where('category.parentCategory is null')
      // .andWhere('organization.id = :orgId', { orgId: user.organization.id })
      .andWhere('(category.defaultOne = :defaultOne OR category.organization_id = :organization)', {
        defaultOne: true,
        organization: user.organization.id
    })
      .getMany();

    return categories;
  }

}
