import { BaseEntity, Column, CreateDateColumn, Entity, OneToMany, PrimaryGeneratedColumn, UpdateDateColumn } from "typeorm"
import { CouponCode } from "./coupon-code.entity";
import { ChannelPartnerSignup } from "./channel-partner-signup.entity";

@Entity()
export class ChannelPartner extends BaseEntity{
    @PrimaryGeneratedColumn()
    id: number;

    @Column()
    name:string;

    @Column({default:false})
    isActive:boolean;

    @CreateDateColumn({ type: 'datetime'})
    createdAt:Date;

    @UpdateDateColumn({ type: 'datetime'})
    updatedAt: Date;

    @OneToMany(()=> CouponCode, (code)=> code.channelPartner)
    couponCode:CouponCode[];

    @OneToMany(() => ChannelPartnerSignup, (signup) => signup.channelPartner)
    signups: ChannelPartnerSignup[];
}
