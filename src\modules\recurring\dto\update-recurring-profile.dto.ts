import { IsDateString, IsEnum, IsOptional } from 'class-validator';
import { RecurringFrequency } from 'src/modules/tasks/dto/types';

class UpdateRecurringProfileDto {
  @IsOptional()
  @IsDateString()
  nextRecurringDate: string;

  @IsOptional()
  @IsDateString()
  endDate: string;

  @IsOptional()
  @IsEnum(RecurringFrequency)
  frequency: RecurringFrequency;
}

export default UpdateRecurringProfileDto;
