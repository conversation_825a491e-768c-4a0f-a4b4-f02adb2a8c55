import {
  BadRequestException,
  ForbiddenException,
  Injectable,
  InternalServerErrorException,
} from '@nestjs/common';
import Client from 'src/modules/clients/entity/client.entity';
import { User } from 'src/modules/users/entities/user.entity';
import { generateInvoiceId, getnerateReceiptNumber } from 'src/utils';
import { Like, Not, createQueryBuilder, getManager, getRepository } from 'typeorm';
import { CreateReceiptDto, NextReceiptNumberDto } from 'src/modules/billing/dto/create-receipt.dto';
import { GetCreditBalanceDto } from 'src/modules/billing/dto/get-credit-balance.dto';
import { GetReceiptsDto } from 'src/modules/billing/dto/get-receipts.dto';
import { Invoice, InvoiceStatus } from 'src/modules/billing/entitities/invoice.entity';
import ReceiptCredit, {
  CreditType,
  ReceiptCreditStatus,
} from 'src/modules/billing/entitities/receipt-credit.entity';
import ReceiptParticular, {
  ReceiptParticularStatus,
} from 'src/modules/billing/entitities/receipt-particular.entity';
import Receipt, { ReceiptStatus, ReceiptType } from 'src/modules/billing/entitities/receipt.entity';
import puppeteer from 'puppeteer';
import { BillingEntity } from 'src/modules/organization/entities/billing-entity.entity';
import { InvoiceService } from './invoice.service';
import InvoiceOtherParticular from 'src/modules/billing/entitities/invoice-other-particular.entity';
import * as xlsx from 'xlsx';

@Injectable()
export class ReceiptsService {
  constructor() { }

  async getClientPortalAll(clientId: number, query: any) {
    let receipts = createQueryBuilder(Receipt, 'receipt')
      .leftJoin('receipt.organization', 'organization')
      .leftJoin('receipt.client', 'client')
      .leftJoin('receipt.billingEntity', 'billingEntity')
      .select([
        'receipt.id',
        'receipt.receiptNumber',
        'receipt.type',
        'receipt.receiptDate',
        'receipt.createdAt',
        'receipt.amount',
        'receipt.status',
        'receipt.paymentMode',
        'receipt.creditsUsed',
        'billingEntity.tradeName',
        'client.displayName',
      ])
      .where('client.id = :clientId', { clientId })
      .orderBy('receipt.createdAt', 'DESC');

    if (query.search) {
      receipts = receipts.andWhere(
        '(receipt.receiptNumber LIKE :search OR client.displayName LIKE :search)',
        {
          search: `%${query.search}%`,
        },
      );
    }

    receipts.skip(query.offset || 0).take(query.limit || 1000);

    let data = await receipts.getManyAndCount();

    return {
      result: data[0],
      total: data[1],
    };
  }

  async getCreditBalance(query: GetCreditBalanceDto) {
    let receiptCredits = await getManager().query(`
       select 
       (
        sum(case when type = 'CREDIT' then amount else 0 end)
         -
        sum(case when type = 'DEBIT' then amount else 0 end)
       ) as balance
       from receipt_credit
       WHERE
       client_id = ${query.clientId} 
       AND
        billing_entity_id=${query.billingEntityId}
        AND
        status='${ReceiptCreditStatus.CREATED}'


    `);

    return receiptCredits[0]?.balance || 0;
  }


  async getReceiptPreview(receiptId: number, query: any) {
    // const user = await User.findOne({ where: { id: userId }, relations: ['organization'] });

    // let receipt = await Receipt.findOne({
    //   where: { id: receiptId, organization: user.organization.id },
    //   relations: ['receiptParticular',
    //     'client',
    //     'billingEntity',
    //     'receiptParticular.invoice',
    //     'receiptParticular.invoice.otherParticulars',
    //     'billingEntity.logStorage',
    //     'billingEntity.signatureStorage',
    //   ]
    // });
    const receiptSql = await getRepository(Receipt)
      .createQueryBuilder('receipt')
      .leftJoinAndSelect('receipt.receiptParticular', 'receiptParticular')
      .leftJoinAndSelect('receipt.client', 'client')
      .leftJoinAndSelect('receipt.clientGroup','clientGroup')
      .leftJoinAndSelect('receipt.billingEntity', 'billingEntity')
      .leftJoinAndSelect('receiptParticular.invoice', 'invoice')
      .leftJoinAndSelect('invoice.otherParticulars', 'otherParticulars')
      .leftJoinAndSelect('billingEntity.logStorage', 'logStorage')
      .leftJoinAndSelect('billingEntity.signatureStorage', 'signatureStorage')
      .where('receipt.id = :receiptId', { receiptId });
    receiptSql.andWhere(
      '(receiptParticular.status != :status OR receiptParticular.status IS NULL)',
      { status: ReceiptParticularStatus.DELETED },
    );
    if (query.orgId) {
      receiptSql.andWhere('receipt.organization = :organizationId', {
        organizationId: query.orgId,
      });
    }

    if (query.clientId) {
      receiptSql.andWhere('client.id = :clientId', { clientId: query.clientId });
    }

    const receipt = await receiptSql.getOne();

    if (receipt) {
      const invoiceIds: number[] = receipt?.receiptParticular.map(
        (eachReceiptParticular) => eachReceiptParticular.invoice.id,
      );
      const sql = `SELECT 
      receipt_particular.invoice_id, 
      IFNULL(sum(pure_agent_amount),0) as payedPgAmount,
       sum(service_amount) as payedSerAmount 
       FROM receipt_particular
       WHERE invoice_id IN (${[...invoiceIds]})
       AND status='${ReceiptParticularStatus.CREATED}'
       GROUP BY invoice_id
       `;
      let srvPgAmountSum: any[] = invoiceIds.length !== 0 ? await getManager().query(sql) : [];

      let details: GetCreditBalanceDto = {
        clientId: receipt?.client ? receipt?.client?.id : receipt?.clientGroup?.id,
        clientType: receipt?.client ? null : receipt?.clientGroup?.type,
        billingEntityId: receipt?.billingEntity?.id,
      };

      const avaliableCredits = await this.getCreditBalance(details);

      receipt.receiptParticular.map((item) => {
        const totalPgAmount = item?.invoice?.otherParticulars?.reduce(
          (accumulator, currentInvoice: any) => {
            return accumulator + currentInvoice.amount * 1;
          },
          0,
        );
        return (
          (item['overallPgDue'] =
            totalPgAmount -
            srvPgAmountSum.find((row) => row.invoice_id === item.invoice.id)?.['payedPgAmount'] *
            1 || 0),
          (item['fixedOverallPgDue'] =
            totalPgAmount +
            item?.pureAgentAmount * 1 -
            srvPgAmountSum.find((row) => row.invoice_id === item.invoice.id)?.['payedPgAmount'] *
            1 || 0),
          (item['tPgAmount'] = totalPgAmount),
          (item['overallServiceDue'] =
            item?.invoice?.grandTotal * 1 -
            +(item?.invoice?.totalCharges * 1) -
            srvPgAmountSum.find((row) => row.invoice_id === item.invoice.id)?.['payedSerAmount'] *
            1 || 0),
          (item['fixedOverallServiceDue'] =
            item?.invoice?.grandTotal -
            +item?.invoice?.totalCharges +
            +(item?.serviceAmount * 1) -
            srvPgAmountSum.find((row) => row.invoice_id === item.invoice.id)?.['payedSerAmount'] *
            1 || 0)
        );
      });

      receipt['avaliableCredits'] = avaliableCredits;
    }
    return receipt || 'Un-Authorized';
  }

  async downloadReceipt(invoiceId: number) {
    let url = `${process.env.WEBSITE_URL}/billing/receipts/${invoiceId}/preview?fromApi=true`;
    // let url = `http://localhost:3000/billing/invoices/${invoiceId}/preview?fromApi=true`;
    try {
const browser = await puppeteer.launch({ headless: true,executablePath: '/usr/bin/chromium-browser',
      });      const page = await browser.newPage();
      await page.setCacheEnabled(false);
      await page.setUserAgent(
        'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/78.0.3904.108 Safari/537.36',
      );
      await page.goto(url, { waitUntil: 'networkidle2' });
      await page.addStyleTag({ content: '#freshworks-container { display: none; }' });
      await page.addStyleTag({ content: '.hide { display: none }' });

      // await page.addStyleTag({ content: '@media print { .myDiv { break-inside: avoid; } }' });

      // await page.addStyleTag({
      //   content: '@page { size: auto; }',
      // });

      // await page.emulateMediaType('screen');
      await page.emulateMediaType('print');

      const pdf = await page.pdf({
        format: 'A4',
        printBackground: true,
        margin: {
          top: '0px',
          right: '0px',
          bottom: '0px',
          left: '0px',
        },
        scale: 0.8,
      });
      await browser.close();

      return pdf;
    } catch (e) {
      console.log(e);
      throw new InternalServerErrorException(e);
    }
  }
}
