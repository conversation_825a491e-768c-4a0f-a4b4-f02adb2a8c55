import {
  BadRequestException,
  ForbiddenException,
  Injectable,
  InternalServerErrorException,
} from '@nestjs/common';
import Client from 'src/modules/clients/entity/client.entity';
import { User } from 'src/modules/users/entities/user.entity';
import { generateInvoiceId, getnerateReceiptNumber } from 'src/utils';
import { Brackets, Like, Not, createQueryBuilder, getManager, getRepository } from 'typeorm';
import { CreateReceiptDto, NextReceiptNumberDto } from '../dto/create-receipt.dto';
import { GetCreditBalanceDto } from '../dto/get-credit-balance.dto';
import { GetReceiptsDto } from '../dto/get-receipts.dto';
import { Invoice, InvoiceStatus } from '../entitities/invoice.entity';
import ReceiptCredit, {
  CreditType,
  ReceiptCreditStatus,
} from '../entitities/receipt-credit.entity';
import ReceiptParticular, {
  ReceiptParticularStatus,
} from '../entitities/receipt-particular.entity';
import Receipt, { ReceiptStatus, ReceiptType } from '../entitities/receipt.entity';
import puppeteer from 'puppeteer';
import { BillingEntity } from 'src/modules/organization/entities/billing-entity.entity';
import { InvoiceService } from './invoice.service';
import InvoiceOtherParticular from '../entitities/invoice-other-particular.entity';
import { Cron, CronExpression } from '@nestjs/schedule';
import * as xlsx from 'xlsx';
import Activity, { ActivityType } from 'src/modules/activity/activity.entity';
import { Event_Actions } from 'src/event-listeners/actions';
import ClientGroup from 'src/modules/client-group/client-group.entity';
import { dateFormation } from 'src/utils/datesFormation';
import * as moment from 'moment';
import { Permissions } from "src/modules/events/permission";
import * as ExcelJS from 'exceljs'


@Injectable()
export class ReceiptsService {
  constructor() { }
  async createReceipt(userId: number, body: CreateReceiptDto) {
    let user = await User.findOne({
      where: { id: userId },
      relations: ['organization'],
    });

    let existingReceipt = await Receipt.findOne({
      where: {
        receiptNumber: body.receiptNumber,
        billingEntity: body.billingEntity,
      },
    });
    if (existingReceipt) {
      throw new InternalServerErrorException('Receipt number already exists');
    }

    let client = null;
    let clientGroup = null;

    if (body?.clientType === "CLIENT_GROUP") {
      clientGroup = await ClientGroup.findOne({ where: { id: body.client } });
    } else {
      client = await Client.findOne({ where: { id: body.client } });
    }

    let billingEntity = await BillingEntity.findOne({ id: body.billingEntity });
    // const receiptrandomnumber = getnerateReceiptNumber(recieptsCount + 1);
    let receipt = new Receipt();
    receipt.receiptNumber = body.receiptNumber;
    receipt.receiptDate = body.receiptDate;
    receipt.amount = body.amount;
    receipt.dueAmount = -body.dueAmount;
    receipt.type = body.type;
    receipt.previousCredits = body.previousCredits;
    receipt.totalCredits = body.totalCredits;
    receipt.creditsUsed = body.creditsUsed;
    receipt.paymentMode = body.paymentMode;
    receipt.paymentDate = body.paymentDate;
    receipt.referenceNumber = body.referenceNumber;
    receipt.client = client;
    receipt.clientGroup = clientGroup;
    receipt.organization = user.organization;
    receipt.invoicesEdited = body?.invoices.map((invoice: any) => invoice.invoice_number);
    receipt.whatsappCheck = body.whatsappCheck;
    receipt.billingEntity = billingEntity;
    // receipt.termsAndConditions = body.termsAndConditions;
    receipt.termsAndConditionsCopy = body.termsAndConditionsCopy;
    receipt.status = ReceiptStatus.CREATED;
    receipt.emailCheck = body.emailCheck;
    receipt['userId'] = userId;
    await receipt.save();

    let savedReceipt = null;

    if (body?.clientType === "CLIENT_GROUP") {
      savedReceipt = await Receipt.findOne({
        where: {
          receiptNumber: body.receiptNumber,
          clientGroup: body.client,
        },
      });
    } else {
      savedReceipt = await Receipt.findOne({
        where: {
          receiptNumber: body.receiptNumber,
          client: body.client,
        },
      });
    }
    if (body?.invoices && body?.invoices?.length > 0) {

      body.selectedInvoices
        .forEach(async (invoice: any) => {
          let currentinvoice = await Invoice.findOne({
            where: { id: invoice.id },
          });
          // const receiptParticularCount = await createQueryBuilder(ReceiptParticular, 'receiptParticular')
          //   .select("SUM(receiptParticular.pureAgentAmount)", "pureAgentAmount")
          //   .addSelect("SUM(receiptParticular.serviceAmount)", "serviceAmount")
          //   .where('receiptParticular.invoiceId=:invoice', { invoice: invoice.id })
          //   .getRawOne();
          // const { pureAgentAmount, serviceAmount } = (receiptParticularCount);
          //Service Amount Calculations
          const finalservicecharge = Math.floor(
            ((invoice.servicecharge -
              ((invoice.subTotal * invoice.tdsRate) / 100)) * 100) + 0.5
          ) / 100;

          const removeddecimal = (invoice.servicedueamount * 1).toFixed(0);
          const receivedservicecharge =
            (invoice.servicepayment * 1) + (invoice.serviceamountpayed * 1)

          //Pure Agent Amount Calculations
          const finalpgcharge = invoice.pgamount * 1;
          const receivedpgcharge =
            invoice.pgamount * 1 - invoice.pgdueamount * 1 + invoice.pgpayment * 1;
          //Invoice Status
          // console.log({ finalpgcharge, receivedpgcharge, finalservicecharge, receivedservicecharge });
          // console.log({ pgpayment: invoice.pgpayment * 1, servicepayment: invoice.servicepayment * 1 })
          // console.log({
          //   servicedueamount: (finalservicecharge - (receivedservicecharge)),
          //   pgdueamount: invoice.pgdueamount * 1,
          //   grand_total: (invoice.grandTotal * 1) - ((((invoice.subTotal * 1) * invoice.tdsRate * 1) / (100)))
          // });
          if (finalpgcharge == receivedpgcharge && finalservicecharge == receivedservicecharge) {
            currentinvoice.status = InvoiceStatus.PAID;
          } else if (invoice.pgpayment * 1 > 0 || invoice.servicepayment * 1 > 0) {
            currentinvoice.status = InvoiceStatus.PARTIALLY_PAID;
          } else if (
            invoice.servicedueamount * 1 + invoice.pgdueamount * 1 ===
            invoice.grandTotal * 1
          ) {
            currentinvoice.status = InvoiceStatus.APPROVAL_PENDING;
          }
          currentinvoice['userId'] = user.id;
          currentinvoice.tdsSection = invoice.tds_section;
          currentinvoice.tdsRate = invoice.tdsRate;
          if (invoice.tds_section && invoice.tdsRate) {
            currentinvoice.hasTds = true;
          }
          await currentinvoice.save();
          if (currentinvoice.status === InvoiceStatus.PARTIALLY_PAID) {
            let activity = new Activity();
            activity.action = Event_Actions.INVOICE_PARTIALLY_PAID;
            activity.actorId = user.id;
            activity.type = client ? ActivityType.BILLING : ActivityType.BILLING_CLIENT_GROUP;
            activity.typeId = client ? client.id : clientGroup?.id;
            activity.remarks = `Invoice "${currentinvoice.invoiceNumber}" Status Changed to Paritally Paid by Payment Receipt "${receipt.receiptNumber}"`;
            await activity.save();
          } else if (currentinvoice.status === InvoiceStatus.PAID) {
            let activity = new Activity();
            activity.action = Event_Actions.INVOICE_PAID;
            activity.actorId = user.id;
            activity.type = client ? ActivityType.BILLING : ActivityType.BILLING_CLIENT_GROUP;
            activity.typeId = client ? client.id : clientGroup?.id;
            activity.remarks = `Invoice "${currentinvoice.invoiceNumber}" Status Changed to Paid by Payment Receipt "${receipt.receiptNumber}"`;
            await activity.save();
          }
          //Receipt Particular for Ecah Invoice
          let receiptParticular = new ReceiptParticular();
          receiptParticular.invoiceId = invoice.id;
          receiptParticular.receiptId = savedReceipt?.id || receipt?.id;
          receiptParticular.pureAgentAmount = invoice.pgpayment * 1 || 0;
          receiptParticular.amount = (invoice.pgpayment * 1 || 0) + (invoice.servicepayment * 1 || 0);
          receiptParticular.serviceAmount = invoice.servicepayment * 1;
          receiptParticular.dueServiceAmount =
            // invoice.servicedueamount * 1 - +(invoice.servicepayment * 1) || 0;
            finalservicecharge - receivedservicecharge;
          receiptParticular.duePureAgentAmount =
            invoice.pgdueamount * 1 - +(invoice.pgpayment * 1) || 0;
          receiptParticular.payFullPgPayment = invoice.payfullpgpayment;
          receiptParticular.payFullServicePayment = invoice.payfullservicepayment;
          receiptParticular.status = ReceiptParticularStatus.CREATED;
          await receiptParticular.save();
        });
    }
    let receiptCredit = new ReceiptCredit();
    receiptCredit.client = client;
    receiptCredit.clientGroup = clientGroup;
    receiptCredit.billingEntity = billingEntity;
    receiptCredit.status = ReceiptCreditStatus.CREATED;
    if (body.type === ReceiptType.ADVANCE) {
      receiptCredit.type = CreditType.CREDIT;
      receiptCredit.amount = body.amount;
      receiptCredit.receipt = receipt;
      await receiptCredit.save();
    } else {
      receiptCredit.type = CreditType.DEBIT;
      receiptCredit.amount = +body.creditsUsed * 1 || 0;
      receiptCredit.receipt = receipt;
      await receiptCredit.save();
    }

    if (receipt.type === ReceiptType.ADVANCE) {
      let activity = new Activity();
      activity.action = Event_Actions.ADVANCE_RECEIPT_CREATED;
      activity.actorId = user.id;
      activity.type = client ? ActivityType.BILLING : ActivityType.BILLING_CLIENT_GROUP;
      activity.typeId = client ? client.id : clientGroup?.id;
      activity.remarks = `Advance Receipt "${receipt.receiptNumber}" Created by ${user.fullName}`;
      await activity.save();
    } else if (receipt.type === ReceiptType.INVOICE) {
      let activity = new Activity();
      activity.action = Event_Actions.PAYMENT_RECEIPT_CREATED;
      activity.actorId = user.id;
      activity.type = client ? ActivityType.BILLING : ActivityType.BILLING_CLIENT_GROUP;
      activity.typeId = client ? client.id : clientGroup?.id;
      activity.remarks = `Payment Receipt "${receipt.receiptNumber}" Created by ${user.fullName}`;
      await activity.save();
    }

    return receipt;
  }

  async updateReceipt(receiptId: number, body: CreateReceiptDto, userId) {
    let user = await User.findOne({
      where: { id: userId },
      relations: ['organization'],
    });


    let receipt = await Receipt.findOne({ where: { id: receiptId }, relations: ['billingEntity', 'client', 'clientGroup'] });
    let existingInvoiceNumber = await Receipt.find({
      where: {
        billingEntity: receipt?.billingEntity?.id,
        receiptNumber: body?.receiptNumber,
        id: Not(receiptId),
      },
    });

    if (existingInvoiceNumber?.length) {
      throw new BadRequestException(`Receipt Number Already Exists`);
    }
    receipt.receiptNumber = body.receiptNumber;
    receipt.receiptDate = body.receiptDate;
    receipt.amount = body.amount;
    receipt.dueAmount = -body.dueAmount;
    receipt.type = body.type;
    receipt.previousCredits = body.previousCredits;
    receipt.totalCredits = body.totalCredits;
    receipt.creditsUsed = body.creditsUsed;
    receipt.paymentMode = body.paymentMode;
    receipt.paymentDate = body.paymentDate;
    receipt.referenceNumber = body.referenceNumber;
    // receipt.termsAndConditions = body.termsAndConditions;
    receipt.termsAndConditionsCopy = body.termsAndConditionsCopy;
    receipt.whatsappCheck = body.whatsappCheck;
    receipt['userId'] = userId;
    await receipt.save();
    if (body?.invoices && body?.invoices?.length > 0) {
      // body.invoices.forEach(async (invoice: any) => {
      //   let currentinvoice = await Invoice.findOne({
      //     where: { id: invoice?.invoice?.id },
      //     relations: ['client', 'clientGroup']
      //   });
      //   const oldStatus = currentinvoice.status;

      //   //Check Invoice Update in Real Time
      //   const realTimePgAmount = await createQueryBuilder(
      //     InvoiceOtherParticular,
      //     'invoiceOtherParticular',
      //   )
      //     .select('IFNULL(SUM(invoiceOtherParticular.amount),0)', 'pureAgentAmount')
      //     .where('invoiceOtherParticular.invoice_id=:invoice', { invoice: invoice?.invoice?.id })
      //     .getRawOne();

      //   const { pureAgentAmount } = realTimePgAmount;
      //   const realTimeServiceAmount = await createQueryBuilder(Invoice, 'invoice')
      //     .select('IFNULL((invoice.grand_total-invoice.total_charges),0)', 'serviceAmount')
      //     .where('invoice.id=:invoice', { invoice: invoice?.invoice?.id })
      //     .getRawOne();
      //   const { serviceAmount } = realTimeServiceAmount;
      //   //todo:
      //   // if ((pureAgentAmount * 1) !== invoice?.tPgAmount || (serviceAmount * 1) !== invoice?.fixedOverallServiceDue) {
      //   //   throw new ForbiddenException(
      //   //     'Invoice Updated In Real Time',
      //   //   );
      //   // }
      //   //Invoice Update
      //   // console.log(invoice)
      //   if (invoice.overallPgDue * 1 + invoice.overallServiceDue === 0) {
      //     currentinvoice.status = InvoiceStatus.PAID;
      //   } else if (
      //     invoice.overallPgDue * 1 + invoice.overallServiceDue * 1 ===
      //     invoice?.invoice?.grandTotal * 1
      //   ) {
      //     currentinvoice.status = InvoiceStatus.APPROVAL_PENDING;
      //   } else {
      //     currentinvoice.status = InvoiceStatus.PARTIALLY_PAID;
      //   }
      //   currentinvoice['userId'] = user.id;
      //   await currentinvoice.save();
      //   //Receipt Particular Update
      //   const receiptParticular = await ReceiptParticular.findOne({ id: invoice.id });
      //   if (invoice?.createdAt) {
      //     receiptParticular.pureAgentAmount = invoice.pureAgentAmount * 1;
      //     receiptParticular.serviceAmount = invoice.serviceAmount * 1;
      //     receiptParticular.amount = invoice.pureAgentAmount * 1 + invoice.serviceAmount * 1;
      //     receiptParticular.duePureAgentAmount = invoice.overallPgDue * 1;
      //     receiptParticular.dueServiceAmount = invoice.overallServiceDue * 1;
      //     receiptParticular.payFullPgPayment = invoice.payFullPgPayment;
      //     receiptParticular.payFullServicePayment = invoice.payFullServicePayment;
      //     await receiptParticular.save();
      //   } else {
      //     const receiptParticular = new ReceiptParticular();
      //     receiptParticular.invoiceId = invoice.id;
      //     receiptParticular.receipt = receipt;
      //     receiptParticular.pureAgentAmount = invoice.pureAgentAmount * 1 || 0;
      //     receiptParticular.amount =
      //       (invoice.pureAgentAmount * 1 || 0) + (invoice.serviceAmount * 1 || 0);
      //     receiptParticular.serviceAmount = invoice.serviceAmount * 1 || 0;
      //     receiptParticular.dueServiceAmount =
      //       invoice.servicedueamount * 1 - +(invoice.servicepayment * 1) || 0;
      //     receiptParticular.duePureAgentAmount =
      //       invoice.pgdueamount * 1 - +(invoice.pgpayment * 1) || 0;
      //     receiptParticular.payFullPgPayment = invoice.payFullPgPayment;
      //     receiptParticular.payFullServicePayment = invoice.payFullServicePayment;
      //     receiptParticular.status = ReceiptParticularStatus.CREATED;
      //     await receiptParticular.save();
      //   }

      //   if (currentinvoice.status === InvoiceStatus.PARTIALLY_PAID && oldStatus !== currentinvoice.status) {
      //     let activity = new Activity();
      //     activity.action = Event_Actions.INVOICE_PARTIALLY_PAID;
      //     activity.actorId = user.id;
      //     activity.type = currentinvoice?.client ? ActivityType.BILLING : ActivityType.BILLING_CLIENT_GROUP;
      //     activity.typeId = currentinvoice?.client ? currentinvoice?.client?.id : currentinvoice?.clientGroup?.id;
      //     activity.remarks = `Invoice "${currentinvoice.invoiceNumber}" Status Changed to Paritally Paid by Payment Receipt "${receipt.receiptNumber}"`;
      //     await activity.save();
      //   } else if (currentinvoice.status === InvoiceStatus.PAID && oldStatus !== currentinvoice.status) {
      //     let activity = new Activity();
      //     activity.action = Event_Actions.INVOICE_PAID;
      //     activity.actorId = user.id;
      //     activity.type = currentinvoice?.client ? ActivityType.BILLING : ActivityType.BILLING_CLIENT_GROUP;
      //     activity.typeId = currentinvoice?.client ? currentinvoice?.client?.id : currentinvoice?.clientGroup?.id;
      //     activity.remarks = `Invoice "${currentinvoice.invoiceNumber}" Status Changed to Paid by Payment Receipt "${receipt.receiptNumber}"`;
      //     await activity.save();
      //   }
      // });

      // body.invoices.forEach(async (invoice: any) => {
      //   let currentinvoice = await Invoice.findOne({
      //     where: { id: invoice?.invoice?.id },
      //     relations: ['client', 'clientGroup']
      //   });
      //   const oldStatus = currentinvoice.status;
      //   // const totalServiceAmount = (+((invoice?.invoice?.grandTotal * 1) - +invoice?.invoice?.totalCharges) - ((((invoice.invoice.subTotal * 1) * invoice.invoice.tdsRate * 1) / (100))));
      //   const serivceAmountDue = (((+((invoice?.invoice?.grandTotal * 1) - +invoice?.invoice?.totalCharges) - ((((invoice.invoice.subTotal * 1) * invoice.invoice.tdsRate * 1) / (100)))) - (invoice.overallServicePayed)))


      //   if (invoice.overallPgDue * 1 + serivceAmountDue * 1 === 0) {
      //     currentinvoice.status = InvoiceStatus.PAID;
      //   } else if (
      //     invoice.overallPgDue * 1 + invoice.serivceAmountDue * 1 ===
      //     invoice?.invoice?.grandTotal * 1
      //   ) {
      //     currentinvoice.status = InvoiceStatus.APPROVAL_PENDING;
      //   } else {
      //     currentinvoice.status = InvoiceStatus.PARTIALLY_PAID;
      //   }
      //   currentinvoice['userId'] = user.id;
      //   currentinvoice.tdsRate = invoice.invoice.tdsRate;
      //   currentinvoice.tdsSection = invoice.invoice.tdsSection;
      //   await currentinvoice.save();
      //   //Receipt Particular Update
      //   const receiptParticular = await ReceiptParticular.findOne({ id: invoice.id });
      //   if (invoice?.createdAt) {
      //     receiptParticular.pureAgentAmount = invoice.pureAgentAmount * 1;
      //     receiptParticular.serviceAmount = invoice.serviceAmount * 1;
      //     receiptParticular.amount = invoice.pureAgentAmount * 1 + invoice.serviceAmount * 1;
      //     receiptParticular.duePureAgentAmount = invoice.overallPgDue * 1;
      //     receiptParticular.dueServiceAmount = invoice.overallServiceDue * 1;
      //     receiptParticular.payFullPgPayment = invoice.payFullPgPayment;
      //     receiptParticular.payFullServicePayment = invoice.payFullServicePayment;
      //     await receiptParticular.save();
      //   } else {
      //     const receiptParticular = new ReceiptParticular();
      //     receiptParticular.invoiceId = invoice.id;
      //     receiptParticular.receipt = receipt;
      //     receiptParticular.pureAgentAmount = invoice.pureAgentAmount * 1 || 0;
      //     receiptParticular.amount =
      //       (invoice.pureAgentAmount * 1 || 0) + (invoice.serviceAmount * 1 || 0);
      //     receiptParticular.serviceAmount = invoice.serviceAmount * 1 || 0;
      //     receiptParticular.dueServiceAmount =
      //       invoice.servicedueamount * 1 - +(invoice.servicepayment * 1) || 0;
      //     receiptParticular.duePureAgentAmount =
      //       invoice.pgdueamount * 1 - +(invoice.pgpayment * 1) || 0;
      //     receiptParticular.payFullPgPayment = invoice.payFullPgPayment;
      //     receiptParticular.payFullServicePayment = invoice.payFullServicePayment;
      //     receiptParticular.status = ReceiptParticularStatus.CREATED;
      //     await receiptParticular.save();
      //   }

      //   if (currentinvoice.status === InvoiceStatus.PARTIALLY_PAID && oldStatus !== currentinvoice.status) {
      //     let activity = new Activity();
      //     activity.action = Event_Actions.INVOICE_PARTIALLY_PAID;
      //     activity.actorId = user.id;
      //     activity.type = currentinvoice?.client ? ActivityType.BILLING : ActivityType.BILLING_CLIENT_GROUP;
      //     activity.typeId = currentinvoice?.client ? currentinvoice?.client?.id : currentinvoice?.clientGroup?.id;
      //     activity.remarks = `Invoice "${currentinvoice.invoiceNumber}" Status Changed to Paritally Paid by Payment Receipt "${receipt.receiptNumber}"`;
      //     await activity.save();
      //   } else if (currentinvoice.status === InvoiceStatus.PAID && oldStatus !== currentinvoice.status) {
      //     let activity = new Activity();
      //     activity.action = Event_Actions.INVOICE_PAID;
      //     activity.actorId = user.id;
      //     activity.type = currentinvoice?.client ? ActivityType.BILLING : ActivityType.BILLING_CLIENT_GROUP;
      //     activity.typeId = currentinvoice?.client ? currentinvoice?.client?.id : currentinvoice?.clientGroup?.id;
      //     activity.remarks = `Invoice "${currentinvoice.invoiceNumber}" Status Changed to Paid by Payment Receipt "${receipt.receiptNumber}"`;
      //     await activity.save();
      //   }
      // });
      body.invoices.forEach(async (invoice: any) => {
        let currentinvoice = await Invoice.findOne({
          where: { id: invoice?.invoice?.id },
          relations: ['client', 'clientGroup']
        });
        const oldStatus = currentinvoice.status;
        // const totalServiceAmount = (+((invoice?.invoice?.grandTotal * 1) - +invoice?.invoice?.totalCharges) - ((((invoice.invoice.subTotal * 1) * invoice.invoice.tdsRate * 1) / (100))));
        const totalServiceAmount = Math.round(
          (invoice?.invoice?.grandTotal * 100 -
            invoice?.invoice?.totalCharges * 100 -
            (invoice.invoice.subTotal * invoice.invoice.tdsRate))
        ) / 100
        // const serivceAmountDue = (((+((invoice?.invoice?.grandTotal * 1) - +invoice?.invoice?.totalCharges) - ((((invoice.invoice.subTotal * 1) * invoice.invoice.tdsRate * 1) / (100)))) - (invoice.overallServicePayed)))
        const serivceAmountDue = Math.round(
          (invoice?.invoice?.grandTotal * 100 -
            invoice?.invoice?.totalCharges * 100 -
            (invoice.invoice.subTotal * invoice.invoice.tdsRate)) -
          invoice.overallServicePayed * 100
        ) / 100

        // console.log({
        //   overallPgDue: invoice.overallPgDue * 1,
        //   serivceAmountDue: serivceAmountDue * 1,
        //   grandTotal: invoice?.invoice?.grandTotal * 1
        // })
        if (invoice.overallPgDue * 1 + serivceAmountDue * 1 === 0) {
          currentinvoice.status = InvoiceStatus.PAID;
        } else if (
          invoice.overallPgDue * 1 + invoice.serivceAmountDue * 1 ===
          invoice?.invoice?.grandTotal * 1
        ) {
          currentinvoice.status = InvoiceStatus.APPROVAL_PENDING;
        } else {
          currentinvoice.status = InvoiceStatus.PARTIALLY_PAID;
        }
        currentinvoice['userId'] = user.id;
        currentinvoice.tdsRate = invoice.invoice.tdsRate;
        currentinvoice.tdsSection = invoice.invoice.tdsSection;
        await currentinvoice.save();
        //Receipt Particular Update
        const receiptParticular = await ReceiptParticular.findOne({ id: invoice.id });
        if (invoice?.createdAt) {
          // console.log({
          //   pureAgentAmount: invoice.pureAgentAmount * 1,
          //   serviceAmount: invoice.serviceAmount * 1,
          //   duePureAgentAmount: invoice.overallPgDue * 1,
          //   overallServiceDue: serivceAmountDue,
          //   payFullPgPayment: invoice.payFullPgPayment,
          //   payFullServicePayment: invoice.payFullServicePayment,
          // })
          receiptParticular.pureAgentAmount = invoice.pureAgentAmount * 1;
          receiptParticular.serviceAmount = invoice.serviceAmount * 1;
          receiptParticular.amount = invoice.pureAgentAmount * 1 + invoice.serviceAmount * 1;
          receiptParticular.duePureAgentAmount = invoice.overallPgDue * 1;
          receiptParticular.dueServiceAmount = serivceAmountDue;
          receiptParticular.payFullPgPayment = invoice.payFullPgPayment;
          receiptParticular.payFullServicePayment = invoice.payFullServicePayment;
          await receiptParticular.save();
        } else {
          const receiptParticular = new ReceiptParticular();
          receiptParticular.invoiceId = invoice.id;
          receiptParticular.receipt = receipt;
          receiptParticular.pureAgentAmount = invoice.pureAgentAmount * 1 || 0;
          receiptParticular.amount =
            (invoice.pureAgentAmount * 1 || 0) + (invoice.serviceAmount * 1 || 0);
          receiptParticular.serviceAmount = (invoice.serviceAmount * 1 || 0);
          //  totalServiceAmount || 0;
          receiptParticular.dueServiceAmount =
            // invoice.servicedueamount * 1 - +(invoice.servicepayment * 1) || 0;
            serivceAmountDue;
          receiptParticular.duePureAgentAmount =
            invoice.pgdueamount * 1 - +(invoice.pgpayment * 1) || 0;
          receiptParticular.payFullPgPayment = invoice.payFullPgPayment;
          receiptParticular.payFullServicePayment = invoice.payFullServicePayment;
          receiptParticular.status = ReceiptParticularStatus.CREATED;
          await receiptParticular.save();
        }

        if (currentinvoice.status === InvoiceStatus.PARTIALLY_PAID && oldStatus !== currentinvoice.status) {
          let activity = new Activity();
          activity.action = Event_Actions.INVOICE_PARTIALLY_PAID;
          activity.actorId = user.id;
          activity.type = currentinvoice?.client ? ActivityType.BILLING : ActivityType.BILLING_CLIENT_GROUP;
          activity.typeId = currentinvoice?.client ? currentinvoice?.client?.id : currentinvoice?.clientGroup?.id;
          activity.remarks = `Invoice "${currentinvoice.invoiceNumber}" Status Changed to Paritally Paid by Payment Receipt "${receipt.receiptNumber}"`;
          await activity.save();
        } else if (currentinvoice.status === InvoiceStatus.PAID && oldStatus !== currentinvoice.status) {
          let activity = new Activity();
          activity.action = Event_Actions.INVOICE_PAID;
          activity.actorId = user.id;
          activity.type = currentinvoice?.client ? ActivityType.BILLING : ActivityType.BILLING_CLIENT_GROUP;
          activity.typeId = currentinvoice?.client ? currentinvoice?.client?.id : currentinvoice?.clientGroup?.id;
          activity.remarks = `Invoice "${currentinvoice.invoiceNumber}" Status Changed to Paid by Payment Receipt "${receipt.receiptNumber}"`;
          await activity.save();
        }
      });
    }
    //creceipt credit Update
    if (body.type === ReceiptType.INVOICE) {
      let receiptCredit = await ReceiptCredit.findOne({ receipt: receipt });
      receiptCredit.amount = +body.creditsUsed * 1;
      await receiptCredit.save();
    }
    if (receipt.type === ReceiptType.ADVANCE) {
      let receiptCredit = await ReceiptCredit.findOne({ receipt: receipt });
      receiptCredit.amount = +body.amount * 1;
      await receiptCredit.save();
    }

    //remove receipt particulrs
    for (let invoice of body.removedRps) {
      let currentinvoice = await Invoice.findOne({
        where: { id: invoice?.invoice?.id },
      });

      //Check Invoice Update in Real Time
      const realTimePgAmount = await createQueryBuilder(
        InvoiceOtherParticular,
        'invoiceOtherParticular',
      )
        .select('IFNULL(SUM(invoiceOtherParticular.amount),0)', 'pureAgentAmount')
        .where('invoiceOtherParticular.invoice_id=:invoice', { invoice: invoice?.invoice?.id })
        .getRawOne();

      const { pureAgentAmount } = realTimePgAmount;
      const realTimeServiceAmount = await createQueryBuilder(Invoice, 'invoice')
        .select('IFNULL((invoice.grand_total-invoice.total_charges),0)', 'serviceAmount')
        .where('invoice.id=:invoice', { invoice: invoice?.invoice?.id })
        .getRawOne();
      const { serviceAmount } = realTimeServiceAmount;
      //todo:
      // if ((pureAgentAmount * 1) !== invoice?.tPgAmount || (serviceAmount * 1) !== invoice?.fixedOverallServiceDue) {
      //   throw new ForbiddenException(
      //     'Invoice Updated In Real Time',
      //   );
      // }
      //Invoice Update
      if (invoice.overallPgDue * 1 + invoice.overallServiceDue === 0) {
        currentinvoice.status = InvoiceStatus.PAID;
      } else if (
        invoice.overallPgDue * 1 + invoice.overallServiceDue * 1 ===
        invoice?.invoice?.grandTotal * 1
      ) {
        currentinvoice.status = InvoiceStatus.APPROVAL_PENDING;
      } else {
        currentinvoice.status = InvoiceStatus.PARTIALLY_PAID;
      }
      currentinvoice['userId'] = user.id;
      await currentinvoice.save();
      let receiptParticular = await ReceiptParticular.findOne({ where: { id: invoice?.id } });
      receiptParticular.status = ReceiptParticularStatus.DELETED;
      await receiptParticular.save();
    }
    if (receipt.type === ReceiptType.ADVANCE) {
      let activity = new Activity();
      activity.action = Event_Actions.ADVANCE_RECEIPT_UPDATED;
      activity.actorId = user.id;
      activity.type = receipt?.client ? ActivityType.BILLING : ActivityType.BILLING_CLIENT_GROUP;
      activity.typeId = receipt?.client ? receipt?.client?.id : receipt?.clientGroup?.id;
      activity.remarks = `Advance Receipt "${receipt.receiptNumber}" Updated by ${user.fullName}`;
      await activity.save();
    } else {
      let activity = new Activity();
      activity.action = Event_Actions.PAYMENT_RECEIPT_UPDATED;
      activity.actorId = user.id;
      activity.type = receipt?.client ? ActivityType.BILLING : ActivityType.BILLING_CLIENT_GROUP;
      activity.typeId = receipt?.client ? receipt?.client?.id : receipt?.clientGroup?.id;
      activity.remarks = `Payment Receipt "${receipt.receiptNumber}" Updated by ${user.fullName}`;
      await activity.save();
    }
    return receipt;
  }

  async getAll(userId: number, query: GetReceiptsDto) {
    let user = await User.findOne({
      where: { id: userId },
      relations: ['organization', 'role'],
    });
    const ViewAll = user.role.permissions.some(
      (permission) => permission.slug === Permissions.VIEW_ALL_CLIENT_MANAGERS
    );
    const ViewAssigned = user.role.permissions.some(
      (permission) => permission.slug === Permissions.VIEW_ASSIGNED_CLIENT_MANAGERS
    );

    let receipts = createQueryBuilder(Receipt, 'receipt')
      .leftJoin('receipt.organization', 'organization')
      .leftJoin('receipt.client', 'client')
      .leftJoin('client.clientManagers', 'clientManagers')
      .leftJoin('receipt.clientGroup', 'clientGroup')
      .leftJoin('clientGroup.clientGroupManagers', 'clientGroupManagers')
      .leftJoin('receipt.billingEntity', 'billingEntity')
      .select([
        'receipt.id',
        'receipt.receiptNumber',
        'receipt.type',
        'receipt.receiptDate',
        'receipt.createdAt',
        'receipt.amount',
        'receipt.status',
        'receipt.paymentMode',
        'receipt.creditsUsed',
        'billingEntity.tradeName',
        'billingEntity.id',
        'client.displayName',
        'clientGroup.displayName',
        'clientGroup.type'
      ])
      .where('organization.id = :organizationId', {
        organizationId: user.organization.id,
      });

    if (query?.billingEntity?.length) {
      receipts.andWhere('billingEntity.id IN (:...billingEntity)', { billingEntity: query.billingEntity })
    }

    if (!ViewAll && ViewAssigned) {
      receipts.andWhere(
        new Brackets(qb => {
          qb.where('client.id IS NOT NULL AND clientManagers.id = :userId', { userId })
            .orWhere('clientGroup.id IS NOT NULL AND clientGroupManagers.id = :userId', { userId })
            .orWhere('client.id IS NULL AND clientGroup.id IS NULL');
        })
      );
    } else if (!ViewAll && !ViewAssigned) {
      receipts.andWhere('client.id IS NULL AND clientGroup.id IS NULL');
    }

    if (query.fromDate && query.toDate) {
      const { startTime, endTime } = dateFormation(query.fromDate, query.toDate);
      receipts.andWhere('receipt.receiptDate BETWEEN :startDate AND :endDate', {
        startDate: startTime,
        endDate: endTime,
      });
    }

    if (query.status && query.status !== '') {
      receipts.andWhere(
        '(receipt.status = :status)',
        {
          status: query.status,
        },
      );
    }

    if (query.search) {
      receipts = receipts.andWhere(
        '(receipt.receiptNumber LIKE :search OR client.displayName LIKE :search OR clientGroup.displayName LIKE :search)',
        {
          search: `%${query.search}%`,
        },
      );
    }
    const sort = (typeof query?.sort === "string") ? JSON.parse(query.sort) : query?.sort;
    if (sort?.column) {
      const columnMap: Record<string, string> = {
        receiptDate: 'receipt.receiptDate',
        amount: 'receipt.amount',
        tradeName: 'billingEntity.tradeName',
        type: 'receipt.type',
        displayName: 'client.displayName',
        paymentMode: 'receipt.paymentMode',
        status: 'receipt.status'

      };
      const column = columnMap[sort.column] || sort.column;
      if (sort.column === 'client.displayName') {
        receipts.orderBy('client.displayName', sort.direction.toUpperCase())
        receipts.addOrderBy('clientGroup.displayName', sort.direction.toUpperCase())
      } else {
        receipts.orderBy(column, sort.direction.toUpperCase());
      }

    } else {
      receipts.orderBy('receipt.createdAt', 'DESC');
    }

    // .orderBy('receipt.createdAt', 'DESC');



    receipts.skip(query.offset || 0).take(query.limit || 1000);

    let data = await receipts.getManyAndCount();

    return {
      result: data[0],
      total: data[1],
    };
  }

  async getClientPortalAll(clientId: number, query: any) {
    let receipts = createQueryBuilder(Receipt, 'receipt')
      .leftJoin('receipt.organization', 'organization')
      .leftJoin('receipt.client', 'client')
      .leftJoin('receipt.clientGroup', 'clientGroup')
      .leftJoin('receipt.billingEntity', 'billingEntity')
      .select([
        'receipt.id',
        'receipt.receiptNumber',
        'receipt.type',
        'receipt.receiptDate',
        'receipt.createdAt',
        'receipt.amount',
        'receipt.status',
        'receipt.paymentMode',
        'receipt.creditsUsed',
        'billingEntity.tradeName',
        'client.displayName',
      ])
      .where('client.id = :clientId', { clientId })
      .orderBy('receipt.createdAt', 'DESC');

    if (query.search) {
      receipts = receipts.andWhere(
        '(receipt.receiptNumber LIKE :search OR client.displayName LIKE :search)',
        {
          search: `%${query.search}%`,
        },
      );
    }

    receipts.skip(query.offset || 0).take(query.limit || 1000);

    let data = await receipts.getManyAndCount();

    return {
      result: data[0],
      total: data[1],
    };
  }

  async exportReceipts(userId: number, body: GetReceiptsDto) {
    const newQuery = { ...body, offset: 0, limit: 100000000 };
    let receipts = await this.getAll(userId, newQuery);



    if (!receipts?.result?.length) throw new BadRequestException('No Data for Export');

    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Receipts');
    const headers = [
      { header: 'S.No', key: 'serialNo' },
      { header: 'Receipt Type', key: 'type' },
      { header: 'Receipt #', key: 'receiptNumber' },
      { header: 'Receipt Date', key: 'receiptDate' },
      { header: 'Billing Entity', key: 'billingEntity' },
      { header: 'Client / Client Group', key: 'clientName' },
      { header: 'Amount Received (₹)', key: 'amount' },
      { header: 'Status', key: 'status' },

    ]
    worksheet.columns = headers;
    let serialCounter = 1; // Initialize a counter
    // Initialize array to store max lengths for each column
    const columnMaxLengths = Array(headers.length).fill(0);

    receipts?.result?.forEach((receipt) => {
      const rowData = {
        serialNo: serialCounter++,
        type: receipt.type,
        receiptNumber: receipt.receiptNumber,
        receiptDate: moment(receipt?.receiptDate).format('DD-MM-YYYY'),
        billingEntity: receipt?.billingEntity?.tradeName,
        clientName: receipt?.client ? receipt?.client?.displayName : receipt?.clientGroup?.displayName,
        amount: 1 * (receipt.amount),
        status: receipt?.status,
      }

      const row = worksheet.addRow(rowData);
      const statusCell = row.getCell('status');

      if (rowData.status) {
        // Apply font color based on status
        switch (rowData.status.toLowerCase()) {
          case 'created':
            statusCell.font = { color: { argb: '149ECD' }, bold: true };
            break;
          case 'invoiced':
            statusCell.font = { color: { argb: '149ECD' }, bold: true };
            break;
          case 'in progress':
            statusCell.font = { color: { argb: 'F49752' }, bold: true };
            break;
          case 'cancelled':
            statusCell.font = { color: { argb: 'F63338' }, bold: true };
            break;
          case 'overdue':
            statusCell.font = { color: { argb: 'F63338' }, bold: true };
            break;
          case 'partially paid':
            statusCell.font = { color: { argb: 'F49752' }, bold: true };
            break;
          case 'converted':
            statusCell.font = { color: { argb: '008000' }, bold: true };
            break;
          case 'paid':
            statusCell.font = { color: { argb: '008000' }, bold: true };
            break;
          default:
            statusCell.font = { color: { argb: '000000' }, bold: true }; // Default black for others
            break;
        }
      }

      worksheet.columns.forEach((column, colIndex) => {
        const headerLength = column.header?.length || 0; // Length of header
        const cellLength = rowData[column.key]?.toString().length || 0; // Length of cell value
        columnMaxLengths[colIndex] = Math.max(columnMaxLengths[colIndex] || 0, headerLength, cellLength);
      });
    });

    // Apply column widths with padding
    worksheet.columns.forEach((column, colIndex) => {
      column.width = columnMaxLengths[colIndex] + 3; // Add padding for readability
    });

    // Style the header row and center alignment
    const headerRow = worksheet.getRow(1);
    headerRow.font = { bold: true };
    headerRow.eachCell((cell) => {
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '64B5F6' },
      };
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' },
      };
      cell.alignment = { horizontal: 'center', vertical: 'middle' };
    });

    worksheet.columns.forEach((column) => {
      if (column.key === 'clientName' || column.key === 'billingEntity') {
        column.width = 50;
        column.alignment = { wrapText: true, horizontal: 'center', vertical: 'middle' };
      }
      else {
        column.alignment = { horizontal: 'center', vertical: 'middle' };
      }
    });

    // Apply wrapText for all rows
    worksheet.eachRow((row) => {
      row.eachCell((cell) => {
        cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true }; // Center alignment and wrap text
      });
    });

    // Freeze the header row
    worksheet.views = [{ state: 'frozen', ySplit: 1 }];

    const buffer = await workbook.xlsx.writeBuffer();
    return buffer;
  }

  async getCreditBalance(query: GetCreditBalanceDto) {
    const clientColumn = query.clientType === 'CLIENT_GROUP' ? 'client_group_id' : 'client_id';
    const receiptCredits = await getManager().query(`
      select 
      (
        sum(case when type = 'CREDIT' then amount else 0 end)
        -
        sum(case when type = 'DEBIT' then amount else 0 end)
      ) as balance
      from receipt_credit
      WHERE
      ${clientColumn} = ${query.clientId}
      AND
      billing_entity_id = ${query.billingEntityId}
      AND
      status = '${ReceiptCreditStatus.CREATED}'
    `);
    return receiptCredits[0]?.balance || 0;
  }


  async getNextReceiptNumber(userId: number, query: NextReceiptNumberDto) {
    let billingEntity = await BillingEntity.findOne({ where: { id: query.billingEntityId } });
    const prifixToMatch = billingEntity?.receiptPrefix;
    if (!prifixToMatch) {
      return null;
    }

    let receiptCount = await Receipt.count({
      where: {
        billingEntity: { id: query.billingEntityId },
        receiptNumber: Like(`${prifixToMatch}%`),
      },
    });
    const rec = parseInt(billingEntity.receiptPrefixNumber) + receiptCount;
    const zeroLength = billingEntity.receiptPrefixNumber.match(/^0*/)[0];
    return getnerateReceiptNumber(userId, rec, query.billingEntityId, zeroLength + '');

    // let user = await User.findOne({
    //   where: { id: userId },
    //   relations: ['organization'],
    // });

    // let recieptsCount = await Receipt.count({
    //   where: {
    //     organization: {
    //       id: user.organization.id,
    //     },
    //   },
    // });

    // return getnerateReceiptNumber(recieptsCount + 1);
  }

  async getReceiptPreview(receiptId: number, query: any) {
    // const user = await User.findOne({ where: { id: userId }, relations: ['organization'] });

    // let receipt = await Receipt.findOne({
    //   where: { id: receiptId, organization: user.organization.id },
    //   relations: ['receiptParticular',
    //     'client',
    //     'billingEntity',
    //     'receiptParticular.invoice',
    //     'receiptParticular.invoice.otherParticulars',
    //     'billingEntity.logStorage',
    //     'billingEntity.signatureStorage',
    //   ]
    // });
    const receiptSql = await getRepository(Receipt)
      .createQueryBuilder('receipt')
      .leftJoinAndSelect('receipt.receiptParticular', 'receiptParticular')
      .leftJoinAndSelect('receipt.client', 'client')
      .leftJoinAndSelect('receipt.clientGroup', 'clientGroup')
      .leftJoinAndSelect('receipt.billingEntity', 'billingEntity')
      .leftJoinAndSelect('receiptParticular.invoice', 'invoice')
      .leftJoinAndSelect('invoice.otherParticulars', 'otherParticulars')
      .leftJoinAndSelect('billingEntity.logStorage', 'logStorage')
      .leftJoinAndSelect('billingEntity.signatureStorage', 'signatureStorage')
      .where('receipt.id = :receiptId', { receiptId });
    receiptSql.andWhere(
      '(receiptParticular.status != :status OR receiptParticular.status IS NULL)',
      { status: ReceiptParticularStatus.DELETED },
    );
    if (query.orgId) {
      receiptSql.andWhere('receipt.organization = :organizationId', {
        organizationId: query.orgId,
      });
    }

    if (query.clientId) {
      receiptSql.andWhere('client.id = :clientId', { clientId: query.clientId });
    }

    const receipt = await receiptSql.getOne();


    if (receipt) {
      const invoiceIds: number[] = receipt?.receiptParticular.map(
        (eachReceiptParticular) => eachReceiptParticular.invoice.id,
      );
      const sql = `SELECT 
      receipt_particular.invoice_id, 
      IFNULL(sum(pure_agent_amount),0) as payedPgAmount,
       sum(service_amount) as payedSerAmount 
       FROM receipt_particular
       WHERE invoice_id IN (${[...invoiceIds]})
       AND status='${ReceiptParticularStatus.CREATED}'
       GROUP BY invoice_id
       `;
      let srvPgAmountSum: any[] = invoiceIds.length !== 0 ? await getManager().query(sql) : [];

      let details: GetCreditBalanceDto = {
        clientId: receipt?.client ? receipt?.client?.id : receipt?.clientGroup?.id,
        clientType: receipt?.client ? null : receipt?.clientGroup?.type,
        billingEntityId: receipt?.billingEntity?.id,
      };

      const avaliableCredits = await this.getCreditBalance(details);

      receipt.receiptParticular.map((item) => {
        const totalPgAmount = item?.invoice?.otherParticulars?.reduce(
          (accumulator, currentInvoice: any) => {
            return accumulator + currentInvoice.amount * 1;
          },
          0,
        );
        return (
          // (item['overallPgDue'] =
          //   totalPgAmount -
          //   srvPgAmountSum.find((row) => row.invoice_id === item.invoice.id)?.['payedPgAmount'] *
          //   1 || 0),
          (item['overallPgDue'] =
            Math.round(
              totalPgAmount * 100 -
              ((srvPgAmountSum.find((row) => row.invoice_id === item.invoice.id)?.['payedPgAmount'] || 0) * 100)
            ) / 100
          ),
          // (item['fixedOverallPgDue'] =
          //   totalPgAmount +
          //   item?.pureAgentAmount * 1 -
          //   srvPgAmountSum.find((row) => row.invoice_id === item.invoice.id)?.['payedPgAmount'] *
          //   1 || 0),
          (item['fixedOverallPgDue'] =
            Math.round(
              totalPgAmount * 100 +
              (item?.pureAgentAmount * 100) -
              ((srvPgAmountSum.find((row) => row.invoice_id === item.invoice.id)?.['payedPgAmount'] || 0) * 100)
            ) / 100),
          (item['tPgAmount'] = totalPgAmount),
          (item['overallServiceDue'] =
            item?.invoice?.grandTotal * 1 -
            +(item?.invoice?.totalCharges * 1) -
            srvPgAmountSum.find((row) => row.invoice_id === item.invoice.id)?.['payedSerAmount'] *
            1 || 0),
          (item['overallServicePayed'] = srvPgAmountSum.find((row) => row.invoice_id === item.invoice.id)?.['payedSerAmount'] * 1 || 0),
          // (item['fixedOverallServiceDue'] =
          //   item?.invoice?.grandTotal -
          //   +item?.invoice?.totalCharges +
          //   +(item?.serviceAmount * 1) -
          //   srvPgAmountSum.find((row) => row.invoice_id === item.invoice.id)?.['payedSerAmount'] *
          //   1 || 0)
          (item['fixedOverallServiceDue'] =
            Math.round(
              (item?.invoice?.grandTotal * 100 -
                item?.invoice?.totalCharges * 100 +
                (item?.serviceAmount * 100) -
                ((srvPgAmountSum.find((row) => row.invoice_id === item.invoice.id)?.['payedSerAmount'] || 0) * 100))
            ) / 100// Convert back to decimals
          )
        );
      });

      receipt['avaliableCredits'] = avaliableCredits;
    }
    return receipt || 'Un-Authorized';
  }

  async downloadReceipt(invoiceId: number) {
    let url = `${process.env.WEBSITE_URL}/billing/receipts/${invoiceId}/preview?fromApi=true`;
    // let url = `http://localhost:3000/billing/invoices/${invoiceId}/preview?fromApi=true`;
    try {
      const browser = await puppeteer.launch({
        headless: true,
        args: ['--no-sandbox', '--disable-setuid-sandbox'],
         executablePath: '/usr/bin/chromium-browser',
      }); const page = await browser.newPage();
      await page.setCacheEnabled(false);
      await page.setUserAgent(
        'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/78.0.3904.108 Safari/537.36',
      );
      const maxRetries = 3;
      let retries = 0;
      let loaded = false;

      // await page.goto(url, { waitUntil: 'networkidle2' });
      while (retries < maxRetries && !loaded) {
        try {
          await page.goto(url, { waitUntil: 'networkidle2', timeout: 60000 }); // 60 seconds timeout

          // Check for a specific element that indicates the page has fully loaded
          await page.waitForFunction(
            'document.querySelector("body").innerText.includes("Powered by")',
            { timeout: 60000 } // Adjust timeout as needed
          );
          await page.waitForFunction(
            'Array.from(document.images).every(img => img.complete && img.naturalHeight !== 0)',
            { timeout: 60000 } // Adjust timeout as needed
          );

          loaded = true;
        } catch (error) {
          retries += 1;
          console.log(`Retrying to load the page (${retries}/${maxRetries})`);
          if (retries >= maxRetries) {
            throw error;
          }
        }
      }
      await page.addStyleTag({ content: '#freshworks-container { display: none; }' });
      await page.addStyleTag({ content: '.hide { display: none }' });

      // await page.addStyleTag({ content: '@media print { .myDiv { break-inside: avoid; } }' });

      // await page.addStyleTag({
      //   content: '@page { size: auto; }',
      // });

      // await page.emulateMediaType('screen');
      await page.emulateMediaType('print');

      const pdf = await page.pdf({
        format: 'A4',
        printBackground: true,
        margin: {
          top: '0px',
          right: '0px',
          bottom: '0px',
          left: '0px',
        },
        scale: 0.8,
      });
      await browser.close();

      return Buffer.from(pdf);;
    } catch (e) {
      console.log(e);
      throw new InternalServerErrorException(e);
    }
  }

  async cancelReceipt(receiptId: number, userId) {
    let user = await User.findOne({
      where: { id: userId },
      relations: ['organization'],
    });

    let receipt = await Receipt.findOne({
      where: { id: receiptId },
      relations: ['client', 'clientGroup', 'billingEntity', 'organization'],
    });
    receipt.status = ReceiptStatus.CANCELLED;
    receipt['userId'] = userId;
    await receipt.save();
    if (receipt.type === ReceiptType.INVOICE) {
      let receiptParticulr = await ReceiptParticular.find({
        where: { receipt },
        relations: ['invoice'],
      });
      for (let item of receiptParticulr) {
        let invoice = await Invoice.findOne({ where: { id: item?.invoice.id }, relations: ['client', 'clientGroup'] });
        const oldStatus = invoice.status;
        const realTimePgAmount = await createQueryBuilder(
          InvoiceOtherParticular,
          'invoiceOtherParticular',
        )
          .select('IFNULL(SUM(invoiceOtherParticular.amount),0)', 'pureAgentAmount')
          .where('invoiceOtherParticular.invoice_id=:invoice', { invoice: item?.invoice?.id })
          .getRawOne();

        const { pureAgentAmount } = realTimePgAmount;
        const realTimeServiceAmount = await createQueryBuilder(Invoice, 'invoice')
          .select('IFNULL((invoice.grand_total-invoice.total_charges),0)', 'serviceAmount')
          .where('invoice.id=:invoice', { invoice: item?.invoice?.id })
          .getRawOne();
        const { serviceAmount } = realTimeServiceAmount;
        const paidSericeAndPgAmountSql = `SELECT IFNULL(sum(pure_agent_amount),0) AS pgpaid,
        IFNULL(sum(service_amount),0) AS servicepaid
        FROM receipt_particular where invoice_id=${item?.invoice?.id} And receipt_id!=${receiptId} and status='${ReceiptParticularStatus.CREATED}';`;
        const paidSericeAndPgAmount = await getManager().query(paidSericeAndPgAmountSql);
        const [{ pgpaid, servicepaid }] = paidSericeAndPgAmount;
        if (pgpaid * 1 == pureAgentAmount * 1 && servicepaid * 1 == serviceAmount * 1) {
          invoice.status = InvoiceStatus.PAID;
        } else if (pgpaid * 1 > 0 || servicepaid * 1 > 0) {
          invoice.status = InvoiceStatus.PARTIALLY_PAID;
        } else if (pgpaid * 1 == 0 && servicepaid * 1 == 0) {
          invoice.status = InvoiceStatus.APPROVAL_PENDING;
        }

        // if ((item?.serviceAmount * 1) + (item?.pureAgentAmount * 1) === 0) {
        // } else if (((item?.serviceAmount * 1) == ((item?.invoice?.grandTotal * 1) - (item?.invoice?.totalCharges * 1)) && (item?.pureAgentAmount * 1 == item?.invoice?.totalCharges * 1))) {
        //   invoice.status = InvoiceStatus.APPROVAL_PENDING
        // } else {
        //   invoice.status = InvoiceStatus.PARTIALLY_PAID
        // }
        invoice['userId'] = user.id;
        await invoice.save();
        if (invoice.status === InvoiceStatus.PARTIALLY_PAID) {
          let activity = new Activity();
          activity.action = Event_Actions.INVOICE_PARTIALLY_PAID;
          activity.actorId = user?.id;
          activity.type = invoice?.client ? ActivityType.BILLING : ActivityType.BILLING_CLIENT_GROUP;
          activity.typeId = invoice?.client ? invoice?.client?.id : invoice?.clientGroup?.id;
          activity.remarks = `Invoice "${invoice.invoiceNumber}" Status Changed to Paritally Paid by Payment Receipt "${receipt.receiptNumber}"`;
          await activity.save();
        } else if (invoice.status === InvoiceStatus.PAID) {
          let activity = new Activity();
          activity.action = Event_Actions.INVOICE_PAID;
          activity.actorId = user?.id;
          activity.type = invoice?.client ? ActivityType.BILLING : ActivityType.BILLING_CLIENT_GROUP;
          activity.typeId = invoice?.client ? invoice?.client?.id : invoice?.clientGroup?.id;
          activity.remarks = `Invoice "${invoice.invoiceNumber}" Status Changed to Paid by Payment Receipt "${receipt.receiptNumber}"`;
          await activity.save();
        }
      }
      let rpSql = `UPDATE receipt_particular
      SET status='${ReceiptParticularStatus.CANCELLED}'
      WHERE receipt_id=${receiptId};`;
      const updateRp = await getManager().query(rpSql);
      let rcSql = `UPDATE receipt_credit
      SET status='${ReceiptCreditStatus.CANCELLED}'
      WHERE receipt_id=${receiptId};`;

      const updateRc = await getManager().query(rcSql);
    }
    if (receipt.type === ReceiptType.ADVANCE) {
      let setCreditSql = `SELECT amount as cancelCredits
      FROM 
      receipt_credit 
      WHERE 
      receipt_id=${receiptId};
      `;
      const getCancelCredits = await getManager().query(setCreditSql);
      const [{ cancelCredits }] = getCancelCredits;
      let details: GetCreditBalanceDto = {
        clientId: receipt?.client ? receipt?.client?.id : receipt?.clientGroup?.id,
        clientType: receipt?.client ? null : receipt?.clientGroup?.type,
        billingEntityId: receipt?.billingEntity?.id,
      };
      const avaliableCredits = await this.getCreditBalance(details);
      if (avaliableCredits * 1 >= cancelCredits * 1) {
        const updateRecepitCredtisSql = `UPDATE 
        receipt_credit
        SET status='${ReceiptCreditStatus.CANCELLED}'
        WHERE receipt_id=${receiptId};`;
        const updateReceiptCredit = await getManager().query(updateRecepitCredtisSql);
      } else {
        throw new ForbiddenException('No Amount In Credits');
      }
    }
    if (receipt.type === ReceiptType.ADVANCE) {
      let activity = new Activity();
      activity.action = Event_Actions.ADVANCE_RECEIPT_CANCELLED;
      activity.actorId = user.id;
      activity.type = receipt?.client ? ActivityType.BILLING : ActivityType.BILLING_CLIENT_GROUP;
      activity.typeId = receipt?.client ? receipt?.client?.id : receipt?.clientGroup?.id;
      activity.remarks = `Advance Receipt "${receipt.receiptNumber}" Cancelled by ${user.fullName}`;
      await activity.save();
    } else if (receipt.type === ReceiptType.INVOICE) {
      let activity = new Activity();
      activity.action = Event_Actions.PAYMENT_RECEIPT_CANCELLED;
      activity.actorId = user.id;
      activity.type = receipt?.client ? ActivityType.BILLING : ActivityType.BILLING_CLIENT_GROUP;
      activity.typeId = receipt?.client ? receipt?.client?.id : receipt?.clientGroup?.id;
      activity.remarks = `Payment Receipt "${receipt.receiptNumber}" Cancelled by ${user.fullName}`;
      await activity.save();
    }
  }
}
