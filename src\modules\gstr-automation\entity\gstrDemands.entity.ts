import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  BaseEntity,
} from 'typeorm';
import GstrCredentials from './gstrCredentials.entity';

export enum PortalStatus {
  YES = 'YES',
  NO = 'NO',
}

@Entity('gstr_outstanding_demands')
export class GstrOutstandingDemand extends BaseEntity{
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  clientId: number;

  @Column()
  organizationId: number;

  @Column()
  gstIn: string;

  @Column()
  demandDt: string;

  @Column()
  demandId: string;

  @Column()
  entityNum: string;

  @Column()
  orderNo: string;

  @Column()
  orderPassedId: string;

  @Column()
  rcvryId: string;

  @Column()
  updateId: string;

  @Column()
  penaltyWaiver: string;

  @Column({ name: 'igst_tot', type: 'decimal', precision: 15, scale: 2, nullable: true })
  igstTot: number;

  @Column({ name: 'cgst_tot', type: 'decimal', precision: 15, scale: 2, nullable: true })
  cgstTot: number;

  @Column({ name: 'sgst_tot', type: 'decimal', precision: 15, scale: 2, nullable: true })
  sgstTot: number;

  @Column({ name: 'cess_tot', type: 'decimal', precision: 15, scale: 2, nullable: true })
  cessTot: number;

  @Column({ name: 'total_tot', type: 'decimal', precision: 15, scale: 2, nullable: true })
  totalTot: number;

  @Column({ name: 'igst_tx', type: 'decimal', precision: 15, scale: 2, nullable: true })
  igstTx: number;

  @Column({ name: 'igst_fee', type: 'decimal', precision: 15, scale: 2, nullable: true })
  igstFee: number;

  @Column({ name: 'igst_pen', type: 'decimal', precision: 15, scale: 2, nullable: true })
  igstPen: number;

  @Column({ name: 'igst_intr', type: 'decimal', precision: 15, scale: 2, nullable: true })
  igstIntr: number;

  @Column({ name: 'igst_oth', type: 'decimal', precision: 15, scale: 2, nullable: true })
  igstOth: number;

  @Column({ name: 'cgst_tx', type: 'decimal', precision: 15, scale: 2, nullable: true })
  cgstTx: number;

  @Column({ name: 'cgst_fee', type: 'decimal', precision: 15, scale: 2, nullable: true })
  cgstFee: number;

  @Column({ name: 'cgst_pen', type: 'decimal', precision: 15, scale: 2, nullable: true })
  cgstPen: number;

  @Column({ name: 'cgst_intr', type: 'decimal', precision: 15, scale: 2, nullable: true })
  cgstIntr: number;

  @Column({ name: 'cgst_oth', type: 'decimal', precision: 15, scale: 2, nullable: true })
  cgstOth: number;

  @Column({ name: 'sgst_tx', type: 'decimal', precision: 15, scale: 2, nullable: true })
  sgstTx: number;

  @Column({ name: 'sgst_fee', type: 'decimal', precision: 15, scale: 2, nullable: true })
  sgstFee: number;

  @Column({ name: 'sgst_pen', type: 'decimal', precision: 15, scale: 2, nullable: true })
  sgstPen: number;

  @Column({ name: 'sgst_intr', type: 'decimal', precision: 15, scale: 2, nullable: true })
  sgstIntr: number;

  @Column({ name: 'sgst_oth', type: 'decimal', precision: 15, scale: 2, nullable: true })
  sgstOth: number;

  @Column({ name: 'cess_tx', type: 'decimal', precision: 15, scale: 2, nullable: true })
  cessTx: number;

  @Column({ name: 'cess_fee', type: 'decimal', precision: 15, scale: 2, nullable: true })
  cessFee: number;

  @Column({ name: 'cess_pen', type: 'decimal', precision: 15, scale: 2, nullable: true })
  cessPen: number;

  @Column({ name: 'cess_intr', type: 'decimal', precision: 15, scale: 2, nullable: true })
  cessIntr: number;

  @Column({ name: 'cess_oth', type: 'decimal', precision: 15, scale: 2, nullable: true })
  cessOth: number;

  @Column({ name: 'total_tx', type: 'decimal', precision: 15, scale: 2, nullable: true })
  totalTx: number;

  @Column({ name: 'total_fee', type: 'decimal', precision: 15, scale: 2, nullable: true })
  totalFee: number;

  @Column({ name: 'total_pen', type: 'decimal', precision: 15, scale: 2, nullable: true })
  totalPen: number;

  @Column({ name: 'total_intr', type: 'decimal', precision: 15, scale: 2, nullable: true })
  totalIntr: number;

  @Column({ name: 'total_oth', type: 'decimal', precision: 15, scale: 2, nullable: true })
  totalOth: number;

  @ManyToOne(()=> GstrCredentials, (gstrCredential)=> gstrCredential.gstrOutstandingDemand)
  gstrCredentials: GstrCredentials;

  @Column({type:"enum",enum:PortalStatus,nullable:true})
  isInPortal: PortalStatus | null;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
