import { User } from 'src/modules/users/entities/user.entity';
import {
  BaseEntity,
  Column,
  CreateDateColumn,
  Entity,
  Generated,
  JoinTable,
  ManyToMany,
  OneToMany,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import ChatMessage from './chat-message.entity';
import Storage from '../storage/storage.entity';

export enum RoomType {
  INDIVIDUAL = 'INDIVIDUAL',
  GROUP = 'GROUP',
}

@Entity()
class ChatRoom extends BaseEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ type: 'enum', enum: RoomType })
  type: RoomType;

  @Column({ nullable: true })
  name: string;

  @Generated('uuid')
  @Column()
  roomId: string;

  @Column({ nullable: true })
  taskId: number;

  @ManyToMany(() => User, { onDelete: 'CASCADE' })
  @JoinTable({ name: 'chat_room_members' })
  members: User[];

  @OneToMany(() => ChatMessage, (message) => message.room)
  messages: ChatMessage[];

  @OneToMany(() => Storage, (storage) => storage.room)
  storage: Storage[];

  @CreateDateColumn()
  createdAt: string;

  @UpdateDateColumn()
  updatedAt: string;

  lastMessage: string = '';

  unread: number = 0;
}

export default ChatRoom;
