
import { BaseEntity, Column, CreateDateColumn, Entity, PrimaryColumn, PrimaryGeneratedColumn, UpdateDateColumn } from "typeorm";

@Entity()
class ReminderEmailLog extends BaseEntity {
    @PrimaryGeneratedColumn()
    id: number;

    @Column()
    userId: number;

    @Column()
    organizationId: number;

    @Column()
    email: string;

  @Column('json')
 clientDetails : object;
 
    @Column({ type: 'timestamp', nullable: true })
    createdAt: Date;

    @UpdateDateColumn()
    updatedAt : Date;

    @Column()
    reminderType: string;

    
}

export default ReminderEmailLog;