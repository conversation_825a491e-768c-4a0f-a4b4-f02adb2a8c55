import {
  <PERSON><PERSON>ntity,
  <PERSON>umn,
  CreateDateColumn,
  Entity,
  JoinColumn,
  OneToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { User } from '../users/entities/user.entity';
import Client from '../clients/entity/client.entity';
import Task from '../tasks/entity/task.entity';
import ClientGroup from '../client-group/client-group.entity';

@Entity()
class CollectData extends BaseEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  name: string;

  @Column()
  appName: string;

  @OneToOne(() => User, (user) => user.collectData)
  @JoinColumn()
  user: User;

  @OneToOne(() => Client, (client) => client.collectData)
  @JoinColumn()
  client: Client;

  @OneToOne(() => ClientGroup, (clientGroup) => clientGroup.collectData)
  @JoinColumn()
  clientGroup: ClientGroup;

  @OneToOne(() => Task, (task) => task.collectData)
  @JoinColumn()
  task: Task;

  @Column({ type: 'text', nullable: true })
  notes: string;

  @Column({ type: 'text', nullable: true })
  comments: string;

  @Column('json')
  listOfFiles: object;

  @Column('json')
  listOfConfirmedFiles: object;

  @Column({ default: true })
  active: boolean;

  @Column()
  uid: string;

  @CreateDateColumn()
  createdAt: string;

  @UpdateDateColumn()
  updatedAt: string;

  @Column()
  expiryUpdateDate: string;

  @Column()
  whatsappCheck: boolean;

  @Column()
  emailCheck: boolean;
}

export default CollectData;
