import { IsDateString, IsOptional } from 'class-validator';

export class getLeadsReportDto {
  @IsOptional()
  category: string;

  @IsOptional()
  subCategory: string;

  @IsOptional()
  @IsDateString()
  createdAt: string;

  @IsOptional()
  @IsDateString()
  updatedAt: string;

  @IsOptional()
  status: string;

  @IsOptional()
  search: string;

  @IsOptional()
  email: string;

  @IsOptional()
  mobile: Number;
}
