import {
  Connection,
  EntitySubscriberInterface,
  EventSubscriber,
  InsertEvent,
  getManager,
} from 'typeorm';

import Event from 'src/modules/events/event.entity';
import {
  getAdminIDsBasedOnOrganizationId,
  getUserDetails,
  getUserIDs,
  insertINTONotificationUpdate,
} from 'src/utils/re-use';
import { User } from 'src/modules/users/entities/user.entity';
import ViderWhatsappSessions from 'src/modules/whatsapp/entity/viderWhatsappSessions';
import { sendWhatsAppTextMessage } from 'src/modules/whatsapp/whatsapp.service';
import * as moment from 'moment';

@EventSubscriber()
export class GeneralAndTaskEventSubscriber implements EntitySubscriberInterface<Event> {

  listenTo() {
    return Event;
  }

  entityManager = getManager();
  async afterInsert(event: InsertEvent<Event>) {}
}
