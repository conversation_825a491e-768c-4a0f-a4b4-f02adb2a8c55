import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Patch,
  Post,
  Put,
  Query,
  Render,
  Req,
  Request,
  Res,
  UseGuards,
} from '@nestjs/common';
import { Response } from 'express';
import { JwtAuthGuard } from 'src/modules/users/jwt/jwt-auth.guard';
import { AddFieldDto } from '../dto/add-field.dto';
import { AddPageDto } from '../dto/add-page.dto';
import CloneFormDto from '../dto/clone-form.dto';
import CreateFormDto, { UpdateFormDto } from '../dto/create-form.dto';
import { EsignDto } from '../dto/esign.dto';
import { FindFormsDto } from '../dto/find.dto';
import ImportFormsDto from '../dto/import-forms.dto';
import { UpdateFieldDto } from '../dto/update-field.dto';
import { UpdatePageDto } from '../dto/update-page.dto';
import { FormsService } from '../services/forms.service';
import axios from 'axios';
import { writeFile } from 'fs';

@Controller('forms')
export class FormsController {
  constructor(private service: FormsService) {}

  @UseGuards(JwtAuthGuard)
  @Post()
  async createForm(@Request() req, @Body() body: CreateFormDto) {
    const { userId } = req.user;
    return this.service.createForm(userId, body);
  }

  @UseGuards(JwtAuthGuard)
  @Put(':id')
  async updateForm(@Request() req, @Body() body: UpdateFormDto, @Param('id') id: string) {
    const { userId } = req.user;
    return this.service.updateForm(userId, id, body);
  }

  @UseGuards(JwtAuthGuard)
  @Post(':id/clone')
  async cloneForm(@Request() req, @Param('id') id: string, @Body() body: CloneFormDto) {
    const { userId } = req.user;
    return this.service.cloneForm(userId, id, body);
  }

  @UseGuards(JwtAuthGuard)
  @Get()
  async getForms(@Request() req, @Query() query: FindFormsDto) {
    const { userId } = req.user;
    return this.service.getAllForms(userId, query);
  }

  @Get('/default')
  async getDefaultForms() {
    return this.service.getDefaultForms();
  }

  @UseGuards(JwtAuthGuard)
  @Post('/import')
  async importForms(@Req() req, @Body() body: ImportFormsDto) {
    const { userId } = req.user;
    return this.service.importForms(userId, body);
  }

  @UseGuards(JwtAuthGuard)
  @Get(':formId')
  async getForm(@Param('formId') formId: string) {
    return this.service.getForm(formId);
  }

  @UseGuards(JwtAuthGuard)
  @Delete(':formId')
  async deleteForm(@Req() req, @Param('formId') formId: string) {
    const { userId } = req.user;
    return this.service.deleteForm(userId, formId);
  }

  @UseGuards(JwtAuthGuard)
  @Post(`/:formId/pages`)
  async addPage(@Request() req, @Body() body: AddPageDto, @Param('formId') formId: string) {
    const { userId } = req.user;
    return this.service.addPage(userId, formId, body);
  }

  @UseGuards(JwtAuthGuard)
  @Post(`/:formId/pages/:pageId/clone`)
  async clonePage(
    @Request() req,
    @Param('formId') formId: string,
    @Param('pageId') pageId: string,
  ) {
    const { userId } = req.user;
    return this.service.clonePage(userId, formId, pageId);
  }

  @UseGuards(JwtAuthGuard)
  @Patch(`/:formId/pages/:pageId`)
  async updatePage(
    @Request() req,
    @Body() body: UpdatePageDto,
    @Param('formId') formId: string,
    @Param('pageId') pageId: string,
  ) {
    const { userId } = req.user;
    return this.service.updatePage(userId, formId, pageId, body);
  }

  @UseGuards(JwtAuthGuard)
  @Delete(`/:formId/pages/:pageId`)
  async deletePage(@Param('formId') formId: string, @Param('pageId') pageId: string) {
    return this.service.deletePage(formId, pageId);
  }

  @UseGuards(JwtAuthGuard)
  @Post(':formId/pages/:pageId/fields')
  async addField(
    @Request() req,
    @Param('formId') formId: string,
    @Param('pageId') pageId: string,
    @Body() body: AddFieldDto,
  ) {
    const { userId } = req.user;
    return this.service.addField(userId, formId, pageId, body);
  }

  @UseGuards(JwtAuthGuard)
  @Patch(':formId/pages/:pageId/fields/:fieldId')
  async updateField(
    @Param('formId') formId: string,
    @Param('pageId') pageId: string,
    @Param('fieldId') fieldId: string,
    @Body() body: UpdateFieldDto,
  ) {
    return this.service.updateField(formId, pageId, fieldId, body);
  }

  @UseGuards(JwtAuthGuard)
  @Delete(':formId/pages/:pageId/fields/:fieldId')
  async deleteField(
    @Param('formId') formId: string,
    @Param('pageId') pageId: string,
    @Param('fieldId') fieldId: string,
  ) {
    return this.service.deleteField(formId, pageId, fieldId);
  }

  @UseGuards(JwtAuthGuard)
  @Get(':id/activity')
  async getActivity(@Request() req) {
    return this.service.getActivity(req.params.id);
  }

  @UseGuards(JwtAuthGuard)
  @Post(':formId/fields/:fieldId/esign')
  async esignDocument(
    @Request() req,
    @Param('formId') formId: string,
    @Param('fieldId') fieldId: string,
    @Body() body: EsignDto,
  ) {
    const { userId } = req.user;
    return this.service.esignDocument(userId, formId, fieldId, body);
  }

  @Post('/esign/success')
  async esignSuccess(@Res() res: Response, @Body() body) {
    return this.service.esignSuccess(res, body);
  }

  @Post('/esign/failure')
  @Render('esign-failure')
  async esignFailure() {
    return {
      message: 'hello world',
    };
  }

  @Post('/esign/cancel')
  @Render('esign-cancel')
  async esignCancel() {
    return {
      message: 'hello world',
    };
  }
}
