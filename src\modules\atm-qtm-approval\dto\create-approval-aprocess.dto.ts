// import { <PERSON>NotEmpty, <PERSON><PERSON><PERSON><PERSON>, ValidateNested } from "class-validator";



// class Level {
//     @IsNotEmpty({ message: 'Role is required' })
//     roleId: number;

//     @IsNotEmpty({ message: 'User is required' })
//     userId: number;

//     @IsNotEmpty({ message: 'Level is required' })
//     level: number;
// }


// class CreateApprovalProcessDtoe {
//     @IsNotEmpty({ message: 'Name is required' })
//     name: string;

//     @IsNotEmpty({ message: 'Description is required' })
//     description: string;

//     @IsNotEmpty()
//     @ValidateNested()
//     approvalLevels: Level[];
// }

// export default CreateApprovalProcessDtoe


import { IsNotEmpty, IsOptional, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';

export class Level {
    @IsNotEmpty({ message: 'Role is required' })
    roleId: number;

    @IsNotEmpty({ message: 'User is required' })
    userId: number;

    @IsNotEmpty({ message: 'Level is required' })
    level: number;
}

class CreateApprovalProcessDto {
    @IsNotEmpty({ message: 'Name is required' })
    name: string;

    @IsOptional()
    description: string;

    @IsNotEmpty({ message: 'Approval levels are required' })
    @ValidateNested({ each: true }) // Use `each: true` to validate each element in the array
    @Type(() => Level) // Use the `@Type()` decorator to specify the nested class
    approvalLevels: Level[];
}

export default CreateApprovalProcessDto;
