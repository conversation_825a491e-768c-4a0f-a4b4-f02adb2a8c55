import { Injectable } from '@nestjs/common';
import NotificationPreferences from './notifications-preferences.entity';
import UpdatePreferencesDto from './dto/preferences.dto';
import { Notification_Actions } from './action';
import { createQueryBuilder } from 'typeorm';
import { User, UserType } from '../users/entities/user.entity';
import { Organization } from '../organization/entities/organization.entity';
import OrganizationPreferences from '../organization-preferences/entity/organization-preferences.entity';
import { Cron, CronExpression } from '@nestjs/schedule';
import { checkUserTosendMorningMsg } from 'src/utils/re-use';



@Injectable()
export class NotificationPreferencesService {
  async get(userId: number) {
    const userPreferences = await NotificationPreferences.findOne({
      where: { user: userId },
    });
    return userPreferences;
  }

  async update(userId: number, data: UpdatePreferencesDto) {
    const { pushPermissions } = data;
    const list = pushPermissions.map((Pushitem: string) => {
      const push = Notification_Actions.Push_Notification;
      const mail = Notification_Actions.Mail_Notification;
      const allNotifications = { ...push, ...mail };
      const filter = allNotifications[Pushitem];
      const i = Pushitem;
      return { [i]: filter };
    });

    const resultObject = list.reduce((acc, obj) => {
      const key = Object.keys(obj)[0];
      const value = obj[key];
      acc[key] = value;
      return acc;
    }, {});

    const stringfyPush = JSON.stringify(resultObject);
    let existingUser = await NotificationPreferences.findOne({ where: { user: userId } });
    existingUser.push = stringfyPush;
    await existingUser.save();
  }

  async getUserPreferences(userId: number,authUserId:number) {
    const authUser = await User.findOne({ where: { id: authUserId }, relations: ['organization'] });

    const userPreferencesOrg = await NotificationPreferences.findOne({
      where: { user: userId },
      relations: ['user'],
    });
    const user = await User.findOne({ where: { id: userId,organization:authUser?.organization?.id }, relations: ['profile', 'role'] });
    if(user){
      if (userPreferencesOrg === undefined) {
        return {
          user,
          push: {},
          email: {},
          whatsapp: {},
          userPresent: 'No',
        };
      } else {
        const notificationPreferences: any = await createQueryBuilder(
          NotificationPreferences,
          'notificationPreferences',
        )
          .leftJoinAndSelect('notificationPreferences.user', 'user')
          .leftJoinAndSelect('user.organization', 'organization')
          .leftJoinAndSelect('user.profile', 'profile')
          .leftJoinAndSelect('user.role', 'role')
          .where('notificationPreferences.user = :userId', { userId: userId })
          .getOne();
        const notificationTypes: any = notificationPreferences?.push;
  
        const mailNotifications = {};
        const pushNotifications = {};
  
        // Iterate over the keys
        for (const key in notificationTypes) {
          if (Object.prototype.hasOwnProperty.call(notificationTypes, key)) {
            if (key.endsWith('_MAIL')) {
              mailNotifications[key] = notificationTypes[key];
            } else if (key.endsWith('_PUSH')) {
              pushNotifications[key] = notificationTypes[key];
            }
          }
        }
        notificationPreferences.push = pushNotifications;
        notificationPreferences.email = {
          ...notificationPreferences.email,
          ...mailNotifications,
        };
        return notificationPreferences;
      }
    }else{
      return user
    }
   
  }

  async updatePreferences(userId, body) {
    const preferences = body?.notificationPreferences;
    let user = await User.findOne({
      where: { id: preferences.user?.id },
      relations: ['organization', 'profile', 'role'],
    });
    if (preferences?.id === undefined) {
      const notificationPreferences = new NotificationPreferences();
      notificationPreferences.user = user;
      notificationPreferences.organization_id = user?.organization?.id;
      notificationPreferences.email = JSON.stringify(preferences?.email);
      notificationPreferences.push = JSON.stringify(preferences?.push);
      notificationPreferences.whatsapp = JSON.stringify(preferences?.whatsapp);
      notificationPreferences.save();
    } else {
      const notificationPreferences = await NotificationPreferences.findOne({
        where: { id: preferences?.id },
      });
      notificationPreferences.organization_id = user?.organization?.id;
      notificationPreferences.email = JSON.stringify(preferences?.email);
      notificationPreferences.push = JSON.stringify(preferences?.push);
      notificationPreferences.whatsapp = JSON.stringify(preferences?.whatsapp);
      notificationPreferences.save();
    }
  }
  async updateWhatsappConfig(userId, body) {
    let userPreferences = await NotificationPreferences.findOne({ where: { user: body?.id } });
    if (userPreferences) {
      userPreferences.whatsappConfig = body?.userWhatsappPreferences;
      userPreferences.save();
      return userPreferences;
    }else{
      const user= await User.findOne({where:{id:body.id}, relations:['organization']})
      const notification= new NotificationPreferences()
      notification.user=user
      notification.organization_id=user.organization?.id 
      notification.whatsappConfig = body?.userWhatsappPreferences;
      notification.save()
      return notification


    }
  }

  // @Cron(CronExpression.EVERY_MINUTE)
  async checkUserConfigBasedOnOrganization() {
    const orgIds = [491];
    for (let orgId of orgIds) {
      const userDetails = await User.find({ where: { organization: orgId, type: UserType.ORGANIZATION } });
      if (userDetails) {
        for (let user of userDetails) {
          const checkWhatsAppPreference = await NotificationPreferences.findOne({
            where: { user: user?.id },
          });
          if (checkWhatsAppPreference) {

            if (checkWhatsAppPreference.whatsappConfig) {
            }
          }
        }
      }
    }
  }

  // @Cron(CronExpression.EVERY_MINUTE)
  async checkAllOrganizations() {
    const allOrganizations = await Organization.find();
    for (const organization of allOrganizations) {
      const organizationPreferences = await createQueryBuilder(OrganizationPreferences, 'orgPrefs')
        .where('orgPrefs.organization = :organizationId', { organizationId: organization?.id })
        .andWhere('JSON_EXTRACT(orgPrefs.notificationConfig, "$.whatsappPreferences") IS NOT NULL')
        .getOne();
      if (organizationPreferences) {
        const userDetails = await User.find({ where: { organization: organization?.id, type: UserType.ORGANIZATION } });
        for (const user of userDetails) {
          const checkWhatsAppPreference = await NotificationPreferences.findOne({
            where: { user: user?.id, whatsappConfig: true },
          });

          if (checkWhatsAppPreference) {
          }
        }
      }
    }
  }
}
