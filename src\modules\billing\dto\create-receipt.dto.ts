import { IsArray, IsEnum, IsNotEmpty, IsNumber, IsNumberString, IsOptional, Min, ValidateIf } from 'class-validator';
import { PaymentMode, ReceiptType } from '../entitities/receipt.entity';

export class CreateReceiptDto {
  @IsNotEmpty()
  receiptDate: string;

  @IsNotEmpty({ message: 'Please select a client' })
  @IsNumber()
  client: number;

  @IsNotEmpty()
  @IsEnum(ReceiptType)
  type: ReceiptType;

  @IsNotEmpty()
  @IsNumber()
  // @Min(1, { message: 'Amount must be greater than 0' })
  amount: number;

  @IsNotEmpty()
  @IsNumber()
  // @Min(1, { message: 'Amount must be greater than 0' })
  dueAmount: number;

  @ValidateIf((o) => o.type === ReceiptType.ADVANCE)
  @IsNotEmpty()
  @IsNumber()
  previousCredits: number;

  @ValidateIf((o) => o.type === ReceiptType.ADVANCE)
  @IsNotEmpty()
  @IsNumber()
  totalCredits: number;

  // @ValidateIf((o) => o.type === ReceiptType.INVOICE)
  // @IsNotEmpty()
  // @IsNumber()
  // creditsUsed: number;
  @IsOptional()
  creditsUsed: string;

  @IsNotEmpty()
  @IsEnum(PaymentMode)
  paymentMode: PaymentMode;

  // @IsNotEmpty()
  @IsOptional()
  paymentDate: string;

  // @IsNotEmpty()
  @IsOptional()
  referenceNumber: string;

  @IsOptional()
  invoices: any;

  @IsOptional()
  selectedInvoices: any;

  @IsOptional()
  invoicesEdited: object[];

  @IsNotEmpty({ message: 'Please Selece Billing Entity' })
  billingEntity: number;

  @IsOptional()
  whatsappCheck: boolean;

  @IsNotEmpty({ message: 'Receipt Number Required' })
  receiptNumber: string;

  // @IsNotEmpty()
  // @IsArray()
  // termsAndConditions: Array<string>;

  @IsOptional()
  termsAndConditionsCopy: any[]



  @IsOptional()
  emailCheck: boolean;

  @IsOptional()
  @IsArray()
  removedRps: Array<any>;

  @IsOptional()
  clientType: string | null;
}


export class NextReceiptNumberDto {
  @IsNotEmpty()
  @IsNumberString()
  billingEntityId: number;
}


