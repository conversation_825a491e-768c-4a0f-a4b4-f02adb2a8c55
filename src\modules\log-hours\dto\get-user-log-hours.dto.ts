import { IsDateString, IsEnum, <PERSON>NotEmpty, IsOptional, ValidateIf } from 'class-validator';

export enum UserLogHoursType {
  SELF = 'SELF',
  USER = 'USER',
}

export class GetUserLogHoursDto {
  @IsNotEmpty()
  @IsEnum(UserLogHoursType)
  type: UserLogHoursType;

  @ValidateIf((o) => o.type === UserLogHoursType.USER)
  @IsNotEmpty()
  userId: number;

  @IsOptional()
  search: string;

  @IsOptional()
  @IsDateString()
  fromDate: string;

  @IsOptional()
  @IsDateString()
  toDate: string;

  @IsOptional()
  limit: number;

  @IsOptional()
  offset: number;

  @IsOptional()
  date: string;
}

export class GetUserLogHoursStatsDto {
  @IsNotEmpty()
  @IsEnum(UserLogHoursType)
  type: UserLogHoursType;

  @ValidateIf((o) => o.type === UserLogHoursType.USER)
  @IsNotEmpty()
  userId: number;

  @IsOptional()
  fromDate: string;

  @IsOptional()
  toDate: string;
}
