import { Is<PERSON>rray, IsEnum, IsNotEmpty, IsNumberString, IsOptional, IsString, ValidateIf } from 'class-validator';

export enum FindExpenditureQueryType {
  TASK = 'TASK',
  USER = 'USER',
  SELF = 'SELF',
}

class FindExpenditureDto {
  @IsNotEmpty()
  @IsEnum(FindExpenditureQueryType)
  type: FindExpenditureQueryType;


  @IsOptional()
  pageSize: any;

  @IsOptional()
  @IsString()
  page: number;

  @IsOptional()
  @IsString()
  offset: number;

  @IsOptional()
  @IsNumberString()
  limit: number;

  @ValidateIf(
    (o: FindExpenditureDto) => o.type === FindExpenditureQueryType.TASK,
  )
  @IsNotEmpty()
  taskId: number;

  @ValidateIf(
    (o: FindExpenditureDto) => o.type === FindExpenditureQueryType.USER,
  )
  @IsNotEmpty()
  userId: number;

  @IsOptional()
  clientId: number;

  @IsOptional()
  search: string;

  @IsOptional()
  sort: string;

  @IsOptional()
  @IsArray()
  expenseNature: Array<string>;

  @IsOptional()
  @IsArray()
  expenseType: Array<string>;

  @IsOptional()
  @IsArray()
  users: Array<number>;

  @IsOptional()
  fromDate: Date;

  @IsOptional()
  toDate: Date;


}

export default FindExpenditureDto;
