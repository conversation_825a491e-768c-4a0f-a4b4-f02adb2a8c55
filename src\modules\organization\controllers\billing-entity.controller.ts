import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  ParseIntPipe,
  Patch,
  Post,
  Query,
  Request,
  UseGuards,
} from '@nestjs/common';
import { JwtAuthGuard } from 'src/modules/users/jwt/jwt-auth.guard';
import { CreateBillingEntityDto } from '../dto/create-billing-entity.dto';
import { UpdateOrganizationProfileDto } from '../dto/update-organization-profile.dto';
import { BillingEntitiesService } from '../services/billing-entities.service';
import { query } from 'express';
import { FindBillingEntityDto } from '../dto/find-billing-entities.dto';

@UseGuards(JwtAuthGuard)
@Controller('billing-entities')
export class BillingEntitesController {
  constructor(private service: BillingEntitiesService) { }

  @Post()
  async create(@Request() req: any, @Body() body: CreateBillingEntityDto) {
    const { userId } = req.user;
    return this.service.create(userId, body);
  }

  @UseGuards(JwtAuthGuard)
  @Get()
  async get(@Request() req: any, @Query() query: FindBillingEntityDto) {
    const { userId } = req.user;
    return this.service.get(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Get('sign-users/:id')
  async getSignUers(@Request() req: any, @Param('id', ParseIntPipe) id: number) {
    const { userId } = req.user;
    return this.service.getSignUsers(userId, id);
  }

  @Get('active')
  async getActive(@Request() req: any) {
    const { userId } = req.user;
    return this.service.getActive(userId);
  }

  @UseGuards(JwtAuthGuard)
  @Get('/:id')
  async getOne(@Request() req: any, @Param('id', ParseIntPipe) id: number) {
    const { userId } = req.user;
    return this.service.getOne(id, userId);
  }

  @Patch(':id')
  async update(@Request() req: any, @Param('id', ParseIntPipe) id: number, @Body() body: any) {
    const { userId } = req.user;
    return this.service.update(id, userId, body);
  }

  @Patch('default/:id')
  async default(@Request() req: any, @Param('id', ParseIntPipe) id: number) {
    const { userId } = req.user;
    return this.service.default(id, userId);
  }

  @Patch('default-sign/:id')
  async defaultSignature(@Param('id', ParseIntPipe) id: number, @Body() body: any) {
    return this.service.defaultSignature(id, body);
  };

  @Patch(`sign-users/:id`)
  async updateSignatures(@Param('id', ParseIntPipe) id: number, @Body() body: any) {
    return this.service.updateSignatures(id, body);
  }

  @Patch('status-change/:id')
  async changeStatus(@Request() req: any, @Param('id', ParseIntPipe) id: number, @Body() body: any) {

    const { userId } = req.user;
    return this.service.changeStatus(id, userId, body);

  }

  @Delete(':id')
  async delete(@Param('id', ParseIntPipe) id: number) {
    return this.service.delete(id);
  }
}
