import { BaseEntity, Column, CreateDateColumn, Entity, Index, ManyToOne, OneToMany, PrimaryGeneratedColumn, UpdateDateColumn } from "typeorm";
import { ChannelPartner } from "./channel-partner.entity";
import { ChannelPartnerSignup } from "./channel-partner-signup.entity";

@Entity()
export class CouponCode extends BaseEntity{
    @PrimaryGeneratedColumn()
    id:number;

    @Column({unique: true })
    code:string;

    @Column({type:'datetime'})
    @Index()
    validFrom:Date;

    @Column()
    @Index()
    validTo:Date;

    @Column()
    description: string;

    @CreateDateColumn({ type: 'datetime'})
    createdAt: Date;

    @UpdateDateColumn({ type: 'datetime'})
    updatedAt: Date;

    @ManyToOne(()=> ChannelPartner, (partner)=> partner.couponCode, {onDelete:'CASCADE'})
    channelPartner: ChannelPartner

    @OneToMany(() => ChannelPartnerSignup, (signup) => signup.coupon)
    signups: ChannelPartnerSignup[];

    
}