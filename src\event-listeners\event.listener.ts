import { Injectable } from '@nestjs/common';
import { OnEvent } from '@nestjs/event-emitter';
import Event from 'src/modules/events/event.entity';
import { User } from 'src/modules/users/entities/user.entity';
import { sendNotification } from 'src/notifications/notify';
import { createQueryBuilder } from 'typeorm';
import { Event_Actions } from './actions';

interface CreateEvent {
  eventData: Event;
  user: User;
}

@Injectable()
export class EventListener {
  @OnEvent(Event_Actions.EVENT_CREATED, { async: true })
  async handleEventCreate(event: CreateEvent) {
    try {
      let { eventData, user } = event;
      let data = await createQueryBuilder(Event, 'event')
        .leftJoinAndSelect('event.task', 'task')
        .leftJoinAndSelect('event.user', 'user')
        .leftJoinAndSelect('task.members', 'members')
        .leftJoinAndSelect('task.client', 'client')
        .leftJoinAndSelect('task.clientGroup', 'clientGroup')
        .leftJoinAndSelect('client.clientManager', 'clientManager')
        .where('event.id = :id', { id: eventData.id })
        .getOne();

      if (data?.type === 'EVENT') {
        let notification = {
          title: 'Event created',
          body: `A new event "${data.title}" has been created for the "${eventData.title}" by ${data.user.fullName}`,
        };
        await sendNotification([data?.user?.id], notification);
      }

      if (data?.type === 'TASK') {
        let ids = data.task.members.map((member) => member.id);

        if (data?.task?.client?.clientManager) {
          ids.push(data?.task?.client?.clientManager?.id);
        }
        // let notification = {
        //   title: 'Event created',
        //   body: `A new Event <strong> ${data.title}</strong> has been created for the "<strong>${eventData.task.name}</strong>" by <strong>${data.user.fullName}</strong>`,
        // };
        // await sendNotification(ids, notification);
      }
    } catch (e) {
      console.log(e);
    }
  }
}
