import { BadRequestException, InternalServerErrorException } from '@nestjs/common';
import Client from 'src/modules/clients/entity/client.entity';
import { BillingEntity } from 'src/modules/organization/entities/billing-entity.entity';
import Task from 'src/modules/tasks/entity/task.entity';
import { User } from 'src/modules/users/entities/user.entity';
import { generateInvoiceId } from 'src/utils';
import { Brackets, In, IsNull, Like, Not, createQueryBuilder, getManager } from 'typeorm';
import { CreateInvoiceDto, GENERATED_NUMBER_TYPE } from '../dto/create-invoice.dto';
import { FindInvoicesDto, NextInvoiceNumberDto } from '../dto/find-invoices.dto';
import { GetUnbilledTasksDto } from '../dto/get-unbilled.dto';
import InvoiceAddress from '../entitities/invoice-address.entity';
import InvoiceBankDetails from '../entitities/invoice-bank-details.entity';
import InvoiceOtherParticular from '../entitities/invoice-other-particular.entity';
import InvoiceParticular from '../entitities/invoice-particular.entity';
import { Invoice, InvoiceStatus, InvoiceType } from '../entitities/invoice.entity';
import puppeteer from 'puppeteer';
import * as xlsx from 'xlsx';
import * as _ from 'lodash';
import * as moment from 'moment';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { v4 as uuidv4 } from 'uuid';
import { PaymentStatusEnum, TaskRecurringStatus } from 'src/modules/tasks/dto/types';
import { Event_Actions } from 'src/event-listeners/actions';
import { FindClientBillingInvoices } from '../dto/find-client-billing-invoices.dto';
import { ReceiptCreditStatus } from '../entitities/receipt-credit.entity';
import { ReceiptsService } from './receipts.service';
import { ReceiptParticularStatus } from '../entitities/receipt-particular.entity';
import Activity, { ActivityType } from 'src/modules/activity/activity.entity';
import { dateFormation } from 'src/utils/datesFormation';
import Storage, { StorageSystem, StorageType } from 'src/modules/storage/storage.entity';
import { OneDriveStorageService } from 'src/modules/ondrive-storage/onedrive-storage.service';
import { Organization } from 'src/modules/organization/entities/organization.entity';
import { AwsService } from 'src/modules/storage/upload.service';
import ClientGroup from 'src/modules/client-group/client-group.entity';
import { ProformaInvoice, ProformaStatus } from '../entitities/proforma-invoice.entity';
import { formattedAmount, getTotalGst, getTotalGstReport, TAX_TYPE_VALUE } from '../totalCalculations';
import { Permissions } from "src/modules/events/permission";
import * as ExcelJS from 'exceljs'
import { Injectable } from '@nestjs/common/decorators/core';
import { precisionFree } from 'src/utils/precisionFree';
import { GoogleDriveStorageService } from 'src/modules/ondrive-storage/googledrive-storage.service';
import { sanitizeFileNameForAWS } from 'src/modules/storage/validations';

interface QueryConditions {
  id: number;
  [key: string]: number | string; // This allows additional properties with string keys and number or string values
}

@Injectable()
export class InvoiceService {
  constructor(
    private eventEmitter: EventEmitter2,
    private receiptsService: ReceiptsService,
    private oneDriveService: OneDriveStorageService,
    private googleDriveService: GoogleDriveStorageService,
    private awsService: AwsService,
  ) { }

  async createInvoice(userId: number, body: CreateInvoiceDto) {

    let invoice = new Invoice();
    let existingInvoice = await Invoice.findOne({
      where: {
        invoiceNumber: body.estimateNumber,
        billingEntity: body.billingEntity,
      },
    });

    if (existingInvoice) {
      throw new InternalServerErrorException('Invoice number already exists');
    }

    invoice.invoiceNumber = body.estimateNumber;
    invoice.invoiceUser = body.invoiceUser;
    // Object.assign(user,body.invoiceUser.uData)

    let user = await User.findOne({
      where: { id: userId },
      relations: ['organization'],
    });

    let billingEntity = await BillingEntity.findOne({
      where: { id: body.billingEntity },
    });


    let client = null;
    let clientGroup = null;

    if (body?.clientType === "CLIENT_GROUP") {
      clientGroup = await ClientGroup.findOne({ where: { id: body.client } });
    } else {
      client = await Client.findOne({ where: { id: body.client } });
    }

    let billingEntityAddress = new InvoiceAddress();
    Object.assign(billingEntityAddress, body.billingEntityAddress);

    let billingAddress = new InvoiceAddress();
    Object.assign(billingAddress, body.billingAddress);

    let shippingAddress = new InvoiceAddress();
    // body.shippingAddress.state = 'Telangana';
    Object.assign(shippingAddress, body.shippingAddress);

    let taskIds = [];
    let particulars: InvoiceParticular[] = [];
    body.particulars.forEach((particular) => {
      let invoiceParticular = new InvoiceParticular();
      Object.assign(invoiceParticular, particular);
      delete invoiceParticular.id;
      !billingEntity.hasGst && delete invoiceParticular.gst;
      !billingEntity.hasGst && delete invoiceParticular.hsn;

      particulars.push(invoiceParticular);
      if (particular.taskId) {
        taskIds.push(particular.taskId);
      }
    });

    const invoiceTasks = await Task.find({
      where: {
        id: In(taskIds),
        invoiceId: Not(IsNull()),
      },
    });

    if (invoiceTasks.length) {
      throw new InternalServerErrorException(`Task Id ${invoiceTasks.map((task) => task.taskNumber).join(', ')} already Billed`);
    }

    let otherParticulars: InvoiceOtherParticular[] = [];
    body.otherParticulars.forEach((otherParticular) => {
      otherParticular['taskExpenseType'] = 'PURE_AGENT';
      const otherParticularClone = { ...otherParticular };
      delete otherParticularClone['id'];
      let invoiceOtherParticular = new InvoiceOtherParticular();
      Object.assign(invoiceOtherParticular, otherParticularClone);

      otherParticulars.push(invoiceOtherParticular);
    });

    let bankDetails = new InvoiceBankDetails();
    bankDetails.accountNumber = body?.bankDetails?.accountNumber;
    bankDetails.bankName = body?.bankDetails?.bankName;
    bankDetails.branchName = body?.bankDetails?.branchName;
    bankDetails.ifscCode = body?.bankDetails?.ifscCode;
    bankDetails.upiId = body?.bankDetails?.upiId;
    bankDetails.accountName = body?.bankDetails?.accountName;
    bankDetails.accountType = body?.bankDetails?.accountType;
    let storage: Storage;
    const existingStorage = await Storage.findOne({
      where: { id: body.bankDetails?.upiAttachmentId },
    });
    if (existingStorage?.storageSystem === StorageSystem.MICROSOFT) {
      const newFile = await this.oneDriveService.copyFile(existingStorage?.fileId, userId, existingStorage?.name);
      storage = new Storage();
      storage.uid = uuidv4();
      storage.name = newFile?.name;
      storage.type = StorageType.FILE;
      storage.fileType = newFile?.file?.mimeType;
      storage.file = newFile?.['@microsoft.graph.downloadUrl'];
      storage.downloadUrl = newFile?.['@microsoft.graph.downloadUrl'];
      storage.fileId = newFile?.id;
      storage.fileSize = existingStorage?.fileSize;
      storage.show = false;
      storage.storageSystem = StorageSystem.MICROSOFT;
      storage.webUrl = newFile?.webUrl;
      storage.authId = user.organization.id;
      storage.organization = user.organization;
      await storage.save();
    } else if (existingStorage?.storageSystem === StorageSystem.GOOGLE) {
      const newFile = await this.googleDriveService.copyFile(existingStorage?.fileId, userId, existingStorage?.name);
      storage = new Storage();
      storage.uid = uuidv4();
      storage.name = newFile?.name;
      storage.type = StorageType.FILE;
      storage.fileType = newFile?.file?.mimeType;
      storage.file = newFile?.file;
      storage.fileId = newFile?.id;
      storage.fileSize = existingStorage?.fileSize;
      storage.show = false;
      storage.storageSystem = StorageSystem.GOOGLE;
      storage.webUrl = newFile?.webUrl;
      storage.authId = user.organization.id;
      storage.organization = user.organization;
      await storage.save();
    }
    else if (existingStorage?.storageSystem === StorageSystem.AMAZON) {
      const newFile = await this.awsService.copyS3Object(
        existingStorage?.file,
        `${existingStorage?.file}-${sanitizeFileNameForAWS(body.estimateNumber)}-${moment().valueOf()}`,
      );
      storage = new Storage();
      storage.uid = uuidv4();
      storage.name = existingStorage?.name;
      storage.type = StorageType.FILE;
      storage.fileType = existingStorage?.fileType;
      storage.file = newFile.newKey;
      storage.fileSize = existingStorage?.fileSize;
      storage.show = false;
      storage.storageSystem = StorageSystem.AMAZON;
      storage.authId = user.organization.id;
      storage.organization = user.organization;
      await storage.save();
    }
    invoice.approvalHierarchyId = body.approvalHierarchyId;
    invoice.organization = user.organization;
    invoice.billingEntity = billingEntity;
    invoice.client = client;
    invoice.clientGroup = clientGroup;
    invoice.billingEntityAddress = billingEntityAddress;
    invoice.billingAddress = billingAddress;
    invoice.shippingAddress = shippingAddress;
    invoice.bankDetails = bankDetails;
    invoice.invoiceDate = body.invoiceDate;
    invoice.invoiceDueDate = body.invoiceDueDate;
    invoice.terms = body.terms;
    invoice.placeOfSupply = body.placeOfSupply;
    // invoice.termsAndConditions = body.termsAndConditions;
    invoice.termsAndConditionsCopy = body.termsAndConditionsCopy;
    invoice.particulars = particulars;
    invoice.otherParticulars = otherParticulars;
    invoice.subTotal = body.subTotal;
    invoice.adjustment = body.adjustment;
    invoice.narration = body.narration;
    invoice.totalGstAmount = body.totalGstAmount;
    invoice.totalCharges = body.totalCharges;
    invoice.roundOff = body.roundOff;
    invoice.grandTotal = body.grandTotal;
    invoice.whatsappCheck = body.whatsappCheck;
    invoice.emailCheck = body.emailCheck;
    invoice.divideTax = body.divideTax;
    invoice.receivable = body.receivable;
    invoice.hasTds = body.hasTds;
    if (body.hasTds) {
      invoice.tdsSection = body.tdsSection;
      invoice.tdsRate = body.tdsRate;
      invoice.tdsView = body.tdsView;
    };


    if (body.divideTax) {
      invoice.supplyType = body.supplyType;
    }
    invoice['userId'] = user.id;
    if (body.submitForApproval) {
      invoice.status = InvoiceStatus.APPROVAL_PENDING;
    }

    if (body?.signatureUser) {
      const signatureUser = await User.findOne(body?.signatureUser);
      invoice.signatureUser = signatureUser;
    } else {
      invoice.signatureUser = null;
    }

    const i = await invoice.save();
    if (storage) {
      storage.invoiceBankAttachement = i.bankDetails;
      await storage.save();
    }
    let activity = new Activity();
    activity.action = Event_Actions.INVOICE_CREATED;
    activity.actorId = user.id;
    activity.type = body?.clientType === "CLIENT_GROUP" ? ActivityType.BILLING_CLIENT_GROUP : ActivityType.BILLING;
    activity.typeId = body?.clientType === "CLIENT_GROUP" ? clientGroup?.id : client?.id;
    activity.remarks = `Invoice "${invoice.invoiceNumber}" Created by ${user.fullName}`;
    await activity.save();

    if (particulars && particulars.length > 0) {
      const taskIds = particulars.map((particular) => particular.taskId);
      await createQueryBuilder(Task, 'task')
        .where('task.id IN (:...ids)', { ids: taskIds })
        .update({ paymentStatus: PaymentStatusEnum.BILLED, invoiceId: '' + invoice.id })
        .execute();
    }
    if (taskIds?.length) {
      const existingProformaInv = await ProformaInvoice.createQueryBuilder('proformaInvoice')
        .innerJoinAndSelect('proformaInvoice.particulars', 'particular')
        .where('particular.taskId IN (:...taskIds)', { taskIds })
        .andWhere('proformaInvoice.status=:status', { status: ProformaStatus.CREATED })
        .getMany();
      for (const proformaInv of existingProformaInv) {
        const pformara = await ProformaInvoice.findOne(proformaInv?.id);
        pformara.status = ProformaStatus.CLOSED;
        await pformara.save();
      }
    }
    if (body.eSign) {
      const invoiceId = invoice.id;
      const body = { eSign: true }
      const pdf = await this.downloadInvoice(invoiceId, body);
      return pdf;
    }
    return invoice;
  }

  async updateInvoice(invoiceid: number, body: any, userId: any) {

    let invoice = await Invoice.findOne({
      where: { id: invoiceid },
      relations: [
        'billingEntity',
        'billingEntityAddress',
        'billingAddress',
        'shippingAddress',
        'client',
        'clientGroup',
        'particulars',
        'otherParticulars',
        'bankDetails',
        'bankDetails.invoiceBankAttachement'
      ],
    });

    let user = await User.findOne({
      where: { id: userId },
      relations: ['organization'],
    });

    let storage: Storage;
    if (body?.bankDetails?.upiAttachmentId) {
      const attachement = await Storage.findOne({ where: { id: body?.bankDetails?.upiAttachmentId } });
      if (invoice?.bankDetails?.invoiceBankAttachement?.fileUrl) {
        if (invoice?.bankDetails?.invoiceBankAttachement?.storageSystem === StorageSystem.AMAZON) {
          await this.awsService.deleteFile(invoice?.bankDetails?.invoiceBankAttachement?.file);
          storage = invoice.bankDetails.invoiceBankAttachement;
          if (invoice?.bankDetails?.invoiceBankAttachement && attachement) {
            const newFile = await this.awsService.copyS3Object(attachement.file, `${attachement?.file}-${sanitizeFileNameForAWS(body.estimateNumber)}-${moment().valueOf()}`);
            storage.name = attachement?.name;
            storage.fileType = attachement?.fileType;
            storage.file = newFile?.newKey;
            storage.fileSize = attachement?.fileSize;
            await storage.save();
          } else if (attachement) {
            const newFile = await this.awsService.copyS3Object(attachement?.file, `${attachement?.file}-${sanitizeFileNameForAWS(body.estimateNumber)}-${moment().valueOf()}`);
            storage = new Storage();
            storage.uid = uuidv4();
            storage.name = attachement?.name;
            storage.type = StorageType.FILE;
            storage.fileType = attachement?.fileType;
            storage.file = newFile.newKey;
            storage.fileSize = attachement?.fileSize;
            storage.show = false;
            storage.storageSystem = StorageSystem.AMAZON;
            storage.authId = user.organization.id;
            storage.organization = user.organization;
            await storage.save();
          }
        } else if (invoice?.bankDetails?.invoiceBankAttachement?.storageSystem === StorageSystem.MICROSOFT) {
          await this.oneDriveService.deleteOneDriveFile(userId, invoice?.bankDetails?.invoiceBankAttachement?.fileId);
          storage = invoice.bankDetails.invoiceBankAttachement;
          if (invoice?.bankDetails?.invoiceBankAttachement && attachement) {
            const newFile = await this.oneDriveService.copyFile(attachement?.fileId, userId, attachement?.name);
            storage.name = newFile?.name;
            storage.fileType = newFile?.file?.mimeType;
            storage.file = newFile?.['@microsoft.graph.downloadUrl'];
            storage.downloadUrl = newFile?.['@microsoft.graph.downloadUrl'];
            storage.fileId = newFile?.id;
            storage.fileSize = attachement?.fileSize;
            storage.webUrl = newFile?.webUrl;
            await storage.save();

          } else if (attachement) {
            const newFile = await this.oneDriveService.copyFile(attachement?.fileId, userId, attachement?.name);
            storage = new Storage();
            storage.uid = uuidv4();
            storage.name = attachement?.name;
            storage.type = StorageType.FILE;
            storage.fileType = attachement?.fileType;
            storage.file = newFile?.['@microsoft.graph.downloadUrl'];
            storage.downloadUrl = newFile?.['@microsoft.graph.downloadUrl'];
            storage.fileId = newFile?.id;
            storage.fileSize = attachement?.fileSize;
            storage.show = false;
            storage.storageSystem = StorageSystem.MICROSOFT;
            storage.webUrl = newFile?.webUrl;
            storage.authId = user.organization.id;
            storage.organization = user.organization;
            await storage.save();
          }
        } else if (invoice?.bankDetails?.invoiceBankAttachement?.storageSystem === StorageSystem.GOOGLE) {
          await this.googleDriveService.deleteGoogleDriveFile(userId, invoice?.bankDetails?.invoiceBankAttachement?.fileId);
          storage = invoice.bankDetails.invoiceBankAttachement;
          if (invoice?.bankDetails?.invoiceBankAttachement && attachement) {
            const newFile = await this.googleDriveService.copyFile(attachement?.fileId, userId, attachement?.name);
            storage.name = newFile?.name;
            storage.fileType = newFile?.file?.mimeType;
            storage.file = newFile?.file;
            storage.fileId = newFile?.id;
            storage.fileSize = attachement?.fileSize;
            storage.webUrl = newFile?.webUrl;
            await storage.save();
          }

        }
      } else {
        storage = invoice.bankDetails.invoiceBankAttachement;
        if (attachement && user?.organization?.storageSystem == StorageSystem.AMAZON) {
          const newFile = await this.awsService.copyS3Object(attachement?.file, `${attachement?.file}-${sanitizeFileNameForAWS(body.estimateNumber)}-${moment().valueOf()}`);
          storage = new Storage();
          storage.uid = uuidv4();
          storage.name = attachement?.name;
          storage.type = StorageType.FILE;
          storage.fileType = attachement?.fileType;
          storage.file = newFile.newKey;
          storage.fileSize = attachement?.fileSize;
          storage.show = false;
          storage.storageSystem = StorageSystem.AMAZON;
          storage.authId = user.organization.id;
          storage.organization = user.organization;
          await storage.save();
        } else if (attachement && user?.organization?.storageSystem == StorageSystem.MICROSOFT) {
          const newFile = await this.oneDriveService.copyFile(attachement?.fileId, userId, attachement?.name);
          storage = new Storage();
          storage.uid = uuidv4();
          storage.name = attachement?.name;
          storage.type = StorageType.FILE;
          storage.fileType = attachement?.fileType;
          storage.file = newFile?.['@microsoft.graph.downloadUrl'];
          storage.downloadUrl = newFile?.['@microsoft.graph.downloadUrl'];
          storage.fileId = newFile?.id;
          storage.fileSize = attachement?.fileSize;
          storage.show = false;
          storage.storageSystem = StorageSystem.MICROSOFT;
          storage.webUrl = newFile?.webUrl;
          storage.authId = user.organization.id;
          storage.organization = user.organization;
          await storage.save();
        } else if (attachement && user?.organization?.storageSystem == StorageSystem.GOOGLE) {
          const newFile = await this.googleDriveService.copyFile(attachement?.fileId, userId, attachement?.name);
          storage = new Storage();
          storage.uid = uuidv4();
          storage.name = attachement?.name;
          storage.type = StorageType.FILE;
          storage.fileType = attachement?.fileType;
          storage.file = newFile?.file;
          storage.fileId = newFile?.id;
          storage.fileSize = attachement?.fileSize;
          storage.show = false;
          storage.storageSystem = StorageSystem.GOOGLE;
          storage.webUrl = newFile?.webUrl;
          storage.authId = user.organization.id;
          storage.organization = user.organization;
          await storage.save();
        }
      }
    };

    if (!body.particulars.length) {
      throw new BadRequestException(`Service Description Should Not Be Empty`);
    }
    if (!body.estimateNumber) {
      throw new BadRequestException('Invoice Number Should Not Empty');
    }
    let existingInvoiceNumber = await Invoice.find({
      where: {
        billingEntity: invoice?.billingEntity?.id,
        invoiceNumber: body?.estimateNumber,
        id: Not(invoiceid),
      },
    });
    if (existingInvoiceNumber?.length) {
      throw new BadRequestException(`Invoice Number Already Exists`);
    }

    const getPastTasks = invoice.particulars.map((item) => {
      if (item['taskId']) {
        return item['taskId'];
      }
    });
    const getNewTasks = body.particulars.map((item) => {
      if (item['taskId']) {
        return item['taskId'];
      }
    });
    const filteredTasksId = getPastTasks.filter((item) => !getNewTasks.includes(item));
    for (let item of filteredTasksId) {
      if (item !== undefined) {
        let task = await Task.findOne({ where: { id: item } });
        task.paymentStatus = PaymentStatusEnum.UNBILLED;
        task.invoiceId = null;
        task['userId'] = user.id;
        task.save();
      }
    }

    // invoice.invoiceNumber = body.invoiceNumber;

    // let user = await User.findOne({
    //   where: { id: userId },
    //   relations: ['organization'],
    // });

    let billingEntity = await BillingEntity.findOne({
      where: { id: body.billingEntity },
    });

    let client = await Client.findOne({ where: { id: body.client } });
    let clientGroup = await ClientGroup.findOne({ where: { id: invoice?.clientGroup?.id } });

    let billingEntityAddress = new InvoiceAddress();
    Object.assign(billingEntityAddress, body.billingEntityAddress);

    let billingAddress = new InvoiceAddress();
    Object.assign(billingAddress, body.billingAddress);

    let shippingAddress = new InvoiceAddress();
    const bodyShippingAddress = body?.shippingAddress?.state
      ? body.shippingAddress.state
      : 'Telangana';
    Object.assign(shippingAddress, bodyShippingAddress);

    let taskIds = [];
    let particulars: InvoiceParticular[] = [];
    body.particulars.forEach((particular) => {
      let invoiceParticular = new InvoiceParticular();
      Object.assign(invoiceParticular, particular);

      if (!billingEntity.hasGst) {
        invoiceParticular.gst = null;
        invoiceParticular.hsn = ''; // Correctly remove the `hsn` property
      }

      particulars.push(invoiceParticular);
      if (particular.taskId) {
        taskIds.push(particular.taskId);
      }
    });

    let otherParticulars: InvoiceOtherParticular[] = [];
    body.otherParticulars.forEach((otherParticular: any) => {
      otherParticular['taskExpenseType'] = 'PURE_AGENT';
      let invoiceOtherParticular = new InvoiceOtherParticular();
      Object.assign(invoiceOtherParticular, otherParticular);
      otherParticulars.push(invoiceOtherParticular);
    });

    let bankDetails = new InvoiceBankDetails();
    Object.assign(bankDetails, body.bankDetails);

    const sql = `SELECT 
    receipt_particular.invoice_id, 
    IFNULL(sum(pure_agent_amount),0) as payedPgAmount,
     IFNULL(sum(service_amount),0) as payedSerAmount 
     FROM receipt_particular
     WHERE invoice_id=${invoiceid}
     AND status='${ReceiptParticularStatus.CREATED}';`;
    let srvPgAmountSum: any[] = await getManager().query(sql);
    const totalPgAmount = invoice?.otherParticulars?.reduce((accumulator, currentInvoice: any) => {
      return accumulator + currentInvoice.amount * 1;
    }, 0);
    const totalServiceCharge = body?.grandTotal * 1 - body?.totalCharges * 1;
    if (
      totalPgAmount * 1 === srvPgAmountSum[0]['payedPgAmount'] * 1 &&
      totalServiceCharge === srvPgAmountSum[0]['payedSerAmount'] * 1
    ) {
      invoice.status = InvoiceStatus.PAID;
    }

    invoice.approvalHierarchyId = body.approvalHierarchyId;
    // invoice.organization = user.organization;
    invoice.billingEntity = billingEntity;
    invoice.client = client;
    invoice.clientGroup = clientGroup;
    invoice.billingEntityAddress = billingEntityAddress;
    // invoice.billingAddress = billingAddress;
    // invoice.shippingAddress = shippingAddress;
    invoice.bankDetails = bankDetails;
    invoice.invoiceDate = body.invoiceDate;
    invoice.invoiceDueDate = body.invoiceDueDate;
    invoice.terms = body.terms;
    invoice.placeOfSupply = body.placeOfSupply;
    // invoice.termsAndConditions = body.termsAndConditions;
    invoice.termsAndConditionsCopy = body.termsAndConditionsCopy;
    invoice.particulars = particulars;
    invoice.otherParticulars = otherParticulars;
    invoice.subTotal = body.subTotal;
    invoice.adjustment = body.adjustment;
    invoice.narration = body.narration;
    invoice.totalGstAmount = body.totalGstAmount;
    invoice.totalCharges = body.totalCharges;
    invoice.roundOff = body.roundOff;
    invoice.grandTotal = body.grandTotal;
    invoice.invoiceNumber = body.estimateNumber;
    invoice.divideTax = body.divideTax;
    invoice.receivable = body.receivable;
    invoice.hasTds = body.hasTds;
    if (body.hasTds) {
      invoice.tdsSection = body.tdsSection;
      invoice.tdsRate = body.tdsRate;
      invoice.tdsView = body.tdsView;
    } else {
      invoice.tdsSection = null;
      invoice.tdsRate = null;
      invoice.tdsView = false;
    }
    if (body.divideTax) {
      invoice.supplyType = body.supplyType;
    };
    if (body?.signatureUser) {
      const signatureUser = await User.findOne(body?.signatureUser);
      invoice.signatureUser = signatureUser;
    } else {
      invoice.signatureUser = null;
    }

    invoice['userId'] = user.id;
    const i = await invoice.save();
    if (storage) {
      storage.invoiceBankAttachement = i.bankDetails;
      await storage.save();

    }
    let activity = new Activity();
    activity.action = Event_Actions.INVOICE_UPDATED;
    activity.actorId = user.id;
    activity.type = client ? ActivityType.BILLING : ActivityType.BILLING_CLIENT_GROUP;
    activity.typeId = client ? client.id : clientGroup?.id;
    activity.remarks = `Invoice "${invoice.invoiceNumber}" Updated by ${user.fullName}`;
    await activity.save();

    if (particulars && particulars.length > 0) {
      const taskIds = particulars.map((particular) => particular.taskId);
      await createQueryBuilder(Task, 'task')
        .where('task.id IN (:...ids)', { ids: taskIds })
        .update({ paymentStatus: PaymentStatusEnum.BILLED, invoiceId: '' + invoice.id })
        .execute();
    };

    if (body.eSign) {
      const invoiceId = invoice.id;
      const body = { eSign: true }
      const pdf = await this.downloadInvoice(invoiceId, body);
      return pdf;
    }


    return invoice;
  }

  async getInvoices(userId: number, query: FindInvoicesDto) {
    let user = await User.findOne({
      where: { id: userId },
      relations: ['organization', 'role'],
    });
    const ViewAll = user.role.permissions.some(
      (permission) => permission.slug === Permissions.VIEW_ALL_CLIENT_MANAGERS
    );
    const ViewAssigned = user.role.permissions.some(
      (permission) => permission.slug === Permissions.VIEW_ASSIGNED_CLIENT_MANAGERS
    );

    let invoices = createQueryBuilder(Invoice, 'invoices')
      .select([
        'invoices.invoiceNumber',
        'invoices.billingEntity',
        'invoices.id',
        'invoices.status',
        'invoices.createdAt',
        'invoices.grandTotal',
        'invoices.subTotal',
        'invoices.invoiceDate',
        'invoices.invoiceDueDate',
        'invoices.placeOfSupply',
        'invoices.divideTax',
        'invoices.narration',
        'invoices.totalCharges',
        'invoices.tdsRate',
        'invoices.tdsSection',
        // 'billingEntity.tradeName',
        // 'billingEntity.locationOfSupply',
        // 'billingEntity.hasGst',
        // 'client.id',
        // 'client.displayName',
        // 'client.address',
        // 'client.state',
        // 'client.gstNumber',
        'organization.id',
        // 'clientGroup.id',
        // 'clientGroup.displayName',
        // 'clientGroup.address',
        // 'clientGroup.state',
        // 'clientGroup.gstNumber'
      ])
      .leftJoinAndSelect('invoices.particulars', 'particulars')
      .leftJoinAndSelect('invoices.otherParticulars', 'otherParticulars')
      .leftJoinAndSelect('invoices.billingEntity', 'billingEntity')
      .leftJoinAndSelect('invoices.client', 'client')
      .leftJoinAndSelect('invoices.clientGroup', 'clientGroup')
      .leftJoin('client.clientManagers', 'clientManagers')
      .leftJoin('clientGroup.clientGroupManagers', 'clientGroupManagers')
      .leftJoin('invoices.organization', 'organization')
      .where('organization.id = :orgId', { orgId: user.organization.id })
    if (query.gst) {
      invoices.andWhere('billingEntity.hasGst=true')
        .andWhere('invoices.status != :cancelled', { cancelled: InvoiceStatus.CANCELLED })
    }
    if (query?.billingEntity?.length) {
      invoices.andWhere('billingEntity.id IN (:...billingEntity)', { billingEntity: query.billingEntity })
    }

    if (!ViewAll && ViewAssigned) {
      invoices.andWhere(
        new Brackets(qb => {
          qb.where('client.id IS NOT NULL AND clientManagers.id = :userId', { userId })
            .orWhere('clientGroup.id IS NOT NULL AND clientGroupManagers.id = :userId', { userId })
            .orWhere('client.id IS NULL AND clientGroup.id IS NULL');
        })
      );
    } else if (!ViewAll && !ViewAssigned) {
      invoices.andWhere('client.id IS NULL AND clientGroup.id IS NULL');
    }

    if (query.fromDate && query.toDate) {
      const { startTime, endTime } = dateFormation(query.fromDate, query.toDate);
      invoices.andWhere('invoices.invoiceDate BETWEEN :startDate AND :endDate', {
        startDate: startTime,
        endDate: endTime,
      });
    }

    if (query.search) {
      invoices = invoices.andWhere(
        '(invoices.invoiceNumber LIKE :search OR client.displayName LIKE :search OR clientGroup.displayName LIKE :search)',
        {
          search: `%${query.search}%`,
        },
      );
    };
    if (query?.screen) {
      if (query.screen === 'tds') {
        invoices.andWhere('invoices.tdsRate is not null')
          .andWhere('invoices.status != :cancelled', { cancelled: InvoiceStatus.CANCELLED })
      }
    }

    if (query.status && query.status !== '') {
      if (query.status === 'OVERDUE') {
        invoices.andWhere('invoices.status NOT IN (:status)', { status: ['PAID', 'CANCELLED'] })
          .andWhere('Date(invoices.invoiceDueDate) <= :time', { time: moment().subtract(1, 'day').format('YYYY-MM-DD') })
      } else if (query.status === 'APPROVAL_PENDING' || query.status === 'PARTIALLY_PAID') {
        invoices.andWhere('(invoices.status = :status)', {
          status: query.status,
        })
          .andWhere('Date(invoices.invoiceDueDate) > :time', { time: moment().subtract(1, 'day').format("YYYY-MM-DD") })

      } else {
        invoices.andWhere('(invoices.status = :status)', {
          status: query.status,
        })
      }
    }


    const sort = (typeof query?.sort === "string") ? JSON.parse(query.sort) : query?.sort;
    if (sort?.column) {
      const columnMap: Record<string, string> = {
        invoiceDate: 'invoices.invoiceDate',
        invoiceDueDate: 'invoices.invoiceDueDate',
        grandTotal: 'invoices.grandTotal',
        tradeName: 'billingEntity.tradeName',
        displayName: 'client.displayName',
        status: 'invoices.status',
        subTotal: 'invoices.subTotal',
        totalCharges: 'invoices.totalCharges'
      };
      const column = columnMap[sort.column] || sort.column;
      invoices.orderBy(column, sort.direction.toUpperCase());
    } else {
      invoices.orderBy('invoices.createdAt', 'DESC');
    }




    if (query.offset) {
      invoices.skip(query.offset);
    }
    if (query.limit) {
      invoices.take(query.limit);
    }


    let data = await invoices.getManyAndCount();
    return {
      totalCount: data[1],
      result: data[0],
    };
  }


  async exportTdsBilling(userId: number, body: any) {
    const newQuery = { ...body, offset: 0, limit: ********* };
    let gstBillings = await this.getInvoices(userId, newQuery);

    if (!gstBillings?.result?.length) throw new BadRequestException('No Data for Export');

    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Receipts');
    const headers = [
      { header: 'S.No', key: 'serialNo' },
      { header: 'Client/Client Group', key: 'clientName' },
      { header: 'TAN', key: 'tan' },
      { header: 'Invoice #', key: 'invoiceNumber' },
      { header: 'Invoice Date', key: 'invoiceDate' },
      { header: 'Billing Entity', key: 'billingEntity' },
      { header: 'PAN', key: 'pan' },
      { header: 'Taxable Value (₹)', key: 'taxableValue' },
      { header: 'Section', key: 'section' },
      { header: 'Rate (%)', key: 'rate' },
      { header: 'TDS Deducted (₹)', key: 'tdsDeducted' }


    ]
    worksheet.columns = headers;
    let serialCounter = 1; // Initialize a counter
    // Initialize array to store max lengths for each column
    const columnMaxLengths = Array(headers.length).fill(0);

    gstBillings?.result?.forEach((gst: any) => {

      const tdsDeducted = (gst.subTotal * gst.tdsRate) / 100;
      const rowData = {
        serialNo: serialCounter++,
        clientName: gst.client
          ? `${gst.client.displayName}`
          : gst.clientGroup
            ? `${gst.clientGroup.displayName}`
            : '',
        tan: gst.client ? gst.client.tanNumber : '',
        invoiceNumber: gst?.invoiceNumber,
        invoiceDate: moment(gst.invoiceDate).format('DD-MM-YYYY'),
        billingEntity: `${gst.billingEntity?.tradeName}`,
        pan: gst?.billingEntity?.panNumber || '',
        taxableValue: 1 * gst.subTotal,
        section: gst.tdsSection,
        rate: gst?.tdsRate,
        tdsDeducted: 1 * tdsDeducted,
      }

      const row = worksheet.addRow(rowData);

      worksheet.columns.forEach((column, colIndex) => {
        const headerLength = column.header?.length || 0; // Length of header
        const cellLength = rowData[column.key]?.toString().length || 0; // Length of cell value
        columnMaxLengths[colIndex] = Math.max(columnMaxLengths[colIndex] || 0, headerLength, cellLength);
      });
    });

    // Apply column widths with padding
    worksheet.columns.forEach((column, colIndex) => {
      column.width = columnMaxLengths[colIndex] + 3; // Add padding for readability
    });

    // Style the header row and center alignment
    const headerRow = worksheet.getRow(1);
    headerRow.font = { bold: true };
    headerRow.eachCell((cell) => {
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '64B5F6' },
      };
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' },
      };
      cell.alignment = { horizontal: 'center', vertical: 'middle' };
    });

    worksheet.columns.forEach((column) => {
      if (column.key === 'clientName' || column.key === 'billingEntity') {
        column.width = 50;
        column.alignment = { wrapText: true, horizontal: 'center', vertical: 'middle' };
      }
      else {
        column.alignment = { horizontal: 'center', vertical: 'middle' };
      }
    });

    // Apply wrapText for all rows
    worksheet.eachRow((row) => {
      row.eachCell((cell) => {
        cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true }; // Center alignment and wrap text
      });
    });

    // Freeze the header row
    worksheet.views = [{ state: 'frozen', ySplit: 1 }];

    const buffer = await workbook.xlsx.writeBuffer();
    return buffer;
  }


  async exportGstBilling(userId: number, body: any) {
    const newQuery = { ...body, offset: 0, limit: ********* };
    let gstBillings = await this.getInvoices(userId, newQuery);
    gstBillings.result.sort((a: any, b: any) => new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime());

    const formatAmount: any = (amount: number | null | undefined): string => {
      if (amount == null || isNaN(amount)) {
        return '0.00';
      }
      return amount.toLocaleString('en-IN', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2,
      });
    };


    if (!gstBillings?.result?.length) throw new BadRequestException('No Data for Export');

    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Outward Supply');
    const headers = [
      { header: 'S.No', key: 'serialNo' },
      { header: 'Invoice #', key: 'invoiceNumber' },
      { header: 'Invoice Date', key: 'invoiceDate' },
      { header: 'Billing Entity', key: 'billingEntity' },
      { header: 'GSTIN', key: 'gstIn' },
      { header: 'Client Name', key: 'clientName' },
      { header: 'Client GSTIN', key: 'clientGstin' },
      { header: 'Taxable Value (₹)', key: 'taxableValue' },
      { header: 'IGST (₹)', key: 'igst' },
      { header: 'CGST (₹)', key: 'cgst' },
      { header: 'SGST (₹)', key: 'sgst' },
      { header: 'Pure Agent (₹)', key: 'pureAgent' },
      { header: 'Invoice Value (₹)', key: 'invoiceValue' },

    ]
    worksheet.columns = headers;
    let serialCounter = 1; // Initialize a counter
    // Initialize array to store max lengths for each column
    const columnMaxLengths = Array(headers.length).fill(0);

    gstBillings?.result?.forEach((gst) => {
      const sameState = gst.billingEntity?.locationOfSupply === gst.placeOfSupply.split('-')[1];
      const iGst = ((gst.billingEntity.hasGst && !sameState) || gst.divideTax)
        ? (getTotalGstReport(gst.particulars))
        : 0;
      const cgst = ((gst.billingEntity.hasGst && sameState) && !gst.divideTax)
        ? precisionFree(getTotalGstReport(gst.particulars) / 2)
        : 0;
      const sgst = ((gst.billingEntity.hasGst && sameState) && !gst.divideTax)
        ? precisionFree(getTotalGstReport(gst.particulars) / 2)
        : 0;
      const rowData = {
        serialNo: serialCounter++,
        invoiceNumber: gst?.invoiceNumber || '',
        invoiceDate: gst.invoiceDate ? moment(gst.invoiceDate).format('DD-MM-YYYY') : '',
        billingEntity: gst.billingEntity?.tradeName || '',
        gstIn: gst?.billingEntity?.gstNumber || '',
        clientName: gst.client
          ? `${gst.client.displayName}`
          : gst.clientGroup
            ? `${gst.clientGroup.displayName}`
            : '',
        clientGstin: gst.client ? gst.client.gstNumber : gst.clientGroup?.gstNumber || '',
        taxableValue: 1 * gst.subTotal,
        igst: 1 * (iGst),
        cgst: 1 * (cgst),
        sgst: 1 * (sgst),
        pureAgent: 1 * formatAmount(gst.totalCharges),
        invoiceValue: 1 * formatAmount(gst.grandTotal),


      }

      const row = worksheet.addRow(rowData);
      worksheet.columns.forEach((column, colIndex) => {
        const headerLength = column.header?.length || 0; // Length of header
        const cellLength = rowData[column.key]?.toString().length || 0; // Length of cell value
        columnMaxLengths[colIndex] = Math.max(columnMaxLengths[colIndex] || 0, headerLength, cellLength);
      });
    });

    // Apply column widths with padding
    worksheet.columns.forEach((column, colIndex) => {
      column.width = columnMaxLengths[colIndex] + 3; // Add padding for readability
    });

    // Style the header row and center alignment
    const headerRow = worksheet.getRow(1);
    headerRow.font = { bold: true };
    headerRow.eachCell((cell) => {
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '64B5F6' },
      };
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' },
      };
      cell.alignment = { horizontal: 'center', vertical: 'middle' };
    });

    worksheet.columns.forEach((column) => {
      if (column.key === 'clientName' || column.key === 'billingEntity') {
        column.width = 50;
        column.alignment = { wrapText: true, horizontal: 'center', vertical: 'middle' };
      }
      else {
        column.alignment = { horizontal: 'center', vertical: 'middle' };
      }
    });

    // Apply wrapText for all rows
    worksheet.eachRow((row) => {
      row.eachCell((cell) => {
        cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true }; // Center alignment and wrap text
      });
    });

    // Freeze the header row
    worksheet.views = [{ state: 'frozen', ySplit: 1 }];

    const buffer = await workbook.xlsx.writeBuffer();
    return buffer;
  }


  async getClientPortalInvoices(clientId: number, query: FindInvoicesDto) {
    let invoices = createQueryBuilder(Invoice, 'invoices')
      .leftJoinAndSelect('invoices.billingEntity', 'billingEntity')
      .leftJoinAndSelect('invoices.client', 'client')
      .leftJoinAndSelect('invoices.clientGroup', 'clientGroup')
      .leftJoinAndSelect('invoices.organization', 'organization')
      .where('client.id = :clientId', { clientId })
      .orderBy('invoices.createdAt', 'DESC');

    if (query.search) {
      invoices = invoices.andWhere(
        '(invoices.invoiceNumber LIKE :search OR client.displayName LIKE :search)',
        {
          search: `%${query.search}%`,
        },
      );
    }

    invoices.skip(query.offset || 0).take(query.limit || 10);

    let data = await invoices.getManyAndCount();

    return {
      totalCount: data[1],
      result: data[0],
    };
  }

  async getClientInvoices(clientid: number) {
    let sql = `SELECT i.id,i.invoice_number,i.invoice_date,i.invoice_due_date,i.grand_total,
    ifnull (iop.amount, 0) as pgamount,
    (ifnull(iop.amount, 0) - ifnull(sum(rp.pure_agent_amount), 0)) as pgdueamount,
    i.grand_total-i.total_charges as servicecharge,
    ifnull((i.grand_total-i.total_charges) - ifnull(sum(rp.service_amount), 0), 0) as servicedueamount 
    FROM invoice i 
    left join invoice_other_particular iop on iop.invoice_id = i.id 
    left join receipt_particular rp on rp.invoice_id = i.id 
    WHERE i.id IN (select id from invoice where client_id='${clientid}') and i.status != 'PAID' group by i.id`;
    let invoices = await getManager().query(sql);

    return invoices;
  }
  async getClientBillingInvoices(query: FindClientBillingInvoices) {
    const sort = (typeof query?.sort === "string") ? JSON.parse(query.sort) : query?.sort;
    let sortQuery = '';
    if (sort?.column) {
      sortQuery = 'ORDER BY ' + `${sort.column} ${sort.direction}`;

    }

    const invoiceIdsCondition =
      query.invoiceIds && query.invoiceIds.length
        ? `AND i.id IN (${query.invoiceIds.map((id) => `'${id}'`).join(', ')})`
        : '';
    let sql = `SELECT
    i.id,
    i.status,
    i.invoice_number,
    i.invoice_date,
    i.invoice_due_date,
    i.grand_total as grandTotal,
    i.tds_section,
    i.tds_rate as tdsRate,
    i.sub_total as subTotal,
    i.total_charges as totalCharges,
    IFNULL((SELECT SUM(iop.amount)
            FROM invoice_other_particular iop
            WHERE iop.invoice_id = i.id), 0) AS pgamount,

    (IFNULL((SELECT SUM(iop.amount)
             FROM invoice_other_particular iop
             WHERE iop.invoice_id = i.id), 0) - IFNULL((SELECT SUM(rp.pure_agent_amount)
                                                        FROM receipt_particular rp
                                                        WHERE rp.invoice_id = i.id AND rp.status='${ReceiptCreditStatus.CREATED
      }'), 0)) AS pgdueamount,

    i.grand_total - i.total_charges AS servicecharge,

    IFNULL((i.grand_total - i.total_charges) - IFNULL((SELECT SUM(rp.service_amount)
                                                       FROM receipt_particular rp
                                                       WHERE rp.invoice_id = i.id AND rp.status='${ReceiptCreditStatus.CREATED
      }'), 0), 0) AS servicedueamount,


     IFNULL((SELECT SUM(rp.service_amount)
                                                       FROM receipt_particular rp
                                                       WHERE rp.invoice_id = i.id AND rp.status='${ReceiptCreditStatus.CREATED
      }'), 0) AS serviceamountpayed
FROM invoice i

WHERE i.id IN (SELECT id
               FROM invoice
               WHERE client_id = '${query.clientId}'
                 AND billing_entity_id = '${query.billingEntityId}')
      AND i.invoice_number LIKE '%${query.search}%'
      AND i.status NOT IN ('${InvoiceStatus.PAID}', '${InvoiceStatus.CANCELLED}')
      ${invoiceIdsCondition}
GROUP BY i.id
${sortQuery}
LIMIT ${query.pageCount ? query.pageCount : 10} OFFSET ${query.page ? query.page : 0};`;

    let countSql = `SELECT COUNT(DISTINCT i.id) AS total
FROM invoice i
WHERE i.id IN (
    SELECT id FROM invoice 
    WHERE client_id = '${query.clientId}' AND billing_entity_id = '${query.billingEntityId}' and i.invoice_number LIKE '%${query.search}%'
)
AND i.status NOT IN ('${InvoiceStatus.PAID}', '${InvoiceStatus.CANCELLED}')
${invoiceIdsCondition}
;`;

    let [invoices, totalResult] = await Promise.all([
      getManager().query(sql),
      getManager().query(countSql),
    ]);

    let totalCount = totalResult[0].total;

    return {
      totalCount,
      invoices,
    };
  }

  async getClientGroupBillingInvoices(query: FindClientBillingInvoices) {
    const invoiceIdsCondition =
      query.invoiceIds && query.invoiceIds.length
        ? `AND i.id IN (${query.invoiceIds.map((id) => `'${id}'`).join(', ')})`
        : '';
    let sql = `SELECT
    i.id,
    i.status,
    i.invoice_number,
    i.invoice_date,
    i.invoice_due_date,
    i.grand_total as grandTotal,
     i.tds_section,
   i.tds_rate as tdsRate,
   i.sub_total as subTotal,
    IFNULL((SELECT SUM(iop.amount)
            FROM invoice_other_particular iop
            WHERE iop.invoice_id = i.id), 0) AS pgamount,

    (IFNULL((SELECT SUM(iop.amount)
             FROM invoice_other_particular iop
             WHERE iop.invoice_id = i.id), 0) - IFNULL((SELECT SUM(rp.pure_agent_amount)
                                                        FROM receipt_particular rp
                                                        WHERE rp.invoice_id = i.id AND rp.status='${ReceiptCreditStatus.CREATED
      }'), 0)) AS pgdueamount,

    i.grand_total - i.total_charges AS servicecharge,

    IFNULL((i.grand_total - i.total_charges) - IFNULL((SELECT SUM(rp.service_amount)
                                                       FROM receipt_particular rp
                                                       WHERE rp.invoice_id = i.id AND rp.status='${ReceiptCreditStatus.CREATED
      }'), 0), 0) AS servicedueamount,
       IFNULL((SELECT SUM(rp.service_amount)
                                                       FROM receipt_particular rp
                                                       WHERE rp.invoice_id = i.id AND rp.status='${ReceiptCreditStatus.CREATED
      }'), 0) AS serviceamountpayed

FROM invoice i

WHERE i.id IN (SELECT id
               FROM invoice
               WHERE client_group_id = '${query.clientId}'
                 AND billing_entity_id = '${query.billingEntityId}')
      AND i.invoice_number LIKE '%${query.search}%'
      AND i.status NOT IN ('${InvoiceStatus.PAID}', '${InvoiceStatus.CANCELLED}')
      ${invoiceIdsCondition}
GROUP BY i.id

LIMIT ${query.pageCount ? query.pageCount : 10} OFFSET ${query.page ? query.page : 0};`;

    let countSql = `SELECT COUNT(DISTINCT i.id) AS total
FROM invoice i
WHERE i.id IN (
    SELECT id FROM invoice 
    WHERE client_group_id = '${query.clientId}' AND billing_entity_id = '${query.billingEntityId}' and i.invoice_number LIKE '%${query.search}%'
)
AND i.status NOT IN ('${InvoiceStatus.PAID}', '${InvoiceStatus.CANCELLED}')
${invoiceIdsCondition}
;`;

    let [invoices, totalResult] = await Promise.all([
      getManager().query(sql),
      getManager().query(countSql),
    ]);

    let totalCount = totalResult[0].total;

    return {
      totalCount,
      invoices,
    };
  }

  async getTasks(query: GetUnbilledTasksDto) {
    let tasks = createQueryBuilder(Task, 'task')
      .leftJoinAndSelect('task.client', 'client')
      .leftJoinAndSelect('task.clientGroup', 'clientGroup')
      .leftJoinAndSelect('task.category', 'category')
      .leftJoinAndSelect('task.members', 'members')
      .leftJoinAndSelect('members.imageStorage', 'imageStorage')
      .leftJoinAndSelect('task.expenditure', 'expenditure');
    if (query?.clientType === "CLIENT_GROUP") {
      tasks.where('clientGroup.id = :clientId', { clientId: query.client })
    } else {
      tasks.where('client.id = :clientId', { clientId: query.client })
    }
    tasks.andWhere('task.status NOT IN (:status)', { status: ['deleted', 'terminated'] })
      .andWhere('task.parentTask IS NULL')
      .andWhere('(task.recurringStatus is null or task.recurringStatus = :recurringStatus)', {
        recurringStatus: TaskRecurringStatus.CREATED,
      })
      .andWhere('task.paymentStatus != :paymentStatus', { paymentStatus: 'BILLED' })
      .andWhere('task.billable IS TRUE');

    if (query.search) {
      tasks = tasks.andWhere('task.name LIKE :search', {
        search: `%${query.search}%`,
      });
    }
    const sort = (typeof query?.sort === "string") ? JSON.parse(query.sort) : query?.sort;
    if (sort?.column) {
      const columnMap: Record<string, string> = {
        feeAmount: 'task.feeAmount',
        taskNumber: 'task.taskNumber',
        name: 'category.name',
        status: 'task.status',
        additionalexpenditure: 'task.feeAmount',
      };
      const column = columnMap[sort.column] || sort.column;

      tasks.orderBy(column, sort.direction.toUpperCase());
    } else {
      tasks.orderBy('task.id', "ASC");
    }

    if (query.offset >= 0) {
      tasks.skip(query.offset);
    }

    if (query.limit) {
      tasks.take(query.limit);
    }

    let data = await tasks.getManyAndCount();

    return {
      totalCount: data[1],
      result: data[0],
    };
  };


  async getInvoice(estimateId: number, query: any) {
    let queryConditions: QueryConditions = {
      id: estimateId,
    };
    if (query.orgId) {
      queryConditions.organization = query.orgId;
    }

    if (query.clientId) {
      queryConditions.client = query.clientId;
    }

    let invoice = await Invoice.findOne({
      where: queryConditions,
      relations: [
        'billingEntity',
        'billingEntityAddress',
        'billingAddress',
        'shippingAddress',
        'client',
        'clientGroup',
        'particulars',
        'otherParticulars',
        'bankDetails',
        'bankDetails.invoiceBankAttachement',
        'organization',
        'billingEntity.logStorage',
        'billingEntity.signatureStorage',
        'signatureUser',
        'signatureUser.profile',
        'signatureUser.profile.profileSign',


      ],
    });
    return invoice || 'Un-Authorized';
  }

  async cancelInvoice(estimateId: number, userId) {
    let user = await User.findOne({
      where: { id: userId },
      relations: ['organization'],
    });

    let invoice = await Invoice.findOne({
      where: { id: estimateId },
      relations: ['particulars', 'otherParticulars', 'client', 'clientGroup'],
    });
    invoice.status = InvoiceStatus.CANCELLED;

    if (invoice.particulars && invoice.particulars.length > 0) {
      invoice.particulars.forEach(async (particular) => {
        if (particular.taskId) {
          let task = await Task.findOne({ where: { id: particular['taskId'] } });
          task.paymentStatus = PaymentStatusEnum.UNBILLED;
          task.invoiceId = null;
          task['userId'] = user.id;
          await task.save();
        }
      });
    }
    invoice['userId'] = user.id;
    await invoice.save();

    let activity = new Activity();
    activity.action = Event_Actions.INVOICE_CANCELLED;
    activity.actorId = user.id;
    activity.type = invoice?.client ? ActivityType.BILLING : ActivityType.BILLING_CLIENT_GROUP;
    activity.typeId = invoice.client ? invoice.client.id : invoice?.clientGroup?.id;
    activity.remarks = `Invoice "${invoice.invoiceNumber}" Cancelled by ${user.fullName}`;
    await activity.save();

    return invoice;
  }

  async downloadInvoice(invoiceId: number, body: any) {
    let url = `${process.env.WEBSITE_URL}/billing/invoices/${invoiceId}/preview?fromApi=true`;
    // let url = `http://localhost:3000/billing/invoices/${invoiceId}/preview?fromApi=true`;
    const options = {
      headless: true,
      executablePath: '/usr/bin/chromium-browser',
      args: ['--no-sandbox', '--disable-setuid-sandbox'],
    };
    try {
      const browser = await puppeteer.launch(options);
      const page = await browser.newPage();
      await page.setCacheEnabled(false);
      await page.setUserAgent(
        'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/78.0.3904.108 Safari/537.36',
      );

      const maxRetries = 3;
      let retries = 0;
      let loaded = false;

      while (retries < maxRetries && !loaded) {
        try {
          await page.goto(url, { waitUntil: 'networkidle2', timeout: 60000 }); // 60 seconds timeout

          // Check for a specific element that indicates the page has fully loaded
          await page.waitForFunction(
            'document.querySelector("body").innerText.includes("Powered by")',
            { timeout: 60000 }, // Adjust timeout as needed
          );
          await page.waitForFunction(
            'Array.from(document.images).every(img => img.complete && img.naturalHeight !== 0)',
            { timeout: 60000 }, // Adjust timeout as needed
          );

          loaded = true;
        } catch (error) {
          retries += 1;
          console.log(`Retrying to load the page (${retries}/${maxRetries})`);
          if (retries >= maxRetries) {
            throw error;
          }
        }
      }

      await page.addStyleTag({ content: '#freshworks-container { display: none; }' });
      await page.addStyleTag({ content: '.hide { display: none }' });
      if (body.eSign) {
        // await page.addScriptTag({ content: '.esignhide {display:block;}' });
        await page.addScriptTag({ content: '.signhide {display:none;}' });
      }

      // await page.addStyleTag({ content: '@media print { .myDiv { break-inside: avoid; } }' });

      // await page.addStyleTag({
      //   content: '@page { size: auto; }',
      // });

      // await page.emulateMediaType('screen');
      this.eventEmitter.emit(Event_Actions.INVOICE_DOWNLOADED, { invoiceId, userId: body.userId });
      await page.emulateMediaType('print');

      const pdf = await page.pdf({
        format: 'A4',
        printBackground: true,
        margin: {
          top: '0px',
          right: '0px',
          bottom: '0px',
          left: '0px',
        },
        scale: 0.6,
      });

      await browser.close();
      return Buffer.from(pdf);
    } catch (error) {
      console.error('Failed to download the invoice:', error);
      throw new InternalServerErrorException('Failed to download the invoice');
    }
  }

  async downloadInvoicewithoutEmittor(invoiceId: number) {
    let url = `${process.env.WEBSITE_URL}/billing/invoices/${invoiceId}/preview?fromApi=true`;
    // let url = `http://localhost:3000/billing/invoices/${invoiceId}/preview?fromApi=true`;
    const options = {
      executablePath: '/usr/bin/chromium-browser',
      headless: true,
      args: ['--no-sandbox', '--disable-setuid-sandbox'],
    };
    try {
      const browser = await puppeteer.launch(options);
      const page = await browser.newPage();
      await page.setCacheEnabled(false);
      await page.setUserAgent(
        'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/78.0.3904.108 Safari/537.36',
      );

      const maxRetries = 3;
      let retries = 0;
      let loaded = false;

      while (retries < maxRetries && !loaded) {
        try {
          await page.goto(url, { waitUntil: 'networkidle2', timeout: 60000 }); // 60 seconds timeout

          // Check for a specific element that indicates the page has fully loaded
          await page.waitForFunction(
            'document.querySelector("body").innerText.includes("Powered by")',
            { timeout: 60000 }, // Adjust timeout as needed
          );
          await page.waitForFunction(
            'Array.from(document.images).every(img => img.complete && img.naturalHeight !== 0)',
            { timeout: 60000 }, // Adjust timeout as needed
          );

          loaded = true;
        } catch (error) {
          retries += 1;
          console.log(`Retrying to load the page (${retries}/${maxRetries})`);
          if (retries >= maxRetries) {
            throw error;
          }
        }
      }

      await page.addStyleTag({ content: '#freshworks-container { display: none; }' });
      await page.addStyleTag({ content: '.hide { display: none }' });

      // await page.addStyleTag({ content: '@media print { .myDiv { break-inside: avoid; } }' });

      // await page.addStyleTag({
      //   content: '@page { size: auto; }',
      // });

      // await page.emulateMediaType('screen');
      await page.emulateMediaType('print');

      const pdf = await page.pdf({
        format: 'A4',
        printBackground: true,
        margin: {
          top: '0px',
          right: '0px',
          bottom: '0px',
          left: '0px',
        },
        scale: 0.6,
      });

      await browser.close();
      return Buffer.from(pdf);
    } catch (error) {
      console.error('Failed to download the invoice:', error);
      throw new InternalServerErrorException('Failed to download the invoice');
    }
  }


  //FIRST TYPE
  async exportLineItem(userId: number, body: FindInvoicesDto) {
    const newQuery = { ...body, offset: 0, limit: ********* };

    let user = await User.findOne({
      where: { id: userId },
      relations: ['organization'],
    });

    let invoicesData = await this.getInvoices(userId, body);
    let invoices = invoicesData.result;

    if (!invoices?.length) throw new BadRequestException('No Data for Export');

    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Invoices', {
      views: [{ state: 'frozen', ySplit: 1 }]
    });

    // Define column headers
    worksheet.columns = [
      { header: 'S.No', key: 'serialNo' },
      { header: 'Invoice #', key: 'invoiceNo' },
      { header: 'Invoice Date', key: 'invoiceDate' },
      { header: 'Billing Entity', key: 'billingEntity' },
      { header: 'Client / Client Group', key: 'client' },
      { header: 'GSTIN', key: 'GSTIN' },
      { header: 'Address', key: 'address' },
      { header: 'State', key: 'state' },
      { header: 'Invoice Due Date', key: 'invoiceDueDate' },
      { header: 'Description', key: 'description' },
      { header: 'HSN', key: 'hsn' },
      { header: 'GST Rate (%)', key: 'gstRate' },
      { header: 'Taxable Value (₹)', key: 'taxableValue' },
      { header: 'IGST (₹)', key: 'igst' },
      { header: 'CGST (₹)', key: 'cgst' },
      { header: 'SGST (₹)', key: 'sgst' },
      { header: 'Invoice Value (₹)', key: 'invoiceValue' },
      { header: 'Invoice Status', key: 'status' },
      { header: 'Narration', key: 'narration' },


    ];

    let serialCounter = 1;
    const columnMaxLengths = Array(worksheet.columns.length).fill(0);
    invoices.forEach((invoice) => {
      let addressLine1 = '';
      if (invoice?.client?.address?.['billingfulladdress']) {
        addressLine1 = invoice.client.address['billingfulladdress'];
      }
      if (invoice?.clientGroup?.address?.['billingfulladdress']) {
        addressLine1 = invoice.clientGroup.address['billingfulladdress'];
      }

      const sameState = invoice.billingEntity?.locationOfSupply === invoice.placeOfSupply.split('-')[1];
      const hasGst = invoice.billingEntity.hasGst;
      const divideTax = invoice.divideTax;

      // const otherParticular = invoice?.otherParticulars?.length
      //   ? {
      //     amount: invoice.otherParticulars.reduce((sum, item) => sum + item.amount * 1, 0),
      //     hsn: '0',
      //     gst: 'GST0',
      //     name: invoice.otherParticulars?.[0]?.name
      //   }
      //   : null;
      const otherParticular = invoice?.otherParticulars?.length
        // ? {
        //   amount: invoice.otherParticulars.reduce((sum, item) => sum + item.amount * 1, 0),
        //   hsn: '0',
        //   gst: 'GST0',
        //   name: invoice.otherParticulars?.[0]?.name
        // }
        ? invoice?.otherParticulars?.map(item => ({ ...item, hsn: '', gst: 'GST0', }))
        : null;

      const rowData = [
        ...invoice.particulars,
        ...(otherParticular ? otherParticular : [])
      ].map((particular) => ({
        serialNo: serialCounter++,
        invoiceNo: invoice.invoiceNumber,
        invoiceDate: moment(invoice.invoiceDate).format('DD-MM-YYYY'),
        billingEntity: invoice.billingEntity?.tradeName || '',
        client: invoice.client?.displayName || invoice.clientGroup?.displayName || '',
        GSTIN: invoice.client ? invoice.client.gstNumber : invoice.clientGroup?.gstNumber,
        address: addressLine1,
        state: invoice.client ? invoice?.client?.address?.['billingState'] : invoice?.clientGroup?.address?.['billingState'],
        invoiceDueDate: moment(invoice.invoiceDueDate).format('DD-MM-YYYY'),
        description: particular?.name,
        hsn: particular.hsn,
        gstRate: TAX_TYPE_VALUE[particular.gst],
        taxableValue: particular.amount * 1,
        igst: ((hasGst && !sameState) || divideTax) ? getTotalGst([particular]) : 0,
        cgst: (hasGst && sameState && !divideTax) ? Number((getTotalGst([particular]) / 2).toFixed(2)) : 0,
        sgst: (hasGst && sameState && !divideTax) ? Number((getTotalGst([particular]) / 2).toFixed(2)) : 0,
        invoiceValue: 1 * invoice.grandTotal,
        status: invoice?.status === InvoiceStatus.APPROVAL_PENDING || invoice.status === InvoiceStatus.PARTIALLY_PAID
          ? invoice.invoiceDueDate > moment().subtract(1, 'day').format('YYYY-MM-DD')
            ? invoice.status === InvoiceStatus.APPROVAL_PENDING
              ? 'Created'
              : 'Partially Paid'
            : 'Overdue'
          : invoice.status === InvoiceStatus.CANCELLED
            ? 'Cancelled'
            : 'Paid',
        narration: invoice.narration,
      }));


      rowData.forEach((data) => {
        const row = worksheet.addRow(Object.values(data)); // Add row from object values
        const statusIndex = Object.keys(data).indexOf('status') + 1; // Get the column index of 'status'
        const statusCell = row.getCell(statusIndex); // Get the correct cell for 'status'

        if (data.status) {
          let color;
          switch (data.status.toLowerCase()) {
            case 'created': color = '149ECD'; break;
            case 'invoiced': color = '149ECD'; break;
            case 'in progress': color = 'F49752'; break;
            case 'cancelled': color = 'F63338'; break;
            case 'converted': color = 'F49752'; break;
            case 'overdue': color = 'F63338'; break;
            case 'partially paid': color = 'F49752'; break;
            case 'cancelled': color = 'F63338'; break;
            case 'closed': color = '008000'; break;
            case 'paid': color = '008000'; break;
            default: color = '000000'; break; // Default black
          }
          statusCell.font = { color: { argb: color }, bold: true };
        }
      });
    });


    // Auto adjust column widths
    worksheet.columns.forEach((column) => {
      let maxLength = column.header.length;
      worksheet.eachRow((row) => {
        const cellValue = row.getCell(column.key as string).value;
        const cellLength = cellValue ? cellValue.toString().length : 0;
        maxLength = Math.max(maxLength, cellLength);
      });
      column.width = maxLength + 3; // Add padding
    });

    // Apply styles to header row
    const headerRow = worksheet.getRow(1);
    headerRow.font = { bold: true };
    headerRow.eachCell((cell) => {
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '64B5F6' },
      };
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' },
      };
      cell.alignment = { horizontal: 'center', vertical: 'middle' };
    });
    worksheet.columns.forEach((column) => {
      if (column.key === 'billingEntity' || column.key === 'address' || column.key === 'client') {
        column.width = 50;
        column.alignment = { wrapText: true, horizontal: 'center', vertical: 'middle' };
      } else if (column.key === 'narration') {
        column.width = 100;
        column.alignment = { wrapText: true, horizontal: 'center', vertical: 'middle' };
      } else {
        column.alignment = { horizontal: 'center', vertical: 'middle' };
      }
    });

    // Apply alignment and wrapText for all rows
    worksheet.eachRow((row, rowNumber) => {
      row.eachCell((cell) => {
        cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
      });
    });

    // Freeze the header row
    worksheet.views = [{ state: 'frozen', ySplit: 1 }];

    // Write workbook to buffer
    const file = await workbook.xlsx.writeBuffer();

    return { file, type: 'Type-A' };
  }
  async exportTaxRate(userId: number, body: FindInvoicesDto) {
    const newQuery = { ...body, offset: 0, limit: ********* };

    let user = await User.findOne({
      where: { id: userId },
      relations: ['organization'],
    });

    let invoicesData = await this.getInvoices(userId, body);
    let invoices = invoicesData.result;

    if (!invoices?.length) throw new BadRequestException('No Data for Export');

    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Invoices', {
      views: [{ state: 'frozen', ySplit: 1 }]
    });

    // Define column headers
    worksheet.columns = [
      { header: 'S.No', key: 'serialNo' },
      { header: 'Invoice #', key: 'invoiceNo' },
      { header: 'Invoice Date', key: 'invoiceDate' },
      { header: 'Billing Entity', key: 'billingEntity' },
      { header: 'Client / Client Group', key: 'client' },
      { header: 'GSTIN', key: 'GSTIN' },
      { header: 'Address', key: 'address' },
      { header: 'State', key: 'state' },
      { header: 'Invoice Due Date', key: 'invoiceDueDate' },
      // { header: 'HSN', key: 'hsn'},
      { header: 'GST Rate (%)', key: 'gstRate' },
      { header: 'Taxable Value (₹)', key: 'taxableValue' },
      { header: 'IGST (₹)', key: 'igst' },
      { header: 'CGST (₹)', key: 'cgst' },
      { header: 'SGST (₹)', key: 'sgst' },
      { header: 'Invoice Value (₹)', key: 'invoiceValue' },
      { header: 'Invoice Status', key: 'status' },
      { header: 'Narration', key: 'narration' },
    ];

    let serialCounter = 1;
    const columnMaxLengths = Array(worksheet.columns.length).fill(0);
    invoices.forEach((invoice) => {
      let addressLine1 = '';
      if (invoice?.client?.address?.['billingfulladdress']) {
        addressLine1 = invoice.client.address['billingfulladdress'];
      }
      if (invoice?.clientGroup?.address?.['billingfulladdress']) {
        addressLine1 = invoice.clientGroup.address['billingfulladdress'];
      }

      const sameState = invoice?.billingEntity?.locationOfSupply === invoice?.placeOfSupply.split('-')[1];
      const hasGst = invoice.billingEntity.hasGst;
      const divideTax = invoice?.divideTax;
      const filterZeroParticulars = invoice.particulars.filter(p => (!hasGst || p.gst == 'GST0' || p.gst == null));

      const nonZeroParticulars = invoice.particulars.filter(p => (p.gst != 'GST0' && p.gst != null && hasGst));
      const combaineWithRates: any = Object.values(
        nonZeroParticulars.reduce((acc, item) => {
          const gst = item.gst;
          const amount = 1 * item.amount;
          if (acc[gst]) {
            acc[gst].amount += amount;
          } else {
            acc[gst] = { gst, amount };
          }
          return acc;
        }, {})
      );



      let otherParticular =

        (invoice.otherParticulars?.length || filterZeroParticulars?.length) ? {
          amount: [...invoice?.otherParticulars, ...filterZeroParticulars].reduce((sum, item) => (sum + (item.amount) * 1), 0),
          hsn: '0',
          gst: 'GST0',
        } : null;

      const rowData = [
        ...combaineWithRates,
        ...(otherParticular ? [otherParticular] : [])
      ].map((particular) => {
        return {
          serialNo: serialCounter++,
          invoiceNo: invoice.invoiceNumber,
          invoiceDate: moment(invoice.invoiceDate).format('DD-MM-YYYY'),
          billingEntity: invoice.billingEntity?.tradeName || '',
          client: invoice.client?.displayName || invoice.clientGroup?.displayName || '',
          GSTIN: invoice.client ? invoice.client.gstNumber : invoice.clientGroup?.gstNumber,
          address: addressLine1,
          state: invoice.client ? invoice?.client?.address?.['billingState'] : invoice.clientGroup?.address?.['billingState'],
          invoiceDueDate: moment(invoice.invoiceDueDate).format('DD-MM-YYYY'),
          // hsn: particular.hsn,
          gstRate: TAX_TYPE_VALUE[particular.gst],
          taxableValue: particular.amount * 1,
          igst: ((hasGst && !sameState) || divideTax) ? getTotalGst([particular]) : 0,
          cgst: (hasGst && sameState && !divideTax) ? Number((getTotalGst([particular]) / 2).toFixed(2)) : 0,
          sgst: (hasGst && sameState && !divideTax) ? Number((getTotalGst([particular]) / 2).toFixed(2)) : 0,
          invoiceValue: 1 * invoice.grandTotal,
          status: invoice.status === InvoiceStatus.APPROVAL_PENDING || invoice.status === InvoiceStatus.PARTIALLY_PAID
            ? invoice.invoiceDueDate > moment().subtract(1, 'day').format('YYYY-MM-DD')
              ? invoice.status === InvoiceStatus.APPROVAL_PENDING
                ? 'Created'
                : 'Partially Paid'
              : 'Overdue'
            : invoice.status === InvoiceStatus.CANCELLED
              ? 'Cancelled'
              : 'Paid',
          narration: invoice.narration,
        };
      });



      rowData.forEach((data) => {
        const row = worksheet.addRow(Object.values(data)); // Add row from object values
        const statusIndex = Object.keys(data).indexOf('status') + 1; // Get the column index of 'status'
        const statusCell = row.getCell(statusIndex); // Get the correct cell for 'status'

        if (data.status) {
          let color;
          switch (data.status.toLowerCase()) {
            case 'created': color = '149ECD'; break;
            case 'invoiced': color = '149ECD'; break;
            case 'in progress': color = 'F49752'; break;
            case 'cancelled': color = 'F63338'; break;
            case 'converted': color = 'F49752'; break;
            case 'overdue': color = 'F63338'; break;
            case 'partially paid': color = 'F49752'; break;
            case 'cancelled': color = 'F63338'; break;
            case 'closed': color = '008000'; break;
            case 'paid': color = '008000'; break;
            default: color = '000000'; break; // Default black
          }
          statusCell.font = { color: { argb: color }, bold: true };
        }
      });
    });

    // Auto adjust column widths
    worksheet.columns.forEach((column) => {
      let maxLength = column.header.length;
      worksheet.eachRow((row) => {
        const cellValue = row.getCell(column.key as string).value;
        const cellLength = cellValue ? cellValue.toString().length : 0;
        maxLength = Math.max(maxLength, cellLength);
      });
      column.width = maxLength + 3; // Add padding
    });

    // Apply styles to header row
    const headerRow = worksheet.getRow(1);
    headerRow.font = { bold: true };
    headerRow.eachCell((cell) => {
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '64B5F6' },
      };
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' },
      };
      cell.alignment = { horizontal: 'center', vertical: 'middle' };
    });
    worksheet.columns.forEach((column) => {
      if (column.key === 'billingEntity' || column.key === 'address' || column.key === 'client') {
        column.width = 50;
        column.alignment = { wrapText: true, horizontal: 'center', vertical: 'middle' };
      } else if (column.key === 'narration') {
        column.width = 100;
        column.alignment = { wrapText: true, horizontal: 'center', vertical: 'middle' };
      } else {
        column.alignment = { horizontal: 'center', vertical: 'middle' };
      }
    });

    // Apply alignment and wrapText for all rows
    worksheet.eachRow((row, rowNumber) => {
      row.eachCell((cell) => {
        cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
      });
    });

    // Freeze the header row
    worksheet.views = [{ state: 'frozen', ySplit: 1 }];

    // Write workbook to buffer
    const file = await workbook.xlsx.writeBuffer();

    return { file, type: 'Type-B' };
  }



  async submitForApproval(invoiceId: number) {
    let invoice = await Invoice.findOne({
      where: { id: invoiceId },
    });
    invoice.status = InvoiceStatus.APPROVAL_PENDING;
    await invoice.save();
    return invoice;
  }

  async getNextInvoiceNumber(userId: number, query: NextInvoiceNumberDto) {
    let billingEntity = await BillingEntity.findOne({ where: { id: query.billingEntity } });
    const prefixToMatch = query?.type == InvoiceType.INVOICE ? billingEntity?.prefix : billingEntity?.proformaPrefix;
    if (!prefixToMatch) {
      return null;
    }
    let invoicesCount = await Invoice.count({
      where: {
        billingEntity: {
          id: query.billingEntity,
        },
        invoiceNumber: Like(`${prefixToMatch}%`),
        type: query?.type
      },
    });
    const prifixNumber = query?.type == InvoiceType.INVOICE ? billingEntity.prefixNumber : billingEntity.proformaPrefixNumber
    const inv = parseInt(prifixNumber) + invoicesCount;
    const zeroLength = prifixNumber.match(/^0*/)[0];

    return generateInvoiceId(userId, inv, query, zeroLength + '');
  }

  async getBillingActivity(userId, id, query) {
    const getTaskActivity = await createQueryBuilder(Activity, 'activity')
      .where('activity.type = :type', { type: query?.type === "billing" ? ActivityType.BILLING : ActivityType.BILLING_CLIENT_GROUP })
      .andWhere('activity.typeId = :id', { id: id });

    if (query?.fromDate && query?.toDate) {
      const { startTime, endTime } = dateFormation(query.fromDate, query.toDate);
      getTaskActivity
        .andWhere('Date(activity.created_at) >= :startTime', { startTime })
        .andWhere('Date(activity.created_at) <= :endTime', { endTime });
    }

    if (query.category) {
      getTaskActivity.andWhere(`activity.action = :action`, { action: query.category });
    }

    getTaskActivity.orderBy('activity.id', 'DESC');

    const data = await getTaskActivity.getMany();

    return data;
  }
}
