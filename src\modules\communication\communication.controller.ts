import { CommunicationService } from './communication.service';
import {
  Body,
  Controller,
  Delete,
  Get,
  InternalServerErrorException,
  Param,
  ParseIntPipe,
  Post,
  Put,
  Query,
  Req,
  Request,
  Res,
  UploadedFile,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import State from 'src/modules/states/state.entity';
import { getManager } from 'typeorm';
import {
  allcategoryandsubcategory,
  allusersefficiency,
  servicecategorystatusbytasks,
  detailedoverduetasks,
  userbasedmasterreport,
  upcommingtasks,
  taskhrmsdetails,
  usertasksbyorgid,
  statuswisetasks,
  overduetasks,
  highestnumberoftaskscompletion,
  clientinvoiceunbilled,
  clientinvoicebilled,
  clientinvoicebilling,
  clientinvoicereceipts,
  clientslistinvoice,
  clientdashboardactivitylog,
  clientpureagentreceivedanddue,
  clientdashboardamountdue,
  clientdashboardamountreceived,
  clientdashboardinvoiceunbilled,
  clientdashboardinvoicebilled,
  ClientTasksCompletedToBilled,
  ClientTasksCompletedToUnBilled,
  balancedueforinvoicesraisedreport,
  invoiceoverduereports,
  receiptmanagementreport,
  taskscompletedtobilledtasks,
  taskscompletedtounbilledtasks,
  detailedoverduecompletedtasks,
} from 'src/utils/sqlqueries';
import * as xlsx from 'xlsx';

import { Response } from 'express';
import * as fs from 'fs';
import * as path from 'path';

import { EventEmitter2 } from '@nestjs/event-emitter';
import { Event_Actions } from 'src/event-listeners/actions';
import { JwtAuthGuard } from '../users/jwt/jwt-auth.guard';
import { IUploadBody } from '../storage/storage.controller';

@Controller('communication')
export class CommunicationController {

  public eventEmitter: EventEmitter2;
  public service: CommunicationService;

  public constructor(eventEmitter: EventEmitter2, service: CommunicationService) {
    this.eventEmitter = eventEmitter;
    this.service = service;
  }

  public static withEventEmitter(eventEmitter: EventEmitter2) {
    return new CommunicationController(eventEmitter, null);
  }

  public static withService(service: CommunicationService) {
    return new CommunicationController(null, service);
  }
  // @UseGuards(JwtAuthGuard)
  // @Post('/create-client-group')
  // async create(@Request() req: any,@Body() body: any) {
  //   // const { userId } = req.user;

  //   return this.service.createClientGroup(body);
  // }

  @UseGuards(JwtAuthGuard)
@Post('/create-client-group')
async create(@Req() req: any, @Body() body: any) {
    const { userId } = req.user;
    return await this.service.createClientGroup(body,userId);
 
}
@UseGuards(JwtAuthGuard)
@Get('/get-client-group')
async getClientGroup(@Req() req: any,@Query() query: any) {
  const { userId } = req.user;
  return this.service.findClientGroup(userId, query)
}

@UseGuards(JwtAuthGuard)
@Delete('/delete-client-group/:id')
async deleteClientGroup(@Param('id', ParseIntPipe) id: number, @Req() req: any) {
  const { userId } = req.user;
  return this.service.deleteClientGroup(id, userId);
}

@UseGuards(JwtAuthGuard)
@Put('update-client-group/:id')
async update(@Param('id', ParseIntPipe) id: number, @Body() body, @Req() req: any) {
  const { userId } = req.user;
  return this.service.updateClientGroup(userId,id, body);
}

@UseGuards(JwtAuthGuard)
@Put('add-clients-to-clientgroup/:id')
async addClientstoClientGroup(@Param('id', ParseIntPipe) id: number, @Body() body, @Req() req: any) {
  const { userId } = req.user;
  return this.service.addClientstoClientGroup(userId,id, body);
}


@UseGuards(JwtAuthGuard)
@Put('remove-clients-from-clientgroup/:id')
async removeClientsFromClientGroup(@Param('id', ParseIntPipe) id: number, @Body() body, @Req() req: any) {
  const { userId } = req.user;
  return this.service.removeClientsFromClientGroup(userId,id, body);
}

@UseGuards(JwtAuthGuard)
@Get('/get-client-groupdetails/:id')
async getOneClientGroup(@Param('id', ParseIntPipe) id: number, @Req() req: any,@Query() query: any) {
  const { userId } = req.user;
  return this.service.findOneClientGroup(userId,id,query)
}

@UseGuards(JwtAuthGuard)
@Post('/create-email-template')
async createEmailTemplate(@Req() req: any, @Body() body: any) {
    const { userId } = req.user;
    return await this.service.createEmailTemplate(body,userId);
 
}

  @Post('sendmail')
  async sendEmailTemplatetoclients(@Body() body: any) {
    return this.service.sendEmailTemplatetoclients(body);
  }

  @UseGuards(JwtAuthGuard)
  @Get('/get-email-template')
  async getEmailTemplates(@Req() req: any,@Query() query: any) {
    const { userId } = req.user;
    return this.service.getEmailTemplates(userId,query)
  }

  @UseGuards(JwtAuthGuard)
@Get('/get-one-emailTemplate/:id')
async getOneEmailTemplate(@Param('id', ParseIntPipe) id: number, @Req() req: any) {
  const { userId } = req.user;
  return this.service.getOneEmailTemplate(userId,id)
}


@UseGuards(JwtAuthGuard)
@Put('update-email-temlate/:id')
async updateEmailTemplate(@Param('id', ParseIntPipe) id: number, @Body() body, @Req() req: any) {
  const { userId } = req.user;
  return this.service.updateEmailTemplate(userId,id, body);
}

  @UseGuards(JwtAuthGuard)
@Delete('/delete-email-template/:id')
async deleteEmailTemplate(@Param('id', ParseIntPipe) id: number, @Req() req: any) {
  const { userId } = req.user;
  return this.service.deleteEmailTemplate(id, userId);
}

@UseGuards(JwtAuthGuard)
@Post('/create-broadcast-activity')
async createBroadActivity(@Req() req: any, @Body() body: any) {
    const { userId } = req.user;
    return await this.service.createBroadcastActivity(body,userId);
 
}

@UseGuards(JwtAuthGuard)
@Get('/get-brocastactivity')
async getBroadcastActivity(@Req() req: any,@Query() query: any) {
  const { userId } = req.user;
  return this.service.getBroadcastActivity(userId, query)
}

@UseGuards(JwtAuthGuard)
@Get('/get-broadcastactivity/:id')
async getBroadcastActivityDetails(@Param('id', ParseIntPipe) id: number, @Req() req: any,@Query() query: any) {
  const { userId } = req.user;
  return this.service.getBroadcastActivityDetails(userId,id,query)
}

@UseGuards(JwtAuthGuard)
@Get('/get-filtered-clients/:id')
get(@Request() req: any, @Param('id', ParseIntPipe) id: number, @Query() query: any) {
    const { userId } = req.user;
    return this.service.filteredClients(userId, id, query);
}


@UseGuards(JwtAuthGuard)
@Post('/upload-files')
@UseInterceptors(FileInterceptor('file'))
uploadFile(
  @UploadedFile() file: Express.Multer.File,
  @Body() body: IUploadBody,
  @Request() req: any,
) {
  const { userId } = req.user;
  return this.service.upload(file.buffer,file.originalname,file.mimetype);
}
}
