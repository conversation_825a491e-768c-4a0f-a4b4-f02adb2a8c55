import { BadRequestException, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { createQueryBuilder, getConnection, getManager, In, Repository } from 'typeorm';
import { OrganizationHierarchy } from '../entities/organization-hierarchy';
import { User, UserStatus, UserType } from '../entities/user.entity';
import LeaveApproval from 'src/modules/attendance/leave-approval.entity';
import axios from 'axios';
import Attendance, { AttendanceStatus } from 'src/modules/attendance/attendance.entity';
import Expenditure from 'src/modules/expenditure/expenditure.entity';
import { EXPENDITURE_STATUS } from 'src/modules/expenditure/dto/types';
import LogHour from 'src/modules/log-hours/log-hour.entity';
import { Cron, CronExpression } from '@nestjs/schedule';
import { Organization } from 'src/modules/organization/entities/organization.entity';
import * as ExcelJS from 'exceljs'
import { formatDate } from 'src/utils';
import * as moment from "moment";
import { formatAmount, getExpenseType } from 'src/utils/re-use';


@Injectable()
export class OrgChartService {

  async get(userId: number) {
    const user = await User.findOne({ where: { id: userId }, relations: ['organization'] });

    const newLabels = await getConnection()
      .getRepository(OrganizationHierarchy)
      .createQueryBuilder('orgHierarchy')
      .leftJoin('orgHierarchy.user', 'user')
      .leftJoinAndSelect('user.imageStorage', 'imageStorage')
      .leftJoin('user.role', 'userRole')
      .leftJoin('orgHierarchy.manager', 'manager')
      .leftJoinAndSelect('user.imageStorage', 'imageStoragee')
      .leftJoin('manager.role', 'manageRole')
      .leftJoin('manager.organization', 'organization')
      .addSelect([
        'orgHierarchy.id',
        'user.id',
        'user.fullName',
        'manager.id',
        'manager.fullName',
        'userRole.name',
        'manageRole.name',
        'user.status',
        'manager.status'

      ])
      .where('organization.id = :organization', { organization: user.organization.id })
      .getMany();

    const hierarchyMap = new Map();
    const roots = [];

    newLabels.forEach((item) => {
      hierarchyMap.set(item.user.id, {
        id: item.user.id,
        fullName: item.user.fullName,
        children: [],
        role: item?.user?.role?.name || "",
        imageUrl: item.user.imageStorage?.fileUrl,
        status: item?.user?.status
      });
    });

    newLabels.forEach((item) => {
      if (item.user.id === item.manager.id) {
        roots.push(hierarchyMap.get(item.user.id));
      } else if (hierarchyMap.has(item.manager.id)) {
        hierarchyMap.get(item.manager.id).children.push(hierarchyMap.get(item.user.id));
      }
    });

    return roots;
  };

  async getCount(userId: number) {
    const connection = getConnection();

    const attendanceCount = await connection
      .createQueryBuilder(Attendance, 'assignedAttendance')
      .leftJoin('assignedAttendance.managers', 'managers')
      .where('assignedAttendance.status In (:...status)', { status: [EXPENDITURE_STATUS.PENDING, EXPENDITURE_STATUS.REJECTED] })
      .andWhere('managers.id = :userId', { userId })
      .getCount();

    const expenditureCount = await connection
      .createQueryBuilder(Expenditure, 'assignedExpenditure')
      .leftJoin('assignedExpenditure.managers', 'managers')
      .where('assignedExpenditure.approvalStatus IN(:...approvalStatus)', { approvalStatus: [EXPENDITURE_STATUS.PENDING, EXPENDITURE_STATUS.REJECTED] })
      .andWhere('managers.id = :userId', { userId })
      .getCount();

    const logHoursCount = await connection
      .createQueryBuilder(LogHour, 'assignedLogHours')
      .leftJoin('assignedLogHours.managers', 'managers')
      .where('assignedLogHours.approvalStatus IN (:...approvalStatus)', { approvalStatus: [EXPENDITURE_STATUS.PENDING, EXPENDITURE_STATUS.REJECTED] })
      .andWhere('managers.id = :userId', { userId })
      .getCount();

    return {
      attendanceCount,
      expenditureCount,
      logHoursCount
    }

  }

  async getAttendence(userId: number, query: any) {

    const user = await User.findOne({ where: { id: userId }, relations: ['organization'] });

    const attendence = getConnection()
      .createQueryBuilder(Attendance, 'attendance')
      .leftJoin('attendance.managers', 'managers')
      .leftJoin('attendance.reviewer', 'reviewer')
      .leftJoin('attendance.user', 'user')
      .leftJoin('attendance.createdBy', 'createdUser')
      .leftJoinAndSelect('user.imageStorage', 'imageStorage')
      .leftJoinAndSelect('createdUser.imageStorage', 'imageStoragee')
      .leftJoinAndSelect('reviewer.imageStorage', 'imageStorageee')
      .addSelect([
        'user.fullName',
        'reviewer.fullName',
        'createdUser.fullName',

      ])
      .where('managers.id = :manager', { manager: user.id })
      .andWhere('attendance.type=:type', { type: AttendanceStatus.Leave })
      .orderBy('attendance.status');

    if (query?.type == 'Pending/Rejected') {
      attendence.andWhere('attendance.status IN (:as)', { as: [EXPENDITURE_STATUS.PENDING, EXPENDITURE_STATUS.REJECTED] })
    }
    if (query?.type == 'Approved') {
      attendence.andWhere('attendance.status= :aap', { aap: EXPENDITURE_STATUS.APPROVED })
    }

    if (query?.users?.length) {
      attendence.andWhere('user.id IN (:...users)', { users: query?.users })
    }


    if (query.offset) {
      attendence.skip(query.offset);
    }
    if (query.limit) {
      attendence.take(query.limit);
    }


    let data = await attendence.getManyAndCount();

    return [data[0], data[1]];
  };
  async exportApprovalLeave(userId: number, body: any) {
    const query = body;
    let leaves: any = await this.getAttendence(userId, query);


    if (!leaves.length) throw new BadRequestException('No Data for Export');

    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Leaves');
    const headers = [
      { header: 'S.No', key: 'serialNo' },
      { header: 'Employee', key: 'employee' },
      { header: 'Date of Leave', key: 'dateOfLeave' },
      { header: 'Description', key: 'description' },
      { header: 'Requested by', key: 'requestedBy' },
      { header: 'Approval Status', key: 'approvalStatus' },
      { header: 'Approved/Declined By', key: 'approvedDeclinedBy' },
      { header: 'Approved/Declined Date & Time', key: 'approvalDateTime' }
    ]


    worksheet.columns = headers;
    let serialCounter = 1; // Initialize a counter
    // Initialize array to store max lengths for each column
    const columnMaxLengths = Array(headers.length).fill(0);

    leaves[0]?.forEach((leave) => {
      const rowData = {
        serialNo: serialCounter++,// Assign and then increment the counter
        employee: leave?.user.fullName,
        dateOfLeave: formatDate(leave?.attendanceDate),
        description: leave?.description,
        requestedBy: leave?.createdBy?.fullName,
        approvedDeclinedBy: leave?.reviewer?.fullName,
        approvalStatus: leave?.status,
        approvalDateTime: moment(leave?.reviewedAt).isValid()
          ? moment(leave?.reviewedAt)
            .utcOffset("+05:30", true)
            .format("DD-MM-YYYY h:mm:ss A")
          : ""

      }


      const row = worksheet.addRow(rowData);
      const statusCell = row.getCell('approvalStatus');
      switch (rowData.approvalStatus?.toLowerCase()) {
        case 'pending':
          statusCell.value = 'Pending';
          statusCell.font = { color: { argb: 'FFA500' }, bold: true }; // Orange
          break;
        case 'rejected':
          statusCell.value = 'Rejected';
          statusCell.font = { color: { argb: 'F63338' }, bold: true };
          break;
        case 'approved':
          statusCell.value = 'Approved';
          statusCell.font = { color: { argb: '008000' }, bold: true };
          break;
        default:
          statusCell.font = { color: { argb: '000000' }, bold: true };
          break;
      }
      worksheet.columns.forEach((column, colIndex) => {
        const headerLength = column.header?.length || 0; // Length of header
        const cellLength = rowData[column.key]?.toString().length || 0; // Length of cell value
        columnMaxLengths[colIndex] = Math.max(columnMaxLengths[colIndex] || 0, headerLength, cellLength);
      });
    });

    // Apply column widths with padding
    worksheet.columns.forEach((column, colIndex) => {
      column.width = columnMaxLengths[colIndex] + 3; // Add padding for readability
    });

    // Style the header row and center alignment
    const headerRow = worksheet.getRow(1);
    headerRow.font = { bold: true };
    headerRow.eachCell((cell) => {
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '64B5F6' },
      };
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' },
      };
      cell.alignment = { horizontal: 'center', vertical: 'middle' };
    });

    worksheet.columns.forEach((column) => {
      if (column.key === 'taskName' || column.key === 'clientName') {
        column.width = 50;
        column.alignment = { wrapText: true, horizontal: 'center', vertical: 'middle' };
      } if (column.key === 'description') {
        column.width = 100;
        column.alignment = { wrapText: true, horizontal: 'center', vertical: 'middle' };
      } else {
        column.alignment = { horizontal: 'center', vertical: 'middle' };
      }
    });

    // Apply wrapText for all rows
    worksheet.eachRow((row) => {
      row.eachCell((cell) => {
        cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true }; // Center alignment and wrap text
      });
    });

    // Freeze the header row
    worksheet.views = [{ state: 'frozen', ySplit: 1 }];

    const buffer = await workbook.xlsx.writeBuffer();
    return buffer;
  }
  async getExpenditure(userId: number, query: any) {
    const user = await User.findOne({ where: { id: userId }, relations: ['organization'] });

    const expenditure = getConnection()
      .createQueryBuilder(Expenditure, 'expenditure')
      .leftJoinAndSelect('expenditure.managers', 'managers')
      .leftJoin('expenditure.reviewer', 'reviewer')

      .leftJoin('expenditure.client', 'client')
      .leftJoin('expenditure.clientGroup', 'clientGroup')
      .leftJoin('expenditure.user', 'user')
      .leftJoin('expenditure.task', 'task')
      .leftJoinAndSelect('expenditure.storage', 'storage')
      .leftJoinAndSelect('reviewer.imageStorage', 'imageStorage')
      .leftJoinAndSelect('user.imageStorage', 'imageStoragee')

      .addSelect([
        // 'expenditure.id',
        // 'expenditure.type',
        // 'expenditure.date',
        // 'expenditure.approvalStatus',
        // 'expenditure.reviewedAt',
        // 'expenditure.taskExpenseType',
        // 'expenditure.particularName',
        // 'expenditure.amount',
        // 'expenditure.createdAt',
        'client.displayName',
        'clientGroup.displayName',
        'user.fullName',
        'reviewer.fullName',
        'task.taskNumber',
        'task.name'
      ])
      .where('managers.id = :manager', { manager: user.id });
    if (query?.type == 'Pending/Rejected') {
      expenditure.andWhere('expenditure.approvalStatus IN (:as)', { as: [EXPENDITURE_STATUS.PENDING, EXPENDITURE_STATUS.REJECTED] })
    }
    if (query?.type == 'Approved') {
      expenditure.andWhere('expenditure.approvalStatus= :aap', { aap: EXPENDITURE_STATUS.APPROVED })
    }

    if (query?.users?.length) {
      expenditure.andWhere('user.id IN (:...users)', { users: query?.users })
    }

    if (query.offset) {
      expenditure.skip(query.offset);
    }
    if (query.limit) {
      expenditure.take(query.limit);
    }
    expenditure.orderBy('expenditure.approvalStatus')
      .getManyAndCount();
    let data = await expenditure.getManyAndCount();
    return [data[0], data[1]];

  };

  async exportApprovalExpenditure(userId: number, body: any) {
    const query = body;
    let expenditures: any = await this.getExpenditure(userId, query);


    if (!expenditures.length) throw new BadRequestException('No Data for Export');

    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Expenditure');
    const headers = [
      { header: 'S.No', key: 'serialNo' },
      { header: 'Expense Nature', key: 'expenseNature' },
      { header: 'Date', key: 'date' },
      { header: 'Client / Clients Group', key: 'clientName' },
      { header: 'Expense Type', key: 'type' },
      { header: 'Task ID', key: 'taskId' },
      { header: 'Task Name | Title', key: 'taskName' },
      { header: 'Expense Title', key: 'expenseTitle' },
      { header: 'Amount (₹)', key: 'amount' },
      { header: 'Requested by', key: 'requestedBy' },
      { header: 'Approval Status', key: 'approvalStatus' },
      { header: 'Approved/Declined By', key: 'approvedDeclinedBy' },
      { header: 'Approved/Declined Date & Time', key: 'approvalDateTime' }

    ]


    worksheet.columns = headers;
    let serialCounter = 1; // Initialize a counter
    // Initialize array to store max lengths for each column
    const columnMaxLengths = Array(headers.length).fill(0);

    expenditures[0]?.forEach((expenditure) => {
      const rowData = {
        serialNo: serialCounter++,// Assign and then increment the counter
        expenseNature: expenditure?.type,
        date: formatDate(expenditure?.date),
        clientName: expenditure?.client
          ? expenditure?.client?.displayName
          : expenditure?.clientGroup?.displayName,
        type: getExpenseType(expenditure?.taskExpenseType),
        taskId: expenditure?.task?.taskNumber,
        taskName: expenditure?.task?.name,
        expenseTitle: expenditure?.particularName,
        amount: 1 * (expenditure?.amount),

        requestedBy: expenditure?.user?.fullName,
        approvedDeclinedBy: expenditure?.reviewer?.fullName,
        approvalStatus: expenditure?.approvalStatus,
        approvalDateTime: moment(expenditure?.reviewedAt).isValid()
          ? moment(expenditure?.reviewedAt)
            .utcOffset("+05:30", true)
            .format("DD-MM-YYYY h:mm:ss A")
          : ""

      }


      const row = worksheet.addRow(rowData);
      const typeCell = row.getCell('expenseNature');
      switch (rowData.expenseNature?.toLowerCase()) {
        case 'general':
          typeCell.value = 'General';
          break;
        case 'task':
          typeCell.value = 'Task';
          break;
        default:
          typeCell.font = { color: { argb: '000000' }, bold: true };
          break;
      }
      const statusCell = row.getCell('approvalStatus');
      switch (rowData.approvalStatus?.toLowerCase()) {
        case 'pending':
          statusCell.value = 'Pending';
          statusCell.font = { color: { argb: 'FFA500' }, bold: true }; // Orange
          break;
        case 'rejected':
          statusCell.value = 'Rejected';
          statusCell.font = { color: { argb: 'F63338' }, bold: true };
          break;
        case 'approved':
          statusCell.value = 'Approved';
          statusCell.font = { color: { argb: '008000' }, bold: true };
          break;
        default:
          statusCell.font = { color: { argb: '000000' }, bold: true };
          break;
      }
      worksheet.columns.forEach((column, colIndex) => {
        const headerLength = column.header?.length || 0; // Length of header
        const cellLength = rowData[column.key]?.toString().length || 0; // Length of cell value
        columnMaxLengths[colIndex] = Math.max(columnMaxLengths[colIndex] || 0, headerLength, cellLength);
      });
    });

    // Apply column widths with padding
    worksheet.columns.forEach((column, colIndex) => {
      column.width = columnMaxLengths[colIndex] + 3; // Add padding for readability
    });

    // Style the header row and center alignment
    const headerRow = worksheet.getRow(1);
    headerRow.font = { bold: true };
    headerRow.eachCell((cell) => {
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '64B5F6' },
      };
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' },
      };
      cell.alignment = { horizontal: 'center', vertical: 'middle' };
    });

    worksheet.columns.forEach((column) => {
      if (column.key === 'taskName' || column.key === 'clientName') {
        column.width = 50;
        column.alignment = { wrapText: true, horizontal: 'center', vertical: 'middle' };
      } else {
        column.alignment = { horizontal: 'center', vertical: 'middle' };
      }
    });

    // Apply wrapText for all rows
    worksheet.eachRow((row) => {
      row.eachCell((cell) => {
        cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true }; // Center alignment and wrap text
      });
    });

    // Freeze the header row
    worksheet.views = [{ state: 'frozen', ySplit: 1 }];

    const buffer = await workbook.xlsx.writeBuffer();
    return buffer;
  }
  async getloghour(userId: number, query: any) {
    const user = await User.findOne({ where: { id: userId }, relations: ['organization'] });

    const logHour = getConnection()
      .createQueryBuilder(LogHour, 'logHour',)
      .leftJoinAndSelect('logHour.managers', 'managers')
      .leftJoin('logHour.reviewer', 'reviewer')
      .leftJoin('logHour.client', 'client')
      .leftJoin('logHour.clientGroup', 'clientGroup')
      .leftJoin('logHour.user', 'user')
      .leftJoin('logHour.createdBy', 'createdBy')
      .leftJoin('logHour.task', 'task')
      .leftJoinAndSelect('reviewer.imageStorage', 'imageStorage')
      .leftJoinAndSelect('createdBy.imageStorage', 'imageStoragee')
      .addSelect([
        // 'logHour.id',
        // 'logHour.completedDate',
        // 'logHour.type',
        // 'logHour.approvalStatus',
        // 'logHour.reviewedAt',
        // 'logHour.createdAt',
        // 'logHour.title',
        // 'logHour.startTimeNew',
        // 'logHour.endTimeNew',
        // 'logHour.duration',
        'client.displayName',
        'clientGroup.displayName',
        'user.fullName',
        'createdBy.fullName',
        'reviewer.fullName',
        'task.taskNumber',
        'task.name'
      ])
      .where('managers.id = :manager', { manager: user.id });

    if (query?.type == 'Pending/Rejected') {
      logHour.andWhere('logHour.approvalStatus IN (:as)', { as: [EXPENDITURE_STATUS.PENDING, EXPENDITURE_STATUS.REJECTED] })
    }
    if (query?.type == 'Approved') {
      logHour.andWhere('logHour.approvalStatus= :aap', { aap: EXPENDITURE_STATUS.APPROVED })
    }

    if (query?.users?.length) {
    logHour.andWhere('user.id IN (:...users)', { users: query?.users })    }

    if (query.offset) {
      logHour.skip(query.offset);
    }
    if (query.limit) {
      logHour.take(query.limit);
    }
    logHour.orderBy('logHour.approvalStatus')
      .getManyAndCount();
    let data = await logHour.getManyAndCount();

    return [data[0], data[1]];

  };
  async exportApprovalLoghours(userId: number, body: any) {
    const query = body;
    let loghours: any = await this.getloghour(userId, query);


    if (!loghours.length) throw new BadRequestException('No Data for Export');

    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Log Hours');
    const headers = [
      { header: 'S.No', key: 'serialNo' },
      { header: 'Employee Name', key: 'employeeName' },
      { header: 'Date', key: 'date' },
      { header: 'Client / Clients Group', key: 'clientName' },
      { header: 'Type', key: 'type' },
      { header: 'Task ID', key: 'taskId' },
      { header: 'Task Name | Title', key: 'taskName' },
      { header: 'Start Time', key: 'startTime' },
      { header: 'End Time', key: 'endTime' },
      { header: 'Duration', key: 'duration' },
      { header: 'Requested by', key: 'requestedBy' },
      { header: 'Approval Status', key: 'approvalStatus' },
      { header: 'Approved/Declined By', key: 'approvedDeclinedBy' },
      { header: 'Approved/Declined Date & Time', key: 'approvalDateTime' }

    ]


    worksheet.columns = headers;
    let serialCounter = 1; // Initialize a counter
    // Initialize array to store max lengths for each column
    const columnMaxLengths = Array(headers.length).fill(0);

    loghours[0]?.forEach((loghour) => {
      const rowData = {
        serialNo: serialCounter++,// Assign and then increment the counter
        employeeName: loghour?.user?.name,
        date: formatDate(loghour?.completedDate),
        clientName: loghour?.client
          ? loghour?.client?.displayName
          : loghour?.clientGroup?.displayName,
        type: loghour?.type,
        taskId: loghour?.task?.taskNumber,
        taskName: loghour?.task?.name,
        startTime:
          (loghour?.startTimeNew ? moment(loghour.startTimeNew, "HH:mm:ss").format("hh:mm A") : "--"),
        endTime:
          (loghour?.endTimeNew ? moment(loghour.endTimeNew, "HH:mm:ss").format("hh:mm A") : "--"),
        duration: moment.utc(+loghour?.duration).format("HH:mm"),
        requestedBy: loghour?.createdBy?.fullName,
        approvedDeclinedBy: loghour?.reviewer?.fullName,
        approvalStatus: loghour?.approvalStatus,
        approvalDateTime: moment(loghour?.reviewedAt).isValid()
          ? moment(loghour?.reviewedAt)
            .utcOffset("+05:30", true)
            .format("DD-MM-YYYY h:mm:ss A")
          : ""

      }


      const row = worksheet.addRow(rowData);
      const typeCell = row.getCell('type');
      switch (rowData.type?.toLowerCase()) {
        case 'general':
          typeCell.value = 'General';
          break;
        case 'task':
          typeCell.value = 'Task';
          break;
        default:
          typeCell.font = { color: { argb: '000000' }, bold: true };
          break;
      }
      const statusCell = row.getCell('approvalStatus');
      switch (rowData.approvalStatus?.toLowerCase()) {
        case 'pending':
          statusCell.value = 'Pending';
          statusCell.font = { color: { argb: 'FFA500' }, bold: true }; // Orange
          break;
        case 'rejected':
          statusCell.value = 'Rejected';
          statusCell.font = { color: { argb: 'F63338' }, bold: true };
          break;
        case 'approved':
          statusCell.value = 'Approved';
          statusCell.font = { color: { argb: '008000' }, bold: true };
          break;
        default:
          statusCell.font = { color: { argb: '000000' }, bold: true };
          break;
      }
      worksheet.columns.forEach((column, colIndex) => {
        const headerLength = column.header?.length || 0; // Length of header
        const cellLength = rowData[column.key]?.toString().length || 0; // Length of cell value
        columnMaxLengths[colIndex] = Math.max(columnMaxLengths[colIndex] || 0, headerLength, cellLength);
      });
    });

    // Apply column widths with padding
    worksheet.columns.forEach((column, colIndex) => {
      column.width = columnMaxLengths[colIndex] + 3; // Add padding for readability
    });

    // Style the header row and center alignment
    const headerRow = worksheet.getRow(1);
    headerRow.font = { bold: true };
    headerRow.eachCell((cell) => {
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '64B5F6' },
      };
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' },
      };
      cell.alignment = { horizontal: 'center', vertical: 'middle' };
    });

    worksheet.columns.forEach((column) => {
      if (column.key === 'taskName' || column.key === 'clientName') {
        column.width = 50;
        column.alignment = { wrapText: true, horizontal: 'center', vertical: 'middle' };
      } else {
        column.alignment = { horizontal: 'center', vertical: 'middle' };
      }
    });

    // Apply wrapText for all rows
    worksheet.eachRow((row) => {
      row.eachCell((cell) => {
        cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true }; // Center alignment and wrap text
      });
    });

    // Freeze the header row
    worksheet.views = [{ state: 'frozen', ySplit: 1 }];

    const buffer = await workbook.xlsx.writeBuffer();
    return buffer;
  }


  async getApprovals(id: string,) {
    try {
      const url = `${process.env.CAMUNDA_URL}/vider/quantum/api/process/${id}`;
      let data: any = {
        method: 'get',
        maxBodyLength: Infinity,
        headers: {
          'Content-Type': 'application/json',
        },
      };

      const response = await axios.get(url, data);
      return response?.data
      // const result = response?.data?.tasks.filter(
      //   (task) => task.name !== 'holdTillDocumentIsSubmitted',
      // );
      // const taskSnapshots = response?.data?.data?.taskSnapshots;
      // const taskSnapshotsMainTasks = taskSnapshots?.map((snapshot) => snapshot.tasks);
      // let resultTasks: any = [];
      // const firstTask = result.filter((task) => {
      //   return task.name[task.name.length - 1] == 1;
      // });
      // for (let taskitems of result) {
      //   const name = taskitems.name;
      //   let rejectionArray: any = [];
      //   if (response?.data?.data?.taskSnapshots) {
      //     rejectionArray = taskSnapshotsMainTasks
      //       ?.map((subArray) => {
      //         const task = subArray?.find(
      //           (task) =>
      //             task?.name === name &&
      //             (task?.status === 'DECLINED' ||
      //               task?.status === 'AUTO_DECLINED' ||
      //               task?.status === 'APPROVED'),
      //         );

      //         if (task) {
      //           return {
      //             lastUpdatedOn: task.lastUpdatedOn,
      //             comments: task.comments,
      //             name: task.name,
      //             status: task.status,
      //           };
      //         }

      //         return null; // Return null if no matching task is found
      //       })
      //       .filter((task) => task !== null);
      //   }


      //   taskitems.rejectionArray = rejectionArray;
      //   resultTasks.push(taskitems);
      // }

      // resultTasks.sort((a: any, b: any) => {
      //   const lastCharA = a.name.charAt(a.name.length - 1);
      //   const lastCharB = b.name.charAt(b.name.length - 1);

      //   if (lastCharA < lastCharB) {
      //     return -1;
      //   } else if (lastCharA > lastCharB) {
      //     return 1;
      //   } else {
      //     return 0;
      //   }
      // });
      // return resultTasks;
    } catch (err) {
      console.error(err);
    }
  }



  async getManager(id: number) {
    const user = await User.findOne(id)
    // const manager = await OrganizationHierarchy.findOne({ where: { user }, realations: [] });
    const manager = await getConnection()
      .getRepository(OrganizationHierarchy)
      .createQueryBuilder('orgHierarchy')
      .leftJoin('orgHierarchy.user', 'user')
      // .leftJoin('user.role', 'userRole')
      .leftJoin('orgHierarchy.manager', 'manager')
      // .leftJoin('manager.role', 'manageRole')
      // .leftJoin('manager.organization', 'organization')
      .select([
        'orgHierarchy.id',
        'user.id',
        // 'user.fullName',
        'manager.id',
        // 'manager.fullName',
        // 'userRole.name',
        // 'manageRole.name'
      ])
      .where('user.id = :user', { user: user.id })
      .getOne();
    return manager.manager.id;
  };

  async updateManager(data: any) {
    const entityManager = getManager();
    const user = await User.findOne({ where: { id: data.user }, relations: ['organization'] });
    const manager = await User.findOne({ where: { id: data.manager } })


    const result = await entityManager.query(`
    WITH RECURSIVE subordinate_chain AS (
        -- Base: Start from the user being updated
        SELECT user_id, manager_id
        FROM organization_hierarchy
        WHERE manager_id = ?

        UNION ALL

        -- Recursive: Find all users managed by the previous level
        SELECT oh.user_id, oh.manager_id
        FROM organization_hierarchy oh
        INNER JOIN subordinate_chain sc ON oh.manager_id = sc.user_id
    )
    SELECT user_id FROM subordinate_chain WHERE user_id = ?
`, [user?.id, manager?.id]);
    if (result?.length > 0) {
      throw new BadRequestException('Invalid Manager Update: the new manager is a subordinate of the user')
    }

    const row = await OrganizationHierarchy.findOne({ where: { user: user } });

    row.manager = manager;
    await row.save();
    const pendingAttandence = await Attendance.find({
      where: {
        organization: user.organization,
        status: In([EXPENDITURE_STATUS.PENDING, EXPENDITURE_STATUS.REJECTED])
      },
      relations: ["user"]
    });

    for (let att of pendingAttandence) {
      let adminUser = await createQueryBuilder(User, 'user')
        .leftJoinAndSelect('user.organization', 'organization')
        .leftJoinAndSelect('user.role', 'role')
        .where('organization.id = :id', { id: user.organization.id })
        .andWhere('role.defaultRole = :defaultRole', { defaultRole: 1 })
        .getOne();
      const entityManager = getManager();
      const result = await entityManager.query(`
                WITH RECURSIVE manager_hierarchy AS (
                    -- Base case: Start with the given user_id
                    SELECT user_id, manager_id
                    FROM organization_hierarchy
                    WHERE user_id = ?
                    
                    UNION ALL
                    
                    -- Recursive case: Fetch the next level manager until manager_id = 123826
                    SELECT oh.user_id, oh.manager_id
                    FROM organization_hierarchy oh
                    INNER JOIN manager_hierarchy mh ON oh.user_id = mh.manager_id
                    WHERE mh.manager_id != ? -- Stop recursion when manager_id = admin_id
                )
                -- Final output: Get all user_id in the hierarchy
                SELECT user_id
                FROM manager_hierarchy
            `, [att.user.id, adminUser.id]);
      const mangers = result?.map(result => result.user_id);
      mangers.push(adminUser.id);
      const filterReqUser = mangers.filter(user => user != att.user.id);
      const users = await User.find({
        where: {
          id: In(filterReqUser)
        }
      });
      att.managers = users;
      await att.save();
    };

    const pendingExpenditure = await Expenditure.find({
      where: {
        user: { organization: user.organization },
        approvalStatus: In([EXPENDITURE_STATUS.PENDING, EXPENDITURE_STATUS.REJECTED])
      },
      relations: ["user"]
    });
    for (let exp of pendingExpenditure) {
      let adminUser = await createQueryBuilder(User, 'user')
        .leftJoinAndSelect('user.organization', 'organization')
        .leftJoinAndSelect('user.role', 'role')
        .where('organization.id = :id', { id: user.organization.id })
        .andWhere('role.defaultRole = :defaultRole', { defaultRole: 1 })
        .getOne();

      const result = await entityManager.query(`
                WITH RECURSIVE manager_hierarchy AS (
                    -- Base case: Start with the given user_id
                    SELECT user_id, manager_id
                    FROM organization_hierarchy
                    WHERE user_id = ?
                    
                    UNION ALL
                    
                    -- Recursive case: Fetch the next level manager until manager_id = 123826
                    SELECT oh.user_id, oh.manager_id
                    FROM organization_hierarchy oh
                    INNER JOIN manager_hierarchy mh ON oh.user_id = mh.manager_id
                    WHERE mh.manager_id != ? -- Stop recursion when manager_id = admin_id
                )
                -- Final output: Get all user_id in the hierarchy
                SELECT user_id
                FROM manager_hierarchy
            `, [exp.user.id, adminUser.id]);

      const mangers = result?.map(result => result.user_id);
      mangers.push(adminUser.id);
      const filterReqUser = mangers.filter(user => user != exp.user.id);
      const users = await User.find({
        where: {
          id: In(filterReqUser)
        }
      });
      exp.managers = users;
      await exp.save();
    };

    const pendingLogHours = await LogHour.find({
      where: {
        user: { organization: user.organization },
        approvalStatus: In([EXPENDITURE_STATUS.PENDING, EXPENDITURE_STATUS.REJECTED])
      },
      relations: ["user"]
    });
    for (let logHour of pendingLogHours) {
      let adminUser = await createQueryBuilder(User, 'user')
        .leftJoinAndSelect('user.organization', 'organization')
        .leftJoinAndSelect('user.role', 'role')
        .where('organization.id = :id', { id: user.organization.id })
        .andWhere('role.defaultRole = :defaultRole', { defaultRole: 1 })
        .getOne();
      const entityManager = getManager();
      const result = await entityManager.query(`
          WITH RECURSIVE manager_hierarchy AS (
              -- Base case: Start with the given user_id
              SELECT user_id, manager_id
              FROM organization_hierarchy
              WHERE user_id = ?
              
              UNION ALL
              
              -- Recursive case: Fetch the next level manager until manager_id = 123826
              SELECT oh.user_id, oh.manager_id
              FROM organization_hierarchy oh
              INNER JOIN manager_hierarchy mh ON oh.user_id = mh.manager_id
              WHERE mh.manager_id != ? -- Stop recursion when manager_id = admin_id
          )
          -- Final output: Get all user_id in the hierarchy
          SELECT user_id
          FROM manager_hierarchy
      `, [logHour.user.id, adminUser.id]);

      const mangers = result?.map(result => result.user_id);
      mangers.push(adminUser.id);
      const filterReqUser = mangers.filter(user => user != logHour.user.id);
      const users = await User.find({
        where: {
          id: In(filterReqUser)
        }
      });
      logHour.managers = users;
      await logHour.save();
    }



    return true;
  };


}
