


import {
  IResetPassword,
  IUserInvited,
} from './interfaces';

import * as nodemailer from 'nodemailer';
import { getManager } from 'typeorm';
import { sendnewMail } from './newemails';

let transporter: any = nodemailer.createTransport({
  host: 'email-smtp.ap-south-1.amazonaws.com',
  port: 587,
  auth: {
    user: 'AKIA5GHOVJDTRJ3PAQ6E',
    pass: 'BFt/gc++ytmTt24jK/317ARm7RQPk9eS12ThV1hZ5Jgc',
  },
});

async function userInvited(data: IUserInvited) {
  await sendnewMail({
    email: data.email,
    data: data,
    filePath: 'user-invited',
    subject: 'Invitation to join | Vider',
    key: "",
    id: 0
  });
}

async function userReInvited(data: IUserInvited) {
  await sendnewMail({
    email: data.email,
    data: data,
    filePath: 'user-invited',
    subject: 'Reminder - Invitation to join | Vider',
    key: "",
    id: 0
  });
}

async function resetPassword(data: IResetPassword) {
  await sendnewMail({
    email: data.email,
    data: data,
    filePath: 'reset-password',
    subject: 'Reset Password | Vider',
    key: "",
    id: 0
  });
}

export default {
  userInvited,
  resetPassword,
  userReInvited,
};