import { MigrationInterface, QueryRunner } from 'typeorm';

export class AlterDscRegisterTable1661452751738 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE dsc_register
       MODIFY COLUMN expiry_date DATE NOT NULL,
       MODIFY COLUMN issued_date DATE NULL,
       MODIFY COLUMN received_date DATE NULL,
       ADD COLUMN created_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
       ADD COLUMN updated_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP;
      `,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}
