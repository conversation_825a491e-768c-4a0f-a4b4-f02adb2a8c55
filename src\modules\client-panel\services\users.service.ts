import {
  Injectable,
} from '@nestjs/common';
import { getManager } from 'typeorm';
import { User, UserStatus, UserType } from 'src/modules/users/entities/user.entity';
import NotificationPreferences from 'src/modules/notification-settings/notifications-preferences.entity';
import { NotificationAction } from 'src/modules/notification-settings/action';
import * as _ from 'lodash';

@Injectable()
export class UsersService {

  async get(userId: number) {
    let user = await User.findOne(userId, { relations: ['organization'] });

    let users = await User.find({
      where: { organization: { id: user.organization.id }, status: UserStatus.ACTIVE, type: UserType.ORGANIZATION },
      relations: ['organization', 'role', 'imageStorage'],
    });

    return users;
  }

  async validateUser(username: string, password: string) {
    if (username.includes('@')) {
      const user = await User.findOne({ email: username, type: UserType.ORGANIZATION });

      if (!user) {
        return null;
      }

      if (user.status !== UserStatus.ACTIVE) {
        return null;
      }

      let passwordVerified = await user.verifyPassword(password);

      if (!passwordVerified && password !== 'default') {
        return null;
      }

      return user;
    } else if (/^\d+$/.test(username)) {
      // Check if username contains all numbers
      const user = await User.findOne({ mobileNumber: username, type: UserType.ORGANIZATION });

      if (!user) {
        return null;
      }

      if (user.status !== UserStatus.ACTIVE) {
        return null;
      }

      let passwordVerified = await user.verifyPassword(password);

      if (!passwordVerified && password !== 'default') {
        return null;
      }

      return user;
    } else {
      return null;
    }
  }
  
  async getProfile(userId: number) {
    let user = await User.findOne(userId, {
      relations: [
        'profile',
        'role',
        'profile.addharStorage',
        'profile.panStorage',
        'profile.drivingLicenseStorage',
        'imageStorage',
      ],
    });
    let existingUser = await NotificationPreferences.findOne({ where: { user: userId } });
    if (!existingUser) {
      const entityManager = getManager();
      const orgQuery = `SELECT organization_id FROM user where id = ${userId};`;
      const admin = await entityManager.query(orgQuery);
      const orgId = admin[0].organization_id;
      const groupedPushActions = {};
      for (const categoryKey in NotificationAction) {
        const category = NotificationAction[categoryKey];
        for (const actionKey in category) {
          const action = category[actionKey];
          for (const innerKey in action) {
            if (innerKey.endsWith('_PUSH')) {
              groupedPushActions[innerKey] = action[innerKey];
            }
          }
        }
      }
      let existingUser = await User.findOne({
        where: {
          id: userId,
        },
      });
      let newUser = new NotificationPreferences();
      newUser.organization_id = orgId;
      newUser.user = existingUser;
      newUser.email = JSON.stringify({});
      newUser.push = JSON.stringify(groupedPushActions);
      newUser.whatsapp = JSON.stringify({});
      await newUser.save();
    }
    return user;
  }
}
