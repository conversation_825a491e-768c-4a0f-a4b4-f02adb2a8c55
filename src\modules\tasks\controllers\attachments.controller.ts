import {
  Body,
  Get,
  Param,
  ParseIntPipe,
  Post,
  Query,
  UploadedFiles,
  UseGuards,
  UseInterceptors,
  Request
} from '@nestjs/common';
import { FilesInterceptor } from '@nestjs/platform-express';
import { TasksController } from './task.controller';
import { JwtAuthGuard } from 'src/modules/users/jwt/jwt-auth.guard';

export class AttachmentsController extends TasksController {
  @UseGuards(JwtAuthGuard)
  @Post('/:taskId/attachments')
  @UseInterceptors(FilesInterceptor('files'))
  addAttachments(

    @UploadedFiles() files: Express.Multer.File[],
    @Param('taskId', ParseIntPipe) taskId: number,
    @Request() req,
  ) {
    const { userId } = req.user;
    return this.attachmentsService.saveAttachments(taskId, files, userId);
  }

  @UseGuards(JwtAuthGuard)
  @Post('/:taskId/attachments/from-storage')
  addAttachmentsFromStorage(
    @Body() body: { fileIds: number[] },
    @Param('taskId', ParseIntPipe) taskId: number,
    @Request() req,
  ) {
    const { userId } = req.user;
    return this.attachmentsService.addAttachmentsFromStorage(taskId, body.fileIds, userId);
  }

  @Get('/attachments')
  getAttachments(@Query('taskId', ParseIntPipe) taskId: number) {
    return this.attachmentsService.findAttachments(taskId);
  }
}
