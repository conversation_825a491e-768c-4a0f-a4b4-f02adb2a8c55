import { Body, Controller, Get, Param, ParseIntPipe, Post, Put, Query, Req, UseGuards } from "@nestjs/common";
import { ProformaService } from "../services/proforma.service";
import { JwtAuthGuard } from "src/modules/users/jwt/jwt-auth.guard";
import { CreateInvoiceDto } from "../dto/create-invoice.dto";
import { FindInvoicesDto } from "../dto/find-invoices.dto";



@Controller('proforma')
export class ProformaController {
    constructor(private service: ProformaService) { }

    @UseGuards(JwtAuthGuard)
    @Post()
    async create(@Body() body: CreateInvoiceDto, @Req() request: any) {
        const { userId } = request.user;
        return this.service.create(userId, body);
    };

    @UseGuards(JwtAuthGuard)
    @Get()
    async get(@Req() request: any, @Query() query: FindInvoicesDto) {
        const { userId } = request.user;
        return this.service.get(userId, query);
    }

    @Get('/:id/preview')
    getInvoice(@Param('id', ParseIntPipe) id: number, @Query() query: any) {
        return this.service.getProformaInvoice(id, query);
    };

    @UseGuards(JwtAuthGuard)
    @Put('/:id')
    updateInvoice(@Param('id', ParseIntPipe) id: number, @Body() body: any, @Req() req: any) {
        const { userId } = req.user;
        return this.service.updateProformaInvoice(id, body, userId);
    };

    @UseGuards(JwtAuthGuard)
    @Get('/performa-tasks')
    getClientProformaTasks(@Query() query: any) {
        return this.service.getProformaTasks(query);
    };

    @Post('/:id/download')
    async downloadEstimate(@Param('id', ParseIntPipe) id: number, @Body() body: any) {
        return this.service.downloadProformaInvoice(id, body);
    };

    @Post('/:id/downloadwithoutemittor')
    async downloadEstimatewithoutEmittor(@Param('id', ParseIntPipe) id: number) {
        return this.service.downloadProformaInvoicewithoutEmittor(id);
    }
    @UseGuards(JwtAuthGuard)
    @Post('/convert')
    convertProformaInvoice(@Body() body: CreateInvoiceDto, @Req() request: any) {
        const { userId } = request.user;
        return this.service.convert(userId, body);
    };

    @UseGuards(JwtAuthGuard)
    @Post('/:id/cancel')
    async cancelEstimate(@Param('id', ParseIntPipe) id: number, @Req() req: any) {
        const { userId } = req.user;
        return this.service.cancelProformaInvoice(id, userId);
    };

    @UseGuards(JwtAuthGuard)
    @Post('/export')
    async exportInvoices(@Req() req: any, @Body() body: any) {
        const { userId } = req.user;
        if (body.type == 'Type-A') {
            return this.service.exportA(userId, body);
        } else if (body.type == 'Type-B') {
            return this.service.exportB(userId, body);
        }
    }




}
