import Team from '../entities/team.entity';
import { BadRequestException, Injectable } from '@nestjs/common';
import { createQueryBuilder } from 'typeorm';
import { CreateTeamDto } from '../dto/create-team.dto';
import { User } from 'src/modules/users/entities/user.entity';

@Injectable()
export class TeamsService {
  async create(userId: number, body: CreateTeamDto) {
    let user = await User.findOne({
      where: { id: userId },
      relations: ['organization'],
    });

    let existingTeam = await Team.findOne({
      where: {
        name: body.name.trim(),
        organization: user.organization.id,
      },
    });

    if (existingTeam) {
      throw new BadRequestException('Team already exists');
    }

    let members = await User.findByIds(body.members);
    let team = new Team();
    team.name = body.name.trim();
    team.organization = user.organization;
    team.members = members;
    team.tags = body.tags;
    await team.save();
    return team;
  }

  async  get(userId: number) {
    let user = await User.findOne(userId, { relations: ['organization'] });
    let teams = createQueryBuilder(Team, 'team')
      .leftJoinAndSelect('team.members', 'members')
      .leftJoinAndSelect('members.imageStorage','imageStorage')
      .leftJoin('team.organization', 'organization')
      .where('team.organization.id = :organizationId', {
        organizationId: user.organization.id,
      });
    return await teams.getMany();
  }

  async getOne(teamId: number) {
    let team = await Team.findOne({ where: { id: teamId }, relations: ['members'] });
    return team;
  }

  async removeFromTeam(teamId: number, body: { userId: number }) {
    let team = await Team.findOne(teamId, { relations: ['members'] });
    team.members = team.members.filter((member) => member.id !== body.userId);
    await team.save();
    return team;
  }

  async update(id: number, body: CreateTeamDto) {
    let team = await Team.findOne(id);
    team.name = body.name.trim();
    team.members = await User.findByIds(body.members);
    team.tags = body.tags;
    await team.save();
    return team;
  }

  async delete(id: number) {
    let team = await Team.findOne({ where: { id } });
    await team.remove();
    return team;
  }
}
