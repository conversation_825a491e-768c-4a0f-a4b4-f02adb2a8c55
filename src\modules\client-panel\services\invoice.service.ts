import { BadRequestException, Injectable, InternalServerErrorException } from '@nestjs/common';
import Client from 'src/modules/clients/entity/client.entity';
import { BillingEntity } from 'src/modules/organization/entities/billing-entity.entity';
import Task from 'src/modules/tasks/entity/task.entity';
import { User } from 'src/modules/users/entities/user.entity';
import { generateInvoiceId } from 'src/utils';
import { In, Like, Not, createQueryBuilder, getManager } from 'typeorm';
import { CreateInvoiceDto, GENERATED_NUMBER_TYPE } from 'src/modules/billing/dto/create-invoice.dto';
import { FindInvoicesDto, NextInvoiceNumberDto } from 'src/modules/billing/dto/find-invoices.dto';
import { GetUnbilledTasksDto } from 'src/modules/billing/dto/get-unbilled.dto';
import InvoiceAddress from 'src/modules/billing/entitities/invoice-address.entity';
import InvoiceBankDetails from 'src/modules/billing/entitities/invoice-bank-details.entity';
import InvoiceOtherParticular from 'src/modules/billing/entitities/invoice-other-particular.entity';
import InvoiceParticular from 'src/modules/billing/entitities/invoice-particular.entity';
import { Invoice, InvoiceStatus, InvoiceType } from 'src/modules/billing/entitities/invoice.entity';
import puppeteer from 'puppeteer';
import * as xlsx from 'xlsx';
import * as _ from 'lodash';
import * as moment from 'moment';

import { PaymentStatusEnum, TaskRecurringStatus } from 'src/modules/tasks/dto/types';
import { FindClientBillingInvoices } from 'src/modules/billing/dto/find-client-billing-invoices.dto';
import { ReceiptCreditStatus } from 'src/modules/billing/entitities/receipt-credit.entity';
import { ReceiptsService } from './receipts.service';
import { ReceiptParticularStatus } from 'src/modules/billing/entitities/receipt-particular.entity';
import { dateFormation } from 'src/utils/datesFormation';

interface QueryConditions {
  id: number;
  [key: string]: number | string; // This allows additional properties with string keys and number or string values
}



@Injectable()
export class InvoiceService {

  async getInvoices(userId: number, query: FindInvoicesDto) {
    let user = await User.findOne({
      where: { id: userId },
      relations: ['organization'],
    });

    let invoices = createQueryBuilder(Invoice, 'invoices')
      .select([
        'invoices.invoiceNumber',
        'invoices.billingEntity',
        'invoices.id',
        'invoices.status',
        'invoices.createdAt',
        'invoices.grandTotal',
        'invoices.subTotal',
        'invoices.invoiceDate',
        'invoices.invoiceDueDate',
        'invoices.placeOfSupply',
        'invoices.divideTax',
        'invoices.narration',
        'invoices.totalCharges',
        'invoices.tdsRate',
        'invoices.tdsSection',
        // 'billingEntity.tradeName',
        // 'billingEntity.locationOfSupply',
        // 'billingEntity.hasGst',
        'client.id',
        'client.displayName',
        'client.address',
        'client.state',
        'client.gstNumber',
        'organization.id',
        'clientGroup.id',
        'clientGroup.displayName',
        'clientGroup.address',
        'clientGroup.state',
        'clientGroup.gstNumber'


      ])
      .leftJoinAndSelect('invoices.particulars', 'particulars')
      .leftJoinAndSelect('invoices.otherParticulars', 'otherParticulars')
      .leftJoinAndSelect('invoices.billingEntity', 'billingEntity')
      .leftJoinAndSelect('invoices.client', 'client')
      .leftJoinAndSelect('invoices.clientGroup', 'clientGroup')
      .leftJoin('invoices.organization', 'organization')
      .where('organization.id = :orgId', { orgId: user.organization.id })
    if (query.gst) {
      invoices.andWhere('billingEntity.hasGst=true')
        .andWhere('invoices.status != :cancelled', { cancelled: InvoiceStatus.CANCELLED })
    }
    if (query?.billingEntity?.length) {
      invoices.andWhere('billingEntity.id IN (:...billingEntity)', { billingEntity: query.billingEntity })
    }

    if (query.fromDate && query.toDate) {
      const { startTime, endTime } = dateFormation(query.fromDate, query.toDate);
      invoices.andWhere('invoices.createdAt BETWEEN :startDate AND :endDate', {
        startDate: startTime,
        endDate: endTime,
      });
    }

    if (query.search) {
      invoices = invoices.andWhere(
        '(invoices.invoiceNumber LIKE :search OR client.displayName LIKE :search OR clientGroup.displayName LIKE :search)',
        {
          search: `%${query.search}%`,
        },
      );
    };
    if (query?.screen) {
      if (query.screen === 'tds') {
        invoices.andWhere('invoices.tdsRate is not null')
          .andWhere('invoices.status != :cancelled', { cancelled: InvoiceStatus.CANCELLED })
      }
    }

    if (query.status && query.status !== '') {
      if (query.status === 'OVERDUE') {
        invoices.andWhere('invoices.status NOT IN (:status)', { status: ['PAID', 'CANCELLED'] })
          .andWhere('Date(invoices.invoiceDueDate) <= :time', { time: moment().subtract(1, 'day').format('YYYY-MM-DD') })
      } else if (query.status === 'APPROVAL_PENDING' || query.status === 'PARTIALLY_PAID') {
        invoices.andWhere('(invoices.status = :status)', {
          status: query.status,
        })
          .andWhere('Date(invoices.invoiceDueDate) > :time', { time: moment().subtract(1, 'day').format("YYYY-MM-DD") })

      } else {
        invoices.andWhere('(invoices.status = :status)', {
          status: query.status,
        })
      }
    }


    const sort = (typeof query?.sort === "string") ? JSON.parse(query.sort) : query?.sort;
    if (sort?.column) {
      const columnMap: Record<string, string> = {
        invoiceDate: 'invoices.invoiceDate',
        invoiceDueDate: 'invoices.invoiceDueDate',
        grandTotal: 'invoices.grandTotal',
        tradeName: 'billingEntity.tradeName',
        displayName: 'client.displayName',
        status: 'invoices.status',
        subTotal: 'invoices.subTotal',
        totalCharges: 'invoices.totalCharges'
      };
      const column = columnMap[sort.column] || sort.column;
      invoices.orderBy(column, sort.direction.toUpperCase());
    } else {
      invoices.orderBy('invoices.createdAt', 'DESC');
    }




    if (query.offset) {
      invoices.skip(query.offset);
    }
    if (query.limit) {
      invoices.take(query.limit);
    }


    let data = await invoices.getManyAndCount();
    return {
      totalCount: data[1],
      result: data[0],
    };
  }

  async getClientPortalInvoices(clientId: number, query: FindInvoicesDto) {
    let invoices = createQueryBuilder(Invoice, 'invoices')
      .leftJoinAndSelect('invoices.billingEntity', 'billingEntity')
      .leftJoinAndSelect('invoices.client', 'client')
      .leftJoinAndSelect('invoices.organization', 'organization')
      .where('client.id = :clientId', { clientId })
      .orderBy('invoices.createdAt', 'DESC')

    if (query.search) {
      invoices = invoices.andWhere(
        '(invoices.invoiceNumber LIKE :search OR client.displayName LIKE :search)',
        {
          search: `%${query.search}%`,
        },
      );
    }

    invoices.skip(query.offset || 0).take(query.limit || 10);

    let data = await invoices.getManyAndCount();

    return {
      totalCount: data[1],
      result: data[0],
    };
  }

  async getClientInvoices(clientid: number) {
    let sql = `SELECT i.id,i.invoice_number,i.invoice_date,i.invoice_due_date,i.grand_total,
    ifnull (iop.amount, 0) as pgamount,
    (ifnull(iop.amount, 0) - ifnull(sum(rp.pure_agent_amount), 0)) as pgdueamount,
    i.grand_total-i.total_charges as servicecharge,
    ifnull((i.grand_total-i.total_charges) - ifnull(sum(rp.service_amount), 0), 0) as servicedueamount 
    FROM invoice i 
    left join invoice_other_particular iop on iop.invoice_id = i.id 
    left join receipt_particular rp on rp.invoice_id = i.id 
    WHERE i.id IN (select id from invoice where client_id='${clientid}') and i.status != 'PAID' group by i.id`;
    let invoices = await getManager().query(sql);

    return invoices;
  }
  async getClientBillingInvoices(query: FindClientBillingInvoices) {
    const invoiceIdsCondition = query.invoiceIds && query.invoiceIds.length
      ? `AND i.id IN (${query.invoiceIds.map(id => `'${id}'`).join(', ')})`
      : '';
    let sql = `SELECT
    i.id,
    i.status,
    i.invoice_number,
    i.invoice_date,
    i.invoice_due_date,
    i.grand_total,
    IFNULL((SELECT SUM(iop.amount)
            FROM invoice_other_particular iop
            WHERE iop.invoice_id = i.id), 0) AS pgamount,
    (IFNULL((SELECT SUM(iop.amount)
             FROM invoice_other_particular iop
             WHERE iop.invoice_id = i.id), 0) - IFNULL((SELECT SUM(rp.pure_agent_amount)
                                                        FROM receipt_particular rp
                                                        WHERE rp.invoice_id = i.id AND rp.status='${ReceiptCreditStatus.CREATED}'), 0)) AS pgdueamount,
    i.grand_total - i.total_charges AS servicecharge,
    IFNULL((i.grand_total - i.total_charges) - IFNULL((SELECT SUM(rp.service_amount)
                                                       FROM receipt_particular rp
                                                       WHERE rp.invoice_id = i.id AND rp.status='${ReceiptCreditStatus.CREATED}'), 0), 0) AS servicedueamount
FROM invoice i

WHERE i.id IN (SELECT id
               FROM invoice
               WHERE client_id = '${query.clientId}'
                 AND billing_entity_id = '${query.billingEntityId}')
      AND i.invoice_number LIKE '%${query.search}%'
      AND i.status NOT IN ('${InvoiceStatus.PAID}', '${InvoiceStatus.CANCELLED}')
      ${invoiceIdsCondition}
GROUP BY i.id
LIMIT ${query.pageCount ? query.pageCount : 10} OFFSET ${(query.page ? query.page : 0)};`;

    let countSql = `SELECT COUNT(DISTINCT i.id) AS total
FROM invoice i
WHERE i.id IN (
    SELECT id FROM invoice 
    WHERE client_id = '${query.clientId}' AND billing_entity_id = '${query.billingEntityId}' and i.invoice_number LIKE '%${query.search}%'
)
AND i.status NOT IN ('${InvoiceStatus.PAID}', '${InvoiceStatus.CANCELLED}')
${invoiceIdsCondition}
;`;


    // let invoices = await getManager().query(sql);

    // return invoices;
    let [invoices, totalResult] = await Promise.all([
      getManager().query(sql),
      getManager().query(countSql)
    ]);

    let totalCount = totalResult[0].total;

    return {
      totalCount,
      invoices
    };
  }

  async getTasks(query: GetUnbilledTasksDto) {
    let tasks = createQueryBuilder(Task, 'task')
      .leftJoinAndSelect('task.client', 'client')
      .leftJoinAndSelect('task.category', 'category')
      .leftJoinAndSelect('task.members', 'members')
      .leftJoinAndSelect('members.imageStorage', 'imageStorage')
      .leftJoinAndSelect('task.expenditure', 'expenditure')
      .where('client.id = :clientId', { clientId: query.client })
      .andWhere('task.status NOT IN (:status)', { status: ['deleted', 'terminated'] })
      .andWhere('task.parentTask IS NULL')
      .andWhere('(task.recurringStatus is null or task.recurringStatus = :recurringStatus)', {
        recurringStatus: TaskRecurringStatus.CREATED,
      })
      .andWhere('task.paymentStatus != :paymentStatus', { paymentStatus: 'BILLED' })
      .andWhere('task.billable IS TRUE');

    if (query.search) {
      tasks = tasks.andWhere('task.name LIKE :search', {
        search: `%${query.search}%`,
      });
    }
    tasks.orderBy('task.id', "ASC");

    if (query.offset >= 0) {
      tasks.skip(query.offset);
    }

    if (query.limit) {
      tasks.take(query.limit);
    }

    let data = await tasks.getManyAndCount();

    return {
      totalCount: data[1],
      result: data[0],
    };
  }


  async getInvoice(estimateId: number, query: any) {
    let queryConditions: QueryConditions = {
      id: estimateId
    };
    if (query.orgId) {
      queryConditions.organization = query.orgId;
    }

    if (query.clientId) {
      queryConditions.client = query.clientId;
    }

    let invoice = await Invoice.findOne({
      where: queryConditions,
      relations: [
        'billingEntity',
        'billingEntityAddress',
        'billingAddress',
        'shippingAddress',
        'client',
        'particulars',
        'otherParticulars',
        'bankDetails',
        'organization',
        'billingEntity.logStorage',
        'billingEntity.signatureStorage',
      ],
    });
    return invoice || "Un-Authorized";
  }

  async cancelInvoice(estimateId: number) {
    let invoice = await Invoice.findOne({
      where: { id: estimateId },
      relations: ['particulars', 'otherParticulars'],
    });
    invoice.status = InvoiceStatus.CANCELLED;

    if (invoice.particulars && invoice.particulars.length > 0) {
      invoice.particulars.forEach(async (particular) => {
        if (particular.taskId) {
          let task = await Task.findOne({ where: { id: particular['taskId'] } });
          task.paymentStatus = PaymentStatusEnum.UNBILLED;
          task.invoiceId = '' + invoice.id;
          await task.save();
        }
      });
    }
    await invoice.save();

    return invoice;
  }

  async downloadInvoice(invoiceId: number) {
    let url = `${process.env.WEBSITE_URL}/billing/invoices/${invoiceId}/preview?fromApi=true`;
    // let url = `http://localhost:3000/billing/invoices/${invoiceId}/preview?fromApi=true`;
    try {
const browser = await puppeteer.launch({ 
      headless: true,executablePath: '/usr/bin/chromium-browser'});
            const page = await browser.newPage();
      await page.setCacheEnabled(false);
      await page.setUserAgent(
        'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/78.0.3904.108 Safari/537.36',
      );
      await page.goto(url, { waitUntil: 'networkidle2' });
      await page.addStyleTag({ content: '#freshworks-container { display: none; }' });
      await page.addStyleTag({ content: '.hide { display: none }' });

      // await page.addStyleTag({ content: '@media print { .myDiv { break-inside: avoid; } }' });

      // await page.addStyleTag({
      //   content: '@page { size: auto; }',
      // });

      // await page.emulateMediaType('screen');
      await page.emulateMediaType('print');

      const pdf = await page.pdf({
        format: 'A4',
        printBackground: true,
        margin: {
          top: '0px',
          right: '0px',
          bottom: '0px',
          left: '0px',
        },
        scale: 0.6,
      });
      await browser.close();

      return pdf;
    } catch (e) {
      throw new InternalServerErrorException(e);
    }
  }


  async downloadInvoicewithoutEmittor(invoiceId: number) {
    let url = `${process.env.WEBSITE_URL}/billing/invoices/${invoiceId}/preview?fromApi=true`;
    // let url = `http://localhost:3000/billing/invoices/${invoiceId}/preview?fromApi=true`;
    try {
const browser = await puppeteer.launch({ headless: true,executablePath: '/usr/bin/chromium-browser',
      });      const page = await browser.newPage();
      await page.setCacheEnabled(false);
      await page.setUserAgent(
        'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/78.0.3904.108 Safari/537.36',
      );
      await page.goto(url, { waitUntil: 'networkidle2' });
      await page.addStyleTag({ content: '#freshworks-container { display: none; }' });
      await page.addStyleTag({ content: '.hide { display: none }' });

      // await page.addStyleTag({ content: '@media print { .myDiv { break-inside: avoid; } }' });

      // await page.addStyleTag({
      //   content: '@page { size: auto; }',
      // });

      // await page.emulateMediaType('screen');
      await page.emulateMediaType('print');

      const pdf = await page.pdf({
        format: 'A4',
        printBackground: true,
        margin: {
          top: '0px',
          right: '0px',
          bottom: '0px',
          left: '0px',
        },
        scale: 0.6,
      });
      await browser.close();

      return pdf;
    } catch (e) {
      console.log(e);
      throw new InternalServerErrorException(e);
    }
  }

  async export(userId: number) {
    let user = await User.findOne({
      where: { id: userId },
      relations: ['organization'],
    });

    const placeOfSupplyStates = [
      '01-Jammu & Kashmir',
      '02-Himchal Pradesh',
      '03-Punjab',
      '04-Chandigarh',
      '05-Uttarakhand',
      '06-Haryana',
      '07-Delhi',
      '08-Rajasthan',
      '09-Uttar Pradesh',
      '10-Bihar',
      '11-Sikkim',
      '12-Arunachal Pradesh',
      '13-Nagaland',
      '14-Manipur',
      '15-Mizoram',
      '16-Tripura',
      '17-Meghalaya',
      '18-Assam',
      '19-West Bengal',
      '20-Jharkhand',
      '21-Odisha',
      '22-Chhattisgarh',
      '23-Madhya Pradesh',
      '24-Gujarat',
      '25-Daman and Diu',
      '26-Dadra and Nagar Haveli and Daman and Diu',
      '27-Maharashtra',
      '29-Karnataka',
      '30-Goa',
      '31-Lakshadweep',
      '32-Kerala',
      '33-Tamil Nadu',
      '34-Puducherry',
      '35-Andaman & Nicobar Islands',
      '36-Telangana',
      '37-Andhra Pradesh',
      '38-Ladakh',
      '96-Foreign Country',
      '97-Other Territory',
    ];

    function convertToTitleCaseAndRemoveUnderscore(inputString) {
      const titleCaseWords = inputString
        .split('_')
        .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase());
      const titleCaseString = titleCaseWords.join(' ');
      return titleCaseString;
    }

    let invoices = await createQueryBuilder(Invoice, 'invoices')
      .leftJoinAndSelect('invoices.billingEntity', 'billingEntity')
      .leftJoinAndSelect('invoices.client', 'client')
      .leftJoinAndSelect('invoices.organization', 'organization')
      .where('organization.id = :orgId', { orgId: user.organization.id })
      .getMany();

    let rows = invoices.map((invoice) => {
      let addressLine1 = '';
      let addressLine2 = '';
      let addressLine3 = '';
      if (invoice?.client?.address && invoice?.client?.address['billingfulladdress']) {
        addressLine1 = invoice?.client?.address['billingfulladdress'];
      }
      return {
        'Invoice Number': invoice?.invoiceNumber,
        'Invoice Date': invoice?.invoiceDate,
        'Invoice Due Date': invoice?.invoiceDueDate,
        'Billing Entity': invoice?.billingEntity?.tradeName,
        'Client': invoice?.client?.displayName,
        'GSTIN': invoice?.client?.gstNumber,
        'Address': addressLine1,
        'State': invoice?.client?.address
          ? invoice?.client?.state
            ? invoice?.client?.['state']
            : ''
          : '',
        'Taxable Value': invoice?.subTotal,
        'Invoice Value': invoice?.grandTotal,
        'Status': invoice?.status == 'APPROVAL_PENDING' || invoice?.status == 'PARTIALLY_PAID'
          ? (invoice?.invoiceDueDate > moment().subtract(1, 'day').format("YYYY-MM-DD")
            ? (invoice?.status == 'APPROVAL_PENDING' ? ('Created')
              : (invoice?.status?.toLowerCase()))
            : ('Overdue'))
          : "Partially Paid",
      };
    });

    const args = {
      user: user,
      // invoices:invoices,
    };

    const worksheet = xlsx.utils.json_to_sheet(rows);
    const workbook = xlsx.utils.book_new();
    xlsx.utils.book_append_sheet(workbook, worksheet, 'Invoices');
    let file = xlsx.write(workbook, { type: 'buffer' });

    return file;
  }

  async submitForApproval(invoiceId: number) {
    let invoice = await Invoice.findOne({
      where: { id: invoiceId },
    });
    invoice.status = InvoiceStatus.APPROVAL_PENDING;
    await invoice.save();
    return invoice;
  }

  async getNextInvoiceNumber(userId: number, query: NextInvoiceNumberDto) {
    let billingEntity = await BillingEntity.findOne({ where: { id: query.billingEntity } });
    const prefixToMatch = query?.type === InvoiceType.INVOICE ? billingEntity?.prefix : billingEntity?.proformaPrefix;
    if (!prefixToMatch) {
      return null;
    }
    let invoicesCount = await Invoice.count({
      where: {
        billingEntity: {
          id: query.billingEntity,
        },
        invoiceNumber: Like(`${prefixToMatch}%`),
        type: query.type
      },
    });
    const prefixNumber = query?.type === InvoiceType.INVOICE ? billingEntity.prefixNumber : billingEntity.proformaPrefixNumber
    const inv = parseInt(prefixNumber) + invoicesCount;
    const zeroLength = billingEntity.prefixNumber.match(/^0*/)[0];

    return generateInvoiceId(userId, inv, query, zeroLength + '');
  }
}
