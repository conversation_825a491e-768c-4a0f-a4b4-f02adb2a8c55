// webhook.service.ts
import { BadRequestException, ConflictException, Injectable } from '@nestjs/common';
import { User, UserStatus, UserType } from '../users/entities/user.entity';
import { Organization } from '../organization/entities/organization.entity';
import Attendance, { AttendanceStatus } from './attendance.entity';
import OrganizationPreferences from '../organization-preferences/entity/organization-preferences.entity';
import axios from 'axios';
import {
  getAdminIDsBasedOnOrganizationId,
  getUserDetails,
  insertINTONotificationUpdate,
} from 'src/utils/re-use';
import * as xlsx from 'xlsx';
import ViderWhatsappSessions from '../whatsapp/entity/viderWhatsappSessions';
import { sendWhatsAppTextMessage, sendWhatsAppTextMessageWithOrgPreference } from '../whatsapp/whatsapp.service';
import LeaveApproval from './leave-approval.entity';
import { createQuery<PERSON><PERSON>er, getManager, In, MoreThanOrEqual } from 'typeorm';
import ApprovalService from './approval.service';
import { compareSpecificUrls } from 'src/utils/inv-comparision';
import { getDatesBetween } from 'src/utils/datesFormation';
import { v4 as uuidv4 } from 'uuid';
import { Cron, CronExpression } from '@nestjs/schedule';
import CronActivity from '../cron-activity/cron-activity.entity';
import { EXPENDITURE_STATUS } from '../expenditure/dto/types';
import { OrganizationPreferencesService } from '../organization-preferences/organization-preferences.service';
import NotificationPreferences from '../notification-settings/notifications-preferences.entity';
import LogNotes from '../log-hours/entity/log-notes.entity';
import * as ExcelJS from 'exceljs';
import * as moment from 'moment';

type AddressType = {
  displayName?: string;
  address?: {
    city?: string;
  };
};

async function getAllDatesAndDays(userId) {
  const userData = await Attendance.findOne({
    where: {
      userId: userId,
    },
  });
  const today = new Date();
  const year = today.getFullYear();
  const month = today.getMonth();
  const firstDayOfMonth = new Date(year, month, 1);
  const lastDayOfMonth = new Date(year, month + 1, 0);
  const result = [];
  for (let day = firstDayOfMonth; day <= lastDayOfMonth; day.setDate(day.getDate() + 1)) {
    const date = day.getDate();
    const dayOfWeek = day.toLocaleDateString('en-US', { weekday: 'long' });
    const formattedDate = `${date < 10 ? '0' : ''}${date}-${month + 1 < 10 ? '0' : ''}${month + 1
      }-${year}`;
    result.push({ date: formattedDate, day: dayOfWeek });
  }
  return result;
}

async function generateMonthArray(input, user = '', organizationpreferences) {
  const [monthStr, yearStr] = input.split('-');
  const month = parseInt(monthStr, 10);
  const year = parseInt(yearStr, 10);
  if (isNaN(month) || isNaN(year)) {
    return [];
  }
  const firstDayOfMonth = new Date(year, month - 1, 1);
  const lastDayOfMonth = new Date(year, month, 0);
  const daysInMonth = lastDayOfMonth.getDate();
  const monthArray = [];

  for (let day = 1; day <= daysInMonth; day++) {
    const currentDate = new Date(year, month - 1, day);
    const dayOfWeek = currentDate.toLocaleDateString('en-US', { weekday: 'long' });
    const formattedDate = `${day.toString().padStart(2, '0')}-${month
      .toString()
      .padStart(2, '0')}-${year}`;
    monthArray.push({
      date: `${day.toString().padStart(2, '0')}-${month.toString().padStart(2, '0')}-${year}`,
      day: dayOfWeek,
    });
  }

  const attendance = await Attendance.find({
    where: { userId: user },
    order: { id: 'DESC' },
    relations: ['organization'],
  });

  const returningData = monthArray.filter((item, index) => {
    const filterDate = attendance.filter((attendenceitem) => {
      const formattedDate = moment(attendenceitem.attendanceDate).format('DD-MM-YYYY');
      return formattedDate === item.date;
    });
    if (filterDate.length) {
      if (filterDate[0].checkin_time && filterDate[0].checkout_time === 'Invalid date') {
        let checkInTimeUpdate;
        if (filterDate[0].checkin_time === 'Invalid date') {
          checkInTimeUpdate = '--.--';
        } else {
          checkInTimeUpdate = filterDate[0].checkin_time;
        }
        let checkOutTimeUpdate;
        if (filterDate[0].checkout_time === 'Invalid date') {
          checkOutTimeUpdate = '--.--';
        } else {
          checkOutTimeUpdate = filterDate[0].checkout_time;
        }
        monthArray[index] = {
          ...item,
          type: filterDate[0].type,
          description: filterDate[0].description,
          checkInTime: checkInTimeUpdate,
          checkOutTime: checkOutTimeUpdate,
          hours: filterDate[0].hours_logged.slice(0, 5),
          checkInCoordinates: filterDate[0].checkInCoordinates,
          checkInAddress: filterDate[0].checkInAddress,
          checkOutAddress: filterDate[0].checkOutAddress,
          checkOutCoordinates: filterDate[0].checkOutCoordinates,
          status: filterDate[0].status,
        };
      } else {
        let checkInTimeUpdate;
        if (filterDate[0].checkin_time === 'Invalid date') {
          checkInTimeUpdate = '--.--';
        } else {
          checkInTimeUpdate = filterDate[0].checkin_time;
        }
        monthArray[index] = {
          ...item,
          type: filterDate[0].type,
          description: filterDate[0].description,
          checkInTime: checkInTimeUpdate,
          checkOutTime: filterDate[0].checkout_time,
          hours: filterDate[0]?.hours_logged?.slice(0, 5),
          checkInCoordinates: filterDate[0].checkInCoordinates,
          checkInAddress: filterDate[0].checkInAddress,
          checkOutAddress: filterDate[0].checkOutAddress,
          checkOutCoordinates: filterDate[0].checkOutCoordinates,
          status: filterDate[0].status,
        };
      }
    } else {
      monthArray[index] = {
        ...item,
        type: '',
        description: '',
        checkInTime: '',
        checkOutTime: '',
        hours: '',
        checkInCoordinates: '',
        checkInAddress: '',
        checkOutAddress: '',
        checkOutCoordinates: '',
        status: '',
      };
    }
  });
  const finalDataArray = [];
  const holidayDataMap = new Map();

  // Extract the holiday dates and descriptions from organizationpreferences.holidayPreferences
  for (const key in organizationpreferences.holidayPreferences) {
    if (organizationpreferences.holidayPreferences.hasOwnProperty(key)) {
      const holidayPreferenceItem = organizationpreferences.holidayPreferences[key];
      holidayDataMap.set(holidayPreferenceItem.date, holidayPreferenceItem.text);
    }
  }
  for (const monthItem of monthArray) {
    const formattedDate = moment(monthItem.date, 'DD-MM-YYYY').format('YYYY-MM-DD');

    if (holidayDataMap.has(formattedDate)) {
      monthItem.type = 'Holiday';
      monthItem.description = holidayDataMap.get(formattedDate);
    }

    finalDataArray.push(monthItem);
  }

  return finalDataArray;
}
@Injectable()
export class AttendanceService {
  constructor(
    private approvalService: ApprovalService,
    private organizationPreferencesService: OrganizationPreferencesService,
  ) { }

  async getDates(date, user, userId) {
    let user1 = await User.findOne({ where: { id: userId }, relations: ['organization'] });
    let organization = await Organization.findOne({ where: { id: user1.organization.id } });
    const organizationPreferences = await OrganizationPreferences.findOne({
      where: { organization },
      order: { id: 'DESC' },
    });

    if (!date && !user) {
      return getAllDatesAndDays(userId);
    } else if (date && !user) {
      return generateMonthArray(date, user, organizationPreferences);
    } else if (date && user) {
      return generateMonthArray(date, user, organizationPreferences);
    }
  }

  async getAddDates(date, user, userId) {
    try {
      const attendence = await Attendance.findOne({
        where: { userId: user, attendanceDate: date || moment().format('YYYY-MM-DD') },
      });
      return attendence;
    } catch (error) {
      console.error('Error fetching attendance:', error?.message);
    }
  }

  async get(userId: number) {
    let user = await User.findOne({ where: { id: userId }, relations: ['organization'] });

    let organization = await Organization.findOne({ where: { id: user.organization.id } });

    const attendance = await Attendance.find({
      where: { organization },
      order: { id: 'DESC' },
      relations: ['organization', 'members'],
    });
    return attendance;
  }

  async getAttendanceReport(userId: number, body: any) {
    const { date } = body; // e.g., "09-2025"
    const [month, year] = date.split('-');

    // Fetch attendances for that month/year
    const attendances = await Attendance.createQueryBuilder('attendance')
      .where('attendance.userId = :userId', { userId }) // use property name
      .andWhere('EXTRACT(MONTH FROM attendance.attendanceDate) = :month', { month })
      .andWhere('EXTRACT(YEAR FROM attendance.attendanceDate) = :year', { year })
      .orderBy('attendance.attendanceDate', 'ASC')
      .getMany();

    if (!attendances || !attendances.length) {
      throw new BadRequestException('No Data for Export');
    }

    const workbook = new ExcelJS.Workbook();
    const userSheet = workbook.addWorksheet('Attendance Report');

    const headers = [
      'Date', 'Day', 'Description', 'Check In Time', 'Check In Address',
      'Check Out Time', 'Check Out Address', 'Working Hours (HH:MM)',
      'Remarks', 'Status',
    ];
    userSheet.addRow(headers);

    userSheet.getRow(1).eachCell((cell) => {
      cell.font = { bold: true };
      cell.alignment = { horizontal: 'center', vertical: 'middle' };
    });

    const STANDARD_HOURS = 8;

    const statusMap = {
      REJECTED: "Rejected",
      APPROVED: "Approved",
      PENDING: "Pending",
    };

    const rows = attendances.map((a) => {
      const checkInAddr = a.checkInAddress as any;
      const checkOutAddr = a.checkOutAddress as any;

      const attendanceDate = a.attendanceDate
        ? moment(a.attendanceDate).format("DD-MM-YYYY")
        : "";

      const checkInTime = a.checkin_time
        ? moment(a.checkin_time, "YYYY-MM-DD HH:mm:ss").format("HH:mm")
        : "--:--";

      const checkOutTime = a.checkout_time
        ? moment(a.checkout_time, "YYYY-MM-DD HH:mm:ss").format("HH:mm")
        : "--:--";

      let remarks = "";
      if (a.hours_logged) {
        const [hours, minutes] = a.hours_logged.split(":").map(Number);
        const totalHours = hours + minutes / 60;
        if (totalHours > STANDARD_HOURS) {
          const diffHours = totalHours - STANDARD_HOURS;
          const diffH = Math.floor(diffHours);
          const diffM = Math.round((diffHours - diffH) * 60);
          remarks = `Overrated (${diffH}:${diffM.toString().padStart(2, "0")})`;
        } else if (totalHours < STANDARD_HOURS) {
          const diffHours = STANDARD_HOURS - totalHours;
          const diffH = Math.floor(diffHours);
          const diffM = Math.round((diffHours - diffH) * 60);
          remarks = `Underrated (${diffH}:${diffM.toString().padStart(2, "0")})`;
        } else {
          remarks = "On Time";
        }
      }

      const location = checkInAddr?.city || checkOutAddr?.city || "-";

      return [
        attendanceDate,
        moment(a.attendanceDate).format("dddd"),
        a.type,
        checkInTime,
        checkInAddr?.displayName || "",
        checkOutTime,
        checkOutAddr?.displayName || "",
        a.hours_logged || "--:--",
        remarks,
        statusMap[a.status] || "-",  // <-- Map status here
        location,
      ];
    });


    userSheet.addRows(rows);

    userSheet.columns = [
      { width: 15 }, { width: 12 }, { width: 15 }, { width: 15 }, { width: 40 },
      { width: 15 }, { width: 40 }, { width: 20 }, { width: 25 }, { width: 15 }, { width: 20 },
    ];

    userSheet.eachRow({ includeEmpty: true }, (row, rowNumber) => {
      if (rowNumber > 1) {
        row.eachCell((cell) => {
          cell.alignment = { horizontal: 'center', vertical: 'middle' };
        });
      }
    });

    const fileBuffer = await workbook.xlsx.writeBuffer();
    return { fileBuffer, month, year };
  }


  async getNotes(userId: number, query: any) {
    const sevenDaysAgo = new Date();
    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
    const user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
    const notes = await LogNotes.find({ where: { user: { id: query?.selectedUser || user.id, createdAt: MoreThanOrEqual(sevenDaysAgo), } }, order: { createdAt: 'DESC' } });
    return notes;
  }

  async addAttendance(userId: number, data: any) {
    if (data.type.value === AttendanceStatus.Leave) {
      const dates = getDatesBetween(data.startDate, data.endDate);
      const leaveRequestId = uuidv4();
      if (dates.length) {
        for (let date of dates) {
          await this.post(userId, { ...data, attendanceDate: date, leaveRequestId });
        }
      }
    } else {

      return await this.post(userId, data);
    }

    return true;
  }

  async post(userId, data) {
    let loginuser = await User.findOne({
      where: { id: userId },
      relations: ['organization', 'organization.organizationPreferences'],
    });
    let organization = await Organization.findOne({ where: { id: loginuser.organization.id } });
    const formattedDate = moment(data.attendanceDate).format('YYYY-MM-DD');
    const getDuplicate = await Attendance.find({
      where: { organization, attendanceDate: formattedDate, userId: data.addedUserId },
      order: { id: 'DESC' },
      relations: ['organization'],
    });
    if (getDuplicate.length) {
      const id = getDuplicate[0].id;
      let attendance = await Attendance.findOne({
        where: { id },
        relations: ['organization'],
      });
      if (getDuplicate[0].status === EXPENDITURE_STATUS.APPROVED) {
        throw new ConflictException("Can't Change Approved Attenance");
      }
      attendance.organization = loginuser.organization;
      attendance.type = data.type.value;
      attendance.userId = data.addedUserId;
      attendance.attendanceDate = moment(data.attendanceDate).format('YYYY-MM-DD');
      attendance.createdBy = data.createdBy;
      if (data.type.value === 'Present') {
        attendance.checkin_time = data.checkInTime;
        attendance.checkout_time = data.checkoutime;
        attendance.hours_logged = data.formattedDuration;
        attendance.description = null;
      } else if (data.type.value === AttendanceStatus.Leave) {
        // const isFutureDate = moment(data.attendanceDate).isAfter(moment(), 'day');
        const isFutureDate = moment(data.attendanceDate).isSameOrAfter(moment(), 'day');
        if (!isFutureDate) {
          throw new ConflictException("Can't Request Past Dates");
        }

        const entityManager = getManager();
        let adminUser = await createQueryBuilder(User, 'user')
          .leftJoinAndSelect('user.organization', 'organization')
          .leftJoinAndSelect('user.role', 'role')
          .where('organization.id = :id', { id: organization.id })
          .andWhere('role.defaultRole = :defaultRole', { defaultRole: 1 })
          .getOne();
        const result = await entityManager.query(
          `
          WITH RECURSIVE manager_hierarchy AS (
              -- Base case: Start with the given user_id
              SELECT user_id, manager_id
              FROM organization_hierarchy
              WHERE user_id = ?
              
              UNION ALL
              
              -- Recursive case: Fetch the next level manager until manager_id = 123826
              SELECT oh.user_id, oh.manager_id
              FROM organization_hierarchy oh
              INNER JOIN manager_hierarchy mh ON oh.user_id = mh.manager_id
              WHERE mh.manager_id != ? -- Stop recursion when manager_id = 123826
          )
          -- Final output: Get all user_id in the hierarchy
          SELECT user_id
          FROM manager_hierarchy
      `,
          [data.addedUserId, adminUser.id],
        );

        if (loginuser.organization.organizationPreferences?.[0]?.approvals?.['attendance']) {
          const mangers = result?.map((result) => result.user_id);
          mangers.push(adminUser.id);
          const filterReqUser = mangers.filter((user) => user != data.addedUserId);
          const users = await User.find({
            where: {
              id: In(filterReqUser),
            },
          });

          if (attendance?.status == 'APPROVED') {
            throw new ConflictException("Can't edit Approved ");
          }
          if (data.addedUserId != adminUser.id) {
            attendance.status = EXPENDITURE_STATUS.PENDING;
            attendance.managers = users;
            attendance.leaveRequestId = data.leaveRequestId;
            attendance.requestedAt = moment().format('YYYY-MM-DD HH:mm:ss.SSSSSS');
          } else if (data.addedUserId == adminUser.id) {
            attendance.status = EXPENDITURE_STATUS.APPROVED;
          }
        }
        attendance.description = data.description;
        attendance.userId = data.addedUserId;

        attendance.reviewer = null;
        attendance.reviewedAt = null;
      } else {
        attendance.checkin_time = null;
        attendance.checkout_time = null;
        attendance.hours_logged = null;
        attendance.description = data.description;
      }
      await attendance.save();
      return attendance;
    } else {
      const attendance = new Attendance();
      attendance.userId = data.addedUserId;
      attendance.type = data.type.value;
      attendance.attendanceDate = moment(data.attendanceDate).format('YYYY-MM-DD');
      attendance.organization = loginuser.organization;
      attendance.createdBy = data.createdBy;
      if (data.type.value === 'Present') {
        attendance.checkin_time = data.checkInTime;
        attendance.checkout_time = data.checkoutime;
        attendance.hours_logged = data.formattedDuration;
        attendance.description = null;
      } else if (data.type.value === AttendanceStatus.Leave) {
        // const isFutureDate = moment(data.attendanceDate).isAfter(moment(), 'day');
        const isFutureDate = moment(data.attendanceDate).isSameOrAfter(moment(), 'day');
        if (!isFutureDate) {
          throw new ConflictException("Can't Request Past Dates");
        }
        if (loginuser.organization.organizationPreferences?.[0]?.approvals?.['attendance']) {
          let adminUser = await createQueryBuilder(User, 'user')
            .leftJoinAndSelect('user.organization', 'organization')
            .leftJoinAndSelect('user.role', 'role')
            .where('organization.id = :id', { id: organization.id })
            .andWhere('role.defaultRole = :defaultRole', { defaultRole: 1 })
            .getOne();
          const entityManager = getManager();
          const result = await entityManager.query(
            `
          WITH RECURSIVE manager_hierarchy AS (
              -- Base case: Start with the given user_id
              SELECT user_id, manager_id
              FROM organization_hierarchy
              WHERE user_id = ?
              
              UNION ALL
              
              -- Recursive case: Fetch the next level manager until manager_id = 123826
              SELECT oh.user_id, oh.manager_id
              FROM organization_hierarchy oh
              INNER JOIN manager_hierarchy mh ON oh.user_id = mh.manager_id
              WHERE mh.manager_id != ? -- Stop recursion when manager_id = admin_id
          )
          -- Final output: Get all user_id in the hierarchy
          SELECT user_id
          FROM manager_hierarchy
      `,
            [data.addedUserId, adminUser.id],
          );

          const mangers = result?.map((result) => result.user_id);
          mangers.push(adminUser.id);
          const filterReqUser = mangers.filter((user) => user != data.addedUserId);
          const users = await User.find({
            where: {
              id: In(filterReqUser),
            },
          });
          if (data.addedUserId != adminUser.id) {
            attendance.status = EXPENDITURE_STATUS.PENDING;
            attendance.managers = users;
            attendance.leaveRequestId = data.leaveRequestId;
            attendance.requestedAt = moment().format('YYYY-MM-DD HH:mm:ss.SSSSSS');
          } else if (data.addedUserId == adminUser.id) {
            attendance.status = EXPENDITURE_STATUS.APPROVED;
          }
        }
        attendance.description = data.description;
        attendance.userId = data.addedUserId;
      } else {
        attendance.checkin_time = null;
        attendance.checkout_time = null;
        attendance.hours_logged = null;
        attendance.description = data.description;
      }
      await attendance.save();
      return attendance;
    }
  }

  async getUserDailyAttendance(userId: number, query: any) {
    let user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
    if (user) {
      const userAttendence = await Attendance.findOne({
        where: { userId, organization: user.organization, attendanceDate: query?.date },
      });
      if (userAttendence) {
        return userAttendence;
      } else {
        return false;
      }
    }
  }

  async getAddressInfo(latitude: any, longitude: any) {
    try {
      const response = await axios.get(
        `https://nominatim.openstreetmap.org/reverse?format=json&lat=${latitude}&lon=${longitude}`,
        {
          headers: {
            'Referer': 'https://www.google.com',
            'User-Agent': 'google',
          },
        },
      );
      const data = response.data;
      const address = {
        address: data.address,
        displayName: data.display_name,
      };
      return address;
    } catch (error) {
      console.error('Error getting address:', error);
      return null;
    }
  }

  async getIndianDataTime(userId: number, query: any) {
    try {
      let orgPreference = null;
      let standardWorkingHours: number = 0;
      let loggedDuration: number = 0;
      const istTime = moment().tz('Asia/Kolkata').format('YYYY-MM-DD HH:mm:ss');
      if (query.type === 'CheckOut') {
        orgPreference = await this.organizationPreferencesService.get(userId);

        const holidayPreferences: any = orgPreference?.holidayPreferences;

        if (holidayPreferences) {
          const updateOvertime = holidayPreferences?.updateovertime;
          if (updateOvertime) {
            const hoursInMilliseconds = parseInt(updateOvertime.hours.value) * 60 * 60 * 1000;
            const minutesInMilliseconds = parseInt(updateOvertime.minutes.value) * 60 * 1000;
            standardWorkingHours = hoursInMilliseconds + minutesInMilliseconds;
          }
        }

        if (userId) {
          const entityManager = getManager();

          const query = `
            SELECT SUM(duration) AS totalDuration 
            FROM log_hour 
            WHERE user_id = ? and completed_date = ?
          `;

          const params = [userId, moment(istTime).format('YYYY-MM-DD')];
          const [result] = await entityManager.query(query, params);
          loggedDuration = result?.totalDuration;
        }

      }
      return {
        time: istTime,
        type: query.type,
        orgPreference,
        standardWorkingHours,
        loggedDuration,
      };
    } catch (error) {
    }
  }

  async UserClickCheckIn(userId: number, body: any) {
    try {
      let user: any = await User.findOne({ where: { id: userId }, relations: ['organization'] });
      if (body?.hasTime) {

        const checkInTime = body?.clickTime;
        const presentDate = body?.presentDate;
        const checkInCoordinates = { latitude: body?.latitude, longitude: body?.longitude };
        const attendenceStatus = AttendanceStatus.Present;
        const userAttendence = await Attendance.findOne({
          where: { userId, organization: user.organization, attendanceDate: presentDate },
        });

        const address = await this.getAddressInfo(body?.latitude, body?.longitude);
        if (userAttendence) {
        } else {
          const attendance = new Attendance();
          attendance.userId = userId;
          attendance.attendanceDate = presentDate;
          attendance.checkin_time = checkInTime;
          attendance.type = attendenceStatus;
          attendance.createdBy = user;
          attendance.checkInCoordinates = checkInCoordinates;
          attendance.organization = user.organization;
          attendance.checkInAddress = address;
          await attendance.save();
          if (attendance && user) {
            const title = 'User Attendance';
            const body = `Attendance confirmed, ${user?.fullName}! Remember to check out when you're done for the day.`;
            const orgId = user?.organization?.id;
            const key = 'ATTENDANCE_PUSH';
            const users: any = [userId];
            insertINTONotificationUpdate(title, body, users, orgId, key); // Used 'users' instead of 'user'

            // admin notification
            const AdminIds = await getAdminIDsBasedOnOrganizationId(user?.organization?.id);

            const adminBody = `${user?.fullName} has clocked in, making every minute count for the Organization.`;
            insertINTONotificationUpdate(title, adminBody, AdminIds, orgId, key);
            // whatsapp
            //         try {
            //           if (AdminIds !== undefined) {
            //             const sessionValidation = await ViderWhatsappSessions.findOne({
            //               where: { userId: AdminIds },
            //             });
            //             if (sessionValidation) {
            //               const adminUserDetails = await getUserDetails(AdminIds);

            //               const { full_name: userFullName, mobile_number: userPhoneNumber, id, organization_id } = adminUserDetails;
            //               const key = 'CHECKIN_CHECKOUT_WHATSAPP';
            //               const whatsappMessageBody = `
            //   Hi ${userFullName}

            //  ${user?.fullName} has clocked in, making every minute count for the Organization.

            //   We hope this helps!`;
            //               await sendWhatsAppTextMessage(
            //                 `91${userPhoneNumber}`,
            //                 whatsappMessageBody,
            //                 organization_id,
            //                 title,
            //                 id,
            //                 key,
            //               );
            //             }

            //           }
            //         } catch (error) {
            //           console.error('Error sending User WhatsApp notification:', error);
            //         }
          }
        }
      }

      if (body.notes?.length) {
        const notesToCreate: LogNotes[] = [];
        const notesToUpdate: LogNotes[] = [];

        for (const note of body.notes) {
          if (typeof note.id === 'number') {
            // Existing note - fetch from DB, then update
            const existingNote = await LogNotes.findOne({ where: { id: note.id } });
            if (existingNote) {
              existingNote.title = note.title;
              existingNote.description = note.description;
              existingNote.isCompleted = note.isCompleted ?? false;
              notesToUpdate.push(existingNote);
            }
          } else {
            // New note - create
            const logNote = new LogNotes();
            logNote.title = note.title;
            logNote.description = note.description;
            logNote.user = user;
            logNote.isCompleted = note.isCompleted ?? false;
            notesToCreate.push(logNote);
          }
        }

        if (notesToCreate.length > 0) {
          await LogNotes.save(notesToCreate);
        }

        if (notesToUpdate.length > 0) {
          await LogNotes.save(notesToUpdate);
        }
      }
      return { success: true, hasTime: body?.hasTime };

    } catch (error) {
    }
  }

  async calculateDuration(checkin_time: any, checkoutime: any) {
    const startTimeFormatted = moment(checkin_time);
    const endTimeFormatted = moment(checkoutime);
    const duration = moment.duration(endTimeFormatted.diff(startTimeFormatted));
    const hours = Math.floor(duration.asHours());
    const minutes = duration.minutes();
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;
  }

  async UserClickCheckOut(userId: number, body: any) {
    try {
      let user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
      const checkoutime = body?.clickTime;
      const presentDate = body?.presentDate;
      const duration = body?.duration;
      const checkOutCoordinates = { latitude: body?.latitude, longitude: body?.longitude };
      const userAttendence = await Attendance.findOne({
        where: { userId, organization: user.organization, attendanceDate: presentDate },
      });
      const checkOutAddress = await this.getAddressInfo(body?.latitude, body?.longitude);
      if (userAttendence) {
        // const checkin_time = userAttendence.checkin_time;
        // const formattedDuration = await this.calculateDuration(checkin_time, checkoutime);
        userAttendence.checkout_time = checkoutime;
        userAttendence.hours_logged = duration;
        userAttendence.checkOutAddress = checkOutAddress;
        userAttendence.checkOutCoordinates = checkOutCoordinates;
        await userAttendence.save();

        if (userAttendence && user) {
          // const userDetails: any = getUserDetails(userId);
          const title = 'User Attendance';
          const body = `Thanks for your hard work, ${user?.fullName}! check out complete. Remember, timesheet submission helps us appreciate your efforts.`;
          const orgId = user?.organization?.id;
          const key = 'ATTENDANCE_PUSH';
          const users: any = [userId]; // Renamed the variable to 'users'
          insertINTONotificationUpdate(title, body, users, orgId, key); // Used 'users' instead of 'user'

          // admin notification
          const AdminIds = await getAdminIDsBasedOnOrganizationId(user?.organization?.id);

          const adminBody = `${user?.fullName} has clocked out, leaving a trail of completed tasks.`;
          insertINTONotificationUpdate(title, adminBody, AdminIds, orgId, key);
          //whatsapp
          try {
            if (AdminIds !== undefined) {
              const sessionValidation = await ViderWhatsappSessions.findOne({
                where: { userId: AdminIds, status: 'ACTIVE' },
              });
              if (sessionValidation) {
                const adminUserDetails = await getUserDetails(AdminIds);

                const {
                  full_name: userFullName,
                  mobile_number: userPhoneNumber,
                  id,
                  organization_id,
                } = adminUserDetails;
                const key = 'CHECKIN_CHECKOUT_WHATSAPP';
                const whatsappMessageBody = `
            Hi ${userFullName}

      ${user?.fullName} has clocked out, leaving a trail of completed tasks.
            We hope this helps!`;
                await sendWhatsAppTextMessage(
                  `91${userPhoneNumber}`,
                  whatsappMessageBody,
                  organization_id,
                  title,
                  id,
                  key,
                );
              }
            }
          } catch (error) {
            console.error('Error sending User WhatsApp notification:', error);
          }
        }
      }

      if (body.notes?.length) {
        const notesToCreate: LogNotes[] = [];
        const notesToUpdate: LogNotes[] = [];

        for (const note of body.notes) {
          if (typeof note.id === 'number') {
            const existingNote = await LogNotes.findOne({ where: { id: note.id } });
            if (existingNote) {
              existingNote.title = note.title;
              existingNote.description = note.description;
              existingNote.isCompleted = note.isCompleted ?? false;
              notesToUpdate.push(existingNote);
            }
          } else {
            const logNote = new LogNotes();
            logNote.title = note.title;
            logNote.description = note.description;
            logNote.user = user;
            logNote.isCompleted = note.isCompleted ?? false;
            notesToCreate.push(logNote);
          }
        }

        if (notesToCreate.length > 0) {
          await LogNotes.save(notesToCreate);
        }

        if (notesToUpdate.length > 0) {
          await LogNotes.save(notesToUpdate);
        }
      }


    } catch (error) {
    }
  }

  async approvalsStatusChange(userId: number, body: any) {
    const user = await User.findOne(userId);
    const attendances = await Attendance.find({
      where: {
        id: In(body.leaveIds),
        status: 'REJECTED',
      },
    });
    if (attendances.length) {
      throw new ConflictException("Can't Approve Declined Requests");
    }
    const attendancesApproved = await Attendance.find({
      where: {
        id: In(body.leaveIds),
        status: 'APPROVED',
      },
    });

    if (attendancesApproved.length) {
      throw new ConflictException("Can't Re Approved");
    }

    // const update = await Attendance.update(
    //   { id: In(body.leaveIds) },
    //   {
    //     status: body?.status,
    //     reviewer: user,
    //     reviewedAt: moment().format('YYYY-MM-DD HH:mm:ss.SSSSSS')
    //   }
    // );

    const attendanceRecords = await Attendance.find({
      where: { id: In(body.leaveIds) },
      relations: ['managers'],
    });

    attendanceRecords.forEach((record) => {
      record.status = body?.status;
      record.reviewer = user;
      record.reviewedAt = moment().format('YYYY-MM-DD HH:mm:ss.SSSSSS');
      record.approvalDescription = body.description;
      record['loginUser'] = userId;
    });
    await Attendance.save(attendanceRecords);

    return true;
  }

  @Cron(CronExpression.EVERY_DAY_AT_6PM)
  async changeIsMarkAbsentAttendanceData() {
    if (process.env.Cron_Running === 'true') {
      const cronData = new CronActivity();
      cronData.cronType = 'Absent Mark Cron';
      cronData.cronDate = moment().toDate().toString();
      cronData.startTime = moment().format('YYYY-MM-DD HH:mm:ss');
      const cornActivityID = await cronData.save();

      const errorList = [];

      try {
        const totalOrganization = await Organization.createQueryBuilder('organization')
          .leftJoinAndSelect('organization.users', 'user')
          .leftJoinAndSelect('organization.organizationPreferences', 'organizationPreferences')
          .where(
            "DATE_FORMAT(STR_TO_DATE(JSON_UNQUOTE(JSON_EXTRACT(config, '$.expirydate')), '%Y-%m-%d'), '%Y-%m-%d') >= :expirydate",
            { expirydate: moment().format('YYYY-MM-DD') },
          )
          .andWhere(
            "JSON_EXTRACT(organizationPreferences.holidayPreferences, '$.isMarkAbsent') = 'YES'",
          )
          .andWhere('user.status = :status', { status: 'active' })
          .andWhere('user.type = :type', { type: UserType.ORGANIZATION })
          .getMany();
        for (let organization of totalOrganization) {
          const users = organization?.users;
          const date = new Date();
          const todayDate = moment(date).format('YYYY-MM-DD');
          const todayDay = new Date().toLocaleString('en-US', { weekday: 'long' }).toLowerCase();

          const organizationPreferences: any = organization?.organizationPreferences;

          if (organizationPreferences) {
            const orgPreferences = organizationPreferences[0];
            if (orgPreferences) {
              const { addholiday, updateweekend } = orgPreferences?.holidayPreferences || {};

              // Check if today is a holiday
              const holidayInfo = addholiday?.find(
                (holiday: { date: string }) => holiday.date.split('T')[0] === todayDate,
              );

              const isHoliday = !!holidayInfo;

              // Check if today is a weekend
              const isWeekend = updateweekend?.some(
                (weekend: { value: string }) => weekend.value.toLowerCase() === todayDay,
              );
              for (let user of users) {
                const attendanceData = await Attendance.findOne({
                  where: {
                    userId: user.id,
                    attendanceDate: todayDate,
                  },
                });

                if (attendanceData) {
                } else {
                  if (isHoliday) {
                    const attendance = new Attendance();
                    attendance.attendanceDate = moment(todayDate).format('YYYY-MM-DD');
                    attendance.type = AttendanceStatus.Holiday;
                    attendance.userId = user.id;
                    attendance.description = holidayInfo.holiday;
                    attendance.createdBy = user;
                    attendance.organization = organization;
                    await attendance.save();
                  } else if (isWeekend) {
                    const attendance = new Attendance();
                    attendance.attendanceDate = moment(todayDate).format('YYYY-MM-DD');
                    attendance.type = AttendanceStatus.Holiday;
                    attendance.userId = user.id;
                    attendance.description = 'Weekend';
                    attendance.createdBy = user;
                    attendance.organization = organization;
                    await attendance.save();
                  } else {
                    const attendance = new Attendance();
                    attendance.attendanceDate = moment(todayDate).format('YYYY-MM-DD');
                    attendance.type = AttendanceStatus.Absent;
                    attendance.userId = user.id;
                    attendance.description = 'Employee did not mark attendance';
                    attendance.createdBy = user;
                    attendance.organization = organization;
                    await attendance.save();
                  }
                }
              }
            }
          } else {
          }
        }
      } catch (error) {
        errorList.push(error.message);
        const getcornActivityID = await createQueryBuilder(CronActivity, 'cronActivity')
          .where('id = :id', { id: cornActivityID?.id })
          .getOne();
        getcornActivityID.responseData = errorList.length ? errorList.join(',') : 'Success';
        getcornActivityID.endTime = moment().format('YYYY-MM-DD HH:mm:ss');
        await getcornActivityID.save();
        return console.log(error.message);
      }
      const getcornActivityID = await createQueryBuilder(CronActivity, 'cronActivity')
        .where('id = :id', { id: cornActivityID?.id })
        .getOne();
      getcornActivityID.responseData = errorList.length ? errorList.join(',') : 'Success';
      getcornActivityID.endTime = moment().format('YYYY-MM-DD HH:mm:ss');
      await getcornActivityID.save();
    }
  }

  // @Cron(CronExpression.EVERY_MINUTE)
  @Cron(CronExpression.EVERY_DAY_AT_5AM)
  // @Cron(CronExpression.EVERY_30_MINUTES)

  //everyday at 10:30am
  async sendReminderforcheckin() {
    if (process.env.Cron_Running === 'true') {
      const cronData = new CronActivity();
      cronData.cronType = 'sendReminderforcheckin';
      cronData.cronDate = moment().toDate().toString();
      cronData.startTime = moment().format('YYYY-MM-DD HH:mm:ss');
      const cornActivityID = await cronData.save();
      const errorList = [];

      const usersWithReminderPref = await createQueryBuilder(OrganizationPreferences, 'organizationPreferences')
        // .leftJoin('organizationPreferences.organization', 'organization') // relation name in your entity
        .select(['organizationPreferences.organization_id as orgId'])
        .where(
          "JSON_UNQUOTE(JSON_EXTRACT(JSON_UNQUOTE(organizationPreferences.whatsapp), '$.ATTENDANCE_CHECKINCHECKOUT_REMINDER_WHATSAPP')) = :reminder",
          { reminder: 'ATTENDANCE CHECKIN/CHECKOUT REMINDER' }
        )
        .getRawMany();
      if (!usersWithReminderPref.length) {
        const getcornActivityID = await createQueryBuilder(CronActivity, 'cronActivity')
          .where('id = :id', { id: cornActivityID?.id })
          .getOne();
        getcornActivityID.responseData = errorList.length ? errorList.join(',') : 'Success';
        getcornActivityID.endTime = moment().format('YYYY-MM-DD HH:mm:ss');
        await getcornActivityID.save();
        return;
      }

      for (let i of usersWithReminderPref) {
        const usersData = await User.find({ where: { organization: i.orgId, status: UserStatus.ACTIVE, type: "organization" } })
        for (let i of usersData) {
          const userId = i.id;

          const today = new Date().toISOString().split('T')[0];
          const title = "Checkin_Reminder"
          const attendanceExists = await createQueryBuilder('attendance', 'a')
            .where('a.user_id = :userId', { userId })
            .andWhere('DATE(a.attendance_date) = :today', { today })
            .getCount();

          if (!attendanceExists) {
            console.log('User ID with reminder preference and attendance today:', userId);
            try {
              if (userId !== undefined) {
                const sessionValidation = await ViderWhatsappSessions.findOne({
                  where: { userId: userId, status: 'ACTIVE' },
                });
                if (sessionValidation) {
                  const userDetails = await getUserDetails(userId);

                  const {
                    full_name: userFullName,
                    mobile_number: userPhoneNumber,
                    id,
                    organization_id,
                  } = userDetails;
                  const key = 'ATTENDANCE_CHECKINCHECKOUT_REMINDER_WHATSAPP';
                  const whatsappMessageBody = `
Hi ${userFullName},

It looks like you haven't checked in yet today.

Please remember to Check-In on the ATOM to mark your attendance.

We hope this helps`;
                  await sendWhatsAppTextMessageWithOrgPreference(
                    `91${userPhoneNumber}`,
                    whatsappMessageBody,
                    organization_id,
                    title,
                    id,
                    key,
                  );
                }
              }
            } catch (error) {
              errorList.push(error.message);
              console.error('Error sending User WhatsApp notification:', error);
            }
          }
        }
      }
      const getcornActivityID = await createQueryBuilder(CronActivity, 'cronActivity')
        .where('id = :id', { id: cornActivityID?.id })
        .getOne();
      getcornActivityID.responseData = errorList.length ? errorList.join(',') : 'Success';
      getcornActivityID.endTime = moment().format('YYYY-MM-DD HH:mm:ss');
      await getcornActivityID.save();
    }
  }

  // @Cron(CronExpression.EVERY_MINUTE)
  //everyday at 7:30pm
  @Cron(CronExpression.EVERY_DAY_AT_2PM)

  async sendReminderforcheckout() {
    if (process.env.Cron_Running === 'true') {
      const cronData = new CronActivity();
      cronData.cronType = 'sendReminderforcheckout';
      cronData.cronDate = moment().toDate().toString();
      cronData.startTime = moment().format('YYYY-MM-DD HH:mm:ss');
      const cornActivityID = await cronData.save();
      const errorList = [];

      const usersWithReminderPref = await createQueryBuilder(OrganizationPreferences, 'organizationPreferences')
        // .leftJoin('organizationPreferences.organization', 'organization') // relation name in your entity
        .select(['organizationPreferences.organization_id as orgId'])
        .where(
          "JSON_UNQUOTE(JSON_EXTRACT(JSON_UNQUOTE(organizationPreferences.whatsapp), '$.ATTENDANCE_CHECKINCHECKOUT_REMINDER_WHATSAPP')) = :reminder",
          { reminder: 'ATTENDANCE CHECKIN/CHECKOUT REMINDER' }
        )
        .getRawMany();
      // console.log(usersWithReminderPref,'usersWithReminderPref')
      if (!usersWithReminderPref.length) {
        const getcornActivityID = await createQueryBuilder(CronActivity, 'cronActivity')
          .where('id = :id', { id: cornActivityID?.id })
          .getOne();
        getcornActivityID.responseData = errorList.length ? errorList.join(',') : 'Success';
        getcornActivityID.endTime = moment().format('YYYY-MM-DD HH:mm:ss');
        await getcornActivityID.save();
        return;
      }

      // console.log(usersWithReminderPref,'length')

      for (let i of usersWithReminderPref) {

        const usersData = await User.find({ where: { organization: i.orgId, status: UserStatus.ACTIVE, type: "organization" } })
        for (let i of usersData) {
          const userId = i.id;

          const today = new Date().toISOString().split('T')[0];
          const title = "Checkout_Reminder"
          const attendanceExists = await createQueryBuilder('attendance', 'a')
            .where('a.user_id = :userId', { userId })
            .andWhere('DATE(a.attendance_date) = :today', { today })
            .andWhere('a.checkin_time IS NOT NULL')
            .andWhere('a.checkout_time IS NULL')
            .getCount();


          if (attendanceExists) {
            console.log('User ID with reminder preference and attendance today:', userId);
            try {
              if (userId !== undefined) {
                const sessionValidation = await ViderWhatsappSessions.findOne({
                  where: { userId: userId, status: 'ACTIVE' },
                });
                if (sessionValidation) {
                  const userDetails = await getUserDetails(userId);

                  const {
                    full_name: userFullName,
                    mobile_number: userPhoneNumber,
                    id,
                    organization_id,
                  } = userDetails;
                  const key = 'ATTENDANCE_CHECKINCHECKOUT_REMINDER_WHATSAPP';
                  const whatsappMessageBody = `
 Hi ${userFullName},

 A quick reminder to please Check-Out on the ATOM to mark your attendance for the day.

 We hope this helps`;
                  console.log(userFullName, userPhoneNumber, whatsappMessageBody, organization_id, title, id)
                  await sendWhatsAppTextMessageWithOrgPreference(
                    `91${userPhoneNumber}`,
                    whatsappMessageBody,
                    organization_id,
                    title,
                    id,
                    key,
                  );
                }
              }
            } catch (error) {
              errorList.push(error.message);
              console.error('Error sending User WhatsApp notification:', error);
            }
          }

        }
      }
      const getcornActivityID = await createQueryBuilder(CronActivity, 'cronActivity')
        .where('id = :id', { id: cornActivityID?.id })
        .getOne();
      getcornActivityID.responseData = errorList.length ? errorList.join(',') : 'Success';
      getcornActivityID.endTime = moment().format('YYYY-MM-DD HH:mm:ss');
      await getcornActivityID.save();
    }

  }



  async updateOrganizationPreferencesCron() {
    const orgPreferences = await OrganizationPreferences.find();
    // console.log(orgPreferences);
    for (let preference of orgPreferences) {
      let preferences: any = preference;
      if (preferences?.taskPreferences) {

        // Add missing properties without updating existing values
        if (!('isPrevious' in preferences.taskPreferences)) {
          preferences.taskPreferences.isPrevious = true;
        }
        if (!('previousDayDate' in preferences.taskPreferences)) {
          preferences.taskPreferences.previousDayDate = 15;
        }
        await preferences.save();
      } else {
        preferences.taskPreferences = { isPrevious: true, previousDayDate: 15 };
        await preferences.save();
      }
    }
  }
}
