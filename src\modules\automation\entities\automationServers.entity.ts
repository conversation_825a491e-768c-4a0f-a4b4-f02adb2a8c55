import { Optional } from '@nestjs/common';
import { Organization } from 'src/modules/organization/entities/organization.entity';
import {
  BaseEntity,
  Column,
  CreateDateColumn,
  Entity,
  ManyToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';

@Entity()
class AutomationServers extends BaseEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  machineName: string;

  @Column()
  healthCheckUrl: string;

  @Column()
  ipAddress: string;

  @Column()
  automationMachinesId: string;

  @Column()
  serverStatus: string;

  @Column()
  syncType: string;

  @CreateDateColumn()
  createdAt: string;

  @UpdateDateColumn()
  updatedAt: string;
}

export default AutomationServers;
