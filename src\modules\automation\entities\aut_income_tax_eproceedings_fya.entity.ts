import { Optional } from '@nestjs/common';
import Client from 'src/modules/clients/entity/client.entity';
import { Organization } from 'src/modules/organization/entities/organization.entity';
import {
  BaseEntity,
  Column,
  CreateDateColumn,
  Entity,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import AutFyaNotice from './aut_income_tax_eproceedings_fya_notice.entity';

export enum EproceedingTypeEnum {
  SELF = 'Self',
  OTHER = 'Other',
}

@Entity()
class AutEProceedingFya extends BaseEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  pan: string;

  @Column()
  proceedingReqId: string;

  @Column()
  proceedingName: string;

  @Column()
  assesmentYear: string;

  @Column()
  financialYear: string;

  @Column()
  proceedingStatus: string;

  @Column()
  noticeName: string;

  @Column()
  itrType: string;

  @Column()
  responseDueDate: string;

  @Column()
  lastResponseSubmittedOn: string;

  @Column()
  isFileAppeal: Boolean;

  @Column('json')
  proceedingStatusDetails: object;

  @Column()
  acknowledgementNo: string;

  @Column()
  createdType: string;

  @ManyToOne(() => Client, (client) => client.auteproceedingfya, { onDelete: 'SET NULL' })
  client: Client;

  @OneToMany(() => AutFyaNotice, (notices) => notices.eProceeding)
  notices: AutFyaNotice[];

  @Column()
  organizationId: number;

  @CreateDateColumn()
  createdAt: string;

  @UpdateDateColumn()
  updatedAt: string;

  @Column({ default: EproceedingTypeEnum.SELF, type: 'enum', enum: EproceedingTypeEnum })
  type: EproceedingTypeEnum;
}

export default AutEProceedingFya;
