import { BaseEntity, Column, CreateDateColumn, Entity, OneToOne, PrimaryGeneratedColumn } from 'typeorm';

export enum ActivityType {
  CLIENT_GROUP = 'client_group',
  CLIENT = 'clients',
  USER = 'user',
  FORM = 'form',
  TASK = 'task',
  BILLING = 'billing',
  BILLING_CLIENT_GROUP = 'billing_client_group',
}

@Entity()
class Activity extends BaseEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  action: string;

  @Column({ type: 'int' })
  actorId: number;

  @Column()
  remarkType: string;

  @Column({ nullable: false })
  remarks: string;

  @Column({ type: 'enum', enum: ActivityType, default: ActivityType.CLIENT })
  type: ActivityType;

  @Column({ type: 'int' })
  typeId: number;


  @CreateDateColumn()
  createdAt: string;
}

export default Activity;
