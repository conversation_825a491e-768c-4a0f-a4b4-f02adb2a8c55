import { BadRequestException, Injectable } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { Event_Actions } from 'src/event-listeners/actions';
import Client from 'src/modules/clients/entity/client.entity';
import * as xlsx from 'xlsx';
import Activity, { ActivityType } from 'src/modules/activity/activity.entity';
import { User } from 'src/modules/users/entities/user.entity';
import { CreateClientPasswordDto, UpdateClientPasswordDto } from 'src/modules/clients/dto/create-password.dto';
import Password from 'src/modules/clients/entity/password.entity';


@Injectable()
export class ClientPasswordService {
  constructor(private eventEmitter: EventEmitter2) { }

  async create(userId: number, data: CreateClientPasswordDto) {
    let user = await User.findOne({ where: { id: userId } });
    const client = await Client.findOne({ where: { id: data.client } });

    const password = new Password();
    password.website = data.website.trim();
    password.websiteUrl = data.websiteUrl.trim();
    password.loginId = data.loginId.trim();
    password.password = data.password.trim();
    password.client = client;
    password['userId'] = userId;
    await password.save();

    let activity = new Activity();
    activity.action = Event_Actions.CREDENTIALS_ADDED;
    activity.actorId = user.id;
    activity.type = ActivityType.CLIENT;
    activity.typeId = data.client;
    activity.remarks = `"${password.website}" Credentials Added by ${user.fullName}`;
    await activity.save();

    this.eventEmitter.emit(Event_Actions.CREDENTIAL_CREATED, {
      userId,
      clientId: client?.id,
    });

    return password;
  }

  async find(id: number) {
    const passwords = await Password.find({ where: { client: { id } } });
    return passwords;
  }

  async exportClientPasswordexport(clientId, query) {
    const id = clientId;
    let clientpasswords = await this.find(id);
    let rows = clientpasswords.map((clientpassword) => {
      return {
        'Website': clientpassword?.website,
        'Website Url': clientpassword?.websiteUrl,
        'Login ID': clientpassword.loginId,
        'Password': clientpassword?.password,
        'Last Modified On': clientpassword?.updatedAt
      };
    });

    if (rows !== undefined && rows.length) {
      const worksheet = xlsx.utils.json_to_sheet(rows);
      const workbook = xlsx.utils.book_new();
      xlsx.utils.book_append_sheet(workbook, worksheet, 'users');
      let file = xlsx.write(workbook, { type: 'buffer' });
      return file;
    } else {
      throw new BadRequestException('No Data for Export');
    }

  }

  async update(userId: number, id: number, body: UpdateClientPasswordDto) {
    let user = await User.findOne({ where: { id: userId } });
    let password = await Password.findOne({ where: { id }, relations: ['client'] });
    password.website = body.website.trim();
    password.websiteUrl = body.websiteUrl.trim();
    password.loginId = body.loginId.trim();
    password.password = body.password.trim();
    password['userId'] = userId;
    await password.save();

    let activity = new Activity();
    activity.action = Event_Actions.CREDENTIALS_UPDATED;
    activity.actorId = user.id;
    activity.type = ActivityType.CLIENT;
    activity.typeId = password.client.id;
    activity.remarks = `"${password.website}" Credentials Updated by ${user.fullName}`;
    await activity.save();

    this.eventEmitter.emit(Event_Actions.CREDENTIAL_UPDATED, {
      userId,
      clientId: password?.client?.id,
    });

    return password;
  }

  async delete(id: number, userId) {
    let user = await User.findOne({ where: { id: userId } });
    const password = await Password.findOne({ where: { id }, relations: ['client']  });
    password['userId'] = userId;
    await password.remove();

    let activity = new Activity();
    activity.action = Event_Actions.CREDENTIALS_DELETED;
    activity.actorId = user.id;
    activity.type = ActivityType.CLIENT;
    activity.typeId = password.client.id;
    activity.remarks = `"${password.website}" Credentials Deleted by ${user.fullName}`;
    await activity.save();

    return 'success';
  }
}
