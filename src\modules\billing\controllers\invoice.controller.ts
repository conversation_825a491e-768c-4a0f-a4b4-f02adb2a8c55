import {
  Body,
  Controller,
  Get,
  Param,
  ParseIntPipe,
  Post,
  Put,
  Query,
  Req,
  UseGuards,
  Request,
} from '@nestjs/common';
import { JwtAuthGuard } from 'src/modules/users/jwt/jwt-auth.guard';
import { CreateInvoiceDto } from '../dto/create-invoice.dto';
import {
  FindInvoicesDto,
  NextInvoiceNumberDto,
} from '../dto/find-invoices.dto';
import { GetUnbilledTasksDto } from '../dto/get-unbilled.dto';
import { InvoiceService } from '../services/invoice.service';
import { FindClientBillingInvoices } from '../dto/find-client-billing-invoices.dto';
import { query } from 'express';

@Controller('invoices')
export class InvoiceController {
  constructor(private service: InvoiceService) { }

  @UseGuards(JwtAuthGuard)
  @Get()
  getInvoices(@Req() request, @Query() query: FindInvoicesDto) {
    const { userId } = request.user;
    return this.service.getInvoices(userId, query);
  }


  @UseGuards(JwtAuthGuard)
  @Post('/tds-export')
  async exportTdsBilling(@Req() req: any, @Body() body: any) {
    const { userId } = req.user;
    return this.service.exportTdsBilling(userId, body);
  }

  @UseGuards(JwtAuthGuard)
  @Post('/gst-billing-export')
  async exportGstBilling(@Req() req: any, @Body() body: any) {
    const { userId } = req.user;
    return this.service.exportGstBilling(userId, body);
  }

  @UseGuards(JwtAuthGuard)
  @Get('/client-panel/:clientid')
  getClientPortalInvoices(@Param('clientid', ParseIntPipe) clientid: number, @Req() request, @Query() query: FindInvoicesDto) {
    const { userId } = request.user;
    return this.service.getClientPortalInvoices(clientid, query);
  }

  @UseGuards(JwtAuthGuard)
  @Get('/client-bill')
  getClientBillingInvoices(@Query() query: FindClientBillingInvoices) {
    return query?.clientType === "CLIENT_GROUP" ? this.service.getClientGroupBillingInvoices(query) : this.service.getClientBillingInvoices(query);
  }

  @UseGuards(JwtAuthGuard)
  @Post()
  createInvoice(@Body() body: CreateInvoiceDto, @Req() request: any) {
    const { userId } = request.user;
    return this.service.createInvoice(userId, body);
  }

  @UseGuards(JwtAuthGuard)
  @Put('/:id')
  updateInvoice(@Param('id', ParseIntPipe) id: number, @Body() body: any, @Req() req: any) {
    const { userId } = req.user;
    return this.service.updateInvoice(id, body, userId);
  }

  @UseGuards(JwtAuthGuard)
  @Get('/tasks')
  getClientTasks(@Query() query: GetUnbilledTasksDto) {
    return this.service.getTasks(query);
  };



  @UseGuards(JwtAuthGuard)
  @Post('/export')
  async exportInvoices(@Req() req: any, @Body() body: FindInvoicesDto) {
    const { userId } = req.user;
    if (body.type == 'Type-A') {
      return this.service.exportLineItem(userId, body);
    } else if (body.type == 'Type-B') {
      return this.service.exportTaxRate(userId, body);
    }


  }

  @UseGuards(JwtAuthGuard)
  @Get('/:clientid')
  getClientInvoices(@Param('clientid', ParseIntPipe) clientid: number) {
    return this.service.getClientInvoices(clientid);
  }


  // @Get('/:id')
  // getEstimate(@Param('id', ParseIntPipe) id: number) {
  //   return this.service.getInvoice(id);
  // }


  // @UseGuards(JwtAuthGuard)
  @Get('/:id/preview')
  getInvoice(@Param('id', ParseIntPipe) id: number, @Query() query: any) {
    return this.service.getInvoice(id, query);
  }

  @Post('/:id/download')
  async downloadEstimate(@Param('id', ParseIntPipe) id: number, @Body() body: any) {
    return this.service.downloadInvoice(id, body);
  }

  @UseGuards(JwtAuthGuard)
  @Post('/:id/cancel')
  async cancelEstimate(@Param('id', ParseIntPipe) id: number, @Req() req: any) {
    const { userId } = req.user;
    return this.service.cancelInvoice(id, userId);
  }

  @Post('/:id/submit-for-approval')
  async submitForApproval(@Param('id', ParseIntPipe) id: number) {
    return this.service.submitForApproval(id);
  }
  @UseGuards(JwtAuthGuard)
  @Get('/generate/next-invoice-number')
  async getNextEstimateNumber(@Req() req: any, @Query() query: NextInvoiceNumberDto) {
    const { userId } = req.user;
    return this.service.getNextInvoiceNumber(userId, query);
  }

  @Post('/:id/downloadwithoutemittor')
  async downloadEstimatewithoutEmittor(@Param('id', ParseIntPipe) id: number) {
    return this.service.downloadInvoicewithoutEmittor(id);
  }

  @UseGuards(JwtAuthGuard)
  @Get('/activity/:clientId')
  async getTaskActivity(@Request() req, @Param('clientId', ParseIntPipe) id: number, @Query() query) {
    const { userId } = req.user;
    return this.service.getBillingActivity(userId, id, query);
  }
}