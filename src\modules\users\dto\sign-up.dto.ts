import { IsEmail, <PERSON>Enum, IsNotEmpty, IsOptional } from 'class-validator';
import {
  OrganizationCategory,
  StorageSystem,
} from 'src/modules/organization/entities/organization.entity';

class SignUpDto {
  @IsOptional()
  @IsEnum(OrganizationCategory)
  category: OrganizationCategory;

  @IsNotEmpty()
  legalName: string;

  @IsOptional()
  tradeName: string;

  @IsOptional()
  constitutionOfBusiness: string;

  @IsOptional()
  placeOfSupply: string;

  @IsOptional()
  firstName: string;

  @IsOptional()
  middleName: string;

  @IsOptional()
  lastName: string;

  @IsOptional()
  fullName: string;

  @IsNotEmpty()
  @IsEmail()
  email: string;

  @IsNotEmpty()
  storageSystem: StorageSystem;

  @IsNotEmpty()
  mobileNumber: string;

  @IsOptional()
  gstStatus: string;

  @IsOptional()
  gstNumber: string;

  @IsOptional()
  builderName: string;

  @IsNotEmpty()
  street: string;

  // @IsNotEmpty()
  @IsOptional()
  state: string;

  @IsNotEmpty()
  city: string;

  @IsNotEmpty()
  pincode: string;

  @IsNotEmpty()
  password: string;

  @IsOptional()
  district: string;

  @IsOptional()
  dateOfFormation: string;

  @IsOptional()
  buildingNo: string;

  @IsOptional()
  config: string;

  @IsOptional()
  userName: string;

  @IsNotEmpty()
  dialCode: string;

  @IsNotEmpty()
  countryCode: string;

  @IsOptional()
  couponCode:string;

  @IsOptional()
  channelPartnerId:number | string;
  
}

export class OtpDto {
  @IsNotEmpty()
  mobileNumber: string;

  @IsNotEmpty()
  email: string;

  @IsNotEmpty()
  dialCode: string;
}

export class VerifyOtpDto {
  @IsNotEmpty()
  mobileNumber: string;

  @IsNotEmpty()
  otp: string;

  @IsNotEmpty()
  token: string;
}

export class JoinUserDto {
  password: string;

  @IsNotEmpty()
  token: string;
}

export class CouponDto{
  @IsOptional()
  couponCode:string;

  @IsOptional()
  channelPartnerId:number | string;

}

export default SignUpDto;
