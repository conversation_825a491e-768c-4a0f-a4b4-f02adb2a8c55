import { <PERSON><PERSON> } from "aws-sdk/clients/robomaker";
import { IsNotEmpty } from "class-validator";
import { BaseEntity, Column, CreateDateColumn, Entity, PrimaryColumn, PrimaryGeneratedColumn, UpdateDateColumn } from "typeorm";
export enum MessageType {
    TEXT = 'text',
    TEMPLATE = 'template',
    DOCUMENT = 'document',
    NONE = 'none',
  }

@Entity()
class WhatsappAudit extends BaseEntity {
    @PrimaryGeneratedColumn()
    id: number;

    @Column()
    userId: number;

    @Column()
    organizationId: number;

    @Column()
    title: string;

 @Column()
 phoneNumberId : string;
 
    @Column({ type: 'timestamp', nullable: true })
    createdAt: Date;

    @UpdateDateColumn()
    updatedAt : Date;

    @Column()
    mobileNumber: string;
  
    @Column({ type: 'enum', enum: MessageType, default: MessageType.NONE })
    messageType: MessageType;
    
}

export default WhatsappAudit;