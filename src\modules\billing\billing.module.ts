import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import InvoiceAddress from './entitities/invoice-address.entity';
import { InvoiceController } from './controllers/invoice.controller';
import { InvoiceService } from './services/invoice.service';
import InvoiceParticular from './entitities/invoice-particular.entity';
import InvoiceOtherParticular from './entitities/invoice-other-particular.entity';
import InvoiceBankDetails from './entitities/invoice-bank-details.entity';
import { Invoice } from './entitities/invoice.entity';
import { ReceiptsController } from './controllers/receipts.controller';
import { ReceiptsService } from './services/receipts.service';
import Receipt from './entitities/receipt.entity';
import ReceiptParticular from './entitities/receipt-particular.entity';
import ReceiptCredit from './entitities/receipt-credit.entity';
import { ReceiptSubscriber } from 'src/event-subscribers/receipt.subscriber';
import { InvoiceSubscriber } from 'src/event-subscribers/invoice.subscriber';
import ExportListeners from 'src/event-listeners/export-invoices.listener';
import { OneDriveStorageService } from '../ondrive-storage/onedrive-storage.service';
import { AttachmentsService } from '../tasks/services/attachments.service';
import { StorageService } from '../storage/storage.service';
import { AwsService } from '../storage/upload.service';
import { ProformaController } from './controllers/proforma.controller';
import { ProformaService } from './services/proforma.service';
import { ProformaInvoice } from './entitities/proforma-invoice.entity';
import { ProformaInvoiceSubscriber } from 'src/event-subscribers/proforma-invoice.subscriber';
import { BharathStorageService } from '../storage/bharath-storage.service';
import { BharathCloudService } from '../storage/bharath-upload.service';
import { GoogleDriveStorageService } from '../ondrive-storage/googledrive-storage.service';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      Invoice,
      InvoiceAddress,
      InvoiceBankDetails,
      InvoiceParticular,
      InvoiceOtherParticular,
      Receipt,
      ReceiptParticular,
      ReceiptCredit,
      ProformaInvoice
    ]),
  ],
  controllers: [InvoiceController, ReceiptsController, ProformaController],
  providers: [InvoiceService,
    InvoiceSubscriber,
    ReceiptSubscriber,
    ReceiptsService,
    ExportListeners,
    OneDriveStorageService,
    AttachmentsService,
    StorageService,
    AwsService,
    ProformaService,
    ProformaInvoiceSubscriber,
    BharathStorageService,
    BharathCloudService,
    GoogleDriveStorageService

  ],
})
export class BillingModule { }
