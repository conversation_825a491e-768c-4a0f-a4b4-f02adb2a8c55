import { Organization } from 'src/modules/organization/entities/organization.entity';
import { User } from 'src/modules/users/entities/user.entity';
import {
  BaseEntity,
  Column,
  Entity,
  JoinTable,
  ManyToMany,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
} from 'typeorm';
import OrgApprovalLevel from './org-approvals-level.entity';
import ApprovalProcedures from './approval-procedures.entity';

export enum APPROVALS_PROCEDURE_STATUS {
  CREATED = 'created',
  DELETED = 'deleted'
}

@Entity()
class OrgApprovals extends BaseEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  name: string;

  @Column()
  description: string;

  @Column()
  oldApprovalId: number;

  @Column()
  copied: boolean;

  @ManyToOne(() => User, (user) => user.orgApprovals)
  user: User;

  @Column({ type: 'enum', enum: APPROVALS_PROCEDURE_STATUS, default: APPROVALS_PROCEDURE_STATUS.CREATED })
  status: APPROVALS_PROCEDURE_STATUS;

  @ManyToOne(() => Organization, (organization) => organization.orgApprovals)
  organization: Organization;

  @OneToMany(() => OrgApprovalLevel, (appHeirLevel) => appHeirLevel.approvalHierarchy, {
    cascade: true,
  })
  approvalLevels: OrgApprovalLevel[];

  @OneToMany(() => ApprovalProcedures, (approvalProcedures) => approvalProcedures.approval)
  approvalProcedures: ApprovalProcedures[];
}

export default OrgApprovals;
