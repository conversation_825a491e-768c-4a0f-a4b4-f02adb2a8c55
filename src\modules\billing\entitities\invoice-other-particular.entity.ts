import {
  <PERSON><PERSON>ntity,
  Column,
  CreateDateColumn,
  Entity,
  ManyToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { Invoice } from './invoice.entity';
import { ProformaInvoice } from './proforma-invoice.entity';

export enum TYPE {
  PURE_AGENT = 'PURE_AGENT',
  ADDITIONAL = 'ADDITIONAL',
}

@Entity()
class InvoiceOtherParticular extends BaseEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @ManyToOne(() => Invoice, (invoice) => invoice.otherParticulars)
  invoice: Invoice;

  @ManyToOne(() => ProformaInvoice, (proformaInvoice) => proformaInvoice.otherParticulars)
  proformaInvoice: ProformaInvoice;

  @Column()
  name: string;

  // @Column({ type: 'bigint' })
  // amount: number;

  @Column({ type: 'decimal', precision: 19, scale: 2 })
  amount: number;


  @Column()
  taskExpenseType: string;

  @CreateDateColumn()
  createdAt: string;

  @UpdateDateColumn()
  updatedAt: string;
}

export default InvoiceOtherParticular;
