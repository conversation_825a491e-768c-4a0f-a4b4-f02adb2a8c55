import { Injectable } from "@nestjs/common";
import axios from "axios";

@Injectable()
export class ApprovalService {


    async createProcess(users: number[], owner) {
        try {
            const data = JSON.stringify({
                processKey: 'genericApprovalProcess',
                taskOwners: users.map(String),
                owner: owner,
                metaData: {
                    typeOfApproval: 'ATOM_TASK',
                    approvalProcessId: `Level1ApprovalProcess`,
                },
            });

            let config: any = {
                method: 'post',
                maxBodyLength: Infinity,
                url: `${process.env.CAMUNDA_URL}/vider/quantum/api/process`,
                headers: { 'Content-Type': 'application/json' },
                data: data,
            };
            const response = await axios.request(config);
            const processInstanceId: string = response?.data?.processInstanceId;
            return processInstanceId

        } catch (err) {
        }
    }

};


export default ApprovalService;