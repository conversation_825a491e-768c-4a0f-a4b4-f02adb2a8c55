import { Body, Controller, Get, Param, ParseIntPipe, Post, Put, Query, Req, UseGuards } from "@nestjs/common";
import { TanSyncService } from "../service/tan-sync.service";
import { JwtAuthGuard } from "src/modules/users/jwt/jwt-auth.guard";

@Controller('tan-sync')
export class TanSyncController {
    constructor(private service:TanSyncService){}
    @UseGuards(JwtAuthGuard)
    @Post('auto-machine/:id')
    createMachine(@Param('id', ParseIntPipe) id: number, @Body() body: any, @Req() req: any) {
      const { userId } = req.user;
      return this.service.createMachine(userId, id, body);
    }

    @UseGuards(JwtAuthGuard)
    @Post('bulkSync')
    bulkAutomationSync(@Body() body: any, @Req() req: any) {
      const { userId } = req.user;
      return this.service.bulkAutomationSync(userId, body);
    }

    @UseGuards(JwtAuthGuard)
    @Get('checkNoOfSync')
    checkAutomationInOrganization(@Req() req: any) {
      const { userId } = req.user;
      return this.service.checkAutomationInOrganization(userId);
    }

    @UseGuards(JwtAuthGuard)
    @Get('new-updates')
    getIncometexUpdates(@Req() req: any, @Query() query: any) {
      const { userId } = req.user;
      return this.service.getIncometexUpdates(userId);
    }

    @UseGuards(JwtAuthGuard)
    @Get('update/:id')
    findForm(@Req() req: any, @Param('id', ParseIntPipe) id: number) {
      const { userId } = req.user;
      return this.service.getUpdatedItem(userId, id);
    }

    // CLILENT CREDENTIALS 
    @UseGuards(JwtAuthGuard)
    @Put('disableIncomeTaxClient')
    disableIncomeTaxClient(@Body() body: any, @Req() req: any) {
      const { userId } = req.user;
      return this.service.disableIncomeTaxClient(userId, body);
    }
  
    @UseGuards(JwtAuthGuard)
    @Put('disableIncomeTax/:id')
    disableIncomeTaxSingleClient(@Req() req: any, @Param('id', ParseIntPipe) id: number) {
      const { userId } = req.user;
      return this.service.disableIncomeTaxSingleClient(id, userId);
    }

    @UseGuards(JwtAuthGuard)
    @Put('enableIncomeTax/:id')
    enableIncometaxClient(@Req() req: any, @Param('id', ParseIntPipe) id: number) {
      const { userId } = req.user;
      return this.service.enableIncometaxClient(id, userId);
    }

    @UseGuards(JwtAuthGuard)
    @Post('enableIncomeTax/bulk-enable')
    enableBulkIncometaxClient(@Req() req: any, @Body() body: any) {
      const { userId } = req.user;
      return this.service.enableBulkIncometaxClient(userId, body);
    }

    @UseGuards(JwtAuthGuard)
    @Get('deletedClients')
    getDeletedIncomeTaxClients(@Req() req: any, @Query() query: any) {
      const { userId } = req.user;
      return this.service.getDeletedIncomeTaxClients(userId, query);
    }
    @UseGuards(JwtAuthGuard)
    @Post('/deletedIncomeTaxreport')
    async exportIncomeTaxTandeletedClients(@Req() req: any, @Body() body: any) {
      const query = body;
      const { userId } = req.user;
      return this.service.exportIncomeTaxTandeletedClients(userId, query);
    }

    @UseGuards(JwtAuthGuard)
    @Get('bulkSyncStatus')
    getTanBulkSyncStatus(@Req() req: any) {
      const { userId } = req.user;
      return this.service.getTanBulkSyncStatus(userId);
    }
  
    @UseGuards(JwtAuthGuard)
    @Put('enableStatus')
    enableTanStatus(@Req() req: any) {
      const { userId } = req.user;
      return this.service.updateTanEnableStatus(userId);
    }
  
    @UseGuards(JwtAuthGuard)
    @Put('disableStatus')
    updateTanDisableStatus(@Req() req: any) {
      const { userId } = req.user;
      return this.service.updateTanDisableStatus(userId);
    }
  
    @UseGuards(JwtAuthGuard)
    @Post('scheduling')
    async organizationTanScheduling(@Req() req: any, @Body() body: any) {
      const { userId } = req.user;
      return this.service.organizationTanScheduling(userId, body);
    }

    @UseGuards(JwtAuthGuard)
    @Get('bulkSyncStatus-trace')
    getTraceBulkSyncStatus(@Req() req: any) {
      const { userId } = req.user;
      return this.service.getTraceBulkSyncStatus(userId);
    }
  
    @UseGuards(JwtAuthGuard)
    @Put('enableStatus-trace')
    enableTraceStatus(@Req() req: any) {
      const { userId } = req.user;
      return this.service.updateTraceEnableStatus(userId);
    }
  
    @UseGuards(JwtAuthGuard)
    @Put('disableStatus-trace')
    updateTraceDisableStatus(@Req() req: any) {
      const { userId } = req.user;
      return this.service.updateTraceDisableStatus(userId);
    }
  
    @UseGuards(JwtAuthGuard)
    @Post('scheduling-trace')
    async organizationTraceScheduling(@Req() req: any, @Body() body: any) {
      const { userId } = req.user;
      return this.service.organizationTraceScheduling(userId, body);
    }


}
