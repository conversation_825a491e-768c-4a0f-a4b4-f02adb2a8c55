import { ConflictException, Injectable } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { Event_Actions } from 'src/event-listeners/actions';
import { User } from 'src/modules/users/entities/user.entity';
import { createQueryBuilder } from 'typeorm';
import CreateLeadDto from './dto/create-lead.dto';
import { FindLeadsDto } from './dto/find-leads.dto';
import Lead from './lead.entity';
import Client from '../clients/entity/client.entity';

@Injectable()
export class LeadsService {
  constructor(private eventEmitter: EventEmitter2) { }

  async create(userId: number, data: CreateLeadDto) {
    let user = await User.findOne({
      where: { id: userId },
      relations: ['organization'],
    });

    data.name = data.name.trim();
    let existingService = await Lead.findOne({
      where: {
        name: data.name, organization: user.organization
      },
    });
    if (existingService) {
      throw new ConflictException(
        'Lead with given display name already exists in the organization',
      );
    }
    let existingUser = await createQuery<PERSON>uilder(Client, 'client')
      .leftJoin('client.organization', 'organization')
      .where('organization.id = :organization', { organization: user.organization.id })
      .andWhere('(client.displayName = :displayName)', {
        displayName: data.name,
      })
      .getOne();

    if (existingUser) {
      throw new ConflictException(
        'Lead | Client with the given Display Name already Exists in your Organization',
      );
    }
    const lead = new Lead();
    lead.name = (' ' + data['name'])?.trim();
    lead.email = data.email.trim();
    lead.description = data.description.trim();
    lead.mobileNumber = data.mobileNumber;
    lead.category = data.category;
    lead.subCategory = data.subCategory;
    lead.countryCode = data.countryCode;
    lead.user = user;
    lead.organization = user.organization;
    lead['userId'] = user.id;
    await lead.save();

    //lead event emit
    this.eventEmitter.emit(Event_Actions.LEAD_CREATED, { lead });
    return lead;
  }

  async get(userId: number, query: FindLeadsDto) {
    const user = await User.findOne({
      where: { id: userId },
      relations: ['organization'],
    });

    const leads = createQueryBuilder(Lead, 'lead')
      .leftJoin('lead.organization', 'organization')
      .where('organization.id = :organizationId', { organizationId: user.organization.id })
      // .orderBy('lead.status', 'ASC');

      const sort = (typeof query?.sort === "string") ? JSON.parse(query.sort) : query?.sort;
      if (sort?.column) {
          const columnMap: Record<string, string> = {
            name: 'lead.name',
             category: 'lead.category',
            createdAt: 'lead.createdAt',
           
          };
          const column = columnMap[sort.column] || sort.column;
          leads.orderBy(column, sort.direction.toUpperCase());
      } else {
        leads.orderBy('lead.createdAt', 'DESC');
      };

    if (query.search) {
      leads.andWhere('lead.name like :search OR lead.email LIKE :search OR lead.mobileNumber LIKE :search', {
        search: `%${query.search}%`,
      });
    }

    if (query.offset >= 0) {
      leads.skip(query.offset);
    }

    if (query.limit) {
      leads.take(query.limit);
    }

    let result = await leads.getManyAndCount();
    // let mySql = await leads.getSql();
    // console.log('lead get data', mySql);

    return {
      data: result[0],
      totalCount: result[1],
    };
  }

  async update(id: number, data: CreateLeadDto, userId: number) {
    const lead = await Lead.findOne({ where: { id } });
    if (data.name.toLowerCase() !== lead.name.toLowerCase()) {
      let user = await User.findOne({
        where: { id: userId },
        relations: ['organization'],
      });

      data.name = data.name.trim();
      const status = "CONVERTED"
      if (data.status !== status.toLowerCase()) {
        let existingService = await Lead.findOne({
          where: {
            name: data.name, organization: user.organization
          },
        });
        if (existingService) {
          throw new ConflictException(
            'Lead with given display name already exists in the organization',
          );
        }

        let existingUser = await createQueryBuilder(Client, 'client')
          .leftJoin('client.organization', 'organization')
          .where('organization.id = :organization', { organization: user.organization.id })
          .andWhere('(client.displayName = :displayName)', {
            displayName: data.name,
          })
          .getOne();
        if (existingUser) {
          throw new ConflictException(
            'Lead | Client with the given Display Name already Exists in your Organization',
          );
        }
      }
    }
    lead.name = data.name;
    lead.email = data.email.trim();
    lead.mobileNumber = data.mobileNumber;
    lead.category = data.category;
    lead.subCategory = data.subCategory;
    lead.description = data.description.trim();
    lead.status = data.status;
    lead.countryCode = data.countryCode;
    lead['userId'] = userId;
    await lead.save();
    return lead;
  }

  async delete(ids: number[]) {
    await createQueryBuilder(Lead, 'lead').whereInIds(ids).delete().execute();

    return { success: true };
  }
}
