import { BadRequestException, Injectable, InternalServerErrorException } from '@nestjs/common';
import { User } from 'src/modules/users/entities/user.entity';
import { createQueryBuilder } from 'typeorm';
import * as xlsx from 'xlsx';
import * as  moment from 'moment';
import { dateFormation } from 'src/utils/datesFormation';
import { getExpenseType } from 'src/utils/re-use';
import FindExpenditureDto, { FindExpenditureQueryType } from 'src/modules/expenditure/dto/find-expenditure.dto';
import Expenditure from 'src/modules/expenditure/expenditure.entity';
import { OneDriveStorageService } from './onedrive-storage.service';
import { StorageService } from './storage.service';
import { AwsService } from './upload.service';

@Injectable()
export class ExpenditureService {
  constructor(private storageService: StorageService,
    private awsService: AwsService,
    private oneDriveService: OneDriveStorageService

  ) { }

  async find(userId: number, query: FindExpenditureDto) {
    const { limit, offset } = query;

    let repo = createQueryBuilder(Expenditure, 'expenditure')
      .leftJoinAndSelect('expenditure.task', 'task')
      .leftJoinAndSelect('task.category', 'category')
      .leftJoinAndSelect('expenditure.client', 'client')
      .leftJoinAndSelect('expenditure.clientGroup', 'clientGroup')
      .leftJoinAndSelect('expenditure.user', 'user')
      .leftJoinAndSelect('user.imageStorage', 'imageStorage')
      .leftJoinAndSelect('expenditure.storage', 'storage')
      .orderBy('expenditure.createdAt', 'DESC');

    if (query.type === FindExpenditureQueryType.TASK) {
      repo.where('task.id = :taskId', { taskId: query.taskId });
    }

    if (query.type === FindExpenditureQueryType.USER) {
      repo.where('user.id = :userId', { userId });
    }

    if (query.type === FindExpenditureQueryType.SELF) {
      repo.where('user.id = :userId', { userId });
    }
    const sort = (typeof query?.sort === "string") ? JSON.parse(query.sort) : query?.sort;
    if (sort?.column) {
        const columnMap: Record<string, string> = {
          type: 'expenditure.type',
          date: 'expenditure.date',
          displayName:'client.displayName',
          expensiveType:'expenditure.taskExpenseType',
          taskNumber:'task.taskNumber',
          name:'task.name',
          particularName:'expenditure.particularName',
          amount:'expenditure.amount',

        };
        const column = columnMap[sort.column] || sort.column;
        repo.orderBy(column, sort.direction.toUpperCase());
    } else {
      repo.orderBy('task.createdAt', 'DESC');
    };
    if (query.search) {
      repo.andWhere('(expenditure.particularName LIKE :search OR task.name LIKE :search OR task.taskNumber LIKE :search OR client.displayName LIKE :search)', {
        search: `%${query.search}%`,
      });
    }
    if (offset >= 0) {
      repo.skip(offset);
    }

    if (limit) {
      repo.take(limit);
    }

    let result = await repo.getManyAndCount();

    return {
      count: result[1],
      result: result[0],
    };

    // return await repo.getMany();
  }

  async export(userId: number, body: FindExpenditureDto) {
    let expenditures = await this.find(userId, body);
    let rows = expenditures?.result?.map((expenditure) => {
      function capitalizeFirstLetter(string) {
        return string.charAt(0).toUpperCase() + string.slice(1);
      }

      return {
        'Expense Nature': capitalizeFirstLetter(expenditure?.type.toLowerCase()),
        'Expense Type':getExpenseType(expenditure?.taskExpenseType),
        'Date': expenditure?.date ? moment(expenditure.date).format('DD-MM-YYYY') : null,
        'Client / Client Group': expenditure?.client
        ? expenditure?.client?.displayName
        : expenditure?.clientGroup?.displayName,        
        'Task ID': expenditure?.task?.taskNumber,
        'Task Name': expenditure?.task?.name,
        'Expense Title': expenditure?.particularName,
        'Amount':1* expenditure?.amount,
      };
    });

    if (rows !== undefined && rows.length) {
      const worksheet = xlsx.utils.json_to_sheet(rows);
      const workbook = xlsx.utils.book_new();
      xlsx.utils.book_append_sheet(workbook, worksheet, 'Expenditure10');
      let file = xlsx.write(workbook, { type: 'buffer' });
      return file;
    } else {
      throw new BadRequestException('No Data for Export');
    }
  }

  async findOrgExpenditure(userId: number, query: FindExpenditureDto) {
    const { limit, offset } = query;
    const user = await User.findOne(userId);
    let repo = createQueryBuilder(Expenditure, 'expenditure')
      .select(['expenditure.id',
        'expenditure.type',
        'expenditure.date',
        'client.id',
        'client.displayName',
        'expenditure.taskExpenseType',
        'task.taskNumber',
        'task.name',
        'expenditure.particularName',
        'expenditure.amount',
        'expenditure.createdAt'
      ])
      .leftJoin('expenditure.task', 'task')
      .leftJoinAndSelect('expenditure.user', 'user')
      .leftJoin('user.organization', 'organization')
      .leftJoin('expenditure.client', 'client')
      .leftJoinAndSelect('expenditure.storage', 'storage')
      .leftJoinAndSelect('user.imageStorage', 'imageStorage')
      .where('organization.id = :org', { org: user.organization.id });
    if(query?.clientId){
      repo.andWhere('client.id = :clientId', { clientId: query?.clientId });
    }
    if (query?.expenseType?.length) {
      repo.andWhere('expenditure.taskExpenseType in (:...expenseType)', { expenseType: query?.expenseType });
    }

    if (query?.expenseNature?.length) {
      repo.andWhere('expenditure.type in (:...type)', { type: query?.expenseNature });
    }
    if (query?.users?.length) {
      repo.andWhere('user.id in (:...users)', { users: query?.users });
    }

    if (query.fromDate && query.toDate) {
      const { startTime, endTime } = dateFormation(query.fromDate, query.toDate);
      repo.andWhere('expenditure.date BETWEEN :startDate AND :endDate', {
        startDate: startTime,
        endDate: endTime,
      });
    }

    if (query.search) {
      repo.andWhere('(expenditure.particularName LIKE :search OR task.name LIKE :search OR task.taskNumber LIKE :search OR client.displayName LIKE :search)', {
        search: `%${query.search}%`,
      });
    }

    const sort = (typeof query?.sort === "string") ? JSON.parse(query.sort) : query?.sort;
    if (sort?.column) {
      const columnMap: Record<string, string> = {
        date: 'expenditure.date',
        amount: 'expenditure.amount',
        type:'expenditure.type',
        displayName:'client.displayName',
        taskNumber:'tasl.taskNumber',
        name:'task.name',
        particularName:'expenditure.particularName',
        

      };
      const column = columnMap[sort.column] || sort.column;
      repo.orderBy(column, sort.direction.toUpperCase());
    } else {
      repo.orderBy('expenditure.createdAt', 'DESC');
    };

    if (offset >= 0) {
      repo.skip(offset);
    }

    if (limit) {
      repo.take(limit);
    }
    let result = await repo.getManyAndCount();
    return {
      count: result[1],
      result: result[0],
    };

  }

  async exportOrgExpenditure(userId: number, body: FindExpenditureDto) {
    let expenditures = await this.findOrgExpenditure(userId, body);
    let rows = expenditures?.result?.map((expenditure) => {
      function capitalizeFirstLetter(string) {
        return string.charAt(0).toUpperCase() + string.slice(1);
      }

      return {
        'Expense Nature': capitalizeFirstLetter(expenditure?.type.toLowerCase()),
        'Date': expenditure?.date ? moment(expenditure.date).format('DD-MM-YYYY') : null,
        'Client': expenditure?.client?.displayName,
        'TASK ID': expenditure?.task?.taskNumber,
        'TASK NAME': expenditure?.task?.name,  
        'Expense Type':getExpenseType(expenditure?.taskExpenseType),
        'Expense Title': expenditure?.particularName,
        'Amount (₹)':1 * expenditure?.amount,
        'Employee': expenditure?.user?.fullName
      };
    });

    if (rows !== undefined && rows.length) {
      const worksheet = xlsx.utils.json_to_sheet(rows);
      const workbook = xlsx.utils.book_new();
      xlsx.utils.book_append_sheet(workbook, worksheet, 'Expenditure67');
      let file = xlsx.write(workbook, { type: 'buffer' });
      return file;
    } else {
      throw new BadRequestException('No Data for Export');
    }
  }
}