import {
  BaseEntity,
  Column,
  CreateDateColumn,
  Entity,
  JoinColumn,
  ManyToOne,
  OneToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import ChatRoom from './chat-room.entity';
import Storage from '../storage/storage.entity';

@Entity()
class ChatMessage extends BaseEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ type: 'text', default: '' })
  message: string;

  @Column({ type: 'text', default: '' })
  file: string;

  @Column({ default: '' })
  fileName: string;

  @Column({ default: '' })
  fileType: string;

  @ManyToOne(() => ChatRoom, (room) => room.messages)
  room: ChatRoom;

  @Column()
  senderId: number;

  @Column({ default: false })
  isRead: boolean;

  @OneToOne(() => Storage, (storage) => storage.chatMessage, { cascade: true })
  @JoinColumn()
  storage: Storage;

  @CreateDateColumn()
  createdAt: string;

  @UpdateDateColumn()
  updatedAt: string;
}

export default ChatMessage;
