import {
  Is<PERSON>rray,
  Is<PERSON>num,
  <PERSON>Not<PERSON>mpty,
  <PERSON><PERSON><PERSON>al,
  IsString,
} from 'class-validator';
import { ChecklistItemStatus } from '../entity/checklist-item.entity';

export class AddChecklistDto {
  @IsNotEmpty()
  @IsString()
  name: string;

  @IsOptional()
  @IsArray()
  checklistItems: Array<{ name: string; description: string }>;
}

export class AddChecklistItems {
  @IsArray()
  checklistItems: Array<{ name: string; description: string }>;
}

export class UpdateChecklist {
  @IsNotEmpty()
  id: number;

  @IsOptional()
  name: string;
}

export class UpdateChecklistItem {
  @IsNotEmpty()
  id: number;

  @IsOptional()
  name: string;

  @IsOptional()
  description: string;

  @IsOptional()
  @IsEnum(ChecklistItemStatus)
  status: ChecklistItemStatus;
}
