import {
  Connection,
  EntitySubscriberInterface,
  EventSubscriber,
  InsertEvent,
  getManager,
} from 'typeorm';
import DscRegister from 'src/modules/dsc-register/entity/dsc-register.entity';
import {
  getAdminIDsBasedOnOrganizationId,
  insertINTONotificationUpdate,
  getUserDetails,
} from 'src/utils/re-use';
import { sendnewMail } from 'src/emails/newemails';
import { sendWhatsAppTextMessage } from 'src/modules/whatsapp/whatsapp.service';
import ViderWhatsappSessions from 'src/modules/whatsapp/entity/viderWhatsappSessions';
import { Organization } from 'src/modules/organization/entities/organization.entity';
const moment = require('moment');

@EventSubscriber()
export class DscAddSubscriber implements EntitySubscriberInterface<DscRegister> {
  constructor(private readonly connection: Connection) {
    connection.subscribers.push(this);
  }

  listenTo() {
    return DscRegister;
  }

  async beforeInsert(event: InsertEvent<DscRegister>) { }

  async afterInsert(event: InsertEvent<DscRegister>) {
    const entityManager = getManager();
    const title = 'DSC Added';
    const logInUserId = event.entity['userId'];
    const { id: dscId } = event.entity;
    if (logInUserId) {
      const userSql = `SELECT full_name FROM user WHERE id=${logInUserId}`;
      const userQuery = await entityManager.query(userSql);
      const [{ full_name: userName }] = userQuery;
      const DscHolderName = event?.entity?.holderName;
      const orgId = event?.entity?.organization?.id;
      const body = `<strong>${userName}</strong> has added DSC details of <strong>${DscHolderName}</strong> to the DSC register.`;
      const orgIds = await getAdminIDsBasedOnOrganizationId(orgId);
      // insertINTOnotification(title, body, orgIds, orgId)
      const key = 'DSC_ADDED_PUSH';
      insertINTONotificationUpdate(title, body, orgIds, orgId, key, dscId);
      const organization = await Organization.findOne({ id: orgId });


    const addressParts = [
      organization.buildingNo || '',
      organization.floorNumber || '',
      organization.buildingName || '',
      organization.street || '',
      organization.location || '',
      organization.city || '',
      organization.district || '',
      organization.state || ''
    ].filter(part => part && part.trim() !== '');
    const pincode = organization.pincode && organization.pincode.trim() !== '' ? ` - ${organization.pincode}` : '';

    const address = addressParts.join(', ') + pincode;
      if (orgIds) {
        for (let user of orgIds) {
          const userDetails = await getUserDetails(user);
          const clientNames = event?.entity?.clients?.map(client => client.displayName)
          await sendnewMail({
            id: userDetails?.id,
            key: 'DSC_ADDED_MAIL',
            email: userDetails?.email,
            data: {
              clientName: clientNames?.join(','),
              taskUserName: userDetails?.full_name,
              dscHolder: DscHolderName,
              dscExpiry: moment(event?.entity?.expiryDate).format('DD-MM-YYYY'),
              userId: event.entity['userId'],
              adress: address,
                  phoneNumber: organization?.mobileNumber,
                  mail: organization?.email,
                  legalName: organization?.tradeName || organization?.legalName
            },
            filePath: 'dsc-added',
            subject: `DSC Added`,
          });
          const title = 'DSC Added';
          try {
            if (orgIds !== undefined) {
              const sessionValidation = await ViderWhatsappSessions.findOne({
                where: { userId: orgIds,status:'ACTIVE' },
              });
              if (sessionValidation) {
                const adminUserDetails = await getUserDetails(orgIds);

                const { full_name: userFullName, mobile_number: userPhoneNumber, id, organization_id } = adminUserDetails;
                const key = 'DSC_ADDED_WHATSAPP';
                const whatsappMessageBody = `
 Hi ${userFullName}

 A New DSC of ${DscHolderName} has been added to DSC register by ${userName}
 
 We hope this helps!;
`;
                await sendWhatsAppTextMessage(
                  `91${userPhoneNumber}`,
                  whatsappMessageBody,
                  organization_id,
                  title,
                  id,
                  key,
                );
              }

            }
          } catch (error) {
            console.error('Error sending User WhatsApp notification:', error);
          }
        }
      }
    }
  }
}
