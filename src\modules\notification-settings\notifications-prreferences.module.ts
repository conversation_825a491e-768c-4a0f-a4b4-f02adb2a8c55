import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { PushNotificationsController } from './notifications-preferences.controller';
import NotificationPreferences from './notifications-preferences.entity';
import { NotificationPreferencesService } from './notifications-preferences.services';


@Module({
    imports: [
        TypeOrmModule.forFeature([NotificationPreferences,]),
    ],
    controllers: [
        PushNotificationsController
    ],
    providers: [
        NotificationPreferencesService
    ],
})
export class PushNotificationModule { }
