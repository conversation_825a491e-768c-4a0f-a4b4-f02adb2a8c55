import { Injectable } from '@nestjs/common';
import { createQ<PERSON>y<PERSON><PERSON>er } from 'typeorm';
import {
  AddChecklistDto,
  AddChecklistItems,
  UpdateChecklist,
  UpdateChecklistItem,
} from '../dto/add-checklist.dto';
import ChecklistItem, { ChecklistItemStatus } from '../entity/checklist-item.entity';
import Checklist from '../entity/checklist.entity';
import Task from '../entity/task.entity';
import Activity, { ActivityType } from 'src/modules/activity/activity.entity';
import { Event_Actions } from 'src/event-listeners/actions';
import { User } from 'src/modules/users/entities/user.entity';

@Injectable()
export class ChecklistsService {
  async addChecklist(userId ,taskId: number, data: AddChecklistDto) {
    let userData = await User.findOne({ where: { id: userId }, relations: ['organization'] });
    let task = await Task.findOne({
      where: {
        id: taskId,
      },
    });

    let checklist = new Checklist();
    checklist.name = data.name.trim();
    checklist.task = task;
    checklist['userId'] = userId;
    let trimmedChecklist = data.checklistItems.map((item) => ({
      name: item.name.trim(),
      description: item.description.trim()
    }))
    checklist.checklistItems = trimmedChecklist as ChecklistItem[];

    await checklist.save();
    let checllistactivity = new Activity();
    checllistactivity.action = Event_Actions.CHECKLIST_ADDED;
    checllistactivity.actorId = userData.id;
    checllistactivity.type = ActivityType.TASK;
    checllistactivity.typeId = task.id;
    checllistactivity.remarks = `"${task.taskNumber}" Checklist "${checklist.name}" Added by ${userData.fullName}`;
    await checllistactivity.save();
    return checklist;
  }

  async findChecklists(id: number) {
    let checklists = await createQueryBuilder(Checklist, 'checklist')
      .leftJoin('checklist.task', 'task')
      .leftJoinAndSelect('checklist.checklistItems', 'checklistItems')
      .where('task.id = :id', { id })
      .getMany();
    return checklists;
  }

  async updateChecklist(userId ,data: UpdateChecklist) {
    let userData = await User.findOne({ where: { id: userId }, relations: ['organization'] });
    data.name = data.name.trim()
    data['userId'] = userId;
    let checllistactivity = new Activity();
    checllistactivity.action = Event_Actions.CHECKLIST_UPDATED;
    checllistactivity.actorId = userData.id;
    checllistactivity.type = ActivityType.TASK;
    checllistactivity.typeId = Number(data['taskId']);
    checllistactivity.remarks = `Checklist "${data.name}" Updated by ${userData.fullName}`;
    await checllistactivity.save();
    return await Checklist.save(data as Checklist);
  }

  async deleteChecklist(id: number, userId) {

    let userData = await User.findOne({ where: { id: userId }, relations: ['organization'] });
    let checklist = await Checklist.findOne({ where: { id: id }, relations: ['task'] });

    let checklistactivity = new Activity();
    checklistactivity.action = Event_Actions.CHECKLIST_DELETED;
    checklistactivity.actorId = userData.id;
    checklistactivity.type = ActivityType.TASK;
    checklistactivity.typeId = checklist.task.id;
    checklistactivity.remarks = `Checklist "${checklist.name}" Deleted by ${userData.fullName}`;
    await checklistactivity.save();

    return await Checklist.delete(id);
  }

  async addChecklistItems(userId,id: number, data: AddChecklistItems) {
    let userData = await User.findOne({ where: { id: userId }, relations: ['organization'] });
    let checklist = await Checklist.findOne({ where: { id: id }, relations: ['task'] });
    let trimmedChecklist = data.checklistItems.map((item) => ({
      name: item.name.trim(),
      description: item.description.trim()
    }))
    let checklistItems = trimmedChecklist.map((item) => ({
      ...item,
      checklist,
    }));
    let result = await ChecklistItem.save(checklistItems as ChecklistItem[]);

    for(let i of trimmedChecklist){
      let checllistactivity = new Activity();
      checllistactivity.action = Event_Actions.CHECKLIST_ITEM_ADDED;
      checllistactivity.actorId = userData.id;
      checllistactivity.type = ActivityType.TASK;
      checllistactivity.typeId = checklist.task.id;
      checllistactivity.remarks = `Checklist Item "${i.name}" Added by ${userData.fullName}`;
      await checllistactivity.save();  
    }

    return result;
  }

  async updateChecklistItem(data: UpdateChecklistItem, userId) {
    let userData = await User.findOne({ where: { id: userId }, relations: ['organization'] });
    let trimmedData = {
      name: data.name.trim(),
      description: data.description.trim(),
      id: data.id,
      status: data?.status === ChecklistItemStatus.DONE ? ChecklistItemStatus.DONE : ChecklistItemStatus.PENDING,
    }

    let checllistactivity = new Activity();
    checllistactivity.action = Event_Actions.CHECKLIST_ITEM_UPDATED;
    checllistactivity.actorId = userData.id;
    checllistactivity.type = ActivityType.TASK;
    checllistactivity.typeId = Number(data['taskId']);
    checllistactivity.remarks = `Checklist Item "${trimmedData.name}" Updated by ${userData.fullName}`;
    await checllistactivity.save();

    return await ChecklistItem.save(trimmedData as ChecklistItem);
  }

  async deleteChecklistItem(id: number, userId,taskId) {
    let userData = await User.findOne({ where: { id: userId }, relations: ['organization'] });
    const checkLsitItem = await ChecklistItem.findOne({ where : { id: id }});

    let checllistactivity = new Activity();
    checllistactivity.action = Event_Actions.CHECKLIST_ITEM_DELETED;
    checllistactivity.actorId = userData.id;
    checllistactivity.type = ActivityType.TASK;
    checllistactivity.typeId = Number(taskId);
    checllistactivity.remarks = `Checklist Item "${checkLsitItem.name}" Deleted by ${userData.fullName}`;
    await checllistactivity.save();

    return await ChecklistItem.delete(id);
  }
}
