import {
  Body,
  Controller,
  Get,
  Param,
  ParseIntPipe,
  Patch,
  Post,
  Query,
  Request,
  UploadedFile,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { JwtAuthGuard } from 'src/modules/users/jwt/jwt-auth.guard';
import { ClientGroupService } from './client-group.service';
import { UpdateClientDto } from '../clients/dto/update-client.dto';
import BulkDeleteDto from '../clients/dto/bulk-delete-dto';
import FindQueryDto from '../clients/dto/find-query.dto';
import BulkUpdateDto from '../clients/dto/bulk-update.dto';
import CreateClientGroupDto from './dto/create-client-group.dto';

@Controller('clientgroup')
export class ClientGroupController {
  constructor(private service: ClientGroupService) { }

  @UseGuards(JwtAuthGuard)
  @Post()
  create(@Request() req: any, @Body() body: any) {
    const { userId } = req.user;
    return this.service.create(userId, body);
  }

  @UseGuards(JwtAuthGuard)
  @Get()
  getAll(@Request() req: any, @Query() query: any) {
    const { userId } = req.user;
    return this.service.findAll(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Get('/:id/details')
  getOne(@Request() req: any, @Param('id', ParseIntPipe) id: number) {
    const { userId } = req.user;
    return this.service.findOne(id, userId);
  }

  @UseGuards(JwtAuthGuard)
  @Get('/all')
  getAllClientGroups(@Request() req: any, @Query() query) {
    const { userId } = req.user;
    return this.service.getAllClientGroups(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Patch('/:id/update')
  update(
    @Param('id', ParseIntPipe) id: number,
    @Body() body: UpdateClientDto,
    @Request() req: any,
  ) {
    const { userId } = req.user;
    return this.service.update(userId, id, body);
  }

  @UseGuards(JwtAuthGuard)
  @Patch('/:id/update/clients')
  updateClients(
    @Param('id', ParseIntPipe) id: number,
    @Body() body: any,
    @Request() req: any,
  ) {
    const { userId } = req.user;
    return this.service.updateClients(userId, id, body);
  }

  @UseGuards(JwtAuthGuard)
  @Patch('/bulk-update')
  bulkUpdate(@Body() body: BulkUpdateDto, @Request() req: any) {
    const { userId } = req.user;
    return this.service.bulkUpdate(body, userId);
  }

  @UseGuards(JwtAuthGuard)
  @Post('/bulk-delete')
  bulkDelete(@Request() req: any, @Body() body: BulkDeleteDto) {
    const { userId } = req.user;
    return this.service.bulkDelete(body.ids, userId);
  }

  @UseGuards(JwtAuthGuard)
  @Post('/delete-clients')
  getDeleteClients(@Request() req: any, @Query() query: FindQueryDto, @Body() payload: any) {
    const { userId } = req.user;
    return this.service.deleteGroupClients(userId, payload);
  }


  @UseGuards(JwtAuthGuard)
  @Get('/deleted')
  getDeleted(@Request() req: any, @Query() query: FindQueryDto, @Body() payload: any) {
    const { userId } = req.user;
    return this.service.findDeleted(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Post('/:id/restore')
  restoreClient(@Request() req: any, @Param('id', ParseIntPipe) id: number, @Body() body: any) {
    const { userId } = req.user;
    return this.service.restoreClient(id, userId, body);
  }

  @UseGuards(JwtAuthGuard)
  @Post('/bulk-restore')
  restoreBulkClient(@Request() req: any, @Body() body: any) {
    const { userId } = req.user;
    return this.service.restoreBulkClient(userId, body);
  }
}
