import { ArrayMinSize, IsArray, IsEnum, IsNotEmpty, IsOptional } from 'class-validator';
import { LogHourType } from 'src/modules/log-hours/log-hour.entity';

export enum BillableType {
  ALL = "ALL",
  BILLABLE = "BILLABLE",
  NON_BILLABLE = "NON_BILLABLE",
}

class getEmployeeLogHoursDto {
  @IsNotEmpty()
  @IsArray()
  @ArrayMinSize(1)
  users: Array<number>;

  @IsNotEmpty()
  fromDate: string;

  @IsNotEmpty()
  toDate: string;

  @IsOptional()
  @IsEnum(LogHourType)
  type: LogHourType;

  @IsOptional()
  @IsEnum(BillableType)
  billableType: BillableType;

}

export default getEmployeeLogHoursDto;
