import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  ParseIntPipe,
  Post,
  Put,
  Query,
  Req,
  UseGuards,
} from '@nestjs/common';
import { JwtAuthGuard } from 'src/modules/users/jwt/jwt-auth.guard';
import { AutDashboardService } from '../services/dashboard.services';

@Controller('incometax-dashboard')
export class AutDashboardController {
  constructor(private service: AutDashboardService) {}
  @UseGuards(JwtAuthGuard)
  @Get()
  findAll(@Req() req: any, @Query() query: any) {
    const { userId } = req.user;
    return this.service.findCount(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Get('returnStatus')
  statusWiseReturnsCount(@Req() req: any, @Query() query: any) {
    const { userId } = req.user;
    return this.service.statusWiseReturnsCount(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Get('returnType')
  typeWiseReturnsCount(@Req() req: any, @Query() query: any) {
    const { userId } = req.user;
    return this.service.typeWiseReturnsCount(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Get('returnVerification')
  verificationWiseReturnsCount(@Req() req: any, @Query() query: any) {
    const { userId } = req.user;
    return this.service.verificationWiseReturnsCount(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Get('demandData')
  demandRaisedfilterDates(@Req() req: any) {
    const { userId } = req.user;
    return this.service.demandRaisedfilterDates(userId);
  }

  @UseGuards(JwtAuthGuard)
  @Get('eProceedingNotices')
  eProccedidingFyiNotice(@Req() req: any, @Query() query: any) {
    const { userId } = req.user;
    return this.service.getCombinedNoticesCount(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Get('verification')
  incometaxClientCheck(@Req() req: any, @Query() query: any) {
    const { userId } = req.user;
    return this.service.incometaxClientCheck(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Post('/exportIncomeTaxInvalid')
  async exportIncomeTaxInvalid(@Req() req: any, @Body() body: any) {
    const { userId } = req?.user;
    const query = body;
    return this.service.exportIncomeTaxInvalid(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Get('/fya-events')
  getFyaEvents(@Req() req: any, @Query() query: any) {
    const { userId } = req.user;
    return this.service.getFyaEvents(userId, query.startDates);
  }

  @UseGuards(JwtAuthGuard)
  @Get('/response-due-events')
  getResponseDueEvents(@Req() req: any, @Query() query: any) {
    const { userId } = req.user;
    return this.service.getResponseDueEvents(userId, query.startDates);
  }

  @UseGuards(JwtAuthGuard)
  @Get('/manual-due-date-events')
  getManualDueDateEvents(@Req() req: any, @Query() query: any) {
    const { userId } = req.user;
    return this.service.getManualDueDateEvents(userId, query.startDates);
  }

  @UseGuards(JwtAuthGuard)
  @Get('/config-status')
  getIncometaxConfigStatus(@Req() req: any, @Query() query: any) {
    const { userId } = req.user;
    return this.service.getIncometaxConfigStatus(userId, query.startDates);
  }

  @UseGuards(JwtAuthGuard)
  @Get('/forms-udin')
  getFormsUdinAnalytics(@Req() req: any, @Query() query: any) {
    const { userId } = req.user;
    return this.service.getFormsUdinAnalytics(userId, query.assessmentYear);
  }

  @UseGuards(JwtAuthGuard)
  @Get('/fya-events-excel')
  getExcelFyaEvents(@Req() req: any, @Query() query: any) {
    const { userId } = req.user;
    return this.service.getExcelFyaEvents(userId, query.startDates);
  }

  @UseGuards(JwtAuthGuard)
  @Get('/response-due-events-excel')
  getExcelResponseDueEvents(@Req() req: any, @Query() query: any) {
    const { userId } = req.user;
    return this.service.getExcelResponseDueEvents(userId, query.startDates);
  }

  @UseGuards(JwtAuthGuard)
  @Get('eProceedingNotices-excel')
  eExcelProccedidingFyiNotice(@Req() req: any, @Query() query: any) {
    const { userId } = req.user;
    return this.service.getExcelCombinedNoticesCount(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Get('returns-file-status')
  getReturnsFilingStatus(@Req() req: any) {
    const { userId } = req.user;
    return this.service.getReturnsFilingStatus(userId);
  }

  @UseGuards(JwtAuthGuard)
  @Get('notfiled-returns')
  getNotFiledReturnsList(@Req() req: any, @Query() query: any) {
    const { userId } = req.user;
    return this.service.getNotFiledReturnsList(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Post('export-notfiled-returns')
  async exportNotFiledReturnsList(@Req() req: any, @Body() body: any) {
    const { userId } = req?.user;
    const query = body;
    return this.service.exportNotFiledReturnsList(userId, query);
  }
}
