import { MigrationInterface, QueryRunner } from 'typeorm';

export class AlterServiceTable1657354977519 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE service
          ADD category_id int null,
          ADD sub_category_id int null,
          ADD CONSTRAINT FK_service_category_id FOREIGN KEY (category_id) REFERENCES category (id) ON DELETE SET NULL,
          ADD CONSTRAINT FK_service_sub_category_id FOREIGN KEY (sub_category_id) REFERENCES category (id) ON DELETE SET NULL
    `,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}
