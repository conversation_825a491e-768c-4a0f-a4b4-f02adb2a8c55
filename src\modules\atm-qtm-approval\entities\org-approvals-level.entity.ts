import { Role } from 'src/modules/roles/entities/role.entity';
import { User } from 'src/modules/users/entities/user.entity';
import { BaseEntity, Column, Entity, ManyToOne, PrimaryGeneratedColumn } from 'typeorm';
import OrgApprovals from './org-approvals.entity';

@Entity('org_approval_level')
class OrgApprovalLevel extends BaseEntity {
    @PrimaryGeneratedColumn()
    id: number;

    @ManyToOne(() => Role, (role) => role.approvalLevels)
    role: Role;

    @ManyToOne(() => User, (user) => user.approvalLevels)
    user: User;

    @Column({ type: 'int' })
    level: number;

    @ManyToOne(() => OrgApprovals, (appHierarchy) => appHierarchy.approvalLevels, { onDelete: 'CASCADE' })
    approvalHierarchy: OrgApprovals;
}

export default OrgApprovalLevel;
