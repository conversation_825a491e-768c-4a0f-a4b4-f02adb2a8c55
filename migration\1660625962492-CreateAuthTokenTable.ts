import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateAuthTokenTable1660625962492 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
        CREATE TABLE auth_token (
            id int NOT NULL AUTO_INCREMENT,
            PRIMARY KEY (id),
            organization_id int not null,
            access_token varchar(255) not null,
            refresh_token varchar(255) not null,
            type enum('MICROSFT', 'GOOGLE') not null,
            created_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
            updated_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )            
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}
