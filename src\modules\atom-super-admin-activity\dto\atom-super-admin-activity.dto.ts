import { IsNotEmpty, IsString, IsN<PERSON>ber, IsBoolean, IsDateString, IsOptional } from 'class-validator';

class CreateAtomSuperAdminActivityDto {
  @IsNotEmpty()
  @IsString()
  type: string;

  @IsNotEmpty()
  @IsNumber()
  id: number;

  @IsOptional()
  @IsDateString()
  createdAt: string;

  @IsOptional()
  @IsDateString()
  lastUpdated: string;

  @IsOptional()
  @IsNumber()
  updatedUserId: number;

  @IsNotEmpty()
  @IsBoolean()
  toProduction: boolean;

  @IsOptional()
  serviceResponse:any;

  @IsNumber()
  prodTypeId: number;

  @IsNumber()
  refId:Number;

}

export default CreateAtomSuperAdminActivityDto;
