import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateEstimateTable1658323658619 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
        CREATE TABLE estimate (
            id int NOT NULL AUTO_INCREMENT,
            estimate_number varchar(255) NOT NULL,
            estimate_date date NOT NULL,
            estimate_due_date date NOT NULL,
            terms varchar(255) NOT NULL,
            approval_hierarchy_id int NULL,
            billing_entity_id int NULL,
            client_id int NULL,
            organization_id int NULL,
            billing_entity_address_id int NULL,
            shipping_address_id int NULL,
            billing_address_id int NULL,
            bank_details_id int NULL,
            sub_total int NOT NULL,
            total_gst_amount int NOT NULL,
            total_charges int NOT NULL,
            adjustment int NOT NULL,
            roundOff varchar(255) NOT NULL,
            grand_total int NOT NULL,
            terms_and_conditions text NOT NULL,
            created_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
            udpated_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            FOREIGN KEY (billing_entity_id) REFERENCES billing_entity(id) ON DELETE SET NULL,
            FOREIGN KEY (client_id) REFERENCES client(id) ON DELETE SET NULL,
            FOREIGN KEY (organization_id) REFERENCES organization(id) ON DELETE SET NULL,
            FOREIGN KEY (billing_entity_address_id) REFERENCES invoice_address(id) ON DELETE SET NULL,
            FOREIGN KEY (shipping_address_id) REFERENCES invoice_address(id) ON DELETE SET NULL,
            FOREIGN KEY (billing_address_id) REFERENCES invoice_address(id) ON DELETE SET NULL,
            FOREIGN KEY (bank_details_id) REFERENCES invoice_bank_details(id) ON DELETE SET NULL
        )            
 `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}
