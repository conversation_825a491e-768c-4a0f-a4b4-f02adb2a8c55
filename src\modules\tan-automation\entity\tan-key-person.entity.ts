import { BaseEntity, Column, <PERSON><PERSON><PERSON>, ManyToOne, PrimaryGeneratedColumn } from "typeorm";
import TanClientCredentials from "./tan-client-credentials.entity";

@Entity()
class TanKeyPerson extends BaseEntity {
    @PrimaryGeneratedColumn()
    id:number;

    @Column()
    name:string;

    @Column()
    pan:string;

    @Column()
    tan:string;

    @Column()
    dscFlag:string;

    @Column()
    dscExpDate:string;

    @Column()
    residentialStatus:string;

    @Column()
    mobNo:string;

    @Column()
    eMail:string;

    @Column()
    activeFlag:string;

    @Column()
    gender:string;

    @Column()
    dob:string;

    @Column()
    organizationId:number;

    @Column()
    clientId:number;

    @ManyToOne(()=> TanClientCredentials, (tanClientCredentials)=> tanClientCredentials.tanKeyPerson, { onDelete: 'SET NULL' })
    tanClientCredentials:TanClientCredentials


}

export default Tan<PERSON>eyPerson;