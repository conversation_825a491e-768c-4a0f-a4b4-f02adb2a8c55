import {
  Connection,
  EntitySubscriberInterface,
  EventSubscriber,
  InsertEvent,
  getManager,
} from 'typeorm';
import { Organization } from 'src/modules/organization/entities/organization.entity';
import { sendnewMail, sendnewMailToBusinessTeam } from 'src/emails/newemails';
import { fullMobileWithCountry } from 'src/utils/validations/fullMobileWithCountry';

@EventSubscriber()
export class OrganizationSubscriber implements EntitySubscriberInterface<Organization> {
  constructor(private readonly connection: Connection) {
    connection.subscribers.push(this);
  }

  listenTo() {
    return Organization;
  }

  async beforeInsert(event: InsertEvent<Organization>) {}

  async afterInsert(event: InsertEvent<Organization>) {
    const entityManager = getManager();
    const {
      email,
      id,
      legalName,
      buildingNo,
      floorNumber,
      street,
      city,
      district,
      state,
      pincode,
      primaryContactFullName,
      mobileNumber,
      gstNumber,
      panNumber,
      countryCode,
    } = event?.entity;
    const data = {
      organizationName: legalName,
      email: email,
    };
    const jsonData = JSON.stringify(data);
    const mailOptions = {
      data: data,
      email: email,
      filePath: 'organization-signup',
      subject: 'Organization Registration - Vider',
      key: '',
      id: 0,
    };
    const orgDetails = {
      email,
      organizationId: id,
      organizationName: legalName,
      buildingNo,
      floorNumber,
      street,
      city,
      district,
      state,
      pincode,
      primaryContactFullName,
      mobileNumber: fullMobileWithCountry(mobileNumber, countryCode),
      gstNumber,
      panNumber,
    };
    const mailOptionsForBusiness = {
      data: orgDetails,
      email: process.env.SOCIAL_EMAIL,
      filePath: 'organization-signup-business-team',
      subject: 'New Organization On-boarded to VIDER',
      key: '',
      id: 0,
    };
    // await sendnewMail(mailOptions);
    // await sendnewMailToBusinessTeam(mailOptionsForBusiness);
  }
}
