import { Injectable } from '@nestjs/common';
import { User } from 'src/modules/users/entities/user.entity';
import { Brackets, createQueryBuilder, getRepository, Like } from 'typeorm';
import axios from 'axios';
import AutProfileDetails from 'src/modules/automation/entities/aut-profile-details.entity';
import AutFyaNotice from 'src/modules/automation/entities/aut_income_tax_eproceedings_fya_notice.entity';
import AutFyiNotice from 'src/modules/automation/entities/aut_income_tax_eproceedings_fyi_notice.entity';
import AutUpdateTracker from 'src/modules/automation/entities/aut_update_tracker.entity';
import AutomationMachines, {
  TypeEnum,
} from 'src/modules/automation/entities/automation_machines.entity';

@Injectable()
export class AutProfileDetailsService {
  async findAll(userId: number, query: any) {
    let user = await User.findOne({
      where: { id: userId },
      relations: ['organization'],
    });

    const parsedQuery = JSON.parse(query.query || '{}');
    if (parsedQuery) {
      const results = await AutProfileDetails.find({
        where: {
          name: Like(`%${parsedQuery.search}%`), // Use Like to perform a case-insensitive search
        },
      });
      return results;
    } else {
      return AutProfileDetails.find();
    }
  }

  async create(userId: number, data: any) {
    let user = await User.findOne({
      where: { id: userId },
      relations: ['organization'],
    });
  }

  async createMachine(userId: number, id: any, data: any) {
    let data1 = [
      {
        modules: data?.requests,
        autoCredentialsId: id,
        type: TypeEnum.INCOMETAX,
      },
    ];
    return this.sendSingleIncometaxAutomationRequestToCamunda(userId, data1);
  }

  async sendSingleIncometaxAutomationRequestToCamunda(userId, data) {
    try {
      let config: any = {
        method: 'post',
        maxBodyLength: Infinity,
        url: `${process.env.CAMUNDA_URL}/vider/quantum/api/automation`,
        headers: {
          'X-USER-ID': userId,
          'Content-Type': 'application/json',
        },
        data: JSON.stringify(data),
      };
      const response = await axios(config);

      const responseData = response?.data;
      return responseData[JSON.stringify(data[0].autoCredentialsId)];
      if (
        responseData[JSON.stringify(data[0].autoCredentialsId)] ===
        'There is already an active request present'
      ) {
        return 'There is already an active request present';
      } else {
        return true;
      }
    } catch (error) {
      console.log('error in sendSingleIncometaxAutomationRequestToCamunda');
    }
  }

  async sendIncometaxAutomationRequestToCamunda(userId, data) {
    let config: any = {
      method: 'post',
      maxBodyLength: Infinity,
      url: `${process.env.CAMUNDA_URL}/vider/quantum/api/automation`,
      headers: {
        'X-USER-ID': userId,
        'Content-Type': 'application/json',
      },
      data: data,
    };

    axios
      .request(config)
      .then((response) => {
        // console.log(JSON.stringify(response.data));
      })
      .catch((error) => {
        console.log(error);
      });
  }

  async addIncomeTaxAutomationsTOMachineTable(userId: number, autoClientId: any, modules: any) {
    let user = await User.findOne({
      where: { id: userId },
    });
    let selectMachine = 'AUTOMATION-TEST-3';
    const automationMachines = new AutomationMachines();
    automationMachines.autoCredentials = autoClientId;
    automationMachines.modules = modules;
    // automationMachines.machineName = 'AUTOMATION-VIDER';
    automationMachines.machineName = selectMachine;
    automationMachines.type = TypeEnum.INCOMETAX;
    automationMachines.status = 'PENDING';
    automationMachines.user = user;
    await automationMachines.save();
    return true;
  }

  async bulkAutomationSync(userId: number, data: any) {
    if (data?.selectedIds) {
      let abc = [];
      for (let autoClient of data?.selectedIds) {
        abc.push({
          modules: data?.requests,
          autoCredentialsId: autoClient?.id,
          type: TypeEnum.INCOMETAX,
        });
      }

      this.sendIncometaxAutomationRequestToCamunda(userId, JSON.stringify(abc));
    }
  }

  async getclientAutoStatus(id: number) {
    const lastCompletedMachine = await AutomationMachines.findOne({
      where: { autoCredentials: id },
      order: {
        id: 'DESC',
      },
      relations: ['autoCredentials', 'autoCredentials.client'],
    });
    return lastCompletedMachine;
  }

  async getclientReport(userId: number, query: any) {
    const { limit, offset, status, remarks } = query;
    let user = await User.findOne({
      where: { id: userId },
      relations: ['organization'],
    });
    const entityManager = getRepository(AutomationMachines);

    let sql = await entityManager
      .createQueryBuilder('automationMachines')
      .leftJoinAndSelect('automationMachines.autoCredentials', 'autoCredentials')
      .leftJoinAndSelect('autoCredentials.client', 'client')
      .where('autoCredentials.organizationId = :id', { id: user.organization.id });

    if (status) {
      sql = sql.andWhere('automationMachines.status = :status', { status });
    }

    if (remarks) {
      sql = sql.andWhere('automationMachines.remarks = :remarks', { remarks });
    }

    sql = sql
      .andWhere((qb) => {
        const subQuery = qb
          .subQuery()
          .select('MAX(innerAutomationMachines.id)', 'maxId')
          .from(AutomationMachines, 'innerAutomationMachines')
          .leftJoin('innerAutomationMachines.autoCredentials', 'innerAutoCredentials')
          .where('innerAutoCredentials.organizationId = :id', { id: user.organization.id })
          .groupBy('innerAutoCredentials.id')
          .getQuery();
        return 'automationMachines.id IN ' + subQuery;
      })
      .orderBy('automationMachines.id', 'DESC')
      .limit(limit)
      .offset(offset);

    const result = await sql.getManyAndCount();
    return {
      data: result[0],
      count: result[1],
    };
  }

  async getIncometexUpdates(userId: number) {
    let user = await User.findOne({
      where: { id: userId },
      relations: ['organization'],
    });
    const autUpdates = await AutUpdateTracker.find({
      where: { organizationId: user?.organization?.id, isChange: true },
      relations: ['client'],
    });
    return autUpdates;
  }

  async getUpdatedItem(userId: any, id) {
    const updateTracker = await AutUpdateTracker.findOne({ where: { id }, relations: ['client'] });
    return updateTracker;
  }

  async getCombinedNotices(userId: number, query: any) {
    const { limit, offset, search, interval, column, assessmentYear } = query;
    const userRepository = getRepository(User);
    const user = await userRepository.findOne({
      where: { id: userId },
      relations: ['organization'],
    });

    if (!user?.organization?.id) {
      return { count: 0, result: [] };
    }

    const organizationId = user.organization.id;

    let fyaNoticeQuery = createQueryBuilder(AutFyaNotice, 'fyaNotice')
      .leftJoinAndSelect('fyaNotice.client', 'client')
      .leftJoinAndSelect('fyaNotice.responses', 'responses')
      .where('fyaNotice.organizationId = :id', { id: organizationId });

    let fyiNoticeQuery = createQueryBuilder(AutFyiNotice, 'fyiNotice')
      .leftJoinAndSelect('fyiNotice.client', 'client')
      .leftJoinAndSelect('fyiNotice.responses', 'responses')
      .where('fyiNotice.organizationId = :id', { id: organizationId });

    const applyConditions = (table: any, alias: string, column) => {
      if (column === 'responseDueDate') {
        if (interval && column) {
          const now = new Date();
          const futureDate = new Date(now);
          if (interval === 'last1week') {
            futureDate.setDate(futureDate.getDate() + 7);
          } else if (interval === 'last15days') {
            futureDate.setDate(futureDate.getDate() + 15);
          } else if (interval === 'last1month') {
            futureDate.setMonth(futureDate.getMonth() + 1);
          } else if (interval === 'last1year') {
            futureDate.setFullYear(futureDate.getFullYear() + 1);
          } else if (interval === 'none') {
          }

          if (interval === 'none') {
          } else {
            table.andWhere(`${alias}.${column} BETWEEN CURDATE() AND :futureDate`, { futureDate });
          }
        }
      }

      if (interval && column && alias && column === 'remarkSubmittedOn' && column !== 'all') {
        const now = new Date();
        let dateCondition;

        if (interval === 'last15days') {
          dateCondition = new Date(now.setDate(now.getDate() - 15));
        } else if (interval === 'last1month') {
          dateCondition = new Date(now.setMonth(now.getMonth() - 1));
        } else if (interval === 'last1week') {
          dateCondition = new Date(now.setDate(now.getDate() - 7));
        }

        if (dateCondition) {
          const responseAlias = alias === 'fyaNotice' ? 'responses' : 'responses';
          table.andWhere(`${responseAlias}.submitted_on >= :dateCondition`, { dateCondition });
        }
      }

      if (
        interval &&
        column &&
        alias &&
        column !== 'responseDueDate' &&
        column !== 'remarkSubmittedOn' &&
        column !== 'all'
      ) {
        const now = new Date();
        if (interval === 'last15days') {
          const last15days = new Date(now.setDate(now.getDate() - 15));
          table.andWhere(`${alias}.${column} >= :last15days`, { last15days });
        } else if (interval === 'last1month') {
          const last1month = new Date(now.setMonth(now.getMonth() - 1));
          table.andWhere(`${alias}.${column} >= :last1month`, { last1month });
        } else if (interval === 'last1week') {
          const last1week = new Date(now.setDate(now.getDate() - 7));
          table.andWhere(`${alias}.${column} >= :last1week`, { last1week });
        }
      }
    };

    const applyFormAndToDateFilter = (table: any, alias: string, column) => {
      if (column === 'all') {
        table.where((qb) => {
          qb.where(`${alias}.responseDueDate BETWEEN :fromDate AND :toDate`, {
            fromDate: query.fromDate,
            toDate: query.toDate,
          })
            .orWhere(`${alias}.issuedOn BETWEEN :fromDate AND :toDate`, {
              fromDate: query.fromDate,
              toDate: query.toDate,
            })
            .orWhere(`${alias}.remarkSubmittedOn BETWEEN :fromDate AND :toDate`, {
              fromDate: query.fromDate,
              toDate: query.toDate,
            });
        });
      } else {
        if (column) {
          if (column === 'remarkSubmittedOn') {
            table.andWhere('responses.submitted_on BETWEEN :fromSubDate AND :toSubDate', {
              fromSubDate: new Date(query.fromDate),
              toSubDate: new Date(query.toDate),
            });
          } else {
            table.andWhere(`DATE(${alias}.${column}) BETWEEN :fromDatee AND :toDatee`, {
              fromDatee: query.fromDate,
              toDatee: query.toDate,
            });
          }
        }
      }
    };
    if (query.fromDate && query.toDate) {
      applyFormAndToDateFilter(fyaNoticeQuery, 'fyaNotice', column);
      applyFormAndToDateFilter(fyiNoticeQuery, 'fyiNotice', column);
    } else {
      applyConditions(fyaNoticeQuery, 'fyaNotice', column);
      applyConditions(fyiNoticeQuery, 'fyiNotice', column);
    }

    if (assessmentYear) {
      fyaNoticeQuery.andWhere('fyaNotice.assesmentYear =:assessmentYear', { assessmentYear });
      fyiNoticeQuery.andWhere('fyiNotice.assessmentYear =:assessmentYear', { assessmentYear });
    }

    fyaNoticeQuery.orderBy('fyaNotice.assesmentYear', 'DESC');
    fyiNoticeQuery.orderBy('fyiNotice.assessmentYear', 'DESC');

    const [fyaResult, fyiResult] = await Promise.all([
      // fyaNoticeQuery.getManyAndCount(),
      fyaNoticeQuery.getRawMany(),
      // fyiNoticeQuery.getManyAndCount(),
      fyiNoticeQuery.getRawMany(),
    ]);

    const mapFields = (notices: any[], type: string) => {
      return notices.map((notice) => ({
        id: notice[`${type}Notice_id`],
        pan: notice[`${type}Notice_pan`],
        documentIdentificationNumber: notice[`${type}Notice_document_identification_number`],
        assessmentYear:
          notice[`${type}Notice_assessment_year`] || notice[`${type}Notice_assesment_year`],
        proceedingName: notice[`${type}Notice_proceeding_name`],
        issuedOn: notice[`${type}Notice_issued_on`],
        responseDueDate: notice[`${type}Notice_response_due_date`],
        responseSubmittedOn: notice.responses_submitted_on,
        type,
      }));
    };

    const fyaNoticesWithType = mapFields(fyaResult, 'fya');
    const fyiNoticesWithType = mapFields(fyiResult, 'fyi');
    let combinedNotices = [...fyaNoticesWithType, ...fyiNoticesWithType];

    const paginatedNotices = combinedNotices.slice(offset, offset + limit);
    const totalCount = fyaResult.length + fyiResult.length;

    return {
      count: totalCount,
      result: paginatedNotices,
    };
  }
}
