import {
  BaseEntity,
  Column,
  CreateDateColumn,
  Entity,
  ManyToOne,
  PrimaryGeneratedColumn,
} from 'typeorm';
import Task from './task.entity';
import { TaskStatusEnum } from '../dto/types';
import { User } from 'src/modules/users/entities/user.entity';

@Entity()
class TaskStatus extends BaseEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({
    type: 'enum',
    enum: TaskStatusEnum,
  })
  status: TaskStatusEnum;

  @Column({
    type: 'enum',
    enum: TaskStatusEnum,
  })
  restore: TaskStatusEnum;

  @ManyToOne(() => Task, (task) => task.taskStatus)
  task: Task;

  @ManyToOne(() => User, (user) => user.taskStatuses)
  user: User;

  @CreateDateColumn()
  createdAt: string;
}

export default TaskStatus;
