import { MigrationInterface, QueryRunner } from 'typeorm';

export class AlterLicenseTable1658117263195 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE organization_license ADD billing_entity_id int null,
       ADD CONSTRAINT fk_organization_license_billing_entity_id FOREIGN KEY (billing_entity_id) REFERENCES billing_entity (id) ON DELETE CASCADE;
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}
