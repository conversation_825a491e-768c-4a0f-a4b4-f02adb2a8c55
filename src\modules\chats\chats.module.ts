import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import ChatMessage from './chat-message.entity';
import ChatRoom from './chat-room.entity';
import { ChatsController } from './chats.controller';
import { ChatsSevices } from './chats.service';
import { ChatMessageSubscriber } from 'src/event-subscribers/chat.subscriber';

@Module({
  imports: [TypeOrmModule.forFeature([ChatRoom, ChatMessage])],
  providers: [ChatsSevices,ChatMessageSubscriber],
  controllers: [ChatsController],
})
export class ChatsModule {}
