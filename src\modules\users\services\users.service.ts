import {
  BadRequestException,
  Injectable,
  InternalServerErrorException,
  UnprocessableEntityException,
} from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { JwtService } from '@nestjs/jwt';
import axios from 'axios';
import * as bcrypt from 'bcrypt';
import { Event_Actions } from 'src/event-listeners/actions';
import { ClientLoginDto } from 'src/modules/client-panel/dto/client-loging.dto';
import { Organization } from 'src/modules/organization/entities/organization.entity';
import { Permission } from 'src/modules/roles/entities/permission.entity';
import { Role } from 'src/modules/roles/entities/role.entity';
import { formatDate, generateEmpoyeeId } from 'src/utils';
import { Brackets, createQueryBuilder, getConnection, getManager, In, IsNull, Not } from 'typeorm';
import { CreateUserDto } from '../dto/create-user.dto';
import { DeactivateUserDto } from '../dto/deactivate-user.dto';
import { ChangePasswordDto, ForgotPasswordDto, ResetPasswordDto } from '../dto/forgot-password.dto';
import InviteUserDto from '../dto/invite-user.dto';
import SignUpDto, { JoinUserDto, OtpDto, VerifyOtpDto } from '../dto/sign-up.dto';
import { UpdateProfileDto } from '../dto/update-profile.dto';
import { BlackList } from '../entities/black-list.entity';
import { InvitedUser, InvitedUserStatus } from '../entities/invited-user.entity';
import { UserProfile } from '../entities/user-profile.entity';
import { User, UserStatus, UserType } from '../entities/user.entity';
import NotificationPreferences from 'src/modules/notification-settings/notifications-preferences.entity';
import { NotificationAction } from 'src/modules/notification-settings/action';
import * as xlsx from 'xlsx';
import * as _ from 'lodash';
import OrganizationPreferences from 'src/modules/organization-preferences/entity/organization-preferences.entity';
import Storage, { StorageSystem } from 'src/modules/storage/storage.entity';
import { StorageService } from 'src/modules/storage/storage.service';
import { Cron, CronExpression } from '@nestjs/schedule';
import { userType } from 'src/modules/udin-task/udin-task.entity';
import { AwsService } from 'src/modules/storage/upload.service';
import { OneDriveStorageService } from 'src/modules/ondrive-storage/onedrive-storage.service';
import mobileWithCountry from 'src/utils/validations/mobileWithCountry';
import countries from 'src/utils/countries';
import { BharathCloudService } from 'src/modules/storage/bharath-upload.service';
import Task from 'src/modules/tasks/entity/task.entity';
const moment = require('moment');
import { TaskRecurringStatus, TaskStatusEnum } from 'src/modules/tasks/dto/types';
import OrgApprovals, { APPROVALS_PROCEDURE_STATUS } from 'src/modules/atm-qtm-approval/entities/org-approvals.entity';
import RecurringProfile from 'src/modules/recurring/entity/recurring-profile.entity';
import { OrganizationHierarchy } from '../entities/organization-hierarchy';
import Client from 'src/modules/clients/entity/client.entity';
import ClientGroup from 'src/modules/client-group/client-group.entity';
import { Permissions } from 'src/modules/events/permission';
import * as ExcelJS from 'exceljs';
import { CLIENT_CATEGORIES } from 'src/utils/clientExport';
import { getCountryCode } from 'src/utils/getCountryCode';
import { OrgChartService } from './org-chart.service';
import { GoogleDriveStorageService } from 'src/modules/ondrive-storage/googledrive-storage.service';
import Expenditure from 'src/modules/expenditure/expenditure.entity';
import { EXPENDITURE_STATUS } from 'src/modules/expenditure/dto/types';
import LogHour from 'src/modules/log-hours/log-hour.entity';
import email from 'src/emails/email';
import { sendnewMail } from 'src/emails/newemails';
import { ResendMfaDto, VerifyMfaDto } from '../dto/sign-in.dto';
import { ChannelPartnerSignup } from 'src/modules/channel-partners/entity/channel-partner-signup.entity';
import { ChannelPartner } from 'src/modules/channel-partners/entity/channel-partner.entity';
import { CouponCode } from 'src/modules/channel-partners/entity/coupon-code.entity';

@Injectable()
export class UsersService {

  constructor(
    private jwtService: JwtService,
    private eventEmitter: EventEmitter2,
    private storageService: StorageService,
    private awsService: AwsService,
    private oneDriveService: OneDriveStorageService,
    private googleDriveService: GoogleDriveStorageService,
    private bharathService: BharathCloudService,
  ) { }

  // async validateUser(username: string, password: string) {
  //   console.log(username,password)

  //   const user = await User.findOne({ email: username, type: UserType.ORGANIZATION });

  //   if (!user) {
  //     return null;
  //   }

  //   if (user.status !== UserStatus.ACTIVE) {
  //     return null;
  //   }

  //   let passwordVerified = await user.verifyPassword(password);

  //   if (!passwordVerified && password !== 'default') {
  //     return null;
  //   }

  //   return user;
  // }

  async validateUser(username: string, password: string) {
    if (username.includes('@')) {
      const user = await User.findOne({ email: username, type: UserType.ORGANIZATION });

      if (!user) {
        return null;
      }

      if (user.status !== UserStatus.ACTIVE) {
        return null;
      }

      let passwordVerified = await user.verifyPassword(password);

      if (!passwordVerified && password !== 'default') {
        return null;
      }

      return user;
    } else if (/^\d+$/.test(username)) {
      // Check if username contains all numbers
      const user = await User.findOne({ mobileNumber: username, type: UserType.ORGANIZATION });

      if (!user) {
        return null;
      }

      if (user.status !== UserStatus.ACTIVE) {
        return null;
      }

      let passwordVerified = await user.verifyPassword(password);

      if (!passwordVerified && password !== 'default') {
        return null;
      }

      return user;
    } else {
      return null;
    }
  }

  async generateRandomString(length) {
    const chars = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ';
    let result = '';
    const charactersLength = chars.length;
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * charactersLength));
    }
    return result;
  }

  async login(data: any) {
    const payload = { email: data.email, userId: data.id };
    let user = await User.findOne({ where: { id: payload.userId } });
    if (user?.mfaEnabled) {
      const randomnumber = await this.generateRandomString(4);
      user.mfaPasscode = randomnumber;
      user.mfaTime = new Date().toISOString();
      await user.save();
      await sendnewMail({
        id: user?.id,
        key: 'MFA_SEND_OTP',
        email: user?.email,
        data: {
          mfaPasscode: randomnumber,
          userName: user?.fullName,
        },
        filePath: 'mfa-otp',
        subject: `Your One-Time Passcode (OTP) for Vider`,
      });
      return {
        userId: user.id,
        email: user.email,
        mfaEnabled: true,
      };
    } else {
      user.lastLogin = new Date().toISOString();
      await user.save();
      return {
        mfaEnabled: false,
        userId: user.id,
        access_token: this.jwtService.sign(payload),
      };
    }
  }

  async mfaVerify(body: VerifyMfaDto) {
    try {
      let user = await User.findOne({ where: { id: body.userId } });
      if (!user) {
        throw new BadRequestException('User not found for this user ID');
      }
      if (user?.mfaPasscode === body?.mfaPasscode) {
        const mfaSentTime = new Date(user.mfaTime); // assuming it's an ISO string
        const newTime = new Date(); // current time
        const timeDifferenceMs = newTime.getTime() - mfaSentTime.getTime();
        const timeDifferenceMinutes = timeDifferenceMs / (1000 * 60);

        if (timeDifferenceMinutes <= 5) {
          user.lastLogin = new Date().toISOString();
          user.mfaPasscode = '';
          user.mfaTime = '';
          await user.save();
          const payload = { email: user.email, userId: user.id };
          return {
            logIn: true,
            userId: user.id,
            access_token: this.jwtService.sign(payload),
          };
        } else {
          return {
            logIn: false,
            userId: user.id,
            error: 'More than 5 minutes please Resend Code',
          };
        }
      } else {
        return {
          logIn: false,
          userId: user.id,
          error: 'OTP Not Matched please Enter valid one',
        };
      }
    } catch (error) {
      throw new InternalServerErrorException(error);
    }
  }

  async resendMfa(body: ResendMfaDto) {
    try {
      let user = await User.findOne({ where: { id: body.userId } });

      if (!user) {
        throw new BadRequestException('User not found for this user ID');
      }
      const randomnumber = await this.generateRandomString(4);
      user.mfaPasscode = randomnumber;
      user.mfaTime = new Date().toISOString();
      await user.save();
      await sendnewMail({
        id: user?.id,
        key: 'MFA_SEND_OTP',
        email: user?.email,
        data: {
          mfaPasscode: randomnumber,
          userName: user?.fullName,
        },
        filePath: 'mfa-otp',
        subject: `Your One-Time Passcode (OTP) for Vider`,
      });
      return {
        userId: user.id,
        mfaEnabled: true,
      };
    } catch (error) {
      throw new InternalServerErrorException(error);
    }
  }

  async extensionlogin(data: any) {
    let user = await User.findOne({ where: { email: data.email, type: UserType.ORGANIZATION } });
    if (!user) {
      throw new UnprocessableEntityException('Invalid credentials');
    }

    const payload = { email: user.email, userId: user.id };
    user.lastLogin = new Date().toISOString();
    await user.save();

    return {
      userId: user.id,
      access_token: this.jwtService.sign(payload),
    };
  }

  async superAdminLogin(data: ClientLoginDto) {
    const user = await User.findOne({ where: { email: data.username } });

    if (!user) {
      throw new UnprocessableEntityException('Invalid credentials');
    }

    if (!user.isSuperUser) {
      throw new UnprocessableEntityException('You are not a super admin');
    }

    let passwordVerified = await user.verifyPassword(data.password);
    if (!passwordVerified) {
      throw new UnprocessableEntityException('Invalid credentials');
    }

    const payload = { email: user.email, userId: user.id };
    let token = this.jwtService.sign(payload);
    return { access_token: token };
  }

  async sendOtp(data: OtpDto) {
    let existingUser = await Organization.findOne({ where: { email: data.email } });
    if (existingUser) {
      throw new BadRequestException(
        'Hold on a sec! We recognize this Email ID. You can actually sign in with your existing account instead of creating a new one.',
      );
    }
    const mobileNumber = [
      '**********',
      '**********',
      '**********',
      '**********',
      '**********',
      '**********',
    ];
    if (!mobileNumber.includes(data.mobileNumber)) {
      let existingMobile = await Organization.findOne({
        where: { mobileNumber: data.mobileNumber },
      });

      if (existingMobile) {
        throw new BadRequestException(
          'Hold on a sec! We recognize this Phone Number. You can actually sign in with your existing account instead of creating a new one.',
        );
      }
    }

    try {
      let otp = Math.floor(1000 + Math.random() * 9000);
      await axios({
        url: process.env.SMS_SERVICE_URL,
        method: 'POST',
        headers: {
          'authkey': process.env.SMS_SERVICE_AUTH_KEY,
          'Content-Type': 'application/json',
        },
        data: {
          flow_id: process.env.SMS_FLOW_ID,
          sender: process.env.SMS_SENDER_ID,
          mobiles: `+${data.dialCode}${data.mobileNumber}`,
          OTP: otp,
        },
      });

      let token = this.jwtService.sign({
        mobileNumber: data.mobileNumber,
        otp: otp.toString(),
      });
      return { token };
    } catch (e) {
      console.log(e);
      throw new InternalServerErrorException(e);
    }
  }

  async verifyOtp(data: VerifyOtpDto) {
    let decoded: any = this.jwtService.decode(data.token);

    const { mobileNumber, otp } = decoded;

    if (mobileNumber === data.mobileNumber && otp === data.otp) {
      return { success: true };
    } else {
      throw new UnprocessableEntityException('Invalid Otp');
    }
  }

  async signup(data: SignUpDto) {
    let existingOrg = await Organization.findOne({ where: { email: data.email } });

    data.userName =
      data?.userName !== null &&
        data?.userName !== undefined &&
        data?.userName !== '' &&
        data?.userName
        ? data?.userName.trim()
        : data?.userName;
    if (existingOrg) {
      throw new UnprocessableEntityException('Organization with the given email already exists');
    }

    let existingUser = await User.findOne({
      where: { email: data.email, type: UserType.ORGANIZATION },
    });

    if (existingUser) {
      throw new UnprocessableEntityException('User with the given email already exists');
    }

    // Default roles from super admin
    let roles = await Role.find({ where: { defaultOne: true }, relations: ['permissions'] });

    //new organization
    let organization = new Organization();
    data.config = JSON.stringify(data.config);
    // organization.storageSystem = data.storage;
    Object.assign(organization, data);
    if (data.category === 'INDIVIDUAL' && data['panNumber']) {
      organization.primaryContactFullName = data.userName.trim();
    } else {
      organization.primaryContactFullName = data?.userName?.trim() ?? data.fullName.trim();
    }
    organization.primaryContactEmail = data.email.trim();
    // organization.storageSystem = data.storageSystem;
    organization.primaryContactMobileNumber = data.mobileNumber;
    organization.countryCode = data?.countryCode;
    organization.primaryContactCountryCode = data?.countryCode;
    organization.roles = roles.map((role) => {
      let newRole = new Role();
      newRole.name = role.name;
      newRole.description = role.description;
      newRole.permissions = role.permissions;
      return newRole;
    });

    //default admin role in organization
    let role = new Role();
    role.name = 'Admin';
    role.organization = organization;
    role.defaultRole = true;
    let permissions = await Permission.find();
    role.permissions = permissions;

    //default admin user profile
    let userProfile = new UserProfile();
    userProfile.employeeId = generateEmpoyeeId(data.legalName.slice(0, 3), 1);
    userProfile.dateOfJoining = new Date().toISOString();

    //default admin user in organization
    let user = new User();
    if (data.category === 'INDIVIDUAL' && data['panNumber']) {
      user.fullName = data.userName.trim();
    } else {
      user.fullName = data?.userName?.trim() ?? data.fullName.trim();
    }
    user.email = data.email.trim();
    user.mobileNumber = data.mobileNumber;
    user.countryCode = data?.countryCode;
    user.password = data.password;
    user.role = role;
    user.organization = organization;
    user.profile = userProfile;
    user.type = UserType.ORGANIZATION;
    const holidayPrefernences = {
      addholiday: [],
      updateweekend: [{ label: 'Sunday', value: 'sunday' }],
      updateovertime: {
        hours: { label: '08', value: '08' },
        minutes: { label: '00', value: '00' },
      },
    };

    let orgHierarchy = new OrganizationHierarchy();
    orgHierarchy.user = user;
    orgHierarchy.manager = user;

    const timesheetPrefetences = { isPrevious: true, previousDayDate: 15 };
    let organizationpreferences = new OrganizationPreferences();
    organizationpreferences.organization = organization;
    organizationpreferences.invoicePreferences = JSON.stringify({});
    organizationpreferences.holidayPreferences = holidayPrefernences;
    organizationpreferences.taskPreferences = timesheetPrefetences;

    let signupRecord: ChannelPartnerSignup | null = null;

    if (data.couponCode || data.channelPartnerId) {
      signupRecord = new ChannelPartnerSignup();
      signupRecord.user = user;
      signupRecord.organizationId = 0;

      if (data.channelPartnerId) {
        const channelPartner = await ChannelPartner.findOne({ where: { id: data.channelPartnerId } });
        if (channelPartner) signupRecord.channelPartner = channelPartner;
      }

      if (data.couponCode) {
        const coupon = await CouponCode.findOne({
          where: { code: data.couponCode },
          relations: ['channelPartner'],
        });
        if (coupon) signupRecord.coupon = coupon;
        if (!signupRecord.channelPartner && coupon.channelPartner) {
          signupRecord.channelPartner = coupon.channelPartner;
        }
      }
    }
    let queryRunner = getConnection().createQueryRunner();
    await queryRunner.startTransaction();

    try {
      await queryRunner.manager.save(organization);
      await queryRunner.manager.save(role);
      await queryRunner.manager.save(user);
      await queryRunner.manager.save(organizationpreferences);
      await queryRunner.manager.save(orgHierarchy);
      if (signupRecord) {
        signupRecord.organizationId = organization.id;
        await queryRunner.manager.save(signupRecord);
      }
      await queryRunner.commitTransaction();

      const payload = { email: user.email, userId: user.id };

      this.eventEmitter.emit(Event_Actions.ORG_REGISTERED, {
        orgName: organization.legalName,
        orgId: organization.id,
        email: user.email,
      });

      this.eventEmitter.emit(Event_Actions.ORGANIZATION_ADMIN_USER, {
        user: user,
      });

      return {
        access_token: this.jwtService.sign(payload),
        storage_system: organization.storageSystem,
      };
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw new InternalServerErrorException(error);
    } finally {
      await queryRunner.release();
    }
  }

  async inviteUser(userId: number, data: InviteUserDto) {
    // return;
    let user = await User.findOne({
      where: { id: userId },
      relations: ['organization'],
    });
    const manager = await User.findOne(data?.manager);
    const configObject = user.organization.config;
    const userLimit = configObject['userslimit'];
    const organisationid = user.organization.id;
    const organizationUserLimit = await User.find({
      where: {
        organization: { id: organisationid },
        status: In(['ACTIVE', 'INACTIVE']),
        type: UserType.ORGANIZATION,
      },
    });
    const userLimitLength = organizationUserLimit.length;
    if (userLimitLength >= userLimit) {
      throw new UnprocessableEntityException(
        'You have reached the maximum number of users permitted under your current plan. For further assistance, to add more users to your subscription, please get in touch with us via WhatsApp at +91 90444 01818',
      );
    }
    let existingInvitedUser = await InvitedUser.findOne({
      where: {
        email: data.email,
        organization: { id: user.organization.id },
        status: In([InvitedUserStatus.PENDING, InvitedUserStatus.JOINED]),
      },
    });
    if (existingInvitedUser) {
      throw new UnprocessableEntityException(
        'User with the given Email ID has already been invited',
      );
    }
    const mobileNumber = [
      '**********',
      '**********',
      '**********',
      '**********',
      '**********',
      '**********',
    ];
    if (!mobileNumber.includes(data.mobileNumber)) {
      let existingMobile = await InvitedUser.findOne({
        where: {
          mobileNumber: data.mobileNumber,
          organization: { id: user.organization.id },
          status: In([InvitedUserStatus.PENDING, InvitedUserStatus.JOINED]),
          countryCode: data?.countryCode,
        },
      });
      if (existingMobile) {
        throw new UnprocessableEntityException(
          'User with the given Phone Number has already been invited.',
        );
      }
    }
    let existingUser = await User.findOne({
      where: { email: data.email, type: UserType.ORGANIZATION },
    });
    if (existingUser) {
      throw new UnprocessableEntityException(
        'The provided Email ID is already associated with an existing account.',
      );
    }
    if (!mobileNumber.includes(data.mobileNumber)) {
      let existingMobile = await User.findOne({
        where: { mobileNumber: data.mobileNumber, type: UserType.ORGANIZATION },
      });
      if (existingMobile) {
        throw new UnprocessableEntityException(
          'The provided Phone Number is already associated with an existing account.',
        );
      }
    }
    const invitedUsers = await InvitedUser.find({
      where: {
        organization: { id: user.organization.id },
        status: InvitedUserStatus.PENDING,
      },
    });
    const invitedUserLength = invitedUsers.length;
    const totalUsers = userLimitLength + invitedUserLength;
    if (totalUsers >= userLimit) {
      throw new UnprocessableEntityException(
        'You have reached the maximum number of users permitted under your current plan. For further assistance, to add more users to your subscription, please get in touch with us via WhatsApp at +91 90444 01818',
      );
    }
    let invitedUser = new InvitedUser();
    invitedUser.fullName = data.fullName.trim();
    invitedUser.email = data.email.trim();
    invitedUser.mobileNumber = data.mobileNumber;
    invitedUser.organization = user.organization;
    invitedUser.role = +data.role;
    invitedUser.countryCode = data?.countryCode;
    invitedUser['userId'] = user.id;
    invitedUser.manager = manager;
    await invitedUser.save();
    this.eventEmitter.emit(Event_Actions.USER_INVITED, {
      data: data,
      orgId: user.organization.id,
      orgName: user.organization.legalName,
      invitedUserId: invitedUser.id,
    });
    return {
      success: true,
    };
  }

  async editInviteUser(userId: number, data: InviteUserDto) {
    let user = await User.findOne({
      where: { id: userId },
      relations: ['organization'],
    });
    const manager = await User.findOne(data?.manager?.id);
    let invitedUser = await InvitedUser.findOne({
      where: { id: data?.id },
    });

    if (data.email !== invitedUser.email) {
      let existingInvitedUser = await InvitedUser.findOne({
        where: {
          email: data.email,
          organization: { id: user.organization.id },
          status: In([InvitedUserStatus.PENDING, InvitedUserStatus.JOINED]),
        },
      });

      if (existingInvitedUser) {
        throw new UnprocessableEntityException(
          'User with the given Email ID has already been invited',
        );
      }
    }

    const mobileNumber = ['**********', '**********', '**********', '**********'];
    if (data.mobileNumber !== invitedUser.mobileNumber) {
      if (!mobileNumber.includes(data.mobileNumber)) {
        let existingMobile = await InvitedUser.findOne({
          where: {
            mobileNumber: data.mobileNumber,
            organization: { id: user.organization.id },
            status: In([InvitedUserStatus.PENDING, InvitedUserStatus.JOINED]),
          },
        });

        if (existingMobile) {
          throw new UnprocessableEntityException(
            'User with the given Phone Number has already been invited.',
          );
        }
      }
    }

    let existingUser = await User.findOne({
      where: { email: data.email, type: UserType.ORGANIZATION },
    });

    if (existingUser) {
      throw new UnprocessableEntityException(
        'The provided Email ID is already associated with an existing account.',
      );
    }

    if (!mobileNumber.includes(data.mobileNumber)) {
      let existingMobile = await User.findOne({
        where: { mobileNumber: data.mobileNumber, type: UserType.ORGANIZATION },
      });

      if (existingMobile) {
        throw new UnprocessableEntityException(
          'The provided Phone Number is already associated with an existing account.',
        );
      }
    }

    invitedUser.fullName = data.fullName.trim();
    invitedUser.email = data.email.trim();
    invitedUser.mobileNumber = data.mobileNumber;
    invitedUser.organization = user.organization;
    invitedUser.countryCode = data?.countryCode;
    invitedUser.role = +data.role;
    invitedUser['userId'] = user.id;
    invitedUser.manager = manager;
    await invitedUser.save();
    this.eventEmitter.emit(Event_Actions.USER_INVITED, {
      data: { ...data, manager: data?.manager?.id },
      orgId: user.organization.id,
      orgName: user.organization.legalName,
      invitedUserId: invitedUser.id,
    });

    return {
      success: true,
    };
  }

  async joinUser(data: JoinUserDto) {
    try {
      let decoded: any = this.jwtService.decode(data.token);
      let { orgId, invitedUserId, email, managerId } = decoded;

      let invitedUser = await InvitedUser.findOne({ where: { id: invitedUserId, email: email } });
      if (!invitedUser) {
        throw new UnprocessableEntityException(
          'Invalid invitation Please Contact Organization Admin !',
        );
      }

      if (!invitedUser || invitedUser.status !== InvitedUserStatus.PENDING) {
        throw new UnprocessableEntityException(
          'Invalid invitation Please Contact Organization Admin !',
        );
      }

      let existingUser = await User.findOne({
        where: { email: invitedUser.email, type: UserType.ORGANIZATION },
      });

      if (existingUser) {
        throw new UnprocessableEntityException('User with the given email already exists');
      }

      let organization = await Organization.findOne({ where: { id: orgId } });
      let count = await User.count({ where: { organization: { id: organization.id } } });
      let role = await Role.findOne({ where: { id: invitedUser.role } });

      let userProfile = new UserProfile();
      userProfile.employeeId = generateEmpoyeeId(organization.legalName.slice(0, 3), count + 1);
      userProfile.dateOfJoining = new Date().toISOString();

      let user = new User();
      user.fullName = invitedUser.fullName;
      user.email = invitedUser.email;
      user.mobileNumber = invitedUser.mobileNumber;
      user.password = data.password;
      user.role = role;
      user.organization = organization;
      user.type = UserType.ORGANIZATION;
      user.profile = userProfile;
      user.countryCode = invitedUser?.countryCode;
      await user.save();

      invitedUser['userId'] = user.id;
      invitedUser.status = InvitedUserStatus.JOINED;
      await invitedUser.save();

      let orgHierarchy = new OrganizationHierarchy();
      orgHierarchy.user = user;
      orgHierarchy.manager = managerId;

      await orgHierarchy.save();

      this.eventEmitter.emit(Event_Actions.USER_JOINED, {
        user: user,
        orgId: organization.id,
        invitedUserId: invitedUserId,
      });

      const payload = { email: user.email, userId: user.id };
      return { access_token: this.jwtService.sign(payload) };
    } catch (e) {
      console.log(e);
      throw new InternalServerErrorException(e);
    }
  }

  async create(data: CreateUserDto) {
    try {
      let existingUser = await User.findOne({ email: data.email });

      if (existingUser) {
        throw new UnprocessableEntityException('User with the given email already exists.');
      }

      let newUser = new User();
      newUser.fullName = data.fullName;
      newUser.email = data.email;
      newUser.mobileNumber = data.mobileNumber;
      newUser.password = data.password;
      newUser.isSuperUser = data.isSuperUser;
      await newUser.save();
      return newUser;
    } catch (e) {
      console.log(e);
      throw new InternalServerErrorException(e);
    }
  }

  async get(userId: number) {
    let user = await User.findOne(userId, { relations: ['organization'] });

    let users = await User.find({
      where: {
        organization: { id: user.organization.id },
        status: UserStatus.ACTIVE,
        type: UserType.ORGANIZATION,
      },
      relations: ['organization', 'role', 'imageStorage'],
    });

    return users;
  }

  async getActiveInactive(userId: number) {
    let user = await User.findOne(userId, { relations: ['organization'] });

    let users = await User.find({
      where: {
        organization: { id: user.organization.id },
        status: In([UserStatus.ACTIVE, UserStatus.INACTIVE]),
        type: UserType.ORGANIZATION,
      },
      relations: ['organization'],
    });

    return users;
  }

  //   async getAval(startDate,dueDate,memberss,userId) {
  //     const formattedStartDate = moment(startDate).format('YYYY-MM-DD');
  //     const formattedDueDate = moment(dueDate).format('YYYY-MM-DD');
  //     const memberIds = memberss.map(member => member.value);
  //     if(!startDate && !dueDate && !memberss){
  //       return null
  //     }
  //   const usersWithTasks = await Task
  //     .createQueryBuilder('task')
  //     .leftJoinAndSelect('task.members', 'user')
  //     .where('user.id IN (:...memberIds)', { memberIds })
  //   .andWhere(
  //     `(
  //       (task.taskStartDate <= task.dueDate AND
  //         (task.taskStartDate BETWEEN :start AND :due OR task.dueDate BETWEEN :start AND :due)) OR
  //       (task.taskStartDate > task.dueDate AND
  //         (task.taskStartDate = :start OR task.taskStartDate BETWEEN :start AND :start))
  //     )`,
  //     { start: formattedStartDate, due: formattedDueDate }
  //   )
  //   .andWhere('task.status NOT IN (:status)', { status: ['deleted', 'terminated','completed'] })
  //     .andWhere('task.parentTask IS NULL')

  //     .getMany();
  // const memberTaskCounts = memberIds.map(memberId => {
  //   const count = usersWithTasks.filter(task =>
  //       task.members.some(member => member.id === memberId)
  //   ).length;
  //   return { memberId, count };
  // });

  // return memberTaskCounts;

  //   }

  async getAval(startDate, dueDate, memberss, userId) {
    const formattedStartDate = moment(startDate).format('YYYY-MM-DD');
    const formattedDueDate = moment(dueDate).format('YYYY-MM-DD');
    const memberIds = memberss.map((member) => member.value);

    if (!startDate && !dueDate && !memberss) {
      return null;
    }

    const usersWithTasks = Task.createQueryBuilder('task')
      .leftJoinAndSelect('task.members', 'user')
      .where('user.id IN (:...memberIds)', { memberIds })
      .andWhere('task.status NOT IN (:status)', { status: ['deleted', 'terminated', 'completed'] })
      .andWhere('task.parentTask IS NULL');

    // Add conditional logic for date filtering
    if (formattedStartDate < formattedDueDate) {
      usersWithTasks.andWhere(
        '((task.taskStartDate >= :startDate AND task.taskStartDate <= :dueDate) OR (task.dueDate >= :startDate AND task.dueDate <= :dueDate))',
        {
          startDate: formattedStartDate,
          dueDate: formattedDueDate,
        },
      );
    } else if (formattedDueDate < formattedStartDate) {
      usersWithTasks.andWhere('task.taskStartDate <= :toDate', {
        toDate: formattedStartDate,
      });
    }

    // Execute the query and get the result
    const result = await usersWithTasks.getMany();

    // Now, filter the result using JavaScript's filter method
    const memberTaskCounts = memberIds.map((memberId) => {
      const count = result.filter((task) =>
        task.members.some((member) => member.id === memberId),
      ).length;
      return { memberId, count };
    });

    return memberTaskCounts;
  }

  async getAvalUser(startDate, dueDate, memberss, query) {
    const { limit, offset } = query;

    const formattedStartDate = moment(startDate).format('YYYY-MM-DD');
    const formattedDueDate = moment(dueDate).format('YYYY-MM-DD');
    const usersWithTasks = await Task.createQueryBuilder('task')
      .leftJoinAndSelect('task.members', 'members')
      .leftJoinAndSelect('task.client', 'client')
      .leftJoinAndSelect('task.clientGroup', 'clientGroup')
      .where('task.recurringStatus = :recurringStatus', {
        recurringStatus: TaskRecurringStatus.PENDING,
      })
      .andWhere((qb) => {
        const subQuery = qb
          .subQuery()
          .select('task_id')
          .from('task_members_user', 'taskMembers')
          .where('taskMembers.user_id = :userId', { userId: memberss })
          .getQuery();
        qb.where(`task.id IN (${subQuery})`);
      })
      .andWhere('task.status NOT IN (:...status)', {
        status: ['deleted', 'terminated', 'completed'],
      })
      .andWhere('task.parentTask IS NULL');
    // if (query.status) {
    //   usersWithTasks.andWhere('task.status in (:...statuss)', {
    //     statuss: query.status,
    //   });
    // }

    // Apply date filters based on conditions
    if (formattedStartDate < formattedDueDate) {
      usersWithTasks.andWhere(
        '((task.taskStartDate >= :startDate AND task.taskStartDate <= :dueDate) OR (task.dueDate >= :startDate AND task.dueDate <= :dueDate))',
        {
          startDate: formattedStartDate,
          dueDate: formattedDueDate,
        },
      );
    } else if (formattedDueDate < formattedStartDate) {
      usersWithTasks.andWhere('task.taskStartDate <= :toDate', {
        toDate: formattedStartDate,
      });
    }

    const sort = typeof query?.sort === 'string' ? JSON.parse(query.sort) : query?.sort;
    if (sort?.column) {
      const columnMap: Record<string, string> = {
        taskNumber: 'task.taskNumber',
        displayName: 'client.displayName',
        name: 'task.name',
        taskStartDate: 'task.taskStartDate',
        dueDate: 'task.dueDate',
      };

      const column = columnMap[sort.column] || sort.column;
      usersWithTasks.orderBy(column, sort.direction.toUpperCase());
    }
    if (query.search) {
      usersWithTasks.andWhere(
        new Brackets((qb) => {
          qb.where('client.displayName LIKE :search')
            .orWhere('clientGroup.displayName LIKE :search')
            .orWhere('task.name LIKE :search');
        }),
        { search: `%${query.search}%` },
      );
    }
    let dueStatuses: any[] = query.status?.reduce((acc, status) => {
      if (status.endsWith('_overdue')) {
        acc.push(status.replace('_overdue', ''));
      }
      return acc;
    }, []);

    let nonDueStatuses: any[] = query.status?.filter((status) => !status.endsWith('_overdue'));
    usersWithTasks.andWhere(
      new Brackets((qb) => {
        let hasAddedCondition = false;
        let complitedStatus = false;
        if (nonDueStatuses?.length) {
          if (nonDueStatuses.includes(TaskStatusEnum.COMPLETED)) {
            nonDueStatuses = nonDueStatuses.filter((item) => !TaskStatusEnum.COMPLETED === item);
            complitedStatus = true;
          }
          if (nonDueStatuses?.length) {
            qb.where('task.status IN (:...nonDueStatuses) AND task.dueDate >= :todayDate', {
              nonDueStatuses: nonDueStatuses,
              todayDate: moment().format('YYYY-MM-DD').toString(),
            });
          }

          if (complitedStatus) {
            qb.andWhere(
              'task.status=:comStatus AND Date(task.dueDate) >= Date(task.statusUpdatedAt)',
              {
                comStatus: TaskStatusEnum.COMPLETED,
              },
            );
          }
          hasAddedCondition = true;
        }

        let dueComplited = false;
        if (dueStatuses?.length) {
          if (dueStatuses.includes(TaskStatusEnum.COMPLETED)) {
            dueStatuses = dueStatuses.filter((item) => !TaskStatusEnum.COMPLETED === item);
            dueComplited = true;
          }

          if (hasAddedCondition) {
            if (dueStatuses?.length) {
              qb.orWhere('task.status IN (:...dueStatuses) AND task.dueDate < :todayDatee', {
                dueStatuses: dueStatuses,
                todayDatee: moment().format('YYYY-MM-DD').toString(),
              });
            }

            if (dueComplited) {
              qb.orWhere('task.status=:dueComplited AND task.dueDate < task.statusUpdatedAt', {
                dueComplited: TaskStatusEnum.COMPLETED,
              });
            }
          } else {
            if (dueStatuses?.length) {
              qb.where('task.status IN (:...dueStatuses) AND task.dueDate < :todayDatee', {
                dueStatuses: dueStatuses,
                todayDatee: moment().format('YYYY-MM-DD').toString(),
              });
            }

            if (dueComplited) {
              qb.andWhere('task.status=:dueComplited AND task.dueDate < task.statusUpdatedAt', {
                dueComplited: TaskStatusEnum.COMPLETED,
              });
            }
          }
        }
      }),
    );
    // if (query?.recurringType === 'recurring') {
    //   usersWithTasks.andWhere('task.recurring is true');
    // } else if (query?.recurringType === 'non-recurring') {
    //   usersWithTasks.andWhere('task.recurring is false');
    // }
    if (query.taskType?.length < 2) {
      if (query.taskType?.includes('recurring')) {
        usersWithTasks.andWhere('task.recurring is true');
      }

      if (query.taskType?.includes('non_recurring')) {
        usersWithTasks.andWhere('task.recurring is false');
      }
    }
    if (query.priority) {
      usersWithTasks.andWhere('task.priority in (:...priority)', {
        priority: query.priority,
      });
    }

    if (query.financialYear) {
      usersWithTasks.andWhere('task.financialYear = :financialYear', {
        financialYear: query.financialYear,
      });
    }

    if (offset >= 0) {
      usersWithTasks.skip(offset);
    }

    if (limit) {
      usersWithTasks.take(limit);
    }
    let result = await usersWithTasks.getManyAndCount();
    return result;
  }

  async getUsersForReports(userId: number, query: any) {
    let user = await User.findOne(userId, { relations: ['organization', 'role'] });
    let ViewAll = user.role.permissions.find(
      (permission) => permission.slug === Permissions.VIEW_ALL_CLIENT_MANAGERS,
    );
    let ViewAssigned = user.role.permissions.find(
      (permission) => permission.slug === Permissions.VIEW_ASSIGNED_CLIENT_MANAGERS,
    );

    const usersQuery = await createQueryBuilder(User, 'user')
      .leftJoinAndSelect('user.organization', 'organization')
      .leftJoinAndSelect('user.role', 'role')
      .where('user.organization.id = :orgId', { orgId: user.organization.id })
      .andWhere('user.type = :type', { type: UserType.ORGANIZATION })
      .andWhere('user.status != :status', { status: UserStatus.DELETED });

    if (!ViewAll && ViewAssigned && query.clientManagers) {
      usersQuery.andWhere('user.id = :userId', { userId });
    }

    const users = await usersQuery.getMany();

    return users;
  }

  async getAllOrganizationUsers(userId: number) {
    let user = await User.findOne(userId, { relations: ['organization'] });

    let users = await User.find({
      where: {
        organization: { id: user.organization.id },
        type: UserType.ORGANIZATION,
      },
      relations: ['organization', 'role', 'imageStorage'],
    });

    return users;
  }

  async getAll(userId: number) {
    let user = await User.findOne(userId, { relations: ['organization'] });

    let users = await User.find({
      where: {
        organization: { id: user.organization.id },
        status: Not(UserStatus.DELETED),
        type: UserType.ORGANIZATION,
      },
      relations: ['organization', 'role', 'imageStorage'],
    });

    return users;
  }

  async getUserDeleteData(id: number) {
    let user = await User.findOne(id, { relations: ['organization'] });

    const memberCount = await createQueryBuilder(Task, 'task')
      .leftJoin('task.members', 'members')
      .where('members.id = :id', { id })
      .andWhere('task.status IN (:...statuses)', {
        statuses: [
          TaskStatusEnum.TODO,
          TaskStatusEnum.IN_PROGRESS,
          TaskStatusEnum.UNDER_REVIEW,
          TaskStatusEnum.ON_HOLD,
        ],
      })
      .andWhere(
        new Brackets((qb) => {
          qb.where('task.recurringStatus IS NULL').orWhere(
            'task.recurringStatus IN (:...recurringStatuses)',
            { recurringStatuses: [TaskRecurringStatus.CREATED] },
          );
        }),
      )
      .getCount();

    const taskLeaderCount = await createQueryBuilder(Task, 'task')
      .leftJoin('task.taskLeader', 'taskLeader')
      .where('taskLeader.id = :id', { id })
      .andWhere('task.status IN (:...statuses)', {
        statuses: [
          TaskStatusEnum.TODO,
          TaskStatusEnum.IN_PROGRESS,
          TaskStatusEnum.UNDER_REVIEW,
          TaskStatusEnum.ON_HOLD,
        ],
      })
      .andWhere(
        new Brackets((qb) => {
          qb.where('task.recurringStatus IS NULL').orWhere(
            'task.recurringStatus IN (:...recurringStatuses)',
            { recurringStatuses: [TaskRecurringStatus.CREATED] },
          );
        }),
      )
      .getCount();

    const approvalCount = await createQueryBuilder(Task, 'task')
      .leftJoin('task.approvalProcedures', 'approvalProcedures')
      .leftJoin('approvalProcedures.approval', 'approval')
      .leftJoin('approval.approvalLevels', 'approvalLevels')
      .leftJoin('approvalLevels.user', 'approvalLevelsUsers')
      .where('approvalLevelsUsers.id = :id', { id })
      .andWhere('task.status IN (:...statuses)', {
        statuses: [
          TaskStatusEnum.TODO,
          TaskStatusEnum.IN_PROGRESS,
          TaskStatusEnum.UNDER_REVIEW,
          TaskStatusEnum.ON_HOLD,
        ],
      })
      .andWhere(
        new Brackets((qb) => {
          qb.where('task.recurringStatus IS NULL').orWhere(
            'task.recurringStatus IN (:...recurringStatuses)',
            { recurringStatuses: [TaskRecurringStatus.CREATED] },
          );
        }),
      )
      .getCount();

    let approvalLevelsData = await createQueryBuilder(OrgApprovals, 'orgApprovals')
      .leftJoin('orgApprovals.organization', 'organization')
      .leftJoin('orgApprovals.approvalLevels', 'levels')
      .leftJoin('levels.user', 'user')
      .where('organization.id = :orgId', { orgId: user.organization.id })
      .andWhere('orgApprovals.status=:status', { status: APPROVALS_PROCEDURE_STATUS.CREATED })
      .andWhere('user.id = :userId', { userId: user.id })
      .getCount();

    const memberRecurringProfileCount = await createQueryBuilder(
      RecurringProfile,
      'recurringProfile',
    )
      .leftJoin('recurringProfile.tasks', 'tasks')
      .leftJoin('tasks.members', 'member')
      .where('member.id = :userId', { userId: user?.id })
      .andWhere('tasks.recurringStatus = :pendingStatus', {
        pendingStatus: TaskRecurringStatus.PENDING,
      })
      .getCount();

    const taskLeaderRecurringProfileCount = await createQueryBuilder(
      RecurringProfile,
      'recurringProfile',
    )
      .leftJoin('recurringProfile.tasks', 'tasks')
      .leftJoin('tasks.taskLeader', 'taskLeader')
      .where('taskLeader.id = :userId', { userId: user?.id })
      .andWhere('tasks.recurringStatus = :pendingStatus', {
        pendingStatus: TaskRecurringStatus.PENDING,
      })
      .getCount();

    const approvalRecurringProfileCount = await createQueryBuilder(
      RecurringProfile,
      'recurringProfile',
    )
      .leftJoin('recurringProfile.tasks', 'tasks')
      .leftJoin('tasks.approvalProcedures', 'approvalProcedures')
      .leftJoin('approvalProcedures.approval', 'approval')
      .leftJoin('approval.approvalLevels', 'approvalLevels')
      .leftJoin('approvalLevels.user', 'approvalLevelsUsers')
      .where('approvalLevelsUsers.id = :userId', { userId: user?.id })
      .andWhere('tasks.recurringStatus = :pendingStatus', {
        pendingStatus: TaskRecurringStatus.PENDING,
      })
      .getCount();

    const memberNonRecuringUpcomingCount = await createQueryBuilder(Task, 'task')
      .leftJoin('task.members', 'members')
      .where('members.id = :id', { id })
      .andWhere('task.status IN (:...statuses)', {
        statuses: [
          TaskStatusEnum.TODO,
          TaskStatusEnum.IN_PROGRESS,
          TaskStatusEnum.UNDER_REVIEW,
          TaskStatusEnum.ON_HOLD,
        ],
      })
      .andWhere('task.recurring is not true')
      .andWhere('task.recurringStatus IN (:...recurringStatuses)', { recurringStatuses: [TaskRecurringStatus.PENDING] })
      .getCount();

    const taskLeaderNonRecurringUpcomingCount = await createQueryBuilder(Task, 'task')
      .leftJoin('task.taskLeader', 'taskLeader')
      .where('taskLeader.id = :id', { id })
      .andWhere('task.status IN (:...statuses)', {
        statuses: [
          TaskStatusEnum.TODO,
          TaskStatusEnum.IN_PROGRESS,
          TaskStatusEnum.UNDER_REVIEW,
          TaskStatusEnum.ON_HOLD,
        ],
      })
      .andWhere('task.recurring is not true')
      .andWhere('task.recurringStatus IN (:...recurringStatuses)', { recurringStatuses: [TaskRecurringStatus.PENDING] })
      .getCount();

    const approvalNonRecurringUpcomingCount = await createQueryBuilder(Task, 'task')
      .leftJoin('task.approvalProcedures', 'approvalProcedures')
      .leftJoin('approvalProcedures.approval', 'approval')
      .leftJoin('approval.approvalLevels', 'approvalLevels')
      .leftJoin('approvalLevels.user', 'approvalLevelsUsers')
      .where('approvalLevelsUsers.id = :id', { id })
      .andWhere('task.status IN (:...statuses)', {
        statuses: [
          TaskStatusEnum.TODO,
          TaskStatusEnum.IN_PROGRESS,
          TaskStatusEnum.UNDER_REVIEW,
          TaskStatusEnum.ON_HOLD,
        ],
      })
      .andWhere('task.recurring is not true')
      .andWhere('task.recurringStatus IN (:...recurringStatuses)', { recurringStatuses: [TaskRecurringStatus.PENDING] })
      .getCount();

    const clientManagersCount = await createQueryBuilder(Client, 'client')
      .leftJoinAndSelect('client.clientManagers', 'clientManagers')
      .where('clientManagers.id = :userId', { userId: user.id })
      .andWhere('client.status != :status', { status: UserStatus.DELETED })
      .getCount();

    const clientsGroupManagersCount = await createQueryBuilder(ClientGroup, 'clientGroup')
      .leftJoinAndSelect('clientGroup.clientGroupManagers', 'clientGroupManagers')
      .where('clientGroupManagers.id = :userId', { userId: user.id })
      .andWhere('clientGroup.status != :status', { status: UserStatus.DELETED })
      .getCount();

    const tasks = {
      memberCount,
      taskLeaderCount,
      approvalCount,
    };

    const recurringProfile = {
      memberRecurringProfileCount,
      taskLeaderRecurringProfileCount,
      approvalRecurringProfileCount,
    };

    const nonRecurringUpcoming = {
      memberNonRecuringUpcomingCount,
      taskLeaderNonRecurringUpcomingCount,
      approvalNonRecurringUpcomingCount,
    }

    const pendingExpendituresCount = await createQueryBuilder(Expenditure, 'expenditure')
      .leftJoin('expenditure.user', 'user')
      .where('user.id=:userId', { userId: user.id })
      .andWhere('expenditure.approvalStatus IN (:...status)', {
        status: [EXPENDITURE_STATUS.PENDING, EXPENDITURE_STATUS.REJECTED],
      })
      .getCount();

    const pendingLogHoursCount = await createQueryBuilder(LogHour, 'logHour')
      .leftJoin('logHour.user', 'user')
      .where('user.id=:userId', { userId: user.id })
      .andWhere('logHour.approvalStatus IN (:...status)', {
        status: [EXPENDITURE_STATUS.PENDING, EXPENDITURE_STATUS.REJECTED],
      })
      .getCount();

    return {
      tasks,
      approvalLevelsData,
      recurringProfile,
      nonRecurringUpcoming,
      clientManagersCount,
      clientsGroupManagersCount,
      pendingExpendituresCount,
      pendingLogHoursCount,
      id,
    };
  }

  async getDeletedUsers(userId: number, query: any) {
    const { limit, offset } = query;
    let user = await User.findOne({
      where: { id: userId },
      relations: ['organization'],
    });

    let users = createQueryBuilder(User, 'user')
      .leftJoinAndSelect('user.profile', 'profile')
      .leftJoinAndSelect('user.organization', 'organization')
      .leftJoinAndSelect('user.role', 'role')
      .where('organization.id = :orgId', { orgId: user.organization.id })
      .andWhere('user.status = :status', { status: UserStatus.DELETED });

    const sort = typeof query?.sort === 'string' ? JSON.parse(query.sort) : query?.sort;
    if (sort?.column) {
      const columnMap: Record<string, string> = {
        employeeId: 'profile.employeeId',
        fullName: 'user.fullName',
      };
      const column = columnMap[sort.column] || sort.column;
      users.orderBy(column, sort.direction.toUpperCase());
    } else {
      users.orderBy('user.updatedAt', 'DESC');
    }
    if (query.search) {
      users.andWhere(
        new Brackets((qb) => {
          qb.where('user.fullName LIKE :fullName', {
            fullName: `%${query.search}%`,
          });
          qb.orWhere('user.email LIKE :email', {
            email: `%${query.search}%`,
          });
        }),
      );
    }

    if (offset >= 0) {
      users.skip(offset);
    }

    if (limit) {
      users.take(limit);
    }

    // let users = await User.findAndCount({
    //   where: {
    //     organization: { id: user.organization.id },
    //     status: UserStatus.DELETED,
    //   },
    //   relations: ['profile'],
    //   take: query.limit,
    //   skip: query.offset,
    // });
    let result = await users.getManyAndCount();

    return result;
  }

  async getProfileById(userId: number, authUserId: number) {
    let authUser = await User.findOne(authUserId, { relations: ['organization'] });
    let user = await User.findOne(userId, {
      relations: [
        'profile',
        'role',
        'profile.addharStorage',
        'profile.panStorage',
        'profile.drivingLicenseStorage',
        'imageStorage',
      ],
      where: {
        organization: authUser?.organization,
      },
    });
    if (user) {
      let existingUser = await NotificationPreferences.findOne({ where: { user: userId } });
      if (!existingUser) {
        const entityManager = getManager();
        const orgQuery = `SELECT organization_id FROM user where id = ${userId};`;
        const admin = await entityManager.query(orgQuery);
        const orgId = admin[0].organization_id;
        const groupedPushActions = {};
        for (const categoryKey in NotificationAction) {
          const category = NotificationAction[categoryKey];
          for (const actionKey in category) {
            const action = category[actionKey];
            for (const innerKey in action) {
              if (innerKey.endsWith('_PUSH')) {
                groupedPushActions[innerKey] = action[innerKey];
              }
            }
          }
        }
        let existingUser = await User.findOne({
          where: {
            id: userId,
          },
        });
        let newUser = new NotificationPreferences();
        newUser.organization_id = orgId;
        newUser.user = existingUser;
        newUser.email = JSON.stringify({});
        newUser.push = JSON.stringify(groupedPushActions);
        newUser.whatsapp = JSON.stringify({});
        await newUser.save();
      }
    }
    return user;
  }

  async getUserCheckById(userId: number, authUserId: number) {
    let authUser = await User.findOne(authUserId, { relations: ['organization'] });
    let user = await User.findOne(userId, {
      relations: [
        'profile',
        'role',
        'profile.addharStorage',
        'profile.panStorage',
        'profile.drivingLicenseStorage',
        'imageStorage',
      ],
      where: {
        organization: authUser?.organization,
      },
    });

    return user;
  }

  async getProfile(userId: number) {
    let user = await User.findOne(userId, {
      relations: [
        'profile',
        'role',
        'profile.addharStorage',
        'profile.panStorage',
        'profile.drivingLicenseStorage',
        'profile.profileSign',
        'imageStorage',
        'organization',
        'organization.organizationPreferences',
      ],
    });
    let existingUser = await NotificationPreferences.findOne({ where: { user: userId } });
    if (!existingUser) {
      const entityManager = getManager();
      const orgQuery = `SELECT organization_id FROM user where id = ${userId};`;
      const admin = await entityManager.query(orgQuery);
      const orgId = admin[0].organization_id;
      const groupedPushActions = {};
      for (const categoryKey in NotificationAction) {
        const category = NotificationAction[categoryKey];
        for (const actionKey in category) {
          const action = category[actionKey];
          for (const innerKey in action) {
            if (innerKey.endsWith('_PUSH')) {
              groupedPushActions[innerKey] = action[innerKey];
            }
          }
        }
      }
      let existingUser = await User.findOne({
        where: {
          id: userId,
        },
      });
      let newUser = new NotificationPreferences();
      newUser.organization_id = orgId;
      newUser.user = existingUser;
      newUser.email = JSON.stringify({});
      newUser.push = JSON.stringify(groupedPushActions);
      newUser.whatsapp = JSON.stringify({});
      await newUser.save();
    }
    return user;
  }

  async updateProfile(userId: number, data: UpdateProfileDto) {
    try {
      let user = await User.findOne({
        where: { id: userId },
        relations: [
          'profile',
          'organization',
          'role',
          'profile.addharStorage',
          'profile.panStorage',
          'imageStorage',
          'profile.drivingLicenseStorage',
          'profile.profileSign'
        ],
      });

      if (!user?.profile) {
        let count = await User.count({ where: { organization: { id: user.organization.id } } });
        let userProfile = new UserProfile();
        userProfile.employeeId = generateEmpoyeeId(
          user.organization.legalName.slice(0, 3),
          count + 1,
        );
        userProfile.dateOfJoining = new Date().toISOString();
        user.profile = userProfile;
      }
      // Check if the mobile number is being updated
      const mobileNumber = ['**********', '**********', '**********', '**********', '**********'];
      if (user.mobileNumber !== data.mobileNumber) {
        if (!mobileNumber.includes(data.mobileNumber)) {
          let existingMobile = await User.findOne({
            where: {
              mobileNumber: data.mobileNumber,
              countryCode: data?.countryCode,
              type: UserType.ORGANIZATION,
            },
          });

          if (existingMobile) {
            throw new BadRequestException('Mobile Number Already Exists');
          }
        }
      }
      if (user.fullName !== data.fullName) {
        let organizationUserLimit = await User.find({
          where: {
            organization: { id: user.organization.id },
            fullName: data.fullName.trim(),
            type: UserType.ORGANIZATION,
          },
        });
        if (organizationUserLimit.length > 0) {
          throw new UnprocessableEntityException('User with the given Full Name already exists.');
        }
      }
      let astorage: Storage;
      if (data?.addharStorage) {
        if (user?.profile?.addharStorage?.id) {
          if (data.addharStorage.name !== user?.profile?.addharStorage?.name) {
            if (
              user?.profile?.addharStorage.storageSystem === StorageSystem.AMAZON &&
              user?.profile?.addharStorage?.file
            ) {
              this.awsService.deleteFile(user?.profile?.addharStorage?.file);
            } else if (
              user?.profile?.addharStorage.storageSystem === StorageSystem.MICROSOFT &&
              user?.profile?.addharStorage?.fileId
            ) {
              this.oneDriveService.deleteOneDriveFile(userId, user?.profile?.addharStorage?.fileId);
            } else if (
              user?.profile?.addharStorage.storageSystem === StorageSystem.GOOGLE &&
              user?.profile?.addharStorage?.fileId
            ) {
              this.googleDriveService.deleteGoogleDriveFile(
                userId,
                user?.profile?.addharStorage?.fileId,
              );
            } else if (
              user?.profile?.addharStorage.storageSystem === StorageSystem.BHARATHCLOUD &&
              user?.profile?.addharStorage?.file
            ) {
              this.bharathService.deleteFile(
                user.organization.id,
                user?.profile?.addharStorage?.file,
              );
            }
          }
          astorage = await Storage.findOne({ where: { id: user?.profile?.addharStorage?.id } });
          astorage.fileType = data?.addharStorage.fileType;
          astorage.fileSize = data?.addharStorage.fileSize;
          astorage.name = data?.addharStorage.name;
          astorage.file = data?.addharStorage.upload ?? data?.addharStorage.file;
          astorage.show = data?.addharStorage.show;
          astorage.storageSystem = data?.addharStorage?.storageSystem;
          astorage.webUrl = data?.addharStorage?.webUrl;
          astorage.downloadUrl = data?.addharStorage?.downloadUrl;
          astorage.fileId = data?.addharStorage?.fileId;
          astorage.authId = user.organization.id;
          astorage.filePath = data?.addharStorage?.filePath;
          // user.profile.addharStorage = astorage;
        } else {
          astorage = await this.storageService.addAttachements(userId, data?.addharStorage);
          // user.profile.addharStorage = storage;
        }
      } else {
        if (user?.profile?.addharStorage?.id) {
          const existingAStorage = await Storage.findOne({
            where: { id: user?.profile?.addharStorage?.id },
          });
          const existingStorage = await existingAStorage.remove();
          if (existingStorage) {
            if (existingStorage.storageSystem === StorageSystem.AMAZON) {
              this.awsService.deleteFile(existingStorage.file);
            } else if (existingStorage.storageSystem === StorageSystem.MICROSOFT) {
              this.oneDriveService.deleteOneDriveFile(userId, existingStorage.fileId);
            } else if (existingStorage.storageSystem === StorageSystem.GOOGLE) {
              this.googleDriveService.deleteGoogleDriveFile(userId, existingStorage.fileId);
            } else if (existingAStorage.storageSystem === StorageSystem.BHARATHCLOUD) {
              this.bharathService.deleteFile(user.organization.id, existingStorage.file);
            }
          }
          user.profile.addharStorage = null;
        }
      }

      let dStorage: Storage;
      if (data?.drivingLicenseStorage) {
        if (user?.profile?.drivingLicenseStorage?.id) {
          if (data.drivingLicenseStorage.name !== user?.profile?.drivingLicenseStorage?.name) {
            if (user?.profile?.drivingLicenseStorage.storageSystem === StorageSystem.AMAZON) {
              this.awsService.deleteFile(user?.profile?.drivingLicenseStorage?.file);
            } else if (
              user?.profile?.drivingLicenseStorage.storageSystem === StorageSystem.MICROSOFT
            ) {
              this.oneDriveService.deleteOneDriveFile(
                userId,
                user?.profile?.drivingLicenseStorage?.fileId,
              );
            } else if (
              user?.profile?.drivingLicenseStorage.storageSystem === StorageSystem.GOOGLE
            ) {
              this.googleDriveService.deleteGoogleDriveFile(
                userId,
                user?.profile?.drivingLicenseStorage?.fileId,
              );
            } else if (
              user?.profile?.drivingLicenseStorage.storageSystem === StorageSystem.BHARATHCLOUD
            ) {
              this.bharathService.deleteFile(
                user.organization.id,
                user?.profile?.drivingLicenseStorage?.file,
              );
            }
          }

          dStorage = await Storage.findOne({
            where: { id: user?.profile?.drivingLicenseStorage?.id },
          });
          dStorage.fileType = data?.drivingLicenseStorage.fileType;
          dStorage.fileSize = data?.drivingLicenseStorage.fileSize;
          dStorage.name = data?.drivingLicenseStorage.name;
          dStorage.file = data?.drivingLicenseStorage.upload ?? data?.drivingLicenseStorage.file;
          dStorage.show = data?.drivingLicenseStorage.show;
          dStorage.storageSystem = data?.drivingLicenseStorage?.storageSystem;
          dStorage.webUrl = data?.drivingLicenseStorage?.weburl;
          dStorage.downloadUrl = data?.drivingLicenseStorage?.downloadUrl;
          dStorage.fileId = data?.drivingLicenseStorage?.fileId;
          dStorage.authId = user.organization.id;
          dStorage.filePath = data?.drivingLicenseStorage?.filePath;
          // user.profile.drivingLicenseStorage = dStorage;
        } else {
          dStorage = await this.storageService.addAttachements(userId, data?.drivingLicenseStorage);
          // user.profile.drivingLicenseStorage = storage;
        }
      } else {
        if (user?.profile?.drivingLicenseStorage?.id) {
          const existingDStorage = await Storage.findOne({
            where: { id: user?.profile?.drivingLicenseStorage?.id },
          });
          const existingStorage = await existingDStorage.remove();
          if (existingStorage) {
            if (existingStorage.storageSystem === StorageSystem.AMAZON) {
              this.awsService.deleteFile(existingStorage.file);
            } else if (existingStorage.storageSystem === StorageSystem.MICROSOFT) {
              this.oneDriveService.deleteOneDriveFile(userId, existingStorage.fileId);
            } else if (existingStorage.storageSystem === StorageSystem.GOOGLE) {
              this.googleDriveService.deleteGoogleDriveFile(userId, existingStorage.fileId);
            } else if (existingStorage.storageSystem === StorageSystem.BHARATHCLOUD) {
              this.bharathService.deleteFile(user.organization.id, existingDStorage.file);
            }
          }
          user.profile.drivingLicenseStorage = null;
        }
      }

      let pStorage: Storage;
      if (data?.panStorage) {
        if (user?.profile?.panStorage?.id) {
          if (data.panStorage.name !== user?.profile?.panStorage?.name) {
            const storageType = user?.profile?.panStorage.storageSystem;
            if (storageType === StorageSystem.AMAZON) {
              this.awsService.deleteFile(user?.profile?.panStorage?.file);
            } else if (storageType === StorageSystem.MICROSOFT) {
              this.oneDriveService.deleteOneDriveFile(userId, user?.profile?.panStorage?.fileId);
            } else if (storageType === StorageSystem.GOOGLE) {
              this.googleDriveService.deleteGoogleDriveFile(
                userId,
                user?.profile?.panStorage?.fileId,
              );
            } else if (storageType == StorageSystem.BHARATHCLOUD) {
              this.bharathService.deleteFile(user.organization.id, user?.profile?.panStorage?.file);
            }
          }

          pStorage = await Storage.findOne({ where: { id: user?.profile?.panStorage?.id } });
          pStorage.fileType = data?.panStorage?.fileType;
          pStorage.fileSize = data?.panStorage?.fileSize;
          pStorage.name = data?.panStorage?.name;
          pStorage.file = data?.panStorage?.upload ?? data?.panStorage?.file;
          pStorage.show = data?.panStorage?.show;
          pStorage.storageSystem = data?.panStorage?.storageSystem;
          pStorage.webUrl = data?.panStorage?.webUrl;
          pStorage.downloadUrl = data?.panStorage?.downloadUrl;
          pStorage.fileId = data?.panStorage?.fileId;
          pStorage.authId = user.organization.id;
          pStorage.filePath = data?.panStorage?.filePath;
        } else {
          pStorage = await this.storageService.addAttachements(userId, data?.panStorage);
        }
      } else {
        if (user?.profile?.panStorage?.id) {
          const existingPStorage = await Storage.findOne({
            where: { id: user?.profile?.panStorage?.id },
          });
          const existingStorage = await existingPStorage.remove();
          if (existingStorage) {
            if (existingStorage.storageSystem === StorageSystem.AMAZON) {
              this.awsService.deleteFile(existingStorage.file);
            } else if (existingStorage.storageSystem === StorageSystem.MICROSOFT) {
              this.oneDriveService.deleteOneDriveFile(userId, existingStorage.fileId);
            } else if (existingStorage.storageSystem === StorageSystem.GOOGLE) {
              this.googleDriveService.deleteGoogleDriveFile(userId, existingStorage.fileId);
            } else if (existingStorage.storageSystem === StorageSystem.BHARATHCLOUD) {
              this.bharathService.deleteFile(user.organization.id, existingStorage.file);
            }
          }
          user.profile.panStorage = null;
        }
      }


      let sStorage: Storage;
      if (data?.profileSign) {
        if (user?.profile?.profileSign?.id) {
          if (data.profileSign.name !== user?.profile?.profileSign?.name) {
            const storageType = user?.profile?.profileSign.storageSystem;
            if (storageType === StorageSystem.AMAZON) {
              this.awsService.deleteFile(user?.profile?.profileSign?.file);
            } else if (storageType === StorageSystem.MICROSOFT) {
              this.oneDriveService.deleteOneDriveFile(userId, user?.profile?.profileSign?.fileId);
            } else if (storageType === StorageSystem.GOOGLE) {
              this.googleDriveService.deleteGoogleDriveFile(userId, user?.profile?.profileSign?.fileId);
            } else if (storageType == StorageSystem.BHARATHCLOUD) {
              this.bharathService.deleteFile(user.organization.id, user?.profile?.profileSign?.file);
            }
          }

          sStorage = await Storage.findOne({ where: { id: user?.profile?.profileSign?.id } });
          sStorage.fileType = data?.profileSign?.fileType;
          sStorage.fileSize = data?.profileSign?.fileSize;
          sStorage.name = data?.profileSign?.name;
          sStorage.file = data?.profileSign?.upload ?? data?.profileSign?.file;
          sStorage.show = data?.profileSign?.show;
          sStorage.storageSystem = data?.profileSign?.storageSystem;
          sStorage.webUrl = data?.profileSign?.webUrl;
          sStorage.downloadUrl = data?.profileSign?.downloadUrl;
          sStorage.fileId = data?.profileSign?.fileId;
          sStorage.authId = user.organization.id;
          sStorage.filePath = data?.profileSign?.filePath;
        } else {
          sStorage = await this.storageService.addAttachements(userId, data?.profileSign);
        }
      } else {
        if (user?.profile?.profileSign?.id) {
          const existingPStorage = await Storage.findOne({
            where: { id: user?.profile?.profileSign?.id },
          });
          const existingStorage = await existingPStorage.remove();
          if (existingStorage) {
            if (existingStorage.storageSystem === StorageSystem.AMAZON) {
              this.awsService.deleteFile(existingStorage.file);
            } else if (existingStorage.storageSystem === StorageSystem.MICROSOFT) {
              this.oneDriveService.deleteOneDriveFile(userId, existingStorage.fileId);
            } else if (existingStorage.storageSystem === StorageSystem.GOOGLE) {
              this.googleDriveService.deleteGoogleDriveFile(userId, existingStorage.fileId);
            } else if (existingStorage.storageSystem === StorageSystem.BHARATHCLOUD) {
              this.bharathService.deleteFile(user.organization.id, existingStorage.file);
            }
          }
          user.profile.profileSign = null;
        }
      }
      if (sStorage) {
        user.profile.profileSign = sStorage;
      };

      user.fullName = data.fullName;
      user.image = data.image;
      user.mobileNumber = data.mobileNumber;
      user.countryCode = data?.countryCode;
      user.mfaEnabled = data?.mfaEnabled;
      user.profile.dob = data.dob;
      user.profile.fatherName =
        data.fatherName !== null && data.fatherName !== undefined
          ? data.fatherName.trim()
          : data.fatherName;
      user.profile.address =
        data.address !== null && data.address !== undefined ? data.address.trim() : data.address;
      user.profile.workEmail = data.workEmail;
      user.profile.specializations = data.specializations;
      user.profile.bankAccountHolderName =
        data.bankAccountHolderName !== null && data.bankAccountHolderName !== undefined
          ? data.bankAccountHolderName.trim()
          : data.bankAccountHolderName;
      user.profile.bankAccountNumber = data.bankAccountNumber;
      user.profile.bankName =
        data.bankName !== null && data.bankName !== undefined
          ? data.bankName.trim()
          : data.bankName;
      user.profile.bankIfscCode = data.bankIfscCode;
      user.profile.aadharNumber = data.aadharNumber;
      user.profile.panNumber = data.panNumber;
      user.profile.aadharCard = data.aadharCard;
      user.profile.panCard = data.panCard;
      user.profile.drivingLicenseNumber = data.drivingLicenseNumber;
      user.profile.drivingLicense = data.drivingLicense;
      user.profile.authorisedDesignation = data.authorisedDesignation;
      user.profile.authorisedName = data.authorisedName;

      if (data.role) {
        let existingRole = await Role.findOne({ where: { id: data.role } });
        user.role = existingRole;
      }
      user['userId'] = userId;
      const us = await user.save();
      if (astorage) {
        astorage.addharStorage = us.profile;
        await astorage.save();
      }
      if (dStorage) {
        dStorage.drivingLicenseStorage = us.profile;
        await dStorage.save();
      }
      if (pStorage) {
        pStorage.panStorage = us.profile;
        await pStorage.save();
      }




      return user;
    } catch (e) {
      console.log(e);
      throw new InternalServerErrorException(e);
    }
  }

  async updateUserProfile(id: number, body: UpdateProfileDto, userId: number) {
    try {
      let user = await User.findOne({
        where: { id: id },
        relations: ['profile', 'organization', 'role'],
      });

      if (body.role) {
        let existingRole = await Role.findOne({ where: { id: body.role } });
        user.role = existingRole;
      }
      user['userId'] = userId;
      await user.save();
      return user;
    } catch (e) {
      console.log(e);
      throw new InternalServerErrorException(e);
    }
  }

  async updateImage(userId: number, data: any) {
    const user = await User.findOne({ where: { id: userId }, relations: ['imageStorage'] });
    let iStorage: Storage;
    if (data?.name) {
      if (user?.imageStorage?.id) {
        if (data.name !== user?.imageStorage?.name) {
          if (user?.imageStorage.storageSystem === StorageSystem.AMAZON) {
            this.awsService.deleteFile(user?.imageStorage?.file);
          } else if (user?.imageStorage.storageSystem === StorageSystem.MICROSOFT) {
            this.oneDriveService.deleteOneDriveFile(userId, user?.imageStorage?.fileId);
          } else if (user?.imageStorage.storageSystem === StorageSystem.GOOGLE) {
            this.googleDriveService.deleteGoogleDriveFile(userId, user?.imageStorage?.fileId);
          } else if (user?.imageStorage.storageSystem === StorageSystem.BHARATHCLOUD) {
            this.bharathService.deleteFile(user.organization.id, user?.imageStorage?.file);
          }
        }
        iStorage = await Storage.findOne({ where: { id: user?.imageStorage?.id } });
        iStorage.fileType = data?.fileType;
        iStorage.fileSize = data?.fileSize;
        iStorage.name = data?.name;
        iStorage.file = data?.upload;
        iStorage.show = data?.show;
        iStorage.storageSystem = data?.storageSystem;
        iStorage.webUrl = data?.webUrl;
        iStorage.downloadUrl = data?.downloadUrl;
        iStorage.fileId = data?.fileId;
        iStorage.authId = user.organization.id;
        iStorage.filePath = data?.filePath;
        // user.imageStorage = iStorage;
      } else {
        iStorage = await this.storageService.addAttachements(userId, data);
        // user.imageStorage = storage;
      }
    } else {
      if (user?.imageStorage?.id) {
        const existingPStorage = await Storage.findOne({ where: { id: user?.imageStorage?.id } });
        const existingStorage = await existingPStorage.remove();
        if (existingStorage) {
          if (existingStorage.storageSystem === StorageSystem.AMAZON) {
            this.awsService.deleteFile(existingStorage.file);
          } else if (existingStorage.storageSystem === StorageSystem.MICROSOFT) {
            this.oneDriveService.deleteOneDriveFile(userId, existingStorage.fileId);
          } else if (existingStorage.storageSystem === StorageSystem.GOOGLE) {
            this.googleDriveService.deleteGoogleDriveFile(userId, existingStorage.fileId);
          } else if (existingStorage.storageSystem === StorageSystem.BHARATHCLOUD) {
            this.bharathService.deleteFile(user.organization.id, existingStorage.file);
          }
        }
        user.imageStorage = null;
      }
    }
    if (iStorage) {
      iStorage.userImage = user;
      await iStorage.save();
    }
  }

  async forgotPassword(data: ForgotPasswordDto) {
    let user: any = await User.findOne({
      where: {
        mobileNumber: data.mobileNumber,
        type: UserType.ORGANIZATION,
        countryCode: data?.countryCode,
      },
    });
    if (!user) {
      throw new BadRequestException("user with the given email doesn't exist");
    }
    let name = '';
    if (user) {
      name = user.fullName;
    }

    const countryCode = countries.find((c) => c.code === data?.countryCode);
    const userPhoneNumber = `${countryCode?.phone}${data?.mobileNumber}`;

    let token = this.jwtService.sign({ email: user.email, mobileNumber: data.mobileNumber });
    let url = `${process.env.WEBSITE_URL}/reset-password?token=${token}`;

    // await email.resetPassword({ email: data.mobileNumber, name: name, link: url });

    try {
      let otp = Math.floor(1000 + Math.random() * 9000);
      await axios({
        url: process.env.SMS_SERVICE_URL,
        method: 'POST',
        headers: {
          'authkey': process.env.SMS_SERVICE_AUTH_KEY,
          'Content-Type': 'application/json',
        },
        data: {
          flow_id: process.env.SMS_FLOW_ID,
          sender: process.env.SMS_SENDER_ID,
          mobiles: userPhoneNumber,
          OTP: otp,
        },
      });

      let token = this.jwtService.sign({
        mobileNumber: data.mobileNumber,
        otp: otp.toString(),
      });
      return { token };
    } catch (e) {
      console.log(e);
      throw new InternalServerErrorException(e);
    }

    // return { success: true };
  }

  async forgotPasswordSendOtp(data) {
    let existingUser = await Organization.findOne({ where: { mobileNumber: data.mobileNumber } });
    if (existingUser) {
      try {
        let otp = Math.floor(1000 + Math.random() * 9000);
        await axios({
          url: process.env.SMS_SERVICE_URL,
          method: 'POST',
          headers: {
            'authkey': process.env.SMS_SERVICE_AUTH_KEY,
            'Content-Type': 'application/json',
          },
          data: {
            flow_id: process.env.SMS_FLOW_ID,
            sender: process.env.SMS_SENDER_ID,
            mobiles: `91${data.mobileNumber}`,
            OTP: otp,
          },
        });

        let token = this.jwtService.sign({
          mobileNumber: data.mobileNumber,
          otp: otp.toString(),
        });
        return { token };
      } catch (e) {
        console.log(e);
        throw new InternalServerErrorException(e);
      }
    }
  }

  async resetPassword(data: ResetPasswordDto) {
    try {
      const decodedPassword = bcrypt.hashSync(data.password, 10);
      let blackToken = await BlackList.findOne({ where: { token: data.token } });

      if (blackToken) {
        throw new BadRequestException('Token expired');
      }

      let decoded = this.jwtService.verify(data.token);
      let user = await User.findOne({
        where: { mobileNumber: decoded.mobileNumber, type: UserType.ORGANIZATION },
      });
      user.password = decodedPassword;
      await user.save();

      let blacklist = new BlackList();
      blacklist.token = data.token;
      await blacklist.save();

      return user;
    } catch (err) {
      console.log(err);
      throw new InternalServerErrorException(err);
    }
  }

  async changePassword(id: number, data: ChangePasswordDto) {
    let user = await User.findOneOrFail(id);
    let isValidPassword = await user.verifyPassword(data.oldPassword);

    if (!isValidPassword) {
      throw new UnprocessableEntityException('The old password is wrong.');
    }

    let hashPassword = await bcrypt.hash(data.newPassword, 10);
    user.password = hashPassword;
    await user.save();
    return { success: true };
  }

  async findInvitedUsers(userId: number, query: any) {
    const sort = JSON.parse(query.sort || '{"column":"createdAt","direction":"desc"}');
    const sortBy = sort.column || 'createdAt';
    const sortDirection = sort.direction === 'asc' ? 'ASC' : 'DESC';

    const validSortFields = ['createdAt', 'fullName', 'email', 'status'];
    if (!validSortFields.includes(sortBy)) {
      throw new Error(`Invalid sort field: ${sortBy}`);
    }

    let user = await User.findOne({ where: { id: userId }, relations: ['organization'] });

    let invitedUsers = await InvitedUser.findAndCount({
      where: { organization: { id: user.organization.id } },
      skip: query.offset || 0,
      take: query.limit || 10,
      order: {
        [sortBy]: sortDirection,
      },
    });

    return {
      totalCount: invitedUsers[1],
      result: invitedUsers[0],
    };
  }

  async cancelInvitation(id: number, userId: number) {
    let invitedUser = await InvitedUser.findOne({ where: { id } });
    invitedUser.status = InvitedUserStatus.CANCELLED;
    invitedUser['userId'] = userId;
    await invitedUser.save();
    return { success: true };
  }

  async resendInvitation(id: number) {
    let invitedUser = await InvitedUser.findOne({ where: { id }, relations: ['organization', 'manager'] });

    this.eventEmitter.emit(Event_Actions.USER_RE_INVITED, {
      data: invitedUser,
      orgId: invitedUser.organization.id,
      orgName: invitedUser.organization.legalName,
      invitedUserId: invitedUser.id,
    });

    return { success: true };
  }

  async activateOrDeactivateUser(id: number, data: DeactivateUserDto, userId) {
    let user = await User.findOne({ where: { id: id } });
    user.status = data.status;
    user['userId'] = userId;
    await user.save();
    return user;
  }

  async deletedUser(id: number, userId) {
    let user = await User.findOne({ where: { id: id }, relations: ['organization'] });
    user.status = UserStatus.DELETED;
    const manager = await OrganizationHierarchy.findOne({
      where: { user },
      relations: ['manager'],
    });
    const managers = manager.manager;
    const assistantes = await OrganizationHierarchy.find({
      where: { manager: user },
      relations: ['user'],
    });
    for (let ass of assistantes) {
      ass.manager = managers;
      await ass.save();
    }
    manager.manager = null;
    await manager.save();
    user['userId'] = userId;
    await user.save();
    return { success: true };
  }

  async restoreUser(id: number, userId: number, body: any) {
    let user = await User.findOne({ where: { id: id }, relations: ['organization'] });
    const configObject = user.organization.config;
    const userLimit = configObject['userslimit'];

    const invitedUsers = await InvitedUser.find({
      where: {
        organization: { id: user.organization.id },
        status: InvitedUserStatus.PENDING,
      },
    });
    const invitedUserLength = invitedUsers.length;
    const organisationid = user.organization.id;
    const organizationUserLimit = await User.find({
      where: {
        organization: { id: organisationid },
        status: In(['ACTIVE', 'INACTIVE']),
        type: UserType.ORGANIZATION,
      },
    });

    const userLimitLength = organizationUserLimit.length;
    const totalUsers = userLimitLength + invitedUserLength;
    if (totalUsers >= userLimit) {
      throw new UnprocessableEntityException(
        'You have reached the maximum number of users permitted under your current plan. For further assistance, to add more users to your subscription, please get in touch with us via WhatsApp at +91 90444 01818',
      );
    }

    const orgHierarchy = await OrganizationHierarchy.findOne({ where: { user: user } });
    const manager = await User.findOne(body.manager);
    orgHierarchy.manager = manager;
    user.status = UserStatus.ACTIVE;
    user['userId'] = userId;
    let queryRunner = getConnection().createQueryRunner();
    await queryRunner.startTransaction();

    try {
      await queryRunner.manager.save(user);
      await queryRunner.manager.save(orgHierarchy);
      await queryRunner.commitTransaction();
      return { success: true };
    } catch (error) {
      await queryRunner.rollbackTransaction();
      console.log(error);
      throw new InternalServerErrorException(error);
    } finally {
      await queryRunner.release();
    }
  }

  async importUsers(userId: number, file: Express.Multer.File) {
    const user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
    const workbook = xlsx.read(file.buffer, { cellDates: true });
    const sheet = workbook.Sheets[workbook.SheetNames[0]];
    const dataFromExcel = xlsx.utils.sheet_to_json(sheet);

    const result: InvitedUser[] = [];
    let errorsArray = [];
    let addedArray = 0;
    let notAddedUser = 0;

    if (dataFromExcel.length > 0) {
      let filteredData = dataFromExcel.filter((item) => {
        return (
          item['S.No'] ||
          item['Full Name *'] ||
          item['Email *'] ||
          item['Mobile Number *'] ||
          item['Role *'] ||
          item['Country *'] ||
          item['Manager User ID']
        );
      });

      for (const [index, item] of filteredData.entries()) {
        const data: any = item;

        if (
          data.hasOwnProperty('Full Name *') &&
          data['Full Name *'] != '' &&
          data.hasOwnProperty('Email *') &&
          data['Email *'] != '' &&
          data.hasOwnProperty('Mobile Number *') &&
          data['Mobile Number *'] != '' &&
          data.hasOwnProperty('Role *') &&
          data['Role *'] != '' &&
          data.hasOwnProperty('Country *') &&
          data['Country *'] != ''
          // &&
          // data.hasOwnProperty('Manager User ID *') &&
          // data['Manager User ID *']
        ) {
          const roleSql = `SELECT id from role where name='${(
            ' ' + data['Role *']
          )?.trim()}' and organization_id = ${user.organization.id}`;
          const entityManager = getManager();
          const roleNumber = await entityManager.query(roleSql);

          if (roleNumber.length === 0) {
            // throw new BadRequestException(
            //   `row ${index + 1} has ${data['Role *']} role doesn't specified in organization`,
            // );
            const errorDuplicate = `row ${index + 1} has ${data['Role *']
              } role doesn't specified in organization and user not invited.`;
            errorsArray = [...errorsArray, errorDuplicate];
            notAddedUser += 1;
            continue;
          }
          const roleId = roleNumber[0].id;

          function isValidEmail(email) {
            var emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            return emailPattern.test(email);
          }
          const emailId = (' ' + data['Email *'])?.trim();
          if (!isValidEmail(emailId)) {
            const errorDuplicate = `row ${index + 1} has Invalid Email.`;
            errorsArray = [...errorsArray, errorDuplicate];
            notAddedUser += 1;
            continue;
          }

          const validateNumber = mobileWithCountry(data['Mobile Number *'], data['Country *']);
          if (validateNumber) {
            const errorDuplicate = `row ${index + 2
              } have mobile number does not match the selected country.`;
            errorsArray = [...errorsArray, errorDuplicate];
            notAddedUser += 1;
            continue;
          }

          let manager = await createQueryBuilder(User, 'user')
            .leftJoinAndSelect('user.organization', 'organization')
            .leftJoinAndSelect('user.role', 'role')
            .where('organization.id = :id', { id: user.organization.id })
            .andWhere('role.defaultRole = :defaultRole', { defaultRole: 1 })
            .getOne();

          if (data['Manager User ID']) {
            manager = await User.findOne({
              where: {
                organization: { id: user.organization.id },
                profile: { employeeId: data['Manager User ID'] },
              },
              relations: ['profile'],
            });
          }

          if (data['Manager User ID'] && !manager) {
            const errorDuplicate = `row ${index + 2} has invalid Manager`;
            errorsArray = [...errorsArray, errorDuplicate];
            notAddedUser += 1;
            continue;
          }

          const countryCode = countries.find((c) => c.label === data['Country *']);
          const invitedUser = new InviteUserDto();
          invitedUser.email = (' ' + data['Email *'])?.trim();
          invitedUser.fullName = (' ' + data['Full Name *'])?.trim();
          invitedUser.mobileNumber = (' ' + data['Mobile Number *'])?.trim();
          invitedUser.role = roleId;
          invitedUser.organization = user.organization;
          invitedUser.status = InvitedUserStatus.PENDING;
          invitedUser.countryCode = countryCode?.code;
          invitedUser.manager = manager;
          invitedUser['userId'] = userId;
          const email = (' ' + data['Email *'])?.trim();
          const fullName = (' ' + data['Full Name *'])?.trim();
          const mobileNumber = (' ' + data['Mobile Number *'])?.trim();
          const role = Number((' ' + data['Role *'])?.trim());

          let existingInvitedUser = await InvitedUser.findOne({
            where: {
              email: email,
              organization: { id: user.organization.id },
              status: In([InvitedUserStatus.PENDING, InvitedUserStatus.JOINED]),
            },
          });

          if (existingInvitedUser) {
            // throw new UnprocessableEntityException('User with the given email has already been invited');
            const errorDuplicate = `row ${index + 1
              } User with the given email has already been invited`;
            errorsArray = [...errorsArray, errorDuplicate];
            notAddedUser += 1;
            continue;
          }

          const mobileNumbers = [
            '**********',
            '**********',
            '**********',
            '**********',
            '**********',
          ];
          if (!mobileNumbers.includes(mobileNumber)) {
            let existingMobile = await InvitedUser.findOne({
              where: {
                mobileNumber: mobileNumber,
                organization: { id: user.organization.id },
                status: In([InvitedUserStatus.PENDING, InvitedUserStatus.JOINED]),
              },
            });

            if (existingMobile) {
              const errorDuplicate = `row ${index + 1
                } User with the given Phone Number has already been invited`;
              errorsArray = [...errorsArray, errorDuplicate];
              notAddedUser += 1;
              continue;
            }
          }

          let existingUser = await User.findOne({
            where: { email: email, type: UserType.ORGANIZATION },
          });

          if (existingUser) {
            // throw new UnprocessableEntityException('User with the given email already exists');
            const errorDuplicate = `row ${index + 1} User with the given email already exists`;
            errorsArray = [...errorsArray, errorDuplicate];
            notAddedUser += 1;
            continue;
          }

          if (!mobileNumbers.includes(mobileNumber)) {
            let existingMobile = await User.findOne({
              where: { mobileNumber: mobileNumber, type: UserType.ORGANIZATION },
            });

            if (existingMobile) {
              const errorDuplicate = `row ${index + 1
                } provided Phone Number is already associated with an existing account.`;
              errorsArray = [...errorsArray, errorDuplicate];
              notAddedUser += 1;
              continue;
            }
          }

          let duplicated = result.findIndex((items) => {
            return items.email === email;
          });

          if (duplicated > -1) {
            // throw new BadRequestException(
            //   `row ${duplicated + 1} and row ${index + 1} have duplicate data`,
            // );
            const errorDuplicate = `row ${duplicated + 1} and row ${index + 1} have duplicate data`;
            errorsArray = [...errorsArray, errorDuplicate];
            notAddedUser += 1;
            continue;
          }

          if (!existingUser) {
            result.push({ ...invitedUser, manager: manager.id as any } as InvitedUser);
          }
        } else {
          // throw new BadRequestException(
          //   `Improper User details, Please upload as per sample sheet`,
          // );
          const errorDuplicate = `row ${index + 1
            } has Improper User details, Mandatory fields are missing`;
          errorsArray = [...errorsArray, errorDuplicate];
          notAddedUser += 1;
          continue;
        }
      }

      if (result.length > 0) {
        try {
          let user = await User.findOne({
            where: { id: userId },
            relations: ['organization'],
          });

          const configObject = user.organization.config;
          const userLimit = configObject['userslimit'];

          const organisationid = user.organization.id;

          let organizationUserLimit = await User.find({
            where: { organization: { id: organisationid }, type: UserType.ORGANIZATION },
          });

          const userLimitLength = organizationUserLimit.length;

          if (userLimitLength + result.length > userLimit) {
            throw new UnprocessableEntityException(
              `${userLimitLength + result.length
              } users are greater than ${userLimit} users, You have reached the maximum number of users permitted under your current plan. For further assistance, to add more users to your subscription, please get in touch with us via WhatsApp at +91 90444 01818`,
            );
          }

          for (const invitesUser of result) {
            addedArray += 1;
            const res = await InvitedUser.save(invitesUser);

            this.eventEmitter.emit(Event_Actions.USER_INVITED, {
              data: invitesUser,
              orgId: user.organization.id,
              orgName: user.organization.legalName,
              invitedUserId: res.id,
            });
          }
          errorsArray = [...errorsArray, notAddedUser, addedArray];
          return errorsArray;
        } catch (err) {
          // throw new InternalServerErrorException(err);
          const errorDuplicate = String(new InternalServerErrorException(err));
          errorsArray = [...errorsArray, errorDuplicate, notAddedUser, addedArray];
          return errorsArray;
        }
      } else {
        const errorone = `All Users are already Added and Atleast one new row required as per sample sheet`;
        errorsArray = [...errorsArray, errorone, notAddedUser, addedArray];
        return errorsArray;
      }
    } else {
      throw new BadRequestException(`Atleast one row required as per sample sheet`);
    }
  }

  async createNonOrganizationUser(userId, data) {
    try {
      let user = await User.findOne({ id: userId });
      let existingUser = await User.findOne({ email: data.email });

      // if (existingUser) {
      //   throw new UnprocessableEntityException('User with the given email already exists.');
      // }

      let newUser = new User();
      newUser.fullName = data.fullName;
      newUser.email = data.email;
      newUser.mobileNumber = data.mobileNumber;
      newUser.type = UserType.NON_ORGANIZATION;
      newUser.organization = user.organization;
      newUser.countryCode = data.countryCode;
      await newUser.save();
      return newUser;
    } catch (e) {
      console.log(e);
      throw new InternalServerErrorException(e);
    }
  }

  async updateNonOrganizationUser(id, data) {
    try {
      let user = await User.findOne({ where: { id, type: userType.NON_ORGANIZATION } });
      let newUser = user;
      newUser.fullName = data.fullName;
      newUser.email = data.email;
      newUser.mobileNumber = data.mobileNumber;
      newUser.countryCode = data?.countryCode;
      await newUser.save();
      return newUser;
    } catch (e) {
      console.log(e);
      throw new InternalServerErrorException(e);
    }
  }

  async deleteNonOrganizationUser(id) {
    try {
      let user = await User.findOne({
        where: { id, type: userType.NON_ORGANIZATION },
        relations: ['organization'],
      });
      user.status = UserStatus.DELETED;
      user['userId'] = id;
      await user.save();
      return { success: true };
    } catch (e) {
      console.log(e);
      throw new InternalServerErrorException(e);
    }
  }

  async getOrgAndNonUsers(userId: number) {
    let user = await User.findOne(userId, { relations: ['organization'] });
    let users = await User.find({
      where: {
        organization: { id: user.organization.id },
        status: UserStatus.ACTIVE,
        type: In([UserType.ORGANIZATION, UserType.NON_ORGANIZATION]),
      },
      relations: ['organization'],
    });
    return users;
  };

  async getSignatureUsers(userId: any) {
    let user = await User.findOne(userId, { relations: ['organization'] });
    let users = await User.find({
      where: {
        organization: { id: user.organization.id },
        status: UserStatus.ACTIVE,
        type: In([UserType.ORGANIZATION, UserType.NON_ORGANIZATION]),
        profile: {
          profileSign: Not(IsNull())
        }
      },
      relations: ['organization', 'profile', 'profile.profileSign'],
    });
    return users;
  }

  async getNonOrgUsers(userId: number) {
    let user = await User.findOne(userId, { relations: ['organization'] });
    let users = await User.find({
      where: {
        organization: { id: user.organization.id },
        status: UserStatus.ACTIVE,
        type: UserType.NON_ORGANIZATION,
      },
      relations: ['organization'],
    });
    return users;
  }

  async getClientManagerClients(userId: number, query: any) {
    const clientsQuery = await createQueryBuilder(Client, 'client')
      .leftJoinAndSelect('client.clientManagers', 'clientManagers')
      .where('clientManagers.id = :userId', {
        userId: query?.selectedUser
          ? query?.selectedUser
          : query?.type === 'SELF'
            ? userId
            : query?.userId,
      })
      .andWhere('client.status != :status', { status: UserStatus.DELETED });
    const sort = typeof query?.sort === 'string' ? JSON.parse(query.sort) : query?.sort;
    if (sort?.column) {
      const columnMap: Record<string, string> = {
        clientId: 'client.clientId',
        clientNumber: 'client.clientNumber',
        displayName: 'client.displayName',
        tradeName: 'client.tradeName',
        category: 'client.category',
      };
      const column = columnMap[sort.column] || sort.column;
      clientsQuery.orderBy(column, sort.direction.toUpperCase());
    } else {
      clientsQuery.orderBy('client.createdAt', 'DESC');
    }
    if (query.search) {
      clientsQuery.andWhere(
        new Brackets((qb) => {
          qb.where('client.displayName LIKE :fullName', {
            fullName: `%${query.search}%`,
          });
          qb.orWhere('client.email LIKE :email', {
            email: `%${query.search}%`,
          });
        }),
      );
    }

    if (query.offset >= 0) {
      clientsQuery.skip(query.offset);
    }

    if (query.limit) {
      clientsQuery.take(query.limit);
    }

    const clients = await clientsQuery.getManyAndCount();

    return {
      totalCount: clients[1],
      result: clients[0],
    };
  }

  async exportUserClientManagersReport(userId: number, body: any) {
    const clients = await this.getClientManagerClients(userId, body);
    if (!clients.result.length) throw new BadRequestException('No Data for Export');

    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Client Managers');

    const headers = [
      { header: 'S.No', key: 'serialNo' },
      { header: 'Client ID', key: 'clientId' },
      { header: 'Category', key: 'category' },
      { header: 'Client Number', key: 'clientNumber' },
      { header: 'GSTIN / PAN', key: 'gst' },
      { header: 'Display Name', key: 'displayName' },
      { header: 'Mobile #', key: 'mobile' },
      { header: 'Email Id', key: 'email' },
      { header: 'Created Date', key: 'createdDate' },
      { header: 'Status', key: 'status' },
    ];

    worksheet.columns = headers;

    const columnMaxLengths: number[] = Array(headers.length).fill(0);
    let serialCounter = 1;
    clients.result.forEach((client) => {
      function capitalizeFirstLetter(string: string) {
        return string.charAt(0).toUpperCase() + string.slice(1);
      }

      const category =
        CLIENT_CATEGORIES.find((item) => item.value === client?.category)?.label ||
        client?.category;

      const rowData = {
        serialNo: serialCounter++,
        clientId: client?.clientId ? client?.clientId : ' ',
        gst: client?.gstNumber
          ? client?.gstNumber
          : client?.panVerified
            ? client?.panVerified
            : '-',
        clientNumber: client?.clientNumber || ' ',
        displayName: client.displayName,
        category: category,
        mobile: `+${getCountryCode(client.countryCode)} ${client?.mobileNumber}`,
        email: client?.email,
        createdDate: formatDate(client?.createdAt),
        status: capitalizeFirstLetter(client.status.toLowerCase()),
      };

      const row = worksheet.addRow(rowData);

      worksheet.columns.forEach((column, index) => {
        const headerLength = column.header?.length || 0;
        Object.keys(rowData).forEach((key) => {
          if (key === column.key && key !== 'displayName') {
            const cellLength = rowData[key].toString().length;
            columnMaxLengths[index] = Math.max(
              columnMaxLengths[index] || 0,
              cellLength,
              headerLength,
            );
          }
        });
      });

      row.getCell('status').font = { bold: true };

      row.getCell('displayName').alignment = { wrapText: true };

      if (rowData.status.toLowerCase() === 'active') {
        row.getCell('status').font = { bold: true, color: { argb: 'FF00B050' } }; // Green text for 'converted'
      } else {
        row.getCell('status').font = { bold: true, color: { argb: 'FFFF0000' } }; // Red text for other statuses
      }
    });

    worksheet.columns.forEach((column, index) => {
      if (column.key === 'displayName') {
        column.width = 50;
        column.alignment = { wrapText: true, horizontal: 'center', vertical: 'middle' };
      } else {
        column.alignment = { horizontal: 'center', vertical: 'middle' };
        column.width = columnMaxLengths[index] + 3;
      }
    });

    const headerRow = worksheet.getRow(1);
    headerRow.font = { bold: true };
    headerRow.eachCell((cell) => {
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '64B5F6' },
      };
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' },
      };
    });
    worksheet.views = [{ state: 'frozen', ySplit: 1 }];

    const buffer = await workbook.xlsx.writeBuffer();
    return buffer;
  }

  async getClientManagerClientGroups(userId: number, query: any) {
    const clientsQuery = await createQueryBuilder(ClientGroup, 'clientGroup')
      .leftJoinAndSelect('clientGroup.clientGroupManagers', 'clientGroupManagers')
      .where('clientGroupManagers.id = :userId', {
        userId: query?.selectedUser
          ? query?.selectedUser
          : query?.type === 'SELF'
            ? userId
            : query?.userId,
      });

    if (query.search) {
      clientsQuery.andWhere(
        new Brackets((qb) => {
          qb.where('clientGroup.displayName LIKE :fullName', {
            fullName: `%${query.search}%`,
          });
          qb.orWhere('clientGroup.email LIKE :email', {
            email: `%${query.search}%`,
          });
        }),
      );
    }
    const sort = typeof query?.sort === 'string' ? JSON.parse(query.sort) : query?.sort;
    if (sort?.column) {
      const columnMap: Record<string, string> = {
        clientId: 'clientGroup.clientId',
        clientNumber: 'clientGroup.clientNumber',
        displayName: 'clientGroup.displayName',
        tradeName: 'clientGroup.tradeName',
        category: 'clientGroup.category',
      };
      const column = columnMap[sort.column] || sort.column;
      clientsQuery.orderBy(column, sort.direction.toUpperCase());
    } else {
      clientsQuery.orderBy('clientGroup.createdAt', 'DESC');
    }

    if (query.offset >= 0) {
      clientsQuery.skip(query.offset);
    }

    if (query.limit) {
      clientsQuery.take(query.limit);
    }

    const clients = await clientsQuery.getManyAndCount();

    return {
      totalCount: clients[1],
      result: clients[0],
    };
  }

  async exportUserClientGroupManagersReport(userId: number, body: any) {
    const clients = await this.getClientManagerClientGroups(userId, body);
    if (!clients.result.length) throw new BadRequestException('No Data for Export');

    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Client Managers');

    const headers = [
      { header: 'S.No', key: 'serialNo' },
      { header: 'Client ID', key: 'clientId' },
      { header: 'Client Number', key: 'clientNumber' },
      { header: 'GSTIN / PAN', key: 'gst' },
      { header: 'Display Name', key: 'displayName' },
      { header: 'Mobile #', key: 'mobile' },
      { header: 'Email Id', key: 'email' },
      { header: 'Created Date', key: 'createdDate' },
      { header: 'Status', key: 'status' },
    ];

    worksheet.columns = headers;

    const columnMaxLengths: number[] = Array(headers.length).fill(0);
    let serialCounter = 1;
    clients.result.forEach((client) => {
      function capitalizeFirstLetter(string: string) {
        return string.charAt(0).toUpperCase() + string.slice(1);
      }
      const rowData = {
        serialNo: serialCounter++,
        clientId: client.clientId,
        gst: client?.gstNumber
          ? client?.gstNumber
          : client?.panVerified
            ? client?.panVerified
            : '-',
        clientNumber: client?.clientNumber || ' ',
        displayName: client.displayName,
        mobile: `+${getCountryCode(client.countryCode)} ${client?.mobileNumber}`,
        email: client?.email,
        createdDate: formatDate(client?.createdAt),
        status: capitalizeFirstLetter(client.status.toLowerCase()),
      };
      const row = worksheet.addRow(rowData);
      worksheet.columns.forEach((column, index) => {
        const headerLength = column.header?.length || 0;
        Object.keys(rowData).forEach((key) => {
          if (key === column.key && key !== 'displayName') {
            const cellLength = rowData[key].toString().length;
            columnMaxLengths[index] = Math.max(
              columnMaxLengths[index] || 0,
              cellLength,
              headerLength,
            );
          }
        });
      });

      row.getCell('status').font = { bold: true };
      row.getCell('displayName').alignment = { wrapText: true };
      if (rowData.status.toLowerCase() === 'active') {
        row.getCell('status').font = { bold: true, color: { argb: 'FF00B050' } }; // Green text for 'converted'
      } else {
        row.getCell('status').font = { bold: true, color: { argb: 'FFFF0000' } }; // Red text for other statuses
      }
    });

    worksheet.columns.forEach((column, index) => {
      if (column.key === 'displayName') {
        column.width = 50;
        column.alignment = { wrapText: true, horizontal: 'center', vertical: 'middle' };
      } else {
        column.alignment = { horizontal: 'center', vertical: 'middle' };
        column.width = columnMaxLengths[index] + 3;
      }
    });

    const headerRow = worksheet.getRow(1);
    headerRow.font = { bold: true };
    headerRow.eachCell((cell) => {
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '64B5F6' },
      };
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' },
      };
    });
    worksheet.views = [{ state: 'frozen', ySplit: 1 }];

    const buffer = await workbook.xlsx.writeBuffer();
    return buffer;
  }

  async updateProfileClientManagers(userId, data) {
    let user = await User.findOne({
      where: { id: data?.id },
      relations: ['organization', 'clientsManager'],
    });
    let clients = await createQueryBuilder(Client, 'client')
      .where('client.id IN (:...ids)', { ids: data?.apiData })
      .getMany();

    user.clientsManager = [...user.clientsManager, ...clients];

    await user.save();

    return { success: true };
  }

  async removeProfileClientManagers(userId, data) {
    let user = await User.findOne({
      where: { id: data?.id },
      relations: ['organization', 'clientsManager'],
    });
    const clientsQuery = await createQueryBuilder(Client, 'client')
      .leftJoinAndSelect('client.clientManagers', 'clientManagers')
      .where('clientManagers.id = :userId', { userId: data?.id })
      .andWhere('client.status != :status', { status: UserStatus.DELETED })
      .andWhere('client.id IN (:...Ids)', { Ids: data?.ids })
      .andWhere((qb) => {
        const subQuery = qb
          .subQuery()
          .select('COUNT(cm.client_id)')
          .from('client_client_managers_user', 'cm')
          .where('cm.client_id = client.id')
          .getQuery();
        return `${subQuery} = 1`;
      })
      .getMany();

    const cantRemoveIds = clientsQuery.map((item) => item.id);
    const newIds = data?.ids.filter((item) => !cantRemoveIds.includes(item));

    const newClientManagers = user.clientsManager.filter((item) => !newIds.includes(item.id));
    user.clientsManager = newClientManagers;
    await user.save();
    if (cantRemoveIds.length) {
      return {
        success: false,
        message: clientsQuery,
      };
    }
    return { success: true };
  }

  async removeProfileAllClientManagers(userId, data) {
    let user = await User.findOne({
      where: { id: data?.id },
      relations: ['organization', 'clientsManager'],
    });

    const clientsQuery = await createQueryBuilder(Client, 'client')
      .leftJoinAndSelect('client.clientManagers', 'clientManagers')
      .where('clientManagers.id = :userId', { userId: data?.id })
      .andWhere('client.status != :status', { status: UserStatus.DELETED })
      .andWhere((qb) => {
        const subQuery = qb
          .subQuery()
          .select('COUNT(cm.client_id)')
          .from('client_client_managers_user', 'cm')
          .where('cm.client_id = client.id')
          .getQuery();
        return `${subQuery} = 1`;
      })
      .getMany();
    const cantRemoveIds = clientsQuery.map((item) => item.id);

    const newClientManagers = user.clientsManager.filter((item) => cantRemoveIds.includes(item.id));
    user.clientsManager = newClientManagers;
    await user.save();

    if (cantRemoveIds.length) {
      return {
        success: false,
        message: clientsQuery,
      };
    }
    return { success: true };
  }

  async updateProfileClientGroupManagers(userId, data) {
    let user = await User.findOne({
      where: { id: data?.id },
      relations: ['organization', 'clientGroupsManagers'],
    });
    let clientGroups = await createQueryBuilder(ClientGroup, 'clientGroup')
      .where('clientGroup.id IN (:...ids)', { ids: data?.apiData })
      .getMany();
    user.clientGroupsManagers = [...user?.clientGroupsManagers, ...clientGroups];
    await user.save();
    return { success: true };
  }

  async removeProfileClientGroupManagers(userId, data) {
    let user = await User.findOne({
      where: { id: data?.id },
      relations: ['organization', 'clientGroupsManagers'],
    });

    const clientsQuery = await createQueryBuilder(ClientGroup, 'clientGroup')
      .leftJoinAndSelect('clientGroup.clientGroupManagers', 'clientGroupManagers')
      .where('clientGroupManagers.id = :userId', { userId: data?.id })
      .andWhere('clientGroup.status != :status', { status: UserStatus.DELETED })
      .andWhere('clientGroup.id IN (:...Ids)', { Ids: data?.ids })
      .andWhere((qb) => {
        const subQuery = qb
          .subQuery()
          .select('COUNT(cm.client_group_id)')
          .from('client_group_client_group_managers_user', 'cm')
          .where('cm.client_group_id = clientGroup.id')
          .getQuery();
        return `${subQuery} = 1`;
      })
      .getMany();

    const cantRemoveIds = clientsQuery.map((item) => item.id);
    const newIds = data?.ids.filter((item) => !cantRemoveIds.includes(item));

    const newClientManagers = user.clientGroupsManagers.filter((item) => !newIds.includes(item.id));
    user.clientGroupsManagers = newClientManagers;
    await user.save();

    if (cantRemoveIds.length) {
      return {
        success: false,
        message: clientsQuery,
      };
    }
    return { success: true };
  }

  async removeProfileAllClientGroupManagers(userId, data) {
    let user = await User.findOne({
      where: { id: data?.id },
      relations: ['organization', 'clientGroupsManagers'],
    });

    const clientsQuery = await createQueryBuilder(ClientGroup, 'clientGroup')
      .leftJoinAndSelect('clientGroup.clientGroupManagers', 'clientGroupManagers')
      .where('clientGroupManagers.id = :userId', { userId: data?.id })
      .andWhere('clientGroup.status != :status', { status: UserStatus.DELETED })
      .andWhere((qb) => {
        const subQuery = qb
          .subQuery()
          .select('COUNT(cm.client_group_id)')
          .from('client_group_client_group_managers_user', 'cm')
          .where('cm.client_group_id = clientGroup.id')
          .getQuery();
        return `${subQuery} = 1`;
      })
      .getMany();
    const cantRemoveIds = clientsQuery.map((item) => item.id);

    const newClientManagers = user.clientGroupsManagers.filter((item) =>
      cantRemoveIds.includes(item.id),
    );
    user.clientGroupsManagers = newClientManagers;
    await user.save();

    if (cantRemoveIds.length) {
      return {
        success: false,
        message: clientsQuery,
      };
    }
    return { success: true };
  }

  async superAdminForgotPassword(data: any) {
    let user: any = await User.findOne({
      where: {
        type: UserType.ORGANIZATION,
        email: data?.email,
        isSuperUser: true
      },
    });
    if (!user) {
      throw new BadRequestException("user with the given email doesn't exist");
    }

    let token = this.jwtService.sign({ email: user.email, });
    let url = `${process.env.ADMIN_WEBSITE_URL}/reset-password?token=${token}`;

    try {
      await email.resetPassword({ email: data?.email, name: user?.fullName, link: url });
      return { token }
    } catch (e) {
      console.log(e);
      throw new InternalServerErrorException(e);
    }

  }

  async superAdminResetPassword(data: ResetPasswordDto) {
    try {
      const decodedPassword = bcrypt.hashSync(data.password, 10);
      let blackToken = await BlackList.findOne({ where: { token: data.token } });

      if (blackToken) {
        throw new BadRequestException('Token expired');
      }

      let decoded = this.jwtService.verify(data.token);
      let user = await User.findOne({
        where: { email: decoded.email, type: UserType.ORGANIZATION, isSuperUser: true },
      });
      user.password = decodedPassword;
      user['isSuperAdmin'] = true;
      await user.save();

      let blacklist = new BlackList();
      blacklist.token = data.token;
      await blacklist.save();

      return user;
    } catch (err) {
      console.log(err);
      throw new InternalServerErrorException(err);
    }
  }

}
