import { MigrationInterface, QueryRunner } from 'typeorm';

export class AlterTaskTable1658398300258 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
        ALTER TABLE task
        ADD COLUMN invoice_id int null,
        ADD COLUMN estimate_id int null,
        ADD FOREIGN KEY (invoice_id) REFERENCES invoice(id) ON DELETE SET NULL,
        ADD FOREIGN KEY (estimate_id) REFERENCES estimate(id) ON DELETE SET NULL
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}
