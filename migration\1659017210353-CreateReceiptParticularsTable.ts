import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateReceiptParticularsTable1659017210353
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
        CREATE TABLE receipt_particular (
            id int NOT NULL AUTO_INCREMENT,
            PRIMARY KEY (id),
            pure_agent_amount DECIMAL(19,2) NULL,
            service_amount DECIMAL(19,2) NULL,
            gst_amount DECIMAL(19,2) NULL,
            amount DECIMAL(19,2) NULL,
            created_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
            receipt_id int NULL,
            invoice_id int NULL,
            task_id int NULL,
            FOREIGN KEY (receipt_id) REFERENCES receipt(id) ON DELETE SET NULL,
            FOREIGN KEY (invoice_id) REFERENCES invoice(id) ON DELETE SET NULL,
            FOR<PERSON><PERSON><PERSON> KEY (task_id) REFERENCES task(id) ON DELETE SET NULL
        )            
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}
