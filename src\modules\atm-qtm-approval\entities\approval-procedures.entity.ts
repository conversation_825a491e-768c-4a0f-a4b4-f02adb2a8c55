import { Organization } from 'src/modules/organization/entities/organization.entity';
import Task from 'src/modules/tasks/entity/task.entity';
import { User } from 'src/modules/users/entities/user.entity';
import {
  BaseEntity,
  Column,
  CreateDateColumn,
  Entity,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
  UpdateDateColumn
} from 'typeorm';
import OrgApprovals from './org-approvals.entity';


export enum APPROVALS_PROCEDURE_STATUS {
  CREATED = 'created',
  DELETED = 'deleted'
}

@Entity()
class ApprovalProcedures extends BaseEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  name: string;

  @Column()
  description: string;

  @Column()
  module: string;

  // @Column()
  // category: number;

  @Column()
  oldApprovalId: number;

  @Column({ type: 'enum', enum: APPROVALS_PROCEDURE_STATUS, default: APPROVALS_PROCEDURE_STATUS.CREATED })
  status: APPROVALS_PROCEDURE_STATUS;

  @ManyToOne(() => Organization, (organization) => organization.orgApprovals)
  organization: Organization;

  @ManyToOne(() => User, (user) => user.approvalProcedures)
  user: User;

  @ManyToOne(() => OrgApprovals, (orgApprovals) => orgApprovals.approvalProcedures, {
    cascade: true
  })
  approval: OrgApprovals;

  @OneToMany(() => Task, (task) => task.approvalProcedures)
  task: Task[];

  @CreateDateColumn()
  createdAt: string;

  @UpdateDateColumn()
  updatedAt: string


}

export default ApprovalProcedures;
