import { MigrationInterface, QueryRunner } from 'typeorm';

export class AlterRecurringProfileTable1660836722795 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
        ALTER TABLE recurring_profile
        DROP COLUMN next_recurring_date,
        DROP COLUMN task_data,
        DROP COLUMN start_date,
        DROP COLUMN end_date,
        DROP COLUMN status,
        ADD COLUMN financial_year varchar(255) null,
        ADD COLUMN start_period varchar(255) null
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}
