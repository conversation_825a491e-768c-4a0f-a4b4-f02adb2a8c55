// webhook.service.ts
import { Injectable } from '@nestjs/common';
import { User } from '../users/entities/user.entity';
import { Organization } from '../organization/entities/organization.entity';
import OrganizationPreferences from './entity/organization-preferences.entity';
import * as moment from 'moment';
import Client from '../clients/entity/client.entity';
import ClientGroup from '../client-group/client-group.entity';

@Injectable()
export class OrganizationPreferencesService {
  async get(userId: number) {
    let user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
    let organization = await Organization.findOne({ where: { id: user.organization.id } });
    const organizationPreferences = await OrganizationPreferences.findOne({
      where: { organization },
      order: { id: 'DESC' },
    });

    let organizationpreferences = new OrganizationPreferences();

    const holidayPrefernences = {
      addholiday: [],
      updateweekend: [{ label: 'Sunday', value: 'sunday' }],
      updateovertime: {
        hours: { label: '08', value: '08' },
        minutes: { label: '00', value: '00' },
      },
    };

    if (!organizationPreferences) {
      organizationpreferences.organization = organization;
      organizationpreferences.invoicePreferences = JSON.stringify({});
      organizationpreferences.holidayPreferences = holidayPrefernences;
      organizationpreferences.save();
    } else if (!organizationPreferences?.holidayPreferences) {
      organizationPreferences.holidayPreferences = holidayPrefernences;
      organizationPreferences.save();
    } else {
      if (!organizationPreferences?.holidayPreferences['addholiday']) {
        organizationPreferences.holidayPreferences['addholiday'] = [];
      }
      if (!organizationPreferences?.holidayPreferences['updateweekend']) {
        organizationPreferences.holidayPreferences['updateweekend'] = [
          { label: 'Sunday', value: 'sunday' },
        ];
      }
      if (!organizationPreferences?.holidayPreferences['updateovertime']) {
        organizationPreferences.holidayPreferences['updateovertime'] = {
          hours: { label: '08', value: '08' },
          minutes: { label: '00', value: '00' },
        };
      }
      organizationPreferences.save();
    }
    if (!organizationPreferences) {
      return organizationpreferences;
    } else {
      return organizationPreferences;
    }
  }


  //This is for Invoice Preferences
  async post(userId, data, orgId) {
    const organizationPreferences = await OrganizationPreferences.findOne({
      where: { organization: orgId },
      order: { id: 'DESC' },
    });
    if (organizationPreferences) {
      organizationPreferences.invoicePreferences = JSON.stringify(data);
      organizationPreferences.lastUpdated = new Date();
      await organizationPreferences.save();
      return organizationPreferences;
    } else {
      let user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
      let organization = await Organization.findOne({ where: { id: user.organization.id } });
      let organizationpreferences = new OrganizationPreferences();
      organizationpreferences.invoicePreferences = JSON.stringify(data);
      organizationpreferences.organization = user.organization;
      organizationpreferences.lastUpdated = new Date();
      organizationpreferences.save();
      return organizationpreferences;
    }
  }

  //This is for Holiday Preferences
  async posts(userId, data, orgId) {
    const organizationPreferences = await OrganizationPreferences.findOne({
      where: { organization: orgId },
      order: { id: 'DESC' },
    });
    if (organizationPreferences) {
      organizationPreferences.holidayPreferences = {};
      await organizationPreferences.save();
      return organizationPreferences;
    } else {
      let user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
      let organization = await Organization.findOne({ where: { id: user.organization.id } });
      let organizationpreferences = new OrganizationPreferences();
      organizationpreferences.holidayPreferences = {};
      organizationpreferences.organization = user.organization;
      organizationpreferences.save();
      return organizationpreferences;
    }
  }

  //This is for Approval Preferences


  async updateWeekendData(userId, data, orgId) {
    const organizationPreferences = await OrganizationPreferences.findOne({
      where: { organization: orgId },
      order: { id: 'DESC' },
    });
    const typeData = data['typeofdata'];
    const typeDataContained = data['data'];

    if (organizationPreferences) {
      if (typeData === 'updateweekend' || typeData === 'updateovertime') {
        organizationPreferences.holidayPreferences[`${typeData}`] = typeDataContained;
        await organizationPreferences.save();
        return organizationPreferences;
      }
      if (typeData === 'addholiday') {
        const newHoliday = data?.data;
        const holidaysList = organizationPreferences.holidayPreferences[`${typeData}`] || [];
        const UpdatedHolidaysList = [...holidaysList, newHoliday];
        organizationPreferences.holidayPreferences[`${typeData}`] = UpdatedHolidaysList;
        await organizationPreferences.save();
        return organizationPreferences;
      }
      if (typeData === 'isMarkAbsent') {
        organizationPreferences.holidayPreferences[`${typeData}`] =
          typeDataContained?.isMarkAbsent || 'NO';

        await organizationPreferences.save();
      }
    } else {
      let user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
      let organizationpreferences = new OrganizationPreferences();
      if (typeData === 'updateweekend' || typeData === 'updateovertime') {
        organizationPreferences.holidayPreferences[`${typeData}`] = typeDataContained;
        organizationpreferences.organization = user.organization;
        organizationpreferences.save();
        return organizationpreferences;
      }
      if (typeData === 'addholiday') {
        // organizationPreferences.holidayPreferences[`${typeData}`] = typeDataContained;
        organizationpreferences.organization = user.organization;
        // organizationpreferences.save();
        return organizationpreferences;
      }
      if (typeData === 'isMarkAbsent') {
        organizationpreferences.organization = user.organization;
        organizationPreferences.holidayPreferences[`${typeData}`] =
          typeDataContained?.isMarkAbsent || 'NO';
        await organizationPreferences.save();
      }
    }
  }

  async updateTaskPreferences(userId, body) {
    const user = await User.findOne({
      where: {
        id: userId,
      },
      relations: ['organization'],
    });

    const organizationPreferences = await OrganizationPreferences.findOne({
      where: { organization: user.organization.id },
      order: { id: 'DESC' },
    });

    organizationPreferences.taskPreferences = body.taskPreferences;
    await organizationPreferences.save();
  }


  async updateApprovalPreferences(userId: number, body) {
    const user = await User.findOne({
      where: {
        id: userId,
      },
      relations: ['organization']
    });

    const organizationPreferences = await OrganizationPreferences.findOne({
      where: { organization: user.organization.id },
      order: { id: 'DESC' },
    });

    organizationPreferences.approvals = body.approvals;
    await organizationPreferences.save();
  }

  async deleteHoliday(userId, body, orgId) {
    const organizationPreferences = await OrganizationPreferences.findOne({
      where: { organization: orgId },
      order: { id: 'DESC' },
    });
    const holidaysList = organizationPreferences.holidayPreferences['addholiday'];
    const filteredList = holidaysList.filter(
      (item) => moment(item?.date).format('DD-MM-YYYY') !== moment(body?.date).format('DD-MM-YYYY'),
    );
    organizationPreferences.holidayPreferences['addholiday'] = filteredList;
    organizationPreferences.save();
    return organizationPreferences;
  }

  async getClientPreferences(userId: number) {
    try {
      const user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
      const orgId = user?.organization?.id;

      if (orgId) {
        const organizationPreferences = await OrganizationPreferences.findOne({
          where: { organization: orgId },
        });

        if (organizationPreferences?.clientPreferences) {
          return organizationPreferences.clientPreferences;
        } else {
          return {};
        }
      } else {
        throw new Error('Organization ID not found for the given user.');
      }
    } catch (error) {
      console.error('Error fetching client preferences:', error.message);
      throw new Error('Failed to fetch client preferences. Please try again later.');
    }
  }

  async updateClientPreferences(userId: number, data: any) {
    try {
      const user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
      const orgId = user?.organization?.id;

      if (orgId) {
        const organizationPreferences = await OrganizationPreferences.findOne({
          where: { organization: orgId },
        });

        if (organizationPreferences) {
          organizationPreferences.clientPreferences = data.notificationPreferences;
          await organizationPreferences.save();
        } else {
          const organizationPreference = new OrganizationPreferences();
          organizationPreference.organization = user?.organization;
          organizationPreference.clientPreferences = data.notificationPreferences;
          await organizationPreference.save();
        }
      } else {
        throw new Error('Organization ID not found for the given user.');
      }
    } catch (error) {
      console.error('Error updating client preferences:', error.message);
      throw new Error('Failed to update client preferences. Please try again later.');
    }
  }

  async addClientPrefix(userId: number, body: any) {
    try {
      const user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
      const orgPreference = await OrganizationPreferences.findOne({
        where: { organization: user.organization.id },
      });
      if (orgPreference) {
        orgPreference.atomClientPrefix = body;
        orgPreference.lastUpdated = new Date();
        orgPreference.save();
      } else {
        let organizationpreferences = new OrganizationPreferences();
        organizationpreferences.atomClientPrefix = body;
        organizationpreferences.organization = user.organization;
        organizationpreferences.lastUpdated = new Date();
        organizationpreferences.save();
        return organizationpreferences;
      }
    } catch (error) {
      console.log('error occur while addClientPrefix', error);
    }
  }

  async getClientPrefix(userId: number) {
    try {
      const user = await User.findOne({ where: { id: userId }, relations: ['organization'] });

      const orgPreference = await OrganizationPreferences.findOne({
        where: { organization: user.organization.id },
      });
      if (orgPreference?.atomClientPrefix) {
        return orgPreference?.atomClientPrefix;
      } else {
        return orgPreference?.atomClientPrefix;
      }
    } catch (error) {
      console.log('error occur while getClientPrefix', error);
    }
  }

  async getClientPrefixId(userId: number) {
    try {
      let user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
      const organizationPreferences: any = await OrganizationPreferences.findOne({
        where: { organization: user.organization.id },
        order: { id: 'DESC' },
      });
      const activity = await Client.find({ where: { organization: user.organization.id } });
      if (organizationPreferences?.atomClientPrefix?.autoGenerate) {
        if (activity) {
          // Use map to extract documentNumber values and filter those starting with the docprefix
          const resultArray = activity
            .map((obj) => obj?.clientNumber)
            .filter(
              (clientNumber) =>
                clientNumber !== null &&
                clientNumber.startsWith(organizationPreferences?.atomClientPrefix?.prefix),
            );
          if (resultArray?.length > 0) {
            const prefix = organizationPreferences?.atomClientPrefix?.prefix || '';
            const regex = new RegExp(`^${prefix}`);

            const numbersList = resultArray
              .map((str) => {
                const numericPart = str.replace(regex, ''); // Remove prefix and leading zeros
                return /^\d+$/.test(numericPart) ? Number(numericPart) : null; // Convert to number if it's a valid numeric part
              })
              .filter((number) => number !== null);

            // const numbersList = resultArray
            //   .map((str) => {
            //     const numericPart = str.replace(
            //       new RegExp(`^${organizationPreferences?.atomClientPrefix?.prefix}*`),
            //       '',
            //     ); // Remove prefix and leading zeros
            //     return /^\d+$/.test(numericPart) ? Number(numericPart) : null; // Convert to number if it's a valid numeric part
            //   })
            //   .filter((number) => number !== null);
            let incrementedNumber = organizationPreferences?.atomClientPrefix?.number;
            while (numbersList.includes(Number(incrementedNumber))) {
              incrementedNumber = String(Number(incrementedNumber) + 1).padStart(
                organizationPreferences?.atomClientPrefix?.number?.length,
                '0',
              );
            }
            const prefixData = {
              prefix: `${organizationPreferences?.atomClientPrefix?.prefix}${incrementedNumber}`,
            };
            return prefixData;
          } else {
            const prefixData = {
              prefix: `${organizationPreferences?.atomClientPrefix?.prefix}${organizationPreferences?.atomClientPrefix?.number}`,
            };
            return prefixData;
          }
        } else {
          const prefixData = {
            prefix: `${organizationPreferences?.atomClientPrefix?.prefix}${organizationPreferences?.atomClientPrefix?.number}`,
          };
          return prefixData;
        }
      } else {
        const prefixData = {
          prefix: '',
        };
        return prefixData;
      }
    } catch (error) {
      console.log('error occur while getClientPrefixId', error);
    }
  }

  async getClientGroupPrefixId(userId: number) {
    try {
      let user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
      const organizationPreferences: any = await OrganizationPreferences.findOne({
        where: { organization: user.organization.id },
        order: { id: 'DESC' },
      });
      const activity = await ClientGroup.find({ where: { organization: user.organization.id } });
      if (organizationPreferences?.atomClientPrefix?.groupAutoGenerate) {
        if (activity) {
          // Use map to extract documentNumber values and filter those starting with the docprefix
          const resultArray = activity
            .map((obj) => obj?.clientNumber)
            .filter(
              (clientNumber) =>
                clientNumber !== null &&
                clientNumber.startsWith(organizationPreferences?.atomClientPrefix?.groupPrefix),
            );
          if (resultArray?.length > 0) {
            const prefix = organizationPreferences?.atomClientPrefix?.groupPrefix || '';
            const regex = new RegExp(`^${prefix}`);

            const numbersList = resultArray
              .map((str) => {
                const numericPart = str.replace(regex, ''); // Remove prefix and leading zeros
                return /^\d+$/.test(numericPart) ? Number(numericPart) : null; // Convert to number if it's a valid numeric part
              })
              .filter((number) => number !== null);

            // const numbersList = resultArray
            //   .map((str) => {
            //     const numericPart = str.replace(
            //       new RegExp(`^${organizationPreferences?.atomClientPrefix?.prefix}*`),
            //       '',
            //     ); // Remove prefix and leading zeros
            //     return /^\d+$/.test(numericPart) ? Number(numericPart) : null; // Convert to number if it's a valid numeric part
            //   })
            //   .filter((number) => number !== null);
            let incrementedNumber = organizationPreferences?.atomClientPrefix?.groupNumber;
            while (numbersList.includes(Number(incrementedNumber))) {
              incrementedNumber = String(Number(incrementedNumber) + 1).padStart(
                organizationPreferences?.atomClientPrefix?.groupNumber?.length,
                '0',
              );
            }
            const prefixData = {
              prefix: `${organizationPreferences?.atomClientPrefix?.groupPrefix}${incrementedNumber}`,
            };
            return prefixData;
          } else {
            const prefixData = {
              prefix: `${organizationPreferences?.atomClientPrefix?.groupPrefix}${organizationPreferences?.atomClientPrefix?.groupNumber}`,
            };
            return prefixData;
          }
        } else {
          const prefixData = {
            prefix: `${organizationPreferences?.atomClientPrefix?.groupPrefix}${organizationPreferences?.atomClientPrefix?.groupNumber}`,
          };
          return prefixData;
        }
      } else {
        const prefixData = {
          prefix: '',
        };
        return prefixData;
      }
    } catch (error) {
      console.log('error occur while getClientPrefixId', error);
    }
  };

  async getDefaultPageLimit(userId: number) {
    let user = await User.findOne({ where: { id: userId }, relations: ['organization'] });

    const organizationPreferences = await OrganizationPreferences.findOne({
      where: { organization: user.organization.id },
      order: { id: 'DESC' },
    });
    return { limit: organizationPreferences.pageLimit }
  };

  async updatePageLimit(userId: number, body: any) {
    let user = await User.findOne({ where: { id: userId }, relations: ['organization'] });

    const organizationPreferences = await OrganizationPreferences.findOne({
      where: { organization: user.organization.id },
      order: { id: 'DESC' },
    });

    organizationPreferences.pageLimit = body.value;
    await organizationPreferences.save();
    return { success: true }
  }

   async updatePreferences(userId, body) {
    const preferences = body?.notificationPreferences;
    let user = await User.findOne({
      where: { id: userId },
      relations: ['organization'],
    });

    const organizationPreferences = await OrganizationPreferences.findOne({
        where: { organization: user?.organization },
      });
    if (!organizationPreferences) {
      const organizationPreferences = new OrganizationPreferences();
            organizationPreferences.organization = user?.organization;
      organizationPreferences.email = JSON.stringify(preferences?.email);
      organizationPreferences.push = JSON.stringify(preferences?.push);
      organizationPreferences.whatsapp = JSON.stringify(preferences?.whatsapp);
      organizationPreferences.save();
    } else {      
      organizationPreferences.email = JSON.stringify(preferences?.email);
      organizationPreferences.push = JSON.stringify(preferences?.push);
      organizationPreferences.whatsapp = JSON.stringify(preferences?.whatsapp);
      organizationPreferences.save();
    }
  }
}
