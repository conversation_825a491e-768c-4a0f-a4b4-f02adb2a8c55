import Client from 'src/modules/clients/entity/client.entity';
import {
  BaseEntity,
  Column,
  CreateDateColumn,
  Entity,
  ManyToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import AutClientCredentials from './aut_client_credentials.entity';

@Entity()
class AutEChallan extends BaseEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  pan: string;

  @Column()
  name: string;

  @Column()
  assessmentYear: string;

  @Column()
  financialYear: string;

  @Column()
  majorDesc: string;

  @Column()
  minorDesc: string;

  @Column()
  majorHead: string;

  @Column()
  minorHead: string;

  @Column()
  amtInWords: string;

  @Column()
  cin: string;

  @Column()
  acin: string;

  @Column()
  paymentModeDesc: string;

  @Column()
  bankName: string;

  @Column()
  brn: string;

  @Column()
  paymentTime: string;

  @Column()
  bsrCode: string;

  @Column()
  tenderDt: string;

  @Column()
  challanNum: string;

  @Column()
  subPymntMode: string;

  @Column()
  basicTax: number;

  @Column()
  surCharge: number;

  @Column()
  eduCess: number;

  @Column()
  interest: number;

  @Column()
  penalty: number;

  @Column()
  others: number;

  @Column()
  totalAmt: number;

  @Column()
  organizationId: number;

  @ManyToOne(() => Client, (client) => client.autChallan, { onDelete: 'SET NULL' })
  client: Client;

  @ManyToOne(
    () => AutClientCredentials,
    (autClientCredentials) => autClientCredentials.autChallan,
    { onDelete: 'SET NULL' },
  )
  autClientCredentials: AutClientCredentials;

  @CreateDateColumn()
  createdAt: string;

  @UpdateDateColumn()
  updatedAt: string;
}

export default AutEChallan;
