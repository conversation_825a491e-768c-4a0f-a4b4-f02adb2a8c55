import {
  Connection,
  EntitySubscriberInterface,
  EventSubscriber,
  InsertEvent,
  getManager,
} from 'typeorm';
import { getAdminIDsBasedOnOrganizationId, getUserDetails, insertINTONotificationUpdate } from 'src/utils/re-use';
import Task from 'src/modules/tasks/entity/task.entity';
import { sendnewMail } from 'src/emails/newemails';
import { sendWhatsAppTextMessage } from 'src/modules/whatsapp/whatsapp.service';
import ViderWhatsappSessions from 'src/modules/whatsapp/entity/viderWhatsappSessions';
import * as moment from 'moment';
import { Organization } from 'src/modules/organization/entities/organization.entity';

@EventSubscriber()
export class SubTaskSubscribers implements EntitySubscriberInterface<Task> {
  constructor(private readonly connection: Connection) {
    connection.subscribers.push(this);
  }

  listenTo() {
    return Task;
  }

  async beforeInsert(event: InsertEvent<Task>) {

  }

  async afterInsert(event: InsertEvent<Task>) {
    const entityManager = getManager();
    const { parentTask, user, name, members, taskNumber: newTaskNumber,dueDate,id:subtaskId} = event.entity;
    if (parentTask !== undefined) {
      const taskName = parentTask.name;
      const taskId = parentTask.id;
      const taskNumber = parentTask.taskNumber;
      const taskDueDate = parentTask.dueDate;
      const formattedSubtaskDueDate = moment(dueDate).format('DD-MM-YYYY');
      const userName = user?.fullName;
      const userId: any = [user?.id];
      const user_list = members.map((item) => item.fullName);
      const taskMemberList = members.map((item) => item.id);
      const getTaskQuery = `SELECT client_id, organization_id FROM task where id = ${taskId};`;
      let getTask = await entityManager.query(getTaskQuery);
      const clientId = getTask[0]?.client_id;
      const orgId = getTask[0]?.organization_id;
      if(clientId){
        const getClientQuery = `SELECT display_name FROM client where id = ${clientId};`;
        let getClient = await entityManager.query(getClientQuery);
        const clientName = getClient[0]?.display_name;
        const body = `A New Subtask "<strong>${name}</strong>" has been created for the "<strong>${taskName}</strong>" of <strong>${clientName}</strong> by <strong>${userName}</strong> and assigned to "<strong>${user_list}</strong>"`;
        const title = 'SubTask Created';
        const uniqueUserIds = Array.from(new Set([...userId, ...taskMemberList]));
        const key = 'SUBTASK_CREATED_PUSH';
        insertINTONotificationUpdate(title, body, uniqueUserIds, orgId, key,subtaskId);
        const organization = await Organization.findOne({ id: orgId });


    const addressParts = [
      organization.buildingNo || '',
      organization.floorNumber || '',
      organization.buildingName || '',
      organization.street || '',
      organization.location || '',
      organization.city || '',
      organization.district || '',
      organization.state || ''
    ].filter(part => part && part.trim() !== '');
    const pincode = organization.pincode && organization.pincode.trim() !== '' ? ` - ${organization.pincode}` : '';

    const address = addressParts.join(', ') + pincode;
        if (event?.entity?.id) {
          if (userId) {
            for (let user of uniqueUserIds) {
              const taskUserDetails = await getUserDetails(user);
              await sendnewMail({
                id: taskUserDetails?.id,
                key: 'SUBTASKS_CREATED_MAIL',
                email: taskUserDetails?.email,
                data: {
                  taskUserName: taskUserDetails?.full_name,
                  taskName,
                  clientName,
                  taskId: taskNumber,
                  newSubtasks: name,
                  newSubTaskId: newTaskNumber,
                  userId: event?.entity['userId'],
                  adress: address,
                  phoneNumber: organization?.mobileNumber,
                  mail: organization?.email,
                  legalName: organization?.tradeName || organization?.legalName
                },
                filePath: 'subtask-created',
                subject: `New Subtasks Created: ${name} of ${clientName}`,
              });
                //  whatsapp
       const title = 'Sub Task Created';
       try {
        if (uniqueUserIds !== undefined) {
            const sessionValidation = await ViderWhatsappSessions.findOne({
              where: { userId: taskUserDetails?.id,status:'ACTIVE' },
            });
            if (sessionValidation) {
              const userDetails = await getUserDetails(taskUserDetails?.id);

              const { full_name: userFullName, mobile_number: userPhoneNumber,id ,organization_id} = userDetails;
              const key = 'SUBTASKS_CREATED_WHATSAPP';
              const whatsappMessageBody = `
Hi ${userFullName}

 A new subtask has been created in ATOM by ${userDetails?.full_name}

 Subtask name: ${name}
  Subtask due date: ${formattedSubtaskDueDate}
  Task name: ${taskName}
  Due date: ${taskDueDate}
  Client Name: ${clientName}

We hope this helps!
    `;
              await sendWhatsAppTextMessage(
                `91${userPhoneNumber}`,
                whatsappMessageBody,
                organization_id,
                title,
                id,
                key,
              );
            }
          
        }
      } catch (error) {
        console.error('Error sending User WhatsApp notification:', error);
      }
            }
          }
        }
      }
    }
  }
}
