import {
  Connection,
  EntitySubscriberInterface,
  EventSubscriber,
  InsertEvent,
  getManager,
} from 'typeorm';

import Event from 'src/modules/events/event.entity';
import {
  getAdminIDsBasedOnOrganizationId,
  getUserDetails,
  getUserIDs,
  insertINTONotificationUpdate,
} from 'src/utils/re-use';
import { User } from 'src/modules/users/entities/user.entity';
import ViderWhatsappSessions from 'src/modules/whatsapp/entity/viderWhatsappSessions';
import { sendClientWhatsAppTemplateMessage, sendWhatsAppTextMessage } from 'src/modules/whatsapp/whatsapp.service';
import * as moment from 'moment';
import { sendnewMail } from 'src/emails/newemails';
import { fullMobileNumberWithCountry } from 'src/utils/validations/fullMobileWithCountry';
import { Organization } from 'src/modules/organization/entities/organization.entity';

@EventSubscriber()
export class GeneralAndTaskEventSubscriber implements EntitySubscriberInterface<Event> {
  constructor(private readonly connection: Connection) {
    connection.subscribers.push(this);
  }

  listenTo() {
    return Event;
  }

  entityManager = getManager();
  async afterInsert(event: InsertEvent<Event>) {
    //Whatsapp Notification
    try {
      const {
        title,
        type,
        location,
        date,
        task,
        client,
        clientGroup,
        members,
        startTime,
        user,
        endTime,
        whatsappEnabled,
        notes
      } = event?.entity;
      const {title:eventTitle} = event?.entity
            const plainNotes = notes?.replace(/<[^>]*>/g, '') || '';

      const inputDate = new Date(date);
      const day = String(inputDate.getDate()).padStart(2, '0');
      const month = String(inputDate.getMonth() + 1).padStart(2, '0'); // Month is zero-based
      const year = inputDate.getFullYear();

      const localDate = new Date(date).toLocaleString('en-US', {
        hour: 'numeric',
        minute: 'numeric',
        hour12: true,
      });
      const [formattedTime] = localDate.split(', ');

      const formattedDateTime = `${day}-${month}-${year} | ${formattedTime}`;

      const orgId = event?.entity?.organization?.id;
      const adminIds = await getAdminIDsBasedOnOrganizationId(orgId);

      const clientName = client?.displayName ? client?.displayName : clientGroup?.displayName;
      const clientMobileNumber = client?.mobileNumber? client?.mobileNumber : clientGroup?.mobileNumber
      const taskName = task?.name;
      const taskId = task?.id;

      let taskMemberIds = [];
      if (taskId) {
        taskMemberIds = await getUserIDs(taskId);
      }

      const clientNumber = client?.clientId;

      const eventTypeSuffix = type === 'TASK' ? '(Task Event)' : '(General Event)';
      const typeEvent = `${title} ${eventTypeSuffix}`;
      const taskStartTime = moment(startTime).format('hh:mm A');
      const taskEndTime = moment(endTime).format('hh:mm A');
      const dateFormatted = moment(date).format('DD-MM-YYYY');
      try {
        if (type === 'TASK' && members) {
          const taskMemberList = members.map((item) => item.id);
          const uniqueUserIds: any = Array.from(new Set([...taskMemberIds, ...taskMemberList]));
          const title = 'Task Event Creation';
          const body = `Your event <strong> ${typeEvent} </strong> has been created for <strong>${dateFormatted}</strong> and  <strong>${taskStartTime}</strong> See you there!`;
          const key = 'TASK_EVENT_CREATION_PUSH';
          insertINTONotificationUpdate(
            title,
            body,
            uniqueUserIds,
            orgId,
            key,
            taskId,
            clientNumber,
          );
          const organization = await Organization.findOne({ id: orgId });
                  const names = members?.map((user) => user.fullName).join(', ');

 if(whatsappEnabled){
  const clientTemplateName = 'task_event_create_client'
    const whatsappOptions = {
            to: clientMobileNumber,
            name: clientTemplateName,
            header: [
              {
                type: 'text',
                text: clientName
              },
            ],
         body:[taskName,names,eventTitle,location,dateFormatted,taskStartTime,plainNotes],
            title: 'Task Event Create Client',
            userId: user?.id,
            orgId:organization?.id,
            key: 'TASK_EVENT_CREATE_CLIENT_WHATSAPP',
          };
                        await sendClientWhatsAppTemplateMessage(whatsappOptions);
        }
          const addressParts = [
            organization.buildingNo || '',
            organization.floorNumber || '',
            organization.buildingName || '',
            organization.street || '',
            organization.location || '',
            organization.city || '',
            organization.district || '',
            organization.state || '',
          ].filter((part) => part && part.trim() !== '');
          const pincode =
            organization.pincode && organization.pincode.trim() !== ''
              ? ` - ${organization.pincode}`
              : '';

          const address = addressParts.join(', ') + pincode;
          if (client) {
            for (let user of taskMemberList) {
              const taskUserDetails = await getUserDetails(user);
              await sendnewMail({
                id: taskUserDetails?.id,
                key: 'TASK_EVENT_CREATION_MAIL',
                email: taskUserDetails?.email,
                data: {
                  memberName: taskUserDetails?.full_name,
                  eventTitle: typeEvent,
                  taskName,
                  clientName,
                  location,
                  date: dateFormatted,
                  startTime: taskStartTime,
                  endTime: taskEndTime,
                  userId: event?.entity['userId'],
                  adress: address,
                  phoneNumber: organization?.mobileNumber,
                  mail: organization?.email,
                  legalName: organization?.tradeName || organization?.legalName,
                },
                filePath: 'task-event-created',
                subject: `${typeEvent}- Task Event Created for ${clientName} - ${taskName}`,
              });
            }
          }
        } 
        
        else if (type === 'EVENT') {
          const title = 'General Event Creation';
          if (user && user?.id) {
            const createdUser: any = [user?.id];
            const body = `Your event ${typeEvent} has been created for ${dateFormatted} at ${taskStartTime}. See you there! `;
            const key = 'GENERAL_EVENT_CREATION_PUSH';
            insertINTONotificationUpdate(title, body, createdUser, orgId, key, taskId);

            if (client) {
              for (let user of createdUser) {
                const taskUserDetails = await getUserDetails(user);
                const organization = await Organization.findOne({ id: orgId });

                const addressParts = [
                  organization.buildingNo || '',
                  organization.floorNumber || '',
                  organization.buildingName || '',
                  organization.street || '',
                  organization.location || '',
                  organization.city || '',
                  organization.district || '',
                  organization.state || '',
                ].filter((part) => part && part.trim() !== '');
                const pincode =
                  organization.pincode && organization.pincode.trim() !== ''
                    ? ` - ${organization.pincode}`
                    : '';
                const address = addressParts.join(', ') + pincode;
                await sendnewMail({
                  id: taskUserDetails?.id,
                  key: 'GENERAL_EVENT_CREATION_MAIL',
                  email: taskUserDetails?.email,
                  data: {
                    userName: taskUserDetails?.full_name,
                    eventTitle: typeEvent,
                    location,
                    date: dateFormatted,
                    startTime: taskStartTime,
                    endTime: taskEndTime,
                    userId: event.entity['userId'],
                    adress: address,
                    phoneNumber: organization?.mobileNumber,
                    mail: organization?.email,
                    legalName: organization?.tradeName || organization?.legalName,
                  },
                  filePath: 'general-event-created',
                  subject: `General Event Created`,
                });
              }
            }
          }
        }
      } catch (error) {
        console.error('Error sending Event Notification:', error);
      }
      // Whatsapp
      for (const userId of adminIds) {
        const sessionValidation = await ViderWhatsappSessions.findOne({
          where: { userId: userId, status: 'ACTIVE' },
        });
        if (sessionValidation) {
          const adminUserDetails = await getUserDetails(userId);
          const {
            full_name: userFullName,
            mobile_number: userPhoneNumber,
            country_code: countryCode,
          } = adminUserDetails;
          const userWhatsAppNumber = fullMobileNumberWithCountry(userPhoneNumber, countryCode);
          const key =
            type === 'TASK' ? 'TASK_EVENT_CREATION_WHATSAPP' : 'GENERAL_EVENT_CREATION_WHATSAPP';
          const whatsappMessageBody = `
      Hi *${userFullName}*
      A new Event has been created in ATOM:
      
      *Event name:* ${typeEvent}
      *Date :* ${dateFormatted}
      *Location:* ${location}
      ${clientName ? `*Client Name:* ${clientName}` : ''}
      ${taskName ? `*Task Name:* ${taskName}` : ''}
      
      We hope this helps!
      `;

          const whatsappTitle = 'Event Created';
          await sendWhatsAppTextMessage(
            // `91${userPhoneNumber}`,
            userWhatsAppNumber,
            whatsappMessageBody,
            orgId,
            whatsappTitle,
            userId,
            key,
          );
        }
      }
    } catch (error) {
      console.error('Error sending Event WhatsApp notification:', error);
    }
  }

  // async afterInsert(event: InsertEvent<Event>) {
  //   // const eventName = event.entity.title
  //   // const userName = event.entity.user.fullName
  //   // const dateOfEvent = new Date(event.entity.date).toISOString().substring(0, 10);
  //   // const startTime = new Date(event.entity.startTime).toISOString().substring(0, 10);
  //   // const endTime = new Date(event.entity.endTime).toISOString().substring(0, 10);
  //   // const location = event.entity.location
  //   // if (event.entity.type === "EVENT") {
  //   //   const orgId = event.entity.organization.id
  //   //   const orgAdminDetails = await getAdminIDsBasedOnOrganizationId(orgId)
  //   //   for (let admin of orgAdminDetails) {
  //   //     await sendMail({
  //   //       email: admin.email,
  //   //       data: {
  //   //         adminName: admin.full_name,
  //   //         eventName: eventName,
  //   //         userName: userName,
  //   //         date: dateOfEvent,
  //   //         startTime: startTime,
  //   //         endTime: endTime,
  //   //         location: location
  //   //       },
  //   //       filePath: "general-event-created",
  //   //       subject: "Event Created | Vider"
  //   //     })
  //   //   }
  //   // }
  //   // else if (event.entity.type === "TASK") {
  //   //   const taskName = event.entity.task.name
  //   //   const clientName = event.entity.client.displayName
  //   //   const eventMembersDetails = event.entity.members
  //   //   for (let member of eventMembersDetails) {
  //   //     await sendMail({
  //   //       email: member.email,
  //   //       data: {
  //   //         userName: member.fullName,
  //   //         eventName: eventName,
  //   //         taskName: taskName,
  //   //         clientName: clientName,
  //   //         date: dateOfEvent,
  //   //         startTime: startTime,
  //   //         endTime: endTime,
  //   //         location: location
  //   //       },
  //   //       filePath: "task-event-created",
  //   //       subject: "Event Created | Vider"
  //   //     })
  //   //   }
  //   // }
  // }
}
