import {
    Connection,
    EntitySubscriberInterface,
    EventSubscriber,
    InsertEvent,
} from 'typeorm';
import Category from '../modules/categories/categories.entity';

@EventSubscriber()
export class CategorySubscriber implements EntitySubscriberInterface<Category> {
    constructor(private readonly connection: Connection) {
        connection.subscribers.push(this);
    }

    listenTo() {
        return Category;
    }

    async beforeInsert(event: InsertEvent<Category>) {
    }

    async afterInsert(event: InsertEvent<Category>) {
    }
}