import { IsNotEmpty, IsOptional, Matches } from 'class-validator';
import { CategoryEnum, SubCategoryEnum } from 'src/modules/clients/dto/types';
import { LeadStatusEnum } from '../lead.entity';

class CreateLeadDto {
  @IsNotEmpty()
  category: CategoryEnum;

  @IsOptional()
  subCategory: SubCategoryEnum;

  // @IsNotEmpty()
  // name: string;

  @IsNotEmpty()
  @Matches(/^[^<>:"\/\\|?*\.]*$/, {
    message: 'Display name cannot contain <, >, :, ", /, \\, |, ?, *, or .',
  })
  name: string;

  @IsOptional()
  description: string;

  @IsNotEmpty()
  mobileNumber: string;

  @IsNotEmpty()
  email: string;

  @IsOptional()
  status: LeadStatusEnum;

  @IsNotEmpty({ message: "Select Country" })
  countryCode: string;
}

export default CreateLeadDto;
