import { ValidationPipe } from '@nestjs/common';
import { NestFactory } from '@nestjs/core';
import { AppModule } from './app/app.module';
import * as compression from 'compression';
import { NestExpressApplication } from '@nestjs/platform-express';
import { join } from 'path';
import { urlencoded, json } from 'express';
const cors = require('cors');

async function bootstrap() {
  const app = await NestFactory.create<NestExpressApplication>(AppModule);
  app.enableCors({
    origin: (origin, callback) => {
      // Allow requests from any chrome-extension origin
      if (origin && (origin.startsWith('chrome-extension://') || origin.includes('vider.in')) ) {
        callback(null, true);
      } else {
        // Disallow other origins
        // callback(new Error('Not allowed by CORS'));
        callback(null, true);
      }
    },
    methods: 'GET,HEAD,PUT,PATCH,POST,DELETE',
    credentials: true,
    // maxAge: 86400,
  });
  app.use(compression());
  app.useGlobalPipes(new ValidationPipe());
  app.use(json({ limit: '50mb' }));
  app.use(urlencoded({ extended: true, limit: '50mb' }));
  app.setBaseViewsDir(join(__dirname, '../views'));
  app.setViewEngine('hbs');
  await app.listen(process.env.PORT || 5000);
}
bootstrap();
