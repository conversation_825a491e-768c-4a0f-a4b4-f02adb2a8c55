import { BadRequestException, Injectable, InternalServerErrorException } from '@nestjs/common';
import CreateCollectDataDto from './dto/create-collect-data.dto';
import CollectData from './collect-data.entity';
import { User } from '../users/entities/user.entity';
import Client from '../clients/entity/client.entity';
import Task from '../tasks/entity/task.entity';
import { AwsService } from 'src/modules/storage/upload.service';
import Storage, { StorageSystem, StorageType } from 'src/modules/storage/storage.entity';
import { In, LessThan, MoreThan, createQueryBuilder, getRepository } from 'typeorm';
import { v4 as uuidv4 } from 'uuid';
import { Cron, CronExpression } from '@nestjs/schedule';
import * as moment from 'moment';
import { StorageService } from '../storage/storage.service';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { Event_Actions } from 'src/event-listeners/actions';
import CronActivity from '../cron-activity/cron-activity.entity';
import { OneDriveStorageService } from '../ondrive-storage/onedrive-storage.service';
import Activity, { ActivityType } from '../activity/activity.entity';
import { BharathStorageService } from '../storage/bharath-storage.service';
import { Notification } from 'src/notifications/notification.entity';
import ClientGroup from '../client-group/client-group.entity';
import { GoogleDriveStorageService } from '../ondrive-storage/googledrive-storage.service';
import OrganizationPreferences from '../organization-preferences/entity/organization-preferences.entity';
import { sendnewMail } from 'src/emails/newemails';

@Injectable()
export class CollectDataService {
  constructor(
    private oneDriveService: OneDriveStorageService,
    private googleDriveService: GoogleDriveStorageService,
    private uploadService: AwsService,
    private storageService: StorageService,
    private bharathService: BharathStorageService,
    private eventEmitter: EventEmitter2,
  ) { }

  async findOne(id: string) {
    const collectData = await CollectData.findOne({
      where: { uid: id, active: true },
      relations: ['user', 'client', 'client.clientImage', 'clientGroup', 'clientGroup.clientGroupImage', 'task', 'task.members', 'user.organization'],
    });

    if (collectData) {
      const members = collectData.task.members.map((member) => member.fullName);
      return {
        id: collectData?.id,
        organization:
          collectData.user.organization.tradeName || collectData.user.organization.legalName,
        user: collectData.user.fullName,
        notes: collectData.notes,
        client: collectData?.client ? collectData?.client?.displayName : collectData?.clientGroup?.displayName,
        fileUrl: collectData?.client?.clientImage ? collectData?.client?.clientImage?.fileUrl : collectData?.clientGroup?.clientGroupImage?.fileUrl,
        phone: collectData.user.organization.mobileNumber,
        email: collectData.user.organization.email,
        serviceName: collectData.name,
        fields: collectData.listOfFiles,
        listOfConfirmedFiles: collectData?.listOfConfirmedFiles,
        createdDate: collectData.createdAt,
        updateData: collectData.updatedAt,
        task: collectData.task.id,
        dueDate: collectData.task.dueDate,
        members: collectData.task.members,
        countryCode: collectData?.user?.organization?.countryCode,
        comments: collectData?.comments,
      };
    }
    return null;
  }

  async findOneTask(id: number) {
    const collectData = await CollectData.find({
      where: { task: id },
      relations: ['task', 'user', 'user.imageStorage'],
      order: {
        ['id']: -1,
      },
    });
    if (collectData[0]?.id) {
      let taskAttachments = await Storage.find({
        where: { collectId: collectData[0]?.id },
        relations: ['task'],
      });
      collectData[0]['taskAttachments'] = taskAttachments;
    }
    return collectData;
  }

  async getAttachments(id: string) {
    const collectData = await CollectData.findOne({ uid: id });
    if (collectData) {
      let taskAttachments = await Storage.find({
        where: { collectId: collectData.id },
        relations: ['task'],
      });
      return taskAttachments;
    }
    return [];
  }

  async getReferenceDocs(id: string) {
    const attachments = await Storage.find({
      where: {
        collectId: id,
        origin: null
      }
    });

    return attachments;
  }

  // async create(userId: number, data: CreateCollectDataDto) {

  //   const { freeSpace, unExpriedLinks } = await this.storageService.getOrgStorage(userId);
  //   const user = await User.findOne({ where: { id: userId } });
  //   const client = await Client.findOne({ where: { id: data.client } });
  //   const task = await Task.findOne({ where: { id: data.task }, relations: ['client', 'category', 'subCategory', 'clientGroup'] });
  //   let referenceStorage: Storage[] = []
  //   data.fields?.map(async (field) => {
  //     if (field.storage) {
  //       const storage = await this.storageService.addAttachements(userId, field.storage);
  //       referenceStorage.push(storage);
  //       return { ...field, storage: storage.id }
  //     } else {
  //       field
  //     }
  //   });
  //   let collectData = new CollectData();
  //   collectData.appName = data.appName;
  //   collectData.name = data.name;
  //   collectData.user = user;
  //   collectData.client = client;
  //   collectData.clientGroup = task.clientGroup;
  //   collectData.notes = data.notes;
  //   collectData.task = task;
  //   collectData.listOfFiles = { fileNames: [...data.fields] };
  //   collectData.listOfConfirmedFiles = { confirmFileNames: [...data.confirmDocuments] };
  //   collectData.active = true;
  //   collectData.whatsappCheck = data.whatsappCheck;
  //   collectData.emailCheck = data.emailCheck;
  //   collectData['userId'] = userId;
  //   collectData.uid = uuidv4();

  //   if (freeSpace - unExpriedLinks >= 209715200) {
  //     const collect = await collectData.save();
  //     let collectactivity = new Activity();
  //     collectactivity.action = Event_Actions.DATA_COLLECTION_LINK_CREATED;
  //     collectactivity.actorId = user.id;
  //     collectactivity.type = ActivityType.TASK;
  //     collectactivity.typeId = task.id;
  //     collectactivity.remarks = `Collect Data Link Created by ${user.fullName}`;
  //     await collectactivity.save();
  //     referenceStorage.map((storage) => ({
  //       ...storage,
  //       collectId: collect.id,
  //     }));
  //     console.log({ referenceStorage })
  //     await Storage.save(referenceStorage);

  //     return collectData;
  //   } else {
  //     throw new BadRequestException(
  //       'We regret to inform you that the storage limit for your organization has been exceeded. As a result, no further data can be stored until the storage usage is reduced or additional storage capacity is allocated.',
  //     );
  //   }
  // }

  async create(userId: number, data: CreateCollectDataDto) {
    // Get organization storage details
    const { freeSpace, unExpriedLinks } = await this.storageService.getOrgStorage(userId);

    // Fetch related entities
    const user = await User.findOne({ where: { id: userId } });
    if (!user) throw new BadRequestException('User not found');

    const client = await Client.findOne({ where: { id: data.client } });
    const clientGroup = await ClientGroup.findOne({ where: { id: data.clientGroup } });

    if (!client && !data.clientGroup) throw new BadRequestException('Client not found');
    if (!clientGroup && !data.client) throw new BadRequestException('Client Group not found');

    const task = await Task.findOne({
      where: { id: data.task },
      relations: ['client', 'category', 'subCategory', 'clientGroup']
    });
    if (!task) throw new BadRequestException('Task not found');

    // Process fields with storage
    let referenceStorage: Storage[] = []
    const fields = await Promise.all(
      (data.fields || []).map(async (field) => {
        if (field.storage) {
          const storage = await this.storageService.addAttachements(userId, field.storage);
          referenceStorage.push(storage);
          return { ...field, storage: storage.id }; // Map field with storage ID
        }
        return field;
      })
    );
    // Create the collectData entity
    const collectData = new CollectData();
    collectData.appName = data.appName;
    collectData.name = data.name;
    collectData.user = user;
    collectData.client = client;
    collectData.clientGroup = task.clientGroup || clientGroup;
    collectData.notes = data.notes;
    collectData.task = task;
    collectData.listOfFiles = { fileNames: fields };
    collectData.listOfConfirmedFiles = { confirmFileNames: [...data.confirmDocuments] };
    collectData.active = true;
    collectData.whatsappCheck = data.whatsappCheck;
    collectData.emailCheck = data.emailCheck;
    collectData.user = user;
    collectData.expiryUpdateDate = moment().format('YYYY-MM-DD HH:mm:ss.SSSSSS');
    collectData.uid = uuidv4();
    collectData['userId'] = userId;

    // Validate storage space
    const requiredSpace = 209715200; // 200MB in bytes
    if (freeSpace - unExpriedLinks < requiredSpace) {
      throw new BadRequestException(
        'We regret to inform you that the storage limit for your organization has been exceeded. ' +
        'No further data can be stored until the storage usage is reduced or additional storage capacity is allocated.'
      );
    }

    // Save collectData
    const savedCollectData = await collectData.save();

    // Create an activity log
    const collectActivity = new Activity();
    collectActivity.action = Event_Actions.DATA_COLLECTION_LINK_CREATED;
    collectActivity.actorId = user.id;
    collectActivity.type = ActivityType.TASK;
    collectActivity.typeId = task.id;
    collectActivity.remarks = `Collect Data Link Created by ${user.fullName}`;
    await collectActivity.save();


    referenceStorage.forEach((storage) => {
      storage.collectId = savedCollectData.id;
    });


    await Storage.save(referenceStorage);


    return savedCollectData;
  }


  async confirmDocument(userId: number, data: any) {
    const user = await User.findOne({ where: { id: userId } });

    const collectData = await CollectData.findOne({ where: { id: data?.id }, relations: ['task', 'task.client', 'task.clientGroup'] });
    for (let i of collectData?.listOfConfirmedFiles['confirmFileNames']) {
      for (let j of data?.listOfConfirmedFiles['confirmFileNames']) {
        if (i.id === j.id) {
          if (i.status !== j.status) {
            let newNotification = new Notification();
            newNotification.title = `Collect Data Document is ${j.status === "confirmed" ? "Confirmed" : j.status === "rejected" ? "Rejected" : ""} `;
            newNotification.body = `For task <strong>${collectData?.task?.name}</strong> with <strong>${collectData?.task?.client ? "Client " + collectData?.task?.client?.displayName : collectData?.task?.clientGroup ? "CLient Group " + collectData?.task?.clientGroup?.displayName : ""}</strong> collect data document <strong>${i?.name}</strong> is ${j.status === "confirmed" ? "Confirmed" : j.status === "rejected" ? "Rejected" : ""} `;
            newNotification.user = user;
            newNotification.status = 'unread';
            newNotification.save()
          }
        }
      }
    }

    collectData.listOfConfirmedFiles = data?.listOfConfirmedFiles;
    collectData.save();
    return collectData;
  }

  async updateData(id: number, userId: number, data: CreateCollectDataDto) {
    const user = await User.findOne({ where: { id: userId } });
    const client = await Client.findOne({ where: { id: data.client } });
    const task = await Task.findOne({ where: { id: data.task }, relations: ['clientGroup'] });

    let referenceStorage: Storage[] = []
    const fields = await Promise.all(
      (data.fields || []).map(async (field) => {
        if (field?.storage?.file) {
          const storage = await this.storageService.addAttachements(userId, field.storage);
          referenceStorage.push(storage);
          return { ...field, storage: storage.id }; // Map field with storage ID
        }
        return field;
      })
    );
    const collectData = await CollectData.findOne({ where: { id: id } });

    const allReferences = await Storage.find({
      where: {
        collectId: collectData.id,
        origin: null
      }
    });
    const existingids: number[] = fields?.filter(i => i.storage).map(field => field.storage);
    const deleteRefs = allReferences.filter((r) => !existingids.includes(r.id));
    for (let ref of deleteRefs) {
      if (ref.storageSystem === StorageSystem.AMAZON) {
        this.storageService.deleteAwsFile(ref.file);
      } else if (ref.storageSystem === StorageSystem.MICROSOFT) {
        this.oneDriveService.deleteOneDriveFile(userId, ref.fileId);
      }
    }
    collectData.appName = data.appName;
    collectData.name = data.name;
    collectData.user = user;
    collectData.client = client;
    collectData.clientGroup = task?.clientGroup;
    collectData.notes = data.notes;
    collectData.task = task;
    collectData.listOfFiles = { fileNames: fields };
    collectData.listOfConfirmedFiles = { confirmFileNames: [...data.confirmDocuments] };
    collectData.active = true;
    collectData.whatsappCheck = data?.whatsappCheck;
    collectData.emailCheck = data?.emailCheck;
    collectData['userId'] = userId;
    await collectData.save();
    referenceStorage.forEach((storage) => {
      storage.collectId = collectData.id;
    });
    await Storage.save(referenceStorage);

    let collectactivity = new Activity();
    collectactivity.action = Event_Actions.DATA_COLLECTION_LINK_UPDATED;
    collectactivity.actorId = user.id;
    collectactivity.type = ActivityType.TASK;
    collectactivity.typeId = task.id;
    collectactivity.remarks = `Collect Data Link Updated by ${user.fullName}`;
    await collectactivity.save();
    return collectData.save();
  }

  async remindData(id: number,  userId: number, data: any) {
    const user = await User.findOne({ where: { id: userId }, relations:['organization'] });
    const collectData = await CollectData.findOne({ where: { id: id }, relations:['client','clientGroup','task'] });
    const {
      task,
      client,
      uid,
      listOfFiles,
      expiryUpdateDate,
      clientGroup,
    } = collectData;
    const { emailCheck, whatsappCheck } = data;
    const fileslist: any = listOfFiles;

    const linkExpiryDate = moment(expiryUpdateDate, 'YYYY-MM-DD HH:mm:ss.SSSSSS').add(5, 'days').format('DD-MM-YYYY');
    const listofDocs = fileslist?.fileNames;
    const key = 'CREATION_COLLECT_DATA_MAIL';
    
    if (emailCheck && key === 'CREATION_COLLECT_DATA_MAIL' && (client|| clientGroup)) {
      const addressParts = [
        user.organization.buildingNo || '',
        user.organization.floorNumber || '',
        user.organization.buildingName || '',
        user.organization.street || '',
        user.organization.location || '',
        user.organization.city || '',
        user.organization.district || '',
        user.organization.state || ''
      ].filter(part => part && part.trim() !== '');
      const pincode = user.organization.pincode && user.organization.pincode.trim() !== '' ? ` - ${user.organization.pincode}` : '';
      const address = addressParts.join(', ') + pincode;
      const mailOptions = {
        id: user?.id,
        key: 'CREATION_COLLECT_DATA_MAIL',
        email: client ? client.email : clientGroup.email,
        clientMail: 'ORGANIZATION_CLIENT_EMAIL',
        data: {
          clientName: client ? client.displayName : clientGroup?.displayName,
          TaskId: task?.taskNumber,
          TaskName: task?.name,
          AccessLink: `${process.env.WEBSITE_URL}/collect-data/${uid}`,
          TaskExpiryDate: linkExpiryDate,
          legalName: user?.organization?.tradeName || user?.organization?.legalName,
          adress: address,
          phoneNumber: user?.organization?.mobileNumber,
          mail: user?.organization?.email,
          website: user?.organization?.website,
          documents: listofDocs,
          userId: userId,
        },
        filePath: 'collect-data-remainder',
        subject: 'Remainder ! Secure Documents Upload Requested for Service',
      };

      const orgPreferences: any = await OrganizationPreferences.findOne({
        where: { organization: user.organization.id },
      });
      const clientPreferences = orgPreferences?.clientPreferences?.email;
      if (clientPreferences && clientPreferences[key]) {
        await sendnewMail(mailOptions);
      }
    }
  }

  async updateDataComments(data) {
    const collectData = await CollectData.findOne({ where: { id: data?.id } });
    collectData.comments = data?.comments;
    await collectData.save();
    return collectData;
  }

  async updateCollectDataExipryExtend(data) {
    const collectData = await CollectData.findOne({ where: { id: data?.id } });
    collectData.active = true;
    collectData.expiryUpdateDate = moment().format('YYYY-MM-DD HH:mm:ss.SSSSSS');
    await collectData.save();
    return collectData;
  }

  async saveAttchement(
    origin: string,
    collectId: any,
    taskId: number,
    files: Express.Multer.File[],
  ) {
    let task = await Task.findOne({
      where: { id: taskId },
      relations: ['organization', 'clientGroup'],
    });

    if (task.organization.storageSystem === StorageSystem.AMAZON) {
      return await this.addAttachments(origin, collectId, taskId, files);
    } else if (task.organization.storageSystem === StorageSystem.MICROSOFT) {
      return await this.oneDriveService.collectDataAddAttc(origin, collectId, taskId, files);
    } else if (task.organization.storageSystem === StorageSystem.GOOGLE) {
      return await this.googleDriveService.collectDataAddAttc(origin, collectId, taskId, files);
    }
    else if (task.organization.storageSystem === StorageSystem.BHARATHCLOUD) {
      return await this.bharathService.collectDataAddAttc(origin, collectId, taskId, files);
    }
  }

  async addAttachments(
    origin: string,
    collectId: any,
    taskId: number,
    files: Express.Multer.File[],
  ) {

    try {
      let task = await Task.findOne({
        where: { id: taskId },
        relations: ['client', 'category', 'subCategory', 'clientGroup'],
      });

      let taskStorage = await this.existingClientTaskStorage(task);

      let taskAttachments: Storage[] = [];
      const collectData = await CollectData.findOne({ where: { uid: collectId } });

      for (let file of files) {
        const { buffer, mimetype, originalname } = file;
        let key = `storage/tasks/${taskId}/${file.originalname}`;

        let upload: any = await this.uploadService.upload(buffer, key, mimetype);

        let storage = new Storage();
        storage.name = originalname;
        storage.file = upload.Key;
        storage.task = task;
        storage.fileType = mimetype;
        storage.fileSize = file.size;
        storage.client = task.client;
        storage.clientGroup = task.clientGroup;
        storage.type = StorageType.FILE;
        storage.parent = taskStorage;
        storage.storageSystem = StorageSystem.AMAZON;
        storage.collectId = collectData?.id;
        storage.origin = origin;
        taskAttachments.push(storage);
      }

      await Storage.save(taskAttachments);
      this.eventEmitter.emit(Event_Actions.COLLECT_DATA_FILES_UPLOAD, {
        origin,
        collectId,
        taskId,
        files: files.map((file) => file.originalname),
      });

      return {
        success: true,
      };
    } catch (err) {
      console.log(err);
      throw new InternalServerErrorException(err);
    }
  }

  async addAttachmentsFromStorage(taskId: number, fileIds: number[]) {
    try {
      let task = await Task.findOne({
        where: { id: taskId },
        relations: ['client', 'category', 'subCategory', 'clientGroup'],
      });

      let files = await Storage.find({
        where: { id: In(fileIds) },
        relations: ['client', 'clientGroup'],
      });

      let taskStorage = await this.existingClientTaskStorage(task);

      let taskAttachments: Storage[] = [];

      for (let file of files) {
        let existingFile: any = await this.uploadService.get(file.file);

        let key = `storage/tasks/${taskId}-${moment().valueOf()}/${file.file}`;

        let uploadedFile: any = await this.uploadService.upload(
          existingFile?.Body as Buffer,
          key,
          file.fileType,
        );

        let storage = new Storage();
        storage.name = file.name;
        storage.file = uploadedFile.Key;
        storage.task = task;
        storage.fileType = file.fileType;
        storage.client = file.client;
        storage.clientGroup = file.clientGroup;
        storage.type = StorageType.FILE;
        storage.parent = taskStorage;
        taskAttachments.push(storage);
      }

      await Storage.save(taskAttachments);

      return {
        success: true,
      };
    } catch (err) {
      throw new BadRequestException(err);
    }
  }

  // async existingClientTaskStorage(task: Task) {
  //   if (!task.client || !task.clientGroup ) return null;

  //   let taskFinYearStorage = await Storage.findOne({
  //     where: {
  //       name: task.financialYear,
  //       client: { id: task.client?.id },
  //     },
  //   });

  //   if (!taskFinYearStorage) {
  //     taskFinYearStorage = new Storage();
  //     taskFinYearStorage.name = task.financialYear;
  //     taskFinYearStorage.client = task.client;
  //     taskFinYearStorage.clientGroup = task.clientGroup;
  //     taskFinYearStorage.type = StorageType.FOLDER;
  //     taskFinYearStorage.uid = uuidv4();
  //     await taskFinYearStorage.save();
  //   }

  //   let taskCategoryStorage = await Storage.findOne({
  //     where: {
  //       name: task.category?.name,
  //       parent: { id: taskFinYearStorage.id },
  //       client: { id: task.client?.id },
  //     },
  //     relations: ['parent'],
  //   });

  //   if (!taskCategoryStorage && task.category) {
  //     taskCategoryStorage = new Storage();
  //     taskCategoryStorage.name = task.category?.name;
  //     taskCategoryStorage.client = task.client;
  //     taskCategoryStorage.clientGroup = task.clientGroup;
  //     taskCategoryStorage.type = StorageType.FOLDER;
  //     taskCategoryStorage.parent = taskFinYearStorage;
  //     taskCategoryStorage.uid = uuidv4();
  //     await taskCategoryStorage.save();
  //   }

  //   let taskSubCategoryStorage = await Storage.findOne({
  //     where: {
  //       name: task.subCategory?.name,
  //       parent: { id: taskCategoryStorage?.id },
  //       client: { id: task.client?.id },
  //     },
  //     relations: ['parent'],
  //   });

  //   if (!taskSubCategoryStorage && task.subCategory) {
  //     taskSubCategoryStorage = new Storage();
  //     taskSubCategoryStorage.name = task.subCategory?.name;
  //     taskSubCategoryStorage.client = task.client;
  //     taskSubCategoryStorage.clientGroup = task.clientGroup;
  //     taskSubCategoryStorage.type = StorageType.FOLDER;
  //     taskSubCategoryStorage.parent = taskCategoryStorage;
  //     taskSubCategoryStorage.uid = uuidv4();
  //     await taskSubCategoryStorage.save();
  //   }

  //   let taskStorage = await createQueryBuilder(Storage, 'storage')
  //     .leftJoinAndSelect('storage.parent', 'parent')
  //     .where('storage.name = :name', { name: task.name })
  //     .andWhere('(parent.id = :subCategory or parent.id = :category)', {
  //       subCategory: taskSubCategoryStorage?.id,
  //       category: taskCategoryStorage?.id,
  //     })
  //     .getOne();

  //   if (!taskStorage) {
  //     let storage = new Storage();
  //     storage.name = task.name;
  //     storage.client = task.client;
  //     storage.clientGroup = task.clientGroup;
  //     storage.type = StorageType.FOLDER;
  //     storage.uid = uuidv4();
  //     storage.parent = taskSubCategoryStorage || taskCategoryStorage || taskFinYearStorage;
  //     taskStorage = await Storage.save(storage);
  //   }

  //   return taskStorage;
  // }

  async existingClientTaskStorage(task: Task) {
    if (!task.client && !task.clientGroup) return null;

    const clientCondition = task.client ? { client: { id: task.client.id } } : {};
    const clientGroupCondition = task.clientGroup ? { clientGroup: { id: task.clientGroup.id } } : {};

    let taskFinYearStorage = await Storage.findOne({
      where: {
        name: task.financialYear,
        ...clientCondition,
        ...clientGroupCondition,
      },
    });

    if (!taskFinYearStorage) {
      taskFinYearStorage = new Storage();
      taskFinYearStorage.name = task.financialYear;
      taskFinYearStorage.client = task.client || null;
      taskFinYearStorage.clientGroup = task.clientGroup || null;
      taskFinYearStorage.type = StorageType.FOLDER;
      taskFinYearStorage.uid = uuidv4();
      await taskFinYearStorage.save();
    }

    let taskCategoryStorage = await Storage.findOne({
      where: {
        name: task.category?.name,
        parent: { id: taskFinYearStorage.id },
        ...clientCondition,
        ...clientGroupCondition,
      },
      relations: ['parent'],
    });

    if (!taskCategoryStorage && task.category) {
      taskCategoryStorage = new Storage();
      taskCategoryStorage.name = task.category.name;
      taskCategoryStorage.client = task.client || null;
      taskCategoryStorage.clientGroup = task.clientGroup || null;
      taskCategoryStorage.type = StorageType.FOLDER;
      taskCategoryStorage.parent = taskFinYearStorage;
      taskCategoryStorage.uid = uuidv4();
      await taskCategoryStorage.save();
    }

    let taskSubCategoryStorage = await Storage.findOne({
      where: {
        name: task.subCategory?.name,
        parent: { id: taskCategoryStorage?.id },
        ...clientCondition,
        ...clientGroupCondition,
      },
      relations: ['parent'],
    });

    if (!taskSubCategoryStorage && task.subCategory) {
      taskSubCategoryStorage = new Storage();
      taskSubCategoryStorage.name = task.subCategory.name;
      taskSubCategoryStorage.client = task.client || null;
      taskSubCategoryStorage.clientGroup = task.clientGroup || null;
      taskSubCategoryStorage.type = StorageType.FOLDER;
      taskSubCategoryStorage.parent = taskCategoryStorage;
      taskSubCategoryStorage.uid = uuidv4();
      await taskSubCategoryStorage.save();
    }

    let taskStorage = await createQueryBuilder(Storage, 'storage')
      .leftJoinAndSelect('storage.parent', 'parent')
      .where('storage.name = :name', { name: task.name })
      .andWhere('(parent.id = :subCategory or parent.id = :category)', {
        subCategory: taskSubCategoryStorage?.id,
        category: taskCategoryStorage?.id,
      })
      .getOne();

    if (!taskStorage) {
      const storage = new Storage();
      storage.name = task.name;
      storage.client = task.client || null;
      storage.clientGroup = task.clientGroup || null;
      storage.type = StorageType.FOLDER;
      storage.uid = uuidv4();
      storage.parent = taskSubCategoryStorage || taskCategoryStorage || taskFinYearStorage;
      taskStorage = await Storage.save(storage);
    }

    return taskStorage;
  }


  @Cron(CronExpression.EVERY_DAY_AT_MIDNIGHT)
  async expireLinks() {
    if (process.env.Cron_Running === 'true') {
      const cronData = new CronActivity();
      cronData.cronType = 'COLLECT DATA';
      cronData.cronDate = moment().toDate().toString();
      cronData.startTime = moment().format('YYYY-MM-DD HH:mm:ss');
      const cornActivityID = await cronData.save();

      const date5DaysAgo = moment().subtract(5, 'days').toDate();
      const collectDataRepository = getRepository(CollectData);
      const collectDataLinks = await CollectData.find({
        where: {
          expiryUpdateDate: LessThan(date5DaysAgo),
          active: true,
        },
        relations: ['user', 'task'],
      });
      try {
        for (const data of collectDataLinks) {
          data.active = false;
          await collectDataRepository.save(data);
          if (data?.user?.id && data?.task?.id) {
            let taskactivity = new Activity();
            taskactivity.action = Event_Actions.DATA_COLLECTION_LINK_EXPIRED;
            taskactivity.actorId = data.user.id;
            taskactivity.type = ActivityType.TASK;
            taskactivity.typeId = data.task.id;
            taskactivity.remarks = `Collect Data Link Expired`;
            await taskactivity.save();
          }
        }
      } catch (error) {
        const getcornActivityID = await createQueryBuilder(CronActivity, 'cronActivity')
          .where('id = :id', { id: cornActivityID.id })
          .getOne();
        getcornActivityID.responseData = error?.message;
        getcornActivityID.endTime = moment().format('YYYY-MM-DD HH:mm:ss');
        await getcornActivityID.save();
        return console.log(error?.message);
      }

      const getcornActivityID = await createQueryBuilder(CronActivity, 'cronActivity')
        .where('id = :id', { id: cornActivityID.id })
        .getOne();
      getcornActivityID.responseData = `total: ${collectDataLinks.length}`;
      getcornActivityID.endTime = moment().format('YYYY-MM-DD HH:mm:ss');
      await getcornActivityID.save();
    }
  }
}
