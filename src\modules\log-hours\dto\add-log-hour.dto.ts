import { IsDateString, IsNotEmpty, IsNumber, IsOptional } from 'class-validator';
import { ExpenditureStatus } from 'src/modules/expenditure/dto/types';
import { User } from 'src/modules/users/entities/user.entity';

export class AddLogHour {
  @IsNotEmpty()
  users: User[];

  @IsNotEmpty()
  @IsDateString()
  completedDate: string;

  @IsNotEmpty()
  @IsNumber()
  duration: number;

  @IsNotEmpty()
  @IsNumber()
  taskId: number;

  @IsOptional()
  description: string;

  @IsOptional()
  startDateTime: string;

  @IsOptional()
  endDateTime: string;

  @IsOptional()
  billable: boolean;
}

export class UpdateLogHour {
  @IsNotEmpty()
  @IsDateString()
  completedDate: string;

  @IsNotEmpty()
  @IsNumber()
  duration: number;

  @IsOptional()
  description: string;

  @IsOptional()
  startTimeNew: string;

  @IsOptional()
  endTimeNew: string;

  @IsOptional()
  approvalStatus: ExpenditureStatus;

  @IsOptional()
  billable: any;
}

export default AddLogHour;
