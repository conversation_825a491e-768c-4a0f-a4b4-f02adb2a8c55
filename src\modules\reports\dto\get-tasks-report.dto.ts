import { IsDateString, IsEnum, IsOptional } from 'class-validator';
import { BillableType } from './get-employee-log-hours-report';

export class getTasksReport {
  @IsOptional()
  category: Array<number>;

  @IsOptional()
  subCategory: Array<number>;

  @IsOptional()
  @IsDateString()
  fromDate: string;

  @IsOptional()
  @IsDateString()
  toDate: string;

  @IsOptional()
  client: number;

  @IsOptional()
  clientType: string | null;

  @IsOptional()
  status: string;

  @IsOptional()
  priority: string;

  @IsOptional()
  financialYear: string;

  @IsOptional()
  members: Array<number>;

  @IsOptional()
  taskLeader: Array<number>;

  @IsOptional()
  @IsEnum(BillableType)
  billingType: BillableType;

  @IsOptional()
  taskType: Array<'recurring' | 'non_recurring'>;

}
