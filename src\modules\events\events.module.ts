import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { EventListener } from 'src/event-listeners/event.listener';
import Event from './event.entity';
import { EventsController } from './events.controller';
import { EventsService } from './events.service';
import { GeneralAndTaskEventSubscriber } from 'src/event-subscribers/generalAndTaskEvent.subscriber';
import { AwsService } from '../storage/upload.service';
import { BharathCloudService } from '../storage/bharath-upload.service';
import { BharathStorageService } from '../storage/bharath-storage.service';
import { OneDriveStorageService } from '../ondrive-storage/onedrive-storage.service';
import { GoogleDriveStorageService } from '../ondrive-storage/googledrive-storage.service';
import { StorageService } from '../storage/storage.service';
import { AttachmentsService } from '../tasks/services/attachments.service';

@Module({
  imports: [TypeOrmModule.forFeature([Event])],
  controllers: [EventsController],
  providers: [EventsService, EventListener, GeneralAndTaskEventSubscriber,
    AwsService,
    BharathCloudService,
    BharathStorageService,
    OneDriveStorageService,
    GoogleDriveStorageService,
    StorageService,
    AttachmentsService
  ],
})
export class EventsModule {}
