import Client from 'src/modules/clients/entity/client.entity';
import { RecurringFrequency } from 'src/modules/tasks/dto/types';
import Task from 'src/modules/tasks/entity/task.entity';
import { User } from 'src/modules/users/entities/user.entity';
import {
  BaseEntity,
  Column,
  CreateDateColumn,
  Entity,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { TaskData } from '../dto/task-data.dto';
import UdinTask from 'src/modules/udin-task/udin-task.entity';
import ClientGroup from 'src/modules/client-group/client-group.entity';

export enum RecurProfileStatus {
  IN_PROGRESS = 'in_progress',
  TERMINATED = 'terminated',
  COMPLETED = 'completed',
}

@Entity()
class RecurringProfile extends BaseEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  name: string;

  @Column()
  financialYear: string;

  @Column({ type: 'enum', enum: RecurringFrequency, nullable: true })
  frequency: RecurringFrequency;

  @OneToMany(() => Task, (task) => task.recurringProfile)
  tasks: Task[];

  @OneToMany(() => UdinTask, (udinTask) => udinTask.recurringProfile)
  udinTasks: UdinTask[];

  @ManyToOne(() => User, (user) => user.recurringProfiles, { eager: true })
  user: User;

  @ManyToOne(() => Client, (client) => client.recurringProfiles, {
    onDelete: 'SET NULL',
  })
  client: Client;

  @ManyToOne(() => ClientGroup, (clientGroup) => clientGroup.recurringProfiles, {
    onDelete: 'SET NULL',
  })
  clientGroup: ClientGroup;

  @CreateDateColumn()
  createdAt: string;

  @UpdateDateColumn()
  updatedAt: string;
}

export default RecurringProfile;
