import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>Empty, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from 'class-validator';

export class ForgotPasswordDto {
  // @IsNotEmpty()
  // @IsEmail()
  // email: string;
  @IsNotEmpty()
  mobileNumber: string;

  @IsNotEmpty()
  countryCode: string;
}

export class ClientForgotPasswordDto {
  @IsNotEmpty()
  @IsEmail()
  email: string;
}

export class ResetPasswordDto {
  @IsNotEmpty()
  @IsString()
  token: string;

  @IsNotEmpty()
  @MinLength(8)
  password: string;
}

export class ChangePasswordDto {
  @IsNotEmpty()
  @IsString()
  oldPassword: string;

  @IsNotEmpty()
  @IsString()
  newPassword: string;
}
