import {
  BadRequestException,
  ConflictException,
  Inject,
  Injectable,
  InternalServerErrorException,
  NotFoundException,
  UnprocessableEntityException,
  forwardRef,
} from '@nestjs/common';
import axios from 'axios';
import { User } from '../users/entities/user.entity';
import AuthToken, { AuthTokenType } from './auth-token.entity';
import FindOneDriveStorageDto from './find-onedrive-storage.dto';
import { IExisting, IUpload, StorageService } from '../storage/storage.service';
import Storage, { StorageType } from '../storage/storage.entity';
import { In, createQueryBuilder } from 'typeorm';
import Client from '../clients/entity/client.entity';
import { v4 as uuidv4 } from 'uuid';
import * as moment from 'moment';
import { Organization, StorageSystem } from '../organization/entities/organization.entity';
import Task from '../tasks/entity/task.entity';
import { AttachmentsService } from '../tasks/services/attachments.service';
import CollectData from '../collect-data/collect-data.entity';
import { Event_Actions } from 'src/event-listeners/actions';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { getName } from 'src/utils/FilterSpecialChars';
import ClientGroup from '../client-group/client-group.entity';
import DocumentsData from '../document-in-out/entity/documents-data.entity';
import { bind } from 'lodash';


@Injectable()
export class OneDriveStorageService {
  constructor(
    private eventEmitter: EventEmitter2,
    @Inject(forwardRef(() => StorageService))
    private storageService: StorageService,
    @Inject((forwardRef(() => AttachmentsService)))
    private attachmentsService: AttachmentsService,

  ) { }

  async saveToken(userId: number, body: any) {
    if (!body.code) {
      throw new BadRequestException('Code is required');
    };

    const user = await User.findOne({
      where: { id: userId },
      relations: ['organization'],
    });


    let existingToken = await AuthToken.findOne({
      where: { organizationId: user.organization.id },
    });

    if (existingToken) {
      await existingToken.remove();
    }

    const data = new URLSearchParams({
      client_id: process.env.ONE_DRIVE_CLIENT_ID,
      scope: process.env.ONE_DRIVE_SCOPE,
      redirect_uri: body?.location === "signup" ? `${process.env.WEBSITE_URL}/onedrive-auth-signup` : `${process.env.WEBSITE_URL}/onedrive-auth`,
      client_secret: process.env.ONE_DRIVE_CLIENT_SECRET,
      code: body.code,
      grant_type: process.env.ONE_DRIVE_GRANT_TYPE_AUTH,
    });

    try {
      let res = await axios({
        method: 'POST',
        url: process.env.ONE_DRIVE_AUTH_TOKEN_URL,
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
        data,
      });

      let token = new AuthToken();
      token.accessToken = res.data.access_token;
      token.refreshToken = res.data.refresh_token;
      token.type = AuthTokenType.MICROSFT;
      token.organizationId = user.organization.id;
      await token.save();
      user.organization.hasStorage = true;
      user.organization.onedriveAuthorize = false;
      await Organization.update(user.organization.id, { hasStorage: true, onedriveAuthorize: false });
      return token;
    } catch (err) {
      console.log('error', err);
      throw new InternalServerErrorException(err);
    }
  };

  async getItems(userId: number, query: FindOneDriveStorageDto) {
    let user = await User.findOne({
      where: { id: userId },
      relations: ['organization'],
    });

    let token = await AuthToken.findOne({
      where: {
        organizationId: user.organization.id,
        type: AuthTokenType.MICROSFT,
      },
    });

    const searchParams = new URLSearchParams({
      client_id: process.env.ONE_DRIVE_CLIENT_ID,
      scope: process.env.ONE_DRIVE_SCOPE,
      response_type: process.env.ONE_DRIVE_RESPONSE_TYPE,
      redirect_uri: query.location === 'signup' ? `${process.env.WEBSITE_URL}/onedrive-auth-signup` : `${process.env.WEBSITE_URL}/onedrive-auth`,
    });

    if (!token) {
      throw new UnprocessableEntityException({
        code: 'NO_TOKEN',
        message: 'User has not been authenticated with Microsoft',
        authorizationUrl: `${process.env.ONE_DRIVE_AUTH_URL}?${searchParams.toString()}`,
      });
    }

    try {
      let response = await this.getData(token, query);
      return response;
    } catch (err) {
      let error = err.response.data?.error;
      if (error.code === 'InvalidAuthenticationToken') {
        await this.refreshToken(token);
        let response = await this.getData(token, query);
        return response;
      }
      throw new InternalServerErrorException(error);
    }
  };

  async uploadFile(args: IUpload) {
    const { file, body, userId } = args;
    const { buffer, mimetype } = file;
    const user = await User.findOne({
      where: { id: userId },
      relations: ['organization'],
    });


    const token = await AuthToken.findOne({
      where: {
        organizationId: user.organization.id,
        type: AuthTokenType.MICROSFT,
      },
    });
    let documentsData: DocumentsData
    if (body?.docId) {
      documentsData = await DocumentsData.findOne(body.docId);
    }
    if (!token) {
      throw new UnprocessableEntityException('No authentication token found. Please authenticate with OneDrive.');
    }

    let storage = new Storage();
    storage.fileType = file.mimetype;
    storage.fileSize = file.size;
    storage.name = file.originalname;
    storage.type = StorageType.FILE;
    storage.uid = uuidv4();
    storage.storageSystem = StorageSystem.MICROSOFT;
    storage.user = user;

    let existingFile = await this.existing({
      name: file.originalname,
      parent: body.folderId,
      type: body.type,
      clientId: body.clientId,
      clientGroupId: body.clientGroup,
      orgId: user.organization.id,
      roomId: body.roomId,
    });
    if (existingFile) {
      throw new ConflictException('File with this name already exists');
    }



    let key: string;
    let upload: any;
    let atomFolder: Storage;
    let clientFolder: Storage;
    let dispalayNameFolder: Storage;


    try {

      if (body.type === 'client') {
        let client = await Client.findOne({ where: { id: body.clientId }, relations: ['organization'] });
        if (body.folderId) {
          const folder = await Storage.findOne({ where: { uid: body.folderId } });
          key = `${folder.fileId}:/${file.originalname}:`;
        } else {

          dispalayNameFolder = await Storage.findOne({
            where: {
              name: client.displayName,
              organization: client.organization.id,
              show: false,
              type: StorageType.FOLDER,
              client: client
            }
          });

          if (!dispalayNameFolder) {
            atomFolder = await Storage.findOne({
              where: {
                name: "Atom",
                organization: client.organization.id,
                show: false
              }
            });
            if (!atomFolder) {
              const folderData = await this.createOneDriveFolder(userId, "Atom");
              atomFolder = new Storage();
              atomFolder.name = 'Atom';
              atomFolder.organization = user.organization;
              atomFolder.type = StorageType.FOLDER;
              atomFolder.uid = uuidv4();
              atomFolder.fileId = folderData.id;
              atomFolder.show = false;
              atomFolder.storageSystem = StorageSystem.MICROSOFT;
              atomFolder.authId = user.organization.id;
              await atomFolder.save();
            };
            clientFolder = await Storage.findOne({
              where: {
                name: "Clients",
                organization: user.organization.id,
                show: false
              }
            });
            if (!clientFolder) {
              const folderData = await this.createOneDriveFolder(userId, "Clients", atomFolder.fileId);
              clientFolder = new Storage();
              clientFolder.name = "Clients";
              clientFolder.organization = user.organization;
              clientFolder.type = StorageType.FOLDER;
              clientFolder.uid = uuidv4();
              clientFolder.fileId = folderData.id;
              clientFolder.show = false;
              clientFolder.storageSystem = StorageSystem.MICROSOFT;
              clientFolder.authId = user.organization.id;
              clientFolder.parent = atomFolder;
              await clientFolder.save();
            };
            dispalayNameFolder = await Storage.findOne({
              where: {
                name: client.displayName,
                organization: user.organization.id,
                show: false,
                type: StorageType.FOLDER,
                client: client
              }
            });
            if (!dispalayNameFolder) {
              const name = getName(client.displayName);
              const folderData = await this.createOneDriveFolder(userId, name, clientFolder?.fileId);
              dispalayNameFolder = new Storage();
              dispalayNameFolder.name = client.displayName;
              dispalayNameFolder.organization = user.organization;
              dispalayNameFolder.type = StorageType.FOLDER;
              dispalayNameFolder.uid = uuidv4();
              dispalayNameFolder.fileId = folderData.id;
              dispalayNameFolder.show = false;
              dispalayNameFolder.storageSystem = StorageSystem.MICROSOFT;
              dispalayNameFolder.authId = user.organization.id;
              dispalayNameFolder.parent = clientFolder;
              dispalayNameFolder.client = client;
              await dispalayNameFolder.save();
            };

          };
          key = `${dispalayNameFolder.fileId}:/${file.originalname}:`;
        };
        upload = await this.upload(buffer, key, mimetype, token, file, userId);
        storage.client = client;
      };

      if (body.type === 'clientGroup') {
        let clientGroup = await ClientGroup.findOne({ where: { id: body.clientGroup }, relations: ['organization'] });
        if (body.folderId) {
          const folder = await Storage.findOne({ where: { uid: body.folderId } });
          key = `${folder.fileId}:/${file.originalname}:`;
        } else {

          dispalayNameFolder = await Storage.findOne({
            where: {
              name: clientGroup.displayName,
              organization: clientGroup.organization.id,
              show: false,
              type: StorageType.FOLDER,
              clientGroup: clientGroup
            }
          });

          if (!dispalayNameFolder) {
            atomFolder = await Storage.findOne({
              where: {
                name: "Atom",
                organization: clientGroup.organization.id,
                show: false
              }
            });
            if (!atomFolder) {
              const folderData = await this.createOneDriveFolder(userId, "Atom");
              atomFolder = new Storage();
              atomFolder.name = 'Atom';
              atomFolder.organization = user.organization;
              atomFolder.type = StorageType.FOLDER;
              atomFolder.uid = uuidv4();
              atomFolder.fileId = folderData.id;
              atomFolder.show = false;
              atomFolder.storageSystem = StorageSystem.MICROSOFT;
              atomFolder.authId = user.organization.id;
              await atomFolder.save();
            };
            clientFolder = await Storage.findOne({
              where: {
                name: "Clients",
                organization: user.organization.id,
                show: false
              }
            });
            if (!clientFolder) {
              const folderData = await this.createOneDriveFolder(userId, "Clients", atomFolder.fileId);
              clientFolder = new Storage();
              clientFolder.name = "Clients";
              clientFolder.organization = user.organization;
              clientFolder.type = StorageType.FOLDER;
              clientFolder.uid = uuidv4();
              clientFolder.fileId = folderData.id;
              clientFolder.show = false;
              clientFolder.storageSystem = StorageSystem.MICROSOFT;
              clientFolder.authId = user.organization.id;
              clientFolder.parent = atomFolder;
              await clientFolder.save();
            };
            dispalayNameFolder = await Storage.findOne({
              where: {
                name: clientGroup.displayName,
                organization: user.organization.id,
                show: false
              }
            });
            if (!dispalayNameFolder) {
              const name = getName(clientGroup.displayName);
              const folderData = await this.createOneDriveFolder(userId, name, clientFolder?.fileId);
              dispalayNameFolder = new Storage();
              dispalayNameFolder.name = clientGroup.displayName;
              dispalayNameFolder.organization = user.organization;
              dispalayNameFolder.type = StorageType.FOLDER;
              dispalayNameFolder.uid = uuidv4();
              dispalayNameFolder.fileId = folderData.id;
              dispalayNameFolder.show = false;
              dispalayNameFolder.storageSystem = StorageSystem.MICROSOFT;
              dispalayNameFolder.authId = user.organization.id;
              dispalayNameFolder.parent = clientFolder;
              dispalayNameFolder.clientGroup = clientGroup;
              await dispalayNameFolder.save();
            };

          };
          // key = `${dispalayNameFolder.fileId}:/${file.originalname.replace(/[<>:"\/\\|?*.]/g, '')}:`;
          key = `${dispalayNameFolder.fileId}:/${file.originalname.replace(/[<>:"\/\\|?*]/g, '')
            .replace(/\.(?=.*\.)/g, '')}:`;
        };
        upload = await this.upload(buffer, key, mimetype, token, file, userId);
        storage.clientGroup = clientGroup;
      };

      if (body.type === 'organization') {
        let orgFolder: Storage;
        if (body?.folderId) {
          const parentFolder = await Storage.findOne({ where: { uid: body.folderId } });
          // const oneDriveFolder = await this.oneDriveService.createOneDriveFolder(userId, body.name, parentFolder.fileId);
          // storage.fileId = oneDriveFolder.id;
          // storage.storageSystem = StorageSystem.MICROSOFT;
          key = `${parentFolder.fileId}:/${file.originalname}:`;
        } else {
          atomFolder = await Storage.findOne({
            where: {
              name: "Atom",
              organization: user.organization.id,
              show: false
            }
          });
          if (!atomFolder) {
            const folderData = await this.createOneDriveFolder(userId, "Atom");
            atomFolder = new Storage();
            atomFolder.name = 'Atom';
            atomFolder.organization = user.organization;
            atomFolder.type = StorageType.FOLDER;
            atomFolder.uid = uuidv4();
            atomFolder.fileId = folderData.id;
            atomFolder.show = false;
            atomFolder.storageSystem = StorageSystem.MICROSOFT;
            atomFolder.authId = user.organization.id;
            await atomFolder.save();
          };
          orgFolder = await Storage.findOne({
            where: {
              name: "Organization Storage",
              organization: user.organization.id,
              show: false
            }
          });
          if (!orgFolder) {
            const folder = await this.createOneDriveFolder(userId, "Organization Storage", atomFolder.fileId);
            orgFolder = new Storage();
            orgFolder.name = "Organization Storage";
            orgFolder.organization = user.organization;
            orgFolder.type = StorageType.FOLDER;
            orgFolder.uid = uuidv4();
            orgFolder.fileId = folder.id;
            orgFolder.show = false;
            orgFolder.storageSystem = StorageSystem.MICROSOFT;
            orgFolder.authId = user.organization.id;
            orgFolder.parent = atomFolder;
            await orgFolder.save();
          };
          // key = `${orgFolder.fileId}:/${file.originalname.replace(/[<>:"\/\\|?*.]/g, '')}:`;
          key = `${orgFolder.fileId}:/${file.originalname.replace(/[<>:"\/\\|?*]/g, '')
            .replace(/\.(?=.*\.)/g, '')}:`;

        }

        // key = `root:/Atom/Organization Storage/${file.originalname}:`;
        upload = await this.upload(buffer, key, mimetype, token, file, userId);
        storage.organization = user.organization;
      };

    } catch (err) {
      let error = err?.response?.data?.error;
      console.log(err);
      if (error?.code === 'InvalidAuthenticationToken') {
        await this.refreshToken(token);
        upload = await this.upload(buffer, key, mimetype, token, file, userId);
      } else if (error.code === 'quotaLimitReached') {
        throw new ConflictException({
          code: 'NO_STORAGE',
          message: error.message,
        });
      }
    };
    storage.file = upload.file;
    storage.webUrl = upload.webUrl;
    storage.downloadUrl = upload["@microsoft.graph.downloadUrl"];
    storage.fileId = upload.id;
    storage.authId = user.organization.id;
    if (body?.docId) {
      storage.documentsData = documentsData;
    }
    if (body.folderId) {
      let folder = await Storage.findOne({ where: { uid: body.folderId } });
      storage.parent = folder;
    };
    await storage.save();
    return storage;
  };

  async refreshToken(token: AuthToken) {
    try {
      const data = new URLSearchParams({
        client_id: process.env.ONE_DRIVE_CLIENT_ID,
        client_secret: process.env.ONE_DRIVE_CLIENT_SECRET,
        scope: process.env.ONE_DRIVE_SCOPE,
        redirect_uri: `${process.env.WEBSITE_URL}/onedrive-auth`,
        grant_type: process.env.ONE_DRIVE_GRANT_TYPE_REFRESH,
        refresh_token: token.refreshToken,
      });

      let res = await axios({
        method: 'POST',
        url: process.env.ONE_DRIVE_AUTH_TOKEN_URL,
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
        data,
      });

      token.accessToken = res.data.access_token;
      token.refreshToken = res.data.refresh_token;
      await token.save();
    } catch (err) {
      let error = err?.response?.data?.error;
      console.log('refresh, error', error);
      throw new InternalServerErrorException(error);
    }
  };

  async reAuthorize() {
    const searchParams = new URLSearchParams({
      client_id: process.env.ONE_DRIVE_CLIENT_ID,
      scope: process.env.ONE_DRIVE_SCOPE,
      response_type: process.env.ONE_DRIVE_RESPONSE_TYPE,
      redirect_uri: `${process.env.WEBSITE_URL}/onedrive-auth`,
    });

    return `${process.env.ONE_DRIVE_AUTH_URL}?${searchParams.toString()}`;
  };

  async getData(token: AuthToken, query: FindOneDriveStorageDto) {
    let res = await axios({
      method: 'GET',
      url: `${process.env.ONE_DRIVE_URL}/${query.id}/children`,
      headers: {
        'Authorization': `Bearer ${token.accessToken}`,
        'Content-Type': 'application/json',
      },
      params: { expand: 'thumbnails' },
    });

    return res.data;
  };



  async getMetadataByFileId(userId: number, fileId: string) {
    const user = await User.findOne({
      where: { id: userId },
      relations: ['organization'],
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    const token = await AuthToken.findOne({
      where: {
        organizationId: user.organization.id,
        type: AuthTokenType.MICROSFT,
      },
    });

    if (!token) {
      throw new UnprocessableEntityException('No authentication token found. Please authenticate with OneDrive.');
    }

    try {
      const response = await axios.get(`https://graph.microsoft.com/v1.0/me/drive/items/${fileId}`, {
        headers: {
          Authorization: `Bearer ${token.accessToken}`,
        },
      });

      return response.data;
    } catch (err) {
      const error = err.response?.data?.error;
      // If token is invalid, try refreshing once
      if (error?.code === 'InvalidAuthenticationToken') {
        await this.refreshToken(token);
        const newResponse = await axios.get(`https://graph.microsoft.com/v1.0/me/drive/items/${fileId}`, {
          headers: {
            Authorization: `Bearer ${token.accessToken}`,
          },
        });
        return newResponse.data;
      }

      throw new InternalServerErrorException(error || err.message);
    }
  }


  async attachementsUpload(args: IUpload) {
    const { file, body, userId, } = args;
    const { stageid } = body;
    const { buffer, mimetype } = file;
    const user = await User.findOne({
      where: { id: userId },
      relations: ['organization'],
    });

    let token = await AuthToken.findOne({
      where: {
        organizationId: user.organization.id,
        type: AuthTokenType.MICROSFT,
      },
    });
    const searchParams = new URLSearchParams({
      client_id: process.env.ONE_DRIVE_CLIENT_ID,
      scope: process.env.ONE_DRIVE_SCOPE,
      response_type: process.env.ONE_DRIVE_RESPONSE_TYPE,
      redirect_uri: `${process.env.WEBSITE_URL}/onedrive-auth`,
    });

    if (!token) {
      throw new UnprocessableEntityException({
        code: 'NO_TOKEN',
        message: 'User has not been authenticated with Microsoft',
        authorizationUrl: `${process.env.ONE_DRIVE_AUTH_URL}?${searchParams.toString()}`,
      });
      // throw new UnprocessableEntityException('No authentication token found. Please authenticate OneDrive.');
    }

    // try {
    // let user = await User.findOne({
    //   where: { id: userId },
    //   relations: ['organization'],
    // });

    let existingFile = await this.existing({
      name: file.originalname,
      parent: body.folderId,
      type: body.type,
      clientId: body.clientId,
      clientGroupId: null,
      orgId: user.organization.id,
      roomId: body.roomId,
    });

    if (body.type !== 'chat' && existingFile) {
      throw new ConflictException('File with this name already exists');
    }
    const { storageLimit, freeSpace } = await this.storageService.getOrgStorage(userId);
    if (!(freeSpace - +file.size > 0)) {
      throw new ConflictException(
        'We regret to inform you that the storage limit for your organization has been exceeded. As a result, no further data can be stored until the storage usage is reduced or additional storage capacity is allocated.',
      );
    }

    let key: string;
    let upload: any;
    let kybStorage: Storage;

    try {
      if (body.type === 'client') {
        let finalFolder: Storage;
        let client = await Client.findOne({ where: { id: body.clientId }, relations: ['organization'] });
        let dispalayNameFolder: Storage;
        let atomFolder: Storage;
        let clientFolder: Storage;

        dispalayNameFolder = await Storage.findOne({
          where: {
            name: client.displayName,
            organization: client.organization.id,
            show: false
          }
        });
        if (!dispalayNameFolder) {
          atomFolder = await Storage.findOne({
            where: {
              name: "Atom",
              organization: client.organization.id,
              show: false
            }
          });
          if (!atomFolder) {
            const folderData = await this.createOneDriveFolder(userId, "Atom");
            atomFolder = new Storage();
            atomFolder.name = 'Atom';
            atomFolder.organization = user.organization;
            atomFolder.type = StorageType.FOLDER;
            atomFolder.uid = uuidv4();
            atomFolder.fileId = folderData.id;
            atomFolder.show = false;
            atomFolder.storageSystem = StorageSystem.MICROSOFT;
            atomFolder.authId = user.organization.id;
            await atomFolder.save();
          };
          clientFolder = await Storage.findOne({
            where: {
              name: "Clients",
              organization: user.organization.id,
              show: false
            }
          });
          if (!clientFolder) {
            const folderData = await this.createOneDriveFolder(userId, "Clients", atomFolder.fileId);
            clientFolder = new Storage();
            clientFolder.name = "Clients";
            clientFolder.organization = user.organization;
            clientFolder.type = StorageType.FOLDER;
            clientFolder.uid = uuidv4();
            clientFolder.fileId = folderData.id;
            clientFolder.show = false;
            clientFolder.storageSystem = StorageSystem.MICROSOFT;
            clientFolder.authId = user.organization.id;
            clientFolder.parent = atomFolder;
            await clientFolder.save();
          };
          dispalayNameFolder = await Storage.findOne({
            where: {
              name: client.displayName,
              organization: user.organization.id,
              show: false
            }
          });
          if (!dispalayNameFolder) {
            const folderData = await this.createOneDriveFolder(userId, client.displayName, clientFolder?.fileId);
            dispalayNameFolder = new Storage();
            dispalayNameFolder.name = client.displayName;
            dispalayNameFolder.organization = user.organization;
            dispalayNameFolder.type = StorageType.FOLDER;
            dispalayNameFolder.uid = uuidv4();
            dispalayNameFolder.fileId = folderData.id;
            dispalayNameFolder.show = false;
            dispalayNameFolder.storageSystem = StorageSystem.MICROSOFT;
            dispalayNameFolder.authId = user.organization.id;
            dispalayNameFolder.parent = clientFolder;
            await dispalayNameFolder.save();
          };
        };
        finalFolder = dispalayNameFolder;
        if (body.kyb == "true") {
          const clientId = body.clientId;
          const storageType = user.organization.storageSystem;
          kybStorage = await this.storageService.existingKybStorage(userId, clientId, undefined, storageType);
          finalFolder = kybStorage;
        };

        key = `${finalFolder.fileId}:/${file.originalname.replace(/[<>:"\/\\|?*]/g, '')
          .replace(/\.(?=.*\.)/g, '')}:`;
        upload = await this.upload(buffer, key, mimetype, token, file, userId);
      }

      if (body.type === 'clientGroup') {
        let finalFolder: Storage;
        let clientGroup = await ClientGroup.findOne({ where: { id: body.clientId }, relations: ['organization'] });
        let dispalayNameFolder: Storage;
        let atomFolder: Storage;
        let clientFolder: Storage;
        dispalayNameFolder = await Storage.findOne({
          where: {
            name: clientGroup.displayName,
            organization: clientGroup.organization.id,
            show: false
          }
        });

        if (!dispalayNameFolder) {
          atomFolder = await Storage.findOne({
            where: {
              name: "Atom",
              organization: clientGroup.organization.id,
              show: false
            }
          });
          if (!atomFolder) {
            const folderData = await this.createOneDriveFolder(userId, "Atom");
            atomFolder = new Storage();
            atomFolder.name = 'Atom';
            atomFolder.organization = user.organization;
            atomFolder.type = StorageType.FOLDER;
            atomFolder.uid = uuidv4();
            atomFolder.fileId = folderData.id;
            atomFolder.show = false;
            atomFolder.storageSystem = StorageSystem.MICROSOFT;
            atomFolder.authId = user.organization.id;
            await atomFolder.save();
          };
          clientFolder = await Storage.findOne({
            where: {
              name: "Clients",
              organization: user.organization.id,
              show: false
            }
          });
          if (!clientFolder) {
            const folderData = await this.createOneDriveFolder(userId, "Clients", atomFolder.fileId);
            clientFolder = new Storage();
            clientFolder.name = "Clients";
            clientFolder.organization = user.organization;
            clientFolder.type = StorageType.FOLDER;
            clientFolder.uid = uuidv4();
            clientFolder.fileId = folderData.id;
            clientFolder.show = false;
            clientFolder.storageSystem = StorageSystem.MICROSOFT;
            clientFolder.authId = user.organization.id;
            clientFolder.parent = atomFolder;
            await clientFolder.save();
          };
          dispalayNameFolder = await Storage.findOne({
            where: {
              name: clientGroup.displayName,
              organization: user.organization.id,
              show: false
            }
          });
          if (!dispalayNameFolder) {
            const folderData = await this.createOneDriveFolder(userId, clientGroup.displayName, clientFolder?.fileId);
            dispalayNameFolder = new Storage();
            dispalayNameFolder.name = clientGroup.displayName;
            dispalayNameFolder.organization = user.organization;
            dispalayNameFolder.type = StorageType.FOLDER;
            dispalayNameFolder.uid = uuidv4();
            dispalayNameFolder.fileId = folderData.id;
            dispalayNameFolder.show = false;
            dispalayNameFolder.storageSystem = StorageSystem.MICROSOFT;
            dispalayNameFolder.authId = user.organization.id;
            dispalayNameFolder.parent = clientFolder;
            await dispalayNameFolder.save();
          };
        };
        finalFolder = dispalayNameFolder;
        if (body?.kyb === 'true') {
          const clientGroupId = body.clientId;
          const storageType = user.organization.storageSystem;
          kybStorage = await this.storageService.existingKybStorage(userId, undefined, clientGroupId, storageType);
          finalFolder = kybStorage;
        }
        key = `${finalFolder.fileId}:/${file.originalname.replace(/[<>:"\/\\|?*]/g, '')
          .replace(/\.(?=.*\.)/g, '')}:`;
        upload = await this.upload(buffer, key, mimetype, token, file, userId);
      }

      if (body.type === 'organization') {
        let atomFolder: Storage;
        let orgFolder: Storage;

        atomFolder = await Storage.findOne({
          where: {
            name: "Atom",
            organization: user.organization.id,
            show: false
          }
        });
        if (!atomFolder) {
          const folderData = await this.createOneDriveFolder(userId, "Atom");
          atomFolder = new Storage();
          atomFolder.name = 'Atom';
          atomFolder.organization = user.organization;
          atomFolder.type = StorageType.FOLDER;
          atomFolder.uid = uuidv4();
          atomFolder.fileId = folderData.id;
          atomFolder.show = false;
          atomFolder.storageSystem = StorageSystem.MICROSOFT;
          atomFolder.authId = user.organization.id;
          await atomFolder.save();
        };
        orgFolder = await Storage.findOne({
          where: {
            name: "Organization Storage",
            organization: user.organization.id,
            show: false
          }
        });
        if (!orgFolder) {
          const folder = await this.createOneDriveFolder(userId, "Organization Storage", atomFolder.fileId);
          orgFolder = new Storage();
          orgFolder.name = "Organization Storage";
          orgFolder.organization = user.organization;
          orgFolder.type = StorageType.FOLDER;
          orgFolder.uid = uuidv4();
          orgFolder.fileId = folder.id;
          orgFolder.show = false;
          orgFolder.storageSystem = StorageSystem.MICROSOFT;
          orgFolder.authId = user.organization.id;
          orgFolder.parent = atomFolder;
          await orgFolder.save();
        };


        key = `${orgFolder.fileId}:/${file.originalname.replace(/[<>:"\/\\|?*]/g, '')
          .replace(/\.(?=.*\.)/g, '')}:`;
        upload = await this.upload(buffer, key, mimetype, token, file, userId);
      }

      if (body.type === 'chat') {
        key = `root:/Atom/Organization Storage/chat-${body.roomId}-${moment().valueOf()}/${file.originalname.replace(/[<>:"\/\\|?*]/g, '')
          .replace(/\.(?=.*\.)/g, '')}:`;
        upload = await this.upload(buffer, key, mimetype, token, file, userId);
      };

    } catch (err) {
      let error = err?.response?.data?.error;
      if (error?.code === 'InvalidAuthenticationToken') {
        await this.refreshToken(token);
        upload = await this.upload(buffer, key, mimetype, token, file, userId);
      } else if (error?.code === 'quotaLimitReached') {
        throw new ConflictException({
          code: 'NO_STORAGE',
          message: error.message,
        });
      }
    }

    return {
      key,
      upload: upload.file,
      webUrl: upload.webUrl,
      downloadUrl: upload["@microsoft.graph.downloadUrl"],
      fileId: upload.id,
      fileSize: file.size,
      fileType: file.mimetype,
      clientId: body.clientId,
      name: file.originalname,
      authId: user.organization.id,
      storageSystem: StorageSystem.MICROSOFT,
      file: upload.file,
      stageid,
      ...(kybStorage && { parentId: kybStorage.id })

    };
  };

  async upload(buffer: Buffer, key: string, contentType = '', tokens, file, userId: number) {
    const user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
    const token = await AuthToken.findOne({ where: { organizationId: user.organization.id } });
    const uploadUrl = `${process.env.ONE_DRIVE_URL}/${key}/content`;
    const response = await axios({
      method: 'PUT',
      url: uploadUrl,
      headers: {
        'Authorization': `Bearer ${token.accessToken}`,
        'Content-Type': 'application/octet-stream',
      },
      maxContentLength: Infinity,
      maxBodyLength: Infinity,
      data: file.buffer,
    });
    const fileDetails = await this.getFileDetailsById(response?.data?.id, userId);
    return { ...response.data, file: fileDetails?.thumbnails[0]?.large?.url };
  };

  async copyFileInOneDrive(fileId: string, userId: number) {
    const user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
    const token = await AuthToken.findOne({ where: { organizationId: user.organization.id } });
    const uploadUrl = `${process.env.ONE_DRIVE_URL}/${fileId}/copy`;



  }

  async createOneDriveFolder(userId: number, folderName: string, parentFolderId?: string) {
    const user = await User.findOne({
      where: { id: userId },
      relations: ['organization'],
    });

    if (!user) {
      throw new BadRequestException('User not found');
    }

    const token = await AuthToken.findOne({
      where: {
        organizationId: user.organization.id,
        type: AuthTokenType.MICROSFT,
      },
    });

    if (!token) {
      throw new UnprocessableEntityException('No authentication token found. Please authenticate with OneDrive.');
    };

    try {
      const parentFolderPath = parentFolderId ? `${parentFolderId}` : 'root';
      const url = `${process.env.ONE_DRIVE_URL}/${parentFolderPath}/children`;
      const response = await axios({
        method: 'POST',
        url,
        headers: {
          Authorization: `Bearer ${token.accessToken}`,
          'Content-Type': 'application/json',
        },
        data: {
          name: folderName.replace(/[<>:"\/\\|?*.]/g, ' '),
          folder: {},
          '@microsoft.graph.conflictBehavior': 'replace',
        },
      });
      return response.data;
    } catch (err) {
      const error = err?.response?.data?.error;
      if (error?.code === 'InvalidAuthenticationToken') {
        await this.refreshToken(token);
        return this.createOneDriveFolder(userId, folderName, parentFolderId); // Retry with the refreshed token
      } else {
        throw new InternalServerErrorException(error?.message || 'Failed to create folder');
      }
    }
  };

  async existing(props: IExisting) {
    const { name, parent, type, orgId, clientId, clientGroupId, roomId } = props;

    let existing = createQueryBuilder(Storage, 'storage');

    let where = `storage.name = :name`;

    if (type === 'client') {
      existing.leftJoin('storage.client', 'client');
      where += ` and client.id = :clientId`;
    }

    if (type === 'clientGroup') {
      existing.leftJoin('storage.clientGroup', 'clientGroup');
      where += ` and clientGroup.id = :clientId`;
    }

    if (type === 'organization') {
      existing.leftJoin('storage.organization', 'organization');
      where += ` and organization.id = :orgId`;
    }
    if (type === 'chat') {
      existing.leftJoin('storage.room', 'room');
      where += ` and room.id = :roomId`;
    }

    if (parent) {
      existing.leftJoin('storage.parent', 'parent');
      where += ` and parent.uid = :parent`;
    }

    if (!parent) {
      where += ` and storage.parent is null`;
    }

    existing.where(`(${where})`, { name, parent, clientId, orgId, roomId });

    let result = await existing.getOne();
    return Boolean(result);
  };




  // Method to get file details by file ID
  async getFileDetailsById(fileId: string, userId: any) {
    const user = await User.findOne({ where: { id: userId }, relations: ['organization'] })
    const token = await AuthToken.findOne({
      where: {
        organizationId: user.organization.id
      }
    });


    try {
      const url = `${process.env.ONE_DRIVE_URL}/${fileId}?expand=thumbnails`;
      const headers = {
        'Authorization': `Bearer ${token.accessToken}`,
        'Content-Type': 'application/json'
      };

      const response = await axios.get(url, { headers });

      return response.data;
    } catch (err) {
      const error = err?.response?.data?.error;
      if (error?.code === 'InvalidAuthenticationToken') {
        await this.refreshToken(token);
        await this.getFileDetailsById(fileId, userId);
      } else if (error?.code === 'itemNotFound') {
        return { success: true };
      } else {
        throw new InternalServerErrorException(error?.message);
      }

    }
  };

  async addAttachments(taskId: number, files: Express.Multer.File[], userId: number, docId: number) {
    try {
      let task = await Task.findOne({
        where: { id: taskId },
        relations: ['client', 'category', 'subCategory', 'user', 'organization', 'clientGroup'],
      });

      let user = await User.findOne({
        where: { id: userId },
        relations: ['organization'],
      });

      let token = await AuthToken.findOne({
        where: {
          organizationId: user.organization.id,
          type: AuthTokenType.MICROSFT,
        },
      });

      let documentsData: DocumentsData;
      if (docId) {
        documentsData = await DocumentsData.findOne(docId);
      }

      let taskStorage = await this.attachmentsService.existingClientTaskStorage(task, StorageSystem.MICROSOFT);
      let taskAttachments: Storage[] = [];
      let errors: string[] = [];

      for (let file of files) {
        const { buffer, mimetype, originalname, size } = file;
        const existingAttachement = await Storage.find({ where: { parent: taskStorage, name: originalname } })
        if (existingAttachement?.length) {
          errors.push(`File name ${originalname} already exists`);
          continue;
        }
        // let key = `${taskStorage.fileId}:/${file.originalname.replace(/[<>:"\/\\|?*.]/g, '')}:`;
        let key = `${taskStorage.fileId}:/${file.originalname.replace(/[<>:"\/\\|?*]/g, '')
          .replace(/\.(?=.*\.)/g, '')}:`;
        let upload: any;
        try {
          upload = await this.upload(buffer, key, mimetype, token, file, userId);
        } catch (err) {
          let error = err?.response?.data?.error;
          if (error.code === 'InvalidAuthenticationToken') {
            await this.refreshToken(token);
            upload = await this.upload(buffer, key, mimetype, token, file, userId);
          } else if (error.code === 'quotaLimitReached') {
            throw new ConflictException({
              code: 'NO_STORAGE',
              message: error.message,
            });
          };
        };
        let storage = new Storage();
        storage.name = originalname;
        storage.file = upload.file;
        storage.webUrl = upload.webUrl;
        storage.downloadUrl = upload["@microsoft.graph.downloadUrl"];
        storage.fileId = upload.id;
        storage.authId = user.organization.id;
        storage.task = task;
        storage.fileSize = size;
        storage.fileType = mimetype;
        storage.client = task.client;
        storage.clientGroup = task?.clientGroup;
        storage.type = StorageType.FILE;
        storage.parent = taskStorage;
        storage.storageSystem = StorageSystem.MICROSOFT;
        storage.user = user;
        if (docId) {
          storage.documentsData = documentsData;
        }
        taskAttachments.push(storage);
      };

      await Storage.save(taskAttachments);

      if (errors?.length) {
        return { errors }
      } else {
        return {
          success: true,
        };
      }


    } catch (err) {
      console.log(err);
      throw new InternalServerErrorException(err);
    }
  };

  async collectDataAddAttc(
    origin: string,
    collectId: any,
    taskId: number,
    files: Express.Multer.File[],
  ) {
    try {
      let task = await Task.findOne({
        where: { id: taskId },
        relations: ['client', 'category', 'subCategory', 'organization', 'user', 'clientGroup'],
      });

      let token = await AuthToken.findOne({
        where: {
          organizationId: task.organization.id
        }
      });

      let taskStorage = await this.attachmentsService.existingClientTaskStorage(task, StorageSystem.MICROSOFT);

      let taskAttachments: Storage[] = [];
      const collectData = await CollectData.findOne({ where: { uid: collectId } })

      for (let file of files) {
        const { buffer, mimetype, originalname } = file;
        let key = `${taskStorage.fileId}:/${file.originalname.replace(/[<>:"\/\\|?*]/g, '')
          .replace(/\.(?=.*\.)/g, '')}:`;
        let upload: any;

        try {
          upload = await this.upload(buffer, key, mimetype, token, file, task.user.id);

        } catch (err) {
          let error = err?.response?.data?.error;
          if (error.code === 'InvalidAuthenticationToken') {
            await this.refreshToken(token);
            upload = await this.upload(buffer, key, mimetype, token, file, task?.user?.id);
          } else if (error.code === 'quotaLimitReached') {
            throw new ConflictException({
              code: 'NO_STORAGE',
              message: error.message,
            });
          }
        }

        let storage = new Storage();
        storage.name = originalname;
        storage.file = upload.file;
        storage.webUrl = upload.webUrl;
        storage.authId = task.organization.id;
        storage.downloadUrl = upload["@microsoft.graph.downloadUrl"];
        storage.fileId = upload.id;
        storage.task = task;
        storage.fileType = mimetype;
        storage.fileSize = file.size;
        storage.client = task.client;
        storage.clientGroup = task.clientGroup;
        storage.type = StorageType.FILE;
        storage.parent = taskStorage;
        storage.storageSystem = StorageSystem.MICROSOFT;
        storage.collectId = collectData?.id;
        storage.origin = origin;
        taskAttachments.push(storage);
      };

      await Storage.save(taskAttachments);
      this.eventEmitter.emit(Event_Actions.COLLECT_DATA_FILES_UPLOAD, {
        origin,
        collectId,
        taskId,
        files: files.map(file => file.originalname)
      });

      return {
        success: true,
      };
    } catch (err) {
      console.log(err);
      throw new InternalServerErrorException(err);
    }
  };

  async deleteOneDriveFile(userId: number, fileId: string, orgId?: number) {
    if (!fileId) return null;
    const user = await User.findOne({
      where: { id: userId },
      relations: ['organization'],
    });


    if (!user && !orgId) {
      throw new BadRequestException('User not found');
    }

    const token = await AuthToken.findOne({
      where: {
        organizationId: user?.organization?.id || orgId,
        type: AuthTokenType.MICROSFT,
      },
    });

    if (!token) {
      throw new UnprocessableEntityException('No authentication token found. Please authenticate with OneDrive.');
    }

    try {
      const url = `${process.env.ONE_DRIVE_URL}/${fileId}`;
      await axios({
        method: 'DELETE',
        url,
        headers: {
          Authorization: `Bearer ${token.accessToken}`,
          'Content-Type': 'application/json',
        },
      });
      return { success: true };
    } catch (err) {
      const error = err?.response?.data?.error;
      if (error?.code === 'InvalidAuthenticationToken') {
        await this.refreshToken(token);
        await this.deleteOneDriveFile(userId, fileId);
      } else if (error?.code === 'itemNotFound') {
        return { success: true };
      } else {
        throw new InternalServerErrorException(error?.message || 'Failed to delete file');
      }
    }
  };


  async copyFile(fileId: string, userId: number, newFileName?: string, destinationFolderId?: string) {
    const url = `${process.env.ONE_DRIVE_URL}/${fileId}/copy`;
    const user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
    const token = await AuthToken.findOne({ where: { organizationId: user.organization.id } });

    const headers = {
      'Authorization': `Bearer ${token.accessToken}`,
      'Content-Type': 'application/json'
    };

    let parentFolderId = destinationFolderId;
    if (!destinationFolderId) {
      try {
        const fileDetails = await this.getFileDetailsById(fileId, userId);
        parentFolderId = fileDetails.parentReference.id; // Get the parent folder ID from original file
      } catch (error) {
        console.error('Error retrieving file details:', error.response ? error.response.data : error.message);
        return;
      }
    };

    const body = {
      parentReference: {
        id: parentFolderId
      },
      name: `${newFileName ?? ''}-${moment().valueOf()}` // Optional: if you want to rename the file
    };

    try {
      const response = await axios.post(url, body, { headers });
      const statusUrl = response.headers['location'];
      if (statusUrl) {
        let copyStatus = await this.checkCopyStatus(statusUrl, userId);
        return copyStatus;
      } else {
        console.error('No status URL returned');
      }
    } catch (error) {
      console.error('Error copying file:', error.response ? error.response.data : error.message);
    }
  };

  async checkCopyStatus(statusUrl: string, userId: number) {
    const user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
    const token = await AuthToken.findOne({ where: { organizationId: user.organization.id } });


    try {
      let status;
      while (true) {
        const response = await axios.get(statusUrl);
        status = response.data;

        if (status.status === 'completed') {
          // return status;
          const fileDetails = await this.getFileDetailsById(status?.resourceId, userId)
          return fileDetails; // This contains the new file details
        } else if (status.status === 'failed') {
          throw new Error('Copy operation failed');
        } else {
          // If the status is not completed or failed, wait for a while before checking again
          await new Promise(resolve => setTimeout(resolve, 2000)); // 2 seconds
        }
      }
    } catch (err) {
      const error = err?.response?.data?.error;
      if (error.code === 'unauthenticated') {
        await this.refreshToken(token);
        await this.checkCopyStatus(statusUrl, userId);
      }

      console.error('Error checking copy status:', err?.response);
    }
  };


  async updateItemName(userId: number, itemId: string, newName: string) {
    const user = await User.findOne({
      where: { id: userId },
      relations: ['organization'],
    });

    if (!user) {
      throw new BadRequestException('User not found');
    };

    const token = await AuthToken.findOne({
      where: {
        organizationId: user.organization.id,
        type: AuthTokenType.MICROSFT,
      },
    });

    if (!token) {
      throw new UnprocessableEntityException('No authentication token found. Please authenticate with OneDrive.');
    };

    try {
      const url = `${process.env.ONE_DRIVE_URL}/${itemId}`;
      const response = await axios({
        method: 'PATCH',
        url,
        headers: {
          Authorization: `Bearer ${token.accessToken}`,
          'Content-Type': 'application/json',
        },
        data: {
          name: newName.replace(/[\/|*?"<>]/g, ' '),
        },
      });
      return response?.data
    } catch (err) {
      const error = err?.response?.data?.error;
      if (error?.code === 'InvalidAuthenticationToken') {
        await this.refreshToken(token);
        return this.updateItemName(userId, itemId, newName); // Retry with the refreshed token
      } else {
        throw new InternalServerErrorException(error?.message || 'Failed to update item name');
      }

    }


  }

  async getOneDriveStorageInfo(userId: number) {
    const user = await User.findOne({
      where: { id: userId },
      relations: ['organization'],
    });

    if (!user) {
      throw new BadRequestException('User not found');
    }

    const token = await AuthToken.findOne({
      where: {
        organizationId: user.organization.id,
        type: AuthTokenType.MICROSFT,
      },
    });

    if (!token) {
      throw new UnprocessableEntityException(
        'No authentication token found. Please authenticate with OneDrive.',
      );
    }

    try {
      // Try to get the default drive storage info
      const driveUrl = `https://graph.microsoft.com/v1.0/me/drive`; // Use /me/drive for delegated tokens or /users/{id}/drive for application
      const response = await axios.get(driveUrl, {
        headers: {
          Authorization: `Bearer ${token.accessToken}`,
          'Content-Type': 'application/json',
        },
      });

      const quota = response.data?.quota;
      if (!quota) {
        throw new InternalServerErrorException('Quota information not found');
      }

      const totalStorageBytes = quota.total;
      const usedStorageBytes = quota.used;
      const remainingStorageBytes = quota.remaining;

      // Get storage usage from DB
      const orgStorage = await createQueryBuilder(Storage, 'storage')
        .select('SUM(storage.fileSize)', 'totalStorage')
        .leftJoin('storage.organization', 'organization')
        .where('organization.id = :orgId', { orgId: user.organization.id })
        .getRawOne();

      const clientStorage = await createQueryBuilder(Storage, 'storage')
        .select('SUM(storage.fileSize)', 'totalStorage')
        .leftJoin('storage.client', 'client')
        .leftJoin('storage.clientGroup', 'clientGroup')
        .leftJoin('client.organization', 'organization')
        .where('organization.id = :orgId', { orgId: user.organization.id })
        .getRawOne();

      const totalStorageUsed = (+orgStorage?.totalStorage || 0) + (+clientStorage?.totalStorage || 0);

      return {
        totalStorageGB: totalStorageBytes,
        usedStorageGB: usedStorageBytes, // Customize if needed, you can use the raw value here.
        remainingStorageGB: remainingStorageBytes, // Customize if needed, you can use the raw value here.
        usedInDriveGB: usedStorageBytes, // Customize if needed, you can use the raw value here.
        totalStorageUsed: totalStorageUsed, // Customize if needed, you can use the raw value here.
      };
    } catch (err) {
      const error = err?.response?.data?.error;
      if (error?.code === 'InvalidAuthenticationToken') {
        await this.refreshToken(token);
        return this.getOneDriveStorageInfo(userId);
      }

      if (error?.code === 'itemNotFound') {
        throw new InternalServerErrorException('Drive not found or inaccessible with the current token');
      }

      throw new InternalServerErrorException(
        error?.message || 'Failed to get OneDrive storage info',
      );
    }
  }







  // async copyFile(fileId: string, userId: number, destinationFolderId: string, newFileName?: string) {
  //   const url = `${process.env.ONE_DRIVE_URL}/${fileId}/copy`;
  //   const user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
  //   const token = await AuthToken.findOne({ where: { organizationId: user.organization.id } })

  //   const headers = {
  //     'Authorization': `Bearer ${token.accessToken}`,
  //     'Content-Type': 'application/json'
  //   };

  //   const body = {
  //     parentReference: {
  //       id: destinationFolderId
  //     },
  //     // name: newFileName // Optional: if you want to rename the file
  //   };

  //   try {
  //     const response = await axios.post(url, body, { headers });
  //     console.log('Copy operation initiated:', response.data);
  //     return response.data;
  //   } catch (error) {
  //     console.error('Error copying file:', error.response ? error.response.data : error.message);
  //   }
  // }



}

