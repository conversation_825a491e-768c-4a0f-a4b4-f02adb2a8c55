import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import LogHour from './log-hour.entity';
import { LogHoursController } from './loghours.controller';
import { LogHoursService } from './loghours.service';
import { LogHourSubscriber } from 'src/event-subscribers/logHourTimeSheet.subscriber';
import LogHourTitle from './entity/log-hour-title.entity';
import LogNotes from './entity/log-notes.entity';

@Module({
  imports: [TypeOrmModule.forFeature([LogHour, LogHourTitle, LogNotes])],
  controllers: [LogHoursController],
  providers: [LogHoursService, LogHourSubscriber],

})
export class LogHoursModule { }
