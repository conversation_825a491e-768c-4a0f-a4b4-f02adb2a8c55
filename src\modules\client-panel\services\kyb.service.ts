import { Injectable, InternalServerErrorException } from '@nestjs/common';
import { User } from 'src/modules/users/entities/user.entity';
import Client from 'src/modules/clients/entity/client.entity';
import Kyb from 'src/modules/kyb/kyb.entity';
import Storage from 'src/modules/storage/storage.entity';
import { StorageService } from './storage.service';

@Injectable()
export class KybService {
    constructor(private storageService: StorageService) { }
    async addKyb(userId: number, data: any) {
        try {
            let user = await User.findOne({ where: { id: userId } });
            let client = await Client.findOne({ where: { id: data.client } });
            let kyb = new Kyb();
            kyb.documentName = data.documentName;
            kyb.documentNumber = data.documentNumber;
            if (data?.storage) {
                const storage = await this.storageService.addAttachements(userId, data.storage);
                kyb.storage = storage;
            }

            kyb.client = client;
            kyb.user = user;
            await kyb.save();
            return kyb;
        } catch (e) {
            throw new InternalServerErrorException(e);
        }
    }

    async getKybs(userId: number, query: any) {
        const kybs = await Kyb.find({ where: { client: query.clientId }, relations: ['storage'] });
        return kybs;
    }

    async updateKyb(userId: number, id: number, data: any) {
        try {
            const user = await User.findOne({ where: { id: userId } });
            const client = await Client.findOne({ where: { id: data.client } });

            const kyb = await Kyb.findOne({ where: { id }, relations: ['storage'] });
            let storage: Storage;
            if (data?.storage) {
                if (kyb?.storage?.id) {
                    storage = await Storage.findOne({ where: { id: kyb.storage.id } });
                    storage.fileType = data.storage.fileType;
                    storage.fileSize = data.storage.fileSize;
                    storage.name = data.storage.name;
                    storage.file = data.storage.upload;
                    kyb.storage = storage;
                } else {
                    const storage = await this.storageService.addAttachements(userId, data.storage);
                    kyb.storage = storage;
                }
            }

            kyb.documentName = data.documentName;
            kyb.documentNumber = data.documentNumber;

            kyb.client = client;
            kyb.user = user;

            await kyb.save();
            return kyb;
        } catch (err) {
            console.log(err);
            throw new InternalServerErrorException(err);
        }
    }

    async delete(id: number) {
        let kyb = await Kyb.findOne(id);

        await kyb.remove();

        return { success: true }
    }
}