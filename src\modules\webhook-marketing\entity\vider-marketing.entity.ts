import {
    BaseEntity,
    Column,
    CreateDateColumn,
    Entity,
    ManyToOne,
    OneToMany,
    PrimaryGeneratedColumn,
    UpdateDateColumn,
} from 'typeorm';

@Entity()
class ViderMarketing extends BaseEntity {
    @PrimaryGeneratedColumn()
    id: number;

    @Column({ type: 'timestamp', nullable: true })
    createdAt: Date;

    @Column()
    name: string

    @Column()
    mobileNumber: string;

    @Column()
    requestedDemo: string;

    @Column()
    visitWebsite: string;

    @Column()
    contactUs: string;

    @Column()
    status: string

    @Column()
    errorTitle: string

}

export default ViderMarketing;