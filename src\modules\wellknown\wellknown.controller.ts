import { Controller, Get } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';

@Controller('.well-known')
export class WellknownController {
  public eventEmitter: EventEmitter2;

  public constructor() { }

  @Get('microsoft-identity-association.json')
  async getWellknown() {
    return {
      "associatedApplications": [
        {
          "applicationId": "085cd4bb-b2ec-4791-9f4f-5b46c5818f9b"
        }
      ]
    };
  }
}