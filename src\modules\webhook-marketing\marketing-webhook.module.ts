import { Module } from '@nestjs/common';

import { MarketingWebhookService } from './marketing-webhook.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ViderWebhookSubscriber } from 'src/event-subscribers/viderWebhook.subscriber';
import ViderMarketingWebhook from './entity/vider-marketing-webhook.entity';
import { MarketingWebhookController } from './marketing-webhook.controller';
import { ViderMarketingWebhookSubscriber } from 'src/event-subscribers/viderMarketingWebhook.subscriber';
import ViderMarketing from './entity/vider-marketing.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      ViderMarketingWebhook,ViderMarketing
    ])
  ],
  controllers: [MarketingWebhookController],
  providers: [MarketingWebhookService, ViderMarketingWebhookSubscriber],
})
export class MarketingWebhookModule { }