import {
  BadRequestException,
  ConflictException,
  Inject,
  Injectable,
  InternalServerErrorException,
  forwardRef,
} from '@nestjs/common';
import * as moment from 'moment';
import Client from 'src/modules/clients/entity/client.entity';
import { User } from 'src/modules/users/entities/user.entity';
import { Brackets, In, createQueryBuilder, getConnection, getManager } from 'typeorm';
import { v4 as uuidv4 } from 'uuid';
import Task from '../tasks/entity/task.entity';
import CreateFolderDto from './dto/create-folder.dto';
import CreateLinkDto, { LinkUserType } from './dto/create-link.dto';
import { FindStorageDto } from './dto/FindStorage.dto';
import MoveFileDto from './dto/move-file.dto';
import { RenameFileDto } from './dto/rename-file.dto';
import { TotalStorageDto } from './dto/total-storage.dto';
import { IUploadBody } from './storage.controller';
import Storage, { StorageType } from './storage.entity';
import { AwsService } from './upload.service';
import { Organization, StorageSystem } from '../organization/entities/organization.entity';
import CollectData from '../collect-data/collect-data.entity';
import { OneDriveStorageService } from '../ondrive-storage/onedrive-storage.service';
import { AttachmentsService } from '../tasks/services/attachments.service';
import Activity, { ActivityType } from '../activity/activity.entity';
import { Event_Actions } from 'src/event-listeners/actions';
import { BharathStorageService } from './bharath-storage.service';
import { getName } from 'src/utils/FilterSpecialChars';
import ClientGroup from '../client-group/client-group.entity';
import CreateB3Credentials from './dto/create-b3-credentials.dto';
import CloudCredentials from './cloud-credentials.entity';
import { BharathCloudService } from './bharath-upload.service';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { GoogleDriveStorageService } from '../ondrive-storage/googledrive-storage.service';
import DocumentsData from '../document-in-out/entity/documents-data.entity';
import ViderAi from '../vider-ai/viderAi.entity';
import { Cron, CronExpression } from '@nestjs/schedule';
import { sanitizeFileNameForAWS } from './validations';

export interface IExisting {
  name: string;
  parent: string;
  type: 'client' | 'organization' | 'chat' | 'clientGroup' | 'viderAi';
  orgId: number;
  clientId: number | null;
  clientGroupId: number | null;
  roomId: number;
}

export interface IUpload {
  file: Express.Multer.File;
  body: IUploadBody;
  userId: number;
}

@Injectable()
export class StorageService {
  isProcessing: boolean = false;
  constructor(
    private awsService: AwsService,
    private bharathService: BharathStorageService,
    private bharathCloudService: BharathCloudService,
    private eventEmitter: EventEmitter2,
    @Inject(forwardRef(() => GoogleDriveStorageService))
    private googleDriveService: GoogleDriveStorageService,
    @Inject(forwardRef(() => OneDriveStorageService))
    private oneDriveService: OneDriveStorageService,
  ) { }

  async createFolder(body: CreateFolderDto, userId: number) {
    let user = await User.findOne(userId, {
      relations: ['organization'],
    });

    let storage = new Storage();
    storage.name = body.name;
    storage.type = StorageType.FOLDER;
    storage.uid = uuidv4();
    storage.authId = user.organization.id;

    let existinFolder = await this.existing({
      name: body.name,
      parent: body.parent,
      type: body.type,
      clientId: body.clientId,
      clientGroupId: body.clientGroup,
      orgId: user.organization.id,
      roomId: body.roomId,
    });

    if (existinFolder) {
      throw new ConflictException('Folder with this name already exists');
    }

    let atomFolder: Storage;
    let clientFolder: Storage;
    let displayNameFolder: Storage;

    if (body.type === 'client') {
      let client = await Client.findOne({
        where: {
          id: body.clientId,
        },
      });
      //MICROSOFT
      if (user.organization.storageSystem === StorageSystem.MICROSOFT) {
        storage.storageSystem = StorageSystem.MICROSOFT;
        storage.authId = user.organization.id;
        if (body.parent) {
          let folder = await Storage.findOne({ where: { uid: body.parent } });
          const folderData = await this.oneDriveService.createOneDriveFolder(
            user?.id,
            body.name,
            folder?.fileId,
          );
          storage.fileId = folderData.id;
        } else {
          atomFolder = await Storage.findOne({
            where: {
              name: 'Atom',
              organization: user.organization.id,
              show: false,
            },
          });
          if (!atomFolder) {
            const folderData = await this.oneDriveService.createOneDriveFolder(user?.id, 'Atom');
            atomFolder = new Storage();
            atomFolder.name = 'Atom';
            atomFolder.organization = user.organization;
            atomFolder.type = StorageType.FOLDER;
            atomFolder.uid = uuidv4();
            atomFolder.fileId = folderData.id;
            atomFolder.show = false;
            atomFolder.storageSystem = StorageSystem.MICROSOFT;
            atomFolder.authId = user.organization.id;
            await atomFolder.save();
          }
          clientFolder = await Storage.findOne({
            where: {
              name: 'Clients',
              organization: user.organization.id,
              show: false,
            },
          });
          if (!clientFolder) {
            const folderData = await this.oneDriveService.createOneDriveFolder(
              user?.id,
              'Clients',
              atomFolder.fileId,
            );
            clientFolder = new Storage();
            clientFolder.name = 'Clients';
            clientFolder.organization = user.organization;
            clientFolder.type = StorageType.FOLDER;
            clientFolder.uid = uuidv4();
            clientFolder.fileId = folderData.id;
            clientFolder.show = false;
            clientFolder.storageSystem = StorageSystem.MICROSOFT;
            clientFolder.authId = user.organization.id;
            await clientFolder.save();
          }
          displayNameFolder = await Storage.findOne({
            where: {
              name: client.displayName,
              organization: user.organization.id,
              show: false,
            },
          });
          if (!displayNameFolder) {
            const folderData = await this.oneDriveService.createOneDriveFolder(
              user?.id,
              client.displayName,
              clientFolder?.fileId,
            );
            displayNameFolder = new Storage();
            displayNameFolder.name = client.displayName;
            displayNameFolder.organization = user.organization;
            displayNameFolder.type = StorageType.FOLDER;
            displayNameFolder.uid = uuidv4();
            displayNameFolder.fileId = folderData.id;
            displayNameFolder.show = false;
            displayNameFolder.storageSystem = StorageSystem.MICROSOFT;
            displayNameFolder.authId = user.organization.id;
            await displayNameFolder.save();
          }
          const folderData = await this.oneDriveService.createOneDriveFolder(
            user?.id,
            body.name,
            displayNameFolder?.fileId,
          );
          storage.fileId = folderData.id;
        }
      } else if (user.organization.storageSystem === StorageSystem.BHARATHCLOUD) {
        storage.storageSystem = StorageSystem.BHARATHCLOUD;
        storage.authId = user.organization.id;
        if (body.parent) {
          let folder = await Storage.findOne({ where: { uid: body.parent } });
          // const folderData = await this.oneDriveService.createOneDriveFolder(user?.id, body.name, folder?.fileId);
          // storage.fileId = folderData.id;
          storage.filePath = `${folder.filePath}/${body.name}`;
        } else {
          atomFolder = await Storage.findOne({
            where: {
              name: 'Atom',
              organization: user.organization.id,
              show: false,
            },
          });
          if (!atomFolder) {
            // const folderData = await this.oneDriveService.createOneDriveFolder(user?.id, "Atom");
            const name = 'Atom';
            atomFolder = new Storage();
            atomFolder.name = 'Atom';
            atomFolder.organization = user.organization;
            atomFolder.type = StorageType.FOLDER;
            atomFolder.uid = uuidv4();
            // atomFolder.fileId = folderData.id;
            atomFolder.filePath = name;
            atomFolder.show = false;
            atomFolder.storageSystem = StorageSystem.BHARATHCLOUD;
            atomFolder.authId = user.organization.id;
            await atomFolder.save();
          }
          clientFolder = await Storage.findOne({
            where: {
              name: 'Clients',
              organization: user.organization.id,
              show: false,
            },
          });
          if (!clientFolder) {
            // const folderData = await this.oneDriveService.createOneDriveFolder(user?.id, "Clients", atomFolder.fileId);
            const client = 'Clients';
            clientFolder = new Storage();
            clientFolder.name = client;
            clientFolder.organization = user.organization;
            clientFolder.type = StorageType.FOLDER;
            clientFolder.uid = uuidv4();
            // clientFolder.fileId = folderData.id;
            clientFolder.filePath = `${atomFolder.filePath}/${client}`;
            clientFolder.show = false;
            clientFolder.storageSystem = StorageSystem.BHARATHCLOUD;
            clientFolder.authId = user.organization.id;
            await clientFolder.save();
          }
          displayNameFolder = await Storage.findOne({
            where: {
              name: client.displayName,
              organization: user.organization.id,
              show: false,
            },
          });
          if (!displayNameFolder) {
            const name = getName(client.displayName);
            // const folderData = await this.oneDriveService.createOneDriveFolder(user?.id, client.displayName, clientFolder?.fileId);
            displayNameFolder = new Storage();
            displayNameFolder.name = client.displayName;
            displayNameFolder.organization = user.organization;
            displayNameFolder.type = StorageType.FOLDER;
            displayNameFolder.uid = uuidv4();
            // displayNameFolder.fileId = folderData.id;
            displayNameFolder.filePath = `${clientFolder.filePath}/${name}`;
            displayNameFolder.show = false;
            displayNameFolder.storageSystem = StorageSystem.BHARATHCLOUD;
            displayNameFolder.authId = user.organization.id;
            await displayNameFolder.save();
          }
          // const folderData = await this.oneDriveService.createOneDriveFolder(user?.id, body.name, displayNameFolder?.fileId);
          // storage.fileId = folderData.id;
          storage.filePath = `${displayNameFolder.filePath}/${body.name}`;
        }
      }
      storage.client = client;
    }

    if (body.type === 'clientGroup') {
      let clientGroup = await ClientGroup.findOne({
        where: {
          id: body.clientGroup,
        },
      });
      if (user.organization.storageSystem === StorageSystem.MICROSOFT) {
        storage.storageSystem = StorageSystem.MICROSOFT;
        storage.authId = user.organization.id;
        if (body.parent) {
          let folder = await Storage.findOne({ where: { uid: body.parent } });
          const folderData = await this.oneDriveService.createOneDriveFolder(
            user?.id,
            body.name,
            folder?.fileId,
          );
          storage.fileId = folderData.id;
        } else {
          atomFolder = await Storage.findOne({
            where: {
              name: 'Atom',
              organization: user.organization.id,
              show: false,
            },
          });
          if (!atomFolder) {
            const folderData = await this.oneDriveService.createOneDriveFolder(user?.id, 'Atom');
            atomFolder = new Storage();
            atomFolder.name = 'Atom';
            atomFolder.organization = user.organization;
            atomFolder.type = StorageType.FOLDER;
            atomFolder.uid = uuidv4();
            atomFolder.fileId = folderData.id;
            atomFolder.show = false;
            atomFolder.storageSystem = StorageSystem.MICROSOFT;
            atomFolder.authId = user.organization.id;
            await atomFolder.save();
          }
          clientFolder = await Storage.findOne({
            where: {
              name: 'Clients',
              organization: user.organization.id,
              show: false,
            },
          });
          if (!clientFolder) {
            const folderData = await this.oneDriveService.createOneDriveFolder(
              user?.id,
              'Clients',
              atomFolder.fileId,
            );
            clientFolder = new Storage();
            clientFolder.name = 'Clients';
            clientFolder.organization = user.organization;
            clientFolder.type = StorageType.FOLDER;
            clientFolder.uid = uuidv4();
            clientFolder.fileId = folderData.id;
            clientFolder.show = false;
            clientFolder.storageSystem = StorageSystem.MICROSOFT;
            clientFolder.authId = user.organization.id;
            await clientFolder.save();
          }
          displayNameFolder = await Storage.findOne({
            where: {
              name: clientGroup.displayName,
              organization: user.organization.id,
              show: false,
            },
          });
          if (!displayNameFolder) {
            const folderData = await this.oneDriveService.createOneDriveFolder(
              user?.id,
              clientGroup.displayName,
              clientFolder?.fileId,
            );
            displayNameFolder = new Storage();
            displayNameFolder.name = clientGroup.displayName;
            displayNameFolder.organization = user.organization;
            displayNameFolder.type = StorageType.FOLDER;
            displayNameFolder.uid = uuidv4();
            displayNameFolder.fileId = folderData.id;
            displayNameFolder.show = false;
            displayNameFolder.storageSystem = StorageSystem.MICROSOFT;
            displayNameFolder.authId = user.organization.id;
            await displayNameFolder.save();
          }
          const folderData = await this.oneDriveService.createOneDriveFolder(
            user?.id,
            body.name,
            displayNameFolder?.fileId,
          );
          storage.fileId = folderData.id;
        }
      }
      storage.clientGroup = clientGroup;
    }

    if (body.type === 'organization') {
      storage.organization = user.organization;
      if (user.organization.storageSystem === StorageSystem.MICROSOFT) {
        if (body?.parent) {
          const parentFolder = await Storage.findOne({ where: { uid: body.parent } });
          const oneDriveFolder = await this.oneDriveService.createOneDriveFolder(
            userId,
            body.name,
            parentFolder.fileId,
          );
          storage.fileId = oneDriveFolder.id;
          storage.storageSystem = StorageSystem.MICROSOFT;
        } else {
          let parentFolder: Storage;
          let atomFolder: Storage;
          parentFolder = await Storage.findOne({
            where: { name: 'Organization Storage', organization: user.organization.id },
          });

          if (!parentFolder) {
            atomFolder = await Storage.findOne({
              where: { name: 'Atom', organization: user.organization.id },
            });
            if (!atomFolder) {
              const folderData = await this.oneDriveService.createOneDriveFolder(userId, 'Atom');
              atomFolder = new Storage();
              atomFolder.uid = uuidv4();
              atomFolder.name = 'Atom';
              atomFolder.type = StorageType.FOLDER;
              atomFolder.fileId = folderData.id;
              atomFolder.show = false;
              atomFolder.storageSystem = StorageSystem.MICROSOFT;
              atomFolder.authId = user.organization.id;
              atomFolder.organization = user.organization;
              await atomFolder.save();
            }

            const orgOneDriveFolder = await this.oneDriveService.createOneDriveFolder(
              userId,
              'Organization Storage',
              atomFolder.fileId,
            );
            parentFolder = new Storage();
            parentFolder.uid = uuidv4();
            parentFolder.name = 'Organization Storage';
            parentFolder.type = StorageType.FOLDER;
            parentFolder.fileId = orgOneDriveFolder.id;
            parentFolder.show = false;
            parentFolder.storageSystem = StorageSystem.MICROSOFT;
            parentFolder.authId = user.organization.id;
            parentFolder.parent = atomFolder;
            parentFolder.organization = user.organization;
            await parentFolder.save();
          }
          const oneDriveFolder = await this.oneDriveService.createOneDriveFolder(
            userId,
            body.name,
            parentFolder.fileId,
          );
          storage.fileId = oneDriveFolder.id;
          storage.storageSystem = StorageSystem.MICROSOFT;
        }
        //BHARATHCLOUD
      } else if (user.organization.storageSystem === StorageSystem.BHARATHCLOUD) {
        if (body?.parent) {
          const parentFolder = await Storage.findOne({ where: { uid: body.parent } });
          // const oneDriveFolder = await this.oneDriveService.createOneDriveFolder(userId, body.name, parentFolder.fileId);
          // storage.fileId = oneDriveFolder.id;
          storage.storageSystem = StorageSystem.BHARATHCLOUD;
          storage.filePath = `${parentFolder.filePath}/${body.name}`;
        } else {
          let parentFolder: Storage;
          let atomFolder: Storage;
          parentFolder = await Storage.findOne({
            where: { name: 'Organization Storage', organization: user.organization.id },
          });

          if (!parentFolder) {
            atomFolder = await Storage.findOne({
              where: { name: 'Atom', organization: user.organization.id },
            });
            if (!atomFolder) {
              // const folderData = await this.oneDriveService.createOneDriveFolder(userId, 'Atom');
              atomFolder = new Storage();
              atomFolder.uid = uuidv4();
              atomFolder.name = 'Atom';
              atomFolder.type = StorageType.FOLDER;
              // atomFolder.fileId = folderData.id;
              atomFolder.filePath = 'Atom';
              atomFolder.show = false;
              atomFolder.storageSystem = StorageSystem.BHARATHCLOUD;
              atomFolder.authId = user.organization.id;
              atomFolder.organization = user.organization;
              await atomFolder.save();
            }

            // const orgOneDriveFolder = await this.oneDriveService.createOneDriveFolder(userId, 'Organization Storage', atomFolder.fileId);
            const orgStorage = 'Organization Storage';
            parentFolder = new Storage();
            parentFolder.uid = uuidv4();
            parentFolder.name = orgStorage;
            parentFolder.type = StorageType.FOLDER;
            // parentFolder.fileId = orgOneDriveFolder.id;
            parentFolder.filePath = `${atomFolder.filePath}/${orgStorage}`;
            parentFolder.show = false;
            parentFolder.storageSystem = StorageSystem.BHARATHCLOUD;
            parentFolder.authId = user.organization.id;
            parentFolder.parent = atomFolder;
            parentFolder.organization = user.organization;
            await parentFolder.save();
          }
          // const oneDriveFolder = await this.oneDriveService.createOneDriveFolder(userId, body.name, parentFolder.fileId);
          // storage.fileId = oneDriveFolder.id;
          storage.filePath = `${parentFolder.filePath}/${body.name}`;
          storage.storageSystem = StorageSystem.BHARATHCLOUD;
        }
      } else {
        storage.storageSystem = StorageSystem.AMAZON;
      }
    }

    if (body.parent) {
      let folder = await Storage.findOne({ where: { uid: body.parent } });
      storage.parent = folder;
    }

    await storage.save();
    return storage;
  }

  async findStorage(query: FindStorageDto, userId: number) {
    const { folderId, clientId, type } = query;
    const user = await User.findOne(userId, { relations: ['organization'] });

    let storage = getConnection()
      .createQueryBuilder(Storage, 'storage')
      .leftJoin('storage.parent', 'parent')
      .leftJoinAndSelect('storage.user', 'user')
      .leftJoinAndSelect('storage.documentsData', 'documentsData');

    if (type === 'client') {
      storage
        .leftJoin('storage.client', 'client')
        .where('client.id = :clientId', { clientId })
        .andWhere('storage.show !=:show', { show: false });
    }

    if (type === 'clientGroup') {
      storage
        .leftJoin('storage.clientGroup', 'clientGroup')
        .where('clientGroup.id = :clientGroup', { clientGroup: query?.clientGroup })
        .andWhere('storage.show !=:show', { show: false });
    }

    if (type === 'organization') {
      storage
        .leftJoin('storage.organization', 'organization')
        .where('organization.id = :orgId', { orgId: user.organization.id })
        .andWhere('storage.show !=:show', { show: false });
    }

    if (query.search) {
      storage.andWhere('storage.name LIKE :search', { search: `%${query.search}%` });
    }

    if (!query.search) {
      if (folderId) {
        storage.andWhere('parent.uid = :folderId', { folderId });
      }

      if (!folderId) {
        storage.andWhere('storage.parent is null');
      }
    }

    let data = await storage.getMany();
    let breadCrumbs = await getManager().query(`
     WITH RECURSIVE cte_storage(id, parent_id, name, uid) as (
       SELECT id,
       parent_id,
       name, uid from storage WHERE uid = '${folderId}'
        UNION ALL
       SELECT s.id, 
       s.parent_id,
       s.name,
       s.uid from cte_storage as cs JOIN storage AS s on cs.parent_id = s.id
      )
      SELECT name, uid from cte_storage order by id;
    `);

    return {
      result: data,
      breadCrumbs,
    };
  }

  async saveFile(args: IUpload) {
    const { userId } = args;
    let user = await User.findOne({
      where: { id: userId },
      relations: ['organization'],
    });
    const storageSystem = user.organization.storageSystem;
    if (storageSystem === 'AMAZON') {
      return await this.uploadFile(args);
    } else if (storageSystem === 'MICROSOFT') {
      return await this.oneDriveService.uploadFile(args);
    } else if (storageSystem === 'GOOGLE') {
      return await this.googleDriveService.uploadFile(args);
    } else if (storageSystem === 'BHARATHCLOUD') {
      return await this.bharathService.uploadFile(args);
    }
    return user.organization.storageSystem;
  }

  async uploadFile(args: IUpload) {
    const { file, body, userId } = args;
    const { buffer, mimetype } = file;
    try {
      let user = await User.findOne({
        where: { id: userId },
        relations: ['organization'],
      });
      let documentsData: DocumentsData;
      if (body?.docId) {
        documentsData = await DocumentsData.findOne(body.docId);
      }

      let storage = new Storage();
      storage.fileType = file.mimetype;
      storage.fileSize = file.size;
      // storage.name = file.originalname.replace(/\+/g, ' ');
      storage.name = sanitizeFileNameForAWS(file.originalname);
      storage.type = StorageType.FILE;
      storage.uid = uuidv4();
      storage.storageSystem = StorageSystem.AMAZON;
      storage.user = user;
      if (body?.docId) {
        storage.documentsData = documentsData;
      }
      let existingFile = await this.existing({
        // name: file.originalname.replace(/\+/g, ' '),
        name: sanitizeFileNameForAWS(file.originalname),
        parent: body.folderId,
        type: body.type,
        clientId: body.clientId,
        clientGroupId: body.clientGroup,
        orgId: user.organization.id,
        roomId: body.roomId,
      });

      if (existingFile) {
        throw new ConflictException('File with this name already exists');
      }

      const { storageLimit, freeSpace } = await this.getOrgStorage(userId);
      if (!(freeSpace - +file.size > 0)) {
        throw new ConflictException(
          'We regret to inform you that the storage limit for your organization has been exceeded. As a result, no further data can be stored until the storage usage is reduced or additional storage capacity is allocated.',
        );
      }

      if (body.type === 'client') {
        let client = await Client.findOne({ where: { id: body.clientId } });
        // let key = `storage/${client.clientId}/${file.originalname.replace(/\+/g, ' ')}`;
        let key = `storage/${client.clientId}/${sanitizeFileNameForAWS(file.originalname)}`;
        let upload: any = await this.awsService.upload(buffer, key, mimetype);
        storage.file = upload.Key;
        storage.client = client;
      }

      if (body.type === 'clientGroup') {
        let clientGroup = await ClientGroup.findOne({ where: { id: body.clientGroup } });
        // let key = `storage/${clientGroup?.clientId || clientGroup?.id}/${file.originalname.replace(
        //   /\+/g,
        //   ' ',
        // )}`;
        let key = `storage/${clientGroup?.clientId || clientGroup?.id}/${sanitizeFileNameForAWS(file.originalname)}`;
        let upload: any = await this.awsService.upload(buffer, key, mimetype);
        storage.file = upload.Key;
        storage.clientGroup = clientGroup;
      }

      if (body.type === 'organization') {
        let key = `storage/orgniazation-${user.organization.id}/${file.originalname.replace(
          /\+/g,
          ' ',
        )}`;
        let upload: any = await this.awsService.upload(buffer, key, mimetype);
        storage.file = upload.Key;
        storage.organization = user.organization;
      }

      if (body.folderId) {
        let folder = await Storage.findOne({ where: { uid: body.folderId } });
        storage.parent = folder;
      }
      await storage.save();
      if (body.type === 'client') {
        let activity = new Activity();
        activity.action = Event_Actions.ATTACHMENT_UPLOADED;
        activity.actorId = user.id;
        activity.type = ActivityType.CLIENT;
        activity.typeId = body.clientId;
        activity.remarks = `Attachement "${storage.name}" Uploaded by ${user.fullName}`;
        await activity.save();
      }
      if (body.type === 'clientGroup') {
        let activity = new Activity();
        activity.action = Event_Actions.ATTACHMENT_UPLOADED;
        activity.actorId = user.id;
        activity.type = ActivityType.CLIENT_GROUP;
        activity.typeId = body.clientGroup;
        activity.remarks = `Attachement "${storage.name}" Uploaded by ${user.fullName}`;
        await activity.save();
      }
      return storage;
    } catch (err) {
      throw new BadRequestException(err);
    }
  }

  async saveAttchement(args: IUpload) {
    const { userId } = args;
    let user = await User.findOne({
      where: { id: userId },
      relations: ['organization'],
    });
    if (user.organization.storageSystem === StorageSystem.AMAZON) {
      return await this.attachementsUpload(args);
    } else if (user.organization.storageSystem === StorageSystem.MICROSOFT) {
      return await this.oneDriveService.attachementsUpload(args);
    } else if (user.organization.storageSystem === StorageSystem.GOOGLE) {
      return await this.googleDriveService.attachementsUpload(args);
    } else if (user.organization.storageSystem === StorageSystem.BHARATHCLOUD) {
      return await this.bharathService.attachementsUpload(args);
    }
    return user.organization.storageSystem;
  }

  async attachementsUpload(args: IUpload) {
    const { file, body, userId } = args;
    const { stageid } = body;

    let user = await User.findOne({
      where: { id: userId },
      relations: ['organization'],
    });

    const { buffer, mimetype } = file;
    try {
      let user = await User.findOne({
        where: { id: userId },
        relations: ['organization'],
      });

      let existingFile = await this.existing({
        name: file.originalname,
        parent: body.folderId,
        type: body.type,
        clientId: body.clientId,
        clientGroupId: body.clientGroup,
        orgId: user.organization.id,
        roomId: body.roomId,
      });

      if (body.type !== 'chat' && existingFile) {
        throw new ConflictException('File with this name already exists');
      }

      const { storageLimit, freeSpace } = await this.getOrgStorage(userId);
      if (!(freeSpace - +file.size > 0)) {
        throw new ConflictException(
          'We regret to inform you that the storage limit for your organization has been exceeded. As a result, no further data can be stored until the storage usage is reduced or additional storage capacity is allocated.',
        );
      }

      let key: string;
      let upload: any;
      let kybStorage = null;
      if (body.type === 'client') {

        let client = await Client.findOne({ where: { id: body.clientId } });
        if (body?.kyb == 'true') {
          const clientId = body.clientId;
          const storageType = user.organization.storageSystem;
          kybStorage = await this.existingKybStorage(userId, clientId, undefined, storageType);
          key = `storage/${client?.clientId ?? body?.clientId}/${file.originalname}`;
          upload = await this.awsService.upload(buffer, key, mimetype);

        } else {
          key = `storage/${client?.clientId ?? body?.clientId}/${file.originalname}`;
          upload = await this.awsService.upload(buffer, key, mimetype);
        }

      }

      if (body.type === 'clientGroup') {
        if (body?.kyb == 'true') {
          const clientGroupId = body.clientId;
          const storageType = user.organization.storageSystem;
          kybStorage = await this.existingKybStorage(userId, undefined, clientGroupId, storageType);
          upload = await this.awsService.upload(buffer, key, mimetype);
        }
        let clientGroup = await ClientGroup.findOne({ where: { id: body.clientId } });
        key = `storage/${clientGroup?.clientId ?? body?.clientId}/${file.originalname}`;
        upload = await this.awsService.upload(buffer, key, mimetype);
      }

      if (body.type === 'organization') {
        key = `storage/orgniazation-${user.organization.id}/${file.originalname}`;
        upload = await this.awsService.upload(buffer, key, mimetype);
      }

      if (body.type === 'chat') {
        key = `storage/chat-${body.roomId}-${moment().valueOf()}/${file.originalname}`;
        upload = await this.awsService.upload(buffer, key, mimetype);
      }

      return {
        key,
        upload: upload.key,
        fileSize: file.size,
        fileType: file.mimetype,
        clientId: body.clientId,
        name: file.originalname,
        storageSystem: StorageSystem.AMAZON,
        file: upload.key,
        stageid,
        parentId: kybStorage?.id
      };
    } catch (err) {
      console.error(err);
      throw new BadRequestException(err);
    }
  };

  async existingKybStorage(userId: number, clientId?: number, clientGroupId?: number, storageType?: StorageSystem) {
    let atomFolder: Storage;
    let clientFolder: Storage;
    let dispalayNameFolder: Storage;
    let kybInfoFolder: Storage;
    const user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
    const client = await Client.findOne({ where: { id: clientId } });
    const clientGroup = await ClientGroup.findOne({ where: { id: clientGroupId } });
    if (storageType && storageType === StorageSystem.MICROSOFT) {
      atomFolder = await Storage.findOne({
        where: {
          name: "Atom",
          organization: user.organization.id,
          show: false
        }
      });
      if (!atomFolder) {
        const folderData = await this.oneDriveService.createOneDriveFolder(user?.id, "Atom");
        atomFolder = new Storage();
        atomFolder.name = 'Atom';
        atomFolder.organization = user.organization;
        atomFolder.type = StorageType.FOLDER;
        atomFolder.uid = uuidv4();
        atomFolder.fileId = folderData.id;
        atomFolder.show = false;
        atomFolder.storageSystem = StorageSystem.MICROSOFT;
        atomFolder.authId = user.organization.id;
        await atomFolder.save();
      };
      clientFolder = await Storage.findOne({
        where: {
          name: "Clients",
          organization: user.organization.id,
          show: false
        }
      });
      if (!clientFolder) {
        const folderData = await this.oneDriveService.createOneDriveFolder(user?.id, "Clients", atomFolder.fileId);
        clientFolder = new Storage();
        clientFolder.name = "Clients";
        clientFolder.organization = user.organization;
        clientFolder.type = StorageType.FOLDER;
        clientFolder.uid = uuidv4();
        clientFolder.fileId = folderData.id;
        clientFolder.show = false;
        clientFolder.storageSystem = StorageSystem.MICROSOFT;
        clientFolder.authId = user.organization.id;
        await clientFolder.save();
      };
      dispalayNameFolder = await Storage.findOne({
        where: {
          name: client.displayName,
          organization: user.organization.id,
          show: false,
          type: StorageType.FOLDER,
          ...(client && { client: client }),
          ...(clientGroup && { clientGroup: clientGroup })
        }
      });
      if (!dispalayNameFolder) {
        const folderData = await this.oneDriveService.createOneDriveFolder(user?.id, client ? client.displayName : clientGroup.displayName, clientFolder?.fileId);
        dispalayNameFolder = new Storage();
        dispalayNameFolder.name = client ? client.displayName : clientGroup.displayName;
        dispalayNameFolder.organization = user.organization;
        dispalayNameFolder.type = StorageType.FOLDER;
        dispalayNameFolder.uid = uuidv4();
        dispalayNameFolder.fileId = folderData.id;
        dispalayNameFolder.show = false;
        dispalayNameFolder.storageSystem = StorageSystem.MICROSOFT;
        dispalayNameFolder.authId = user.organization.id;
        if (client) {
          dispalayNameFolder.client = client;
        } else if (clientGroup) {
          dispalayNameFolder.clientGroup = clientGroup;
        }
        await dispalayNameFolder.save();
      };

      kybInfoFolder = await Storage.findOne({
        where: {
          name: "KYB Info",
          show: true,
          type: StorageType.FOLDER,
          ...(client && { client: client }),
          ...(clientGroup && { clientGroup: clientGroup })
        }
      });
      if (!kybInfoFolder) {
        const folderData = await this.oneDriveService.createOneDriveFolder(user?.id, "KYB Info", dispalayNameFolder?.fileId);
        kybInfoFolder = new Storage();
        kybInfoFolder.name = "KYB Info";
        kybInfoFolder.type = StorageType.FOLDER;
        kybInfoFolder.uid = uuidv4();
        kybInfoFolder.fileId = folderData.id;
        kybInfoFolder.show = true;
        kybInfoFolder.storageSystem = StorageSystem.MICROSOFT;
        kybInfoFolder.authId = user.organization.id;
        if (client) {
          kybInfoFolder.client = client;
        } else if (clientGroup) {
          kybInfoFolder.clientGroup = clientGroup;
        }
        await kybInfoFolder.save();
      };

      return kybInfoFolder;


    } else if (storageType && storageType === StorageSystem.GOOGLE) {
      atomFolder = await Storage.findOne({
        where: {
          name: "Atom",
          organization: user.organization.id,
          show: false,
          type: StorageType.FOLDER
        }
      });
      if (!atomFolder) {
        const folderData = await this.googleDriveService.createGoogleDriveFolder(user?.id, "Atom");
        atomFolder = new Storage();
        atomFolder.name = 'Atom';
        atomFolder.organization = user.organization;
        atomFolder.type = StorageType.FOLDER;
        atomFolder.uid = uuidv4();
        atomFolder.fileId = folderData.id;
        atomFolder.show = false;
        atomFolder.storageSystem = StorageSystem.GOOGLE;
        atomFolder.authId = user.organization.id;
        await atomFolder.save();
      };
      clientFolder = await Storage.findOne({
        where: {
          name: "Clients",
          organization: user.organization.id,
          show: false,
          type: StorageType.FOLDER
        }
      });
      if (!clientFolder) {
        const folderData = await this.googleDriveService.createGoogleDriveFolder(user?.id, "Clients", atomFolder.fileId);
        clientFolder = new Storage();
        clientFolder.name = "Clients";
        clientFolder.organization = user.organization;
        clientFolder.type = StorageType.FOLDER;
        clientFolder.uid = uuidv4();
        clientFolder.fileId = folderData.id;
        clientFolder.show = false;
        clientFolder.storageSystem = StorageSystem.GOOGLE;
        clientFolder.authId = user.organization.id;
        await clientFolder.save();
      };
      dispalayNameFolder = await Storage.findOne({
        where: {
          name: client ? client.displayName : clientGroup?.displayName,
          organization: user.organization.id,
          show: false,
          type: StorageType.FOLDER,
          ...(client && { client: client }),
          ...(clientGroup && { clientGroup: clientGroup })
        }
      });
      if (!dispalayNameFolder) {
        const folderData = await this.googleDriveService.createGoogleDriveFolder(user?.id, client ? client.displayName : clientGroup.displayName, clientFolder?.fileId);
        dispalayNameFolder = new Storage();
        dispalayNameFolder.name = client ? client.displayName : clientGroup.displayName;
        dispalayNameFolder.organization = user.organization;
        dispalayNameFolder.type = StorageType.FOLDER;
        dispalayNameFolder.uid = uuidv4();
        dispalayNameFolder.fileId = folderData.id;
        dispalayNameFolder.show = false;
        dispalayNameFolder.storageSystem = StorageSystem.GOOGLE;
        dispalayNameFolder.authId = user.organization.id;
        if (client) {
          dispalayNameFolder.client = client;
        } else if (clientGroup) {
          dispalayNameFolder.clientGroup = clientGroup;
        }
        await dispalayNameFolder.save();
      };
      kybInfoFolder = await Storage.findOne({
        where: {
          name: 'KYB Info',
          show: true,
          type: StorageType.FOLDER,
          ...(client && { client: client }),
          ...(clientGroup && { clientGroup: clientGroup })
        }
      });
      if (!kybInfoFolder) {
        const folderData = await this.googleDriveService.createGoogleDriveFolder(user?.id, "KYB Info", dispalayNameFolder?.fileId);
        kybInfoFolder = new Storage();
        kybInfoFolder.name = "KYB Info";
        kybInfoFolder.type = StorageType.FOLDER;
        kybInfoFolder.uid = uuidv4();
        kybInfoFolder.fileId = folderData.id;
        kybInfoFolder.show = true;
        kybInfoFolder.storageSystem = StorageSystem.GOOGLE;
        kybInfoFolder.authId = user.organization.id;
        if (client) {
          kybInfoFolder.client = client;
        } else if (clientGroup) {
          kybInfoFolder.clientGroup = clientGroup;
        }
        await kybInfoFolder.save();
        return kybInfoFolder
      };
      return kybInfoFolder;

    } else {
      kybInfoFolder = await Storage.findOne({
        where: {
          name: 'KYB Info',
          show: true,
          type: StorageType.FOLDER,
          ...(client && { client: client }),
          ...(clientGroup && { clientGroup: clientGroup })
        }
      });
      if (!kybInfoFolder) {
        kybInfoFolder = new Storage();
        kybInfoFolder.name = "KYB Info";
        kybInfoFolder.type = StorageType.FOLDER;
        kybInfoFolder.uid = uuidv4();
        kybInfoFolder.show = true;
        kybInfoFolder.storageSystem = StorageSystem.AMAZON;
        kybInfoFolder.authId = user.organization.id;
        if (client) {
          kybInfoFolder.client = client;
        } else if (clientGroup) {
          kybInfoFolder.clientGroup = clientGroup;
        }
        await kybInfoFolder.save();

      };
      return kybInfoFolder;
    }

  }



  async uploadAndStoreFile(args: IUpload) {
    const { file, body, userId } = args;
    const { stageid } = body;
    let user = await User.findOne({
      where: { id: userId },
      relations: ['organization'],
    });

    const { buffer, mimetype } = file;
    try {
      let user = await User.findOne({
        where: { id: userId },
        relations: ['organization'],
      });

      const { storageLimit, freeSpace } = await this.getOrgStorage(userId);

      let key: string;
      let upload: any;

      if (body.type === 'viderAi') {
        const timestamp = Date.now();

        key = `storage/organization-${user.organization.id}/${file.originalname}-${timestamp}`;
        upload = await this.awsService.upload(buffer, key, mimetype);
        // Save file details to Storage table
        let storage = new Storage();
        storage.fileType = file.mimetype;
        storage.fileSize = file.size;
        storage.file = key;
        storage.name = file.originalname;
        storage.type = StorageType.FILE;
        storage.uid = uuidv4();
        storage.user = user;
        storage.authId = user.organization.id;
        storage.storageSystem = StorageSystem.AMAZON;

        await storage.save();
        let viderAi = new ViderAi();
        viderAi.response = body.aiResult;
        viderAi.storage = storage; // Storing reference to Storage table
        viderAi.organizationId = user?.organization?.id;
        await viderAi.save();
      }
      return {
        key,
        upload: upload.key,
        fileSize: file.size,
        fileType: file.mimetype,
        clientId: body.clientId,
        name: file.originalname,
        storageSystem: StorageSystem.AMAZON,
        file: upload.key,
        stageid,
      };
    } catch (err) {
      console.error(err);
      throw new BadRequestException(err);
    }
  }

  async addAttachements(userId: number, body: any) {
    let user = await User.findOne({
      where: { id: userId },
      relations: ['organization'],
    });
    let client = await Client.findOne({ where: { id: body.clientId } });
    let clientGroup = await ClientGroup.findOne({ where: { id: body.clientId } });
    let storage = new Storage();
    storage.fileType = body.fileType;
    storage.fileSize = body.fileSize;
    storage.name = body.name;
    storage.type = StorageType.FILE;
    storage.uid = uuidv4();
    storage.user = user;
    storage.storageSystem = body.storageSystem;
    storage.webUrl = body.webUrl;
    storage.downloadUrl = body.downloadUrl;
    storage.fileId = body.fileId;
    storage.show = body?.show;
    storage.authId = user.organization.id;
    storage.filePath = body.filePath;
    if (body?.parentId) {
      storage.parent = body.parentId;
      storage.show = true
    }


    if (body.type === 'client') {
      storage.file = body.upload;
      storage.client = client;
    }
    if (body.type === 'clientGroup') {
      storage.file = body.upload;
      storage.clientGroup = clientGroup;
    }
    if (body.type === 'organization') {
      storage.file = body.upload;
      storage.organization = user.organization;
    }
    return storage.save();
  }

  async createLink(userId: number, body: CreateLinkDto) {
    let user = await User.findOne({
      where: { id: userId },
      relations: ['organization'],
    });

    let storage = new Storage();
    storage.name = body.name;
    storage.file = body.file;
    storage.uid = uuidv4();
    storage.type = StorageType.LINK;

    if (body.local) {
      storage.type = StorageType.LOCAL_PATH;
    }

    if (body.folderId) {
      let folder = await Storage.findOne({ where: { uid: body.folderId } });
      storage.parent = folder;
    }

    if (body.type === LinkUserType.CLIENT) {
      let client = await Client.findOne({ where: { id: body.clientId } });
      storage.client = client;
    }

    if (body.type === LinkUserType.CLIENT_GROUP) {
      let clientGroup = await ClientGroup.findOne({ where: { id: body.clientId } });
      storage.clientGroup = clientGroup;
    }

    if (body.type === LinkUserType.ORGANIZATION) {
      storage.organization = user.organization;
    }

    if (body.type === LinkUserType.TASK) {
      let task = await Task.findOne({
        where: { id: body.taskId },
        relations: ['client', 'clientGroup'],
      });
      storage.task = task;
      storage.client = task?.client;
      storage.clientGroup = task?.clientGroup;
    }

    if (storage.task && storage.type === StorageType.LOCAL_PATH) {
      let activity = new Activity();
      activity.action = Event_Actions.LOCAL_DIRECTORY_PATH_ADDED;
      activity.actorId = user.id;
      activity.type = ActivityType.TASK;
      activity.typeId = storage?.task?.id;
      activity.remarks = `Local Directory Path "${storage?.name}" added by ${user.fullName}`;
      await activity.save();
    }

    await storage.save();
    return storage;
  }

  async moveFile(userId: number, data: MoveFileDto) {
    let { originId, destinationId } = data;

    try {
      let originStorage = await Storage.findOne({ where: { id: originId } });

      let destinationStorage = await createQueryBuilder(Storage, 'storage')
        .leftJoinAndSelect('storage.client', 'client')
        .leftJoinAndSelect('storage.clientGroup', 'clientGroup')
        .leftJoinAndSelect('storage.organization', 'organization')
        .leftJoinAndSelect('storage.chatRoom', 'chatRoom')
        .where('storage.id = :id', { id: destinationId })
        .getOne();

      let existing = await this.existing({
        name: originStorage.name,
        parent: destinationStorage.uid,
        type: Boolean(destinationStorage.client) ? 'client' : 'organization',
        clientId: destinationStorage?.client?.id,
        clientGroupId: destinationStorage?.clientGroup?.id,
        orgId: destinationStorage?.organization?.id ?? null,
        roomId: destinationStorage?.room?.id ?? null,
      });

      if (existing) {
        throw new ConflictException(
          'The file or folder with this name already exists in the destination folder',
        );
      }

      originStorage.parent = destinationStorage;
      await originStorage.save();
      return 'success';
    } catch (err) {
      throw new InternalServerErrorException(err);
    }
  }

  async replaceFile(data: MoveFileDto) {
    let { originId, destinationId } = data;
    let originStorage = await Storage.findOne({ where: { id: originId } });
    let destinationStorage = await Storage.findOne({ where: { id: destinationId } });

    let existingStorage = await Storage.findOne({
      where: {
        name: originStorage.name,
        parent: { id: destinationStorage.id },
        type: originStorage.type,
      },
    });

    if (existingStorage) {
      await existingStorage.remove();
    }

    originStorage.parent = destinationStorage;
    await originStorage.save();
  }

  async keepBothFilesOrFolders(data: MoveFileDto) {
    let { originId, destinationId } = data;
    let originStorage = await Storage.findOne({ where: { id: originId } });
    let destinationStorage = await Storage.findOne({ where: { id: destinationId } });

    let existingStorage = await Storage.findOne({
      where: {
        name: originStorage.name,
        parent: { id: destinationStorage.id },
        type: originStorage.type,
      },
    });

    if (existingStorage) {
      let type = originStorage.type;
      let name = originStorage.name;
      let eStartIndex = name.lastIndexOf('.');
      let fileName = name.substring(0, eStartIndex);
      let fileExtension = name.substring(eStartIndex + 1, name.length);
      let folderRegex = JSON.stringify(name + ' ' + '\\(\\d+\\)');
      let fileRegex = JSON.stringify(fileName + ' ' + '\\(\\d+\\)');
      let regex = type === 'folder' ? folderRegex : fileRegex;

      let similarStorage = await createQueryBuilder(Storage, 'storage')
        .leftJoinAndSelect('storage.parent', 'parent')
        .where('parent.id = :parent', { parent: destinationStorage.id })
        .andWhere('storage.type = :type', { type: originStorage.type })
        .andWhere(`storage.name regexp ${regex}`)
        .getCount();

      let count = similarStorage + 1;

      if (originStorage.type === 'file') {
        originStorage.name = `${fileName} (${count}).${fileExtension}`;
      } else {
        originStorage.name = `${name} (${count})`;
      }
    }

    originStorage.parent = destinationStorage;
    await originStorage.save();
    return 'success';
  }

  async removeFile(id: number, userId: number) {
    try {
      let user = await User.findOne({
        where: { id: userId },
        relations: ['organization'],
      });
      let storage = await Storage.findOne({
        where: { id },
        relations: ['task', 'client', 'clientGroup'],
      });
      if (storage.storageSystem === StorageSystem.MICROSOFT) {
        const deleteFile = await this.oneDriveService.deleteOneDriveFile(userId, storage.fileId);
        if (deleteFile.success) {
          await storage.remove();
        }
      } else if (storage.storageSystem === StorageSystem.GOOGLE) {
        const deleteFile = await this.googleDriveService.deleteGoogleDriveFile(
          userId,
          storage.fileId,
        );
        if (deleteFile.success) {
          await storage.remove();
        }
      } else if (storage.storageSystem === StorageSystem.AMAZON) {
        if (storage.type === StorageType.FILE) {
          await this.deleteAwsFile(storage.file);
        }
        await storage.remove();
      } else if (storage.storageSystem === StorageSystem.BHARATHCLOUD) {
        if (storage.type === StorageType.FILE) {
          await this.bharathService.deleteB3File(userId, storage.file);
        } else if (storage.type === StorageType.FOLDER) {
          await this.bharathService.deleteB3Folder(userId, storage.filePath);
        }
        await storage.remove();
      } else {
        await storage.remove();
      }

      if (storage.task && storage.type !== StorageType.LOCAL_PATH) {
        let collectactivity = new Activity();
        collectactivity.action = Event_Actions.ATTACHEMENT_DELETED;
        collectactivity.actorId = userId;
        collectactivity.type = ActivityType.TASK;
        collectactivity.typeId = storage.task.id;
        collectactivity.remarks = `Attachement "${storage.name}" Deleted by ${user.fullName}`;
        await collectactivity.save();
      } else if (storage.client || storage.clientGroup) {
        let collectactivity = new Activity();
        collectactivity.action = Event_Actions.ATTACHEMENT_DELETED;
        collectactivity.actorId = userId;
        collectactivity.type = storage.client ? ActivityType.CLIENT : ActivityType.CLIENT_GROUP;
        collectactivity.typeId = storage.client ? storage.client?.id : storage.clientGroup?.id;
        collectactivity.remarks = `Attachement "${storage.name}" Deleted by ${user.fullName}`;
        await collectactivity.save();
      }

      if (storage.task && storage.type === StorageType.LOCAL_PATH) {
        let activity = new Activity();
        activity.action = Event_Actions.LOCAL_DIRECTORY_PATH_REMOVED;
        activity.actorId = userId;
        activity.type = ActivityType.TASK;
        activity.typeId = storage?.task?.id;
        activity.remarks = `Local Directory Path "${storage?.name}" removed by ${user.fullName}`;
        await activity.save();
      }
      if (storage.collectId) {
        this.eventEmitter.emit(Event_Actions.COLLECT_DATA_FILES_DELETED, {
          origin: storage.origin,
          collectId: storage.collectId,
          taskId: storage.task.id,
          file: storage.name,
        });
      }

      return { success: true };
    } catch (err) {
      throw new Error(`Error In Deleting Folder/File`);
    }

    // try {
    //   let storage = await Storage.findOne({ where: { id } });
    //   console.log(storage);
    //   if (storage.storageSystem === StorageSystem.MICROSOFT) {
    //     console.log("Test-1");
    //     const deleteFile = await this.oneDriveService.deleteFile(userId, storage.fileId);
    //     if (deleteFile.success) {
    //       await storage.remove();
    //       return { success: true };
    //     };
    //   };

    // } catch (err) {
    //   throw new Error(`Error In Microsoft Server`);

    // }
  }
  async removecollectFile(id: number) {
    try {
      let storage = await Storage.findOne({
        where: { id },
        relations: ['task', 'client', 'task.organization'],
      });
      if (storage.storageSystem === StorageSystem.MICROSOFT) {
        const deleteFile = await this.oneDriveService.deleteOneDriveFile(
          null,
          storage.fileId,
          storage.task.organization.id,
        );
        if (deleteFile.success) {
          await storage.remove();
        }
      } else if (storage.storageSystem === StorageSystem.AMAZON) {
        if (storage.type === StorageType.FILE) {
          await this.deleteAwsFile(storage.file);
        }
        await storage.remove();
      } else if (storage.storageSystem === StorageSystem.BHARATHCLOUD) {
        if (storage.type === StorageType.FILE) {
          await this.bharathCloudService.deleteFile(storage.task.organization.id, storage.file);
        } else if (storage.type === StorageType.FOLDER) {
          await this.bharathCloudService.deleteFolder(
            storage.task.organization.id,
            storage.filePath,
          );
        }
        await storage.remove();
      } else {
        await storage.remove();
      }

      return { success: true };
    } catch (err) {
      console.error(err);
      throw new Error(`Error In Deleting Folder/File`);
    }

    // try {
    //   let storage = await Storage.findOne({ where: { id } });
    //   console.log(storage);
    //   if (storage.storageSystem === StorageSystem.MICROSOFT) {
    //     console.log("Test-1");
    //     const deleteFile = await this.oneDriveService.deleteFile(userId, storage.fileId);
    //     if (deleteFile.success) {
    //       await storage.remove();
    //       return { success: true };
    //     };
    //   };

    // } catch (err) {
    //   throw new Error(`Error In Microsoft Server`);

    // }
  }

  async renameFile({ name, file, id }: RenameFileDto) {
    let storage = await Storage.findOne({ where: { id } });
    storage.name = name;
    storage.file = file;
    await storage.save();
    return storage;
  }

  async getStorageTree(clientId: number) {
    let storage = getConnection()
      .createQueryBuilder(Storage, 'storage')
      .select([
        'storage.id as id',
        'storage.name as name',
        'storage.file as file',
        'storage.type as type',
        'parent.id as parent',
      ])
      .leftJoin('storage.client', 'client')
      .leftJoin('storage.clientGroup', 'clientGroup')
      .leftJoin('storage.parent', 'parent')
      .where('client.id = :clientId', { clientId })
      .andWhere('storage.type IN (:...type)', { type: [StorageType.FOLDER, StorageType.FILE] });

    let data = await storage.getRawMany();

    return data;
  }

  async getTotalStorage(query: TotalStorageDto) {
    let storage = await createQueryBuilder(Storage, 'storage')
      .select('SUM(storage.fileSize) as totalStorage')
      .leftJoin('storage.client', 'client')
      .leftJoin('storage.clientGroup', 'clientGroup');
    if (query.clientId) {
      storage.where('client.id = :clientId', { clientId: query.clientId });
    }
    if (query.clientGroupId) {
      storage.where('clientGroup.id = :clientGroupId', { clientGroupId: query.clientGroupId });
    }
    let data = await storage.getRawOne();

    return +data?.totalStorage ?? 0;
  }

  async getOrgStorage(userId: number, query?: any) {
    if (query?.storageSystem === StorageSystem.GOOGLE) {
      return await this.googleDriveService.getGoogleDriveStorageInfo(userId);
    };
    if (query?.storageSystem === StorageSystem.MICROSOFT) {
      return await this.oneDriveService.getOneDriveStorageInfo(userId);
    };

    const user = await User.findOne(userId, { relations: ['organization'] });
    let orgStorage = await createQueryBuilder(Storage, 'storage')
      .select('SUM(storage.fileSize) as totalStorage')
      .leftJoin('storage.organization', 'organization')
      .where('organization.id = :orgId', { orgId: user.organization.id })
      .getRawOne();

    let clientStorage = await createQueryBuilder(Storage, 'storage')
      .select('SUM(storage.fileSize) as totalStorage')
      .leftJoin('storage.client', 'client')
      .leftJoin('storage.clientGroup', 'clientGroup')
      .leftJoin('client.organization', 'organization')
      .where('organization.id = :orgId', { orgId: user.organization.id })
      .getRawOne();

    let unexpriedLinks = await createQueryBuilder(CollectData, 'collectData')
      .leftJoin('collectData.user', 'user')
      .leftJoin('user.organization', 'organization')
      .where('organization.id = :orgId', { orgId: user.organization.id })
      .andWhere('collectData.active=:active', { active: true })
      .getCount();

    let organization = await Organization.findOne({ where: { id: user.organization.id } });
    const orgLimit = organization.config?.['stroagelimit'] || 53687091200;

    return {
      storageLimit: orgLimit,
      totalStorageUsed:
        +clientStorage?.totalStorage + +orgStorage?.totalStorage + unexpriedLinks * 209715200 || 0,
      freeSpace:
        orgLimit -
        (+clientStorage?.totalStorage + +orgStorage?.totalStorage + unexpriedLinks * 209715200) ??
        0,
      unExpriedLinks: unexpriedLinks * 209715200,
    };
  }

  async existing(props: IExisting) {
    const { name, parent, type, orgId, clientId, clientGroupId, roomId } = props;
    let existing = createQueryBuilder(Storage, 'storage');

    let where = `storage.name = :name`;

    if (type === 'client') {
      existing.leftJoin('storage.client', 'client');
      where += ` and client.id = :clientId`;
    }

    if (type === 'clientGroup') {
      existing.leftJoin('storage.clientGroup', 'clientGroup');
      where += ` and clientGroup.id = :clientId`;
    }

    if (type === 'organization') {
      existing.leftJoin('storage.organization', 'organization');
      where += ` and organization.id = :orgId`;
    }
    if (type === 'chat') {
      existing.leftJoin('storage.room', 'room');
      where += ` and room.id = :roomId`;
    }

    if (parent) {
      existing.leftJoin('storage.parent', 'parent');
      where += ` and parent.uid = :parent`;
    }

    if (!parent) {
      where += ` and storage.parent is null`;
    }

    existing.where(`(${where})`, { name, parent, clientId, orgId, roomId });

    let result = await existing.getOne();
    return Boolean(result);
  }

  async getAutOrgStorage(orgId: number) {
    let orgStorage = await createQueryBuilder(Storage, 'storage')
      .select('SUM(storage.fileSize) as totalStorage')
      .leftJoin('storage.organization', 'organization')
      .where('organization.id = :orgId', { orgId })
      .getRawOne();

    let clientStorage = await createQueryBuilder(Storage, 'storage')
      .select('SUM(storage.fileSize) as totalStorage')
      .leftJoin('storage.client', 'client')
      .leftJoin('storage.clientGroup', 'clientGroup')
      .leftJoin('client.organization', 'organization')
      .where('organization.id = :orgId', { orgId })
      .getRawOne();

    let unexpiredLinks = await createQueryBuilder(CollectData, 'collectData')
      .leftJoin('collectData.user', 'user')
      .leftJoin('user.organization', 'organization')
      .where('organization.id = :orgId', { orgId })
      .andWhere('collectData.active = :active', { active: true })
      .getCount();

    let organization = await Organization.findOne({ where: { id: orgId } });
    const orgLimit = organization.config?.['stroagelimit'] || 53687091200;

    return {
      storageLimit: orgLimit,
      totalStorageUsed:
        +clientStorage?.totalStorage + +orgStorage?.totalStorage + unexpiredLinks * 209715200 || 0,
      freeSpace:
        orgLimit -
        (+clientStorage?.totalStorage + +orgStorage?.totalStorage + unexpiredLinks * 209715200) ??
        0,
      unexpiredLinks: unexpiredLinks * 209715200,
    };
  }

  async addBharathCredentials(userId: number, data: CreateB3Credentials) {
    const user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
    const cloudCredentials = new CloudCredentials();
    cloudCredentials.accessKey = data.accessKey;
    cloudCredentials.secretKey = data.seceretKey;
    cloudCredentials.bucketName = data.bucketName;
    cloudCredentials.endPoint = data.endPoint;
    cloudCredentials.organizationId = user.organization.id;
    await cloudCredentials.save();
    return cloudCredentials;
  }

  async getBharathCloudItems(userId: number, data: any) {
    return await this.bharathService.getBharathCloudItems(userId, data);
  }

  async deleteAwsFile(key: string) {
    return this.awsService.deleteFile(key);
  }
  async deleteAwsFolder(key: string) {
    return this.awsService.deleteFolder(key);
  }

  // @Cron(CronExpression.EVERY_MINUTE)
  async transfer() {
    try {
      const start = new Date('2025-05-02T00:30:00.000Z');
      const end = new Date('2025-05-02T13:10:00.000Z');
      const storage = await Storage.createQueryBuilder('storage')
        .where('storage.storageSystem = :ss', { ss: StorageSystem.AMAZON })
        .andWhere('storage.type=:type', { type: StorageType.FILE })
        .andWhere(
          new Brackets((qb) => {
            qb.where('storage.created_at BETWEEN :start AND :end', { start, end }).orWhere(
              'storage.updated_at BETWEEN :start AND :end',
              { start, end },
            );
          }),
        )
        .getMany();
      for (let s of storage) {
        await this.awsService.transferFileToNewAccountIfNotExists(s.file, s.fileType);
      }
      // return storage
      return { success: true };
    } catch (err) {
      console.log(err);
    }
  }

  // @Cron(CronExpression.EVERY_MINUTE)

  // async transfer() {
  //   // Check if the job is already running
  //   if (this.isProcessing) {
  //     console.log("Cron job is already running. Skipping this execution.");
  //     return;  // Skip execution if the previous job is still running
  //   }

  //   try {
  //     // Set the flag to true when the job starts
  //     this.isProcessing = true;
  //     console.log("Starting cron job...");

  //     let storageRecords = await Storage.find({ where: { id: In([112126, 112128]) } });
  //     console.log("storageRecords");

  //     for (const s of storageRecords) {
  //       console.log("One Record", s.id);
  //       try {
  //         // Await each transfer operation
  //         await this.awsService.transferFileToNewAccountIfNotExists(s.file, s.fileType);
  //       } catch (error) {
  //         console.error(`Error transferring file for storage ID ${s.id}:`, error.message);
  //       }
  //     }

  //     console.log("Cron job completed successfully.");
  //   } catch (error) {
  //     console.error("Error in cron job:", error.message);
  //   } finally {
  //     // Reset the flag after the cron job is complete
  //     this.isProcessing = false;
  //   }
  // }
}
