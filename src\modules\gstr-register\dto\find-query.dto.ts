import { IsNotEmpty, IsOptional } from 'class-validator';
import { RegistrationType } from '../entity/gstr-register.entity';

class FindQueryDto {
    @IsOptional()
    sort: string;
    @IsNotEmpty()
    selectedYear: string;

    @IsOptional()
    selectedMonth: string;

    @IsOptional()
    selectedQuarter: string;

    @IsOptional()
    offset: number;

    @IsOptional()
    limit: number;

    @IsOptional()
    search: string;

    @IsOptional()
    type: RegistrationType;

    @IsOptional()
    rtntype: string;

    @IsOptional()
    category: string;

    @IsOptional()
    subCategory: string;

    @IsOptional()
    jurisdiction: string;

    @IsOptional()
    status: string[];
}

export default FindQueryDto;
