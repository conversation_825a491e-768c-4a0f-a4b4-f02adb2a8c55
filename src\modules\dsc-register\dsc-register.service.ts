import { BadRequestException, Injectable, InternalServerErrorException, NotFoundException } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { Event_Actions } from 'src/event-listeners/actions';
import Client from 'src/modules/clients/entity/client.entity';
import { User } from 'src/modules/users/entities/user.entity';
import { Brackets, createQueryBuilder, In } from 'typeorm';
import DscActivity, { DscActivityTypeEnum } from './entity/dsc-activity.entity';
import DscRegister, { DscRegisterStatus } from './entity/dsc-register.entity';
import { CreateDscRegisterDto, UpdateDscRegisterDto } from './dto/create-dsc-register.dto';
import { DscRegiserQueyType, FindDscRegisterDto } from './dto/find-dsc-register.dto';
import IssueOrReceiveDto from './dto/issue-receive.dto';
import { ImportDscRegisterDto } from './dto/import-dsc-registers.dto';
import * as xlsx from 'xlsx';
import * as moment from 'moment';
import { validate } from 'class-validator';
import _ from 'lodash';
import DscApply, { DscApplicationStatus, DscType } from './entity/dsc-apply.entity';
import Storage from '../storage/storage.entity';
import { StorageService } from '../storage/storage.service';
import { getLoginUser } from 'src/utils/re-use';
import { formatDate } from 'src/utils';
import Activity, { ActivityType } from '../activity/activity.entity';
import mobileWithCountry from 'src/utils/validations/mobileWithCountry';
import countries from 'src/utils/countries';
import ClientGroup from '../client-group/client-group.entity';
import { DeleteDscClientsDto } from './dto/delete-dsc-clients.dto';
import { AddDscClientsDto } from './dto/add-dsc-clients.dto';
import * as ExcelJS from 'exceljs';
import { Permissions } from '../events/permission';

@Injectable()
export class DscRegisterService {
  constructor(private eventEmitter: EventEmitter2, private storageService: StorageService) { }

  async create(userId: number, data: CreateDscRegisterDto) {
    if (data.holderName.trim() === "") {
      throw new BadRequestException(
        `Invalid DSC Holder Name`,
      );
    }
    if (data.password.trim() === "" || data.password.trim().length < 8) {
      throw new BadRequestException(
        `Invalid DSC Password`,
      );
    }
    const dscHolderName = data.holderName.trim();
    let user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
    let existingUser = await createQueryBuilder(DscRegister, 'dscRegister')
      .leftJoin('dscRegister.organization', 'organization')
      .where('organization.id = :organization', { organization: user.organization.id })
      .andWhere('(dscRegister.holderName = :dscHolderName)', {
        dscHolderName: dscHolderName,
      })
      .getOne();
    if (existingUser) {
      throw new BadRequestException(
        `DSC Holder Name Already Exists`,
      );
    }
    let clientGroups = null
    let clients = null;
    if (data?.clientGroups) {
      clientGroups = await ClientGroup.find({ where: { id: data.clientGroups } });
    }
    if (data.clients?.length) {
      clients = await Client.find({ where: { id: In(data.clients) } });
    }

    // console.log(data)

    let dscRegister = new DscRegister();
    dscRegister.holderName = data.holderName.trim();
    dscRegister.expiryDate = data.expiryDate;
    dscRegister.password = data.password.trim();
    dscRegister.tokenNumber = data.tokenNumber.trim();
    dscRegister.panNumber = data.panNumber.trim();
    dscRegister.holderDesignation = data.holderDesignation.trim();
    dscRegister.mobileNumber = data.mobileNumber.trim();
    dscRegister.email = data.email !== null && data.email !== undefined ? data.email.trim() : data.email;
    dscRegister.countryCode = data.countryCode;
    dscRegister.clients = clients;
    dscRegister.clientGroups = clientGroups;
    dscRegister.organization = user.organization;
    dscRegister['userId'] = user.id;
    await dscRegister.save();


    if (dscRegister?.clients?.length) {
      let activities = [];
      for (let client of dscRegister.clients) {
        let activity = new Activity();
        activity.action = Event_Actions.DSC_RECORD_ADDED;
        activity.actorId = user.id;
        activity.type = ActivityType.CLIENT;
        activity.typeId = client.id;
        activity.remarks = `DSC Record "${dscRegister.holderName}" Created by ${user.fullName}`;
        activities.push(activity)
      };
      await Activity.save(activities)
    };
    if (dscRegister.clientGroups) {
      let activity = new Activity();
      activity.action = Event_Actions.DSC_RECORD_ADDED;
      activity.actorId = user.id;
      activity.type = ActivityType.CLIENT_GROUP;
      activity.typeId = data.clientGroups;
      activity.remarks = `DSC Record "${dscRegister.holderName}" Created by ${user.fullName}`;
      await activity.save()
    };


    // if (dscRegister.client || dscRegister.clientGroup) {
    //   let activity = new Activity();
    //   activity.action = Event_Actions.DSC_RECORD_ADDED;
    //   activity.actorId = user.id;
    //   activity.type = dscRegister.client ? ActivityType.CLIENT : ActivityType.CLIENT_GROUP;
    //   activity.typeId = dscRegister.client ? dscRegister.client.id : dscRegister.clientGroup.id;
    //   activity.remarks = `DSC Record "${dscRegister.holderName}" Created by ${user.fullName}`;
    //   await activity.save();
    // }


    return dscRegister;
  }

  async find(userId: number, query: FindDscRegisterDto) {
    const user = await User.findOne({
      where: { id: userId },
      relations: ['organization', 'role']
    });
    const ViewAll = user.role.permissions.some(
      (permission) => permission.slug === Permissions.VIEW_ALL_CLIENT_MANAGERS
    );
    const ViewAssigned = user.role.permissions.some(
      (permission) => permission.slug === Permissions.VIEW_ASSIGNED_CLIENT_MANAGERS
    );
    let dscRegisters = createQueryBuilder(DscRegister, 'dscRegister')
      .select([
        'dscRegister',
        'client.id',
        'client.displayName',
        'clientManagers.id',
        'clientManagers.fullName',
        'clientGroup.id',
        'clientGroup.displayName',
        'clientGroupManagers.id',
        'clientGroupManagers.fullName',
        'organization.id'
      ])
      .leftJoin('dscRegister.clients', 'client')
      .leftJoin('client.clientManagers', 'clientManagers')
      .leftJoin('dscRegister.clientGroups', 'clientGroup')
      .leftJoin('clientGroup.clientGroupManagers', 'clientGroupManagers')
      .leftJoin('dscRegister.organization', 'organization');

    if (query.type === DscRegiserQueyType.CLIENT) {
      dscRegisters.andWhere(
        qb => {
          const subQuery = qb.subQuery()
            .select('dsc.id')
            .from(DscRegister, 'dsc')
            .innerJoin('dsc.clients', 'subClient')
            .where('subClient.id = :clientId', { clientId: query.clientId })
            .getQuery();
          return `dscRegister.id IN ${subQuery}`;
        }
      );
    }


    if (query.type === DscRegiserQueyType.CLIENT_GROUP) {
      const clientGroup = await createQueryBuilder(ClientGroup, 'clientGroup')
        .select([
          'clientGroup',
          'organization.id',
          'clients.id',
          'clients.displayName'
        ])
        .leftJoin('clientGroup.organization', 'organization')
        .leftJoin('clientGroup.clients', 'clients')
        .where('clientGroup.id = :id', { id: query.clientGroupId })
        .andWhere('organization.id = :organizationId', { organizationId: user.organization.id })
        .getOne();

      const clientGroupIDs = clientGroup?.clients?.map((item) => item.id) || [];

      dscRegisters.andWhere(
        new Brackets((qb) => {
          qb.where('clientGroup.id = :clientGroupId', { clientGroupId: query.clientGroupId });

          if (clientGroupIDs.length) {
            qb.orWhere('client.id IN (:...clientGroupIDs)', { clientGroupIDs });
          }
        })
      );
    }
    if (!ViewAll && ViewAssigned) {
      dscRegisters.andWhere(
        new Brackets(qb => {
          qb.where('client.id IS NOT NULL AND clientManagers.id = :userId', { userId })
            .orWhere('clientGroup.id IS NOT NULL AND clientGroupManagers.id = :userId', { userId })
            .orWhere('client.id IS NULL AND clientGroup.id IS NULL');
        })
      );
    } else if (!ViewAll && !ViewAssigned) {
      dscRegisters.andWhere('client.id IS NULL AND clientGroup.id IS NULL');
    }

    if (query.type === DscRegiserQueyType.ORGANIZATION) {
      dscRegisters.andWhere('organization.id = :organizationId', {
        organizationId: user.organization.id,
      });
    }

    if (query.search) {
      dscRegisters.andWhere(
        new Brackets((qb) => {
          qb.where('dscRegister.holderName LIKE :search', { search: `%${query.search}%` })
            .orWhere('dscRegister.tokenNumber LIKE :search', { search: `%${query.search}%` })
            .orWhere('client.displayName LIKE :search', { search: `%${query.search}%` })
            .orWhere('clientGroup.displayName LIKE :search', { search: `%${query.search}%` });
        })
      );
    }

    if (query.offset >= 0) {
      dscRegisters.skip(query.offset);
    }

    if (query.limit) {
      dscRegisters.take(query.limit);
    }
    const sort = (typeof query?.sort === 'string') ? JSON.parse(query.sort) : query?.sort;
    if (sort?.column) {
      const columnMap: Record<string, string> = {
        holderName: 'dscRegister.holderName',
        expiryDate: 'dscRegister.expiryDate',
        daysexpiryDate: 'dscRegister.expiryDate',
        tokenNumber: 'dscRegister.tokenNumber',
      };
      const column = columnMap[sort.column] || sort.column;
      dscRegisters.orderBy(column, sort.direction.toUpperCase());
    } else {
      dscRegisters.orderBy('dscRegister.createdAt', 'DESC');
    };

    const [data, totalCount] = await dscRegisters.getManyAndCount();

    return {
      data,
      totalCount
    };
  }
  async exportClientDscReport(userId, query: FindDscRegisterDto) {
    const dscregisters = await this.find(userId, query);

    if (!dscregisters.data.length) {
      throw new BadRequestException('No Data for Export');
    }

    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('DSC Register');
    // Define headers for the worksheet
    const headers = [
      { header: 'S.No', key: 'serialNo' },
      { header: 'DSC Token #', key: 'tokenNumber' },
      { header: 'DSC Holder', key: 'holderName' },
      { header: 'Token Password', key: 'password' },
      { header: 'Certificate Expiry Date', key: 'expiryDate' },
      { header: 'Days Left to Expiry', key: 'daysLeft' },
      { header: 'Token Last Issued Date', key: 'issuedDate' },
      { header: 'Token Last Received Date', key: 'receivedDate' },
      { header: 'PAN', key: 'panNumber' },
      { header: 'Mobile #', key: 'mobileNumber' },
      { header: 'Email ID', key: 'email' },
    ];

    worksheet.columns = headers;
    let serialCounter = 1; // Initialize a counter
    const formatDate = (dateString) => {
      if (!dateString || isNaN(new Date(dateString).getTime())) {
        return ""; // Return empty if the dateString is invalid or not provided
      }
      const date = new Date(dateString);
      const day = date.getDate().toString().padStart(2, '0');
      const month = (date.getMonth() + 1).toString().padStart(2, '0');
      const year = date.getFullYear();
      return `${day}-${month}-${year}`;
    };
    // Helper functions


    const calculateDaysLeft = (expiryDate: string): string => {
      if (!expiryDate || isNaN(new Date(expiryDate).getTime())) return 'No Expiry Date';
      const currentDate = new Date();
      const expiry = new Date(expiryDate);
      const daysLeft = Math.floor((expiry.getTime() - currentDate.getTime()) / (1000 * 60 * 60 * 24)) + 1;

      if (daysLeft > 0) return `${daysLeft} ${daysLeft === 1 ? 'Day' : 'Days'} Left`;
      if (daysLeft === 0) return 'Expiring Today';
      return `Expired (${Math.abs(daysLeft)} ${Math.abs(daysLeft) === 1 ? 'day' : 'days'})`;
    };
    const formatDated = (dateString) => {
      if (!dateString || isNaN(new Date(dateString).getTime())) return '';
      const date = new Date(dateString);
      return `${date.getDate().toString().padStart(2, '0')}-${(date.getMonth() + 1)
        .toString()
        .padStart(2, '0')}-${date.getFullYear()}`;
    };


    // Map data to rows
    dscregisters.data.forEach((dscregister) => {
      const daysLeft = calculateDaysLeft(dscregister?.expiryDate);

      const row = worksheet.addRow({
        serialNo: serialCounter++,// Assign and then increment the counter
        tokenNumber: dscregister?.tokenNumber || ' ',
        holderName: dscregister?.holderName || ' ',
        password: dscregister?.password || ' ',
        expiryDate: formatDated(dscregister?.expiryDate) || ' ',
        daysLeft,
        issuedDate: formatDate(dscregister?.issuedDate) || ' ',
        receivedDate: formatDate(dscregister?.receivedDate) || ' ',
        panNumber: dscregister?.panNumber || ' ',
        mobileNumber: dscregister?.mobileNumber || ' ',
        email: dscregister?.email || ' ',
      });

      if (daysLeft.includes('Expired')) {
        row.getCell('daysLeft').font = { color: { argb: 'FF0000' } }; // Red color for expired
      }
    });

    // Style the header row
    const headerRow = worksheet.getRow(1);
    headerRow.font = { bold: true };
    headerRow.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };

    // Apply background color only for actual headers
    headers.forEach((_, index) => {
      const cell = headerRow.getCell(index + 1);
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '64B5F6' },
      };
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' },
      };
    });
    // Adjust column widths dynamically
    worksheet.columns.forEach((column) => {
      let maxLength = column.header.length;
      column.eachCell({ includeEmpty: true }, (cell) => {
        const cellValue = cell.value || '';
        maxLength = Math.max(maxLength, cellValue.toString().length);
      });
      column.width = maxLength + 2; // Add padding
      column.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
    });

    // Freeze the header row
    worksheet.views = [{ state: 'frozen', ySplit: 1 }];

    // Generate the Excel file as a buffer
    const buffer = await workbook.xlsx.writeBuffer();
    return buffer;
  }


  async getClientgroupDscRegistersexport(userId, query: FindDscRegisterDto) {
    const dscregisters = await this.find(userId, query);

    if (!dscregisters.data.length) {
      throw new BadRequestException('No Data for Export');
    }

    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('DSC Register');
    // Define headers for the worksheet
    const headers = [
      { header: 'S.No', key: 'serialNo' },
      { header: 'Client / Client Group', key: 'clientGroup' },
      { header: 'DSC Token #', key: 'tokenNumber' },
      { header: 'DSC Holder', key: 'holderName' },
      { header: 'Token Password', key: 'password' },
      { header: 'Certificate Expiry Date', key: 'expiryDate' },
      { header: 'Days Left to Expiry', key: 'daysLeft' },
      { header: 'Token Last Issued Date', key: 'issuedDate' },
      { header: 'Token Last Received Date', key: 'receivedDate' },
      { header: 'PAN', key: 'panNumber' },
      { header: 'Mobile #', key: 'mobileNumber' },
      { header: 'Email ID', key: 'email' },
    ];

    worksheet.columns = headers;
    let serialCounter = 1; // Initialize a counter

    // Helper functions
    const formatDate = (dateString) => {
      if (!dateString || isNaN(new Date(dateString).getTime())) return '';
      const date = new Date(dateString);
      let serialCounter = 1; // Initialize a counter
      const day = date.getDate().toString().padStart(2, '0');
      const month = (date.getMonth() + 1).toString().padStart(2, '0');
      const year = date.getFullYear();
      const hours = date.getHours().toString().padStart(2, '0');
      const minutes = date.getMinutes().toString().padStart(2, '0');

      return `${day}-${month}-${year} ${hours}:${minutes}`;
    };
    const formatDated = (dateString) => {
      if (!dateString || isNaN(new Date(dateString).getTime())) return '';
      const date = new Date(dateString);
      return `${date.getDate().toString().padStart(2, '0')}-${(date.getMonth() + 1)
        .toString()
        .padStart(2, '0')}-${date.getFullYear()}`;
    };


    const calculateDaysLeft = (expiryDate: string): string => {
      if (!expiryDate || isNaN(new Date(expiryDate).getTime())) return 'No Expiry Date';
      const currentDate = new Date();
      const expiry = new Date(expiryDate);
      const daysLeft = Math.floor((expiry.getTime() - currentDate.getTime()) / (1000 * 60 * 60 * 24)) + 1;

      if (daysLeft > 0) return `${daysLeft} ${daysLeft === 1 ? 'Day' : 'Days'} Left`;
      if (daysLeft === 0) return 'Expiring Today';
      return `Expired (${Math.abs(daysLeft)} ${Math.abs(daysLeft) === 1 ? 'day' : 'days'})`;
    };


    dscregisters.data.forEach((dscregister) => {
      const clientGroupData = [
        ...dscregister?.clients?.map((client: any) => client?.displayName) ?? [],
        ...dscregister?.clientGroups?.map((clientGroup: any) => clientGroup?.displayName) ?? [],
      ];

      const clientGroupString = clientGroupData.join(", ") || " ";
      const daysLeft = calculateDaysLeft(dscregister?.expiryDate);

      const row = worksheet.addRow({
        serialNo: serialCounter++,// Assign and then increment the counter
        clientGroup: clientGroupString || ' ',
        tokenNumber: dscregister?.tokenNumber || ' ',
        holderName: dscregister?.holderName || ' ',
        password: dscregister?.password || ' ',
        expiryDate: formatDated(dscregister?.expiryDate) || ' ',
        daysLeft,
        issuedDate: formatDate(dscregister?.issuedDate) || ' ',
        receivedDate: formatDate(dscregister?.receivedDate) || ' ',
        panNumber: dscregister?.panNumber || ' ',
        mobileNumber: dscregister?.mobileNumber || ' ',
        email: dscregister?.email || ' ',
      });

      if (daysLeft.includes('Expired')) {
        row.getCell('daysLeft').font = { color: { argb: 'FF0000' } }; // Red color for expired
      }
    });

    // Style the header row
    const headerRow = worksheet.getRow(1);
    headerRow.font = { bold: true };
    headerRow.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };

    // Apply background color only for actual headers
    headers.forEach((_, index) => {
      const cell = headerRow.getCell(index + 1);
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '64B5F6' },
      };
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' },
      };
    });

    // Adjust column widths dynamically
    worksheet.columns.forEach((column) => {
      let maxLength = column.header.length;
      column.eachCell({ includeEmpty: true }, (cell) => {
        const cellValue = cell.value || '';
        maxLength = Math.max(maxLength, cellValue.toString().length);
      });
      column.width = maxLength + 2; // Add padding
      column.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
    });

    // Freeze the header row
    worksheet.views = [{ state: 'frozen', ySplit: 1 }];

    // Generate the Excel file as a buffer
    const buffer = await workbook.xlsx.writeBuffer();
    return buffer;
  }

  async update(id: number, body: UpdateDscRegisterDto, userId: number) {
    let user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
    let dscRegister = await DscRegister.findOne({ where: { id }, relations: ['clients', 'clientGroups'] });
    let newClientGroups: boolean = false;
    let removeClientGroups: boolean = false;
    let removedGroup: null | ClientGroup = null;
    if (body.clientGroup) {
      if (dscRegister?.clientGroups?.length) {
        if (body.clientGroup && dscRegister.clientGroups[0].id !== body.clientGroup) {
          const newClientGroup = await ClientGroup.find({
            where: {
              id: body.clientGroup
            }
          });
          newClientGroups = true;
          removeClientGroups = true;
          removedGroup = dscRegister.clientGroups[0]
          dscRegister.clientGroups = newClientGroup
        }

      } else {
        const newClientGroup = await ClientGroup.find({
          where: {
            id: body.clientGroup
          }
        });
        dscRegister.clientGroups = newClientGroup
        newClientGroups = true;
      }
    } else if (dscRegister.clientGroups[0]?.id && !body.clientGroup) {
      removedGroup = dscRegister.clientGroups[0]
      dscRegister.clientGroups = null;
      removeClientGroups = true
    }
    if (dscRegister.holderName !== body?.holderName) {
      const dscHolderName = body?.holderName?.trim();
      if (!(' ' + body.holderName)?.trim().length) {
        throw new BadRequestException('Invalid DSC Holder Name');
      }
      let existingUser = await createQueryBuilder(DscRegister, 'dscRegister')
        .leftJoin('dscRegister.organization', 'organization')
        .where('organization.id = :organization', { organization: user.organization.id })
        .andWhere('(dscRegister.holderName = :dscHolderName)', {
          dscHolderName: dscHolderName,
        })
        .getOne();
      if (existingUser) {
        throw new BadRequestException(
          `DSC Holder Name Already Exists`,
        );
      }
    }
    if (body.password.trim() === "" || body.password.trim().length < 8) {
      throw new BadRequestException(
        `Invalid DSC Password`,
      );
    }
    dscRegister.holderName = body.holderName.trim();
    dscRegister.expiryDate = body.expiryDate;
    dscRegister.password = body.password.trim();
    dscRegister.tokenNumber = body.tokenNumber?.trim();
    dscRegister.panNumber = body.panNumber?.trim();
    dscRegister.holderDesignation = body.holderDesignation?.trim();;
    dscRegister.mobileNumber = body.mobileNumber.trim();
    dscRegister.email = body.email !== null && body.email !== undefined ? body.email.trim() : body.email;
    dscRegister.countryCode = body.countryCode;
    dscRegister['userId'] = user.id;
    await dscRegister.save();
    if (newClientGroups) {
      let activity = new Activity();
      activity.action = Event_Actions.DSC_RECORD_ADDED;
      activity.actorId = user.id;
      activity.type = ActivityType.CLIENT_GROUP;
      activity.typeId = dscRegister.clientGroups[0].id;
      activity.remarks = `"${dscRegister.holderName}" DSC Token Tagged by ${user.fullName}`;
      await activity.save();
    };
    if (removeClientGroups) {
      let activity = new Activity();
      activity.action = Event_Actions.DSC_RECORD_REMOVED;
      activity.actorId = user.id;
      activity.type = ActivityType.CLIENT_GROUP;
      activity.typeId = removedGroup.id;
      activity.remarks = `"${dscRegister.holderName}" DSC Token Tag Removed by ${user.fullName}`;
      await activity.save();
    }

    if (dscRegister?.clients?.length) {
      let activities = [];
      for (let client of dscRegister.clients) {
        let activity = new Activity();
        activity.action = Event_Actions.DSC_RECORD_UPDATED;
        activity.actorId = user.id;
        activity.type = ActivityType.CLIENT;
        activity.typeId = client.id;
        activity.remarks = `"${dscRegister.holderName}" DSC Record Updated by ${user.fullName}`;
        activities.push(activity)
      };
      await Activity.save(activities)
    };
    if (dscRegister?.clientGroups?.length && !newClientGroups) {
      let activity = new Activity();
      activity.action = Event_Actions.DSC_RECORD_UPDATED;
      activity.actorId = user.id;
      activity.type = ActivityType.CLIENT_GROUP;
      activity.typeId = dscRegister.clientGroups[0].id;
      activity.remarks = `"${dscRegister.holderName}" DSC Record Updated by ${user.fullName}`;
      await activity.save()
    };

    // if (dscRegister.client || dscRegister.clientGroup) {
    //   let activity = new Activity();
    //   activity.action = Event_Actions.DSC_RECORD_UPDATED;
    //   activity.actorId = user.id;
    //   activity.type = dscRegister.client ? ActivityType.CLIENT : ActivityType.CLIENT_GROUP;
    //   activity.typeId = dscRegister.client ? dscRegister.client.id : dscRegister.clientGroup.id;
    //   activity.remarks = `DSC Record "${dscRegister.holderName}" Updated by ${user.fullName}`;
    //   await activity.save();
    // }

    //Todo:dsc-expire send espo mail
    var expireDate = moment(dscRegister?.expiryDate);
    var currentDate = moment(new Date());

    var numberOfDaysLeft = currentDate.diff(expireDate, 'days');

    if (numberOfDaysLeft === 7) {
      console.log('equal', dscRegister);
    }

    return dscRegister;
  }

  async delete(id: number, userId) {
    let user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
    let dscRegister = await DscRegister.findOne({ where: { id }, relations: ['clients', 'clientGroups'] });
    await dscRegister.remove();
    // if (dscRegister.client) {
    //   let activity = new Activity();
    //   activity.action = Event_Actions.DSC_RECORD_DELETED;
    //   activity.actorId = user.id;
    //   activity.type = dscRegister.client ? ActivityType.CLIENT : ActivityType.CLIENT_GROUP;
    //   activity.typeId = dscRegister.client ? dscRegister.client.id : dscRegister.clientGroup.id;
    //   activity.remarks = `DSC Record "${dscRegister.holderName}" Deleted by ${user.fullName}`;
    //   await activity.save();
    // }
    return { success: true };
  }

  async bulkDelete(userId, body) {
    try {
      for (let i of body?.data?.dscRegister) {
        let dscRegister = await DscRegister.findOne({ where: { id: i.id }, relations: ['clients', 'clientGroups'] });
        await dscRegister.remove();
      }
      return { success: true };
    } catch (error) {
      throw new InternalServerErrorException(error);
    }
  }

  async deleteClients(userId: number, data: DeleteDscClientsDto) {
    const dscRegister = await DscRegister.findOne({
      where: {
        id: data.dsc
      },
      relations: ['clients']
    });
    if (!dscRegister) {
      throw new NotFoundException('Dsc Register not found');
    }
    let user = await User.findOne({ where: { id: userId } });
    const filterdClients = dscRegister.clients.filter((client) => !data.clients.includes(client.id));
    const removedClients = dscRegister.clients.filter((client) => data.clients.includes(client.id));
    dscRegister.clients = filterdClients;
    await dscRegister.save();


    if (removedClients.length) {
      let activites = [];
      for (let client of removedClients) {
        const activity = new Activity();
        activity.action = Event_Actions.DSC_RECORD_REMOVED;
        activity.actorId = user.id;
        activity.type = ActivityType.CLIENT;
        activity.typeId = client.id;
        activity.remarks = `"${dscRegister.holderName}" DSC Token Tag Removed by ${user.fullName}`;
        activites.push(activity)
      };
      await Activity.save(activites)
    }
    return dscRegister;
  };

  async addClients(userId: number, data: AddDscClientsDto) {
    let user = await User.findOne({ where: { id: userId } });
    const dscRegister = await DscRegister.findOne({
      where: {
        id: data.dsc
      },
      relations: ['clients']
    });
    const newClients = await Client.find({
      where: {
        id: In(data.clients)
      }
    });
    dscRegister.clients = [...dscRegister.clients, ...newClients];
    await dscRegister.save();
    if (newClients.length) {
      let activites = [];
      for (let client of newClients) {
        const activity = new Activity();
        activity.action = Event_Actions.DSC_RECORD_ADDED;
        activity.actorId = user.id;
        activity.type = ActivityType.CLIENT;
        activity.typeId = client.id;
        activity.remarks = `"${dscRegister.holderName}" DSC Token Tagged by ${user.fullName}`;
        activites.push(activity)
      };
      await Activity.save(activites);
      return dscRegister;
    }
  };

  async getOne(id: number, userId: number, query: any) {
    let user = await User.findOne({ where: { id: userId }, relations: ['organization'] });

    const dscRegisterQuery = await createQueryBuilder(DscRegister, 'dscRegister')
      .leftJoinAndSelect('dscRegister.dscActivity', 'dscActivity')
      .leftJoinAndSelect('dscRegister.clients', 'clients')
      .leftJoinAndSelect('dscRegister.clientGroups', 'clientGroups')
      .leftJoinAndSelect('dscRegister.organization', 'organization')
      .where('dscRegister.id = :id', { id })
      .andWhere('organization.id =:orgId', { orgId: user.organization.id });

    if (query.clientId) {
      dscRegisterQuery.andWhere('client.id =:clientId', { clientId: query.clientId })
    }

    let dscRegister = dscRegisterQuery.getOne();

    return dscRegister;
  }

  async issueOrReceive(userId: number, id: number, body: IssueOrReceiveDto) {
    let dscRegister = await DscRegister.findOne({ where: { id }, relations: ['clients', 'clientGroups'] });
    let dscActivity = new DscActivity();
    dscActivity.dscRegister = dscRegister;
    dscActivity.type = body.type;
    dscActivity.personName = body.personName;
    dscActivity.whatsappCheck = body.whatsappCheck;
    dscActivity.emailCheck = body.emailCheck
    dscActivity['userId'] = userId;
    await dscActivity.save();

    if (body.type === DscActivityTypeEnum.ISSUE) {
      dscRegister.status = DscRegisterStatus.ISSUED;
      dscRegister.issuedDate = new Date();
    }

    if (body.type === DscActivityTypeEnum.RECEIVE) {
      dscRegister.status = DscRegisterStatus.RECEIVED;
      dscRegister.receivedDate = new Date();
    }
    dscRegister['userId'] = userId;
    await dscRegister.save();

    // if (body.type === DscActivityTypeEnum.ISSUE && dscRegister?.client?.id) {
    //   this.eventEmitter.emit(Event_Actions.DSC_REGISTER_ISSUED, {
    //     clientId: dscRegister?.client?.id,
    //     clientGroupId: dscRegister?.clientGroup?.id,
    //     userId: userId,
    //     issuedTo: body.personName,
    //     dscRegister,
    //   });
    // }

    // if (body.type === DscActivityTypeEnum.RECEIVE && dscRegister?.client?.id) {
    //   this.eventEmitter.emit(Event_Actions.DSC_REGISTER_RECEIVED, {
    //     clientId: dscRegister?.client?.id,
    //     clientGroupId: dscRegister?.clientGroup?.id,
    //     userId: userId,
    //     receivedBy: body.personName,
    //     dscRegisterId: dscRegister.id,
    //   });
    // }

    return { success: true };
  }

  async findClients(userId: number) {
    let user = await User.findOne({
      where: { id: userId },
      relations: ['organization'],
    });
    let dscRegisters = await Client.find({
      where: { organization: { id: user.organization.id }, status: "ACTIVE" },
      relations: ['contactPersons'],
    });
    return dscRegisters;
  }


  async importDscRegisters(userId: number, body: any) {
    // console.log(body);
    // const result: DscRegister[] = [];
    let errorsArray = [];
    // let addedArray = 0;
    let notAddedDsc = 0;
    // let validateError = 0;
    const user = await User.findOne({ where: { id: userId }, relations: ['organization'] });

    if (body.length <= 0) {
      throw new BadRequestException('No Data For Import');
    }
    let count = 0;
    for (let register of body) {
      count += 1;
      let existingUser = await createQueryBuilder(DscRegister, 'dscRegister')
        .leftJoin('dscRegister.organization', 'organization')
        .where('organization.id = :organization', { organization: user.organization.id })
        .andWhere('(dscRegister.holderName = :dscHolderName)', {
          dscHolderName: String(register?.dscHolderName)?.trim(),
        })
        .getOne();
      // console.log({ existingUser });
      if (existingUser) {
        const errorDuplicate = `row ${count} duplicate data`;
        errorsArray = [...errorsArray, errorDuplicate];
        notAddedDsc += 1;
        continue;
      }
      const clients = await Client.find({
        where: {
          id: In(register.clients)
        }
      });
      let dscRegister = new DscRegister()
      dscRegister.holderName = String(register?.dscHolderName)?.trim();
      dscRegister.expiryDate = moment(register?.expiryDate, "DD-MM-YYYY").format("YYYY-MM-DD");
      dscRegister.password = String(register?.password)?.trim();
      dscRegister.tokenNumber = String(register?.dscTokenNumber)?.trim();
      dscRegister.panNumber = String(register?.pan)?.trim();
      dscRegister.holderDesignation = String(register?.dscHolderDesignation)?.trim();
      dscRegister.mobileNumber = String(register?.mobileNumber).toString().trim();
      dscRegister.email = register?.email !== null && register.email !== undefined ? register.email.trim() : register.email;
      dscRegister.countryCode = register?.countryCode;
      dscRegister.clients = clients;
      dscRegister.organization = user.organization;
      dscRegister['userId'] = user.id;
      await dscRegister.save();
    };

    errorsArray = [errorsArray, notAddedDsc]


    return errorsArray;

    // const user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
    // const workbook = xlsx.read(file.buffer, { cellDates: true });
    // const sheet = workbook.Sheets[workbook.SheetNames[0]];
    // const dataFromExcel = xlsx.utils.sheet_to_json(sheet);

    // const result: DscRegister[] = [];
    // let errorsArray = [];
    // let addedArray = 0;
    // let notAddedDsc = 0;
    // let validateError = 0;

    // if (dataFromExcel.length > 0) {
    //   let filteredData = dataFromExcel.filter((item) => {
    //     return (
    //       item['S.no'] ||
    //       item['Email ID'] ||
    //       item['Mobile Number'] ||
    //       item['DSC Holder Name *'] ||
    //       item['Expiry Date* (DD-MM-YYYY)'] ||
    //       item['Password *'] ||
    //       item['DSC Token Number'] ||
    //       item['PAN']
    //     );
    //   });

    //   for (const [index, item] of filteredData.entries()) {
    //     const data: any = item;
    //     validateError = 0;

    //     if (data.hasOwnProperty('DSC Holder Name *') && data['DSC Holder Name *'] != "" &&
    //       data.hasOwnProperty('Expiry Date* (DD-MM-YYYY)') && data['Expiry Date* (DD-MM-YYYY)'] != "" &&
    //       data.hasOwnProperty('Password *') && data['Password *'] != "" &&
    //       data.hasOwnProperty('Mobile Number *') && data['Mobile Number *'] != "" &&
    //       data.hasOwnProperty('Country Code *') && data['Country Code *'] != "") {

    //       let date = null;
    //       const dateStr = data['Expiry Date* (DD-MM-YYYY)'];

    //       if (typeof (dateStr) === "string" && dateStr !== null && dateStr !== undefined && dateStr !== "undefined") {
    //         const trimmedDateString = dateStr?.trim();
    //         date = moment(trimmedDateString).subtract(1, "day").format("YYYY-MM-DD");
    //       } else if (typeof (dateStr) === "object" && dateStr !== null && dateStr !== undefined && dateStr !== "undefined") {
    //         const dateObj = moment.utc(dateStr);
    //         const dateObjAdd = dateObj.add(1, 'days');
    //         date = moment(dateObjAdd).subtract(1, "day").format("YYYY-MM-DD")
    //       } else {
    //         const errorDuplicate = row ${index + 1} have Invalid Date;
    //         errorsArray = [...errorsArray, errorDuplicate];
    //         notAddedDsc += 1;
    //         continue;
    //       };

    //       const validateNumber = mobileWithCountry(data['Mobile Number *'], data['Country Code *']);
    //       if (validateNumber) {
    //         const errorDuplicate = row ${index + 1} have mobile number does not match the selected country.
    //         errorsArray = [...errorsArray, errorDuplicate];
    //         notAddedDsc += 1;
    //         continue;
    //       };
    //       const displayNamesArray = data['Client Name(s)'].split(',')
    //         .map((name) => name.trim());
    //       console.log({ displayNamesArray });
    //       const countryCode = countries.find(c => c.label === data['Country Code *']);
    //       const clients = await Client.find({
    //         where: {
    //           displayName: In(displayNamesArray),
    //           organization: user.organization.id
    //         }
    //       });


    //       const dscRegister = new ImportDscRegisterDto();
    //       dscRegister.holderName = (' ' + data['DSC Holder Name *'])?.trim();
    //       dscRegister.expiryDate = date
    //       dscRegister.password = (' ' + data['Password *'])?.trim();
    //       dscRegister.panNumber = (' ' + data['PAN'])?.trim() !== "undefined" ? (' ' + data['PAN'])?.trim() : "";
    //       dscRegister.email = (' ' + data['Email ID'])?.trim() !== "undefined" ? (' ' + data['Email ID'])?.trim() : null;
    //       dscRegister.mobileNumber = (' ' + data['Mobile Number *'])?.trim() !== "undefined" ? (' ' + data['Mobile Number *'])?.trim() : "";
    //       dscRegister.holderDesignation = (' ' + data['DSC Holder Designation'])?.trim() !== "undefined" ? (' ' + data['DSC Holder Designation'])?.trim() : "";
    //       dscRegister.tokenNumber = (' ' + data['DSC Token Number'])?.trim() !== "undefined" ? (' ' + data['DSC Token Number'])?.trim() : "";
    //       dscRegister.organization = user.organization;
    //       dscRegister.countryCode = countryCode.code;
    //       dscRegister.clients = clients;
    //       dscRegister['userId'] = user.id;

    //       const panNumber = (' ' + data['PAN'])?.trim();
    //       const dscHolderName = (' ' + data['DSC Holder Name *'])?.trim();

    //       let existingUser = await createQueryBuilder(DscRegister, 'dscRegister')
    //         .leftJoin('dscRegister.organization', 'organization')
    //         .where('organization.id = :organization', { organization: user.organization.id })
    //         .andWhere('(dscRegister.holderName = :dscHolderName OR  dscRegister.panNumber = :panNumber)', {
    //           dscHolderName: dscHolderName,
    //           panNumber: panNumber,
    //         })
    //         .getOne();

    //       let duplicated = result.findIndex((items) => {
    //         if (dscHolderName !== "undefined") {
    //           if (panNumber !== "undefined") {
    //             return items.holderName === dscHolderName || items.panNumber === panNumber;
    //           } else {
    //             return items.holderName === dscHolderName;
    //           }
    //         } else {
    //           return false;
    //         }
    //       });

    //       if (duplicated > -1) {
    //         // throw new BadRequestException(
    //         //   row ${duplicated + 1} and row ${index + 1} have duplicate data,
    //         // );
    //         const errorDuplicate = row ${duplicated + 1} and row ${index + 1} have duplicate data;
    //         errorsArray = [...errorsArray, errorDuplicate];
    //         notAddedDsc += 1;
    //         continue;
    //       }

    //       await validate(dscRegister).then((errors) => {
    //         if (errors.length > 0) {
    //           console.log('validation failed. errors: ', errors);
    //           // throw new BadRequestException(
    //           //   Invalid ${_.startCase(errors[0].property)} in row ${index + 1},
    //           // );
    //           const errorDuplicate = Invalid ${errors[0].property} in row ${index + 1};
    //           errorsArray = [...errorsArray, errorDuplicate];
    //           notAddedDsc += 1;
    //           validateError = 1;
    //         }
    //         //  else {
    //         //   console.log("errors.length",errors.length)
    //         //   result.push({ ...client } as Client);
    //         // }
    //       });
    //       if (validateError) {
    //         continue;
    //       }

    //       if (!existingUser) {
    //         result.push({ ...dscRegister } as DscRegister);
    //       }
    //     } else {
    //       // throw new BadRequestException(
    //       //   Improper DSC details, Please upload as per sample sheet,
    //       // );
    //       const errorDuplicate = Row ${index + 1} have Improper DSC details, Mandatory Fields Missing.;
    //       errorsArray = [...errorsArray, errorDuplicate];
    //       notAddedDsc += 1;
    //       continue;
    //     }
    //   }

    //   if (result.length > 0) {
    //     try {
    //       for (const dscregister of result) {
    //         addedArray += 1;
    //         await DscRegister.save(dscregister);
    //       }
    //       errorsArray = [...errorsArray, notAddedDsc, addedArray];
    //       return errorsArray;
    //     } catch (error) {
    //       console.log(error);
    //       throw new InternalServerErrorException(error);
    //     }
    //   } else {
    //     // throw new BadRequestException(
    //     //   Atleast one row required as per sample sheet,
    //     // );
    //     const errorone = All Dsc's are already Added and Atleast one new row required as per sample sheet
    //     errorsArray = [...errorsArray, errorone, notAddedDsc, addedArray];
    //     return errorsArray;
    //   }
    // } else {
    //   throw new BadRequestException(
    //     Atleast one row required as per sample sheet,
    //   );
    // }
  }


  async applyDsc(userId: number, body) {

    let applyDsc = new DscApply();
    let user = await User.findOne({ where: { id: userId }, relations: ['organization'] });

    let storage: Storage;
    if (body?.aadharStorage) {
      if (applyDsc?.dscAadhar?.id) {
        storage = await Storage.findOne({ where: { id: applyDsc?.dscAadhar?.id } });
        storage.fileType = body.aadharStorage.fileType;
        storage.fileSize = body.aadharStorage.fileSize;
        storage.name = body.aadharStorage.name;
        storage.file = body.aadharStorage.upload;
        storage.show = false;
        applyDsc.dscAadhar = storage;
      } else {
        const storage = await this.storageService.addAttachements(userId, { ...body?.aadharStorage, show: false });
        applyDsc.dscAadhar = storage;
      }
    }
    if (body?.panStorage) {
      if (applyDsc?.dscPan?.id) {
        storage = await Storage.findOne({ where: { id: applyDsc?.dscPan?.id } });
        storage.fileType = body.panStorage.fileType;
        storage.fileSize = body.panStorage.fileSize;
        storage.name = body.panStorage.name;
        storage.file = body.panStorage.upload;
        storage.show = false;
        applyDsc.dscPan = storage;
      } else {
        const storage = await this.storageService.addAttachements(userId, { ...body?.panStorage, show: false });
        applyDsc.dscPan = storage;
      }
    }
    if (body?.photoStorage) {
      if (applyDsc?.dscPhoto?.id) {
        storage = await Storage.findOne({ where: { id: applyDsc?.dscPhoto?.id } });
        storage.fileType = body.photoStorage.fileType;
        storage.fileSize = body.photoStorage.fileSize;
        storage.name = body.photoStorage.name;
        storage.file = body.photoStorage.upload;
        storage.show = false;
        applyDsc.dscPhoto = storage;
      } else {
        const storage = await this.storageService.addAttachements(userId, { ...body?.photoStorage, show: false });
        applyDsc.dscPhoto = storage;
      }
    }

    applyDsc.applicantName = body.applicantName;
    applyDsc.mobileNumber = body.mobileNumber;
    applyDsc.applicationStatus = body.applicationStatus;
    applyDsc.selectedType = body.selectedType;
    applyDsc.email = body.email;
    applyDsc.certificateType = body.certificateType;
    applyDsc.certificateValidity = body.certificateValidity;
    applyDsc.organization = user.organization;

    await applyDsc.save();
    return applyDsc;
  }

  async getAppliedDsc(userId: number) {
    let user = await User.findOne({
      where: { id: userId },
      relations: ['organization'],
    });
    let appliedDSC = await DscApply.find({
      where: { organization: { id: user.organization.id } }, relations: ['organization', 'dscAadhar', 'dscPan', 'dscPhoto'],
      order: { createdAt: 'DESC' }
    });
    return appliedDSC;
  }


  async getAppliedDscAdmin(userId: number) {
    let user = await User.findOne({
      where: { id: userId },
      relations: ['organization'],
    });
    let appliedDSC = await DscApply.find({
      relations: ['organization', 'dscAadhar', 'dscPan', 'dscPhoto']
    });
    return appliedDSC;
  }

  async getOneAppliedDsc(dscId: number) {
    let appliedDSC = await DscApply.findOne({ where: { id: dscId }, relations: ['organization', 'dscAadhar', 'dscPan', 'dscPhoto'] })
    return appliedDSC;
  }

  async updateApplyDsc(id: number, body: any) {
    const dsc = await DscApply.findOne({ where: { id } })
    if (dsc) {
      dsc.id = body.id,
        dsc.applicationStatus = body.applicationStatus
      dsc.save()
    }
  }
}
