import {
  Body,
  Controller,
  Get,
  Post,
  Query,
  Req,
  UploadedFile,
  UseGuards,
  UseInterceptors,
  Request,
  Delete,
  Param,
  ParseIntPipe,
} from '@nestjs/common';
import { JwtAuthGuard } from 'src/modules/users/jwt/jwt-auth.guard';
import FindOneDriveStorageDto from './find-onedrive-storage.dto';
import { OneDriveStorageService } from './onedrive-storage.service';
import { FileInterceptor } from '@nestjs/platform-express';
import { IUploadBody } from '../storage/storage.controller';

@Controller('onedrive')
export class OneDriveStorageController {
  constructor(private readonly service: OneDriveStorageService) { }

  @UseGuards(JwtAuthGuard)
  @Get()
  getItems(@Req() req: any, @Query() query: FindOneDriveStorageDto) {
    const { userId } = req.user;
    return this.service.getItems(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Post('/upload-file')
  @UseInterceptors(FileInterceptor('file'))
  attachementsUpload(
    @UploadedFile() file: Express.Multer.File,
    @Body() body: IUploadBody,
    @Request() req: any,
  ) {
    const { userId } = req.user;
    return this.service.attachementsUpload({ file, body, userId });
  };


  @UseGuards(JwtAuthGuard)
  @Post('/upload-files')
  @UseInterceptors(FileInterceptor('file'))
  uploadFile(
    @UploadedFile() file: Express.Multer.File,
    @Body() body: IUploadBody,
    @Request() req: any,
  ) {
    const { userId } = req.user;
    return this.service.uploadFile({ file, body, userId });
  };

  @UseGuards(JwtAuthGuard)
  @Post('save-token')
  saveToken(@Req() req: any, @Body() body: any) {
    const { userId } = req.user;
    return this.service.saveToken(userId, body);
  };

  @UseGuards(JwtAuthGuard)
  @Post('re-authorize')
  reAuthorize(@Req() req: any) {
    return this.service.reAuthorize();
  };


  //TODO:Create folder in One Drive

  @UseGuards(JwtAuthGuard)
  @Post('create-folder')
  createFolder(@Req() req: any) {
    const { userId } = req.user;
    return this.service.createOneDriveFolder(userId, "folderName");
  }



  //Todo: delete file from One drive
  @UseGuards(JwtAuthGuard)
  @Delete('remove-file/:id')
  deleteFile(@Req() req: any, @Param('id') id: string) {
    const { userId } = req.user;
    return this.service.deleteOneDriveFile(userId, id);
  };

  @UseGuards(JwtAuthGuard)
  @Get('/storage-info')
  getOneDriveStorageInfo(@Req() req: any) {
    const { userId } = req.user;
    return this.service.getOneDriveStorageInfo(userId);
  }
}
