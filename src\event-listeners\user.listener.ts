import { Injectable } from '@nestjs/common';
import { OnEvent } from '@nestjs/event-emitter';
import { JwtService } from '@nestjs/jwt';
import email from 'src/emails/email';
import { Organization } from 'src/modules/organization/entities/organization.entity';
import { User } from 'src/modules/users/entities/user.entity';
import { sendNotification } from 'src/notifications/notify';
import notify from 'src/notifications/notify';
import { createQueryBuilder } from 'typeorm';
import { Event_Actions } from './actions';
import { Wallets } from 'src/modules/wallet/entities/wallets.entity';
import { sendWhatsAppTemplateMessage } from 'src/modules/whatsapp/whatsapp.service';
import countries from 'src/utils/countries';
import { fullMobileNumberWithCountry } from 'src/utils/validations/fullMobileWithCountry';
import { log } from 'console';

interface Event {
  data: any;
  orgId: number;
  invitedUserId: number;
  orgName: string;
  user: User;
  userId: number;
  email: string;
}

@Injectable()
export class UserListener {
  constructor(private jwtService: JwtService) { }

  @OnEvent(Event_Actions.ORG_REGISTERED, { async: true })
  async handleOrgSignup(event: Event) {
    try {
      const { email: Email, orgName, orgId } = event;
      // todo email convert to espo
      // await email.organizationSignup({ email: Email, organization: orgName });
    } catch (e) {
      console.error(e);
    }


  }

  @OnEvent(Event_Actions.USER_INVITED, { async: true })
  async handleUserInvite(event: Event) {
    try {
      const { data, orgId, invitedUserId, orgName } = event;
      let token = this.jwtService.sign({
        email: data.email,
        mobileNumber: data.mobileNumber,
        fullName: data.fullName,
        role: data.role,
        orgId: orgId,
        invitedUserId: invitedUserId,
        managerId: data.manager
      });
      await email.userInvited({
        email: data.email,
        name: data.fullName,
        link: `${process.env.WEBSITE_URL}/join?token=${token}`,
        organization: orgName,
      });

      const tempevent = JSON.parse(JSON.stringify(event));
      tempevent.link = `${process.env.WEBSITE_URL}/join?token=${token}`;

      //Whatsapp Notification
      const title = 'invitation link';
      // const countryCode = countries.find((c) => c.code === data?.countryCode);
      // const userPhoneNumber = `${countryCode?.phone}${data?.mobileNumber}`;
      const userPhoneNumber = fullMobileNumberWithCountry(data?.mobileNumber, data?.countryCode);
      const templateName = 'invitation_link_to_users_1';
      const userName = data?.fullName;
      const buttonLink = `join?token=${token}`;
      const whatsappOptions = {
        to: userPhoneNumber,
        name: templateName,
        header: [
          {
            type: 'text',
            text: userName,
          },
        ],
        orgId,
        title,
        body: [],
        button: buttonLink,
      };
      await sendWhatsAppTemplateMessage(whatsappOptions);
    } catch (e) {
      console.error(e);
    }
  }

  @OnEvent(Event_Actions.USER_RE_INVITED, { async: true })
  async handleUserReInvite(event: Event) {
    try {
      const { data, orgId, invitedUserId, orgName } = event;
      let token = this.jwtService.sign({
        email: data.email,
        mobileNumber: data.mobileNumber,
        fullName: data.fullName,
        role: data.role,
        orgId: orgId,
        invitedUserId: invitedUserId,
        managerId: data?.manager?.id,
      });
      await email.userReInvited({
        email: data.email,
        name: data.fullName,
        link: `${process.env.WEBSITE_URL}/join?token=${token}`,
        organization: orgName,
      });

      //Whatsapp Notification
      const title = 'invitation link';

      const userPhoneNumber = fullMobileNumberWithCountry(data?.mobileNumber, data?.countryCode);
      // const userPhoneNumber = `91${data?.mobileNumber}`;
      const templateName = 'invitation_link_to_users_1';
      const userName = data?.fullName;
      const buttonLink = `join?token=${token}`;
      const whatsappOptions = {
        to: userPhoneNumber,
        name: templateName,
        header: [
          {
            type: 'text',
            text: userName,
          },
        ],
        orgId,
        title,
        body: [],
        button: buttonLink,
      };
      await sendWhatsAppTemplateMessage(whatsappOptions);
    } catch (e) {
      console.error(e);
    }
  }

  @OnEvent(Event_Actions.USER_JOINED, { async: true })
  async handleUserJoin(event: Event) {
    try {
      const { user, orgId } = event;
      let org = await createQueryBuilder(Organization, 'org')
        .leftJoinAndSelect('org.users', 'users')
        .leftJoinAndSelect('users.role', 'role')
        .where('org.id = :id', { id: orgId })
        .getOne();

      let orgAdmin = org.users.find((user) => user.role?.defaultRole);

      if (!orgAdmin) return;

      let notification = {
        title: `New member has joined your organization`,
        body: `${user.fullName} has joined your organization`,
      };

      // await sendNotification([orgAdmin.id], notification);
    } catch (e) {
      console.error(e);
    }
  }

  @OnEvent(Event_Actions.ORGANIZATION_ADMIN_USER, { async: true })
  async handleOrgWallet(event: Event) {
    const user = event.user;
    let wallet = new Wallets();
    wallet.user = user;
    wallet.organization = user.organization;
    wallet.updatedBy = user.id;
    await wallet.save();
  }
}
