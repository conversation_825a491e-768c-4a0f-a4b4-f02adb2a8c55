import {
    Connection,
    EntitySubscriberInterface,
    EventSubscriber,
    InsertEvent,
    Like,
    UpdateEvent,
} from 'typeorm';
import Expenditure from 'src/modules/expenditure/expenditure.entity';
import { User } from 'src/modules/users/entities/user.entity';
import { EXPENDITURE_STATUS, ExpenditureType } from 'src/modules/expenditure/dto/types';
import { insertINTONotificationUpdate } from 'src/utils/re-use';
import * as moment from 'moment'

let oldApprovalStatus: EXPENDITURE_STATUS;
@EventSubscriber()
export class ExpenditureSubscriber implements EntitySubscriberInterface<Expenditure> {
    constructor(private readonly connection: Connection) {
        connection.subscribers.push(this);
    }
    listenTo() {
        return Expenditure;
    }


    async beforeInsert(event: InsertEvent<Expenditure>) {
        const expnditure = event?.entity;
        if (!expnditure) return;

        const year = moment().year();
        const reqYear = year.toString().slice(-2);
        let prefix = "";

        if (expnditure.type === ExpenditureType.GENERAL) {
            prefix = "GEXP";
        } else if (expnditure.type === ExpenditureType.TASK) {
            prefix = "TEXP";
        }

        if (prefix) {
            const latest = await Expenditure.createQueryBuilder("expenditure")
                .leftJoin('expenditure.user', 'user')
                .leftJoin('user.organization', 'organization')
                .where("expenditure.expenditureNumber LIKE :likePattern", {
                    likePattern: `${reqYear}${prefix}%`,
                })
                .andWhere("organization.id=:orgId", { orgId: event?.entity?.user.organization.id })
                .andWhere("expenditure.type = :type", { type: expnditure.type })
                .orderBy("expenditure.createdAt", "DESC")
                .getOne();

            let nextNumber = 1;

            if (latest?.expenditureNumber) {
                const match = latest.expenditureNumber.match(
                    new RegExp(`${reqYear}${prefix}(\\d+)`)
                );
                if (match) {
                    nextNumber = parseInt(match[1], 10) + 1;
                }
            }

            // Pad the number to 3 digits: 001, 002, ..., 010, 099, etc.
            const paddedNumber = String(nextNumber).padStart(3, '0');

            event.entity.expenditureNumber = `${reqYear}${prefix}${paddedNumber}`;
        }
    }





    async afterInsert(event: InsertEvent<Expenditure>) {
        let { approvalStatus, user, managers, type, particularName, task, client, clientGroup, amount, date } = event.entity;
        const clientName = client ? client?.displayName : clientGroup?.displayName;
        if (approvalStatus === EXPENDITURE_STATUS.PENDING) {
            const userDetails = await User.findOne({ where: { id: user.id }, relations: ['organization'] });
            let title: string;
            const key = 'EXPENDITURE_PUSH';
            const orgId = userDetails?.organization?.id;
            const users: any[] = managers.map(user => user.id);
            let managerBody: string;
            let employeeBody: string;
            if (type === ExpenditureType.TASK) {
                title = "Task Expenditure Approval Request";
                managerBody = `An expenditure request from <strong>${user.fullName}</strong> for <strong>${particularName}</strong> related to Task ID: <strong/>${task?.taskNumber}</strong>, <strong>${task?.name}</strong>, for <strong>${clientName}</strong>, with a value of ₹ <strong>${amount}</strong> dated <strong>${moment(date).format("DD-MM-YYYY")}</strong>, is pending your approval`;
                employeeBody = `Your expenditure request for <strong>${particularName}</strong> related to <strong>${task?.taskNumber}</strong>, <strong>${task?.name}</strong>, for <strong>${clientName}</strong>, with a value of ₹ <strong>${amount}</strong> dated <strong>${moment(date).format("DD-MM-YYYY")}</strong>, has been sent to your superiors for approval`;
            } else if (type === ExpenditureType.GENERAL) {
                title = "General Expenditure Approval Request";
                managerBody = `An expenditure request from <strong>${user.fullName}</strong> for <strong>${particularName}</strong>, with a value of ₹ <strong>${amount}</strong>, dated <strong>${moment(date).format("DD-MM-YYYY")}</strong>, is pending your approval`;
                employeeBody = `Your expenditure request for <strong>${particularName}</strong>, with a value of ₹ <strong>${amount}</strong>, dated <strong>${moment(date).format("DD-MM-YYYY")}</strong>, has been sent to your superiors for approval`;
            }


            //Managers
            insertINTONotificationUpdate(title, managerBody, users, orgId, key);
            //EMPLOYEE
            insertINTONotificationUpdate(title, employeeBody, [user.id as any], orgId, key);
        }
    }

    async beforeUpdate(event: UpdateEvent<Expenditure>) {
        oldApprovalStatus = event?.databaseEntity?.approvalStatus;

    }

    async afterUpdate(event: UpdateEvent<Expenditure>) {
        const { approvalStatus, managers, user, userId, type, particularName, task, client, clientGroup, amount, date } = event.entity;
        let title: string;
        const logInUser = await User.findOne({ where: { id: userId }, relations: ['organization'] });
        const orgId = logInUser?.organization?.id;
        const key = 'EXPENDITURE_PUSH';
        const clientName = client ? client?.displayName : clientGroup?.displayName;
        if (oldApprovalStatus == EXPENDITURE_STATUS.PENDING && approvalStatus == EXPENDITURE_STATUS.REJECTED) {

            let userBody: string;
            let employeeBody: string;
            let managerBody: string;
            if (type === ExpenditureType.TASK) {
                title = "Task Expenditure Rejected";
                userBody = `You have rejected <strong>${user.fullName}</strong>'s expenditure request for <strong>${particularName}</strong> related to Task ID: <strong>${task?.taskNumber}</strong>, <strong>${task?.name}</strong>, for <strong>${clientName}</strong>, with a value of ₹ <strong>${amount}</strong> dated <strong>${moment(date).format("DD-MM-YYYY")}</strong>`;
                employeeBody = `Your expenditure request for <strong>${particularName}</strong> related to Task ID: <strong>${task?.taskNumber}</strong>, <strong>${task?.name}</strong>, for <strong>${clientName}</strong>, with a value of ₹ <strong>${amount}</strong> dated <strong>${moment(date).format("DD-MM-YYYY")}</strong>, has been rejected by <strong>${logInUser.fullName}</strong>`;
                managerBody = `<strong>${logInUser.fullName}</strong> has rejected <strong>${user.fullName}</strong>'s expenditure request for <strong>${particularName}</strong> related to Task ID: <strong>${task?.taskNumber}</strong>, <strong>${task?.name}</strong>, for <strong>${clientName}</strong>, with a value of ₹ <strong>${amount}</strong> dated <strong>${moment(date).format("DD-MM-YYYY")}</strong>`;


            } else if (type === ExpenditureType.GENERAL) {
                title = "General Expenditure Rejected";
                userBody = `You have rejected <strong>${user.fullName}</strong>'s expenditure request for <strong>${particularName}</strong>, with a value of ₹ <strong>${amount}</strong>, dated <strong>${moment(date).format("DD-MM-YYYY")}</strong>`;
                employeeBody = `Your expenditure request for <strong>${particularName}</strong>, with a value of ₹ <strong>${amount}</strong>, dated <strong>${moment(date).format("DD-MM-YYYY")}</strong>, has been rejected by <strong>${logInUser.fullName}</strong>`;
                managerBody = `<strong>${logInUser.fullName}</strong> has rejected <strong>${user.fullName}</strong>'s expenditure request for <strong>${particularName}</strong>, with a value of ₹ <strong>${amount}</strong>, dated <strong>${moment(date).format("DD-MM-YYYY")}</strong>`;
            }

            //Login User
            insertINTONotificationUpdate(title, userBody, [logInUser.id as any], orgId, key);
            //Employee
            insertINTONotificationUpdate(title, employeeBody, [user.id as any], orgId, key);
            //Managers
            const managerUsers: any[] = managers.map(user => user.id).filter(id => id != userId);
            insertINTONotificationUpdate(title, managerBody, managerUsers, orgId, key);
        } else if (oldApprovalStatus == EXPENDITURE_STATUS.PENDING && approvalStatus == EXPENDITURE_STATUS.APPROVED) {


            let userBody: string;
            let employeeBody: string;
            let managerBody: string;
            if (type === ExpenditureType.TASK) {
                title = "Task Expenditure Approved";
                userBody = `You have approved <strong>${user.fullName}</strong>'s expenditure request for <strong>${particularName}</strong> related to Task ID: <strong>${task?.taskNumber}</strong>, <strong>${task?.name}</strong>, for <strong>${clientName}</strong>, with a value of ₹ <strong>${amount}</strong> dated <strong>${moment(date).format("DD-MM-YYYY")}</strong>`;
                employeeBody = `Your expenditure request for <strong>${particularName}</strong> related to Task ID: <strong>${task?.taskNumber}</strong>, <strong>${task?.name}</strong>, for <strong>${clientName}</strong>, with a value of ₹ <strong>${amount}</strong> dated <strong>${moment(date).format("DD-MM-YYYY")}</strong>, has been approved by <strong>${logInUser.fullName}</strong>`;
                managerBody = `<strong>${logInUser.fullName}</strong> has approved <strong>${user.fullName}</strong>'s expenditure request for <strong>${particularName}</strong> related to Task ID: <strong>${task?.taskNumber}</strong>, <strong>${task?.name}</strong>, for <strong>${clientName}</strong>, with a value of ₹ <strong>${amount}</strong> dated <strong>${moment(date).format("DD-MM-YYYY")}</strong>`;

            } else if (type === ExpenditureType.GENERAL) {
                title = "General Expenditure Approved";
                userBody = `You have approved <strong>${user.fullName}</strong>'s expenditure request for <strong>${particularName}</strong>, with a value of ₹ <strong>${amount}</strong>, dated <strong>${moment(date).format("DD-MM-YYYY")}</strong>`;
                employeeBody = `Your expenditure request for <strong>${particularName}</strong>, with a value of ₹ <strong>${amount}</strong>, dated <strong>${moment(date).format("DD-MM-YYYY")}</strong>, has been approved by <strong>${logInUser.fullName}</strong>`;
                managerBody = `<strong>${logInUser.fullName}</strong> has approved <strong>${user.fullName}</strong>'s expenditure request for <strong>${particularName}</strong>, with a value of ₹ <strong>${amount}</strong>, dated <strong>${moment(date).format("DD-MM-YYYY")}</strong>`;

            }

            //Login User
            insertINTONotificationUpdate(title, userBody, [logInUser.id as any], orgId, key);

            //Employee
            insertINTONotificationUpdate(title, employeeBody, [user.id as any], orgId, key);

            //Managers
            const managerUsers: any[] = managers.map(user => user.id).filter(id => id != userId);
            insertINTONotificationUpdate(title, managerBody, managerUsers, orgId, key);

        } else if (oldApprovalStatus == EXPENDITURE_STATUS.REJECTED && approvalStatus == EXPENDITURE_STATUS.PENDING) {
            let body: string;
            let managerBody: string;

            if (type === ExpenditureType.TASK) {
                title = "Task Expenditure Approval Request";
                body = `Your modified expenditure request for <strong>${particularName}</strong> related to Task ID: <strong>${task?.taskNumber}</strong>, <strong>${task?.name}</strong>, for <strong>${clientName}</strong>, with a value of ₹ <strong>${amount}</strong> dated <strong>${moment(date).format("DD-MM-YYYY")}</strong>, has been sent to your superiors for approval`;
                managerBody = `<strong>${user.fullName}</strong> has resubmitted their expenditure request for <strong>${particularName}</strong> related to Task ID: <strong>${task?.taskNumber}</strong>, <strong>${task?.name}</strong>, for <strong>${clientName}</strong>, with a value of ₹ <strong>${amount}</strong> dated <strong>${moment(date).format("DD-MM-YYYY")}</strong>, with modifications for your approval`;

            } else if (type === ExpenditureType.GENERAL) {
                title = "General Expenditure Approval Request";
                body = `Your modified expenditure request for <strong>${particularName}</strong>, with a value of ₹ <strong>${amount}</strong>, dated <strong>${moment(date).format("DD-MM-YYYY")}</strong>, has been sent to your superiors for approval`;
                managerBody = `<strong>${user.fullName}</strong> has resubmitted their expenditure request for <strong>${particularName}</strong>, with a value of ₹ <strong>${amount}</strong>, dated <strong>${moment(date).format("DD-MM-YYYY")}</strong>, with modifications for your approval`;

            }
            //Login User
            insertINTONotificationUpdate(title, body, [user.id as any], orgId, key);
            //Managers
            const managerUsers: any[] = managers.map(user => user.id).filter(id => id != userId);
            insertINTONotificationUpdate(title, managerBody, managerUsers, orgId, key);
        }

    }
}