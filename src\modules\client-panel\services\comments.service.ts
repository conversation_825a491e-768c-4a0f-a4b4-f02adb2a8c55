import { Injectable } from '@nestjs/common';
import TaskComment from 'src/modules/tasks/entity/comment.entity';

@Injectable()
export class CommentsServie {
  async findComments(id: number) {
    const data = await TaskComment.find({
      where: {
        task: { id },
      },
      relations: ['user', 'task'],
    });

    // Sort all data by createdAt descending (latest first)
    data.sort(
      (a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
    );

    const comments = data.filter((comment) => comment.parentId === null);
    const replies = data.filter((comment) => comment.parentId !== null);

    const replyMap = new Map<number, TaskComment[]>();
    for (const reply of replies) {
      if (!replyMap.has(reply.parentId)) {
        replyMap.set(reply.parentId, []);
      }
      replyMap.get(reply.parentId).push(reply);
    }

    const result = comments.map((comment) => ({
      ...comment,
      replies: replyMap.get(comment.id) || [],
    }));

    return result;
  }
}
