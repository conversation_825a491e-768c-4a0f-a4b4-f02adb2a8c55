import { BaseEntity, Column, <PERSON>tity, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, OneToOne, PrimaryGeneratedColumn } from 'typeorm';
import { User } from '../users/entities/user.entity';
import { Optional } from '@nestjs/common';

@Entity({ name: 'notification_preferences' })
class NotificationPreferences extends BaseEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  organization_id: number;

  @OneToOne(() => User, { cascade: true })
  @JoinColumn()
  user: User;

  @Optional()
  @Column('json')
  email: string;

  @Optional()
  @Column('json')
  push: string;

  @Optional()
  @Column('json')
  whatsapp: string;

  @Optional()
  @Column('boolean')
  whatsappConfig: boolean;
}


export default NotificationPreferences;
