import { IsDateString, IsEnum, IsOptional } from 'class-validator';
import { BillableType } from 'src/modules/reports/dto/get-employee-log-hours-report';

export class getBulkTasks {
  @IsOptional()
  category: number;

  @IsOptional()
  subCategory: number;

  @IsOptional()
  @IsDateString()
  fromDate: string;

  @IsOptional()
  @IsDateString()
  toDate: string;

  @IsOptional()
  @IsDateString()
  dueFromDate: string;

  @IsOptional()
  @IsDateString()
  dueToDate: string;

  @IsOptional()
  @IsDateString()
  completionFromDate: string;

  @IsOptional()
  @IsDateString()
  completionToDate: string;

  @IsOptional()
  @IsDateString()
  createdFromDate: string;

  @IsOptional()
  @IsDateString()
  createdToDate: string;

  @IsOptional()
  client: number;

  @IsOptional()
  clientType: string;

  @IsOptional()
  status: string;

  @IsOptional()
  taskServiceType: string;

  @IsOptional()
  frequency: string;

  @IsOptional()
  priority: string;

  @IsOptional()
  financialYear: string;

  @IsOptional()
  members: Array<number>;

  @IsOptional()
  taskLeader: number | string;

  @IsOptional()
  approvalUser: number | string;

  @IsOptional()
  @IsEnum(BillableType)
  billingType: BillableType;

  @IsOptional()
  taskType: Array<'recurring' | 'non_recurring'>;

  @IsOptional()
  dateType: string;

  @IsOptional()
  createdBy:string | number;

  @IsOptional()
  createdOn:string;

  @IsOptional()
  service:number;

  @IsOptional()
  offset:number;

  @IsOptional()
  limit:number;

}
