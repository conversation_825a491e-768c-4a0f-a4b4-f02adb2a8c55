import {
  Connection,
  EntitySubscriberInterface,
  EventSubscriber,
  UpdateEvent,
  getManager,
} from 'typeorm';
import { UserProfile } from 'src/modules/users/entities/user-profile.entity';
import { getAdminIds, insertINTOnotification } from 'src/utils/re-use';


@EventSubscriber()
export class UserProfileSubscriber implements EntitySubscriberInterface<UserProfile> {
  constructor(private readonly connection: Connection) {
    connection.subscribers.push(this);
  }

  listenTo() {
    return UserProfile;
  }

  async beforeUpdate(event: UpdateEvent<UserProfile>) {
  }

  async afterUpdate(event: UpdateEvent<UserProfile>) {
  }
}
