import { Optional } from '@nestjs/common';
import Client from 'src/modules/clients/entity/client.entity';
import { Organization } from 'src/modules/organization/entities/organization.entity';
import {
  BaseEntity,
  Column,
  CreateDateColumn,
  Entity,
  ManyToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';

@Entity()
class TanUpdateTracker extends BaseEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column('json')
  forms: object;

  @Column('json')
  eChallan: object;

  @Column('json')
  myCas: object;

  @Column()
  isChange: boolean;

  @Column()
  organizationId: number;

  @Column()
  clientId: number;

  @ManyToOne(() => Client, (client) => client.tanUpdateTracker, { onDelete: 'SET NULL' })
  client: Client;

  @Column()
  tanClientCredentialsId: number;

  @Column('json')
  traceCommunication: object;

  @Column('json')
  tempNoticeFya: object;

  @Column('json')
  tempNoticeFyi: object;

  @Column('json')
  outstandingDemand: object;

  @CreateDateColumn()
  createdAt: string;

  @UpdateDateColumn()
  updatedAt: string;
}

export default TanUpdateTracker;
