import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateReceiptCreditsTable1659000811501
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
        CREATE TABLE receipt_credit (
            id int NOT NULL AUTO_INCREMENT,
            PRIMARY KEY (id),
            type enum('CREDIT', 'DEBIT') NOT NULL,
            amount DECIMAL(19,2) NOT NULL,
            created_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
            receipt_id int NULL,
            client_id int NULL,
            organization_id int NULL,
            billing_entity_id int NULL,
            FOREIG<PERSON> KEY (client_id) REFERENCES client(id) ON DELETE SET NULL,                                                    
            FOREIGN KEY (organization_id) REFERENCES organization(id) ON DELETE SET NULL,
            FOREIGN KEY (billing_entity_id) REFERENCES billing_entity(id) ON DELETE SET NULL
        )            
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}
