import { MigrationInterface, QueryRunner } from 'typeorm';

export class AlterOrganizationTable1662365456119 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
        ALTER TABLE organization
        ADD COLUMN date_of_formation varchar(255) null,
        ADD COLUMN building_no varchar(255) null,
        ADD COLUMN district varchar(255) null
  `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}
