import { IsDateString, IsOptional } from 'class-validator';

export class getClientsReportDto {
  @IsOptional()
  category: string;

  @IsOptional()
  subCategory: string;

  @IsOptional()
  @IsDateString()
  fromDate: string;

  @IsOptional()
  @IsDateString()
  toDate: string;

  @IsOptional()
  status: string;

  @IsOptional()
  labels: Array<string>;

  @IsOptional()
  monthAdded: string;

  @IsOptional()
  search: string;
}
