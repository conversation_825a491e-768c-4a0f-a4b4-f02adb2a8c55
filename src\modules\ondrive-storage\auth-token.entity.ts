import { BaseEntity, Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

export enum AuthTokenType {
  MICROSFT = 'MICROSFT',
  GOOGLE = 'GOOGLE',
}

@Entity()
class AuthToken extends BaseEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  organizationId: number;

  @Column()
  accessToken: string;

  @Column()
  refreshToken: string;

  @Column({
    type: 'enum',
    enum: AuthTokenType,
  })
  type: AuthTokenType;
}

export default AuthToken;
