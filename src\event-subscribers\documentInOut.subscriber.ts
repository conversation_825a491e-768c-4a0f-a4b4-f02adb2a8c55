import {
  Connection,
  EntitySubscriberInterface,
  EventSubscriber,
  InsertEvent,
  UpdateEvent,
  createQueryBuilder,
  getManager,
} from 'typeorm';
import DocumentInOut from 'src/modules/document-in-out/entity/document-in-out.entity';
import * as moment from 'moment';

@EventSubscriber()
export class DocumentInOutSubscriber implements EntitySubscriberInterface<DocumentInOut> {
  constructor(private readonly connection: Connection) {
    connection.subscribers.push(this);
  }

  listenTo() {
    return DocumentInOut;
  }

  beforePriority = '';
  beforeStatus = '';
  async beforeInsert(event: InsertEvent<DocumentInOut>) {
    const yearLastTwoDigits = moment().format('YY');
    const { documentType, useType, organization } = event.entity;
    const string = yearLastTwoDigits + (documentType === "in" ? "DI" : "DO") + (useType === "kyb" ? "K" : useType === "task" ? "T" : "G")
    const count = await createQueryBuilder(DocumentInOut, 'documentInOut')
      .leftJoin('documentInOut.organization', 'organization')
      .where('organization.id = :organizationId', { organizationId: organization.id })
      .andWhere("documentInOut.documentId LIKE :documentId", { documentId: `${string}%` })
      .getCount();
    function generateDOCId(id: number) {
      if (id < 10000) {
        return string + id.toString().padStart(4, '0');
      }
      return string + id;
    }
    event.entity.documentId = generateDOCId(count + 1);
  }

  async afterInsert(event: InsertEvent<DocumentInOut>) {
  }
}
