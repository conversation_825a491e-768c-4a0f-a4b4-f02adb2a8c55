import { MigrationInterface, QueryRunner } from 'typeorm';

export class AlterServiceTable1662461272326 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
        ALTER TABLE service
        ADD COLUMN from_admin boolean default false,
        ADD COLUMN admin_service_id int null
  `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}
