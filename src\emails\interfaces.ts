export interface ISignup {
  email: string;
  organization: string;
}

export interface IUserInvited {
  name: string;
  email: string;
  link: string;
  organization: string;
}

export interface IClientCreated {
  fullName: string;
  orgName: string;
  email: string;
  password: string;
}

export interface IResetPassword {
  email: string;
  link: string;
  name: string;
}

export interface IMemberAdded {
  name: string;
  email: string;
  link: string;
}

export interface ICommentAdded {
  email: string;
  userName: string;
  memberName: string;
  taskName: string;
  clientName: string;
  comment: string;
}

export interface ITaskCreatedClient {
  email: string;
  clientName: string;
  taskName: string;
  assignedTo: string;
  taskStartDate: string;
  taskEndDate: string;
}

export interface ITaskCreatedAssignedUser {
  email: string;
  userName: string;
  taskName: string;
  assignedTo: string;
  taskStartDate: string;
  taskEndDate: string;
}

export interface ITaskStatusUpdatedClient {
  email: string;
  userName: string;
  taskName: string;
  assignedTo: string;
  taskStartDate: string;
  taskEndDate: string;
}

export interface ITaskStatusUpdatedAssignedUser {
  email: string;
  memberName: string;
  userName: string;
  taskName: string;
  subTaskName: String;
  assignedTo: string;
  dueDate: string;
}

export interface ISubTaskCreatedClient {
  email: string;
  clientName: string;
  orgName: string;
  taskName: string;
  subTaskName: String;
  assignedTo: string;
  dueDate: string;
}

export interface ISubTaskCreatedAssignedUser {
  email: string;
  userName: string;
  taskName: string;
  assignedTo: string;
  taskStartDate: string;
  taskEndDate: string;
}

export interface ILeadCreated {
  email: string;
  orgName: string;
  leadName: string;
}
