import {
  BadRequestException,
  ConsoleLogger,
  Injectable,
  InternalServerErrorException,
} from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { validate } from 'class-validator';
import { randomBytes } from 'crypto';
import * as _ from 'lodash';
import { Event_Actions } from 'src/event-listeners/actions';
import { User, UserStatus, UserType } from 'src/modules/users/entities/user.entity';
import { Brackets, Not, createQueryBuilder, getConnection, getRepository } from 'typeorm';
import * as xlsx from 'xlsx';
import axios from 'axios';
import Task from 'src/modules/tasks/entity/task.entity';
import { TaskRecurringStatus, TaskStatusEnum } from 'src/modules/tasks/dto/types';
import Storage from 'src/modules/storage/storage.entity';
import { StorageService } from 'src/modules/storage/storage.service';
import GstrRegister, { RegistrationType } from 'src/modules/gstr-register/entity/gstr-register.entity';
import AutClientCredentials, { syncStatus } from 'src/modules/automation/entities/aut_client_credentials.entity';
import parsePhoneNumberFromString from "libphonenumber-js";
import countries from 'src/utils/countries';
import mobileWithCountry from 'src/utils/validations/mobileWithCountry';
import ClientGroup from './client-group.entity';
import { UpdateClientDto } from '../clients/dto/update-client.dto';
import FindQueryDto from '../clients/dto/find-query.dto';
import Activity, { ActivityType } from '../activity/activity.entity';
import BulkUpdateDto from '../clients/dto/bulk-update.dto';
import { getTitle } from 'src/utils';
import * as moment from 'moment';
import { Permissions } from '../events/permission';


@Injectable()
export class ClientGroupService {

  async create(userId: number, data: any) {
    let user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
    const displayName = (' ' + data.apiData.clientGroup)?.trim();

    let existingUser = await createQueryBuilder(ClientGroup, 'clientGroup')
      .leftJoin('clientGroup.organization', 'organization')
      .where('organization.id = :organization', { organization: user.organization.id })
      .andWhere('(clientGroup.displayName = :displayName)', {
        displayName: displayName,
      })
      .getOne();

    if (existingUser) {
      throw new BadRequestException(
        'Client Group with the given Display Name already Exists in your Organization',
      );
    }

    if (!displayName || !/^[^<>:"\/\\|?*\.]*$/.test(displayName)) {
      throw new BadRequestException('Display name cannot contain <, >, :, ", /, \\, |, ?, *, or .');
    }

    let clientManagers = await createQueryBuilder(User, 'user')
      .where('user.id IN (:...ids)', { ids: data.apiData.clientManagers })
      .getMany();

    const group = new ClientGroup();
    group.displayName = displayName || data.apiData.clientGroup;
    group.organization = user.organization;
    group.countryCode = data.apiData.countryCode;
    group.mobileNumber = data.apiData.mobileNumber;
    group.clientNumber = data.apiData.clientGroupNumber;
    group.email = data.apiData.email;
    group.clients = data.clients;
    group.clientGroupManagers = clientManagers;
    group.type = "CLIENT_GROUP";
    group['userId'] = user.id;
    await group.save();
    let activity = new Activity();
    activity.action = Event_Actions.CLIENT_GROUP_CREATED;
    activity.actorId = user?.id;
    activity.type = ActivityType.CLIENT_GROUP;
    activity.typeId = group.id;
    activity.remarks = `"${group.displayName}" Client Group Profile Created by ${user.fullName}`;
    await activity.save();

    return group;
  }

  async findAll(userId: number, query: any) {

    let user = await User.findOne({ where: { id: userId }, relations: ['organization', 'role'] });

    let ViewAll = user.role.permissions.find(
      (permission) => permission.slug === Permissions.VIEW_ALL_CLIENT_MANAGERS,
    );
    let ViewAssigned = user.role.permissions.find(
      (permission) => permission.slug === Permissions.VIEW_ASSIGNED_CLIENT_MANAGERS,
    );
    let clients = getConnection()
      .createQueryBuilder(ClientGroup, 'clientGroup')
      .leftJoin('clientGroup.clientGroupManagers', 'clientGroupManagers')
      .leftJoinAndSelect('clientGroup.organization', 'organization')
      .leftJoinAndSelect('clientGroup.clients', 'clients')
      .where('organization.id = :organization', { organization: user.organization.id })
      .andWhere('clientGroup.status != :status', { status: UserStatus.DELETED });

    if (ViewAssigned && !ViewAll) {
      clients.andWhere('clientGroupManagers.id = :userId', { userId });
    }
    if (query.search) {
      clients.andWhere(
        new Brackets((qb) => {
          qb.where('clientGroup.displayName LIKE :search', { search: `%${query.search}%` });
          qb.orWhere('clientGroup.email LIKE :search', { search: `%${query.search}%` });
        }),
      );
    }
    if (query.offset >= 0) {
      clients.skip(query.offset);
    }

    if (query.limit) {
      clients.take(query.limit);
    }

    let result = await clients.getManyAndCount();

    return {
      count: result[1],
      result: result[0],
    };
  }

  async findOne(id: number, userId: number) {
    let user = await User.findOne({ where: { id: userId }, relations: ['organization', 'role'] });
    const clientGroupQuery = await createQueryBuilder(ClientGroup, 'clientGroup')
      .leftJoinAndSelect('clientGroup.organization', 'organization')
      .leftJoinAndSelect('clientGroup.clientGroupManagers', 'clientGroupManagers')
      .leftJoinAndSelect('clientGroup.clientGroupImage', 'clientGroupImage')
      .leftJoinAndSelect('clientGroup.clients', 'clients')
      .where('clientGroup.id = :id', { id })
      .andWhere('organization.id = :organization', { organization: user.organization.id })

    let ViewAll = user.role.permissions.find(
      (permission) => permission.slug === Permissions.VIEW_ALL_CLIENT_MANAGERS,
    );
    let ViewAssigned = user.role.permissions.find(
      (permission) => permission.slug === Permissions.VIEW_ASSIGNED_CLIENT_MANAGERS,
    );

    if (ViewAssigned && !ViewAll) {
      clientGroupQuery.andWhere('clientGroupManagers.id = :userId', { userId });
    }

    const clientGroup = await clientGroupQuery.getOne();

    return clientGroup;
  }

  async getAllClientGroups(userId: number, query: any) {
    const { limit, offset } = query;
    const { status: state, category, subCategory, monthAdded, search, labels } = query;
    const month = new Date(monthAdded).getMonth() + 1;
    const year = new Date(monthAdded).getFullYear();

    let user = await User.findOne({ where: { id: userId }, relations: ['organization', 'role'] });
    if (!user) throw new Error('User not found');

    let ViewAll = user.role.permissions.find(
      (permission) => permission.slug === Permissions.VIEW_ALL_CLIENT_MANAGERS,
    );
    let ViewAssigned = user.role.permissions.find(
      (permission) => permission.slug === Permissions.VIEW_ASSIGNED_CLIENT_MANAGERS,
    );
    const organizationId = user.organization.id;

    let clients = getConnection()
      .createQueryBuilder(ClientGroup, 'clientgroup')
      .leftJoin('clientgroup.clientGroupManagers', 'clientGroupManagers')
      .leftJoinAndSelect('clientgroup.organization', 'organization')
      .where('clientgroup.status != :status', { status: UserStatus.DELETED })
      .andWhere('organization.id = :id', { id: user.organization.id });

    if (ViewAssigned && !ViewAll) {
      clients.andWhere('clientGroupManagers.id = :userId', { userId });
    }
    if (search) {
      clients.andWhere(
        new Brackets((qb) => {
          qb.where('clientgroup.displayName LIKE :search', { search: `%${search}%` })
            .orWhere('clientgroup.email LIKE :search', { search: `%${search}%` })
            .orWhere('clientgroup.mobileNumber LIKE :search', { search: `%${search}%` })
            .orWhere('clientgroup.panNumber LIKE :search', { search: `%${search}%` });
        })
      );
    }
    const sort = (typeof query?.sort === "string") ? JSON.parse(query.sort) : query?.sort;
    if (sort?.column) {
      const columnMap: Record<string, string> = {
        clientId: 'clientgroup.createdAt',
        clientNumber: 'clientgroup.clientNumber',
        displayName: 'clientgroup.displayName',
        active: 'clientgroup.status'

      };
      const column = columnMap[sort.column] || sort.column;
      clients.orderBy(column, sort.direction.toUpperCase());
    } else {
      clients.orderBy('clientgroup.createdAt', 'DESC');
    };
    if (offset >= 0) {
      clients.skip(offset);
    }

    if (limit) {
      clients.take(limit);
    }

    let result = await clients.getManyAndCount();

    return {
      count: result[1],
      result: result[0],
    };
  }

  async update(userId: number, id: number, data: UpdateClientDto) {
    const TAN_REGEX = /^[A-Z]{4}[0-9]{5}[A-Z]{1}$/;

    let user = await User.findOne({ where: { id: userId }, relations: ['organization'] });

    const clientDataBaseState = await ClientGroup.findOne({ where: { id } });

    if (!(' ' + data.displayName)?.trim().length) {
      throw new BadRequestException('Invalid Display Name');
    }

    let clientGroup = await ClientGroup.findOne({ where: { id }, relations: ['clientGroupImage', 'user', 'organization'], });

    if (data?.displayName !== undefined && data?.displayName !== "undefined") {
      if (data?.displayName?.length !== clientGroup?.displayName?.length) {
        const displayName = (' ' + data.displayName)?.trim();
        let existingUser = await createQueryBuilder(ClientGroup, 'clientGroup')
          .leftJoin('clientGroup.organization', 'organization')
          .where('organization.id = :organization', { organization: user.organization.id })
          .andWhere('(clientGroup.displayName = :displayName)', {
            displayName: displayName,
          })
          .getOne();
        if (existingUser) {
          throw new BadRequestException(
            'Client Group with the given display name already exists in your organization',
          );
        }
      }
    }

    if (data?.gstNumber) {
      let existingGstNumber = await ClientGroup.findOne({
        where: {
          gstNumber: data.gstNumber,
          organization: user.organization.id,
          id: Not(id),
        },
      });
      if (existingGstNumber) {
        throw new BadRequestException(
          'Duplicate GSTIN Detected! To ensure accurate data management, GSTINs must be unique for each client. Please double-check the entered GSTIN.',
        );
      }
    }


    clientGroup.displayName =
      data.displayName !== null && data.displayName !== undefined
        ? data.displayName.trim()
        : data.displayName;
    clientGroup.mobileNumber = data.mobileNumber;
    clientGroup.email = data.email;
    clientGroup.panNumber = data.panNumber;
    clientGroup.tradeName = data.tradeName;
    clientGroup.legalName = data.legalName;
    clientGroup.constitutionOfBusiness = data.constitutionOfBusiness;
    clientGroup.middleName = data.middleName;
    clientGroup.gstRegistrationDate = data.gstRegistrationDate;
    clientGroup.placeOfSupply = data.placeOfSupply;
    clientGroup.fullName = data.fullName;
    clientGroup.firstName = data.firstName;
    clientGroup.lastName = data.lastName;
    clientGroup.gstNumber = data.gstNumber;
    clientGroup.panNumber = data.panNumber;
    // clientGroup.incomeTaxAudit = data.incomeTaxAudit;
    // clientGroup.gstAnnualForm = data.gstAnnualForm;
    clientGroup.tanNumber = data.tanNumber;
    clientGroup.clientNumber = data.clientNumber !== null && data.clientNumber !== undefined
      ? data.clientNumber.trim()
      : data.clientNumber;
    clientGroup.countryCode = data.countryCode;
    if (data?.gstNumber) {
      const gstCharAt13 = data.gstNumber.charAt(13);
      const extractedNumber = data.gstNumber.substring(2, data.gstNumber.length - 3);
      if (gstCharAt13 === 'D') {
        const pattren = TAN_REGEX.test(extractedNumber);
        if (pattren) {
          clientGroup.tanNumber = data.tanNumber;
        } else {
          clientGroup.panNumber = data.panNumber;
        }

        clientGroup.registrationType = RegistrationType.TAX_DEDUCTOR;
      } else if (gstCharAt13 === 'Z' || gstCharAt13 === 'C') {
        clientGroup.panNumber = data.panNumber;
        clientGroup.registrationType =
          gstCharAt13 === 'Z' ? RegistrationType.REGULAR_TAXPAYER : RegistrationType.TAX_COLLECTOR;
      }
    } else {
      clientGroup.tanNumber = data.tanNumber;
      clientGroup.panNumber = data.panNumber;
    }
    clientGroup.gstVerified = data.gstVerified;
    clientGroup.panVerified = data.panVerified;
    clientGroup.dob = data.dob;
    clientGroup.buildingName = data.buildingName;
    clientGroup.street = data.street;
    clientGroup.city = data.city;
    clientGroup.state = data.state;
    clientGroup.pincode = data.pincode;
    clientGroup.status = data.status;
    clientGroup.clientPortalAccess = data.clientPortalAccess;
    clientGroup.address = data.address;
    clientGroup.issameaddress = data.issameaddress;
    clientGroup.countryCode = data.countryCode;
    if (data.clientGroupManagers) {
      let clientGroupManagers = await createQueryBuilder(User, 'user')
        .where('user.id IN (:...ids)', { ids: data.clientGroupManagers })
        .getMany();
      clientGroup.clientGroupManagers = clientGroupManagers;
    }

    if (
      clientDataBaseState?.status !== UserStatus.INACTIVE &&
      data.status === UserStatus.INACTIVE
    ) {
      clientGroup.inactiveAt = new Date();
    }

    if (data.status === UserStatus.INACTIVE) {
      await createQueryBuilder(Task, 'task')
        .leftJoinAndSelect('task.clientGroup', 'clientGroup')
        .where('clientGroup.id = :id', { id: clientGroup.id })
        .andWhere('recurringStatus = :status', { status: TaskRecurringStatus.PENDING })
        .update()
        .set({
          recurringStatus: TaskRecurringStatus.TERMINATED,
          status: TaskStatusEnum.TERMINATED,
          restore: TaskStatusEnum.TODO,
          statusUpdatedAt: moment().format('YYYY-MM-DD HH:mm:ss.SSSSSS')
        })
        .execute();
    }

    clientGroup['userId'] = user?.id;
    await clientGroup.save();

    let activity = new Activity();
    activity.action = Event_Actions.CLIENT_GROUP_UPDATED;
    activity.actorId = user?.id;
    activity.type = ActivityType.CLIENT_GROUP;
    activity.typeId = clientGroup.id;
    activity.remarks = `"${clientGroup.displayName}" Client Group Profile Updated by ${user.fullName}`;
    await activity.save();

    return clientGroup;
  }

  async updateClients(userId: number, id: number, data: any) {
    let user = await User.findOne({ where: { id: userId }, relations: ['organization'] });

    let clientGroup = await ClientGroup.findOne({ where: { id }, relations: ['clients'] });
    clientGroup.clients = [...clientGroup.clients, ...data];


    clientGroup['userId'] = user?.id;
    await clientGroup.save();

    let activity = new Activity();
    activity.action = Event_Actions.CLIENTS_GROUP_CLIENTS_UPDATED;
    activity.actorId = user?.id;
    activity.type = ActivityType.CLIENT_GROUP;
    activity.typeId = clientGroup.id;
    activity.remarks = `"${clientGroup.displayName}" Client Group Clients Updated by ${user.fullName}`;
    await activity.save();

    for (let i of data) {
      let activity = new Activity();
      activity.action = Event_Actions.CLIENT_GROUP_CLIENTS_UPDATED;
      activity.actorId = user?.id;
      activity.type = ActivityType.CLIENT;
      activity.typeId = i.id;
      activity.remarks = `Client Added to ${clientGroup.displayName} (Client Group) by ${user.fullName}`;
      await activity.save();
    }

    return clientGroup;
  }

  async bulkDelete(ids: number[], userId) {
    let user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
    let clientIds = [];

    const pendingTasks = await createQueryBuilder(Task, 'task')
      .leftJoinAndSelect('task.clientGroup', 'clientGroup')
      .where('task.status IN (:...statuses)', {
        statuses: [
          TaskStatusEnum.TODO,
          TaskStatusEnum.IN_PROGRESS,
          TaskStatusEnum.ON_HOLD,
          TaskStatusEnum.UNDER_REVIEW,
        ],
      })
      .andWhere('clientGroup.id IN (:...ids)', { ids })
      .getMany();

    pendingTasks.forEach(item => {
      if (!clientIds.includes(item?.clientGroup?.displayName)) {
        clientIds.push(item?.clientGroup?.displayName);
      }
    })

    if (clientIds.length > 0) {
      return { errorsList: clientIds };
    }

    const numberids = ids.map(item => Number(item));
    await createQueryBuilder(Task, 'task')
      .leftJoinAndSelect('task.clientGroup', 'clientGroup')
      .where('clientGroup.id IN (:...ids)', { ids: numberids })
      .andWhere('recurringStatus = :status', { status: TaskRecurringStatus.PENDING })
      .update(Task)
      .set({
        recurringStatus: TaskRecurringStatus.TERMINATED,
        status: TaskStatusEnum.TERMINATED,
        restore: TaskStatusEnum.TODO,
        statusUpdatedAt: moment().format('YYYY-MM-DD HH:mm:ss.SSSSSS')
      })
      .execute();

    const clientGroupRepository = getRepository(ClientGroup);
    const clientGroups = await clientGroupRepository.findByIds(numberids);
    for (const clientGroup of clientGroups) {
      clientGroup.clients = [];
      clientGroup.status = UserStatus.DELETED;
      clientGroup['userId'] = userId;
      await clientGroupRepository.save(clientGroup);
    }
    for (let i of numberids) {
      let clientGroup = await ClientGroup.findOne({ where: { id: i } });

      let activity = new Activity();
      activity.action = Event_Actions.CLIENT_GROUP_DELETED;
      activity.actorId = userId;
      activity.type = ActivityType.CLIENT_GROUP;
      activity.typeId = clientGroup.id;
      activity.remarks = `"${clientGroup.displayName}" Client Group Profile Deleted by ${user.fullName}`;
      await activity.save();
    }

    return { success: true };
  }

  async findDeleted(userId: number, query: FindQueryDto) {
    const user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
    const clientGroupsQuery = await ClientGroup.createQueryBuilder('clientGroup')
      .leftJoinAndSelect('clientGroup.organization', 'organization')
      .leftJoinAndSelect('clientGroup.clientGroupManagers', 'clientGroupManagers')
      .where('organization.id = :organizationId', { organizationId: user.organization.id })
      .andWhere('clientGroup.status = :status', { status: UserStatus.DELETED });
    const sort = (typeof query?.sort === "string") ? JSON.parse(query.sort) : query?.sort;
    if (sort?.column) {
      const columnMap: Record<string, string> = {
        clientId: 'clientGroup.clientId',
        displayName: 'clientGroup.displayName',
      };
      const column = columnMap[sort.column] || sort.column;
      clientGroupsQuery.orderBy(column, sort.direction.toUpperCase());
    } else {
      clientGroupsQuery.orderBy('clientGroup.createdAt', 'DESC');
    };
    if (query.search) {
      clientGroupsQuery.andWhere(
        new Brackets((qb) => {
          qb.where('clientGroup.displayName LIKE :displayName', {
            displayName: `%${query.search}%`,
          });
          qb.orWhere('clientGroup.email LIKE :email', {
            email: `%${query.search}%`,
          });
          qb.orWhere('clientGroup.clientId LIKE :clientId', {
            clientId: `%${query.search}%`,
          });
          qb.orWhere('clientGroup.gstNumber LIKE :gstNumber', {
            gstNumber: `%${query.search}%`,
          });
          qb.orWhere('clientGroup.mobileNumber LIKE :mobileNumber', {
            mobileNumber: `%${query.search}%`,
          });
          qb.orWhere('clientGroup.panNumber LIKE :panNumber', {
            panNumber: `%${query.search}%`,
          });
        }),
      );
    }


    let clientGroups = clientGroupsQuery.skip(query.offset)
      .take(query.limit)
      .getManyAndCount();

    return clientGroups;
  }

  async deleteGroupClients(userId: number, payload) {
    let user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
    const ids = payload.ids.map(item => item.id);

    const clientGroup = await ClientGroup.findOne({ where: { id: payload.clientGroupId }, relations: ['clients'] });

    const clients = clientGroup.clients.filter(item => !ids.includes(item.id))

    clientGroup.clients = clients;
    clientGroup['userId'] = userId;
    if (!clientGroup.clients.length) {
      throw new BadRequestException("Must Contain Atleast One Client !");
    }
    clientGroup.save();

    let activity = new Activity();
    activity.action = Event_Actions.CLIENTS_GROUP_CLIENTS_DELETED;
    activity.actorId = user?.id;
    activity.type = ActivityType.CLIENT_GROUP;
    activity.typeId = clientGroup.id;
    activity.remarks = `"${clientGroup.displayName}" Client Group Clients Removed by ${user.fullName}`;
    await activity.save();

    for (let i of ids) {
      let activity = new Activity();
      activity.action = Event_Actions.CLIENT_GROUP_CLIENTS_DELETED;
      activity.actorId = user?.id;
      activity.type = ActivityType.CLIENT;
      activity.typeId = i;
      activity.remarks = `Client Removed from ${clientGroup.displayName} (Client Group) by ${user.fullName}`;
      await activity.save();
    }

    return clientGroup;
  }

  async restoreClient(id: number, userId: number, body: any) {
    const clientGroup = await ClientGroup.findOne({ where: { id } });
    const user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
    clientGroup.status = UserStatus.ACTIVE;
    clientGroup['userId'] = userId;
    if (body?.clientGroupManagers) {
      clientGroup['clientGroupManagers'] = body?.clientGroupManagers;
    }
    await clientGroup.save();

    let activity = new Activity();
    activity.action = Event_Actions.CLIENT_GROUP_RESTORED;
    activity.actorId = userId;
    activity.type = ActivityType.CLIENT_GROUP;
    activity.typeId = clientGroup.id;
    activity.remarks = `"${clientGroup.displayName}" Client Group Profile Restored by ${user.fullName}`;
    await activity.save();

    return clientGroup;
  }

  async restoreBulkClient(userId: number, body: any) {
    let clientIds = body?.ids;
    const user = await User.findOne({ where: { id: userId }, relations: ['organization'] });

    for (let i of body?.ids) {
      const clientGroup = await ClientGroup.findOne({ where: { id: i } });
      clientGroup.status = UserStatus.ACTIVE;
      clientGroup['userId'] = userId;
      if (body?.activeInactiveDataa?.length) {
        clientGroup['clientGroupManagers'] = body?.activeInactiveDataa;
      }
      await clientGroup.save();

      let activity = new Activity();
      activity.action = Event_Actions.CLIENT_GROUP_RESTORED;
      activity.actorId = userId;
      activity.type = ActivityType.CLIENT_GROUP;
      activity.typeId = clientGroup.id;
      activity.remarks = `"${clientGroup.displayName}" Client Group Profile Restored by ${user.fullName}`;
      await activity.save();
    }
    return clientIds;
  }

  async bulkUpdate(data: BulkUpdateDto, userId) {
    const user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
    if (data.status === 'INACTIVE') {
      await createQueryBuilder(Task, 'task')
        .leftJoinAndSelect('task.clientGroup', 'clientGroup')
        .where('clientGroup.id IN (:...ids)', { ids: data.ids })
        .andWhere('recurringStatus = :status', { status: TaskRecurringStatus.PENDING })
        .update(Task)
        .set({
          recurringStatus: TaskRecurringStatus.TERMINATED,
          status: TaskStatusEnum.TERMINATED,
          restore: TaskStatusEnum.TODO,
          statusUpdatedAt: moment().format('YYYY-MM-DD HH:mm:ss.SSSSSS')
        })
        .execute();
    }

    const clientGroupRepository = getRepository(ClientGroup);

    const clientGroups = await createQueryBuilder(ClientGroup, 'clientGroup')
      .whereInIds(data.ids)
      .getMany();

    for (const clientGroup of clientGroups) {
      clientGroup.status = data.status;
      clientGroup['userId'] = userId;

      await clientGroupRepository.save(clientGroup);
    }

    for (let i of data.ids) {
      let activity = new Activity();
      activity.action = Event_Actions.CLIENT_GROUP_STATUS_UPDATED;
      activity.actorId = user.id;
      activity.type = ActivityType.CLIENT_GROUP;
      activity.typeId = i;
      activity.remarks = `Client Group Status Changed to ${getTitle(data.status.toLowerCase())} by ${user.fullName
        }`;
      await activity.save();
    }

    return { success: true };
  }
}
