import { IsBoolean, IsNotEmpty, IsOptional } from 'class-validator';

class CreateQuantumTemplateDto {
  @IsNotEmpty()
  name: string;

  @IsNotEmpty()
  templateCategoryId: number;

  @IsNotEmpty()
  labels: Object[];

  @IsNotEmpty()
  metadata: Object[];

  @IsOptional()
  description: string;

  @IsOptional()
  updatedBy: string;

  @IsOptional()
  googleDrivelink: string;

  @IsOptional()
  version: number;

  @IsOptional()
  excerpt: string;

  @IsOptional()
  saved: boolean;

  @IsNotEmpty()
  templateName: string;

  @IsOptional()
  subCatId: number;

  @IsOptional()
  price: number;

  @IsOptional()
  imageUrls: object[];
}

export default CreateQuantumTemplateDto;
