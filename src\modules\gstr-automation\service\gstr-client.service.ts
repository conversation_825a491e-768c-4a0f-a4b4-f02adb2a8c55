import { BadRequestException, Injectable, InternalServerErrorException } from '@nestjs/common';
import { User } from 'src/modules/users/entities/user.entity';
import GstrCredentials, { GstrStatus } from '../entity/gstrCredentials.entity';
import { Brackets, createQueryBuilder, getConnection, getManager, LessThan } from 'typeorm';
import GstrMachines from '../entity/gstrMachines.entity';
import axios from 'axios';
import AutomationMachines, {
  TypeEnum,
} from 'src/modules/automation/entities/automation_machines.entity';
import OrganizationPreferences from 'src/modules/organization-preferences/entity/organization-preferences.entity';
import Client from 'src/modules/clients/entity/client.entity';
import Password, { IsExistingAtomPro } from 'src/modules/clients/entity/password.entity';
import { createClientCredentials, updateClientCredentials } from 'src/utils/atomProReUse';
import { UserStatus } from 'src/modules/whatsapp/whatsapp.controller';
import * as xlsx from 'xlsx';
import * as ExcelJS from 'exceljs';

const categoryLabels = {
  individual: 'Individual',
  huf: 'Hindu Undivided Family',
  partnership_firm: 'Partnership Firm',
  llp: 'Limited Liability Partnership',
  company: 'Company',
  opc: 'OPC',
  public: 'Public Limited',
  government: 'Government',
  sec_8: 'Section-8',
  foreign: 'Foreign',
  aop: 'Association of Persons',
  boi: 'Body of Individuals',
  trust: 'Trust',
  public_trust: 'Public Trust',
  private_discretionary_trust: 'Private Discretionary Trust',
  state: 'State',
  central: 'Central',
  local_authority: 'Local Authority',
  artificial_judicial_person: 'Artificial Juridical Person',
};

import { dateFormation } from 'src/utils/datesFormation';
import GstrAdditionalNoticeOrders from '../entity/gstrAdditionalOrdersAndNotices.entity';
import AutomationMachinesArchive from 'src/modules/automation/entities/automation_machines_archive.entity';
import { Permissions } from 'src/modules/tasks/permission';

@Injectable()
export class GstrClientService {
  async getGstrClients(userId: number, query: any) {
    const user = await User.findOne(userId, { relations: ['organization', 'role'] });
    let ViewAll = user.role.permissions.find(
      (permission) => permission.slug === Permissions.VIEW_ALL_CLIENT_MANAGERS,
    );
    let ViewAssigned = user.role.permissions.find(
      (permission) => permission.slug === Permissions.VIEW_ASSIGNED_CLIENT_MANAGERS,
    );

    let clients = getConnection()
      .createQueryBuilder(GstrCredentials, 'gstrCredentials')
      .select([
        'client.displayName',
        'gstrCredentials.id',
        'client.clientId',
        'client.category',
        'gstrCredentials.userName',
        'gstrCredentials.password',
        'profile.gstin',
        'clientManagers.id',
      ])
      .leftJoin('gstrCredentials.client', 'client')
      .leftJoin('client.clientManagers', 'clientManagers')
      .leftJoin('gstrCredentials.profile', 'profile')
      .leftJoin('client.organization', 'organization')
      .where('organization.id=:organization', { organization: user?.organization?.id })
      .andWhere('client.status != :status', { status: UserStatus.DELETED })
      // .andWhere('gstrCredentials.status != :disStatus', { disStatus: GstrStatus.DISABLE });
      .andWhere(
        new Brackets((qb) => {
          qb.where('gstrCredentials.status IS NULL').orWhere(
            'gstrCredentials.status = :enabledStatus',
            { enabledStatus: GstrStatus.ENABLE },
          );
        }),
      );
    const sort = typeof query?.sort === 'string' ? JSON.parse(query.sort) : query?.sort;
    if (sort?.column) {
      const columnMap: Record<string, string> = {
        category: 'client.category',
        clientId: 'client.clientId',
        displayName: 'client.displayName',
      };
      const column = columnMap[sort.column] || sort.column;
      clients.orderBy(column, sort.direction.toUpperCase());
    }
    if (query.search) {
      clients.andWhere(
        new Brackets((qb) => {
          qb.where('client.displayName LIKE :search', {
            search: `%${query.search}%`,
          });
          qb.orWhere('gstrCredentials.userName LIKE :search', {
            search: `%${query.search}%`,
          });
          qb.orWhere('profile.gstIn LIKE :search', {
            search: `%${query.search}%`,
          });
        }),
      );
    }

    if (ViewAssigned && !ViewAll) {
      clients.andWhere('clientManagers.id = :userId', { userId });
    }

    if (query.offset >= 0) {
      clients.skip(query.offset);
    }

    if (query.limit) {
      clients.take(query.limit);
    }

    let result = await clients.getManyAndCount();
    return {
      count: result[1],
      result: result[0],
    };
  }

  async exportGstrClient(userId: number, query: any) {
    const newQuery = { ...query, offset: 0, limit: 100000000 };
    let gstrClients = await this.getGstrClients(userId, newQuery);
    if (!gstrClients.result.length) throw new BadRequestException('No Data for Export');
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('GST Clients');
    const headers = [
      { header: 'S.No', key: 'serialNo' },
      { header: 'Client ID', key: 'clientId' },
      { header: 'Category', key: 'category' },
      { header: 'Client Name', key: 'clientName' },
      { header: 'GST Number', key: 'gst' },
      { header: 'User Name', key: 'userName' },
      { header: 'Password', key: 'password' },
    ];

    worksheet.columns = headers;
    const columnMaxLengths = Array(headers.length).fill(0);
    let serialCounter = 1; // Initialize a counter
    gstrClients.result.forEach((gstrclient) => {
      const rowData = {
        serialNo: serialCounter++, // Assign and then increment the counter
        clientId: gstrclient?.client?.clientId,
        category: categoryLabels[gstrclient?.client?.category] || '-',
        clientName: gstrclient?.client?.displayName,
        gst: gstrclient?.profile?.gstin,
        userName: gstrclient?.userName,
        password: gstrclient?.password,
      };

      const row = worksheet.addRow(rowData);

      worksheet.columns.forEach((column, colIndex) => {
        const headerLength = column.header?.length || 0;
        const cellLength = rowData[column.key]?.toString().length || 0;
        columnMaxLengths[colIndex] = Math.max(
          columnMaxLengths[colIndex] || 0,
          headerLength,
          cellLength,
        );
      });
    });

    worksheet.columns.forEach((column, colIndex) => {
      column.width = columnMaxLengths[colIndex] + 2;
    });

    const headerRow = worksheet.getRow(1);
    headerRow.font = { bold: true };
    headerRow.eachCell((cell) => {
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '64B5F6' },
      };
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' },
      };
      cell.alignment = { horizontal: 'center', vertical: 'middle' };
    });

    worksheet.columns.forEach((column) => {
      if (column.key === 'clientName') {
        column.width = 50;
        column.alignment = { wrapText: true, horizontal: 'center', vertical: 'middle' };
      } else {
        column.alignment = { horizontal: 'center', vertical: 'middle' };
      }
    });

    worksheet.eachRow((row) => {
      row.eachCell((cell) => {
        cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
      });
    });

    worksheet.views = [{ state: 'frozen', ySplit: 1 }];

    const buffer = await workbook.xlsx.writeBuffer();
    return buffer;
  }

  async getAtomClients(userId: number, data: any) {
    let user = await User.findOne({
      where: { id: userId },
      relations: ['organization'],
    });

    const entityManager = getManager();
    let query = `
  SELECT id, display_name as displayName, status
  FROM client 
  WHERE organization_id = ${user?.organization.id} 
    AND status != 'DELETED'
    AND id NOT IN (
      SELECT client_id 
      FROM gstr_credentials
      WHERE organization_id = ${user?.organization.id}
        AND client_id IS NOT NULL
    )
`;

    if (data?.search) {
      query += ` AND display_name LIKE '%${data?.search}%'`;
    }
    if (data?.limit) {
      query += ` LIMIT ${data?.limit}`;
      if (data?.page) {
        query += ` OFFSET ${data?.page}`;
      }
    }


    let clients = await entityManager.query(query);
    return clients;
  }

  async addGstrCredentials(userId, body) {
    try {
      let user = await User.findOne({
        where: { id: userId },
        relations: ['organization'],
      });

      const credential = await GstrCredentials.findOne({
        where: { organizationId: user?.organization?.id, userName: body?.userName },
      });

      const organizationPreferences: any = await OrganizationPreferences.findOne({
        where: { organization: user.organization },
      });
      if (organizationPreferences) {
        const organizationLimit = organizationPreferences?.automationConfig?.gstrLimit;
        if (organizationLimit) {
          const gstrCredentialsCount = await GstrCredentials.count({
            where: { organizationId: user.organization.id, status: GstrStatus.ENABLE },
          });
          if (organizationLimit >= gstrCredentialsCount) {
            if (credential) {
              throw new BadRequestException(
                'Specified pannumber Utilize your organization already',
              );
            } else {
              const client = await Client.findOne({ where: { id: body.clientId } });

              const details = {
                website: 'GST | e-Filing',
                websiteUrl: 'https://services.gst.gov.in/services/login',
                loginId: body?.userName,
                password: body?.password,
                client: client,
                isaddAtomPro: IsExistingAtomPro.YES,
                userId,
              };

              let password = null;

              const passwordCheck = await Password.findOne({where:{client,website:details.website,loginId:details.loginId},order:{createdAt:'DESC'}});
              if(passwordCheck){
                password = await updateClientCredentials(details,passwordCheck?.id);
              }else{
               password = await createClientCredentials(details);
              }
              const gstrCredentials = new GstrCredentials();
              gstrCredentials.userName = body?.userName.trim();
              gstrCredentials.password = body?.password.trim();
              gstrCredentials.userId = userId;
              gstrCredentials.clientId = body.clientId;
              gstrCredentials.organizationId = user?.organization?.id;
              gstrCredentials.status = GstrStatus.ENABLE;
              gstrCredentials.passwordId = password?.id;
              gstrCredentials.save();
            }
          } else {
            throw new BadRequestException('Maximum Gstr Client Count Reached');
          }
        }
      }
      // if (credential) {
      //   throw new BadRequestException('Specified pannumber Utilize your organization already');
      // } else {
      //   const gstrCredentials = new GstrCredentials();
      //   gstrCredentials.userName = body?.userName.trim();
      //   gstrCredentials.password = body?.password.trim();
      //   gstrCredentials.userId = userId;
      //   gstrCredentials.clientId = body.clientId;
      //   gstrCredentials.organizationId = user?.organization?.id;
      //   gstrCredentials.save();
      // }
    } catch (error) {
      console.log('Error occur while add the incomeTax client credentials', error);
      throw new InternalServerErrorException(error);
    }
  }

  async updateGstrCredentials(id: number, body: any, userId: number) {
    try {
      const clientCredential = await GstrCredentials.findOne({
        where: { id },
        relations: ['client'],
      });

      if (clientCredential.passwordId) {
        const password = await Password.findOne({
          where: { id: clientCredential.passwordId },
          relations: ['client'],
        });
        password.password = body.password;
        password['userId'] = userId;
        await password.save();
      }
      if (clientCredential) {
        clientCredential.password = body.password;
        await clientCredential.save();
        return clientCredential;
      }
    } catch (error) {
      console.log('Error occur while update the gstr client credentials', error);
    }
  }

  async sendSingleGstrCamundaRequest(userId: number, data: any) {
    try {
      let config: any = {
        method: 'post',
        maxBodyLength: Infinity,
        url: `${process.env.CAMUNDA_URL}/vider/quantum/api/automation`,
        headers: {
          'X-USER-ID': userId,
          'Content-Type': 'application/json',
        },
        data: JSON.stringify(data),
      };
      const response = await axios(config);

      const responseData = response?.data;
      return responseData[JSON.stringify(data[0].gstrCredentialsId)];
      if (
        responseData[JSON.stringify(data[0].autoCredentialsId)] ===
        'There is already an active request present'
      ) {
        return 'There is already an active request present';
      } else {
        return true;
      }
    } catch (error) {
      console.log('error in sendSingleIncometaxAutomationRequestToCamunda', error);
    }
  }

  async createGsrRequest(userId: number, id: number, body?: any) {
    let data1 = [
      {
        modules: body?.requests,
        type: 'GSTR',
        gstrCredentialsId: id,
      },
    ];
    return this.sendSingleGstrCamundaRequest(userId, data1);

    // const gstrMachines = await AutomationMachines.findOne({
    //   where: { gstrCredentials: id, status: 'PENDING' },
    // });
    // if (gstrMachines) {
    //   return 'waiting';
    // } else {
    //   const machine = new AutomationMachines();
    //   machine.gstrCredentials = await GstrCredentials.findOne({ where: { id } });
    //   machine.user = await User.findOne({ where: { id: userId } });
    //   machine.status = 'PENDING';
    //   machine.type = TypeEnum.GSTR;
    //   machine.machineName = 'AUTOMATION-TEST-555';
    //   machine.modules = body?.requests;
    //   await machine.save();
    // }
  }

  async sendIncometaxAutomationRequestToCamunda(userId: number, data: any) {
    let config: any = {
      method: 'post',
      maxBodyLength: Infinity,
      url: `${process.env.CAMUNDA_URL}/vider/quantum/api/automation`,
      headers: {
        'X-USER-ID': userId,
        'Content-Type': 'application/json',
      },
      data: JSON.stringify(data),
    };

    axios
      .request(config)
      .then((response) => { })
      .catch((error) => {
        console.log(error);
      });
  }

  async bulkGstrSync(userId, data) {
    // let alreadyPrecentList = [];
    // if (data?.selectedIds) {
    //   for (let autoClient of data?.selectedIds) {
    //     const checkAutomation = await AutomationMachines.findOne({
    //       where: { gstrCredentials: autoClient.id, status: 'PENDING' },
    //     });
    //     if (checkAutomation) {
    //       alreadyPrecentList.push(autoClient);
    //     } else {
    //       this.createGsrRequest(userId, autoClient?.id);
    //     }
    //   }
    // }
    // return alreadyPrecentList;

    if (data?.selectedIds) {
      let requestClientList = [];
      for (let gstr of data?.selectedIds) {
        requestClientList.push({
          modules: data?.requests,
          gstrCredentialsId: gstr?.id,
          type: TypeEnum.GSTR,
        });
      }
      this.sendIncometaxAutomationRequestToCamunda(userId, requestClientList);
    }
  }

  async getActivityLog(id: any, query: any, userId: number) {
    try {
      const user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
      const clientCredential = await GstrCredentials.findOne({
        where: { id, organizationId: user?.organization?.id },
        relations: ['client'],
      });
      if (clientCredential) {
        const autActivity = await createQueryBuilder(AutomationMachines, 'autActivity')
          .leftJoinAndSelect('autActivity.user', 'user')
          .where('autActivity.gstrCredentials =:id', { id: id });

        if (query.fromDate && query.toDate) {
          const { startTime, endTime } = dateFormation(query.fromDate, query.toDate);
          autActivity
            .andWhere('Date(autActivity.created_at) >= :startTime', { startTime })
            .andWhere('Date(autActivity.created_at) <= :endTime', { endTime });
        }

        autActivity.orderBy('autActivity.id', 'DESC');
        let result = await autActivity.getMany();
        return { result, accessDenied: true };
      } else {
        return { accessDenied: false };
      }
    } catch (error) {
      console.error('Error fetching activity log data:', error);
      return null; // Or throw an error
    }
  }

  async getActivityArchiveLog(id: any, query: any, userId: number) {
    try {
      const user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
      const clientCredential = await GstrCredentials.findOne({
        where: { id, organizationId: user?.organization?.id },
        relations: ['client'],
      });
      if (clientCredential) {
        const autActivity = await createQueryBuilder(AutomationMachinesArchive, 'autActivity')
          .leftJoinAndSelect('autActivity.user', 'user')
          .where('autActivity.gstrCredentials =:id', { id: id });

        if (query.fromDate && query.toDate) {
          const { startTime, endTime } = dateFormation(query.fromDate, query.toDate);
          autActivity
            .andWhere('Date(autActivity.created_at) >= :startTime', { startTime })
            .andWhere('Date(autActivity.created_at) <= :endTime', { endTime });
        }

        autActivity.orderBy('autActivity.id', 'DESC');
        let result = await autActivity.getMany();
        return { result, accessDenied: true };
      } else {
        return { accessDenied: false };
      }
    } catch (error) {
      console.error('Error fetching activity log data:', error);
      return null; // Or throw an error
    }
  }

  async getclientSyncStatus(id: number, userId: number) {
    try {
      const user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
      const clientCredentials = await GstrCredentials.findOne({
        where: { id, organizationId: user?.organization?.id },
      });

      if (clientCredentials) {
        const lastCompletedMachine = await AutomationMachines.findOne({
          where: { gstrCredentials: id },
          order: {
            id: 'DESC',
          },
          relations: ['gstrCredentials', 'gstrCredentials.client'],
        });
        let totalInqueueCount = 0;
        if (lastCompletedMachine?.status === 'INQUEUE') {
          totalInqueueCount = await AutomationMachines.count({
            where: { status: 'INQUEUE', id: LessThan(lastCompletedMachine.id), type: 'GSTR' },
          });
        }
        return { lastCompletedMachine, totalInqueueCount, accessDenied: true };
      } else {
        return { accessDenied: false };
      }
    } catch (error) {
      console.log('error occur while getting getclientAutoStatus', error);
    }
  }

  async getCaseIdBasedClientNotices(id: number, userId: number, query: any) {
    const { offset, limit } = query;
    const user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
    const checkGstrCredentials = await GstrCredentials.findOne({
      where: { id: id, organizationId: user.organization.id },
    });

    const totalCountQuery = await createQueryBuilder(
      GstrAdditionalNoticeOrders,
      'gstrAdditionalNoticeOrders',
    )
      .select('COUNT(DISTINCT gstrAdditionalNoticeOrders.arn)', 'count')
      .where('gstrAdditionalNoticeOrders.gstrCredentialsId = :id', { id })
      .andWhere('gstrAdditionalNoticeOrders.organizationId = :orgId', {
        orgId: user?.organization?.id,
      })
      .getRawOne();
    const total = Number(totalCountQuery.count);

    if (checkGstrCredentials) {
      const caseIdUniqueRecords = await createQueryBuilder(
        GstrAdditionalNoticeOrders,
        'gstrAdditionalNoticeOrders',
      )
        .select([
          'gstrAdditionalNoticeOrders.fy',
          'gstrAdditionalNoticeOrders.arn',
          'gstrAdditionalNoticeOrders.gstIn',
          'gstrAdditionalNoticeOrders.caseTypeName',
          'gstrAdditionalNoticeOrders.id',
          'gstrAdditionalNoticeOrders.gstrCredentialsId',
          'gstrAdditionalNoticeOrders.caseStatus',
          'gstrAdditionalNoticeOrders.caseId',
          'gstrAdditionalNoticeOrders.section',
          'gstrAdditionalNoticeOrders.nm',
          'gstrAdditionalNoticeOrders.designation',
          'gstrAdditionalNoticeOrders.refId',
          'gstrAdditionalNoticeOrders.categoryDate',
          'gstrAdditionalNoticeOrders.caseTypeId'
        ])
        .where('gstrAdditionalNoticeOrders.gstrCredentialsId = :id', { id })
        .andWhere('gstrAdditionalNoticeOrders.organizationId = :orgId', {
          orgId: user?.organization?.id,
        })
        .orderBy('gstrAdditionalNoticeOrders.fy', 'DESC')
        .groupBy('gstrAdditionalNoticeOrders.arn');
      const sort = typeof query?.sort === 'string' ? JSON.parse(query.sort) : query?.sort;
      if (sort?.column) {
        const columnMap: Record<string, string> = {
          fy: 'gstrAdditionalNoticeOrders.fy',
          caseType: 'gstrAdditionalNoticeOrders.caseTypeName',
        };
        const column = columnMap[sort.column] || sort.column;
        caseIdUniqueRecords.orderBy(column, sort.direction.toUpperCase());
      }
      if (offset >= 0) {
        caseIdUniqueRecords.skip(offset);
      }

      if (limit) {
        caseIdUniqueRecords.take(limit);
      }

      const result = await caseIdUniqueRecords.getManyAndCount();
      return {
        count: total,
        result: result[0],
        accessDenied: true,
      };
    }
  }

  async exportCaseBasedNotices(userId: number, query: any) {
    const exportQuery = { ...query, offset: 0, limit: 100000000 };
    const gstrid = query?.gstrid;
    let gstNoticeorders = await this.getCaseIdBasedClientNotices(gstrid, userId, exportQuery);
    if (!gstNoticeorders.result.length) throw new BadRequestException('No Data for Export');
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Add.Notice & Orders (Case ID)');
    const headers = [
      { header: 'S.No', key: 'serialNo' },
      { header: 'FY', key: 'fy' },
      { header: 'GST', key: 'gst' },
      { header: 'Case Type', key: 'caseType' },
      { header: 'Case ID', key: 'caseId' },
      { header: 'Case Status', key: 'caseStatus' },
    ];

    worksheet.columns = headers;
    const columnMaxLengths = Array(headers.length).fill(0);
    let serialCounter = 1; // Initialize a counter
    if (!gstNoticeorders.result.length) throw new BadRequestException('No Data for Export');
    gstNoticeorders.result.forEach((noticeorder) => {
      const rowData = {
        serialNo: serialCounter++, // Assign and then increment the counter
        fy: noticeorder?.fy,
        gst: noticeorder?.gstIn,
        caseType: noticeorder?.caseTypeName,
        caseId: noticeorder?.arn,
        caseStatus: noticeorder?.caseStatus,
      };

      const row = worksheet.addRow(rowData);
      const caseStatusCell = row.getCell('caseStatus'); // Get the cell for the "Type" column

      if (rowData.caseStatus === 'OPEN') {
        caseStatusCell.font = {
          color: { argb: 'FF800000' }, // ARGB for Maroon
          bold: true, // Bold text
        };
        caseStatusCell.value = 'Open'; // Add text
      } else if (rowData.caseStatus === 'CLOSED') {
        caseStatusCell.font = {
          color: { argb: 'FF008000' }, // ARGB for Green
          bold: true, // Bold text
        };
        caseStatusCell.value = 'Closed'; // Add text
      }

      worksheet.columns.forEach((column, colIndex) => {
        const headerLength = column.header?.length || 0;
        const cellLength = rowData[column.key]?.toString().length || 0;
        columnMaxLengths[colIndex] = Math.max(
          columnMaxLengths[colIndex] || 0,
          headerLength,
          cellLength,
        );
      });
    });

    worksheet.columns.forEach((column, colIndex) => {
      column.width = columnMaxLengths[colIndex] + 2;
    });

    const headerRow = worksheet.getRow(1);
    headerRow.font = { bold: true };
    headerRow.eachCell((cell) => {
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '64B5F6' },
      };
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' },
      };
      cell.alignment = { horizontal: 'center', vertical: 'middle' };
    });

    worksheet.columns.forEach((column) => {
      if (column.key === 'clientName') {
        column.width = 50;
        column.alignment = { wrapText: true, horizontal: 'center', vertical: 'middle' };
      } else {
        column.alignment = { horizontal: 'center', vertical: 'middle' };
      }
    });

    worksheet.eachRow((row) => {
      row.eachCell((cell) => {
        cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
      });
    });

    worksheet.views = [{ state: 'frozen', ySplit: 1 }];

    const buffer = await workbook.xlsx.writeBuffer();
    return buffer;
  }

  async getCaseIdBasedOrgNotices(userId: number, query: any) {
    const { offset, limit, search, type, financialYear, caseType } = query;

    const user = await User.findOne({ where: { id: userId }, relations: ['organization', 'role'] });
    let ViewAll = user.role.permissions.find(
      (permission) => permission.slug === Permissions.VIEW_ALL_CLIENT_MANAGERS,
    );
    let ViewAssigned = user.role.permissions.find(
      (permission) => permission.slug === Permissions.VIEW_ASSIGNED_CLIENT_MANAGERS,
    );
    const totalCountQuery = await createQueryBuilder(
      GstrAdditionalNoticeOrders,
      'gstrAdditionalNoticeOrders',
    )
      .leftJoinAndSelect('gstrAdditionalNoticeOrders.client', 'client')
      .leftJoin('client.clientManagers', 'clientManagers')
      .leftJoinAndSelect('client.gstrCredentials', 'gstrCredentials')
      .select('COUNT(DISTINCT gstrAdditionalNoticeOrders.arn)', 'count')
      .where('gstrAdditionalNoticeOrders.organizationId = :orgId', {
        orgId: user?.organization?.id,
      })
      .andWhere('client.status != :status', { status: UserStatus.DELETED })
      .andWhere('gstrCredentials.status != :disStatus', { disStatus: GstrStatus.DISABLE });

    if (ViewAssigned && !ViewAll) {
      totalCountQuery.andWhere('clientManagers.id = :userId', { userId });
    }

    if (search) {
      totalCountQuery.andWhere(
        new Brackets((qb) => {
          qb.where('client.displayName LIKE :search', { search: `%${search}%` });
          qb.orWhere('gstrAdditionalNoticeOrders.gstIn LIKE :search', { search: `%${search}%` });
        }),
      );
    }

    if (type) {
      totalCountQuery.andWhere('gstrAdditionalNoticeOrders.caseTypeName LIKE :typeName', {
        typeName: `%${type}%`,
      });
    }

    if (caseType) {
      totalCountQuery.andWhere('gstrAdditionalNoticeOrders.caseStatus LIKE :caseTypeStatus', {
        caseTypeStatus: caseType,
      });
    }

    if (financialYear) {
      if (financialYear === 'NA') {
        totalCountQuery.andWhere('gstrAdditionalNoticeOrders.fy is null');
      } else {
        totalCountQuery.andWhere('gstrAdditionalNoticeOrders.fy = :finy', { finy: financialYear });
      }
    }

    // Fetch the total count (filtered)
    const total = Number((await totalCountQuery.getRawOne()).count);

    const caseIdUniqueRecords = await createQueryBuilder(
      GstrAdditionalNoticeOrders,
      'gstrAdditionalNoticeOrders',
    )
      .leftJoinAndSelect('gstrAdditionalNoticeOrders.client', 'client')
      .leftJoin('client.clientManagers', 'clientManagers')
      .leftJoinAndSelect('client.gstrCredentials', 'gstrCredentials')
      .select([
        'gstrAdditionalNoticeOrders.fy',
        'gstrAdditionalNoticeOrders.arn',
        'client.displayName',
        'gstrAdditionalNoticeOrders.gstIn',
        'gstrAdditionalNoticeOrders.caseTypeName',
        'gstrAdditionalNoticeOrders.id',
        'gstrAdditionalNoticeOrders.gstrCredentialsId',
        'gstrAdditionalNoticeOrders.caseStatus',
      ])
      .where('gstrAdditionalNoticeOrders.organizationId = :orgId', {
        orgId: user?.organization?.id,
      })
      .andWhere('client.status != :status', { status: UserStatus.DELETED })
      .andWhere('gstrCredentials.status != :disStatus', { disStatus: GstrStatus.DISABLE })
      .orderBy('gstrAdditionalNoticeOrders.fy', 'DESC')
      .groupBy('gstrAdditionalNoticeOrders.arn');
    const sort = typeof query?.sort === 'string' ? JSON.parse(query.sort) : query?.sort;
    if (sort?.column) {
      const columnMap: Record<string, string> = {
        name: 'client.displayName',
        fy: 'gstrAdditionalNoticeOrders.fy',
        type: 'gstrAdditionalNoticeOrders.caseTypeName',
      };
      const column = columnMap[sort.column] || sort.column;
      caseIdUniqueRecords.orderBy(column, sort.direction.toUpperCase());
    }
    if (search) {
      caseIdUniqueRecords.andWhere(
        new Brackets((qb) => {
          qb.where('client.displayName LIKE :search', {
            search: `%${search}%`,
          });
          qb.orWhere('gstrAdditionalNoticeOrders.gstIn LIKE :search', {
            search: `%${search}%`,
          });
        }),
      );
    }

    if (ViewAssigned && !ViewAll) {
      caseIdUniqueRecords.andWhere('clientManagers.id = :userId', { userId });
    }

    if (type) {
      caseIdUniqueRecords.andWhere('gstrAdditionalNoticeOrders.caseTypeName LIKE :typeName', {
        typeName: `%${type}%`,
      });
    }

    if (caseType) {
      caseIdUniqueRecords.andWhere('gstrAdditionalNoticeOrders.caseStatus LIKE :caseTypeStatus', {
        caseTypeStatus: caseType,
      });
    }

    if (financialYear) {
      if (financialYear === 'NA') {
        caseIdUniqueRecords.andWhere('gstrAdditionalNoticeOrders.fy is null');
      } else {
        caseIdUniqueRecords.andWhere('gstrAdditionalNoticeOrders.fy = :finy', {
          finy: financialYear,
        });
      }
    }

    if (offset >= 0) {
      caseIdUniqueRecords.skip(offset);
    }

    if (limit) {
      caseIdUniqueRecords.take(limit);
    }

    const results = await caseIdUniqueRecords.getManyAndCount();
    return {
      count: total,
      result: results[0],
    };
  }
  async exportCasebasedOrgNotices(userId: number, query: any) {
    const exportQuery = { ...query, offset: 0, limit: 100000000 };
    let caseorders = await this.getCaseIdBasedOrgNotices(userId, exportQuery);
    if (!caseorders.result.length) throw new BadRequestException('No Data for Export');
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('GST Add. Notice & Orders');
    const headers = [
      { header: 'S.No', key: 'serialNo' },
      { header: 'FY', key: 'fy' },
      { header: 'Client Name', key: 'clientName' },
      { header: 'GSTIN', key: 'gstIn' },
      { header: 'Case Type', key: 'caseType' },
      { header: 'Case ID', key: 'caseId' },
      { header: 'Case Status', key: 'caseStatus' },
    ];

    worksheet.columns = headers;
    const columnMaxLengths = Array(headers.length).fill(0);
    let serialCounter = 1; // Initialize a counter
    if (!caseorders.result.length) throw new BadRequestException('No Data for Export');
    caseorders.result.forEach((caseorder) => {
      const {
        client: { displayName: clientName },
      } = caseorder;
      const rowData = {
        serialNo: serialCounter++, // Assign and then increment the counter
        fy: caseorder?.fy,
        clientName: clientName,
        gstIn: caseorder?.gstIn,
        caseType: caseorder?.caseTypeName,
        caseId: caseorder?.arn,
        caseStatus: caseorder?.caseStatus,
      };

      const row = worksheet.addRow(rowData);
      const caseStatusCell = row.getCell('caseStatus'); // Get the cell for the "Type" column

      if (rowData.caseStatus === 'OPEN') {
        caseStatusCell.font = {
          color: { argb: 'FF800000' }, // ARGB for Maroon
          bold: true, // Bold text
        };
        caseStatusCell.value = 'Open'; // Add text
      } else if (rowData.caseStatus === 'CLOSED') {
        caseStatusCell.font = {
          color: { argb: 'FF008000' }, // ARGB for Green
          bold: true, // Bold text
        };
        caseStatusCell.value = 'Closed'; // Add text
      }

      worksheet.columns.forEach((column, colIndex) => {
        const headerLength = column.header?.length || 0;
        const cellLength = rowData[column.key]?.toString().length || 0;
        columnMaxLengths[colIndex] = Math.max(
          columnMaxLengths[colIndex] || 0,
          headerLength,
          cellLength,
        );
      });
    });

    worksheet.columns.forEach((column, colIndex) => {
      column.width = columnMaxLengths[colIndex] + 2;
    });

    const headerRow = worksheet.getRow(1);
    headerRow.font = { bold: true };
    headerRow.eachCell((cell) => {
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '64B5F6' },
      };
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' },
      };
      cell.alignment = { horizontal: 'center', vertical: 'middle' };
    });

    worksheet.columns.forEach((column) => {
      if (column.key === 'clientName') {
        column.width = 50;
        column.alignment = { wrapText: true, horizontal: 'center', vertical: 'middle' };
      } else {
        column.alignment = { horizontal: 'center', vertical: 'middle' };
      }
    });

    worksheet.eachRow((row) => {
      row.eachCell((cell) => {
        cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
      });
    });

    worksheet.views = [{ state: 'frozen', ySplit: 1 }];

    const buffer = await workbook.xlsx.writeBuffer();
    return buffer;
  }
}
