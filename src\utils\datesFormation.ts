import * as moment from 'moment';

export const dateFormation = (start, end) => {
    const startDate = moment.utc(start);
    const startTime = startDate.startOf('day').toISOString();
    const endDate = moment.utc(end);
    const endTime = endDate.endOf('day').toISOString();
    return { startTime, endTime };
};



export function incrementFinancialYear(year) {
    const [start, end] = year.split('-').map(Number);
    return `${start + 1}-${end + 1}`;
};


export function getCurrentFinancialYear() {
    const today = new Date();
    const year = today.getFullYear();
    const month = today.getMonth() + 1;
    const nextYearShort = (year + 1) % 100;
    return month < 4 ? `${year - 1}-${year % 100}` : `${year}-${nextYearShort}`;
};

export function getFinancialYearFromRtnPrd(mmYYYY: string) {
    const month = mmYYYY.slice(0, 2);
    const year = parseInt(mmYYYY.slice(2), 10);

    // If the month is January, February, or March
    if (['01', '02', '03'].includes(month)) {
        // Return the financial year as previous year-current year (e.g., 2019-20)
        return `${year - 1}-${year.toString().slice(-2)}`;
    } else {
        // Return the financial year as current year-next year (e.g., 2020-21)
        return `${year}-${(year + 1).toString().slice(-2)}`;
    }
};

export function getReturnPrdFromYearMonth(fy: string, mm: string): string {
    const [startYear, endYear] = fy.split('-').map((year, index) =>
        index === 0 ? parseInt(year, 10) : parseInt(year, 10) + 2000
    );
    const month = parseInt(mm, 10);

    if (month === 1 || month === 2 || month === 3) {
        return `${mm}${endYear}`;
    } else {
        return `${mm}${startYear}`;
    }
};

export function getMonthYear(dateString: string) {
    const [day, month, year] = dateString.split('/').map(Number);
    const adjustedYear = (month === 1 || month === 2 || month === 3) ? year : year;
    return `${month.toString().padStart(2, '0')}${adjustedYear}`;
};

export function compareFiscalMonths(fyMonth1: string, fyMonth2: string): boolean {
    const year1 = parseInt(fyMonth1.slice(2), 10);
    const month1 = parseInt(fyMonth1.slice(0, 2), 10);
    const year2 = parseInt(fyMonth2.slice(2), 10);
    const month2 = parseInt(fyMonth2.slice(0, 2), 10);
    if (year1 > year2) {
        return true;
    } else if (year1 < year2) {
        return false;
    }
    if (month1 > month2) {
        return true;
    } else if (month1 < month2) {
        return false;
    };
    return false;
};

export function getRtnPrdFromFyMonFreq(financialYear: string, month: string) {
    const [startYearPart, endYearPart] = financialYear.split('-');
    const startYear = `${startYearPart}`;
    const endYear = `20${endYearPart}`;
    const monthMapping = {
        "January": "01",
        "February": "02",
        "March": "03",
        "April": "04",
        "May": "05",
        "June": "06",
        "July": "07",
        "August": "08",
        "September": "09",
        "October": "10",
        "November": "11",
        "December": "12"
    };
    const yearToUse = (month === "January" || month === "February" || month === "March") ? endYear : startYear;
    return `${monthMapping[month]}${yearToUse}`;
};

export function getRtnPrdFromFyAndQtr(financialYear: string, month: string) {
    const [startYearPart, endYearPart] = financialYear.split('-');
    const startYear = `${startYearPart}`;
    const endYear = `20${endYearPart}`;
    const monthMapping = {
        'Q1 (Apr - Jun)': '06',
        'Q2 (Jul - Sep)': '09',
        'Q3 (Oct - Dec)': '12',
        'Q4 (Jan - Mar)': '03',
    };
    const yearToUse = (month === 'Q4 (Jan - Mar)') ? endYear : startYear;
    return `${monthMapping[month]}${yearToUse}`;
};


export function getDatesBetween(startDate, endDate) {
    const start = new Date(startDate);
    const end = new Date(endDate);
    const dates = [];

    while (start <= end) {
        dates.push(start.toISOString().split('T')[0]);
        start.setDate(start.getDate() + 1);
    }

    return dates;
};



