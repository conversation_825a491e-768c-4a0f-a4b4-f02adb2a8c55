import { Optional } from '@nestjs/common';
import Client from 'src/modules/clients/entity/client.entity';
import { Organization } from 'src/modules/organization/entities/organization.entity';
import {
  BaseEntity,
  Column,
  CreateDateColumn,
  Entity,
  JoinColumn,
  ManyToOne,
  OneToMany,
  OneToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import AutIncometaxReturns from './aut_incometax_returns.entity';
import AutoIncomeTaxForms from './aut_income_tax_forms.entity';
import AutomationMachines from './automation_machines.entity';
import Password from 'src/modules/clients/entity/password.entity';
import AutIncometaxEChallan from './aut_incometax_e-challan.entity';
import AutMycas from './aut_incometax_my-cas.entity';
import AutOutstandingDemand from './aut-outstanding-demand.entity';
import { decrypt, encrypt } from 'src/utils/encryption';

export enum syncStatus {
  SYNC = 'SYNC',
  NOTSYNC = 'NOT-SYNC',
}

export enum IncomeTaxStatus {
  ENABLE = 'ENABLE',
  DISABLE = 'DISABLE',
}
// ENUM('ENABLE', 'DISABLE')
@Entity()
class AutClientCredentials extends BaseEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  panNumber: string;

  // @Column()
    @Column({
    transformer: {
      to: (value: string) => (value ? encrypt(value) : value),
      from: (value: string) => (value ? decrypt(value) : value),
    },
  })
  password: string;

  @Column({ type: 'enum', enum: syncStatus })
  syncStatus: syncStatus;

  @Column()
  organizationId: number;

  @Column()
  clientId: number;

  @Column({ type: 'enum', enum: IncomeTaxStatus, default: IncomeTaxStatus.ENABLE })
  status: IncomeTaxStatus;

  @Column()
  passwordId: number;

  @ManyToOne(() => Client, (client) => client.autClientCredentials, { onDelete: 'SET NULL' })
  client: Client;

  @OneToMany(() => AutIncometaxReturns, (autReturns) => autReturns.autClientCredentials, {
    onDelete: 'SET NULL',
  })
  autReturns: AutIncometaxReturns;

  @OneToMany(() => AutoIncomeTaxForms, (autForms) => autForms.autClientCredentials, {
    onDelete: 'SET NULL',
  })
  autForms: AutoIncomeTaxForms;

  
  @OneToMany(() => AutOutstandingDemand, (autDemand) => autDemand.autClientCredentials, {
    onDelete: 'SET NULL',
  })
  autDemand: AutOutstandingDemand;

  @OneToMany(() => AutomationMachines, (automationMachines) => automationMachines.autoCredentials)
  autoCredentials: AutomationMachines[];

  @OneToOne(() => Password, (password) => password.autClientCredentials, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'password_id' })
  passwordI: Password;

  @OneToMany(() => AutIncometaxEChallan, (autChallan) => autChallan.autClientCredentials, {
    onDelete: 'SET NULL',
  })
  autChallan: AutIncometaxEChallan;

  @OneToMany(() => AutMycas, (autMycas) => autMycas.autClientCredentials, {
    onDelete: 'SET NULL',
  })
  autMycas: AutMycas;

  @Column()
  remarks:string;

  @CreateDateColumn()
  createdAt: string;

  @UpdateDateColumn()
  updatedAt: string;
}

export default AutClientCredentials;
