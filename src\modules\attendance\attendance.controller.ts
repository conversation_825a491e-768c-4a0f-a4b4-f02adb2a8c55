import {
  BadRequestException,
  Body,
  Controller,
  Get,
  Param,
  ParseIntPipe,
  Patch,
  Post,
  Put,
  Query,
  Req,
  Request,
  UseGuards,
  Res,
} from '@nestjs/common';
import { JwtAuthGuard } from '../users/jwt/jwt-auth.guard';
import { User } from '../users/entities/user.entity';
import { Response } from 'express';
import { AttendanceService } from './attendance.service';
import { OrganizationPreferencesService } from '../organization-preferences/organization-preferences.service';

@Controller('attendance')
export class AttendanceController {
  constructor(private readonly service: AttendanceService) { }

  @UseGuards(JwtAuthGuard)
  @Get()
  async getDates(@Req() req: any, @Query() query) {
    const { date, user } = query;
    const { userId } = req.user;
    return this.service.getDates(date, user, userId);
  }

  @UseGuards(JwtAuthGuard)
  @Get('date')
  async getAddDates(@Req() req: any, @Query() query) {
    const { date, user } = query;
    const { userId } = req.user;
    return this.service.getAddDates(date, user, userId);
  }

  @UseGuards(JwtAuthGuard)
  @Get('getAttendance')
  async getAttendance(@Request() req: any) {
    const { userId } = req.user;
    return this.service.get(userId);
  }


  @UseGuards(JwtAuthGuard)
  @Get('get-notes')
  async getNotes(@Request() req: any, @Query() query) {
    const { userId } = req.user;
    return this.service.getNotes(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Post('getAttendance-report')
  async getAttendanceReport(
    @Request() req: any,
    @Body() body: any,
    @Res() res: Response,  // inject Express Response
  ) {
    const { userId } = req.user;
    const { fileBuffer, month, year } = await this.service.getAttendanceReport(userId, body);

    const user = await User.findOne({ where: { id: body.user } });
    const fileName = `${user.fullName}_attendance_${String(month).padStart(2, '0')}_${year}.xlsx`;

    // Send raw Excel file
    res.set({
      'Content-Type': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'Content-Disposition': `attachment; filename="${fileName}"`,
    });
    res.send(fileBuffer); // send raw buffer, NOT JSON
  }
  @UseGuards(JwtAuthGuard)
  @Post()
  async updateAttendance(@Request() req: any, @Body() body) {
    const { userId } = req.user;
    // return this.service.post(userId, body);
    return this.service.addAttendance(userId, body);
  }

  @UseGuards(JwtAuthGuard)
  @Get('getUserDailyAttendance')
  async getUserDailyAttendance(@Request() req: any, @Query() query) {
    const { userId } = req.user;
    return this.service.getUserDailyAttendance(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Get('getIndianDataTime')
  async getIndianDataTime(@Request() req: any, @Query() query) {
    const { userId } = req.user;
    return this.service.getIndianDataTime(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Post('check-in')
  async UserClickCheckIn(@Request() req: any, @Body() body) {
    const { userId } = req.user;
    return this.service.UserClickCheckIn(userId, body);
  }

  @UseGuards(JwtAuthGuard)
  @Post('check-out')
  async UserClickCheckOut(@Request() req: any, @Body() body) {
    const { userId } = req.user;
    return this.service.UserClickCheckOut(userId, body);
  }

  @UseGuards(JwtAuthGuard)
  @Put('/status-change')
  async approvalsStatusChange(@Request() req: any, @Body() body) {
    const { userId } = req.user;
    return this.service.approvalsStatusChange(userId, body);
  }
}
