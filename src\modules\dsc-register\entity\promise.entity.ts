import Client from 'src/modules/clients/entity/client.entity';
import { Organization } from 'src/modules/organization/entities/organization.entity';
import {
  BaseEntity,
  Column,
  CreateDateColumn,
  Entity,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';

@Entity()
class Promise extends BaseEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ nullable: true })
  request: string;

  @Column({ type: 'date' })
  lastUpdated: string;

  @Column({ nullable: true })
  response: string;

  @Column({ nullable: true })
  vendor: string;

  @Column({ type: 'datetime' })
  insertedTime: string;

  @Column({ nullable: true })
  userId: number;

  @Column({ nullable: true })
  clientId: number;

  @Column({ nullable: true })
  organizationId: number;

  @Column({ nullable: true })
  taskId: number;

  @Column({ nullable: true })
  invocieId: number;

  @Column({ nullable: true })
  receiptId: number;

}

export default Promise;
