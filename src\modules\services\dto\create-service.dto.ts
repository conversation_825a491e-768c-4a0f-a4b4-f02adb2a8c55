import { Type } from 'class-transformer';
import { IsArray, IsBoolean, IsNotEmpty, IsOptional, IsString, Matches, ValidateNested } from 'class-validator';
import ChecklistDto from './checklist.dto';
import MilestoneDto from './milestone.dto';
import StageOfWorkDto from './stage-of-work.dto';
import SubtaskDto from './subtask.dto';
import { ApplicationTypeDto } from './applicationType.dto';
import { FrequenctDto } from './frequenct.dto';

class CreateServiceDto {
  @IsNotEmpty()
  category: number;

  @IsOptional()
  subCategory: number;

  @IsOptional()
  clientSubCategory: string;

  @IsOptional()
  state: string;

  @IsNotEmpty()
  @IsString()
  @Matches(/^[^<>:"\/\\|?*\.]*$/, {
    message: 'Service Name cannot contain <, >, :, ", /, \\, |, ?, *, or .',
  })
  name: string;

  @IsOptional()
  description: string;

  @IsOptional()
  hourlyPrice: number;

  @IsOptional()
  totalPrice: number;

  @IsOptional()
  @IsArray()
  organizations: [];

  @IsOptional()
  @IsArray()
  @Type(() => ChecklistDto)
  @ValidateNested()
  checklists: Array<ChecklistDto>;

  @IsOptional()
  @IsArray()
  @Type(() => MilestoneDto)
  @ValidateNested()
  milestones: Array<MilestoneDto>;

  @IsOptional()
  @IsArray()
  @Type(() => StageOfWorkDto)
  @ValidateNested()
  stageOfWork: Array<StageOfWorkDto>;

  @IsOptional()
  @IsArray()
  @Type(() => SubtaskDto)
  @ValidateNested()
  subTasks: Array<SubtaskDto>;

  @IsOptional()
  @IsBoolean()
  isActive: boolean;

  @IsOptional()
  content: string;

  @IsOptional()
  serviceReadTime: string;

  @IsOptional()
  prismServiceName: string;

  @IsOptional()
  subtaskServices: string;

  @IsOptional()
  prismPrice: string;

  @IsOptional()
  @IsArray()
  linkedServices: string;

  @IsOptional()
  prismImage: string;

  @IsOptional()
  prismSampleCertificate: string;

  @IsOptional()
  prismProcessImage: string;

  @IsOptional()
  @IsArray()
  prismChecklists: string;

  @IsOptional()
  serviceProcedure: string;

  @IsOptional()
  serviceFaqs: string;

  @IsOptional()
  prismYoutubeLink: string;

  @IsOptional()
  postIsActive: boolean;

  @IsOptional()
  prismDescription: string;

  @IsOptional()
  @IsArray()
  labels: [];

  @IsOptional()
  @IsString()
  applicationType: ApplicationTypeDto;

  @IsOptional()
  @IsString()
  frequency: FrequenctDto;

  @IsOptional()
  isRecurring: boolean;

  @IsOptional()
  recurringFrequencyDetails: Object[];

  @IsOptional()
  servicePreference: boolean;
}

export default CreateServiceDto;
