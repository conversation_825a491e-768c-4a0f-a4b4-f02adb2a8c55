import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>umn,
  <PERSON>reateDate<PERSON><PERSON><PERSON>n,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  ManyToOne,
  OneToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import Client from './client.entity';
import ClientGroup from 'src/modules/client-group/client-group.entity';
import GstrCredentials from 'src/modules/gstr-automation/entity/gstrCredentials.entity';
import AutClientCredentials from 'src/modules/automation/entities/aut_client_credentials.entity';
import TanClientCredentials from 'src/modules/tan-automation/entity/tan-client-credentials.entity';

export enum IsExistingAtomPro {
  YES = 'YES',
  NO = 'NO',
}

@Entity()
class Password extends BaseEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  website: string;

  @Column()
  websiteUrl: string;

  @Column()
  loginId: string;

  @Column()
  password: string;

  @ManyToOne(() => Client, (client) => client.passwords, { onDelete: 'CASCADE' })
  client: Client;

  @ManyToOne(() => ClientGroup, (clientGroup) => clientGroup.passwords, { onDelete: 'CASCADE' })
  clientGroup: ClientGroup;

  @OneToOne(() => AutClientCredentials, (autClientCredentials) => autClientCredentials.passwordI, {
    onDelete: 'CASCADE',
  })
  autClientCredentials: AutClientCredentials;

  @OneToOne(() => GstrCredentials, (gstrCredentials) => gstrCredentials.passwordI, {
    onDelete: 'CASCADE',
  })
  gstrCredentials: GstrCredentials;

  @OneToOne(() => TanClientCredentials, (tanClientCredentials) => tanClientCredentials.passwordI, {
    onDelete: 'CASCADE',
  })
  tanClientCredentials: TanClientCredentials;

  @CreateDateColumn()
  createdAt: string;

  @UpdateDateColumn()
  updatedAt: string;

  @Column({ type: 'enum', enum: IsExistingAtomPro })
  isExistingAtomPro: IsExistingAtomPro;

  @Column()
  tracesTan: string;
}

export default Password;
