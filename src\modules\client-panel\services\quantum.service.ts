import { Injectable, NotFoundException } from '@nestjs/common';
import { User } from 'src/modules/users/entities/user.entity';
import { createQ<PERSON><PERSON><PERSON><PERSON><PERSON>, get<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Not } from 'typeorm';
import axios from 'axios';
import QtmActivity from 'src/modules/admin/entities/qtmActivity.entity';
import QtmTemplate from 'src/modules/admin/entities/quantumTemplateFeed.entity';
import OrganizationPreferences from 'src/modules/organization-preferences/entity/organization-preferences.entity';
import { error } from 'console';
import AtomToQtmrequests from 'src/modules/quantum/entity/atm-qtm-requests.entity';
import QtmCategories from 'src/modules/admin/entities/quantumCategories';

@Injectable()
export class QuantumService {

  async getCreatedTemplates(userId, query) {
    try {
      const { limit, offset } = query;

      let qtmActivity = createQueryBuilder(QtmActivity, 'qtmActivity')
        .leftJoinAndSelect('qtmActivity.user', 'user')
        .leftJoinAndSelect('qtmActivity.task', 'task')
        .where('qtmActivity.taskId = :taskId', { taskId: query?.taskId });

      if (query.search) {
        qtmActivity.andWhere('qtmActivity.templateName like :search', {
          search: `%${query.search}%`,
        });
      }
      if (offset >= 0) {
        qtmActivity.skip(offset);
      }

      if (limit) {
        qtmActivity.take(limit);
      }

      qtmActivity.orderBy('task.id', 'DESC');
      let result = await qtmActivity.getManyAndCount();
      return {
        count: result[1],
        result: result[0],
      };
    } catch (err) {
      console.log('error occur while getCreatedTemplates', err);
    }
  }

  async getQtmqtmConfig(userId: number) {
    try {
      let user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
      const orgPreferences = await OrganizationPreferences.findOne({
        where: { organization: user?.organization?.id },
      });
      return orgPreferences;
    } catch (err) {
      console.log('error occur while getting the getQtmqtmConfig', err);
    }
  }
}
