import { Optional } from "@nestjs/common";
import { BaseEntity, Column, Entity, JoinColumn, ManyToOne, OneToOne, PrimaryGeneratedColumn } from "typeorm";
import { User } from "../../users/entities/user.entity";
import Client from "../../clients/entity/client.entity";
import { Organization } from "../../organization/entities/organization.entity";
import GstrRegister from "./gstr-register.entity";



@Entity()
export class GstrPromise extends BaseEntity {
    @PrimaryGeneratedColumn()
    id: number;

    @Optional()
    @Column('json')
    request: string;

    @Optional()
    @Column('json')
    response: string;

    @ManyToOne(() => User, (user) => user.gstrPromises)
    user: User;

    @ManyToOne(() => Client, (client) => client.gstrPromises)
    client: Client;

    @ManyToOne(() => Organization, (organization) => organization.gstrPromises)
    organization: Organization;

    @Column()
    status: string;

    @OneToOne(() => GstrRegister, { cascade: true })
    @JoinColumn()
    gstrRegister: GstrRegister;







}