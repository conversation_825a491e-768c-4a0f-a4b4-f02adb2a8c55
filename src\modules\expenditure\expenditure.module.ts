import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ExpenditureController } from './expenditure.controller';
import Expenditure from './expenditure.entity';
import { ExpenditureService } from './expenditure.service';
import { StorageService } from '../storage/storage.service';
import { AwsService } from '../storage/upload.service';
import { OneDriveStorageService } from '../ondrive-storage/onedrive-storage.service';
import { AttachmentsService } from '../tasks/services/attachments.service';
import { BharathStorageService } from '../storage/bharath-storage.service';
import { BharathCloudService } from '../storage/bharath-upload.service';
import { ExpenditureSubscriber } from 'src/event-subscribers/expenditure.subscriber';
import { GoogleDriveStorageService } from '../ondrive-storage/googledrive-storage.service';

@Module({
  imports: [TypeOrmModule.forFeature([Expenditure])],
  controllers: [ExpenditureController],
  providers: [ExpenditureService,
    StorageService,
    AwsService,
    AttachmentsService,
    OneDriveStorageService,
    BharathStorageService,
    BharathCloudService,
    ExpenditureSubscriber,
    GoogleDriveStorageService
  ],
})
export class ExpenditureModule { }
