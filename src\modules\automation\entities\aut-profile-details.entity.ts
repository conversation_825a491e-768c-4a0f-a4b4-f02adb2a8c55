import { Optional } from '@nestjs/common';
import Client from 'src/modules/clients/entity/client.entity';
import { Organization } from 'src/modules/organization/entities/organization.entity';
import {
  BaseEntity,
  Column,
  CreateDateColumn,
  Entity,
  ManyToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';

@Entity()
class AutProfileDetails extends BaseEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  name: string;

  @Column()
  dob: string;

  @Column()
  pan: string;

  @Column()
  gender: string;

  @Column()
  panStatus: string;

  @Column()
  aadharNumberLinkStatus: string;

  @Column()
  citizenship: string;

  @Column()
  residentialStatus: string;

  @ManyToOne(() => Organization, (organization) => organization.labels)
  organization: Organization;

  @Column()
  primaryNumber: string;

  @Column()
  emailId: string;
  
  @Column()
  secondaryNumber: string;

  @Column()
  secondaryEmailId: string;

  @Optional()
  @Column()
  address: string;

  @Column()
  ClientId: number;

  @ManyToOne(() => Client, (client) => client.autProfileDetails)
  client: Client;

  @CreateDateColumn()
  createdAt: string;

  @UpdateDateColumn()
  updatedAt: string;
}

export default AutProfileDetails;
