import {
  Body,
  Controller,
  Get,
  Param,
  ParseIntPipe,
  Post,
  Put,
  Query,
  Req,
  UseGuards,
} from '@nestjs/common';
import { TanAutomationService } from '../service/tan-automation.service';
import { JwtAuthGuard } from 'src/modules/users/jwt/jwt-auth.guard';

@Controller('incometaxtan')
export class TanAutomationController {
  constructor(private service: TanAutomationService) { }

  @UseGuards(JwtAuthGuard)
  @Get('credentials')
  findAll(@Req() req: any, @Query() query: any) {
    const { userId } = req.user;
    return this.service.findAll(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Post('/client-credentialsexport')
  async exportIncomeTaxTanClients(@Req() req: any, @Body() body: any) {
    const query = body;
    const { userId } = req.user;
    return this.service.exportIncomeTaxTanClients(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Post('credentials')
  addClientTanCredentials(@Body() body: any, @Req() req: any) {
    const { userId } = req.user;
    return this.service.addClientTanCredentials(userId, body);
  }

  @UseGuards(JwtAuthGuard)
  @Get('clients')
  getAllClients(@Req() req: any, @Query() query: any) {
    const { userId } = req.user;
    return this.service.getAllClients(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Get('profile/:id')
  getIncomeTaxProfile(@Param('id', ParseIntPipe) id: number, @Req() req: any) {
    const { userId } = req.user;
    return this.service.getIncomeTaxProfile(userId, id);
  }

  @UseGuards(JwtAuthGuard)
  @Get('clientechallan/:id')
  clientEChallan(@Param('id', ParseIntPipe) id: number, @Query() query: any, @Req() req: any) {
    const { userId } = req.user;
    return this.service.clientEChallan(userId, query, id);
  }

  @UseGuards(JwtAuthGuard)
  @Post('/tanClientChallan-export')
  async exportTanClientChallan(@Req() req: any, @Body() body: any) {
    const query = body;
    const { userId } = req.user;
    return this.service.exportTanClientChallan(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Get('e-challan/:id')
  findEchallan(@Req() req: any, @Param('id', ParseIntPipe) id: number) {
    const { userId } = req.user;
    return this.service.findEchallan(userId, id);
  }

  @UseGuards(JwtAuthGuard)
  @Get('e-challans')
  findEchallans(@Req() req: any, @Query() query: any) {
    const { userId } = req.user;
    return this.service.findEchallans(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Post('/e-challans-export')
  async exportTanIncomeTaxChallans(@Req() req: any, @Body() body: any) {
    const query = body;
    const { userId } = req.user;
    return this.service.exportTanIncomeTaxChallans(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Get('clientform/:id')
  getClientform(@Param('id', ParseIntPipe) id: number, @Query() query: any, @Req() req: any) {
    const { userId } = req.user;
    return this.service.getClientform(id, query, userId);
  }

  @UseGuards(JwtAuthGuard)
  @Post('/tanClientForm-export')
  async exportTanClientForm(@Req() req: any, @Body() body: any) {
    const query = body;
    const { userId } = req.user;
    return this.service.exportTanClientForm(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Get('form/:id')
  findForm(@Req() req: any, @Param('id', ParseIntPipe) id: number) {
    const { userId } = req.user;
    return this.service.findForm(userId, id);
  }

  @UseGuards(JwtAuthGuard)
  @Get('forms')
  findAllForms(@Req() req: any, @Query() query: any) {
    const { userId } = req.user;
    return this.service.findAllForms(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Post('/forms-export')
  async exportTanIncomeTaxForms(@Req() req: any, @Body() body: any) {
    const query = body;
    const { userId } = req.user;
    return this.service.exportTanIncomeTaxForms(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Get('activity/:id')
  getActivityLogData(@Req() req: any, @Param('id', ParseIntPipe) id: number, @Query() query: any) {
    const { userId } = req.user;
    return this.service.getActivityLogData(id, query, userId);
  }

  @UseGuards(JwtAuthGuard)
  @Get('reports')
  getclientReport(@Req() req: any, @Query() query: any) {
    const { userId } = req.user;
    return this.service.getclientReport(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Post('/exportTanSyncStatus-export')
  async exportTanSyncStatus(@Req() req: any, @Body() body: any) {
    const query = body;
    const { userId } = req.user;
    return this.service.exportTanSyncStatus(userId, query);
  }


  @UseGuards(JwtAuthGuard)
  @Post('/lastSync-export')
  async exportTanIncomeTaxReports(@Req() req: any, @Body() body: any) {
    const query = body;
    const { userId } = req.user;
    return this.service.exportTanIncomeTaxReports(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Get('clientAutoStatus/:id')
  getclientAutoStatus(@Param('id', ParseIntPipe) id: number, @Req() req: any) {
    const { userId } = req.user;
    return this.service.getclientAutoStatus(id, userId);
  }

  @UseGuards(JwtAuthGuard)
  @Get('my-cas')
  findMycas(@Req() req: any, @Query() query: any) {
    const { userId } = req.user;
    return this.service.findMycas(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Post('/myCas-export')
  async exportTanIncomeTaxMycas(@Req() req: any, @Body() body: any) {
    const query = body;
    const { userId } = req.user;
    return this.service.exportTanIncomeTaxMycas(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Get('form-types')
  getMycaFormTypes(@Req() req: any) {
    const { userId } = req.user;
    return this.service.getMycaFormTypes(userId);
  }

  @UseGuards(JwtAuthGuard)
  @Get('mycas/:id')
  clientMycas(@Param('id', ParseIntPipe) id: number, @Query() query: any, @Req() req: any) {
    const { userId } = req.user;
    return this.service.clientMycas(userId, query, id);
  }

  @UseGuards(JwtAuthGuard)
  @Post('/tanMyCas-export')
  async exportTanClientMyCas(@Req() req: any, @Body() body: any) {
    const query = body;
    const { userId } = req.user;
    return this.service.exportTanClientMyCas(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Put('credentials/:id')
  update(@Param('id', ParseIntPipe) id: number, @Body() body, @Req() req: any) {
    const { userId } = req.user;
    return this.service.updateClientTanCredentials(id, body, userId);
  }

  @UseGuards(JwtAuthGuard)
  @Get('traces-communication/:id')
  getClientTraceCommunications(
    @Param('id', ParseIntPipe) id: number,
    @Query() query: any,
    @Req() req: any,
  ) {
    const { userId } = req.user;
    return this.service.getClientTraceCommunications(id, query, userId);
  }
  @UseGuards(JwtAuthGuard)
  @Post('/clientTanTraces-export')
  async exportClientTanTracesInbox(@Req() req: any, @Body() body: any) {
    const query = body;
    const { userId } = req.user;
    return this.service.exportClientTanTracesInbox(userId, query);
  }
  @UseGuards(JwtAuthGuard)
  @Get('traces-communications')
  findAllTraceCommunication(@Req() req: any, @Query() query: any) {
    const { userId } = req.user;
    return this.service.findAllTraceCommunication(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Post('/clientTraces-export')
  async exportClientTracesInbox(@Req() req: any, @Body() body: any) {
    const query = body;
    const { userId } = req.user;
    return this.service.exportClientTracesInbox(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Get('activity-traces/:id')
  getActivityLogTracesData(
    @Req() req: any,
    @Param('id', ParseIntPipe) id: number,
    @Query() query: any,
  ) {
    const { userId } = req.user;
    return this.service.getActivityLogTracesData(id, query, userId);
  }

  @UseGuards(JwtAuthGuard)
  @Get('clientAutoStatus-traces/:id')
  getclientAutoTracesStatus(@Param('id', ParseIntPipe) id: number, @Req() req: any) {
    const { userId } = req.user;
    return this.service.getclientAutoTracesStatus(id, userId);
  }

  @UseGuards(JwtAuthGuard)
  @Get('traces-reports')
  getTraceReport(@Req() req: any, @Query() query: any) {
    const { userId } = req.user;
    return this.service.getTraceReport(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Post('/exportTanTraceSyncStatus-export')
  async exportTanTraceSyncStatus(@Req() req: any, @Body() body: any) {
    const query = body;
    const { userId } = req.user;
    return this.service.exportTanTraceSyncStatus(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Get('temp-notice-fya')
  findFyaTempNotices(@Req() req: any, @Query() query: any) {
    const { userId } = req.user;
    return this.service.findFyaTempNotices(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Get('temp-notice-fyi')
  findFyiTempNotices(@Req() req: any, @Query() query: any) {
    const { userId } = req.user;
    return this.service.findFyiTempNotices(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Get('sections-fya-excel')
  getExcelFyaSections(@Req() req: any) {
    const { userId } = req.user;
    return this.service.getExcelFyaSections(userId);
  }

  @UseGuards(JwtAuthGuard)
  @Get('sections-fyi-excel')
  getExcelFyiSections(@Req() req: any) {
    const { userId } = req.user;
    return this.service.getExcelFyiSections(userId);
  }

  @UseGuards(JwtAuthGuard)
  @Get('clientproceedingfyi-excel/:id')
  getClientExcelProceedingFyi(
    @Param('id', ParseIntPipe) id: number,
    @Query() query: any,
    @Req() req: any,
  ) {
    const { userId } = req.user;
    return this.service.getClientExcelProceedingFyi(id, query, userId);
  }

  @UseGuards(JwtAuthGuard)
  @Get('clientproceedingfya-excel/:id')
  getClientExcelProceedingFya(
    @Param('id', ParseIntPipe) id: number,
    @Query() query: any,
    @Req() req: any,
  ) {
    const { userId } = req.user;
    return this.service.getClientExcelProceedingFya(id, query, userId);
  }

  @UseGuards(JwtAuthGuard)
  @Get('eproceeding-notice-excel')
  getExcelFyiNotices(@Req() req: any, @Query() query: any) {
    const { userId } = req.user;
    return this.service.getExcelCombinedNotices(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Get('demands')
  findAllDemands(@Req() req: any, @Query() query: any) {
    const { userId } = req.user;
    return this.service.findAllDemands(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Get('demand/:id')
  findDeamnd(@Req() req: any, @Param('id', ParseIntPipe) id: number) {
    const { userId } = req.user;
    return this.service.findDemand(userId, id);
  }

  @UseGuards(JwtAuthGuard)
  @Get('clientDemand/:id')
  getClientDemand(@Param('id', ParseIntPipe) id: number, @Query() query: any, @Req() req: any) {
    const { userId } = req.user;
    return this.service.getClientDemand(id, query, userId);
  }

    @UseGuards(JwtAuthGuard)
    @Post('/demands-export')
    async exportDemands(@Req() req: any, @Body() body: any) {
      const { userId } = req?.user;
      const query = body;
      return this.service.exportDemands(userId, query);
    }

      @UseGuards(JwtAuthGuard)
      @Post('/export-clientdemand')
      async exportClientDemand(@Req() req: any, @Body() body: any) {
        const { userId } = req?.user;
        const query = body;
        return this.service.exportClientDemand(userId, query);
      }

}
