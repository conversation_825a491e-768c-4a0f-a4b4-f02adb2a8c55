import {
  Body,
  Get,
  Param,
  ParseIntPipe,
  Post,
  Query,
  UploadedFiles,
  UseGuards,
  UseInterceptors,
  Request,
  Controller,
} from '@nestjs/common';
import { FilesInterceptor } from '@nestjs/platform-express';
import { JwtAuthGuard } from 'src/modules/users/jwt/jwt-auth.guard';
import { AutIncomeTaxFormsController } from './income-tax-forms.controller';

export class AttachmentFyaController extends AutIncomeTaxFormsController {
  @UseGuards(JwtAuthGuard)
  @Post('/:autFyaNoticeId/attachments')
  @UseInterceptors(FilesInterceptor('files'))
  addAttachments(
    @UploadedFiles() files: Express.Multer.File[],
    @Param('autFyaNoticeId', ParseIntPipe) autFyaNoticeId: number,
    @Request() req,
    @Body() body: any,
  ) {
    const { userId } = req.user;
    return this.attachmentFyaService.saveAttachment(autFyaNoticeId, files, userId, body);
  }
}
