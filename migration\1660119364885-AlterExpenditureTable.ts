import { MigrationInterface, QueryRunner } from 'typeorm';

export class AlterExpenditureTable1660119364885 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
        ALTER TABLE expenditure
        MODIFY COLUMN task_expense_type enum('PURE_AGENT','ADDITIONAL') null
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}
