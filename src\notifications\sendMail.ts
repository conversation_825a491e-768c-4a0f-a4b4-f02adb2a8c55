import { InternalServerErrorException } from '@nestjs/common';
import axios from 'axios';
import * as moment from 'moment';

export function sendEspoMail(data: any) {

  try {
    axios({
      url: 'http://vidersupport.com/espo/crm/api/v1/AtomEmails',
      method: 'POST',
      headers: {
        'X-Api-Key': 'cb7397dc8250e64516602f5894f7bf5f',
      },
      data: {
        assignedUserId: '1',
        assignedUserName: 'Admin',
        atomID: 'atom11',
        attachments: data.attachments || '',
        bccid: data.bccid || '',
        body: data.body || '',
        cCid: data.cCid || '',
        description: data.description || '',
        emailType: data.emailType || '',
        fromaddress: data.fromAddress || '',
        name: data.name || 'default',
        preferredsource: data.preferredsource || '',
        signature: data.signature || '',
        subject: data.subject || '',
        templateid: data.templateid || '',
        toAddress: data.toAddress || '',
        clientName: data.clientName || '',
        orgName: data.orgName || '',
        payload: data.payload || '',
        status: data.status || 'Active',
        link: data.link,
      },
    }).then((res) => res);
  } catch (error) {
    console.log(error);
    throw new InternalServerErrorException(error);
  }
}

async function organizationSignup(data: any) {
  const newRecordPayload = [];
  try {
    axios({
      url: 'http://vidersupport.com/espo/crm/api/v1/EmailTemplate',
      method: 'GET',
      headers: {
        'X-Api-Key': 'cb7397dc8250e64516602f5894f7bf5f',
      }
    }).then((templates: any) => {

      if (templates?.data?.list && templates?.data?.list.length > 0) {
        templates?.data?.list.forEach((template) => {
          if (template?.name && template?.name == 'organizationSignUp') {
            newRecordPayload.push({ template: template?.name, client: true, user: true })
          } else {
            newRecordPayload.push({ template: template?.name, client: false, user: false })
          }
        });
        axios({
          url: 'http://vidersupport.com/espo/crm/api/v1/EmailPrefernces',
          method: 'POST',
          headers: {
            'X-Api-Key': 'cb7397dc8250e64516602f5894f7bf5f',
          },
          data: {
            assignedUserId: "1",
            assignedUserName: "Admin",
            name: data.orgName,
            orgName: data.orgName,
            orgnizationId: data.orgId,
            orgEmail: data.email,
            payload: JSON.stringify(newRecordPayload),
            teamsIds: [],
            teamsNames: {},
            template: ""
          },
        }).then((res) => {
          sendEspoMail({
            name: 'organizationSignUp-TEST',
            templateid: 'organizationSignUp',
            fromAddress: '<EMAIL>',
            toAddress: data?.email,
            subject: 'Organization Signup',
            body: 'Organization Signup',
            preferredsource: 'Atom',
            payload: JSON.stringify({
              viderLeadName: `${data?.orgName}`,
              viderOrganizationId: `${data?.orgId}`,
            }),
          });
        });
      }

    });
  } catch (error) {
    console.log(error);
    throw new InternalServerErrorException(error);
  }
}

async function leadCreated(lead: any) {
  sendEspoMail({
    fromAddress: '<EMAIL>',
    toAddress: lead?.email,
    subject: 'Lead Created',
    body: 'Lead Created',
    preferredsource: 'Atom',
    templateid: 'leadCreated',
    name: 'Lead-Created-TEST1',
    clientName: 'leadClientName',
    payload: JSON.stringify({
      viderLeadName: `${lead?.name}`,
      viderOrganizationName: `${lead?.organization?.legalName}`,
      viderOrganizationId: `${lead?.organization?.id}`
    }),
  });
}

async function taskCreated(taskCreationData: any) {
  const flatten = (nums) => {
    return nums.reduce(
      (acc, ele) => {
        if (Array.isArray(ele)) {
          acc = { ...acc, num: [...flatten(ele)] };
        } else {
          acc = { ...acc, num: [...acc.num, ele] };
        }
        return acc;
      },
      { num: [] },
    );
  };
  let { num } = flatten(taskCreationData?.event?.members);
  let memberNames = num.map((member) => member.fullName).toString();

  taskCreationData?.event?.clients.forEach((client) => {
    sendEspoMail({
      fromAddress: '<EMAIL>',
      toAddress: client?.email,
      subject: 'Task Created',
      body: 'Task-Created',
      preferredsource: 'Atom',
      templateid: 'taskCreatedForAClient',
      name: 'Task_Created_TEST',
      payload: JSON.stringify({
        viderClientName: client.displayName,
        viderTaskName: taskCreationData?.event?.data?.name
          ? `${taskCreationData?.event?.data?.name}`
          : `${taskCreationData?.event?.service?.name}`,
        viderOrgnizationName: `${taskCreationData?.user?.organization?.legalName}`,
        viderOrganizationId: `${taskCreationData?.user?.organization?.id}`,
        viderTaskId:
          taskCreationData?.user?.fullName.slice(0, 2) + '000' + Math.floor(Math.random() * 10) + 1,
        viderStartDate: moment(taskCreationData?.event?.data?.startDate).format('DD-MM-YYYY'),
        viderDueDate: moment(taskCreationData?.event?.data?.expectedCompletionDate).format(
          'DD-MM-YYYY',
        ),
        viderAssigneNames: memberNames,
      }),
    });
    num.forEach((member) => {
      sendEspoMail({
        fromAddress: '<EMAIL>',
        toAddress: member?.email,
        subject: 'Task Created',
        body: 'task created to you',
        preferredsource: 'Atom',
        templateid: 'taskCreatedForMember',
        name: 'taskCreatedForMembers_TEST',
        payload: JSON.stringify({
          viderMemberName: `${member?.fullName}`,
          viderOrgnizationName: `${taskCreationData?.user?.organization?.legalName}`,
          viderOrganizationId: '',
          viderTaskName: taskCreationData?.event?.data?.name
            ? `${taskCreationData?.event?.data?.name}`
            : `${taskCreationData?.event?.service?.name}`,
          viderClientName: client.displayName,
          viderTaskId:
            taskCreationData?.user?.fullName.slice(0, 2) +
            '000' +
            Math.floor(Math.random() * 10) +
            1,
          viderStartDate: moment(taskCreationData?.event?.data?.startDate).format('DD-MM-YYYY'),
          viderDueDate: moment(taskCreationData?.event?.data?.expectedCompletionDate).format(
            'DD-MM-YYYY',
          ),
          viderAssineesNames: memberNames,
        }),
      });
    });
  });
}

async function subTaskCreated(subTaskCreationData: any) {
  let { task } = subTaskCreationData;

  task.members.map((member) => {
    sendEspoMail({
      fromAddress: '<EMAIL>',
      toAddress: member?.email,
      subject: 'Sub-Task Created',
      body: 'Sub-Task-Created',
      preferredsource: 'Atom',
      templateid: 'subTaskCreatedForMember',
      name: 'Sub_Task_Created_TEST',
      payload: JSON.stringify({
        viderMemberName: `${member?.fullName}`,
        viderOrganizationName: `${task?.organization?.legalName}`,
        viderOrganizationId: `${task?.organization?.id}`,
        viderTaskName: `${task?.parentTask?.name}`,
        viderClientName: `${task?.client?.displayName}`,
        viderTaskID: `${task?.taskNumber}`,
        viderSubTaskName: `${task?.name}`,
        viderDueDate: moment(task?.dueDate).format('DD-MM-YYYY'),
        viderAsignees: task?.members?.map((member) => member.fullName).toString(),
      }),
    });
  });

  let membersNames = task?.members?.map((member) => member.fullName).toString();
  sendEspoMail({
    fromAddress: '<EMAIL>',
    toAddress: task.client.email,
    subject: 'Sub-Task Created',
    body: 'Sub-Task-Created',
    preferredsource: 'Atom',
    templateid: 'subTaskCreatedForAClient',
    name: 'Sub_Task_Created_TEST',
    payload: JSON.stringify({
      viderClientName: `${task?.client?.displayName}`,
      viderOrganizationId: '',
      viderTaskName: `${task?.parentTask?.name}`,
      viderTaskID: `${task?.taskNumber}`,
      viderSubTaskName: `${task?.name}`,
      viderDueDate: moment(task?.dueDate).format('DD-MM-YYYY'),
      viderAsignees: membersNames,
    }),
  });
}

async function taskDeleted(taskDeletedData: any) {
  let { task, user } = taskDeletedData;
  sendEspoMail({
    fromAddress: '<EMAIL>',
    toAddress: user?.email,
    subject: 'Task Deleted',
    body: 'Task-Deleted',
    preferredsource: 'Atom',
    templateid: 'taskDeleted',
    name: 'taskDeleted_TEST',
    payload: JSON.stringify({
      viderMemberName: `${task?.client?.displayName}`,
      viderTaskName: `${task?.name}`,
      viderOrganizationName: `${user?.organization?.legalName}`,
      viderOrganizationId: `${user?.organization?.id}`,
      viderDateOfTaskCreation: moment(task?.createdAt).format('DD-MM-YYYY'),
      viderDeletionDate: moment(task?.dueDate).format('DD-MM-YYYY'),
      viderTaskId: `${task?.taskNumber}`,
      viderCompName: `${user?.organization?.legalName}`,
      viderCompRegStartDate: moment(task?.receivedDate?.dateOfFormation).format('DD-MM-YYYY'),
      viderCompRegEndDate: moment(task?.receivedDate?.dateOfFormation).format('DD-MM-YYYY'),
      viderAssignedTo: user?.fullName,
    }),
  });
}

async function taskUpdateStatusDone(taskStatusUpdatedData: any) {
  let { task, user } = taskStatusUpdatedData;

  const flatten = (nums) => {
    return nums.reduce(
      (acc, ele) => {
        if (Array.isArray(ele)) {
          acc = { ...acc, num: [...flatten(ele)] };
        } else {
          acc = { ...acc, num: [...acc.num, ele] };
        }
        return acc;
      },
      { num: [] },
    );
  };

  let { num } = flatten(taskStatusUpdatedData?.task?.members);
  let memberNames = num.map((member) => member.fullName).toString();

  num.forEach((member) => {
    sendEspoMail({
      fromAddress: '<EMAIL>',
      toAddress: member?.email,
      subject: 'Task Status Moved To Done',
      body: 'Task-Status-Moved-To-Done',
      preferredsource: 'Atom',
      templateid: 'taskStatusUpdatedForUser',
      name: 'taskStatusUpdatedDone-TEST',
      payload: JSON.stringify({
        viderMemberName: `${member?.fullName}`,
        viderOrganizationName: `${task?.user?.organization?.legalName}`,
        viderOrganizationId: `${task?.user?.organization?.id}`,
        viderMemberDone: user?.fullName === member?.fullName ? 'You' : `${user?.fullName}`,
        viderTaskId: `${task?.taskNumber}`,
        viderTaskName: `${task?.name}`,
        viderToStatus: `${task?.status}`,
        viderClientName: `${task?.client?.displayName}`,
        viderAssignees: memberNames,
      }),
    });
  });

  sendEspoMail({
    fromAddress: '<EMAIL>',
    toAddress: task?.client?.email,
    subject: 'Task Status Moved To Done',
    body: 'Task-Status-Moved-To-Done',
    preferredsource: 'Atom',
    templateid: 'taskStatusUpdatedForClient',
    name: 'taskStatusUpdatedDone-TEST',
    payload: JSON.stringify({
      viderClientName: `${task?.client?.displayName}`,
      viderOrganizationName: `${task?.user?.organization?.legalName}`,
      viderOrganizationId: `${task?.user?.organization?.id}`,
      viderMemberName: `${user?.fullName}`,
      viderTaskId: `${task?.taskNumber}`,
      viderTaskName: `${task?.name}`,
      viderToStatus: `${task?.status}`,
      viderStartDate: moment(task?.taskStartDate).format('DD-MM-YYYY'),
      viderEndDate: moment(task?.dueDate).format('DD-MM-YYYY'),
      viderAssignees: memberNames,
    }),
  });
}

async function dscRegisterIssued(dscIssueData: any) {
  let { body, dscRegister } = dscIssueData;

  sendEspoMail({
    fromAddress: '<EMAIL>',
    toAddress: dscRegister?.email,
    subject: 'DSC-Register-Issued',
    body: 'DSC-Register-Issued',
    preferredsource: 'Atom',
    templateid: 'dscRegisterIssued',
    name: 'dscRegisterIssued-TEST',
    payload: JSON.stringify({
      viderClientName: `${dscRegister?.client?.displayName}`,
      viderDSCHolderName: `${dscRegister?.holderName}`,
      viderIssueDate: moment(dscRegister?.issuedDate).format('DD-MM-YYYY'),
      viderExpiryDate: moment(dscRegister?.expiryDate).format('DD-MM-YYYY'),
      viderOrganizationName: `${dscRegister?.client?.legalName}`,
      viderOrganizationId: '',
      viderReceiverName: `${body?.personName}`,
    }),
  });
}

async function dscRegisterReceived(dscReceivedData: any) {
  let { body, dscRegister } = dscReceivedData;
  sendEspoMail({
    fromAddress: '<EMAIL>',
    toAddress: dscRegister?.email,
    subject: 'DSC-Register-Received',
    body: 'DSC-Register-Received',
    preferredsource: 'Atom',
    templateid: 'dscRegisterReceived',
    name: 'dscRegisterReceived-TEST',
    payload: JSON.stringify({
      viderClientName: `${dscRegister?.client?.displayName}`,
      viderDSCHolderName: `${dscRegister?.holderName}`,
      viderDateOfReceipt: moment(dscRegister?.receivedDate).format('DD-MM-YYYY'),
      viderExpiryDate: moment(dscRegister?.expiryDate).format('DD-MM-YYYY'),
      viderOrganizationName: `${dscRegister?.client?.legalName}`,
      viderOrganizationId: `${dscReceivedData?.user?.organization?.id}`,
      viderReceiverName: `${body?.personName}`,
    }),
  });
}

async function dscRegisterExpire(dscExpireData: any) {
  let { dscRegister, numberOfDaysLeft } = dscExpireData;
  sendEspoMail({
    fromAddress: '<EMAIL>',
    toAddress: dscRegister?.email,
    subject: 'DSC-Register-Expire',
    body: 'DSC-Register-Expire',
    preferredsource: 'Atom',
    templateid: 'dscRegisterIsExpiring',
    name: 'dscRegisterIsExpiring-TEST',
    payload: JSON.stringify({
      viderClientName: `${dscRegister?.holderName}`,
      viderOrganizationId: '',
      ViderDSCHolderName: `${dscRegister?.holderName}`,
      viderExpiryDate: moment(dscRegister?.receivedDate).format('DD-MM-YYYY'),
      viderDaysLeft: numberOfDaysLeft,
    }),
  });
}

async function clientCreated(clientCreatedData: any) {
  sendEspoMail({
    fromAddress: '<EMAIL>',
    toAddress: clientCreatedData?.data?.email + ',' + clientCreatedData?.data?.organization?.email,
    subject: 'Clien Creation',
    body: 'Client-Creation',
    preferredsource: 'Atom',
    templateid: 'clientCreation',
    name: 'clientCreation-TEST',
    payload: JSON.stringify({
      viderClientName: `${clientCreatedData?.data?.displayName}`,
      viderOrganizationName: `${clientCreatedData?.orgName}`,
      viderOrganizationId: `${clientCreatedData?.data?.organization?.id}`,
    }),
  });
}

async function eventCreated(eventCreatedData: any) {
  sendEspoMail({
    fromAddress: '<EMAIL>',
    toAddress: eventCreatedData?.event?.user?.email,
    subject: 'Event Created',
    body: 'Event-Created',
    preferredsource: 'Atom',
    templateid: 'eventCreatedAtom',
    name: 'eventCreated-TEST',
    payload: JSON.stringify({
      ViderClientName: `${eventCreatedData?.event?.user?.fullName}`,
      ViderEventName: `${eventCreatedData?.event?.eventData?.title}`,
      viderTaskName: `${eventCreatedData?.event?.eventData?.type}`,
      ViderOrganizationName: `${eventCreatedData?.event?.orgName}`,
      ViderDate: moment(eventCreatedData?.event?.eventData?.date).format('DD-MM-YYYY'),
      ViderStartDate: moment(eventCreatedData?.event?.eventData?.startTime).format('LLLL'),
      ViderEndDate: moment(eventCreatedData?.event?.eventData?.endTime).format('LLLL'),
      ViderLocation: `${eventCreatedData?.event?.eventData?.location}`,
      viderOrganizationId: ''
    }),
  });
}

async function eventCreatedWithTaskData(eventCreatedData: any) {
  sendEspoMail({
    fromAddress: '<EMAIL>',
    toAddress: eventCreatedData?.data?.user?.email,
    subject: 'Event Created',
    body: 'Event-Created',
    preferredsource: 'Atom',
    templateid: 'eventCreatedWithTaskAtom',
    name: 'eventCreatedWithTask-TEST',
    payload: JSON.stringify({
      ViderClientName: `${eventCreatedData?.data?.user?.fullName}`,
      ViderEventName: `${eventCreatedData?.event?.eventData?.title}`,
      ViderTaskName: `${eventCreatedData?.data?.task?.name}`,
      ViderOrganizationName: `${eventCreatedData?.event?.orgName}`,
      ViderDate: moment(eventCreatedData?.event?.eventData?.date).format('DD-MM-YYYY'),
      ViderStartDate: moment(eventCreatedData?.event?.eventData?.startTime).format('LLLL'),
      ViderEndDate: moment(eventCreatedData?.event?.eventData?.endTime).format('LLLL'),
      viderOrganizationId: '',
      ViderLocation: `${eventCreatedData?.event?.eventData?.location}`,
    }),
  });
}

async function userInvite(userInviteData: any) {
  sendEspoMail({
    fromAddress: '<EMAIL>',
    toAddress: userInviteData?.tempevent?.data?.email,
    subject: 'User Invite',
    body: 'User-invite',
    preferredsource: 'Atom',
    templateid: 'userInvited',
    name: 'User_Invite_TEST',
    payload: JSON.stringify({
      viderJoinNowUrl: `${userInviteData?.tempevent?.link}`,
      viderUserName: `${userInviteData?.tempevent?.data?.fullName}`,
      viderOrganizationName: `${userInviteData?.tempevent?.orgName}`,
      viderOrganizationId: `${userInviteData?.tempevent?.orgId}`,
    }),
  });
}

async function dscRegisterAdd(dscRegisterAddData: any) {
  sendEspoMail({
    fromAddress: '<EMAIL>',
    toAddress: dscRegisterAddData?.dscRegister?.email,
    subject: 'dscRegistered',
    body: 'dscRegistered',
    preferredsource: 'Atom',
    templateid: 'dscRegisterAdd',
    name: 'dscRegisterAdd_TEST',
    payload: JSON.stringify({
      viderClientName: `${dscRegisterAddData?.client?.displayName}`,
      viderOrganizationName: `${dscRegisterAddData?.dscRegister?.organization?.legalName}`,
      viderOrganizationId: `${dscRegisterAddData?.dscRegister?.organization?.id}`,
      viderDSCHolderName: `${dscRegisterAddData?.dscRegister?.holderName}`,
      viderDateOfReceipt: moment(dscRegisterAddData?.dscRegister).format('DD-MM-YYYY'),
      viderExpiryDate: moment(dscRegisterAddData?.dscRegister?.expiryDate).format('DD-MM-YYYY'),
    }),
  });
}

export default {
  leadCreated,
  organizationSignup,
  taskCreated,
  subTaskCreated,
  taskDeleted,
  taskUpdateStatusDone,
  dscRegisterIssued,
  dscRegisterReceived,
  dscRegisterExpire,
  clientCreated,
  eventCreated,
  eventCreatedWithTaskData,
  userInvite,
  dscRegisterAdd,
};
