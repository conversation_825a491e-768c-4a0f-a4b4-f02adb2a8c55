import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  ParseIntPipe,
  Post,
  Put,
  Query,
  Req,
  Request,
  UploadedFile,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { JwtAuthGuard } from 'src/modules/users/jwt/jwt-auth.guard';
import AddLogHour, { UpdateLogHour } from './dto/add-log-hour.dto';
import { AddUserLogHourDto } from './dto/add-user-log-hour.dto';
import EndTimerDto from './dto/end-timer.dto';
import {
  GetUserLogHoursDto,
  GetUserLogHoursStatsDto,
  UserLogHoursType,
} from './dto/get-user-log-hours.dto';
import StartTimerDto from './dto/start-timer.dto';
import { LogHoursService } from './loghours.service';
import { FileInterceptor } from '@nestjs/platform-express';
import { EditApprovalsDto } from './dto/edit-approvals.dto';

@Controller('log-hours')
export class LogHoursController {
  constructor(private readonly logHoursService: LogHoursService) { }

  @UseGuards(JwtAuthGuard)
  @Post('/start-timer')
  startTimer(@Request() req: any, @Body() body: StartTimerDto) {
    const { userId } = req.user;
    return this.logHoursService.startTimer(userId, body);
  }

  @UseGuards(JwtAuthGuard)
  @Post('/:id/end-timer')
  endTimer(@Request() req: any, @Body() body: EndTimerDto, @Param('id', ParseIntPipe) id: number) {
    const { userId } = req.user;
    return this.logHoursService.endTimer(id, userId, body);
  }

  @Get()
  getTaskLogHours(@Query('taskId', ParseIntPipe) taskId: number) {
    return this.logHoursService.find(taskId);
  }

  @UseGuards(JwtAuthGuard)
  @Get('/user')
  getUserLogHours(@Request() req: any, @Query() query: GetUserLogHoursDto) {
    const { userId } = req.user;
    const id = query.type === UserLogHoursType.USER ? query.userId : userId;
    return this.logHoursService.findUserLogHours(id, query);
  }

  @UseGuards(JwtAuthGuard)
  @Get('/user/stats')
  getUserLogHoursStats(@Request() req: any, @Query() query: GetUserLogHoursStatsDto) {
    const { userId } = req.user;
    const id = query.type === UserLogHoursType.USER ? query.userId : userId;
    return this.logHoursService.getUserLogHourStats(id, query);
  }

  @UseGuards(JwtAuthGuard)
  @Post('/user/add')
  addUserLogHour(@Body() body: AddUserLogHourDto, @Request() req: any) {
    const { userId } = req.user;
    return this.logHoursService.addUserLogHour(userId, body);
  }

  @UseGuards(JwtAuthGuard)
  @Post('/add')
  addTaskLogHours(@Body() body: AddLogHour, @Request() req: any) {
    const { userId } = req.user;
    return this.logHoursService.add(body, userId);
  }

  @UseGuards(JwtAuthGuard)
  @Put('/status-change')
  async approvalsStatusChange(@Request() req: any, @Body() body: EditApprovalsDto) {
    const { userId } = req.user;
    return this.logHoursService.approvalsStatusChange(userId, body);
  }

  @UseGuards(JwtAuthGuard)
  @Put('/:id')
  updateLogHour(
    @Body() body: UpdateLogHour,
    @Param('id', ParseIntPipe) id: number,
    @Request() req: any,
  ) {
    const { userId } = req.user;
    return this.logHoursService.update(id, body, userId);
  }

  @UseGuards(JwtAuthGuard)
  @Delete('/bulk-delete')
  deleteLogHours(@Request() req: any, @Body() body: any) {
    const { userId } = req.user;
    return this.logHoursService.bulkDelete(userId, body);
  }

  @UseGuards(JwtAuthGuard)
  @Delete('/:id')
  deleteLogHour(@Param('id', ParseIntPipe) id: number, @Request() req: any) {
    const { userId } = req.user;
    return this.logHoursService.delete(id, userId);
  }






  @UseGuards(JwtAuthGuard)
  @Post('timesheet')
  addDailyLogHourTimeSheet(@Request() req: any, @Body() body: any) {
    const { userId } = req.user;
    return this.logHoursService.addDailyLogHourTimeSheet(userId, body);
  }

  @UseGuards(JwtAuthGuard)
  @Get('/user-timesheet')
  getUserTimesheet(@Request() req: any, @Query() query) {
    return this.logHoursService.getUserTimesheet(query);
  }

  @UseGuards(JwtAuthGuard)
  @Post('/usertimesheetexport')
  async exportuserTimeSheetReport(@Request() req: any, @Body() body: GetUserLogHoursStatsDto) {
    const query = body;
    const { userId } = req.user;
    return this.logHoursService.exportuserTimeSheetReport(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Get('/user-timesheet/stats')
  getUserTimesheetStats(@Request() req: any, @Query() query: GetUserLogHoursStatsDto) {
    return this.logHoursService.getUserTimesheetStats(query);
  }

  @UseGuards(JwtAuthGuard)
  @Get('/timesheet-report')
  timesheetReport(@Request() req: any, @Query() query) {
    const { userId } = req.user;
    return this.logHoursService.timesheetReport(userId, query);
  }
  @UseGuards(JwtAuthGuard)
  @Post('/timesheet-export')
  async exportLogHoursTimesheetReport(@Req() req: any, @Body() body: any) {
    const { userId } = req.user;
    return this.logHoursService.exportLogHoursTimesheetReport(userId, body);
  }

  @UseGuards(JwtAuthGuard)
  @Get('/log-hour-time')
  logHourTime(@Request() req: any, @Query() query) {
    const { userId } = req.user;
    return this.logHoursService.logHourTime(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Post('/import')
  @UseInterceptors(FileInterceptor('file'))
  importTimesheet(@UploadedFile() file: Express.Multer.File, @Request() req: any) {
    const { userId } = req.user;
    return this.logHoursService.importTimesheet(userId, file);
  }

  @UseGuards(JwtAuthGuard)
  @Post('/logHourTitle')
  async create(@Body() body: any, @Request() req: any) {
    const { userId } = req.user;
    return this.logHoursService.createLogHourTitle(userId, body);
  }

  @UseGuards(JwtAuthGuard)
  @Get('/logHourTitle')
  async getLogHourTitle(@Request() req: any, @Query() query: any) {
    const { userId } = req.user;
    return this.logHoursService.getLogHourTitle(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Post('/logHourTitle/delete')
  deleteLogHourTitle(@Body() body: any) {
    return this.logHoursService.deleteLogHourTitle(body.ids);
  }

  @UseGuards(JwtAuthGuard)
  @Post('/timesheetStart')
  async timesheetStart(@Body() body: any, @Request() req: any) {
    const { userId } = req.user;
    return this.logHoursService.timesheetStartTime(userId, body);
  }

  @UseGuards(JwtAuthGuard)
  @Get('/timesheetStartTime')
  async getTimesheetStartTime(@Request() req: any, @Query() query: any) {
    const { userId } = req.user;
    return this.logHoursService.getTimesheetStartTime(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Post('/timesheetStopTime')
  async timesheetStopTime(@Body() body: any, @Request() req: any) {
    const { userId } = req.user;
    return this.logHoursService.timesheetStopTime(userId, body);
  }

  @UseGuards(JwtAuthGuard)
  @Post('/timesheetPauseTime')
  async timesheetPauseTime(@Body() body: any, @Request() req: any) {
    const { userId } = req.user;
    return this.logHoursService.timesheetPauseTime(userId, body);
  }

  @UseGuards(JwtAuthGuard)
  @Delete('/timer/:id')
  deleteTimerLoghour(@Param('id', ParseIntPipe) id: number, @Request() req: any) {
    const { userId } = req.user;
    return this.logHoursService.deleteTimerLoghour(id, userId);
  }

  @UseGuards(JwtAuthGuard)
  @Post('/timesheetRestart')
  async timesheetRestartTime(@Body() body: any, @Request() req: any) {
    const { userId } = req.user;
    return this.logHoursService.timesheetRestartTime(userId, body);
  }
}
