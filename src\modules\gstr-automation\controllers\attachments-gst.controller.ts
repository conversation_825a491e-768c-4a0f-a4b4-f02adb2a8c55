import {
  Body,
  Get,
  Param,
  ParseIntPipe,
  Post,
  Query,
  UploadedFiles,
  UseGuards,
  UseInterceptors,
  Request,
  Controller,
} from '@nestjs/common';
import { FilesInterceptor } from '@nestjs/platform-express';
import { JwtAuthGuard } from 'src/modules/users/jwt/jwt-auth.guard';
import { GstrController } from './notices.controllers';

export class AttachmentGstController extends GstrController {
  @UseGuards(JwtAuthGuard)
  @Post('/:noticeId/attachments')
  @UseInterceptors(FilesInterceptor('files'))
  addAttachments(
    @UploadedFiles() files: Express.Multer.File[],
    @Param('noticeId', ParseIntPipe) noticeId: number,
    @Request() req,
    @Body() body: any,
  ) {
    const { userId } = req.user;
    return this.attachmentGstService.saveAttachment(noticeId, files, userId, body?.type);
  }
}
