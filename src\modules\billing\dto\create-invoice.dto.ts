import { Type } from 'class-transformer';
import {
  ArrayMinSize,
  IsArray,
  IsBoolean,
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  ValidateNested,
  isNotEmpty,
} from 'class-validator';
import { TAX_TYPE } from '../entitities/invoice-particular.entity';
import { InvoiceType, TdsRate, TdsSection } from '../entitities/invoice.entity';
import { SupplyTypes } from '../entitities/invoice.entity';
import { AccountType } from 'src/modules/organization/entities/bank-account.entity';

export enum GENERATED_NUMBER_TYPE {
  AUTO_GENERATED = 'AUTO_GENERATED',
  MANUAL = 'MANUAL',
}


class Address {

  @IsOptional()
  id: number;

  @IsOptional()
  legalName: string;

  @IsOptional()
  email: string;

  @IsOptional()
  mobileNumber: string;

  @IsOptional()
  buildingName: string;

  @IsOptional()
  street: string;

  @IsNotEmpty()
  city: string;

  // @IsNotEmpty()
  @IsOptional()
  state: string;

  @IsNotEmpty()
  pincode: string;
}

class BankDetails {
  @IsNotEmpty()
  accountNumber: string;

  @IsNotEmpty()
  ifscCode: string;

  @IsNotEmpty()
  bankName: string;

  @IsNotEmpty()
  branchName: string;

  @IsOptional()
  upiId: string;

  @IsOptional()
  accountName: string;

  @IsOptional()
  accountType: AccountType

  @IsOptional()
  upiAttachment: string;

  @IsOptional()
  upiAttachmentId: string;
}

class OtherParticular {
  @IsNotEmpty()
  name: string;

  @IsNotEmpty()
  amount: number;
}

export class Particular {
  @IsNotEmpty()
  name: string;

  @IsOptional()
  hsn: string;

  @IsNotEmpty()
  units: number;

  @IsNotEmpty()
  rate: number;

  @IsNotEmpty()
  discountType: 'PERCENT' | 'AMOUNT';

  @IsNotEmpty()
  discount: number;

  @IsOptional()
  @IsEnum(TAX_TYPE)
  gst: TAX_TYPE;

  @IsNotEmpty()
  amount: number;

  @IsOptional()
  taskId: number;
}

export class CreateBillingDto {
  @IsNotEmpty()
  @IsNumber()
  client: number;

  @IsOptional()
  @IsString()
  placeOfSupply: string;

  // @IsNotEmpty()
  // @IsNumber()
  @IsOptional()
  approvalHierarchyId: number;

  @IsNotEmpty()
  @IsNumber()
  billingEntity: number;

  @IsNotEmpty()
  @IsArray()
  @ArrayMinSize(1, { message: 'Particulars should not be empty' })
  // @ArrayMinSize(1)
  @ValidateNested()
  @Type(() => Particular)
  particulars: Particular[];

  @IsNotEmpty()
  @ValidateNested()
  @Type(() => OtherParticular)
  otherParticulars: OtherParticular[];

  @IsNotEmpty()
  @ValidateNested()
  @Type(() => Address)
  billingEntityAddress: Address;

  // @IsNotEmpty()
  // @ValidateNested()
  @IsOptional()
  @Type(() => Address)
  shippingAddress: Address;

  // @IsNotEmpty()
  // @ValidateNested()
  @IsOptional()
  @Type(() => Address)
  billingAddress: Address;

  @IsNotEmpty()
  @ValidateNested()
  @Type(() => BankDetails)
  bankDetails: BankDetails;

  @IsNotEmpty()
  terms: string;

  @IsNotEmpty()
  @IsArray()
  termsAndConditions: Array<string>;

  @IsNotEmpty()
  subTotal: number;

  @IsNotEmpty()
  @IsNumber()
  totalGstAmount: number;

  @IsNotEmpty()
  @IsNumber()
  totalCharges: number;

  @IsNotEmpty()
  @IsNumber()
  adjustment: number;

  @IsNotEmpty()
  roundOff: string;

  @IsNotEmpty()
  @IsNumber()
  grandTotal: number;

  @IsNotEmpty()
  @IsBoolean()
  submitForApproval: boolean;

  @IsOptional()
  clientType: string | null;
}



export class CreateInvoiceDto extends CreateBillingDto {

  @IsNotEmpty()
  @IsEnum(GENERATED_NUMBER_TYPE)
  invoiceNumberType: GENERATED_NUMBER_TYPE;

  // @IsNotEmpty()
  // invoiceNumber: string;

  // @IsNotEmpty({ message: 'Prefix Should not be empty' })
  // prifix: string;

  // @IsNotEmpty()
  // invNumber: string;

  @IsNotEmpty({ message: 'Invoice Number Should Not Empty' })
  estimateNumber: string;

  @IsNotEmpty()
  invoiceDate: string;

  @IsNotEmpty()
  invoiceDueDate: string;

  @IsOptional()
  invoiceUser: any;

  @IsOptional()
  termsAndConditions: string[];

  @IsOptional()
  termsAndConditionsCopy: any[];

  @IsOptional()
  whatsappCheck: boolean;

  @IsOptional()
  emailCheck: boolean;

  @IsOptional()
  proformaId: number;

  @IsOptional()
  divideTax: boolean;

  @IsOptional()
  supplyType: SupplyTypes;

  @IsOptional()
  narration: string

  @IsOptional()
  tdsSection: TdsSection;

  @IsOptional()
  tdsRate: TdsRate;

  @IsOptional()
  tdsView: boolean;

  @IsOptional()
  hasTds: boolean;

  @IsOptional()
  receivable: number;

  @IsOptional()
  eSign: boolean

  @IsOptional()
  signatureUser: number;

}
