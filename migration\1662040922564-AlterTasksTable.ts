import { MigrationInterface, QueryRunner } from 'typeorm';

export class AlterTasksTable1662040922564 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
        ALTER TABLE task
        ADD COLUMN service_id int null,
        ADD FOREIGN KEY (service_id) REFERENCES service(id) ON DELETE SET NULL;
  `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}
