import { Injectable, InternalServerErrorException } from '@nestjs/common';
import { User } from 'src/modules/users/entities/user.entity';
// import Client from '../clients/entity/client.entity';
// import Kyb from './kyb.entity';
// import Storage from '../storage/storage.entity';
// import { StorageService } from '../storage/storage.service';
// import Activity, { ActivityType } from '../activity/activity.entity';
import { Event_Actions } from 'src/event-listeners/actions';
import { getTitle } from 'src/utils';
import { ClientPermission } from '../entity/client-permission.entity';

@Injectable()
export class ClientPermissionsService {
    private makeForest(id: number, list: any) {
        return list
          .filter(({ parentId }) => parentId == id)
          .map((item: any) => {
            let children = this.makeForest(item.id, list);
            if (children.length) item.children = children;
            return item;
        });
    }

    async getClientPermissions(userId: number, query: any) {
        let data = await ClientPermission.find();
        let forest = this.makeForest(null, data);
        return forest;
    }

}