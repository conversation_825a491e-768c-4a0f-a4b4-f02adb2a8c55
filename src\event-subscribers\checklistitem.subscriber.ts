import {
  Connection,
  EntitySubscriberInterface,
  EventSubscriber,
  InsertEvent,
  UpdateEvent,
  getManager,
} from 'typeorm';
import ChecklistItem from 'src/modules/tasks/entity/checklist-item.entity';
import { getUserDetails, getUserIDs, insertINTONotificationUpdate } from 'src/utils/re-use';
import { sendnewMail } from 'src/emails/newemails';
import Task from 'src/modules/tasks/entity/task.entity';

@EventSubscriber()
export class ChecklistitemSubscriber implements EntitySubscriberInterface<ChecklistItem> {
  constructor(private readonly connection: Connection) {
    connection.subscribers.push(this);
  }

  listenTo() {
    return ChecklistItem;
  }

  async beforeInsert(event: InsertEvent<ChecklistItem>) {}

  async afterInsert(event: InsertEvent<ChecklistItem>) {
    const { checklist, name: itemName } = event.entity;
    // console.log("Event entify for checkilst",event.entity)
    if (checklist && checklist.id) {
      const { id } = checklist;
      const sqlTask = `SELECT task_id,name from checklist where id=${id}`;
      const entityManager = getManager();
      let task = await entityManager.query(sqlTask);
      const [{ task_id, name: checklistName }] = task;
     
      if (task_id) {
        const taskDetails = await Task.findOne({
          where: { id: task_id },
          relations: ['client', 'organization','clientGroup'],
        });
        const clientId = taskDetails?.client?.clientId;
        if (taskDetails) {
          const taskName = taskDetails?.name;
          const orgId = taskDetails?.organization?.id;
          const clientName = taskDetails?.client ? taskDetails?.client.displayName : taskDetails?.clientGroup?.displayName;
          const title = 'Checklist item added';
          const body = `A new checklist item "<strong>${itemName}</strong>" of the "<strong>${checklistName}</strong>" has been added to "<strong>${taskName}</strong>" of "<strong>${clientName}</strong>".`;
          const users = await getUserIDs(task_id);
          const key = 'CHECKLIST_ITEM_ADDED_PUSH';
          insertINTONotificationUpdate(title, body, users, orgId, key,task_id,clientId);
          if (users) {
            for (let user of users) {
              const taskUserDetails = await getUserDetails(user);
              await sendnewMail({
                id: taskUserDetails?.id,
                key: 'CHECKLIST_ITEM_ADDED_MAIL',
                email: taskUserDetails?.email,
                data: {
                  taskName,
                  clientName: clientName,
                  taskId: taskDetails?.taskNumber,
                  newChecklistItem: itemName,
                  taskUserName: taskUserDetails?.full_name,
                  userId: event.entity['userId']
                },
                filePath: 'checklist-item-added',
                subject: `Checklist Update: ${taskName} of ${clientName}`,
              });
            }
          }
        }
      }
    }
  }

  async beforeUpdate(event: UpdateEvent<ChecklistItem>) {}

  async afterUpdate(event: UpdateEvent<ChecklistItem>) {
    const entityManager = getManager();
    if(event?.entity && event?.entity?.id){
      const { id: checklistItemId, name: checkList } = event.entity;
      const trySql = `SELECT task.id as taskId,
       task.name as taskName,
       task.organization_id as orgId,
       checklist.name as checkListName,
       client.display_name as clientName,
       checklist_item.name as checklistItemName
       FROM checklist_item
       INNER JOIN checklist ON checklist_item.checklist_id= checklist.id
       INNER JOIN task ON checklist.task_id=task.id
       INNER JOIN client ON task.client_id=client.id
       WHERE checklist_item.id=${checklistItemId}`;
      const allDetails = await entityManager.query(trySql);
      if(allDetails.length){
        const [{ taskName, clientName, checklistItemName, taskId, checkListName, orgId }] = allDetails;
        const { status } = event?.entity;
        const title = status === undefined ? 'Checklist item edited' : 'Checklist item status changed';
        const markStatus = status === 'DONE' ? 'marked' : 'un-marked';
        const body =
          status !== undefined
            ? `a checklist item "<strong>${checklistItemName}</strong>" of the "<strong>${checkListName}</strong>" for the "<strong>${taskName}</strong>" of <strong>${clientName}</strong> has been "<strong>${markStatus}</strong>".`
            : `the checklist item "<strong>${checklistItemName}</strong>" of the "<strong>${checkListName}</strong>" for the "<strong>${taskName}</strong>" of <strong>${clientName}</strong> has been edited.`;
        const users = await getUserIDs(taskId);
        // insertINTOnotification(title, body, users, orgId);
        const key = 'CHECKLIST_ITEM_UPDATE_PUSH';
        insertINTONotificationUpdate(title, body, users, orgId, key);  
      }
    }
  }
}
