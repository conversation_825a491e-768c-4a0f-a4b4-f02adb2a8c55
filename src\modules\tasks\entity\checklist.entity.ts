import {
  BaseEntity,
  Column,
  CreateDateColumn,
  Entity,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import ChecklistItem from './checklist-item.entity';
import Task from './task.entity';

enum ChecklistStatus {
  PENDING = 'pending',
  DONE = 'done',
}

@Entity('checklist')
class Checklist extends BaseEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  name: string;

  @Column({
    type: 'enum',
    enum: ChecklistStatus,
    default: ChecklistStatus.PENDING,
  })
  status: ChecklistStatus;

  @OneToMany(() => ChecklistItem, (checklistItem) => checklistItem.checklist, {
    eager: true,
    cascade: true,
  })
  checklistItems: ChecklistItem[];

  @ManyToOne(() => Task, (task) => task.checklists)
  task: Task;

  @CreateDateColumn()
  createdAt: string;

  @UpdateDateColumn()
  updatedAt: string;
}

export default Checklist;
