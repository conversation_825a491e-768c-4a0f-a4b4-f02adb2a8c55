import { Injectable, InternalServerErrorException } from '@nestjs/common';
import {
  MessageBody,
  SubscribeMessage,
  WebSocketGateway,
  WebSocketServer,
} from '@nestjs/websockets';
import * as _ from 'lodash';
import { Server } from 'socket.io';
import { User } from 'src/modules/users/entities/user.entity';
import { createQueryBuilder, getManager, Not } from 'typeorm';
import ChatMessage from './chat-message.entity';
import ChatRoom, { RoomType } from './chat-room.entity';
import { CreateRoomDto, QueryDto } from './chats.controller';
import { MessageDto } from './dto/message.dto';
import Storage, { StorageType } from '../storage/storage.entity';
import { v4 as uuidv4 } from 'uuid';

@WebSocketGateway({
  cors: {
    origin: '*',
  },
  serveClient: true,
})
@Injectable()
export class ChatsSevices {
  @WebSocketServer()
  server: Server;

  async getChatRooms(userId: number, query: QueryDto) {
    let repo = createQueryBuilder(ChatRoom, 'chatroom')
      .leftJoin('chatroom.members', 'members')
      .leftJoinAndSelect('chatroom.members', 'roomMembers')
      .leftJoinAndSelect('chatroom.messages', 'messages')
      .leftJoinAndSelect('messages.storage', 'storage')
      .orderBy('chatroom.createdAt', 'ASC')
      .addOrderBy('messages.createdAt', 'ASC')
      .where('chatroom.type = :type', { type: query.type });

    if (query.taskId) {
      repo.andWhere('chatroom.taskId = :taskId', {
        taskId: query.taskId,
      });
    }

    if (!query.taskId) {
      repo.andWhere('members.id = :userId', {
        userId: userId,
      });
    }

    let rooms = await repo.getMany();

    rooms.forEach((room) => {
      let messages = room.messages;
      const lastMessage = messages[messages.length - 1]?.message || '';

      let unread = room.messages.filter(
        (message) => message.isRead === false && message.senderId !== userId,
      );

      room.lastMessage = lastMessage;
      room.unread = unread.length;
    });

    return rooms;
  }

  async createRoom(data: CreateRoomDto) {
    const existing = await getManager().query(
      `select id, type, concat(min(crm.user_id),",",max(crm.user_id)) as members from chat_room
       left join chat_room_members crm on chat_room.id = crm.chat_room_id
       group by id having members = "${_.sortBy(data.members).join(',')}"
       and type = '${RoomType.INDIVIDUAL}'
      `,
    );

    if (existing.length > 0) {
      let existingRoom = await createQueryBuilder(ChatRoom, 'chatroom')
        .leftJoinAndSelect('chatroom.members', 'members')
        .leftJoinAndSelect('chatroom.messages', 'messages')
        .orderBy('messages.createdAt', 'ASC')
        .where('chatroom.id = :id', { id: existing[0].id })
        .getOne();
      return existingRoom;
    }

    let members = await User.findByIds(data.members);
    let room = new ChatRoom();
    room.members = members;
    room.messages = [];
    await room.save();
    return room;
  }

  async createGroup(data: CreateRoomDto) {
    let members = await User.findByIds(data.members);
    let group = new ChatRoom();
    group.name = data.name;
    group.members = members;
    group.type = RoomType.GROUP;
    group.taskId = data.taskId;
    await group.save();
  }

  async editGroup(id: number, data: any) {
    let members = await User.findByIds(data.members);
    const chatRoom = await ChatRoom.findOne({
      where: { roomId: id },
      relations: ['members']
    });
    chatRoom.name = data.name;
    chatRoom.members = members;
    await chatRoom.save();
    return chatRoom;

  }

  async readMessages(roomId: string, userId: number) {
    try {
      let messages = await ChatMessage.find({
        where: {
          room: { roomId },
          senderId: Not(userId),
        },
        relations: ['room'],
      });

      messages.forEach((message) => {
        message.isRead = true;
      });

      await ChatMessage.save(messages);
    } catch (e) {
      console.log(e);
      throw new InternalServerErrorException(e);
    }
  }

  async updateChatRoom(id: number, body: CreateRoomDto) {
    try {
      let room = await ChatRoom.findOne({
        where: { id },
      });
      room.name = body.name;
      let members = await User.findByIds(body.members);
      room.members = members;
      await room.save();
    } catch (e) {
      console.log(e);
    }
  }

  async deleteChatRoom(id: number) {
    try {
      let room = await ChatRoom.findOne(id);
      await room.remove();
    } catch (e) {
      console.log(e);
    }
  }

  @SubscribeMessage('message')
  async onMessage(@MessageBody() data: MessageDto) {
    try {
      this.server.emit(data.roomId, data);

      let room = await ChatRoom.findOne({
        where: { roomId: data.roomId },
      });

      let user = await User.findOne({
        where: { id: data?.senderId }
      })


      let message = new ChatMessage();
      message.room = room;
      message.message = data.message;
      if (data?.storage) {
        let storage = new Storage();
        storage.uid = uuidv4()
        storage.name = data.storage?.name;
        storage.fileType = data?.storage?.fileType;
        storage.file = data?.storage?.upload;
        storage.fileSize = data?.storage?.fileSize;
        storage.downloadUrl = data?.storage?.downloadUrl;
        storage.fileId = data?.storage?.fileId;
        storage.webUrl = data?.storage?.webUrl;
        storage.authId = data?.storage?.authId;
        storage.storageSystem = data?.storage?.storageSystem;
        storage.user = user;
        storage.room = room;
        storage.type = StorageType.FILE;
        message.storage = storage;
      }
      // message.file = data.file;
      // message.fileName = data.fileName;
      // message.fileType = data.fileType;
      message.senderId = data.senderId;
      message['userId'] = user.id;
      await message.save();
    } catch (e) {
      console.log(e);
    }
  }

  @SubscribeMessage('typing')
  async onTyping(@MessageBody() data: any) {
    this.server.emit(`${data?.roomId}/typing`, {
      userId: data?.userId,
      typing: data?.typing,
    });
  }
}
