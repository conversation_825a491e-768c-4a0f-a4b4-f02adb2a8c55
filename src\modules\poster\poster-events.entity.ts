import { Entity, PrimaryGeneratedColumn, Column, ManyToOne, BaseEntity, OneToMany, CreateDateColumn, UpdateDateColumn } from "typeorm";
import { PosterEventTypes } from "./poster-event-types.entity";
import Storage from "../storage/storage.entity";

@Entity()
export class PosterEvents extends  BaseEntity  {
    
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ unique: true })
  name: string;

  @CreateDateColumn()
  createdAt: string;

  @UpdateDateColumn()
  updatedAt: string;

  @Column()
  date: string;

  @ManyToOne(() => PosterEventTypes, (eventType) => eventType.events, { onDelete: "CASCADE" })
  posterEventTypes: PosterEventTypes;

  @OneToMany(() => Storage, (storage) => storage.posterEvents)
  storage: Storage[];
   
}

export default PosterEvents;




