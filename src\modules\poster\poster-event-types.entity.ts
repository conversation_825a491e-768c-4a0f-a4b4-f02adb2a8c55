import { Entity, PrimaryGeneratedColumn, Column, OneToMany, BaseEntity, CreateDateColumn, UpdateDateColumn } from "typeorm";
import { PosterEvents } from "./poster-events.entity";


@Entity()
export class PosterEventTypes extends BaseEntity {
    @PrimaryGeneratedColumn()
    id: number;

    @Column({ unique: true })
    name: string;

    @CreateDateColumn()
    createdAt: string;

    @UpdateDateColumn()
    updatedAt: string;

    @OneToMany(() => PosterEvents, (event) => event.posterEventTypes)
    events: PosterEvents[];

    
}
export default PosterEventTypes;
