import { Controller, Get, UseGuards } from "@nestjs/common";
import { IncomeTaxCronJobService } from "../services/incometax-cron-job-service";
import { CronAuthGuard } from "src/cron-auth/api-key-auth.guard";

@Controller('pan-cron')
export class PanCronController{
    constructor(private service:IncomeTaxCronJobService){}

    @UseGuards(CronAuthGuard)
    @Get('/notices')
    async getEproceedingNotices(){
        return this.service.getEproceedingNotices();
    }
}