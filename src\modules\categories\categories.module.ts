import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { CategoriesController } from './categories.controller';
import Category from './categories.entity';
import { CategoriesService } from './categories.service';
import  {CategorySubscriber} from '../../event-subscribers/categories.subscriber';

@Module({
  imports: [TypeOrmModule.forFeature([Category])],
  controllers: [CategoriesController],
  providers: [CategoriesService,CategorySubscriber],
})
export class CategoriesModule {}
