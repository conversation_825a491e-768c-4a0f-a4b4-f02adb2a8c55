import {
  Body,
  Controller,
  Get,
  Param,
  ParseIntPipe,
  Patch,
  Post,
  Put,
  Query,
  Req,
  Request,
  UseGuards,
} from '@nestjs/common';
import { HasPermission } from 'src/modules/roles/has-permission.guard';
import { JwtAuthGuard } from 'src/modules/users/jwt/jwt-auth.guard';
import { AddRemkarDto } from '../dto/add-remark.dto';
import CreateSubtaskDto from '../dto/create-subtask.dto';
import CreateTaskDto from '../dto/create-task.dto';
import FindTasksQuery from '../dto/find-query.dto';
import FindUserTasksDto, { FindUserTasksQueryType } from '../dto/find-user-tasks.dto';
import { UpdateStatusBody } from '../dto/types';
import UpdateTaskDto from '../dto/update-task.dto';
import { Permissions } from '../permission';
import { AttachmentsService } from '../services/attachments.service';
import { ChecklistsService } from '../services/checklists.service';
import { CommentsServie } from '../services/comments.service';
import { MilestonesService } from '../services/milestones.service';
import { StageOfWorkService } from '../services/stage-of-work.service';
import { TasksService } from '../services/tasks.service';
import FindQueryDto from 'src/modules/clients/dto/find-query.dto';
import { getBulkTasks } from '../dto/get-bulk-tasks.dto';
import { query } from 'express';
import Activity from 'src/modules/activity/activity.entity';
import UpdateRemarksDto from '../dto/update-remarks.dto';

@Controller('tasks')
export class TasksController {
  constructor(
    protected service: TasksService,
    protected checklistsService: ChecklistsService,
    protected commentsService: CommentsServie,
    protected attachmentsService: AttachmentsService,
    protected milestoneService: MilestonesService,
    protected stageOfWorkService: StageOfWorkService,
  ) {}

  @UseGuards(
    JwtAuthGuard,
    new HasPermission([Permissions.VIEW_ALL_TASKS, Permissions.VIEW_ASSIGNED_TASKS]),
  )
  @Get()
  async get(@Request() req: any, @Query() query: FindTasksQuery) {
    const { userId } = req.user;
    if (query?.task == 'board') {
      return this.service.findondemand(userId, query);
    } else if (query?.pending == 'pending') {
      return this.service.findpending(userId, query);
    } else {
      return this.service.find(userId, query);

      // return this.service.findApprovalTasks(userId, query);
    }
  }

  @UseGuards(JwtAuthGuard)
  @Get('/approval-tasks-count')
  async getApprovalTasksCount(@Request() req: any, @Query() query: FindTasksQuery) {
    const { userId } = req.user;
    return this.service.findApprovalTasksCount(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Get('/approval-tasks')
  async getApprovalTasksDetails(@Request() req: any, @Query() query: FindTasksQuery) {
    const { userId } = req.user;
    return this.service.findApprovalTasks(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Get('/sub-tasks')
  async getSubTasks(@Request() req: any, @Query() query: FindTasksQuery) {
    const { userId } = req.user;
    return this.service.findSubTask(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Post('/taskexportview')
  async exportTaskViewReport(@Request() req: any, @Body() body: FindTasksQuery) {
    const query = body;
    const { userId } = req.user;
    return this.service.exportTaskViewReport(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Post('/taskexport')
  async exportClientTaskReport(@Request() req: any, @Body() body: FindTasksQuery) {
    const query = body;
    const { userId } = req.user;
    return this.service.exportClientTaskReport(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Post('/grouptaskexport')
  async exportClientGroupTaskReport(@Request() req: any, @Body() body: FindTasksQuery) {
    const query = body;
    const { userId } = req.user;
    return this.service.exportClientGroupTaskReport(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Get('/calender-tasks')
  async getCalenderTasks(@Request() req: any, @Query() query: FindTasksQuery) {
    const { userId } = req.user;
    if (query?.task == 'calender') {
      return this.service.findcalender(userId, query);
    } else {
      return this.service.find(userId, query);
    }
  }

  @UseGuards(JwtAuthGuard)
  @Get('/pending')
  getPendingTasks(@Request() req: any, @Query() query: any) {
    const { userId } = req.user;
    return this.service.getPedningTasks(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Get('/group-pending')
  getGroupPendingTasks(@Request() req: any, @Query() query: any) {
    const { userId } = req.user;
    return this.service.getGroupPedningTasks(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Get('/user-upcoming-tasks')
  getUserUpcomingTasks(@Request() req: any, @Query() query: any) {
    const { userId } = req.user;
    return this.service.getUserUpcomingTasks(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Post('/export-usercard-assaignedtasks')
  async exportUserUpcomingAssignedTasks(@Request() req: any, @Body() body: FindUserTasksDto) {
    const query = body;
    const { userId } = req.user;
    let id = query.type === FindUserTasksQueryType.USER ? query.userId : userId;
    return this.service.exportUserUpcomingAssignedTasks(+id, query);
  }
  f;
  @UseGuards(JwtAuthGuard)
  @Post('/export-user-assaignedtasks')
  async exportuserUpcomingAssaignedTasks(@Request() req: any, @Body() body: FindUserTasksDto) {
    const query = body;
    const { userId } = req.user;
    let id = query.type === FindUserTasksQueryType.USER ? query.userId : userId;
    return this.service.exportuserUpcomingAssaignedTasks(+id, query);
  }

  @UseGuards(JwtAuthGuard)
  @Get('/user')
  getUserTasks(@Request() req: any, @Query() query: FindUserTasksDto) {
    const { userId } = req.user;
    let id = query.type === FindUserTasksQueryType.USER ? query.userId : userId;
    return this.service.getUserTasks(+id, query);
  }

  @UseGuards(JwtAuthGuard)
  @Get('approval')
  getApprovalTasks(@Request() req: any, @Query() query: any) {
    const { userId } = req.user;
    let id = query.type === FindUserTasksQueryType.USER ? query.userId : userId;
    return this.service.getUesrApprovalTasks(+id, query);
  }

  @UseGuards(JwtAuthGuard)
  @Post('/usertask-export')
  async exportTaskUserReport(@Request() req: any, @Body() body: FindUserTasksDto) {
    const query = body;
    const { userId } = req.user;
    let id = query.type === FindUserTasksQueryType.USER ? query.userId : userId;
    return this.service.exportTaskUserReport(+id, query);
  }

  @UseGuards(JwtAuthGuard)
  @Post('/approvaltask-export')
  async exportApprovalTaskUserReport(@Request() req: any, @Body() body: FindUserTasksDto) {
    const query = body;
    const { userId } = req.user;
    let id = query.type === FindUserTasksQueryType.USER ? query.userId : userId;
    return this.service.exportApprovalTaskUserReport(+id, query);
  }

  @UseGuards(JwtAuthGuard)
  @Post('/user-card-task-export')
  async exportTaskUserCardReport(@Request() req: any, @Body() body: FindUserTasksDto) {
    const query = body;
    const { userId } = req.user;
    let id = query.type === FindUserTasksQueryType.USER ? query.userId : userId;
    return this.service.exportTaskUserCardReport(+id, query);
  }

  @UseGuards(JwtAuthGuard)
  @Post('/user-export')
  async exportUserTaskLeaderTasksReport(@Request() req: any, @Body() body: FindUserTasksDto) {
    const query = body;
    const { userId } = req.user;
    return this.service.exportUserTaskLeaderTasksReport(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Get('/task-leader')
  getTaskLeaderTasks(@Request() req: any, @Query() query: FindUserTasksDto) {
    const { userId } = req.user;
    let id = query.type === FindUserTasksQueryType.USER ? query.userId : userId;
    return this.service.getTaskLeaderTasks(+id, query);
  }

  @UseGuards(JwtAuthGuard)
  @Get('/upcoimg-task-leader')
  getTaskLeaderUpcomingTasks(@Request() req: any, @Query() query: FindUserTasksDto) {
    const { userId } = req.user;
    let id = query.type === FindUserTasksQueryType.USER ? query.userId : userId;
    return this.service.getTaskLeaderUpcomingTasks(+id, query);
  }
  @UseGuards(JwtAuthGuard)
  @Post('/upcoimg-usertask-leader-export')
  async exportUsercardUpcomingLeaderTasks(@Request() req: any, @Body() body: FindUserTasksDto) {
    const query = body;
    const { userId } = req.user;
    let id = query.type === FindUserTasksQueryType.USER ? query.userId : userId;
    return this.service.exportUsercardUpcomingLeaderTasks(+id, query);
  }
  @UseGuards(JwtAuthGuard)
  @Post('/upcoimg-task-leader-export')
  async exportuserUpcomingLeaderTasks(@Request() req: any, @Body() body: FindUserTasksDto) {
    const query = body;
    const { userId } = req.user;
    let id = query.type === FindUserTasksQueryType.USER ? query.userId : userId;
    return this.service.exportuserUpcomingLeaderTasks(+id, query);
  }

  @UseGuards(JwtAuthGuard)
  @Post('/task-leader-export')
  async exportTaskLeaderTasksReport(@Request() req: any, @Body() body: FindUserTasksDto) {
    const query = body;
    const { userId } = req.user;
    let id = query.type === FindUserTasksQueryType.USER ? query.userId : userId;
    return this.service.exportTaskLeaderTasksReport(+id, query);
  }

  @UseGuards(JwtAuthGuard)
  @Get('/as-options')
  getAsOptions(@Request() req: any) {
    const { userId } = req.user;
    return this.service.getAsOptions(userId);
  }

  @UseGuards(JwtAuthGuard)
  @Get('/task-details/:id')
  getOne(@Request() req: any, @Param('id', ParseIntPipe) id: number, @Query() query: any) {
    const { userId } = req.user;
    return this.service.findOne(id, userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Get('/task-analytics')
  async taskAnalytics(@Req() request: any, @Query() query: any) {
    let { userId } = request.user;
    return this.service.getTaskAnalytics(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Post()
  create(@Request() req: any, @Body() body: CreateTaskDto) {
    const { userId } = req.user;
    return this.service.create(userId, body);
  }

  @UseGuards(JwtAuthGuard)
  @Post('/:id/subtasks')
  createSubTask(
    @Request() req: any,
    @Body() body: CreateTaskDto,
    @Param('id', ParseIntPipe) taskId: number,
  ) {
    const { userId } = req.user;
    return this.service.createSubTask(userId, taskId, body);
  }

  @UseGuards(JwtAuthGuard)
  @Put('/update-status/:id')
  updateStatus(
    @Param('id', ParseIntPipe) id: number,
    @Body() body: UpdateStatusBody,
    @Request() req: any,
  ) {
    const { userId } = req.user;
    return this.service.updateStatus(id, userId, body);
  }

  @UseGuards(JwtAuthGuard)
  @Post('/import')
  importTasks(@Request() req: any, @Body() body: any) {
    const { userId } = req.user;
    return this.service.importTasks(userId, body);
  }

  @Put('/reorder')
  reorder(@Body() body: { items: number[] }) {
    return this.service.reorder(body.items);
  }

  @UseGuards(JwtAuthGuard)
  @Put(':id')
  updateTask(
    @Param('id', ParseIntPipe) id: number,
    @Body() body: UpdateTaskDto,
    @Request() req: any,
  ) {
    const { userId } = req.user;
    return this.service.update(id, userId, body);
  }

  @Get('updateTaskMembers/:id')
  updateTaskMemebers(@Param('id', ParseIntPipe) id: number) {
    return this.service.updateTaskMemebers(id);
  }

  @UseGuards(JwtAuthGuard)
  @Post('/:id/restore')
  restoreTask(@Request() req: any, @Param('id', ParseIntPipe) taskId: number, @Body() body: any) {
    const { userId } = req.user;
    return this.service.restoreTask(userId, taskId, body);
  }

  @UseGuards(JwtAuthGuard)
  @Post('/:id/terminate')
  terminateTask(
    @Request() req: any,
    @Param('id', ParseIntPipe) taskId: number,
    @Body() { reason }: { reason: string },
  ) {
    const { userId } = req.user;
    return this.service.terminateTask(userId, taskId, reason);
  }

  @UseGuards(JwtAuthGuard)
  @Post('/:id/delete')
  deleteTask(@Request() req: any, @Param('id', ParseIntPipe) taskId: number) {
    const { userId } = req.user;
    return this.service.deleteTask(userId, taskId);
  }

  @UseGuards(JwtAuthGuard)
  @Post('/:id/remarks')
  addRemark(
    @Request() req: any,
    @Param('id', ParseIntPipe) taskId: number,
    @Body() body: AddRemkarDto,
  ) {
    const { userId } = req.user;
    return this.service.addRemarks(userId, taskId, body);
  }

  @UseGuards(JwtAuthGuard)
  @Get('/terminated-tasks')
  getTerminatedTasks(@Query() query: FindTasksQuery, @Req() req: any) {
    const { userId } = req.user;
    return this.service.getTerminatedTasks(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Post('/terminatedTaskexport')
  async exportTerminatedTasksReport(@Req() req: any, @Body() body: FindTasksQuery) {
    const query = body;
    const { userId } = req.user;
    return this.service.exportTerminatedTasksReport(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Post('/terminated-tasks-export')
  async exportClientTerminatedTaskReport(@Req() req: any, @Body() body: FindTasksQuery) {
    const query = body;
    const clientId = body.client;
    const { userId } = req.user;
    return this.service.exportClientTerminatedTaskReport(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Get('/group-tasks')
  getGroupTasks(@Query() query: FindTasksQuery, @Req() req: any) {
    const { userId } = req.user;
    return this.service.getGroupTasks(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Get('/completed-tasks')
  getCompletedTasks(@Query() query: FindTasksQuery, @Req() req: any) {
    const { userId } = req.user;
    return this.service.getCompletedTasks(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Post('/completed-tasks-export')
  async exportClientCompletedTaskReport(@Req() req: any, @Body() body: FindTasksQuery) {
    const query = body;
    const clientId = body.client;
    const { userId } = req.user;
    return this.service.exportClientCompletedTaskReport(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Get('/recurring-tasks')
  getRecurringTasks(@Req() req: any, @Query() query: FindTasksQuery) {
    const { userId } = req.user;
    return this.service.getRecurringTasks(query, userId);
  }

  @UseGuards(JwtAuthGuard)
  @Post('/recurring-tasks-export')
  async exportgetRecurringTasksReport(@Req() req: any, @Body() body: FindTasksQuery) {
    const query = body;
    const { userId } = req.user;
    return this.service.exportgetRecurringTasksReport(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Get('/non-recurring-tasks')
  getNonRecurringTasks(@Req() req: any, @Query() query: FindTasksQuery) {
    const { userId } = req.user;
    return this.service.getNonRecurringTasks(query, userId);
  }

  @UseGuards(JwtAuthGuard)
  @Post('/non-recurring-tasks-export')
  async exportgetNonRecurringTasksReport(@Req() req: any, @Body() body: FindTasksQuery) {
    const query = body;
    const { userId } = req.user;
    return this.service.exportgetNonRecurringTasksReport(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Get('/user-completed-tasks')
  getUserCompletedTasks(@Req() req: any, @Query() query: FindTasksQuery) {
    const { userId } = req.user;
    return this.service.getUserCompletedTasks(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Get('/deleted-tasks')
  getDeletedTasks(@Query() query: FindTasksQuery, @Req() req: any) {
    const { userId } = req.user;
    return this.service.getDeletedTasks(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Post('/deleted-tasks-export')
  exportClientDeletedTasksReport(@Body() body: FindTasksQuery, @Req() req: any) {
    const { userId } = req.user;
    const query = body;
    return this.service.exportClientDeletedTasksReport(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Get('/terminatedtaskslist')
  getTerminatedTasksList(@Query() query: FindTasksQuery, @Req() req: any) {
    const { userId } = req.user;
    return this.service.getTerminatedTasksList(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Post('/bulk-tasks')
  getBulkTasks(@Req() req: any, @Body() body: getBulkTasks) {
    const { userId } = req.user;
    return this.service.getBulkTasks(userId, body);
  }

  @UseGuards(JwtAuthGuard)
  @Post('/bulk-recurring-profile')
  getBulkRecurringProfile(@Req() req: any, @Body() body: getBulkTasks) {
    const { userId } = req.user;
    return this.service.getBulkRecurringProfile(userId, body);
  }

  @UseGuards(JwtAuthGuard)
  @Post('/bulk-non-recurring')
  getBulkNonRecurring(@Req() req: any, @Body() body: getBulkTasks) {
    const { userId } = req.user;
    return this.service.getBulkNonRecurring(userId, body);
  }

  @UseGuards(JwtAuthGuard)
  @Post('/bulk-edit')
  taskBulkUpdate(@Req() req: any, @Body() body: any) {
    const { userId } = req.user;
    return this.service.taskBulkUpdate(userId, body);
  }

  @UseGuards(JwtAuthGuard)
  @Post('/bulk-edit-recurring-profile')
  recurringProfileBulkUpdate(@Req() req: any, @Body() body: any) {
    const { userId } = req.user;
    return this.service.recurringProfileBulkUpdate(userId, body);
  }

  @UseGuards(JwtAuthGuard)
  @Post('/bulk-edit-non-recurring')
  nonRecurringBulkUpdate(@Req() req: any, @Body() body: any) {
    const { userId } = req.user;
    return this.service.nonRecurringBulkUpdate(userId, body);
  }

  @UseGuards(JwtAuthGuard)
  @Get('/activity')
  async getTaskActivity(@Request() req: any, @Query() query: FindTasksQuery) {
    const { userId } = req.user;
    return this.service.getTaskActivity(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Patch('/update-remark/:id')
  async udateTaskRemark(@Param('id', ParseIntPipe) id: number, @Body() body: UpdateRemarksDto) {
    return this.service.updateRemark(id, body);
  }

  // @UseGuards(JwtAuthGuard)
  // @Get('udin-register')
  // getUdinTasks(@Req() req:any, @Query() query:any){
  //   const {userId} = req.user;
  //   return this.service.getUdinTasks(userId,query);
  // }

  @UseGuards(JwtAuthGuard)
  @Get('allStatusTasks')
  getAllStatusTasks(@Req() req: any, @Query() query: any) {
    const { userId } = req.user;
    return this.service.getAllStatusTasksCopy(userId, query);
  }
}
