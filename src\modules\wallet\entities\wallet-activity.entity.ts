import { BaseEntity, Column, CreateDateColumn, Entity, PrimaryGeneratedColumn, UpdateDateColumn } from "typeorm";


@Entity()
export class WalletActivity extends BaseEntity {

    @PrimaryGeneratedColumn()
    id: number;

    @Column()
    activityType: string;

    @Column()
    credits: string

    @CreateDateColumn()
    activityTime: string;

    @UpdateDateColumn()
    lastUpdated: string;

    @Column()
    activityUserId: number;
}