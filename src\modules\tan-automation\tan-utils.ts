export function getCurrentQuarter(): string {
    const currentMonth = new Date().getMonth() + 1;
    return getQuarterFromMonth(currentMonth);
  }

 export function getQuarterFromMonth(month: number): string {
    if (month >= 4 && month <= 6) return '1';
    if (month >= 7 && month <= 9) return '2';
    if (month >= 10 && month <= 12) return '3';
    return '4';
  }

  export const tanFormTypes = ["Form 24Q","Form 26Q","Form 27Q","Form 27EQ"]

  export function getAllotmentQuarter(month:number):string{
    if (month >= 4 && month <= 6) return 'Q1';
    if (month >= 7 && month <= 9) return 'Q2';
    if (month >= 10 && month <= 12) return 'Q3';
    return 'Q4';
  }

  export function getBehindQuarter(quarter:string){
    if(quarter === "Q1"){
      return "Q4"
    }else if(quarter === "Q2"){
      return "Q1"
    }else if(quarter === "Q3"){
      return "Q2"
    }else if(quarter === "Q4"){
      return "Q3"
    }
  }



export function getQuartersBetween(startYear: number, startQuarterIndex: number, endYear: number, endQuarterIndex: number) {
  const quarters = [];
  let year = startYear;
  let quarterIndex = startQuarterIndex;

  while (year < endYear || (year === endYear && quarterIndex <= endQuarterIndex)) {
      quarters.push({ year, quarter: `Q${quarterIndex}` });
      quarterIndex++;
      if (quarterIndex > 4) {
        quarterIndex = 1; 
          year++;    
      }
  }
  return quarters;
}

export function getFinancialQuarter(date: Date): number {
  const month = date.getMonth() + 1; 
  if (month >= 4 && month <= 6) return 1; 
  if (month >= 7 && month <= 9) return 2; 
  if (month >= 10 && month <= 12) return 3;
  return 4;
}


export function getFormValidQuarters(startDate: string, financialYear: number) {
  const validQuarters = [];
  const startQuarter = parseInt(getQuarterFromMonth(new Date(startDate).getMonth() +1)); 
  const startYear = new Date(startDate).getFullYear();

  const today = new Date();
  const currentYear = today.getFullYear();
  const currentQuarter = parseInt(getQuarterFromMonth(today.getMonth() +1)); 

  const financialStartDate = `${financialYear}-04-01`;
  const financialEndDate = `${financialYear + 1}-03-31`;

  if (new Date(startDate) < new Date(financialStartDate)) {
    // Allotment date before the financial year start date
    validQuarters.push("Q1", "Q2", "Q3", "Q4");
  } else if (new Date(startDate) > new Date(financialEndDate)) {
    // Allotment date is after the financial year end date
    return validQuarters; // Return empty as no valid quarters
  } else if (
    new Date(startDate) >= new Date(financialStartDate) &&
    new Date(startDate) <= new Date(financialEndDate)
  ) {
    // Allotment date is in between the financial year
    if (financialYear === currentYear) {
      // If financial year is same as current year
      for (let quarter = startQuarter; quarter < currentQuarter; quarter++) {
        validQuarters.push(`Q${quarter}`);
      }
    } else {
      // If financial year is not same as current year
      for (let quarter = startQuarter; quarter <= 4; quarter++) {
        validQuarters.push(`Q${quarter}`);
      }
    }
  }

  return validQuarters;
}

// export function getQuartersBetweenFinancialYear(
//   dateOfAllotment: Date,
//   financialYear: number,
//   currentYear: number,
//   currentQuarterIndex: number
// ) {
//   console.log("dateOfAllotment",dateOfAllotment);
//   const quarters = [];
//   const allotmentYear = dateOfAllotment.getFullYear();
//   const allotmentQuarterIndex = parseInt(getQuarterFromMonth(dateOfAllotment.getMonth() + 1));

//   const financialYearStart = new Date(`${financialYear}-04-01`);
//   const financialYearEnd = new Date(`${financialYear + 1}-03-31`);
//   const today = new Date();
//   const currentYearStart = new Date(`${currentYear}-04-01`);
//   console.log({financialYearStart,financialYearEnd});

//   // Case 1: Allotment date is after the financial year, no valid quarters
//   if (dateOfAllotment > financialYearEnd) {
//     return quarters;
//   }

//   // Case 2: Allotment date is before the financial year
//   if (dateOfAllotment < financialYearStart) {
//     for (let i = 1; i <= 4; i++) {
//       quarters.push({ year: financialYear, quarter: `Q${i}` });
//     }
//     return quarters;
//   }

//   // Case 3: Allotment date is within the financial year
//   if (allotmentYear === financialYear) {
//     if (financialYear === currentYear) {
//       // Financial year matches the current year
//       for (let i = allotmentQuarterIndex; i < currentQuarterIndex; i++) {
//         quarters.push({ year: financialYear, quarter: `Q${i}` });
//       }
//     } else {
//       // Financial year is less than the current year

//       for (let i = allotmentQuarterIndex; i <= 4; i++) {

//         quarters.push({ year: financialYear, quarter: `Q${i}` });
//       }
//     }
//   }

//   // Case 4: Allotment date is in the current calendar year but before the current financial year
//   if (
//     allotmentYear === currentYear &&
//     dateOfAllotment < currentYearStart 
//     && financialYear === currentYear
//   ) {
//     for (let i = allotmentQuarterIndex; i < currentQuarterIndex; i++) {
//       quarters.push({ year: financialYear, quarter: `Q${i}` });
//     }
//   }

//   // const startQuarter = allotmentQuarterIndex; // Start from the quarter of allotment
//   // const endQuarter =
//   //   financialYear === currentYear ? currentQuarterIndex : 4; // End at the current quarter or Q4

//   // for (let i = startQuarter; i <= endQuarter; i++) {
//   //   quarters.push({ year: financialYear, quarter: `Q${i}` });
//   // }


// console.log(financialYear,quarters)
//   return quarters;
// }

export function getQuartersBetweenFinancialYear(
  dateOfAllotment: Date,
  financialYear: number,
  currentYear: number,
  currentQuarterIndex: number
) {

  const quarters = [];
  const allotmentQuarterIndex = parseInt(getQuarterFromMonth(dateOfAllotment.getMonth() + 1));
  const financialYearStart = new Date(`${financialYear}-04-01`);
  const financialYearEnd = new Date(`${financialYear + 1}-03-31`);
  const currentYearStart = new Date(`${currentYear}-04-01`);

  if (dateOfAllotment > financialYearEnd) {
    // Case 1: Allotment date is after the financial year
    return [];
  } else if (dateOfAllotment < financialYearStart) {
    // Case 2: Allotment date is before the financial year
    for (let i = 1; i <= 4; i++) {
      quarters.push({ year: financialYear, quarter: `Q${i}` });
    }
  } else if (dateOfAllotment >= financialYearStart && dateOfAllotment <= financialYearEnd) {
    // Case 3: Allotment date is within the financial year
    const startQuarter = allotmentQuarterIndex;
    const endQuarter = financialYear === currentYear ? currentQuarterIndex : 4;

    for (let i = startQuarter; i <= endQuarter; i++) {
      quarters.push({ year: financialYear, quarter: `Q${i}` });
    }
  } else if (
    allotmentQuarterIndex < currentQuarterIndex &&
    dateOfAllotment >= new Date(`${currentYear}-01-01`) &&
    dateOfAllotment < currentYearStart
  ) {
    // Case 4: Allotment date is in the current calendar year but before the financial year
    for (let i = allotmentQuarterIndex; i < currentQuarterIndex; i++) {
      quarters.push({ year: financialYear, quarter: `Q${i}` });
    }
  }
  return quarters;
}


