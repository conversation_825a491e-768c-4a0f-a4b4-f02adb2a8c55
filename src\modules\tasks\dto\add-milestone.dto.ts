import {
  IsArray,
  IsBoolean,
  IsEnum,
  IsNotEmpty,
  IsOptional,
  IsString,
} from 'class-validator';
import { MilestoneStatus } from '../entity/milestone.entity';

class AddMileStoneDto {
  @IsNotEmpty()
  @IsString()
  name: string;

  @IsOptional()
  @IsString()
  description: string;

  @IsNotEmpty()
  @IsBoolean()
  referenceNumber: boolean;

  @IsOptional()
  @IsString()
  referenceNumberValue: string;

  @IsOptional()
  @IsEnum(MilestoneStatus)
  status: MilestoneStatus;
}

export default AddMileStoneDto;
