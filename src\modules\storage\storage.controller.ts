import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  ParseIntPipe,
  Post,
  Put,
  Query,
  Req,
  Request,
  UploadedFile,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { JwtAuthGuard } from 'src/modules/users/jwt/jwt-auth.guard';
import CreateFolderDto from './dto/create-folder.dto';
import CreateLinkDto from './dto/create-link.dto';
import { FindStorageDto } from './dto/FindStorage.dto';
import MoveFileDto from './dto/move-file.dto';
import { RenameFileDto } from './dto/rename-file.dto';
import { TotalStorageDto } from './dto/total-storage.dto';
import { StorageService } from './storage.service';
import CreateB3Credentials from './dto/create-b3-credentials.dto';

export interface IUploadBody {
  clientId: number;
  folderId: string | null;
  type: 'organization' | 'client' | 'chat' | 'clientGroup' | 'viderAi';
  roomId: number | null;
  stageid?: number | null;
  clientGroup: number | null;
  docId?: number | null;
  aiResult?: any;
  kyb?: string | null
}

@Controller('storage')
export class StorageController {
  constructor(private service: StorageService) { }








  @UseGuards(JwtAuthGuard)
  @Get()
  async getInitialStorage(@Query() query: FindStorageDto, @Request() req: any) {
    const { userId } = req.user;
    return this.service.findStorage(query, userId);
  }

  @Get('/tree')
  async getStorageTree(@Query() { clientId }: { clientId: number }) {
    return this.service.getStorageTree(clientId);
  }

  @UseGuards(JwtAuthGuard)
  @Post('/create-folder')
  async createFolder(@Body() body: CreateFolderDto, @Request() req: any) {
    const { userId } = req.user;
    return this.service.createFolder(body, userId);
  }

  @UseGuards(JwtAuthGuard)
  @Post('/create-link')
  async createLink(@Body() body: CreateLinkDto, @Request() req: any) {
    const { userId } = req.user;
    return this.service.createLink(userId, body);
  }

  @UseGuards(JwtAuthGuard)
  @Post('/upload-files')
  @UseInterceptors(FileInterceptor('file'))
  uploadFile(
    @UploadedFile() file: Express.Multer.File,
    @Body() body: IUploadBody,
    @Request() req: any,
  ) {
    const { userId } = req.user;
    return this.service.saveFile({ file, body, userId });
  }

  //new
  @UseGuards(JwtAuthGuard)
  @Post('/upload-file')
  @UseInterceptors(FileInterceptor('file'))
  attachementsUpload(
    @UploadedFile() file: Express.Multer.File,
    @Body() body: IUploadBody,
    @Request() req: any,
  ) {
    const { userId } = req.user;
    return this.service.saveAttchement({ file, body, userId });
  };

  @UseGuards(JwtAuthGuard)
  @Post('/upload-and-store-file')
  @UseInterceptors(FileInterceptor('file'))
  uploadAndStoreFile(
    @UploadedFile() file: Express.Multer.File,
    @Body() body: IUploadBody,
    @Request() req: any,
  ) {
    const { userId } = req.user;


    // return this.service.attachementsUpload({ file, body, userId });
    return this.service.uploadAndStoreFile({ file, body, userId });
  };

  @Delete('/remove-collect-file/:id')
  removeCollectFile(@Param('id', ParseIntPipe) id: number) {
    return this.service.removecollectFile(id);
  }

  @UseGuards(JwtAuthGuard)
  @Delete('/remove-file/:id')
  removeFile(@Param('id', ParseIntPipe) id: number, @Request() req: any) {
    const { userId } = req.user;
    return this.service.removeFile(id, userId);
  }

  @Post('/rename-file')
  renameFile(@Body() body: RenameFileDto) {
    return this.service.renameFile(body);
  }

  @UseGuards(JwtAuthGuard)
  @Put('/move-file')
  async moveFile(@Req() req: any, @Body() body: MoveFileDto) {
    const { userId } = req.user;
    return this.service.moveFile(userId, body);
  }

  @UseGuards(JwtAuthGuard)
  @Post('/replace')
  async replaceFile(@Body() body: MoveFileDto) {
    return this.service.replaceFile(body);
  }

  @UseGuards(JwtAuthGuard)
  @Post('/keep-both')
  async keepBothFiles(@Body() body: MoveFileDto) {
    return this.service.keepBothFilesOrFolders(body);
  }


  @UseGuards(JwtAuthGuard)
  @Post('/bharath-credentials')
  async addBharathCredentials(@Req() req: any, @Body() body: CreateB3Credentials) {
    const { userId } = req.user;
    return this.service.addBharathCredentials(userId, body);
  }

  @UseGuards(JwtAuthGuard)
  @Get('/bharathcloud-items')
  async getBharathCloudItems(@Req() req: any, @Query() query: any) {
    const { userId } = req.user;
    return this.service.getBharathCloudItems(userId, query)
  }

  @Get('/total-storage')
  async getTotalStorage(@Query() query: TotalStorageDto) {
    return this.service.getTotalStorage(query);
  }

  @UseGuards(JwtAuthGuard)
  @Get('/org-total')
  async getOrgTotal(@Req() req: any, @Query() query: any) {
    const { userId } = req.user;
    return this.service.getOrgStorage(userId, query);
  }

  @Get('/:id')
  async getAutOrgStorage(@Param('id', ParseIntPipe) id: number) {
    return this.service.getAutOrgStorage(id);
  }



  @Delete('/delete/:id')
  deleteAwsFile(@Param('id') id: string) {
    return this.service.deleteAwsFile(id);
  }


  @Post('/transfer')
  transfer() {
    return this.service.transfer()

  }


}
