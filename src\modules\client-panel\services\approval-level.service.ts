import { Injectable } from '@nestjs/common';
import { User } from 'src/modules/users/entities/user.entity';
import ApprovalProcedures from 'src/modules/atm-qtm-approval/entities/approval-procedures.entity';
import { createQueryBuilder } from 'typeorm';
import Task from 'src/modules/tasks/entity/task.entity';
import axios from 'axios';
import { TaskStatusEnum } from 'src/modules/tasks/dto/types';
import { each } from 'lodash';
import { UpdateTaskApprovalsDto } from 'src/modules/atm-qtm-approval/dto/update-approvals.dto';

@Injectable()
export class ApprovalLevelService {

  async find(userId: number) {
    let user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
    let result = await createQueryBuilder(ApprovalProcedures, 'approvalProcedures')
      .leftJoin('approvalProcedures.organization', 'organization')
      .leftJoinAndSelect('approvalProcedures.approval', 'approval')
      .leftJoinAndSelect('approval.approvalLevels', 'approvalLevel')
      .where('organization.id = :orgId', { orgId: user.organization.id })
      .andWhere('approvalProcedures.module=:module', { module: 'task' })
      .getMany();
    return result;
  }

  async findTasks(data: any) {
    let task = await Task.findOne({ where: { id: data.taskId } });

    if (task.processInstanceId) {
      let result: any = [];

      result = await this.getApprovals(task.processInstanceId, task.status);

      //  else {
      //     result = await this.recentSnapshot(task.processInstanceId)
      // }
      return { result, taskStatus: task.status };
    }
    return [];
  }

  async getApprovals(id: string, status: string) {
    try {
      const url = `${process.env.CAMUNDA_URL}/vider/quantum/api/process/${id}`;
      let data: any = {
        method: 'get',
        maxBodyLength: Infinity,
        headers: {
          'Content-Type': 'application/json',
        },
      };

      const response = await axios.get(url, data);
      const result = response?.data?.tasks.filter(
        (task) => task.name !== 'holdTillDocumentIsSubmitted',
      );
      const taskSnapshots = response?.data?.data?.taskSnapshots;
      const taskSnapshotsMainTasks = taskSnapshots?.map((snapshot) => snapshot.tasks);
      // console.log(taskSnapshotsMainTasks)

      // console.log("taskSnapshots", taskSnapshots);
      // console.log(taskSnapshotsMainTasks);
      let resultTasks: any = [];
      const firstTask = result.filter((task) => {
        return task.name[task.name.length - 1] == 1;
      });
      for (let taskitems of result) {
        const name = taskitems.name;
        let rejectionArray: any = [];
        if (response?.data?.data?.taskSnapshots) {
          rejectionArray = taskSnapshotsMainTasks
            ?.map((subArray) => {
              const task = subArray?.find(
                (task) =>
                  task?.name === name &&
                  (task?.status === 'DECLINED' ||
                    task?.status === 'AUTO_DECLINED' ||
                    task?.status === 'APPROVED'),
              );

              if (task) {
                return {
                  lastUpdatedOn: task.lastUpdatedOn,
                  comments: task.comments,
                  name: task.name,
                  status: task.status,
                };
              }

              return null; // Return null if no matching task is found
            })
            .filter((task) => task !== null);
        }
        if (status == TaskStatusEnum.UNDER_REVIEW) {
          rejectionArray.push({
            lastUpdatedOn: taskitems.lastUpdatedOn,
            comments: taskitems.comments,
            name: taskitems.name,
            status: taskitems.status,
          });
        }

        if (status !== TaskStatusEnum.UNDER_REVIEW) {
          if (firstTask[0].status) {
            rejectionArray.push({
              lastUpdatedOn: taskitems.lastUpdatedOn,
              comments: taskitems.comments,
              name: taskitems.name,
              status: taskitems.status,
            });
          }
        }

        taskitems.rejectionArray = rejectionArray;
        resultTasks.push(taskitems);
      }

      resultTasks.sort((a: any, b: any) => {
        const lastCharA = a.name.charAt(a.name.length - 1);
        const lastCharB = b.name.charAt(b.name.length - 1);

        if (lastCharA < lastCharB) {
          return -1;
        } else if (lastCharA > lastCharB) {
          return 1;
        } else {
          return 0;
        }
      });
      return resultTasks;
    } catch (err) {
      console.error(err);
    }
  }



  async getNextApprovalDetails(id: string, level: string) {
    try {
      const url = `${process.env.CAMUNDA_URL}/vider/quantum/api/process/${id}`;
      let data: any = {
        method: 'get',
        maxBodyLength: Infinity,
        headers: {
          'Content-Type': 'application/json',
        },
      };

      const response = await axios.get(url, data);
      const result = response?.data?.tasks?.filter(
        (task: any) => task.name === 'holdTillDocumentIsSubmitted',
      );
      if (result[0]?.status) {
        const arr = [];
        return arr;
      } else {
        const lastDigit = parseInt(level.slice(-1));
        const newLastDigit = (lastDigit + 1) % 10;
        const updatedString = level.slice(0, -1) + newLastDigit;
        const nextLevel = response?.data?.tasks.filter((task: any) => task.name === updatedString);
        return nextLevel;
      }
    } catch (err) {
      console.error(err);
    }
  }

  async updateTaskApprovals(body: UpdateTaskApprovalsDto, userId: number) {
    const { taskId, approvalHierarchyId, removeApproval } = body;

    let task = await Task.findOne({ where: { id: taskId } });
    let user = await User.findOne({ where: { id: userId } });
    if (removeApproval) {
      task.approvalProcedures = null;
      task.processInstanceId = null;
      task.approvalStatus = null;
    }

    if (approvalHierarchyId) {
      const approval = await ApprovalProcedures.findOne({
        where: { id: approvalHierarchyId, organization: user.organization.id },
        relations: ['approval', 'approval.approvalLevels', 'approval.approvalLevels.user'],
        select: ['id'],
      });
      task.approvalProcedures = approval;
      try {
        const data = JSON.stringify({
          processKey: 'genericApprovalProcess',
          metaData: {
            typeOfApproval: 'ATOM_TASK',
            approvalProcessId: `Level${approval?.approval?.approvalLevels.length}ApprovalProcess`,
          },
        });

        let config: any = {
          method: 'post',
          maxBodyLength: Infinity,
          url: `${process.env.CAMUNDA_URL}/vider/quantum/api/process`,
          headers: {
            'Content-Type': 'application/json',
          },
          data: data,
        };

        await axios.request(config).then(async (response) => {
          const processInstanceId = response?.data?.processInstanceId;

          if (processInstanceId)
            task.approvalStatus = [
              {
                status: `Approval Levels (${approval?.approval?.approvalLevels.length})`,
                completed: false,
              },
            ];
          task.processInstanceId = processInstanceId;
          this.approvalProcess(processInstanceId, approval);
        });
      } catch (err) {
        console.log(`Error on creating camunda approval process:${err}`);
      }
    }
    await task.save();

    return 'success';
  }

  async approvalProcess(id: string, approvalData: ApprovalProcedures) {
    try {
      const url = `${process.env.CAMUNDA_URL}/vider/quantum/api/process/${id}`;
      let data: any = {
        method: 'get',
        maxBodyLength: Infinity,
        headers: {
          'Content-Type': 'application/json',
        },
      };

      const response = await axios.get(url, data);
      const { approval } = approvalData;
      const { approvalLevels } = approval;
      let assignApprovalTasks: any[] = [];
      assignApprovalTasks = response?.data?.tasks
        .filter((item: any) => item.name !== 'holdTillDocumentIsSubmitted')
        .map((item: any) => {
          let levelNumber = parseInt(item.name.slice(-1));
          const foundUser = approvalLevels.find((level) => level.level === levelNumber);
          const { user } = foundUser;
          const { id } = user;
          // const response = await axios.get(url, config);
          return `${process.env.CAMUNDA_URL}/vider/quantum/api/task/${item.id}/assign/${id}`;
        });

      const makeApiCall = async (url) => {
        try {
          let config: any = {
            method: 'put',
            maxBodyLength: Infinity,
            headers: {
              'Content-Type': 'application/json',
            },
          };

          const response = await axios.put(url, config);
          return response.data;
        } catch (error) {
          console.error('Error for', url, ':', error);
          throw error;
        }
      };

      const apiPromises = assignApprovalTasks.map((endpoint) => makeApiCall(endpoint));

      Promise.all(apiPromises)
        .then((apiResponses) => { })
        .catch((error) => {
          console.error('One or more API calls failed:', error);
        });
    } catch (err) {
      console.log(err);
    }
  }
}
