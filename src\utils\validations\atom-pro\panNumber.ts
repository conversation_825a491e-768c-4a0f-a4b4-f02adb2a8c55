import { TAN_REGEX } from "../regex-pattrens";

const PAN_REGEX = /^[A-Z]{3}[A,B,C,F,G,H,L,J,P,T]{1}[A-Z]{1}[0-9]{4}[A-Z]{1}$/;

function checkPanNumber(panNumber: any) {
  return panNumber && panNumber !== 'undefined' && panNumber !== '' && !PAN_REGEX.test(panNumber);
}

export function checkTanNumber(tanNumber: any) {
  return tanNumber && tanNumber !== 'undefined' && tanNumber !== '' && !TAN_REGEX.test(tanNumber);
}

export default checkPanNumber;
