import Client from 'src/modules/clients/entity/client.entity';
import { Organization } from 'src/modules/organization/entities/organization.entity';
import {
  BaseEntity,
  Column,
  CreateDateColumn,
  Entity,
  JoinTable,
  ManyToMany,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import DscActivity from './dsc-activity.entity';
import ClientGroup from 'src/modules/client-group/client-group.entity';

export enum DscRegisterStatus {
  NOT_ISSUED = 'not_issued',
  ISSUED = 'issued',
  RECEIVED = 'received',
}

@Entity()
class DscRegister extends BaseEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  holderName: string;

  @Column({ type: 'date' })
  expiryDate: string;

  @Column({ type: 'datetime', nullable: true })
  issuedDate: Date;

  @Column({ type: 'datetime', nullable: true })
  receivedDate: Date;

  @Column({ nullable: true })
  mobileNumber: string;

  @Column({ nullable: true })
  email: string;

  @Column()
  password: string;

  @Column({ nullable: true })
  tokenNumber: string;

  @Column({ nullable: true })
  panNumber: string;

  @Column({ nullable: true })
  holderDesignation: string;

  @Column({ nullable: true })
  countryCode: string;

  @Column({ type: 'enum', enum: DscRegisterStatus, default: DscRegisterStatus.NOT_ISSUED })
  status: DscRegisterStatus;

  // @ManyToOne(() => Client, (client) => client.dscRegisters, { onDelete: 'CASCADE' })
  // client: Client;

  // @ManyToOne(() => ClientGroup, (clientGroup) => clientGroup.dscRegisters, { onDelete: 'CASCADE' })
  // clientGroup: ClientGroup;

  @ManyToMany(() => Client, (client) => client.dscRegisters)
  @JoinTable()
  clients: Client[];

  @ManyToMany(() => ClientGroup, (clientGroup) => clientGroup.dscRegisters)
  @JoinTable()
  clientGroups: ClientGroup[]


  @ManyToOne(() => Organization, (organization) => organization.dscRegisters)
  organization: Organization;

  @OneToMany(() => DscActivity, (dscActivity) => dscActivity.dscRegister)
  dscActivity: DscActivity[];

  @CreateDateColumn()
  createdAt: string;

  @UpdateDateColumn()
  updatedAt: string;


}

export default DscRegister;
