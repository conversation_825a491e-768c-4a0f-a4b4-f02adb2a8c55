import { BadRequestException } from "@nestjs/common";
import { ReceiptStatus, ReceiptType } from "src/modules/billing/entitities/receipt.entity";
import { BillableType } from "src/modules/reports/dto/get-employee-log-hours-report";
import { Organization } from 'src/modules/organization/entities/organization.entity';
import { TaskRecurringStatus, TaskStatusEnum } from 'src/modules/tasks/dto/types';
import Task from 'src/modules/tasks/entity/task.entity';
import { Brackets, createQueryBuilder, getManager } from 'typeorm';
import * as moment from 'moment';
import { ReceiptParticularStatus } from 'src/modules/billing/entitities/receipt-particular.entity';
import { formattedAmount, getTotalGstReport } from "src/modules/billing/totalCalculations";
import { Invoice, InvoiceStatus } from "src/modules/billing/entitities/invoice.entity";
import { title } from "process";
import InvoiceParticular from "src/modules/billing/entitities/invoice-particular.entity";

export const usertasksbyorgid = (payload: any) => {
  return `SELECT u.id,u.full_name,u.role_id,rol.name as rolename,COUNT(t.id) AS task_count,
  SUM(t.budgetedhours) as budgetedhours,
  SUM(CASE WHEN t.priority = 'none' THEN 1 ELSE 0 END) AS 'none',
  SUM(CASE WHEN t.priority = 'medium' THEN 1 ELSE 0 END) AS 'medium',
  SUM(CASE WHEN t.priority = 'low' THEN 1 ELSE 0 END) AS 'low',
  SUM(CASE WHEN t.priority = 'high' THEN 1 ELSE 0 END) AS 'high'
  FROM task_members_user tmu 
  left join task t on t.id = tmu.task_id and 
  (t.task_start_date > '${payload.from_taskstartdate}' and t.due_date < '${payload.to_taskduedate}') 
  RIGHT JOIN user u ON u.id = tmu.user_id 
  left join role rol on u.role_id = rol.id   
  where u.organization_id='${payload.organizationid}' and u.status = 'ACTIVE' and u.type = 'ORGANIZATION'  group by u.id`;
};

export const taskhrmsdetails = (payload: any) => {
  return (
    `select task.name,task.id,task.user_id,task.description,task.task_start_date,` +
    `task.due_date,lh.duration,cnt.display_name,task.budgetedhours,task.recurring_profile_id,task.priority,` +
    `usr.full_name as username,rle.name as rolename ` +
    `from task ` +
    `left join log_hour lh on lh.task_id = task.id ` +
    `left join client cnt on cnt.id = task.client_id ` +
    `left join user usr on task.user_id = usr.id ` +
    `left join role rle on usr.role_id = rle.id ` +
    `where task.organization_id='${payload.organizationid}' and task.user_id = '${payload.memberid}' and ` +
    `usr.type = 'ORGANIZATION' and ` +
    `task.recurring_profile_id is null and ` +
    `task_start_date >= '${payload.from_taskstartdate}' AND task.due_date <= '${payload.to_taskduedate}'`
  );
};

export const upcommingtasks = (payload: any) => {
  return `SELECT task.task_number,task.name,task.frequency,task.payment_status,task.task_start_date,
  task.due_date,cnt.display_name,tmu.user_id, usr.full_name as username,task.financial_year as period
  FROM task  
  left join client cnt on cnt.id = task.client_id 
  left join task_members_user tmu on tmu.task_id = task.id 
  left join user usr on usr.id = tmu.user_id 
  where task.organization_id ='${payload.organizationid}' and task.recurring='1' and 
  task.recurring_status='pending' and task.user_id ='${payload.memberid}'`;
};

export const statuswisetasks = (payload: any) => {
  return `SELECT u.id as id,u.full_name AS 'user_name',
  SUM(CASE WHEN t.status = 'todo' THEN 1 ELSE 0 END) AS 'todo',
  SUM(CASE WHEN t.status = 'in_progress' THEN 1 ELSE 0 END) AS 'in_progress',
  SUM(CASE WHEN t.status = 'on_hold' THEN 1 ELSE 0 END) AS 'on_hold',
  SUM(CASE WHEN t.status = 'under_review' THEN 1 ELSE 0 END) AS 'under_review',
  SUM(CASE WHEN t.status = 'completed' THEN 1 ELSE 0 END) AS 'completed',
  SUM(CASE WHEN t.status='terminated' THEN 1 ELSE 0 END) AS 'terminated',
  SUM(CASE WHEN t.status = 'deleted' THEN 1 ELSE 0 END) AS 'deleted',

  COUNT(t.id) AS 'total'
 FROM task_members_user tmu 
 left join task t on t.id = tmu.task_id and (t.task_start_date BETWEEN '${payload.fromDate}' AND '${payload.toDate}') 
 and t.parent_task_id is null
 and (t.status!='')
 and (t.recurring_status is null or t.recurring_status = 'created')
 RIGHT JOIN user u ON u.id = tmu.user_id and u.status IN ('ACTIVE','INACTIVE','DELETED')
 left join role rol on u.role_id = rol.id 
 where u.organization_id='${payload.organizationid}' and u.status IN ('ACTIVE','INACTIVE','DELETED')
 and u.type = 'ORGANIZATION' 
 ${payload?.billableType && payload?.billableType !== BillableType.ALL
      ? payload?.billableType === BillableType.BILLABLE
        ? `AND t.billable is true`
        : `AND t.billable is false`
      : ''
    }
  group by u.id
 ;`;
};

export const servicecategorystatusbytasks = (payload: any) => {
  return `SELECT u.full_name AS 'user_name',
  SUM(CASE WHEN t.status = 'todo' THEN 1 ELSE 0 END) AS 'todo',
  SUM(CASE WHEN t.status = 'in_progress' THEN 1 ELSE 0 END) AS 'in_progress',
  SUM(CASE WHEN t.status = 'under_review' THEN 1 ELSE 0 END) AS 'under_review',
  SUM(CASE WHEN t.status = 'on_hold' THEN 1 ELSE 0 END) AS 'on_hold',
  SUM(CASE WHEN t.status = 'completed' THEN 1 ELSE 0 END) AS 'completed',
  COUNT(t.id) AS 'total' FROM task_members_user tmu 
  left join task t on t.id = tmu.task_id and (t.task_start_date BETWEEN '${payload.fromDate}' AND '${payload.toDate}' ) and t.status !='terminated' and t.status !='deleted' and t.parent_task_id is null
  RIGHT JOIN user u ON u.id = tmu.user_id and u.status = 'ACTIVE'
  left join role rol on u.role_id = rol.id
  left join category c on c.id = t.category_id
  where u.organization_id='${payload.organizationid}' and u.status = 'ACTIVE' and u.type = 'ORGANIZATION'  and c.name = '${payload.category}'
  group by u.id`;
};

export const allcategoryandsubcategory = (payload: any) => {
  return `SELECT c.name as category,
  SUM(CASE WHEN t.status = 'todo' THEN 1 ELSE 0 END) AS 'todo',
  SUM(CASE WHEN t.status = 'in_progress' THEN 1 ELSE 0 END) AS 'in_progress',
  SUM(CASE WHEN t.status = 'under_review' THEN 1 ELSE 0 END) AS 'under_review',
  SUM(CASE WHEN t.status = 'on_hold' THEN 1 ELSE 0 END) AS 'on_hold',
  SUM(CASE WHEN t.status = 'completed' THEN 1 ELSE 0 END) AS 'completed',
  COUNT(t.id) AS 'total' FROM task_members_user tmu 
  left join task t on t.id = tmu.task_id and (t.task_start_date BETWEEN '${payload.fromDate}' AND '${payload.toDate}' ) and t.status !='terminated' and t.status !='deleted' and t.parent_task_id is null
  RIGHT JOIN user u ON u.id = tmu.user_id and u.status = 'ACTIVE'
  left join role rol on u.role_id = rol.id
  left join category c on c.id = t.category_id
  where u.organization_id='${payload.organizationid}' and u.id = '${payload.users}' and u.type = 'ORGANIZATION' 
  group by u.id `;
};

export const userbasedmasterreport = (payload: any) => {
  return `select 
    t.id as id,
    COALESCE(cnt.display_name, cntgrp.display_name) as clientname_clientgroupname,
    COALESCE(cnt.display_name, 0) as clientType,
    COALESCE(cntgrp.display_name, 0) as clientgroupType,
    cat.name as category,
     scat.name as sub_category,
     t.task_number as task_id,
     t.name as taskname,
      t.status as status
  FROM task_members_user tmu 
  left join task t on t.id = tmu.task_id  and (t.task_start_date BETWEEN '${payload.fromDate}' AND '${payload.toDate}' ) and t.parent_task_id is null
  RIGHT JOIN user u ON u.id = tmu.user_id 
  left join service svc on svc.id = t.service_id 
  left join category cat on cat.id = t.category_id 
  left join category scat on scat.id=t.sub_category_id
  left join client cnt on cnt.id = t.client_id 
  left join client_group cntgrp on cntgrp.id = t.client_group_id 
  where tmu.user_id ='${payload.users}' and t.organization_id = ${payload.organizationid} and (t.recurring_status = 'created' OR t.recurring_status IS NULL) 
  ${payload?.billableType && payload?.billableType !== BillableType.ALL
      ? payload?.billableType === BillableType.BILLABLE
        ? `AND t.billable is true`
        : `AND t.billable is false`
      : ''
    }

  `;
};

export const overduetasks = (payload: any) => {
  return `SELECT 
    u.id as id,
    u.full_name,
    SUM(CASE WHEN t.status = 'todo' THEN 1 ELSE 0 END) AS 'todo',
    SUM(CASE WHEN t.status = 'in_progress' THEN 1 ELSE 0 END) AS 'in_progress',
    SUM(CASE WHEN t.status = 'on_hold' THEN 1 ELSE 0 END) AS 'Hold',
    SUM(CASE WHEN t.status = 'under_review' THEN 1 ELSE 0 END) AS 'under_review',
    SUM(CASE WHEN t.status IN ('todo', 'in_progress', 'on_hold', 'under_review') THEN 1 ELSE 0 END) AS 'total'
  FROM 
    task_members_user tmu 
    LEFT JOIN task t ON t.id = tmu.task_id  
      AND t.status NOT IN ('terminated', 'deleted', 'completed') 
      AND (t.recurring_status = 'created' OR t.recurring_status IS NULL) and t.parent_task_id is null
      AND t.due_date < '${payload.fromDate}'
    RIGHT JOIN user u ON u.id = tmu.user_id
    LEFT JOIN role rol ON u.role_id = rol.id 
    LEFT JOIN category c ON c.id = t.category_id
  WHERE 
    t.organization_id = '${payload.organizationid}' and u.type = 'ORGANIZATION'
    ${payload?.billableType && payload?.billableType !== BillableType.ALL
      ? payload?.billableType === BillableType.BILLABLE
        ? `AND t.billable is true`
        : `AND t.billable is false`
      : ''
    }

  GROUP BY u.full_name;`;
};

export const detailedoverduetasks = (payload: any) => {
  return `select 
    t.id as id,
    COALESCE(cnt.display_name, cntgrp.display_name) as clientname_clientgroupname,
    COALESCE(cnt.display_name, 0) as clientType,
    COALESCE(cntgrp.display_name, 0) as clientgroupType,
    cat.name as category,
    scat.name as sub_category,
    t.task_number as task_id,
    t.name as taskname, 
    t.status as status ,
    DATE_FORMAT(t.due_date, '%d-%m-%y') dueDate,
    DATEDIFF( '${payload.fromDate}',t.due_date) AS daysoverdue  
  FROM task_members_user tmu 
  left join task t on t.id = tmu.task_id and t.status !='terminated' and t.status !='deleted' 
  and status !='completed' and t.parent_task_id is null and (t.recurring_status = 'created' OR t.recurring_status IS NULL) 
  RIGHT JOIN user u ON u.id = tmu.user_id
  left join service svc on svc.id = t.service_id 
  left join category cat on cat.id = t.category_id 
  left join category scat on scat.id =t.sub_category_id
  left join client cnt on cnt.id = t.client_id 
  left join client_group cntgrp on cntgrp.id = t.client_group_id 
  where t.organization_id = '${payload.organizationid}' and u.id='${payload.users}' and t.due_date < '${payload.fromDate}'
  ${payload?.billableType && payload?.billableType !== BillableType.ALL
      ? payload?.billableType === BillableType.BILLABLE
        ? `AND t.billable is true`
        : `AND t.billable is false`
      : ''
    }
   `;
};

export const detailedoverduecompletedtasks = async (payload: any) => {
  const organization = await Organization.findOne({
    where: { id: Number(payload?.organizationid) },
  });

  const overDueTasks = await createQueryBuilder(Task, 'task')
    .leftJoinAndSelect('task.client', 'client')
    .leftJoinAndSelect('task.clientGroup', 'clientGroup')
    .leftJoinAndSelect('task.organization', 'organization')
    .leftJoinAndSelect('task.members', 'members')
    .leftJoinAndSelect('task.category', 'category')
    .leftJoinAndSelect('members.imageStorage', 'imageStorage')
    .leftJoinAndSelect('task.subCategory', 'subCategory')
    .where('task.dueDate >= :fromDate', { fromDate: payload?.fromDate })
    .andWhere('task.dueDate <= :toDate', { toDate: payload?.toDate })
    .andWhere('task.status IN (:...status)', {
      status: [
        TaskStatusEnum.TODO,
        TaskStatusEnum.IN_PROGRESS,
        TaskStatusEnum.ON_HOLD,
        TaskStatusEnum.UNDER_REVIEW,
      ],
    })
    .andWhere('(task.recurringStatus is null or task.recurringStatus = :recurringStatus)', {
      recurringStatus: TaskRecurringStatus.CREATED,
    })
    .andWhere('task.parentTask IS NULL')
    .andWhere('task.organization.id = :orgId', { orgId: organization.id });

  if (payload?.users) {
    overDueTasks.andWhere('members.id = :userID', { userID: payload?.users });
  }

  if (payload?.billableType) {
    if (payload?.billableType === 'BILLABLE') {
      overDueTasks.andWhere('task.billable IS true');
    } else if (payload?.billableType === 'NON_BILLABLE') {
      overDueTasks.andWhere('task.billable IS false');
    }
  }

  const overDueTasksData = await overDueTasks.getMany();

  const completedOverDueTasks = await createQueryBuilder(Task, 'task')
    .leftJoinAndSelect('task.client', 'client')
    .leftJoinAndSelect('task.clientGroup', 'clientGroup')
    .leftJoinAndSelect('task.organization', 'organization')
    .leftJoinAndSelect('task.members', 'members')
    .leftJoinAndSelect('task.taskStatus', 'taskStatus')
    .leftJoinAndSelect('task.category', 'category')
    .leftJoinAndSelect('members.imageStorage', 'imageStorage')
    .leftJoinAndSelect('task.subCategory', 'subCategory')
    .where('task.dueDate >= :fromDate', { fromDate: payload?.fromDate })
    .andWhere('task.dueDate <= :toDate', { toDate: payload?.toDate })
    .andWhere('task.status = :status', { status: TaskStatusEnum.COMPLETED })
    .andWhere('(task.recurringStatus is null or task.recurringStatus = :recurringStatus)', {
      recurringStatus: TaskRecurringStatus.CREATED,
    })
    .andWhere('task.parentTask IS NULL')
    .andWhere('task.organization.id = :orgId', { orgId: organization.id });

  if (payload?.users) {
    completedOverDueTasks.andWhere('members.id = :userID', { userID: payload?.users });
  }

  if (payload?.billableType) {
    if (payload?.billableType === 'BILLABLE') {
      completedOverDueTasks.andWhere('task.billable IS true');
    } else if (payload?.billableType === 'NON_BILLABLE') {
      completedOverDueTasks.andWhere('task.billable IS false');
    }
  }

  const completedOverDueTasksdata = await completedOverDueTasks.getMany();

  const compltetedData = [];

  for (let item of completedOverDueTasksdata) {
    const sqlQuery = `SELECT MAX(ts.created_at) AS max_created_at 
    FROM task_status ts 
    WHERE ts.task_id = ${item.id} 
    AND ts.status = 'completed'`;
    const data = await getManager().query(sqlQuery);
    if (moment(data[0]?.max_created_at) > moment(item?.dueDate).endOf('day')) {
      item['maxCreatedAt'] = data[0]?.max_created_at;
      compltetedData.push(item);
    }
  }

  const allTaskData = [...overDueTasksData, ...compltetedData];

  allTaskData.sort((a, b) => b.id - a.id);

  for (let eachtask of allTaskData) {
    if (eachtask.status === TaskStatusEnum.COMPLETED) {
      eachtask['overDueBy'] = moment(eachtask['maxCreatedAt']).diff(
        moment(eachtask.dueDate),
        'days',
      );
    } else {
      eachtask['overDueBy'] = moment().diff(moment(eachtask.dueDate), 'days');
    }
  }

  const overDueFilterData = allTaskData.filter(item => item?.overDueBy !== 0);

  return overDueFilterData;
};

export const allusersefficiency = (payload: any) => {
  return `select usr.full_name,
  count(tsk.status) as completedtasks, sum(tsk.bhallocation) as allocated, 
    sum(JSON_EXTRACT(bhallocation, '$.budgeted_hours')) as bugeetedhours,
    ((sum(tsk.bhallocation)/JSON_EXTRACT(bhallocation, '$.budgeted_hours'))*100) as efficiency 
    from task as tsk
    left join service svc on svc.id = tsk.service_id
    left join category cat on cat.id = tsk.category_id
    left join client cnt on cnt.id = tsk.client_id
    left join user usr on usr.id = tsk.user_id
    where tsk.organization_id = '${payload.organizationid}' and tsk.status = 'completed' and tsk.due_date < now() and usr.type = 'ORGANIZATION' 
    group by usr.full_name`;
};

export const highestnumberoftaskscompletion = (payload: any) => {
  return `SELECT u.id as id,u.full_name AS 'user_name',
    COUNT(t.id)  as 'totaltasksassigned', 
    SUM(CASE WHEN t.status = 'terminated' THEN 1 ELSE 0 END) AS 'terminated_count',
    SUM(CASE WHEN t.status = 'deleted' THEN 1 ELSE 0 END) AS 'deleted_count',
    SUM(CASE WHEN t.status = 'completed' THEN 1 ELSE 0 END) AS 'completed_count'
    FROM task_members_user tmu 
    left join task t on t.id = tmu.task_id and (t.task_start_date BETWEEN '${payload.fromDate
    }' AND '${payload.toDate}')   and t.parent_task_id is null
    RIGHT JOIN user u ON u.id = tmu.user_id
    left join role rol on u.role_id = rol.id 
    where u.organization_id='${payload.organizationid}' and (t.recurring_status = 'created' OR t.recurring_status IS NULL) and u.type = 'ORGANIZATION' 
    ${payload?.billableType && payload?.billableType !== BillableType.ALL
      ? payload?.billableType === BillableType.BILLABLE
        ? `AND t.billable is true`
        : `AND t.billable is false`
      : ''
    }
    group by u.full_name`;
};

export const clientinvoiceunbilled = (payload: any) => {
  return `SELECT t.id,t.task_number as tasknumber,t.name, t.status,t.proforma_status, IFNULL(t.fee_amount,0) AS fee_amount, SUM(e.amount) AS total_expenditure,
    IFNULL(SUM( CASE WHEN e.task_expense_type = 'PURE_AGENT' THEN e.amount END),0) AS pureagent,
    IFNULL(SUM( CASE WHEN e.task_expense_type = 'ADDITIONAL' THEN e.amount END),0) AS additional,
    (
        SELECT COUNT(DISTINCT(t2.id)) 
        FROM task t2 
        LEFT JOIN expenditure e2 ON e2.task_id = t2.id 
        WHERE t2.client_id = '${payload.clientId}'
        AND (t2.name LIKE '%${payload.search}%' OR t2.task_number LIKE '%${payload.search}%')
        AND (t2.recurring_status = 'created' OR t2.recurring_status IS NULL)
        AND t2.status != 'deleted' AND t2.status != 'terminated'
        AND t2.payment_status = 'UNBILLED'
        AND t2.billable is true
        AND t2.parent_task_id IS NULL 
    ) AS total_count
    FROM task t
    LEFT JOIN expenditure e ON e.task_id = t.id 
    WHERE t.client_id = '${payload.clientId}'
    AND (t.name LIKE '%${payload.search}%' OR t.task_number LIKE '%${payload.search}%')
    AND (t.recurring_status = 'created' OR t.recurring_status IS NULL)
    AND t.status != 'deleted' and t.status !='terminated'
    AND t.billable is true
    AND t.payment_status = 'UNBILLED'
    AND t.billable is true
    AND t.parent_task_id IS NULL
    GROUP BY t.id, t.name, t.status, t.fee_amount
       ${payload.sortQuery}
    LIMIT ${payload.pageCount} OFFSET ${payload.offset};`;
};

export const clientGroupinvoiceunbilled = (payload: any) => {
  let query = `SELECT t.id,t.task_number as tasknumber,t.name, t.status, t.proforma_status, IFNULL(t.fee_amount,0) AS fee_amount, SUM(e.amount) AS total_expenditure,
    IFNULL(SUM( CASE WHEN e.task_expense_type = 'PURE_AGENT' THEN e.amount END),0) AS pureagent,
    IFNULL(SUM( CASE WHEN e.task_expense_type = 'ADDITIONAL' THEN e.amount END),0) AS additional,
    (
        SELECT COUNT(DISTINCT(t2.id)) 
        FROM task t2 
        LEFT JOIN expenditure e2 ON e2.task_id = t2.id 
        WHERE t2.client_group_id = '${payload.clientGroupId}'
        AND (t2.name LIKE '%${payload.search}%' OR t2.task_number LIKE '%${payload.search}%')
        AND (t2.recurring_status = 'created' OR t2.recurring_status IS NULL)
        AND t2.status != 'deleted' AND t2.status != 'terminated'
        AND t2.payment_status = 'UNBILLED'
        AND t2.billable is true
        AND t2.parent_task_id IS NULL 
    ) AS total_count
    FROM task t
    LEFT JOIN expenditure e ON e.task_id = t.id 
    WHERE t.client_group_id = '${payload.clientGroupId}'
    AND (t.name LIKE '%${payload.search}%' OR t.task_number LIKE '%${payload.search}%')
    AND (t.recurring_status = 'created' OR t.recurring_status IS NULL)
    AND t.status != 'deleted' and t.status !='terminated'
    AND t.billable is true
    AND t.payment_status = 'UNBILLED'
    AND t.billable is true
    AND t.parent_task_id IS NULL
    GROUP BY t.id, t.name, t.status, t.fee_amount`;
  if (payload.limit) {
    query += ` LIMIT ${payload.limit} `;
  }
  if (payload.offset) {
    query += ` OFFSET ${payload.offset}`
  }
  return query;
};

export const clientinvoicebilled = (payload: any) => {
  // payload.sortQuery = '';
  // if (payload.sort?.column && payload?.sort?.direction) {
  //   payload.sortQuery = 'ORDER BY ' + `${payload.sort.column} ${payload.sort.direction}`;
  // }
  payload.sortQuery = '';
  if (payload?.sort?.column && payload?.sort?.direction) {
    if (payload.sort.column === 'invoice_date') {
      payload.sortQuery = `ORDER BY STR_TO_DATE(invoice_date, '%Y-%m-%d') ${payload.sort.direction}`;
    } else if (payload.sort.column === 'invoice_due_date') {
      payload.sortQuery = `ORDER BY STR_TO_DATE(invoice_due_date, '%Y-%m-%d') ${payload.sort.direction}`;
    }
    else {
      payload.sortQuery = `ORDER BY ${payload.sort.column} ${payload.sort.direction}`;
    }
  }
  return `SELECT 
            t.id, 
            t.name, 
            t.task_number AS tasknumber, 
            t.status AS status, 
            DATE_FORMAT(i.invoice_date, '%d-%m-%Y') AS invoice_date,
            DATE_FORMAT(i.invoice_due_date, '%d-%m-%Y') AS invoice_due_date, 
            SUM(i.grand_total) AS amount,
            (SELECT COUNT(t2.id) 
             FROM task t2 
             LEFT JOIN invoice i2 ON i2.id = t2.invoice_id
             WHERE t2.client_id = '${payload.clientId}'
                 AND t2.status != 'deleted' 
                 AND t2.status != 'terminated' 
                 AND t2.payment_status = 'BILLED'
                 AND (t2.name LIKE '%${payload.search}%' OR t2.task_number LIKE '%${payload.search}%') 
                 AND t2.parent_task_id IS NULL
            ) AS total_count
        FROM task t 
        LEFT JOIN invoice i ON i.id = t.invoice_id
        WHERE t.client_id = '${payload.clientId}'
            AND t.status != 'deleted' 
            AND t.status != 'terminated' 
            AND t.payment_status = 'BILLED'
            AND (t.name LIKE '%${payload.search}%' OR t.task_number LIKE '%${payload.search}%') 
            AND t.parent_task_id IS NULL 
        GROUP BY t.id
        ${payload.sortQuery}
        LIMIT ${payload.pageCount} OFFSET ${payload.offset};`;
};

export const clientgroupinvoicebilled = (payload: any) => {
  let query = `SELECT 
            t.id, 
            t.name, 
            t.task_number AS tasknumber, 
            t.status AS status, 
            DATE_FORMAT(i.invoice_date, '%d-%m-%Y') AS invoice_date,
            DATE_FORMAT(i.invoice_due_date, '%d-%m-%Y') AS invoice_due_date, 
            SUM(i.grand_total) AS amount,
            (SELECT COUNT(t2.id) 
             FROM task t2 
             LEFT JOIN invoice i2 ON i2.id = t2.invoice_id
             WHERE t2.client_group_id = '${payload.clientGroupId}'
                 AND t2.status != 'deleted' 
                 AND t2.status != 'terminated' 
                 AND t2.payment_status = 'BILLED'
                 AND (t2.name LIKE '%${payload.search}%' OR t2.task_number LIKE '%${payload.search}%') 
                 AND t2.parent_task_id IS NULL
            ) AS total_count
        FROM task t 
        LEFT JOIN invoice i ON i.id = t.invoice_id
        WHERE t.client_group_id = '${payload.clientGroupId}'
            AND t.status != 'deleted' 
            AND t.status != 'terminated' 
            AND t.payment_status = 'BILLED'
            AND (t.name LIKE '%${payload.search}%' OR t.task_number LIKE '%${payload.search}%') 
            AND t.parent_task_id IS NULL 
        GROUP BY t.id`;
  if (payload?.limit) {
    query += ` LIMIT ${payload.limit} `;
  }
  if (payload?.offset) {
    query += ` OFFSET ${payload.offset}`
  }
  return query;
};

export const clientinvoicebilling = (payload: any) => {
  return `select * from invoice where client_id = ${payload.clientId}
    AND invoice_number LIKE '%${payload.search}%';`;
};

export const clientgroupinvoicebilling = (payload: any) => {
  return `select * from invoice where client_group_id = ${payload.clientGroupId}
    AND invoice_number LIKE '%${payload.search}%';`;
};

export const clientproformainvoicebilling = (payload: any) => {
  return `select * from proforma_invoice where client_id=${payload.clientId}
  AND invoice_number LIKE '%${payload.search}%';`;
}

export const clientgroupproformainvoicebilling = (payload: any) => {
  return `select * from proforma_invoice where client_group_id=${payload.clientGroupId}
  AND invoice_number LIKE '%${payload.search}%';`;
}

export const clientinvoicereceipts = (payload: any) => {
  payload.sortQuery = '';
  if (payload?.sort?.column && payload?.sort?.direction) {
    if (payload.sort.column === 'receipt_date') {
      payload.sortQuery = `ORDER BY STR_TO_DATE(receipt_date, '%Y-%m-%d') ${payload.sort.direction}`;
    } else {
      payload.sortQuery = `ORDER BY ${payload.sort.column} ${payload.sort.direction}`;
    }
  }
  return `select r.id as id,receipt_number,
    status,
    DATE_FORMAT(receipt_date, '%d-%m-%y') receipt_date,
    payment_mode,amount,
    DATE_FORMAT(r.created_at, '%d-%m-%y') created_at,
    (
      SELECT COUNT(*)
      FROM receipt r
      WHERE r.client_id = '${payload.clientId}'
      AND r.receipt_number LIKE '%${payload.search}%'
  ) AS total_count,
  r.type,
  be.trade_name
  from receipt r LEFT JOIN billing_entity be ON r.billing_entity_id = be.id
  where client_id='${payload.clientId}'
    AND receipt_number LIKE '%${payload.search}%'
     ${payload.sortQuery}
      LIMIT ${payload.pageCount} OFFSET ${payload.offset};`;
};

export const clientgroupinvoicereceipts = (payload: any) => {
  let query = `select r.id as id,receipt_number,
    status,
    DATE_FORMAT(receipt_date, '%d-%m-%y')receipt_date,
    payment_mode,amount,
    DATE_FORMAT(r.created_at, '%d-%m-%y') created_at,
    (
      SELECT COUNT(*)
      FROM receipt r
      WHERE r.client_group_id = '${payload.clientGroupId}'
      AND r.receipt_number LIKE '%${payload.search}%'
  ) AS total_count,
  r.type,
  be.trade_name
  from receipt r LEFT JOIN billing_entity be ON r.billing_entity_id = be.id
  where client_group_id='${payload.clientGroupId}'
    AND receipt_number LIKE '%${payload.search}%'
    order by r.created_at desc`;
  if (payload?.limit) {
    query += ` LIMIT ${payload.limit} `;
  }
  if (payload?.offset) {
    query += ` OFFSET ${payload.offset}`;
  }
  return query;
};

export const clientdashboardamountreceived = (payload: any) => {
  return `select sum(amount) as amountrecieved from receipt where client_id= '${payload.clientId}';`;
};
export const clientdashboardamountdue = (payload: any) => {
  return `SELECT 
        IFNULL((
            SELECT SUM(i.grand_total) 
            FROM invoice i 
            WHERE i.client_id = '${payload.clientId}' 
            AND i.status != 'CANCELLED'
        ) -  IFNULL(
            (
                SELECT SUM(r.amount)
                FROM receipt r
                WHERE r.client_id = '${payload.clientId}'
            ), 0
        ), 0) AS dueamount
    FROM 
        client c 
    WHERE 
        c.id = '${payload.clientId}'
        AND c.status != 'deleted';
    `;
};
export const clientdashboardinvoiceunbilled = (payload: any) => {
  return `SELECT COUNT(*) AS total
    FROM (
      SELECT t.id,t.task_number,t.name, t.status, t.fee_amount, SUM(e.amount) AS total_expenditure
      FROM task t 
      LEFT JOIN expenditure e ON e.task_id = t.id 
      WHERE t.client_id = '${payload.clientId}'
      AND (t.recurring_status != 'pending' OR t.recurring_status IS NULL)
      AND t.status != 'terminated' 
      AND t.status != 'deleted'
      AND t.billable is true
      AND t.payment_status = 'UNBILLED'
      AND t.parent_task_id IS NULL
      GROUP BY t.id, t.name, t.status, t.fee_amount
    ) subquery;
    `;
};

export const clientdashboardinvoicebilled = (payload: any) => {
  return `SELECT COUNT(*) AS total
        FROM (
          SELECT t.id,t.task_number,t.name, t.status, t.fee_amount, SUM(e.amount) AS total_expenditure
          FROM task t 
          LEFT JOIN expenditure e ON e.task_id = t.id 
          WHERE t.client_id = '${payload.clientId}'
          AND (t.recurring_status != 'pending' OR t.recurring_status IS NULL)
          AND t.status != 'terminated' 
          AND t.status != 'deleted'
          AND t.payment_status = 'BILLED'
          AND t.parent_task_id IS NULL
          GROUP BY t.id, t.name, t.status, t.fee_amount
        ) subquery;`;
};

export const clientpureagentreceivedanddue = (payload: any) => {
  return `SELECT SUM(rp.pure_agent_amount) AS pureagentamountreceived,
        (SUM(iop.amount) - SUM(rp.pure_agent_amount)) AS pureagentamountdue 
        FROM vider.invoice i 
        LEFT JOIN vider.receipt_particular rp ON rp.invoice_id = i.id 
        LEFT JOIN vider.ON iop ON iop.invoice_id = i.id 
        WHERE i.client_id = '${payload.clientId}';`;
};

export const clientdashboardactivitylog = (payload: any) => {
  return `SELECT grand_total AS amount, created_at, 'Invoice Raised' AS label, NULL AS credits_used
        FROM invoice
        WHERE client_id = '${payload.clientId}'
        
        UNION ALL
        
        SELECT amount, created_at AS created_at, 
          CASE 
            WHEN type = 'INVOICE' THEN 'Payment Receipt'
            WHEN type = 'ADVANCE' THEN 'Advance Received' 
          END AS label,
          credits_used
        FROM receipt 
        WHERE client_id = '${payload.clientId}' 
        ORDER BY created_at DESC 
        LIMIT 20`;
};

export const clientslistinvoice = (payload: any, user: any, ViewAll: any, ViewAssigned: any) => {


  return `SELECT 
    c.id,
    c.display_name,
    c.category,
    COUNT(DISTINCT t.id) AS totaltasks,
    COUNT(DISTINCT CASE WHEN t.billable = true THEN t.id END) AS billabletasks,
    COUNT(DISTINCT CASE WHEN t.payment_status = 'UNBILLED' AND t.billable = true THEN t.id END) AS unbilledtasks,
    COUNT(DISTINCT CASE WHEN t.payment_status = 'BILLED' AND t.billable = true THEN t.id END) AS billedtasks,
   CASE 
    WHEN ABS(
        IFNULL(
            (
                SELECT SUM(i.grand_total)
                FROM invoice i
                WHERE i.client_id = c.id 
                  AND i.organization_id = '${payload.organizationid}'
                  AND i.status != 'CANCELLED'
            )
            - IFNULL(
                (
                    SELECT SUM(r.amount + r.credits_used)
                    FROM receipt r
                    WHERE r.organization_id = '${payload.organizationid}'
                      AND r.client_id = c.id 
                      AND r.status = '${ReceiptStatus.CREATED}' 
                      AND r.type = '${ReceiptType.INVOICE}'
                ), 
                0
            )
            - IFNULL(
                (
                    SELECT SUM(invoice.sub_total * (CAST(invoice.tds_rate AS CHAR)) / 100)
                    FROM invoice
                    WHERE invoice.client_id = c.id
                      AND invoice.organization_id = '${payload.organizationid}' 
                      AND invoice.status != 'CANCELLED'
                ),
                0
            ),
            0
        )
    ) < 0.0000001 THEN 0
    ELSE (
        IFNULL(
            (
                SELECT SUM(i.grand_total)
                FROM invoice i
                WHERE i.client_id = c.id 
                  AND i.organization_id = '${payload.organizationid}'
                  AND i.status != 'CANCELLED'
            )
            - IFNULL(
                (
                    SELECT SUM(r.amount + r.credits_used)
                    FROM receipt r
                    WHERE r.organization_id = '${payload.organizationid}'
                      AND r.client_id = c.id 
                      AND r.status = '${ReceiptStatus.CREATED}' 
                      AND r.type = '${ReceiptType.INVOICE}'
                ), 
                0
            )
            - IFNULL(
                (
                    SELECT SUM(invoice.sub_total * (CAST(invoice.tds_rate AS CHAR)) / 100)
                    FROM invoice
                    WHERE invoice.client_id = c.id
                      AND invoice.organization_id = '${payload.organizationid}' 
                      AND invoice.status != 'CANCELLED'
                ),
                0
            ),
            0
        )
    )
END AS dueamount,
    (
        SELECT COUNT(DISTINCT c2.id)
        FROM client c2
        LEFT JOIN task t2 ON c2.id = t2.client_id 
            AND (t2.recurring_status = 'created' OR t2.recurring_status IS NULL) 
            AND t2.status NOT IN ('terminated', 'deleted') 
            AND t2.parent_task_id IS NULL
        LEFT JOIN client_client_managers_user ccmu ON c2.id = ccmu.client_id
        LEFT JOIN user u ON ccmu.user_id = u.id
        WHERE c2.organization_id = '${payload.organizationid}' 
          AND c2.status != 'deleted' 
          AND c2.display_name LIKE '%${payload.search}%'
          ${ViewAssigned && !ViewAll ? `AND u.id = ${user.id}` : " "}
    ) AS total_count
FROM 
    client c
LEFT JOIN task t ON c.id = t.client_id 
    AND (t.recurring_status = 'created' OR t.recurring_status IS NULL) 
    AND t.status NOT IN ('terminated', 'deleted') 
    AND t.parent_task_id IS NULL
LEFT JOIN client_client_managers_user ON c.id = client_client_managers_user.client_id
LEFT JOIN user ON client_client_managers_user.user_id = user.id
WHERE 
    c.organization_id = '${payload.organizationid}' 
    AND (t.billable IS TRUE OR t.id IS NULL OR t.billable IS FALSE)
    AND c.status != 'deleted' 
    AND c.display_name LIKE '%${payload.search}%'
    ${ViewAssigned && !ViewAll ? `AND user.id = ${user.id}` : " "}
GROUP BY 
    c.id
${payload.sortQuery}
LIMIT ${payload.limit} OFFSET ${payload.offset};
`
};
export const clientsoverduereminder = (payload: any, user: any, ViewAll: any, ViewAssigned: any) => {
  // return `
  let query = `    SELECT 
      client_data.*,
      COUNT(*) OVER() AS total_count
    FROM (
      SELECT 
        c.id,
        c.display_name,
        c.email,
        CASE 
          WHEN ABS(
            IFNULL((
              SELECT SUM(i.grand_total)
              FROM invoice i
              WHERE i.client_id = c.id 
                AND i.organization_id = '${payload.organizationid}'
                AND i.status != 'CANCELLED'
            ) - IFNULL((
              SELECT SUM(r.amount + r.credits_used)
              FROM receipt r
              WHERE r.organization_id = '${payload.organizationid}'
                AND r.client_id = c.id 
                AND r.status = '${ReceiptStatus.CREATED}' 
                AND r.type = '${ReceiptType.INVOICE}'
            ), 0) - IFNULL((
              SELECT SUM(invoice.sub_total * (CAST(invoice.tds_rate AS CHAR)) / 100)
              FROM invoice
              WHERE invoice.client_id = c.id
                AND invoice.organization_id = '${payload.organizationid}' 
                AND invoice.status != 'CANCELLED'
            ), 0), 0)
          ) < 0.0000001 THEN 0
          ELSE (
            IFNULL((
              SELECT SUM(i.grand_total)
              FROM invoice i
              WHERE i.client_id = c.id 
                AND i.organization_id = '${payload.organizationid}'
                AND i.status != 'CANCELLED'
            ) - IFNULL((
              SELECT SUM(r.amount + r.credits_used)
              FROM receipt r
              WHERE r.organization_id = '${payload.organizationid}'
                AND r.client_id = c.id 
                AND r.status = '${ReceiptStatus.CREATED}' 
                AND r.type = '${ReceiptType.INVOICE}'
            ), 0) - IFNULL((
              SELECT SUM(invoice.sub_total * (CAST(invoice.tds_rate AS CHAR)) / 100)
              FROM invoice
              WHERE invoice.client_id = c.id
                AND invoice.organization_id = '${payload.organizationid}' 
                AND invoice.status != 'CANCELLED'
            ), 0), 0)
          )
        END AS dueamount
      FROM 
        client c
      WHERE 
        c.organization_id = '${payload.organizationid}' 
        AND c.status != 'deleted'
        AND c.display_name LIKE '%${payload.search}%'
      GROUP BY 
        c.id
      HAVING 
        dueamount > 0
      ${payload.sortQuery}
    ) AS client_data
  `;

  if (payload.limit) {
    query += ` LIMIT ${payload.limit} `;
  }
  if (payload.offset) {
    query += ` OFFSET ${payload.offset}`;
  }

  return query;
};

export const clientsgrouplistinvoice = (
  payload: any,
  user: any = {},
  ViewAll: any = false,
  ViewAssigned: any = false
) => {
  let query = `SELECT 
    c.id,
    c.display_name,
    COUNT(DISTINCT t.id) AS totaltasks,
    COUNT(DISTINCT CASE WHEN t.billable = true THEN t.id END) AS billabletasks,
    COUNT(DISTINCT CASE WHEN t.payment_status = 'UNBILLED' AND t.billable = true THEN t.id END) AS unbilledtasks,
    COUNT(DISTINCT CASE WHEN t.payment_status = 'BILLED' AND t.billable = true THEN t.id END) AS billedtasks,
    CASE 
      WHEN ABS(
        COALESCE((
          SELECT SUM(i.grand_total)
          FROM invoice i
          WHERE i.client_group_id = c.id 
            AND i.organization_id = '${payload.organizationid}'
            AND i.status != 'CANCELLED'
        ), 0)
        - COALESCE((
          SELECT SUM(r.amount + r.credits_used)
          FROM receipt r
          WHERE r.organization_id = '${payload.organizationid}'
            AND r.client_group_id = c.id 
            AND r.status = '${ReceiptStatus.CREATED}' 
            AND r.type = '${ReceiptType.INVOICE}'
        ), 0)
        - COALESCE((
          SELECT SUM(i2.sub_total * (CAST(i2.tds_rate AS DECIMAL(10,2))) / 100)
          FROM invoice i2
          WHERE i2.client_group_id = c.id
            AND i2.organization_id = '${payload.organizationid}' 
            AND i2.status != 'CANCELLED'
        ), 0)
      ) < 0.0000001 THEN 0
      ELSE (
        COALESCE((
          SELECT SUM(i.grand_total)
          FROM invoice i
          WHERE i.client_group_id = c.id 
            AND i.organization_id = '${payload.organizationid}'
            AND i.status != 'CANCELLED'
        ), 0)
        - COALESCE((
          SELECT SUM(r.amount + r.credits_used)
          FROM receipt r
          WHERE r.organization_id = '${payload.organizationid}'
            AND r.client_group_id = c.id 
            AND r.status = '${ReceiptStatus.CREATED}' 
            AND r.type = '${ReceiptType.INVOICE}'
        ), 0)
        - COALESCE((
          SELECT SUM(i2.sub_total * (CAST(i2.tds_rate AS DECIMAL(10,2))) / 100)
          FROM invoice i2
          WHERE i2.client_group_id = c.id
            AND i2.organization_id = '${payload.organizationid}' 
            AND i2.status != 'CANCELLED'
        ), 0)
      )
    END AS dueamount,
    (
      SELECT COUNT(DISTINCT c2.id)
      FROM client_group c2
      LEFT JOIN client_group_client_group_managers_user ccmu ON c2.id = ccmu.client_group_id
      LEFT JOIN user u ON ccmu.user_id = u.id
      WHERE c2.organization_id = '${payload.organizationid}' 
        AND c2.status != 'deleted' 
        AND c2.display_name LIKE '%${payload.search || ''}%'
        ${ViewAssigned && !ViewAll ? `AND u.id = ${user.id}` : ''}
    ) AS total_count
FROM 
    client_group c
LEFT JOIN task t ON c.id = t.client_group_id 
    AND (t.recurring_status = 'created' OR t.recurring_status IS NULL) 
    AND t.status NOT IN ('terminated', 'deleted') 
    AND t.parent_task_id IS NULL
LEFT JOIN client_group_client_group_managers_user ON c.id = client_group_client_group_managers_user.client_group_id
LEFT JOIN user ON client_group_client_group_managers_user.user_id = user.id
WHERE 
    c.organization_id = '${payload.organizationid}' 
    AND (t.billable IS TRUE OR t.id IS NULL OR t.billable IS FALSE)
    AND c.status != 'deleted' 
    ${payload?.search ? `AND c.display_name LIKE '%${payload.search}%'` : ''} 
    ${ViewAssigned && !ViewAll ? `AND user.id = ${user.id}` : ''}
GROUP BY 
    c.id
${payload.sortQuery}
LIMIT ${payload.limit} OFFSET ${payload.offset};
`;
  return query;
};

export const taskscompletedtobilledtasks = (payload: any) => {
  return `SELECT t.id as id,
        COALESCE(c.display_name, cntgrp.display_name) as clientname_clientgroupname,
        COALESCE(c.display_name, 0) as clientType,
        COALESCE(cntgrp.display_name, 0) as clientgroupType,
        t.task_number as tasknumber,
        t.name as taskname,
        i.invoice_number as invoicenumber,
        DATE_FORMAT(i.invoice_date,'%d-%m%-%y') as invoicedate,
        DATE_FORMAT(i.invoice_due_date,'%d-%m%-%y') as invoiceduedate,i.grand_total as invoiceamount  
        FROM task t 
        LEFT JOIN invoice i ON i.id = t.invoice_id 
        LEFT JOIN client c ON c.id = t.client_id 
        LEFT JOIN client_group cntgrp on cntgrp.id = t.client_group_id 
        LEFT JOIN expenditure e ON e.task_id = t.id 
        WHERE t.payment_status = 'BILLED' AND (t.recurring_status = 'created' OR t.recurring_status IS NULL) AND t.parent_task_id is null 
        AND (t.status = 'completed') AND t.organization_id ='${payload.organizationid}' AND 
        (i.created_at BETWEEN '${payload.fromDate}' AND '${payload.toDate}')
        ${payload.billingEntity ? `AND i.billing_entity_id IN (${payload.billingEntity})` : ''}

        GROUP BY t.name, t.task_number, t.fee_amount,c.display_name 
        ORDER BY c.display_name;`;
};

export const taskscompletedtounbilledtasks = (payload: any) => {
  return `SELECT t.id as id,
        COALESCE(c.display_name, cntgrp.display_name) as clientname_clientgroupname,
        COALESCE(c.display_name, 0) as clientType,
        COALESCE(cntgrp.display_name, 0) as clientgroupType,
        t.task_number as tasknumber,
        t.name as taskname, 
        COALESCE(t.fee_amount, 0) as fee,
        COALESCE(SUM(CASE WHEN e.task_expense_type = 'PURE_AGENT' THEN e.amount END), 0) AS pureagent,
        COALESCE(SUM(CASE WHEN e.task_expense_type = 'ADDITIONAL' THEN e.amount END), 0) AS additionalchargers,
        COALESCE(SUM(CASE WHEN e.task_expense_type IN ('PURE_AGENT', 'ADDITIONAL') THEN e.amount END), 0) + COALESCE(t.fee_amount, 0) AS amounttotal 
        FROM task t 
        LEFT JOIN invoice i ON i.id = t.invoice_id 
        LEFT JOIN client c ON c.id = t.client_id 
        LEFT JOIN client_group cntgrp on cntgrp.id = t.client_group_id 
        LEFT JOIN expenditure e ON e.task_id = t.id 
        WHERE t.payment_status = 'UNBILLED' AND (t.recurring_status = 'created' OR t.recurring_status IS NULL) AND t.parent_task_id is null 
        AND t.billable is true 
        AND (t.status = 'completed') AND t.organization_id ='${payload.organizationid}' AND (DATE(t.updated_at) >= '${payload.fromDate}' AND DATE(t.updated_at) <= '${payload.toDate}')
        GROUP BY c.display_name, t.task_number, t.name, t.fee_amount
        ORDER BY c.display_name;`;
};

// export const taskInvoice = (payload: any) => {
//   console.log(payload, 'payload');
//   return `SELECT t.id as id,
//         COALESCE(c.display_name, cntgrp.display_name) as clientname_clientgroupname,
//         COALESCE(c.display_name, 0) as clientType,
//         COALESCE(cntgrp.display_name, 0) as clientgroupType,          
//         t.task_number as tasknumber,
//         t.name as taskname,
//         i.invoice_number as invoicenumber,
//         DATE_FORMAT(i.invoice_date,'%d-%m-%y') as invoicedate,
//         DATE_FORMAT(i.invoice_due_date,'%d-%m-%y') as invoiceduedate,i.grand_total as invoiceamount,
//         bi.location_of_supply,
//         bi.has_gst,
//         i.place_of_supply

//         FROM task t             
//         LEFT JOIN invoice i ON i.id = t.invoice_id 
//         LEFT JOIN billing_entity bi ON i.billing_entity_id=bi.id
//         LEFT JOIN client c ON c.id = t.client_id 
//         LEFT JOIN client_group cntgrp on cntgrp.id = t.client_group_id 
//         WHERE t.organization_id = '${payload.organizationid}'
//         ${payload.client?.length ? `AND (c.id IN (${payload.client}) OR cntgrp.id IN (${payload.client}))` : ''} 

//         AND (t.recurring_status = 'created' OR t.recurring_status IS NULL) 
//         AND t.status != 'terminated' 
//         AND t.status != 'deleted' 
//         AND t.parent_task_id IS NULL
//         AND t.payment_status = 'BILLED'
//         GROUP BY c.display_name, t.task_number, t.name, t.fee_amount
//         ORDER BY c.display_name;`;
// }

// export const taskInvoice = async (payload: any) => {
//   console.log("T-1")
//   const query = createQueryBuilder(Task, 't')
//     .leftJoin('t.parentTask', 'parentTask')
//     .leftJoin('t.invoice', 'i')
//     .leftJoin('t.organization', 'organization')
//     .leftJoin('i.billingEntity', 'bi')
//     .leftJoin('t.client', 'c')
//     .leftJoin('t.clientGroup', 'cntgrp')
//     .leftJoinAndSelect('i.particulars', 'particulars')

//     .select([
//       't.id AS id',
//       "COALESCE(c.displayName, cntgrp.displayName) AS clientname_clientgroupname",
//       "COALESCE(c.displayName, 0) AS clientType",
//       "COALESCE(cntgrp.displayName, 0) AS clientgroupType",
//       't.taskNumber AS tasknumber',
//       't.name AS taskname',
//       'i.invoiceNumber AS invoicenumber',
//       `DATE_FORMAT(i.invoiceDate,'%d-%m-%y') AS invoiceDate`,
//       `DATE_FORMAT(i.invoiceDueDate,'%d-%m-%y') AS invoiceDueDate`,
//       'i.grandTotal',
//       'bi.locationOfSupply',
//       'bi.hasGst',
//       'i.placeOfSupply',
//       `'' AS igst`,
//       `'' AS cgst`,
//       `'' AS sgst`,
//     ])
//     .where('organization.id = :organizationId', { organizationId: payload.organizationid })
//     .andWhere('(t.recurringStatus = :created OR t.recurringStatus IS NULL)', {
//       created: 'created',
//     })
//     .andWhere('t.status NOT IN (:...statuses)', {
//       statuses: ['terminated', 'deleted'],
//     })
//     .andWhere('parentTask.id IS NULL')
//     .andWhere('t.paymentStatus = :billed', {
//       billed: 'BILLED',
//     });

//   if (payload.client?.length) {
//     const clientIds = payload.client.split(',').map((id: string) => +id);
//     query.andWhere('(c.id IN (:...clientIds) OR cntgrp.id IN (:...clientIds))', {
//       clientIds,
//     });
//   }

//   query.limit(10)

//   let data = await query.getRawMany();



//   return data;

// };
// export const taskInvoice = async (payload: any) => {
//   console.log(payload)



//   let qb = createQueryBuilder(Task, 'task')
//     .select([
//       'task.id',
//       'task.name',
//       'task.taskNumber',
//       'client.displayName',
//       'clientGroup.displayName',
//       'invoice.id',
//       'invoice.invoiceNumber',
//       'invoice.invoiceDate',
//       'invoice.invoiceDueDate',
//       'invoice.grandTotal',
//       'invoice.placeOfSupply',
//       'invoice.divideTax',
//       'billingEntity.locationOfSupply',
//       'billingEntity.hasGst',
//     ])
//     .leftJoin('task.invoice', 'invoice')

//     .leftJoin('invoice.billingEntity', 'billingEntity')
//     .leftJoin('task.client', 'client')
//     .leftJoin('task.clientGroup', 'clientGroup')
//     .leftJoin('task.organization', 'organization')
//     .leftJoin('task.members', 'member')
//     .where('organization.id = :orgId', { orgId: payload.organizationid })
//     .where('invoice.id is not null')
//     .andWhere('billingEntity.hasGst=:bolValue', { bolValue: true })
//     .andWhere('invoice.status !=:invStatus', { invStatus: InvoiceStatus.CANCELLED })
//     .andWhere('task.taskStartDate >= :fromDate', {
//       fromDate: moment(payload?.fromdate).format('YYYY-MM-DD'),
//     })
//     .andWhere('task.taskStartDate <= :toDate', {
//       toDate: moment(payload?.todate).format('YYYY-MM-DD'),
//     })


//   if (payload.client?.length) {
//     qb.andWhere(
//       new Brackets(qb => {
//         qb.where('client.id IN (:...clientIds)', { clientIds: payload.client })
//           .orWhere('clientGroup.id IN (:...clientIds)', { clientIds: payload.client });
//       })
//     );
//   }
//   if (payload.users?.length) {
//     qb.andWhere('member.id IN (:...userIds)', { userIds: payload.users });
//   }


//   let d: any = await qb.getMany();


//   const invIds = d.map((task: Task) => task.invoice.id);
//   const particulars = await Invoice.createQueryBuilder('invoice')
//     .leftJoinAndSelect('invoice.particulars', 'particulars')
//     .where('invoice.id IN (:...invIds)', { invIds })
//     .addSelect(['invoice.id'])
//     .getMany();

//   const particularsMap = new Map(
//     particulars.map(invoice => [invoice.id, invoice.particulars])
//   );

//   d = d.map(task => ({
//     ...task,
//     invoice: {
//       ...task.invoice,
//       particulars: particularsMap.get(task.invoice.id)
//     }
//   }))


//   const taskIds = d.map(task => task.id);
//   const leaders = await Task.createQueryBuilder('task')
//     .select(['task.id', 'leader.id', 'leader.fullName'])
//     .leftJoin('task.taskLeader', 'leader')
//     .where('task.id IN (:...taskIds)', { taskIds })
//     .getMany();
//   const leaderMap = new Map(
//     leaders.map(task => [task.id, task.taskLeader])
//   );

//   d = d.map(task => ({
//     ...task,
//     taskLeader: leaderMap.get(task.id) || [],
//   }));



//   const members = await Task.createQueryBuilder('task')
//     .select(['task.id', 'members.id', 'members.fullName'])
//     .leftJoin('task.members', 'members')
//     .where('task.id IN (:...taskIds)', { taskIds })
//     .getMany();
//   // Create a map for quick lookup
//   const memberMap = new Map(
//     members.map(task => [task.id, task.members])
//   );



//   d = d.map(task => ({
//     ...task,
//     members: memberMap.get(task.id) || [],
//   }));


//   const formatted = d.map(task => {
//     const sameState = task.invoice?.billingEntity?.locationOfSupply === task.invoice.placeOfSupply?.split('-')[1];


//     return {
//       id: task.id,
//       clientname_clientgroupname: task?.client?.displayName ?? task?.clientGroup?.displayName,
//       taskname: task.name,
//       task_id: task.taskNumber,
//       invoicenumber: task.invoice?.invoiceNumber,
//       invoicedate: task.invoice?.invoiceDate,
//       invoiceduedate: task.invoice?.invoiceDueDate,
//       grandtotal: task.invoice?.grandTotal,
//       sgst: ((task.invoice.billingEntity.hasGst && sameState) && !task.invoice.divideTax) ? formattedAmount(getTotalGstReport(task.invoice.particulars) / 2) : 0,
//       cgst: ((task.invoice.billingEntity.hasGst && sameState) && !task.invoice.divideTax) ? formattedAmount(getTotalGstReport(task.invoice.particulars) / 2) : 0,
//       igst: ((task.invoice.billingEntity.hasGst && !sameState) || task.invoice.divideTax) ? formattedAmount(getTotalGstReport(task.invoice.particulars)) : 0,
//       taskLeaders: task.taskLeader.map(item => ({ title: item.fullName, src: item?.imageStorage?.fileUrl })),
//       members: task.members.map(item => ({ title: item.fullName, src: item?.imageStorage?.fileUrl })),
//       taxRate: task.invoice.particulars?.find(item => item?.taskId == task.id)?.gst

//     };
//   });

//   return formatted
// }

export const taskInvoice = async (payload: any) => {
  let qb = createQueryBuilder(Task, 'task')
    .select([
      'task.id',
      'task.name',
      'task.taskNumber',
      'client.displayName',
      'clientGroup.displayName',
      'invoice.id',
      'invoice.invoiceNumber',
      'invoice.invoiceDate',
      'invoice.invoiceDueDate',
      'invoice.grandTotal',
      'invoice.placeOfSupply',
      'invoice.divideTax',
      'billingEntity.locationOfSupply',
      'billingEntity.hasGst',
    ])
    .leftJoin('task.invoice', 'invoice')
    .leftJoin('invoice.billingEntity', 'billingEntity')
    .leftJoin('task.client', 'client')
    .leftJoin('task.clientGroup', 'clientGroup')
    .leftJoin('task.organization', 'organization')
    .leftJoin('task.members', 'member')
    .where('organization.id = :orgId', { orgId: payload.organizationid })
    .andWhere('invoice.id IS NOT NULL')
    .andWhere('billingEntity.hasGst = :bolValue', { bolValue: true })
    .andWhere('invoice.status != :invStatus', { invStatus: InvoiceStatus.CANCELLED })
    .andWhere('task.taskStartDate >= :fromDate', {
      fromDate: moment(payload?.fromDate).format('YYYY-MM-DD'),
    })
    .andWhere('task.taskStartDate <= :toDate', {
      toDate: moment(payload?.toDate).format('YYYY-MM-DD'),
    });

  if (payload.client?.length) {
    qb.andWhere(
      new Brackets(qb => {
        qb.where('client.id IN (:...clientIds)', { clientIds: payload.client })
          .orWhere('clientGroup.id IN (:...clientIds)', { clientIds: payload.client });
      })
    );
  }

  if (payload.users?.length) {
    qb.andWhere('member.id IN (:...userIds)', { userIds: payload.users });
  }

  let d: any = await qb.getMany();

  if (!d.length) return [];

  const invIds = d.map((task: Task) => task.invoice.id);
  if (!invIds.length) return [];

  const particulars = await Invoice.createQueryBuilder('invoice')
    .leftJoinAndSelect('invoice.particulars', 'particulars')
    .where('invoice.id IN (:...invIds)', { invIds })
    .addSelect(['invoice.id'])
    .getMany();

  const particularsMap = new Map(
    particulars.map(invoice => [invoice.id, invoice.particulars])
  );

  d = d.map(task => ({
    ...task,
    invoice: {
      ...task.invoice,
      particulars: particularsMap.get(task.invoice.id),
    },
  }));

  const taskIds = d.map(task => task.id);
  if (!taskIds.length) return [];

  const leaders = await Task.createQueryBuilder('task')
    .select(['task.id', 'leader.id', 'leader.fullName'])
    .leftJoin('task.taskLeader', 'leader')
    .where('task.id IN (:...taskIds)', { taskIds })
    .getMany();

  const leaderMap = new Map(
    leaders.map(task => [task.id, task.taskLeader])
  );

  d = d.map(task => ({
    ...task,
    taskLeader: leaderMap.get(task.id) || [],
  }));

  const members = await Task.createQueryBuilder('task')
    .select(['task.id', 'members.id', 'members.fullName'])
    .leftJoin('task.members', 'members')
    .where('task.id IN (:...taskIds)', { taskIds })
    .getMany();

  const memberMap = new Map(
    members.map(task => [task.id, task.members])
  );

  d = d.map(task => ({
    ...task,
    members: memberMap.get(task.id) || [],
  }));

  const formatted = d.map(task => {
    const sameState =
      task.invoice?.billingEntity?.locationOfSupply ===
      task.invoice.placeOfSupply?.split('-')[1];

    return {
      id: task.id,
      clientname_clientgroupname:
        task?.client?.displayName ?? task?.clientGroup?.displayName,
      taskname: task.name,
      task_id: task.taskNumber,
      invoicenumber: task.invoice?.invoiceNumber,
      invoicedate: task.invoice?.invoiceDate,
      invoiceduedate: task.invoice?.invoiceDueDate,
      grandtotal: task.invoice?.grandTotal,
      sgst:
        task.invoice.billingEntity.hasGst &&
          sameState &&
          !task.invoice.divideTax
          ? formattedAmount(getTotalGstReport(task.invoice.particulars) / 2)
          : 0,
      cgst:
        task.invoice.billingEntity.hasGst &&
          sameState &&
          !task.invoice.divideTax
          ? formattedAmount(getTotalGstReport(task.invoice.particulars) / 2)
          : 0,
      igst:
        (task.invoice.billingEntity.hasGst && !sameState) ||
          task.invoice.divideTax
          ? formattedAmount(getTotalGstReport(task.invoice.particulars))
          : 0,
      taskLeaders: task.taskLeader.map(item => ({
        title: item.fullName,
        src: item?.imageStorage?.fileUrl,
      })),
      members: task.members.map(item => ({
        title: item.fullName,
        src: item?.imageStorage?.fileUrl,
      })),
      taxRate: task.invoice.particulars?.find(
        item => item?.taskId == task.id
      )?.gst,
    };
  });

  return formatted;
};



export const ClientTasksCompletedToBilled = (payload: any) => {
  let sqlQuery = `SELECT t.id as id,t.task_number as tasknumber,t.name as taskname,i.invoice_number as invoicenumber,DATE_FORMAT(i.invoice_date, '%d-%m-%y') as invoicedate,
        DATE_FORMAT(i.invoice_due_date,'%d-%m-%y') as invoiceduedate,i.grand_total as invoiceamount   
        FROM task t LEFT JOIN invoice i ON i.id = t.invoice_id 
        LEFT JOIN client c ON c.id = t.client_id 
        LEFT JOIN client_group cg ON cg.id = t.client_group_id 
        LEFT JOIN expenditure e ON e.task_id = t.id 
        WHERE t.payment_status = 'BILLED' AND (t.recurring_status = 'created' OR t.recurring_status IS NULL) AND t.parent_task_id is null 
        AND (t.status = 'completed') AND t.organization_id ='${payload.organizationid}'`;
  if (payload.clientType === 'CLIENT_GROUP') {
    sqlQuery += ` AND t.client_group_id = '${payload.clients}' `
  }
  if (payload.clientType !== 'CLIENT_GROUP') {
    sqlQuery += ` AND t.client_id = '${payload.clients}' `
  }
  sqlQuery += ` ${payload.billingEntity ? `AND i.billing_entity_id IN (${payload.billingEntity})` : ''}
        GROUP BY t.name, t.task_number, t.fee_amount, c.display_name`;
  return sqlQuery;
};

export const ClientTasksCompletedToUnBilled = (payload: any) => {
  let sqlQuery = `SELECT t.id as id,
        t.task_number as tasknumber,
        t.name AS taskname,
        COALESCE(t.fee_amount, 0) as fee,
        COALESCE(SUM(CASE WHEN e.task_expense_type = 'PURE_AGENT' THEN e.amount END), 0) AS pureagent,
        COALESCE(SUM(CASE WHEN e.task_expense_type = 'ADDITIONAL' THEN e.amount END), 0) AS additionalchargers,
        COALESCE(SUM(CASE WHEN e.task_expense_type IN ('PURE_AGENT', 'ADDITIONAL') THEN e.amount END), 0) + COALESCE(t.fee_amount, 0) AS amounttotal 
        FROM task t LEFT JOIN invoice i ON i.id = t.invoice_id 
        LEFT JOIN client c ON c.id = t.client_id 
        LEFT JOIN client_group cg ON cg.id = t.client_group_id 
        LEFT JOIN expenditure e ON e.task_id = t.id 
        WHERE t.payment_status = 'UNBILLED' AND (t.recurring_status = 'created' OR t.recurring_status IS NULL) AND t.parent_task_id is null 
        AND t.billable is true
        AND (t.status = 'completed') 
        AND t.organization_id ='${payload.organizationid}'`;
  if (payload.clientType === 'CLIENT_GROUP') {
    sqlQuery += ` AND t.client_group_id = '${payload.clients}' `
  }
  if (payload.clientType !== 'CLIENT_GROUP') {
    sqlQuery += ` AND t.client_id = '${payload.clients}' `
  }
  sqlQuery += ` GROUP BY t.name, t.task_number,t.fee_amount`;
  return sqlQuery;
};

export const balancedueforinvoicesraisedreport = (payload: any) => {
  return `SELECT 
  i.id as id,
  COALESCE(c.display_name, cg.display_name) as clientname_clientgroupname,
  COALESCE(c.display_name, 0) as clientType,
  COALESCE(cg.display_name, 0) as clientgroupType,
  i.invoice_number as invoicenumber,
  DATE_FORMAT(i.invoice_date,'%d-%m-%y') as invoicedate,
  IFNULL(i.grand_total, 0) AS invoiceamount,
  IFNULL((sum(rp.pure_agent_amount)+(sum(rp.service_amount))),0) as amountrecieved,
  IFNULL(
  IFNULL(i.grand_total,0)
   - (IFNULL(SUM(rp.pure_agent_amount),0)
    + IFNULL(SUM(rp.service_amount),0)


   
  +  IFNULL(i.sub_total * (CAST(i.tds_rate AS CHAR)) / 100, 0)

   
    ),
     0) AS balancedueamount  ,
      IFNULL(i.sub_total * (CAST(i.tds_rate AS CHAR)) / 100, 0) as tds_amount
  FROM invoice i 
  LEFT JOIN client c on c.id = i.client_id 
  LEFT JOIN client_group cg on cg.id = i.client_group_id 
  LEFT JOIN receipt_particular rp on rp.invoice_id=i.id
  WHERE i.organization_id = '${payload.organizationid}' AND i.created_at <= '${payload.fromDate
    }' AND i.status != "cancelled" AND i.status != "paid"
    AND (rp.status!='${ReceiptParticularStatus.CANCELLED}' OR rp.status is null)
      ${payload.billingEntity ? `AND i.billing_entity_id IN (${payload.billingEntity})` : ''}
      GROUP BY i.id`
};
export const balancedueforinvoicesraisedreminder = (payload: any) => {
  console.log(payload.offset,payload.limit,payload.search)
let query= `SELECT 
  i.id as id,
  COALESCE(c.display_name, cg.display_name) as clientname_clientgroupname,c.email as email,
  COALESCE(c.display_name, 0) as clientType,
  COALESCE(cg.display_name, 0) as clientgroupType,
  i.invoice_number as invoicenumber,
  DATE_FORMAT(i.invoice_date,'%d-%m-%y') as invoicedate,
  IFNULL(i.grand_total, 0) AS invoiceamount,
  IFNULL((sum(rp.pure_agent_amount)+(sum(rp.service_amount))),0) as amountrecieved,
  IFNULL(
  IFNULL(i.grand_total,0)
   - (IFNULL(SUM(rp.pure_agent_amount),0)
    + IFNULL(SUM(rp.service_amount),0)


   
  +  IFNULL(i.sub_total * (CAST(i.tds_rate AS CHAR)) / 100, 0)

   
    ),
     0) AS balancedueamount  ,
      IFNULL(i.sub_total * (CAST(i.tds_rate AS CHAR)) / 100, 0) as tds_amount,
          (
        SELECT COUNT(DISTINCT(i2.id)) 
        FROM invoice i2 
        LEFT JOIN receipt_particular rp2 on rp2.invoice_id=i2.id
        WHERE i2.organization_id = '${payload.organizationid}' AND i2.created_at <= '${payload.fromDate
          }' AND i2.status != "cancelled" AND i2.status != "paid"
          AND (rp2.status!='${ReceiptParticularStatus.CANCELLED}' OR rp2.status is null)
            ${payload.billingEntity ? `AND i2.billing_entity_id IN (${payload.billingEntity})` : ''}
                  AND (c.display_name LIKE '%${payload.search}%')

    ) AS total_count
  FROM invoice i 
  LEFT JOIN client c on c.id = i.client_id 
  LEFT JOIN client_group cg on cg.id = i.client_group_id 
  LEFT JOIN receipt_particular rp on rp.invoice_id=i.id
  WHERE i.organization_id = '${payload.organizationid}' AND 
  i.created_at <= '${payload.fromDate
    }' AND i.status != "cancelled" AND i.status != "paid"
    AND (rp.status!='${ReceiptParticularStatus.CANCELLED}' OR rp.status is null)
      ${payload.billingEntity ? `AND i.billing_entity_id IN (${payload.billingEntity})` : ''}
      AND (c.display_name LIKE '%${payload.search}%')

      GROUP BY i.id`
       if (payload.limit) {
    query += ` LIMIT ${payload.limit} `;
  }
  if (payload.offset) {
    query += ` OFFSET ${payload.offset}`
  }
  return query;
};

export const invoiceoverduereports = (payload: any) => {
  return `SELECT i.id as id,
        COALESCE(c.display_name, cntgrp.display_name) as clientname_clientgroupname,
        COALESCE(c.display_name, 0) as clientType,
        COALESCE(cntgrp.display_name, 0) as clientgroupType,
        b.trade_name as billing_entity,
        i.invoice_number as invoicenumber,DATE_FORMAT(i.invoice_date, '%d-%m-%y') as invoicedate,
        DATE_FORMAT(i.invoice_due_date,'%d-%m-%y') as invoiceduedate,
        DATEDIFF(NOW(), i.invoice_due_date) as daysoverdue, i.grand_total as invoiceamount
        FROM invoice i
        LEFT JOIN client c ON c.id = i.client_id
        LEFT JOIN client_group cntgrp on cntgrp.id = i.client_group_id  
        LEFT JOIN billing_entity b ON b.id = i.billing_entity_id
        WHERE i.organization_id = '${payload.organizationid
    }' AND i.invoice_date != 0 AND i.invoice_due_date <= '${payload.fromDate
    }' AND i.status != 'PAID' AND i.status != 'CANCELLED'
        ${payload.billingEntity ? `AND i.billing_entity_id IN (${payload.billingEntity})` : ''}
        AND i.invoice_due_date != 0 AND i.grand_total != 0 AND (i.invoice_date + i.invoice_due_date + i.grand_total) != 0`;
};

export const receiptmanagementreport = (payload: any) => {
  return `SELECT r.id as id,
  COALESCE(c.display_name, cg.display_name) as clientname_clientgroupname,
  COALESCE(c.display_name, 0) as clientType,
  COALESCE(cg.display_name, 0) as clientgroupType,
  be.trade_name as billing_entity,
  CASE 
  WHEN r.type = 'INVOICE' THEN 'Payment Receipt'
  WHEN r.type = 'ADVANCE' THEN 'Advance'
  ELSE 'Unknown Type'
END as receipttype,
        r.receipt_number as receiptnumber,
        DATE_FORMAT(r.receipt_date ,'%d-%m-%y') as receiptdate,r.receipt_number as receiptnumber,r.amount as receiptamount,
        CASE 
        WHEN r.status='CREATED' THEN 'Created'
        WHEN r.status='CANCELLED' THEN 'Cancelled'
        ELSE 'Unknown Type'
        END  as status
        FROM receipt r 
        LEFT JOIN client c ON c.id = r.client_id 
        LEFT JOIN client_group cg ON cg.id = r.client_group_id 
        LEFT JOIN receipt_particular rp ON rp.receipt_id = r.id 
        LEFT JOIN billing_entity be ON be.id=r.billing_entity_id
        WHERE r.organization_id = '${payload.organizationid}' AND r.receipt_number IS NOT NULL
        ${payload.billingEntity ? `AND r.billing_entity_id IN (${payload.billingEntity})` : ''} 
        AND r.receipt_date IS NOT NULL AND r.amount IS NOT NULL 
        AND (r.created_at BETWEEN '${payload.fromDate}' AND '${payload.toDate}') 
        GROUP BY c.display_name,c.client_id, r.receipt_date, r.receipt_number, r.created_at;`;
};


export const proformaInvoiceReport = (payload: any) => {
  return `SELECT 
    COALESCE(c.display_name, cg.display_name) as clientname_clientgroupname,
    COALESCE(c.display_name, 0) as clientType,
    COALESCE(cg.display_name, 0) as clientgroupType,
    b.trade_name as billing_entity,
   i.invoice_number as proformaInvoicenumber,
    DATE_FORMAT(i.invoice_date, '%d-%m-%y') as invoicedate,
   i.grand_total as invoiceamount,
   i.status as status
    FROM proforma_invoice i 
    LEFT JOIN client c ON c.id = i.client_id 
        LEFT JOIN client_group cg ON cg.id = i.client_group_id 
      LEFT JOIN billing_entity b ON b.id = i.billing_entity_id
     WHERE i.organization_id='${payload.organizationid}'
      ${payload.billingEntity ? `AND i.billing_entity_id IN (${payload.billingEntity})` : ''} 
      ${payload.client ? `AND c.id IN (${payload.client})` : ''} 
      ${payload.client ? `AND cg.id IN (${payload.client})` : ''} 
      ${payload.status ? `AND i.status IN (${payload.status})` : ''} 
      `
}
