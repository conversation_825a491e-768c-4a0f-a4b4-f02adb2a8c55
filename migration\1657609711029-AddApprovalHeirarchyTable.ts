import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddApprovalHeirarchyTable1657609711029
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `create table approval_heirarchy (
            id int not null auto_increment,
            name varchar(255) not null,
            type enum('SERVICE','TASK','IPRO') not null,
            organization_id int null,
            created_at datetime(6) default current_timestamp(6),
            updated_at datetime(6) default current_timestamp(6),
            primary key (id),
            CONSTRAINT FK_approval_heirarchy_organization_id FOREIGN KEY (organization_id) REFERENCES organization (id) ON DELETE SET NULL
        );`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}
