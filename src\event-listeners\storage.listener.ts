import { Injectable } from '@nestjs/common';
import { OnEvent } from '@nestjs/event-emitter';
import { Event_Actions } from './actions';
import Task from 'src/modules/tasks/entity/task.entity';
import { getUserDetails, insertINTONotificationUpdate } from 'src/utils/re-use';
import ViderWhatsappSessions from 'src/modules/whatsapp/entity/viderWhatsappSessions';
import { sendWhatsAppTextMessage } from 'src/modules/whatsapp/whatsapp.service';
import { fullMobileNumberWithCountry } from 'src/utils/validations/fullMobileWithCountry';

@Injectable()
export class StorageListner {
  @OnEvent(Event_Actions.COLLECT_DATA_FILES_UPLOAD, { async: true })
  async fileUpload(event) {
    const task = await Task.findOne({
      where: {
        id: event.taskId,
      },
      relations: ['members', 'organization', 'client', 'clientGroup'],
    });
    for (const file of event.files) {
      const users: any[] = task.members.map((member) => member.id);
      const title = 'Task I-Pro (Data Collection)';
      const body = `"<strong>${task?.client ? task?.client?.displayName : task?.clientGroup?.displayName
        }</strong>" has just shared a new document,"<strong>${file}</strong>", to help you complete the task "<strong>${task.taskNumber
        }</strong>" -"<strong>${task.name
        }</strong>". This document might contain additional information, instructions, or resources to assist you.`;
      const key = 'COLLECT_DATA_PUSH';
      insertINTONotificationUpdate(title, body, users, task.organization.id, key);
      // whatsapp
      try {
        if (users !== undefined) {
          for (let userId of users) {
            const sessionValidation = await ViderWhatsappSessions.findOne({
              where: { userId: userId,status:'ACTIVE' },
            });
            if (sessionValidation) {
              const loggedinuserDetails = await getUserDetails(userId);
              const {
                full_name: userFullName,
                mobile_number: userPhoneNumber,
                country_code: countryCode,
              } = loggedinuserDetails;
              const userWhatsAppNumber = fullMobileNumberWithCountry(userPhoneNumber, countryCode);
              const key = 'CREATION_COLLECT_DATA_WHATSAPP';
              const formattedDate = new Date(task?.dueDate).toISOString().split('T')[0];

              const whatsappMessageBody = `
Hi ${userFullName},
Great news!
The client for task "${task?.name}" has uploaded documents for in IPRO

Task details:
Task ID: ${task?.taskNumber}
Task Name: ${task?.name}
Task Due Date: ${formattedDate}
Client Name: ${task?.client?.displayName}


Thanks,
The ATOM Team `;
              await sendWhatsAppTextMessage(
                userWhatsAppNumber,
                whatsappMessageBody,
                task?.organization?.id,
                title,
                userId,
                key,
              );
            }
          }
        }
      } catch (error) {
        console.error('Error sending Client WhatsApp notification:', error);
      }
    }
  }

  @OnEvent(Event_Actions.COLLECT_DATA_FILES_DELETED, { async: true })
  async fileDelete(event) {
    const task = await Task.findOne({
      where: {
        id: event.taskId,
      },
      relations: ['members', 'organization', 'client', 'clientGroup'],
    });

    const users: any[] = task.members.map((member) => member.id);
    const title = 'Task I-Pro (Data Collection) Delete';
    const body = `"<strong>${task?.client ? task?.client?.displayName : task?.clientGroup?.displayName
      }</strong>" has just delete a new document,"<strong>${event.file}</strong>", to help you complete the task "<strong>${task.taskNumber
      }</strong>" -"<strong>${task.name
      }</strong>". This document might contain additional information, instructions, or resources to assist you.`;
    const key = 'COLLECT_DATA_PUSH';
    insertINTONotificationUpdate(title, body, users, task.organization.id, key);
    // whatsapp
    try {
      if (users !== undefined) {
        for (let userId of users) {
          const sessionValidation = await ViderWhatsappSessions.findOne({
            where: { userId: userId,status:'ACTIVE' },
          });
          if (sessionValidation) {
            const loggedinuserDetails = await getUserDetails(userId);
            const {
              full_name: userFullName,
              mobile_number: userPhoneNumber,
              country_code: countryCode,
            } = loggedinuserDetails;
            const userWhatsAppNumber = fullMobileNumberWithCountry(userPhoneNumber, countryCode);
            const key = 'CREATION_COLLECT_DATA_WHATSAPP';
            const formattedDate = new Date(task?.dueDate).toISOString().split('T')[0];

            const whatsappMessageBody = `
Hi ${userFullName},
Great news!
The client for task "${task?.name}" has deleted document for in IPRO

Task details:
Task ID: ${task?.taskNumber}
Task Name: ${task?.name}
Task Due Date: ${formattedDate}
Client Name: ${task?.client?.displayName}


Thanks,
The ATOM Team `;
            await sendWhatsAppTextMessage(
              userWhatsAppNumber,
              whatsappMessageBody,
              task?.organization?.id,
              title,
              userId,
              key,
            );
          }
        }
      }
    } catch (error) {
      console.error('Error sending Client WhatsApp notification:', error);
    }

  }
}
