import { MigrationInterface, QueryRunner } from 'typeorm';

export class AlterBankAccountTableColumTypes1655197406058
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE bank_account
                         MODIFY COLUMN upi_id varchar(255) null,
                         MODIFY COLUMN upi_attachment varchar(255) null`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}
