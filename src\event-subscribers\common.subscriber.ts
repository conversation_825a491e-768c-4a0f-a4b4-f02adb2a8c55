import { taskUpdated } from 'src/utils/afterInsert';
import { taskafterUpdated, taskafterUpdatedThenStatusUpdate } from 'src/utils/afterUpdate';
import { EntitySubscriberInterface, EventSubscriber, InsertEvent, UpdateEvent } from 'typeorm';

@EventSubscriber()
export class CommonSubscriber<T> implements EntitySubscriberInterface<T> {
  constructor(private cls: new (...a: any) => T) { }
  listenTo(): any {
    return this.cls;
  }
  
async afterInsert(event: InsertEvent<any>) {
    taskUpdated(event);
  }

  async afterUpdate(event: UpdateEvent<any>): Promise<any> {
    taskafterUpdatedThenStatusUpdate(event);
    taskafterUpdated(event);
  }
}
