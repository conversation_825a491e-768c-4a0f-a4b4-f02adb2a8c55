import { MigrationInterface, QueryRunner } from 'typeorm';

export class AlterOrganizationTable1655128860763 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE organization
             DROP COLUMN gst_regisered,
             ADD gst_verified boolean NOT NULL DEFAULT false,
             ADD pan_verified boolean NOT NULL DEFAULT false`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}
