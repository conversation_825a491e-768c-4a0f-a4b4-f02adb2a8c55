import { MigrationInterface, QueryRunner } from 'typeorm';

export class AlterServiceTable1657357827940 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE service
            ADD organization_id int null,
            ADD CONSTRAINT FK_service_organization_id FOREIGN KEY (organization_id) REFERENCES organization (id) ON DELETE SET NULL
        `,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}
