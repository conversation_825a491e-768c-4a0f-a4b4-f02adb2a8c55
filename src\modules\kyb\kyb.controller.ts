import {
    Body,
    Controller,
    Delete,
    Get,
    Param,
    ParseIntPipe,
    Post,
    Put,
    Query,
    Request,
    UseGuards,
} from '@nestjs/common';
import { JwtAuthGuard } from 'src/modules/users/jwt/jwt-auth.guard';
import { KybService } from './kyb.service';

@Controller('kyb')
export class KybController {
    constructor(private service: KybService) { }

    @UseGuards(JwtAuthGuard)
    @Post()
    add(@Request() req: any, @Body() body: any) {
        const { userId } = req.user;
        return this.service.addKyb(userId, body);
    }

    @UseGuards(JwtAuthGuard)
    @Get()
    get(@Request() req: any, @Query() query: any) {
        const { userId } = req.user;
        return this.service.getKybs(userId, query);
    }

    @UseGuards(JwtAuthGuard)
    @Put('/:id')
    update(@Request() req: any, @Body() body: any, @Param('id', ParseIntPipe) id: number) {
        const { userId } = req.user;
        return this.service.updateKyb(userId, id, body);
    }

    @UseGuards(JwtAuthGuard)
    @Delete('/:id')
    delete(@Param('id', ParseIntPipe) id: number, @Request() req: any) {
        const { userId } = req.user;
        return this.service.delete(id, userId);
    }
}