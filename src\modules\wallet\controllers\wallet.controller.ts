import { Body, Controller, Get, Param, ParseIntPipe, Patch, Post, Req, UseGuards, } from "@nestjs/common";
import { WalletService } from "../services/wallet.service";
import { JwtAuthGuard } from "src/modules/users/jwt/jwt-auth.guard";
import CreateTransactionDto from "../dto/create-transaction.dto";


@Controller('wallet')
export class WalletController {
    constructor(private service: WalletService) { }


    @UseGuards(JwtAuthGuard)
    @Get('activity')
    viewWalletActivity(@Req() req: any) {
        const { userId } = req.user;
        return this.service.viewWalletActivity(userId)
    };

    @UseGuards(JwtAuthGuard)
    @Get('history')
    viewWalletHistory(@Req() req:any){
        const { userId } = req.user;
        return this.service.viewWalletHistory(userId)

    }


    @UseGuards(JwtAuthGuard)
    @Patch()
    async addMoneyToWallet(@Req() req: any, @Body() data: any) {
        const { userId } = req.user;
        return this.service.addMoneyToWallet(userId, data);

    };

    @UseGuards(JwtAuthGuard)
    @Get('/balance')
    getCurrentWalletBalance(@Req() req: any) {
        const { userId } = req.user;
        return this.service.getCurrentWalletBalance(userId);
    };

    @UseGuards(JwtAuthGuard)
    @Post('transactions')
    addWalletTransaction(@Req() req: any, @Body() data: CreateTransactionDto) {
        const { userId } = req.user;
        return this.service.addWalletTransaction(data, userId);
    };

    @UseGuards(JwtAuthGuard)
    @Post('/activity')
    addWalletActivity(@Req() req: any, @Body() data: any) {
        const { userId } = req.user;
    
        return this.service.addWalletActivity(data, userId);
    };

    
    @UseGuards(JwtAuthGuard)
    @Get('debit-balance')
    getDebitBalance(@Req() req: any) {
        const { userId } = req.user;
        return this.service.getDebitBalance(userId);
    };


















};
