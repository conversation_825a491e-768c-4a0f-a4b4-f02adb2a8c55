import {
    BadRequestException,
    Body,
    Controller,
    Get,
    Param,
    ParseIntPipe,
    Post,
    Put,
    Query,
    Req,
    Request,
    UseGuards,
  } from '@nestjs/common';
  import { MarketingWebhookService } from './marketing-webhook.service'; // Create this service to handle webhook data

import ViderMarketingWebhook from './entity/vider-marketing-webhook.entity';
  
  @Controller('webhook') // The base path for the webhook endpoint
  export class MarketingWebhookController {
    constructor(private readonly webhookService: MarketingWebhookService) {}
  
    // @Get('/')
    // get(@Request() req: any, @Query() query: any) {
    //   return this.webhookService.getWebhook();
    // }
  
    @Get('/')
    async handleWebhook(@Request() req: any, @Query() query: any) {
      // Process webhook
      // const res = await this.webhookService.receiveWebhook(query);
      if (query['hub.mode'] == 'subscribe' && query['hub.verify_token'] == ('marketing')) {
        return query['hub.challenge'];
      } else {
        return 400;
       
      }
      // Return 200 OK response
      // return res.sendStatus(200);
    }
  
    @Post()
    async sendMessages(@Body() payload: any) {
      // Call your webhook service to process and store the payload
  
      const webhookLogData = new ViderMarketingWebhook();
      webhookLogData.createdTimestamp = new Date();
      webhookLogData.source = 'meta';
      webhookLogData.payload = JSON.stringify(payload);
      webhookLogData.status = 'RECEIVED';
      webhookLogData.destinationApplication = 'Marketing';
  
      await webhookLogData.save();
  
      return { success: true };
    }
  }
  