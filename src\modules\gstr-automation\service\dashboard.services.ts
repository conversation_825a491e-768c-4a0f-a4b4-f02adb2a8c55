import { BadRequestException, Injectable } from '@nestjs/common';
import { User, UserStatus } from 'src/modules/users/entities/user.entity';
import { Brackets, createQ<PERSON>y<PERSON><PERSON><PERSON>, getManager, getRepository, Like } from 'typeorm';

import Client from 'src/modules/clients/entity/client.entity';
import * as moment from 'moment';
import GstrCredentials, { GstrStatus } from '../entity/gstrCredentials.entity';
import OrganizationPreferences from 'src/modules/organization-preferences/entity/organization-preferences.entity';
import AutomationMachines from 'src/modules/automation/entities/automation_machines.entity';
import { IncomeTaxStatus } from 'src/modules/automation/entities/aut_client_credentials.entity';
import * as xlsx from 'xlsx';
import { Permissions } from 'src/modules/tasks/permission';
import * as ExcelJS from 'exceljs'
import { GstrOutstandingDemand } from '../entity/gstrDemands.entity';
import GstrAdditionalNoticeOrders from '../entity/gstrAdditionalOrdersAndNotices.entity';
import { CreatedType } from 'src/modules/automation/entities/aut_income_tax_eproceedings_fyi_notice_response_fya.entity';

@Injectable()
export class GstrDashboardService {
  async getNoticeAndOrdersIssueDates(
    organizationId: number,
    interval: '1week' | '15days' | '1month' | '1year' | 'today',
    dateColumn: 'date_of_issuance',
    query: any,
    userId: any,
    ViewAll: any,
    ViewAssigned: any
  ): Promise<number> {
    try {
      if (!organizationId) {
        throw new Error('Organization not found for the user.');
      }

      let intervalQuery = '';
      switch (interval) {
        case 'today':
          intervalQuery = 'INTERVAL 0 DAY';
          break;
        case '1week':
          intervalQuery = 'INTERVAL 1 WEEK';
          break;
        case '15days':
          intervalQuery = 'INTERVAL 15 DAY';
          break;
        case '1month':
          intervalQuery = 'INTERVAL 1 MONTH';
          break;
        case '1year':
          intervalQuery = 'INTERVAL 1 YEAR';
          break;
        default:
          throw new Error('Invalid interval');
      }

      let dateStr = '';
      if(interval === 'today'){
        dateStr = `STR_TO_DATE(${dateColumn}, '%d/%m/%Y') = CURDATE()`;
      }else{
        dateStr = `STR_TO_DATE(${dateColumn}, '%d/%m/%Y') >= DATE_SUB(CURDATE(), ${intervalQuery})`
      }

      let sql = `
        SELECT COUNT(DISTINCT gstr_notice_orders.id) AS count
        FROM gstr_notice_orders
        LEFT JOIN client ON gstr_notice_orders.client_id = client.id
        LEFT JOIN gstr_credentials ON gstr_credentials.client_id = client.id
        WHERE ${dateStr} AND gstr_notice_orders.organization_id = ${organizationId}
        AND  client.status != '${UserStatus.DELETED}'
        AND gstr_credentials.status != '${GstrStatus.DISABLE}'
      `;
      // if (query.assessmentYear && query.assessmentYear !== '') {
      //   sql += `AND assesment_year = '${query.assessmentYear}'`;
      // }
      if (ViewAssigned && !ViewAll) {
        sql += `
        AND EXISTS (
          SELECT 1
          FROM client_client_managers_user cm
          WHERE cm.client_id = client.id
          AND cm.user_id = ${userId}
        )
      `;
      }

      const result = await getManager().query(sql);
      return parseInt(result[0].count);
    } catch (error) {
      console.error(`Error fetching ${dateColumn} dates:`, error);
      throw error;
    }
  }

  async getAddNoticeAndOrderIssueDates(
    organizationId: number,
    interval: '1week' | '15days' | '1month' | '1year' | 'today',
    dateColumn: 'category_date',
    query: any,
    userId: any,
    ViewAll: any,
    ViewAssigned: any
  ): Promise<number> {
    try {
      if (!organizationId) {
        throw new Error('Organization not found for the user.');
      }

      let intervalQuery = '';
      switch (interval) {
        case 'today':
          intervalQuery = 'INTERVAL 0 DAY';
          break;
        case '1week':
          intervalQuery = 'INTERVAL 1 WEEK';
          break;
        case '15days':
          intervalQuery = 'INTERVAL 15 DAY';
          break;
        case '1month':
          intervalQuery = 'INTERVAL 1 MONTH';
          break;
        case '1year':
          intervalQuery = 'INTERVAL 1 YEAR';
          break;
        default:
          throw new Error('Invalid interval');
      }

      // let sql = `
      //   SELECT COUNT(*) AS count
      //   FROM gstr_additional_notice_orders
      //   WHERE ${dateColumn} >= DATE_SUB(CURDATE(), ${intervalQuery})
      //   AND organization_id = ${organizationId}
      // `;

      let dateStr = '';
      if(interval === 'today'){
        dateStr = `STR_TO_DATE(${dateColumn},'%d/%m/%Y') = CURDATE()`
      }else{
        dateStr = `STR_TO_DATE(${dateColumn},'%d/%m/%Y') >= DATE_SUB(CURDATE(), ${intervalQuery})`
      }

      let sql = `
        SELECT COUNT(DISTINCT(gstr_additional_notice_orders.id)) AS count
        FROM gstr_additional_notice_orders
        LEFT JOIN client ON gstr_additional_notice_orders.client_id = client.id
        LEFT JOIN gstr_credentials ON gstr_credentials.client_id = client.id
        WHERE ${dateStr}
        AND gstr_additional_notice_orders.organization_id = ${organizationId}
        AND  client.status != '${UserStatus.DELETED}'
        AND (gstr_credentials.status != '${GstrStatus.DISABLE}' OR gstr_credentials.status IS NULL)
      `;

      if (ViewAssigned && !ViewAll) {
        sql += `
        AND EXISTS (
          SELECT 1
          FROM client_client_managers_user cm
          WHERE cm.client_id = client.id
          AND cm.user_id = ${userId}
        )
      `;
      }

      if (query?.assessmentYear && query?.assessmentYear !== '') {
        sql += ` AND fy = '${query.assessmentYear}'`;
      }

      const result = await getManager().query(sql);
      return parseInt(result[0]?.count);
    } catch (error) {
      console.error(`Error fetching ${dateColumn} dates:`, error);
      throw error;
    }
  }

  async getNoticeAndOrderDueDates(
    organizationId: number,
    interval: '1week' | '15days' | '1month' | '1year' | 'today',
    dateColumn: 'due_date',
    query: any,
    userId: any,
    ViewAll: any,
    ViewAssigned: any
  ): Promise<number> {
    try {
      if (!organizationId) {
        throw new Error('Organization not found for the user.');
      }

      let intervalQuery = '';
      switch (interval) {
        case 'today':
          intervalQuery = 'INTERVAL 0 DAY';
          break;
        case '1week':
          intervalQuery = 'INTERVAL 1 WEEK';
          break;
        case '15days':
          intervalQuery = 'INTERVAL 15 DAY';
          break;
        case '1month':
          intervalQuery = 'INTERVAL 1 MONTH';
          break;
        case '1year':
          intervalQuery = 'INTERVAL 1 YEAR';
          break;
        default:
          throw new Error('Invalid interval');
      }

      let sql = `
      SELECT COUNT(DISTINCT gstr_notice_orders.id) AS count
      FROM gstr_notice_orders
       LEFT JOIN client ON gstr_notice_orders.client_id = client.id
        LEFT JOIN gstr_credentials ON gstr_credentials.client_id = client.id
      WHERE STR_TO_DATE(${dateColumn},'%d/%m/%Y') BETWEEN CURDATE() AND DATE_ADD(CURDATE(), ${intervalQuery})
      AND gstr_notice_orders.organization_id = ${organizationId}
        AND  client.status != '${UserStatus.DELETED}'
        AND gstr_credentials.status != '${GstrStatus.DISABLE}'
    `;

      // if (query?.assessmentYear && query?.assessmentYear !== '') {
      //   sql += `AND fy = '${query?.assessmentYear}'`;
      // }
      if (ViewAssigned && !ViewAll) {
        sql += `
        AND EXISTS (
          SELECT 1
          FROM client_client_managers_user cm
          WHERE cm.client_id = client.id
          AND cm.user_id = ${userId}
        )
      `;
      }
      const result = await getManager().query(sql);
      return parseInt(result[0]?.count, 10);
    } catch (error) {
      console.error(`Error fetching ${dateColumn} dates:`, error);
      throw error;
    }
  }

  async getAddNoticeAndOrderDueDates(
    organizationId: number,
    interval: '1week' | '15days' | '1month' | '1year' | 'today',
    dateColumn: 'due_date',
    query: any,
    userId: any,
    ViewAll: any,
    ViewAssigned: any
  ): Promise<number> {
    try {
      if (!organizationId) {
        throw new Error('Organization not found for the user.');
      }

      let intervalQuery = '';
      switch (interval) {
        case 'today':
          intervalQuery = 'INTERVAL 0 DAY';
          break;
        case '1week':
          intervalQuery = 'INTERVAL 1 WEEK';
          break;
        case '15days':
          intervalQuery = 'INTERVAL 15 DAY';
          break;
        case '1month':
          intervalQuery = 'INTERVAL 1 MONTH';
          break;
        case '1year':
          intervalQuery = 'INTERVAL 1 YEAR';
          break;
        default:
          throw new Error('Invalid interval');
      }

      let dateStr = '';
      if(interval === 'today'){
        dateStr =  `STR_TO_DATE(${dateColumn},'%d/%m/%Y') = CURDATE()`
      }else{
        dateStr = `STR_TO_DATE(${dateColumn},'%d/%m/%Y') BETWEEN CURDATE() AND DATE_ADD(CURDATE(), ${intervalQuery})`
      }

      let sql = `
      SELECT COUNT(*) AS count
      FROM gstr_additional_notice_orders
      LEFT JOIN client ON gstr_additional_notice_orders.client_id = client.id
      LEFT JOIN gstr_credentials ON gstr_credentials.client_id = client.id
      WHERE ${dateStr}
      AND gstr_additional_notice_orders.organization_id = ${organizationId}
        AND  client.status != '${UserStatus.DELETED}'
        AND gstr_credentials.status != '${GstrStatus.DISABLE}'
    `;

      if (ViewAssigned && !ViewAll) {
        sql += `
        AND EXISTS (
          SELECT 1
          FROM client_client_managers_user cm
          WHERE cm.client_id = client.id
          AND cm.user_id = ${userId}
        )
      `;
      }
      if (query?.assessmentYear && query?.assessmentYear !== '') {
        sql += `AND fy = '${query?.assessmentYear}'`;
      }
      const result = await getManager().query(sql);
      return parseInt(result[0]?.count, 10);
    } catch (error) {
      console.error(`Error fetching ${dateColumn} dates:`, error);
      throw error;
    }
  }

  async getNoticeOrdersDateCount(userId: number, query: any) {
    const user = await User.findOne({ where: { id: userId }, relations: ['organization', 'role'] });
    let ViewAll = user.role.permissions.find(
      (permission) => permission.slug === Permissions.VIEW_ALL_CLIENT_MANAGERS,
    );
    let ViewAssigned = user.role.permissions.find(
      (permission) => permission.slug === Permissions.VIEW_ASSIGNED_CLIENT_MANAGERS,
    );
    const organizationId = user?.organization?.id;
    if (organizationId) {
      // FYA Notices
       const todayIssueDate = await this.getNoticeAndOrdersIssueDates(
        organizationId,
        'today',
        'date_of_issuance',
        query,
        userId,
        ViewAll,
        ViewAssigned
      );
      const last1WeekIssueDate = await this.getNoticeAndOrdersIssueDates(
        organizationId,
        '1week',
        'date_of_issuance',
        query,
        userId,
        ViewAll,
        ViewAssigned
      );
      const last15DaysIssueDate = await this.getNoticeAndOrdersIssueDates(
        organizationId,
        '15days',
        'date_of_issuance',
        query,
        userId,
        ViewAll,
        ViewAssigned
      );
      const last1MonthIssueDate = await this.getNoticeAndOrdersIssueDates(
        organizationId,
        '1month',
        'date_of_issuance',
        query,
        userId,
        ViewAll,
        ViewAssigned
      );

      // FYI Notices
        const todayDueDate = await this.getNoticeAndOrderDueDates(
        organizationId,
        'today',
        'due_date',
        query,
        userId,
        ViewAll,
        ViewAssigned
      );

      const nxt1WeekDueDate = await this.getNoticeAndOrderDueDates(
        organizationId,
        '1week',
        'due_date',
        query,
        userId,
        ViewAll,
        ViewAssigned
      );
      const nxt15DaysDueDate = await this.getNoticeAndOrderDueDates(
        organizationId,
        '15days',
        'due_date',
        query,
        userId,
        ViewAll,
        ViewAssigned
      );
      const nxt1MonthDueDate = await this.getNoticeAndOrderDueDates(
        organizationId,
        '1month',
        'due_date',
        query,
        userId,
        ViewAll,
        ViewAssigned
      );

      return {
        issuanceDate: {
          last1Week: last1WeekIssueDate,
          last15Days: last15DaysIssueDate,
          last1Month: last1MonthIssueDate,
          today: todayIssueDate
        },
        dueDate: {
          nxt1Week: nxt1WeekDueDate,
          nxt15Days: nxt15DaysDueDate,
          nxt1Month: nxt1MonthDueDate,
          today:todayDueDate
        }
      };
    } else {
      return {
        issuanceDate: {
          last1Week: 0,
          last15Days: 0,
          last1Month: 0,
          today: 0
        },
        dueDate: {
          nxt1Week: 0,
          nxt15Days: 0,
          nxt1Month: 0,
          today:0
        },
      };
    }
  }

  async getAdditionalNoticeOrdersDateCount(userId: number, query: any) {
    const user = await User.findOne({ where: { id: userId }, relations: ['organization', 'role'] });
    let ViewAll = user.role.permissions.find(
      (permission) => permission.slug === Permissions.VIEW_ALL_CLIENT_MANAGERS,
    );
    let ViewAssigned = user.role.permissions.find(
      (permission) => permission.slug === Permissions.VIEW_ASSIGNED_CLIENT_MANAGERS,
    );
    const organizationId = user?.organization?.id;
    if (organizationId) {

        const todayIssueDate = await this.getAddNoticeAndOrderIssueDates(
        organizationId,
        'today',
        'category_date',
        query,
        userId,
        ViewAll,
        ViewAssigned
      );

      const last1WeekIssueDate = await this.getAddNoticeAndOrderIssueDates(
        organizationId,
        '1week',
        'category_date',
        query,
        userId,
        ViewAll,
        ViewAssigned
      );
      const last15DaysIssueDate = await this.getAddNoticeAndOrderIssueDates(
        organizationId,
        '15days',
        'category_date',
        query,
        userId,
        ViewAll,
        ViewAssigned
      );
      const last1MonthIssueDate = await this.getAddNoticeAndOrderIssueDates(
        organizationId,
        '1month',
        'category_date',
        query,
        userId,
        ViewAll,
        ViewAssigned
      );

        const todayDueDate = await this.getAddNoticeAndOrderDueDates(
        organizationId,
        'today',
        'due_date',
        query,
        userId,
        ViewAll,
        ViewAssigned
      );
      const nxt1WeekDueDate = await this.getAddNoticeAndOrderDueDates(
        organizationId,
        '1week',
        'due_date',
        query,
        userId,
        ViewAll,
        ViewAssigned
      );
      const nxt15DaysDueDate = await this.getAddNoticeAndOrderDueDates(
        organizationId,
        '15days',
        'due_date',
        query,
        userId,
        ViewAll,
        ViewAssigned
      );
      const nxt1MonthDueDate = await this.getAddNoticeAndOrderDueDates(
        organizationId,
        '1month',
        'due_date',
        query,
        userId,
        ViewAll,
        ViewAssigned
      );

      return {
        issuanceDate: {
          last1Week: last1WeekIssueDate,
          last15Days: last15DaysIssueDate,
          last1Month: last1MonthIssueDate,
          today: todayIssueDate
        },
        dueDate: {
          nxt1WeekDue: nxt1WeekDueDate,
          nxt15DaysDue: nxt15DaysDueDate,
          nxt1MonthDue: nxt1MonthDueDate,
          today: todayDueDate
        },
      };
    } else {
      return {
        issuanceDate: {
          last1Week: 0,
          last15Days: 0,
          last1Month: 0,
          today: 0
        },
        dueDate: {
          nxt1WeekDue: 0,
          nxt15DaysDue: 0,
          nxt1MonthDue: 0,
          today: 0
        },
      };
    }
  }

    async getAddNoticeAndOrdersStats(
    organizationId: number,
    status: 'open' | 'closed' | 'replied' | 'notReplied',
    query: any,
    userId: any,
    ViewAll: any,
    ViewAssigned: any
  ): Promise<number> {
    try {
      if (!organizationId) {
        throw new Error('Organization not found for the user.');
      }

      let intervalQuery = '';
      switch (status) {
        case 'open':
          intervalQuery = 'gstr_additional_notice_orders.case_status LIKE "OPEN"';
          break;
        case 'closed':
          intervalQuery = 'gstr_additional_notice_orders.case_status LIKE "CLOSED"';
          break;
        case 'replied':
          intervalQuery = 'gstr_additional_notice_orders.ref_status LIKE "REPLIED"';
          break;
        case 'notReplied':
          intervalQuery = 'gstr_additional_notice_orders.ref_status LIKE "NOT_REPLIED"';
          break;
        default:
          throw new Error('Invalid interval');
      }

      // let sql = `
      //   SELECT COUNT(*) AS count
      //   FROM gstr_additional_notice_orders
      //   WHERE ${dateColumn} >= DATE_SUB(CURDATE(), ${intervalQuery})
      //   AND organization_id = ${organizationId}
      // `;

      let sql = `
        SELECT COUNT(DISTINCT(gstr_additional_notice_orders.id)) AS count
        FROM gstr_additional_notice_orders
        LEFT JOIN client ON gstr_additional_notice_orders.client_id = client.id
        LEFT JOIN gstr_credentials ON gstr_credentials.client_id = client.id
        WHERE ${intervalQuery} AND gstr_additional_notice_orders.case_folder_type_name NOT IN ('REPLIES','APPLICATIONS')
        AND gstr_additional_notice_orders.organization_id = ${organizationId}
        AND  client.status != '${UserStatus.DELETED}'
        AND (gstr_credentials.status != '${GstrStatus.DISABLE}' OR gstr_credentials.status IS NULL)
      `;

      if (ViewAssigned && !ViewAll) {
        sql += `
        AND EXISTS (
          SELECT 1
          FROM client_client_managers_user cm
          WHERE cm.client_id = client.id
          AND cm.user_id = ${userId}
        )
      `;
      }

      if (query?.assessmentYear && query?.assessmentYear !== '') {
        sql += ` AND fy = '${query.assessmentYear}'`;
      }

      const result = await getManager().query(sql);
      return parseInt(result[0]?.count);
    } catch (error) {
      console.error(`Error fetching ${status} status:`, error);
      throw error;
    }
  }

  async getAdditionalNoticesStatCount(userId:number,query:any){
        const user = await User.findOne({ where: { id: userId }, relations: ['organization', 'role'] });
    let ViewAll = user.role.permissions.find(
      (permission) => permission.slug === Permissions.VIEW_ALL_CLIENT_MANAGERS,
    );
    let ViewAssigned = user.role.permissions.find(
      (permission) => permission.slug === Permissions.VIEW_ASSIGNED_CLIENT_MANAGERS,
    );
    const organizationId = user?.organization?.id;
    if (organizationId) {

        const openCount = await this.getAddNoticeAndOrdersStats(
        organizationId,
        'open',
        query,
        userId,
        ViewAll,
        ViewAssigned
      );

      const closedCount = await this.getAddNoticeAndOrdersStats(
        organizationId,
        'closed',
        query,
        userId,
        ViewAll,
        ViewAssigned
      );

      const repliedCount = await this.getAddNoticeAndOrdersStats(
        organizationId,
        'replied',
        query,
        userId,
        ViewAll,
        ViewAssigned
      );

      const notRepliedCount = await this.getAddNoticeAndOrdersStats(
        organizationId,
        'notReplied',
        query,
        userId,
        ViewAll,
        ViewAssigned
      );


      return {
        stats: {
          open: openCount,
          closed: closedCount,
          replied: repliedCount,
          notReplied: notRepliedCount
        }
      };
    } else {
      return {
        stats: {
          open: 0,
          closed: 0,
          replied: 0,
          notReplied: 0
        }
      };
    }
  }

  async getGstrConfigStatus(userId: any, query: Date) {
    let user = await User.findOne(userId, { relations: ['organization'] });
    const organizationPref: any = await OrganizationPreferences.findOne({
      where: { organization: user?.organization },
    });
    if (organizationPref) {
      const organizationLimit = organizationPref?.automationConfig?.gstrLimit || 300;
      let clientCredentials = createQueryBuilder(GstrCredentials, 'gstrCredentials')
        .leftJoinAndSelect('gstrCredentials.client', 'client')
        .where('gstrCredentials.organizationId = :id', { id: user?.organization?.id })
        .andWhere('client.status != :status', { status: UserStatus.DELETED })
        .andWhere(
          new Brackets((qb) => {
            qb.where('gstrCredentials.status IS NULL').orWhere(
              'gstrCredentials.status = :enabledStatus',
              { enabledStatus: GstrStatus.ENABLE },
            );
          }),
        );
      let result = (await clientCredentials.getCount()) || 0;

      const abc = {
        totalLimit: organizationLimit,
        difference: organizationLimit - result,
        presentClients: result,
      };
      return abc;
    }
  }

  async clientCheck(userId: number, queryy: any) {
    const { search, limit, offset } = queryy;
    const user = await User.findOne({
      where: { id: userId },
      relations: ['organization','role'],
    });
    let ViewAll = user.role.permissions.find(
      (permission) => permission.slug === Permissions.VIEW_ALL_CLIENT_MANAGERS,
    );
    let ViewAssigned = user.role.permissions.find(
      (permission) => permission.slug === Permissions.VIEW_ASSIGNED_CLIENT_MANAGERS,
    );
    const entityManager = getRepository(GstrCredentials);

    const query = await entityManager
      .createQueryBuilder('gstrCredentials')
      .leftJoinAndSelect('gstrCredentials.client', 'client')
      .leftJoin('client.clientManagers', 'clientManagers')
      .where('gstrCredentials.organizationId = :id', { id: user.organization.id })
      .andWhere('client.status != :status', { status: UserStatus.DELETED })
      .andWhere('gstrCredentials.status = :inStatus', { inStatus: IncomeTaxStatus.ENABLE })
      .andWhere('gstrCredentials.remarks IN (:...remarks)', {
        remarks: [
          'Invalid Username or Password. Please try again.',
          'Your GST Login Password is Expired. Please change it with New Password!',
          'You have entered a wrong password for 3 consecutive times. Your account has been locked. Kindly access ‘Forgot Password’ link to reset your password.',
        ],
      })
     

    if (search) {
      query.andWhere(
        new Brackets((qb) => {
          qb.where('gstrCredentials.userName LIKE :search', {
            search: `%${search}%`,
          });
          qb.orWhere('client.displayName LIKE :namesearch', {
            namesearch: `%${search}%`,
          });
        }),
      );
    }

    if (ViewAssigned && !ViewAll) {
      query.andWhere('clientManagers.id = :userId', { userId });
    }

    if (offset) {
      query.skip(offset);
    }

    if (limit) {
      query.take(limit);
    }

    let [queryResult, totalCount] = await query.getManyAndCount();

    // const totalClients = await AutClientCredentials.count({
    //   where: { organizationId: user.organization.id },
    // });

    const totalClients = await createQueryBuilder(GstrCredentials, 'credentials')
      .leftJoin('credentials.client', 'client')
      .where('credentials.organizationId = :organizationId', {
        organizationId: user?.organization?.id,
      })
      .andWhere('client.status != :status', { status: UserStatus.DELETED })
      .andWhere('credentials.status = :inStatus', { inStatus: IncomeTaxStatus.ENABLE })
      .getCount();

    // const uniquePansCount = await Client.count({ where: { organization: user.organization } });

    // const client = await createQueryBuilder(Client, 'client')
    //   .select('COUNT(DISTINCT client.gst_number)', 'count')
    //   .where('client.organization_id = :orgId', { orgId: user.organization?.id })
    //   .andWhere('client.gst_number IS NOT NULL')
    //   .getRawOne();

    // const count = parseInt(client.count);
    const result = {
      queryResult,
      totalClients,
      count: queryResult.length,
      totalCount,
      // uniquePansCount: count,
    };
    return result;
  }



  async exportGstrInvalid(userId: number, query: any) {
    const newQuery = { ...query, offset: 0, limit: ********* };
    let invalidRows = await this.clientCheck(userId, newQuery);
    if (!invalidRows.queryResult.length) throw new BadRequestException('No Data for Export');
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('GST Invalid Credentials');
    const headers =  [
      { header: 'S.No', key: 'serialNo' },
      { header: 'Category', key: 'category' },
      { header: 'Client Name', key: 'clientName' },
      { header: 'Client ID', key: 'clientId' },
      { header: 'User Name', key: 'userName' },
      {header:'Remarks',key:'remarks'}
    

       
      ]
      
    worksheet.columns = headers;
    const columnMaxLengths = Array(headers.length).fill(0);
    let serialCounter = 1; // Initialize a counter
    invalidRows.queryResult.forEach((invalidRow) => {
      const formatDateTime1 = (dateString: any) => {
        if (!dateString) return '-'; // Handle cases where date is missing
        const date = new Date(dateString);
        return date.toLocaleString(); // Format to local date and time
      };
      
      const rowData =  {
        serialNo:serialCounter++,// Assign and then increment the counter
        category: invalidRow?.client?.category,
        clientName: invalidRow?.client?.displayName,
        clientId: invalidRow?.client?.clientId,
        userName: invalidRow?.userName,
        remarks: invalidRow?.remarks,
        
        }
       
  
      const row = worksheet.addRow(rowData);
  
  
      worksheet.columns.forEach((column, colIndex) => {
        const headerLength = column.header?.length || 0;
        const cellLength = rowData[column.key]?.toString().length || 0;
        columnMaxLengths[colIndex] = Math.max(columnMaxLengths[colIndex] || 0, headerLength, cellLength);
      });
    });
  
    worksheet.columns.forEach((column, colIndex) => {
      column.width = columnMaxLengths[colIndex] + 3; 
    });
   
  
    const headerRow = worksheet.getRow(1);
    headerRow.font = { bold: true };
    headerRow.eachCell((cell) => {
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '64B5F6' },
      };
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' },
      };
      cell.alignment = { horizontal: 'center', vertical: 'middle' };
    });
  
    worksheet.columns.forEach((column) => {
      if (column.key === 'clientName') {
        column.width = 50;
        column.alignment = { wrapText: true, horizontal: 'center', vertical: 'middle' };
      } else {
        column.alignment = { horizontal: 'center', vertical: 'middle' };
      }
    });
  
  
    worksheet.eachRow((row) => {
      row.eachCell((cell) => {
        cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
      });
    });
  
    worksheet.views = [{ state: 'frozen', ySplit: 1 }];
  
    const buffer = await workbook.xlsx.writeBuffer();
    return buffer;
  }

  async outstandingDemandStats(userId:number,query:any){
    const user = await User.findOne({where:{id:userId}});
    const demandStats = createQueryBuilder(GstrOutstandingDemand,'gstrOutstandingDemand')
    .leftJoinAndSelect('gstrOutstandingDemand.gstrCredentials','gstrCredentials')
    .leftJoinAndSelect('gstrCredentials.client','client')
    .select([
      'gstrOutstandingDemand.id',
      'client.id',
      'client.displayName',
      'gstrCredentials.id',
      'gstrOutstandingDemand.gstIn',
      'gstrOutstandingDemand.demandDt',
      'gstrOutstandingDemand.totalTot',
    ])
    .where('gstrOutstandingDemand.organizationId = :orgId',{orgId:user?.organization?.id})
    .andWhere('client.status != :status', { status: UserStatus.DELETED })
    .andWhere(
        new Brackets((qb) => {
          qb.where('gstrCredentials.status != :disStatus', {
            disStatus: GstrStatus.DISABLE,
          }).orWhere('gstrCredentials.status IS NULL');
        }),
      ).orderBy('gstrOutstandingDemand.demandDt', 'DESC')
    .take(5);

      const result = await demandStats.getMany();
      return {result}
  }

  async getAdditionalNoticeStats(userId:number, query:any){
    try{

    const user = await User.findOne({ where: { id: userId } });
  const orgId = user?.organization?.id;
  if (!orgId) {
    return null;
  }

  const uniqueCaseTypeNames = await createQueryBuilder(GstrAdditionalNoticeOrders,'n')
    .leftJoinAndSelect('n.client','client')
    .leftJoinAndSelect('client.gstrCredentials','gstrCredentials')
    .select('DISTINCT n.caseTypeName', 'caseTypeName')
    .where('n.organizationId = :orgId', { orgId })
    .andWhere('n.caseFolderTypeName IN  (:...caseNames)',{caseNames:['NOTICES','ORDERS']})
    .andWhere('client.status != :status', { status: UserStatus.DELETED })
    .andWhere(
        new Brackets((qb) => {
          qb.where('gstrCredentials.status != :disStatus', {
            disStatus: GstrStatus.DISABLE,
          }).orWhere('gstrCredentials.status IS NULL');
        }))
    .andWhere('(n.createdType != :createdType OR n.createdType IS NULL)', {
    createdType: CreatedType.MANUAL,
      }) 
    .getRawMany();


  const noticeRows: Array<{
    caseTypeName: string | null;
    caseStatus: string | null;
    refStatus: string | null;
    cnt: string;
  }> = await createQueryBuilder(GstrAdditionalNoticeOrders, 'n')
    .leftJoin('n.client', 'client')
    .leftJoin('client.gstrCredentials', 'gc')
    .select([
      'n.caseTypeName AS caseTypeName',
      'n.caseStatus AS caseStatus',
      'n.refStatus AS refStatus',
      'COUNT(DISTINCT n.refNum) AS cnt',
    ])
    .where('n.organizationId = :orgId', { orgId })
    .andWhere('client.status != :status', { status: UserStatus.DELETED })
    .andWhere(
      new Brackets((qb) => {
        qb.where('gc.status != :disStatus', { disStatus: GstrStatus.DISABLE }).orWhere('gc.status IS NULL');
      }),
    )
    .andWhere('n.caseFolderTypeName = :folder', { folder: 'NOTICES' })
    .andWhere('n.refNum IS NOT NULL') 
.andWhere('(n.createdType != :createdType OR n.createdType IS NULL)', {
  createdType: CreatedType.MANUAL,
})    .groupBy('n.caseTypeName, n.caseStatus, n.refStatus')
    .getRawMany();



  const orderRows: Array<{
    caseTypeName: string | null;
    cnt: string;
  }> = await createQueryBuilder(GstrAdditionalNoticeOrders, 'n')
    .leftJoin('n.client', 'client')
    .leftJoin('client.gstrCredentials', 'gc')
    .select([
      'n.caseTypeName AS caseTypeName',
      'COUNT(DISTINCT n.refNum) AS cnt',
    ])
    .where('n.organizationId = :orgId', { orgId })
    .andWhere('client.status != :status', { status: UserStatus.DELETED })
    .andWhere(
      new Brackets((qb) => {
        qb.where('gc.status != :disStatus', { disStatus: GstrStatus.DISABLE }).orWhere('gc.status IS NULL');
      }),
    )
    .andWhere('n.caseFolderTypeName = :folder', { folder: 'ORDERS' })
    .andWhere('n.refNum IS NOT NULL') 
.andWhere('(n.createdType != :createdType OR n.createdType IS NULL)', {
  createdType: CreatedType.MANUAL,
})    .groupBy('n.caseTypeName')
    .getRawMany();
    

  const noticesByCaseType: Record<string, {
    openReplied: number;
    openNotReplied: number;
    closedReplied: number;
    closedNotReplied: number;
    totalNotices: number;
    totalOrders: number;
  }> = {};

  const uniqueKeys = new Set<string>();
  for (const u of uniqueCaseTypeNames) {
    const raw = u.caseTypeName;
    if (raw === null || raw === undefined) continue; 
    const key = String(raw).trim();
    if (!key) continue;
    uniqueKeys.add(key);
    noticesByCaseType[key] = {
      openReplied: 0,
      openNotReplied: 0,
      closedReplied: 0,
      closedNotReplied: 0,
      totalNotices: 0,
      totalOrders: 0,
    };
  }

  let totalNotices = 0;
  for (const r of noticeRows) {
    const rawCaseType = r.caseTypeName;
    if (rawCaseType === null || rawCaseType === undefined) continue;
    const caseType = String(rawCaseType).trim();
    if (!caseType) continue;

    if (!uniqueKeys.has(caseType)) continue;

    const caseStatus = (r.caseStatus ?? '').toString().trim().toUpperCase();
    const refStatus = (r.refStatus ?? '').toString().trim().toUpperCase();
    const cnt = Number(r.cnt ?? 0);

    const isReplied = refStatus === 'REPLIED';
    const isNotReplied = refStatus === 'NOT_REPLIED';
    const isOpen = caseStatus === 'OPEN';
    const isClosed = caseStatus === 'CLOSED';

    if (isClosed) {
      if (isReplied) noticesByCaseType[caseType].closedReplied += cnt;
      else if (isNotReplied) noticesByCaseType[caseType].closedNotReplied += cnt;
    } else if (isOpen) {
      if (isReplied) noticesByCaseType[caseType].openReplied += cnt;
      else if (isNotReplied) noticesByCaseType[caseType].openNotReplied += cnt;
    }

    noticesByCaseType[caseType].totalNotices += cnt;
    totalNotices += cnt;
  }

  let totalOrders = 0;
  for (const r of orderRows) {
    const rawCaseType = r.caseTypeName;
    if (rawCaseType === null || rawCaseType === undefined) continue;
    const caseType = String(rawCaseType).trim();
    if (!caseType) continue;

    if (!uniqueKeys.has(caseType)) continue;

    const cnt = Number(r.cnt ?? 0);
    if (!noticesByCaseType[caseType]) {
      noticesByCaseType[caseType] = {
        openReplied: 0,
        openNotReplied: 0,
        closedReplied: 0,
        closedNotReplied: 0,
        totalNotices: 0,
        totalOrders: 0,
      };
    }

    noticesByCaseType[caseType].totalOrders = cnt;
    totalOrders += cnt;
  }

  return {
    noticesByCaseType,
    totals: { totalNotices, totalOrders },
  };
    }catch(e){
      console.log('Error while fetching additional notice and orders stats',e)
    }
  }
}
