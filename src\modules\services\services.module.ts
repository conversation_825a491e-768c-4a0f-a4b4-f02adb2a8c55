import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ServiceListener } from 'src/event-listeners/service.listener';
import ChecklistItem from './entities/checklist-item.entity';
import { Checklist } from './entities/checklist.entity';
import Milestone from './entities/milestone.entity';
import { Service } from './entities/service.entity';
import StageOfWork from './entities/stage-of-work.entity';
import { SubTask } from './entities/subtask.entity';
import { ServicesController } from './services.controller';
import { ServicesService } from './services.service';
import { ServiceSubscriber } from 'src/event-subscribers/service.subscriber';
import ServicePreferences from './entities/service-preferences.entity';
import { StorageService } from '../storage/storage.service';
import { AwsService } from '../storage/upload.service';
import { OneDriveStorageService } from '../ondrive-storage/onedrive-storage.service';
import { AttachmentsService } from '../tasks/services/attachments.service';
import { BharathCloudService } from '../storage/bharath-upload.service';
import { BharathStorageService } from '../storage/bharath-storage.service';
import { GoogleDriveStorageService } from '../ondrive-storage/googledrive-storage.service';
import { ServiceFavorite } from './entities/service_favorite.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      Service,
      Checklist,
      ChecklistItem,
      Milestone,
      StageOfWork,
      SubTask,
      ServicePreferences,
      ServiceFavorite,
    ]),
  ],
  controllers: [ServicesController],
  providers: [
    ServicesService,
    ServiceListener,
    ServiceSubscriber,
    StorageService,
    AwsService,
    BharathStorageService,
    BharathCloudService,
    OneDriveStorageService,
    AttachmentsService,
    GoogleDriveStorageService,
  ],
})
export class ServicesModule {}
