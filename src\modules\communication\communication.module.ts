import { <PERSON>du<PERSON> } from '@nestjs/common';
import { CommunicationController } from './communication.controller';
import { CommunicationService } from './communication.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import ClientGroupBroadcast from './entity/client-group-broadcast.entity';
import BroadcastEmailTemplates from './entity/broadcast-email-templates-entity';
import BroadcastActivity from './entity/broadcast-activity.entity';
import { AwsService } from '../storage/upload.service';
import BroadcastActivityDetails from './entity/broadcast-activity-details.entity';

@Module({
  imports: [TypeOrmModule.forFeature([ClientGroupBroadcast,BroadcastEmailTemplates,BroadcastActivity,BroadcastActivityDetails])],
  controllers: [CommunicationController],
  providers: [CommunicationService,AwsService],
})
export class CommunicationModule {}
