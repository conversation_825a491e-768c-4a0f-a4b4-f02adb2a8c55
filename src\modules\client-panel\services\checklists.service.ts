import { Injectable } from '@nestjs/common';
import { createQueryBuilder } from 'typeorm';
import {
  AddChecklistDto,
  AddChecklistItems,
  UpdateChecklist,
  UpdateChecklistItem,
} from 'src/modules/tasks/dto/add-checklist.dto';
import ChecklistItem from 'src/modules/tasks/entity/checklist-item.entity';
import Checklist from 'src/modules/tasks/entity/checklist.entity';
import Task from 'src/modules/tasks/entity/task.entity';

@Injectable()
export class ChecklistsService {
  async findChecklists(id: number) {
    let checklists = await createQueryBuilder(Checklist, 'checklist')
      .leftJoin('checklist.task', 'task')
      .leftJoinAndSelect('checklist.checklistItems', 'checklistItems')
      .where('task.id = :id', { id })
      .getMany();
    return checklists;
  }
}
