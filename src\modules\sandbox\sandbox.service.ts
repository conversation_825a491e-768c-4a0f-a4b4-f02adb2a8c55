import { Injectable, InternalServerErrorException } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import axios from 'axios';


@Injectable()
export class SandboxService {
  public eventEmitter: EventEmitter2;
  public constructor(eventEmitter: EventEmitter2) {
    this.eventEmitter = eventEmitter;
  }


  async getSandboxToken() {
    return axios({
      url: process.env.REACT_APP_SANDBOX_AUTH_URL || "",
      method: "GET",
      headers: {
        Accept: "application/json",
        "x-api-key": process.env.REACT_APP_SANDBOX_API_KEY || "",
        "x-api-secret": process.env.REACT_APP_SANDBOX_API_SECRET || "",
        "x-api-version": "1.0",
      },
    });
  };


  async getGstDetails({ token, gstNumber }) {
    try {
      const headers = {
        accept: "application/json",
        Authorization: token,
        "x-api-key": "key_live_tckndBNOkHnwcBGKuWzvBPh2S5odGTVV",
        "x-api-version": "1.0",
      };

      const url = `https://api.sandbox.co.in/gsp/public/gstin/${gstNumber}`;

      const response = await axios.get(url, { headers });

      // console.log(response.data);
      return response.data;
    } catch (error) {
      console.error(error);
    }
  };

  async fynGstDetails({ gstNumber }) {
    try {
      const headers = {
        Authorization: process.env.FYN_AUTH,
      };
      const url = `${process.env.FYN_URL}${gstNumber}`;
      const response = await axios.get(url, { headers });
      const res = { data: response.data }
      if (response?.data.errorMessage === "Action is invalid.") {
        throw new InternalServerErrorException("Error while verifying GSTIN");
      }
      return res;
    } catch (err) {
      console.log(err);
      throw new InternalServerErrorException("Error while verifying GSTIN")
    }
  }

  async getPanDetails({ token, panNumber }) {
    try {
      const headers = {
        accept: "application/json",
        Authorization: token,
        "x-api-key": "key_live_tckndBNOkHnwcBGKuWzvBPh2S5odGTVV",
        "x-api-version": "1.0",
        "content-type": "application/json",
      };

      const data = { pan: panNumber, consent: "y", reason: "For%2520KYC%2520of%2520User" };

      const panData = await axios.post(
        "https://api.sandbox.co.in/kyc/pan",
        data,
        {
          headers: headers,
        }
      );

      // console.log(panData.data);
      return panData.data.data;
    } catch (error) {
      console.error(error);
    }

  }
}