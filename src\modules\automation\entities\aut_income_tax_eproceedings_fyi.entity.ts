import { Optional } from '@nestjs/common';
import Client from 'src/modules/clients/entity/client.entity';
import { Organization } from 'src/modules/organization/entities/organization.entity';
import {
  BaseEntity,
  Column,
  CreateDateColumn,
  Entity,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import AutFyiNotice from './aut_income_tax_eproceedings_fyi_notice.entity';
import { StorageSystem } from 'src/modules/storage/storage.entity';
import { EproceedingTypeEnum } from './aut_income_tax_eproceedings_fya.entity';

@Entity()
class AutEProceedingFyi extends BaseEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  pan: string;

  @Column()
  proceedingReqId: string;

  @Column()
  proceedingName: string;

  @Column()
  nameOfAssesse: string;

  @Column()
  assessmentYear: string;

  @Column()
  financialYear: string;

  @Column()
  proceedingStatus: string;

  @Column()
  proceedingType: string;

  @Column()
  noticeName: string;

  @Column()
  itrType: string;

  @Column()
  responseDueDate: string;

  @Column()
  responseDate: string;

  @Column()
  proceedingClosureOrder: string;

  @Column()
  proceedingClosureDate: string;

  @Column()
  lastResponseSubmittedOn: string;

  @Column()
  isFileAppeal: Boolean;

  @Column('json')
  proceedingStatusDetails: object;

  @Column('json')
  closureOrderAttatchments: object;

  @Column()
  acknowledgementNo: string;

  @ManyToOne(() => Client, (client) => client.auteproceedingfyi, { onDelete: 'SET NULL' })
  client: Client;

  @OneToMany(() => AutFyiNotice, (notices) => notices.eProceeding)
  notices: AutFyiNotice[];

  @Column()
  organizationId: number;

  @Column({ type: 'enum', enum: StorageSystem, default: StorageSystem.AMAZON })
  storageSystem: StorageSystem;

  @CreateDateColumn()
  createdAt: string;

  @UpdateDateColumn()
  updatedAt: string;

  @Column({ default: EproceedingTypeEnum.SELF, type: 'enum', enum: EproceedingTypeEnum })
  type: EproceedingTypeEnum;
}

export default AutEProceedingFyi;
