import Client from "src/modules/clients/entity/client.entity";
import { BaseEntity, Column, CreateDateColumn, Entity, ManyToOne, PrimaryGeneratedColumn, UpdateDateColumn } from "typeorm";
import TanClientCredentials from "./tan-client-credentials.entity";

@Entity()
class TanProfile extends BaseEntity{
@PrimaryGeneratedColumn()
id:number;

@Column()
tanNumber:string;

@Column()
name:string;

@Column()
dateOfAllotment:string;

@Column()
primaryEmail:string;

@Column()
secondaryEmail:string;

@Column()
primaryNumber:string;

@Column()
secondaryNumber:string;

@Column()
address:string;

@Column()
commincationAddressedTo:string;

@Column()
panAvailability:string;

@Column()
orgPan:string;

@Column()
panStatus:string;

@Column()
nameAsPerPan:string;

@Column()
clientId:number;


// @ManyToOne(()=> Client, (client) => client.tanProfile ,{ onDelete: 'SET NULL' })
// client:Client;

@ManyToOne(
    () => TanClientCredentials,
    (tanClientCredentials) => tanClientCredentials.tanProfile,
    { onDelete: 'SET NULL' },
  )
  tanClientCredentials: TanClientCredentials;

@Column()
organizationId:number

@CreateDateColumn()
createdAt:string

@UpdateDateColumn()
updatedAt:string;
}

export default TanProfile;