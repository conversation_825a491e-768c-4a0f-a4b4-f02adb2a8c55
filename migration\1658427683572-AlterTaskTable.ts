import { MigrationInterface, QueryRunner } from 'typeorm';

export class AlterTaskTable1658427683572 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
    ALTER TABLE task
    MODIFY COLUMN payment_status enum("B<PERSON>LE<PERSON>", "UNBILLED") NOT NULL DEFAULT "UNBILLED"
`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}
