import { Organization } from 'src/modules/organization/entities/organization.entity';
import { User } from 'src/modules/users/entities/user.entity';
import {
  BaseEntity,
  Column,
  CreateDateColumn,
  Entity,
  JoinColumn,
  ManyToOne,
  OneToMany,
  OneToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import Client from 'src/modules/clients/entity/client.entity';
import Task from 'src/modules/tasks/entity/task.entity';
import ClientGroup from 'src/modules/client-group/client-group.entity';

export enum documentStatusEnum {
  IN_PROGRESS = 'in_progress',
  UNDER_REVIEW = 'under_review',
  COMPLETED = 'completed',
}

export enum priorityType {
  NONE = 'none',
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
}

export enum paymentStatusType {
  NONE = 'NONE',
  SUCCESS = 'SUCCESS',
  PENDING = 'PENDING',
}

@Entity()
class QtmActivity extends BaseEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  processInstanceId: string;

  @CreateDateColumn()
  createdAt: string;

  @UpdateDateColumn()
  lastUpdated: string;

  @Column()
  rawdata: string;

  @Column()
  templateId: number;

  @Column()
  organizationId: number;

  @Column()
  userId: number;

  @Column()
  status: boolean;

  @Column()
  templateName: string;

  @Column()
  percentage: number;

  @Column()
  clientId: number;

  @Column({ type: 'json', nullable: true })
  assignedUsers: any[];

  @Column('json', { array: true })
  aprovalProgress: object[];

  @Column()
  approvalStatus: string;

  @Column()
  documentNumber: string;

  @Column()
  documentDeleteStatus: boolean;

  @Column()
  taskId: number;

  @Column({ default: 'IN_PROGRESS', type: 'enum', enum: documentStatusEnum })
  documentStatus: documentStatusEnum;

  @Column({ default: 'none', type: 'enum', enum: priorityType })
  priority: priorityType;

  @Column({ default: 'NONE', type: 'enum', enum: paymentStatusType })
  paymentStatus: paymentStatusType;

  //   @ManyToOne(() => ApprovalProcedures, (approvalProcedures) => approvalProcedures.qtmActivity)
  //   approval: ApprovalProcedures;

  //   @ManyToOne(() => QtmTemplate, (qtmTemplate) => qtmTemplate.qtmActivity)
  //   template: QtmTemplate;

  @ManyToOne(() => Client, (client) => client.qtmActivitys)
  client: Client;

  @ManyToOne(() => ClientGroup, (clientGroup) => clientGroup.qtmActivitys)
  clientGroup: ClientGroup;

  //   @ManyToOne(() => Organization, (organization) => organization.qtmActivitys)
  //   organization: Organization;

  //   @OneToMany(() => QtmContractDate, (qtmContractDate) => qtmContractDate.qtmActivity)
  //   qtmContractDate: QtmContractDate[];

  @ManyToOne(() => User, (user) => user.qtmActivitee)
  user: User;

  @ManyToOne(() => Task, (task) => task.qtmActivity)
  task: Task;
}

export default QtmActivity;
