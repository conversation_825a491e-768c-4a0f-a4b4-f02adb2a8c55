import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, IsNotEmpty, IsOptional } from 'class-validator';

export class CreateBillingEntityDto {
  @IsOptional()
  legalName: string;

  @IsOptional()
  tradeName: string;

  @IsNotEmpty()
  category: string;

  @IsOptional()
  panNumber: string;

  @IsOptional()
  gstNumber: string;

  @IsOptional()
  showDiscount: string;

  @IsOptional()
  floorNumber: string;

  @IsOptional()
  buildingNumber: string;

  @IsOptional()
  buildingName: string;

  @IsOptional()
  street: string;

  @IsOptional()
  city: string;

  @IsOptional()
  district: string;

  @IsOptional()
  state: string;

  @IsOptional()
  pincode: string;

  @IsNotEmpty()
  registrationDate: string;

  @IsOptional()
  location: string;

  @IsOptional()
  gstStatus: string;

  @IsOptional()
  @IsArray()
  terms: Array<string>;

  @IsOptional()
  hasGst: boolean;

  @IsOptional()
  gstVerified: boolean;

  @IsOptional()
  panVerified: boolean;

}