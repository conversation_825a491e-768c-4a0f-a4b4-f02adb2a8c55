import {
  Body,
  Controller,
  Get,
  Param,
  ParseIntPipe,
  Post,
  Put,
  Query,
  Req,
  Request,
  UseGuards,
} from '@nestjs/common';

import { JwtAuthGuard } from 'src/modules/users/jwt/jwt-auth.guard';
import { GstrClientService } from '../service/gstr-client.service';

@Controller('gstr-client')
export class GstrClientController {
  constructor(private service: GstrClientService) { }

  @UseGuards(JwtAuthGuard)
  @Get()
  async getGstrClients(@Request() req, @Query() query: any) {
    const { userId } = req.user;
    return this.service.getGstrClients(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Post('/gstr-client-export')
  async exportGstrClient(@Req() req: any, @Body() body: any) {
    const { userId } = req?.user;
    const query = body;
    return this.service.exportGstrClient(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Get('clients')
  async getAtomClients(@Request() req: any, @Query() query: any) {
    const { userId } = req.user;
    return this.service.getAtomClients(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Post('credentials')
  addClientAutCredentials(@Body() body: any, @Req() req: any) {
    const { userId } = req.user;
    return this.service.addGstrCredentials(userId, body);
  }

  @UseGuards(JwtAuthGuard)
  @Put('credentials/:id')
  update(@Param('id', ParseIntPipe) id: number, @Body() body, @Req() req: any) {
    const { userId } = req.user;
    return this.service.updateGstrCredentials(id, body, userId);
  }

  @UseGuards(JwtAuthGuard)
  @Post('gstr-request/:id')
  createGsrRequest(@Req() req: any, @Param('id', ParseIntPipe) id: number, @Body() body: any) {
    const { userId } = req.user;
    return this.service.createGsrRequest(userId, id, body);
  }

  @UseGuards(JwtAuthGuard)
  @Post('bulkSync')
  bulkAutomationSync(@Body() body: any, @Req() req: any) {
    const { userId } = req.user;
    return this.service.bulkGstrSync(userId, body);
  }

  @UseGuards(JwtAuthGuard)
  @Get('activity/:id')
  getActivityLog(@Req() req: any, @Param('id', ParseIntPipe) id: number, @Query() query: any) {
    const { userId } = req.user;
    return this.service.getActivityLog(id, query, userId);
  }

  @UseGuards(JwtAuthGuard)
  @Get('activityArchive/:id')
  getActivityArchiveLog(
    @Req() req: any,
    @Param('id', ParseIntPipe) id: number,
    @Query() query: any,
  ) {
    const { userId } = req.user;
    return this.service.getActivityArchiveLog(id, query, userId);
  }

  @UseGuards(JwtAuthGuard)
  @Get('syncStatus/:id')
  getclientSyncStatus(@Param('id', ParseIntPipe) id: number, @Req() req: any) {
    const { userId } = req.user;
    return this.service.getclientSyncStatus(id, userId);
  }

  @UseGuards(JwtAuthGuard)
  @Get('case-id/:id')
  async getCaseIdBasedClientNotices(
    @Param('id', ParseIntPipe) id: number,
    @Req() req: any,
    @Query() query: any,
  ) {
    const { userId } = req.user;
    return this.service.getCaseIdBasedClientNotices(id, userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Post('/export-casenotices')
  async exportCaseBasedNotices(@Req() req: any, @Body() body: any) {
    const { userId } = req?.user;
    const query = body;
    return this.service.exportCaseBasedNotices(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Get('case-ids')
  async getCaseIdBasedOrgNotices(@Req() req: any, @Query() query: any) {
    const { userId } = req.user;
    return this.service.getCaseIdBasedOrgNotices(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Post('/gstr-casebased-export')
  async exportCasebasedOrgNotices(@Req() req: any, @Body() body: any) {
    const { userId } = req?.user;
    const query = body;
    return this.service.exportCasebasedOrgNotices(userId, query);
  }
}
