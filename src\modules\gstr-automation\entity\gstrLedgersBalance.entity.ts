import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  Unique,
  BaseEntity,
} from 'typeorm';
import GstrCredentials from './gstrCredentials.entity';

export enum LedgerType {
  LIABILITY = 'LIABILITY',
  CASH = 'CASH',
  ITC = 'ITC',
  ITC_REVERSAL = 'ITC_REVERSAL',
  RCM = 'RCM',
  NEGATIVE_LIABILITY = 'NEGATIVE_LIABILITY',
}

@Entity('gstr_ledger_balance')
@Unique('uniq_gstr_ledger', ['gstrCredentials', 'gstIn', 'ledgerType'])
export class GstrLedgerBalance extends BaseEntity{
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  clientId: number;

  @Column()
  organizationId: number;

  @Column({ name: 'gst_in' })
  gstIn: string;

  @Column({
    name: 'ledger_type',
    type: 'enum',
    enum: LedgerType,
    nullable: true,
  })
  ledgerType: LedgerType;

  @Column({ type: 'decimal', precision: 15, scale: 2, nullable: true })
  igst: number;

  @Column({ type: 'decimal', precision: 15, scale: 2, nullable: true })
  cgst: number;

  @Column({ type: 'decimal', precision: 15, scale: 2, nullable: true })
  sgst: number;

  @Column({ type: 'decimal', precision: 15, scale: 2, nullable: true })
  cess: number;

  @Column({ type: 'decimal', precision: 15, scale: 2, nullable: true })
  total: number;

  @ManyToOne(() => GstrCredentials, (cred) => cred.gstrLedgersBalance)
  gstrCredentials: GstrCredentials;

  @CreateDateColumn({ name: 'created_at', type: 'datetime', precision: 6 })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at', type: 'datetime', precision: 6 })
  updatedAt: Date;
}
