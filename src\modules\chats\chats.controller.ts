import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Patch,
  Post,
  Query,
  Req,
  UseGuards,
} from '@nestjs/common';
import {
  ArrayMinSize,
  IsArray,
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsOptional,
} from 'class-validator';
import { JwtAuthGuard } from 'src/modules/users/jwt/jwt-auth.guard';
import { RoomType } from './chat-room.entity';
import { ChatsSevices } from './chats.service';

export class CreateRoomDto {
  @IsNotEmpty()
  @IsArray()
  @ArrayMinSize(2)
  members: number[];

  @IsOptional()
  name: string;

  @IsOptional()
  taskId: number;
}

export class ReadMessagesDto {
  @IsNotEmpty()
  roomId: string;
}

export class QueryDto {
  @IsNotEmpty()
  @IsEnum(RoomType)
  type: RoomType;

  @IsOptional()
  taskId: number;
}

@Controller('chats')
export class ChatsController {
  constructor(private service: ChatsSevices) { }

  @UseGuards(JwtAuthGuard)
  @Get('/rooms')
  async get(@Req() req, @Query() query: QueryDto) {
    let { userId } = req.user;
    return this.service.getChatRooms(userId, query);
  }

  @Post('/rooms')
  async create(@Body() body: CreateRoomDto) {
    return this.service.createRoom(body);
  }

  @Post('/groups')
  async createGroup(@Body() body: CreateRoomDto) {
    return this.service.createGroup(body);
  }

  @Patch('/groups/:id')
  async updateGroup(@Param() param, @Body() body: any) {
    return this.service.editGroup(param.id, body);
  }

  @Patch(`/rooms/:id`)
  async updateChatRoom(@Param() params, @Body() body: CreateRoomDto) {
    return this.service.updateChatRoom(params.id, body);
  }

  @Delete(`/rooms/:id`)
  async deleteChatroom(@Param() params) {
    return this.service.deleteChatRoom(params.id);
  }

  @UseGuards(JwtAuthGuard)
  @Post('/messages/read')
  async readMessages(@Req() req, @Body() { roomId }: ReadMessagesDto) {
    let { userId } = req.user;
    return this.service.readMessages(roomId, userId);
  }
}
