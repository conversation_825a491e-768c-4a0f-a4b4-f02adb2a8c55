<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Montserrat:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap"
      rel="stylesheet"
    />
    <title>Summary Report</title>
    <style>
      body {
        font-family: Arial, sans-serif;
      }
      table {
        width: 50%;
        border-collapse: collapse;
        margin: 20px auto;
      }

      th {
        color: whitesmoke;
        background-color: rgba(32, 94, 180, 1);
        text-align: center;
        padding: 8px;
        font-weight: 600;
        line-height: 150.523%;
        letter-spacing: 0.665px;
        width: 20vw;
        border: 0.923px solid #cfcfcf;
        font-size: 8px;
        font-family: 'Montserrat';
      }
      td {
        color: rgba(68, 68, 68, 1);
        background-color: #ffffff;
        text-align: center;
        padding: 8px;
        font-weight: 600;
        border: 0.923px solid #cfcfcf;
        font-family: 'Montserrat';
        font-size: 8px;
      }

      caption {
        font-size: 18px;
        font-weight: bold;
        margin: 10px 0;
      }

      .container {
        max-width: 800px;
        margin: 0 auto;
        padding: 20px;
      }
      .header {
        text-align: center;
        margin-bottom: 20px;
      }
      .header h1 {
        margin: 0;
        font-size: 24px;
      }
      .summary-report {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 20px;
        margin-bottom: 50px;
      }
      .count-color {
        color: rgba(13, 71, 161, 1);
        font-family: 'Montserrat';
        font-weight: 700;
        font-size: 28px;
        line-height: 34.13px;
        align-content: center;
      }
      .card {
        text-align: center;
        background-color: #ffffff;
        padding: 20px;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      }
      .card h2 {
        margin: 0;
        font-size: 28px;
        color: #0056a8;
      }
      .card p {
        margin: 10px 0 0;
        font-size: 9px;
        color: #0056a8;
        font-weight: bold;
        align-items: center;
        line-height: 11px;
      }
      .note {
        font-family: 'Montserrat';
        font-weight: 300;
        font-style: italic;
        font-size: 16px;
        line-height: 15px;
        color: rgba(62, 62, 62, 1);
      }
      .line {
        width: 725px;
        height: 2px;
        background-color: rgba(100, 181, 246, 1); /* Use background-color for solid color */
        border: none; /* Remove default border if any */
        margin: 10px auto; /* Center it horizontally */
      }

      .title {
        font-family: 'Montserrat';
        font-weight: 700;
        font-size: 16px;
        color: rgba(78, 78, 78, 1);
        line-height: 19.5px;
      }
      .space-first-page{
        margin-bottom: 400px;
      }

      @media print {
        body {
          font-family: Arial, sans-serif;
          margin: 0;
          padding: 0;
        }
        table {
          width: 100%;
          border-collapse: collapse;
        }
        table {
          border: 1px solid black;
        }

        .page-content {
          display: block;
          break-inside: avoid;
        }
      }
    </style>
  </head>
  <body style="margin: 0; padding: 0; font-family: Mulish">
    <div>
      <tr>
        <td>
          <img
            src="https://jss-vider.s3.ap-south-1.amazonaws.com/Top-summary-report.png"
            alt="logo"
            width="100%"
            height="54px"
          />
        </td>
      </tr>
      <div style="display: flex; justify-content: space-between">
        <h2 class="title">
          Summary Report of
          <span
            style="
              font-family: Montserrat;
              font-weight: 700;
              font-size: 18px;
              line-height: 21.94px;
              color: #0056a8;
            "
            ><%= reportDate %></span
          >
        </h2>
        <img
          src="https://jss-vider.s3.ap-south-1.amazonaws.com/atom-summary-report.png"
          alt="logo"
          height="30px"
        />
      </div>
      <hr class="line" />
      <div class="container">
        <!-- Summary Section -->
        <div class="summary-report">
          <div class="card">
            <h2 class="count-color"><%= completedTaskCount %></h2>
            <p>Task Completed Today</p>
          </div>
          <div class="card">
            <h2 class="count-color"><%= clientsCreated %></h2>
            <p>Clients On-boarded Today</p>
          </div>
          <div class="card">
            <h2 class="count-color"><%= invoiceCreated %></h2>
            <p>Invoice Generated Today</p>
          </div>
          <div class="card">
            <h2 class="count-color"><%= receiptsCreated %></h2>
            <p>Receipts Generated Today</p>
          </div>
          <div class="card">
            <h2 class="count-color"><%= proformaInvoiceCreated %></h2>
            <p>Proforma Invoice Generated Today</p>
          </div>
          <div class="card">
            <h2 class="count-color"><%= expenditureToday %></h2>
            <p>Expenditure Added Today</p>
          </div>
          <div class="card">
            <h2 class="count-color"><%= tasksDueTommorowCount %></h2>
            <p>Tasks Due tomorrow</p>
          </div>
        </div>

        <!-- ATOM Pro Section -->
        <h2>ATOM Pro</h2>
        <hr class="line" />
        <div class="container">

        <div class="summary-report">
          <div class="card">
            <h2 class="count-color"><%= totalITNoticeDue %></h2>
            <p>Income Tax Notice Due</p>
          </div>
          <div class="card">
            <h2 class="count-color"><%= totalGSTDueCount %></h2>
            <p>GST Notices Due</p>
          </div>
        </div>
</div>
        <p class="note">
          *Note: You can find the Attendance and Timesheet Report at the end of the PDF
        </p>
        <div class="space-first-page"></div>
      </div>
      
      <% if (completedTasks.length> 0) { %>
      <tr>
        <td class="title">Tasks Completed Today</td>
      </tr>
      <hr class="line" />

      <tr>
        <td>
          <table style="border-spacing: 0px; border: 0.923px solid #cfcfcf; margin-bottom: 100px">
            <thead>
              <tr>
                <th>S.No</th>
                <th>Client Name</th>
                <th>Service Category</th>
                <th>Task Id</th>
                <th>Task Name</th>
                <th>Priority</th>
                <th>Status</th>
                <th>Task Due Date</th>
                <th>User Name</th>
              </tr>
            </thead>
            <tbody>
              <% for(let i=0; i < completedTasks.length; i++ ) { %> <% let incrementedValue=i + 1;
              %>
              <tr>
                <td><%= incrementedValue %></td>
                <td
                  style="
                    text-align: center;
                    padding: 5px;
                    color: #5f5f5f;
                    font-size: 11.533px;
                    font-style: normal;
                    font-weight: 600;
                    line-height: 150.523%;
                    letter-spacing: 0.692px;
                    border: 0.923px solid #cfcfcf;
                  "
                >
                  <%= completedTasks[i].clientName %>
                </td>
                <td><%= completedTasks[i].serviceCategory %></td>
                <td><%= completedTasks[i].taskId %></td>
                <td><%= completedTasks[i].taskName %></td>
                <td><%= completedTasks[i].priority %></td>
                <td><%= completedTasks[i].status %></td>
                <td><%= completedTasks[i].taskDueDate %></td>
                <td><%= completedTasks[i].userName %></td>
              </tr>
              <% } %>
            </tbody>
          </table>
        </td>
      </tr>
      <% } %> 
      
      <% if (clientsOnBoardedToday.length> 0) { %>
      <tr>
        <td>Clients On boarded Today</td>
      </tr>
      <hr class="line" />

      <tr>
        <td>
          <table style="border-spacing: 0px; border: 0.923px solid #cfcfcf; margin-bottom: 100px">
            <thead>
              <tr>
                <th>S.No</th>
                <th>Client ID / Client Group Id</th>
                <th>Client Category</th>
                <th>Client Sub Category</th>
                <th>Client Number / Client Group Number</th>
                <th>Display Name</th>
                <th>Trade Name</th>
                <th>Mobile</th>
                <th>Email</th>
                <th>Status</th>
              </tr>
            </thead>
            <tbody>
              <% for(let i=0; i < clientsOnBoardedToday.length; i++ ) { %> <% let incrementedValue=i
              + 1; %>
              <tr>
                <td><%= incrementedValue %></td>
                <td><%= clientsOnBoardedToday[i].clientId %></td>
                <td><%= clientsOnBoardedToday[i].clientCategory %></td>
                <td><%= clientsOnBoardedToday[i].clientSubCategory %></td>
                <td><%= clientsOnBoardedToday[i].clientNumber %></td>
                <td><%= clientsOnBoardedToday[i].clientName %></td>
                <td><%= clientsOnBoardedToday[i].clientTradeName %></td>
                <td><%= clientsOnBoardedToday[i].mobileNumber %></td>
                <td><%= clientsOnBoardedToday[i].email %></td>
                <td><%= clientsOnBoardedToday[i].status %></td>
              </tr>
              <% } %>
            </tbody>
          </table>
        </td>
      </tr>
      <% } %> <% if (proformaInvoicesToday.length> 0) { %>
      <tr>
        <td>Proforma Invoice Created Today</td>
      </tr>
      <hr class="line" />

      <tr>
        <td>
          <table style="border-spacing: 0px; border: 0.923px solid #cfcfcf; margin-bottom: 100px">
            <thead>
              <tr>
                <th>S.No</th>
                <th>Invoice#</th>
                <th>Invoice Date</th>
                <th>Billing Entity</th>
                <th>Client / Client Group</th>
                <th>Invoice Amount</th>

                <th>Due Date</th>
                <th>Status</th>
              </tr>
            </thead>
            <tbody>
              <% for(let i=0; i < proformaInvoicesToday.length; i++ ) { %> <% let incrementedValue=i
              + 1; %>
              <tr>
                <td><%= incrementedValue %></td>
                <td><%= proformaInvoicesToday[i].proformaInvoiceNumber %></td>
                <td><%= proformaInvoicesToday[i].proformaInvoiceDate %></td>
                <td><%= proformaInvoicesToday[i].proformaBillingEntity %></td>

                <td><%= proformaInvoicesToday[i].proformaClientName %></td>
                <td><%= proformaInvoicesToday[i].proformaInvoiceAmount %></td>
                <td><%= proformaInvoicesToday[i].proformaDueDate %></td>
                <td><%= proformaInvoicesToday[i].proformaStatus %></td>
              </tr>
              <% } %>
            </tbody>
          </table>
        </td>
      </tr>
      <% } %> <% if (invoicesToday.length> 0) { %>
      <tr>
        <td>Invoices Created Today</td>
      </tr>
      <hr class="line" />

      <tr>
        <td>
          <table style="border-spacing: 0px; border: 0.923px solid #cfcfcf; margin-bottom: 100px">
            <thead>
              <tr>
                <th>S.No</th>
                <th>Invoice#</th>
                <th>Invoice Date</th>
                <th>Billing Entity</th>
                <th>Client / Client Group</th>
                <th>Invoice Amount</th>

                <th>Due Date</th>
                <th>Status</th>
              </tr>
            </thead>
            <tbody>
              <% for(let i=0; i < invoicesToday.length; i++ ) { %> <% let incrementedValue=i + 1; %>
              <tr>
                <td><%= incrementedValue %></td>
                <td><%= invoicesToday[i].invoiceNumber %></td>
                <td><%= invoicesToday[i].invoiceDate %></td>
                <td><%= invoicesToday[i].billingEntity %></td>

                <td><%= invoicesToday[i].clientName %></td>
                <td><%= invoicesToday[i].invoiceAmount %></td>
                <td><%= invoicesToday[i].dueDate %></td>
                <td><%= invoicesToday[i].status %></td>
              </tr>
              <% } %>
            </tbody>
          </table>
        </td>
      </tr>
      <% } %> <% if (receiptsToday.length> 0) { %>
      <tr>
        <td>Receipts Created Today</td>
      </tr>
      <hr class="line" />

      <tr>
        <td>
          <table style="border-spacing: 0px; border: 0.923px solid #cfcfcf; margin-bottom: 100px">
            <thead>
              <tr>
                <th>S.No</th>
                <th>Receipt#</th>
                <th>Receipt Date</th>
                <th>Billing Entity</th>
                <th>Receipt Type</th>
                <th>Client / Client Group</th>
                <th>Receipt Mode</th>

                <th>Amount</th>
                <th>Status</th>
              </tr>
            </thead>
            <tbody>
              <% for(let i=0; i < receiptsToday.length; i++ ) { %> <% let incrementedValue=i + 1; %>
              <tr>
                <td><%= incrementedValue %></td>
                <td><%= receiptsToday[i].receiptNumber %></td>
                <td><%= receiptsToday[i].receiptDate %></td>
                <td><%= receiptsToday[i].billingEntity %></td>

                <td><%= receiptsToday[i].receiptType %></td>
                <td><%= receiptsToday[i].clientName %></td>
                <td><%= receiptsToday[i].receiptMode %></td>
                <td><%= receiptsToday[i].receiptAmount %></td>
                <td><%= receiptsToday[i].status %></td>
              </tr>
              <% } %>
            </tbody>
          </table>
        </td>
      </tr>
      <% } %> <% if (expenditureCreatedToday.length> 0) { %>
      <tr>
        <td>Expenditure Added Today</td>
      </tr>
      <hr class="line" />

      <tr>
        <td>
          <table style="border-spacing: 0px; border: 0.923px solid #cfcfcf; margin-bottom: 100px">
            <thead>
              <tr>
                <th>S.No</th>
                <th>expenditureType</th>
                <th>client /Client Group</th>

                <th>expenseType</th>
                <th>expenditureType</th>
                <th>taskId</th>
                <th>taskName</th>
                <th>expenseTitle</th>
                <th>amount</th>
                <th>User Name</th>

              </tr>
            </thead>
            <tbody>
              <% for(let i=0; i < expenditureCreatedToday.length; i++ ) { %> <% let
              incrementedValue=i + 1; %>
              <tr>
                <td><%= incrementedValue %></td>
                <td><%= expenditureCreatedToday[i].expenditureType %></td>
                <td><%= expenditureCreatedToday[i].client %></td>
                <td><%= expenditureCreatedToday[i].expenseType %></td>
                <td><%= expenditureCreatedToday[i].expenditureType %></td>
                <td><%= expenditureCreatedToday[i].taskId %></td>
                <td><%= expenditureCreatedToday[i].taskName %></td>
                <td><%= expenditureCreatedToday[i].expenseTitle %></td>

                <td><%= expenditureCreatedToday[i].amount %></td>
                <td><%= expenditureCreatedToday[i].userName %></td>


              </tr>
              <% } %>
            </tbody>
          </table>
        </td>
      </tr>
      <% } %>
      <% if (tasksDueTommorow.length> 0) { %>
        <tr>
          <td class="title">Tasks Due Tomorrow</td>
        </tr>
        <hr class="line" />
  
        <tr>
          <td>
            <table style="border-spacing: 0px; border: 0.923px solid #cfcfcf; margin-bottom: 100px">
              <thead>
                <tr>
                  <th>S.No</th>
                  <th>Client Name</th>
                  <th>Service Category</th>
                  <th>Task Id</th>
                  <th>Task Name</th>
                  <th>Priority</th>
                  <th>Status</th>
                  <th>Task Due Date</th>
                </tr>
              </thead>
              <tbody>
                <% for(let i=0; i < tasksDueTommorow.length; i++ ) { %> <% let incrementedValue=i + 1;
                %>
                <tr>
                  <td><%= incrementedValue %></td>
                  <td
                    style="
                      text-align: center;
                      padding: 5px;
                      color: #5f5f5f;
                      font-size: 11.533px;
                      font-style: normal;
                      font-weight: 600;
                      line-height: 150.523%;
                      letter-spacing: 0.692px;
                      border: 0.923px solid #cfcfcf;
                    "
                  >
                    <%= tasksDueTommorow[i].clientName %>
                  </td>
                  <td><%= tasksDueTommorow[i].serviceCategory %></td>
                  <td><%= tasksDueTommorow[i].taskId %></td>
                  <td><%= tasksDueTommorow[i].taskName %></td>
                  <td><%= tasksDueTommorow[i].priority %></td>
                  <td><%= tasksDueTommorow[i].status %></td>
                  <td><%= tasksDueTommorow[i].taskDueDate %></td>
                </tr>
                <% } %>
              </tbody>
            </table>
          </td>
        </tr>
        <% } %> 
      <% if (additionalNoticeOrdersArray.length> 0) { %>
        <tr>
          <td>ATOM Pro</td>
        </tr>
        <hr class="line" />
        <tr>
          <td>Additional Notice & Orders</td>
        </tr>
        <tr>
          <td>
            <table style="border-spacing: 0px; border: 0.923px solid #cfcfcf">
              <thead>
                <tr>
                  <th>S.No</th>
                  <th>Client Name</th>
                  <th>FY</th>
                  <th>Folder</th>
                  <th>Type</th>
                  <th>Reference ID</th>
                  <th>Date of Issuance</th>
                  <th>Due Date</th>
                </tr>
              </thead>
              <tbody>
                <% for(let i=0; i < additionalNoticeOrdersArray.length; i++ ) { %> <% let
                incrementedValue=i + 1; %>
                <tr>
                  <td><%= incrementedValue %></td>
                  <td><%= additionalNoticeOrdersArray[i].clientName %></td>
                  <td><%= additionalNoticeOrdersArray[i].financialYear %></td>
                  <td><%= additionalNoticeOrdersArray[i].folder %></td>
                  <td><%= additionalNoticeOrdersArray[i].type %></td>
                  <td><%= additionalNoticeOrdersArray[i].referenceNumber %></td>
                  <td><%= additionalNoticeOrdersArray[i].issuanceDate %></td>
                  <td><%= additionalNoticeOrdersArray[i].dueDate %></td>
                </tr>
                <% } %>
              </tbody>
            </table>
          </td>
        </tr>
        <% } %>
         <% if (noticeAndOrdersArray.length> 0) { %>
        <tr>
          <td>Notice & Orders</td>
        </tr>
        <tr>
          <td>
            <table style="border-spacing: 0px; border: 0.923px solid #cfcfcf; margin-bottom: 100px">
              <thead>
                <tr>
                  <th>S.No</th>
                  <th>Client Name</th>
                  <th>Notice / Order No.</th>
                  <th>Type</th>
                  <th>Amount</th>
                  <th>Date of Issuance</th>
  
                  <th>Due Date</th>
                </tr>
              </thead>
              <tbody>
                <% for(let i=0; i < noticeAndOrdersArray.length; i++ ) { %> <% let incrementedValue=i
                + 1; %>
                <tr>
                  <td><%= incrementedValue %></td>
                  <td><%= noticeAndOrdersArray[i].clientName %></td>
                  <td><%= noticeAndOrdersArray[i].orderNumber %></td>
                  <td><%= noticeAndOrdersArray[i].type %></td>
                  <td><%= noticeAndOrdersArray[i].amount %></td>
                  <td><%= noticeAndOrdersArray[i].issuanceDate %></td>
  
                  <td><%= noticeAndOrdersArray[i].dueDate %></td>
                </tr>
                <% } %>
              </tbody>
            </table>
          </td>
        </tr>
        <% } %> <% if (fyaRecordsArray.length> 0) { %>
        <tr>
          <td>Income Tax | PAN</td>
        </tr>
        <tr>
          <td>e-Proceeding (For Your Action)</td>
        </tr>
        <tr>
          <td>
            <table style="border-spacing: 0px; border: 0.923px solid #cfcfcf; margin-bottom: 100px">
              <thead>
                <tr>
                  <th>S.No</th>
                  <th>Client Name</th>
                  <th>PAN</th>
                  <th>AY</th>
                  <th>Proceeding Name</th>
                  <th>DIN</th>
  
                  <th>Issues On</th>
                  <th>Response Due Date</th>
                </tr>
              </thead>
              <tbody>
                <% for(let i=0; i < fyaRecordsArray.length; i++ ) { %> <% let incrementedValue=i + 1;
                %>
                <tr>
                  <td><%= incrementedValue %></td>
                  <td><%= fyaRecordsArray[i].clientName %></td>
                  <td><%= fyaRecordsArray[i].panNumber %></td>
                  <td><%= fyaRecordsArray[i].ay %></td>
                  <td><%= fyaRecordsArray[i].proceedingName %></td>
                  <td><%= fyaRecordsArray[i].din %></td>
  
                  <td><%= fyaRecordsArray[i].issuedOnDate %></td>
                  <td><%= fyaRecordsArray[i].responseDueDate %></td>
                </tr>
                <% } %>
              </tbody>
            </table>
          </td>
        </tr>
        <% } %> <% if (fyiRecordsArray.length> 0) { %>
        <tr>
          <td>e-Proceeding (For Your Information)</td>
        </tr>
        <tr>
          <td>
            <table style="border-spacing: 0px; border: 0.923px solid #cfcfcf">
              <thead>
                <tr>
                  <th>S.No</th>
                  <th>Client Name</th>
                  <th>PAN</th>
                  <th>AY</th>
                  <th>Proceeding Name</th>
                  <th>DIN</th>
  
                  <th>Issues On</th>
                  <th>Response Due Date</th>
                </tr>
              </thead>
              <tbody>
                <% for(let i=0; i < fyiRecordsArray.length; i++ ) { %> <% let incrementedValue=i + 1;
                %>
                <tr>
                  <td><%= incrementedValue %></td>
                  <td><%= fyiRecordsArray[i].clientName %></td>
                  <td><%= fyiRecordsArray[i].panNumber %></td>
                  <td><%= fyiRecordsArray[i].ay %></td>
                  <td><%= fyiRecordsArray[i].proceedingName %></td>
                  <td><%= fyiRecordsArray[i].din %></td>
  
                  <td><%= fyiRecordsArray[i].issuedOnDate %></td>
                  <td><%= fyiRecordsArray[i].responseDueDate %></td>
                </tr>
                <% } %>
              </tbody>
            </table>
          </td>
        </tr>
        <% } %>
        <tr>
          <td>Attendance and TimeSheet Report</td>
        </tr>
        <hr class="line" />      <table>
        <thead>
          <tr>
            <th>User Name</th>
            <th>Role</th>
            <th>Status</th>
            <th>Check-in</th>
            <th>Check-out</th>
            <th>Total Hours</th>
            <th>Type</th>
            <th>Client/Client Group</th>
            <th>TaskName /title</th>
            <!-- <th>TaskId</th> -->

            <th>Duration</th>
          </tr>
        </thead>
        <tbody>
          <% mergedData.forEach(user => { %> <% if (user.logHours.length) { %> <% let firstRow =
          true; %> <% user.logHours.forEach(log => { %>
          <tr>
            <% if (firstRow) { %>
            <td rowspan="<%= user.logHours.length %>"><%= user.userName %></td>
            <td rowspan="<%= user.logHours.length %>"><%= user.role %></td>
            <td rowspan="<%= user.logHours.length %>"><%= user.attendanceStatus %></td>
            <td rowspan="<%= user.logHours.length %>"><%= user.checkinTime %></td>
            <td rowspan="<%= user.logHours.length %>"><%= user.checkoutTime %></td>
            <td rowspan="<%= user.logHours.length %>"><%= user.totalLogHours %></td>

            <% } %>
            <td><%= log.type %></td>

            <td><%= log.client %></td>
            <td><%= log.taskName %></td>
            <!-- <td><%= log.taskId %></td> -->

            <td><%= log.duration %></td>
          </tr>
          <% firstRow = false; %> <% }); %> <% } else { %>
          <tr>
            <td><%= user.userName %></td>
            <td><%= user.role %></td>
            <td><%= user.attendanceStatus %></td>
            <td><%= user.checkinTime %></td>
            <td><%= user.checkoutTime %></td>
            <td><%= user.totalLogHours %></td>
            <td colspan="4">No log hours available</td>
          </tr>
          <% } %> <% }); %>
        </tbody>
      </table>
    </div>
  </body>
</html>
