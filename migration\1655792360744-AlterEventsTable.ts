import { MigrationInterface, QueryRunner } from 'typeorm';

export class AlterEventsTable1655792360744 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE event 
         MODIFY COLUMN start_time datetime null,
         MODIFY COLUMN end_time datetime null`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}
