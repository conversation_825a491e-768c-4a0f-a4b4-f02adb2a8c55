import { MigrationInterface, QueryRunner } from 'typeorm';

export class AlterInvoiceOtherParticularTable1658398151957
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
        ALTER TABLE invoice_other_particular
        ADD COLUMN type enum("PURE_AGENT", "ADDITIONAL") not null   
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}
