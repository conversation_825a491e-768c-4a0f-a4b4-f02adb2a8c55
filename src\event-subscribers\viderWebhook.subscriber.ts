import { User, UserType } from "src/modules/users/entities/user.entity";
import ViderWebhook from "src/modules/webhook/entity/vider-webhook.entity";
import ViderWhatsappSessions from "src/modules/whatsapp/entity/viderWhatsappSessions";
import { Connection, EntitySubscriberInterface, EventSubscriber, InsertEvent } from "typeorm";
import { UserStatus } from "src/modules/whatsapp/whatsapp.controller";
import { buildBodyAndSendMessage, sendConversationData, sendDocumentConversationData } from "src/modules/whatsapp/whatsapp.service";
import WhatsappRequests from "src/modules/whatsapp/entity/whatsappRequests";
import * as AWS from 'aws-sdk';
import MetaTemplatesStatusWebhook from "src/modules/whatsapp/entity/metaTemplatesStatusWebhook";
const axios = require('axios');

const s3 = new AWS.S3();

@EventSubscriber()
export class ViderWebhookSubscriber implements EntitySubscriberInterface<ViderWebhook> {
    constructor(private readonly connection: Connection) {
        connection.subscribers.push(this);
    }

    listenTo() {
        return ViderWebhook;
    }

    beforeInsert(event: InsertEvent<ViderWebhook>): void | Promise<any> {
    }

    async afterInsert(event: InsertEvent<ViderWebhook>) {
        try {
            const payload = JSON.parse(event.entity.payload);
            const changes = payload.entry?.[0]?.changes;
            if (changes && changes[0]?.field === "messages") {
                const messages = changes[0]?.value?.messages?.[0];

                if (messages) {
                    // const { from, text } = messages;
                    const { from,text, image, document,type } = messages;

                    if(process.env.WEBSITE_URL === 'https://test-ca.vider.in' && process.env.Cron_Running === 'true'){

                    const whatsappRequests = new WhatsappRequests()
                    whatsappRequests.mobileNumber = from;
                    whatsappRequests.message = text.body;
                    await whatsappRequests.save();
                    }

                    // const { from } = messages;
                    const mobileNumber = from?.substring(2);
                    const conversationMobileNumber = from 
                    if (type === 'image' && image) {
                        await this.handleMedia(image.id, conversationMobileNumber,image);
                    }
        
                    if (type === 'document' && document) {
                        await this.handleMedia(document.id, conversationMobileNumber,document);
                    }
                    if (type === 'text' && text?.body) {
                        await sendConversationData(from, text.body);
                    }

                    if (mobileNumber && messages.button?.text === 'Yes, I require updates.') {
                        const userDetails = await User.findOne({ where: { mobileNumber,type: UserType.ORGANIZATION } });

                        if (userDetails) {
                            const { id, organization } = userDetails;
                            

                            if (id && organization) {
                                const existingSession = await ViderWhatsappSessions.findOne({ userId: id });
                                if (existingSession) {
                                    // If user exists, update the status column to ACTIVE
                                    const result = await ViderWhatsappSessions.findOne({
                                        where: { userId: id },
                                      });
                                      result.status = UserStatus.ACTIVE;
                                      result.isSent = false;
                                      result.save();
                                    if(process.env.WEBSITE_URL === 'https://atom.vider.in'&& process.env.Cron_Running === 'no'){

                                    await buildBodyAndSendMessage(id,organization?.id);
                                    }
                                }
                                else{
                                
                                const session = new ViderWhatsappSessions();
                                session.userId = id;
                                session.organizationId = organization?.id;
                                await session.save();
                                if(process.env.WEBSITE_URL === 'https://atom.vider.in'&& process.env.Cron_Running === 'no'){

                                await buildBodyAndSendMessage(id,organization?.id);
                                }
                                }

                            } else {
                            }

                        } else {
                        }

                    } else {
                    }

                } else {
                }

            } 
               if (changes[0]?.field === 'message_template_status_update') {
                          const templateData = changes[0]?.value;
                          
                          if (templateData) {
                              const {
                                  event: status,
                                  message_template_id: templateId,
                                  message_template_name: templateName,
                                  reason,
                              } = templateData;
                      
                              const whatsappBusinessId = payload.entry?.[0]?.id;
                      
                              const metaStatus = new MetaTemplatesStatusWebhook();
                              metaStatus.templateName = templateName;
                              metaStatus.templateId = String(templateId);
                              metaStatus.status = status;
                              metaStatus.reason = reason || '';
                              metaStatus.whatsappBusinessId = whatsappBusinessId;
                              metaStatus.createdAt = new Date();
                      
                              await metaStatus.save();
                      
                          }
                      }
            else {
                // console.log("No 'messages' field found in changes or changes array is empty");
            }

        } catch (error) {
            console.error("Error in afterInsert:", error);
            // Handle the error appropriately, e.g., log, notify, or take corrective action
        }
    }

    async handleMedia(mediaId: string, mobileNumber: string,document) {
        try {
            const mediaUrlResponse = await axios.get(`https://graph.facebook.com/v17.0/${mediaId}`, {
                headers: {
                    Authorization: `Bearer EAALleIgpd0QBOx6wyMKR1uEo4rnCscXE4ZBCV4JqHykfpdvqzrqy7cZA0wFhIrVZCGP0jNiLZARZCZCBgl9g6OfWGY1UGMaGqUvevsMbZBbwnQH13DkSg9IIWYmZAQEoQhTSXpshoYIu8gZCcqAeO9VVCouxzxtuMcQ6JPtbuLsFzyFZAknnzfs6mxZBxSp2wGe5XRL`,
                },
            });
  
            if (!mediaUrlResponse.data.url) {
                console.error('Failed to retrieve media URL');
                return;
            }
  
            const mediaUrl = mediaUrlResponse.data.url;
  
            // You can either store this URL or download the media
            const mediaResponse = await axios.get(mediaUrl, {
                headers: { Authorization: ` Bearer EAALleIgpd0QBOx6wyMKR1uEo4rnCscXE4ZBCV4JqHykfpdvqzrqy7cZA0wFhIrVZCGP0jNiLZARZCZCBgl9g6OfWGY1UGMaGqUvevsMbZBbwnQH13DkSg9IIWYmZAQEoQhTSXpshoYIu8gZCcqAeO9VVCouxzxtuMcQ6JPtbuLsFzyFZAknnzfs6mxZBxSp2wGe5XRL` },
                responseType: 'arraybuffer', // Download as binary
            });
  
            const bucketS3 = process.env.AWS_BUCKET_NAME;
            const timestamp = new Date().toISOString(); // Get current timestamp
            const fileExtension = document?.mime_type.includes('/') ? `.${document?.mime_type.split('/')[1]}` : '';
  
  
            // Upload PDF to S3
                            const s3Params = {
                              Bucket: process.env.AWS_BUCKET_NAME,
                              Key: `whatsapp-conversation-${timestamp}${fileExtension}`,
                              Body: mediaResponse?.data,
                              ContentType:  document?.mime_type,
                            };
                            const uploadResult = await s3.upload(s3Params).promise();
                            const pdfLink = uploadResult.Location;
                            await sendDocumentConversationData(mobileNumber,uploadResult,document?.mime_type)
            // Process or store the downloaded file as needed
        } catch (error) {
            console.error(`Error fetching ${document?.mime_type}:`, error);
        }
    }

}

