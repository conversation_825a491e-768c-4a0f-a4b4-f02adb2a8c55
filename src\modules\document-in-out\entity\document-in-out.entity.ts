import {
  BaseEntity,
  Column,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  Join<PERSON><PERSON>,
  ManyToMany,
  ManyToOne,
  OneToMany,
  OneToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import Client from 'src/modules/clients/entity/client.entity';
import ClientGroup from 'src/modules/client-group/client-group.entity';
import DocumentsData from './documents-data.entity';
import { Organization } from 'src/modules/organization/entities/organization.entity';
import { User } from 'src/modules/users/entities/user.entity';
import DocumentCategory from './document-category.entity';
import Task from 'src/modules/tasks/entity/task.entity';

@Entity()
class DocumentInOut extends BaseEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  documentId: string;

  @Column()
  documentType: string;

  @Column()
  useType: string;

  @Column()
  description: string;

  @Column()
  keptAtName: string;

  @ManyToOne(() => Client, (client) => client.documentInOut)
  client: Client;

  @ManyToOne(() => ClientGroup, (clientGroup) => clientGroup.documentInOut)
  clientGroup: ClientGroup;

  @OneToMany(() => DocumentsData, (documentData) => documentData.documentInOut, { cascade: true })
  documentData: DocumentsData[];

  @ManyToOne(() => Organization, (organization) => organization.tasks)
  organization: Organization;

  @ManyToMany(() => User, (user) => user.documentReceivedBy)
  @JoinTable()
  receivedBy: User[];

  @Column()
  receivedTo: string;

  @OneToOne(() => User, (user) => user.documentInOut)
  @JoinColumn()
  user: User;

  @OneToOne(() => User, (user) => user.updatedByDocumentInOut)
  @JoinColumn()
  updatedBy: User;

  @ManyToMany(() => User, (user) => user.documentGivenBy)
  @JoinTable()
  givenBy: User[];

  @Column('json')
  mailData: object;

  @ManyToOne(() => DocumentCategory, (documentCategory) => documentCategory.documentInOut)
  keptAt: DocumentCategory;

  @ManyToOne(() => Task, (task) => task.id)
  task: Task[];

  @Column()
  updatedDateTime: string;

}

export default DocumentInOut;