import {
  BaseEntity,
  Column,
  CreateDateColumn,
  Entity,
  ManyToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { Invoice } from './invoice.entity';
import Receipt from './receipt.entity';



export enum ReceiptParticularStatus {
  CREATED = 'CREATED',
  CANCELLED = 'CANCELLED',
  DELETED = 'DELETED',
}


@Entity()
class ReceiptParticular extends BaseEntity {
  @PrimaryGeneratedColumn()
  id: number;

  // @Column()
  // invoice: number;

  @Column()
  invoiceId: number;

  @Column({ type: 'enum', enum: ReceiptParticularStatus })
  status: ReceiptParticularStatus;

  @ManyToOne(() => Invoice, (invoice) => invoice.receiptParticular)
  invoice: Invoice;

  @Column()
  receiptId: number;

  @Column({ type: 'decimal', precision: 19, scale: 2 })
  pureAgentAmount: number;

  @Column({ type: 'decimal', precision: 19, scale: 2 })
  serviceAmount: number;

  @Column({ type: 'decimal', precision: 19, scale: 2 })
  gstAmount: number;

  @Column({ type: 'decimal', precision: 19, scale: 2 })
  amount: number;

  @Column({ type: 'decimal', precision: 19, scale: 2 })
  dueServiceAmount: number;

  @Column({ type: 'decimal', precision: 19, scale: 2 })
  duePureAgentAmount: number;

  @Column({ default: false })
  payFullPgPayment: boolean;

  @Column({ default: false })
  payFullServicePayment: boolean;

  @Column()
  taskId: number;

  @ManyToOne(() => Receipt, (receipt) => receipt.receiptParticular)
  receipt: Receipt;

  @CreateDateColumn()
  createdAt: string;

  @UpdateDateColumn()
  updatedAt: string;

}

export default ReceiptParticular;
