import { Injectable, NotFoundException } from '@nestjs/common';
import { User } from '../users/entities/user.entity';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { Event_Actions } from 'src/event-listeners/actions';
import { create<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, get<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Not } from 'typeorm';
import { Cron, CronExpression } from '@nestjs/schedule';
import { Role } from '../roles/entities/role.entity';
import { type } from 'os';
import { Checklist } from '../services/entities/checklist.entity';
import { Service } from '../services/entities/service.entity';
import axios from 'axios';
import QtmActivity from '../admin/entities/qtmActivity.entity';
import QtmTemplate from '../admin/entities/quantumTemplateFeed.entity';
import OrganizationPreferences from '../organization-preferences/entity/organization-preferences.entity';
import { error } from 'console';
import AtomToQtmrequests from './entity/atm-qtm-requests.entity';
import QtmCategories from '../admin/entities/quantumCategories';
import Activity, { ActivityType } from '../activity/activity.entity';

@Injectable()
export class QuantumService {
  constructor(private eventEmitter: EventEmitter2) {}

  async getCreatedTemplates(userId, query) {
    try {
      const { limit, offset } = query;

      let qtmActivity = createQueryBuilder(QtmActivity, 'qtmActivity')
        .leftJoinAndSelect('qtmActivity.user', 'user')
        .leftJoinAndSelect('qtmActivity.task', 'task')
        .where('qtmActivity.taskId = :taskId', { taskId: query?.taskId })
        .andWhere('qtmActivity.documentDeleteStatus = :deleteStatus', { deleteStatus: false });

      if (query.search) {
        qtmActivity.andWhere('qtmActivity.templateName like :search', {
          search: `%${query.search}%`,
        });
      }
      if (offset >= 0) {
        qtmActivity.skip(offset);
      }

      if (limit) {
        qtmActivity.take(limit);
      }

      qtmActivity.orderBy('task.id', 'DESC');
      let result = await qtmActivity.getManyAndCount();
      return {
        count: result[1],
        result: result[0],
      };
    } catch (err) {
      console.log('error occur while getCreatedTemplates', err);
    }
  }

  async getQuantumTemplates(query) {
    try {
      let qtmTemplate = createQueryBuilder(QtmTemplate, 'qtmTemplate');

      if (query.search) {
        qtmTemplate.andWhere('qtmTemplate.name like :search', {
          search: `%${query.search}%`,
        });
      }

      if (query.category) {
        qtmTemplate.andWhere('qtmTemplate.templateCategoryId = :category', {
          category: query.category,
        });
      }

      if (query.subCategory) {
        qtmTemplate.andWhere('qtmTemplate.subCatId = :subCategory', {
          subCategory: query.subCategory,
        });
      }

      qtmTemplate.orderBy('qtmTemplate.templateCategoryId', 'ASC');
      let result = await qtmTemplate.getMany();
      return result;
    } catch (err) {
      console.log('error occur while getQuantumTemplates', err);
    }
  }

  async getPreviewDetails(id: any) {
    try {
      const axios = require('axios');

      const config: any = {
        method: 'get',
        maxBodyLength: Infinity,
        url: `${process.env.CAMUNDA_URL}/vider/quantum/api/variable/doc/${id}/export/html`,
        headers: {
          'Content-Type': 'application/json',
        },
      };

      return axios
        .request(config)
        .then((response) => {
          return JSON.stringify(response.data);
        })
        .catch((error) => {
          console.log(error);
        });
    } catch (err) {
      console.log('error occur while getting document html format', err);
    }
  }

  async syncToQuantum(userId: any) {
    try {
      let config: any = {
        method: 'get',
        maxBodyLength: Infinity,
        url: `${process.env.QUANTUM_URL}/users/atomToQuantumSignIn/${userId}`,
        headers: {},
      };

      return axios
        .request(config)
        .then((response) => {
          return JSON.stringify(response.data);
        })
        .catch((error) => {
          console.log(error);
        });
    } catch (err) {
      console.log('error occur while syncToQuantum', err);
    }
  }

  async getQtmCategories() {
    try {
      let categories = await QtmCategories.find({
        relations: ['subCategories', 'parentCategory'],
        where: {
          defaultOne: true,
          parentCategory: IsNull(),
        },
      });
      return categories;
    } catch (err) {
      console.log('error occur while getQtmCategories', err);
    }
  }

  async downlaodQtmDocument(data: any, userId) {
    let user = await User.findOne({
      where: { id: userId },
      relations: ['organization'],
    });
    try {
      let config: any = {
        method: 'get',
        maxBodyLength: Infinity,
        url: `${process.env.CAMUNDA_URL}/vider/quantum/api/variable/doc/${data?.processInstanceId}/export/pdf?downloadableDocId=true`,
        headers: {
          'Content-Type': 'application/json',
        },
      };

      return axios
        .request(config)
        .then(async (response: any) => {
          const result = {
            downloadType: data?.type,
            documentId: response.data,
          };
          let taskactivity = new Activity();
          taskactivity.action = Event_Actions.QUANTUM_DOCUMENT_DOWNLOADED;
          taskactivity.actorId = userId;
          taskactivity.type = ActivityType.TASK;
          taskactivity.typeId = data.taskId;
          taskactivity.remarks = `Quantum Document "${data.templateName}" Downloaded by ${user.fullName}`;
          await taskactivity.save();
          return result;
        })
        .catch((error: any) => {
          console.log(error);
        });
    } catch (err) {
      console.log('error occur while downlaodQtmDocument', err);
    }
  }

  async deleteDocument(id: string) {
    let activityData = await QtmActivity.findOne({ where: { id } });
    if (activityData) {
      activityData.documentDeleteStatus = !activityData.documentDeleteStatus;
      await activityData.save();
      return true;
    }
  }

  async getClientQtmDocuments(clientId: any, query) {
    try {
      const { limit, offset } = query;

      let qtmActivity = createQueryBuilder(QtmActivity, 'qtmActivity')
        .leftJoinAndSelect('qtmActivity.user', 'user')
        .leftJoinAndSelect('qtmActivity.task', 'task')
        .leftJoinAndSelect('qtmActivity.client', 'client')
        .leftJoinAndSelect('qtmActivity.clientGroup', 'clientGroup');
        
        if(query?.clientId){
          qtmActivity.where('qtmActivity.clientId = :clientId', { clientId: query?.clientId });
        }
        if(query?.clientGroup){
          qtmActivity.where('qtmActivity.clientGroup = :clientGroup', { clientGroup: query?.clientGroup });
        }

      if (query.search) {
        qtmActivity.andWhere('qtmActivity.templateName like :search', {
          search: `%${query.search}%`,
        });
      }
      if (offset >= 0) {
        qtmActivity.skip(offset);
      }

      if (limit) {
        qtmActivity.take(limit);
      }
      const sort = (typeof query?.sort === "string") ? JSON.parse(query.sort) : query?.sort;
      if (sort?.column) {
          const columnMap: Record<string, string> = {
            taskNumber: 'task.task',
            name: 'task.name',
            templateName:'qtmActivity.templateName',
            id:'qtmActivity.createdAt',
            documentStatus:'qtmActivity.documentStatus',
          
          };
          const column = columnMap[sort.column] || sort.column;
          qtmActivity.orderBy(column, sort.direction.toUpperCase());
      } else {
        qtmActivity.orderBy('task.id', 'DESC');
      };
      let result = await qtmActivity.getManyAndCount();
      return {
        count: result[1],
        result: result[0],
      };
    } catch (err) {
      console.log('error occur while getClientQtmDocuments', err);
    }
  }

  async getQuantumAnalytics(userId: number) {
    try {
      let user = await User.findOne({ where: { id: userId }, relations: ['organization', 'role'] });
      let sql = `  
      select count(qtm_activity.id) as count,
       sum(case when qtm_activity.document_status = 'in_progress' then 1 else 0 end) as inProgress,
      sum(case when qtm_activity.document_status = 'under_review' then 1 else 0 end) as underReview,
      sum(case when qtm_activity.document_status = 'completed' then 1 else 0 end) as Completed
      from  qtm_activity`;

      sql += ` where organization_id = ${user.organization.id} and document_status IN ( 'in_progress', 'completed', 'under_review') and document_delete_status is false`;

      let qtmActivity = await getManager().query(sql);
      return qtmActivity;
    } catch (err) {
      console.log('error occur while getting getQuantumAnalytics', err);
    }
  }

  async getQuantumDocumentAnalytics(userId: number, query: any) {
    try {
      let user = await User.findOne({ where: { id: userId }, relations: ['organization', 'role'] });

      let sql = `select u.full_name as fullName,
      sum(case when qtm_activity.document_status = 'in_progress' then 1 else 0 end) as inProgress,
      sum(case when qtm_activity.document_status = 'completed' then 1 else 0 end) as completed,
      sum(case when qtm_activity.document_status = 'under_review' then 1 else 0 end) as underReview
      from qtm_activity 
      left join user u on u.id = qtm_activity.user_id
      where qtm_activity.organization_id = ${user.organization.id} 
      AND (qtm_activity.document_status IN ( 'in_progress', 'completed', 'under_review')) 
      AND document_delete_status is false `;

      sql += ` group by u.id having u.id is not null`;
      // limit ${query?.offset || 0}, ${query?.limit || 10}`;

      let result = await getManager().query(sql);
      return result;
    } catch (err) {
      console.log('error occur while getting getQuantumAnalytics', err);
    }
  }

  async getQtmqtmConfig(userId: number) {
    try {
      let user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
      const orgPreferences = await OrganizationPreferences.findOne({
        where: { organization: user?.organization?.id },
      });
      return orgPreferences;
    } catch (err) {
      console.log('error occur while getting the getQtmqtmConfig', err);
    }
  }

  async sendQuantumRequest(userId: number) {
    try {
      let user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
      const atomToQtmRequest = new AtomToQtmrequests();
      atomToQtmRequest.user = user;
      atomToQtmRequest.organizationId = user?.organization?.id;
      atomToQtmRequest.status = false;
      atomToQtmRequest.save();
    } catch (err) {
      console.log('error occur while sending sendQuantumRequest', err);
    }
  }
}
