import {
  Connection,
  EntitySubscriberInterface,
  EventSubscriber,
  UpdateEvent,
} from 'typeorm';
import DscRegister from 'src/modules/dsc-register/entity/dsc-register.entity';

@EventSubscriber()
export class DscDeleteSubscriber implements EntitySubscriberInterface<DscRegister> {
  constructor(private readonly connection: Connection) {
    connection.subscribers.push(this);
  }

  listenTo() {
    return DscRegister;
  }

  async beforeUpdate(event: UpdateEvent<DscRegister>) {
  }

  async afterUpdate(event: UpdateEvent<DscRegister>) {
    //     const entity_manager=getManager()
    //     const title = "Dsc Register"
    //     const DscHolderName = event.entity.holderName
    //     const orgId = event.entity.organization.id
    //     const body = `{Username} has added DSC details of ${DscHolderName} to the DSC register. click to view more details`
    //      const orgIds = await getAdminIDsBasedOnOrganizationId(orgId)
    //  insertINTOnotification(title,body,orgIds)
  }
}
