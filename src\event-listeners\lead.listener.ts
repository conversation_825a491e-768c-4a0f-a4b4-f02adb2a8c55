import { Injectable } from '@nestjs/common';
import { OnEvent } from '@nestjs/event-emitter';
import Lead from 'src/modules/leads/lead.entity';
import { sendNotification } from 'src/notifications/notify';
import { User } from 'src/modules/users/entities/user.entity';
import { createQueryBuilder } from 'typeorm';
import { Event_Actions } from './actions';

interface CreateEvent {
  lead: Lead;
}

@Injectable()
export class LeadListener {
  @OnEvent(Event_Actions.LEAD_CREATED, { async: true })
  async onLeadCreated(event: CreateEvent) {
    try {
      let { lead } = event;
      let data = await createQueryBuilder(Lead, 'lead')
        .leftJoinAndSelect('lead.user', 'user')
        .leftJoinAndSelect('lead.organization', 'organization')
        .leftJoinAndSelect('organization.users', 'users')
        .leftJoinAndSelect('users.role', 'role')
        .where('lead.id = :id', { id: lead.id })
        .getOne();

      let orgAdmin = data.organization.users.find((user: User) => user.role.defaultRole);

      if (!orgAdmin) return;

      let notification = {
        title: 'Lead created',
        body: `New lead ${data.name} has been created by ${data.user.fullName}`,
      };

      // await sendNotification([orgAdmin.id], notification);
      
      
    } catch (e) {
      console.log(e);
    }
  }
}
