import {
  Body,
  Controller,
  Get,
  Param,
  ParseIntPipe,
  Post,
  Put,
  Query,
  Req,
  UploadedFiles,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { CollectDataService } from './collect-data.service';
import { JwtAuthGuard } from '../users/jwt/jwt-auth.guard';
import CreateCollectDataDto from './dto/create-collect-data.dto';
import { FilesInterceptor } from '@nestjs/platform-express';

@Controller('collect-data')
export class CollectDataController {
  constructor(private service: CollectDataService) { }

  @Get('/:id')
  async get(@Param('id') id: string) {
    return this.service.findOne(id);
  }

  @Get('task/:id')
  async getTask(@Param('id', ParseIntPipe) id: number) {
    return this.service.findOneTask(id);
  }

  @Get('attachments/:id')
  async getAttachments(@Param('id') id: string) {
    return this.service.getAttachments(id);
  }

  @Get('reference/:id')
  async getReferenceDocs(@Param('id') id: string) {
    return this.service.getReferenceDocs(id);
  }

  @UseGuards(JwtAuthGuard)
  @Post('/remind/:id')
  async remindData(@Req() req: any, @Param('id', ParseIntPipe) id: number, @Body() body: any) {
    const { userId } = req.user;
    return this.service.remindData(id, userId, body);
  }

  @UseGuards(JwtAuthGuard)
  @Put(':id')
  async updateData(@Req() req: any, @Param('id', ParseIntPipe) id: number, @Body() body: CreateCollectDataDto) {
    const { userId } = req.user;
    return this.service.updateData(id, userId, body);
  }

  @UseGuards(JwtAuthGuard)
  @Post('comments')
  async updateDataComments(@Req() req: any, @Body() body: any) {
    const { userId } = req.user;
    return this.service.updateDataComments(body);
  }

  @UseGuards(JwtAuthGuard)
  @Post('expiry-date-extend')
  async updateCollectDataExipryExtend(@Req() req: any, @Body() body: any) {
    const { userId } = req.user;
    return this.service.updateCollectDataExipryExtend(body);
  }

  @UseGuards(JwtAuthGuard)
  @Post()
  async create(@Req() req: any, @Body() body: CreateCollectDataDto) {
    const { userId } = req.user;
    return this.service.create(userId, body);
  }

  @UseGuards(JwtAuthGuard)
  @Post('confirm-attachments/confirm')
  async confirmDOcument(@Req() req: any, @Body() body: any) {
    const { userId } = req.user;
    return this.service.confirmDocument(userId, body);
  }

  @Post('/:taskId/:collectId/attachments')
  @UseInterceptors(FilesInterceptor('files'))
  addAttachments(
    @UploadedFiles() files: Express.Multer.File[],
    @Param('taskId', ParseIntPipe) taskId: number,
    @Param('collectId') collectId: any,
    @Query('origin') origin: string,
  ) {
    return this.service.saveAttchement(origin, collectId, taskId, files);
  }
}
