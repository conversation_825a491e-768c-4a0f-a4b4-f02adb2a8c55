import { Injectable } from '@nestjs/common';
import { Brackets, create<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, getRepository } from 'typeorm';
import Client from 'src/modules/clients/entity/client.entity';
import { User } from 'src/modules/users/entities/user.entity';
import AutomationMachines from 'src/modules/automation/entities/automation_machines.entity';
import GstrAdditionalNoticeOrders from 'src/modules/gstr-automation/entity/gstrAdditionalOrdersAndNotices.entity';
import GstrCredentials from 'src/modules/gstr-automation/entity/gstrCredentials.entity';
import GstrProfile from 'src/modules/gstr-automation/entity/gstrProfile.entity';
import GstrNoticeOrders from 'src/modules/gstr-automation/entity/noticeOrders.entity';

@Injectable()
export class GstrService {
  async getOrderNotices(userId, id) {
    const noticeOrders = await GstrNoticeOrders.find({ where: { gstrCredentialsId: id } });
    return noticeOrders;
  }

  async getOrderNotice(userId, id) {
    const noticeOrders = await GstrNoticeOrders.findOne({
      where: { id: id },
      relations: ['client'],
    });
    return noticeOrders;
  }

  async getGstrAdditionalDeailss(userId, id) {
    const additionalData = await GstrAdditionalNoticeOrders.findOne({ where: { id } });
    const relatedData = await GstrAdditionalNoticeOrders.find({
      where: {
        caseTypeId: additionalData?.caseTypeId,
        caseId: additionalData?.caseId,
        gstrCredentialsId: additionalData?.gstrCredentialsId,
      },
    });
    return { additionalData, relatedData };
  }

  async getGstrProfile(userId, id) {
    const gstrProfile = await GstrProfile.findOne({ where: { gstrCredentialsId: id } });
    return gstrProfile;
  }

  async getGstrClientCompliance(userId, id) {
    const gstrCredentials = await GstrCredentials.findOne({ where: { id } });
    const clientId = gstrCredentials?.clientId;
    const client = await Client.findOne({ id: clientId });
    return client;
  }

  async getAddNoticeAndOrders(userId, query: any) {
    const { offset, limit } = query;

    const user = await User.findOne({ where: { id: userId }, relations: ['organization'] });

    const additionalRecords = createQueryBuilder(GstrAdditionalNoticeOrders, 'gstrAdditional')
      .leftJoinAndSelect('gstrAdditional.client', 'client')
      .where('gstrAdditional.organizationId = :id', { id: user?.organization?.id });

    if (query.search) {
      additionalRecords.andWhere(
        new Brackets((qb) => {
          // qb.where('gstrNoticeOrders.pan LIKE :pansearch', {
          //   pansearch: `%${query.search}%`,
          // });
          qb.orWhere('client.displayName LIKE :namesearch', {
            namesearch: `%${query.search}%`,
          });
        }),
      );
    }

    if (query.financialYear) {
      additionalRecords.andWhere('gstrAdditional.fy = :financialYear', {
        financialYear: query.financialYear,
      });
    }

    if (query.folderType) {
      additionalRecords.andWhere('gstrAdditional.caseFolderTypeName = :folderType', {
        folderType: query.folderType,
      });
    }

    if (query.type) {
      additionalRecords.andWhere('gstrAdditional.caseTypeName like :search', {
        search: `%${query.type}%`,
      });
    }

    if (query.interval) {
      const now = new Date();
      const interval = query.interval;
      if (interval === 'last15days') {
        const last15days = new Date(now.getTime() - 15 * 24 * 60 * 60 * 1000);
        additionalRecords.andWhere('DATE(gstrAdditional.dateOfIssuance) >= :last15days', {
          last15days,
        });
      } else if (interval === 'last1month') {
        const last1month = new Date(now.getFullYear(), now.getMonth() - 1, now.getDate());
        additionalRecords.andWhere('DATE(gstrAdditional.dateOfIssuance) >= :last1month', {
          last1month,
        });
      } else if (interval === 'last1week') {
        const last1week = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        additionalRecords.andWhere('DATE(gstrAdditional.dateOfIssuance) >= :last1week', {
          last1week,
        });
      }
    }

    if (offset >= 0) {
      additionalRecords.skip(offset);
    }

    if (limit) {
      additionalRecords.take(limit);
    }

    let result = await additionalRecords.getManyAndCount();
    return {
      count: result[1],
      result: result[0],
    };
  }

  async getNoticeAndOrders(userId, query: any) {
    const { offset, limit, search, issuedBy, assesmentYear, interval, dueInterval } = query;

    const user = await User.findOne({ where: { id: userId }, relations: ['organization'] });

    const noticeAndOrders = createQueryBuilder(GstrNoticeOrders, 'gstrNoticeOrders')
      .leftJoinAndSelect('gstrNoticeOrders.client', 'client')
      .where('gstrNoticeOrders.organizationId = :id', { id: user?.organization?.id });

    if (query.search) {
      noticeAndOrders.andWhere(
        new Brackets((qb) => {
          // qb.where('gstrNoticeOrders.pan LIKE :pansearch', {
          //   pansearch: `%${query.search}%`,
          // });
          qb.orWhere('client.displayName LIKE :namesearch', {
            namesearch: `%${query.search}%`,
          });
        }),
      );
    }

    if (query.type) {
      noticeAndOrders.andWhere('gstrNoticeOrders.type like :search', { search: `%${query.type}%` });
    }

    if (query.issuedBy) {
      noticeAndOrders.andWhere('gstrNoticeOrders.issuedBy like :search', {
        search: `%${query.issuedBy}%`,
      });
    }

    if (query.interval) {
      const now = new Date();
      const interval = query.interval;
      if (interval === 'last15days') {
        const last15days = new Date(now.getTime() - 15 * 24 * 60 * 60 * 1000);
        noticeAndOrders.andWhere(
          'STR_TO_DATE(gstrNoticeOrders.dateOfIssuance, "%d/%m/%Y") >= :last15days',
          { last15days },
        );
      } else if (interval === 'last1month') {
        const last1month = new Date(now.getFullYear(), now.getMonth() - 1, now.getDate());
        noticeAndOrders.andWhere(
          'STR_TO_DATE(gstrNoticeOrders.dateOfIssuance, "%d/%m/%Y") >= :last1month',
          { last1month },
        );
      } else if (interval === 'last1week') {
        const last1week = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        noticeAndOrders.andWhere(
          'STR_TO_DATE(gstrNoticeOrders.dateOfIssuance, "%d/%m/%Y") >= :last1week',
          { last1week },
        );
      }
    }

    // if (dueInterval) {
    //       const now = new Date();
    //       const futureDate = new Date(now);
    //       if (dueInterval === 'next1week') {
    //         futureDate.setDate(futureDate.getDate() + 7);
    //       } else if (dueInterval === 'next15days') {
    //         futureDate.setDate(futureDate.getDate() + 15);
    //       } else if (dueInterval === 'next1month') {
    //         futureDate.setMonth(futureDate.getMonth() + 1);
    //       } else if (dueInterval === 'next1year') {
    //         futureDate.setFullYear(futureDate.getFullYear() + 1);
    //       } else if (dueInterval === 'none') {
    //       }
    //         noticeAndOrders.andWhere(`gstrNoticeOrders.dueDate BETWEEN CURDATE() AND :futureDate`, { futureDate });
    //   }
    if (query.dueInterval) {
      const now = new Date();
      const interval = query.dueInterval;
      if (interval === 'next15days') {
        const last15days = new Date(now.getTime() + 15 * 24 * 60 * 60 * 1000);
        noticeAndOrders.andWhere(
          'STR_TO_DATE(gstrNoticeOrders.dueDate, "%d/%m/%Y") BETWEEN CURDATE() AND :last15days',
          { last15days },
        );
      } else if (interval === 'next1month') {
        const last1month = new Date(now.getFullYear(), now.getMonth() + 1, now.getDate());
        noticeAndOrders.andWhere(
          'STR_TO_DATE(gstrNoticeOrders.dueDate, "%d/%m/%Y") BETWEEN CURDATE() AND :last1month',
          { last1month },
        );
      } else if (interval === 'next1week') {
        const last1week = new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000);
        noticeAndOrders.andWhere(
          'STR_TO_DATE(gstrNoticeOrders.dueDate, "%d/%m/%Y") BETWEEN CURDATE() AND :last1week',
          { last1week },
        );
      }
    }

    if (offset >= 0) {
      noticeAndOrders.skip(offset);
    }

    if (limit) {
      noticeAndOrders.take(limit);
    }

    let result = await noticeAndOrders.getManyAndCount();
    return {
      count: result[1],
      result: result[0],
    };
  }

  async getGstrReport(userId: number, query: any) {
    const { limit, offset, status, remarks } = query;
    let user = await User.findOne({
      where: { id: userId },
      relations: ['organization'],
    });
    const entityManager = getRepository(AutomationMachines);

    let sql = await entityManager
      .createQueryBuilder('automationMachines')
      .leftJoinAndSelect('automationMachines.gstrCredentials', 'gstrCredentials')
      .leftJoinAndSelect('gstrCredentials.client', 'client')
      .where('gstrCredentials.organizationId = :id', { id: user.organization.id })
      .andWhere('automationMachines.type = :type', { type: 'GSTR' });

    if (status) {
      sql = sql.andWhere('automationMachines.status = :status', { status });
    }

    if (remarks) {
      sql = sql.andWhere('automationMachines.remarks = :remarks', { remarks });
    }

    sql = sql
      .andWhere((qb) => {
        const subQuery = qb
          .subQuery()
          .select('MAX(innerAutomationMachines.id)', 'maxId')
          .from(AutomationMachines, 'innerAutomationMachines')
          .leftJoin('innerAutomationMachines.gstrCredentials', 'gstrCredentials')
          .where('gstrCredentials.organizationId = :id', { id: user.organization.id })
          .groupBy('gstrCredentials.id')
          .getQuery();
        return 'automationMachines.id IN ' + subQuery;
      })
      .orderBy('automationMachines.id', 'DESC')
      .limit(limit)
      .offset(offset);

    const result = await sql.getManyAndCount();
    return {
      data: result[0],
      count: result[1],
    };
  }

  async getGstrAdditionalNoticeOrders(userId, id) {
    const additional = await GstrAdditionalNoticeOrders.find({ where: { gstrCredentialsId: id } });
    return additional;
  }
}
