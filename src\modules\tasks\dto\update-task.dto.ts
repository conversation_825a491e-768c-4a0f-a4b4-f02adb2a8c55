import { IsEnum, IsOptional } from 'class-validator';
import Category from 'src/modules/categories/categories.entity';
import Label from 'src/modules/labels/label.entity';
import { User } from 'src/modules/users/entities/user.entity';
import { FeeType, PaymentStatusEnum, PriorityEnum, TaskRecurringStatus, TaskStatusEnum } from './types';

class UpdateTaskDto {
  @IsOptional()
  remarks: string;

  @IsOptional()
  members: User[];

  @IsOptional()
  directory: string;

  @IsOptional()
  @IsEnum(FeeType)
  feeType: FeeType;

  @IsOptional()
  feeAmount: number;

  @IsOptional()
  budgetedhours: number;

  @IsOptional()
  bhallocation: string;

  @IsOptional()
  description: string;

  @IsOptional()
  taskStartDate: string;

  @IsOptional()
  dueDate: string;

  @IsOptional()
  expectedCompletionDate: string;

  @IsOptional()
  createdDate: string;

  @IsOptional()
  category: Category;

  @IsOptional()
  subCategory: Category;

  @IsOptional()
  labels: Label[];

  @IsOptional()
  priority: PriorityEnum;

  @IsOptional()
  status: TaskStatusEnum;

  @IsOptional()
  recurringStatus: TaskRecurringStatus;

  @IsOptional()
  taskLeader: User[];

  @IsOptional()
  financialYear: string;

  @IsOptional()
  user: User;

  @IsOptional()
  paymentStatus: PaymentStatusEnum;

  @IsOptional()
  billable: boolean;

  @IsOptional()
  approvalProcedures: any;

  @IsOptional()
  isUdin:boolean;

  // @IsOptional()
  // udinDate:string;

  // @IsOptional()
  // udinNumber:string;

  // @IsOptional()
  // udinUser: User;

  @IsOptional()
  udinTask:any;
}

export default UpdateTaskDto;