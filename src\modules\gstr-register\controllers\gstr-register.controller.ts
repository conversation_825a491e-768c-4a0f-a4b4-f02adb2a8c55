import { Body, Controller, Post, UseGuards, Request, Get, Query, Req } from '@nestjs/common';
import { GstrRegisterService } from '../services/gstr-register.service';
import { JwtAuthGuard } from '../../users/jwt/jwt-auth.guard';
import FindQueryDto from '../dto/find-query.dto';
import CreateGstrDto from '../dto/create-gstrs.dto';
import FindComplianceDto from '../dto/find-compliance.dto';

@Controller('gstr')
export class GstrRegisterController {
  constructor(private service: GstrRegisterService) { }

  @UseGuards(JwtAuthGuard)
  @Get()
  getAll(@Request() req: any, @Query() query: FindQueryDto) {
    const { userId } = req.user;
    return this.service.findAll(userId, query);
  }

  
@UseGuards(JwtAuthGuard)
@Post('/exportgstr1')
async exportGstr1FF(@Req() req: any, @Body() body: any) {
  const { userId } = req?.user;
  const query= body;
  return this.service.exportGstr1FF(userId, query);
}

@UseGuards(JwtAuthGuard)
@Post('/exportgstr2x')
async exportGst2x(@Req() req: any, @Body() body: any) {
  const { userId } = req?.user;
  const query= body;
  return this.service.exportGst2x(userId, query);
}

@UseGuards(JwtAuthGuard)
@Post('/exportgstr3b')
async exportGst3b(@Req() req: any, @Body() body: any) {
  const { userId } = req?.user;
  const query= body;
  return this.service.exportGst3b(userId, query);
}

@UseGuards(JwtAuthGuard)
@Post('/exportgstrcmp8')
async exportGstrCmp8(@Req() req: any, @Body() body: any) {
  const { userId } = req?.user;
  const query= body;
  return this.service.exportGstrCmp8(userId, query);
}

@UseGuards(JwtAuthGuard)
@Post('/exportgstr4')
async exportGstr4(@Req() req: any, @Body() body: any) {
  const { userId } = req?.user;
  const query= body;
  return this.service.exportGstr4(userId, query);
}

@UseGuards(JwtAuthGuard)
@Post('/exportgstr9')
async exportGstr9(@Req() req: any, @Body() body: any) {
  const { userId } = req?.user;
  const query= body;
  return this.service.exportGstr9(userId, query);
}

@UseGuards(JwtAuthGuard)
@Post('/exportgstr9c')
async exportGstr9c(@Req() req: any, @Body() body: any) {
  const { userId } = req?.user;
  const query= body;
  return this.service.exportGstr9c(userId, query);
}

@UseGuards(JwtAuthGuard)
@Post('/exportgstrdeductor')
async exportGstrtaxdeductor(@Req() req: any, @Body() body: any) {
  const { userId } = req?.user;
  const query= body;
  return this.service.exportGstrtaxdeductor(userId, query);
}

@UseGuards(JwtAuthGuard)
@Post('/exportgstrcollector')
async exportGstrtaxCollector(@Req() req: any, @Body() body: any) {
  const { userId } = req?.user;
  const query= body;
  return this.service.exportGstrtaxCollector(userId, query);
}

  @UseGuards(JwtAuthGuard)
  @Post()
  create(@Body() body: CreateGstrDto, @Request() req: any) {
    const { userId } = req.user;
    return this.service.create(body, userId);
  }

  @UseGuards(JwtAuthGuard)
  @Get('/clients')
  async getAssigned(@Request() req: any, @Query() query: any) {
    const { userId } = req.user;
    return this.service.findClients(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Get('compliance')
  async getCompliance(@Request() req: any, @Query() query: FindComplianceDto) {
    return this.service.getCompliance(query);
  };
}
