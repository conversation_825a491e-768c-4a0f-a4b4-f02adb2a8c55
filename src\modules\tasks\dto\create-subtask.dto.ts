import { IsNotEmpty, IsOptional } from 'class-validator';
import { PriorityEnum, TaskStatus } from 'src/modules/tasks/dto/types';
import { User } from 'src/modules/users/entities/user.entity';

class CreateSubtaskDto {
  @IsNotEmpty()
  name: string;

  @IsOptional()
  description: string;

  @IsNotEmpty()
  dueDate: string;

  @IsOptional()
  priority: PriorityEnum;

  @IsOptional()
  status: TaskStatus;

  @IsOptional()
  members: User[];
}

export default CreateSubtaskDto;
