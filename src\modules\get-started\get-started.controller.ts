import { Body, Controller, Get, Patch, Req, UseGuards } from '@nestjs/common';
import { IsEnum, IsNotEmpty } from 'class-validator';
import { JwtAuthGuard } from 'src/modules/users/jwt/jwt-auth.guard';
import { UpdateGetStarted } from './dto/update-get-started.dto';
import { GetStartedService } from './get-started.service';

export enum DashboardQueryType {
  TASKS = 'TASKS',
  CLIENT_CATEGORIES = 'CLIENT_CATEGORIES',
  TASK_NUMERALS = 'TASK_NUMERALS',
  USER_TASKS = 'USER_TASKS',
  USER_LOG_HOURS = 'USER_LOG_HOURS',
}

export class DashboardQueryDto {
  @IsNotEmpty()
  @IsEnum(DashboardQueryType)
  type: DashboardQueryType;
}

@UseGuards(JwtAuthGuard)
@Controller('get-started')
export class GestStartedController {
  constructor(private service: GetStartedService) {}

  @Get()
  async getGetStarted(@Req() request: any) {
    const { userId } = request.user;
    return await this.service.getGetStarted(userId);
  }

  @Patch()
  async updateGetStarted(@Req() request: any, @Body() body: UpdateGetStarted) {
    const { userId } = request.user;
    return await this.service.updateGetStarted(userId, body);
  }
}
