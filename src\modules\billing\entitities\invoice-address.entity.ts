import { BaseEntity, Column, <PERSON>tity, PrimaryGeneratedColumn } from 'typeorm';

@Entity()
class InvoiceAddress extends BaseEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  legalName: string;

  @Column({ nullable: true })
  email: string;

  @Column({ nullable: true })
  mobileNumber: string;

  @Column({ nullable: true })
  buildingName: string;

  @Column({ nullable: true })
  street: string;

  @Column()
  city: string;

  @Column()
  state: string;

  @Column()
  pincode: string;
}

export default InvoiceAddress;
