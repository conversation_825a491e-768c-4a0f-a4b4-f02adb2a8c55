<!DOCTYPE html>
<html>

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <script src="https://kit.fontawesome.com/912caee8bf.js" crossorigin="anonymous"></script>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css"
    integrity="sha512-ViRYz8YQJl/Z+Izv+lN1dESiuuZn4MJir+ovlwez4YopS8XH9AveH+wHnQ1lT4r6/+l8cz7gLDh/Ox5kkCxohg=="
    crossorigin="anonymous" referrerpolicy="no-referrer" />
  <link rel="preconnect" href="https://fonts.googleapis.com" />
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />

  <title>Task Deleted Notification</title>

  <title>Task Deleted</title>
  <style>
    body {
      margin: 0;
      font-family: Arial, sans-serif;
      font-size: 16px;
      line-height: 1.5;
    }

    .container {
      max-width: 800px;
      margin: 0 auto;
      padding: 20px;
      background-color: #f2f2f2;
    }

    .blue {
      border-top: 2px solid #0099cc;
    }

    h4 {
      font-size: 20px;
      color: #0099cc;
    }

    p {
      margin: 0;
    }

    .icon {
      height: 30px;
      margin: 6px;
    }

    button {
      background-color: #4CAF50;
      /* Background color */
      border: none;
      /* Remove borders */
      color: white;
      /* Text color */
      padding: 12px 24px;
      /* Padding */
      text-align: center;
      /* Text alignment */
      text-decoration: none;
      /* Remove underline */
      display: inline-block;
      /* Display as inline-block */
      font-size: 16px;
      /* Font size */
      margin: 4px 2px;
      /* Margin */
      transition-duration: 0.4s;
      /* Transition duration */
      cursor: pointer;
      /* Add cursor on hover */
      margin-top: 10px;
      margin-bottom: 20px;
      border-radius: 5px;
    }

    button:hover {
      background-color: #4167ad;
      /* Background color on hover */
      color: white;
      /* Text color on hover */
    }

    button:active {
      background-color: #2c1670;
      /* Background color when clicked */
      color: #6d65b4;
      /* Text color when clicked */
      border: 1px solid #2d39bd;
      /* Add border when clicked */
    }
  </style>
</head>

<body>
  <div class="container">
    <div style="text-align: center;">
      <img src="https://vider.in/images/Vider-Logo.png" style="width:70%" alt="vider-logo" />
    </div>
    <div style="text-align: center;">
      <img src="https://vider.in/images/e-task-deleted.jpg" style="width:70%" alt="vider-logo" />
    </div>
    <h1 style="text-align: center;">Task Deleted</h1>

    <hr class="blue">
    <p> Hello Mr/Mrs/Ms. <%= adminName %>
    </p>

    <p style="margin-top: 5px;"> The task <%= taskName %> with <%=clientName%> has been deleted by username</p>

    <p style="margin-top: 5px;"> The details of the task are given below :</p>
    <ul>
      <li>Task ID: <%= taskId %>
      </li>
      <li>Task Name: <%= taskName%>
      </li>
      <li>Start Date: <%= taskStartDate %>
      </li>
      <li>Client name: <%= clientName%>
      </li>
      <li>Task members : <%= taskMembers%>
      </li>
    </ul>

    <hr class="blue" />
    <p>Please be updated with your mails, stay connected with your team and get your work
      done Smarter, Faster and Better.</p>

    <center>
      <h4>To know more about us, check out our website</h4>
      <a href="https://example.com/"><button class="button">WEBSITE</button></a>
    </center>

    <footer class="details">
      <div style="text-align: center;">
        <div class="icons">
          <a href=" https://www.instagram.com/vider_india/"> <img src="https://vider.in/images/e-insta.jpg" alt="insta"
              class="icon" /></a>
          <a href="https://www.linkedin.com/company/viderindia/"><img src="https://vider.in/images/e-linkedin.jpg"
              alt="linkedin" class="icon" /></a>
          <a href="https://www.facebook.com/vider.india"> <img src="https://vider.in/images/e-facebook.jpg"
              alt="facebook" class="icon" /></a>
          <a href="https://www.youtube.com/@Vider_India"><img src="https://vider.in/images/e-youtube.jpg" alt="youtube"
              class="icon" /></a>
          <a href="https://twitter.com/Vider_India"><img src="https://vider.in/images/e-twitter.jpg" alt="twitter"
              class="icon" /></a>
          <a href="https://whatsapp.com/Vider_India"><img src="https://vider.in/images/e-whatsapp.jpg" alt="whatsapp"
              class="icon" /></a>

        </div>
        <p>Address: GVR's Pride, HIG - 58 A, Phase 5, Kukatpally Housing Board Colony,
          Kukatpally, Hyderabad, Telangana 500072.</p>
      </div>
      <div style="text-align: center;">
        <p>Email us at: <a href="mailto: <EMAIL>"><EMAIL></a></p>
      </div>
    </footer>
  </div>
</body>

</html>