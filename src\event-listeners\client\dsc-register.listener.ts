import { Injectable } from '@nestjs/common';
import { OnEvent } from '@nestjs/event-emitter';
import Client from 'src/modules/clients/entity/client.entity';
import notify from 'src/notifications/notify';
import { User } from 'src/modules/users/entities/user.entity';
import { Event_Actions } from '../actions';
import DscActivity, {
  DscActivityTypeEnum,
} from 'src/modules/dsc-register/entity/dsc-activity.entity';
import ClientGroup from 'src/modules/client-group/client-group.entity';

interface DscRegisterIssue {
  userId: number;
  issuedTo: string;
  clientId: number;
  clientGroupId: number;
}

interface DscRegisterReceive {
  userId: number;
  clientId: number;
  receivedBy: string;
  dscRegisterId: number;
}

@Injectable()
export class DscRegisterListener {
  @OnEvent(Event_Actions.DSC_REGISTER_ISSUED, { async: true })
  async handleDscRegisterIssue(event: DscRegisterIssue) {
    try {
      const { userId, clientId, issuedTo, clientGroupId } = event;

      let user = await User.findOne({ where: { id: userId } });

      let client = await Client.findOne({
        where: { id: clientId },
        relations: ['clientManager'],
      });

      let clientGroup = await ClientGroup.findOne({ where  : { id : clientGroupId }})

      if (!client.clientManager) return;

      await notify.DscRegisterIssue({
        userName: user.fullName,
        clientName: client.displayName,
        clientGroupName: clientGroup.displayName,
        issuedTo,
        userIds: [client.clientManager.id],
      });
    } catch (err) {
      console.log(err);
    }
  }

  @OnEvent(Event_Actions.DSC_REGISTER_RECEIVED, { async: true })
  async handleDscRegisterReceive(event: DscRegisterReceive) {
    try {
      const { clientId, receivedBy, dscRegisterId } = event;

      let dscActivity = await DscActivity.find({
        where: {
          dscRegister: { id: dscRegisterId },
          type: DscActivityTypeEnum.ISSUE,
        },
        order: { date: 'DESC' },
        take: 1,
      });

      if (!dscActivity.length) return;

      let client = await Client.findOne({
        where: { id: clientId },
        relations: ['clientManager'],
      });

      if (!client.clientManager) return;

      await notify.DscRegisterReceive({
        clientName: client.displayName,
        receivedBy,
        userIds: [client.clientManager.id],
        receivedFrom: dscActivity[0]?.personName,
      });
    } catch (err) {
      console.log(err);
    }
  }
}
