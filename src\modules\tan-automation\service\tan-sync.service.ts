import { BadRequestException, Injectable } from '@nestjs/common';
import axios from 'axios';
import { IncomeTaxStatus } from 'src/modules/automation/entities/aut_client_credentials.entity';
import AutomationMachines, {
  TypeEnum,
} from 'src/modules/automation/entities/automation_machines.entity';
import { UserStatus } from 'src/modules/client-group/client-group.entity';
import { User } from 'src/modules/users/entities/user.entity';
import { Brackets, createQueryBuilder, getConnection } from 'typeorm';
import TanUpdateTracker from '../entity/tan-update-tracker.entity';
import TanClientCredentials from '../entity/tan-client-credentials.entity';
import {
  checkAtomProConfigIncomeTax,
  checkAtomProConfigIncomeTaxTan,
} from 'src/utils/atomProReUse';
import { Permissions } from 'src/modules/tasks/permission';
import * as ExcelJS from 'exceljs'
import OrganizationPreferences from 'src/modules/organization-preferences/entity/organization-preferences.entity';
import { getTitle } from 'src/utils';
import * as moment from 'moment';


@Injectable()
export class TanSyncService {
  async createMachine(userId: number, id: any, data: any) {
    let data1 = [
      {
        modules: data?.tanData?.requests,
        tanCredentialsId: id,
        type: TypeEnum.TAN,
      },
      {
        modules: data?.tracesData?.requests,
        tanCredentialsId: id,
        type: TypeEnum.TRACES,
      },
    ];

    data1 = data1.filter((item) => item.modules && item.modules.length > 0);

    return this.sendSingleIncometaxTanAutomationRequestToCamunda(userId, data1);
  }

  async sendSingleIncometaxTanAutomationRequestToCamunda(userId, data) {
    try {
      let config: any = {
        method: 'post',
        maxBodyLength: Infinity,
        url: `${process.env.CAMUNDA_URL}/vider/quantum/api/automation`,
        headers: {
          'X-USER-ID': userId,
          'Content-Type': 'application/json',
        },
        data: JSON.stringify(data),
      };
      const response = await axios(config);

      const responseData = response?.data;
      return responseData[JSON.stringify(data[0].tanCredentialsId)];
    } catch (error) {
      console.log('error in sendSingleIncometaxAutomationRequestToCamunda');
    }
  }

  async sendIncometaxAutomationRequestToCamunda(userId: number, data: any) {
    let config: any = {
      method: 'post',
      maxBodyLength: Infinity,
      url: `${process.env.CAMUNDA_URL}/vider/quantum/api/automation`,
      headers: {
        'X-USER-ID': userId,
        'Content-Type': 'application/json',
      },
      data: data,
    };

    axios
      .request(config)
      .then((response) => {
        // console.log(JSON.stringify(response.data));
      })
      .catch((error) => {
        console.log(error);
      });
  }

  async bulkAutomationSync(userId: number, data: any) {
    try {
      if (data?.selectedIds) {
        let abc = [];
        for (let tanClient of data?.selectedIds) {
          const { tanRequest, tracesRequest } = data?.requests;

          if (tanRequest?.length > 0) {
            abc.push({
              modules: tanRequest,
              tanCredentialsId: tanClient?.id,
              type: TypeEnum.TAN,
            });
          }
          if (tracesRequest?.length > 0) {
            abc.push({
              modules: tracesRequest,
              tanCredentialsId: tanClient?.id,
              type: TypeEnum.TRACES,
            });
          }
        }

        this.sendIncometaxAutomationRequestToCamunda(userId, JSON.stringify(abc));
      }
    } catch (error) {
      console.log('error occur while bulkAutomationSync', error);
    }
  }

  async checkAutomationInOrganization(userId: any) {
    try {
      let user = await User.findOne({
        where: { id: userId },
        relations: ['organization'],
      });
      const sectionsData = await createQueryBuilder(AutomationMachines, 'automationMachines')
        .leftJoin('automationMachines.tanCredentials', 'tanCredentials')
        .where('tanCredentials.organizationId = :id', { id: user.organization.id })
        .andWhere('automationMachines.status = :status', { status: 'PENDING' })
        .getCount();
      return sectionsData;
    } catch (error) {
      console.error('Error fetching checkAutomationInOrganization', error);
    }
  }

  async getIncometexUpdates(userId: number) {
    try {
      let user = await User.findOne({
        where: { id: userId },
        relations: ['organization', 'role'],
      });

      let ViewAll = user.role.permissions.find(
        (permission) => permission.slug === Permissions.VIEW_ALL_CLIENT_MANAGERS,
      );
      let ViewAssigned = user.role.permissions.find(
        (permission) => permission.slug === Permissions.VIEW_ASSIGNED_CLIENT_MANAGERS,
      );

      let tanUpdateTracker = createQueryBuilder(TanUpdateTracker, 'tanUpdateTracker')
        .leftJoinAndSelect('tanUpdateTracker.client', 'client')
        .leftJoin('client.clientManagers', 'clientManagers')
        .leftJoinAndSelect('client.tanClientCredentials', 'tanClientCredentials')
        .where('tanUpdateTracker.organizationId = :organizationId', {
          organizationId: user?.organization?.id,
        })
        .andWhere('tanUpdateTracker.isChange = :isChange', { isChange: true })
        .andWhere('client.status != :status', { status: UserStatus.DELETED })
        .andWhere('tanClientCredentials.status != :disStatus', {
          disStatus: IncomeTaxStatus.DISABLE,
        });
      if (ViewAssigned && !ViewAll) {
        tanUpdateTracker.andWhere('clientManagers.id = :userId', { userId });
      }
      const result = await tanUpdateTracker.getMany();
      return result;
    } catch (error) {
      console.log('error occur while getting  getIncometexUpdates', error);
    }
  }

  async getUpdatedItem(userId: any, id: number) {
    try {
      const user = await User.findOne({ where: { id: userId }, relations: ['organization'] });

      let updateTracker = getConnection()
        .createQueryBuilder(TanUpdateTracker, 'tanUpdateTracker')
        .leftJoinAndSelect('tanUpdateTracker.client', 'client')
        .leftJoin('client.organization', 'organization')
        .where('tanUpdateTracker.id = :id', { id: id })
        .andWhere('organization.id = :organization', { organization: user.organization.id })
        .getOne();

      return updateTracker;
    } catch (error) {
      console.log('error occur while getting  getUpdatedItem', error);
    }
  }

  async disableIncomeTaxClient(userId: number, body: any) {
    const ids = body.ids;
    for (let incomeTaxId of ids) {
      const incomeTaxClient = await TanClientCredentials.findOne({ where: { id: incomeTaxId } });
      if (incomeTaxClient) {
        incomeTaxClient.status = IncomeTaxStatus.DISABLE;
        await incomeTaxClient.save();
      }
    }
  }

  async disableIncomeTaxSingleClient(id: number, userId: number) {
    const incomeTaxClient = await TanClientCredentials.findOne({ where: { id: id } });
    if (incomeTaxClient) {
      incomeTaxClient.status = IncomeTaxStatus.DISABLE;
      await incomeTaxClient.save();
    }
  }

  async enableIncometaxClient(id: number, userId: number) {
    const user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
    const organizationId = user?.organization?.id;
    const incomeTaxClient = await TanClientCredentials.findOne({ where: { id: id } });
    if (incomeTaxClient) {
      const checkIncomeTaxTan = await checkAtomProConfigIncomeTaxTan(organizationId);
      incomeTaxClient.status = IncomeTaxStatus.ENABLE;
      if (checkIncomeTaxTan === true) {
        await incomeTaxClient.save();
      } else {
        throw new BadRequestException(checkIncomeTaxTan);
      }
    }
  }

  async enableBulkIncometaxClient(userId: number, body: any) {
    const user = await User.findOne({
      where: { id: userId },
      relations: ['organization'],
    });

    const organizationId = user?.organization?.id;
    if (!organizationId) {
      throw new BadRequestException('Organization not found for this user');
    }

    // Fetch org preferences only once
    const orgPreferences: any = await OrganizationPreferences.findOne({
      where: { organization: organizationId },
    });

    const isIncomeTaxTanEnabled = orgPreferences?.automationConfig?.tan === 'YES';
    if (!isIncomeTaxTanEnabled) {
      throw new BadRequestException(
        'Subscribe Atom Pro Income Tax TAN to enable clients for this organization',
      );
    }

    const tanClientCredentialCount = await TanClientCredentials.count({
      where: { organizationId, status: IncomeTaxStatus.ENABLE },
    });

    const organizationIncomeTaxLimit =
      orgPreferences?.automationConfig?.tanLimit || 0;

    // check if bulk enable will exceed the limit
    const clientsToEnable = body?.incomeTaxTanClients?.length || 0;
    if (tanClientCredentialCount + clientsToEnable > organizationIncomeTaxLimit) {
      throw new BadRequestException(
        `Cannot enable clients. You can only enable up to ${organizationIncomeTaxLimit} Income Tax TAN clients in Atom Pro.`,
      );
    }

    // If within limit, enable all
    for (let i of body?.incomeTaxTanClients) {
      const incomeTaxClient = await TanClientCredentials.findOne({
        where: { id: i?.id },
      });
      if (incomeTaxClient) {
        incomeTaxClient.status = IncomeTaxStatus.ENABLE;
        await incomeTaxClient.save();
      }
    }
  }

  async getDeletedIncomeTaxClients(userId: number, query) {
    const { limit, offset } = query;
    const user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
    const organizationId = user?.organization?.id;
    if (organizationId) {
      let deletedClients = createQueryBuilder(TanClientCredentials, 'clientCredentials')
        .leftJoinAndSelect('clientCredentials.client', 'client')
        .where('clientCredentials.organizationId = :organizationId', {
          organizationId: organizationId,
        })
        .andWhere('client.status != :status', { status: UserStatus.DELETED })
        .andWhere('clientCredentials.status = :disStatus', { disStatus: IncomeTaxStatus.DISABLE });

      if (query.search) {
        deletedClients.andWhere(
          new Brackets((qb) => {
            qb.where('clientCredentials.tanNumber LIKE :tanNumber', {
              tanNumber: `%${query.search}%`,
            });
            qb.orWhere('client.displayName LIKE :namesearch', {
              namesearch: `%${query.search}%`,
            });
            qb.orWhere('client.clientId LIKE :namesearch', {
              namesearch: `%${query.search}%`,
            });
          }),
        );
      }

      const sort = (typeof query?.sort === "string") ? JSON.parse(query.sort) : query?.sort;
      if (sort?.column) {
        const columnMap: Record<string, string> = {
          displayName: 'client.displayName',
          tanNumber: 'clientCredentials.tanNumber',

        };
        const column = columnMap[sort.column] || sort.column;
        deletedClients.orderBy(column, sort.direction.toUpperCase());
      } else {
        deletedClients.orderBy('clientCredentials.createdAt', 'DESC');
      };

      if (offset >= 0) {
        deletedClients.skip(offset);
      }

      if (limit) {
        deletedClients.take(limit);
      }

      let result = await deletedClients.getManyAndCount();
      return {
        count: result[1],
        result: result[0],
      };
    }
  }

  async exportIncomeTaxTandeletedClients(userId: number, body: any) {
    const newQuery = { ...body, offset: 0, limit: ********* };
    let clients = await this.getDeletedIncomeTaxClients(userId, newQuery);

    if (!clients.result.length) throw new BadRequestException('No Data for Export');

    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Deleted Income Tax Clients');
    const headers = [
      { header: 'S.No', key: 'serialNo' },
      { header: 'Client ID', key: 'clientId' },
      { header: 'Client #', key: 'clientNumber' },
      { header: 'TAN', key: 'tan' },
      { header: 'Category', key: 'category' },
      { header: 'Sub Category', key: 'subCategory' },
      { header: 'Client Name', key: 'clientName' },
      { header: 'Status Updated At', key: 'statusUpdatedAt' },
    ];

    worksheet.columns = headers;

    let serialCounter = 1; // Initialize a counter

    // Initialize array to store max lengths for each column
    const columnMaxLengths = Array(headers.length).fill(0);

    clients.result.forEach((client) => {
      const rowData = {
        serialNo: serialCounter++,
        clientId: client?.client?.clientId,
        clientNumber: client?.client?.clientNumber,
        category: client?.client?.category ? getTitle(client?.client?.category) : "",
        subCategory: client?.client?.subCategory ? getTitle(client?.client?.subCategory) : "",
        clientName: client?.client?.displayName,
        tan: client?.tanNumber,
        password: client?.password,
        statusUpdatedAt: moment(client?.updatedAt).format("DD-MM-YYYY h:mm a")
      };

      const row = worksheet.addRow(rowData);
      worksheet.columns.forEach((column, colIndex) => {
        const headerLength = column.header?.length || 0; // Length of header
        const cellLength = rowData[column.key]?.toString().length || 0; // Length of cell value
        columnMaxLengths[colIndex] = Math.max(
          columnMaxLengths[colIndex] || 0,
          headerLength,
          cellLength,
        );
      });
    });

    // Apply column widths with padding
    worksheet.columns.forEach((column, colIndex) => {
      column.width = columnMaxLengths[colIndex] + 3; // Add padding for readability
    });

    // Style the header row and center alignment
    const headerRow = worksheet.getRow(1);
    headerRow.font = { bold: true };
    headerRow.eachCell((cell) => {
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '64B5F6' },
      };
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' },
      };
      cell.alignment = { horizontal: 'center', vertical: 'middle' };
    });

    worksheet.columns.forEach((column) => {
      if (column.key === 'clientName') {
        column.width = 50;
        column.alignment = { wrapText: true, horizontal: 'center', vertical: 'middle' };
      } else {
        column.alignment = { horizontal: 'center', vertical: 'middle' };
      }
    });

    // Apply wrapText for all rows
    worksheet.eachRow((row) => {
      row.eachCell((cell) => {
        cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true }; // Center alignment and wrap text
      });
    });

    // Freeze the header row
    worksheet.views = [{ state: 'frozen', ySplit: 1 }];

    const buffer = await workbook.xlsx.writeBuffer();
    return buffer;
  }

  async getTanBulkSyncStatus(userId: number) {
    try {
      const user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
      let data = '';

      let config: any = {
        method: 'get',
        maxBodyLength: Infinity,
        url: `${process.env.CAMUNDA_URL}/vider/quantum/api/automation/bulk/sync/${user?.organization?.id}/tan`,
        headers: {},
        data: data,
      };

      const response = await axios.request(config);
      return JSON.stringify(response?.data);
    } catch (error) {
      console.log('error occure while getting into getBulkSyncStatus for TAN', error.message);
    }
  }

  async updateTanEnableStatus(userId: number) {
    try {
      const user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
      let config: any = {
        method: 'put',
        maxBodyLength: Infinity,
        url: `${process.env.CAMUNDA_URL}/vider/quantum/api/automation/bulk/sync/${user?.organization?.id}/enable/tan`,
        headers: {},
        data: '',
      };
      const response = await axios.request(config);
    } catch (error) {
      console.log('error while updateEnableStatus for TAN', error);
    }
  }

  async updateTanDisableStatus(userId: number) {
    try {
      const user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
      let config: any = {
        method: 'put',
        maxBodyLength: Infinity,
        url: `${process.env.CAMUNDA_URL}/vider/quantum/api/automation/bulk/sync/${user?.organization?.id}/disable/tan`,
        headers: {},
        data: '',
      };
      const response = await axios.request(config);
    } catch (error) {
      console.log('error while updateEnableStatus for TAN', error);
    }
  }

  async organizationTanScheduling(userId, body) {
    const { periodicity, day, hour, minute, weekDay } = body;

    try {
      const user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
      const organizarionId = user.organization.id;

      const amPm = hour >= 12 ? 'pm' : 'am'; // Determine AM/PM
      const adjustedHour = hour > 12 ? hour - 12 : hour; // Convert to 12-hour format if needed

      // Prepare the originalBody object based on the provided periodicity
      const originalBody = [
        {
          periodicity: periodicity || 'DAILY', // Default to 'DAILY' if not provided
          days: periodicity === 'WEEKLY' ? (weekDay ? weekDay.toUpperCase() : null) : null,
          daysInstance: {}, // Can add more logic if required for weekly scheduling
          hour: adjustedHour, // Using 12-hour format
          minute: minute,
          seconds: 0,
          amPm: amPm, // "am" or "pm" based on the hour
          dayOfMonth: periodicity === 'MONTHLY' && day ? day : 0, // Set day if 'MONTHLY'
          month: 0, // Assuming no month field for DAILY/WEEKLY
          intervalMinutes: 0, // Assuming no intervalMinutes for now
        },
      ];
      let data = JSON.stringify(
        {
          modules: ['P', 'F', 'EC', 'EP', 'OD'],
          orgId: organizarionId,
          type: 'TAN',
          schedules: originalBody,
        },
      );

      let config: any = {
        method: 'post',
        maxBodyLength: Infinity,
        url: `${process.env.CAMUNDA_URL}/vider/quantum/api/automation/bulk/sync`,
        headers: {
          'X-USER-ID': userId,
          'Content-Type': 'application/json',
        },
        data: data,
      };

      const response = await axios.request(config);
      return JSON.stringify(response?.data);
    } catch (error) {
      console.log('error occur while organizationScheduling for TAN', error);
    }
  }

  async getTraceBulkSyncStatus(userId: number) {
    try {
      const user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
      let data = '';

      let config: any = {
        method: 'get',
        maxBodyLength: Infinity,
        url: `${process.env.CAMUNDA_URL}/vider/quantum/api/automation/bulk/sync/${user?.organization?.id}/traces`,
        headers: {},
        data: data,
      };

      const response = await axios.request(config);
      return JSON.stringify(response?.data);
    } catch (error) {
      console.log('error occure while getting into getBulkSyncStatus for TAN', error.message);
    }
  }

  async updateTraceEnableStatus(userId: number) {
    try {
      const user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
      let config: any = {
        method: 'put',
        maxBodyLength: Infinity,
        url: `${process.env.CAMUNDA_URL}/vider/quantum/api/automation/bulk/sync/${user?.organization?.id}/enable/traces`,
        headers: {},
        data: '',
      };
      const response = await axios.request(config);
    } catch (error) {
      console.log('error while updateEnableStatus for TAN', error);
    }
  }

  async updateTraceDisableStatus(userId: number) {
    try {
      const user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
      let config: any = {
        method: 'put',
        maxBodyLength: Infinity,
        url: `${process.env.CAMUNDA_URL}/vider/quantum/api/automation/bulk/sync/${user?.organization?.id}/disable/traces`,
        headers: {},
        data: '',
      };
      const response = await axios.request(config);
    } catch (error) {
      console.log('error while updateEnableStatus for TAN', error);
    }
  }

  async organizationTraceScheduling(userId, body) {
    const { periodicity, day, hour, minute, weekDay } = body;

    try {
      const user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
      const organizarionId = user.organization.id;

      const amPm = hour >= 12 ? 'pm' : 'am'; // Determine AM/PM
      const adjustedHour = hour > 12 ? hour - 12 : hour; // Convert to 12-hour format if needed

      // Prepare the originalBody object based on the provided periodicity
      const originalBody = [
        {
          periodicity: periodicity || 'DAILY', // Default to 'DAILY' if not provided
          days: periodicity === 'WEEKLY' ? (weekDay ? weekDay.toUpperCase() : null) : null,
          daysInstance: {}, // Can add more logic if required for weekly scheduling
          hour: adjustedHour, // Using 12-hour format
          minute: minute,
          seconds: 0,
          amPm: amPm, // "am" or "pm" based on the hour
          dayOfMonth: periodicity === 'MONTHLY' && day ? day : 0, // Set day if 'MONTHLY'
          month: 0, // Assuming no month field for DAILY/WEEKLY
          intervalMinutes: 0, // Assuming no intervalMinutes for now
        },
      ];
      let data = JSON.stringify(
        {
          modules: ['CI', 'F', 'OD'],
          orgId: organizarionId,
          type: 'TRACES',
          schedules: originalBody,
        },
      );

      let config: any = {
        method: 'post',
        maxBodyLength: Infinity,
        url: `${process.env.CAMUNDA_URL}/vider/quantum/api/automation/bulk/sync`,
        headers: {
          'X-USER-ID': userId,
          'Content-Type': 'application/json',
        },
        data: data,
      };

      const response = await axios.request(config);
      return JSON.stringify(response?.data);
    } catch (error) {
      console.log('error occur while organizationScheduling for TAN', error);
    }
  }
}
