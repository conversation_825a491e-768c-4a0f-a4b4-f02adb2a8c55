import { BaseEntity, Column, <PERSON>tity, PrimaryGeneratedColumn } from "typeorm";

@Entity()
class CronActivity extends BaseEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  cronType: string;

  @Column({ type: 'datetime' })
  cronDate: string;

  @Column({ nullable: true })
  responseData: string;

  @Column({ type: 'datetime', nullable: true })
  startTime: string;

  @Column({ type: 'datetime', nullable: true })
  endTime: string;
}

export default CronActivity;
