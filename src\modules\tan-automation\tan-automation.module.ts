import { Modu<PERSON> } from '@nestjs/common';
import { TanAutomationController } from './controller/tan-automation.controller';
import { TanAutomationService } from './service/tan-automation.service';
import TanClientCredentials from './entity/tan-client-credentials.entity';
import { TypeOrmModule } from '@nestjs/typeorm';
import TanProfile from './entity/tan-profile.entity';
import TanEChallan from './entity/tan-e-challan.entity';
import TanIncomeTaxForms from './entity/tan-income-tax-forms.entity';
import TanMyCas from './entity/tan-my-cas.entity';
import { TanSyncService } from './service/tan-sync.service';
import { TanSyncController } from './controller/tan-sync.controller';
import TanUpdateTracker from './entity/tan-update-tracker.entity';
import Tan<PERSON>ey<PERSON>erson from './entity/tan-key-person.entity';
import { TanDashboardService } from './service/tan-dashboard-service';
import { TanDashboardController } from './controller/tan-dashboard.controller';
import { TanClientCredentialsSubscriber } from 'src/event-subscribers/TanClientCredentials.subscriber';
import TanCommunicationInbox from './entity/tan-communication-inbox.entity';
import TanTempEproFya from './entity/tan_temp_epro_fya.entity';
import TanTempEproFyi from './entity/tan_temp_epro_fyi.entity';
import { TanCronController } from './controller/tan-cron.controller';
import { TanCronService } from './service/tan-cron.service';
import TraceOutstandingDemand from './entity/trace-outstanding-deman.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      TanClientCredentials,
      TanProfile,
      TanEChallan,
      TanIncomeTaxForms,
      TanMyCas,
      TanUpdateTracker,
      TanKeyPerson,
      TanCommunicationInbox,
      TanTempEproFya,
      TanTempEproFyi,
      TraceOutstandingDemand
    ]),
  ],
  controllers: [TanAutomationController, TanSyncController, TanDashboardController,TanCronController],
  providers: [
    TanAutomationService,
    TanSyncService,
    TanDashboardService,
    TanClientCredentialsSubscriber,
    TanCronService
  ],
})
export class TanAutomationModule {}
