import {
  Connection,
  EntitySubscriberInterface,
  EventSubscriber,
  InsertEvent,
  UpdateEvent,
} from 'typeorm';
import { User } from 'src/modules/users/entities/user.entity';
import axios from 'axios';
import { compareSpecificUrls } from 'src/utils/inv-comparision';
import { BadRequestException } from '@nestjs/common';

@EventSubscriber()
export class CamundaUserSubscriber implements EntitySubscriberInterface<User> {
  constructor(private readonly connection: Connection) {
    connection.subscribers.push(this);
  }

  listenTo() {
    return User;
  }

  async afterInsert(event: InsertEvent<User>) {
    if (event.entity.role) {
      const compareEnvUrls = compareSpecificUrls(process.env.CAMUNDA_URL, process.env.SERVER_URL);
      if (!compareEnvUrls) {
        console.error("It appears that Camunda is currently connected to the incorrect environment. This misconfiguration may lead to unexpected behavior or issues, as it could be interacting with the wrong database or set of processes. To resolve this, we should verify the connection settings and ensure that <PERSON><PERSON> is properly configured to connect to the intended environment")
        throw new BadRequestException("Error in Approval Management");
      }

      const QUANTUM_ADD_USER = `${process.env.CAMUNDA_URL}/vider/quantum/api/users`;

      const { fullName, email, id: userId } = event.entity;
      const { name: roleName } = event.entity.role;
      const { id: orgId } = event.entity.organization;
      let convertrdRoleName = roleName;
      const regex = /[~`!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]+/;
      const filanlName = convertrdRoleName.replace(regex, '');
      convertrdRoleName = filanlName;

      const words = filanlName.split(' ');
      if (words.length >= 2) {
        words[1] = words[1].charAt(0).toUpperCase() + words[1].slice(1);
        convertrdRoleName = words.join('');
      }

      const data = {
        userId: userId,
        firstName: fullName,
        lastName: '',
        email: email,
        imageUrl: '',
        roles: event.entity.role.name,
        groups: [`${orgId}${convertrdRoleName}`],
      };
      // try {
      let res = await axios({
        method: 'POST',
        url: QUANTUM_ADD_USER,
        headers: { 'Content-Type': 'application/json' },
        data,
      });

      if (res?.data) {
      }
      // } catch (err) {
      //   console.error(`Error In Camunda User Add Call\n${err}`);
      // }
    }
  }

  async afterUpdate(event: UpdateEvent<User>) {
    setTimeout(async () => {
      if (event?.entity?.role?.name) {
        const compareEnvUrls = compareSpecificUrls(process.env.CAMUNDA_URL, process.env.SERVER_URL);
        if (!compareEnvUrls) {
          console.error("It appears that Camunda is currently connected to the incorrect environment. This misconfiguration may lead to unexpected behavior or issues, as it could be interacting with the wrong database or set of processes. To resolve this, we should verify the connection settings and ensure that Camunda is properly configured to connect to the intended environment")
          throw new BadRequestException("Error in Approval Management");
        }
        const user = await User.findOne({ where: { id: event.entity.id }, relations: ['role'] });
        const QUANTUM_UPDATE_USER = `${process.env.CAMUNDA_URL}/vider/quantum/api/users/${user.id}`;
        const regex = /[~`!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]+/;
        let role = event.entity.role.name;
        let trimRole = role.replace(regex, '');
        const words = trimRole.split(' ');
        if (words.length >= 2) {
          words[1] = words[1].charAt(0).toUpperCase() + words[1].slice(1);
          trimRole = words.join('');
        }

        const data = {
          firstName: event.entity.fullName,
          lastName: '',
          roles: event.entity.role.name,
          email: event.entity.email,
          imageUrl: event?.entity?.imageStorage
            ? `${event.entity.imageStorage?.fileUrl}`
            : null,
          groups: [`${user.organization.id}${trimRole}`],
        };
        // try {
        let res = await axios({
          method: 'PUT',
          url: QUANTUM_UPDATE_USER,
          headers: { 'Content-Type': 'application/json' },
          data,
        });

        if (res?.data) {
        }
        // } catch (err) {
        //   console.error(err);
        //   console.error(`Error In Camunda User Update Call-Used\n${err}`);
        // }

      }
    }, 3000);
  }
}
