import { MigrationInterface, QueryRunner } from 'typeorm';

export class AlterUserTable1661830913154 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
        ALTER TABLE user
        DROP COLUMN deleted,
        DROP COLUMN is_active,
        ADD COLUMN status enum('ACTIVE', 'INACTIVE', 'DELETED') NOT NULL DEFAULT 'ACTIVE';
      `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}
