import {
  BaseEntity,
  Column,
  CreateDateColumn,
  Entity,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';

@Entity()
class AutActivity extends BaseEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column('json')
  modules: Object;

  @Column()
  status: string;

  @Column()
  remarks: string;

  @Column()
  action: string;

  @Column()
  clientId: number;

  @Column()
  userId: number;

  @Column()
  autClientCredentialsId: number;

  @CreateDateColumn()
  createdAt: string;

  @UpdateDateColumn()
  updatedAt: string;
}

export default AutActivity;
