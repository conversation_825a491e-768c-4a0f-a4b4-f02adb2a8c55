import { MigrationInterface, QueryRunner } from 'typeorm';

export class AlterTaskLogHourTable1659502665805 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
        ALTER TABLE task_log_hours
        RENAME to log_hour,
        ADD COLUMN type enum('GENERAL','TASK') not null default 'TASK',
        ADD COLUMN title varchar(255) null,
        ADD COLUMN description text null,
        ADD COLUMN client_id int null,
        ADD FOREIGN KEY (client_id) REFERENCES client(id) ON DELETE SET NULL
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}
