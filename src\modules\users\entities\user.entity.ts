import * as bcrypt from 'bcrypt';
import Client from 'src/modules/clients/entity/client.entity';
import Contact<PERSON>erson from 'src/modules/clients/entity/contact-person.entity';
import Lead from 'src/modules/leads/lead.entity';
import Event from 'src/modules/events/event.entity';
import { Notification } from 'src/notifications/notification.entity';
import RecurringProfile from 'src/modules/recurring/entity/recurring-profile.entity';
import { Role } from 'src/modules/roles/entities/role.entity';
import Storage from 'src/modules/storage/storage.entity';
import ClientPin from 'src/modules/clients/entity/client-pin.entity';
import TaskComment from 'src/modules/tasks/entity/comment.entity';
import TaskStatus from 'src/modules/tasks/entity/task-status.entity';
import Task from 'src/modules/tasks/entity/task.entity';
import {
  AfterLoad,
  BaseEntity,
  BeforeInsert,
  Column,
  CreateDateColumn,
  Entity,
  JoinColumn,
  JoinTable,
  ManyToMany,
  ManyToOne,
  OneToMany,
  OneToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { UserProfile } from './user-profile.entity';
import { Organization } from 'src/modules/organization/entities/organization.entity';
import LogHour from 'src/modules/log-hours/log-hour.entity';
import Expenditure from 'src/modules/expenditure/expenditure.entity';
import { GstrPromise } from 'src/modules/gstr-register/entity/promise.entity';
import { Wallets } from 'src/modules/wallet/entities/wallets.entity';
import OrgApprovals from 'src/modules/atm-qtm-approval/entities/org-approvals.entity';
import ApprovalProcedures from 'src/modules/atm-qtm-approval/entities/approval-procedures.entity';
import CollectData from 'src/modules/collect-data/collect-data.entity';
import Kyb from 'src/modules/kyb/kyb.entity';
import BudgetedHours from 'src/modules/budgeted-hours/budgeted-hours.entity';
import QtmActivity from 'src/modules/admin/entities/qtmActivity.entity';
import AtomToQtmrequests from 'src/modules/quantum/entity/atm-qtm-requests.entity';
import AutomationMachines from 'src/modules/automation/entities/automation_machines.entity';
import UdinTask from 'src/modules/udin-task/udin-task.entity';
import BroadcastEmailTemplates from 'src/modules/communication/entity/broadcast-email-templates-entity';
import AtomProLimitRequests from 'src/modules/automation/entities/atomProLimitRequests.entity';
import BroadcastActivity from 'src/modules/communication/entity/broadcast-activity.entity';
import OrgApprovalLevel from 'src/modules/atm-qtm-approval/entities/org-approvals-level.entity';
import DocumentInOut from 'src/modules/document-in-out/entity/document-in-out.entity';
import Attendance from 'src/modules/attendance/attendance.entity';
import ClientGroup from 'src/modules/client-group/client-group.entity';
import { InvitedUser } from './invited-user.entity';
import LogHourTitle from 'src/modules/log-hours/entity/log-hour-title.entity';
import { BillingEntity } from 'src/modules/organization/entities/billing-entity.entity';
import LogNotes from 'src/modules/log-hours/entity/log-notes.entity';
import { ChannelPartnerSignup } from 'src/modules/channel-partners/entity/channel-partner-signup.entity';

export enum UserType {
  CLIENT = 'CLIENT',
  ORGANIZATION = 'ORGANIZATION',
  CLIENT_USER = 'CLIENT_USER',
  NON_ORGANIZATION = 'NON_ORGANIZATION',
}

export enum UserStatus {
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE',
  DELETED = 'DELETED',
}

@Entity()
export class User extends BaseEntity {
  static findMany(arg0: { where: { organization: { id: number } } }) {
    throw new Error('Method not implemented.');
  }
  @PrimaryGeneratedColumn()
  id: number;

  @OneToOne(() => UserProfile, { cascade: true })
  @JoinColumn()
  profile: UserProfile;

  @Column()
  fullName: string;

  @Column()
  mobileNumber: string;

  @Column({ unique: true })
  email: string;

  @Column({ nullable: true })
  password: string;

  @Column({ default: false })
  isSuperUser: boolean;

  @Column({ nullable: true })
  image: string;

  @Column({ nullable: true })
  countryCode: string;

  @Column()
  mfaEnabled: boolean;

  @Column()
  mfaPasscode: string;

  @Column({ type: 'timestamp', default: null })
  mfaTime: string;

  @OneToMany(() => Attendance, (attendance) => attendance.user, {
    cascade: true,
  })
  Attendance: Attendance[];
  @OneToMany(() => Attendance, (attendance) => attendance.reviewer, {
    cascade: true,
  })
  attendanceReviewer: Attendance[];

  @OneToMany(() => Attendance, (attendance) => attendance.createdBy, {
    cascade: true,
  })
  attendanceCreatedBy: Attendance[];
  @OneToMany(() => LogHour, (loghour) => loghour.createdBy, {
    cascade: true,
  })
  logHoursCreatedBy: LogHour[];

  @OneToMany(() => LogHourTitle, (loghourTitle) => loghourTitle.createdBy, {
    cascade: true,
  })
  logHourTitleCreatedBy: LogHourTitle[];

  @OneToMany(() => Expenditure, (expenditure) => expenditure.reviewer, {
    cascade: true,
  })
  expenditureReviewer: Expenditure[];

  @OneToOne(() => Storage, (storage) => storage.userImage, { cascade: true })
  imageStorage: Storage;

  @Column({ type: 'timestamp', default: null })
  lastLogin: string;

  @Column({ type: 'enum', enum: UserType, default: UserType.ORGANIZATION })
  type: UserType;

  @CreateDateColumn()
  createdAt: string;

  @UpdateDateColumn()
  updatedAt: string;

  @ManyToOne(() => Role, (role) => role.users, { cascade: true })
  @JoinColumn()
  role: Role;

  @OneToOne(() => Client, (client) => client.user)
  client: Client;

  @OneToOne(() => ContactPerson, (contactPerson) => contactPerson.user)
  clientUser: ContactPerson;

  @OneToOne(() => Wallets, (wallets) => wallets.user)
  wallets: Wallets;

  @OneToMany(() => OrgApprovals, (orgApprovals) => orgApprovals.user)
  orgApprovals: OrgApprovals[];

  @OneToMany(() => ApprovalProcedures, (approvalProcedures) => approvalProcedures.user)
  approvalProcedures: ApprovalProcedures[];

  @OneToOne(() => CollectData, (collectData) => collectData.user)
  collectData: CollectData;

  @OneToOne(() => DocumentInOut, (documentInOut) => documentInOut.user)
  documentInOut: DocumentInOut;

  @OneToOne(() => DocumentInOut, (documentInOut) => documentInOut.updatedBy)
  updatedByDocumentInOut: DocumentInOut;

  @ManyToOne(() => Organization, (organization) => organization.users, { eager: true })
  organization: Organization;



  @OneToMany(() => Task, (task) => task.user)
  tasks: Task[];

  @OneToMany(() => Client, (client) => client.createdBy)
  clients: Client[];

  @OneToMany(() => InvitedUser, (invitedUser) => invitedUser.manager)
  invitedUser: InvitedUser[];

  @OneToMany(() => Client, (client) => client.clientManager, { cascade: true })
  clientsAsManager: Client[];

  @ManyToMany(() => Client, (client) => client.clientManagers)
  clientsManager: Client[];

  @ManyToMany(() => ClientGroup, (clientGroup) => clientGroup.clientGroupManagers)
  clientGroupsManagers: ClientGroup[];

  @ManyToMany(() => BillingEntity, (billingEntity) => billingEntity.signatures)
  billingEntity: BillingEntity[];

  @OneToOne(() => BillingEntity, (billingEntity) => billingEntity.defaultSignature)
  defaultBillingEntity: BillingEntity

  @OneToMany(() => RecurringProfile, (recurringProfile) => recurringProfile.user)
  recurringProfiles: RecurringProfile[];

  @OneToMany(() => TaskComment, (taskComment) => taskComment.user)
  comments: TaskComment[];

  @OneToMany(() => LogHour, (logHour) => logHour.user)
  taskLogHours: LogHour[];

  @OneToMany(() => BudgetedHours, (budgetedHours) => budgetedHours.user)
  taskBudgetedHours: BudgetedHours[];

  @OneToMany(() => Event, (event) => event.user)
  events: Event[];

  @OneToMany(() => ClientPin, (clientPin) => clientPin.user)
  clientPins: ClientPin[];

  @OneToMany(() => TaskStatus, (taskStatus) => taskStatus.user)
  taskStatuses: TaskStatus[];

  @OneToMany(() => Expenditure, (expenditure) => expenditure.user)
  expenditure: Expenditure[];

  @OneToMany(() => Kyb, (kyb) => kyb.user)
  kyb: Kyb[];

  @OneToMany(() => Notification, (notification) => notification.user)
  notifications: Notification[];

  @ManyToMany(() => Task, (task) => task.members)
  assignedTasks: Task[];

  @ManyToMany(() => Attendance, (attendance) => attendance.managers)
  assignedAttendance: Attendance[];

  @ManyToMany(() => Expenditure, (expenditure) => expenditure.managers)
  assignedExpenditure: Expenditure[];

  @ManyToMany(() => LogHour, (logHour) => logHour.managers)
  assignedLogHours: LogHour[];

  @ManyToMany(() => User, (user) => user.udinUsers)
  udinUsers: User[];

  @ManyToMany(() => Task, (task) => task.taskLeader)
  assignedLeaderTasks: Task[];

  @ManyToMany(() => DocumentInOut, (documentInOut) => documentInOut.receivedBy)
  documentReceivedBy: DocumentInOut[];

  @ManyToMany(() => DocumentInOut, (documentInOut) => documentInOut.givenBy)
  documentGivenBy: DocumentInOut[];

  @OneToMany(() => Lead, (lead) => lead.user)
  leads: Lead[];

  @OneToMany(() => Storage, (storage) => storage.user)
  storage: Storage[];

  @OneToMany(() => OrgApprovalLevel, (appLevel) => appLevel.user)
  approvalLevels: OrgApprovalLevel[];

  @OneToOne(
    () => BroadcastEmailTemplates,
    (broadcastEmailTemplates) => broadcastEmailTemplates.user,
  )
  broadcastEmailTemplates: CollectData;

  @OneToMany(() => BroadcastActivity, (broadcastActivity) => broadcastActivity.user)
  broadcastActivity: BroadcastActivity[];

  //Automation
  @OneToMany(() => AutomationMachines, (automationMachines) => automationMachines.user)
  automationMachines: AutomationMachines[];

  //quantum
  @OneToMany(() => QtmActivity, (qtmActivitee) => qtmActivitee.user)
  qtmActivitee: QtmActivity[];

  @OneToMany(() => AtomToQtmrequests, (atomToQtmrequests) => atomToQtmrequests.user)
  atomToQtmrequests: AtomToQtmrequests[];
  //quantum

  //atom pro atomProLimitRequests
  @OneToMany(() => AtomProLimitRequests, (atomProLimitRequests) => atomProLimitRequests.user)
  atomProLimitRequests: AtomProLimitRequests[];

  @OneToMany(() => GstrPromise, (gstrPromise) => gstrPromise.user)
  gstrPromises: GstrPromise[];

  @OneToMany(() => UdinTask, (udinTask) => udinTask.user)
  udinTasks: UdinTask[];

  @OneToMany(() => LogNotes, (logNotes) => logNotes.user)
  logNotes: LogNotes[];
  @OneToMany(() => ChannelPartnerSignup, (signup) => signup.user)
  channelSignups: ChannelPartnerSignup[];

  @Column({ type: 'enum', enum: UserStatus, default: UserStatus.ACTIVE })
  status: UserStatus;

  imageUrl: string;

  @AfterLoad()
  renderImageUrl() {
    if (this.image) {
      this.imageUrl = `${process.env.AWS_BASE_URL}/${this.image}`;
    }
  }

  @BeforeInsert()
  async hashPassword() {
    if (this.type === UserType.ORGANIZATION || this.type === UserType.CLIENT_USER) {
      this.password = await bcrypt.hash(this.password, 10);
    }
  }

  async verifyPassword(plainTextPassword: string) {
    return await bcrypt.compare(plainTextPassword, this.password);
  }
}
