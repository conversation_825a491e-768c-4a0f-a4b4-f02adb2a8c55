import {
  Connection,
  EntitySubscriberInterface,
  EventSubscriber,
  InsertEvent,
  getManager,
} from 'typeorm';
import Lead from 'src/modules/leads/lead.entity';
import {
  insertINTOnotification,
  getAdminIDsBasedOnOrganizationId,
  getAdminEmailsBasedOnOrganizationId,
  getUserDetails,
  insertINTONotificationUpdate,
} from 'src/utils/re-use';
import { sendWhatsAppTextMessage } from 'src/modules/whatsapp/whatsapp.service';
import ViderWhatsappSessions from 'src/modules/whatsapp/entity/viderWhatsappSessions';
import { sendnewMail } from 'src/emails/newemails';
import { fullMobileNumberWithCountry } from 'src/utils/validations/fullMobileWithCountry';
import { Organization } from 'src/modules/organization/entities/organization.entity';
import notify from 'src/notifications/notify';

@EventSubscriber()
export class LeadSubscriber implements EntitySubscriberInterface<Lead> {
  constructor(private readonly connection: Connection) {
    connection.subscribers.push(this);
  }

  listenTo() {
    return Lead;
  }

  async beforeInsert(event: InsertEvent<Lead>) { }

  async afterInsert(event: InsertEvent<Lead>) {
    const entity_manager = getManager();
    const title = 'Lead Created';
    const Lead = event?.entity;
    const { email, mobileNumber, category, subCategory } = Lead;
    const leadName = Lead?.name;
    const { user } = Lead;
    const { id } = user;
    const userName = user?.fullName;
    const organization = event?.entity;
    const orgId = user?.organization?.id;
    const orgdetails = await Organization.findOne({ id: orgId });
    const addressParts = [
      orgdetails.buildingNo || '',
      orgdetails.floorNumber || '',
      orgdetails.buildingName || '',
      orgdetails.street || '',
      orgdetails.location || '',
      orgdetails.city || '',
      orgdetails.district || '',
      orgdetails.state || ''
    ].filter(part => part && part.trim() !== '');
    const pincode = orgdetails.pincode && orgdetails.pincode.trim() !== '' ? ` - ${orgdetails.pincode}` : '';

    const address = addressParts.join(', ') + pincode;
    const userID: any = [];
    userID.push(id);
    const userId = [event?.entity?.user?.id];
    const body = `<strong>${leadName}</strong> has been createdjj by <strong>${userName}</strong>.`;
    // insertINTOnotification(title,body,userID)  //to user who created the lead//
    //To send Email//
    const leadEmail = Lead?.email;
    const userIds = await getAdminIDsBasedOnOrganizationId(orgId);
    let organizationsmtp = await Organization.findOne({ where: { id: user.organization.id } });
    const othersSmtpMail = organizationsmtp?.othersSmtp?.[1].auth?.user;
    const concatenatedArray = userID.concat(userIds);
    const usersList = [];
    concatenatedArray.forEach(function (element) {
      if (!usersList.includes(element)) {
        usersList.push(element);
      }
    });
    const key = 'LEAD_CREATED_PUSH';
    // insertINTOnotification(title, body, usersList, orgId)  //to the organization admins//
    insertINTONotificationUpdate(title, body, usersList, orgId, key);
                  await notify.leadCreated({leadName,userName,usersList})
    
    const userListMails = await getAdminEmailsBasedOnOrganizationId(orgId);
    for (let orgAmins of userListMails) {
      await sendnewMail({
        id: orgAmins?.id,
        key: 'NEW_LEAD_CREATED_MAIL',
        email: orgAmins?.email,
        data: {
          leadName: Lead?.name,
          UserName: user?.fullName,
          orgName: Lead?.organization?.legalName,
          othersSmtpMail: othersSmtpMail || '<EMAIL>',
          adminName: orgAmins?.full_name,
          userId: event.entity['userId'],
          adress: address,
          phoneNumber: orgdetails?.mobileNumber,
          mail: orgdetails?.email,
          legalName: orgdetails?.tradeName || orgdetails?.legalName
        },
        filePath: 'new-lead-created',
        subject: 'Lead Created | Vider',
      });
    }

    //Whatsapp Notification
    try {
      if (userIds !== undefined) {
        for (let userId of userIds) {
          const sessionValidation = await ViderWhatsappSessions.findOne({
            where: { userId: userId, status: 'ACTIVE' },
          });
          if (sessionValidation) {
            const adminUserDetails = await getUserDetails(userId);
            const {
              full_name: userFullName,
              mobile_number: userPhoneNumber,
              country_code: countryCode,
            } = adminUserDetails;

            const userWhatsAppNumber = fullMobileNumberWithCountry(userPhoneNumber, countryCode);

            const key = 'LEAD_CREATED_WHATSAPP';
            const whatsappMessageBody = `
Hi ${userFullName}
A new lead has been created in ATOM:

Lead name: ${leadName}
Email address: ${email}
Phone number: ${mobileNumber}
Category: ${category}
${subCategory ? `*Sub-Category:* ${subCategory} ` : ''}

We hope this helps!
`;

            await sendWhatsAppTextMessage(
              userWhatsAppNumber,
              whatsappMessageBody,
              orgId,
              title,
              userId,
              key,
            );
          }
        }
      }
    } catch (error) {
      console.error('Error sending Lead WhatsApp notification:', error);
    }
  }
}
