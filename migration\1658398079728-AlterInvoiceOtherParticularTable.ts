import { MigrationInterface, QueryRunner } from 'typeorm';

export class AlterInvoiceOtherParticularTable1658398079728
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
        ALTER TABLE invoice_other_particular
        ADD COLUMN estimate_id int null,
        ADD FOREIGN KEY (estimate_id) REFERENCES estimate(id)
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}
