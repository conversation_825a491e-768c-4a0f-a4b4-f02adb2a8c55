import { AfterLoad, BaseEntity, Column, Entity, JoinColumn, ManyToOne, OneToOne, PrimaryGeneratedColumn } from 'typeorm';
import { LicenseTypes } from '../dto/types';
import { BillingEntity } from './billing-entity.entity';
import { Organization } from './organization.entity';
import Storage from 'src/modules/storage/storage.entity';

@Entity()
export class OrganizationLicense extends BaseEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ type: 'enum', enum: LicenseTypes })
  type: LicenseTypes;

  @Column()
  name: string;

  @Column()
  licenseNumber: string;

  @ManyToOne(() => Organization, (organization) => organization.licenses, { nullable: true })
  organization: Organization;

  @ManyToOne(() => BillingEntity, (billingEntity) => billingEntity.licenses, { onDelete: 'CASCADE' })
  billingEntity: BillingEntity;

  @Column({ nullable: true })
  attachment: string;

  @OneToOne(() => Storage, (storage) => storage.organizationLicense, { cascade: true })
  storage: Storage;

  attachmentUrl: string;

  @AfterLoad()
  renderUrl() {
    if (this.attachment) {
      this.attachmentUrl = `${process.env.AWS_BASE_URL}/${this.attachment}`;
    }
  }
}
