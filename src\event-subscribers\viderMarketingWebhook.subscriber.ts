import { Connection, EntitySubscriberInterface, EventSubscriber, InsertEvent } from "typeorm";
import { buildBodyAndSendMessage, sendConversationData, sendDocumentConversationData, sendWhatsAppMarketingTextMessage } from "src/modules/whatsapp/whatsapp.service";
import ViderMarketingWebhook from "src/modules/webhook-marketing/entity/vider-marketing-webhook.entity";
import ViderMarketing from "src/modules/webhook-marketing/entity/vider-marketing.entity";
import * as AWS from 'aws-sdk';
import MetaTemplatesStatusWebhook from "src/modules/whatsapp/entity/metaTemplatesStatusWebhook";

const axios = require('axios');

const s3 = new AWS.S3();

@EventSubscriber()

export class ViderMarketingWebhookSubscriber implements EntitySubscriberInterface<ViderMarketingWebhook> {
    constructor(private readonly connection: Connection) {
        connection.subscribers.push(this);
    }

    listenTo() {
        return ViderMarketingWebhook;
    }

    beforeInsert(event: InsertEvent<ViderMarketingWebhook>): void | Promise<any> {

    }

    async afterInsert(event: InsertEvent<ViderMarketingWebhook>) {
        try {
            const payload = JSON.parse(event.entity.payload);
            const changes = payload.entry?.[0]?.changes;

            if (!changes || changes.length === 0) {
                return;
            }
    
    
            const statusData = changes[0]?.value?.statuses?.[0];
    
            // **Handle message event**
            if (changes[0]?.field === "messages") {
                const messages = changes[0]?.value?.messages?.[0];
                if (messages) {
                    // textt is for flow 
                    const { from,text, image, document,type } = messages;
                    // const mobileNumber = from?.substring(2); // Extract phone number
                    const mobileNumber = from;
                    if (type === 'image' && image) {
                      await this.handleMedia(image.id, mobileNumber,image);
                  }
      
                  if (type === 'document' && document) {
                      await this.handleMedia(document.id, mobileNumber,document);
                  }
                    if (text&& text.body) {
                        const messageBody = text.body.toLowerCase(); // Convert to lowercase for case-insensitive matching
            
                        if (messageBody.includes('hello')) {
                            // const axios = require('axios');
let data = JSON.stringify({
  "messaging_product": "whatsapp",
  "to": mobileNumber,
  "type": "template",
  "template": {
    "name": "flow",
    "language": {
      "code": "en"
    },
    "components": [
      {
        "type": "button",
        "sub_type": "flow",
        "index": "0",
        "parameters": [
          {
            "type": "action",
            "action": {
              "flow_token": "1213767040178313",
              "flow_action_data": {}
            }
          }
        ]
      }
    ]
  }
});

let config = {
  method: 'post',
  maxBodyLength: Infinity,
  url: 'https://graph.facebook.com/v18.0/531157636751620/messages',
  headers: { 
    'Content-Type': 'application/json', 
    'Authorization': 'Bearer EAAQoOMDMYXwBO4pWsjFj4zOv1s4zTrZArDEdUPDzD9nOZAAUKt5YCfzWAFUBjb3ykZCZAsBcffjzc1tQRUma1jrpxsbNXCdhAJ9M54cWhY8ZCcexaOOgGqpVk15CgP0RoO1qQyPEkcEWOfsiR8r210u2mZCohmqGRr409ivgVNSpftKHEfvdLLj0LS9bxFZBKpBMwZDZD'
  },
  data : data
};

axios.request(config)
.then((response) => {
})
.catch((error) => {
  console.log(error);
});


                            // You can add your function call here
                            // myFunctionForHello(mobileNumber);
                        } 
                        if (type === 'text' && text?.body) {
                          await sendConversationData(from, text.body);
                      }
          
                     
                        // Add more conditions for other text patterns as needed
                    }
                    // await sendConversationData(
                    //     from,
                    //   text.body,
                    // );
                    if (mobileNumber && messages.button?.text) {
                        
    
                        if (messages.button?.text === 'Request Demo') {
                            let marketingRecord = await ViderMarketing.findOne({ where: { mobileNumber } });
    
                        // if (!marketingRecord) {
                        //     marketingRecord = new ViderMarketing();
                        //     marketingRecord.mobileNumber = mobileNumber;
                        // }
    
                        // Update response column
                        marketingRecord.requestedDemo = 'yes';
                        await marketingRecord.save();
                            const mobileNumberCountryCode = `91${mobileNumber}`;
                            const title = 'response for request demo'

  const whatsappMessageBody = `
Thank you for your interest in taking demo!

please schedule your demo below

https://vider.in/demo.html

Thank you,
Team Vider`;
            await sendWhatsAppMarketingTextMessage(
                mobileNumber,
              whatsappMessageBody,
              title,
            );
        
        } else if (messages.button?.text === 'Visit Website') {
                            let marketingRecord = await ViderMarketing.findOne({ where: { mobileNumber } });
    
                        // if (!marketingRecord) {
                        //     marketingRecord = new ViderMarketing();
                        //     marketingRecord.mobileNumber = mobileNumber;
                        // }
    
                        // Update response column
                        marketingRecord.visitWebsite = 'yes';
                        await marketingRecord.save();
                            const mobileNumberCountryCode = `91${mobileNumber}`;
                            const title = 'visit website'

  const whatsappMessageBody = `
Thank you for your interest to visit our website,

Click here to visit: https://vider.in/

Thank you,
Team Vider`;
            await sendWhatsAppMarketingTextMessage(
                mobileNumber,
              whatsappMessageBody,
              title,
            );
        

                        }
                        else if(messages.button?.text === 'Contact us'){
                            let marketingRecord = await ViderMarketing.findOne({ where: { mobileNumber } });
    
                            marketingRecord.contactUs = 'yes';
                            await marketingRecord.save();
                                const title = 'contact us'
    
      const whatsappMessageBody = `
Thank you for your interest to contact us,

We will Reach out to you very shortly,

Meanwhile if you want to contact us 
Mobile : 9171121121 | 9044401818
Mail   : <EMAIL>

Thank you,
Team Vider`;
                await sendWhatsAppMarketingTextMessage(
                    mobileNumber,
                  whatsappMessageBody,
                  title,
                );
            

                        }
                    }
                }
            }

            if (changes[0]?.field === 'message_template_status_update') {
              const templateData = changes[0]?.value;
              
              if (templateData) {
                  const {
                      event: status,
                      message_template_id: templateId,
                      message_template_name: templateName,
                      reason,
                  } = templateData;
          
                  const whatsappBusinessId = payload.entry?.[0]?.id;
          
                  const metaStatus = new MetaTemplatesStatusWebhook();
                  metaStatus.templateName = templateName;
                  metaStatus.templateId = String(templateId);
                  metaStatus.status = status;
                  metaStatus.reason = reason || '';
                  metaStatus.whatsappBusinessId = whatsappBusinessId;
                  metaStatus.createdAt = new Date();
          
                  await metaStatus.save();
          
              }
          }
          
    
            // **Handle status update event**
            if (statusData) {
                const { status, recipient_id,errors } = statusData;
                // const phoneNumber = recipient_id?.substring(2); // Extract phone number
                const phoneNumber = recipient_id; //phone number
                if (phoneNumber) {
    
                    let marketingRecord = await ViderMarketing.findOne({ where: { mobileNumber: phoneNumber } });
    
                    if (marketingRecord) {
                        // Update status column if record exists
                        marketingRecord.status = status;
                        // If status is "failed", store the error title as well
                    if (status === 'failed' && errors?.[0]?.title) {
                        marketingRecord.errorTitle = errors[0].title; // Store error title
                    }
                        await marketingRecord.save();
                    } else {
                    }
                }
            }
        } catch (error) {
            console.error("Error in afterInsert:", error);
        }
    }
    
    async handleMedia(mediaId: string, mobileNumber: string,document) {
      try {
          const mediaUrlResponse = await axios.get(`https://graph.facebook.com/v17.0/${mediaId}`, {
              headers: {
                  Authorization: `Bearer EAAQoOMDMYXwBO4pWsjFj4zOv1s4zTrZArDEdUPDzD9nOZAAUKt5YCfzWAFUBjb3ykZCZAsBcffjzc1tQRUma1jrpxsbNXCdhAJ9M54cWhY8ZCcexaOOgGqpVk15CgP0RoO1qQyPEkcEWOfsiR8r210u2mZCohmqGRr409ivgVNSpftKHEfvdLLj0LS9bxFZBKpBMwZDZD`,
              },
          });

          if (!mediaUrlResponse.data.url) {
              console.error('Failed to retrieve media URL');
              return;
          }

          const mediaUrl = mediaUrlResponse.data.url;

          // You can either store this URL or download the media
          const mediaResponse = await axios.get(mediaUrl, {
              headers: { Authorization: ` Bearer EAAQoOMDMYXwBO4pWsjFj4zOv1s4zTrZArDEdUPDzD9nOZAAUKt5YCfzWAFUBjb3ykZCZAsBcffjzc1tQRUma1jrpxsbNXCdhAJ9M54cWhY8ZCcexaOOgGqpVk15CgP0RoO1qQyPEkcEWOfsiR8r210u2mZCohmqGRr409ivgVNSpftKHEfvdLLj0LS9bxFZBKpBMwZDZD` },
              responseType: 'arraybuffer', // Download as binary
          });

          const bucketS3 = process.env.AWS_BUCKET_NAME;
          const timestamp = new Date().toISOString(); // Get current timestamp
          const fileExtension = document?.mime_type.includes('/') ? `.${document?.mime_type.split('/')[1]}` : '';


          // Upload PDF to S3
                          const s3Params = {
                            Bucket: process.env.AWS_BUCKET_NAME,
                            Key: `whatsapp-conversation-${timestamp}${fileExtension}`,
                            Body: mediaResponse?.data,
                            ContentType:  document?.mime_type,
                          };
                          const uploadResult = await s3.upload(s3Params).promise();
                          const pdfLink = uploadResult.Location;
                          await sendDocumentConversationData(mobileNumber,uploadResult,document?.mime_type)
          // Process or store the downloaded file as needed
      } catch (error) {
          console.error(`Error fetching ${document?.mime_type}:`, error);
      }
  }
}

