import { Module } from '@nestjs/common';
import { JwtModule } from '@nestjs/jwt';
import { MongooseModule } from '@nestjs/mongoose';
import { PassportModule } from '@nestjs/passport';
import { Form, FormSchema } from 'src/modules/forms/schemas/form.schema';
import {
  Validation,
  ValidationSchema,
} from 'src/modules/forms/schemas/validation.schema';
import { jwtConstants } from 'src/modules/users/jwt/constants';
import { JwtStrategy } from './services/jwt.strategy';
import { ClientPanelController } from './client-panel.controller';
import { ClientPanelService } from './client-panel.service';
import { LabelService } from './services/label.service';
import { TasksService } from './services/tasks.service';
import { CategoriesService } from './services/categories.service';
import { UsersService } from './services/users.service';
import { StorageService } from './services/storage.service';
import { UserListener } from './services/user.listener';
import { CamundaUserSubscriber } from './services/camunda-user.subscriber';
import { InviteUserSubscriber } from './services/inviteuser.subscriber';
import { UserProfileSubscriber } from './services/profile.subscriber';
import { UserWalletSubscriber } from './services/user-wallet.subscriber';
import { UserSubscriber } from './services/user.subscriber';
import { UserStatusSubscriber } from './services/userStatus.subscriber';
import { AwsService } from './services/upload.service';
import { LocalStrategy } from './services/local.strategy';
import { CollectDataService } from './services/collect-data.service';
import { CollectDataSubscriber } from './services/collectData.subscriber';
import { StorageListner } from './services/storage.listener';
import { GeneralAndTaskEventSubscriber } from './services/generalAndTaskEvent.subscriber';
import { EventListener } from './services/event.listener';
import { EventsService } from './services/events.service';
import { ChecklistsService } from './services/checklists.service';
import { CommentsServie } from './services/comments.service';
import { AttachmentsService } from './services/attachments.service';
import { MilestonesService } from '../tasks/services/milestones.service';
import { StageOfWorkService } from './services/stage-of-work.service';
import { LogHoursService } from './services/loghours.service';
import { BudgetedHoursService } from './services/budgeted-hours.service';
import { ApprovalLevelService } from '../atm-qtm-approval/services/approval-level.service';
import { QuantumService } from './services/quantum.service';
import { StatsService } from './services/stats.service';
import { DscRegisterService } from './services/dsc-register.service';
import { RecurringService } from './services/recurring.service';
import { InvoiceService } from './services/invoice.service';
import { ReceiptsService } from './services/receipts.service';
import { ClientService } from './services/clients.service';
import { OneDriveStorageService } from './services/onedrive-storage.service';
import { KybService } from './services/kyb.service';
import { AutProfileDetailsService } from './services/aut-profile-details.service';
import { AutDashboardService } from './services/dashboard.services';
import { GstrClientService } from './services/gstr-client.service';
import { GstrRegisterService } from './services/gstr-register.service';
import { AutIncomeTaxFormsService } from './services/income-tax-forms.services';
import { GstrService } from './services/notices.service';
import { PromiseService } from './services/promise.service';
import { ClientPasswordService } from './services/client-passwords.service';
import { ProformaService } from './services/proforma.service';
import { BillingEntitiesService } from './services/billing-entities.service';
import { ExpenditureService } from './services/expenditure.service';

@Module({
  imports: [
    MongooseModule.forFeature([{ name: Form.name, schema: FormSchema }]),
    MongooseModule.forFeature([
      { name: Validation.name, schema: ValidationSchema },
    ]),
    PassportModule.register({
      defaultStrategy: 'jwt',
    }),
    JwtModule.register({
      secret: jwtConstants.secret,
      signOptions: { expiresIn: '7d' },
    }),
  ],
  controllers: [ClientPanelController],
  providers: [
    ClientPanelService,
    JwtStrategy,
    LabelService,
    TasksService,
    ChecklistsService,
    CommentsServie,
    AttachmentsService,
    MilestonesService,
    StageOfWorkService,
    CategoriesService,
    UsersService,
    LocalStrategy,
    JwtStrategy,
    UserListener,
    UserProfileSubscriber,
    UserSubscriber,
    UserStatusSubscriber,
    InviteUserSubscriber,
    UserWalletSubscriber,
    CamundaUserSubscriber,
    StorageService,
    AwsService,
    CollectDataService,
    CollectDataSubscriber,
    StorageListner,
    EventsService,
    EventListener,
    GeneralAndTaskEventSubscriber,
    LogHoursService,
    BudgetedHoursService,
    ApprovalLevelService,
    QuantumService,
    StatsService,
    DscRegisterService,
    RecurringService,
    InvoiceService,
    ReceiptsService,
    ClientService,
    OneDriveStorageService,
    KybService,
    AutProfileDetailsService,
    AutDashboardService,
    AutIncomeTaxFormsService,
    ClientPasswordService,
    GstrService,
    GstrClientService,
    GstrRegisterService,
    PromiseService,
    ProformaService,
    BillingEntitiesService,
    ExpenditureService,
  ],
})
export class ClientPanelModule { }
