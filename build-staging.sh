#!/bin/bash

echo "Starting staging deployment process..."

# Step 1: Clean the local dist directory
echo "Cleaning local dist directory..."
rm -rf dist || { echo "Failed to clean dist directory"; exit 1; }

# Step 2: Build the project with increased memory limit
echo "Building the project for staging..."
yarn build || { echo "Build failed"; exit 1; }

# Step 3: Clean the remote build directory
echo "Cleaning remote dist directory..."
ssh viderTest 'rm -rf /home/<USER>/vider_server/dist' || { echo "Failed to clean remote directory"; exit 1; }

# Step 4: Transfer the dist directory
echo "Transferring dist directory to remote server..."
scp -rv ./dist viderTest:/home/<USER>/vider_server/ || { echo "File transfer failed"; exit 1; }

# Step 5: Verify transferred files
echo "Verifying transferred files on the remote server..."
ssh viderTest <<EOF
    echo "Listing files in the remote dist directory..."
    ls -la /home/<USER>/vider_server/dist || { echo "Failed to list files"; exit 1; }
EOF

echo "Staging deployment process completed successfully."

