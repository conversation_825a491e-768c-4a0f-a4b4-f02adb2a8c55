import { Controller, Get, Query, Request, UseGuards } from '@nestjs/common';
import { JwtAuthGuard } from 'src/modules/users/jwt/jwt-auth.guard';
import Activity from './activity.entity';
import GetActivityDto from './dto/get-activity.dto';
import { createQueryBuilder } from 'typeorm';
import { dateFormation } from 'src/utils/datesFormation';

@Controller('activity')
export class ActivityController {
  @UseGuards(JwtAuthGuard)
  @Get()
  async getAll(@Request() req, @Query() query: GetActivityDto) {
    const activityData = await createQueryBuilder(Activity, 'activity')
      .where('activity.type =:type', { type: query.type })
      .andWhere('activity.typeId =:typeId', { typeId: query.typeId });

    if (query?.fromDate && query?.toDate) {
      const { startTime, endTime } = dateFormation(query.fromDate, query.toDate);
      activityData
        .andWhere('Date(activity.created_at) >= :startTime', { startTime })
        .andWhere('Date(activity.created_at) <= :endTime', { endTime });
    }

    if (query?.category) {
      activityData.andWhere(`activity.action = :action`, { action: query.category });
    }

    activityData.orderBy('activity.id', 'DESC');
    const result = await activityData.getMany();
    return result;
  }
}
