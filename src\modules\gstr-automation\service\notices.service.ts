import { BadRequestException, Injectable, NotFoundException } from '@nestjs/common';
import { Brackets, createQueryBuilder, getConnection, getRepository } from 'typeorm';
import GstrNoticeOrders, { CreatedType } from '../entity/noticeOrders.entity';
import GstrProfile from '../entity/gstrProfile.entity';
import Client from 'src/modules/clients/entity/client.entity';
import { User } from 'src/modules/users/entities/user.entity';
import AutomationMachines from 'src/modules/automation/entities/automation_machines.entity';
import GstrCredentials, { GstrStatus } from '../entity/gstrCredentials.entity';
import GstrAdditionalNoticeOrders from '../entity/gstrAdditionalOrdersAndNotices.entity';
import { add, capitalize } from 'lodash';
import * as xlsx from 'xlsx';
import { formatDate } from 'src/utils';
import { UserStatus } from 'src/modules/whatsapp/whatsapp.controller';
import * as moment from 'moment';
import GstrUpdateTracker from '../entity/gstr_update_tracker.entity';
import axios from 'axios';
import { Permissions } from 'src/modules/tasks/permission';
import * as ExcelJS from 'exceljs';
import { GstrOutstandingDemand } from '../entity/gstrDemands.entity';
import { GstrLedgerBalance } from '../entity/gstrLedgersBalance.entity';


const categoryLabels = {
  individual: 'Individual',
  huf: 'Hindu Undivided Family',
  partnership_firm: 'Partnership Firm',
  llp: 'Limited Liability Partnership',
  company: 'Company',
  opc: 'OPC',
  public: 'Public Limited',
  government: 'Government',
  sec_8: 'Section-8',
  foreign: 'Foreign',
  aop: 'Association of Persons',
  boi: 'Body of Individuals',
  trust: 'Trust',
  public_trust: 'Public Trust',
  private_discretionary_trust: 'Private Discretionary Trust',
  state: 'State',
  central: 'Central',
  local_authority: 'Local Authority',
  artificial_judicial_person: 'Artificial Juridical Person',
};

const ledgerTypeMap = {
  LIABILITY: 'Electronic Liability Register (Return related)',
  CASH: 'Electronic Cash Ledger',
  ITC: 'Electronic Credit Ledger',
  ITC_REVERSAL: 'Electronic Credit Reversal and Re-claimed Statement',
  RCM: 'RCM Liability/ITC Statement',
  NEGATIVE_LIABILITY: 'Negative liability statement - Regular Taxpayers',
};

@Injectable()
export class GstrService {
  async getOrderNotices(userId: number, id: number, query: any) {
    const { offset, limit } = query;
    try {
      const user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
      const checkGstrCredentials = await GstrCredentials.findOne({
        where: { id: id, organizationId: user.organization.id },
      });

      if (checkGstrCredentials) {
        const additionalRecords = createQueryBuilder(GstrNoticeOrders, 'gstrNoticeOrders').where(
          'gstrNoticeOrders.gstrCredentialsId = :id',
          { id },
        );
        const sort = typeof query?.sort === 'string' ? JSON.parse(query.sort) : query?.sort;
        if (sort?.column) {
          const columnMap: Record<string, string> = {
            type: 'gstrNoticeOrders.type',
            issuedBy: 'gstrNoticeOrders.issuedBy',
            amountOfDemand: 'gstrNoticeOrders.amountOfDemand',
            dateOfIssuance: 'gstrNoticeOrders.dateOfIssuance',
            dueDate: 'gstrNoticeOrders.dueDate',
          };
          const column = columnMap[sort.column] || sort.column;
          additionalRecords.orderBy(column, sort.direction.toUpperCase());
        }
        if (offset >= 0) {
          additionalRecords.skip(offset);
        }

        if (limit) {
          additionalRecords.take(limit);
        }

        // additionalRecords.orderBy('STR_TO_DATE(gstrNoticeOrders.dateOfIssuance,"%d/%m/%Y")', 'DESC');

        let result = await additionalRecords.getManyAndCount();
        return {
          count: result[1],
          result: result[0],
          accessDenied: true,
        };
      } else {
        return { accessDenied: false };
      }
    } catch (error) {
      console.log('error accure while getting getGstrProfile', error);
    }
  }

  async gstClientNoticeandordersExport(userId: number, query: any) {
    const exportQuery = { ...query, offset: 0, limit: 100000000 };
    const gstrid = query.gstrid;
    let gstNoticeorders = await this.getOrderNotices(userId, gstrid, exportQuery);
    if (!gstNoticeorders.result.length) throw new BadRequestException('No Data for Export');
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('GST Notice & Orders');
    const headers = [
      { header: 'S.No', key: 'serialNo' },
      { header: 'GSTIN', key: 'gstIn' },
      { header: 'Type', key: 'type' },
      { header: 'Date of Issuance', key: 'issuanceDate' },
      { header: 'Issued by', key: 'issuedBy' },

      { header: 'Notice/Order #', key: 'noticeNum' },
      { header: 'Due Date', key: 'dueDate' },
      { header: 'Amount (₹)', key: 'amount' },
    ];

    worksheet.columns = headers;
    const columnMaxLengths = Array(headers.length).fill(0);
    let serialCounter = 1; // Initialize a counter
    if (!gstNoticeorders.result.length) throw new BadRequestException('No Data for Export');
    gstNoticeorders.result.forEach((noticeorder) => {
      const amount = noticeorder?.amountOfDemand === ' ' ? 0 : noticeorder?.amountOfDemand;

      const rowData = {
        serialNo: serialCounter++, // Assign and then increment the counter
        gstIn: noticeorder?.gstIn,
        issuanceDate: noticeorder?.dateOfIssuance,
        issuedBy: noticeorder?.issuedBy,
        type: noticeorder?.type,
        noticeNum: noticeorder?.orderNumber,
        issuedDate: noticeorder?.dateOfIssuance,
        dueDate: noticeorder?.dueDate,
        amount: amount,
      };

      const row = worksheet.addRow(rowData);

      worksheet.columns.forEach((column, colIndex) => {
        const headerLength = column.header?.length || 0;
        const cellLength = rowData[column.key]?.toString().length || 0;
        columnMaxLengths[colIndex] = Math.max(
          columnMaxLengths[colIndex] || 0,
          headerLength,
          cellLength,
        );
      });
    });

    worksheet.columns.forEach((column, colIndex) => {
      column.width = columnMaxLengths[colIndex] + 2;
    });

    const headerRow = worksheet.getRow(1);
    headerRow.font = { bold: true };
    headerRow.eachCell((cell) => {
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '64B5F6' },
      };
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' },
      };
      cell.alignment = { horizontal: 'center', vertical: 'middle' };
    });

    worksheet.columns.forEach((column) => {
      if (column.key === 'clientName') {
        column.width = 50;
        column.alignment = { wrapText: true, horizontal: 'center', vertical: 'middle' };
      } else {
        column.alignment = { horizontal: 'center', vertical: 'middle' };
      }
    });

    worksheet.eachRow((row) => {
      row.eachCell((cell) => {
        cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
      });
    });

    worksheet.views = [{ state: 'frozen', ySplit: 1 }];

    const buffer = await workbook.xlsx.writeBuffer();
    return buffer;
  }
  async getOrderNotice(userId: number, id: number) {
    try {
      const user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
      let noticeOrders = await GstrNoticeOrders.findOne({
        where: { id: id, organizationId: user?.organization?.id },
        relations: ['client'],
      });

      if (noticeOrders?.createdType === 'MANUAL') {
        noticeOrders = await GstrNoticeOrders.findOne({
          where: { id: id, organizationId: user?.organization?.id },
          relations: ['client', 'storage'],
        });
      }
      return noticeOrders;
    } catch (error) {
      console.log('error occur while getting getOrderNotice');
    }
  }

  async getGstrAdditionalDeailss(userId: number, id: number) {
    try {
      const user = await User.findOne({ where: { id: userId }, relations: ['organization'] });

      let additionalData = await GstrAdditionalNoticeOrders.findOne({
        where: { id, organizationId: user?.organization?.id },
      });

      if (additionalData?.createdType === 'MANUAL') {
        additionalData = await GstrAdditionalNoticeOrders.findOne({
          where: { id, organizationId: user?.organization?.id },
          relations: ['storage'],
        });
      }
      let relatedData = [];
      if (additionalData) {
        relatedData = await GstrAdditionalNoticeOrders.find({
          where: {
            caseTypeId: additionalData?.caseTypeId,
            caseId: additionalData?.caseId,
            gstrCredentialsId: additionalData?.gstrCredentialsId,
          },
        });
      }
      const groupedData = relatedData.reduce((acc, record) => {
        const type = record.caseFolderTypeName;
        if (!acc[type]) {
          acc[type] = [];
        }
        acc[type].push(record);
        return acc;
      }, {} as Record<string, typeof relatedData>);

      const categorizedData = Object.entries(groupedData).map(([type, records]) => ({
        type,
        records,
      }));

      return { additionalData, categorizedData };
    } catch (error) {
      console.log('error accure while getting getGstrAdditionalDeailss', error);
    }
  }

  async getGstrProfile(userId: number, id: number) {
    try {
      const user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
      const checkGstrCredentials = await GstrCredentials.findOne({
        where: { id: id, organizationId: user.organization.id },
      });
      if (checkGstrCredentials) {
        const lastCompletedMachine = await AutomationMachines.findOne({
          where: { gstrCredentials: id, status: 'COMPLETED' },
          order: {
            id: 'DESC',
          }, // Assuming you have a createdAt field indicating creation timestamp
        });

        const gstrProfile = await createQueryBuilder(GstrProfile, 'gstrProfile')
          .leftJoin('gstrProfile.gstrCredentials', 'gstrCredentials')
          .where('gstrCredentials.id = :gstrCredId', { gstrCredId: id })
          .getOne();
        return { gstrProfile, lastCompletedMachine, accessDenied: true };
      } else {
        return { accessDenied: false };
      }
    } catch (error) {
      console.log('error accure while getting getGstrProfile', error);
    }
  }

  async getGstrClientCompliance(userId: number, id: number) {
    try {
      const user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
      const checkGstrCredentials = await GstrCredentials.findOne({
        where: { id: id, organizationId: user.organization.id },
      });
      if (checkGstrCredentials) {
        const gstrCredentials = await GstrCredentials.findOne({ where: { id } });
        const clientId = gstrCredentials?.clientId;
        const client = await Client.findOne({ id: clientId });
        return client;
      } else {
        return { accessDenied: false };
      }
    } catch (error) {
      console.log('error accure while getting getGstrClientCompliance', error);
    }
  }

  // async getAddNoticeAndOrders(userId: number, query: any) {
  //   try {
  //     const { offset, limit, search, financialYear, folderType, type, interval, caseType, responseType, dueInterval, uniqueType } = query;
  //     const user = await User.findOne({
  //       where: { id: userId },
  //       relations: ['organization', 'role'],
  //     });

  //     let ViewAll = user.role.permissions.find(
  //       (permission) => permission.slug === Permissions.VIEW_ALL_CLIENT_MANAGERS,
  //     );
  //     let ViewAssigned = user.role.permissions.find(
  //       (permission) => permission.slug === Permissions.VIEW_ASSIGNED_CLIENT_MANAGERS,
  //     );

  //     const additionalRecords = createQueryBuilder(GstrAdditionalNoticeOrders, 'gstrAdditional')
  //       .leftJoinAndSelect('gstrAdditional.client', 'client')
  //       .leftJoin('client.clientManagers', 'clientManagers')
  //       .leftJoinAndSelect('client.gstrCredentials', 'gstrCredentials')
  //       .select([
  //         'gstrAdditional.id',
  //         'client.displayName',
  //         'gstrAdditional.gstIn',
  //         'gstrAdditional.name',
  //         'gstrAdditional.caseTypeName',
  //         'gstrAdditional.refId',
  //         'gstrAdditional.gstrCredentialsId',
  //         'gstrAdditional.fy',
  //         'gstrAdditional.caseFolderTypeName',
  //         'gstrAdditional.categoryDate',
  //         'gstrAdditional.dueDate',
  //         'gstrAdditional.refNum',
  //         'gstrAdditional.categoryType',
  //         'gstrAdditional.caseStatus',
  //         'gstrAdditional.refStatus',
  //         'gstrAdditional.description',
  //         'gstrAdditional.createdType',
  //         'clientManagers.id',
  //       ])
  //       .where('gstrAdditional.organizationId = :id', { id: user?.organization?.id })
  //       .andWhere('client.status != :status', { status: UserStatus.DELETED })
  //       .andWhere(
  //         new Brackets((qb) => {
  //           qb.where('gstrCredentials.status != :disStatus', {
  //             disStatus: GstrStatus.DISABLE,
  //           }).orWhere('gstrCredentials.status IS NULL');
  //         }),
  //       )
  //       .andWhere('gstrAdditional.caseFolderTypeName NOT IN (:...typeName)', {
  //         typeName: ['REPLIES', 'APPLICATIONS'],
  //       });

  //     additionalRecords.addSelect(
  //       `COALESCE(
  //           STR_TO_DATE(NULLIF(gstrAdditional.categoryDate, 'NA'), '%d/%m/%Y'),
  //           '0000-01-01'
  //         )`,
  //       'issueDateOrder',
  //     );
  //     additionalRecords.addOrderBy('issueDateOrder', 'DESC');
  //     const sort = typeof query?.sort === 'string' ? JSON.parse(query.sort) : query?.sort;
  //     if (sort?.column) {
  //       const columnMap: Record<string, string> = {
  //         name: 'client.displayName',
  //         fy: 'gstrAdditional.fy',
  //         casetype: 'gstrAdditional.caseTypeName',
  //         folder: 'gstrAdditional.caseFolderTypeName',
  //         type: 'gstrAdditional.categoryType',
  //         categoryDate: 'gstrAdditional.categoryDate',
  //         dueDate: 'gstrAdditional.dueDate',
  //         createdType: 'gstrAdditional.createdType',
  //       };
  //       const column = columnMap[sort.column] || sort.column;
  //       additionalRecords.orderBy(column, sort.direction.toUpperCase());
  //     }
  //     if (search) {
  //       additionalRecords.andWhere(
  //         new Brackets((qb) => {
  //           qb.where('gstrAdditional.gstIn LIKE :gstsearch', {
  //             gstsearch: `%${search}%`,
  //           });
  //           qb.orWhere('client.displayName LIKE :namesearch', {
  //             namesearch: `%${search}%`,
  //           });
  //         }),
  //       );
  //     }

  //     if (ViewAssigned && !ViewAll) {
  //       additionalRecords.andWhere('clientManagers.id = :userId', { userId });
  //     }

  //     if (financialYear) {
  //       if (financialYear === 'NA') {
  //         additionalRecords.andWhere('gstrAdditional.fy is null');
  //       } else {
  //         additionalRecords.andWhere('gstrAdditional.fy = :financialYr', {
  //           financialYr: financialYear,
  //         });
  //       }
  //     }

  //     if (folderType) {
  //       additionalRecords.andWhere('gstrAdditional.caseFolderTypeName = :folderType', {
  //         folderType: folderType,
  //       });
  //     }

  //     if (type) {
  //       additionalRecords.andWhere('gstrAdditional.caseTypeName like :search', {
  //         search: `${type}`,
  //       });
  //     }

  //     if (interval) {
  //       const now = new Date();
  //       if(interval === 'today'){
  //         const today = moment(new Date()).format('YYYY-MM-DD');
  //         additionalRecords.andWhere(
  //           'STR_TO_DATE(gstrAdditional.categoryDate, "%d/%m/%Y") = :today',{today}
  //         )
  //       } else if (interval === 'last15days') {
  //         const last15days = new Date(now.getTime() - 15 * 24 * 60 * 60 * 1000)
  //           .toISOString()
  //           .split('T')[0];
  //           console.log('last15days',last15days)
  //         additionalRecords.andWhere(
  //           'STR_TO_DATE(gstrAdditional.categoryDate, "%d/%m/%Y") BETWEEN :last15days AND CURDATE()',
  //           {
  //             last15days,
  //           },
  //         );
  //       } else if (interval === 'last1month') {
  //         const now = new Date();
  //         const last1month = new Date(now.setMonth(now.getMonth() - 1)).toISOString().split('T')[0];
  //         // const last1month = new Date(now.getFullYear(), now.getMonth() - 1, now.getDate()).toISOString().split('T')[0];
  //         additionalRecords.andWhere(
  //           'STR_TO_DATE(gstrAdditional.categoryDate,"%d/%m/%Y") BETWEEN  :last1month AND CURDATE() ',
  //           {
  //             last1month,
  //           },
  //         );
  //       } else if (interval === 'last1week') {
  //         const last1week = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
  //           .toISOString()
  //           .split('T')[0];
  //         additionalRecords.andWhere(
  //           'STR_TO_DATE(gstrAdditional.categoryDate,"%d/%m/%Y") BETWEEN :last1week AND CURDATE()',
  //           {
  //             last1week,
  //           },
  //         );
  //       }
  //     }

  //     if (dueInterval) {
  //       const now = new Date();
  //       const interval = dueInterval;
  //       if(interval === 'today'){
  //         const today = moment(new Date()).format("YYYY-MM-DD");
  //         additionalRecords.andWhere(
  //           'STR_TO_DATE(gstrAdditional.dueDate, "%d/%m/%Y") = :today',{today}
  //         );
  //       } else if (interval === 'next15days') {
  //         const next15days = new Date(now.getTime() + 15 * 24 * 60 * 60 * 1000);
  //         additionalRecords.andWhere(
  //           'STR_TO_DATE(gstrAdditional.dueDate, "%d/%m/%Y") BETWEEN CURDATE() AND :next15days',
  //           { next15days },
  //         );
  //       } else if (interval === 'next1month') {
  //         const next1month = new Date(now.getFullYear(), now.getMonth() + 1, now.getDate());
  //         additionalRecords.andWhere(
  //           'STR_TO_DATE(gstrAdditional.dueDate, "%d/%m/%Y") BETWEEN CURDATE() AND :next1month',
  //           { next1month },
  //         );
  //       } else if (interval === 'next1week') {
  //         const next1week = new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000);
  //         additionalRecords.andWhere(
  //           'STR_TO_DATE(gstrAdditional.dueDate, "%d/%m/%Y") BETWEEN CURDATE() AND :next1week',
  //           { next1week },
  //         );
  //       }
  //     }

  //     if (responseType) {
  //       additionalRecords.andWhere('gstrAdditional.refStatus = :responseType', {
  //         responseType
  //       });
  //     }

  //     if (caseType) {
  //       additionalRecords.andWhere('gstrAdditional.caseStatus = :caseType', {
  //         caseType
  //       });
  //     }

  //     if(uniqueType){
  //       additionalRecords.andWhere('gstrAdditional.refNum IS NOT NULL')
  //       .andWhere('(gstrAdditional.createdType != :createdType OR gstrAdditional.createdType IS NULL)', {
  //         createdType: CreatedType.MANUAL,
  //       }).groupBy('gstrAdditional.refNum');   
  // //        const subQuery = createQueryBuilder(GstrAdditionalNoticeOrders, 'sub')
  // //   .select('MIN(sub.id)', 'minId')
  // //   .where('sub.organizationId = :orgId', { orgId: user.organization.id })
  // //   .andWhere('sub.refNum IS NOT NULL')
  // //   .andWhere('(sub.createdType != :createdType OR sub.createdType IS NULL)', { createdType: CreatedType.MANUAL })
  // //   .groupBy('sub.refNum');

  // // // Step 2: Filter main query to only these IDs
  // // additionalRecords.andWhere(`gstrAdditional.id IN (${subQuery.getQuery()})`)
  // //   .setParameters(subQuery.getParameters());
  //     }

  //     if (offset >= 0) {
  //       additionalRecords.skip(offset);
  //     }

  //     if (limit) {
  //       additionalRecords.take(limit);
  //     }

  //     let result = await additionalRecords.getManyAndCount();
  //     return {
  //       count: result[1],
  //       result: result[0],
  //     };
  //   } catch (error) {
  //     console.log('error accure while getting getAddNoticeAndOrders', error);
  //   }
  // }
  async getAddNoticeAndOrders(userId: number, query: any) {
  try {
    const {
      offset,
      limit,
      search,
      financialYear,
      folderType,
      type,
      interval,
      caseType,
      responseType,
      dueInterval,
      uniqueType,
    } = query;

    const user = await User.findOne({
      where: { id: userId },
      relations: ['organization', 'role'],
    });

    let ViewAll = user.role.permissions.find(
      (permission) => permission.slug === Permissions.VIEW_ALL_CLIENT_MANAGERS,
    );
    let ViewAssigned = user.role.permissions.find(
      (permission) => permission.slug === Permissions.VIEW_ASSIGNED_CLIENT_MANAGERS,
    );

    // Helper to apply the same filtering clauses to any QueryBuilder instance.
    // Note: this helper will NOT apply pagination (skip/take) or final ordering.
    const applyFiltersToBuilder = (qb: any) => {
      qb.where('gstrAdditional.organizationId = :id', { id: user?.organization?.id })
        .andWhere('client.status != :status', { status: UserStatus.DELETED })
        .andWhere(
          new Brackets((subQb: any) => {
            subQb
              .where('gstrCredentials.status != :disStatus', { disStatus: GstrStatus.DISABLE })
              .orWhere('gstrCredentials.status IS NULL');
          }),
        )
        .andWhere('gstrAdditional.caseFolderTypeName NOT IN (:...typeName)', {
          typeName: ['REPLIES', 'APPLICATIONS'],
        });

      // search
      if (search) {
        qb.andWhere(
          new Brackets((subQb: any) => {
            subQb.where('gstrAdditional.gstIn LIKE :gstsearch', {
              gstsearch: `%${search}%`,
            });
            subQb.orWhere('client.displayName LIKE :namesearch', {
              namesearch: `%${search}%`,
            });
          }),
        );
      }

      // ViewAssigned permission restriction
      if (ViewAssigned && !ViewAll) {
        qb.andWhere('clientManagers.id = :userId', { userId });
      }

      // financialYear
      if (financialYear) {
        if (financialYear === 'NA') {
          qb.andWhere('gstrAdditional.fy is null');
        } else {
          qb.andWhere('gstrAdditional.fy = :financialYr', {
            financialYr: financialYear,
          });
        }
      }

      // folderType
      if (folderType) {
        qb.andWhere('gstrAdditional.caseFolderTypeName = :folderType', {
          folderType: folderType,
        });
      }

      // case type name
      if (type) {
        qb.andWhere('gstrAdditional.caseTypeName like :searchType', {
          searchType: type,
        });
      }

      // interval filters (categoryDate)
      if (interval) {
        const now = new Date();
        if (interval === 'today') {
          const today = moment(new Date()).format('YYYY-MM-DD');
          qb.andWhere('STR_TO_DATE(gstrAdditional.categoryDate, "%d/%m/%Y") = :today', { today });
        } else if (interval === 'last15days') {
          const last15days = new Date(now.getTime() - 15 * 24 * 60 * 60 * 1000)
            .toISOString()
            .split('T')[0];
          qb.andWhere(
            'STR_TO_DATE(gstrAdditional.categoryDate, "%d/%m/%Y") BETWEEN :last15days AND CURDATE()',
            { last15days },
          );
        } else if (interval === 'last1month') {
          const last1month = new Date(now.setMonth(now.getMonth() - 1)).toISOString().split('T')[0];
          qb.andWhere(
            'STR_TO_DATE(gstrAdditional.categoryDate,"%d/%m/%Y") BETWEEN  :last1month AND CURDATE() ',
            { last1month },
          );
        } else if (interval === 'last1week') {
          const last1week = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
            .toISOString()
            .split('T')[0];
          qb.andWhere(
            'STR_TO_DATE(gstrAdditional.categoryDate,"%d/%m/%Y") BETWEEN :last1week AND CURDATE()',
            { last1week },
          );
        }
      }

      // dueInterval filters (dueDate)
      if (dueInterval) {
        const now = new Date();
        const dueI = dueInterval;
        if (dueI === 'today') {
          const today = moment(new Date()).format('YYYY-MM-DD');
          qb.andWhere('STR_TO_DATE(gstrAdditional.dueDate, "%d/%m/%Y") = :today', { today });
        } else if (dueI === 'next15days') {
          const next15days = new Date(now.getTime() + 15 * 24 * 60 * 60 * 1000);
          qb.andWhere(
            'STR_TO_DATE(gstrAdditional.dueDate, "%d/%m/%Y") BETWEEN CURDATE() AND :next15days',
            { next15days },
          );
        } else if (dueI === 'next1month') {
          const next1month = new Date(now.getFullYear(), now.getMonth() + 1, now.getDate());
          qb.andWhere(
            'STR_TO_DATE(gstrAdditional.dueDate, "%d/%m/%Y") BETWEEN CURDATE() AND :next1month',
            { next1month },
          );
        } else if (dueI === 'next1week') {
          const next1week = new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000);
          qb.andWhere(
            'STR_TO_DATE(gstrAdditional.dueDate, "%d/%m/%Y") BETWEEN CURDATE() AND :next1week',
            { next1week },
          );
        }
      }

      // responseType
      if (responseType) {
        qb.andWhere('gstrAdditional.refStatus = :responseType', {
          responseType,
        });
      }

      // caseType (caseStatus)
      if (caseType) {
        qb.andWhere('gstrAdditional.caseStatus = :caseType', {
          caseType,
        });
      }

      return qb;
    };

    // Main query builder (this will be the query returning the full rows)
    const additionalRecords = createQueryBuilder(GstrAdditionalNoticeOrders, 'gstrAdditional')
      .leftJoinAndSelect('gstrAdditional.client', 'client')
      .leftJoin('client.clientManagers', 'clientManagers')
      .leftJoinAndSelect('client.gstrCredentials', 'gstrCredentials')
      .select([
        'gstrAdditional.id',
        'client.displayName',
        'gstrAdditional.gstIn',
        'gstrAdditional.name',
        'gstrAdditional.caseTypeName',
        'gstrAdditional.refId',
        'gstrAdditional.gstrCredentialsId',
        'gstrAdditional.fy',
        'gstrAdditional.caseFolderTypeName',
        'gstrAdditional.categoryDate',
        'gstrAdditional.dueDate',
        'gstrAdditional.refNum',
        'gstrAdditional.categoryType',
        'gstrAdditional.caseStatus',
        'gstrAdditional.refStatus',
        'gstrAdditional.description',
        'gstrAdditional.createdType',
        'clientManagers.id',
      ]);

    // apply same filters to the main builder
    applyFiltersToBuilder(additionalRecords);

    // computed column (issueDateOrder) and default ordering
    additionalRecords.addSelect(
      `COALESCE(
          STR_TO_DATE(NULLIF(gstrAdditional.categoryDate, 'NA'), '%d/%m/%Y'),
          '0000-01-01'
        )`,
      'issueDateOrder',
    );
    additionalRecords.addOrderBy('issueDateOrder', 'DESC');

    // handle sorting param (if present) - will override the default ordering set above
    const sort = typeof query?.sort === 'string' ? JSON.parse(query.sort) : query?.sort;
    if (sort?.column) {
      const columnMap: Record<string, string> = {
        name: 'client.displayName',
        fy: 'gstrAdditional.fy',
        casetype: 'gstrAdditional.caseTypeName',
        folder: 'gstrAdditional.caseFolderTypeName',
        type: 'gstrAdditional.categoryType',
        categoryDate: 'gstrAdditional.categoryDate',
        dueDate: 'gstrAdditional.dueDate',
        createdType: 'gstrAdditional.createdType',
      };
      const column = columnMap[sort.column] || sort.column;
      additionalRecords.orderBy(column, sort.direction.toUpperCase());
    }

    // If uniqueType is requested, compute the "latest id per refNum" using a filtered subquery,
    // then filter the main builder to only those ids.
    if (uniqueType) {
      // Build a subquery that applies the same filters, selects MAX(id) and groups by refNum.
      const subQb = createQueryBuilder(GstrAdditionalNoticeOrders, 'gstrAdditional')
        .leftJoin('gstrAdditional.client', 'client')
        .leftJoin('client.clientManagers', 'clientManagers')
        .leftJoin('client.gstrCredentials', 'gstrCredentials')
        .select('MAX(gstrAdditional.id)', 'id')
        .groupBy('gstrAdditional.refNum');

      // apply the exact same filters (without pagination or ordering) to the subquery
      applyFiltersToBuilder(subQb);
       subQb.andWhere(
        '(gstrAdditional.createdType != :createdType OR gstrAdditional.createdType IS NULL)',
        { createdType: CreatedType.MANUAL },
      ).andWhere('gstrAdditional.refNum IS NOT NULL');

      // execute subquery to get ids
      const rawIds = await subQb.getRawMany(); // returns [{ id: <maxId> }, ...]
      const ids = rawIds.map((r: any) => r.id).filter(Boolean);

      // if no ids found, return empty immediately
      if (!ids.length) {
        return { count: 0, result: [] };
      }

      // restrict the main query to those ids
      additionalRecords.andWhere('gstrAdditional.id IN (:...uniqueIds)', { uniqueIds: ids });
    }

    // pagination
    if (offset >= 0) {
      additionalRecords.skip(offset);
    }
    if (limit) {
      additionalRecords.take(limit);
    }

    // execute
    const result = await additionalRecords.getManyAndCount();
    return {
      count: result[1],
      result: result[0],
    };
  } catch (error) {
    console.log('error accure while getting getAddNoticeAndOrders', error);
  }
}


  async exportAdditionalGstNoticeAndOrders(userId: number, query: any) {
    const exportQuery = { ...query, offset: 0, limit: 100000000 };
    let gstNoticeorders = await this.getAddNoticeAndOrders(userId, exportQuery);
    if (!gstNoticeorders.result.length) throw new BadRequestException('No Data for Export');
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('GST Add. Notice & Orders');
    const headers = [
      { header: 'S.No', key: 'serialNo' },
      { header: 'Client Name', key: 'clientName' },
      { header: 'GSTIN', key: 'gstIn' },
      { header: 'FY', key: 'fy' },
      { header: 'Folder', key: 'folder' },
      { header: 'Date of Issuance', key: 'issueDate' },

      { header: 'Case Type', key: 'caseType' },
      { header: 'Type', key: 'type' },
      { header: 'Reference ID', key: 'referenceId' },
      { header: 'Due Date', key: 'dueDate' },
      { header: 'Response Status', key: 'responseStatus' },
      { header: 'Case Status', key: 'caseStatus' },
    ];

    worksheet.columns = headers;
    const columnMaxLengths = Array(headers.length).fill(0);
    let serialCounter = 1; // Initialize a counter
    if (!gstNoticeorders.result.length) throw new BadRequestException('No Data for Export');
    gstNoticeorders.result.forEach((noticeorder) => {
      const {
        client: { displayName: clientName },
      } = noticeorder;
      const rowData = {
        serialNo: serialCounter++, // Assign and then increment the counter
        fy: noticeorder?.fy,

        clientName: clientName,
        gstIn: noticeorder?.gstIn,
        caseType: noticeorder?.caseTypeName,
        type: noticeorder?.categoryType,
        folder: noticeorder?.caseFolderTypeName,
        noticeName: noticeorder?.name,
        referenceId: noticeorder?.refNum,
        issueDate: noticeorder?.categoryDate,
        dueDate: noticeorder?.dueDate,
        responseStatus: noticeorder?.refStatus,
        caseStatus: noticeorder?.caseStatus,
      };

      const row = worksheet.addRow(rowData);
      const responseStatusCell = row.getCell('responseStatus'); // Get the cell for the "Type" column
      if (rowData.responseStatus === 'REPLIED') {
        responseStatusCell.font = {
          color: { argb: 'FF800080' }, // Purple
          bold: true, // Bold text
        };
        responseStatusCell.value = 'Replied'; // Add text
      } else if (rowData.responseStatus === 'NOT_REPLIED') {
        responseStatusCell.font = {
          color: { argb: 'FF008080' }, // Teal
          bold: true, // Bold text
        };
        responseStatusCell.value = 'Not Replied'; // Add text
      } else if (rowData.responseStatus === 'NA') {
        responseStatusCell.font = {
          color: { argb: 'FF000000' }, // Default black color
          bold: true, // Bold text
        };
        responseStatusCell.value = 'NA'; // Default text
      }

      const caseStatusCell = row.getCell('caseStatus'); // Get the cell for the "Type" column

      if (rowData.caseStatus === 'OPEN') {
        caseStatusCell.font = {
          color: { argb: 'FF800000' }, // ARGB for Maroon
          bold: true, // Bold text
        };
        caseStatusCell.value = 'Open'; // Add text
      } else if (rowData.caseStatus === 'CLOSED') {
        caseStatusCell.font = {
          color: { argb: 'FF008000' }, // ARGB for Green
          bold: true, // Bold text
        };
        caseStatusCell.value = 'Closed'; // Add text
      }

      worksheet.columns.forEach((column, colIndex) => {
        const headerLength = column.header?.length || 0;
        const cellLength = rowData[column.key]?.toString().length || 0;
        columnMaxLengths[colIndex] = Math.max(
          columnMaxLengths[colIndex] || 0,
          headerLength,
          cellLength,
        );
      });
    });

    worksheet.columns.forEach((column, colIndex) => {
      column.width = columnMaxLengths[colIndex] + 2;
    });

    const headerRow = worksheet.getRow(1);
    headerRow.font = { bold: true };
    headerRow.eachCell((cell) => {
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '64B5F6' },
      };
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' },
      };
      cell.alignment = { horizontal: 'center', vertical: 'middle' };
    });

    worksheet.columns.forEach((column) => {
      if (column.key === 'clientName') {
        column.width = 50;
        column.alignment = { wrapText: true, horizontal: 'center', vertical: 'middle' };
      } else {
        column.alignment = { horizontal: 'center', vertical: 'middle' };
      }
    });

    worksheet.eachRow((row) => {
      row.eachCell((cell) => {
        cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
      });
    });

    worksheet.views = [{ state: 'frozen', ySplit: 1 }];

    const buffer = await workbook.xlsx.writeBuffer();
    return buffer;
  }

  async getNoticeAndOrders(userId: number, query: any) {
    try {
      const { offset, limit, search, issuedBy, interval, dueInterval, type } = query;

      const user = await User.findOne({
        where: { id: userId },
        relations: ['organization', 'role'],
      });

      let ViewAll = user.role.permissions.find(
        (permission) => permission.slug === Permissions.VIEW_ALL_CLIENT_MANAGERS,
      );
      let ViewAssigned = user.role.permissions.find(
        (permission) => permission.slug === Permissions.VIEW_ASSIGNED_CLIENT_MANAGERS,
      );

      const noticeAndOrders = createQueryBuilder(GstrNoticeOrders, 'gstrNoticeOrders')
        .leftJoinAndSelect('gstrNoticeOrders.client', 'client')
        .leftJoin('client.clientManagers', 'clientManagers')
        .leftJoinAndSelect('client.gstrCredentials', 'gstrCredentials')
        .where('gstrNoticeOrders.organizationId = :id', { id: user?.organization?.id })
        .andWhere('client.status != :status', { status: UserStatus.DELETED })
        .andWhere('gstrCredentials.status != :disStatus', { disStatus: GstrStatus.DISABLE });

      noticeAndOrders.addSelect(
        `STR_TO_DATE(NULLIF(gstrNoticeOrders.dateOfIssuance, 'NA'), '%d/%m/%Y')`,
        'issueDateOrder',
      );
      noticeAndOrders.addOrderBy('issueDateOrder', 'DESC');

      const sort = typeof query?.sort === 'string' ? JSON.parse(query.sort) : query?.sort;
      if (sort?.column) {
        const columnMap: Record<string, string> = {
          name: 'client.displayName',
          type: 'gstrNoticeOrders.type',
          issuedBy: 'gstrNoticeOrders.issuedBy',
          amountOfDemand: 'gstrNoticeOrders.amountOfDemand',
          dateOfIssuance: 'gstrNoticeOrders.dateOfIssuance',
          dueDate: 'gstrNoticeOrders.dueDate',
        };
        const column = columnMap[sort.column] || sort.column;
        noticeAndOrders.orderBy(column, sort.direction.toUpperCase());
      }
      if (search) {
        noticeAndOrders.andWhere(
          new Brackets((qb) => {
            // qb.where('gstrNoticeOrders.pan LIKE :pansearch', {
            //   pansearch: `%${query.search}%`,
            // });
            qb.orWhere('client.displayName LIKE :namesearch', {
              namesearch: `%${search}%`,
            });
            qb.orWhere('gstrNoticeOrders.gstIn LIKE :gstsearch', {
              gstsearch: `%${search}%`,
            });
          }),
        );
      }

      if (ViewAssigned && !ViewAll) {
        noticeAndOrders.andWhere('clientManagers.id = :userId', { userId });
      }

      if (type) {
        noticeAndOrders.andWhere('gstrNoticeOrders.type like :search', {
          search: type,
        });
      }

      if (issuedBy) {
        noticeAndOrders.andWhere('gstrNoticeOrders.issuedBy like :search', {
          search: `%${issuedBy}%`,
        });
      }

      if (interval) {
        const now = new Date();
        if(interval === 'today'){
          const today = moment(new Date()).format('YYYY-MM-DD');
          noticeAndOrders.andWhere(
            'STR_TO_DATE(gstrNoticeOrders.dateOfIssuance, "%d/%m/%Y") = :today',{today}
          );
        } else if (interval === 'last15days') {
          const last15days = new Date(now.getTime() - 15 * 24 * 60 * 60 * 1000);
          noticeAndOrders.andWhere(
            'STR_TO_DATE(gstrNoticeOrders.dateOfIssuance, "%d/%m/%Y") >= :last15days',
            { last15days },
          );
        } else if (interval === 'last1month') {
          const last1month = new Date(now.getFullYear(), now.getMonth() - 1, now.getDate());
          noticeAndOrders.andWhere(
            'STR_TO_DATE(gstrNoticeOrders.dateOfIssuance, "%d/%m/%Y") >= :last1month',
            { last1month },
          );
        } else if (interval === 'last1week') {
          const last1week = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
          noticeAndOrders.andWhere(
            'STR_TO_DATE(gstrNoticeOrders.dateOfIssuance, "%d/%m/%Y") >= :last1week',
            { last1week },
          );
        }
      }

      if (dueInterval) {
        const now = new Date();
        const interval = dueInterval;
        if(interval === 'today'){
          const today = moment(new Date()).format("YYYY-MM-DD");
          noticeAndOrders.andWhere(
            'STR_TO_DATE(gstrNoticeOrders.dueDate, "%d/%m/%Y") = :today',{today}
          );
        } else if (interval === 'next15days') {
          const next15days = new Date(now.getTime() + 15 * 24 * 60 * 60 * 1000);
          noticeAndOrders.andWhere(
            'STR_TO_DATE(gstrNoticeOrders.dueDate, "%d/%m/%Y") BETWEEN CURDATE() AND :next15days',
            { next15days },
          );
        } else if (interval === 'next1month') {
          const next1month = new Date(now.getFullYear(), now.getMonth() + 1, now.getDate());
          noticeAndOrders.andWhere(
            'STR_TO_DATE(gstrNoticeOrders.dueDate, "%d/%m/%Y") BETWEEN CURDATE() AND :next1month',
            { next1month },
          );
        } else if (interval === 'next1week') {
          const next1week = new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000);
          noticeAndOrders.andWhere(
            'STR_TO_DATE(gstrNoticeOrders.dueDate, "%d/%m/%Y") BETWEEN CURDATE() AND :next1week',
            { next1week },
          );
        }
      }

      if (offset >= 0) {
        noticeAndOrders.skip(offset);
      }

      if (limit) {
        noticeAndOrders.take(limit);
      }

      let result = await noticeAndOrders.getManyAndCount();
      return {
        count: result[1],
        result: result[0],
      };
    } catch (error) {
      console.log('error accure while getting getNoticeAndOrders', error);
    }
  }

  async exportGstNoticeAndOrders(userId: number, query: any) {
    const exportQuery = { ...query, offset: 0, limit: 100000000 };
    let gstNoticeorders = await this.getNoticeAndOrders(userId, exportQuery);
    if (!gstNoticeorders.result.length) throw new BadRequestException('No Data for Export');
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('GST Notice & Orders');
    const headers = [
      { header: 'S.No', key: 'serialNo' },
      { header: 'Client Name', key: 'clientName' },
      { header: 'GSTIN', key: 'gstIn' },
      { header: 'Type', key: 'type' },
      { header: 'Date of Issuance', key: 'issuanceDate' },
      { header: 'Issued by', key: 'issuedBy' },

      { header: 'Notice/Order #', key: 'noticeNum' },
      { header: 'Due Date', key: 'dueDate' },
      { header: 'Amount (₹)', key: 'amount' },
    ];

    worksheet.columns = headers;
    const columnMaxLengths = Array(headers.length).fill(0);
    let serialCounter = 1; // Initialize a counter
    if (!gstNoticeorders.result.length) throw new BadRequestException('No Data for Export');
    gstNoticeorders.result.forEach((noticeorder) => {
      const amount = noticeorder?.amountOfDemand === ' ' ? 0 : noticeorder?.amountOfDemand;

      const rowData = {
        serialNo: serialCounter++, // Assign and then increment the counter
        clientName: noticeorder?.client?.displayName,
        gstIn: noticeorder?.gstIn,
        issuanceDate: noticeorder?.dateOfIssuance,
        issuedBy: noticeorder?.issuedBy,
        type: noticeorder?.type,
        noticeNum: noticeorder?.orderNumber,
        issuedDate: noticeorder?.dateOfIssuance,
        dueDate: noticeorder?.dueDate,
        amount: noticeorder?.amountOfDemand,
      };

      const row = worksheet.addRow(rowData);

      worksheet.columns.forEach((column, colIndex) => {
        const headerLength = column.header?.length || 0;
        const cellLength = rowData[column.key]?.toString().length || 0;
        columnMaxLengths[colIndex] = Math.max(
          columnMaxLengths[colIndex] || 0,
          headerLength,
          cellLength,
        );
      });
    });

    worksheet.columns.forEach((column, colIndex) => {
      column.width = columnMaxLengths[colIndex] + 2;
    });

    const headerRow = worksheet.getRow(1);
    headerRow.font = { bold: true };
    headerRow.eachCell((cell) => {
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '64B5F6' },
      };
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' },
      };
      cell.alignment = { horizontal: 'center', vertical: 'middle' };
    });

    worksheet.columns.forEach((column) => {
      if (column.key === 'clientName') {
        column.width = 50;
        column.alignment = { wrapText: true, horizontal: 'center', vertical: 'middle' };
      } else {
        column.alignment = { horizontal: 'center', vertical: 'middle' };
      }
    });

    worksheet.eachRow((row) => {
      row.eachCell((cell) => {
        cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
      });
    });

    worksheet.views = [{ state: 'frozen', ySplit: 1 }];

    const buffer = await workbook.xlsx.writeBuffer();
    return buffer;
  }
  async getGstrReport(userId: number, query: any) {
    try {
      const { limit, offset, status, remarks } = query;
      let user = await User.findOne({
        where: { id: userId },
        relations: ['organization', 'role'],
      });
      let ViewAll = user.role.permissions.find(
        (permission) => permission.slug === Permissions.VIEW_ALL_CLIENT_MANAGERS,
      );
      let ViewAssigned = user.role.permissions.find(
        (permission) => permission.slug === Permissions.VIEW_ASSIGNED_CLIENT_MANAGERS,
      );
      const entityManager = getRepository(AutomationMachines);

      let sql = await entityManager
        .createQueryBuilder('automationMachines')
        .leftJoinAndSelect('automationMachines.gstrCredentials', 'gstrCredentials')
        .leftJoinAndSelect('gstrCredentials.client', 'client')
        // .leftJoin('client.clientManagers', 'clientManagers')
        .where('gstrCredentials.organizationId = :id', { id: user.organization.id })
        .andWhere('automationMachines.type = :type', { type: 'GSTR' })
        .andWhere('client.status != :status', { status: UserStatus.DELETED })
        .andWhere('gstrCredentials.status != :disStatus', { disStatus: GstrStatus.DISABLE });
      const sort = typeof query?.sort === 'string' ? JSON.parse(query.sort) : query?.sort;
      if (sort?.column) {
        const columnMap: Record<string, string> = {
          clientname: 'client.displayName',
          userName: 'gstrCredentials.userName',
          remarks: 'automationMachines.remarks',
          createdAt: 'automationMachines.createdAt',
        };
        const column = columnMap[sort.column] || sort.column;
        sql.orderBy(column, sort.direction.toUpperCase());
      }
      if (status) {
        sql = sql.andWhere('automationMachines.status = :status', { status });
      }

      // if (ViewAssigned && !ViewAll) {
      //   sql.andWhere('clientManagers.id = :userId', { userId });
      // }

      if (remarks) {
        sql = sql.andWhere('automationMachines.remarks = :remarks', { remarks });
      }

      sql = sql
        .andWhere((qb) => {
          const subQuery = qb
            .subQuery()
            .select('MAX(innerAutomationMachines.id)', 'maxId')
            .from(AutomationMachines, 'innerAutomationMachines')
            .leftJoin('innerAutomationMachines.gstrCredentials', 'gstrCredentials')
            .where('gstrCredentials.organizationId = :id', { id: user.organization.id })
            .groupBy('gstrCredentials.id')
            .getQuery();
          return 'automationMachines.id IN ' + subQuery;
        })
        // .orderBy('automationMachines.id', 'DESC')
        .limit(limit)
        .offset(offset);

      const result = await sql.getManyAndCount();
      return {
        data: result[0],
        count: result[1],
      };
    } catch (error) {
      console.log('error accure while getting getGstrReport', error);
    }
  }

  async exportGstClientReport(userId: number, query: any) {
    const newQuery = { ...query, offset: 0, limit: 100000000 };
    let reports = await this.getGstrReport(userId, newQuery);
    if (!reports.data.length) throw new BadRequestException('No Data for Export');
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('GST Sync Status');
    const headers = [
      { header: 'S.No', key: 'serialNo' },
      { header: 'Client Name', key: 'clientName' },
      { header: 'GSTIN', key: 'gst' },
      { header: 'User Name', key: 'userName' },
      { header: 'Password', key: 'password' },
      { header: 'Last Sync', key: 'lastSync' },
      { header: 'Status', key: 'status' },
      { header: 'Remarks', key: 'remarks' },
    ];

    worksheet.columns = headers;
    const columnMaxLengths = Array(headers.length).fill(0);
    let serialCounter = 1; // Initialize a counter
    reports.data.forEach((report) => {
      const formatDateTime1 = (dateString: any) => {
        if (!dateString) return '-'; // Handle cases where date is missing
        const date = new Date(dateString);
        return date.toLocaleString(); // Format to local date and time
      };

      const rowData = {
        serialNo: serialCounter++, // Assign and then increment the counter
        clientName: report?.gstrCredentials?.client?.displayName,
        gst: report?.gstrCredentials?.client?.gstNumber,
        userName: report?.gstrCredentials?.userName,
        password: report?.gstrCredentials?.password,
        lastSync: formatDateTime1(report?.createdAt),
        status: capitalize(report?.status),
        remarks: report?.remarks,
      };

      const row = worksheet.addRow(rowData);

      worksheet.columns.forEach((column, colIndex) => {
        const headerLength = column.header?.length || 0;
        const cellLength = rowData[column.key]?.toString().length || 0;
        columnMaxLengths[colIndex] = Math.max(
          columnMaxLengths[colIndex] || 0,
          headerLength,
          cellLength,
        );
      });
    });

    worksheet.columns.forEach((column, colIndex) => {
      column.width = columnMaxLengths[colIndex] + 3;
    });

    const headerRow = worksheet.getRow(1);
    headerRow.font = { bold: true };
    headerRow.eachCell((cell) => {
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '64B5F6' },
      };
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' },
      };
      cell.alignment = { horizontal: 'center', vertical: 'middle' };
    });

    worksheet.columns.forEach((column) => {
      if (column.key === 'clientName') {
        column.width = 50;
        column.alignment = { wrapText: true, horizontal: 'center', vertical: 'middle' };
      } else {
        column.alignment = { horizontal: 'center', vertical: 'middle' };
      }
    });

    worksheet.eachRow((row) => {
      row.eachCell((cell) => {
        cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
      });
    });

    worksheet.views = [{ state: 'frozen', ySplit: 1 }];

    const buffer = await workbook.xlsx.writeBuffer();
    return buffer;
  }

  async getGstrAdditionalNoticeOrders(userId: number, id: number, query: any) {
    const { offset, limit } = query;
    try {
      const user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
      const checkGstrCredentials = await GstrCredentials.findOne({
        where: { id: id, organizationId: user.organization.id },
      });

      if (checkGstrCredentials) {
        const additionalRecords = createQueryBuilder(
          GstrAdditionalNoticeOrders,
          'gstrAdditionalNoticeOrders',
        )
          .where('gstrAdditionalNoticeOrders.gstrCredentialsId = :id', { id })
          .andWhere('gstrAdditionalNoticeOrders.organizationId = :orgId', {
            orgId: user?.organization?.id,
          })
          .andWhere('gstrAdditionalNoticeOrders.caseFolderTypeName NOT IN (:...typeName)', {
            typeName: ['REPLIES', 'APPLICATIONS'],
          });

        additionalRecords.orderBy(
          `STR_TO_DATE(gstrAdditionalNoticeOrders.categoryDate, '%d/%m/%Y')`,
          'DESC',
        );
        const sort = typeof query?.sort === 'string' ? JSON.parse(query.sort) : query?.sort;
        if (sort?.column) {
          const columnMap: Record<string, string> = {
            fy: 'gstrAdditionalNoticeOrders.fy',
            categoryDate: 'gstrAdditionalNoticeOrders.categoryDate',
            dueDate: 'gstrAdditionalNoticeOrders.dueDate',
            casetype: 'gstrAdditionalNoticeOrders.caseTypeName',
            folder: 'gstrAdditionalNoticeOrders.caseFolderTypeName',
            type: 'gstrAdditionalNoticeOrders.categoryType',
          };
          const column = columnMap[sort.column] || sort.column;
          additionalRecords.orderBy(column, sort.direction.toUpperCase());
        }
        if (offset >= 0) {
          additionalRecords.skip(offset);
        }

        if (limit) {
          additionalRecords.take(limit);
        }

        let result = await additionalRecords.getManyAndCount();
        return {
          count: result[1],
          result: result[0],
          accessDenied: true,
        };
      } else {
        return { accessDenied: false };
      }
    } catch (error) {
      console.log('error accure while getting getGstrAdditionalNoticeOrders', error);
    }
  }

  async exportreferenceBasedNotices(userId: number, query: any) {
    const exportQuery = { ...query, offset: 0, limit: 100000000 };
    const gstrid = query?.gstrid;
    let gstNoticeorders = await this.getGstrAdditionalNoticeOrders(userId, gstrid, exportQuery);
    if (!gstNoticeorders.result.length) throw new BadRequestException('No Data for Export');
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Add.Notice & Orders (Ref ID)');
    const headers = [
      { header: 'S.No', key: 'serialNo' },
      { header: 'FY', key: 'fy' },
      { header: 'Folder', key: 'folder' },
      { header: 'Date of Issuance', key: 'issueDate' },

      { header: 'Case Type', key: 'caseType' },
      { header: 'Type', key: 'type' },
      { header: 'Reference ID', key: 'referenceId' },

      { header: 'Due Date', key: 'dueDate' },
      { header: 'Response Status', key: 'responseStatus' },
      { header: 'Case Status', key: 'caseStatus' },
    ];

    worksheet.columns = headers;
    const columnMaxLengths = Array(headers.length).fill(0);
    let serialCounter = 1; // Initialize a counter
    if (!gstNoticeorders.result.length) throw new BadRequestException('No Data for Export');
    gstNoticeorders.result.forEach((noticeorder) => {
      const rowData = {
        serialNo: serialCounter++, // Assign and then increment the counter
        fy: noticeorder?.fy,
        caseType: noticeorder?.caseTypeName,
        folder: noticeorder?.caseFolderTypeName,
        noticeName: noticeorder?.description,
        type: noticeorder?.categoryType,
        referenceId: noticeorder?.refId,
        issueDate: noticeorder?.categoryDate,
        dueDate: noticeorder?.dueDate,
        responseStatus: noticeorder?.refStatus,
        caseStatus: noticeorder?.caseStatus,
      };

      const row = worksheet.addRow(rowData);
      const responseStatusCell = row.getCell('responseStatus'); // Get the cell for the "Type" column
      if (rowData.responseStatus === 'REPLIED') {
        responseStatusCell.font = {
          color: { argb: 'FF800080' }, // Purple
          bold: true, // Bold text
        };
        responseStatusCell.value = 'Replied'; // Add text
      } else if (rowData.responseStatus === 'NOT_REPLIED') {
        responseStatusCell.font = {
          color: { argb: 'FF008080' }, // Teal
          bold: true, // Bold text
        };
        responseStatusCell.value = 'Not Replied'; // Add text
      } else if (rowData.responseStatus === 'NA') {
        responseStatusCell.font = {
          color: { argb: 'FF000000' }, // Default black color
          bold: true, // Bold text
        };
        responseStatusCell.value = 'NA'; // Default text
      }

      const caseStatusCell = row.getCell('caseStatus'); // Get the cell for the "Type" column

      if (rowData.caseStatus === 'OPEN') {
        caseStatusCell.font = {
          color: { argb: 'FF800000' }, // ARGB for Maroon
          bold: true, // Bold text
        };
        caseStatusCell.value = 'Open'; // Add text
      } else if (rowData.caseStatus === 'CLOSED') {
        caseStatusCell.font = {
          color: { argb: 'FF008000' }, // ARGB for Green
          bold: true, // Bold text
        };
        caseStatusCell.value = 'Closed'; // Add text
      }

      worksheet.columns.forEach((column, colIndex) => {
        const headerLength = column.header?.length || 0;
        const cellLength = rowData[column.key]?.toString().length || 0;
        columnMaxLengths[colIndex] = Math.max(
          columnMaxLengths[colIndex] || 0,
          headerLength,
          cellLength,
        );
      });
    });

    worksheet.columns.forEach((column, colIndex) => {
      column.width = columnMaxLengths[colIndex] + 2;
    });

    const headerRow = worksheet.getRow(1);
    headerRow.font = { bold: true };
    headerRow.eachCell((cell) => {
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '64B5F6' },
      };
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' },
      };
      cell.alignment = { horizontal: 'center', vertical: 'middle' };
    });

    worksheet.columns.forEach((column) => {
      if (column.key === 'clientName') {
        column.width = 50;
        column.alignment = { wrapText: true, horizontal: 'center', vertical: 'middle' };
      } else {
        column.alignment = { horizontal: 'center', vertical: 'middle' };
      }
    });

    worksheet.eachRow((row) => {
      row.eachCell((cell) => {
        cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
      });
    });

    worksheet.views = [{ state: 'frozen', ySplit: 1 }];

    const buffer = await workbook.xlsx.writeBuffer();
    return buffer;
  }

  async getNoticeAndOrderDueDateEvents(userId: number, query: Date) {
    let user = await User.findOne(userId, { relations: ['organization', 'role'] });

    let ViewAll = user.role.permissions.find(
      (permission) => permission.slug === Permissions.VIEW_ALL_CLIENT_MANAGERS,
    );
    let ViewAssigned = user.role.permissions.find(
      (permission) => permission.slug === Permissions.VIEW_ASSIGNED_CLIENT_MANAGERS,
    );

    let gstrDueDate = createQueryBuilder(GstrNoticeOrders, 'gstrNoticeOrders')
      .leftJoinAndSelect('gstrNoticeOrders.client', 'client')
      .leftJoin('client.clientManagers', 'clientManagers')
      .leftJoin('client.gstrCredentials', 'gstrCredentials')
      .where('gstrNoticeOrders.organizationId = :organizationId', {
        organizationId: user?.organization?.id,
      })
      .andWhere('client.status != :status', { status: UserStatus.DELETED })
      .andWhere('gstrCredentials.status != :disStatus', {
        disStatus: GstrStatus.DISABLE,
      });
    if (query) {
      const startOfMonth = moment(query).startOf('month').format('DD/MM/YYYY');
      const endOfMonth = moment(query).endOf('month').format('DD/MM/YYYY');
      gstrDueDate.andWhere(
        `(gstrNoticeOrders.dueDate IS NOT NULL AND gstrNoticeOrders.dueDate != 'NA' AND STR_TO_DATE(gstrNoticeOrders.dueDate, '%d/%m/%Y') BETWEEN STR_TO_DATE(:startOfMonth, '%d/%m/%Y') AND STR_TO_DATE(:endOfMonth, '%d/%m/%Y'))`,
        { startOfMonth, endOfMonth },
      );
    }
    if (ViewAssigned && !ViewAll) {
      gstrDueDate.andWhere('clientManagers.id = :userId', { userId });
    }
    const result2 = await gstrDueDate.getMany();
    return result2;
  }

  async getNoticeAndOrderIssueDateEvents(userId: number, query: Date) {
    let user = await User.findOne(userId, { relations: ['organization', 'role'] });

    let ViewAll = user.role.permissions.find(
      (permission) => permission.slug === Permissions.VIEW_ALL_CLIENT_MANAGERS,
    );
    let ViewAssigned = user.role.permissions.find(
      (permission) => permission.slug === Permissions.VIEW_ASSIGNED_CLIENT_MANAGERS,
    );

    let gstrIssueDate = createQueryBuilder(GstrNoticeOrders, 'gstrNoticeOrders')
      .leftJoinAndSelect('gstrNoticeOrders.client', 'client')
      .leftJoin('client.clientManagers', 'clientManagers')
      .leftJoin('client.gstrCredentials', 'gstrCredentials')
      .where('gstrNoticeOrders.organizationId = :organizationId', {
        organizationId: user?.organization?.id,
      })
      .andWhere('client.status != :status', { status: UserStatus.DELETED })
      .andWhere('gstrCredentials.status != :disStatus', {
        disStatus: GstrStatus.DISABLE,
      });
    if (query) {
      const startOfMonth = moment(query).startOf('month').format('DD/MM/YYYY');
      const endOfMonth = moment(query).endOf('month').format('DD/MM/YYYY');
      gstrIssueDate.andWhere(
        `(gstrNoticeOrders.dateOfIssuance IS NOT NULL AND gstrNoticeOrders.dateOfIssuance != 'NA' AND STR_TO_DATE(gstrNoticeOrders.dateOfIssuance, '%d/%m/%Y') BETWEEN STR_TO_DATE(:startOfMonth, '%d/%m/%Y') AND STR_TO_DATE(:endOfMonth, '%d/%m/%Y'))`,
        { startOfMonth, endOfMonth },
      );
    }

    if (ViewAssigned && !ViewAll) {
      gstrIssueDate.andWhere('clientManagers.id = :userId', { userId });
    }

    const result2 = await gstrIssueDate.getMany();
    return result2;
  }

  async getAdditionalNoticeOrderIssueDateEvents(userId: number, query: Date) {
    let user = await User.findOne(userId, { relations: ['organization', 'role'] });

    let ViewAll = user.role.permissions.find(
      (permission) => permission.slug === Permissions.VIEW_ALL_CLIENT_MANAGERS,
    );
    let ViewAssigned = user.role.permissions.find(
      (permission) => permission.slug === Permissions.VIEW_ASSIGNED_CLIENT_MANAGERS,
    );

    let gstrIssueDate = createQueryBuilder(GstrAdditionalNoticeOrders, 'gstrAddNoticeOrders')
      .leftJoinAndSelect('gstrAddNoticeOrders.client', 'client')
      .leftJoin('client.clientManagers', 'clientManagers')
      .leftJoin('client.gstrCredentials', 'gstrCredentials')
      .where('gstrAddNoticeOrders.organizationId = :organizationId', {
        organizationId: user?.organization?.id,
      })
      .andWhere('client.status != :status', { status: UserStatus.DELETED })
      .andWhere(
        new Brackets((qb) => {
          qb.where('gstrCredentials.status != :disStatus', {
            disStatus: GstrStatus.DISABLE,
          }).orWhere('gstrCredentials.status IS NULL');
        }),
      );

    if (query) {
      const startOfMonth = moment(query).startOf('month').format('DD/MM/YYYY');
      const endOfMonth = moment(query).endOf('month').format('DD/MM/YYYY');

      gstrIssueDate.andWhere(
        `(gstrAddNoticeOrders.categoryDate IS NOT NULL 
          AND gstrAddNoticeOrders.categoryDate != 'NA' 
          AND STR_TO_DATE(gstrAddNoticeOrders.categoryDate, '%d/%m/%Y') 
            BETWEEN STR_TO_DATE(:startOfMonth, '%d/%m/%Y') 
            AND STR_TO_DATE(:endOfMonth, '%d/%m/%Y'))`,
        { startOfMonth, endOfMonth },
      );
    }

    if (ViewAssigned && !ViewAll) {
      gstrIssueDate.andWhere('clientManagers.id = :userId', { userId });
    }

    const result2 = await gstrIssueDate.getMany();
    return result2;
  }

  async getAdditionalNoticeOrderDueDateEvents(userId: number, query: Date) {
    let user = await User.findOne(userId, { relations: ['organization', 'role'] });

    let ViewAll = user.role.permissions.find(
      (permission) => permission.slug === Permissions.VIEW_ALL_CLIENT_MANAGERS,
    );
    let ViewAssigned = user.role.permissions.find(
      (permission) => permission.slug === Permissions.VIEW_ASSIGNED_CLIENT_MANAGERS,
    );

    let gstrIssueDate = createQueryBuilder(GstrAdditionalNoticeOrders, 'gstrAddNoticeOrders')
      .leftJoinAndSelect('gstrAddNoticeOrders.client', 'client')
      .leftJoin('client.clientManagers', 'clientManagers')
      .leftJoin('client.gstrCredentials', 'gstrCredentials')
      .where('gstrAddNoticeOrders.organizationId = :organizationId', {
        organizationId: user?.organization?.id,
      })
      .andWhere('client.status != :status', { status: UserStatus.DELETED })
      .andWhere(
        new Brackets((qb) => {
          qb.where('gstrCredentials.status != :disStatus', {
            disStatus: GstrStatus.DISABLE,
          }).orWhere('gstrCredentials.status IS NULL');
        }),
      );
    if (query) {
      const startOfMonth = moment(query).startOf('month').format('DD/MM/YYYY');
      const endOfMonth = moment(query).endOf('month').format('DD/MM/YYYY');
      gstrIssueDate.andWhere(
        `(gstrAddNoticeOrders.dueDate IS NOT NULL AND gstrAddNoticeOrders.dueDate != 'NA' AND STR_TO_DATE(gstrAddNoticeOrders.dueDate, '%d/%m/%Y') BETWEEN STR_TO_DATE(:startOfMonth, '%d/%m/%Y') AND STR_TO_DATE(:endOfMonth, '%d/%m/%Y'))`,
        { startOfMonth, endOfMonth },
      );
    }

    if (ViewAssigned && !ViewAll) {
      gstrIssueDate.andWhere('clientManagers.id = :userId', { userId });
    }

    const result2 = await gstrIssueDate.getMany();
    return result2;
  }

  async getGstrUpdates(userId: number, query: any) {
    try {
      let user = await User.findOne({
        where: { id: userId },
        relations: ['organization', 'role'],
      });

      let ViewAll = user.role.permissions.find(
        (permission) => permission.slug === Permissions.VIEW_ALL_CLIENT_MANAGERS,
      );
      let ViewAssigned = user.role.permissions.find(
        (permission) => permission.slug === Permissions.VIEW_ASSIGNED_CLIENT_MANAGERS,
      );

      let gstrUpdateTracker = createQueryBuilder(GstrUpdateTracker, 'gstrUpdateTracker')
        .leftJoinAndSelect('gstrUpdateTracker.client', 'client')
        .leftJoin('client.clientManagers', 'clientManagers')
        .leftJoinAndSelect('client.gstrCredentials', 'gstrCredentials')
        .where('gstrUpdateTracker.organizationId = :organizationId', {
          organizationId: user?.organization?.id,
        })
        .andWhere('gstrUpdateTracker.isChange = :isChange', { isChange: true })
        .andWhere('client.status != :status', { status: UserStatus.DELETED })
        .andWhere('gstrCredentials.status != :disStatus', {
          disStatus: GstrStatus.DISABLE,
        });

      if (ViewAssigned && !ViewAll) {
        gstrUpdateTracker.andWhere('clientManagers.id = :userId', { userId });
      }

      const sort = typeof query?.sort === 'string' ? JSON.parse(query.sort) : query?.sort;
      if (sort?.column) {
        const columnMap: Record<string, string> = {
          displayName: 'client.displayName',
        };
        const column = columnMap[sort.column] || sort.column;
        gstrUpdateTracker.orderBy(column, sort.direction.toUpperCase());
      }
      const result = await gstrUpdateTracker.getMany();
      // const autUpdates = await AutUpdateTracker.find({
      //   where: { organizationId: user?.organization?.id, isChange: true },
      //   relations: ['client'],
      // });
      return result;
    } catch (error) {
      console.log('error occur while getting  getIncometexUpdates', error);
    }
  }

  async findGstrUpdateItem(userId: any, id: number) {
    try {
      const user = await User.findOne({ where: { id: userId }, relations: ['organization'] });

      let updateTracker = getConnection()
        .createQueryBuilder(GstrUpdateTracker, 'gstrUpdateTracker')
        .leftJoinAndSelect('gstrUpdateTracker.client', 'client')
        .leftJoin('client.organization', 'organization')
        .where('gstrUpdateTracker.id = :id', { id: id })
        .andWhere('organization.id = :organization', { organization: user.organization.id })
        .getOne();

      // const updateTracker = await AutUpdateTracker.findOne({ where: { id }, relations: ['client'] });
      return updateTracker;
    } catch (error) {
      console.log('error occur while getting  getUpdatedItem', error);
    }
  }

  async organizationGstrScheduling(userId: any) {
    try {
      const user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
      const organizarionId = user.organization.id;
      let data = JSON.stringify({
        modules: ['P', 'NAO', 'ANO', 'LB', 'OD'],
        orgId: organizarionId,
        type: 'GSTR',
      });

      let config: any = {
        method: 'post',
        maxBodyLength: Infinity,
        url: `${process.env.CAMUNDA_URL}/vider/quantum/api/automation/bulk/sync`,
        headers: {
          'X-USER-ID': userId,
          'Content-Type': 'application/json',
        },
        data: data,
      };

      axios
        .request(config)
        .then((response) => { })
        .catch((error) => {
          console.log(error);
        });
    } catch (error) {
      console.log('error occur while organizationScheduling', error);
    }
  }

  async getAllGstrDemands(userId:number, query:any){
    const {limit,offset,fromDate,toDate,search} = query;

    const user = await User.findOne({where:{id:userId},relations:['organization']});

    if(!user){
      throw new NotFoundException("User not found");
    }

    const demandRecords = createQueryBuilder(GstrOutstandingDemand,"gstrOutstandingDemand")
    .leftJoinAndSelect("gstrOutstandingDemand.gstrCredentials",'gstrCredentials')
    .leftJoinAndSelect('gstrCredentials.client','client')
    .where('gstrOutstandingDemand.organizationId = :orgId',{orgId:user?.organization?.id})
     .andWhere('client.status != :status', { status: UserStatus.DELETED })
     .andWhere(
          new Brackets((qb) => {
            qb.where('gstrOutstandingDemand.isInPortal != :portalStatus', { portalStatus: 'NO' }).orWhere(
              'gstrOutstandingDemand.isInPortal IS NULL',
            );
          }),
        )
        .andWhere(
          new Brackets((qb) => {
            qb.where('gstrCredentials.status != :disStatus', {
              disStatus: GstrStatus.DISABLE,
            }).orWhere('gstrCredentials.status IS NULL');
          }),
        );

        demandRecords.orderBy('gstrOutstandingDemand.demandDt','DESC')

        const sort = typeof query?.sort === 'string' ? JSON.parse(query.sort) : query?.sort;

        if(sort?.column){
          const columnMap: Record<string,string>={
            gstIn:'gstrOutstandingDemand.gstIn',
            demandDt:'gstrOutstandingDemand.demandDt',
            igstTot:'gstrOutstandingDemand.igstTot',
            cgstTot:'gstrOutstandingDemand.cgstTot',
            sgstTot:'gstrOutstandingDemand.sgstTot',
            cessTot:'gstrOutstandingDemand.cessTot',
            totalTot:'gstrOutstandingDemand.totalTot',
            orderNo:'gstrOutstandingDemand.orderNo'
          }
          const column = columnMap[sort?.column] || sort?.column;
        demandRecords.orderBy(column,sort?.direction.toUpperCase())
        }

        if(fromDate || toDate){
          const fromDateMs = new Date(fromDate).getTime();
          const toDateMs = new Date(toDate).getTime();
          demandRecords.andWhere(
            new Brackets((qb)=>{
              qb.where('gstrOutstandingDemand.demandDt BETWEEN :fromDate AND :toDate',{fromDate:fromDateMs,toDate:toDateMs})
            })
          )
        }

        if(search){
          console.log(search)
          demandRecords.andWhere(
            new Brackets((qb)=>{
              qb.where('client.displayName LIKE :clientSearch',{clientSearch:`%${search}%`});
              qb.orWhere('gstrOutstandingDemand.gstIn LIKE :gstInSearch',{gstInSearch:`%${search}%`});
            })
              
          )
        }

        if(limit){
          demandRecords.take(limit);
        }

        if(offset>=0){
          demandRecords.skip(offset)
        }


    const result = await demandRecords.getManyAndCount();

    return {
      count: result[1],
      result:result[0]
    }
  }

  async getClientDemands(userId:number,id:number){
    const user = await User.findOne({
      where:{id:userId},
      relations:['organization']
    });

    if(!user){
      throw new NotFoundException("User not found")
    }

    const clientDemands = createQueryBuilder(GstrOutstandingDemand,"gstrOutstandingDemand")
    .leftJoinAndSelect('gstrOutstandingDemand.gstrCredentials','gstrCredentials')
    .where('gstrCredentials.id = :gstrCredId',{gstrCredId:id})
    .andWhere(
          new Brackets((qb) => {
            qb.where('gstrOutstandingDemand.isInPortal != :portalStatus', { portalStatus: 'NO' }).orWhere(
              'gstrOutstandingDemand.isInPortal IS NULL',
            );
          }),
        )
    const result = await clientDemands.getManyAndCount();
    return {
      result:result[0],
      count:result[1]
    }
  }

    async getAllGstrLedgers(userId:number, query:any){
    const {limit,offset,search,type} = query;

    const user = await User.findOne({where:{id:userId},relations:['organization']});

    if(!user){
      throw new NotFoundException("User not found");
    }

    const ledgerRecords = createQueryBuilder(GstrLedgerBalance,"gstrLedgersBalance")
    .leftJoinAndSelect("gstrLedgersBalance.gstrCredentials",'gstrCredentials')
    .leftJoinAndSelect('gstrCredentials.client','client')
    .where('gstrLedgersBalance.organizationId = :orgId',{orgId:user?.organization?.id})
     .andWhere('client.status != :status', { status: UserStatus.DELETED })
        .andWhere(
          new Brackets((qb) => {
            qb.where('gstrCredentials.status != :disStatus', {
              disStatus: GstrStatus.DISABLE,
            }).orWhere('gstrCredentials.status IS NULL');
          }),
        );

        const sort = typeof query?.sort === 'string' ? JSON.parse(query.sort) : query?.sort;

        if(sort?.column){
          const columnMap: Record<string,string>={
            gstIn:'gstrLedgersBalance.gstIn',
            igst:'gstrLedgersBalance.igst',
            cgst:'gstrLedgersBalance.cgst',
            sgst:'gstrLedgersBalance.sgst',
            cess:'gstrLedgersBalance.cess',
            total:'gstrLedgersBalance.total',
          }
          const column = columnMap[sort?.column] || sort?.column;
        ledgerRecords.orderBy(column,sort?.direction.toUpperCase())
        }

        if(type){
          ledgerRecords.andWhere('gstrLedgersBalance.ledgerType LIKE :type',{type})
        }

        

        if(search){
          ledgerRecords.andWhere(
            new Brackets((qb)=>{
              qb.where('client.displayName LIKE :clientSearch',{clientSearch:`%${search}%`});
              qb.orWhere('gstrLedgersBalance.gstIn LIKE :gstInSearch',{gstInSearch:`%${search}%`});
            })
              
          )
        }

        if(limit){
          ledgerRecords.take(limit);
        }

        if(offset>=0){
          ledgerRecords.skip(offset)
        }


    const result = await ledgerRecords.getManyAndCount();

    return {
      count: result[1],
      result:result[0]
    }
  }

    async getClientLedgers(userId:number,query:any,id:number){
          const {limit,offset} = query;

    const user = await User.findOne({
      where:{id:userId},
      relations:['organization']
    });

    if(!user){
      throw new NotFoundException("User not found")
    }

    const clientLedgers = createQueryBuilder(GstrLedgerBalance,"gstrLedgersBalance")
    .leftJoinAndSelect("gstrLedgersBalance.gstrCredentials",'gstrCredentials')
    .where('gstrCredentials.id = :gstrCredId',{gstrCredId:id})
     const sort = typeof query?.sort === 'string' ? JSON.parse(query.sort) : query?.sort;

        if(sort?.column){
          const columnMap: Record<string,string>={
            gstIn:'gstrLedgersBalance.gstIn',
            igst:'gstrLedgersBalance.igst',
            cgst:'gstrLedgersBalance.cgst',
            sgst:'gstrLedgersBalance.sgst',
            cess:'gstrLedgersBalance.cess',
            total:'gstrLedgersBalance.total',
          }
          const column = columnMap[sort?.column] || sort?.column;
        clientLedgers.orderBy(column,sort?.direction.toUpperCase())
        }

         if(limit){
          clientLedgers.take(limit);
        }

        if(offset>=0){
          clientLedgers.skip(offset)
        }

    const result = await clientLedgers.getManyAndCount();
    return {
      result:result[0],
      count:result[1]
    }
  }


  async exportDemands(userId: number, query: any) {
    const newQuery = { ...query, offset: 0, limit: 100000000 };
    const { result: demands } = await this.getAllGstrDemands(userId, newQuery);

    if (!demands.length) {
      throw new BadRequestException('No Data for Export');
    }

    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('GSTR Demands');

    const headers = [
      { header: 'S.No', key: 'serialNo' },
      { header: 'Client Name', key: 'clientName' },
      { header: 'Category', key: 'category' },
      { header: 'GSTIN', key: 'gstIn' },
      { header: 'Demand Date', key: 'demandDt' },
      { header: 'Order Number', key: 'orderNo' },

      { header: 'IGST Total', key: 'igstTot' },
      { header: 'IGST Tax', key: 'igstTx' },
      { header: 'IGST Fee', key: 'igstFee' },
      { header: 'IGST Penalty', key: 'igstPen' },
      { header: 'IGST Interest', key: 'igstIntr' },
      { header: 'IGST Others', key: 'igstOth' },

      { header: 'CGST Total', key: 'cgstTot' },
      { header: 'CGST Tax', key: 'cgstTx' },
      { header: 'CGST Fee', key: 'cgstFee' },
      { header: 'CGST Penalty', key: 'cgstPen' },
      { header: 'CGST Interest', key: 'cgstIntr' },
      { header: 'CGST Others', key: 'cgstOth' },

      { header: 'SGST Total', key: 'sgstTot' },
      { header: 'SGST Tax', key: 'sgstTx' },
      { header: 'SGST Fee', key: 'sgstFee' },
      { header: 'SGST Penalty', key: 'sgstPen' },
      { header: 'SGST Interest', key: 'sgstIntr' },
      { header: 'SGST Others', key: 'sgstOth' },

      { header: 'CESS Total', key: 'cessTot' },
      { header: 'CESS Tax', key: 'cessTx' },
      { header: 'CESS Fee', key: 'cessFee' },
      { header: 'CESS Penalty', key: 'cessPen' },
      { header: 'CESS Interest', key: 'cessIntr' },
      { header: 'CESS Others', key: 'cessOth' },

      { header: 'Grand Total', key: 'totalTot' },
      { header: 'Total Tax', key: 'totalTx' },
      { header: 'Total Fee', key: 'totalFee' },
      { header: 'Total Penalty', key: 'totalPen' },
      { header: 'Total Interest', key: 'totalIntr' },
      { header: 'Total Others', key: 'totalOth' }
    ];

    worksheet.columns = headers;

    const columnMaxLengths = Array(headers.length).fill(0);
    let serialCounter = 1;

    const numberFields = headers
      .map(h => h.key)
      .filter(
        key =>
          !['serialNo', 'clientName', 'category', 'gstIn', 'orderNo', 'demandDt'].includes(key)
      );

    for (const demand of demands) {
      const rowData: any = {
        serialNo: serialCounter++,
        clientName: demand?.gstrCredentials?.client?.displayName || '-',
        category: categoryLabels[demand?.gstrCredentials?.client?.category] || '-',
        gstIn: demand?.gstIn || '-',
        demandDt: demand?.demandDt ? new Date(Number(demand.demandDt)) : null,
        orderNo: demand?.orderNo || '-'
      };

      for (const key of numberFields) {
        const val = demand?.[key];
        rowData[key] = typeof val === 'number' ? val : val ? Number(val) : null;
      }

      const row = worksheet.addRow(rowData);

      // Apply number formatting
      for (const key of numberFields) {
        const colIndex = headers.findIndex(h => h.key === key) + 1;
        const cell = row.getCell(colIndex);
        if (rowData[key] !== null && rowData[key] !== undefined) {
          cell.numFmt = '₹#,##,##0.00';
        }
      }

      // Format date column
      const dateColIndex = headers.findIndex(h => h.key === 'demandDt') + 1;
      const dateCell = row.getCell(dateColIndex);
      if (rowData.demandDt instanceof Date && !isNaN(rowData.demandDt)) {
        dateCell.numFmt = 'dd-mmm-yyyy';
      }

      // Calculate max column length for autosizing
      worksheet.columns.forEach((column, colIndex) => {
        const headerLength = column.header?.toString().length || 0;
        const cellLength = rowData[column.key]?.toString().length || 0;
        columnMaxLengths[colIndex] = Math.max(
          columnMaxLengths[colIndex],
          headerLength,
          cellLength
        );
      });
    }

    // Set column widths
    worksheet.columns.forEach((column, colIndex) => {
      column.width = columnMaxLengths[colIndex] + 3;
    });

    // Style header row
    const headerRow = worksheet.getRow(1);
    headerRow.font = { bold: true };
    headerRow.eachCell((cell) => {
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '64B5F6' }
      };
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' }
      };
      cell.alignment = { horizontal: 'center', vertical: 'middle' };
    });

    const rightAlignedColumns = [
  'IGST Tax', 'IGST Fee', 'IGST Penalty', 'IGST Interest', 'IGST Others', 'IGST Total',
  'CGST Tax', 'CGST Fee', 'CGST Penalty', 'CGST Interest', 'CGST Others', 'CGST Total',
  'SGST Tax', 'SGST Fee', 'SGST Penalty', 'SGST Interest', 'SGST Others', 'SGST Total',
  'CESS Tax', 'CESS Fee', 'CESS Penalty', 'CESS Interest', 'CESS Others', 'CESS Total',
  'Total Tax', 'Total Fee', 'Total Penalty', 'Total Interest', 'Total Others', 'Grand Total'
];

    // Style all data cells with borders
  worksheet.eachRow((row, rowNumber) => {
    row.eachCell((cell,colNumber) => {
      const colHeader = headers[colNumber - 1]?.header;
       if (rightAlignedColumns.includes(colHeader)) {
      cell.alignment = { horizontal: 'right', vertical: 'middle', wrapText: true };
    } else {
      cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
    }
      // cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' }
      };
    });
  });


    // Style all data cells
    // worksheet.eachRow((row, rowNumber) => {
    //   if (rowNumber !== 1) {
    //     row.eachCell((cell) => {
    //       cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
    //     });
    //   }
    // });

    worksheet.views = [{ state: 'frozen', ySplit: 1 }];

    const buffer = await workbook.xlsx.writeBuffer();
    return buffer;
  }

   async exportLedgers(userId: number, query: any) {
    const newQuery = { ...query, offset: 0, limit: 100000000 };
    const { result: ledgers } = await this.getAllGstrLedgers(userId, newQuery);

    if (!ledgers.length) {
      throw new BadRequestException('No Data for Export');
    }

    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('GSTR Demands');

    const headers = [
      { header: 'S.No', key: 'serialNo' },
      { header: 'Client Name', key: 'clientName' },
      { header: 'Category', key: 'category' },
      { header: 'GSTIN', key: 'gstIn' },
      { header: 'Ledger Type', key: 'ledgerType' },
      { header: 'IGST', key: 'igst' },
      { header: 'CGST', key: 'cgst' },
      { header: 'SGST', key: 'sgst' },
      { header: 'CESS', key: 'cess' },
      { header: 'Total', key: 'total' },
    ];

    worksheet.columns = headers;

    const columnMaxLengths = Array(headers.length).fill(0);
    let serialCounter = 1;

    const numberFields = headers
      .map(h => h.key)
      .filter(
        key =>
          !['serialNo', 'clientName', 'category', 'gstIn', 'ledgerType', ].includes(key)
      );

    for (const demand of ledgers) {
      const rowData: any = {
        serialNo: serialCounter++,
        clientName: demand?.gstrCredentials?.client?.displayName || '-',
        category: categoryLabels[demand?.gstrCredentials?.client?.category] || '-',
        gstIn: demand?.gstIn || '-',
        ledgerType: ledgerTypeMap[demand?.ledgerType] || '-'
      };

      for (const key of numberFields) {
        const val = demand?.[key];
        rowData[key] = typeof val === 'number' ? val : val ? Number(val) : null;
      }

      const row = worksheet.addRow(rowData);

      // Apply number formatting
      for (const key of numberFields) {
        const colIndex = headers.findIndex(h => h.key === key) + 1;
        const cell = row.getCell(colIndex);
        if (rowData[key] !== null && rowData[key] !== undefined) {
          cell.numFmt = '₹#,##,##0.00';
        }
      }


      // Calculate max column length for autosizing
      worksheet.columns.forEach((column, colIndex) => {
        const headerLength = column.header?.toString().length || 0;
        const cellLength = rowData[column.key]?.toString().length || 0;
        columnMaxLengths[colIndex] = Math.max(
          columnMaxLengths[colIndex],
          headerLength,
          cellLength
        );
      });
    }

    // Set column widths
    worksheet.columns.forEach((column, colIndex) => {
      column.width = columnMaxLengths[colIndex] + 3;
    });

    // Style header row
    const headerRow = worksheet.getRow(1);
    headerRow.font = { bold: true };
    headerRow.eachCell((cell) => {
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '64B5F6' }
      };
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' }
      };
      cell.alignment = { horizontal: 'center', vertical: 'middle' };
    });

    // Style all data cells with borders
  worksheet.eachRow((row, rowNumber) => {
    row.eachCell((cell) => {
      cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' }
      };
    });
  });


    // Style all data cells
    worksheet.eachRow((row, rowNumber) => {
      if (rowNumber !== 1) {
        row.eachCell((cell) => {
          cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
        });
      }
    });

    worksheet.views = [{ state: 'frozen', ySplit: 1 }];

    const buffer = await workbook.xlsx.writeBuffer();
    return buffer;
  }

    async exportClientDemand(userId: number, query: any) {
    const newQuery = { ...query, offset: 0, limit: 100000000 };
    const id = query.gstrid;
    const { result: demands } = await this.getClientDemands(userId,id);

    if (!demands.length) {
      throw new BadRequestException('No Data for Export');
    }

    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('GSTR Demands');

    const headers = [
      { header: 'S.No', key: 'serialNo' },
      { header: 'GSTIN', key: 'gstIn' },
      { header: 'Demand Date', key: 'demandDt' },
      { header: 'Order Number', key: 'orderNo' },

      { header: 'IGST Tax', key: 'igstTx' },
      { header: 'IGST Fee', key: 'igstFee' },
      { header: 'IGST Penalty', key: 'igstPen' },
      { header: 'IGST Interest', key: 'igstIntr' },
      { header: 'IGST Others', key: 'igstOth' },
      { header: 'IGST Total', key: 'igstTot' },
 
      { header: 'CGST Tax', key: 'cgstTx' },
      { header: 'CGST Fee', key: 'cgstFee' },
      { header: 'CGST Penalty', key: 'cgstPen' },
      { header: 'CGST Interest', key: 'cgstIntr' },
      { header: 'CGST Others', key: 'cgstOth' },
      { header: 'CGST Total', key: 'cgstTot' },
      
      { header: 'SGST Tax', key: 'sgstTx' },
      { header: 'SGST Fee', key: 'sgstFee' },
      { header: 'SGST Penalty', key: 'sgstPen' },
      { header: 'SGST Interest', key: 'sgstIntr' },
      { header: 'SGST Others', key: 'sgstOth' },
      { header: 'SGST Total', key: 'sgstTot' },

      { header: 'CESS Tax', key: 'cessTx' },
      { header: 'CESS Fee', key: 'cessFee' },
      { header: 'CESS Penalty', key: 'cessPen' },
      { header: 'CESS Interest', key: 'cessIntr' },
      { header: 'CESS Others', key: 'cessOth' },
      { header: 'CESS Total', key: 'cessTot' },
      
      { header: 'Total Tax', key: 'totalTx' },
      { header: 'Total Fee', key: 'totalFee' },
      { header: 'Total Penalty', key: 'totalPen' },
      { header: 'Total Interest', key: 'totalIntr' },
      { header: 'Total Others', key: 'totalOth' },
      { header: 'Grand Total', key: 'totalTot' },
    ];

    worksheet.columns = headers;

    const columnMaxLengths = Array(headers.length).fill(0);
    let serialCounter = 1;

    const numberFields = headers
      .map(h => h.key)
      .filter(
        key =>
          !['serialNo', 'gstIn', 'orderNo', 'demandDt'].includes(key)
      );

    for (const demand of demands) {
      const rowData: any = {
        serialNo: serialCounter++,
        gstIn: demand?.gstIn || '-',
        demandDt: demand?.demandDt ? new Date(Number(demand.demandDt)) : null,
        orderNo: demand?.orderNo || '-'
      };

      for (const key of numberFields) {
        const val = demand?.[key];
        rowData[key] = typeof val === 'number' ? val : val ? Number(val) : null;
      }

      const row = worksheet.addRow(rowData);

      // Apply number formatting
      for (const key of numberFields) {
        const colIndex = headers.findIndex(h => h.key === key) + 1;
        const cell = row.getCell(colIndex);
        if (rowData[key] !== null && rowData[key] !== undefined) {
          cell.numFmt = '₹#,##,##0.00';
        }
      }

      // Format date column
      const dateColIndex = headers.findIndex(h => h.key === 'demandDt') + 1;
      const dateCell = row.getCell(dateColIndex);
      if (rowData.demandDt instanceof Date && !isNaN(rowData.demandDt)) {
        dateCell.numFmt = 'dd-mmm-yyyy';
      }

      // Calculate max column length for autosizing
      worksheet.columns.forEach((column, colIndex) => {
        const headerLength = column.header?.toString().length || 0;
        const cellLength = rowData[column.key]?.toString().length || 0;
        columnMaxLengths[colIndex] = Math.max(
          columnMaxLengths[colIndex],
          headerLength,
          cellLength
        );
      });
    }

    // Set column widths
    worksheet.columns.forEach((column, colIndex) => {
      column.width = columnMaxLengths[colIndex] + 3;
    });

    // Style header row
    const headerRow = worksheet.getRow(1);
    headerRow.font = { bold: true };
    headerRow.eachCell((cell) => {
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '64B5F6' }
      };
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' }
      };
      cell.alignment = { horizontal: 'center', vertical: 'middle' };
    });
    const rightAlignedColumns = [
      'IGST Tax', 'IGST Fee', 'IGST Penalty', 'IGST Interest', 'IGST Others', 'IGST Total',
      'CGST Tax', 'CGST Fee', 'CGST Penalty', 'CGST Interest', 'CGST Others', 'CGST Total',
      'SGST Tax', 'SGST Fee', 'SGST Penalty', 'SGST Interest', 'SGST Others', 'SGST Total',
      'CESS Tax', 'CESS Fee', 'CESS Penalty', 'CESS Interest', 'CESS Others', 'CESS Total',
      'Total Tax', 'Total Fee', 'Total Penalty', 'Total Interest', 'Total Others', 'Grand Total'
    ];
    // Style all data cells with borders
  worksheet.eachRow((row, rowNumber) => {
    row.eachCell((cell,colNumber) => {
    const colHeader = headers[colNumber - 1]?.header;
       if (rightAlignedColumns.includes(colHeader)) {
        console.log("hiii")
      cell.alignment = { horizontal: 'right', vertical: 'middle', wrapText: true };
    } else {
      cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
    }
      // cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' }
      };
    });
  });


    // Style all data cells
    // worksheet.eachRow((row, rowNumber) => {
    //   if (rowNumber !== 1) {
    //     row.eachCell((cell) => {
    //       cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
    //     });
    //   }
    // });

    worksheet.views = [{ state: 'frozen', ySplit: 1 }];

    const buffer = await workbook.xlsx.writeBuffer();
    return buffer;
  }

   async exportClientLedger(userId: number, query: any) {
    const newQuery = { ...query, offset: 0, limit: 100000000 };
    const id = query.gstrid;

    const { result: ledgers } = await this.getClientLedgers(userId, newQuery, id);

    if (!ledgers.length) {
      throw new BadRequestException('No Data for Export');
    }

    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('GSTR Demands');

    const headers = [
      { header: 'S.No', key: 'serialNo' },
      { header: 'GSTIN', key: 'gstIn' },
      { header: 'Ledger Type', key: 'ledgerType' },
      { header: 'IGST', key: 'igst' },
      { header: 'CGST', key: 'cgst' },
      { header: 'SGST', key: 'sgst' },
      { header: 'CESS', key: 'cess' },
      { header: 'Total', key: 'total' },
    ];

    worksheet.columns = headers;

    const columnMaxLengths = Array(headers.length).fill(0);
    let serialCounter = 1;

    const numberFields = headers
      .map(h => h.key)
      .filter(
        key =>
          !['serialNo', 'gstIn', 'ledgerType', ].includes(key)
      );

    for (const demand of ledgers) {
      const rowData: any = {
        serialNo: serialCounter++,
        gstIn: demand?.gstIn || '-',
        ledgerType: ledgerTypeMap[demand?.ledgerType] || '-'
      };

      for (const key of numberFields) {
        const val = demand?.[key];
        rowData[key] = typeof val === 'number' ? val : val ? Number(val) : null;
      }

      const row = worksheet.addRow(rowData);

      // Apply number formatting
      for (const key of numberFields) {
        const colIndex = headers.findIndex(h => h.key === key) + 1;
        const cell = row.getCell(colIndex);
        if (rowData[key] !== null && rowData[key] !== undefined) {
          cell.numFmt = '₹#,##,##0.00';
        }
      }


      // Calculate max column length for autosizing
      worksheet.columns.forEach((column, colIndex) => {
        const headerLength = column.header?.toString().length || 0;
        const cellLength = rowData[column.key]?.toString().length || 0;
        columnMaxLengths[colIndex] = Math.max(
          columnMaxLengths[colIndex],
          headerLength,
          cellLength
        );
      });
    }

    // Set column widths
    worksheet.columns.forEach((column, colIndex) => {
      column.width = columnMaxLengths[colIndex] + 3;
    });

    // Style header row
    const headerRow = worksheet.getRow(1);
    headerRow.font = { bold: true };
    headerRow.eachCell((cell) => {
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '64B5F6' }
      };
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' }
      };
      cell.alignment = { horizontal: 'center', vertical: 'middle' };
    });

    // Style all data cells with borders
  worksheet.eachRow((row, rowNumber) => {
    row.eachCell((cell) => {
      cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' }
      };
    });
  });


    // Style all data cells
    worksheet.eachRow((row, rowNumber) => {
      if (rowNumber !== 1) {
        row.eachCell((cell) => {
          cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
        });
      }
    });

    worksheet.views = [{ state: 'frozen', ySplit: 1 }];

    const buffer = await workbook.xlsx.writeBuffer();
    return buffer;
  }

  async getCaseTypeNames(userId){
    try{
        const user = await User.findOne({where:{id:userId}});

        const uniqueCaseTypeName = await createQueryBuilder(GstrAdditionalNoticeOrders,'n')
        .select('DISTINCT n.caseTypeName', 'caseTypeName')
        .leftJoin('n.client','c')
        .where('n.organizationId = :orgId', { orgId:user?.organization?.id })
        .andWhere('c.status != :clientStatus',{clientStatus:UserStatus.DELETED})
        .getRawMany();
        
        const filteredCaseTypes = uniqueCaseTypeName
            .map((section) => section.caseTypeName)
            .filter((section) => section !== '' && section !== null);
        return filteredCaseTypes;
    }catch(error){
      console.log('Error in fetching Case Type Names',error)
    }
    
  }

    async getCaseFolderTypeNames(userId){
      try{
        const user = await User.findOne({where:{id:userId}});
          const uniqueCaseFolderTypeName = await createQueryBuilder(GstrAdditionalNoticeOrders,'n')
          .select('DISTINCT n.caseFolderTypeName', 'caseFolderTypeName')
          .leftJoin('n.client','c')
          .leftJoin('c.gstrCredentials','gc')
          .where('n.organizationId = :orgId', { orgId:user?.organization?.id })
          .andWhere('gc.status != :clientStatus',{clientStatus:GstrStatus.DISABLE})
          .andWhere('n.caseFolderTypeName NOT IN (:...folderTypes)',{folderTypes:['REPLIES','APPLICATIONS']})
          .getRawMany();
          
          const filteredCaseFolderTypes = uniqueCaseFolderTypeName
              .map((section) => section.caseFolderTypeName)
              .filter((section) => section !== '' && section !== null);
          return filteredCaseFolderTypes;

      }catch(e){
        console.log('Error in fethinf case folder type names',e)
      }
    
  }


}
