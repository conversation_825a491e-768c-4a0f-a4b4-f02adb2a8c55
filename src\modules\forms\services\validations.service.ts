import { Injectable, InternalServerErrorException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { User } from 'src/modules/users/entities/user.entity';
import CreateValidationDto from '../dto/create-validation.dto';
import { Validation, ValidationDocument } from '../schemas/validation.schema';

@Injectable()
export class ValidataionsService {
  constructor(
    @InjectModel(Validation.name)
    private validationModel: Model<ValidationDocument>,
  ) {}

  async create(userId: number, data: CreateValidationDto) {
    let user = await User.findOne({
      where: {
        id: userId,
      },
      relations: ['organization'],
    });

    const validation = new this.validationModel({
      ...data,
      organizationId: user.organization.id,
    });

    return validation.save();
  }

  async get(userId: number) {
    let user = await User.findOne({
      where: {
        id: userId,
      },
      relations: ['organization'],
    });
    let data = await this.validationModel.find({
      organizationId: user.organization.id,
    });
    return data;
  }

  async getDefaultValidations() {
    let validations = await this.validationModel.find({
      defaultOne: true,
    });
    return validations;
  }

  async importValidations(userId: number, data) {
    try {
      let user = await User.findOne({
        where: {
          id: userId,
        },
        relations: ['organization'],
      });

      let validations = await this.validationModel.find({
        _id: {
          $in: data.validations,
        },
      });

      for (let validation of validations) {
        let clonedValidation = JSON.stringify(validation, (key, value) => {
          if (key === '_id') {
            return undefined;
          }
          if (key === '__v') {
            return undefined;
          }
          return value;
        });

        let parsedValidation = JSON.parse(clonedValidation);
        let newValidation = new this.validationModel(parsedValidation);
        newValidation.organizationId = user.organization.id;
        newValidation.defaultOne = false;
        await newValidation.save();
      }

      return {
        message: 'Form validattions imported',
      };
    } catch (e) {
      console.log(e);
      throw new InternalServerErrorException(e);
    }
  }

  async update(id: string, data: CreateValidationDto) {
    let validation = await this.validationModel.findById(id);
    validation.set(data);
    return validation.save();
  }

  async delete(id: string) {
    let validation = await this.validationModel.findById(id);
    return validation.remove();
  }
}
