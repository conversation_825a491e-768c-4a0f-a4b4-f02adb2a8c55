import {
  BadRequestException,
  ConsoleLogger,
  Injectable,
  InternalServerErrorException,
} from '@nestjs/common';
import { User, UserStatus } from 'src/modules/users/entities/user.entity';
import { Brackets, Connection, createQ<PERSON><PERSON><PERSON><PERSON><PERSON>, getManager, Like } from 'typeorm';
import Client from 'src/modules/clients/entity/client.entity';
import axios from 'axios';
import { dateFormation } from 'src/utils/datesFormation';
import { ClientPasswordService } from './client-passwords.service';
import { ClientService } from './clients.service';
import { TAN_REGEX } from 'src/utils/validations/regex-pattrens';
import { RegistrationType } from 'src/modules/gstr-register/entity/gstr-register.entity';
import OrganizationPreferences from 'src/modules/organization-preferences/entity/organization-preferences.entity';
import Password, { IsExistingAtomPro } from 'src/modules/clients/entity/password.entity';
import GstrCredentials from 'src/modules/gstr-automation/entity/gstrCredentials.entity';
import AutJurisdictionDetails from 'src/modules/automation/entities/aut-jurisdiction-details.entity';
import AutOutstandingDemand from 'src/modules/automation/entities/aut-outstanding-demand.entity';
import AutProfileDetails from 'src/modules/automation/entities/aut-profile-details.entity';
import AutActivity from 'src/modules/automation/entities/aut_activity.entity';
import AutClientCredentials, { syncStatus } from 'src/modules/automation/entities/aut_client_credentials.entity';
import AutEProceedingFya from 'src/modules/automation/entities/aut_income_tax_eproceedings_fya.entity';
import AutFyaNotice from 'src/modules/automation/entities/aut_income_tax_eproceedings_fya_notice.entity';
import AutEProceedingFyi from 'src/modules/automation/entities/aut_income_tax_eproceedings_fyi.entity';
import AutFyiNotice from 'src/modules/automation/entities/aut_income_tax_eproceedings_fyi_notice.entity';
import AutIncometaxReturns from 'src/modules/automation/entities/aut_incometax_returns.entity';
import AutomationMachines from 'src/modules/automation/entities/automation_machines.entity';
import AutIncomeTaxForms from 'src/modules/automation/entities/aut_income_tax_forms.entity';
import AutEProceeding from 'src/modules/automation/entities/aut_income_tax_eproceedings_fya.entity';

@Injectable()
export class AutIncomeTaxFormsService {
  constructor(
    private clientPasswordsService: ClientPasswordService,
    private clientService: ClientService,
  ) { }
  async findAll(userId: number, query: any) {
    const { limit, offset } = query;
    let user = await User.findOne({
      where: { id: userId },
      relations: ['organization'],
    });

    let forms = createQueryBuilder(AutIncomeTaxForms, 'forms')
      .leftJoinAndSelect('forms.client', 'client')
      .leftJoinAndSelect('forms.autClientCredentials', 'clientCredentials')
      .where('forms.organizationId = :id', { id: user?.organization?.id });

    if (query.search) {
      forms.andWhere('client.displayName LIKE :search', { search: `%${query.search}%` });
      forms.orWhere('clientCredentials.panNumber LIKE :search', { search: `%${query.search}%` });
    }

    if (query.formDesc) {
      forms.andWhere('forms.formDesc = :formDesc', {
        formDesc: query.formDesc,
      });
    }

    if (query.filingType) {
      forms.andWhere('forms.filingTypeCd = :filingType', {
        filingType: query.filingType,
      });
    }

    if (query.assessmentYear) {
      forms.andWhere('forms.refYear = :refYear', {
        refYear: query.assessmentYear,
      });
      forms.andWhere('forms.refYearType = :refYearType', {
        refYearType: 'AY',
      });
    }

    if (query.financialYear) {
      forms.andWhere('forms.refYear = :refYearFy', {
        refYearFy: query.financialYear,
      });
      forms.andWhere('forms.refYearType = :refYearTypeFy', {
        refYearTypeFy: 'FY',
      });
    }

    if (query.clientCategory) {
      forms.andWhere(`client.category = :clientCategory`, {
        clientCategory: query.clientCategory,
      });
    }

    if (offset >= 0) {
      forms.skip(offset);
    }

    if (limit) {
      forms.take(limit);
    }

    forms.orderBy('forms.refYear', 'DESC');

    let result = await forms.getManyAndCount();
    return {
      count: result[1],
      result: result[0],
    };
  }

  async findForm(userId, id) {
    const incomeTaxForm = await AutIncomeTaxForms.findOne({
      where: { id },
      relations: ['client', 'client.autProfileDetails'],
    });
    return incomeTaxForm;
  }

  async getClientAutCredentials(userId: number, query) {
    const { offset, limit } = query;
    let user = await User.findOne({
      where: { id: userId },
      relations: ['organization'],
    });

    let clientCredentials = createQueryBuilder(AutClientCredentials, 'clientCredentials')
      .leftJoinAndSelect('clientCredentials.client', 'client')
      .where('clientCredentials.organizationId = :id', { id: user?.organization?.id });

    if (query.search) {
      clientCredentials.andWhere('client.displayName LIKE :search', {
        search: `%${query.search}%`,
      });
    }
    if (offset >= 0) {
      clientCredentials.skip(offset);
    }

    if (limit) {
      clientCredentials.take(limit);
    }
    let result = await clientCredentials.getManyAndCount();
    return {
      count: result[1],
      result: result[0],
    };
  }

  async addClientAutCredentials(userId: number, body: any) {
    try {
      let user = await User.findOne({
        where: { id: userId },
        relations: ['organization'],
      });

      const credential = await AutClientCredentials.findOne({
        where: { organizationId: user?.organization?.id, panNumber: body?.panNumber },
      });

      const organizationPreferences: any = await OrganizationPreferences.findOne({
        where: { organization: user.organization },
      });
      if (organizationPreferences) {
        const organizationLimit = organizationPreferences?.automationConfig?.incomeTaxLimit;
        if (organizationLimit) {
          const autClientCredential = await AutClientCredentials.count({
            where: { organizationId: user.organization.id },
          });
          if (organizationLimit >= autClientCredential) {
            if (credential) {
              throw new BadRequestException(
                'Specified pannumber Utilize your organization already',
              );
            } else {
              const client= await Client.findOne({where: {id:body?.selectedClient?.id}})
              const clientCredentials = new AutClientCredentials();
              clientCredentials.panNumber = body?.panNumber;
              clientCredentials.password = body?.password;
              clientCredentials.client = client;
              clientCredentials.organizationId = user?.organization?.id;
              clientCredentials.syncStatus = syncStatus.NOTSYNC;
              await clientCredentials.save();
            }
          } else {
            throw new BadRequestException('Maximum Income tax Client Count Reached');
          }
        }
      }
    } catch (error) {
      console.log('Error occur while add the incomeTax client credentials', error);
      throw new InternalServerErrorException(error);
    }
  }

  async getClientCredential(id: number,query: any) {
    try {
      const IncomeTaxClient = await createQueryBuilder(AutClientCredentials, 'AutClientCredentials')
        .leftJoinAndSelect('AutClientCredentials.client','client')
        .where('client.id = :clientId', { clientId : parseInt(query.clientId) })
        .getOne();
      return IncomeTaxClient;      
    } catch (error) {
      console.log('error occured while fetching income tax client credentials', error);
    }
  }

  async getAllClients(userId: number) {
    let user = await User.findOne({
      where: { id: userId },
      relations: ['organization'],
    });

    const entityManager = getManager();
    const query = `
    SELECT id, display_name as displayName, status
    FROM client 
    WHERE organization_id = ${user?.organization.id} 
    AND status != 'DELETED'
    AND id NOT IN (
        SELECT client_id 
        FROM aut_client_credentials
    )   
`;

    let clients = await entityManager.query(query);
    return clients;
  }

  async getIncomeTaxProfile(id: number) {
    const checkStatus = await AutomationMachines.findOne({
      where: { autoCredentials: id, status: 'PENDING' },
    });

    if (checkStatus) {
    }

    const lastCompletedMachine = await AutomationMachines.findOne({
      where: { autoCredentials: id, status: 'COMPLETED' },
      order: {
        id: 'DESC',
      }, // Assuming you have a createdAt field indicating creation timestamp
    });
    try {
      const clientCredential = await AutClientCredentials.findOne({
        where: { id },
        relations: ['client'],
      });
      if (clientCredential) {
        const profileDetails = await AutProfileDetails.findOne({
          where: { ClientId: clientCredential?.client?.id },
        });
        return { profileDetails, lastCompletedMachine };
      }
    } catch (error) {
      console.log('error occured while fetching income tax client credentials', error);
    }
  }

  async getIncomeTaxJurisdiction(id: number) {
    try {
      const clientCredentials = await AutClientCredentials.findOne({
        where: { id },
        relations: ['client'],
      });

      if (clientCredentials) {
        const jurisdictionsDetails = await AutJurisdictionDetails.findOne({
          where: { clientId: clientCredentials?.client?.id },
        });
        return jurisdictionsDetails;
      }
    } catch (error) {
      console.log('error occured while fetching income tax jurisdiction details', error);
    }
  }

  async getClientform(id: number, query: any) {
    const { limit, offset } = query;
    try {
      const clientCredential = await AutClientCredentials.findOne({
        where: { id },
        relations: ['client'],
      });
      if (clientCredential) {
        const formDetails = await createQueryBuilder(AutIncomeTaxForms, 'autIncomeTaxForms')
          .leftJoinAndSelect('autIncomeTaxForms.client', 'client')
          .leftJoinAndSelect('autIncomeTaxForms.autClientCredentials', 'autClientCredentials')
          .where('autClientCredentials.id =:id', { id: id });

        if (offset) {
          formDetails.skip(offset);
        }

        if (limit) {
          formDetails.take(limit);
        }

        let result = await formDetails.getManyAndCount();
        return {
          count: result[1],
          result: result[0],
        };
      }
    } catch (error) {
      console.log('error occured while fetching income tax client forms', error);
    }
  }

  async getIncomeTaxReturns(userId: number, query: any) {
    const { limit, offset } = query;
    let user = await User.findOne({
      where: { id: userId },
      relations: ['organization'],
    });
    let returns = createQueryBuilder(AutIncometaxReturns, 'returns')
      .leftJoinAndSelect('returns.client', 'client')
      .leftJoinAndSelect('returns.autClientCredentials', 'clientCredentials')
      .where('returns.organizationId = :id', { id: user?.organization?.id });

    if (query.search) {
      const whereClause = `(
          returns.ackNum like '%${query.search}%' or
          client.displayName like '%${query.search}%' or
          clientCredentials.panNumber like '%${query.search}%'
        )`;
      returns.andWhere(whereClause);
    }

    if (query.itrType) {
      returns.andWhere('returns.formtypeCd = :itrType', {
        itrType: query.itrType,
      });
    }
    if (query.filingType) {
      returns.andWhere('returns.filingTypeCd = :filingType', {
        filingType: query.filingType,
      });
    }
    if (query.assessmentYear) {
      returns.andWhere('returns.assmentYear = :assessmentYear', {
        assessmentYear: query.assessmentYear,
      });
    }

    if (query.clientCategory) {
      returns.andWhere(`client.category = :clientCategory`, {
        clientCategory: query.clientCategory,
      });
    }
    if (query.status) {
      if (query.status === 'empty') {
        returns.andWhere('returns.verStatus IS NULL');
      } else if (query.status === 'Y') {
        returns.andWhere('returns.verStatus = :status', {
          status: query.status,
        });
      } else if (query.status === 'N') {
        returns.andWhere('returns.verStatus = :aastatus', {
          aastatus: query.status,
        });
      } else if (query.status === 'X') {
        returns.andWhere('returns.verStatus = :aastatus', {
          aastatus: query.status,
        });
      }
    }

    if (offset >= 0) {
      returns.skip(offset);
    }

    if (limit) {
      returns.take(limit);
    }

    returns.orderBy('returns.assmentYear', 'DESC');

    let result = await returns.getManyAndCount();
    return {
      count: result[1],
      result: result[0],
    };
  }

  async findReturn(userId, id) {
    const incometaxReturns = await AutIncometaxReturns.findOne({
      where: { id },
      relations: ['client', 'client.autProfileDetails'],
    });
    return incometaxReturns;
  }

  async getClientReturn(id: number, query: any) {
    const { limit, offset } = query;
    try {
      const clientCredential = await AutClientCredentials.findOne({
        where: { id },
        relations: ['client', 'client.organization'],
      });
      if (clientCredential) {
        const returnDetails = await createQueryBuilder(AutIncometaxReturns, 'autIncometaxReturns')
          .leftJoinAndSelect('autIncometaxReturns.client', 'client')
          // .where('client.id =: id',{id:clientCredential?.client?.id})
          .where('client.id =:id', { id: clientCredential?.client?.id })
          .andWhere(`autIncometaxReturns.organizationId=:organization`, {
            organization: clientCredential?.client?.organization.id,
          });

        if (offset) {
          returnDetails.skip(offset);
        }

        if (limit) {
          returnDetails.take(limit);
        }

        let result = await returnDetails.getManyAndCount();
        // console.log(result)
        return {
          count: result[1],
          result: result[0],
        };
      }
    } catch (error) {
      console.log('error occured while fetching income tax client forms', error);
    }
  }

  async getIncomeTaxDemands(userId: number, query: any) {
    const { limit, offset, section, assessmentYear, clientCategory, sortValue } = query;
    let user = await User.findOne({
      where: { id: userId },
      relations: ['organization'],
    });
    let demands = createQueryBuilder(AutOutstandingDemand, 'demands')
      .leftJoinAndSelect('demands.client', 'client')
      .where('demands.organizationId = :id', { id: user?.organization?.id });

    if (query.search) {
      demands.andWhere(
        new Brackets((qb) => {
          qb.where('demands.pan LIKE :pansearch', {
            pansearch: `%${query.search}%`,
          });
          qb.orWhere('client.displayName LIKE :namesearch', {
            namesearch: `%${query.search}%`,
          });
        }),
      );
    }

    if (query.section) {
      demands.andWhere('demands.sectionCodeText like :search', { search: `%${query.section}%` });
    }

    if (query.assessmentYear) {
      if (query.assessmentYear === 'null') {
        demands.andWhere('demands.assessmentYear = 0');
      } else {
        demands.andWhere('demands.assessmentYear like :as', { as: `%${query.assessmentYear}%` });
      }
    }

    if (query.clientCategory) {
      demands.andWhere(`client.category = :clientCategory`, {
        clientCategory: query.clientCategory,
      });
    }

    if (query.interval) {
      const now = new Date();
      const interval = query.interval;

      if (interval === 'last15days') {
        const last15days = new Date();
        last15days.setDate(now.getDate() - 15);
        demands.andWhere('demands.demandRaisedDate >= :last15days', { last15days });
      } else if (interval === 'last1month') {
        const last1month = new Date();
        last1month.setMonth(now.getMonth() - 1);
        demands.andWhere('demands.demandRaisedDate >= :last1month', { last1month });
      } else if (interval === 'last1week') {
        const last1week = new Date();
        last1week.setDate(now.getDate() - 7);
        demands.andWhere('demands.demandRaisedDate >= :last1week', { last1week });
      }
    }
    demands.orderBy('demands.assessmentYear', 'DESC');

    if (query.sortValue) {
      if (query.sortValue === 'AMOUNT_DESC') {
        demands.orderBy('demands.outstandingDemandAmount', 'DESC');
      } else if (query.sortValue === 'AMOUNT_ASC') {
        demands.orderBy('demands.outstandingDemandAmount');
      } else if (query.sortValue === 'DATE_NEWEST') {
        demands.orderBy('demands.demandRaisedDate', 'DESC');
      } else if (query.sortValue === 'DATE_OLDEST') {
        demands.orderBy('demands.demandRaisedDate', 'ASC');
      }
    }
    if (offset >= 0) {
      demands.skip(offset);
    }

    if (limit) {
      demands.take(limit);
    }

    // demands.orderBy('demands.id', 'DESC');

    let result = await demands.getManyAndCount();
    return {
      count: result[1],
      result: result[0],
    };
  }

  async findDemand(userId, id) {
    const incometaxDemands = await AutOutstandingDemand.findOne({
      where: { id },
      relations: ['client'],
    });

    return incometaxDemands;
  }

  async getClientDemand(id: number, query: any) {
    const { offset, limit } = query;
    try {
      const clientCredential = await AutClientCredentials.findOne({
        where: { id },
        relations: ['client'],
      });
      if (clientCredential) {
        const demandDetails = await createQueryBuilder(AutOutstandingDemand, 'autOutstandingDemand')
          .leftJoinAndSelect('autOutstandingDemand.client', 'client')
          .where('client.id =:id', { id: clientCredential?.client?.id });

        if (offset) {
          demandDetails.skip(offset);
        }

        if (limit) {
          demandDetails.take(limit);
        }

        let result = await demandDetails.getManyAndCount();
        return {
          count: result[1],
          result: result[0],
        };
      }
    } catch (error) {
      console.log('error occured while fetching income tax client demands', error);
    }
  }

  async getIncomeTaxEproceedings(userId: number, query: any) {
    const { limit, offset } = query;
    let user = await User.findOne({
      where: { id: userId },
      relations: ['organization'],
    });
    let epro = createQueryBuilder(AutEProceedingFya, 'autEProceedingFya')
      .leftJoinAndSelect('autEProceedingFya.client', 'client')
      .leftJoinAndSelect('autEProceedingFya.notices', 'notice')
      .where('autEProceedingFya.organizationId = :id', { id: user?.organization?.id });

    if (query.search) {
      epro.andWhere(
        new Brackets((qb) => {
          qb.where('autEProceedingFya.pan LIKE :pansearch', {
            pansearch: `%${query.search}%`,
          });
          qb.orWhere('client.displayName LIKE :namesearch', {
            namesearch: `%${query.search}%`,
          });
          qb.where('autEProceedingFya.proceedingName LIKE :prosearch', {
            prosearch: `%${query.search}%`,
          });
        }),
      );
    }

    if (query.section) {
      if (query.section === 'null') {
        epro.andWhere('autEProceedingFya.noticeName is NULL');
      } else {
        epro.andWhere('autEProceedingFya.noticeName like :sectionsearch', {
          sectionsearch: `%${query.section}%`,
        });
      }
    }

    if (query.assessmentYear) {
      if (query.assessmentYear === 'null') {
        epro.andWhere('autEProceedingFya.assesmentYear = 0');
      } else {
        epro.andWhere('autEProceedingFya.assesmentYear like :as', {
          as: `%${query.assessmentYear}%`,
        });
      }
    }

    if (query.clientCategory) {
      epro.andWhere(`client.category = :clientCategory`, { clientCategory: query.clientCategory });
    }

    if (offset) {
      epro.skip(offset);
    }

    if (limit) {
      epro.take(limit);
    }

    epro.orderBy('autEProceedingFya.assesmentYear', 'DESC');

    let result = await epro.getManyAndCount();
    return {
      count: result[1],
      result: result[0],
    };
  }

  async getIncomeTaxFyiEproceedings(userId: number, query: any) {
    const { limit, offset } = query;
    let user = await User.findOne({
      where: { id: userId },
      relations: ['organization'],
    });
    let epro = createQueryBuilder(AutEProceedingFyi, 'autEProceedingFyi')
      .leftJoinAndSelect('autEProceedingFyi.client', 'client')
      .leftJoinAndSelect('autEProceedingFyi.notices', 'notice')
      .where('autEProceedingFyi.organizationId = :id', { id: user?.organization?.id });

    if (query.search) {
      epro.andWhere(
        new Brackets((qb) => {
          qb.where('autEProceedingFyi.pan LIKE :pansearch', {
            pansearch: `%${query.search}%`,
          });
          qb.orWhere('client.displayName LIKE :namesearch', {
            namesearch: `%${query.search}%`,
          });
          qb.where('autEProceedingFyi.proceedingName LIKE :prosearch', {
            prosearch: `%${query.search}%`,
          });
        }),
      );
    }

    if (query.section) {
      if (query.section === 'null') {
        epro.andWhere('autEProceedingFyi.noticeName is NUll');
      } else {
        epro.andWhere('autEProceedingFyi.noticeName like :sectionsearch', {
          sectionsearch: `%${query.section}%`,
        });
      }
    }

    if (query.assessmentYear) {
      if (query.assessmentYear === 'null') {
        epro.andWhere('autEProceedingFyi.assessmentYear = 0');
      } else {
        epro.andWhere('autEProceedingFyi.assessmentYear like :as', {
          as: `%${query.assessmentYear}%`,
        });
      }
    }

    if (query.clientCategory) {
      epro.andWhere(`client.category = :clientCategory`, { clientCategory: query.clientCategory });
    }
    epro.orderBy('autEProceedingFyi.assessmentYear', 'DESC');

    if (offset) {
      epro.skip(offset);
    }

    if (limit) {
      epro.take(limit);
    }
    let result = await epro.getManyAndCount();
    return {
      count: result[1],
      result: result[0],
    };
  }

  async findEproceeding(userId, id) {
    const queryBuilder = await createQueryBuilder(AutEProceedingFya, 'eproceeding')
      .leftJoinAndSelect('eproceeding.client', 'client')
      .leftJoinAndSelect('eproceeding.notices', 'notices')
      .leftJoinAndSelect('notices.responses', 'responses')
      .where('eproceeding.id = :id', { id });

    const incometaxEpro = await queryBuilder.getOne();
    return incometaxEpro;
  }

  async findEproceedingFyi(userId, id) {
    const queryBuilder = await createQueryBuilder(AutEProceedingFyi, 'eproceeding')
      .leftJoinAndSelect('eproceeding.client', 'client')
      .leftJoinAndSelect('eproceeding.notices', 'notices')
      .leftJoinAndSelect('notices.responses', 'responses')
      .where('eproceeding.id = :id', { id });

    const incometaxEpro = await queryBuilder.getOne();
    return incometaxEpro;
  }

  async getFyaNotice(userId, id) {
    const notice = await AutFyaNotice.findOne({
      where: { id },
      relations: ['client', "responses"],
    });
    return notice;
  }

  async getFyiNotice(userId, id) {
    const notice = await AutFyiNotice.findOne({
      where: { id },
      relations: ['client', 'responses'],
    });
    return notice;
  }

  async getClientEproceeding(id: number) {
    try {
      const clientCredential = await AutClientCredentials.findOne({
        where: { id },
        relations: ['client'],
      });
      if (clientCredential) {
        const eproDetails = await AutEProceeding.find({
          where: { client: clientCredential?.client },
        });
        return eproDetails;
      }
    } catch (error) {
      console.log('error occured while fetching income tax client demands', error);
    }
  }

  async getClientProceedingFya(id: number, query: any) {
    const { limit, offset } = query;
    try {
      const clientCredential = await AutClientCredentials.findOne({
        where: { id },
        relations: ['client'],
      });
      if (clientCredential) {
        const clientEpro = createQueryBuilder(AutEProceedingFya, 'autEProceedingFya')
          .leftJoinAndSelect('autEProceedingFya.client', 'client')
          .leftJoinAndSelect('autEProceedingFya.notices', 'notice')
          .where('client.id =:id', { id: clientCredential?.client?.id });

        clientEpro.orderBy('autEProceedingFya.assesmentYear', 'DESC');

        if (offset >= 0) {
          clientEpro.skip(offset);
        }

        if (limit) {
          clientEpro.take(limit);
        }

        let result = await clientEpro.getManyAndCount();
        return {
          count: result[1],
          result: result[0],
        };
      }
    } catch (error) {
      console.log('error occured while fetching income tax client demands', error);
    }
  }

  async getFyaSections(userId: number) {
    try {
      let user = await User.findOne({
        where: { id: userId },
        relations: ['organization'],
      });

      const sectionsData = await createQueryBuilder(AutEProceedingFya, 'autEProceedingFya')
        .select('autEProceedingFya.noticeName', 'noticeName')
        .where('autEProceedingFya.organizationId = :id', { id: user.organization.id })
        .groupBy('autEProceedingFya.noticeName')
        .getRawMany();

      const filteredSections = sectionsData
        .map((section) => section.noticeName)
        .filter((section) => section !== '' && section !== null);

      return filteredSections;
    } catch (error) {
      console.error('Error occurred while fetching sections:', error);
      throw error;
    }
  }

  async getFyiSections(userId: number) {
    try {
      let user = await User.findOne({
        where: { id: userId },
        relations: ['organization'],
      });

      const sectionsData = await createQueryBuilder(AutEProceedingFyi, 'autEProceedingFyi')
        .select('autEProceedingFyi.noticeName', 'noticeName')
        .where('autEProceedingFyi.organizationId = :id', { id: user.organization.id })
        .groupBy('autEProceedingFyi.noticeName')
        .getRawMany();

      const filteredSections = sectionsData
        .map((section) => section.noticeName)
        .filter((section) => section !== '' && section !== null);

      return filteredSections;
    } catch (error) {
      console.error('Error occurred while fetching sections:', error);
      throw error;
    }
  }

  async getDemandsSections(userId: number) {
    try {
      let user = await User.findOne({
        where: { id: userId },
        relations: ['organization'],
      });

      const sectionsData = await createQueryBuilder(AutOutstandingDemand, 'autOutstandingDemand')
        .select('autOutstandingDemand.sectionCodeText', 'sectionCodeText')
        .where('autOutstandingDemand.organizationId = :id', { id: user.organization.id })
        .groupBy('autOutstandingDemand.sectionCodeText')
        .getRawMany();

      const filteredSections = sectionsData
        .map((section) => section.sectionCodeText)
        .filter((section) => section !== '' && section !== null);

      return filteredSections;
    } catch (error) {
      console.error('Error occurred while fetching sections:', error);
      throw error;
    }
  }

  async getClientProceedingFyi(id: number, query: any) {
    const { limit, offset } = query;
    try {
      const clientCredential = await AutClientCredentials.findOne({
        where: { id },
        relations: ['client'],
      });
      let clientEpro = createQueryBuilder(AutEProceedingFyi, 'autEProceedingFyi')
        .leftJoinAndSelect('autEProceedingFyi.client', 'client')
        .leftJoinAndSelect('autEProceedingFyi.notices', 'notice')
        .where('client.id = :id', { id: clientCredential?.client?.id });
      clientEpro.orderBy('autEProceedingFyi.assessmentYear', 'DESC');

      if (offset >= 0) {
        clientEpro.skip(offset);
      }

      if (limit) {
        clientEpro.take(limit);
      }

      // epro.orderBy('autEProceedingFya.id', 'DESC');

      let result = await clientEpro.getManyAndCount();
      return {
        count: result[1],
        result: result[0],
      };
    } catch (error) {
      console.log('Error occurred while fetching income tax client demands', error);
    }
  }

  async getAuthToken() {
    const axios = require('axios');
    let data = JSON.stringify({
      entity: '',
      serviceName: 'wLoginService',
    });

    let config = {
      method: 'post',
      maxBodyLength: Infinity,
      url: 'https://eportal.incometax.gov.in/iec/loginapi/login',
      headers: {
        'Content-Type': 'application/json',
        'Cookie':
          '4a75cee7266fb5ae654dc5e51e6a9fe3=d572fbc9cf9510b71280bde0385ff167; 83b39a8b4ea14550011a0e5e6ca7f4cc=469b38b021805b99b052a60d9c64a00c',
      },
      data: data,
    };

    axios
      .request(config)
      .then((response) => {
        // console.log(response);
        const abc = response.data;
        // console.log('bbbbbbbbbbbbbbbbbbbbbbbbbbbbb', response.headers);
        // let data1: any = {
        //   aadhaarMobileValidated: `${abc.aadhaarMobileValidated}`,
        //   contactEmail: null,
        //   contactMobile: null,
        //   contactPan: null,
        //   dtoService: `${abc.dtoService}`,
        //   email: null,
        //   entity: `${abc.entity}`,
        //   entityType: `${abc.entityType}`,
        //   errors: [],
        //   exemptedPan: `${abc.exemptedPan}`,
        //   forgnDirEmailId: null,
        //   imagePath: null,
        //   imgByte: null,
        //   mobileNo: null,
        //   otp: null,
        //   otpGenerationFlag: null,
        //   otpSourceFlag: null,
        //   otpValdtnFlg: null,
        //   pass: 'bWFub2prYWxpeWExKg==',
        //   passValdtnFlg: null,
        //   reqId: `${abc.reqId}`,
        //   role: `${abc.role}`,
        //   secAccssMsg: '',
        //   secLoginOptions: '',
        //   serviceName: 'loginService',
        //   uidValdtnFlg: `${abc.uidValdtnFlg}`,
        //   userConsent: `${abc.userConsent}`,
        // };
        // console.log({ data1 });
        // let config1 = {
        //   method: 'post',
        //   maxBodyLength: Infinity,
        //   url: 'https://eportal.incometax.gov.in/iec/loginapi/login',
        //   headers: {
        //     'Content-Type': 'application/json',
        //     'Origin': 'https://eportal.incometax.gov.in',
        //     'Referer': 'https://eportal.incometax.gov.in/iec/foservices/',
        //   },
        //   data: data1,
        // };

        // axios
        //   .request(config1)
        //   .then((response) => {
        //     console.log(JSON.stringify(response.data));
        //   })
        //   .catch((error) => {
        //     console.log(error);
        //   });
      })
      .catch((error) => {
        console.log(error);
      });
  }

  async updateClientAutCredentials(id: any, body: any) {
    try {
      const clientCredential = await AutClientCredentials.findOne({
        where: { id },
        relations: ['client'],
      });
      if (clientCredential) {
        clientCredential.password = body.password;
        clientCredential.save();
        return clientCredential;
      }
    } catch (error) {
      console.log('Error occur while update the incomeTax client credentials', error);
    }
  }

  async sendActivityData(userId, body) {
    let user = await User.findOne({
      where: { id: userId },
    });
    const autoClient = await AutClientCredentials.findOne({
      where: { id: body.id },
      relations: ['client'],
    });

    const moduleArray = body.selectedModules;
    const moduleObject = {
      P: 'Basic Profile',
      F: 'Forms',
      R: 'Returns',
      OD: 'Outstanding Demand',
      EP: 'e-Proceedings',
    };
    const matchedValues = moduleArray?.map((item: string) => moduleObject[item] || 'Not Found');
    const autActivity = new AutActivity();
    autActivity.modules = body.selectedModules;
    autActivity.action = `DATA_SYNCHRONIZATION`;
    autActivity.userId = userId;
    autActivity.clientId = autoClient?.client?.id;
    autActivity.autClientCredentialsId = body.id;
    autActivity.remarks = `${user?.fullName
      } synchronizes client data for specific modules of ${matchedValues.join(', ')}`;
    autActivity.save();
  }

  async getActivityLogData(id: any, query: any) {
    try {
      const autActivity = await createQueryBuilder(AutomationMachines, 'autActivity')
        .leftJoinAndSelect('autActivity.user', 'user')
        .where('autActivity.autoCredentials =:id', { id: id });

      if (query.fromDate && query.toDate) {
        const { startTime, endTime } = dateFormation(query.fromDate, query.toDate);
        autActivity
          .andWhere('Date(autActivity.created_at) >= :startTime', { startTime })
          .andWhere('Date(autActivity.created_at) <= :endTime', { endTime });
      }

      autActivity.orderBy('autActivity.id', 'DESC');
      let result = await autActivity.getMany();
      return result;
    } catch (error) {
      console.error('Error fetching activity log data:', error);
      return null; // Or throw an error
    }
  }

  async checkAutomationInOrganization(userId: any) {
    let user = await User.findOne({
      where: { id: userId },
      relations: ['organization'],
    });
    // const automation = await AutomationMachines.find({ relations: ['autoCredentials'] });
    const sectionsData = await createQueryBuilder(AutomationMachines, 'automationMachines')
      .leftJoin('automationMachines.autoCredentials', 'autoCredentials')
      .where('autoCredentials.organizationId = :id', { id: user.organization.id })
      .andWhere('automationMachines.status = :status', { status: 'PENDING' })
      .getCount();
    return sectionsData;
  }

  async importCredentials(userId: number, data: any) {
    try {
      if (!data.length) {
        throw new BadRequestException('No Data Available');
      }
      let addedDate = {
        existingIncomeTaxClinet: [],
        existingIncometaxRecord: [],
        incomeTaxAdd: [],
        incomeTaxReject: [],
        gstrAdd: [],
        gstrReject: [],
        existingGstrClinet: [],
        existingGstrRecord: [],
      };
      const user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
      let createCredentials = async (userId, data) => {
        const client = await Client.findOne({ where: { id: data.client } });

        const password = new Password();
        password.website = data.website.trim();
        password.websiteUrl = data.websiteUrl.trim();
        password.loginId = data.loginId.trim();
        password.password = data.password.trim();
        password.client = client;
        password['userId'] = userId;
        password.isExistingAtomPro = data.isExistingAtomPro;
        await password.save();
      };

      const organizationPreferences: any = await OrganizationPreferences.findOne({
        where: { organization: user?.organization },
      });

      const organizationIncomeTaxLimit = organizationPreferences?.automationConfig?.incomeTaxLimit;
      const organizationGstrLimit = organizationPreferences?.automationConfig?.gstrLimit;
     

      for (const item of data) {
        const client = await Client.findOne(item.client);

        if (item?.panNumber && item?.password) {
          const details = {
            website: 'Income Tax | e-Filing (PAN)',
            websiteUrl: 'https://eportal.incometax.gov.in/iec/foservices/#/login',
            loginId: String(item?.panNumber),
            password: String(item?.password),
            client: item?.client,
            isExistingAtomPro: IsExistingAtomPro.NO,
          };

          // insert into income client tax Table
          const autClientCredential = await AutClientCredentials.count({
            where: { organizationId: user?.organization?.id },
          });

          const existingClinet = await AutClientCredentials.findOne({
            where: {
              clientId: item.client,
            },
          });

          if (existingClinet) {
            await createCredentials(userId, details);
            addedDate.existingIncomeTaxClinet.push(item);
          } else {
            const existingRecord = await AutClientCredentials.findOne({
              where: { organizationId: user?.organization?.id, panNumber: item.panNumber },
            });

            if (existingRecord) {
              addedDate.existingIncometaxRecord.push(item);
              await createCredentials(userId, details);
            } else {
              if (organizationIncomeTaxLimit >= autClientCredential) {
                const clientCredentials = new AutClientCredentials();
                clientCredentials.panNumber = String(item.panNumber).trim();
                clientCredentials.password = String(item?.password).trim();
                clientCredentials.client = client;
                clientCredentials.organizationId = user?.organization?.id;
                clientCredentials.syncStatus = syncStatus.NOTSYNC;
                await clientCredentials.save();
                addedDate.incomeTaxAdd.push(item);
                details.isExistingAtomPro = IsExistingAtomPro.YES;
                await createCredentials(userId, details);
              } else {
                addedDate.incomeTaxReject.push(item);
                await createCredentials(userId, details);
              }
            }
          }
        }
        if (item?.gstId && item?.gstPassword) {
          const details = {
            website: 'GST | e-Filing',
            websiteUrl: 'https://services.gst.gov.in/services/login',
            loginId: String(item?.gstId),
            password: String(item?.gstPassword),
            client: item?.client,
            isExistingAtomPro: IsExistingAtomPro.NO,
          };
          // insert into Gstr client  Table
          const existingClinet = await GstrCredentials.findOne({
            where: {
              clientId: item.client,
            },
          });
          if (existingClinet) {
            addedDate.existingGstrClinet.push(item);
            await createCredentials(userId, details);
          } else {
            const checkCredential = await GstrCredentials.findOne({
              where: { organizationId: user?.organization?.id, userName: item.gstId },
            });

            if (checkCredential) {
              addedDate.existingGstrRecord.push(item);
              await createCredentials(userId, details);
            } else {
              const gstrCredentialsCount = await GstrCredentials.count({
                where: { organizationId: user?.organization?.id },
              });
             
              if (organizationGstrLimit >= gstrCredentialsCount) {

                const gstrCredentials = new GstrCredentials();
                gstrCredentials.userName = String(item?.gstId.trim());
                gstrCredentials.password = String(item?.gstPassword).trim();
                gstrCredentials.userId = user?.id;
                gstrCredentials.clientId = client.id;
                gstrCredentials.organizationId = user?.organization?.id;
                gstrCredentials.save();
                addedDate.gstrAdd.push(item);
                details.isExistingAtomPro = IsExistingAtomPro.YES;
                await createCredentials(userId, details);
              } else {

                addedDate.gstrReject.push(item);
                await createCredentials(userId, details);
              }
            }
          }
        }

        if (item?.gstNumber) {
          const updatedClient = await this.getClientGstUpdatePayload(item, client);
          this.clientService.update(userId, item.client, { ...client, ...updatedClient });
        }
      }

      return addedDate;
    } catch (error) {
      console.log(error);
    }
  }

  async getClientGstUpdatePayload(item, client) {
    const existingClient = await Client.findOne({ where: { gstNumber: item?.gstNumber } });
    if (existingClient) {
      return null;
    }
    const gstDetailsData = await this.clientService.gstData(item?.gstNumber);

    if (gstDetailsData.data.sts) {
      const address: any = {
        communicationfulladdress: this.clientService.getGstAddress(
          gstDetailsData?.data?.pradr?.addr,
        ),
      };
      address['billingfulladdress'] = address?.communicationfulladdress;
      address['shippingfulladdress'] = address?.communicationfulladdress;

      client.issameaddress = true;
      client.gstVerified = true;
      client.gstNumber = gstDetailsData?.data?.gstin;
      client.legalName = gstDetailsData?.data?.lgnm;
      client.address = address;
      client.gstRegistrationDate = gstDetailsData?.data?.rgdt || null;
      client.tradeName = gstDetailsData?.data?.tradeNam;
      client.constitutionOfBusiness = gstDetailsData?.data?.ctb;
      client.placeOfSupply = gstDetailsData?.data?.pradr?.addr?.stcd;
      client.buildingName = gstDetailsData?.data?.pradr?.addr?.bnm;
      client.street = gstDetailsData?.data?.pradr?.addr?.st;
      client.city = gstDetailsData?.data?.pradr?.addr?.dst;
      client.state = gstDetailsData?.data?.pradr?.addr?.stcd;
      client.pincode = gstDetailsData?.data?.pradr?.addr?.pncd;

      const gstCharAt13 = client.gstNumber.charAt(13);
      const extractedNumber = client.gstNumber.substring(2, client.gstNumber.length - 3);
      if (gstCharAt13 === 'D') {
        const pattern = TAN_REGEX.test(extractedNumber);
        if (pattern) {
          client.tanNumber = client.tanNumber;
        } else {
          client.panNumber = client.panNumber;
        }
        client.registrationType = RegistrationType.TAX_DEDUCTOR;
      } else if (gstCharAt13 === 'Z' || gstCharAt13 === 'C') {
        client.panNumber = client.panNumber;
        client.registrationType =
          gstCharAt13 === 'Z' ? RegistrationType.REGULAR_TAXPAYER : RegistrationType.TAX_COLLECTOR;
      }
    } else {
      client.gstNumber = item.gstNumber;
    }

    return client;
  }

  async organizationScheduling(userId) {
    const user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
    const organizarionId = user.organization.id;

    let data = JSON.stringify({
      modules: ['P', 'F', 'R', 'OD', 'RWA', 'EP'],
      orgId: organizarionId,
      type: 'INCOMETAX',
    });

    let config: any = {
      method: 'post',
      maxBodyLength: Infinity,
      url: 'http://test-cmd-api.vider.in/vider/quantum/api/automation/bulk/sync',
      headers: {
        'X-USER-ID': userId,
        'Content-Type': 'application/json',
      },
      data: data,
    };

    axios
      .request(config)
      .then((response) => {
      })
      .catch((error) => {
        console.log(error);
      });
  }
}
