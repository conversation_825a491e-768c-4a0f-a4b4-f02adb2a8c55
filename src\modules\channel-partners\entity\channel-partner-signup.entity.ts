import { User } from 'src/modules/users/entities/user.entity';
import {
  BaseEntity,
  Column,
  CreateDateColumn,
  Entity,
  ManyToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { ChannelPartner } from './channel-partner.entity';
import { CouponCode } from './coupon-code.entity';

export enum leadStatusEnum {
  NEW = 'NEW',
  CONVERTED = 'CONVERTED',
  CONTACTED = 'CONTACTED',
  HOLD = 'HOLD',
}

@Entity()
export class ChannelPartnerSignup extends BaseEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  organizationId: number;

  @ManyToOne(() => ChannelPartner, (partner) => partner.signups)
  channelPartner: ChannelPartner;

  @ManyToOne(() => CouponCode, (coupon) => coupon.signups)
  coupon: CouponCode;

  @ManyToOne(() => User, (user) => user.channelSignups)
  user: User;

  @Column({ type: 'enum', enum: leadStatusEnum })
  leadStatus: leadStatusEnum;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
