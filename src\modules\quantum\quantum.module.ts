import { <PERSON><PERSON><PERSON> } from '@nestjs/common';
import { QuantumController } from './quantum.controller';
import { QuantumService } from './quantum.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import AtomToQtmrequests from './entity/atm-qtm-requests.entity';
import { QuantumSubscriber } from 'src/event-subscribers/quantum-subscriber';

@Module({
  imports: [TypeOrmModule.forFeature([AtomToQtmrequests])],
  controllers: [QuantumController],
  providers: [QuantumService,QuantumSubscriber],
})
export class QuantumModule {}
