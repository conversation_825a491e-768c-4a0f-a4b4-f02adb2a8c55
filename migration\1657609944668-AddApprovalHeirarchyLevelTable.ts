import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddApprovalHeirarchyLevelTable1657609944668
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `create table approval_heirarchy_level (
            id int not null auto_increment,
            role_id int null,
            user_id int null,
            approval_hierarchy_id int null,
            created_at datetime(6) default current_timestamp(6),
            updated_at datetime(6) default current_timestamp(6),
            primary key (id),
            CONSTRAINT FK_approval_heirarchy_level_approval_hierarchy_id FOREIGN KEY (approval_hierarchy_id) REFERENCES approval_heirarchy (id) ON DELETE SET NULL
        );`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}
