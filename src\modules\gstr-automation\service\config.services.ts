import { BadRequestException, Injectable, InternalServerErrorException } from '@nestjs/common';
import { User, UserStatus } from 'src/modules/users/entities/user.entity';
import * as xlsx from 'xlsx';
import axios from 'axios';
import GstrCredentials, { GstrStatus } from '../entity/gstrCredentials.entity';
import { checkAtomProConfigGstr } from 'src/utils/atomProReUse';
import { Brackets, createQueryBuilder } from 'typeorm';
import * as ExcelJS from 'exceljs';
import GstrNoticeOrders, { CreatedType } from '../entity/noticeOrders.entity';
import * as moment from 'moment';
import GstrProfile from '../entity/gstrProfile.entity';
import GstrAdditionalNoticeOrders from '../entity/gstrAdditionalOrdersAndNotices.entity';
import Task from 'src/modules/tasks/entity/task.entity';
import { ReturnsData } from 'src/modules/gstr-register/entity/returns-data.entity';
import { TaskStatusEnum } from 'src/modules/tasks/dto/types';
import Activity, { ActivityType } from 'src/modules/activity/activity.entity';
import { Event_Actions } from 'src/event-listeners/actions';
import { getTitle } from 'src/utils';
import TaskStatus from 'src/modules/tasks/entity/task-status.entity';
import { StorageService } from 'src/modules/storage/storage.service';
import OrganizationPreferences from 'src/modules/organization-preferences/entity/organization-preferences.entity';

@Injectable()
export class GstrConfigService {
  constructor(private storageService: StorageService) { }
  async gstAtomClient(userId: number, id: number) {
    const gstClient = await GstrCredentials.findOne({
      where: { id: id },
      relations: ['client'],
    });
    return gstClient?.client;
  }

  async disableAtomProGstrClient(userId: number, body: any) {
    const ids = body.ids;
    for (let gstrId of ids) {
      const gstrClient = await GstrCredentials.findOne({ where: { id: gstrId } });
      if (gstrClient) {
        gstrClient.status = GstrStatus.DISABLE;
        await gstrClient.save();
      }
    }
  }

  async disableGstrSingleClient(id: number, userId: number) {
    const gstrClient = await GstrCredentials.findOne({ where: { id: id } });
    if (gstrClient) {
      gstrClient.status = GstrStatus.DISABLE;
      await gstrClient.save();
    }
  }

  async getDeletedGstrClients(userId: number, query) {
    const { limit, offset } = query;
    const user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
    const organizationId = user?.organization?.id;
    if (organizationId) {
      let deletedClients = createQueryBuilder(GstrCredentials, 'gstrCredentials')
        .leftJoinAndSelect('gstrCredentials.client', 'client')
        .leftJoinAndSelect('gstrCredentials.profile', 'profile')
        .where('gstrCredentials.organizationId = :organizationId', {
          organizationId: organizationId,
        })
        .andWhere('client.status != :status', { status: UserStatus.DELETED })
        .andWhere('gstrCredentials.status = :disStatus', { disStatus: GstrStatus.DISABLE });
      const sort = typeof query?.sort === 'string' ? JSON.parse(query.sort) : query?.sort;
      if (sort?.column) {
        const columnMap: Record<string, string> = {
          displayName: 'client.displayName',
        };
        const column = columnMap[sort.column] || sort.column;
        deletedClients.orderBy(column, sort.direction.toUpperCase());
      } else {
        deletedClients.orderBy('gstrCredentials.updatedAt', 'DESC');
      }

      if (query.search) {
        deletedClients.andWhere(
          new Brackets((qb) => {
            qb.where('gstrCredentials.userName LIKE :userName', {
              userName: `%${query.search}%`,
            });
            qb.orWhere('client.displayName LIKE :namesearch', {
              namesearch: `%${query.search}%`,
            });
            qb.orWhere('client.clientId LIKE :namesearch', {
              namesearch: `%${query.search}%`,
            });
            qb.orWhere('profile.gstin LIKE :namesearch', {
              namesearch: `%${query.search}%`,
            });
          }),
        );
      }

      if (offset >= 0) {
        deletedClients.skip(offset);
      }

      if (limit) {
        deletedClients.take(limit);
      }

      let result = await deletedClients.getManyAndCount();
      return {
        count: result[1],
        result: result[0],
      };
    }
  }

  async exportdeletedGstClient(userId: number, query: any) {
    const newQuery = { ...query, offset: 0, limit: 100000000 };
    let clients = await this.getDeletedGstrClients(userId, newQuery);

    if (!clients.result.length) throw new BadRequestException('No Data for Export');

    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Deleted GST Clients');
    const headers = [
      { header: 'S.No', key: 'serialNo' },
      { header: 'Client ID', key: 'clientId' },
      { header: 'Client #', key: 'clientNumber' },
      { header: 'GSTIN', key: 'gstNumber' },
      { header: 'Category', key: 'category' },
      { header: 'Sub Category', key: 'subCategory' },
      { header: 'Client Name', key: 'displayName' },
      { header: 'Username', key: 'userName' },
      { header: 'Status Updated At', key: 'statusUpdatedAt' },
    ];

    worksheet.columns = headers;

    let serialCounter = 1; // Initialize a counter

    // Initialize array to store max lengths for each column
    const columnMaxLengths = Array(headers.length).fill(0);

    clients.result.forEach((client) => {
      function capitalizeFirstLetter(string) {
        return string.charAt(0).toUpperCase() + string.slice(1);
      }
      const rowData = {
        serialNo: serialCounter++,
        clientId: client?.client?.clientId,
        clientNumber: client?.client?.clientNumber,
        category: client?.client?.category ? getTitle(client?.client?.category) : "",
        subCategory: client?.client?.subCategory ? getTitle(client?.client?.subCategory) : "",
        displayName: client.client.displayName,
        gstNumber: client.client.gstNumber,
        userName: client.userName,
        statusUpdatedAt: moment(client?.updatedAt).format("DD-MM-YYYY h:mm a")
      };

      const row = worksheet.addRow(rowData);

      worksheet.columns.forEach((column, colIndex) => {
        const headerLength = column.header?.length || 0; // Length of header
        const cellLength = rowData[column.key]?.toString().length || 0; // Length of cell value
        columnMaxLengths[colIndex] = Math.max(
          columnMaxLengths[colIndex] || 0,
          headerLength,
          cellLength,
        );
      });
    });

    // Apply column widths with padding
    worksheet.columns.forEach((column, colIndex) => {
      column.width = columnMaxLengths[colIndex] + 3; // Add padding for readability
    });

    // Style the header row and center alignment
    const headerRow = worksheet.getRow(1);
    headerRow.font = { bold: true };
    headerRow.eachCell((cell) => {
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '64B5F6' },
      };
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' },
      };
      cell.alignment = { horizontal: 'center', vertical: 'middle' };
    });

    worksheet.columns.forEach((column) => {
      if (column.key === 'displayName') {
        column.width = 50;
        column.alignment = { wrapText: true, horizontal: 'center', vertical: 'middle' };
      } else {
        column.alignment = { horizontal: 'center', vertical: 'middle' };
      }
    });

    // Apply wrapText for all rows
    worksheet.eachRow((row) => {
      row.eachCell((cell) => {
        cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true }; // Center alignment and wrap text
      });
    });

    // Freeze the header row
    worksheet.views = [{ state: 'frozen', ySplit: 1 }];

    const buffer = await workbook.xlsx.writeBuffer();
    return buffer;
  }

  async enableGstrClient(id: number, userId: number) {
    const user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
    const organizationId = user?.organization?.id;
    const gstrClient = await GstrCredentials.findOne({ where: { id: id } });
    if (gstrClient) {
      const checkGstr = await checkAtomProConfigGstr(organizationId);
      gstrClient.status = GstrStatus.ENABLE;
      if (checkGstr === true) {
        await gstrClient.save();
      } else {
        throw new BadRequestException(checkGstr);
      }
    }
  }

  async enableBulkGstrClient(userId: number, body: any) {
    const user = await User.findOne({
      where: { id: userId },
      relations: ['organization'],
    });

    const organizationId = user?.organization?.id;
    if (!organizationId) {
      throw new BadRequestException('Organization not found for this user');
    }

    // Fetch org preferences only once
    const orgPreferences: any = await OrganizationPreferences.findOne({
      where: { organization: organizationId },
    });

    const isGstrEnabled = orgPreferences?.automationConfig?.gstr === 'YES';
    if (!isGstrEnabled) {
      throw new BadRequestException(
        'Subscribe Atom Pro Gstr to access for this Client',
      );
    }

    const gstrCredentialCount = await GstrCredentials.count({
      where: { organizationId, status: GstrStatus.ENABLE },
    });

    const organizationGstrLimit =
      orgPreferences?.automationConfig?.gstrLimit || 0;

    // check if bulk enable will exceed the limit
    const clientsToEnable = body?.gstrClients?.length || 0;
    if (gstrCredentialCount + clientsToEnable > organizationGstrLimit) {
      throw new BadRequestException(
        `Cannot enable clients. You can only enable up to ${organizationGstrLimit} clients in Atom Pro GSTR.`,
      );
    }

    // If within limit, enable all
    for (let i of body?.gstrClients) {
      const gstrClient = await GstrCredentials.findOne({ where: { id: i?.id } });
      if (gstrClient) {
        gstrClient.status = GstrStatus.ENABLE;
        await gstrClient.save();
      }
    }
  }

  async getBulkSyncStatus(userId: number) {
    try {
      const user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
      let data = '';

      let config: any = {
        method: 'get',
        maxBodyLength: Infinity,
        url: `${process.env.CAMUNDA_URL}/vider/quantum/api/automation/bulk/sync/${user?.organization?.id}/gstr`,
        headers: {},
        data: data,
      };

      const response = await axios.request(config);
      return JSON.stringify(response?.data);
    } catch (error) {
      console.log('error occure while getting into getBulkSyncStatus', error.message);
    }
  }

  async updateEnableStatus(userId: number) {
    try {
      const user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
      let config: any = {
        method: 'put',
        maxBodyLength: Infinity,
        url: `${process.env.CAMUNDA_URL}/vider/quantum/api/automation/bulk/sync/${user?.organization?.id}/enable/gstr`,
        headers: {},
        data: '',
      };
      const response = await axios.request(config);
    } catch (error) {
      console.log('error while updateEnableStatus', error);
    }
  }

  async updateDisableStatus(userId: number) {
    try {
      const user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
      let config: any = {
        method: 'put',
        maxBodyLength: Infinity,
        url: `${process.env.CAMUNDA_URL}/vider/quantum/api/automation/bulk/sync/${user?.organization?.id}/disable/gstr`,
        headers: {},
        data: '',
      };
      const response = await axios.request(config);
    } catch (error) {
      console.log('error while updateEnableStatus', error);
    }
  }

  async organizationGstrScheduling(userId, body) {
    const { periodicity, day, hour, minute, weekDay } = body;

    try {
      const user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
      const organizarionId = user.organization.id;

      const amPm = hour >= 12 ? 'pm' : 'am'; // Determine AM/PM
      const adjustedHour = hour > 12 ? hour - 12 : hour; // Convert to 12-hour format if needed

      // Prepare the originalBody object based on the provided periodicity
      const originalBody = [
        {
          periodicity: periodicity || 'DAILY', // Default to 'DAILY' if not provided
          days: periodicity === 'WEEKLY' ? (weekDay ? weekDay.toUpperCase() : null) : null,
          daysInstance: {}, // Can add more logic if required for weekly scheduling
          hour: adjustedHour, // Using 12-hour format
          minute: minute,
          seconds: 0,
          amPm: amPm, // "am" or "pm" based on the hour
          dayOfMonth: periodicity === 'MONTHLY' && day ? day : 0, // Set day if 'MONTHLY'
          month: 0, // Assuming no month field for DAILY/WEEKLY
          intervalMinutes: 0, // Assuming no intervalMinutes for now
        },
      ];
      let data = JSON.stringify({
        modules: ['P', 'NAO', 'ANO', 'OD', 'LB'],
        orgId: organizarionId,
        type: 'GSTR',
        schedules: originalBody,
      });

      let config: any = {
        method: 'post',
        maxBodyLength: Infinity,
        url: `${process.env.CAMUNDA_URL}/vider/quantum/api/automation/bulk/sync`,
        headers: {
          'X-USER-ID': userId,
          'Content-Type': 'application/json',
        },
        data: data,
      };

      const response = await axios.request(config);
      return JSON.stringify(response?.data);
    } catch (error) {
      console.log('error occur while organizationScheduling', error);
    }
  }

  async createNoticeAndOrderItem(userId: number, body: any) {
    try {
      const user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
      if (body?.gstrCredentialsId) {
        const gstrCreadentials = await GstrCredentials.findOne({
          where: { id: body?.gstrCredentialsId },
          relations: ['client'],
        });

        const gstProfile = await GstrProfile.findOne({
          where: { gstrCredentials: gstrCreadentials },
        });
        const noticeOrders = new GstrNoticeOrders();
        noticeOrders.orderNumber = body.orderNumber;
        noticeOrders.issuedBy = 'Manual';
        noticeOrders.type = body?.type;
        noticeOrders.amountOfDemand = body?.amountOfDemand;
        noticeOrders.description = body?.description;
        noticeOrders.dateOfIssuance = body?.dateOfIssuance
          ? moment(body?.dateOfIssuance).format('DD/MM/YYYY')
          : null;
        noticeOrders.dueDate = body?.dueDate ? moment(body?.dueDate).format('DD/MM/YYYY') : null;

        noticeOrders.client = gstrCreadentials?.client;
        noticeOrders.gstrCredentialsId = body?.gstrCredentialsId;
        noticeOrders.organizationId = user?.organization?.id;
        if (gstProfile) {
          noticeOrders.gstIn = gstProfile?.gstin;
        }
        noticeOrders.createdType = CreatedType.MANUAL;
        await noticeOrders.save();
      }
    } catch (error) {
      console.log('Error occur while CreateFyaItem', error?.message);
    }
  }

  async updateNoticeAndOrder(userId: number, body: any) {
    try {
      const user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
      if (body?.id) {
        const noticeOrders = await GstrNoticeOrders.findOne({
          where: { id: body?.id, organizationId: user?.organization?.id },
        });
        if (noticeOrders) {
          noticeOrders.orderNumber = body.orderNumber;
          noticeOrders.type = body?.type;
          noticeOrders.amountOfDemand = body?.amountOfDemand;
          noticeOrders.description = body?.description;
          noticeOrders.dateOfIssuance = body?.dateOfIssuance;
          noticeOrders.dueDate = body?.dueDate;
          await noticeOrders.save();
          return true;
        }
      }
    } catch (error) {
      throw new BadRequestException('Error Occur while Update FYA Proceeding', error?.message);
    }
  }

  async deleteNoticeAndOrder(userId: number, id: number) {
    try {
      const user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
      if (id) {
        const noticeOrder = await GstrNoticeOrders.findOne({
          where: { id: id, organizationId: user?.organization?.id },
          relations: ['storage'],
        });
        if (noticeOrder) {
          for (const storage of noticeOrder.storage) {
            await this.storageService.removeFile(storage.id, userId);
          }
          await GstrNoticeOrders.delete({ id: id, organizationId: user?.organization?.id });
          return true;
        }
      }
    } catch (error) {
      throw new BadRequestException('Error Occur while deleting Proceeding', error?.message);
    }
  }

  async createAdditionalNotice(userId: number, body: any) {
    try {
      const user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
      if (body?.gstrCredentialsId) {
        const gstrCreadentials = await GstrCredentials.findOne({
          where: { id: body?.gstrCredentialsId },
          relations: ['client'],
        });

        const gstProfile = await GstrProfile.findOne({
          where: { gstrCredentials: gstrCreadentials },
        });
        const additionalNotice = new GstrAdditionalNoticeOrders();
        additionalNotice.fy = body?.fiscalYear;
        additionalNotice.arn = body?.arn;
        additionalNotice.caseId = body?.caseId;
        additionalNotice.caseTypeId = body?.caseTypeId;
        additionalNotice.refId = body?.refId;
        additionalNotice.refNum = body?.referenceNumber;
        additionalNotice.section = body?.section;
        additionalNotice.caseFolderTypeName = body?.caseFolderType;
        additionalNotice.caseTypeName = body?.caseTypeName;
        additionalNotice.categoryType = body?.categoryType;
        additionalNotice.venue = body?.place;
        additionalNotice.personalHearning = body?.personalHearing;
        additionalNotice.nm = body?.issuedByName;
        additionalNotice.designation = body?.issuedByDesignation;
        additionalNotice.summary = body?.summary;
        additionalNotice.categoryDate = body?.dateOfIssuance
          ? moment(body?.dateOfIssuance).format('DD/MM/YYYY')
          : null;
        additionalNotice.dueDate = body?.dueDate
          ? moment(body?.dueDate).format('DD/MM/YYYY')
          : null;
        additionalNotice.description = body?.description;
        additionalNotice.client = gstrCreadentials?.client;
        additionalNotice.gstrCredentialsId = body?.gstrCredentialsId;
        additionalNotice.organizationId = user?.organization?.id;
        if (gstProfile) {
          additionalNotice.gstIn = gstProfile?.gstin;
          additionalNotice.name = gstProfile?.legalName;
        }
        additionalNotice.createdType = CreatedType.MANUAL;
        await additionalNotice.save();
        if (additionalNotice) {
          if (body.type === 'AddReplies') {
            const additionalItem = await GstrAdditionalNoticeOrders.findOne({
              where: { id: body?.additionalNoticeId },
            });
            if (additionalItem) {
              additionalItem.refStatus = 'REPLIED';
              await additionalItem.save();
            }
          }

          if (additionalNotice?.caseFolderTypeName === 'ORDERS') {
            const additionalItem = await GstrAdditionalNoticeOrders.find({
              where: { arn: additionalNotice.arn, organizationId: user?.organization?.id },
            });

            if (additionalItem) {
              for (let item of additionalItem) {
                item.caseStatus = 'CLOSED';
                await item.save();
              }
            }
          }
        }
      }
    } catch (error) {
      console.log('Error occur while CreateFyaItem', error?.message);
    }
  }

  async updateAdditionalNotice(userId: number, body: any) {
    try {
      const user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
      if (body?.id) {
        const additionalNotice = await GstrAdditionalNoticeOrders.findOne({
          where: { id: body?.id, organizationId: user?.organization?.id },
        });
        if (additionalNotice) {
          additionalNotice.fy = body?.fy;
          additionalNotice.arn = body?.arn;
          additionalNotice.refId = body?.refId;
          additionalNotice.refNum = body?.refNum;
          additionalNotice.section = body?.section;
          additionalNotice.caseFolderTypeName = body?.caseFolderTypeName;
          additionalNotice.caseTypeName = body?.caseTypeName;
          additionalNotice.categoryType = body?.categoryType;
          additionalNotice.venue = body?.venue;
          additionalNotice.personalHearning = body?.personalHearning;
          additionalNotice.nm = body?.nm;
          additionalNotice.designation = body?.designation;
          additionalNotice.categoryDate = body?.categoryDate;
          additionalNotice.dueDate = body?.dueDate;
          additionalNotice.description = body?.description;
          additionalNotice.summary = body?.summary;
          await additionalNotice.save();
          return true;
        }
      }
    } catch (error) {
      console.log('Error occur while CreateFyaItem', error?.message);
      throw new BadRequestException('Error Occur while Update FYA Proceeding', error?.message);
    }
  }

  async deleteAdditionalNotice(userId: number, id: number) {
    try {
      const user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
      if (id) {
        const additionalNotice = await GstrAdditionalNoticeOrders.findOne({
          where: { id: id, organizationId: user?.organization?.id },
          relations: ['storage'],
        });
        if (additionalNotice) {
          for (const storage of additionalNotice?.storage) {
            await this.storageService.removeFile(storage.id, userId);
          }
          await GstrAdditionalNoticeOrders.delete({
            id: id,
            organizationId: user?.organization?.id,
          });
          return true;
        }
      }
    } catch (error) {
      throw new BadRequestException('Error Occur while deleting Additional Notice', error?.message);
    }
  }

  normalizeFinancialYear(yearStr: string) {
    // '2023-2024' → '2023-2024'
    // '2024-25' → '2024-2025'
    if (yearStr.includes('-')) {
      const parts = yearStr.split('-');
      const first = parts[0];
      let second = parts[1];

      // If second part is 2 digits, add century
      if (second.length === 2) {
        const century = first.slice(0, 2);
        second = century + second;
      }

      return `${first}-${second}`;
    }
    return yearStr;
  }

  async completeTaskGstrOne(userId: number) {
    try {
      const user = await User.findOne({ where: { id: userId }, relations: ['organization'] });

      const organizationId = user?.organization?.id;
      let completedGstrOneTasks = [];
      let completedGstr3BTasks = [];
      let errorMsg = [];
      if (organizationId) {
        const tasks = await createQueryBuilder(Task, 'task')
          .leftJoin('task.organization', 'organization')
          .leftJoinAndSelect('task.client', 'client')
          .leftJoin('task.category', 'category')
          .leftJoin('task.subCategory', 'subCategory')
          .where('organization.id = :organizationId', { organizationId })
          .andWhere('category.id = :categoryId', { categoryId: 45 })
          .andWhere('subCategory.id = :subCategoryId', { subCategoryId: 111 })
          .andWhere('task.recurringStatus = :recurringStatus', { recurringStatus: 'created' })
          .andWhere('task.status != :status', { status: 'completed' })
          .andWhere('task.name LIKE :taskName', { taskName: '%GSTR-1%' })
          .andWhere('task.processInstanceId IS NULL')
          .andWhere('task.approvalStatus IS NULL')
          .getMany();
        if (tasks?.length > 0) {
          // Step 1: Get all filed GSTR1 ReturnsData for the org
          const filedReturns = await createQueryBuilder(ReturnsData, 'returnsData')
            .leftJoinAndSelect('returnsData.gstrRegister', 'gstrRegister')
            .leftJoin('gstrRegister.organization', 'organization')
            .leftJoinAndSelect('gstrRegister.client', 'client')
            .where('organization.id = :organizationId', { organizationId })
            .andWhere('returnsData.rtntype = :rtntype', { rtntype: 'GSTR1' })
            .andWhere('returnsData.status = :status', { status: 'Filed' })
            .getMany();

          if (filedReturns.length === 0) {
            errorMsg.push('No filed GSTR1 returns found');
          }
          for (const task of tasks) {
            const taskFY = this.normalizeFinancialYear(task.financialYear);

            const match = task.name.match(/([A-Za-z]+)\s(\d{4})/);
            if (!match) continue;
            // console.log('matched', match);
            const [_, monthName, yearStr] = match;
            const month = moment().month(monthName).format('MM'); // 'April' → '04'

            // console.log('monthmonth', month);
            const year = yearStr;
            const taskRetPrd = `${month}${year}`; // e.g., '042023'
            const returnMatch = filedReturns.find((ret: any) => {
              if (!ret?.gstrRegister?.client?.id) {
                return null;
              }
              const returnFY = this.normalizeFinancialYear(ret?.financialYear);
              // console.log({ returnFY, taskFY, 'ret?.retPrd': ret.retPrd, taskRetPrd });
              return (
                returnFY === taskFY &&
                ret?.retPrd === taskRetPrd &&
                task?.client?.id === ret?.gstrRegister?.client?.id
              );
            });

            if (returnMatch) {
              const oldStatus = task?.status;
              task.status = TaskStatusEnum.COMPLETED;
              task.restore = TaskStatusEnum.COMPLETED;
              task.isCompletedBy = 'ATOM Pro - Sync';
              task.statusUpdatedAt = moment().format('YYYY-MM-DD HH:mm:ss.SSSSSS');
              // await task.save();
              completedGstrOneTasks.push(task);
              let activity = new Activity();
              activity.action = Event_Actions.TASK_STATUS_UPDATED;
              activity.actorId = user.id;
              activity.type = ActivityType.TASK;
              activity.typeId = task?.id;
              activity.remarks = `"${task?.taskNumber}" Task moved from ${getTitle(
                oldStatus,
              )} to ${getTitle(task?.status)} by ${user.fullName} - ATOM Pro - Sync`;
              // await activity.save();

              let clientactivity = new Activity();
              clientactivity.action = Event_Actions.TASK_STATUS_UPDATED;
              clientactivity.actorId = user.id;
              clientactivity.type = ActivityType.CLIENT;
              clientactivity.typeId = task?.client?.id;
              clientactivity.remarks = `"${task?.taskNumber}" Task moved from ${getTitle(
                oldStatus,
              )} to ${getTitle(task?.status)} by ${user.fullName} - ATOM Pro - Sync`;
              // await clientactivity.save();

              let taskStatus = new TaskStatus();
              taskStatus.restore = TaskStatusEnum.COMPLETED;
              taskStatus.status = TaskStatusEnum.COMPLETED;
              taskStatus.task = task;
              taskStatus.user = user;
              // await taskStatus.save();
            }
          }
        } else {
          errorMsg.push('No Gstr Task are Find to complete');
        }
      }

      if (organizationId) {
        const tasks = await createQueryBuilder(Task, 'task')
          .leftJoin('task.organization', 'organization')
          .leftJoinAndSelect('task.client', 'client')
          .leftJoin('task.category', 'category')
          .leftJoin('task.subCategory', 'subCategory')
          .where('organization.id = :organizationId', { organizationId })
          .andWhere('category.id = :categoryId', { categoryId: 45 })
          .andWhere('subCategory.id = :subCategoryId', { subCategoryId: 111 })
          .andWhere('task.recurringStatus = :recurringStatus', { recurringStatus: 'created' })
          .andWhere('task.status != :status', { status: 'completed' })
          .andWhere('task.name LIKE :taskName', { taskName: 'GSTR-3B%' })
          .andWhere('task.processInstanceId IS NULL')
          .andWhere('task.approvalStatus IS NULL')
          .getMany();
        if (tasks?.length > 0) {
          // Step 1: Get all filed GSTR1 ReturnsData for the org
          const filedReturns = await createQueryBuilder(ReturnsData, 'returnsData')
            .leftJoinAndSelect('returnsData.gstrRegister', 'gstrRegister')
            .leftJoin('gstrRegister.organization', 'organization')
            .leftJoinAndSelect('gstrRegister.client', 'client')
            .where('organization.id = :organizationId', { organizationId })
            .andWhere('returnsData.rtntype = :rtntype', { rtntype: 'GSTR3B' })
            .andWhere('returnsData.status = :status', { status: 'Filed' })
            .getMany();

          if (filedReturns.length === 0) {
            errorMsg.push('No filed GSTR3B returns found');
          }

          // console.log('filedReturns', filedReturns);
          // console.log('taskstasks', tasks);
          for (const task of tasks) {
            const taskFY = this.normalizeFinancialYear(task.financialYear);

            const match = task.name.match(/([A-Za-z]+)\s(\d{4})/);
            if (!match) continue;
            // console.log('matched', match);
            const [_, monthName, yearStr] = match;
            const month = moment().month(monthName).format('MM'); // 'April' → '04'

            // console.log('monthmonth', month);
            const year = yearStr;
            const taskRetPrd = `${month}${year}`; // e.g., '042023'
            const returnMatch = filedReturns.find((ret: any) => {
              if (!ret?.gstrRegister?.client?.id) {
                return null;
              }
              const returnFY = this.normalizeFinancialYear(ret?.financialYear);
              // console.log({ returnFY, taskFY, 'ret?.retPrd': ret.retPrd, taskRetPrd });
              return (
                returnFY === taskFY &&
                ret?.retPrd === taskRetPrd &&
                task?.client?.id === ret?.gstrRegister?.client?.id
              );
            });

            if (returnMatch) {
              const oldStatus = task?.status;
              task.status = TaskStatusEnum.COMPLETED;
              task.restore = TaskStatusEnum.COMPLETED;
              task.isCompletedBy = 'ATOM Pro - Sync';
              task.statusUpdatedAt = moment().format('YYYY-MM-DD HH:mm:ss.SSSSSS');
              // await task.save();
              completedGstr3BTasks.push(task);
              let activity = new Activity();
              activity.action = Event_Actions.TASK_STATUS_UPDATED;
              activity.actorId = user.id;
              activity.type = ActivityType.TASK;
              activity.typeId = task?.id;
              activity.remarks = `"${task?.taskNumber}" Task moved from ${getTitle(
                oldStatus,
              )} to ${getTitle(task?.status)} by ${user.fullName} - ATOM Pro - Sync`;
              // await activity.save();

              let clientactivity = new Activity();
              clientactivity.action = Event_Actions.TASK_STATUS_UPDATED;
              clientactivity.actorId = user.id;
              clientactivity.type = ActivityType.CLIENT;
              clientactivity.typeId = task?.client?.id;
              clientactivity.remarks = `"${task?.taskNumber}" Task moved from ${getTitle(
                oldStatus,
              )} to ${getTitle(task?.status)} by ${user.fullName} - ATOM Pro - Sync`;
              // await clientactivity.save();

              let taskStatus = new TaskStatus();
              taskStatus.restore = TaskStatusEnum.COMPLETED;
              taskStatus.status = TaskStatusEnum.COMPLETED;
              taskStatus.task = task;
              taskStatus.user = user;
              // await taskStatus.save();
            }
          }
        } else {
          errorMsg.push('No GSTR-3B Task are Found to complete');
        }
      }
      return { completedGstrOneTasks, completedGstr3BTasks, errorMsg };
    } catch (error) {
      console.log('Error Occur while Complete TaskGstrOne', error?.message);
      throw new InternalServerErrorException(Error);
    }
  }

  async completeTasksWithAtomProSync(userId: number, body: any) {
    try {
      const user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
      const organizationId = user?.organization?.id;
      if (!organizationId) throw new Error('Organization not found for user');

      const [gstr1Tasks, err1] = await this.processGstrTasks(
        'GSTR1',
        111,
        user,
        organizationId,
        body,
      );
      const [gstr3BTasks, err2] = await this.processGstrTasks(
        'GSTR3B',
        111,
        user,
        organizationId,
        body,
      );

      const [gstr9Tasks, err3] = await this.processGstrTasks(
        'GSTR9',
        111,
        user,
        organizationId,
        body,
      );

      let successRows: any = [];
      if (gstr1Tasks) {
        let gstrOneCompletedTasks: any = gstr1Tasks.map(
          (item: any) =>
            `Task : ${item?.name} Task Number : ${item?.taskNumber} Client : ${item?.client?.displayName}  GSTR-1 Task is Completed`,
        );
        successRows = [...successRows, ...gstrOneCompletedTasks];
      }
      if (gstr3BTasks) {
        let completedGstr3BTasks: any = gstr3BTasks.map(
          (item: any) =>
            `Task : ${item?.name} Task Number : ${item?.taskNumber} Client : ${item?.client?.displayName}  GSTR-3B Task is Completed`,
        );
        successRows = [...successRows, ...completedGstr3BTasks];
      }

      if (gstr9Tasks) {
        let completedGstr9Tasks: any = gstr9Tasks.map(
          (item: any) =>
            `Task : ${item?.name} Task Number : ${item?.taskNumber} Client : ${item?.client?.displayName}  GSTR-9 Task is Completed`,
        );
        successRows = [...successRows, ...completedGstr9Tasks];
      }
      return {
        // gstr1TasksCount: gstr1Tasks?.length,
        // gstr3BTasksCount: gstr3BTasks?.length,
        // Gstr9TasksCount: Gstr9Tasks?.length,
        gstCompletedTasks: [...gstr1Tasks, ...gstr3BTasks, ...gstr9Tasks],
        successRows,
        // errorMsg: [...err1, ...err2, ...err3],
      };
    } catch (error) {
      console.log('Error Occur while Complete TaskGstrOne', error?.message);
      throw new InternalServerErrorException(error);
    }
  }

  async processGstrTasks(
    rtntype: 'GSTR1' | 'GSTR3B' | 'GSTR9',
    subCategoryId: number,
    user: User,
    organizationId: number,
    body,
  ): Promise<[Task[], string[]]> {
    const completedTasks = [];
    const errorMsg = [];

    const gstrType = {
      GSTR1: 'GSTR-1%',
      GSTR3B: 'GSTR-3B%',
      GSTR9: 'GSTR-9 Annual Return by Normal Taxpayer%',
    };

    // const taskNamePattern = rtntype === 'GSTR1' ? '%GSTR-1%' : 'GSTR-3B%';

    const tasks = await createQueryBuilder(Task, 'task')
      .leftJoin('task.organization', 'organization')
      .leftJoinAndSelect('task.client', 'client')
      .leftJoin('task.category', 'category')
      .leftJoin('task.subCategory', 'subCategory')
      .where('organization.id = :organizationId', { organizationId })
      .andWhere('client.id IN (:...clientIds)', { clientIds: body?.clientDetails })
      .andWhere('category.id = 45')
      .andWhere('subCategory.id = :subCategoryId', { subCategoryId })
      .andWhere('task.recurringStatus = :recurringStatus', { recurringStatus: 'created' })
      .andWhere('task.status != :status', { status: 'completed' })
      .andWhere('task.name LIKE :taskName', { taskName: gstrType[rtntype] })
      .andWhere('task.processInstanceId IS NULL')
      .andWhere('task.approvalStatus IS NULL')
      .getMany();

    if (!tasks.length) {
      errorMsg.push(`No ${rtntype} Task are Found to complete`);
      return [completedTasks, errorMsg];
    }

    const filedReturns = await createQueryBuilder(ReturnsData, 'returnsData')
      .leftJoinAndSelect('returnsData.gstrRegister', 'gstrRegister')
      .leftJoin('gstrRegister.organization', 'organization')
      .leftJoinAndSelect('gstrRegister.client', 'client')
      .where('organization.id = :organizationId', { organizationId })
      .andWhere('client.id IN (:...clientIds)', { clientIds: body?.clientDetails })
      .andWhere('returnsData.rtntype = :rtntype', { rtntype })
      .andWhere('returnsData.status = :status', { status: 'Filed' })
      .getMany();
    if (!filedReturns.length) {
      errorMsg.push(`No filed ${rtntype} returns found`);
    }

    for (const task of tasks) {
      const taskFY = this.normalizeFinancialYear(task.financialYear);
      const match = task.name.match(/([A-Za-z]+)\s(\d{4})/);
      if (!match) continue;
      const [_, monthName, yearStr] = match;
      const month = moment().month(monthName).format('MM');
      const year = yearStr;
      const taskRetPrd = `${month}${year}`;

      const returnMatch = filedReturns.find((ret) => {
        const returnFY = this.normalizeFinancialYear(ret.financialYear);
        return (
          returnFY === taskFY &&
          ret.retPrd === taskRetPrd &&
          ret.gstrRegister?.client?.id === task.client?.id
        );
      });

      if (returnMatch) {
        const oldStatus = task.status;
        task.status = TaskStatusEnum.COMPLETED;
        task.restore = TaskStatusEnum.COMPLETED;
        task.isCompletedBy = 'ATOM Pro - Sync';
        task.statusUpdatedAt = moment().format('YYYY-MM-DD HH:mm:ss.SSSSSS');
        completedTasks.push(task);

        const activity = new Activity();
        activity.action = Event_Actions.TASK_STATUS_UPDATED;
        activity.actorId = user.id;
        activity.type = ActivityType.TASK;
        activity.typeId = task.id;
        activity.remarks = `"${task.taskNumber}" Task moved from ${getTitle(
          oldStatus,
        )} to ${getTitle(task.status)} by ${user.fullName} - ATOM Pro - Sync`;

        const clientactivity = new Activity();
        clientactivity.action = Event_Actions.TASK_STATUS_UPDATED;
        clientactivity.actorId = user.id;
        clientactivity.type = ActivityType.CLIENT;
        clientactivity.typeId = task.client?.id;
        clientactivity.remarks = activity.remarks;

        const taskStatus = new TaskStatus();
        taskStatus.restore = TaskStatusEnum.COMPLETED;
        taskStatus.status = TaskStatusEnum.COMPLETED;
        taskStatus.task = task;
        taskStatus.user = user;

        // Save logic (uncomment in real usage)
        await task.save();
        await activity.save();
        await clientactivity.save();
        await taskStatus.save();
      }
    }

    return [completedTasks, errorMsg];
  }
}
