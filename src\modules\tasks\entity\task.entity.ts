import { Invoice } from 'src/modules/billing/entitities/invoice.entity';
import Category from 'src/modules/categories/categories.entity';
import Client from 'src/modules/clients/entity/client.entity';
import Event from 'src/modules/events/event.entity';
import Label from 'src/modules/labels/label.entity';
import { Organization } from 'src/modules/organization/entities/organization.entity';
import Storage from 'src/modules/storage/storage.entity';
import { User } from 'src/modules/users/entities/user.entity';
import {
  AfterLoad,
  BaseEntity,
  Column,
  CreateDateColumn,
  Entity,
  getManager,
  JoinColumn,
  JoinTable,
  ManyToMany,
  ManyToOne,
  OneToMany,
  OneToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import {
  FeeType,
  PaymentStatusEnum,
  PriorityEnum,
  ProformaTaskStatus,
  RecurringFrequency,
  TaskRecurringStatus,
  TaskStatusEnum,
} from '../dto/types';
import LogHour from 'src/modules/log-hours/log-hour.entity';
import Checklist from './checklist.entity';
import TaskComment from './comment.entity';
import Milestone from './milestone.entity';
import StageOfWork from './stage-of-work.entity';
import TaskStatus from './task-status.entity';
import Expenditure from 'src/modules/expenditure/expenditure.entity';
import RecurringProfile from 'src/modules/recurring/entity/recurring-profile.entity';
import { Service } from 'src/modules/services/entities/service.entity';
import ApprovalProcedures from 'src/modules/atm-qtm-approval/entities/approval-procedures.entity';
import CollectData from 'src/modules/collect-data/collect-data.entity';
import BudgetedHours from 'src/modules/budgeted-hours/budgeted-hours.entity';
import QtmActivity from 'src/modules/admin/entities/qtmActivity.entity';
import UdinTask from 'src/modules/udin-task/udin-task.entity';
import ClientGroup from 'src/modules/client-group/client-group.entity';
import { ProformaInvoice } from 'src/modules/billing/entitities/proforma-invoice.entity';
import DocumentInOut from 'src/modules/document-in-out/entity/document-in-out.entity';

@Entity()
class Task extends BaseEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  taskNumber: string;

  @Column()
  uid: string;

  @Column()
  order: number;

  @Column()
  financialYear: string;

  @Column({ type: 'date', nullable: true })
  expectedCompletionDate: string;

  @Column()
  name: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({ type: 'text', nullable: true })
  remarks: string;

  @Column({ default: false })
  recurring: boolean;

  @Column({ default: true })
  billable: boolean;

  @Column({ nullable: true })
  directory: string;

  @Column({ type: 'enum', enum: FeeType })
  feeType: FeeType;

  @Column({ nullable: true, type: 'int' })
  feeAmount: number;

  @Column({ type: 'date', nullable: true })
  taskStartDate: string;

  @Column({ type: 'date', nullable: true })
  dueDate: string;

  @Column({ type: 'bigint', nullable: true })
  budgetedhours: number;

  @Column({ type: 'text', nullable: true })
  bhallocation: string;

  @Column({
    type: 'enum',
    enum: PriorityEnum,
    default: PriorityEnum.NONE,
  })
  priority: PriorityEnum;

  @Column({ type: 'enum', enum: TaskStatusEnum, default: TaskStatusEnum.TODO })
  status: TaskStatusEnum;

  @Column({ type: 'enum', enum: TaskStatusEnum, default: TaskStatusEnum.TODO })
  restore: TaskStatusEnum;

  @Column({ type: 'enum', enum: TaskRecurringStatus, nullable: true })
  recurringStatus: TaskRecurringStatus;

  @Column({
    type: 'enum',
    enum: PaymentStatusEnum,
    default: PaymentStatusEnum.UNBILLED,
  })
  paymentStatus: PaymentStatusEnum;

  @OneToOne(() => CollectData, (collectData) => collectData.user)
  collectData: CollectData;

  @ManyToOne(() => Client, (client) => client.tasks, { onDelete: 'SET NULL' })
  client: Client;

  @ManyToOne(() => ClientGroup, (client) => client.tasks, { onDelete: 'SET NULL' })
  clientGroup: ClientGroup;

  @ManyToOne(() => Category, (category) => category.tasks)
  category: Category;

  @ManyToOne(() => Category, (category) => category.tasks, { nullable: true })
  subCategory: Category;

  @OneToMany(() => LogHour, (logHour) => logHour.task)
  taskLogHours: LogHour[];

  @OneToMany(() => BudgetedHours, (budgetedHours) => budgetedHours.task)
  taskBudgetedHours: BudgetedHours[];

  @OneToOne(() => UdinTask, (udinTask) => udinTask.task, { nullable: true })
  udinTask: UdinTask;

  @ManyToMany(() => Label)
  @JoinTable()
  labels: Label[];

  @ManyToOne(() => Organization, (organization) => organization.tasks)
  organization: Organization;

  @ManyToOne(() => User, (user) => user.tasks)
  user: User;

  @OneToMany(() => TaskComment, (taskComment) => taskComment.task)
  comments: TaskComment[];

  @ManyToOne(() => Task, (task) => task.subTasks, {
    onDelete: 'CASCADE',
  })
  parentTask: Task;

  @OneToMany(() => Task, (task) => task.parentTask, {
    cascade: true,
  })
  subTasks: Task[];

  @OneToMany(() => Storage, (storage) => storage.task, {
    cascade: true,
  })
  attachments: Storage[];

  @ManyToMany(() => User, (user) => user.assignedTasks)
  @JoinTable()
  members: User[];

  @ManyToMany(() => User, (user) => user.assignedLeaderTasks)
  @JoinTable()
  taskLeader: User[];

  @OneToMany(() => Checklist, (checklist) => checklist.task, {
    cascade: true,
  })
  checklists: Checklist[];

  @OneToMany(() => Milestone, (milestone) => milestone.task, {
    cascade: true,
  })
  milestones: Milestone[];

  @OneToMany(() => StageOfWork, (stageOfWork) => stageOfWork.task, {
    cascade: true,
  })
  stageOfWorks: StageOfWork[];

  @OneToMany(() => TaskStatus, (taskStatus) => taskStatus.task)
  taskStatus: TaskStatus[];

  @OneToMany(() => Event, (event) => event.task)
  events: Event[];

  @OneToMany(() => Expenditure, (expenditure) => expenditure.task)
  expenditure: Expenditure[];

  @ManyToOne(() => Invoice, (invoice) => invoice.id)
  invoice: Invoice;

  @ManyToOne(() => ProformaInvoice, (proformaInvoice) => proformaInvoice.id)
  ProformaInvoice: ProformaInvoice[];

  @OneToMany(() => QtmActivity, (qtmActivity) => qtmActivity.task)
  qtmActivity: QtmActivity[];

  @Column()
  invoiceId: string;

  @Column()
  proformaInvoiceId: string;

  @ManyToOne(() => RecurringProfile, (recurringProfile) => recurringProfile.tasks, {
    cascade: true,
  })
  recurringProfile: RecurringProfile;

  @ManyToOne(() => Service, (service) => service.tasks)
  service: Service;

  @Column({ type: 'enum', enum: RecurringFrequency, nullable: true })
  frequency: RecurringFrequency;

  @Column({ nullable: true })
  terminationReason: string;

  @Column({ nullable: true })
  processInstanceId: string;

  @ManyToOne(() => ApprovalProcedures, (approvalProcedures) => approvalProcedures.task)
  approvalProcedures: ApprovalProcedures;

  @Column('json')
  approvalStatus: object[];

  @Column({ type: 'enum', enum: ProformaTaskStatus, default: ProformaTaskStatus.NOT_GENERATED })
  proformaStatus: ProformaTaskStatus;

  @Column({ nullable: true, type: 'datetime' })
  createdDate: Date;

  @Column({ default: false })
  isUdin: boolean;

  @CreateDateColumn()
  createdAt: string;

  @UpdateDateColumn()
  updatedAt: string;

  @Column()
  isCompletedBy: string;

  @Column()
  statusUpdatedAt: string;
}

export default Task;
