import { BadRequestException, ConflictException, Injectable, InternalServerErrorException } from "@nestjs/common";
import { User } from "src/modules/users/entities/user.entity";
import { BillingEntity } from "src/modules/organization/entities/billing-entity.entity";
import Client from "src/modules/clients/entity/client.entity";
import Storage, { StorageSystem, StorageType } from "src/modules/storage/storage.entity";
import { v4 as uuidv4 } from 'uuid';
import { createQueryBuilder, getManager, In, IsNull, Not } from "typeorm";
import Task from "src/modules/tasks/entity/task.entity";
import { PaymentStatusEnum, ProformaTaskStatus, TaskRecurringStatus } from "src/modules/tasks/dto/types";
import { EventEmitter2 } from "@nestjs/event-emitter";
import puppeteer from 'puppeteer';
import { Event_Actions } from "src/event-listeners/actions";
import * as xlsx from 'xlsx';
import Activity, { ActivityType } from "src/modules/activity/activity.entity";
import ClientGroup from "src/modules/client-group/client-group.entity";
import { dateFormation } from "src/utils/datesFormation";
import * as moment from "moment";
import { CreateInvoiceDto } from "src/modules/billing/dto/create-invoice.dto";
import { FindInvoicesDto } from "src/modules/billing/dto/find-invoices.dto";
import { GetUnbilledTasksDto } from "src/modules/billing/dto/get-unbilled.dto";
import InvoiceAddress from "src/modules/billing/entitities/invoice-address.entity";
import InvoiceBankDetails from "src/modules/billing/entitities/invoice-bank-details.entity";
import InvoiceOtherParticular from "src/modules/billing/entitities/invoice-other-particular.entity";
import InvoiceParticular from "src/modules/billing/entitities/invoice-particular.entity";
import { Invoice } from "src/modules/billing/entitities/invoice.entity";
import { ProformaInvoice, ProformaStatus, InvoiceStatus } from "src/modules/billing/entitities/proforma-invoice.entity";
import { TAX_TYPE_VALUE, getTotalGst } from "src/modules/billing/totalCalculations";
import { OneDriveStorageService } from "./onedrive-storage.service";
import { AwsService } from "./upload.service";

interface QueryConditions {
    id: number;
    [key: string]: number | string;
}


@Injectable()
export class ProformaService {
    constructor(
        private oneDriveService: OneDriveStorageService,
        private awsService: AwsService,
        private eventEmitter: EventEmitter2,
    ) { }
    async create(userId: number, body: CreateInvoiceDto) {
        const proforma = new ProformaInvoice();
        let existingInvoice = await ProformaInvoice.findOne({
            where: {
                invoiceNumber: body.estimateNumber,
                billingEntity: body.billingEntity
            },
        });

        if (existingInvoice) {
            throw new ConflictException('Invoice number already exists');
        }
        proforma.invoiceNumber = body.estimateNumber;
        proforma.invoiceUser = body.invoiceUser;

        let user = await User.findOne({
            where: { id: userId },
            relations: ['organization'],
        });
        let billingEntity = await BillingEntity.findOne({
            where: { id: body.billingEntity },
        });

        let client;
        let clientGroup;
        if (body.clientType === "CLIENT_GROUP") {
            clientGroup = await ClientGroup.findOne({ where: { id: body.client } });
        }
        if (body.clientType !== "CLIENT_GROUP") {
            client = await Client.findOne({ where: { id: body.client } });
        }

        let billingEntityAddress = new InvoiceAddress();
        Object.assign(billingEntityAddress, body.billingEntityAddress);

        let billingAddress = new InvoiceAddress();
        Object.assign(billingAddress, body.billingAddress);

        let shippingAddress = new InvoiceAddress();
        // body.shippingAddress.state = 'Telangana';
        Object.assign(shippingAddress, body.shippingAddress);

        let taskIds = [];
        let particulars: InvoiceParticular[] = [];
        body.particulars.forEach((particular) => {
            let invoiceParticular = new InvoiceParticular();
            Object.assign(invoiceParticular, particular);
            delete invoiceParticular.id;
            !billingEntity.hasGst && delete invoiceParticular.gst;
            !billingEntity.hasGst && delete invoiceParticular.hsn;
            particulars.push(invoiceParticular);
            if (particular.taskId) {
                taskIds.push(particular.taskId);
            }
        });

        const invoiceTasks = await Task.find({
            where: {
                id: In(taskIds),
                proformaInvoiceId: Not(IsNull()),
            },
        });


        if (invoiceTasks.length) {
            throw new InternalServerErrorException(`Task Id ${invoiceTasks.map((task) => task.taskNumber).join(', ')} already Billed`);
        }
        let otherParticulars: InvoiceOtherParticular[] = [];
        body.otherParticulars.forEach((otherParticular) => {
            otherParticular['taskExpenseType'] = 'PURE_AGENT';
            const otherParticularClone = { ...otherParticular };
            delete otherParticularClone['id'];
            let invoiceOtherParticular = new InvoiceOtherParticular();
            Object.assign(invoiceOtherParticular, otherParticularClone);

            otherParticulars.push(invoiceOtherParticular);
        });
        let bankDetails = new InvoiceBankDetails();
        bankDetails.accountNumber = body?.bankDetails?.accountNumber;
        bankDetails.bankName = body?.bankDetails?.bankName;
        bankDetails.branchName = body?.bankDetails?.branchName;
        bankDetails.ifscCode = body?.bankDetails?.ifscCode;
        bankDetails.upiId = body?.bankDetails?.upiId;
        let storage: Storage;
        const existingStorage = await Storage.findOne({ where: { id: body.bankDetails?.upiAttachmentId } });
        if (existingStorage?.storageSystem === StorageSystem.MICROSOFT) {
            const newFile = await this.oneDriveService.copyFile(existingStorage?.fileId, userId, existingStorage?.name);
            storage = new Storage();
            storage.uid = uuidv4();
            storage.name = newFile?.name;
            storage.type = StorageType.FILE;
            storage.fileType = newFile?.file?.mimeType;
            storage.file = newFile?.['@microsoft.graph.downloadUrl'];
            storage.downloadUrl = newFile?.['@microsoft.graph.downloadUrl'];
            storage.fileId = newFile?.id;
            storage.fileSize = existingStorage?.fileSize;
            storage.show = false;
            storage.storageSystem = StorageSystem.MICROSOFT;
            storage.webUrl = newFile?.webUrl;
            storage.authId = user.organization.id;
            storage.organization = user.organization;
            await storage.save();
        } else if (existingStorage?.storageSystem === StorageSystem.AMAZON) {
            const newFile = await this.awsService.copyS3Object(existingStorage?.file, `${existingStorage?.file}-${body.estimateNumber}`);
            storage = new Storage();
            storage.uid = uuidv4();
            storage.name = existingStorage?.name;
            storage.type = StorageType.FILE;
            storage.fileType = existingStorage?.fileType;
            storage.file = newFile.newKey;
            storage.fileSize = existingStorage?.fileSize;
            storage.show = false;
            storage.storageSystem = StorageSystem.AMAZON;
            storage.authId = user.organization.id;
            storage.organization = user.organization;
            await storage.save();

        };
        proforma.organization = user.organization;
        proforma.billingEntity = billingEntity;
        proforma.client = client;
        proforma.clientGroup = clientGroup;
        proforma.billingEntityAddress = billingEntityAddress;
        proforma.billingAddress = billingAddress;
        proforma.shippingAddress = shippingAddress;
        proforma.bankDetails = bankDetails;
        proforma.invoiceDate = body.invoiceDate;
        proforma.invoiceDueDate = body.invoiceDueDate;
        proforma.terms = body.terms;
        proforma.placeOfSupply = body.placeOfSupply;
        proforma.termsAndConditionsCopy = body.termsAndConditionsCopy;
        proforma.particulars = particulars;
        proforma.otherParticulars = otherParticulars;
        proforma.subTotal = body.subTotal;
        proforma.adjustment = body.adjustment;
        proforma.narration = body.narration;
        proforma.totalGstAmount = body.totalGstAmount;
        proforma.totalCharges = body.totalCharges;
        proforma.roundOff = body.roundOff;
        proforma.grandTotal = body.grandTotal;
        proforma.whatsappCheck = body.whatsappCheck;
        proforma.emailCheck = body.emailCheck;
        proforma.status = ProformaStatus.CREATED;
        proforma.divideTax = body.divideTax;
        proforma.hasTds = body.hasTds;
        if (body.hasTds) {
            proforma.tdsSection = body.tdsSection;
            proforma.tdsRate = body.tdsRate;
            proforma.tdsView = body.tdsView;
        };
        proforma['userId'] = user.id;
        if (body.divideTax) {
            proforma.supplyType = body.supplyType;
        }
        const p = await proforma.save();
        if (storage) {
            storage.invoiceBankAttachement = p.bankDetails;
            await storage.save();
        };
        let activity = new Activity();
        activity.action = Event_Actions.PROFORMA_INVOICE_CREATED;
        activity.actorId = user.id;
        activity.type = client ? ActivityType.BILLING : ActivityType.BILLING_CLIENT_GROUP;
        activity.typeId = client ? client?.id : clientGroup?.id;
        activity.remarks = `Proforma Invoice "${proforma.invoiceNumber}" Created by ${user.fullName}`;
        await activity.save();

        if (particulars && particulars.length > 0) {
            const taskIds = particulars.map((particular) => particular.taskId);
            await createQueryBuilder(Task, 'task')
                .where('task.id IN (:...ids)', { ids: taskIds })
                .update({ proformaStatus: ProformaTaskStatus.GENEREATED, proformaInvoiceId: '' + proforma.id })
                .execute();
        }
        return proforma;
    };

    async get(userId: number, query: FindInvoicesDto) {
        let user = await User.findOne({
            where: { id: userId },
            relations: ['organization'],
        });

        let invoices = createQueryBuilder(ProformaInvoice, 'invoices')
            .select(['invoices.invoiceNumber',
                'invoices.billingEntity',
                'invoices.id',
                'invoices.status',
                'invoices.createdAt',
                'invoices.grandTotal',
                'invoices.subTotal',
                'invoices.invoiceDate',
                'invoices.invoiceDueDate',
                'invoices.divideTax',
                'invoices.narration',
                'invoices.placeOfSupply',
                'invoices.totalCharges',
                'billingEntity.tradeName',
                'billingEntity.hasGst',
                'billingEntity.locationOfSupply',
                'client.displayName',
                'client.gstNumber',
                'client.address',
                'organization.id',
                'clientGroup.displayName',
                'clientGroup.gstNumber',
                'clientGroup.address',
                'particulars.gst',
                'particulars.discount',
                'particulars.discountType',
                'particulars.rate',
                'particulars.units',
                'particulars.hsn',
                'particulars.amount',
            ])
            .leftJoin('invoices.particulars', 'particulars')
            .leftJoinAndSelect('invoices.otherParticulars', 'otherParticulars')
            .leftJoin('invoices.billingEntity', 'billingEntity')
            .leftJoin('invoices.client', 'client')
            .leftJoin('invoices.clientGroup', 'clientGroup')
            .leftJoin('invoices.organization', 'organization')
            .where('organization.id = :orgId', { orgId: user.organization.id });

        if (query?.clientId) {
            invoices.where('client.id = :clientId', { clientId: query?.clientId });
        }

        if (query?.billingEntity?.length) {
            invoices.andWhere('billingEntity.id IN (:...billingEntity)', { billingEntity: query.billingEntity })
        }
        if (query.fromDate && query.toDate) {
            const { startTime, endTime } = dateFormation(query.fromDate, query.toDate);
            invoices.andWhere('invoices.createdAt BETWEEN :startDate AND :endDate', {
                startDate: startTime,
                endDate: endTime,
            });
        }

        if (query.search) {
            invoices = invoices.andWhere(
                '(invoices.invoiceNumber LIKE :search OR client.displayName LIKE :search  OR clientGroup.displayName LIKE :search)',
                {
                    search: `%${query.search}%`,
                },
            );
        };
        if (query.status && query.status !== '') {
            invoices.andWhere(
                '(invoices.status = :status)',
                {
                    status: query.status,
                },
            );
        }
        const sort = (typeof query?.sort === "string") ? JSON.parse(query.sort) : query?.sort;
        if (sort?.column) {
            const columnMap: Record<string, string> = {
                invoiceDate: 'invoices.invoiceDate',
                invoiceDueDate: 'invoices.invoiceDueDate',
                grandTotal: 'invoices.grandTotal',
                tradeName: 'billingEntity.tradeName',
                displayName: 'client.displayName',
                status: 'invoices.status'
            };
            const column = columnMap[sort.column] || sort.column;
            invoices.orderBy(column, sort.direction.toUpperCase());
        } else {
            invoices.orderBy('invoices.createdAt', 'DESC');
        };

        if (query.offset) {
            invoices.skip(query.offset);
        }
        if (query.limit) {
            invoices.take(query.limit);
        }

        let data = await invoices.getManyAndCount();

        return {
            totalCount: data[1],
            result: data[0],
        };
    };

    async getProformaInvoice(estimateId: number, query: any) {
        let queryConditions: QueryConditions = {
            id: estimateId
        };
        if (query.orgId) {
            queryConditions.organization = query.orgId;
        }

        if (query.clientId) {
            queryConditions.client = query.clientId;
        }

        let invoice = await ProformaInvoice.findOne({
            where: queryConditions,
            relations: [
                'billingEntity',
                'billingEntityAddress',
                'billingAddress',
                'shippingAddress',
                'client',
                'clientGroup',
                'particulars',
                'otherParticulars',
                'bankDetails',
                'bankDetails.invoiceBankAttachement',
                'organization',
                'billingEntity.logStorage',
                'billingEntity.signatureStorage',
            ],
        });
        return invoice || "Un-Authorized";
    };

    async downloadProformaInvoicewithoutEmittor(proformaId: number) {
        let url = `${process.env.WEBSITE_URL}/billing/proforma/${proformaId}/preview?fromApi=true`;
        // let url = `http://localhost:3000/billing/invoices/${invoiceId}/preview?fromApi=true`;
        const options = {
            executablePath: '/usr/bin/chromium-browser',
            headless: true,
            args: ['--no-sandbox', '--disable-setuid-sandbox'],
        };
        try {
            const browser = await puppeteer.launch(options);
            const page = await browser.newPage();
            await page.setCacheEnabled(false);
            await page.setUserAgent(
                'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/78.0.3904.108 Safari/537.36',
            );

            const maxRetries = 3;
            let retries = 0;
            let loaded = false;

            while (retries < maxRetries && !loaded) {
                try {
                    await page.goto(url, { waitUntil: 'networkidle2', timeout: 60000 }); // 60 seconds timeout

                    // Check for a specific element that indicates the page has fully loaded
                    await page.waitForFunction(
                        'document.querySelector("body").innerText.includes("Powered by")',
                        { timeout: 60000 }, // Adjust timeout as needed
                    );
                    await page.waitForFunction(
                        'Array.from(document.images).every(img => img.complete && img.naturalHeight !== 0)',
                        { timeout: 60000 }, // Adjust timeout as needed
                    );

                    loaded = true;
                } catch (error) {
                    retries += 1;
                    console.log(`Retrying to load the page (${retries}/${maxRetries})`);
                    if (retries >= maxRetries) {
                        throw error;
                    }
                }
            }

            await page.addStyleTag({ content: '#freshworks-container { display: none; }' });
            await page.addStyleTag({ content: '.hide { display: none }' });
            await page.emulateMediaType('print');

            const pdf = await page.pdf({
                format: 'A4',
                printBackground: true,
                margin: {
                    top: '0px',
                    right: '0px',
                    bottom: '0px',
                    left: '0px',
                },
                scale: 0.6,
            });

            await browser.close();
            return pdf;
        } catch (error) {
            console.error('Failed to download the invoice:', error);
            throw new InternalServerErrorException('Failed to download the invoice');
        }
    }

    async getProformaTasks(query: GetUnbilledTasksDto) {
        let tasks = createQueryBuilder(Task, 'task')
            .leftJoinAndSelect('task.client', 'client')
            .leftJoinAndSelect('task.clientGroup', 'clientGroup')
            .leftJoinAndSelect('task.category', 'category')
            .leftJoinAndSelect('task.members', 'members')
            .leftJoinAndSelect('members.imageStorage', 'imageStorage')
            .leftJoinAndSelect('task.expenditure', 'expenditure');

        if (query.client) {
            tasks.where('client.id = :clientId', { clientId: query.client })
        }

        if (query.clientGroup) {
            tasks.where('clientGroup.id = :clientGroupId', { clientGroupId: query.clientGroup })
        }

        tasks.andWhere('task.status NOT IN (:status)', { status: ['deleted', 'terminated'] })
            .andWhere('task.parentTask IS NULL')
            .andWhere('(task.recurringStatus is null or task.recurringStatus = :recurringStatus)', {
                recurringStatus: TaskRecurringStatus.CREATED,
            })
            .andWhere('task.proformaStatus != :profomaStatus', { profomaStatus: 'GENERATED' })
            .andWhere('task.paymentStatus !=:paymentStatus', { paymentStatus: 'BILLED' })
            .andWhere('task.billable IS TRUE');

        if (query.search) {
            tasks = tasks.andWhere('task.name LIKE :search', {
                search: `%${query.search}%`,
            });
        }
        const sort = (typeof query?.sort === "string") ? JSON.parse(query.sort) : query?.sort;
        if (sort?.column) {
            const columnMap: Record<string, string> = {
                feeAmount: 'task.feeAmount',
                taskNumber: 'task.taskNumber',
                name: 'category.name',
                status: 'task.status',
                additionalexpenditure: 'task.feeAmount',



            };
            const column = columnMap[sort.column] || sort.column;

            tasks.orderBy(column, sort.direction.toUpperCase());
        } else {
            tasks.orderBy('task.id', "ASC");
        }



        if (query.offset >= 0) {
            tasks.skip(query.offset);
        }

        if (query.limit) {
            tasks.take(query.limit);
        }

        let data = await tasks.getManyAndCount();

        return {
            totalCount: data[1],
            result: data[0],
        };

    };

    async downloadProformaInvoice(invoiceId: number, body: any) {
        let url = `${process.env.WEBSITE_URL}/billing/proforma/${invoiceId}/preview?fromApi=true`;
        // let url = `http://localhost:3000/billing/invoices/${invoiceId}/preview?fromApi=true`;
        const options = {
            executablePath: '/usr/bin/chromium-browser',
            headless: true,
            args: [
                '--no-sandbox',
                '--disable-setuid-sandbox',
            ],
        };
        try {
            const browser = await puppeteer.launch(options);
            const page = await browser.newPage();
            await page.setCacheEnabled(false);
            await page.setUserAgent(
                'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/78.0.3904.108 Safari/537.36',
            );

            const maxRetries = 3;
            let retries = 0;
            let loaded = false;

            while (retries < maxRetries && !loaded) {
                try {
                    await page.goto(url, { waitUntil: 'networkidle2', timeout: 60000 }); // 60 seconds timeout

                    // Check for a specific element that indicates the page has fully loaded
                    await page.waitForFunction(
                        'document.querySelector("body").innerText.includes("Powered by")',
                        { timeout: 60000 } // Adjust timeout as needed
                    );
                    await page.waitForFunction(
                        'Array.from(document.images).every(img => img.complete && img.naturalHeight !== 0)',
                        { timeout: 60000 } // Adjust timeout as needed
                    );

                    loaded = true;
                } catch (error) {
                    retries += 1;
                    console.log(`Retrying to load the page (${retries}/${maxRetries})`);
                    if (retries >= maxRetries) {
                        throw error;
                    }
                }
            }

            await page.addStyleTag({ content: '#freshworks-container { display: none; }' });
            await page.addStyleTag({ content: '.hide { display: none }' });

            // await page.addStyleTag({ content: '@media print { .myDiv { break-inside: avoid; } }' });

            // await page.addStyleTag({
            //   content: '@page { size: auto; }',
            // });

            // await page.emulateMediaType('screen');
            this.eventEmitter.emit(Event_Actions.PROFORMA_INVOICE_DOWNLOADED, { invoiceId, userId: body.userId });
            await page.emulateMediaType('print');

            const pdf = await page.pdf({
                format: 'A4',
                printBackground: true,
                margin: {
                    top: '0px',
                    right: '0px',
                    bottom: '0px',
                    left: '0px',
                },
                scale: 0.6,
            });

            await browser.close();
            return pdf;
        } catch (error) {
            console.error('Failed to download the invoice:', error);
            throw new InternalServerErrorException('Failed to download the invoice');
        }
    };

    async convert(userId: number, body: CreateInvoiceDto) {
        let invoice = new Invoice();
        let existingInvoice = await Invoice.findOne({
            where: {
                invoiceNumber: body.estimateNumber,
                billingEntity: body.billingEntity
            },
        });
        if (existingInvoice) {
            throw new InternalServerErrorException('Invoice number already exists');
        }
        invoice.invoiceNumber = body.estimateNumber;
        invoice.invoiceUser = body.invoiceUser;

        let user = await User.findOne({
            where: { id: userId },
            relations: ['organization'],
        });

        let billingEntity = await BillingEntity.findOne({
            where: { id: body.billingEntity },
        });

        let client = null;
        let clientGroup = null;
        if (body?.clientType !== "CLIENT_GROUP") {
            client = await Client.findOne({ where: { id: body.client } });
        }
        if (body?.clientType === "CLIENT_GROUP") {
            clientGroup = await ClientGroup.findOne({ where: { id: body.client } });
        }
        let billingEntityAddress = new InvoiceAddress();
        Object.assign(billingEntityAddress, body.billingEntityAddress);
        let billingAddress = new InvoiceAddress();
        const { id, ...billingAddressData } = body.billingAddress;
        Object.assign(billingAddress, billingAddressData);

        let shippingAddress = new InvoiceAddress();
        // body.shippingAddress.state = 'Telangana';
        Object.assign(shippingAddress, body.shippingAddress);

        let taskIds = [];
        let particulars: InvoiceParticular[] = [];
        body.particulars.forEach((particular: any) => {
            let invoiceParticular = new InvoiceParticular();
            Object.assign(invoiceParticular, particular);
            delete invoiceParticular.id;
            delete invoiceParticular.proformaInvoice;
            particulars.push(invoiceParticular);
            if (particular.taskId) {
                taskIds.push(particular.taskId);
            };
        });

        let otherParticulars: InvoiceOtherParticular[] = [];
        body.otherParticulars.forEach((otherParticular) => {
            otherParticular['taskExpenseType'] = 'PURE_AGENT';
            const otherParticularClone = { ...otherParticular };
            delete otherParticularClone['id'];
            delete otherParticularClone['proformaInvoice']
            let invoiceOtherParticular = new InvoiceOtherParticular();
            Object.assign(invoiceOtherParticular, otherParticularClone);
            otherParticulars.push(invoiceOtherParticular);
        });
        let bankDetails = new InvoiceBankDetails();
        bankDetails.accountNumber = body?.bankDetails?.accountNumber;
        bankDetails.bankName = body?.bankDetails?.bankName;
        bankDetails.branchName = body?.bankDetails?.branchName;
        bankDetails.ifscCode = body?.bankDetails?.ifscCode;
        bankDetails.upiId = body?.bankDetails?.upiId;
        let storage: Storage;
        const existingStorage = await Storage.findOne({ where: { id: body.bankDetails?.upiAttachmentId } });
        if (existingStorage?.storageSystem === StorageSystem.MICROSOFT) {
            const newFile = await this.oneDriveService.copyFile(existingStorage?.fileId, userId, existingStorage.name);
            storage = new Storage();
            storage.uid = uuidv4();
            storage.name = newFile?.name;
            storage.type = StorageType.FILE;
            storage.fileType = newFile?.file?.mimeType;
            storage.file = newFile?.['@microsoft.graph.downloadUrl'];
            storage.downloadUrl = newFile?.['@microsoft.graph.downloadUrl'];
            storage.fileId = newFile?.id;
            storage.fileSize = existingStorage?.fileSize;
            storage.show = false;
            storage.storageSystem = StorageSystem.MICROSOFT;
            storage.webUrl = newFile?.webUrl;
            storage.authId = user.organization.id;
            storage.organization = user.organization;
            await storage.save();
        } else if (existingStorage?.storageSystem === StorageSystem.AMAZON) {
            const newFile = await this.awsService.copyS3Object(existingStorage?.file, `${existingStorage?.file}-${body.estimateNumber}`);
            storage = new Storage();
            storage.uid = uuidv4();
            storage.name = existingStorage?.name;
            storage.type = StorageType.FILE;
            storage.fileType = existingStorage?.fileType;
            storage.file = newFile.newKey;
            storage.fileSize = existingStorage?.fileSize;
            storage.show = false;
            storage.storageSystem = StorageSystem.AMAZON;
            storage.authId = user.organization.id;
            storage.organization = user.organization;
            await storage.save();

        };
        // invoice.approvalHierarchyId = body.approvalHierarchyId;
        invoice.organization = user.organization;
        invoice.billingEntity = billingEntity;
        invoice.client = client;
        invoice.clientGroup = clientGroup;
        invoice.billingEntityAddress = billingEntityAddress;
        invoice.billingAddress = billingAddress;
        // invoice.shippingAddress = shippingAddress;
        invoice.bankDetails = bankDetails;
        invoice.invoiceDate = body.invoiceDate;
        invoice.invoiceDueDate = body.invoiceDueDate;
        invoice.terms = body.terms;
        invoice.placeOfSupply = body.placeOfSupply;
        // invoice.termsAndConditions = body.termsAndConditions;
        invoice.termsAndConditionsCopy = body.termsAndConditionsCopy;
        invoice.particulars = particulars;
        invoice.otherParticulars = otherParticulars;
        invoice.subTotal = body.subTotal;
        invoice.adjustment = body.adjustment;
        invoice.narration = body.narration;
        invoice.totalGstAmount = body.totalGstAmount;
        invoice.totalCharges = body.totalCharges;
        invoice.roundOff = body.roundOff;
        invoice.grandTotal = body.grandTotal;
        invoice.whatsappCheck = body.whatsappCheck;
        invoice.emailCheck = body.emailCheck;
        invoice.divideTax = body.divideTax;
        invoice.hasTds = body.hasTds;
        if (body.hasTds) {
            invoice.tdsSection = body.tdsSection;
            invoice.tdsRate = body.tdsRate;
            invoice.tdsView = body.tdsView;
        };
        if (body.divideTax) {
            invoice.supplyType = body.supplyType;
        }
        invoice['userId'] = user.id;
        if (body.submitForApproval) {
            invoice.status = InvoiceStatus.APPROVAL_PENDING;
        }
        const i = await invoice.save();
        if (storage) {
            storage.invoiceBankAttachement = i.bankDetails;
            await storage.save();
        };
        const proforma = await ProformaInvoice.findOne(body.proformaId);
        proforma.status = ProformaStatus.CONVERTED;
        await proforma.save();
        let activity = new Activity();
        activity.action = Event_Actions.PROFORMA_INVOICE_CONVERTED;
        activity.actorId = user.id;
        activity.type = invoice.client ? ActivityType.BILLING : ActivityType.BILLING_CLIENT_GROUP;
        activity.typeId = invoice.client ? invoice.client.id : invoice.clientGroup.id;
        activity.remarks = `Proforma Invoice "${proforma.invoiceNumber}" Converted by ${user.fullName}`;
        await activity.save();
        if (particulars && particulars.length > 0) {
            const taskIds = particulars.map((particular) => particular.taskId);
            await createQueryBuilder(Task, 'task')
                .where('task.id IN (:...ids)', { ids: taskIds })
                .update({ paymentStatus: PaymentStatusEnum.BILLED, invoiceId: '' + invoice.id })
                .execute();
        }

        return invoice;

    };

    async cancelProformaInvoice(estimateId: number, userId) {

        let user = await User.findOne({
            where: { id: userId },
            relations: ['organization'],
        });

        let invoice = await ProformaInvoice.findOne({
            where: { id: estimateId },
            relations: ['particulars', 'otherParticulars', 'client', 'clientGroup'],
        });
        invoice.status = ProformaStatus.CANCELLED;

        if (invoice.particulars && invoice.particulars.length > 0) {
            invoice.particulars.forEach(async (particular) => {
                if (particular.taskId) {
                    let task = await Task.findOne({ where: { id: particular['taskId'] } });
                    task.proformaStatus = ProformaTaskStatus.NOT_GENERATED;
                    task.proformaInvoiceId = null;
                    task['userId'] = user.id;
                    await task.save();
                }
            });
        }
        invoice['userId'] = user.id;
        await invoice.save();

        let activity = new Activity();
        activity.action = Event_Actions.PROFORMA_INVOICE_CANCELLED;
        activity.actorId = user.id;
        activity.type = invoice.client ? ActivityType.BILLING : ActivityType.BILLING_CLIENT_GROUP;
        activity.typeId = invoice.client ? invoice.client?.id : invoice.clientGroup?.id;
        activity.remarks = `Proforma Invoice "${invoice.invoiceNumber}" Cancelled by ${user.fullName}`;
        await activity.save();


        return invoice;
    };

    //FIRST TYPE

    async exportA(userId: number, body: FindInvoicesDto) {
        let user = await User.findOne({
            where: { id: userId },
            relations: ['organization'],
        });

        let invoicesData = await this.get(userId, body);
        let invoices = invoicesData.result;

        if (!invoices.length) throw new BadRequestException('No Data for Export');

        let rows = invoices.flatMap((invoice) => {
            let addressLine1 = '';
            if (invoice?.client?.address?.['billingfulladdress']) {
                addressLine1 = invoice.client.address['billingfulladdress'];
            }
            if (invoice?.clientGroup?.address?.['billingfulladdress']) {
                addressLine1 = invoice.clientGroup.address['billingfulladdress'];
            }

            const sameState = invoice.billingEntity?.locationOfSupply === invoice.placeOfSupply.split('-')[1];
            const hasGst = invoice.billingEntity.hasGst;
            const divideTax = invoice.divideTax;

            const otherParticular = invoice?.otherParticulars?.length
                ? {
                    amount: [...invoice.otherParticulars].reduce((sum, item) => sum + item.amount * 1, 0),
                    hsn: '0',
                    gst: 'GST0',
                }
                : null;

            return [
                ...invoice.particulars,
                ...(otherParticular ? [otherParticular] : []), // Add only if otherParticular exists
            ].map((particular) => ({
                'Invoice #': invoice.invoiceNumber,
                'Invoice Date': moment(invoice.invoiceDate).format('DD-MM-YYYY'),
                'Billing Entity': invoice.billingEntity?.tradeName,
                'Client / Client Group': invoice.client ? invoice.client.displayName : invoice.clientGroup?.displayName,
                'GSTIN': invoice.client ? invoice.client.gstNumber : invoice.clientGroup?.gstNumber,
                'Address': addressLine1,
                'State': invoice.client ? invoice.client.state ?? '' : invoice.clientGroup?.state ?? '',
                'Invoice Due Date': moment(invoice.invoiceDueDate).format('DD-MM-YYYY'),
                'HSN': particular.hsn,
                'GST Rate (%)': TAX_TYPE_VALUE[particular.gst],
                'Taxable Value (₹)': particular.amount * 1,
                'IGST (₹)': ((hasGst && !sameState) || divideTax) ? getTotalGst([particular]) : 0,
                'CGST (₹)': (hasGst && sameState && !divideTax) ? Number((getTotalGst([particular]) / 2).toFixed(2)) : 0,
                'SGST (₹)': (hasGst && sameState && !divideTax) ? Number((getTotalGst([particular]) / 2).toFixed(2)) : 0,
                'Invoice Value (₹)': 1 * invoice.grandTotal,
                'Narration': invoice.narration,
                'Status': invoice.status.charAt(0).toUpperCase() + invoice.status.slice(1).toLowerCase(),
            }));

        });

        const args = {
            user: user,
        };

        this.eventEmitter.emit(Event_Actions.EXPORT_PROFORMA_INVOICES, { ...args });
        const worksheet = xlsx.utils.json_to_sheet(rows);
        const workbook = xlsx.utils.book_new();
        xlsx.utils.book_append_sheet(workbook, worksheet, 'Invoices6');
        let file = xlsx.write(workbook, { type: 'buffer' });

        return { file, type: 'Type-A' };
    }

    async exportB(userId: number, body: FindInvoicesDto) {
        let user = await User.findOne({
            where: { id: userId },
            relations: ['organization'],
        });

        let invoicesData = await this.get(userId, body);
        let invoices = invoicesData.result;
        if (!invoices.length) throw new BadRequestException('No Data for Export');
        let rows = invoices.map((invoice) => {
            let addressLine1 = '';
            if (invoice?.client?.address && invoice?.client?.address['billingfulladdress']) {
                addressLine1 = invoice?.client?.address['billingfulladdress'];
            }
            if (invoice?.clientGroup?.address && invoice?.clientGroup?.address['billingfulladdress']) {
                addressLine1 = invoice?.clientGroup?.address['billingfulladdress'];
            }
            const sameState = invoice?.billingEntity?.locationOfSupply === invoice?.placeOfSupply.split('-')[1];
            const hasGst = invoice.billingEntity.hasGst;
            const divideTax = invoice?.divideTax;
            const filterZeroParticulars = invoice.particulars.filter(p => (!hasGst || p.gst == 'GST0' || p.gst == null));
            const nonZeroParticulars = invoice.particulars.filter(p => (p.gst != 'GST0' && p.gst != null && hasGst));
            const combaineWithRates: any = Object.values(
                nonZeroParticulars.reduce((acc, item) => {
                    const gst = item.gst;
                    const amount = 1 * item.amount;
                    if (acc[gst]) {
                        acc[gst].amount += amount;
                    } else {
                        acc[gst] = { gst, amount };
                    }
                    return acc;
                }, {})
            );
            const otherParticular = {
                amount: [...invoice?.otherParticulars, ...filterZeroParticulars].reduce((sum, item) => (sum + (item.amount) * 1), 0),
                hsn: '0',
                gst: 'GST0',
            };
            return [...combaineWithRates, otherParticular].map((particular) => ({
                'Invoice #': invoice.invoiceNumber,
                'Invoice Date': moment(invoice.invoiceDate).format('DD-MM-YYYY'),
                'Billing Entity': invoice.billingEntity?.tradeName,
                'Client / Client Group': invoice.client ? invoice.client.displayName : invoice.clientGroup?.displayName,
                'GSTIN': invoice.client ? invoice.client.gstNumber : invoice.clientGroup?.gstNumber,
                'Address': addressLine1,
                'State': invoice.client ? invoice.client.state ?? '' : invoice.clientGroup?.state ?? '',
                'Due Date': invoice.invoiceDueDate,
                'GST Rate (%)': TAX_TYPE_VALUE[particular.gst],
                'Taxable Value (₹)': particular.amount * 1,
                'IGST (₹)': ((hasGst && !sameState) || divideTax) ? getTotalGst([particular]) : 0,
                'CGST (₹)': (hasGst && sameState && !divideTax) ? Number((getTotalGst([particular]) / 2).toFixed(2)) : 0,
                'SGST (₹)': (hasGst && sameState && !divideTax) ? Number((getTotalGst([particular]) / 2).toFixed(2)) : 0,
                'Invoice Value (₹)': 1 * invoice.grandTotal,
                'Narration': invoice.narration,
                'Status': invoice.status.charAt(0).toUpperCase() + invoice.status.slice(1).toLowerCase(),



            }));



        });


        const args = {
            user: user,
            // invoices:invoices,
        };

        this.eventEmitter.emit(Event_Actions.EXPORT_PROFORMA_INVOICES, { ...args });
        const worksheet = xlsx.utils.json_to_sheet(rows.flat());
        const workbook = xlsx.utils.book_new();
        xlsx.utils.book_append_sheet(workbook, worksheet, 'Invoices7');
        let file = xlsx.write(workbook, { type: 'buffer' });

        return { file, type: 'Type-B' };
    }



}