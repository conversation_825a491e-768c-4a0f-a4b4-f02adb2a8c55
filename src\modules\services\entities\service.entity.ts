import Category from 'src/modules/categories/categories.entity';
import { Organization } from 'src/modules/organization/entities/organization.entity';
import { RecurringFrequency } from 'src/modules/tasks/dto/types';
import Task from 'src/modules/tasks/entity/task.entity';
import {
  AfterLoad,
  BaseEntity,
  Column,
  CreateDateColumn,
  Entity,
  JoinTable,
  ManyToMany,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { Checklist } from './checklist.entity';
import Milestone from './milestone.entity';
import StageOfWork from './stage-of-work.entity';
import { SubTask } from './subtask.entity';
import State from 'src/modules/states/state.entity';
import { ApplicationTypeDto } from '../dto/applicationType.dto';
import Label from 'src/modules/labels/label.entity';
import { FrequenctDto } from '../dto/frequenct.dto';
import { ServiceFavorite } from './service_favorite.entity';

export enum ServiceStatus {
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE',
}

@Entity('service')
export class Service extends BaseEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  name: string;

  @Column({ default: false })
  defaultOne: boolean;

  @Column({ default: false })
  fromAdmin: boolean;

  @Column({ nullable: true })
  adminServiceId: number;

  @Column({ nullable: true })
  description: string;

  @Column({ nullable: true })
  hourlyPrice: number;

  @Column({ nullable: true })
  totalPrice: number;

  @ManyToOne(() => Organization, (organization) => organization.services)
  organization: Organization;

  @ManyToOne(() => Category, (category) => category.services)
  category: Category;

  @ManyToOne(() => Category, (category) => category.services, { nullable: true })
  subCategory: Category;

  @OneToMany(() => Task, (task) => task.service)
  tasks: Task[];

  @OneToMany(() => Checklist, (checklist) => checklist.service, { cascade: true })
  checklists: Checklist[];

  @OneToMany(() => Milestone, (milestone) => milestone.service, { cascade: true })
  milestones: Milestone[];

  @OneToMany(() => StageOfWork, (stageOfWork) => stageOfWork.service, { cascade: true })
  stageOfWorks: StageOfWork[];

  @OneToMany(() => SubTask, (subTask) => subTask.service, { cascade: true })
  subTasks: SubTask[];

  @Column({ type: 'enum', enum: RecurringFrequency, nullable: true })
  recurringFrequency: RecurringFrequency;

  @Column({ type: 'json', nullable: true })
  recurringDates: string;

  @Column({ default: 1 })
  version: number;

  @Column('json')
  clientSubCategory: string;

  @Column({ name: 'state' })
  state: string;

  @CreateDateColumn()
  createdAt: string;

  @UpdateDateColumn()
  updatedAt: string;

  @Column({ default: false })
  isActive: boolean;

  @Column({ type: 'json', nullable: true })
  linkedServices: string;

  @Column({ type: 'json', nullable: true })
  subtaskServices: any;

  @Column({ type: 'json', nullable: true })
  serviceFaqs: string;

  @Column({ type: 'json', nullable: true })
  serviceProcedure: string;

  @Column({ nullable: true })
  content: string;

  @Column({ nullable: true })
  serviceReadTime: string;

  @Column({ nullable: true })
  prismServiceName: string;

  @Column({ nullable: true })
  prismYoutubeLink: string;

  @Column({ default: false })
  postIsActive: boolean;

  @Column('json')
  prismChecklists: string;

  @Column({ nullable: true })
  prismPrice: string;

  @Column({ nullable: true })
  prismDescription: string;

  @Column({ type: 'enum', enum: ApplicationTypeDto })
  applicationType: ApplicationTypeDto;

  @Column({ type: 'enum', enum: FrequenctDto })
  frequency: FrequenctDto;

  @Column({ default: false })
  isRecurring: boolean;

  @Column('json', { array: true })
  recurringFrequencyDetails: object[];

  @ManyToMany(() => Label)
  @JoinTable()
  labels: Label[];

  @Column({ nullable: true })
  prismImage: string;

  @Column({ nullable: true })
  prismSampleCertificate: string;

  @OneToMany(() => ServiceFavorite, (favorite) => favorite.service)
  favorites: ServiceFavorite[];

  @Column({ nullable: true })
  prismProcessImage: string;

  prismImageUrl: string;

  prismSampleCertificateUrl: string;

  prismProcessImageUrl: string;

  @AfterLoad()
  renderUrl() {
    if (this.prismImage) {
      this.prismImageUrl = `${process.env.AWS_BASE_URL}/${this.prismImage}`;
    }
    if (this.prismSampleCertificate) {
      this.prismSampleCertificateUrl = `${process.env.AWS_BASE_URL}/${this.prismSampleCertificate}`;
    }
    if (this.prismProcessImage) {
      this.prismProcessImageUrl = `${process.env.AWS_BASE_URL}/${this.prismProcessImage}`;
    }
  }
}
