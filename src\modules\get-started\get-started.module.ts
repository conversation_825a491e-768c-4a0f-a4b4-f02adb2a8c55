import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { GestStartedController } from './get-started.controller';
import { GetStarted } from './get-started.entity';
import { GetStartedService } from './get-started.service';

@Module({
  imports: [TypeOrmModule.forFeature([GetStarted])],
  controllers: [GestStartedController],
  providers: [GetStartedService],
})
export class GetStartedModule {}
