import {
    Connection,
    EntitySubscriberInterface,
    EventSubscriber,
    InsertEvent,
    UpdateEvent,
} from 'typeorm';
import { Form } from '../modules/forms/schemas/form.schema';

@EventSubscriber()
export class FormsSubscriber implements EntitySubscriberInterface<Form> {
    constructor(private readonly connection: Connection) {
        connection.subscribers.push(this);
    }
    listenTo() {
        return Form;
    }

    async beforeInsert(event: InsertEvent<Form>) {
    }

    async afterInsert(event: InsertEvent<Form>) {
    }

    async beforeUpdate(event: UpdateEvent<Form>) {
    }
    
    async afterUpdate(event: UpdateEvent<Form>) {
    }
}