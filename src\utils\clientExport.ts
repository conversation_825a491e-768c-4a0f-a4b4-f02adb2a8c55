import { CategoryEnum, SubCategoryEnum } from "src/modules/clients/dto/types";
import countries from "./countries";

export function getCountryLabel(code: string) {
    const country = countries.find((c) => c.code === code);
    return country?.label || '';
}

export const CLIENT_CATEGORIES = [
      { label: "Individual", value: "individual" },
      { label: "Hindu Undivided Family", value: "huf" },
      {
        label: "Partnership Firm",
        value: "partnership_firm",
      },
      {
        label: "Limited Liability Partnership",
        value: "llp",
      },
  
      {
        label: "Company",
        value: "company",
        subCategories: [
          { label: "OPC", value: "opc" },
          { label: "Private Limited", value: "private" },
          { label: "Public Limited", value: "public" },
          { label: "Government", value: "government" },
          { label: "Section-8", value: "sec_8" },
          { label: "Foreign", value: "foreign" },
        ],
      },
      {
        label: "Association of Persons",
        value: "aop",
      },
      {
        label: "Body of Individuals",
        value: "boi",
      },
      {
        label: "Trust",
        value: "trust",
        subCategories: [
          { label: "Public Trust", value: "public_trust" },
          {
            label: "Private Discretionary Trust",
            value: "private_discretionary_trust",
          },
        ],
      },
      {
        label: "Government",
        value: "government",
        subCategories: [
          { label: "State", value: "state" },
          { label: "Central", value: "central" },
        ],
      },
      {
        label: "Local Authority",
        value: "local_authority",
      },
      {
        label: "Artificial Juridical Person",
        value: "artificial_judicial_person",
      },
    ];




   export const categoryDisplayNames = {
        [CategoryEnum.INDIVIDUAL]: 'Individual',
        [CategoryEnum.HINDU_UNDIVIDED_FAMILY]: 'Hindu Undivided Family',
        [CategoryEnum.PARTNERSHIP_FIRM]: 'Partnership Firm',
        [CategoryEnum.LIMITED_LIABILITY_PARTNERSHIP]: 'Limited Liability Partnership',
        [CategoryEnum.COMPANY]: 'Company',
        [CategoryEnum.TRUST]: 'Trust',
        [CategoryEnum.SOCIETY]: 'Society',
        [CategoryEnum.ASSOCIATION_OF_PERSONS]: 'Association of Persons',
        [CategoryEnum.BODY_OF_INDIVIDUALS]: 'Body of Individuals',
        [CategoryEnum.GOVERNMENT]: 'Government',
        [CategoryEnum.ARTIFICIAL_JURIDICAL_PERSON]: 'Artificial Juridical Person',
        [CategoryEnum.LOCAL_AUTHORITY]: 'Local Authority',
      };
      
   export   const subCategoryDisplayNames = {
        [SubCategoryEnum.NONE]: null,
        [SubCategoryEnum.INDIAN_FIRM]: 'Indian Firm',
        [SubCategoryEnum.FOREIGN_FIRM]: 'Foreign Firm',
        [SubCategoryEnum.INDIAN]: 'Indian',
        [SubCategoryEnum.FOREIGN]: 'Foreign',
        [SubCategoryEnum.PRIVATE_LIMITED]: 'Private Limited',
        [SubCategoryEnum.PUBLIC_LIMITED]: 'Public Limited',
        [SubCategoryEnum.GOVERNMENT]: 'Government',
        [SubCategoryEnum.OPC]: 'Opc',
        [SubCategoryEnum['SECTION-8']]: 'Section-8',
        [SubCategoryEnum.PUBLIC_TRUST]: 'Public Trust',
        [SubCategoryEnum.PRIVATE_DISCRETIONARY_TRUST]: 'Private Discretionary Trust',
        [SubCategoryEnum.COOPERATIVE_SOCIETY]: 'Cooperative Society',
        [SubCategoryEnum.STATE]: 'State',
        [SubCategoryEnum.CENTRAL]: 'Central',
      };

      export const STATES = [
        {
          value: "AN",
          label: "Andaman and Nicobar Islands",
        },
        {
          value: "AP",
          label: "Andhra Pradesh",
        },
        {
          value: "AR",
          label: "Arunachal Pradesh",
        },
        {
          value: "AS",
          label: "Assam",
        },
        {
          value: "BR",
          label: "Bihar",
        },
        {
          value: "CH",
          label: "Chandigarh",
        },
        {
          value: "CT",
          label: "Chhattisgarh",
        },
        {
          value: "DN",
          label: "Dadra and Nagar Haveli",
        },
        {
          value: "DD",
          label: "Daman and Diu",
        },
        {
          value: "DL",
          label: "Delhi",
        },
        {
          value: "GA",
          label: "Goa",
        },
        {
          value: "GJ",
          label: "Gujarat",
        },
        {
          value: "HR",
          label: "Haryana",
        },
        {
          value: "HP",
          label: "Himachal Pradesh",
        },
        {
          value: "JK",
          label: "Jammu and Kashmir",
        },
        {
          value: "JH",
          label: "Jharkhand",
        },
        {
          value: "KA",
          label: "Karnataka",
        },
        {
          value: "KL",
          label: "Kerala",
        },
        {
          value: "LD",
          label: "Lakshadweep",
        },
        {
          value: "MP",
          label: "Madhya Pradesh",
        },
        {
          value: "MH",
          label: "Maharashtra",
        },
        {
          value: "MN",
          label: "Manipur",
        },
        {
          value: "ML",
          label: "Meghalaya",
        },
        {
          value: "MZ",
          label: "Mizoram",
        },
        {
          value: "NL",
          label: "Nagaland",
        },
        {
          value: "OR",
          label: "Odisha",
        },
        {
          value: "PY",
          label: "Puducherry",
        },
        {
          value: "PB",
          label: "Punjab",
        },
        {
          value: "RJ",
          label: "Rajasthan",
        },
        {
          value: "SK",
          label: "Sikkim",
        },
        {
          value: "TN",
          label: "Tamil Nadu",
        },
        {
          value: "TG",
          label: "Telangana",
        },
        {
          value: "TR",
          label: "Tripura",
        },
        {
          value: "UP",
          label: "Uttar Pradesh",
        },
        {
          value: "UT",
          label: "Uttarakhand",
        },
        {
          value: "WB",
          label: "West Bengal",
        },
      ];

      export  const content1 = ['Client Number','Client Category (Drop-Down)','Client Sub-category (Depends on Main Category)','Country Code (Drop-Down)','Mobile Number','Email Id','PAN','TAN','Authorized Person','Designation','A - Country Code (Drop-Down)','Alternate Mobile Number','Date of Birth(DD-MM-YYYY)','First Name','Last Name','Middle Name','Full Name','Legal Name','Trade Name','Communication Address','State / Union Territory (Drop-Down)','Billing Address','Billing State (Drop-Down)'];
      export const content2 = ['Client Id','Display Name','GSTIN'];
      export const content3 =  ["Display Name: This is the name displayed in ATOM Platform, This Field is Non-Editable","Client Category: Select the appropriate category from the provided list (e.g., Individual, Company, HUF). This selection will determine the available options for the Sub Category field.","Sub Category: This field depends on the chosen Client Category. Select the relevant sub-category from the list that appears based on your Client Category selection.","Country Code: Select the Country Code from the provided list.","Mobile Number: Enter the mobile number without spaces or special characters (only digits).","Email ID: Enter the client's valid email address in the standard email format."];
      export const content4 = ['Client Number: Any alphanumeric combination can be used (letters and numbers).',"GSTIN (Goods and Services Tax Identification Number): Enter the client's GSTIN number if available, If a GSTIN is provided, ATOM will fetch the Details like PAN, Legal Name, Constitution of Business, State Jurisdiction and Address.","PAN (Permanent Account Number): Enter the client's PAN if available, even if a GSTIN is provided, Ensure the PAN format is correct (10 characters alphanumeric).","TAN (Tax Deduction and Collection Account Number): Enter the client's TAN if available.","Authorized Person: Enter the name of the authorized person for the client.","Designation: Enter the designation of the authorized person if their name is provided in the previous column.","A-Country Code: Select the Country Code from the provided list.","Alternate Mobile Number: Enter the alternate mobile number (without spaces or special characters).","Date of Birth (DD-MM-YYYY): Enter the client's date of birth in the specified format (DD-MM-YYYY).","First Name: Enter the client's first name.","Last Name: Enter the client's last name.","Middle Name: Enter the client's middle name.","Full Name: Enter the client's full name.","Legal Name: Enter the client's legal name (if different from the Display Name). This is particularly relevant for companies or businesses.","Trade Name: Enter the client's trade name.","Communication Address: Enter the client's complete address, including street address, city, state/province, and postal code.","Constitution of Business: Enter the legal structure of the client's business (e.g., Sole Proprietorship, Partnership, Limited Liability Company).","State / Union Territory: Select the client's state or union territory from the provided drop-down list.","Billing Address: Enter the client's complete address, including street address, city, state/province, and postal code.","Billing State / Union Territory: Select the client's state or union territory from the provided drop-down list."];
      export const content5 = ["Deleting fields from First Name to Billing State in Non-Editable Fields will not be updated if the specific client contains GSTIN, Because we are fetching the Details From GST Portal","Selecting Client Sub-category is Mandatory for Company, Trust and Government (Client Categories), If not Selected Clients will not be Updated","Only Exported Excel sheet from this Organization is accepted for Uploading"]
    