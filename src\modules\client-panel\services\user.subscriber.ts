import {
  Connection,
  EntitySubscriberInterface,
  EventSubscriber,
  InsertEvent,
  UpdateEvent,
  getManager,
} from 'typeorm';
import { User, UserType } from 'src/modules/users/entities/user.entity';
import { getAdminIds, insertINTONotificationUpdate } from 'src/utils/re-use';
import {
  insertINTOnotification,
  getAdminEmailsBasedOnOrganizationId,
  getAdminIDsBasedOnOrganizationId,
} from 'src/utils/re-use';
import NotificationPreferences from 'src/modules/notification-settings/notifications-preferences.entity';
import { Notification_Actions } from 'src/modules/notification-settings/action';
import { InternalServerErrorException } from '@nestjs/common';
import { sendWhatsAppTemplateMessage } from 'src/modules/whatsapp/whatsapp.service';

let oldUserStatus: string;
let oldUserName: string;
let oldUserLogIn: any;
@EventSubscriber()
export class UserSubscriber implements EntitySubscriberInterface<User> {
  constructor(private readonly connection: Connection) {
    connection.subscribers.push(this);
  }

  listenTo() {
    return User;
  }

  async beforeUpdate(event: UpdateEvent<User>) {
  }

  async afterUpdate(event: UpdateEvent<User>) {
  }

  async beforeInsert(event: InsertEvent<User>) {}

  async afterInsert(event: InsertEvent<User>) {
  }
}
