import { <PERSON>du<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Notification } from './notification.entity';
import { NotificationsController } from './notifications.controller';
import { NotificationsService } from './notifications.service';
import { Token } from './token.entity';
import ReminderEmailLog from './reminder-email-log.entity';

@Module({
  imports: [TypeOrmModule.forFeature([Notification, Token,ReminderEmailLog])],
  controllers: [NotificationsController],
  providers: [NotificationsService],
})
export class NotificationsModule {}
