import {
  Body,
  Controller,
  Get,
  Param,
  ParseIntPipe,
  Post,
  Put,
  Query,
  Request,
  UseGuards,
} from '@nestjs/common';
import { JwtAuthGuard } from 'src/modules/users/jwt/jwt-auth.guard';
import BulkDeleteDto from './dto/bulk-delete-dto';
import CreateLeadDto from './dto/create-lead.dto';
import { FindLeadsDto } from './dto/find-leads.dto';
import { LeadsService } from './leads.service';

@Controller('leads')
export class LeadController {
  constructor(private service: LeadsService) { }

  @UseGuards(JwtAuthGuard)
  @Post()
  async create(@Body() body: CreateLeadDto, @Request() req: any) {
    const { userId } = req.user;
    return this.service.create(userId, body);
  }

  @UseGuards(JwtAuthGuard)
  @Get()
  async get(@Request() req: any, @Query() query: FindLeadsDto) {
    const { userId } = req.user;
    return this.service.get(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Put('/:id')
  async update(@Request() req: any, @Param('id', ParseIntPipe) id: number, @Body() body: CreateLeadDto) {
    const { userId } = req.user;
    return this.service.update(id, body, userId);
  }

  @UseGuards(JwtAuthGuard)
  @Post('/delete')
  delete(@Body() body: BulkDeleteDto) {
    return this.service.delete(body.ids);
  }
}
