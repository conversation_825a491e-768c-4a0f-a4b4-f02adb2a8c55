"Fri Apr 21 2023 13:19:00 GMT+0530 (India Standard Time)---START------>executing query: \n      select task.id as id, task.status, task.name, max(date(ts.created_at)) as statusDate from task\n      left join task_status ts on task.id = ts.task_id\n      where task.status = 'done'\n      group by task.id having statusDate <= '2023-04-14'\n    <------E<PERSON>"me` AS `task_name`, `task`.`description` AS `task_description`, `task`.`remarks` AS `task_remarks`, `task`.`recurring` AS `task_recurring`, `task`.`directory` AS `task_directory`, `task`.`fee_type` AS `task_fee_type`, `task`.`fee_amount` AS `task_fee_amount`, `task`.`task_start_date` AS `task_task_start_date`, `task`.`due_date` AS `task_due_date`, `task`.`priority` AS `task_priority`, `task`.`status` AS `task_status`, `task`.`recurring_status` AS `task_recurring_status`, `task`.`payment_status` AS `task_payment_status`, `task`.`frequency` AS `task_frequency`, `task`.`termination_reason` AS `task_termination_reason`, `task`.`created_at` AS `task_created_at`, `task`.`updated_at` AS `task_updated_at`, `task`.`client_id` AS `task_client_id`, `task`.`category_id` AS `task_category_id`, `task`.`sub_category_id` AS `task_sub_category_id`, `task`.`organization_id` AS `task_organization_id`, `task`.`user_id` AS `task_user_id`, `task`.`task_leader_id` AS `task_task_leader_id`, `task`.`parent_task_id` AS `task_parent_task_id`, `task`.`estimate_id` AS `task_estimate_id`, `task`.`invoice_id` AS `task_invoice_id`, `task`.`recurring_profile_id` AS `task_recurring_profile_id`, `task`.`service_id` AS `task_service_id` FROM `task` `task` WHERE `task`.`recurring` = ? AND `task`.`recurring_status` = ? AND `task`.`task_start_date` = ?<------END""Fri Apr 21 2023 13:19:58 GMT+0530 (India Standard Time)---START------>executing query: SELECT DISTINCT `distinctAlias`.`User_id` as \"ids_User_id\" FROM (SELECT `User`.`id` AS `User_id`, `User`.`full_name` AS `User_full_name`, `User`.`mobile_number` AS `User_mobile_number`, `User`.`email` AS `User_email`, `User`.`password` AS `User_password`, `User`.`is_super_user` AS `User_is_super_user`, `User`.`image` AS `User_image`, `User`.`last_login` AS `User_last_login`, `User`.`type` AS `User_type`, `User`.`created_at` AS `User_created_at`, `User`.`updated_at` AS `User_updated_at`, `User`.`status` AS `User_status`, `User`.`profile_id` AS `User_profile_id`, `User`.`role_id` AS `User_role_id`, `User`.`organization_id` AS `User_organization_id`, `User__organization`.`id` AS `User__organization_id`, `User__organization`.`category` AS `User__organization_category`, `User__organization`.`legal_name` AS `User__organization_legal_name`, `User__organization`.`trade_name` AS `User__organization_trade_name`, `User__organization`.`place_of_supply` AS `User__organization_place_of_supply`, `User__organization`.`constitution_of_business` AS `User__organization_constitution_of_business`, `User__organization`.`registration_number` AS `User__organization_registration_number`, `User__organization`.`registration_date` AS `User__organization_registration_date`, `User__organization`.`date_of_formation` AS `User__organization_date_of_formation`, `User__organization`.`gst_verified` AS `User__organization_gst_verified`, `User__organization`.`gst_number` AS `User__organization_gst_number`, `User__organization`.`gst_status` AS `User__organization_gst_status`, `User__organization`.`gst_attachment` AS `User__organization_gst_attachment`, `User__organization`.`pan_number` AS `User__organization_pan_number`, `User__organization`.`pan_verified` AS `User__organization_pan_verified`, `User__organization`.`pan_attachment` AS `User__organization_pan_attachment`, `User__organization`.`first_name` AS `User__organization_first_name`, `User__organization`.`middle_name` AS `User__organization_middle_name`, `User__organization`.`last_name` AS `User__organization_last_name`, `User__organization`.`mobile_number` AS `User__organization_mobile_number`, `User__organization`.`alternate_mobile_number` AS `User__organization_alternate_mobile_number`, `User__organization`.`email` AS `User__organization_email`, `User__organization`.`website` AS `User__organization_website`, `User__organization`.`primary_contact_full_name` AS `User__organization_primary_contact_full_name`, `User__organization`.`primary_contact_email` AS `User__organization_primary_contact_email`, `User__organization`.`primary_contact_mobile_number` AS `User__organization_primary_contact_mobile_number`, `User__organization`.`building_no` AS `User__organization_building_no`, `User__organization`.`building_name` AS `User__organization_building_name`, `User__organization`.`street` AS `User__organization_street`, `User__organization`.`city` AS `User__organization_city`, `User__organization`.`district` AS `User__organization_district`, `User__organization`.`state` AS `User__organization_state`, `User__organization`.`pincode` AS `User__organization_pincode`, `User__organization`.`is_active` AS `User__organization_is_active`, `User__organization`.`logo` AS `User__organization_logo`, `User__organization`.`created_at` AS `User__organization_created_at`, `User__organization`.`updated_at` AS `User__organization_updated_at` FROM `user` `User` LEFT JOIN `organization` `User__organization` ON `User__organization`.`id`=`User`.`organization_id` WHERE `User`.`id` = ?) `distinctAlias` ORDER BY `User_id` ASC LIMIT 1<------END""Fri Apr 21 2023 13:19:58 GMT+0530 (India Standard Time)---START------>executing query: SELECT `User`.`id` AS `User_id`, `User`.`full_name` AS `User_full_name`, `User`.`mobile_number` AS `User_mobile_number`, `User`.`email` AS `User_email`, `User`.`password` AS `User_password`, `User`.`is_super_user` AS `User_is_super_user`, `User`.`image` AS `User_image`, `User`.`last_login` AS `User_last_login`, `User`.`type` AS `User_type`, `User`.`created_at` AS `User_created_at`, `User`.`updated_at` AS `User_updated_at`, `User`.`status` AS `User_status`, `User`.`profile_id` AS `User_profile_id`, `User`.`role_id` AS `User_role_id`, `User`.`organization_id` AS `User_organization_id`, `User__organization`.`id` AS `User__organization_id`, `User__organization`.`category` AS `User__organization_category`, `User__organization`.`legal_name` AS `User__organization_legal_name`, `User__organization`.`trade_name` AS `User__organization_trade_name`, `User__organization`.`place_of_supply` AS `User__organization_place_of_supply`, `User__organization`.`constitution_of_business` AS `User__organization_constitution_of_business`, `User__organization`.`registration_number` AS `User__organization_registration_number`, `User__organization`.`registration_date` AS `User__organization_registration_date`, `User__organization`.`date_of_formation` AS `User__organization_date_of_formation`, `User__organization`.`gst_verified` AS `User__organization_gst_verified`, `User__organization`.`gst_number` AS `User__organization_gst_number`, `User__organization`.`gst_status` AS `User__organization_gst_status`, `User__organization`.`gst_attachment` AS `User__organization_gst_attachment`, `User__organization`.`pan_number` AS `User__organization_pan_number`, `User__organization`.`pan_verified` AS `User__organization_pan_verified`, `User__organization`.`pan_attachment` AS `User__organization_pan_attachment`, `User__organization`.`first_name` AS `User__organization_first_name`, `User__organization`.`middle_name` AS `User__organization_middle_name`, `User__organization`.`last_name` AS `User__organization_last_name`, `User__organization`.`mobile_number` AS `User__organization_mobile_number`, `User__organization`.`alternate_mobile_number` AS `User__organization_alternate_mobile_number`, `User__organization`.`email` AS `User__organization_email`, `User__organization`.`website` AS `User__organization_website`, `User__organization`.`primary_contact_full_name` AS `User__organization_primary_contact_full_name`, `User__organization`.`primary_contact_email` AS `User__organization_primary_contact_email`, `User__organization`.`primary_contact_mobile_number` AS `User__organization_primary_contact_mobile_number`, `User__organization`.`building_no` AS `User__organization_building_no`, `User__organization`.`building_name` AS `User__organization_building_name`, `User__organization`.`street` AS `User__organization_street`, `User__organization`.`city` AS `User__organization_city`, `User__organization`.`district` AS `User__organization_district`, `User__organization`.`state` AS `User__organization_state`, `User__organization`.`pincode` AS `User__organization_pincode`, `User__organization`.`is_active` AS `User__organization_is_active`, `User__organization`.`logo` AS `User__organization_logo`, `User__organization`.`created_at` AS `User__organization_created_at`, `User__organization`.`updated_at` AS `User__organization_updated_at` FROM `user` `User` LEFT JOIN `organization` `User__organization` ON `User__organization`.`id`=`User`.`organization_id` WHERE ( `User`.`id` = ? ) AND ( `User`.`id` IN (107) )<------END""Fri Apr 21 2023 13:19:58 GMT+0530 (India Standard Time)---START------>executing query: SELECT DISTINCT `distinctAlias`.`dscRegister_id` as \"ids_dscRegister_id\" FROM (SELECT `dscRegister`.`id` AS `dscRegister_id`, `dscRegister`.`holder_name` AS `dscRegister_holder_name`, `dscRegister`.`expiry_date` AS `dscRegister_expiry_date`, `dscRegister`.`issued_date` AS `dscRegister_issued_date`, `dscRegister`.`received_date` AS `dscRegister_received_date`, `dscRegister`.`mobile_number` AS `dscRegister_mobile_number`, `dscRegister`.`email` AS `dscRegister_email`, `dscRegister`.`password` AS `dscRegister_password`, `dscRegister`.`token_number` AS `dscRegister_token_number`, `dscRegister`.`pan_number` AS `dscRegister_pan_number`, `dscRegister`.`holder_designation` AS `dscRegister_holder_designation`, `dscRegister`.`status` AS `dscRegister_status`, `dscRegister`.`created_at` AS `dscRegister_created_at`, `dscRegister`.`updated_at` AS `dscRegister_updated_at`, `dscRegister`.`client_id` AS `dscRegister_client_id`, `dscRegister`.`organization_id` AS `dscRegister_organization_id`, `client`.`id` AS `client_id`, `client`.`client_id` AS `client_client_id`, `client`.`slug` AS `client_slug`, `client`.`display_name` AS `client_display_name`, `client`.`category` AS `client_category`, `client`.`sub_category` AS `client_sub_category`, `client`.`email` AS `client_email`, `client`.`mobile_number` AS `client_mobile_number`, `client`.`alternate_mobile_number` AS `client_alternate_mobile_number`, `client`.`authorized_person` AS `client_authorized_person`, `client`.`designation` AS `client_designation`, `client`.`gst_verified` AS `client_gst_verified`, `client`.`gst_number` AS `client_gst_number`, `client`.`legal_name` AS `client_legal_name`, `client`.`trade_name` AS `client_trade_name`, `client`.`place_of_supply` AS `client_place_of_supply`, `client`.`constitution_of_business` AS `client_constitution_of_business`, `client`.`pan_verified` AS `client_pan_verified`, `client`.`pan_number` AS `client_pan_number`, `client`.`first_name` AS `client_first_name`, `client`.`last_name` AS `client_last_name`, `client`.`full_name` AS `client_full_name`, `client`.`building_name` AS `client_building_name`, `client`.`street` AS `client_street`, `client`.`city` AS `client_city`, `client`.`state` AS `client_state`, `client`.`pincode` AS `client_pincode`, `client`.`notes` AS `client_notes`, `client`.`dob` AS `client_dob`, `client`.`image` AS `client_image`, `client`.`local_directory_path` AS `client_local_directory_path`, `client`.`inactive_at` AS `client_inactive_at`, `client`.`client_portal_access` AS `client_client_portal_access`, `client`.`created_at` AS `client_created_at`, `client`.`updated_at` AS `client_updated_at`, `client`.`status` AS `client_status`, `client`.`organization_id` AS `client_organization_id`, `client`.`created_by_id` AS `client_created_by_id`, `client`.`user_id` AS `client_user_id`, `client`.`client_manager_id` AS `client_client_manager_id` FROM `dsc_register` `dscRegister` LEFT JOIN `client` `client` ON `client`.`id`=`dscRegister`.`client_id`  LEFT JOIN `organization` `organization` ON `organization`.`id`=`dscRegister`.`organization_id` WHERE `organization`.`id` = ?) `distinctAlias` ORDER BY `dscRegister_id` ASC LIMIT 10<------END""Fri Apr 21 2023 13:19:58 GMT+0530 (India Standard Time)---START------>executing query: SELECT `dscRegister`.`id` AS `dscRegister_id`, `dscRegister`.`holder_name` AS `dscRegister_holder_name`, `dscRegister`.`expiry_date` AS `dscRegister_expiry_date`, `dscRegister`.`issued_date` AS `dscRegister_issued_date`, `dscRegister`.`received_date` AS `dscRegister_received_date`, `dscRegister`.`mobile_number` AS `dscRegister_mobile_number`, `dscRegister`.`email` AS `dscRegister_email`, `dscRegister`.`password` AS `dscRegister_password`, `dscRegister`.`token_number` AS `dscRegister_token_number`, `dscRegister`.`pan_number` AS `dscRegister_pan_number`, `dscRegister`.`holder_designation` AS `dscRegister_holder_designation`, `dscRegister`.`status` AS `dscRegister_status`, `dscRegister`.`created_at` AS `dscRegister_created_at`, `dscRegister`.`updated_at` AS `dscRegister_updated_at`, `dscRegister`.`client_id` AS `dscRegister_client_id`, `dscRegister`.`organization_id` AS `dscRegister_organization_id`, `client`.`id` AS `client_id`, `client`.`client_id` AS `client_client_id`, `client`.`slug` AS `client_slug`, `client`.`display_name` AS `client_display_name`, `client`.`category` AS `client_category`, `client`.`sub_category` AS `client_sub_category`, `client`.`email` AS `client_email`, `client`.`mobile_number` AS `client_mobile_number`, `client`.`alternate_mobile_number` AS `client_alternate_mobile_number`, `client`.`authorized_person` AS `client_authorized_person`, `client`.`designation` AS `client_designation`, `client`.`gst_verified` AS `client_gst_verified`, `client`.`gst_number` AS `client_gst_number`, `client`.`legal_name` AS `client_legal_name`, `client`.`trade_name` AS `client_trade_name`, `client`.`place_of_supply` AS `client_place_of_supply`, `client`.`constitution_of_business` AS `client_constitution_of_business`, `client`.`pan_verified` AS `client_pan_verified`, `client`.`pan_number` AS `client_pan_number`, `client`.`first_name` AS `client_first_name`, `client`.`last_name` AS `client_last_name`, `client`.`full_name` AS `client_full_name`, `client`.`building_name` AS `client_building_name`, `client`.`street` AS `client_street`, `client`.`city` AS `client_city`, `client`.`state` AS `client_state`, `client`.`pincode` AS `client_pincode`, `client`.`notes` AS `client_notes`, `client`.`dob` AS `client_dob`, `client`.`image` AS `client_image`, `client`.`local_directory_path` AS `client_local_directory_path`, `client`.`inactive_at` AS `client_inactive_at`, `client`.`client_portal_access` AS `client_client_portal_access`, `client`.`created_at` AS `client_created_at`, `client`.`updated_at` AS `client_updated_at`, `client`.`status` AS `client_status`, `client`.`organization_id` AS `client_organization_id`, `client`.`created_by_id` AS `client_created_by_id`, `client`.`user_id` AS `client_user_id`, `client`.`client_manager_id` AS `client_client_manager_id` FROM `dsc_register` `dscRegister` LEFT JOIN `client` `client` ON `client`.`id`=`dscRegister`.`client_id`  LEFT JOIN `organization` `organization` ON `organization`.`id`=`dscRegister`.`organization_id` WHERE ( `organization`.`id` = ? ) AND ( `dscRegister`.`id` IN (29, 53, 54, 55, 56, 57, 58, 59, 85, 86) )<------END""Fri Apr 21 2023 13:19:58 GMT+0530 (India Standard Time)---START------>executing query: SELECT COUNT(DISTINCT `dscRegister`.`id`) AS `cnt` FROM `dsc_register` `dscRegister` LEFT JOIN `client` `client` ON `client`.`id`=`dscRegister`.`client_id`  LEFT JOIN `organization` `organization` ON `organization`.`id`=`dscRegister`.`organization_id` WHERE `organization`.`id` = ?<------END""Fri Apr 21 2023 13:20:00 GMT+0530 (India Standard Time)---START------>executing query: \n      select task.id as id, task.status, task.name, max(date(ts.created_at)) as statusDate from task\n      left join task_status ts on task.id = ts.task_id\n      where task.status = 'done'\n      group by task.id having statusDate <= '2023-04-14'\n    <------END""Fri Apr 21 2023 13:20:00 GMT+0530 (India Standard Time)---START------>executing query: SELECT `task`.`id` AS `task_id`, `task`.`task_number` AS `task_task_number`, `task`.`uid` AS `task_uid`, `task`.`order` AS `task_order`, `task`.`financial_year` AS `task_financial_year`, `task`.`expected_completion_date` AS `task_expected_completion_date`, `task`.`name` AS `task_name`, `task`.`description` AS `task_description`, `task`.`remarks` AS `task_remarks`, `task`.`recurring` AS `task_recurring`, `task`.`directory` AS `task_directory`, `task`.`fee_type` AS `task_fee_type`, `task`.`fee_amount` AS `task_fee_amount`, `task`.`task_start_date` AS `task_task_start_date`, `task`.`due_date` AS `task_due_date`, `task`.`priority` AS `task_priority`, `task`.`status` AS `task_status`, `task`.`recurring_status` AS `task_recurring_status`, `task`.`payment_status` AS `task_payment_status`, `task`.`frequency` AS `task_frequency`, `task`.`termination_reason` AS `task_termination_reason`, `task`.`created_at` AS `task_created_at`, `task`.`updated_at` AS `task_updated_at`, `task`.`client_id` AS `task_client_id`, `task`.`category_id` AS `task_category_id`, `task`.`sub_category_id` AS `task_sub_category_id`, `task`.`organization_id` AS `task_organization_id`, `task`.`user_id` AS `task_user_id`, `task`.`task_leader_id` AS `task_task_leader_id`, `task`.`parent_task_id` AS `task_parent_task_id`, `task`.`estimate_id` AS `task_estimate_id`, `task`.`invoice_id` AS `task_invoice_id`, `task`.`recurring_profile_id` AS `task_recurring_profile_id`, `task`.`service_id` AS `task_service_id` FROM `task` `task` WHERE `task`.`recurring` = ? AND `task`.`recurring_status` = ? AND `task`.`task_start_date` = ?<------END""Fri Apr 21 2023 13:21:00 GMT+0530 (India Standard Time)---START------>executing query: \n      select task.id as id, task.status, task.name, max(date(ts.created_at)) as statusDate from task\n      left join task_status ts on task.id = ts.task_id\n      where task.status = 'done'\n      group by task.id having statusDate <= '2023-04-14'\n    <------END""Fri Apr 21 2023 13:21:00 GMT+0530 (India Standard Time)---START------>executing query: SELECT `task`.`id` AS `task_id`, `task`.`task_number` AS `task_task_number`, `task`.`uid` AS `task_uid`, `task`.`order` AS `task_order`, `task`.`financial_year` AS `task_financial_year`, `task`.`expected_completion_date` AS `task_expected_completion_date`, `task`.`name` AS `task_name`, `task`.`description` AS `task_description`, `task`.`remarks` AS `task_remarks`, `task`.`recurring` AS `task_recurring`, `task`.`directory` AS `task_directory`, `task`.`fee_type` AS `task_fee_type`, `task`.`fee_amount` AS `task_fee_amount`, `task`.`task_start_date` AS `task_task_start_date`, `task`.`due_date` AS `task_due_date`, `task`.`priority` AS `task_priority`, `task`.`status` AS `task_status`, `task`.`recurring_status` AS `task_recurring_status`, `task`.`payment_status` AS `task_payment_status`, `task`.`frequency` AS `task_frequency`, `task`.`termination_reason` AS `task_termination_reason`, `task`.`created_at` AS `task_created_at`, `task`.`updated_at` AS `task_updated_at`, `task`.`client_id` AS `task_client_id`, `task`.`category_id` AS `task_category_id`, `task`.`sub_category_id` AS `task_sub_category_id`, `task`.`organization_id` AS `task_organization_id`, `task`.`user_id` AS `task_user_id`, `task`.`task_leader_id` AS `task_task_leader_id`, `task`.`parent_task_id` AS `task_parent_task_id`, `task`.`estimate_id` AS `task_estimate_id`, `task`.`invoice_id` AS `task_invoice_id`, `task`.`recurring_profile_id` AS `task_recurring_profile_id`, `task`.`service_id` AS `task_service_id` FROM `task` `task` WHERE `task`.`recurring` = ? AND `task`.`recurring_status` = ? AND `task`.`task_start_date` = ?<------END"