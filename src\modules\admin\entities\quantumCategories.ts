import {
    BaseEntity,
    Column,
    CreateDateColumn,
    Entity,
    ManyToOne,
    OneToMany,
    PrimaryGeneratedColumn,
    UpdateDateColumn,
} from 'typeorm';

@Entity()
class QtmCategories extends BaseEntity {
    @PrimaryGeneratedColumn()
    id: number;

    @Column()
    name: string;

    @Column({ default: false })
    defaultOne: boolean;

    @ManyToOne(() => QtmCategories, (category) => category.subCategories, { onDelete: 'CASCADE' })
    parentCategory: QtmCategories;

    @OneToMany(() => QtmCategories, (category) => category.parentCategory, { cascade: true })
    subCategories: QtmCategories[];

    @UpdateDateColumn()
    updatedAt: string;

    @CreateDateColumn()
    createdAt: string;

    @Column()
    version: number;

}

export default QtmCategories;