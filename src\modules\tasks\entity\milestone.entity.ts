import {
  BaseEntity,
  Column,
  CreateDateColumn,
  Entity,
  ManyToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import Task from './task.entity';

export enum MilestoneStatus {
  PENDING = 'PENDING',
  DONE = 'DONE',
}

@Entity('milestone')
class Milestone extends BaseEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  name: string;

  @Column({ nullable: true })
  description: string;

  @Column({ default: false })
  referenceNumber: boolean;

  @Column({ nullable: true })
  referenceNumberValue: string;

  @Column({
    type: 'enum',
    enum: MilestoneStatus,
    default: MilestoneStatus.PENDING,
  })
  status: MilestoneStatus;

  @ManyToOne(() => Task, (task) => task.milestones)
  task: Task;

  @CreateDateColumn()
  createdAt: string;

  @UpdateDateColumn()
  updatedAt: string;
}

export default Milestone;
