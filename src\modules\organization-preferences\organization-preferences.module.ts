import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { OrganizationPreferencesController } from './organization-preferences.controller';
import { OrganizationPreferencesService } from './organization-preferences.service';
import OrganizationPreferences from './entity/organization-preferences.entity';


@Module({
  imports: [
    TypeOrmModule.forFeature([
        OrganizationPreferences      
    ])
  ],
  controllers: [OrganizationPreferencesController],
  providers: [OrganizationPreferencesService],
})
export class OrganizationPreferencesModule { }