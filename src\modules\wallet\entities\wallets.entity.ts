import {
    BaseEntity,
    Entity,
    PrimaryGeneratedColumn,
    ManyToOne,
    OneToOne,
    JoinColumn,
    Column,
    CreateDateColumn,
    UpdateDateColumn,
} from "typeorm";

import { Organization } from "src/modules/organization/entities/organization.entity";
import { User } from "src/modules/users/entities/user.entity";


@Entity()
export class Wallets extends BaseEntity {

    @PrimaryGeneratedColumn()
    id: number;

    @ManyToOne(() => Organization, (organization) => organization.wallets)
    organization: Organization;

    @OneToOne(() => User, (user) => user.wallets, { cascade: true, onDelete: 'CASCADE' })
    @JoinColumn()
    user: User

    @Column({ type: 'decimal', precision: 19, scale: 2, nullable: true }) //ReceiptParticular
    balance: number;

    @CreateDateColumn()
    createdAt: string;

    @UpdateDateColumn()
    updatedAt: string;

    @Column()
    updatedBy: number;


};