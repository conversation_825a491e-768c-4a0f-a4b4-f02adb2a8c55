import { Optional } from '@nestjs/common';
import Client from 'src/modules/clients/entity/client.entity';
import { Organization } from 'src/modules/organization/entities/organization.entity';
import {
  BaseEntity,
  Column,
  CreateDateColumn,
  Entity,
  ManyToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import AutClientCredentials from './aut_client_credentials.entity';
import { StorageSystem } from 'src/modules/storage/storage.entity';
import AutOutstandingDemand from './aut-outstanding-demand.entity';

@Entity()
class AutDemandResponse extends BaseEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  demandDt:string;

  @Column()
  currentDemandAmt:string;

  @Column()
  confirmStatus: string;

  @Column()
  confirmDt: string;

  @Column()
  updatedBy: string;

  @Column()
  transactionNo: string;

  @Column()
  arrearDemandRsnCd: string;

  @Column()
  arrearDemandRsnId: string;

  @Column()
  orderPassedBy: string;

  @Column()
  orderDt:string;

  @Column()
  orderRefNum:string;

  @Column()
  demandResponseId:string;

  @Column('json')
  attachments: object;

  @Column({ type: 'enum', enum: StorageSystem, default: StorageSystem.AMAZON })
  storageSystem: StorageSystem;


  @Column()
  organizationId: number;

  @Column()
  clientId: number;

  @Column()
  lastUpdatedAt:string;

  @Column()
  lastUpdatedAtMs:string;

  // @ManyToOne(() => Client, (client) => client.autOutstandingDemand, { onDelete: 'SET NULL' })
  // client: Client;

  @ManyToOne(()=> AutOutstandingDemand, (autOutStandingDemand) => autOutStandingDemand.autDemandResponse)
  autOutstandingDemand:AutOutstandingDemand



}

export default AutDemandResponse;


