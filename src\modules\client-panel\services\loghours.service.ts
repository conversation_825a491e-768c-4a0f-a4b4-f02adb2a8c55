import { BadRequestException, ForbiddenException, Injectable } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import * as moment from 'moment';
import Client from 'src/modules/clients/entity/client.entity';
import { User } from 'src/modules/users/entities/user.entity';
import { createQueryBuilder, getManager } from 'typeorm';

import { insertINTONotificationUpdate, insertINTOnotification } from 'src/utils/re-use';
import { dateFormation } from 'src/utils/datesFormation';
import LogHour, { LogHourType, TimerStatus } from 'src/modules/log-hours/log-hour.entity';
import { GetUserLogHoursDto } from 'src/modules/log-hours/dto/get-user-log-hours.dto';
import TaskStatus from 'src/modules/tasks/entity/task-status.entity';
import { TaskStatusEnum } from 'src/modules/tasks/dto/types';

@Injectable()
export class LogHoursService {
  async find(taskId: number) {
    let logHours = await createQueryBuilder(LogHour, 'taskLogHour')
      .leftJoin('taskLogHour.task', 'task')
      .leftJoinAndSelect('taskLogHour.user', 'user')
      .where('task.id = :taskId', { taskId })
      .andWhere('taskLogHour.status = :status', { status: TimerStatus.STOPPED })
      .andWhere('taskLogHour.duration<> :duration', { duration: 0 })
      .getMany();

    let timeline = await this.getTimeline(taskId);

    return { logHours, timeline: timeline };
  }
  // async findStartedLogHoursByUserId(userId: number) {
  //   return await createQueryBuilder(LogHour, 'logHour')
  //     .leftJoinAndSelect('logHour.user', 'user')
  //     .leftJoinAndSelect('logHour.task', 'task')
  //     .where('logHour.status = :status', { status: TimerStatus.STARTED })
  //     .andWhere('logHour.user.id = :userId', { userId })
  //     .getMany();
  // }

  async findUserLogHours(userId: number, query: GetUserLogHoursDto) {
    let user = await User.findOne({ where: { id: userId }, relations: ['organization'] });

    let logHours = createQueryBuilder(LogHour, 'logHour')
      .leftJoin('logHour.user', 'user')
      .leftJoin('user.organization', 'organization')
      .leftJoinAndSelect('logHour.task', 'task')
      .leftJoinAndSelect('logHour.client', 'client')
      .where('user.id = :userId', { userId })
      .andWhere('organization.id = :orgId', { orgId: user.organization.id })
      .andWhere('logHour.status = :status', { status: TimerStatus.STOPPED })
      .andWhere('logHour.duration != :duration', { duration: 0 })
      .andWhere('task.parentTask is null')
      .skip(query.offset || 0)
      .take(query.limit || 10)
      .orderBy('logHour.completedDate', 'DESC');
    if (query.search) {
      logHours.andWhere('logHour.title LIKE :search', { search: `%${query.search}%` });
      logHours.orWhere('task.name LIKE :search', { search: `%${query.search}%` });
      logHours.orWhere('client.displayName LIKE :search', { search: `%${query.search}%` });
    }

    if (query.fromDate && query.toDate) {
      const { startTime, endTime } = dateFormation(query.fromDate, query.toDate);
      if (query.fromDate) {
        logHours.andWhere('logHour.completedDate >= :fromDate', { fromDate: startTime });
      }

      if (query.toDate) {
        logHours.andWhere('logHour.completedDate <= :toDate', { toDate: endTime });
      }
    }

    let data = await logHours.getManyAndCount();

    return {
      totalCount: data[1],
      result: data[0],
    };
  }

  async getUserLogHourStats(userId: number, query) {
    const sql = () => {
      let sql = `
    select sum(case when type = 'GENERAL' then duration else 0 end) as generalLogHours,
    sum(case when type = 'TASK' then duration else 0 end) as taskLogHours
    from log_hour where user_id = ${userId}`;
      if (query.fromDate && query.toDate) {
        const { startTime, endTime } = dateFormation(query.fromDate, query.toDate);
        sql += ` and Date(completed_date) >='${startTime}' and Date(completed_date) <='${endTime}'`;
      }

      return sql;
    };

    let result = await getManager().query(sql());

    return result[0];
  }

  async getTimeline(taskId: number) {
    let taskStatus = await TaskStatus.find({
      where: { task: { id: taskId } },
      order: { createdAt: 'DESC' },
    });
    const getStatus = (status: TaskStatusEnum) => {
      let result = taskStatus.filter((taskStatus) => {
        return taskStatus.status === status;
      });

      return result[0] ? result[0].createdAt : null;
    };

    let todo = getStatus(TaskStatusEnum.TODO);
    let inProgress = getStatus(TaskStatusEnum.IN_PROGRESS);
    let onHold = getStatus(TaskStatusEnum.ON_HOLD);
    let underReview = getStatus(TaskStatusEnum.UNDER_REVIEW);
    let completed = getStatus(TaskStatusEnum.COMPLETED);

    return {
      todo,
      in_progress: inProgress,
      on_hold: onHold,
      under_review: underReview,
      completed,
    };
  }

  // async add(body: AddLogHour) {
  //   const { startTime, endTime } = dateFormation(body.completedDate, body.completedDate);

  //   let task = await Task.findOne({ where: { id: body.taskId }, relations: ['client'] });

  //   for (const user of body.users) {
  //     let totalLogHours = await getManager().query(
  //       `SELECT SUM(duration) as count FROM log_hour WHERE user_id=${user} AND Date(completed_date) >='${startTime}' and Date(completed_date) <='${endTime}'`,
  //     );

  //     let totalLogHoursCount = Number(totalLogHours[0].count) + Number(body.duration);
  //     let logHour = new LogHour();
  //     logHour.task = task;
  //     logHour.client = task.client;
  //     logHour.user = user;
  //     if (totalLogHoursCount > 86400000) {
  //       throw new BadRequestException('24 hours completed');
  //     }
  //     logHour.duration = body.duration;
  //     logHour.completedDate = body.completedDate;
  //     logHour.status = TimerStatus.STOPPED;
  //     logHour.description = body.description;
  //     await logHour.save();
  //   }

  //   return { success: true };
  // }

  // async addUserLogHour(userId: number, body: AddUserLogHourDto) {
  //   const { startTime, endTime } = dateFormation(body.completedDate, body.completedDate);
  //   let totalLogHours = await getManager().query(
  //     `SELECT SUM(duration) as count FROM log_hour WHERE user_id=${userId} AND Date(completed_date) >='${startTime}' and Date(completed_date) <='${endTime}'`,
  //   );
  //   let totalLogHoursCount = Number(totalLogHours[0].count) + Number(body.duration);
  //   let user = await User.findOne({ where: { id: userId } });
  //   let client = await Client.findOne({ where: { id: body.client } });
  //   let logHour = new LogHour();
  //   logHour.user = user;
  //   logHour.status = TimerStatus.STOPPED;

  //   if (totalLogHoursCount > 86400000) {
  //     throw new BadRequestException('24 hours completed');
  //   }
  //   logHour.duration = body.duration;
  //   logHour.completedDate = body.completedDate;
  //   logHour.title = body.title.trim();
  //   logHour.description =
  //     body.description !== null && body.description !== undefined
  //       ? body.description.trim()
  //       : body.description;
  //   logHour.type = body.logHourType;
  //   logHour.client = client;
  //   if (body.logHourType === LogHourType.TASK) {
  //     let task = await Task.findOne({
  //       where: { id: body.task },
  //       relations: ['client'],
  //     });
  //     logHour.task = task;
  //   }

  //   await logHour.save();
  //   return logHour;
  // }

  // async update(id: number, body: UpdateLogHour, userId: number) {
  //   let logHour = await LogHour.findOne({ where: { id } });
  //   const { startTime, endTime } = dateFormation(body.completedDate, body.completedDate);
  //   let totalLogHours = await getManager().query(
  //     `SELECT SUM(duration) as count FROM log_hour WHERE user_id=${userId} AND Date(completed_date) >='${startTime}' and Date(completed_date) <='${endTime}'`,
  //   );

  //   let totalLogHoursCount = Number(totalLogHours[0].count) + Number(body.duration);
  //   if (totalLogHoursCount > 86400000) {
  //     throw new BadRequestException('24 hours completed');
  //   }
  //   logHour.duration = body.duration;
  //   // logHour.completedDate = body.completedDate;
  //   logHour.description =
  //     body.description !== null && body.description !== undefined
  //       ? body.description.trim()
  //       : body.description;
  //   await logHour.save();
  //   return { success: true };
  // }

  // async delete(id: number) {
  //   let taskLogHour = await LogHour.findOne(id);
  //   await taskLogHour.remove();
  //   return { success: true };
  // }

  // async startTimer(userId: number, body: StartTimerDto) {
  //   let user = await User.findOne({ where: { id: userId } });
  //   let task = await Task.findOne({
  //     where: { id: body.taskId },
  //     relations: ['members', 'taskLeader', 'client'],
  //   });

  //   if (!task) {
  //     throw new BadRequestException('Invalid task ID');
  //   }
  //   const startedLogHours = await this.findStartedLogHoursByUserId(userId);
  //   const existingTaskNumber =
  //     startedLogHours.length > 0 ? startedLogHours[0].task.taskNumber : null;

  //   let assignedMember = task.members.some((member) => {
  //     return member.id === userId;
  //   });

  //   let taskLeader = task.taskLeader.filter(item => item.id === userId);

  //   if (!assignedMember && !taskLeader.length) {
  //     throw new ForbiddenException('You are not allowed to start timer for this task');
  //   }
  //   let existingTaskLogHour = await LogHour.findOne({
  //     where: {
  //       status: TimerStatus.STARTED,
  //       user: { id: userId },
  //     },
  //   });
  //   if (
  //     existingTaskLogHour &&
  //     existingTaskLogHour.status == TimerStatus.STARTED &&
  //     existingTaskLogHour.user.id == userId
  //   ) {
  //     throw new BadRequestException(`The timer for this task ID ${existingTaskNumber}`);
  //   }
  //   let taskLogHour = new LogHour();
  //   taskLogHour.startTime = body.startTime;
  //   taskLogHour.status = TimerStatus.STARTED;
  //   taskLogHour.task = task;
  //   taskLogHour.user = user;
  //   taskLogHour.client = task.client;
  //   await taskLogHour.save();
  //   return { success: true };
  // }

  // async endTimer(id: number, userId: number, body: EndTimerDto) {
  //   let taskLogHour = await LogHour.findOne({ where: { id }, relations: ['user'] });
  //   const { startTime, endTime } = dateFormation(
  //     moment().format('YYYY-MM-DD'),
  //     moment().format('YYYY-MM-DD'),
  //   );
  //   let totalLogHours = await getManager().query(
  //     `SELECT SUM(duration) as count FROM log_hour WHERE user_id=${userId} AND Date(completed_date) >='${startTime}' and Date(completed_date) <='${endTime}'`,
  //   );
  //   if (taskLogHour.user?.id !== userId) {
  //     throw new ForbiddenException('You are not allowed to end timer for this task');
  //   }
  //   taskLogHour.endTime = +body.endTime;
  //   taskLogHour.duration = +body.endTime - +taskLogHour.startTime;
  //   const duration =
  //     body.endTime - +taskLogHour.startTime - ((body.endTime - +taskLogHour.startTime) % 60000);
  //   if (duration > 0 && totalLogHours[0].count < 86400000) {
  //     taskLogHour.duration = +duration;
  //     taskLogHour.status = TimerStatus.STOPPED;
  //     taskLogHour.completedDate = moment().format('YYYY-MM-DD');
  //     await taskLogHour.save();
  //   } else if (totalLogHours[0].count > 86400000) {
  //     throw new BadRequestException('24 hours completed');
  //   } else {
  //     taskLogHour.duration = 0;
  //     taskLogHour.status = TimerStatus.STOPPED;
  //     taskLogHour.completedDate = moment().format('YYYY-MM-DD');
  //     await taskLogHour.save();
  //   }

  //   return { success: true };
  // }

  // @Cron(CronExpression.EVERY_MINUTE)
  // async sendLogHoursReportToEmployee() {
  //   console.log('Sending log hours report to employees',new Date());
  // }

  // @Cron(CronExpression.EVERY_DAY_AT_4AM)
  // async sendInvocieOverDueMessage() {
  //   const userIdTwo = GetUserIdOne();
  //   if (userIdTwo !== undefined) {
  //     const entityManager = getManager();
  //     const getUserQuery = `SELECT organization_id,full_name FROM user where id = ${userIdTwo};`;
  //     const getUser = await entityManager.query(getUserQuery);
  //     const orgId = getUser[0].organization_id;
  //     const userName = getUser[0].full_name;
  //     const getInvoiceQuery = `SELECT id,invoice_number,invoice_due_date,status FROM invoice where organization_id = ${orgId};`;
  //     const getInvoice = await entityManager.query(getInvoiceQuery);
  //     const presentDate = new Date();
  //     const invoiceOverDueList = getInvoice.filter(
  //       (item) =>
  //         item.invoice_due_date < presentDate &&
  //         item.status !== 'PAID' &&
  //         item.status !== 'CANCELLED',
  //     );
  //     const invoiceList = invoiceOverDueList.map((item) => ({
  //       number: item.invoice_number,
  //       dueDate: new Date(item.invoice_due_date).toISOString().substring(0, 10),
  //     }));
  //     const invoiceString = invoiceList.map(
  //       (item) => `  ${item.number}  \n` + '                        ',
  //     );
  //     const getRoleQuery = `SELECT id FROM role where organization_id = ${orgId}  and name = "Admin";`;
  //     let getRole = await entityManager.query(getRoleQuery);
  //     const role_id = getRole[0].id;

  //     const getUserTwoQuery = `select id,email from user where organization_id=${orgId} and role_id = ${role_id} and type = 'ORGANIZATION'`;
  //     let getUserTwo = await entityManager.query(getUserTwoQuery);
  //     const userIDs = getUserTwo.map((row) => [row.id, row.email]);
  //     const user: User[] = userIDs.map((row) => row[0]);

  //     if (invoiceString.length !== 0) {
  //       let title = `Invoice Overdue`;
  //       let body = `List of Invoices Overdue : ${invoiceString}`;
  //       const key = 'INVOICE_OVERDUE_PUSH';
  //       // insertINTOnotification(title, body, user, orgId);
  //       insertINTONotificationUpdate(title, body, user, orgId, key);

  //       for (let a of userIDs) {
  //         let getEmailQuery = `SELECT id,  email,full_name FROM user where id = ${a[0]} and type = 'ORGANIZATION';`;
  //         let getEmail = await entityManager.query(getEmailQuery);
  //         let mail = getEmail[0].email;
  //         let fullname = getEmail[0].full_name;
  //         let id = getEmail[0].id;
  //         let data = {
  //           invoiceNumber: invoiceString,
  //           username: fullname,
  //           userName: userName,
  //         };
  //         let IData = {
  //           id,
  //           key: 'INVOICE_OVERDUE_MAIL',
  //           data: data,
  //           subject: `Invoice Over Due`,
  //           email: mail,
  //           filePath: 'invoice-overdue',
  //         };
  //         await sendnewMail(IData);
  //       }
  //     }
  //   }
  // }
}
