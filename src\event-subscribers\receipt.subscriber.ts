import {
  Connection,
  EntitySubscriberInterface,
  EventSubscriber,
  InsertEvent,
  UpdateEvent,
  getManager,
} from 'typeorm';
import Receipt from '../modules/billing/entitities/receipt.entity';
import { Notification } from 'src/notifications/notification.entity';
import { User } from 'src/modules/users/entities/user.entity';
import { getUserDetails, insertINTONotificationUpdate, insertINTOnotification } from 'src/utils/re-use';
import { sendnewMail } from 'src/emails/newemails';
import * as moment from 'moment';
import OrganizationPreferences from 'src/modules/organization-preferences/entity/organization-preferences.entity';
import { sendWhatsAppTextMessage } from 'src/modules/whatsapp/whatsapp.service';
import ViderWhatsappSessions from 'src/modules/whatsapp/entity/viderWhatsappSessions';
import { Organization } from 'src/modules/organization/entities/organization.entity';

@EventSubscriber()
export class ReceiptSubscriber implements EntitySubscriberInterface<Receipt> {
  constructor(private readonly connection: Connection) {
    connection.subscribers.push(this);
  }

  listenTo() {
    return Receipt;
  }

  async beforeInsert(event: InsertEvent<Receipt>) {}

  async afterInsert(event: InsertEvent<Receipt>) {
    const entityManager = getManager();
    const { organization, receiptNumber, amount, receiptDate, client, clientGroup, paymentMode , emailCheck,id:receiptId} = event?.entity;
    const organizationId = organization?.id;
    const clientName = client?.displayName || clientGroup?.displayName;
    const getRoleQuery = `SELECT id FROM role where organization_id = ${organizationId} and name = "Admin";`;
    let getRole = await entityManager.query(getRoleQuery);
    const role_id = getRole[0]?.id;
    const getUserQuery = `select id from user where organization_id=${organizationId} and role_id = ${role_id}`;
    let getUser = await entityManager.query(getUserQuery);
    const userIDs: User[] = getUser.map((row) => row.id);
    const useridtwo = event.entity['userId'];
    const logInUser = event.entity['userId'];

    const receiptDateFormat = moment(receiptDate).format('DD-MM-YYYY');
    // const address = `${organization.buildingNo || " " ? organization.buildingNo || " " + ', ' : ''}${organization.floorNumber || " " ? organization.floorNumber || " " + ', ' : ''}${organization.buildingName || " " ? organization.buildingName + ', ' : ''}${organization.street ? organization.street + ', ' : ''}${organization.location ? organization.location + ', ' : ''}${organization.city ? organization.city + ', ' : ''}${organization.district ? organization.district + ', ' : ''}${organization.state ? organization.state + ', ' : ''}${organization.pincode ? organization.pincode : ''}`;
    const addressParts = [
      organization.buildingNo || '',
      organization.floorNumber || '',
      organization.buildingName || '',
      organization.street || '',
      organization.location || '',
      organization.city || '',
      organization.district || '',
      organization.state || ''
    ].filter(part => part && part.trim() !== '');
    const pincode = organization.pincode && organization.pincode.trim() !== '' ? ` - ${organization.pincode}` : '';

      const address = addressParts.join(', ') + pincode;
    const key = 'RECEIPT_CREATION_MAIL';
    if (emailCheck && key ==='RECEIPT_CREATION_MAIL' && client){
      const mailOptions = {
        id: logInUser,
        key: 'RECEIPT_CREATION_MAIL',
        email: client?.email,
        clientMail: 'ORGANIZATION_CLIENT_EMAIL',
        data: {
          legalName:  organization?.tradeName || organization?.legalName,
          receiptDate:receiptDate,
          Amountreceived:amount,
          clientName: clientName,
          address:address,
          phoneNumber: organization?.mobileNumber,
          mail: organization?.email,
          paymentMode: paymentMode,
          userId: event.entity['userId'],
        },

        filePath: 'client-receipt-created',
        subject: ' Confirmation of Payment Received for Service Rendered',
      };

      const orgPreferences: any = await OrganizationPreferences.findOne({ where: { organization: organizationId } })
      const clientPreferences = orgPreferences?.clientPreferences?.email;

      if (clientPreferences && clientPreferences[key]) {
        await sendnewMail(mailOptions);
      }
    }
    if (useridtwo) {
      const getUserTwoQuery = `SELECT full_name FROM user where id = ${useridtwo};`;
      let getUserTwo = await entityManager.query(getUserTwoQuery);
      const userName = getUserTwo[0]?.full_name;
      async function insertINTOnotifications(event: any, users: Array<User>) {
        const key = 'RECEIPT_CREATED_PUSH';
        let title = 'Receipt Created';
        let body = `A New Receipt "<strong>${receiptNumber}</strong>" has been added for Client <strong>${clientName}</strong>.`;
        insertINTONotificationUpdate(title, body, users, organizationId, key,receiptId);
        const organization = await Organization.findOne({ id: organizationId });


        const addressParts = [
          organization.buildingNo || '',
          organization.floorNumber || '',
          organization.buildingName || '',
          organization.street || '',
          organization.location || '',
          organization.city || '',
          organization.district || '',
          organization.state || ''
        ].filter(part => part && part.trim() !== '');
        const pincode = organization.pincode && organization.pincode.trim() !== '' ? ` - ${organization.pincode}` : '';
    
        const address = addressParts.join(', ') + pincode;
        if(client){
          for (let a of users) {
            let getEmailQuery = `SELECT id, email,full_name FROM user where id = ${a};`;
            let getEmail = await entityManager.query(getEmailQuery);
            let mail = getEmail[0]?.email;
            let fullname = getEmail[0]?.full_name;
            let id = getEmail[0]?.id;
            let data = {
              clientName: clientName,
              receiptNumber: receiptNumber,
              receiptDate: receiptDateFormat,
              username: fullname,
              receiptAmount: amount,
              userName: userName,
              userId: useridtwo,
              adress: address,
                  phoneNumber: organization?.mobileNumber,
                  mail: organization?.email,
                  legalName: organization?.tradeName || organization?.legalName
            };
            let IData = {
              id,
              key: 'RECEIPT_ADDED_MAIL',
              data: data,
              subject: `Receipt has been generated`,
              email: mail,
              filePath: 'receipt-added',
            };
            await sendnewMail(IData);
          }  
        }
        try {
          if (userIDs !== undefined) {
              const sessionValidation = await ViderWhatsappSessions.findOne({
                where: { userId: userIDs ,status:'ACTIVE'},
              });
              if (sessionValidation) {
               const adminUserDetails = await getUserDetails(userIDs);
 
                const { full_name: userFullName, mobile_number: userPhoneNumber,id ,organization_id} = adminUserDetails;
                const key = 'RECEIPT_CREATED_WHATSAPP';
                const whatsappMessageBody = `
  Hi ${userFullName}
 
 A New Receipt ${receiptNumber} for ${clientName} have been created by ${userName}
 
 Receipt number: ${receiptNumber}
 Client name: ${clientName}
 Receipt amount: ${amount}

view detailed receipt to know furthur details!

We hope this helps!`
                await sendWhatsAppTextMessage(
                  `91${userPhoneNumber}`,
                  whatsappMessageBody,
                  organization_id,
                  title,
                  id,
                  key,
                );
              }
            
          }
        } catch (error) {
          console.error('Error sending User WhatsApp notification:', error);
        }
        // await Notification.save(notifications);
      }
      insertINTOnotifications(this.afterInsert, userIDs);
    }
  }

  async afterUpdate(event: UpdateEvent<Receipt>) {
    const entityManager = getManager();
    if(event?.entity?.status === "CANCELLED"){
      const { organization, receiptNumber, amount, receiptDate, client, clientGroup, paymentMode , emailCheck} = event?.entity;
      const organizationId = organization?.id;
      const clientName = client?.displayName || clientGroup?.displayName;
      const getRoleQuery = `SELECT id FROM role where organization_id = ${organizationId} and name = "Admin";`;
      let getRole = await entityManager.query(getRoleQuery);
      const role_id = getRole[0]?.id;
      const getUserQuery = `select id from user where organization_id=${organizationId} and role_id = ${role_id}`;
      let getUser = await entityManager.query(getUserQuery);
      const userIDs: User[] = getUser.map((row) => row.id);
      const useridtwo = event.entity['userId'];
      if (useridtwo) {
        const getUserTwoQuery = `SELECT full_name FROM user where id = ${useridtwo};`;
        let getUserTwo = await entityManager.query(getUserTwoQuery);
        const userName = getUserTwo[0]?.full_name;
        async function insertINTOnotifications(event: any, users: Array<User>) {
          const key = 'RECEIPT_CANCELLED_PUSH';
          let title = 'Receipt Cancelled';
          let body = `A Receipt "<strong>${receiptNumber}</strong>" for <strong>${clientName}</strong> has been Cancelled.`;
          insertINTONotificationUpdate(title, body, users, organizationId, key);
        }
        insertINTOnotifications(this.afterInsert, userIDs);
      }  
    }
  }
}
