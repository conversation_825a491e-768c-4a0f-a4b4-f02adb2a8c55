import { IsEnum, IsNotEmpty, IsOptional, Matches } from 'class-validator';
import { CategoryEnum, SubCategoryEnum } from './types';

class CreateClientDto {
  @IsNotEmpty()
  @IsEnum(CategoryEnum)
  category: CategoryEnum;

  @IsOptional()
  subCategory: SubCategoryEnum;

  @IsNotEmpty()
  @Matches(/^[^<>:"\/\\|?*\.]*$/, {
    message: 'Display name cannot contain <, >, :, ", /, \\, |, ?, *, or .',
  })
  displayName: string;


  @IsOptional()
  clientManager: number;

  @IsOptional()
  clientManagers: number[];

  @IsNotEmpty()
  mobileNumber: string;

  @IsNotEmpty()
  email: string;

  @IsOptional()
  authorizedPerson: string;

  @IsOptional()
  designation: string;

  @IsOptional()
  gstVerified: boolean;

  @IsOptional()
  gstNumber: string;

  @IsOptional()
  tradeName: string;

  @IsOptional()
  legalName: string;

  @IsOptional()
  constitutionOfBusiness: string;

  @IsOptional()
  placeOfSupply: string;

  @IsOptional()
  panVerified: boolean;

  @IsOptional()
  panNumber: string;

  @IsOptional()
  firstName: string;

  @IsOptional()
  middleName: string;

  @IsOptional()
  lastName: string;

  @IsOptional()
  fullName: string;

  @IsNotEmpty()
  clientPortalAccess: boolean;

  @IsOptional()
  isEmail: boolean;

  @IsOptional()
  issameaddress: boolean;

  @IsOptional()
  clientNumber: string;

  @IsOptional()
  clientNumberDuplicate: boolean;

  @IsOptional()
  countryCode: string;

}

export default CreateClientDto;
