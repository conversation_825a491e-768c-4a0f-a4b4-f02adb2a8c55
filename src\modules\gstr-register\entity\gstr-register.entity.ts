import { BaseEntity, <PERSON>umn, <PERSON><PERSON><PERSON>, <PERSON>in<PERSON><PERSON><PERSON><PERSON>, ManyToOne, OneToMany, OneToOne, PrimaryColumn, PrimaryGeneratedColumn } from "typeorm";
import Client from "../../clients/entity/client.entity";
import { Organization } from "../../organization/entities/organization.entity";
import { Optional } from "@nestjs/common";
import { ReturnsData } from "./returns-data.entity";
import ClientGroup from "src/modules/client-group/client-group.entity";

export enum RegistrationType {
  REGULAR_TAXPAYER = 'regular_taxpayer',
  TAX_COLLECTOR = 'tax_collector',
  TAX_DEDUCTOR = 'tax_deductor',
}

export enum YesOrNoType {
  YES = 'YES',
  NO = 'NO'
}

@Entity()
class GstrRegister extends BaseEntity {

  @PrimaryGeneratedColumn()
  id: number;

  @Column({ type: 'enum', enum: RegistrationType, default: RegistrationType.REGULAR_TAXPAYER })
  registrationType: string;

  @ManyToOne(() => Client, (client) => client.gstrRegister)
  client: Client;

  @ManyToOne(() => ClientGroup, (clientGroup) => clientGroup.gstrRegister)
  clientGroup: ClientGroup;

  @ManyToOne(() => Organization, (organization) => organization.gstrRegisters)
  organization: Organization;

  @OneToMany(() => ReturnsData, (returnsData) => returnsData.gstrRegister)
  returnsData: ReturnsData[];

  @Optional()
  @Column('json')
  gstrData: string;

}

export default GstrRegister