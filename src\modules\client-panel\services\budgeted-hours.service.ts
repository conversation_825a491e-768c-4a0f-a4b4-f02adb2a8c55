import { BadRequestException, Injectable, InternalServerErrorException } from '@nestjs/common';
import BudgetedHours, { BudgetedHourStatus } from 'src/modules/budgeted-hours/budgeted-hours.entity';
import { In, createQueryBuilder } from 'typeorm';
import Task from 'src/modules/tasks/entity/task.entity';
import { User, UserType } from 'src/modules/users/entities/user.entity';

@Injectable()
export class BudgetedHoursService {
  async find(taskId: number) {
    const budgetedHours = await createQueryBuilder(BudgetedHours, 'taskBudgetedHours')
      .leftJoinAndSelect('taskBudgetedHours.task', 'task')
      .leftJoinAndSelect('taskBudgetedHours.user', 'user')
      .where('task.id = :taskId', { taskId })
      .getMany();
    return budgetedHours;
  }
}
