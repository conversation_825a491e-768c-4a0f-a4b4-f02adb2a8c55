#GENERAL
PORT=5000

#DATABASE-MYSQL
DB_HOST=vider-db.ctnm6mdla58b.ap-south-1.rds.amazonaws.com
DB_PORT=3306
DB_USERNAME=admin
DB_PASSWORD=viderBusiness!123
DB_NAME=vider_preprod

#DATABASE-MANGODB
MONGO_DB_HOST=cluster0.elklxuj.mongodb.net
MONGO_DB_USERNAME=root
MONGO_DB_PASSWORD=Mango@Vider
MONGO_DB_NAME=vider_preprod

#AWS STORAGE
AWS_BASE_URL=https://jss-vider-bucket.s3.ap-south-1.amazonaws.com
AWS_BUCKET_NAME=jss-vider-bucket
AWS_ACCESS_KEY_ID=********************
AWS_SECRET_ACCESS_KEY=rZK98GBe0vTX+n8Tku4qgIF/zCFTLw/5bSfJ0zBi

#SMS
SMS_SERVICE_URL=https://api.msg91.com/api/v5/flow/
SMS_SERVICE_AUTH_KEY=***************************
SMS_FLOW_ID=6283825a4730554883490a0d
SMS_SENDER_ID=iVIDER

#EMAIL
EMAIL_HOST=email-smtp.ap-south-1.amazonaws.com
EMAIL_PORT=587
EMAIL_USER=********************
EMAIL_PASSWORD=BFt/gc++ytmTt24jK/**************************
FROM_EMAIL=<EMAIL>


#WEBSITE
WEBSITE_URL=https://test-ca.vider.in

#EMSIGNER
EMSIGNER_CRYPTO_SERVER_URL=http://************:8080
EMSIGNER_URL=https://gateway.emsigner.com/eMsecure/SignerGateway/Index
EMSIGNER_TOKEN=59d8464b-bdbc-452e-8e73-5f5a269e1358

#CAMUNDA
CAMUNDA_URL=http://test-cmd-api.vider.in

#FYNAMICS
FYN_URL=https://www.fynamics.co.in/api/gst/search-taxpayer/TP/
FYN_AUTH='Bearer 065557598af9641c650d8eb6b216b6cbd88d79f75fd0416afb16b0ee24f1ab240338c228512e952556906051072271856cdf5f2e7b01997db6b8d009167c1b9c692c7613d4da9d17751a035fbc496de1fb69817dc487cbadbb66626d00b46c15d27577302f491edce3cd65610c2007b921f508fa53b329a7'
FYN_RETURNS=https://www.fynamics.co.in/api/gst/view-returns/




#ONE DRIVE

ONE_DRIVE_AUTH_URL =https://login.microsoftonline.com/common/oauth2/v2.0/authorize
ONE_DRIVE_AUTH_TOKEN_URL = https://login.microsoftonline.com/common/oauth2/v2.0/token
ONE_DRIVE_URL = https://graph.microsoft.com/v1.0/me/drive/items
ONE_DRIVE_CLIENT_ID = 10f13ff9-869b-4ed7-9284-813f4faa2a37
ONE_DRIVE_CLIENT_SECRET = ****************************************
ONE_DRIVE_SCOPE = Files.ReadWrite.All offline_access User.Read
ONE_DRIVE_RESPONSE_TYPE = code
ONE_DRIVE_GRANT_TYPE_AUTH = authorization_code
ONE_DRIVE_GRANT_TYPE_REFRESH = refresh_token

#Cron

Cron_Running = 'no'

CLIENT_WEBSITE_URL=https://test-client.vider.in
