import { IsNotEmpty, IsString, IsN<PERSON>ber, ArrayNotEmpty, ValidateNested, Matches } from 'class-validator';
import { Type } from 'class-transformer';

class PasswordDetailsDto {
    @IsNotEmpty()
    @IsNumber()
    client: number;

    @IsNotEmpty()
    @IsString()
    gstId: string;

    @IsNotEmpty()
    @IsString()
    @Matches(/^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}Z[0-9A-Z]{1}$/, {
        message: 'gstId must follow the GSTIN format'
    })
    gstNumber: string;

    @IsNotEmpty()
    @IsString()
    gstPassword: string;

    @IsNotEmpty()
    @IsString()
    @Matches(/^[A-Z]{5}[0-9]{4}[A-Z]{1}$/, {
        message: 'panNumber must follow the PAN format'
    })
    panNumber: string;

    @IsNotEmpty()
    @IsString()
    password: string;
}

class ImportPasswordsDto {
    @ArrayNotEmpty()
    @ValidateNested({ each: true })
    @Type(() => PasswordDetailsDto)
    passwords: PasswordDetailsDto[];
}

export { PasswordDetailsDto, ImportPasswordsDto };
