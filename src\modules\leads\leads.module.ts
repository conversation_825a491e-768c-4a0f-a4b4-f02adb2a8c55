import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { LeadController } from './lead.controller';
import Lead from './lead.entity';
import { LeadsService } from './leads.service';
import { LeadToClientSubscriber } from 'src/event-subscribers/leadToClient.subscriber';
import { LeadSubscriber } from '../../event-subscribers/lead.subscriber';

@Module({
  imports: [TypeOrmModule.forFeature([Lead])],
  controllers: [LeadController],
  providers: [LeadsService, LeadToClientSubscriber,LeadSubscriber],

})
export class LeadsModule {}
