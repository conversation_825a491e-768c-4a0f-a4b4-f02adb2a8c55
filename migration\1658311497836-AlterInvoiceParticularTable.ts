import { MigrationInterface, QueryRunner } from 'typeorm';

export class AlterInvoiceParticularTable1658311497836
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            ALTER TABLE invoice_particular
            DROP COLUMN igst_percent,
            DROP COLUMN cgst_percent,
            DROP COLUMN sgst_percent,
            DROP COLUMN gst_amount,
            DROP COLUMN taxable_amount,
            ADD igst enum('NON_TAXABLE', 'OUT_OF_SCOPE', 'NON_GST_SUPPLY', 'GST0', 'GST5', 'GST12', 'GST18', 'GST28') not null,
            ADD cgst enum('NON_TAXABLE', 'OUT_OF_SCOPE', 'NON_GST_SUPPLY', 'GST0', 'GST5', 'GST12', 'GST18', 'GST28') not null,
            ADD sgst enum('NON_TAXABLE', 'OUT_OF_SCOPE', 'NON_GST_SUPPLY', 'GST0', 'GST5', 'GST12', 'GST18', 'GST28') not null
     `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}
