import { Controller, Get, UseGuards } from "@nestjs/common";
import { TanCronService } from "../service/tan-cron.service";
import { CronAuthGuard } from "src/cron-auth/api-key-auth.guard";

@Controller('tan-cron')
export class TanCronController{
    constructor(private service:TanCronService){}
    @UseGuards(CronAuthGuard)
     @Get('trace-notices')
     async handleTracesComminicationMail(){
        return this.service.handleTracesComminicationMail();
     }
}