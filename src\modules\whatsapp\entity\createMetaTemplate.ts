import { <PERSON><PERSON> } from "aws-sdk/clients/robomaker";
import { IsNotEmpty } from "class-validator";
import { BaseEntity, Column, CreateDateColumn, Entity, PrimaryColumn, PrimaryGeneratedColumn, UpdateDateColumn } from "typeorm";


@Entity()
class CreateMetaTemplate extends BaseEntity {
    @PrimaryGeneratedColumn()
    id: number;

    @Column()
    userId: number;

    @Column()
    organizationId: number;

    @Column()
    templateName: string;

    @Column()
    language: string;

 @Column()
 category : string;

 @Column()
 variable: string;
 
 @Column()
 header: string;
 
 @Column()
 body: string;
 
 @Column()
 footer: string;

 @Column()
 buttons: string;
 
    @Column({ type: 'timestamp', nullable: true })
    createdAt: Date;

    @UpdateDateColumn()
    updatedAt : Date;

    @Column()
    wabaId: number;

    @Column()
    default:boolean
    
}

export default CreateMetaTemplate;