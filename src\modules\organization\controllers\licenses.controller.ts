import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  ParseIntPipe,
  Patch,
  Post,
  Query,
  Request,
  UseGuards,
} from '@nestjs/common';
import { JwtAuthGuard } from 'src/modules/users/jwt/jwt-auth.guard';
import { CreateOrganizationLicenseDto } from '../dto/create-organization-license.dto';
import { GetBankAccountsDto } from '../dto/get-bank-accounts.dto';
import { LicensesService } from '../services/licenses.service';

@Controller('organization-lincenses')
export class LicensesController {
  constructor(private service: LicensesService) { }

  @UseGuards(JwtAuthGuard)
  @Get()
  async getOrganizationLicenses(
    @Request() request: any,
    @Query() query: GetBankAccountsDto,
  ) {
    let { userId } = request.user;

    return this.service.getOrganizationLicenses(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Post()
  async createOrganizationLicense(
    @Request() request: any,
    @Body() body: CreateOrganizationLicenseDto,
  ) {
    let { userId } = request.user;
    return this.service.createOrganizationLicense(userId, body);
  }

  @UseGuards(JwtAuthGuard)
  @Patch(':licenseId')
  async updateOrganizationLicense(
    @Request() request: any,
    @Body() body: CreateOrganizationLicenseDto,
    @Param('licenseId', ParseIntPipe) licenseId: number,
  ) {
    let { userId } = request.user;
    return this.service.updateOrganizationLicense(licenseId, userId, body);
  }

  @UseGuards(JwtAuthGuard)
  @Delete(':licenseId')
  async deleteOrganizationLicense(
    @Request() request: any,
    @Param('licenseId', ParseIntPipe) licenseId: number,
  ) {
    let { userId } = request.user;
    return this.service.deleteOrganizationLicense(userId, licenseId);
  }
}
