import {
  Connection,
  EntitySubscriberInterface,
  EventSubscriber,
  UpdateEvent,
  getManager,
} from 'typeorm';
import { UserProfile } from 'src/modules/users/entities/user-profile.entity';
import { getAdminIds, insertINTOnotification, insertINTONotificationUpdate } from 'src/utils/re-use';


@EventSubscriber()
export class UserProfileSubscriber implements EntitySubscriberInterface<UserProfile> {
  constructor(private readonly connection: Connection) {
    connection.subscribers.push(this);
  }

  listenTo() {
    return UserProfile;
  }

  async beforeUpdate(event: UpdateEvent<UserProfile>) {
  }

  async afterUpdate(event: UpdateEvent<UserProfile>) {
    const { id: userProfileId } = event.entity
    const orgSql = `SELECT organization_id,full_name from user_profile inner join user on user_profile.id=user.profile_id where user_profile.id=${userProfileId}`
    const entityManager = getManager()
    const org = await entityManager.query(orgSql)
    const [{ organization_id: orgId, full_name: userName }] = org
    const adminArry = await getAdminIds(orgId)
    const title = "User Profile Updation"
    const body = `<strong>${userName}</strong> has updated his profile details, check the profile to know full details`;
    insertINTOnotification(title, body, adminArry, orgId,userProfileId)
  }
}
