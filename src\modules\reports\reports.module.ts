import { <PERSON>du<PERSON> } from '@nestjs/common';
import { ReportsController } from './reports.controller';
import { ReportsService } from './reports.service';
import { TasksService } from '../tasks/services/tasks.service';
import { ClientGroupService } from '../client-group/client-group.service';
import { ClientService } from '../clients/services/clients.service';
import { StorageService } from '../storage/storage.service';
import { OneDriveStorageService } from '../ondrive-storage/onedrive-storage.service';
import { AwsService } from '../storage/upload.service';
import { AttachmentsService } from '../tasks/services/attachments.service';
import { GoogleDriveStorageService } from '../ondrive-storage/googledrive-storage.service';
import { BharathStorageService } from '../storage/bharath-storage.service';
import { BharathCloudService } from '../storage/bharath-upload.service';
import { PromiseService } from '../gstr-register/services/promise.service';
import { GstrRegisterService } from '../gstr-register/services/gstr-register.service';

@Module({
  controllers: [ReportsController],
  providers: [
    ReportsService,
    TasksService,
    ClientGroupService,
    ClientService,
    StorageService,
    OneDriveStorageService,
    AwsService,
    GoogleDriveStorageService,
    AttachmentsService,
    BharathStorageService,
    BharathCloudService,
    PromiseService,
    GstrRegisterService,
  ],
})
export class ReportsModule {}
