import { BadRequestException, Injectable, InternalServerErrorException } from '@nestjs/common';
import CreateCollectDataDto from 'src/modules/collect-data/dto/create-collect-data.dto';
import CollectData from 'src/modules/collect-data/collect-data.entity';
import { User } from 'src/modules/users/entities/user.entity';
import Client from 'src/modules/clients/entity/client.entity';
import Task from 'src/modules/tasks/entity/task.entity';
import { AwsService } from './upload.service';
import Storage, { StorageSystem, StorageType } from 'src/modules/storage/storage.entity';
import { In, <PERSON><PERSON>han, <PERSON><PERSON>han, createQueryBuilder, getRepository } from 'typeorm';
import { v4 as uuidv4 } from 'uuid';
import { Cron, CronExpression } from '@nestjs/schedule';
import * as moment from 'moment';
import { StorageService } from './storage.service';
import { OneDriveStorageService } from './onedrive-storage.service';

@Injectable()
export class CollectDataService {
  constructor(
    private oneDriveService: OneDriveStorageService,
    private uploadService: AwsService,
    private storageService: StorageService,
  ) { }


  async findOne(id: string) {
    const collectData = await CollectData.findOne({
      where: { uid: id, active: true },
      relations: ['user', 'client', 'task', 'task.members', 'user.organization'],
    });


    if (collectData) {
      const members = collectData.task.members.map((member) => member.fullName)
      return {
        organization: collectData.user.organization.tradeName || collectData.user.organization.legalName,
        user: collectData.user.fullName,
        notes: collectData.notes,
        client: collectData.client.displayName,
        phone: collectData.user.organization.mobileNumber,
        email: collectData.user.organization.email,
        serviceName: collectData.name,
        fields: collectData.listOfFiles,
        createdDate: collectData.createdAt,
        updateData: collectData.updatedAt,
        task: collectData.task.id,
        dueDate: collectData.task.dueDate,
        members

      };
    }
    return [];
  }

  async findOneTask(id: number) {
    const collectData = await CollectData.find({
      where: { task: id, },
      relations: ['task', 'user', 'user.imageStorage'],
      order: {
        ['id']: -1
      }
    });
    return collectData


  }

  async getAttachments(id: string) {
    const collectData = await CollectData.findOne({ uid: id });
    if (collectData) {
      let taskAttachments = await Storage.find({
        where: { collectId: collectData.id },
        relations: ['task'],
      });
      return taskAttachments;
    };
    return [];


  }

  async create(userId: number, data: CreateCollectDataDto) {
    const {
      freeSpace,
      unExpriedLinks } = await this.storageService.getOrgStorage(userId)
    const user = await User.findOne({ where: { id: userId } });
    const client = await Client.findOne({ where: { id: data.client } });
    const task = await Task.findOne({ where: { id: data.task } });
    let collectData = new CollectData();
    collectData.appName = data.appName;
    collectData.name = data.name;
    collectData.user = user;
    collectData.client = client;
    collectData.notes = data.notes;
    collectData.task = task;
    collectData.listOfFiles = { fileNames: [...data.fields] };
    collectData.active = true;
    collectData.whatsappCheck = data.whatsappCheck;
    collectData.emailCheck = data.emailCheck;
    collectData.uid = uuidv4();
    collectData['userId'] = userId;


    if (((freeSpace - unExpriedLinks) >= 209715200)) {
      return collectData.save();
    } else {
      throw new BadRequestException(
        'We regret to inform you that the storage limit for your organization has been exceeded. As a result, no further data can be stored until the storage usage is reduced or additional storage capacity is allocated.',
      );
    }
  }

  async updateData(id: number, userId: number, data: CreateCollectDataDto) {

    const user = await User.findOne({ where: { id: userId } });
    const client = await Client.findOne({ where: { id: data.client } });
    const task = await Task.findOne({ where: { id: data.task } });
    const collectData = await CollectData.findOne({ where: { id: id } });
    collectData.appName = data.appName;
    collectData.name = data.name;
    collectData.user = user;
    collectData.client = client
    collectData.notes = data.notes;
    collectData.task = task;
    collectData.listOfFiles = { fileNames: [...data.fields] };
    collectData.active = true;
    collectData.whatsappCheck = data?.whatsappCheck
    collectData.emailCheck = data?.emailCheck;
    collectData['userId'] = userId;
    return collectData.save();

  };


  // async saveAttchement(origin: string,
  //   collectId: any,
  //   taskId: number,
  //   files: Express.Multer.File[],) {
  //   let task = await Task.findOne({
  //     where: { id: taskId },
  //     relations: ['organization'],
  //   });

  //   if (task.organization.storageSystem === 'AMAZON') {
  //     return await this.addAttachments(origin,
  //       collectId,
  //       taskId,
  //       files)

  //   } else if (task.organization.storageSystem === 'MICROSOFT') {
  //     return await this.oneDriveService.collectDataAddAttc(origin, collectId, taskId, files)
  //   };
  // }

  async addAttachments(
    origin: string,
    collectId: any,
    taskId: number,
    files: Express.Multer.File[],
  ) {
    try {
      let task = await Task.findOne({
        where: { id: taskId },
        relations: ['client', 'category', 'subCategory','clientGroup'],
      });

      let taskStorage = await this.existingClientTaskStorage(task);

      let taskAttachments: Storage[] = [];
      const collectData = await CollectData.findOne({ where: { uid: collectId } })

      for (let file of files) {
        const { buffer, mimetype, originalname } = file;
        let key = `storage/tasks/${taskId}/${file.originalname}`;

        let upload: any = await this.uploadService.upload(buffer, key, mimetype);

        let storage = new Storage();
        storage.name = originalname;
        storage.file = upload.Key;
        storage.task = task
        storage.fileType = mimetype;
        storage.fileSize = file.size;
        storage.client = task?.client;
        storage.clientGroup = task?.clientGroup;
        storage.type = StorageType.FILE;
        storage.parent = taskStorage;
        storage.storageSystem = StorageSystem.AMAZON;
        storage.collectId = collectData?.id;
        storage.origin = origin;
        taskAttachments.push(storage);
      };

      await Storage.save(taskAttachments);
      // this.eventEmitter.emit(Event_Actions.COLLECT_DATA_FILES_UPLOAD, {
      //   origin,
      //   collectId,
      //   taskId,
      //   files: files.map(file => file.originalname)
      // });

      return {
        success: true,
      };
    } catch (err) {
      console.log(err);
      throw new InternalServerErrorException(err);
    }
  }

  async addAttachmentsFromStorage(taskId: number, fileIds: number[]) {
    try {
      let task = await Task.findOne({
        where: { id: taskId },
        relations: ['client', 'category', 'subCategory','clientGroup'],
      });

      let files = await Storage.find({
        where: { id: In(fileIds) },
        relations: ['client','clientGroup'],
      });

      let taskStorage = await this.existingClientTaskStorage(task);

      let taskAttachments: Storage[] = [];

      for (let file of files) {
        let existingFile: any = await this.uploadService.get(file.file);

        let key = `storage/tasks/${taskId}-${moment().valueOf()}/${file.file}`;

        let uploadedFile: any = await this.uploadService.upload(
          existingFile?.Body as Buffer,
          key,
          file.fileType,
        );

        let storage = new Storage();
        storage.name = file.name;
        storage.file = uploadedFile.Key;
        storage.task = task;
        storage.fileType = file.fileType;
        storage.client = file?.client;
        storage.clientGroup = file?.clientGroup;
        storage.type = StorageType.FILE;
        storage.parent = taskStorage;
        taskAttachments.push(storage);
      }

      await Storage.save(taskAttachments);

      return {
        success: true,
      };
    } catch (err) {
      throw new BadRequestException(err);
    }
  }

  async existingClientTaskStorage(task: Task) {
    if (!task.client) return null;

    let taskFinYearStorage = await Storage.findOne({
      where: {
        name: task.financialYear,
        client: { id: task.client?.id },
      },
    });

    if (!taskFinYearStorage) {
      taskFinYearStorage = new Storage();
      taskFinYearStorage.name = task.financialYear;
      taskFinYearStorage.client = task?.client;
      taskFinYearStorage.clientGroup = task?.clientGroup;
      taskFinYearStorage.type = StorageType.FOLDER;
      taskFinYearStorage.uid = uuidv4();
      await taskFinYearStorage.save();
    }

    let taskCategoryStorage = await Storage.findOne({
      where: {
        name: task.category?.name,
        parent: { id: taskFinYearStorage.id },
        client: { id: task.client?.id },
      },
      relations: ['parent'],
    });

    if (!taskCategoryStorage && task.category) {
      taskCategoryStorage = new Storage();
      taskCategoryStorage.name = task.category?.name;
      taskCategoryStorage.client = task?.client;
      taskCategoryStorage.clientGroup = task?.clientGroup;
      taskCategoryStorage.type = StorageType.FOLDER;
      taskCategoryStorage.parent = taskFinYearStorage;
      taskCategoryStorage.uid = uuidv4();
      await taskCategoryStorage.save();
    }

    let taskSubCategoryStorage = await Storage.findOne({
      where: {
        name: task.subCategory?.name,
        parent: { id: taskCategoryStorage?.id },
        client: { id: task.client?.id },
      },
      relations: ['parent'],
    });

    if (!taskSubCategoryStorage && task.subCategory) {
      taskSubCategoryStorage = new Storage();
      taskSubCategoryStorage.name = task.subCategory?.name;
      taskSubCategoryStorage.client = task.client;
      taskSubCategoryStorage.clientGroup = task?.clientGroup;
      taskSubCategoryStorage.type = StorageType.FOLDER;
      taskSubCategoryStorage.parent = taskCategoryStorage;
      taskSubCategoryStorage.uid = uuidv4();
      await taskSubCategoryStorage.save();
    }

    let taskStorage = await createQueryBuilder(Storage, 'storage')
      .leftJoinAndSelect('storage.parent', 'parent')
      .where('storage.name = :name', { name: task.name })
      .andWhere('(parent.id = :subCategory or parent.id = :category)', {
        subCategory: taskSubCategoryStorage?.id,
        category: taskCategoryStorage?.id,
      })
      .getOne();

    if (!taskStorage) {
      let storage = new Storage();
      storage.name = task.name;
      storage.client = task.client;
      storage.clientGroup = task?.clientGroup;
      storage.type = StorageType.FOLDER;
      storage.uid = uuidv4();
      storage.parent = taskSubCategoryStorage || taskCategoryStorage || taskFinYearStorage;
      taskStorage = await Storage.save(storage);
    }

    return taskStorage;
  }

}
