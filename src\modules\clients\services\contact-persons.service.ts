import { BadRequestException, Injectable } from '@nestjs/common';
import Client from 'src/modules/clients/entity/client.entity';
import Contact<PERSON>erson from '../entity/contact-person.entity';
import CreateConctactPersonDto from '../dto/create-contact-person.dto';
import { User, UserType } from 'src/modules/users/entities/user.entity';
import { randomBytes } from 'crypto';
import { Event_Actions } from 'src/event-listeners/actions';
import { EventEmitter2 } from '@nestjs/event-emitter';
import Activity, { ActivityType } from 'src/modules/activity/activity.entity';

@Injectable()
export class ContactPersonService {
  constructor(private eventEmitter: EventEmitter2) { }

  async create(data: CreateConctactPersonDto, userId) {
    const userData = await User.findOne({ where: { id: userId }, relations: ['organization'] });
    let client = await Client.findOne({ where: { id: data.client } });
    // let existingUser = await User.findOne({ where: { email: data.email } });
    // if (existingUser) {
    //   throw new BadRequestException('User with this email already exists');
    // }
    // let existingUserName = await User.findOne({where: { fullName: data.name }});
    // if(existingUserName){
    //   throw new BadRequestException('User with this Name already exists');
    // }
    let contactPerson = new ContactPerson();
    contactPerson.name = data.name.trim();
    contactPerson.email = data.email.trim();
    contactPerson.mobile = data.mobile;
    contactPerson.countryCode = data.countryCode;
    contactPerson.role = data.role;
    contactPerson.client = client;



    let user = new User();
    let randomPassword = randomBytes(8).toString('hex');
    user.image = data.image;
    user.fullName = data.name.trim();
    user.email = data.email.trim();
    user.password = randomPassword;
    user.mobileNumber = data.mobile;
    user.type = UserType.CLIENT_USER;
    user['userId'] = userId;
    contactPerson.user = user;

    await contactPerson.save();

    let activity = new Activity();
    activity.action = Event_Actions.CLIENT_USER_CREATED;
    activity.actorId = userData.id;
    activity.type = ActivityType.CLIENT;
    activity.typeId = client.id;
    activity.remarks = `Client User "${contactPerson.name}" Created by ${userData.fullName}`;
    await activity.save();

    this.eventEmitter.emit(Event_Actions.CLIENT_USER_CREATED, {
      email: data.email,
      fullName: data.name,
      password: randomPassword,
    });

    return contactPerson;
  }

  async update(id: number, data: CreateConctactPersonDto, userId) {
    const userData = await User.findOne({ where: { id: userId }, relations: ['organization'] });
    let contactPerson = await ContactPerson.findOne({ where: { id }, relations: ['client'] });
    // if(contactPerson.email.length !== data.email.length){
    //   let existingUser = await User.findOne({ where: { email: data.email } });
    //   if (existingUser) {
    //     throw new BadRequestException('User with this email already exists');
    //   }
    // }
    // if(contactPerson.name.length !== data.name.length){
    //   let existingUserName = await User.findOne({where: { fullName: data.name }});
    //   if(existingUserName){
    //     throw new BadRequestException('User with this Name already exists');
    //   } 
    // }
    contactPerson.name = data.name.trim();
    contactPerson.email = data.email.trim();
    contactPerson.mobile = data.mobile;
    contactPerson.role = data.role;
    contactPerson.countryCode = data.countryCode;
    await contactPerson.save();

    let activity = new Activity();
    activity.action = Event_Actions.CLIENT_USER_UPDATED;
    activity.actorId = userData.id;
    activity.type = ActivityType.CLIENT;
    activity.typeId = contactPerson.client.id;
    activity.remarks = `Client User "${contactPerson.name}" Updated by ${userData.fullName}`;
    await activity.save();

    return contactPerson;
  }

  async delete(id: number, userId) {
    const userData = await User.findOne({ where: { id: userId }, relations: ['organization'] });
    let contactPerson = await ContactPerson.findOne({ where: { id }, relations: ['client'] });
    await contactPerson.remove();

    let activity = new Activity();
    activity.action = Event_Actions.CLIENT_USER_DELETED;
    activity.actorId = userData.id;
    activity.type = ActivityType.CLIENT;
    activity.typeId = contactPerson.client.id;
    activity.remarks = `Client User "${contactPerson.name}" Deleted by ${userData.fullName}`;
    await activity.save();

    return { success: true };
  }
}
