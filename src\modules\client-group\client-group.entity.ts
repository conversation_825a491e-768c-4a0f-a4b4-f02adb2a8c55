import { Organization } from 'src/modules/organization/entities/organization.entity';
import {
  BaseEntity,
  Column,
  CreateDateColumn,
  Entity,
  JoinColumn,
  JoinTable,
  ManyToMany,
  ManyToOne,
  OneToMany,
  OneToOne,
  PrimaryGeneratedColumn,
} from 'typeorm';
import Client from '../clients/entity/client.entity';
import Task from '../tasks/entity/task.entity';
import CollectData from '../collect-data/collect-data.entity';
import Expenditure from '../expenditure/expenditure.entity';
import Storage from 'src/modules/storage/storage.entity';
import LogHour from '../log-hours/log-hour.entity';
import Event from 'src/modules/events/event.entity';
import Kyb from '../kyb/kyb.entity';
import Password from '../clients/entity/password.entity';
import DscRegister from '../dsc-register/entity/dsc-register.entity';
import ClientPin from '../clients/entity/client-pin.entity';
import RecurringProfile from '../recurring/entity/recurring-profile.entity';
import QtmActivity from '../admin/entities/qtmActivity.entity';
import UdinTask from '../udin-task/udin-task.entity';
import { Invoice } from '../billing/entitities/invoice.entity';
import ReceiptCredit from '../billing/entitities/receipt-credit.entity';
import Receipt from '../billing/entitities/receipt.entity';
import GstrRegister, { RegistrationType, YesOrNoType } from '../gstr-register/entity/gstr-register.entity';
import { User } from '../users/entities/user.entity';
import { ProformaInvoice } from '../billing/entitities/proforma-invoice.entity';
import DocumentInOut from '../document-in-out/entity/document-in-out.entity';

export enum UserStatus {
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE',
  DELETED = 'DELETED',
}

@Entity()
class ClientGroup extends BaseEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ nullable: true })
  clientId: string;

  @Column()
  displayName: string;

  @Column({ type: 'enum', enum: RegistrationType })
  registrationType: RegistrationType;

  @Column()
  email: string;

  @Column()
  mobileNumber: string;

  // @Column({ type: 'enum', enum: YesOrNoType })
  // incomeTaxAudit: YesOrNoType;

  // @Column({ type: 'enum', enum: YesOrNoType })
  // gstAnnualForm: YesOrNoType;

  @Column({ nullable: true })
  panNumber: string;

  @Column({ nullable: true })
  clientNumber: string;

  @Column({ nullable: true })
  countryCode: string;

  @Column({ default: false })
  gstVerified: boolean;

  @Column({ nullable: true })
  gstNumber: string;

  @Column({ nullable: true })
  tanNumber: string;

  @Column({ nullable: true })
  legalName: string;

  @Column({ nullable: true })
  tradeName: string;

  @Column({ nullable: true })
  placeOfSupply: string;

  @Column({ nullable: true })
  constitutionOfBusiness: string;

  @Column({ default: false })
  panVerified: boolean;

  @Column({ nullable: true })
  firstName: string;

  @Column({ nullable: true })
  lastName: string;

  @Column({ nullable: true })
  middleName: string;

  @Column({ nullable: true })
  fullName: string;

  @Column({ nullable: true })
  buildingName: string;

  @Column({ nullable: true })
  street: string;

  @Column({ nullable: true })
  city: string;

  @Column({ nullable: true })
  state: string;

  @Column({ nullable: true })
  pincode: string;

  @Column({ nullable: true, type: 'date' })
  dob: string;

  @Column({ nullable: true })
  gstRegistrationDate: string;

  @Column({ type: 'enum', enum: UserStatus, default: UserStatus.ACTIVE })
  status: UserStatus;

  @ManyToOne(() => Organization, (organization) => organization.clientGroup)
  organization: Organization;

  @ManyToMany(() => Client, (client) => client.groupClients)
  @JoinTable()
  clients: Client[];

  @OneToMany(() => ClientPin, (clientPin) => clientPin.clientGroup)
  clientPins: ClientPin[];

  @OneToMany(() => RecurringProfile, (recurringProfile) => recurringProfile.clientGroup)
  recurringProfiles: RecurringProfile[];

  @OneToOne(() => CollectData, (collectData) => collectData.clientGroup)
  collectData: CollectData;

  @OneToOne(() => Expenditure, (expenditure) => expenditure.clientGroup)
  expenditure: Expenditure;

  @OneToMany(() => Storage, (storage) => storage.clientGroup)
  storage: Storage[];

  @OneToMany(() => LogHour, (logHour) => logHour.clientGroup)
  logHours: LogHour[];

  @OneToMany(() => Event, (event) => event.clientGroup)
  events: Event[];

  @OneToMany(() => Task, (task) => task.clientGroup)
  tasks: Task[];

  @OneToMany(() => Kyb, (kyb) => kyb.clientGroup)
  kyb: Kyb[];

  @OneToMany(() => Password, (password) => password.clientGroup)
  passwords: Password[];

  @ManyToMany(() => DscRegister, (dscRegister) => dscRegister.clientGroups)
  dscRegisters: DscRegister[];

  @OneToMany(() => QtmActivity, (qtmActivity) => qtmActivity.clientGroup)
  qtmActivitys: QtmActivity[];

  @OneToMany(() => UdinTask, (udinTask) => udinTask.clientGroup)
  udinTasks: UdinTask[];

  @OneToMany(() => Invoice, (invoice) => invoice.clientGroup)
  invoices: Invoice[];

  @OneToMany(() => ReceiptCredit, (rc) => rc.clientGroup)
  receiptCredits: ReceiptCredit[];

  @OneToMany(() => Receipt, (receipt) => receipt.clientGroup)
  receipts: Receipt[];

  @OneToMany(() => ProformaInvoice, (proformaInvoice) => proformaInvoice.clientGroup)
  proformaInvoice: ProformaInvoice[];

  @OneToOne(() => Storage, (storage) => storage.clientGroupImage, { cascade: true })
  clientGroupImage: Storage;

  @ManyToOne(() => User, (user) => user.clients)
  createdBy: User;

  @OneToOne(() => User, (user) => user.client, { cascade: true })
  @JoinColumn()
  user: User;

  @ManyToMany(() => User, (user) => user.clientGroupsManagers)
  @JoinTable()
  clientGroupManagers: User[];

  @OneToMany(() => GstrRegister, (gstrRegister) => gstrRegister.clientGroup)
  gstrRegister: GstrRegister[];

  @OneToMany(() => DocumentInOut, (documentInOut) => documentInOut.clientGroup)
  documentInOut: DocumentInOut[];

  @Column({ type: 'timestamp', nullable: true })
  inactiveAt: Date;

  @Column({ default: false })
  clientPortalAccess: boolean;

  @Column('json', { array: true })
  address: object[];

  @Column({ default: false })
  issameaddress: boolean;

  @CreateDateColumn()
  createdAt: string;

  @CreateDateColumn()
  updatedAt: string;

  @Column()
  type: string;
}

export default ClientGroup;
