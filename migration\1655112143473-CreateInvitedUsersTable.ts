import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateInvitedUsersTable1655112143473
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `create table invited_users (
                id int not null auto_increment,
                full_name varchar(255) not null,
                email varchar(255) not null,
                mobile_number varchar(255) not null,
                created_at datetime(6) default current_timestamp(6),
                primary key (id)
              );`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}
