import { Injectable, NestMiddleware } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';

@Injectable()
export class RequestTimeMiddleware implements NestMiddleware {
  use(req: Request, res: Response, next: NextFunction) {
    const start = Date.now();

    res.on('finish', () => {
      const duration = Date.now() - start;
      if (duration > 10000) {
        console.log(`${req.method} ${req.originalUrl} Took- ${duration}ms`);
      }
    });

    next();
  }
}
