import { BadRequestException, Injectable, InternalServerErrorException } from '@nestjs/common';
import BudgetedHours, { BudgetedHourStatus } from './budgeted-hours.entity';
import { Brackets, In, createQueryBuilder } from 'typeorm';
import Task from '../tasks/entity/task.entity';
import { User, UserType } from '../users/entities/user.entity';
import Activity, { ActivityType } from '../activity/activity.entity';
import { Event_Actions } from 'src/event-listeners/actions';
import * as moment from 'moment';
import LogHour from '../log-hours/log-hour.entity';
import { TaskRecurringStatus, TaskStatusEnum } from '../tasks/dto/types';
import * as ExcelJS from 'exceljs';
import { getTitle } from 'src/utils';

@Injectable()
export class BudgetedHoursService {
  async find(taskId: number) {
    const budgetedHours = await createQueryBuilder(BudgetedHours, 'taskBudgetedHours')
      .leftJoinAndSelect('taskBudgetedHours.task', 'task')
      .leftJoinAndSelect('taskBudgetedHours.user', 'user')
      .where('task.id = :taskId', { taskId })
      .getMany();
    return budgetedHours;
  }

  async update(body: any, userId: number) {
    let user = await User.findOne({
      where: { id: userId },
      relations: ['organization'],
    });

    if (!body?.members?.length) {
      throw new BadRequestException('Please add Atleast One Member !');
    }

    const budgetedHours = await createQueryBuilder(BudgetedHours, 'taskBudgetedHours')
      .leftJoinAndSelect('taskBudgetedHours.task', 'task')
      .leftJoinAndSelect('taskBudgetedHours.user', 'user')
      .where('task.id = :taskId', { taskId: body?.taskId })
      .getMany();

    let oldUserIds = budgetedHours.map((item) => item?.user?.id);

    const task = await Task.findOne({ where: { id: body?.taskId }, relations: ['client', 'members', 'clientGroup'] });
    const oldTaskMembers = task?.members;
    const oldBudgetedHours = task.budgetedhours;

    let newUserIds = body.members.map((item) => item?.id);

    const oldUsersNotInNew = oldUserIds.filter((userId) => !newUserIds.includes(userId));

    if (body?.budgetedHoursInSeconds) {
      task.budgetedhours = body?.budgetedHoursInSeconds;

      for (let item of body?.members) {
        let userData = await User.findOne({ where: { id: item?.id }, relations: ['organization'] });

        if (oldUserIds.includes(item?.id)) {
          const hours = await BudgetedHours.findOne({ where: { id: item?.taskBudgetedHours?.id } });

          if (hours) {
            hours.budgetedHours = item?.taskBudgetedHours?.userBudgetedHoursInSeconds;
            hours.status = BudgetedHourStatus.ACTIVE;
            hours.client = task?.client;
            await hours.save();
          } else {
            const oldInactive = await BudgetedHours.findOne({
              where: { task: task, user: userData },
            });

            oldInactive.budgetedHours = item?.taskBudgetedHours?.userBudgetedHoursInSeconds;
            oldInactive.status = BudgetedHourStatus.ACTIVE;
            oldInactive.client = task?.client;
            oldInactive.save();
          }
        } else {
          let budgetedHours = new BudgetedHours();
          budgetedHours.status = BudgetedHourStatus.ACTIVE;
          budgetedHours.budgetedHours = item?.taskBudgetedHours['userBudgetedHoursInSeconds'];
          budgetedHours.organization = user?.organization;
          budgetedHours.task = task;
          budgetedHours.user = userData;
          budgetedHours.client = task?.client;
          await budgetedHours.save();
        }
      }

      if (oldUsersNotInNew.length) {
        await createQueryBuilder(BudgetedHours, 'taskBudgetedHours')
          .update(BudgetedHours)
          .set({ status: BudgetedHourStatus.INACTIVE })
          .where('task.id = :taskId', { taskId: body?.taskId })
          .andWhere('user.id IN (:userIds)', { userIds: oldUsersNotInNew })
          .execute();
      }
    } else {
      task.budgetedhours = 0;

      const budgetedHours = await createQueryBuilder(BudgetedHours, 'taskBudgetedHours')
        .leftJoinAndSelect('taskBudgetedHours.task', 'task')
        .leftJoinAndSelect('taskBudgetedHours.user', 'user')
        .where('task.id = :taskId', { taskId: body?.taskId })
        .getMany();

      for (let i of budgetedHours) {
        const DeltingBudgetedHours = await BudgetedHours.findOne({ where: { id: i?.id } });
        DeltingBudgetedHours.budgetedHours = 0;
        DeltingBudgetedHours.status = BudgetedHourStatus.INACTIVE;
        DeltingBudgetedHours.save();
      }
    }
    const oldHours = Math.floor(moment.duration(oldBudgetedHours).asHours());
    const remainingDuration = moment.duration(oldBudgetedHours).subtract(Math.floor(moment.duration(oldBudgetedHours).asHours()), 'hours');
    const remainingMinutes = Math.floor(remainingDuration.asMinutes());
    const oldMinutes = remainingMinutes;

    const newHours = Math.floor(moment.duration(body?.budgetedHoursInSeconds).asHours());
    const newremainingDuration = moment
      .duration(body?.budgetedHoursInSeconds)
      .subtract(Math.floor(moment.duration(body?.budgetedHoursInSeconds).asHours()), 'hours');
    const newremainingMinutes = Math.floor(newremainingDuration.asMinutes());
    const newMinutes = newremainingMinutes;

    if (body?.budgetedHoursInSeconds) {
      if (parseInt(body?.budgetedHoursInSeconds) === Number(oldBudgetedHours)) {
      } else {
        let taskactivity = new Activity();
        taskactivity.action = Event_Actions.BUDGETED_HOURS_UPDATED;
        taskactivity.actorId = user.id;
        taskactivity.type = ActivityType.TASK;
        taskactivity.typeId = task.id;
        taskactivity.remarks = `Previous Budgeted Hours : ${oldHours} hrs ${oldMinutes} Mns Current Budgeted Hours : ${newHours} Hrs ${newMinutes} Mns`;
        await taskactivity.save();
      }
    } else if (Number(oldBudgetedHours) !== parseInt(body?.budgetedHoursInSeconds)) {
      let taskactivity = new Activity();
      taskactivity.action = Event_Actions.BUDGETED_HOURS_UPDATED;
      taskactivity.actorId = user.id;
      taskactivity.type = ActivityType.TASK;
      taskactivity.typeId = task.id;
      taskactivity.remarks = `Previous Budgeted Hours : ${oldHours} hrs ${oldMinutes} Mns Current Budgeted Hours : 0 hrs 0 Mns`;
      await taskactivity.save();
    }


    const taskMemebersIds = task.members.map(item => item?.id);
    const taskmembersName = task.members.map(item => item.fullName);
    const membersIds = body.members.map((item) => item?.id);
    const membersName = body.members.map((item) => item.fullName);
    let membersdifference = membersIds.filter(x => !taskMemebersIds.includes(x));
    let taskmembersdifference = taskMemebersIds.filter(x => !membersIds.includes(x));
    if (membersdifference.length || taskmembersdifference.length) {
      let taskactivity = new Activity();
      taskactivity.action = Event_Actions.TASK_MEMBERS_UPDATED;
      taskactivity.actorId = user.id;
      taskactivity.type = ActivityType.TASK;
      taskactivity.typeId = task.id;
      taskactivity.remarks = `Past Members: ${taskmembersName.join(", ")}\n\n\n\n\nCurrent Members: ${membersName.join(", ")}`;
      await taskactivity.save();
    }
    let members = await User.find({ where: { id: In(membersIds), type: UserType.ORGANIZATION } });
    task.members = members;
    task['userId'] = user.id;
    task['oldTaskMembers'] = oldTaskMembers;
    await task.save();
    return task;
  }

  async timesheetReport(userId: number, query: any) {
    try {
      const user = await User.findOne({
        where: { id: userId },
        relations: ['organization'],
      });

      if (!user || !user.organization) {
        throw new Error('User or organization not found');
      }

      if (query.type === "groupByAssignee") {
        const tasksDataQUery = await createQueryBuilder(Task, 'task')
          .select([
            'task.id',
            'task.name',
            'task.taskNumber',
            'task.status',
            'task.taskStartDate',
            'task.budgetedhours',
            'task.recurringStatus',
            'organization.id',
            'members.id',
            'members.fullName',
            'client.id',
            'client.displayName',
            'clientGroup.id',
            'clientGroup.displayName',
            'category.id',
            'category.name',
            'subCategory.id',
            'subCategory.name',
            'taskBudgetedHours',
            'userBudgeted',
            'taskLogHours',
            'user'
          ])
          .leftJoin('task.category', 'category')
          .leftJoin('task.subCategory', 'subCategory')
          .leftJoin('task.client', 'client')
          .leftJoin('task.clientGroup', 'clientGroup')
          .leftJoin('task.organization', 'organization')
          .leftJoin('task.members', 'members')
          .leftJoin(
            'task.taskBudgetedHours',
            'taskBudgetedHours',
            'taskBudgetedHours.status = :status'
          )
          .setParameter('status', 'ACTIVE')
          .leftJoin('taskBudgetedHours.user', 'userBudgeted')
          .leftJoin('task.taskLogHours', 'taskLogHours')
          .leftJoin('taskLogHours.user', 'user')
          .where('task.taskStartDate >= :fromDate', { fromDate: moment(query.fromDate, "DD-MM-YYYY").format('YYYY-MM-DD') })
          .andWhere('task.taskStartDate <= :endDate', { endDate: moment(query.toDate, "DD-MM-YYYY").format('YYYY-MM-DD') })
          .andWhere('task.organization = :orgId', { orgId: user.organization.id })
          .andWhere('task.recurringStatus = :recurringStatus', { recurringStatus: TaskRecurringStatus.CREATED })
          .andWhere('task.status IN (:...statuses)', {
            statuses: [
              TaskStatusEnum.TODO,
              TaskStatusEnum.IN_PROGRESS,
              TaskStatusEnum.UNDER_REVIEW,
              TaskStatusEnum.ON_HOLD,
              TaskStatusEnum.COMPLETED,
            ],
          })
          .andWhere(
            new Brackets(qb => {
              qb.where('task.budgetedhours IS NOT NULL')
                .andWhere('task.budgetedhours != 0');
            })
          )

        if (query.search) {
          tasksDataQUery.andWhere(
            '(clientGroup.displayName LIKE :search OR client.displayName LIKE :search OR task.name LIKE :search OR task.taskNumber LIKE :search)',
            {
              search: `%${query.search}%`,
            },
          );
        }

        const tasksData = await tasksDataQUery.getMany();
        const userData = {};

        for (let task of tasksData) {
          for (let budgetedHour of task.taskBudgetedHours) {
            const userIds = Object.keys(userData);
            const userID = String(budgetedHour.user.id);
            if (!userIds.includes(userID)) {
              userData[`${budgetedHour.user.id}`] = {};
            }
            const taskData = {
              id: task.id,
              taskNumber: task.taskNumber,
              status: task.status,
              name: task.name,
              client: {
                id: task.client?.id,
                displayName: task.client?.displayName,
              },
              clientGroup: {
                id: task.clientGroup?.id,
                displayName: task.clientGroup?.displayName
              },
              category: {
                id: task.category?.id,
                name: task?.category?.name,
              },
              subCategory: {
                id: task.subCategory?.id,
                name: task.subCategory?.name
              },
              budgetedHours: budgetedHour.budgetedHours,
            }
            userData[`${budgetedHour.user.id}`][task.id] = taskData
            userData[`${budgetedHour.user.id}`]['fullName'] = budgetedHour.user?.fullName;
          }
          const logHourData = {};

          for (let logHour of task.taskLogHours) {
            if (!(Object.keys(logHourData).includes(`${logHour.user.id}`))) {
              logHourData[`${logHour.user.id}`] = 0;
            }
            const userIds = Object.keys(userData);
            const userID = String(logHour.user.id);
            if (!userIds.includes(userID)) {
              userData[`${logHour.user.id}`] = {};
              userData[`${logHour.user.id}`]['fullName'] = logHour.user.fullName;
            }
            if (userData[`${logHour.user.id}`]?.[task.id]) {
              logHourData[`${logHour.user.id}`] = parseInt(logHourData[`${logHour.user.id}`]) + Number(logHour.duration);
              userData[`${logHour.user.id}`][task.id]['logHourData'] = logHourData;
            }
          }
        }

        return {
          groupedData: userData,
          globalTotals: {
            totalBillable: 0,
            totalNonBillable: 0,
            totalDuration: 0,
          },
        };
      }

      if (query.type === "groupByTask") {
        const tasksDataQuery = await createQueryBuilder(Task, 'task')
          .select([
            'task.id',
            'task.taskNumber',
            'task.status',
            'task.name',
            'task.taskStartDate',
            'task.recurringStatus',
            'task.budgetedhours',
            'organization.id',
            'members.id',
            'members.fullName',
            'client.id',
            'client.displayName',
            'clientGroup.id',
            'clientGroup.displayName',
            'category.id',
            'category.name',
            'subCategory.id',
            'subCategory.name',
            'taskBudgetedHours',
            'userBudgeted',
            'taskLogHours',
            'user'
          ])
          .leftJoin('task.category', 'category')
          .leftJoin('task.subCategory', 'subCategory')
          .leftJoin('task.client', 'client')
          .leftJoin('task.clientGroup', 'clientGroup')
          .leftJoin('task.organization', 'organization')
          .leftJoin('task.members', 'members')
          .leftJoin('task.taskBudgetedHours', 'taskBudgetedHours')
          .leftJoin('taskBudgetedHours.user', 'userBudgeted')
          .leftJoin('task.taskLogHours', 'taskLogHours')
          .leftJoin('taskLogHours.user', 'user')
          .where('task.taskStartDate >= :fromDate', { fromDate: moment(query.fromDate, "DD-MM-YYYY").format('YYYY-MM-DD') })
          .andWhere('task.taskStartDate <= :endDate', { endDate: moment(query.toDate, "DD-MM-YYYY").format('YYYY-MM-DD') })
          .andWhere('task.organization = :orgId', { orgId: user.organization.id })
          .andWhere('task.recurringStatus = :recurringStatus', { recurringStatus: TaskRecurringStatus.CREATED })
          .andWhere('task.status IN (:...status)', {
            status: [
              TaskStatusEnum.TODO,
              TaskStatusEnum.IN_PROGRESS,
              TaskStatusEnum.UNDER_REVIEW,
              TaskStatusEnum.ON_HOLD,
              TaskStatusEnum.COMPLETED,
            ],
          })
          .andWhere(
            new Brackets(qb => {
              qb.where('task.budgetedhours IS NOT NULL')
                .andWhere('task.budgetedhours != 0');
            })
          );

        if (query.search) {
          tasksDataQuery.andWhere(
            '(clientGroup.displayName LIKE :search OR client.displayName LIKE :search OR task.name LIKE :search OR task.taskNumber LIKE :search)',
            {
              search: `%${query.search}%`,
            },
          );
        }

        const tasksData = await tasksDataQuery.getMany();

        return {
          groupedData: tasksData,
          globalTotals: {
            totalBillable: 0,
            totalNonBillable: 0,
            totalDuration: 0,
          },
        };
      }

    } catch (error) {
      console.error('Error occurred while fetching new timesheet:', error);
      throw error;
    }
  }

  async exportLogHoursTimesheetReport(userId: number, body: any) {

    const newQuery = { ...body, type: "groupByAssignee" };
    let timesheets = await this.timesheetReport(userId, newQuery);

    const workbook = new ExcelJS.Workbook();
    const userSheet = workbook.addWorksheet('Group by Assignee');

    const userHeaders = [
      { header: 'S.No', key: 'serialNo', width: 10 },
      { header: 'User Name', key: 'userName', width: 10 },
      { header: 'Client / Clients Group', key: 'client', width: 50 },
      { header: 'Task ID', key: 'taskId', width: 10 },
      { header: 'Service Category', key: 'serviceCategory', width: 10 },
      { header: 'Service Sub-Category', key: 'subCategory', width: 10 },
      { header: 'Task Name', key: 'taskName', width: 50 },
      { header: 'Task Status', key: 'status', width: 10 },
      { header: 'Budgeted Hours', key: 'budgetedhours', width: 10 },
      { header: 'Actual Hours', key: 'actualhours', width: 10 },
      { header: 'Variance (In Mnts) BH-AH', key: 'variance', width: 10 },
      { header: 'Efficiency % (BH / AH * 100)', key: 'efficiency', width: 10 },
    ];

    userSheet.columns = userHeaders;

    let serialNo = 1;

    const userData = Object.entries(timesheets.groupedData);
    const tabDataa = userData.map((item: any) => {
      const tasksArray = Object.values(item[1]);
      tasksArray.pop();
      const BH: any = tasksArray.reduce((acc: number, curr: any) => acc + parseInt(curr.budgetedHours), 0);
      const lhours = BH ? Math.floor(Math.abs(BH) / (1000 * 60 * 60)) : 0;
      const lminutes = BH ? Math.floor((Math.abs(BH) % (1000 * 60 * 60)) / (1000 * 60)) : 0;
      let TLH = 0;
      const TLHH = tasksArray.map((acc: any) => {
        if (acc?.logHourData?.[item[0]]) {
          TLH += acc?.logHourData?.[item[0]];
        }
      });
      const loghours = TLH ? Math.floor(Math.abs(TLH) / (1000 * 60 * 60)) : 0;
      const logminutes = TLH ? Math.floor((Math.abs(TLH) % (1000 * 60 * 60)) / (1000 * 60)) : 0;
      const variance = BH - TLH;
      const variancehours = TLH ? Math.floor(Math.abs(variance) / (1000 * 60 * 60)) : 0;
      const varianceminutes = TLH ? Math.floor((Math.abs(variance) % (1000 * 60 * 60)) / (1000 * 60)) : 0;
      const sign = variance < 0 ? "-" : "";
      const totalVariance = `${sign} ${variancehours < 10 ? "0" : ""}${variancehours} hrs ${varianceminutes < 10 ? "0" : ""}${varianceminutes} mins`

      return {
        id: item[0],
        fullName: item[1].fullName,
        tasks: tasksArray,
        BH: `${lhours < 10 ? "0" : ""}${lhours} hrs ${lminutes < 10 ? "0" : ""}${lminutes} mins`,
        LH: `${loghours < 10 ? "0" : ""}${loghours} hrs ${logminutes < 10 ? "0" : ""}${logminutes} mins`,
        TV: totalVariance
      }
    });

    tabDataa.forEach((group) => {
      group.tasks.forEach((task: any) => {
        const budgeted = parseInt(task.budgetedHours || 0);
        const logged = task?.logHourData?.[group.id] || 0;

        const variance = budgeted - logged;

        const formatDuration = (ms: number) => {
          const h = Math.floor(ms / (1000 * 60 * 60));
          const m = Math.floor((ms % (1000 * 60 * 60)) / (1000 * 60));
          return `${h < 10 ? '0' : ''}${h} hrs ${m < 10 ? '0' : ''}${m} mins`;
        };

        const budgetedStr = budgeted ? formatDuration(Math.abs(budgeted)) : 'NA';
        const loggedStr =
          logged && (Math.floor(logged / (1000 * 60 * 60)) > 0 || Math.floor((logged % (1000 * 60 * 60)) / (1000 * 60)) > 0)
            ? formatDuration(Math.abs(logged))
            : 'NA';

        let varianceStr = 'NA';
        if (logged) {
          const sign = variance < 0 ? '-' : '';
          const absVariance = Math.abs(variance);
          const vh = Math.floor(absVariance / (1000 * 60 * 60));
          const vm = Math.floor((absVariance % (1000 * 60 * 60)) / (1000 * 60));
          if (vh !== 0 || vm !== 0) {
            varianceStr = `${sign} ${vh < 10 ? '0' : ''}${vh} hrs ${vm < 10 ? '0' : ''}${vm} mins`;
          }
        }

        let efficiency = 'NA';
        if (budgeted && logged) {
          efficiency = `${((budgeted / logged) * 100).toFixed(2)}%`;
        }

        userSheet.addRow({
          serialNo: serialNo++,
          userName: group.fullName,
          taskId: task.taskNumber || '',
          taskName: task.name || '',
          client: task.client?.displayName || task.clientGroup?.displayName || '',
          serviceCategory: task.category?.name || '',
          subCategory: task.subCategory?.name || '',
          status: getTitle(task.status) || '',
          budgetedhours: budgetedStr,
          actualhours: loggedStr,
          variance: varianceStr,
          efficiency: efficiency,
        });
      });
    });

    // Format headers
    const headerRow = userSheet.getRow(1);
    headerRow.font = { bold: true };
    headerRow.eachCell((cell) => {
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '64B5F6' },
      };
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' },
      };
      cell.alignment = { horizontal: 'center', vertical: 'middle' };
    });

    // Freeze header
    userSheet.views = [{ state: 'frozen', ySplit: 1 }];

    // Style all rows
    userSheet.eachRow({ includeEmpty: false }, (row, rowNumber) => {
      if (rowNumber !== 1) {
        row.eachCell((cell) => {
          cell.alignment = { horizontal: 'center', vertical: 'middle' };
        });
      }
    });

    // Apply status colors after adding all rows
    userSheet.eachRow({ includeEmpty: false }, (row, rowNumber) => {
      if (rowNumber === 1) return; // Skip header

      const statusCell = row.getCell('status'); // Column key from userSheet.columns
      const status = (statusCell.value || '').toString().toLowerCase();

      switch (status) {
        case 'todo':
          statusCell.font = { color: { argb: '149ECD' }, bold: true }; // Blue
          break;
        case 'in progress':
          statusCell.font = { color: { argb: 'F49752' }, bold: true }; // Orange
          break;
        case 'under review':
          statusCell.font = { color: { argb: '653BBA' }, bold: true }; // Purple
          break;
        case 'on hold':
        case 'deleted':
        case 'terminated':
          statusCell.font = { color: { argb: 'F63338' }, bold: true }; // Red
          break;
        case 'completed':
          statusCell.font = { color: { argb: '008000' }, bold: true }; // Green
          break;
        default:
          statusCell.font = { color: { argb: '000000' }, bold: true }; // Black
          break;
      }
    });


    // Auto column widths with wrapping for long columns
    userSheet.columns.forEach((column) => {
      let maxLength = column.header.length;
      column.eachCell({ includeEmpty: true }, (cell) => {
        const val = cell.value ? cell.value.toString() : '';
        maxLength = Math.max(maxLength, val.length);
      });
      if (column.key === 'taskName' || column.key === 'client') {
        column.width = 50;
        column.alignment = { wrapText: true, horizontal: 'center', vertical: 'middle' };
      } else {
        column.width = maxLength + 2;
      }
    });

    // Apply efficiency colors after adding all rows
    userSheet.eachRow({ includeEmpty: false }, (row, rowNumber) => {
      if (rowNumber === 1) return; // Skip header

      const efficiencyCell = row.getCell('efficiency'); // Column key from userSheet.columns
      const efficiencyValue = (efficiencyCell.value || '').toString().replace('%', '').trim();

      if (efficiencyValue.toLowerCase() === 'na' || efficiencyValue === '') return;

      const efficiencyNum = parseFloat(efficiencyValue);

      let colorCode = '000000'; // default black
      if (efficiencyNum === 100) {
        colorCode = '52A76E'; // Green
      } else if (efficiencyNum < 100) {
        colorCode = 'F32A32'; // Red
      } else if (efficiencyNum > 100) {
        colorCode = '0F73AE'; // Blue
      }

      efficiencyCell.font = { color: { argb: colorCode }, bold: true };
    });

    // Sheet 2 - Group by Date
    const dateSheet = workbook.addWorksheet('Group by Task');
    const dateHeaders = [
      { header: 'S.No', key: 'serialNo', width: 10 },
      { header: 'Client / Clients Group', key: 'client', width: 50 },
      { header: 'Task ID', key: 'taskId', width: 10 },
      { header: 'Service Category', key: 'serviceCategory', width: 10 },
      { header: 'Service Sub-Category', key: 'subCategory', width: 10 },
      { header: 'Task Name', key: 'taskName', width: 50 },
      { header: 'Task Status', key: 'status', width: 10 },
      { header: 'Budgeted Hours', key: 'budgetedhours', width: 10 },
      { header: 'Actual Hours', key: 'actualhours', width: 10 },
      { header: 'Variance (In Mnts) BH-AH', key: 'variance', width: 10 },
      { header: 'Efficiency % (BH / AH * 100)', key: 'efficiecny', width: 10 },
      { header: 'Task Assignees', key: 'taskAssignee', width: 50 },
    ];
    dateSheet.columns = dateHeaders;

    let dateSerial = 1;
    const newQuery1 = { ...body, type: "groupByTask" };
    let timesheets1: any = await this.timesheetReport(userId, newQuery1);

    const groupedByDateSheet: { [key: string]: any[] } = {};

    const tabDataa1 = timesheets1?.groupedData?.map((item: any) => {
      const BH = item.budgetedhours || 0;
      let TLH = 0;

      const totalLogHours = item.members.map(eachItem => {
        const logHours = item.taskLogHours.filter(logItem => logItem.user.id === eachItem.id)
        const totalLogHours = logHours?.reduce((acc, curr) => acc + parseInt(curr.duration), 0);
        TLH += totalLogHours;
      });
      const lhours = Math.floor(BH / (1000 * 60 * 60));
      const lminutes = Math.floor((BH % (1000 * 60 * 60)) / (1000 * 60));
      const loghours = Math.floor(TLH / (1000 * 60 * 60));
      const logminutes = Math.floor((TLH % (1000 * 60 * 60)) / (1000 * 60));

      const variance = BH - TLH;
      const variancehours = TLH ? Math.floor(Math.abs(variance) / (1000 * 60 * 60)) : 0;
      const varianceminutes = TLH ? Math.floor((Math.abs(variance) % (1000 * 60 * 60)) / (1000 * 60)) : 0;
      const sign = variance < 0 ? "-" : "";
      const totalVariance = `${sign} ${variancehours < 10 ? "0" : ""}${variancehours} hrs ${varianceminutes < 10 ? "0" : ""}${varianceminutes} mins`;
      const variancePEr = TLH ? (BH / TLH) * 100 : 0;
      const formattedPercentageVariance = TLH ? Math.abs(variancePEr).toFixed(2) : 'NA';
      return {
        completedDate: item.completedDate,
        user: item.user,
        client: item.client?.displayName || item.clientGroup?.displayName || '',
        task: item.task,
        category: item.category,
        subCategory: item.subCategory,
        status: getTitle(item.status),
        taskName: item.name || item.title,
        taskId: item.taskNumber,
        budgetedhours: `${lhours < 10 ? "0" : ""}${lhours} hrs ${lminutes < 10 ? "0" : ""}${lminutes} mins`,
        actualhours: `${loghours < 10 ? "0" : ""}${loghours} hrs ${logminutes < 10 ? "0" : ""}${logminutes} mins`,
        variance: totalVariance,
        efficiecny: formattedPercentageVariance,
        taskAssignee: item.members.map(eachMember => eachMember.fullName).join(", "),
      };
    });

    // Group by completedDate
    tabDataa1.forEach((entry) => {
      const formattedDate = entry.completedDate
        ? moment(entry.completedDate).format('DD-MM-YYYY')
        : 'Unknown Date';

      if (!groupedByDateSheet[formattedDate]) {
        groupedByDateSheet[formattedDate] = [];
      }
      groupedByDateSheet[formattedDate].push(entry);
    });

    // Sort and write to sheet
    const sortedDatesSheet = Object.keys(groupedByDateSheet).sort((a, b) =>
      moment(a, 'DD-MM-YYYY').toDate().getTime() - moment(b, 'DD-MM-YYYY').toDate().getTime()
    );

    sortedDatesSheet.forEach((dateKey) => {
      const entries = groupedByDateSheet[dateKey];

      entries.forEach((entry) => {
        dateSheet.addRow({
          serialNo: dateSerial++,
          client: entry.client || '',
          serviceCategory: entry.category?.name || '',
          subCategory: entry.subCategory?.name || '',
          taskId: entry.taskId || '',
          taskName: entry.taskName || '',
          status: entry.status || '',
          budgetedhours: entry.budgetedhours || '',
          actualhours: entry.actualhours || '',
          variance: entry.variance || '',
          efficiecny: entry.efficiecny !== "NA" ? `${entry.efficiecny} %` : entry.efficiecny || "",
          taskAssignee: entry.taskAssignee,
        });
      });
    });

    // Freeze header row
    dateSheet.views = [{ state: 'frozen', ySplit: 1 }];

    // Style header
    const dateHeaderRow = dateSheet.getRow(1);
    dateHeaderRow.font = { bold: true };
    dateHeaderRow.eachCell((cell) => {
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '64B5F6' },
      };
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' },
      };
      cell.alignment = { horizontal: 'center', vertical: 'middle' };
    });

    // Center align all cells
    dateSheet.eachRow({ includeEmpty: false }, (row, rowNumber) => {
      if (rowNumber !== 1) {
        row.eachCell((cell) => {
          cell.alignment = { horizontal: 'center', vertical: 'middle' };
        });
      }
    });

    // Apply status colors after adding all rows
    dateSheet.eachRow({ includeEmpty: false }, (row, rowNumber) => {
      if (rowNumber === 1) return; // Skip header

      const statusCell = row.getCell('status'); // Column key from userSheet.columns
      const status = (statusCell.value || '').toString().toLowerCase();

      switch (status) {
        case 'todo':
          statusCell.font = { color: { argb: '149ECD' }, bold: true }; // Blue
          break;
        case 'in progress':
          statusCell.font = { color: { argb: 'F49752' }, bold: true }; // Orange
          break;
        case 'under review':
          statusCell.font = { color: { argb: '653BBA' }, bold: true }; // Purple
          break;
        case 'on hold':
        case 'deleted':
        case 'terminated':
          statusCell.font = { color: { argb: 'F63338' }, bold: true }; // Red
          break;
        case 'completed':
          statusCell.font = { color: { argb: '008000' }, bold: true }; // Green
          break;
        default:
          statusCell.font = { color: { argb: '000000' }, bold: true }; // Black
          break;
      }
    });

    // Apply status colors after adding all rows
    dateSheet.eachRow({ includeEmpty: false }, (row, rowNumber) => {
      if (rowNumber === 1) return; // Skip header

      const statusCell = row.getCell('efficiecny'); // Column key from userSheet.columns
      let efficiencyValue = (statusCell.value || '').toString().replace('%', '').trim();

      // Convert to number
      const variance = parseFloat(efficiencyValue);

      let color = '000000';

      if (!isNaN(variance)) {
        if (variance === 100) {
          color = '52A76E'; // Green
        } else if (variance < 100) {
          color = 'F32A32'; // Red
        } else if (variance > 100) {
          color = '0F73AE'; // Blue
        }
      }

      statusCell.font = { color: { argb: color }, bold: true };
    });

    // Auto-adjust column widths
    dateSheet.columns.forEach((column) => {
      let maxLength = column.header?.toString().length || 10;
      column.eachCell({ includeEmpty: true }, (cell) => {
        const value = cell.value ? cell.value.toString() : '';
        maxLength = Math.max(maxLength, value.length);
      });
      if (column.key === 'taskName' || column.key === 'client' || column.key === 'taskAssignee') {
        column.width = 50;
        column.alignment = { wrapText: true, horizontal: 'center', vertical: 'middle' };
      } else {
        column.width = maxLength + 2;
      }
    });

    // Write the workbook to a buffer
    const buffer = await workbook.xlsx.writeBuffer();
    return buffer;
  }
}
