import {
  Body,
  Controller,
  Get,
  Param,
  ParseIntPipe,
  Post,
  Put,
  Query,
  Req,
  UseGuards,
} from '@nestjs/common';
import { JwtAuthGuard } from 'src/modules/users/jwt/jwt-auth.guard';
import { CreateReceiptDto, NextReceiptNumberDto } from '../dto/create-receipt.dto';
import { GetCreditBalanceDto } from '../dto/get-credit-balance.dto';
import { GetReceiptsDto } from '../dto/get-receipts.dto';
import { ReceiptsService } from '../services/receipts.service';

@Controller('receipts')
export class ReceiptsController {
  constructor(private service: ReceiptsService) { }

  @UseGuards(JwtAuthGuard)
  @Post()
  createReceipt(@Req() req: any, @Body() body: CreateReceiptDto) {
    const { userId } = req.user;
    return this.service.createReceipt(userId, body);
  }

  @UseGuards(JwtAuthGuard)
  @Put('/:id')
  updateReceipt(@Param('id', ParseIntPipe) id: number, @Body() body: any, @Req() req: any) {
    const { userId } = req.user;
    return this.service.updateReceipt(id, body, userId);
  };

  @UseGuards(JwtAuthGuard)
  @Get()
  getAll(@Req() req: any, @Query() query: GetReceiptsDto) {
    const { userId } = req.user;
    return this.service.getAll(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Get('/client-panel/:clientId')
  getClientPortalAll(@Param('clientId', ParseIntPipe) id: number, @Req() req: any, @Query() query: any) {
    return this.service.getClientPortalAll(id, query);
  }

  @UseGuards(JwtAuthGuard)
  @Post('/export')
  async exportReceipts(@Req() req: any, @Body() body: GetReceiptsDto) {
    const { userId } = req.user;
    return this.service.exportReceipts(userId, body);
  }

  @Get('credit-balance')
  getCreditBalance(@Query() query: GetCreditBalanceDto) {
    return this.service.getCreditBalance(query);
  }

  @UseGuards(JwtAuthGuard)
  @Get('/generate/next-receipt-number')
  async getNextEstimateNumber(@Req() req: any, @Query() query: NextReceiptNumberDto) {
    const { userId } = req.user;
    return this.service.getNextReceiptNumber(userId, query);
  }



  // @UseGuards(JwtAuthGuard)
  @Get('/:id/preview')
  getInvoice(@Param('id', ParseIntPipe) id: number, @Query() query: any) {
    // const { userId } = req.user;
    return this.service.getReceiptPreview(id, query);
  }

  @Post('/:id/download')
  async downloadEstimate(@Param('id', ParseIntPipe) id: number) {
    return this.service.downloadReceipt(id);
  };

  @UseGuards(JwtAuthGuard)
  @Post('/:id/cancel')
  async cancelReceipt(@Param('id', ParseIntPipe) id: number, @Req() req: any) {
    const { userId } = req.user;
    return this.service.cancelReceipt(id, userId);
  }

}
