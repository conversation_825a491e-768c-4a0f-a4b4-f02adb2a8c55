import { Modu<PERSON>, forwardRef } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { StorageController } from './storage.controller';
import Storage from './storage.entity';
import { StorageService } from './storage.service';
import { AwsService } from './upload.service';
import { AttachmentsService } from '../tasks/services/attachments.service';
import { OneDriveStorageService } from '../ondrive-storage/onedrive-storage.service';
import { BharathCloudService } from './bharath-upload.service';
import { BharathStorageService } from './bharath-storage.service';
import CloudCredentials from './cloud-credentials.entity';
import { GoogleDriveStorageService } from '../ondrive-storage/googledrive-storage.service';

@Module({
  imports: [TypeOrmModule.forFeature([Storage, CloudCredentials]),
  ],
  controllers: [StorageController],
  providers: [
    AttachmentsService,
    AwsService,
    BharathStorageService,
    BharathCloudService,
    StorageService,
    OneDriveStorageService,
    GoogleDriveStorageService
  ],
})
export class StorageModule { }
