import { Optional } from '@nestjs/common';
import Client from 'src/modules/clients/entity/client.entity';
import { Organization } from 'src/modules/organization/entities/organization.entity';
import {
  BaseEntity,
  Column,
  CreateDateColumn,
  Entity,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import AutFyaNotice from './aut_income_tax_eproceedings_fya_notice.entity';
import Storage from 'src/modules/storage/storage.entity';

export enum StorageSystem {
  AMAZON = 'AMAZON',
  MICROSOFT = 'MICROSOFT',
  GOOGLE = 'GOOGLE',
  BHARATHCLOUD = 'BHARATHCLOUD',
}

export enum CreatedType {
  MANUAL = 'MANUAL',
  PORTAL = 'PORTAL',
}

@Entity()
class AutProceedingResponseFya extends BaseEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @ManyToOne(() => AutFyaNotice, (autFyaNotice) => autFyaNotice.responses)
  fyaNotice: AutFyaNotice;

  @Column()
  responseType: string;

  @Column('json')
  attachments: object;

  @Column()
  remarks: string;

  @Column()
  submittedOn: string;

  @Column()
  organizationId: number;

  @Column()
  clientId: number;

  @Column()
  noticeId: number;

  @Column({ type: 'enum', enum: StorageSystem, default: StorageSystem.AMAZON })
  storageSystem: StorageSystem;

  @Column({ type: 'enum', enum: CreatedType, default: null })
  createdType: CreatedType;

  @OneToMany(() => Storage, (storage) => storage.autProceedingResponseFya, {
    cascade: true,
  })
  storage: Storage[];
}

export default AutProceedingResponseFya;
