import { <PERSON><PERSON>, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

export type EsignTransactionDocument = EsignTransaction & Document;

@Schema({ timestamps: true })
export class EsignTransaction extends Document {
  @Prop({ required: true })
  referenceNumber: number;

  @Prop({ required: true })
  uniqueSessionKey: string;

  @Prop({ required: true })
  formId: string;

  @Prop({ required: true })
  fieldId: string;

  @Prop({ required: false })
  signedData: string;

  @Prop({ required: false })
  signatureName: string;
}

export const EsignTransactionSchema = SchemaFactory.createForClass(EsignTransaction);
