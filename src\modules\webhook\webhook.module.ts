import { Module } from '@nestjs/common';

import { WebhookService } from './webhook.service';
import { WebhookController } from './webhook.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import ViderWebhook from './entity/vider-webhook.entity';
import { ViderWebhookSubscriber } from 'src/event-subscribers/viderWebhook.subscriber';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      ViderWebhook
    ])
  ],
  controllers: [WebhookController],
  providers: [WebhookService, ViderWebhookSubscriber],
})
export class WebhookModule { }