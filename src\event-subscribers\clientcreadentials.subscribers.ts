import {
  Connection,
  EntitySubscriberInterface,
  EventSubscriber,
  InsertEvent,
  getManager,
  UpdateEvent,
  RemoveEvent,
} from 'typeorm';
import Password from 'src/modules/clients/entity/password.entity';
import {
  getAdminIdsWithClientId,
  getAdminEmailssWithClientId,
  getOrgNameBasedonClientId,
  insertINTONotificationUpdate,
  getUserDetails,
  getAllOrganizationUsersBasedOnClientId,
} from 'src/utils/re-use';
import { sendnewMail } from 'src/emails/newemails';
import ViderWhatsappSessions from 'src/modules/whatsapp/entity/viderWhatsappSessions';
import { sendWhatsAppTextMessage } from 'src/modules/whatsapp/whatsapp.service';
import { Organization } from 'src/modules/organization/entities/organization.entity';
import { User } from 'src/modules/users/entities/user.entity';

let clientOldCreadentials: object = {};
@EventSubscriber()
export class PasswordSubscriber implements EntitySubscriberInterface<Password> {
  constructor(private readonly connection: Connection) {
    connection.subscribers.push(this);
  }

  listenTo() {
    return Password;
  }

  async afterRemove(event: RemoveEvent<Password>) { }

  async beforeRemove(event: RemoveEvent<Password>) {
    const entityManager = getManager();
    const Id = event?.databaseEntity?.id;
    const websiteName = event?.databaseEntity?.website;
    const getPasswordQuery = `SELECT client_id FROM password WHERE id=${Id}`;
    const getPassword = await entityManager.query(getPasswordQuery);
    const clientId = getPassword[0]?.client_id;
    const clientDetails = `SELECT display_name, organization_id FROM client WHERE id=${clientId}`;
    const clientNameQuery = await entityManager.query(clientDetails);
    const clientName = clientNameQuery[0]?.display_name;
    const orgId = clientNameQuery[0]?.organization_id;
    const logInUserId = event?.entity?.['userId'];
    let user = await User.findOne({
      where: {
        id: logInUserId,
      },
    });
    let organizationsmtp = await Organization.findOne({ where: { id: user.organization.id } });
    const othersSmtpMail = organizationsmtp?.othersSmtp?.[1].auth?.user;
    const sqlQuery = `SELECT full_name FROM user where id = ${logInUserId};`;
    const response = await entityManager.query(sqlQuery);
    const UserName = response[0]?.full_name;

    if (clientId) {

      const adminList = await getAdminIdsWithClientId(clientId);
      const title = 'Credentials Have Been Deleted';
      const body = `"<strong>${websiteName}</strong>" credentials of <strong>${clientName}</strong> have been deleted by ${UserName}`;
      const key = 'CREDENTIALS_HAVE_BEEN_DELETED_PUSH';
      // insertINTOnotification(title, body, adminList, orgId);
      insertINTONotificationUpdate(title, body, adminList, orgId, key);
      const organization = await Organization.findOne({ id: orgId });
      const addressParts = [
        organization.buildingNo || '',
        organization.floorNumber || '',
        organization.buildingName || '',
        organization.street || '',
        organization.location || '',
        organization.city || '',
        organization.district || '',
        organization.state || ''
      ].filter(part => part && part.trim() !== '');
      const pincode = organization.pincode && organization.pincode.trim() !== '' ? ` - ${organization.pincode}` : '';

      const address = addressParts.join(', ') + pincode;
      if (adminList) {
        for (let user of adminList) {
          const userDetails = await getUserDetails(user);
          await sendnewMail({
            id: userDetails?.id,
            key: 'CREDENTIALS_DELETED_MAIL',
            email: userDetails?.email,
            data: {
              userName: userDetails?.full_name,
              clientName: clientName,
              othersSmtpMail: othersSmtpMail || '<EMAIL>',
              deletedCredentialsName: websiteName,
              userId: event.entity['userId'],
              adress: address,
              phoneNumber: organization?.mobileNumber,
              mail: organization?.email,
              legalName: organization?.tradeName || organization?.legalName

            },
            filePath: 'credentials-deleted',
            subject: `Credentials Deleted: ${websiteName} with ${clientName}`,
          });
          //  whatsapp
          const title = 'Client Credential Deleted';

          try {
            if (adminList !== undefined) {
              const sessionValidation = await ViderWhatsappSessions.findOne({
                where: { userId: userDetails?.id, status: 'ACTIVE' },
              });
              if (sessionValidation) {
                const { full_name: userFullName, mobile_number: userPhoneNumber, id, organization_id } = userDetails;
                const key = 'CREDENTIALS_DELETED_WHATSAPP';
                const whatsappMessageBody = `
Hi ${userFullName}

${websiteName} of ${clientName} have been deleted by ${userDetails?.full_name}

We hope this helps!
    `;
                await sendWhatsAppTextMessage(
                  `91${userPhoneNumber}`,
                  whatsappMessageBody,
                  organization_id,
                  title,
                  id,
                  key,
                );
              }

            }
          } catch (error) {
            console.error('Error sending User WhatsApp notification:', error);
          }
        }
      }
    }
  }

  async beforeInsert(event: InsertEvent<Password>) { }

  async beforeUpdate(event: UpdateEvent<Password>) {
    clientOldCreadentials = event?.databaseEntity;
  }

  async afterInsert(event: InsertEvent<Password>) {
    const entityManager = getManager();

    const { client, website } = event?.entity;
    if (client) {


      const { id, displayName, id: clientId } = client;
      const userList = await getAdminIdsWithClientId(id);
      const allOrgUsers = await getAllOrganizationUsersBasedOnClientId(id);
      const logInUserId = event?.entity['userId'];
      let user = await User.findOne({
        where: {
          id: logInUserId,
        },
      });
      let organizationsmtp = await Organization.findOne({ where: { id: user.organization.id } });
      const othersSmtpMail = organizationsmtp?.othersSmtp?.[1].auth?.user;
      if (logInUserId) {
        const userSql = `SELECT full_name, organization_id FROM user WHERE id=${logInUserId}`;
        const userQuery = await entityManager.query(userSql);
        const [{ full_name: userName, organization_id: orgId }] = userQuery;
        const title = 'Client Credentials added';
        const body = `New "<strong>${website}</strong>" credentials of "<strong>${displayName}</strong>" has been added by <strong>${userName}</strong> .`;
        // insertINTOnotification(title, body, userList, orgId)
        const key = 'CLIENT_CREDENTIALS_ADDED_PUSH';
        insertINTONotificationUpdate(title, body, userList, orgId, key, clientId);
        const organization = await Organization.findOne({ id: orgId });
        const addressParts = [
          organization.buildingNo || '',
          organization.floorNumber || '',
          organization.buildingName || '',
          organization.street || '',
          organization.location || '',
          organization.city || '',
          organization.district || '',
          organization.state || ''
        ].filter(part => part && part.trim() !== '');
        const pincode = organization.pincode && organization.pincode.trim() !== '' ? ` - ${organization.pincode}` : '';

        const address = addressParts.join(', ') + pincode;

        if (allOrgUsers) {
          for (let user of allOrgUsers) {
            await sendnewMail({
              id: user?.id,
              key: 'CLIENT_CREDENTIALS_ADDED_MAIL',
              email: user?.email,
              data: {
                adminName: user?.fullName,
                websiteName: website,
                userName: userName,
                createdby: userName,
                othersSmtpMail: othersSmtpMail || '<EMAIL>',
                clientName: displayName,
                userId: event.entity['userId'],
                adress: address,
                phoneNumber: organization?.mobileNumber,
                mail: organization?.email,
                legalName: organization?.tradeName || organization?.legalName,
              },
              filePath: 'client-creadentials-added',
              subject: 'Client Credentials Added',
            });
            //  whatsapp
            const title = 'Client Credential Added';

            try {
              if (userList !== undefined) {
                const sessionValidation = await ViderWhatsappSessions.findOne({
                  where: { userId: user?.id, status: 'ACTIVE' },
                });
                if (sessionValidation) {
                  const userDetails = await getUserDetails(user?.id);
                  const { full_name: userFullName, mobile_number: userPhoneNumber, id, organization_id } = userDetails;
                  const key = 'CLIENT_CREDENTIALS_ADDED_WHATSAPP';
                  const whatsappMessageBody = `
Hi ${userFullName}

${website} of ${client?.displayName} have been added by ${userName}

We hope this helps!
    `;
                  await sendWhatsAppTextMessage(
                    `91${userPhoneNumber}`,
                    whatsappMessageBody,
                    organization_id,
                    title,
                    id,
                    key,
                  );
                }

              }
            } catch (error) {
              console.error('Error sending User WhatsApp notification:', error);
            }
          }
        }
      }
    }
  }

  async afterUpdate(event: UpdateEvent<Password>) {
    const entityManager = getManager();
    const oldwebsit = clientOldCreadentials['website'];
    const oldpassword = clientOldCreadentials['password'];
    const { client, website, password, loginId } = event?.entity;
    if (client) {
      const { id: clientId } = client;
      const { id, displayName } = client;
      const userList = await getAdminIdsWithClientId(id);
      const allOrgUsers = await getAllOrganizationUsersBasedOnClientId(id);
      const adminMails = await getAdminEmailssWithClientId(id);
      const oldLogInId = clientOldCreadentials['loginId'];
      const logInUserId = event?.entity['userId'];
      let user = await User.findOne({
        where: {
          id: logInUserId,
        },
      });
      let organizationsmtp = await Organization.findOne({ where: { id: user.organization.id } });
      const othersSmtpMail = organizationsmtp?.othersSmtp?.[1].auth?.user;
      if (logInUserId) {
        const userSql = `SELECT full_name,organization_id FROM user WHERE id=${logInUserId}`;
        const userQuery = await entityManager.query(userSql);

        const [{ full_name: userName, organization_id: orgId }] = userQuery;
        const title = 'Client credentials updated';
        const body = `"<strong>${website}</strong>" credentials of "<strong>${displayName}</strong>" have been updated by <strong>${userName}</strong>.`;
        // insertINTOnotification(title, body, userList, orgId);
        const key = 'CLIENT_CREDENTIALS_UPDATED_PUSH';
        insertINTONotificationUpdate(title, body, userList, orgId, key, clientId);
        const organization = await Organization.findOne({ id: orgId });
        const addressParts = [
          organization.buildingNo || '',
          organization.floorNumber || '',
          organization.buildingName || '',
          organization.street || '',
          organization.location || '',
          organization.city || '',
          organization.district || '',
          organization.state || ''
        ].filter(part => part && part.trim() !== '');
        const pincode = organization.pincode && organization.pincode.trim() !== '' ? ` - ${organization.pincode}` : '';

        const address = addressParts.join(', ') + pincode;
        // for (let i = 0; i < adminMails.length; i++) {
        //   const data = {
        // adminName: adminMails[i].fullName,
        // clientName: displayName,
        // onboardedBy: userName,
        // websiteName: oldwebsit,
        // oldPassword: oldpassword,
        // newPassword: password,
        // oldWebsit: oldwebsit,
        // oldLogId: oldLogInId,
        // newLogId: loginId,
        //   };
        //   const mailOptions = {
        //     data: data,
        //     email: adminMails[i].email,
        //     filePath: 'client-creadential',
        //     subject: 'Credentials updated',
        //   };
        // await sendMail(mailOptions);
        // }

        if (adminMails) {
          for (let user of adminMails) {
            await sendnewMail({
              id: user?.id,
              key: 'CLIENT_CREDENTIALS_UPDATED_MAIL',
              email: user?.email,
              data: {
                adminName: user?.fullName,
                clientName: displayName,
                onboardedBy: userName,
                websiteName: oldwebsit,
                oldPassword: oldpassword,
                newPassword: password,
                oldWebsit: oldwebsit,
                oldLogId: oldLogInId,
                othersSmtpMail: othersSmtpMail || '<EMAIL>',
                newLogId: loginId,
                userId: event.entity['userId'],
                adress: address,
                phoneNumber: organization?.mobileNumber,
                mail: organization?.email,
                legalName: organization?.tradeName || organization?.legalName,
              },
              filePath: 'client-creadential',
              subject: 'Client Credentials updated',
            });
            //  whatsapp
            const title = 'Client Credential Added';

            try {
              if (userList !== undefined) {
                const sessionValidation = await ViderWhatsappSessions.findOne({
                  where: { userId: user?.id, status: 'ACTIVE' },
                });
                if (sessionValidation) {
                  const userDetails = await getUserDetails(user?.id);
                  const { full_name: userFullName, mobile_number: userPhoneNumber, id, organization_id } = userDetails;
                  const key = 'CLIENT_CREDENTIALS_UPDATED_WHATSAPP';
                  const whatsappMessageBody = `
Hi ${userFullName}

${website} of ${client?.displayName} have been updated by ${userName}

We hope this helps!
    `;
                  await sendWhatsAppTextMessage(
                    `91${userPhoneNumber}`,
                    whatsappMessageBody,
                    organization_id,
                    title,
                    id,
                    key,
                  );
                }

              }
            } catch (error) {
              console.error('Error sending User WhatsApp notification:', error);
            }
          }
        }
      }
    }
  }
}
