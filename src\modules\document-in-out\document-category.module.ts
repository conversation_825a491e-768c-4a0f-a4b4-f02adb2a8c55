import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import DocumentCategory from './entity/document-category.entity';
import { DocumentInOutController } from './document-category.controller';
import { DocumentCategoryService } from './doucment-category.service';

@Module({
  imports: [TypeOrmModule.forFeature([DocumentCategory])],
  controllers: [DocumentInOutController],
  providers: [DocumentCategoryService],

})
export class DocumentCategoryModule { }
