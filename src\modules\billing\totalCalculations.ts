export enum TAX_TYPE {
    NON_TAXABLE = "NON_TAXABLE",
    OUT_OF_SCOPE = "OUT_OF_SCOPE",
    NON_GST_SUPPLY = "NON_GST_SUPPLY",
    GST0 = "GST0",
    GST0_1 = "GST0_1",
    GST0_25 = "GST0_25",
    GST1 = "GST1",
    GST1_5 = "GST1_5",
    GST3 = "GST3",
    GST5 = "GST5",
    GST6 = "GST6",
    GST7_5 = "GST7_5",
    GST12 = "GST12",
    GST18 = "GST18",
    GST28 = "GST28",
}
export enum TAX_TYPE_VALUE {
    NON_TAXABLE = "NON_TAXABLE",
    OUT_OF_SCOPE = "OUT_OF_SCOPE",
    NON_GST_SUPPLY = "NON_GST_SUPPLY",
    GST0 = "0",
    GST0_1 = "0.1",
    GST0_25 = "0.25",
    GST1 = "1",
    GST1_5 = "1.5",
    GST3 = "3",
    GST5 = "5",
    GST6 = "6",
    GST7_5 = "7.5",
    GST12 = "12",
    GST18 = "18",
    GST28 = "28",
}


// export function getTotalGst(particulars: any[]) {
//     const value = particulars?.reduce((acc, particular) => {
//         return acc + getGstAmount(particular, particular.gst);
//     }, 0);
//     return value;

// }

export function getTotalGst(particulars: any[]): number {
    const value = particulars?.reduce((acc, particular) => {
        return acc + getGstAmount(particular, particular.gst);
    }, 0);

    // Fix precision errors by rounding to 2 decimal places
    return Math.round(value * 100) / 100;
}


export const formattedAmount: any = (amount) =>
    parseFloat(amount)
        .toLocaleString('en-IN', {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2
        });

export function getTotalGstReport(particulars: any[]): number {
    const value = particulars?.reduce((acc, particular) => {
        return acc + getGstAmount(particular, particular.gst);
    }, 0);

    return Math.round(value * 100) / 100;
}

export function getGstAmount(particular: any, value: string) {
    const taxableAmount = getAmount(particular);

    if (!value) return 0;

    let gstPercent = 0;

    switch (value) {
        case TAX_TYPE.NON_GST_SUPPLY:
            gstPercent = 0;
            break;
        case TAX_TYPE.NON_TAXABLE:
            gstPercent = 0;
            break;
        case TAX_TYPE.OUT_OF_SCOPE:
            gstPercent = 0;
            break;
        case TAX_TYPE.GST0:
            gstPercent = 0;
            break;
        case TAX_TYPE.GST0_1:
            gstPercent = 0.1;
            break;
        case TAX_TYPE.GST0_25:
            gstPercent = 0.25;
            break;
        case TAX_TYPE.GST1:
            gstPercent = 1;
            break;
        case TAX_TYPE.GST1_5:
            gstPercent = 1.5;
            break;
        case TAX_TYPE.GST3:
            gstPercent = 3;
            break;
        case TAX_TYPE.GST5:
            gstPercent = 5;
            break;
        case TAX_TYPE.GST6:
            gstPercent = 6;
            break;
        case TAX_TYPE.GST7_5:
            gstPercent = 7.5;
            break;
        case TAX_TYPE.GST12:
            gstPercent = 12;
            break;
        case TAX_TYPE.GST18:
            gstPercent = 18;
            break;
        case TAX_TYPE.GST28:
            gstPercent = 28;
            break;
        default:
            gstPercent = 0;
    }
    return (taxableAmount * gstPercent) / 100;
};



export function getAmount(particular: any) {
    const { discount, discountType, amount } = particular;

    if (!amount) return 0;

    let result = 1 * amount;
    // if (discount && discountType === "PERCENT") {
    //     result = result - (result * discount) / 100;
    // }

    // if (discount && discountType === "AMOUNT") {
    //     result = result - discount;
    // }
    result = Number(result.toFixed(2))

    return result;
}