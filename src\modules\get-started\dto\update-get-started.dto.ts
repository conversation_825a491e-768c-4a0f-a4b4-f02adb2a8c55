import { IsBoolean, IsEnum, IsOptional } from 'class-validator';
import { GetStartedStatus } from '../types';

export class UpdateGetStarted {
  @IsOptional()
  @IsBoolean()
  importClients: boolean;

  @IsOptional()
  @IsBoolean()
  importUsers: boolean;

  @IsOptional()
  @IsBoolean()
  importTasks: boolean;

  @IsOptional()
  @IsBoolean()
  importDsc: boolean;

  @IsOptional()
  @IsBoolean()
  selectServices: boolean;

  @IsOptional()
  @IsBoolean()
  createUser: boolean;

  @IsOptional()
  @IsEnum(GetStartedStatus)
  status: GetStartedStatus;
}
