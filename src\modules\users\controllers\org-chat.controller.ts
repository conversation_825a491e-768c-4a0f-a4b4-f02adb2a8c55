import { Body, Controller, Get, Param, ParseIntPipe, Post, Put, Query, Request, UseGuards } from "@nestjs/common";
import { OrgChartService } from "../services/org-chart.service";
import { JwtAuthGuard } from "../jwt/jwt-auth.guard";

@Controller('org-chat')
export class OrgChartController {
  constructor(private service: OrgChartService) { }

  @UseGuards(JwtAuthGuard)
  @Get()
  async get(@Request() req: any) {
    const { userId } = req.user;
    return this.service.get(userId);
  };

  @UseGuards(JwtAuthGuard)
  @Get('/count')
  async getCount(@Request() req: any) {
    const { userId } = req.user;
    return this.service.getCount(userId);
  }

  @UseGuards(JwtAuthGuard)
  @Get('/attendence')
  async getAttendence(@Request() req: any, @Query() query: any) {
    const { userId } = req.user;
    return this.service.getAttendence(userId, query)
  };

  @UseGuards(JwtAuthGuard)
  @Post('/attendance-export')
  async exportApprovalLeave(@Request() req: any, @Body() body: any) {
    const query = body;
    const { userId } = req.user;
    return this.service.exportApprovalLeave(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Get('/expenditure')
  async getExpendature(@Request() req: any, @Query() query: any) {
    const { userId } = req.user;
    return this.service.getExpenditure(userId, query)
  };
  @UseGuards(JwtAuthGuard)
  @Get('/log-hours')
  async getloghour(@Request() req: any, @Query() query: any) {
    const { userId } = req.user;
    return this.service.getloghour(userId, query)
  };

  @UseGuards(JwtAuthGuard)
  @Get('/manager/:id')
  async getManager(@Param('id', ParseIntPipe) id: number) {
    return this.service.getManager(id)
  };

  @UseGuards(JwtAuthGuard)
  @Put('/update-manager')
  async updateManager(@Body() body) {
    return this.service.updateManager(body)
  }

  @UseGuards(JwtAuthGuard)
  @Post('/log-hours-export')
  async exportApprovalLoghours(@Request() req: any, @Body() body: any) {
    const query = body;
    const { userId } = req.user;
    return this.service.exportApprovalLoghours(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Post('/expenditure-export')
  async exportApprovalExpenditure(@Request() req: any, @Body() body: any) {
    const query = body;
    const { userId } = req.user;
    return this.service.exportApprovalExpenditure(userId, query);
  }
}