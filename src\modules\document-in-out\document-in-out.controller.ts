import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  ParseIntPipe,
  Post,
  Put,
  Query,
  Req,
  Request,
  UploadedFiles,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { JwtAuthGuard } from 'src/modules/users/jwt/jwt-auth.guard';
import { DocumentInOutService } from './doucment-in-out.service';
import { FilesInterceptor } from '@nestjs/platform-express';


@Controller('document-in-out')
export class DocumentInOutController {
  constructor(
    private service: DocumentInOutService,

  ) { }

  @UseGuards(JwtAuthGuard)
  @Post()
  async create(@Body() body: any, @Request() req: any) {
    const { userId } = req.user;
    return this.service.create(userId, body);
  }

  @UseGuards(JwtAuthGuard)
  @Post('/add-document-data')
  async createDocumentItem(@Body() body: any, @Request() req: any) {
    const { userId } = req.user;
    return this.service.createDocumentItem(userId, body);
  }

  @UseGuards(JwtAuthGuard)
  @Post('/delete-document-data')
  async deleteDocumentItem(@Body() body: any, @Request() req: any) {
    const { userId } = req.user;
    return this.service.deleteDocumentItem(userId, body);
  }

  @UseGuards(JwtAuthGuard)
  @Get()
  async get(@Request() req: any, @Query() query: any) {
    const { userId } = req.user;
    return this.service.get(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Get('/:id')
  findOne(@Req() req: any, @Param('id', ParseIntPipe) id: number) {
    const { userId } = req.user;
    return this.service.findOne(id, userId);
  }

  @UseGuards(JwtAuthGuard)
  @Put('/:id')
  async update(@Request() req: any, @Param('id', ParseIntPipe) id: number, @Body() body: any) {
    const { userId } = req.user;
    return this.service.update(id, body, userId);
  }

  @UseGuards(JwtAuthGuard)
  @Delete('/:id')
  async delete(@Param('id', ParseIntPipe) id: number, @Request() req: any, @Query() query) {
    const { userId } = req.user;
    return this.service.delete(id, userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Post('/:taskId/:docId/attachments')
  @UseInterceptors(FilesInterceptor('files'))
  addAttachments(

    @UploadedFiles() files: Express.Multer.File[],
    @Param('taskId', ParseIntPipe) taskId: number,
    @Param('docId', ParseIntPipe) docId: number,
    @Request() req: any,
  ) {
    const { userId } = req.user;
    return this.service.saveAttachments(taskId, docId, files, userId);
  }
}
