import {
  Injectable,
} from '@nestjs/common';
import * as moment from 'moment';
import Activity, { ActivityType } from 'src/modules/activity/activity.entity';
import Category from 'src/modules/categories/categories.entity';
import Client from 'src/modules/clients/entity/client.entity';
import Label from 'src/modules/labels/label.entity';
import { Service } from 'src/modules/services/entities/service.entity';
import { User } from 'src/modules/users/entities/user.entity';
import { Brackets, createQueryBuilder } from 'typeorm';
import CreateTaskDto from 'src/modules/tasks/dto/create-task.dto';
import FindTasksQuery from 'src/modules/tasks/dto/find-query.dto';
import {
  TaskRecurringStatus,
  TaskStatusEnum,
} from 'src/modules/tasks/dto/types';
import Task from 'src/modules/tasks/entity/task.entity';
import { Permissions } from 'src/modules/tasks/permission';
import ApprovalProcedures from 'src/modules/atm-qtm-approval/entities/approval-procedures.entity';
import axios from 'axios';
import FindExpenditureDto, { FindExpenditureQueryType } from 'src/modules/expenditure/dto/find-expenditure.dto';
import Expenditure from 'src/modules/expenditure/expenditure.entity';

export enum TimerStatus {
  STARTED = 'started',
  STOPPED = 'stopped',
}

@Injectable()
export class TasksService {

  async findOne(id: number, userId: number, query: any) {
    let user = await User.findOne({
      where: { id: userId },
      relations: ['organization', 'role', 'role.permissions'],
    });

    let allTasks = user?.role?.permissions?.find(
      (permission) => permission.slug === Permissions.VIEW_ALL_TASKS,
    );

    let assignedTasks = user?.role?.permissions?.find(
      (permission) => permission.slug === Permissions.VIEW_ASSIGNED_TASKS,
    );

    let all = allTasks ? true : false;
    let assigned = !all && assignedTasks ? true : false;

    let taskQuery = await createQueryBuilder(Task, 'task')
      .leftJoinAndSelect('task.parentTask', 'parentTask')
      .leftJoinAndSelect('task.client', 'client')
      .leftJoinAndSelect('task.subTasks', 'subTasks')
      .leftJoinAndSelect('subTasks.members', 'subTaskMembers')
      .leftJoinAndSelect('task.taskLogHours', 'taskLogHours')
      .leftJoinAndSelect('task.user', 'user')
      .leftJoinAndSelect('task.members', 'members')
      .leftJoinAndSelect('task.taskLeader', 'taskLeader')
      .leftJoinAndSelect('task.category', 'category')
      .leftJoinAndSelect('task.subCategory', 'subCategory')
      .leftJoinAndSelect('task.labels', 'labels')
      .leftJoinAndSelect('task.organization', 'organization')
      .leftJoinAndSelect('task.taskBudgetedHours', 'taskBudgetedHours')
      .leftJoinAndSelect('taskBudgetedHours.user', 'budgetedHoursUser')
      .leftJoinAndSelect('task.approvalProcedures', 'approvalProcedures')
      .leftJoinAndSelect('approvalProcedures.approval', 'approval')
      .leftJoinAndSelect('approval.approvalLevels', 'approvalLevels')
      .leftJoinAndSelect('parentTask.members', 'parentTaskMembers')
      .leftJoinAndSelect('task.service', 'service')
      .leftJoinAndSelect('task.expenditure', 'expenditure')
      .leftJoinAndSelect('approvalLevels.user', 'approvalLevelsUsers')
      .leftJoinAndSelect('task.udinTask', 'udinTask')
      .leftJoinAndMapMany(
        'task.activity',
        Activity,
        'activity',
        `activity.type = '${ActivityType.TASK}' 
        AND activity.typeId = task.id 
        AND activity.action IN ('Remark Added')`
      )
      .leftJoinAndMapOne(
        'activity.user',
        User,
        'activityUser',
        'activityUser.id = activity.actorId',
      )
      .where('task.id = :id', { id })
      .andWhere('organization.id =:orgId', { orgId: user?.organization?.id });

    if (assigned && user.role.name !== 'Admin') {
      // taskQuery.andWhere(
      //   new Brackets((qb) => {
      //     qb.where('approvalLevelsUsers.id = :userId', { userId })
      //       .orWhere('members.id = :userId', { userId })
      //       .orWhere('taskLeader.id = :userId', { userId });
      //   }),
      // );
      taskQuery.andWhere(
        new Brackets((qb) => {
          qb.where('approvalLevelsUsers.id = :userId AND task.status = :statusApproval', {
            userId,
            statusApproval: TaskStatusEnum.UNDER_REVIEW,
          })
            .orWhere(qb => {
              const subQuery = qb.subQuery()
                .select('task_id')
                .from('task_members_user', 'taskMembers')
                .where('taskMembers.user_id= :userId')
                .getQuery();
              qb.where(`task.id IN ${subQuery}`);
            })
            .orWhere('taskLeader.id = :userId', { userId });
        }),
      );

    }

    if (query?.clientId) {
      taskQuery.andWhere('client.id =:clientId', { clientId: query?.clientId });
    }

    let task = await taskQuery.getOne();

    if (task) {
      if (task && task.subTasks && !task.parentTask) {
        task.subTasks = task.subTasks.filter(
          (subTask) => subTask.status !== 'deleted' && subTask.status !== 'terminated',
        );

        task.subTasks.forEach((subTask) => {
          subTask.members = subTask.members.filter((subtaskMember) => {
            return task.members.some((taskMember) => taskMember.id === subtaskMember.id);
          });
        });

        if (task.service) {
          if (task.service.subtaskServices) {
            const subtaskNames = task.subTasks.map((subTask) => subTask.name);
            task.service.subtaskServices = task.service.subtaskServices.filter((subtaskService) => {
              return !subtaskNames.includes(subtaskService.name);
            });
          }
        }
      }
      // if (task.parentTask) {
      //   task.members = task.members.filter((subtaskMember) => {
      //     return task.parentTask.members.some(
      //       (parentTaskMember) => parentTaskMember.id === subtaskMember.id,
      //     );
      //   });
      // }

      task['budgetedHoursData'] = Math.floor(moment.duration(task.budgetedhours).asHours());
      const remainingDuration = moment
        .duration(task.budgetedhours)
        .subtract(Math.floor(moment.duration(task.budgetedhours).asHours()), 'hours');
      const remainingMinutes = Math.floor(remainingDuration.asMinutes());
      task['budgetedMinutesData'] = remainingMinutes;
      for (let i of task.taskBudgetedHours) {
        i['budgetedHoursData'] = Math.floor(moment.duration(i.budgetedHours).asHours());
        const remainingDuration = moment
          .duration(i.budgetedHours)
          .subtract(Math.floor(moment.duration(i.budgetedHours).asHours()), 'hours');
        const remainingMinutes = Math.floor(remainingDuration.asMinutes());
        i['budgetedMinutesData'] = remainingMinutes;
      }
    }
    return task;
  }

  async approvalProcess(id: string, approvalData: ApprovalProcedures) {
    try {
      const url = `${process.env.CAMUNDA_URL}/vider/quantum/api/process/${id}`;
      let data: any = {
        method: 'get',
        maxBodyLength: Infinity,
        headers: {
          'Content-Type': 'application/json',
        },
      };

      const response = await axios.get(url, data);
      const { approval } = approvalData;
      const { approvalLevels } = approval;
      let assignApprovalTasks: any[] = [];
      assignApprovalTasks = response?.data?.tasks
        .filter((item: any) => item.name !== 'holdTillDocumentIsSubmitted')
        .map((item: any) => {
          let levelNumber = parseInt(item.name.slice(-1));
          const foundUser = approvalLevels.find((level) => level.level === levelNumber);
          const { user } = foundUser;
          const { id } = user;
          return `${process.env.CAMUNDA_URL}/vider/quantum/api/task/${item.id}/assign/${id}`;
        });

      const makeApiCall = async (url) => {
        try {
          let config: any = {
            method: 'put',
            maxBodyLength: Infinity,
            headers: {
              'Content-Type': 'application/json',
            },
          };

          const response = await axios.put(url, config);
          return response.data;
        } catch (error) {
          console.error('Error for', url, ':', error);
          throw error;
        }
      };

      const apiPromises = assignApprovalTasks.map((endpoint) => makeApiCall(endpoint));

      Promise.all(apiPromises)
        .then((apiResponses) => { })
        .catch((error) => {
          console.error('One or more API calls failed:', error);
        });
    } catch (err) {
      console.log(err);
    }
  }

  async getTerminatedTasks(clientId: number, query: FindTasksQuery) {
    const { limit, offset, search } = query;
    const client = await Client.findOne({ where: { id: clientId }, relations: ['organization'] });

    let tasks = await createQueryBuilder(Task, 'task')
      .leftJoinAndSelect('task.client', 'client')
      .leftJoinAndSelect('task.organization', 'organization')
      .leftJoinAndSelect('task.members', 'members')
      .leftJoinAndSelect('members.imageStorage', 'imageStorage')
      .leftJoinAndSelect('task.taskLeader', 'taskLeader')
      .leftJoinAndSelect('taskLeader.imageStorage', 'leaderImageStorage')
      .where('task.client.id = :client', { client: client.id })
      .andWhere('task.organization.id = :orgID', { orgID: client.organization.id })
      .andWhere('task.status = :status', { status: TaskStatusEnum.TERMINATED });
    if (query.allTasks) {
      query.allTasks === 'main_task'
        ? tasks.andWhere('task.parentTask is null')
        : query.allTasks === 'sub_task'
          ? tasks.andWhere('task.parentTask is not null')
          : '';
    }

    if (search) {
      tasks.andWhere('task.taskNumber LIKE :search OR task.name LIKE :search', {
        search: `%${search}%`,
      });
    }

    if (offset >= 0) {
      tasks.skip(offset);
    }

    if (limit) {
      tasks.take(limit);
    }

    let result = await tasks.getManyAndCount();

    return {
      count: result[1],
      result: result[0],
    };
  }

  async getCompletedTasks(clientId: number, query: FindTasksQuery) {
    const { limit, offset, search } = query;

    const client = await Client.findOne({ where: { id: clientId }, relations: ['organization'] });
    let tasks = await createQueryBuilder(Task, 'task')
      .leftJoinAndSelect('task.client', 'client')
      .leftJoinAndSelect('task.organization', 'organization')
      .leftJoinAndSelect('task.members', 'members')
      .leftJoinAndSelect('task.taskLeader', 'taskLeader')
      .leftJoinAndSelect('members.imageStorage', 'imageStorage')
      .leftJoinAndSelect('taskLeader.imageStorage', 'leaderImageStorage')
      .where('task.client.id = :client', { client: client.id })
      .andWhere('task.organization.id = :orgID', { orgID: client.organization.id })
      .andWhere('task.status = :status', { status: TaskStatusEnum.COMPLETED });
    if (query.allTasks) {
      query.allTasks === 'main_task'
        ? tasks.andWhere('task.parentTask is null')
        : query.allTasks === 'sub_task'
          ? tasks.andWhere('task.parentTask is not null')
          : '';
    }

    if (search) {
      tasks.andWhere('task.taskNumber LIKE :search OR task.name LIKE :search', {
        search: `%${search}%`,
      });
    }

    if (offset >= 0) {
      tasks.skip(offset);
    }

    if (limit) {
      tasks.take(limit);
    }

    let result = await tasks.getManyAndCount();
    return {
      count: result[1],
      result: result[0],
    };
  }

  async getDeletedTasks(userId: number, query: FindTasksQuery) {
    const { limit, offset, search } = query;

    const user = await User.findOne({ where: { id: userId }, relations: ['organization'] });

    let tasks = createQueryBuilder(Task, 'task')
      .leftJoinAndSelect('task.organization', 'organization')
      .leftJoinAndSelect('task.client', 'client')
      .leftJoinAndSelect('task.members', 'members')
      .leftJoinAndSelect('members.imageStorage', 'imageStorage')
      .leftJoinAndSelect('task.taskLeader', 'taskLeader')
      .leftJoinAndSelect('taskLeader.imageStorage', 'leaderImageStorage')
      .where('task.status = :status', { status: TaskStatusEnum.DELETED })
      .andWhere('(task.recurringStatus is null or task.recurringStatus = :recurringStatus)', {
        recurringStatus: TaskRecurringStatus.CREATED,
      })
      .andWhere('organization.id = :organizationId', { organizationId: user.organization.id });

    if (query.allTasks) {
      query.allTasks === 'main_task'
        ? tasks.andWhere('task.parentTask is null')
        : query.allTasks === 'sub_task'
          ? tasks.andWhere('task.parentTask is not null')
          : '';
    }

    if (search) {
      tasks.andWhere('task.taskNumber LIKE :search OR task.name LIKE :search', {
        search: `%${search}%`,
      });
    }

    if (query?.client) {
      tasks.andWhere('task.client.id = :clientId', { clientId: query.client });
    }
    if (offset >= 0) {
      tasks.skip(offset);
    }

    if (limit) {
      tasks.take(limit);
    }

    let result = await tasks.getManyAndCount();

    return {
      count: result[1],
      result: result[0],
    };
  }

  async findExpenditure(userId: number, query: FindExpenditureDto) {
    const { limit, offset } = query;

    let repo = createQueryBuilder(Expenditure, 'expenditure')
      .leftJoinAndSelect('expenditure.task', 'task')
      .leftJoinAndSelect('task.category', 'category')

      .leftJoinAndSelect('expenditure.client', 'client')
      .leftJoinAndSelect('expenditure.user', 'user')
      .leftJoinAndSelect('user.imageStorage', 'imageStorage')

      .leftJoinAndSelect('expenditure.storage', 'storage')
      .orderBy('expenditure.createdAt', 'DESC');

    if (query.type === FindExpenditureQueryType.TASK) {
      repo.where('task.id = :taskId', { taskId: query.taskId });
    }

    if (query.type === FindExpenditureQueryType.USER) {
      repo.where('user.id = :userId', { userId });
    }

    if (query.type === FindExpenditureQueryType.SELF) {
      repo.where('user.id = :userId', { userId });
    }

    if (query.search) {
      repo.andWhere('(expenditure.particularName LIKE :search OR task.name LIKE :search)', {
        search: `%${query.search}%`,
      });
    }
    if (offset >= 0) {
      repo.skip(offset);
    }

    if (limit) {
      repo.take(limit);
    }

    let result = await repo.getManyAndCount();

    return {
      count: result[1],
      result: result[0],
    };
  }
}
