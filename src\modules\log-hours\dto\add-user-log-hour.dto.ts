import {
  IsDateString,
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  ValidateIf,
} from 'class-validator';
import { LogHourType } from '../log-hour.entity';
import { UserLogHoursType } from './get-user-log-hours.dto';

export class AddUserLogHourDto {
  @ValidateIf((o) => o.type === UserLogHoursType.USER)
  @IsNotEmpty()
  userId: number;

  @IsNotEmpty()
  @IsEnum(LogHourType)
  logHourType: LogHourType;

  @ValidateIf((v: AddUserLogHourDto) => v.logHourType === LogHourType.GENERAL)
  @IsNotEmpty()
  title: string;

  @ValidateIf((v: AddUserLogHourDto) => v.logHourType === LogHourType.TASK)
  @IsNumber()
  task: number;

  @IsOptional()
  @IsNumber()
  client: number;

  @IsOptional()
  @IsNumber()
  clientGroup: number;

  @IsNotEmpty()
  @IsNumber()
  duration: number;

  @IsNotEmpty()
  @IsDateString()
  completedDate: string;

  @IsOptional()
  description: string;
}
