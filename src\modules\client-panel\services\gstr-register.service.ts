import { Injectable } from "@nestjs/common";
import { User, UserStatus } from "../../users/entities/user.entity";
import Client from "../../clients/entity/client.entity";
import FindQueryDto from "src/modules/gstr-register/dto/find-query.dto";
import { createQ<PERSON>y<PERSON><PERSON>er, getConnection, getManager } from "typeorm";
import axios from "axios";
import { getCurrentFinancialYear, incrementFinancialYear } from "src/utils/datesFormation";
import CreateGstrDto from "src/modules/gstr-register/dto/create-gstrs.dto";
import FindComplianceDto from "src/modules/gstr-register/dto/find-compliance.dto";
import GstrRegister from "src/modules/gstr-register/entity/gstr-register.entity";
import { ReturnsData } from "src/modules/gstr-register/entity/returns-data.entity";



@Injectable()
export class GstrRegisterService {

    async create(data: CreateGstrDto, userId: number) {
        let gstrRegisters = [];
        let user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
        for (const clientItem of data.clients) {
            let client = await Client.findOne({ id: clientItem })
            let gstrRegister = new GstrRegister();
            gstrRegister.client = client;
            gstrRegister.registrationType = data.registrationType;
            gstrRegister.organization = user.organization;
            gstrRegisters.push(gstrRegister);
        }
        if (gstrRegisters.length > 0) {
            await GstrRegister.save(gstrRegisters);
        }
        if (data.syncAll) {
            await this.syncClients(data)
        }
        return gstrRegisters;
    }

    async syncClients(data: any) {
        const clientDetails = await createQueryBuilder(Client, 'client')
            .select(['client.id', 'client.gstNumber', 'client.gstRegistrationDate'])
            .whereInIds(data.clients)
            .getMany();
        const results = [];
        for (const client of clientDetails) {
            const [, month] = client.gstRegistrationDate.split('/');
            const regYear = (client.gstRegistrationDate.slice(-4));
            let financialYear: string
            if (parseInt(month) <= 3) {
                financialYear = `${parseInt(regYear) - 1}-${regYear.slice(-2)}`;
            } else {
                financialYear = `${regYear}-${parseInt(regYear.slice(-2)) + 1}`
            }
            const result = await fetchWithRetry({ id: client.id, gstNumber: client.gstNumber, fixedFy: data.financialYear }, financialYear);
            results.push(result);

        }
        return results;

        async function fetchWithRetry(item, financialYear) {
            const registerId = await GstrRegister.findOne({ where: { client: item.id } });
            const headers = { Authorization: process.env.FYN_AUTH };
            const url = `${process.env.FYN_RETURNS}/${item.gstNumber}/${financialYear}`;
            try {
                console.log(`Fetching returns for GST: ${item.gstNumber}, FY: ${financialYear}`);
                const response = await axios.get(url, { headers });
                if (response?.data?.EFiledlist?.length > 0) {
                    try {
                        const returnsData = [];
                        await getManager().transaction(async transactionalEntityManager => {
                            for (const i of response?.data?.EFiledlist) {
                                const returnData = new ReturnsData();
                                returnData.gstrRegister = registerId;
                                returnData.financialYear = financialYear;
                                returnData.valid = i.valid;
                                returnData.mof = i.mof;
                                returnData.dof = i.dof;
                                returnData.rtntype = i.rtntype;
                                returnData.retPrd = i.ret_prd;
                                returnData.arn = i.arn;
                                returnData.status = i.status;
                                returnsData.push(returnData);
                            }
                            if (returnsData.length > 0) {
                                await transactionalEntityManager.insert(ReturnsData, returnsData);
                            }
                        });
                    } catch (error) {
                        console.error("Failed to save returns data:", error);
                    }

                } else {
                    console.log(response?.data?.error?.message)
                }
                if (financialYear !== getCurrentFinancialYear()) {
                    const nextYear = incrementFinancialYear(financialYear);
                    return fetchWithRetry(item, nextYear);
                }
                else {
                    return true;
                }
            } catch (e) {
                console.error(`Failed to fetch data for GST number: ${item.gstNumber} in year: ${financialYear}`, e);
                throw e;
            }
        };

    }

    async findClients(userId: number, query: any) {
        let user = await User.findOne({
            where: { id: userId },
            relations: ['organization'],
        });
        let clients = await createQueryBuilder(Client, 'client')
            .leftJoinAndSelect('client.organization', 'organization')
            .leftJoinAndSelect('client.gstrRegister', 'gstrRegister')
            .where('organization.id = :organization', { organization: user.organization.id })
            .andWhere('gstrRegister.id is null')
            .andWhere('client.registrationType=:registration', { registration: query.registrationType })
            .andWhere('client.gstVerified is true')
            .andWhere('client.status=:status', { status: UserStatus.ACTIVE })
            .getMany();

        return clients;
    }

    async findAll(userId: number, query: FindQueryDto) {
        const { limit,
            offset,
            search,
            type,
            category,
            subCategory,
            jurisdiction,
            status
        } = query;

        let comparisonYear: number
        if (query.selectedMonth) {
            const [startYear, endYear] = query.selectedYear.split('-').map(y => parseInt(y, 10));
            const normalizedEndYear = endYear < 100 ? 2000 + endYear : endYear;
            if (query.selectedMonth === "03") {
                comparisonYear = parseInt(query.selectedMonth) >= 4 && parseInt(query.selectedMonth) <= 12 ? startYear : normalizedEndYear - 1;
            } else {
                comparisonYear = parseInt(query.selectedMonth) >= 4 && parseInt(query.selectedMonth) <= 12 ? startYear : normalizedEndYear;
            }
        }

        if (["CMP08"].includes(query.rtntype)) {
            const [startYear, endYear] = query.selectedYear.split('-').map(y => parseInt(y, 10));
            const normalizedEndYear = endYear < 100 ? 2000 + endYear : endYear;
            if (query.selectedQuarter === "03") {
                comparisonYear = parseInt(query.selectedQuarter) >= 4 && parseInt(query.selectedQuarter) <= 12 ? startYear : normalizedEndYear - 1;

            } else {
                comparisonYear = parseInt(query.selectedQuarter) >= 4 && parseInt(query.selectedQuarter) <= 12 ? startYear : normalizedEndYear;
            }
        }
        if (["GSTR4", "GSTR9", "GSTR9C"].includes(query.rtntype)) {
            comparisonYear = query.selectedYear.split('-').map(y => parseInt(y, 10))[0];
        }
        let user = await User.findOne({ where: { id: userId }, relations: ['organization'] });


        let getrClients = getConnection()
            .createQueryBuilder(GstrRegister, 'gstrRegister')
            .addSelect(['client.displayName',
                'client.clientId',
                'client.gstNumber',
                'client.category',
                'client.id',
            ])
            .leftJoin('gstrRegister.client', 'client')
        if (["GSTR1", "GSTR2X", "GSTR3B", "GSTR7", "GSTR8"].includes(query.rtntype)) {

            getrClients.leftJoinAndSelect('gstrRegister.returnsData', 'returnsData',
                'returnsData.financialYear >= :financialYear AND returnsData.rtntype=:rtntype AND  LEFT(ret_prd, 2) =:selectedMonth AND RIGHT(ret_prd, 4)=:comparisonYear',
                {
                    financialYear: query.selectedYear,
                    rtntype: query.rtntype,
                    selectedMonth: query.selectedMonth,
                    comparisonYear: comparisonYear
                });


        } else if (["CMP08"].includes(query.rtntype)) {
            getrClients.leftJoinAndSelect('gstrRegister.returnsData', 'returnsData',
                'returnsData.financialYear >= :financialYear AND returnsData.rtntype=:rtntype AND  LEFT(ret_prd, 2) =:selectedQuarter AND RIGHT(ret_prd, 4)=:comparisonYear',
                {
                    financialYear: query.selectedYear,
                    rtntype: query.rtntype,
                    selectedQuarter: query.selectedQuarter,
                    comparisonYear: comparisonYear
                })
        } else if (["GSTR4", "GSTR9", "GSTR9C"].includes(query.rtntype)) {
            getrClients.leftJoinAndSelect('gstrRegister.returnsData', 'returnsData',
                'returnsData.financialYear >=:financialYear AND returnsData.rtntype=:rtntype AND RIGHT(ret_prd,4)=:comparisonYear',
                {
                    financialYear: query.selectedYear,
                    rtntype: query.rtntype,
                    comparisonYear: comparisonYear
                }
            )
        };

        getrClients.where('gstrRegister.organization = :organization', { organization: user.organization.id })
            .andWhere('gstrRegister.registrationType = :type', { type });
        if (status?.length && status[0] === "Filed") {
            getrClients.andWhere('returnsData.status=:status', { status: "Filed" });
            
        } else if (status?.length && status[0] === "Not Filed") {
            getrClients.andWhere('returnsData.status is null');
        }

        if (category?.length) {
            getrClients.andWhere('client.category in (:...category)', { category });
        }

        if (subCategory?.length) {
            getrClients.andWhere('client.subCategory in (:...subCategory)', { subCategory });
        }

        if (jurisdiction?.length) {
            getrClients.andWhere('LEFT(client.gstNumber, 2) in (:...jurisdiction)', { jurisdiction });
        }

        if (search) {
            const whereClause = `(client.displayName LIKE '%${search}%' OR client.gstNumber LIKE '%${search}%')`;
            getrClients.andWhere(whereClause);
        }

        if (offset >= 0) {
            getrClients.skip(offset);
        }

        if (limit) {
            getrClients.take(limit);
        }

        let result = await getrClients.getManyAndCount();
        return {
            count: result[1],
            result: result[0],
        };
    }

    async getCompliance(query: FindComplianceDto) {
        const client = await Client.findOne(query.clientId);
        const gstRegister = await GstrRegister.findOne({ where: { client: query.clientId } });
        if (!gstRegister) {
            return 'Not-synchronized';
        }
        const [startYear, endYear] = query.selectedYear.split('-').map(y => parseInt(y, 10));
        const start = `${startYear}`;
        const end = `${20 + `${endYear}`}`;


        const resultquery = await createQueryBuilder(ReturnsData, 'returnsData')
            .leftJoin('returnsData.gstrRegister', 'gstrRegister')
            .leftJoinAndSelect('gstrRegister.client', 'client')
            .select([
                'returnsData',
                'client.id',
            ])

            .where('client.id = :clientId', { clientId: query.clientId })
            .andWhere(
                `(
                    (substring(returnsData.retPrd, 1, 2) BETWEEN '04' AND '12' AND substring(returnsData.retPrd, 3, 6) = :start) OR 
                    (substring(returnsData.retPrd, 1, 2) BETWEEN '01' AND '03' AND substring(returnsData.retPrd, 3, 6) = :end)
                )`,
                { start: start, end: end }
            );

        if (['GSTR1', 'GSTR2X', 'GSTR3B', 'GSTR7', 'GSTR8'].includes(query.rtntype)) {
            resultquery.orderBy(
                `CASE
                        WHEN substring(returnsData.retPrd, 1, 2) = '04' AND substring(returnsData.retPrd, 3, 6) = :start THEN 1
                        WHEN substring(returnsData.retPrd, 1, 2) = '05' AND substring(returnsData.retPrd, 3, 6) = :start THEN 2
                        WHEN substring(returnsData.retPrd, 1, 2) = '06' AND substring(returnsData.retPrd, 3, 6) = :start THEN 3
                        WHEN substring(returnsData.retPrd, 1, 2) = '07' AND substring(returnsData.retPrd, 3, 6) = :start THEN 4
                        WHEN substring(returnsData.retPrd, 1, 2) = '08' AND substring(returnsData.retPrd, 3, 6) = :start THEN 5
                        WHEN substring(returnsData.retPrd, 1, 2) = '09' AND substring(returnsData.retPrd, 3, 6) = :start THEN 6
                        WHEN substring(returnsData.retPrd, 1, 2) = '10' AND substring(returnsData.retPrd, 3, 6) = :start THEN 7
                        WHEN substring(returnsData.retPrd, 1, 2) = '11' AND substring(returnsData.retPrd, 3, 6) = :start THEN 8
                        WHEN substring(returnsData.retPrd, 1, 2) = '12' AND substring(returnsData.retPrd, 3, 6) = :start THEN 9
                        WHEN substring(returnsData.retPrd, 1, 2) = '01' AND substring(returnsData.retPrd, 3, 6) = :end THEN 10
                        WHEN substring(returnsData.retPrd, 1, 2) = '02' AND substring(returnsData.retPrd, 3, 6) = :end THEN 11
                        WHEN substring(returnsData.retPrd, 1, 2) = '03' AND substring(returnsData.retPrd, 3, 6) = :end THEN 12
                    END`,
                'ASC'
            )
                .limit(12);

        } else if (['CMP08'].includes(query.rtntype)) {
            resultquery.orderBy(
                `CASE
                        WHEN substring(returnsData.retPrd, 1, 2) = '06' AND substring(returnsData.retPrd, 3, 6) = :start THEN 1
                        WHEN substring(returnsData.retPrd, 1, 2) = '09' AND substring(returnsData.retPrd, 3, 6) = :start THEN 2
                        WHEN substring(returnsData.retPrd, 1, 2) = '12' AND substring(returnsData.retPrd, 3, 6) = :start THEN 3
                        WHEN substring(returnsData.retPrd, 1, 2) = '03' AND substring(returnsData.retPrd, 3, 6) = :end THEN 4
                    END`,
                'ASC'
            )
                .limit(4);
        } else {
            resultquery.orderBy(
                `CASE
                        WHEN substring(returnsData.retPrd, 1, 2) = '03' AND substring(returnsData.retPrd, 3, 6) = :end THEN 1
                    END`,
                'ASC'
            )
                .limit(1);
        }

        resultquery.andWhere('returnsData.financialYear >= :financialYear AND returnsData.rtntype = :rtntype', {
            financialYear: query.selectedYear,
            rtntype: query.rtntype,
        });

        const r = await resultquery.getManyAndCount();

        const monthMapping = {
            "04": "April",
            "05": "May",
            "06": "June",
            "07": "July",
            "08": "August",
            "09": "September",
            "10": "October",
            "11": "November",
            "12": "December",
            "01": "January",
            "02": "February",
            "03": "March"
        };

        const emptyMonthData = {
            id: null,
            mof: null,
            dof: "-",
            retPrd: null,
            arn: '-',
            status: 'Not Filed',
            legalName: null,
            tradeName: null,
            frequency: null,
        };

        if (['GSTR1', 'GSTR2X', 'GSTR3B', 'GSTR7', 'GSTR8'].includes(query.rtntype)) {

            const fiscalYearMonths = ['04', '05', '06', '07', '08', '09', '10', '11', '12', '01', '02', '03'];

            let results = fiscalYearMonths.map(month => ({
                ...emptyMonthData,
                legalName: client.legalName,
                tradeName: client.tradeName,
                frequency: monthMapping[month],
            }));

            r[0].forEach(dataItem => {
                const monthIndex = fiscalYearMonths.indexOf(dataItem.retPrd.substring(0, 2));
                if (monthIndex !== -1) {
                    results[monthIndex] = {
                        ...dataItem,
                        legalName: client.legalName,
                        tradeName: client.tradeName,
                        frequency: monthMapping[dataItem.retPrd.slice(0, 2)]
                    };
                }
            });

            return results;
        } else if (['CMP08'].includes(query.rtntype)) {
            const quarterMapping = {
                "06": "Q1 (Apr - Jun)",
                "09": "Q2 (Jul - Sep)",
                "12": "Q3 (Oct - Dec)",
                "03": "Q4 (Jan - Mar)"
            }
            const fiscalYearQuarter = ['06', '09', '12', '03'];
            let results2 = fiscalYearQuarter.map(quarter => ({
                ...emptyMonthData,
                legalName: client.legalName,
                tradeName: client.tradeName,
                frequency: quarterMapping[quarter],

            }))

            r[0].forEach(dataItem => {
                const monthIndex = fiscalYearQuarter.indexOf(dataItem.retPrd.substring(0, 2));
                if (monthIndex !== -1) {
                    results2[monthIndex] = {
                        ...dataItem,
                        legalName: client.legalName,
                        tradeName: client.tradeName,
                        frequency: monthMapping[dataItem.retPrd.slice(0, 2)]
                    };
                }
            })
            return results2;

        } else {
            const fiscalYear = ['03'];
            const yearMapping = {
                "03": "Yearly",
            };
            let results3 = fiscalYear.map(year => ({
                ...emptyMonthData,
                legalName: client.legalName,
                tradeName: client.tradeName,
                frequency: yearMapping[year],
            }));

            r[0].forEach(dataItem => {
                const monthIndex = fiscalYear.indexOf(dataItem.retPrd.substring(0, 2));
                if (monthIndex !== -1) {
                    results3[monthIndex] = {
                        ...dataItem,
                        legalName: client.legalName,
                        tradeName: client.tradeName,
                        frequency: yearMapping[dataItem.retPrd.slice(0, 2)]
                    };
                }
            })
            return results3
        }
    }
}

