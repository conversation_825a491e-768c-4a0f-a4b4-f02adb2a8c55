<!DOCTYPE html>
<html>

<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width,initial-scale=1" />
  <link rel="preconnect" href="https://fonts.googleapis.com" />
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
  <link
    href="https://fonts.googleapis.com/css2?family=Montserrat:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap"
    rel="stylesheet" />
</head>

<body style="margin: 0; padding: 0">
  <div style="background-color: aliceblue" align="center">
    <table style="
          background: #ffffff;
          font-family: Mulish;
          color: #404040;
          padding: 40px;
          margin: auto;
          width: 100%;
        ">
      <!-- <tr>
        <td>
          <img src="https://jss-vider-bucket.s3.ap-south-1.amazonaws.com/storage/VID0085/Atom%20Pro%20(3).png"
            alt="ATOM Pro Logo" width="100%" height="35px" />
        </td>
      </tr> -->
      ..
      <tr>
        <td style="
          margin-top: 20px;
          height: auto;
          display: block;
          font-size: 18px;
          color: #182f53;
        ">
          Dear Bussiness Team,
        </td>
      </tr>
      <tr>
        <td style="
          margin-top: 10px;
          height: auto;
          display: block;
          font-size: 18px;
          color: #182f53;
          margin-bottom: 20px;
        ">
          These are the Organization Details Which are about to expire in a week and month ago expired organization lists
        </td>
      </tr>

      <tr>
        <td style="
        background-color: #2c5c9c;
        color: white;
        border: none;
        font-size: 20px;
        border-radius: 5px;
        text-align: center;
        text-decoration: none;
      ">Expiring and Expired Organizations</td>
      </tr>
      <% if (oneWeekAboutToExpireArray.length> 0) { %>
        <tr>
          <td style="
            background-color: #64B5F6;
            color: white;
            padding: 5px;
            font-size: 18px;
            border-top-left-radius: 5px;
            border-top-right-radius: 5px;
            display: inline-block; 
            margin-top: 50px;
        ">
            About to Expire in One Week Organization(s) List.
          </td>
        </tr>
        <tr>
          <td>
            <table style="border-spacing: 0px; border: 0.923px solid #cfcfcf; margin-bottom: 100px;">
              <thead>
                <tr>
                  <th style="
                      border: 0.923px solid #cfcfcf;
                      background: #e4eeff;
                      padding: 5px;
                      color: #404040;
                      font-size: 11.082px;
                      font-style: normal;
                      font-weight: 500;
                      line-height: 150.523%;
                      letter-spacing: 0.665px;
                      width: 2vw;
                      font-weight: bold;
                    ">
                    S.No
                  </th>
                  <th style="
                      border: 0.923px solid #cfcfcf;
                      background: #e4eeff;
                      padding: 5px;
                      color: #404040;
                      font-size: 11.082px;
                      font-style: normal;
                      font-weight: 500;
                      line-height: 150.523%;
                      letter-spacing: 0.665px;
                      width: 5vw;
                      font-weight: bold;
                    ">
                    Organization ID
                  </th>
                  <th style="
                      border: 0.923px solid #cfcfcf;
                      background: #e4eeff;
                      padding: 5px;
                      color: #404040;
                      font-size: 11.082px;
                      font-style: normal;
                      font-weight: 500;
                      line-height: 150.523%;
                      letter-spacing: 0.665px;
                      width: 15vw;
                      font-weight: bold;
                    ">
                     Name
                  </th>
                  <th style="
                      border: 0.923px solid #cfcfcf;
                      background: #e4eeff;
                      padding: 5px;
                      color: #404040;
                      font-size: 11.082px;
                      font-style: normal;
                      font-weight: 500;
                      line-height: 150.523%;
                      letter-spacing: 0.665px;
                      width: 7vw;
                      font-weight: bold;
                    ">
                    Email
                  </th>
                  <th style="
                      border: 0.923px solid #cfcfcf;
                      background: #e4eeff;
                      padding: 5px;
                      color: #404040;
                      font-size: 11.082px;
                      font-style: normal;
                      font-weight: 500;
                      line-height: 150.523%;
                      letter-spacing: 0.665px;
                      width: 5vw;
                      font-weight: bold;
                    ">
                    Mobile No.
                  </th>
                  <th style="
                      border: 0.923px solid #cfcfcf;
                      background: #e4eeff;
                      padding: 5px;
                      color: #404040;
                      font-size: 11.082px;
                      font-style: normal;
                      font-weight: 500;
                      line-height: 150.523%;
                      letter-spacing: 0.665px;
                      width: 10vw;
                      font-weight: bold;
                    ">
                    Expiry Date
                  </th>

                  <th style="
                      border: 0.923px solid #cfcfcf;
                      background: #e4eeff;
                      padding: 5px;
                      color: #404040;
                      font-size: 11.082px;
                      font-style: normal;
                      font-weight: 500;
                      line-height: 150.523%;
                      letter-spacing: 0.665px;
                      width: 10vw;
                      font-weight: bold;
                    ">
                    Created At
                  </th>
                </tr>
              </thead>
              <tbody>
                <% for(let i=0; i < oneWeekAboutToExpireArray.length; i++ ) { %>
                  <% let incrementedValue=i + 1; %>
                    <tr>
                      <td style="
                      text-align: center;
                      padding: 5px;
                      color: #5f5f5f;
                      font-size: 11.533px;
                      font-style: normal;
                      font-weight: 600;
                      line-height: 150.523%;
                      letter-spacing: 0.692px;
                      border: 0.923px solid #cfcfcf;
                    ">
                        <%= incrementedValue %>
                      </td>
                      <td style="
                      text-align: center;
                      padding: 5px;
                      color: #5f5f5f;
                      font-size: 11.533px;
                      font-style: normal;
                      font-weight: 600;
                      line-height: 150.523%;
                      letter-spacing: 0.692px;
                      border: 0.923px solid #cfcfcf;
                    ">
                        <%= oneWeekAboutToExpireArray[i].id %>
                      </td>
                      <td style="
                      text-align: center;
                      padding: 5px;
                      color: #5f5f5f;
                      font-size: 11.533px;
                      font-style: normal;
                      font-weight: 600;
                      line-height: 150.523%;
                      letter-spacing: 0.692px;
                      border: 0.923px solid #cfcfcf;
                    ">
                        <%= oneWeekAboutToExpireArray[i].name %>
                      </td>
                      <td style="
                      text-align: center;
                      padding: 5px;
                      color: #5f5f5f;
                      font-size: 11.533px;
                      font-style: normal;
                      font-weight: 600;
                      line-height: 150.523%;
                      letter-spacing: 0.692px;
                      border: 0.923px solid #cfcfcf;
                    ">
                        <%= oneWeekAboutToExpireArray[i].email %>
                      </td>
                      <td style="
                      text-align: center;
                      padding: 5px;
                      color: #5f5f5f;
                      font-size: 11.533px;
                      font-style: normal;
                      font-weight: 600;
                      line-height: 150.523%;
                      letter-spacing: 0.692px;
                      border: 0.923px solid #cfcfcf;
                    ">
                        <%= oneWeekAboutToExpireArray[i].mobileNumber %>
                      </td>
                      <td style="
                      text-align: center;
                      padding: 5px;
                      color: #5f5f5f;
                      font-size: 11.533px;
                      font-style: normal;
                      font-weight: 600;
                      line-height: 150.523%;
                      letter-spacing: 0.692px;
                      border: 0.923px solid #cfcfcf;
                    ">
                        <%= oneWeekAboutToExpireArray[i].expiryDate %>
                      </td>

                      <td style="
                      text-align: center;
                      padding: 5px;
                      color: #5f5f5f;
                      font-size: 11.533px;
                      font-style: normal;
                      font-weight: 600;
                      line-height: 150.523%;
                      letter-spacing: 0.692px;
                      border: 0.923px solid #cfcfcf;
                    ">
                        <%= oneWeekAboutToExpireArray[i].createdAt %>
                      </td>
                    </tr>
                    <% } %>
              </tbody>
            </table>
          </td>
        </tr>
        <% } %>
          <% if (monthAgoExpiredArray.length> 0) { %>
            <tr>
              <td style="
            background-color: #64B5F6;
            color: white;
            padding: 5px;
            font-size: 18px;
            border-top-left-radius: 5px;
            border-top-right-radius: 5px;
            display: inline-block; 
        ">
                One Month Ago Expired Organization(s) List
              </td>
            </tr>
            <tr>
              <td>
                <table style="border-spacing: 0px; border: 0.923px solid #cfcfcf">
                  <thead>
                    <tr>
                      <th style="
                      border: 0.923px solid #cfcfcf;
                      background: #e4eeff;
                      padding: 5px;
                      color: #404040;
                      font-size: 11.082px;
                      font-style: normal;
                      font-weight: 500;
                      line-height: 150.523%;
                      letter-spacing: 0.665px;
                      width: 2vw;
                      font-weight: bold;
                    ">
                        S.No
                      </th>
                      <th style="
                    border: 0.923px solid #cfcfcf;
                    background: #e4eeff;
                    padding: 5px;
                    color: #404040;
                    font-size: 11.082px;
                    font-style: normal;
                    font-weight: 500;
                    line-height: 150.523%;
                    letter-spacing: 0.665px;
                    width: 5vw;
                    font-weight: bold;
                  ">
                        Organization ID
                      </th>
                      <th style="
                  border: 0.923px solid #cfcfcf;
                  background: #e4eeff;
                  padding: 5px;
                  color: #404040;
                  font-size: 11.082px;
                  font-style: normal;
                  font-weight: 500;
                  line-height: 150.523%;
                  letter-spacing: 0.665px;
                  width: 15vw;
                  font-weight: bold;
                ">
                        Name
                      </th>
                      <th style="
                border: 0.923px solid #cfcfcf;
                background: #e4eeff;
                padding: 5px;
                color: #404040;
                font-size: 11.082px;
                font-style: normal;
                font-weight: 500;
                line-height: 150.523%;
                letter-spacing: 0.665px;
                width: 7vw;
                font-weight: bold;
              ">
                        Email
                      </th>
                      <th style="
              border: 0.923px solid #cfcfcf;
              background: #e4eeff;
              padding: 5px;
              color: #404040;
              font-size: 11.082px;
              font-style: normal;
              font-weight: 500;
              line-height: 150.523%;
              letter-spacing: 0.665px;
              width: 5vw;
              font-weight: bold;
            ">
                        Mobile No.
                      </th>
                      <th style="
                      border: 0.923px solid #cfcfcf;
                      background: #e4eeff;
                      padding: 5px;
                      color: #404040;
                      font-size: 11.082px;
                      font-style: normal;
                      font-weight: 500;
                      line-height: 150.523%;
                      letter-spacing: 0.665px;
                      width: 10w;
                      font-weight: bold;
                    ">
                        Expired Date
                      </th>
                      <th style="
                      border: 0.923px solid #cfcfcf;
                      background: #e4eeff;
                      padding: 5px;
                      color: #404040;
                      font-size: 11.082px;
                      font-style: normal;
                      font-weight: 500;
                      line-height: 150.523%;
                      letter-spacing: 0.665px;
                      width: 10vw;
                      font-weight: bold;
                    ">
                        Created At
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    <% for(let i=0; i < monthAgoExpiredArray.length; i++ ) { %>
                      <% let incrementedValue=i + 1; %>
                        <tr>
                          <td style="
                      text-align: center;
                      padding: 5px;
                      color: #5f5f5f;
                      font-size: 11.533px;
                      font-style: normal;
                      font-weight: 600;
                      line-height: 150.523%;
                      letter-spacing: 0.692px;
                      border: 0.923px solid #cfcfcf;
                    ">
                            <%= incrementedValue %>
                          </td>
                          <td style="
                      text-align: center;
                      padding: 5px;
                      color: #5f5f5f;
                      font-size: 11.533px;
                      font-style: normal;
                      font-weight: 600;
                      line-height: 150.523%;
                      letter-spacing: 0.692px;
                      border: 0.923px solid #cfcfcf;
                    ">
                            <%= monthAgoExpiredArray[i].id %>
                          </td>
                          <td style="
                    text-align: center;
                    padding: 5px;
                    color: #5f5f5f;
                    font-size: 11.533px;
                    font-style: normal;
                    font-weight: 600;
                    line-height: 150.523%;
                    letter-spacing: 0.692px;
                    border: 0.923px solid #cfcfcf;
                  ">
                            <%= monthAgoExpiredArray[i].name %>
                          </td>
                          <td style="
                  text-align: center;
                  padding: 5px;
                  color: #5f5f5f;
                  font-size: 11.533px;
                  font-style: normal;
                  font-weight: 600;
                  line-height: 150.523%;
                  letter-spacing: 0.692px;
                  border: 0.923px solid #cfcfcf;
                ">
                            <%= monthAgoExpiredArray[i].email %>
                          </td>
                          <td style="
                text-align: center;
                padding: 5px;
                color: #5f5f5f;
                font-size: 11.533px;
                font-style: normal;
                font-weight: 600;
                line-height: 150.523%;
                letter-spacing: 0.692px;
                border: 0.923px solid #cfcfcf;
              ">
                            <%= monthAgoExpiredArray[i].mobileNumber %>
                          </td>
                          <td style="
                      text-align: center;
                      padding: 5px;
                      color: #5f5f5f;
                      font-size: 11.533px;
                      font-style: normal;
                      font-weight: 600;
                      line-height: 150.523%;
                      letter-spacing: 0.692px;
                      border: 0.923px solid #cfcfcf;
                    ">
                            <%= monthAgoExpiredArray[i].expiryDate %>
                          </td>
                          <td style="
                          text-align: center;
                          padding: 5px;
                          color: #5f5f5f;
                          font-size: 11.533px;
                          font-style: normal;
                          font-weight: 600;
                          line-height: 150.523%;
                          letter-spacing: 0.692px;
                          border: 0.923px solid #cfcfcf;
                        ">
                                <%= monthAgoExpiredArray[i].createdAt %>
                              </td>
                        </tr>
                        <% } %>
                  </tbody>
                </table>
              </td>
            </tr>
            <% } %>

              <tr>
                <td style="font-family: Mulish; font-size: 12px; color: maroon;">
                  <strong>Note:</strong> This is an automated email. Please do not reply, as this mailbox is not
                  monitored.
                </td>
              </tr>
              <tr>
                <td align="center">
                  <p style="
                text-align: center;
                padding: 20px 0px;
                color: #2a5398;
                font-size: 18px;
                font-style: normal;
                font-weight: 600;
              ">
                    Vider Business Solutions
                  </p>
                 
                </td>
              </tr>
              <tr>
                <td align="center">
                  <table style="width: 310px ">
                    <tr>
                      <td align="center">
                        <a href=""><img style="height: 25px" src="https://jss-vider.s3.ap-south-1.amazonaws.com/3.png"
                            alt="" />
                        </a>
                        <p style="font-size: 11px">+91 9171 121 121</p>
                      </td>

                      <td align="center">
                        <a href="http://vider.in/atom.html"><img style="height: 25px"
                            src="https://jss-vider.s3.ap-south-1.amazonaws.com/4.png" alt="" /></a>
                        <p style="font-size: 11px">vider.in/atom.html</p>
                      </td>

                      <td align="center">
                        <a href="mailto:<EMAIL>"><img style="height: 25px"
                            src="https://jss-vider.s3.ap-south-1.amazonaws.com/5.png" alt="" /></a>
                        <p style="font-size: 11px"><EMAIL></p>
                      </td>
                    </tr>
                  </table>
                </td>
              </tr>
    </table>
  </div>
</body>
</html>