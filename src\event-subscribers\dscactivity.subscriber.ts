import {
  Connection,
  EntitySubscriberInterface,
  EventSubscriber,
  InsertEvent,
  getManager,
} from 'typeorm';
import {
  insertINTOnotification,
  getAdminIdsWithClientId,
  getAdminEmailssWithClientId,
  getUserNamewithUserId,
  getAdminEmailssBasedOnOrganizationId,
  insertINTONotificationUpdate,
  getUserDetails,
} from 'src/utils/re-use';
import DscActivity from 'src/modules/dsc-register/entity/dsc-activity.entity';
import { sendnewMail } from 'src/emails/newemails';
import * as moment from 'moment';
import { sendClientWhatsAppTemplateMessage, sendWhatsAppTextMessage } from 'src/modules/whatsapp/whatsapp.service';
import OrganizationPreferences from 'src/modules/organization-preferences/entity/organization-preferences.entity';
import { Organization } from 'src/modules/organization/entities/organization.entity';
import { User } from 'src/modules/users/entities/user.entity';
import { fullMobileNumberWithCountry } from 'src/utils/validations/fullMobileWithCountry';
import ViderWhatsappSessions from 'src/modules/whatsapp/entity/viderWhatsappSessions';
// const { Kafka } = require('kafkajs')
// const { generateAuthToken } = require('aws-msk-iam-sasl-signer-js')

// async function oauthBearerTokenProvider(region) {
//    // Uses AWS Default Credentials Provider Chain to fetch credentials
//    const authTokenResponse = await generateAuthToken({ region });
//    return {
//        value: authTokenResponse.token
//    }
// }

@EventSubscriber()
export class DscSubscriber implements EntitySubscriberInterface<DscActivity> {
  constructor(private readonly connection: Connection) {
    connection.subscribers.push(this);
  }

  listenTo() {
    return DscActivity;
  }

  async beforeInsert(event: InsertEvent<DscActivity>) { }
  async afterInsert(event: InsertEvent<DscActivity>) {
  //   const run = async () => {
  //     const kafka = new Kafka({
  //         clientId: 'my-app',
  //         brokers: [
  //    'b-2-public.vidermskcluster.b3wq8s.c2.kafka.ap-south-1.amazonaws.com:9198', 
  //    'b-1-public.vidermskcluster.b3wq8s.c2.kafka.ap-south-1.amazonaws.com:9198'
  //  ],
  //         ssl: true,
  //         sasl: {
  //             mechanism: 'oauthbearer',
  //             oauthBearerProvider: () => oauthBearerTokenProvider('ap-south-1')
  //         }
  //     })
  //  const admin = kafka.admin();
  //  try {
  //      await admin.createTopics({
  //        topics: [
  //          {
  //            topic: 'dsc-activity',
  //            numPartitions: 3, // Specify the number of partitions for the topic
  //            replicationFactor: 2, // Specify the replication factor (must be <= number of brokers)
  //          },
  //        ],
  //      });
  //    } catch (error) {
  //      console.error(`Error creating topic, error`);
  //    } finally {
  //      await admin.disconnect();
  //    }
   
  //     const producer = kafka.producer()
  //    //  const consumer = kafka.consumer({ groupId: 'new-group' })
  // //  console.log('producing',{entityDetails: event.entity, clientOldDetails})
  //     // Producing
  //     await producer.connect()
  //     await producer.send({
  //         topic: 'dsc-activity',
  //         messages: [
  //         {key:'DSC Activity',value: JSON.stringify({entityDetails: event.entity}) }
  //        ]
   
  //     })
      
  //  }         
   
  //  run().catch(console.error)
    const { dscRegister } = event.entity;
    const dscId = dscRegister?.id;
    if (event?.entity !== undefined) {
      const logInUser = event.entity['userId'];
      const entityManager = getManager();
      function getAdminData(fullName, holderName, expiryDateFormatted, personName, userName, date) {
        return {
          adminName: fullName,
          holderName: holderName,
          expiryDate: expiryDateFormatted,
          issueName: personName,
          ReceiverName: personName,
          userName: userName,
          date: moment(date).format('DD-MM-YYYY'),
          userId: event.entity['userId'],
        };
      }
      if (event.entity.dscRegister?.clients?.length) {
        const { id, dscRegister, type, personName, date, whatsappCheck, emailCheck } =
          event?.entity;
        const { holderName, clients, email, mobileNumber, countryCode } = dscRegister;
        const expiryDateFormatted = moment(dscRegister?.expiryDate).format('DD-MM-YYYY');
        // new Date(dscRegister?.expiryDate).toLocaleDateString('en-GB');



        const client_id = clients[0]?.id;
        const orgQuery = `SELECT organization_id FROM client where id = ${client_id};`;
        const orgIdSql = await entityManager.query(orgQuery);
        const orgId = orgIdSql[0]?.organization_id;
        const organization = await Organization.findOne({ id: orgId });


        const addressParts = [
          organization.buildingNo || '',
          organization.floorNumber || '',
          organization.buildingName || '',
          organization.street || '',
          organization.location || '',
          organization.city || '',
          organization.district || '',
          organization.state || ''
        ].filter(part => part && part.trim() !== '');
        const pincode = organization.pincode && organization.pincode.trim() !== '' ? ` - ${organization.pincode}` : '';
    
        const address = addressParts.join(', ') + pincode;
        const orgdetails = await Organization.findOne({ id: orgId });

        const userList = await getAdminIdsWithClientId(client_id);
        const adminMails: any = await getAdminEmailssWithClientId(client_id);
        const userName = await getUserNamewithUserId(event.entity['userId']);

        if (type === 'issue') {
          const key = 'DSC_ISSUED_PUSH';
          const title = 'DSC issued';
          const body = `DSC of <strong>${holderName}</strong> has been issued by <strong>${userName}</strong>.`;
          // insertINTOnotification(title, body, userList, orgId);
          insertINTONotificationUpdate(title, body, userList, orgId, key, dscId);

          try {
            if (userList !== undefined) {
              const sessionValidation = await ViderWhatsappSessions.findOne({
                where: { userId: userList, status: 'ACTIVE' },
              });
              if (sessionValidation) {
                const adminUserDetails = await getUserDetails(userList);

                const { full_name: userFullName, mobile_number: userPhoneNumber, id, organization_id } = adminUserDetails;
                const key = 'DSC_ISSUED_WHATSAPP';
                const whatsappMessageBody = `
   Hi ${userFullName}
   DSC of ${holderName} has been issued to ${personName} by ${userName}

  We hope this helps!`;
                ;
                await sendWhatsAppTextMessage(
                  `91${userPhoneNumber}`,
                  whatsappMessageBody,
                  organization_id,
                  title,
                  id,
                  key,
                );
              }

            }
          } catch (error) {
            console.error('Error sending User WhatsApp notification:', error);
          }

          for (let i = 0; i < adminMails.length; i++) {
            const fullName = adminMails[i]?.fullName;
            const data = {
              ...getAdminData(
                fullName,
                holderName,
                expiryDateFormatted,
                personName,
                userName,
                date
              ),
              adress: address,
              phoneNumber: organization?.mobileNumber,
              mail: organization?.email,
              legalName: organization?.tradeName || organization?.legalName,
            };
            const mailOptions = {
              id: adminMails[i]?.id,
              key: 'DSC_ACTIVITY_ISSUE_MAIL',
              data: data,
             
              email: adminMails[i]?.email,
              filePath: 'dsc-activity-issue',
              subject: 'Dsc token issued | Vider',
            };
            await sendnewMail(mailOptions);
          }

          // issue template to client
          if (whatsappCheck) {
            const templateName = 'dsc_issue_client';
            // const dscMobileNumber = `91${mobileNumber}`;
            const dscMobileNumber = fullMobileNumberWithCountry(mobileNumber, countryCode);
            if (dscMobileNumber && dscMobileNumber !== null) {
              const whatsappOptions = {
                to: dscMobileNumber,
                name: templateName,
                header: [
                  {
                    type: 'text',
                    text: holderName,
                  },
                ],
                body: [holderName, expiryDateFormatted, personName, userName],
                title: 'DSC Issue',
                userId: logInUser,
                orgId: orgId,
                key: 'DSC_ISSUE_WHATSAPP',
              };
              await sendClientWhatsAppTemplateMessage(whatsappOptions);
            }
          }

          const key1 = 'DSC_ISSUE_MAIL'
          if (emailCheck && key1 === 'DSC_ISSUE_MAIL') {
            const address = `${orgdetails.buildingNo || " " ? orgdetails.buildingNo || " " + ', ' : ''}${orgdetails.floorNumber || " " ? orgdetails.floorNumber || " " + ', ' : ''}${orgdetails.buildingName || " " ? orgdetails.buildingName + ', ' : ''}${orgdetails.street ? orgdetails.street + ', ' : ''}${orgdetails.location ? orgdetails.location + ', ' : ''}${orgdetails.city ? orgdetails.city + ', ' : ''}${orgdetails.district ? orgdetails.district + ', ' : ''}${orgdetails.state ? orgdetails.state + ', ' : ''}${orgdetails.pincode ? orgdetails.pincode : ''}`;
            const dscHolderName = dscRegister.holderName;

            const mailOptions = {
              id: logInUser,
              key: 'DSC_ISSUE_MAIL',
              email: dscRegister?.email,
              clientMail: 'ORGANIZATION_CLIENT_EMAIL',
              data: {
                dscHolderName: dscHolderName,
                dscIssueTo: personName,
                tradeName: orgdetails?.tradeName,
                legalName: orgdetails?.tradeName || orgdetails?.legalName,
                adress: address,
                phoneNumber: orgdetails?.mobileNumber,
                mail: orgdetails?.email,
                userId: event.entity['userId'],
              },

              filePath: 'client-dsc-issued',
              subject: 'Issue of your DSC Token',
            };

            const orgPreferences: any = await OrganizationPreferences.findOne({ where: { organization: orgId } })
            const clientPreferences = orgPreferences?.clientPreferences?.email;

            if (clientPreferences && clientPreferences[key1]) {
              await sendnewMail(mailOptions);
            }
          }
        } else {
          const key = 'DSC_RECEIVED_PUSH';
          const title = 'DSC received';
          const body = `DSC of <strong>${holderName}</strong> has been received by <strong>${userName}</strong>.`;
          // insertINTOnotification(title, body, userList, orgId);
          insertINTONotificationUpdate(title, body, userList, orgId, key, dscId);
          try {
            if (userList !== undefined) {
              const sessionValidation = await ViderWhatsappSessions.findOne({
                where: { userId: userList, status: 'ACTIVE' },
              });
              if (sessionValidation) {
                const adminUserDetails = await getUserDetails(userList);

                const { full_name: userFullName, mobile_number: userPhoneNumber, id, organization_id } = adminUserDetails;
                const key = 'DSC_RECEIVED_WHATSAPP';
                const whatsappMessageBody = `
    Hi ${userFullName}
    DSC of ${holderName} has been received from ${personName} by ${userName}
 
   We hope this helps!`;
                ;
                await sendWhatsAppTextMessage(
                  `91${userPhoneNumber}`,
                  whatsappMessageBody,
                  organization_id,
                  title,
                  id,
                  key,
                );
              }

            }
          } catch (error) {
            console.error('Error sending User WhatsApp notification:', error);
          }
          if (adminMails != undefined) {
            for (let i = 0; i < adminMails.length; i++) {
              const fullName = adminMails[i]?.fullName;
              const data = {
                ...getAdminData(
                  fullName,
                  holderName,
                  expiryDateFormatted,
                  personName,
                  userName,
                  date
                ),
                adress: address,
                phoneNumber: organization?.mobileNumber,
                mail: organization?.email,
                legalName: organization?.tradeName || organization?.legalName,
              };
              const mailOptions = {
                id: adminMails[i]?.id,
                key: 'DSC_ACTIVITY_RECEIVED_MAIL',
                data: data,
                email: adminMails[i]?.email,
                filePath: 'dsc-activity-received',
                subject: 'Dsc token received | Vider',
              };
              await sendnewMail(mailOptions);
            }
          }

          if (whatsappCheck) {
            const templateName = 'dsc_receive_client';
            const dscMobileNumber = fullMobileNumberWithCountry(mobileNumber, countryCode);
            // const dscMobileNumber = `91${mobileNumber}`;
            if (dscMobileNumber && dscMobileNumber !== null) {
              const whatsappOptions = {
                to: dscMobileNumber,
                name: templateName,
                header: [
                  {
                    type: 'text',
                    text: holderName,
                  },
                ],
                body: [holderName, expiryDateFormatted, personName, userName],
                title: 'Dsc Receive',
                userId: logInUser,
                orgId: orgId,
                key: 'DSC_RECEIVE_WHATSAPP',
              };
              await sendClientWhatsAppTemplateMessage(whatsappOptions);
            }
          }

          const key1 = 'DSC_RECEIVE_MAIL'

          if (emailCheck && key1 === 'DSC_RECEIVE_MAIL') {
            const addressParts = [
              orgdetails.buildingNo || '',
              orgdetails.floorNumber || '',
              orgdetails.buildingName || '',
              orgdetails.street || '',
              orgdetails.location || '',
              orgdetails.city || '',
              orgdetails.district || '',
              orgdetails.state || ''
            ].filter(part => part && part.trim() !== '');

            const pincode = orgdetails.pincode && orgdetails.pincode.trim() !== '' ? ` - ${orgdetails.pincode}` : '';

            const address = addressParts.join(', ') + pincode;
            const dscHolderName = dscRegister.holderName;

            const mailOptions = {
              id: logInUser,
              key: 'DSC_RECEIVE_MAIL',
              email: dscRegister?.email,
              clientMail: 'ORGANIZATION_CLIENT_EMAIL',
              data: {
                dscHolderName: dscHolderName,
                dscIssueTo: personName,
                legalName: orgdetails?.tradeName || orgdetails?.legalName,
                adress: address,
                phoneNumber: orgdetails?.mobileNumber,
                mail: orgdetails?.email,
                userId: event.entity['userId'],
              },

              filePath: 'client-dsc-receive',
              subject: 'Receipt of your DSC Token',
            };
            const orgPreferences: any = await OrganizationPreferences.findOne({ where: { organization: orgId } })
            const clientPreferences = orgPreferences?.clientPreferences?.email;
            if (clientPreferences && clientPreferences[key1]) {
              await sendnewMail(mailOptions);
            }
          }
        }
      } else {
        const { dscRegister, type, personName, date, whatsappCheck, emailCheck } = event?.entity;
        const { holderName, clients, id, mobileNumber, countryCode } = dscRegister;
        const expiryDateFormatted = moment(dscRegister?.expiryDate).format('DD-MM-YYYY');
        const issueData: any = moment(date).format('DD-MM-YYYY');
        const orgQuery = `SELECT organization_id FROM dsc_register where id = ${id};`;
        const orgIdSql = await entityManager.query(orgQuery);
        const orgId = orgIdSql[0]?.organization_id;
        const orgdetails = await Organization.findOne({ id: orgId });
        const logInUser = event.entity['userId'];

        const userName = await getUserNamewithUserId(event.entity['userId']);
        const user_list = await getAdminEmailssBasedOnOrganizationId(orgId);
        const userList = [];
        for (let i of user_list) {
          userList.push(i.id);
        }
        if (type === 'issue') {
          const key = 'DSC_ISSUED_PUSH';
          const title = 'DSC issued';
          const body = `DSC of <strong>${holderName}</strong> has been issued by <strong>${userName}</strong>.`;
          // insertINTOnotification(title, body, userList, orgId);
          insertINTONotificationUpdate(title, body, userList, orgId, key);
           const organization = await Organization.findOne({ id: orgId });


        const addressParts = [
          organization.buildingNo || '',
          organization.floorNumber || '',
          organization.buildingName || '',
          organization.street || '',
          organization.location || '',
          organization.city || '',
          organization.district || '',
          organization.state || ''
        ].filter(part => part && part.trim() !== '');
        const pincode = organization.pincode && organization.pincode.trim() !== '' ? ` - ${organization.pincode}` : '';
    
        const address = addressParts.join(', ') + pincode;
          try {
            if (userList !== undefined) {
              const sessionValidation = await ViderWhatsappSessions.findOne({
                where: { userId: userList, status: 'ACTIVE' },
              });
              if (sessionValidation) {
                const adminUserDetails = await getUserDetails(userList);

                const { full_name: userFullName, mobile_number: userPhoneNumber, id, organization_id } = adminUserDetails;
                const key = 'DSC_ISSUED_WHATSAPP';
                const whatsappMessageBody = `
    Hi ${userFullName}
    DSC of ${holderName} has been issued to ${personName} by ${userName}
 
   We hope this helps!`;
                ;
                await sendWhatsAppTextMessage(
                  `91${userPhoneNumber}`,
                  whatsappMessageBody,
                  organization_id,
                  title,
                  id,
                  key,
                );
              }

            }
          } catch (error) {
            console.error('Error sending User WhatsApp notification:', error);
          }
          for (let i = 0; i < user_list.length; i++) {
            const fullName = user_list[i]?.full_name;
            const data = {
              ...getAdminData(
                fullName,
                holderName,
                expiryDateFormatted,
                personName,
                userName,
                date
              ),
              adress: address,
              phoneNumber: organization?.mobileNumber,
              mail: organization?.email,
              legalName: organization?.tradeName || organization?.legalName,
            };
            const mailOptions = {
              id: user_list[i]?.id,
              key: 'DSC_ACTIVITY_ISSUE_MAIL',
              data: data,
              email: user_list[i]?.email,
              filePath: 'dsc-activity-issue',
              subject: 'Dsc token issued | Vider',
            };
            await sendnewMail(mailOptions);

            if (whatsappCheck) {
              const templateName = 'dsc_issue_client';
              // const dscMobileNumber = `91${mobileNumber}`;
              const dscMobileNumber = fullMobileNumberWithCountry(mobileNumber, countryCode);

              if (dscMobileNumber && dscMobileNumber !== null) {
                const whatsappOptions = {
                  to: dscMobileNumber,
                  name: templateName,
                  header: [
                    {
                      type: 'text',
                      text: holderName,
                    },
                  ],
                  body: [holderName, expiryDateFormatted, personName, userName],
                  title: 'DSC Issue',
                  userId: logInUser,
                  orgId: orgId,
                  key: 'DSC_ISSUE_WHATSAPP',
                };
                await sendClientWhatsAppTemplateMessage(whatsappOptions);
              }
            }
          }

          //client Mail Notification
          const key1 = 'DSC_ISSUE_MAIL';
          if (emailCheck && key1 === 'DSC_ISSUE_MAIL') {
            const addressParts = [
              orgdetails.buildingNo || '',
              orgdetails.floorNumber || '',
              orgdetails.buildingName || '',
              orgdetails.street || '',
              orgdetails.location || '',
              orgdetails.city || '',
              orgdetails.district || '',
              orgdetails.state || ''
            ].filter(part => part && part.trim() !== '');

            const pincode = orgdetails.pincode && orgdetails.pincode.trim() !== '' ? ` - ${orgdetails.pincode}` : '';

            const address = addressParts.join(', ') + pincode;

            const dscHolderName = dscRegister.holderName;

            const mailOptions = {
              id: logInUser,
              key: 'DSC_ISSUE_MAIL',
              email: dscRegister?.email,
              clientMail: 'ORGANIZATION_CLIENT_EMAIL',
              data: {
                dscHolderName: dscHolderName,
                dscIssueTo: personName,
                tradeName: orgdetails?.tradeName,
                legalName: orgdetails?.tradeName || orgdetails?.legalName,
                adress: address,
                phoneNumber: orgdetails?.mobileNumber,
                mail: orgdetails?.email,
                userId: event.entity['userId'],
              },

              filePath: 'client-dsc-issued',
              subject: 'Issue of your DSC Token',
            };

            const orgPreferences: any = await OrganizationPreferences.findOne({
              where: { organization: orgId },
            });
            const clientPreferences = orgPreferences?.clientPreferences?.email;

            if (clientPreferences && clientPreferences[key1]) {
              await sendnewMail(mailOptions);
            }
          }
        } else {
          const key = 'DSC_RECEIVED_PUSH';
          const title = 'DSC received';
          const body = `DSC of <strong>${holderName}</strong> has been received by <strong>${userName}</strong>.`;
          // insertINTOnotification(title, body, userList, orgId);
          insertINTONotificationUpdate(title, body, userList, orgId, key);
          const organization = await Organization.findOne({ id: orgId });


        const addressParts = [
          organization.buildingNo || '',
          organization.floorNumber || '',
          organization.buildingName || '',
          organization.street || '',
          organization.location || '',
          organization.city || '',
          organization.district || '',
          organization.state || ''
        ].filter(part => part && part.trim() !== '');
        const pincode = organization.pincode && organization.pincode.trim() !== '' ? ` - ${organization.pincode}` : '';
    
        const address = addressParts.join(', ') + pincode;
          try {
            if (userList !== undefined) {
              const sessionValidation = await ViderWhatsappSessions.findOne({
                where: { userId: userList, status: 'ACTIVE' },
              });
              if (sessionValidation) {
                const adminUserDetails = await getUserDetails(userList);

                const { full_name: userFullName, mobile_number: userPhoneNumber, id, organization_id } = adminUserDetails;
                const key = 'DSC_RECEIVED_WHATSAPP';
                const whatsappMessageBody = `
    Hi ${userFullName}
    DSC of ${holderName} has been received from ${personName} by ${userName}
 
   We hope this helps!`;
                await sendWhatsAppTextMessage(
                  `91${userPhoneNumber}`,
                  whatsappMessageBody,
                  organization_id,
                  title,
                  id,
                  key,
                );
              }

            }
          } catch (error) {
            console.error('Error sending User WhatsApp notification:', error);
          }
          if (user_list != undefined) {
            for (let i = 0; i < user_list.length; i++) {
              const fullName = user_list[i]?.full_name;
              const data = {
                ...getAdminData(
                  fullName,
                  holderName,
                  expiryDateFormatted,
                  personName,
                  userName,
                  date
                ),
                adress: address,
                phoneNumber: organization?.mobileNumber,
                mail: organization?.email,
                legalName: organization?.tradeName || organization?.legalName,
              };
              const mailOptions = {
                id: user_list[i]?.id,
                key: 'DSC_ACTIVITY_RECEIVED_MAIL',
                data: data,
                email: user_list[i]?.email,
                filePath: 'dsc-activity-received',
                subject: 'Dsc token received | Vider',
              };
              await sendnewMail(mailOptions);
            }
          }

          if (whatsappCheck) {
            const templateName = 'dsc_receive_client';
            // const dscMobileNumber = `91${mobileNumber}`;
            const dscMobileNumber = fullMobileNumberWithCountry(mobileNumber, countryCode);

            if (dscMobileNumber && dscMobileNumber !== null) {
              const whatsappOptions = {
                to: dscMobileNumber,
                name: templateName,
                header: [
                  {
                    type: 'text',
                    text: holderName,
                  },
                ],
                body: [holderName, expiryDateFormatted, personName, userName],
                title: 'Dsc Receive',
                userId: logInUser,
                orgId: orgId,
                key: 'DSC_RECEIVE_WHATSAPP',
              };
              await sendClientWhatsAppTemplateMessage(whatsappOptions);
            }
          }

          //Mail Notification
          const key1 = 'DSC_RECEIVE_MAIL';

          if (emailCheck && key1 === 'DSC_RECEIVE_MAIL') {
            const addressParts = [
              orgdetails.buildingNo || '',
              orgdetails.floorNumber || '',
              orgdetails.buildingName || '',
              orgdetails.street || '',
              orgdetails.location || '',
              orgdetails.city || '',
              orgdetails.district || '',
              orgdetails.state || ''
            ].filter(part => part && part.trim() !== '');

            const pincode = orgdetails.pincode && orgdetails.pincode.trim() !== '' ? ` - ${orgdetails.pincode}` : '';

            const address = addressParts.join(', ') + pincode;
            const dscHolderName = dscRegister.holderName;

            const mailOptions = {
              id: logInUser,
              key: 'DSC_RECEIVE_MAIL',
              email: dscRegister?.email,
              clientMail: 'ORGANIZATION_CLIENT_EMAIL',
              data: {
                dscHolderName: dscHolderName,
                dscIssueTo: personName,
                legalName: orgdetails?.tradeName || orgdetails?.legalName,
                adress: address,
                phoneNumber: orgdetails?.mobileNumber,
                mail: orgdetails?.email,
                userId: event.entity['userId'],
              },

              filePath: 'client-dsc-receive',
              subject: 'Receipt of your DSC Token',
            };
            const orgPreferences: any = await OrganizationPreferences.findOne({
              where: { organization: orgId },
            });
            const clientPreferences = orgPreferences?.clientPreferences?.email;
            if (clientPreferences && clientPreferences[key1]) {
              await sendnewMail(mailOptions);
            }
          }
        }
      }
    }
  }
}
