import Client from 'src/modules/clients/entity/client.entity';
import { User } from 'src/modules/users/entities/user.entity';
import { AfterLoad, BaseEntity, Column, Entity, JoinColumn, ManyToOne, OneToOne, PrimaryGeneratedColumn } from 'typeorm';
import Storage from '../storage/storage.entity';
import ClientGroup from '../client-group/client-group.entity';
import DocumentsData from '../document-in-out/entity/documents-data.entity';

@Entity()
class Kyb extends BaseEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  documentName: string;

  @Column()
  documentNumber: string;

  // @Column()
  // attachment: string;

  @ManyToOne(() => Client, (client) => client.kyb, { onDelete: 'SET NULL' })
  client: Client;

  @ManyToOne(() => ClientGroup, (clientGroup) => clientGroup.kyb, { onDelete: 'SET NULL' })
  clientGroup: ClientGroup;

  @ManyToOne(() => User, (user) => user.kyb)
  user: User;

  // attachmentUrl: string;

  @OneToOne(() => Storage, (storage) => storage.kyb, { cascade: true })
  storage: Storage;

  @ManyToOne(() => DocumentsData, (documentsData) => documentsData.kyb)
  documentsData: DocumentsData;

  // @AfterLoad()
  // renderUrl() {
  //   if (this.attachment) {
  //     this.attachmentUrl = `${process.env.AWS_BASE_URL}/${this.attachment}`;
  //   }
  // }
}

export default Kyb;