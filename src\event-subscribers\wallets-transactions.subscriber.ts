import {
    Connection,
    EntitySubscriberInterface,
    EventSubscriber,
    InsertEvent,
    UpdateEvent,
    getManager,
  } from 'typeorm';
  import { User } from 'src/modules/users/entities/user.entity';
  import { getAdminIDsBasedOnOrganizationId, insertINTOnotification } from 'src/utils/re-use';
import { Type, WalletsTransactions } from 'src/modules/wallet/entities/wallets-transactions.entity';
import { Wallets } from 'src/modules/wallet/entities/wallets.entity';
  
  @EventSubscriber()
  export class WalletsTransactionsSubscriber implements EntitySubscriberInterface<WalletsTransactions> {
    constructor(private readonly connection: Connection) {
      connection.subscribers.push(this);
    }
  
    listenTo() {
      return WalletsTransactions;
    }

    async afterInsert(event: InsertEvent<WalletsTransactions>) {


        const wallet = await Wallets.findOne({
            where: {
                organization: event.entity.organization_id,
            }
        });
        let balanceNumeric: any;
        if (typeof (wallet.balance) === 'string') {
            balanceNumeric = parseFloat(wallet.balance);
        };
        try {
            if (event.entity.type === Type.CREDIT) {
                wallet.balance = balanceNumeric + event.entity.amount;
            } else {
                wallet.balance = balanceNumeric - event.entity.amount;
            };
            await wallet.save();

        } catch (error) {
            console.error("Error updating wallet balance:", error);
        };
        // console.log(event.entity)
        
    };
  
   
  }