import {
  BadRequestException,
  ConflictException,
  Inject,
  Injectable,
  InternalServerErrorException,
  UnprocessableEntityException,
  forwardRef,
} from '@nestjs/common';
import axios from 'axios';
import { User } from 'src/modules/users/entities/user.entity';
import AuthToken, { AuthTokenType } from 'src/modules/ondrive-storage/auth-token.entity';
import FindOneDriveStorageDto from 'src/modules/ondrive-storage/find-onedrive-storage.dto';
import { IExisting, IUpload, StorageService } from 'src/modules/client-panel/services/storage.service';
import Storage, { StorageType } from 'src/modules/storage/storage.entity';
import { In, createQueryBuilder } from 'typeorm';
import Client from 'src/modules/clients/entity/client.entity';
import { v4 as uuidv4 } from 'uuid';
import * as moment from 'moment';
import { StorageSystem } from 'src/modules/organization/entities/organization.entity';
import Task from 'src/modules/tasks/entity/task.entity';
import { AttachmentsService } from './attachments.service';
import CollectData from 'src/modules/collect-data/collect-data.entity';

@Injectable()
export class OneDriveStorageService {
  constructor(
    @Inject(forwardRef(() => StorageService))
    private storageService: StorageService,
    @Inject((forwardRef(() => AttachmentsService)))
    private attachmentsService: AttachmentsService,
  ) { }

  async saveToken(userId: number, body: any) {
    if (!body.code) {
      throw new BadRequestException('Code is required');
    };

    const user = await User.findOne({
      where: { id: userId },
      relations: ['organization'],
    });


    let existingToken = await AuthToken.findOne({
      where: { organizationId: user.organization.id },
    });

    if (existingToken) {
      await existingToken.remove();
    }

    const data = new URLSearchParams({
      client_id: process.env.ONE_DRIVE_CLIENT_ID,
      scope: process.env.ONE_DRIVE_SCOPE,
      redirect_uri: `${process.env.WEBSITE_URL}/onedrive-auth`,
      client_secret: process.env.ONE_DRIVE_CLIENT_SECRET,
      code: body.code,
      grant_type: process.env.ONE_DRIVE_GRANT_TYPE_AUTH,
    });

    try {
      let res = await axios({
        method: 'POST',
        url: process.env.ONE_DRIVE_AUTH_TOKEN_URL,
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
        data,
      });

      let token = new AuthToken();
      token.accessToken = res.data.access_token;
      token.refreshToken = res.data.refresh_token;
      token.type = AuthTokenType.MICROSFT;
      token.organizationId = user.organization.id;
      await token.save();
      return token;
    } catch (err) {
      console.log('error', err);
      throw new InternalServerErrorException(err);
    }
  }

  async getItems(userId: number, query: FindOneDriveStorageDto) {
    let user = await User.findOne({
      where: { id: userId },
      relations: ['organization'],
    });

    let token = await AuthToken.findOne({
      where: {
        organizationId: user.organization.id,
        type: AuthTokenType.MICROSFT,
      },
    });

    const searchParams = new URLSearchParams({
      client_id: process.env.ONE_DRIVE_CLIENT_ID,
      scope: process.env.ONE_DRIVE_SCOPE,
      response_type: process.env.ONE_DRIVE_RESPONSE_TYPE,
      redirect_uri: `${process.env.WEBSITE_URL}/onedrive-auth`,
    });

    if (!token) {
      throw new UnprocessableEntityException({
        code: 'NO_TOKEN',
        message: 'User has not been authenticated with Microsoft',
        authorizationUrl: `${process.env.ONE_DRIVE_AUTH_URL}?${searchParams.toString()}`,
      });
    }

    try {
      let response = await this.getData(token, query);
      return response;
    } catch (err) {
      let error = err.response.data?.error;
      if (error.code === 'InvalidAuthenticationToken') {
        await this.refreshToken(token);
        let response = await this.getData(token, query);
        return response;
      }
      throw new InternalServerErrorException(error);
    }
  }

  async uploadFile(args: IUpload) {
    const { file, body, userId } = args;
    const { buffer, mimetype } = file;
    const user = await User.findOne({
      where: { id: userId },
      relations: ['organization'],
    });


    const token = await AuthToken.findOne({
      where: {
        organizationId: user.organization.id,
        type: AuthTokenType.MICROSFT,
      },
    });

    if (!token) {
      throw new UnprocessableEntityException('No authentication token found. Please authenticate with OneDrive.');
    }

    let storage = new Storage();
    storage.fileType = file.mimetype;
    storage.fileSize = file.size;
    storage.name = file.originalname;
    storage.type = StorageType.FILE;
    storage.uid = uuidv4();
    storage.storageSystem = StorageSystem.MICROSOFT;
    storage.user = user;

    let existingFile = await this.existing({
      name: file.originalname,
      parent: body.folderId,
      type: body.type,
      clientId: body.clientId,
      orgId: user.organization.id,
      roomId: body.roomId,
    });
    if (existingFile) {
      throw new ConflictException('File with this name already exists');
    }

    const { storageLimit, freeSpace } = await this.storageService.getOrgStorage(userId);
    if (!(freeSpace - +file.size > 0)) {
      throw new ConflictException(
        'We regret to inform you that the storage limit for your organization has been exceeded. As a result, no further data can be stored until the storage usage is reduced or additional storage capacity is allocated.',
      );
    }

    let key: string;
    let upload: any;


    try {

      if (body.type === 'client') {
        let client = await Client.findOne({ where: { id: body.clientId } });
        key = `root:/Atom/Clients/${client.displayName}/${file.originalname}:`;
        upload = await this.upload(buffer, key, mimetype, token, file);
        storage.client = client;
      };

      if (body.type === 'organization') {
        key = `root:/Atom/Organization Storage/${file.originalname}:`;
        upload = await this.upload(buffer, key, mimetype, token, file);
        storage.organization = user.organization;
      };

    } catch (err) {
      let error = err?.response?.data?.error;
      if (error.code === 'InvalidAuthenticationToken') {
        await this.refreshToken(token);
        upload = await this.upload(buffer, key, mimetype, token, file);
      } else if (error.code === 'quotaLimitReached') {
        throw new ConflictException({
          code: 'NO_STORAGE',
          message: error.message,
        });
      }
    };

    storage.file = upload.file;
    storage.webUrl = upload.webUrl;
    storage.downloadUrl = upload.downloadUrl;
    storage.fileId = upload.fileId;
    storage.authId = user.organization.id;


    if (body.folderId) {
      let folder = await Storage.findOne({ where: { uid: body.folderId } });
      storage.parent = folder;
    };
    await storage.save();
    return storage;
  };

  async attachementsUpload(args: IUpload) {
    const { file, body, userId } = args;

    const { buffer, mimetype } = file;
    const user = await User.findOne({
      where: { id: userId },
      relations: ['organization'],
    });

    let token = await AuthToken.findOne({
      where: {
        organizationId: user.organization.id,
        type: AuthTokenType.MICROSFT,
      },
    });
    const searchParams = new URLSearchParams({
      client_id: process.env.ONE_DRIVE_CLIENT_ID,
      scope: process.env.ONE_DRIVE_SCOPE,
      response_type: process.env.ONE_DRIVE_RESPONSE_TYPE,
      redirect_uri: `${process.env.WEBSITE_URL}/onedrive-auth`,
    });

    if (!token) {
      throw new UnprocessableEntityException({
        code: 'NO_TOKEN',
        message: 'User has not been authenticated with Microsoft',
        authorizationUrl: `${process.env.ONE_DRIVE_AUTH_URL}?${searchParams.toString()}`,
      });
      // throw new UnprocessableEntityException('No authentication token found. Please authenticate OneDrive.');
    }

    // try {
    // let user = await User.findOne({
    //   where: { id: userId },
    //   relations: ['organization'],
    // });

    let existingFile = await this.existing({
      name: file.originalname,
      parent: body.folderId,
      type: body.type,
      clientId: body.clientId,
      orgId: user.organization.id,
      roomId: body.roomId,
    });

    if (body.type !== 'chat' && existingFile) {
      throw new ConflictException('File with this name already exists');
    }
    const { storageLimit, freeSpace } = await this.storageService.getOrgStorage(userId);
    if (!(freeSpace - +file.size > 0)) {
      throw new ConflictException(
        'We regret to inform you that the storage limit for your organization has been exceeded. As a result, no further data can be stored until the storage usage is reduced or additional storage capacity is allocated.',
      );
    }

    let key: string;
    let upload: any;

    try {
      if (body.type === 'client') {
        let client = await Client.findOne({ where: { id: body.clientId } });
        key = `root:/Atom/Clients/${client.displayName}/${file.originalname}:`;

        upload = await this.upload(buffer, key, mimetype, token, file);
      }

      if (body.type === 'organization') {
        key = `root:/Atom/Organization Storage/${file.originalname}:`;
        upload = await this.upload(buffer, key, mimetype, token, file);
      }

      if (body.type === 'chat') {
        key = `root:/Atom/Organization Storage/chat-${body.roomId}-${moment().valueOf()}/${file.originalname}:`;
        upload = await this.upload(buffer, key, mimetype, token, file);
      }

    } catch (err) {
      let error = err?.response?.data?.error;
      if (error.code === 'InvalidAuthenticationToken') {
        await this.refreshToken(token);
        upload = await this.upload(buffer, key, mimetype, token, file);
      } else if (error.code === 'quotaLimitReached') {
        throw new ConflictException({
          code: 'NO_STORAGE',
          message: error.message,
        });
      }
    }


    return {
      key,
      upload: upload.file,
      webUrl: upload.webUrl,
      downloadUrl: upload["@microsoft.graph.downloadUrl"],
      fileId: upload.id,
      fileSize: file.size,
      fileType: file.mimetype,
      clientId: body.clientId,
      name: file.originalname,
      storageSystem: StorageSystem.MICROSOFT
    };
  }

  async upload(buffer: Buffer, key: string, contentType = '', token, file) {
    const uploadUrl = `${process.env.ONE_DRIVE_URL}/${key}/content`;
    const response = await axios({
      method: 'PUT',
      url: uploadUrl,
      headers: {
        'Authorization': `Bearer ${token.accessToken}`,
        'Content-Type': 'application/octet-stream',
      },
      data: file.buffer,
    });
    const fileDetails = await this.getFileDetailsById(response?.data?.id, token);
    return { ...response.data, file: fileDetails?.thumbnails[0]?.large?.url };
  };

  async existing(props: IExisting) {
    const { name, parent, type, orgId, clientId, roomId } = props;

    let existing = createQueryBuilder(Storage, 'storage');

    let where = `storage.name = :name`;

    if (type === 'client') {
      existing.leftJoin('storage.client', 'client');
      where += ` and client.id = :clientId`;
    }

    if (type === 'organization') {
      existing.leftJoin('storage.organization', 'organization');
      where += ` and organization.id = :orgId`;
    }
    if (type === 'chat') {
      existing.leftJoin('storage.room', 'room');
      where += ` and room.id = :roomId`;
    }

    if (parent) {
      existing.leftJoin('storage.parent', 'parent');
      where += ` and parent.uid = :parent`;
    }

    if (!parent) {
      where += ` and storage.parent is null`;
    }

    existing.where(`(${where})`, { name, parent, clientId, orgId, roomId });

    let result = await existing.getOne();
    return Boolean(result);
  }

  async getData(token: AuthToken, query: FindOneDriveStorageDto) {
    let res = await axios({
      method: 'GET',
      url: `${process.env.ONE_DRIVE_URL}/${query.id}/children`,
      headers: {
        'Authorization': `Bearer ${token.accessToken}`,
        'Content-Type': 'application/json',
      },
      params: { expand: 'thumbnails' },
    });

    return res.data;
  }

  async refreshToken(token: AuthToken) {
    try {
      const data = new URLSearchParams({
        client_id: process.env.ONE_DRIVE_CLIENT_ID,
        client_secret: process.env.ONE_DRIVE_CLIENT_SECRET,
        scope: process.env.ONE_DRIVE_SCOPE,
        redirect_uri: `${process.env.WEBSITE_URL}/onedrive-auth`,
        grant_type: process.env.ONE_DRIVE_GRANT_TYPE_REFRESH,
        refresh_token: token.refreshToken,
      });

      let res = await axios({
        method: 'POST',
        url: process.env.ONE_DRIVE_AUTH_TOKEN_URL,
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
        data,
      });

      token.accessToken = res.data.access_token;
      token.refreshToken = res.data.refresh_token;
      await token.save();
    } catch (err) {
      console.error(err);
      let error = err?.response?.data?.error;
      console.log('refresh, error', error);
      throw new InternalServerErrorException(error);
    }
  }

  async reAuthorize() {
    const searchParams = new URLSearchParams({
      client_id: process.env.ONE_DRIVE_CLIENT_ID,
      scope: process.env.ONE_DRIVE_SCOPE,
      response_type: process.env.ONE_DRIVE_RESPONSE_TYPE,
      redirect_uri: `${process.env.WEBSITE_URL}/onedrive-auth`,
    });

    return `${process.env.ONE_DRIVE_AUTH_URL}?${searchParams.toString()}`;
  }

  async getFileDetailsById(fileId: string, token: any) {
    try {
      const url = `${process.env.ONE_DRIVE_URL}/${fileId}?expand=thumbnails`;
      const headers = {
        'Authorization': `Bearer ${token.accessToken}`,
        'Content-Type': 'application/json'
      };

      const response = await axios.get(url, { headers });

      return response.data;
    } catch (error) {
      console.error('Error:', error.response.data);
      throw new InternalServerErrorException('Failed to get file details');
    }
  };

  async addAttachments(taskId: number, files: Express.Multer.File[], userId: number) {
    try {
      let task = await Task.findOne({
        where: { id: taskId },
        relations: ['client', 'category', 'subCategory','clientGroup'],
      });

      let user = await User.findOne({
        where: { id: userId },
        relations: ['organization'],
      });

      let token = await AuthToken.findOne({
        where: {
          organizationId: user.organization.id,
          type: AuthTokenType.MICROSFT,
        },
      });


      let taskStorage = await this.attachmentsService.existingClientTaskStorage(task);
      let taskClinetFolder = 'root:/Atom/Clients/';
      if (task?.client?.displayName) {
        taskClinetFolder += `${task.client.displayName}/`;
      }
      if (task.financialYear) {
        taskClinetFolder += `${task.financialYear}/`;
      };
      if (task.category?.name) {
        taskClinetFolder += `${task.category.name}/`;
      };
      if (task.subCategory?.name) {
        taskClinetFolder += `${task.subCategory.name}/`;
      }
      if (task?.name) {
        taskClinetFolder += `${task.name}/`;
      };

      let taskAttachments: Storage[] = [];

      for (let file of files) {
        const { buffer, mimetype, originalname, size } = file;

        const { freeSpace, } = await this.storageService.getOrgStorage(userId);

        if (!((freeSpace - (file.size)) >= 0)) {
          throw new ConflictException('We regret to inform you that the storage limit for your organization has been exceeded. As a result, no further data can be stored until the storage usage is reduced or additional storage capacity is allocated.');
        };


        // let key = `storage/tasks/${taskId}-${moment().valueOf()}/${file.originalname}`;
        // let key = `root:/organization-storage/chat-${body.roomId}-${moment().valueOf()}/${file.originalname}:`;
        let key = `${taskClinetFolder}${file.originalname}:`


        let upload: any;
        try {
          upload = await this.upload(buffer, key, mimetype, token, file);

        } catch (err) {
          let error = err?.response?.data?.error;
          if (error.code === 'InvalidAuthenticationToken') {
            await this.refreshToken(token);
            upload = await this.upload(buffer, key, mimetype, token, file);
          } else if (error.code === 'quotaLimitReached') {
            throw new ConflictException({
              code: 'NO_STORAGE',
              message: error.message,
            });
          }
        }

        let storage = new Storage();
        storage.name = originalname;
        storage.file = upload.file;
        storage.webUrl = upload.webUrl;
        storage.downloadUrl = upload.downloadUrl;
        storage.fileId = upload.fileId;
        storage.authId = user.organization.id;
        storage.task = task;
        storage.fileSize = size;
        storage.fileType = mimetype;
        storage.client = task?.client;
        storage.clientGroup = task?.clientGroup;
        storage.type = StorageType.FILE;
        storage.parent = taskStorage;
        storage.storageSystem = StorageSystem.MICROSOFT;
        storage.user = user;
        taskAttachments.push(storage);
      }

      await Storage.save(taskAttachments);

      return {
        success: true,
      };
    } catch (err) {
      console.log(err);
      throw new InternalServerErrorException(err);
    }
  };

  async collectDataAddAttc(
    origin: string,
    collectId: any,
    taskId: number,
    files: Express.Multer.File[],
  ) {
    try {
      let task = await Task.findOne({
        where: { id: taskId },
        relations: ['client', 'category', 'subCategory', 'organization','clientGroup'],
      });

      let token = await AuthToken.findOne({
        where: {
          organizationId: task.organization.id
        }
      })


      let taskStorage = await this.attachmentsService.existingClientTaskStorage(task);
      let taskClinetFolder = 'root:/Atom/Clients/';
      if (task?.client?.displayName) {
        taskClinetFolder += `${task.client.displayName}/`;
      }
      if (task.financialYear) {
        taskClinetFolder += `${task.financialYear}/`;
      };
      if (task.category?.name) {
        taskClinetFolder += `${task.category.name}/`;
      };
      if (task.subCategory?.name) {
        taskClinetFolder += `${task.subCategory.name}/`;
      }
      if (task?.name) {
        taskClinetFolder += `${task.name}/`;
      };

      let taskAttachments: Storage[] = [];
      const collectData = await CollectData.findOne({ where: { uid: collectId } })

      for (let file of files) {
        const { buffer, mimetype, originalname } = file;
        let key = `${taskClinetFolder}${file.originalname}:`
        let upload: any;

        try {
          upload = await this.upload(buffer, key, mimetype, token, file);

        } catch (err) {
          let error = err?.response?.data?.error;
          if (error.code === 'InvalidAuthenticationToken') {
            await this.refreshToken(token);
            upload = await this.upload(buffer, key, mimetype, token, file);
          } else if (error.code === 'quotaLimitReached') {
            throw new ConflictException({
              code: 'NO_STORAGE',
              message: error.message,
            });
          }
        }

        let storage = new Storage();
        storage.name = originalname;
        storage.file = upload.file;
        storage.webUrl = upload.webUrl;
        storage.downloadUrl = upload.downloadUrl;
        storage.authId = task.organization.id;
        storage.fileId = upload.fileId;
        storage.task = task;
        storage.fileType = mimetype;
        storage.fileSize = file.size;
        storage.client = task.client;
        storage.clientGroup = task?.clientGroup;
        storage.type = StorageType.FILE;
        storage.parent = taskStorage;
        storage.storageSystem = StorageSystem.MICROSOFT;
        storage.collectId = collectData?.id;
        storage.origin = origin;
        taskAttachments.push(storage);
      };

      await Storage.save(taskAttachments);
      return {
        success: true,
      };
    } catch (err) {
      console.log(err);
      throw new InternalServerErrorException(err);
    }
  }

  async checkCopyStatus(statusUrl: string, userId: number) {
    const user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
    const token = await AuthToken.findOne({ where: { organizationId: user.organization.id } });


    try {
      let status;
      while (true) {
        const response = await axios.get(statusUrl);
        status = response.data;

        if (status.status === 'completed') {
          // return status;
          const fileDetails = await this.getFileDetailsById(status?.resourceId, userId)
          return fileDetails; // This contains the new file details
        } else if (status.status === 'failed') {
          throw new Error('Copy operation failed');
        } else {
          // If the status is not completed or failed, wait for a while before checking again
          await new Promise(resolve => setTimeout(resolve, 2000)); // 2 seconds
        }
      }
    } catch (err) {
      const error = err?.response?.data?.error;
      if (error.code === 'unauthenticated') {
        await this.refreshToken(token);
        await this.checkCopyStatus(statusUrl, userId);
      }

      console.error('Error checking copy status:', err?.response);
    }
  };

  async copyFile(fileId: string, userId: number, destinationFolderId?: string, newFileName?: string) {
    const url = `${process.env.ONE_DRIVE_URL}/${fileId}/copy`;
    const user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
    const token = await AuthToken.findOne({ where: { organizationId: user.organization.id } });

    const headers = {
      'Authorization': `Bearer ${token.accessToken}`,
      'Content-Type': 'application/json'
    };

    let parentFolderId = destinationFolderId;
    if (!destinationFolderId) {
      try {
        const fileDetails = await this.getFileDetailsById(fileId, userId);
        parentFolderId = fileDetails.parentReference.id; // Get the parent folder ID from original file
      } catch (error) {
        console.error('Error retrieving file details:', error.response ? error.response.data : error.message);
        return;
      }
    };

    const body = {
      parentReference: {
        id: parentFolderId
      },
      // name: newFileName // Optional: if you want to rename the file
    };

    try {
      const response = await axios.post(url, body, { headers });
      const statusUrl = response.headers['location'];
      if (statusUrl) {
        let copyStatus = await this.checkCopyStatus(statusUrl, userId);
        return copyStatus;
      } else {
        console.error('No status URL returned');
      }
    } catch (error) {
      console.error('Error copying file:', error.response ? error.response.data : error.message);
    }
  };

  async deleteOneDriveFile(userId: number, fileId: string) {
    if (!fileId) return null;
    const user = await User.findOne({
      where: { id: userId },
      relations: ['organization'],
    });

    if (!user) {
      throw new BadRequestException('User not found');
    }

    const token = await AuthToken.findOne({
      where: {
        organizationId: user.organization.id,
        type: AuthTokenType.MICROSFT,
      },
    });

    if (!token) {
      throw new UnprocessableEntityException('No authentication token found. Please authenticate with OneDrive.');
    }

    try {
      const url = `${process.env.ONE_DRIVE_URL}/${fileId}`;
      await axios({
        method: 'DELETE',
        url,
        headers: {
          Authorization: `Bearer ${token.accessToken}`,
          'Content-Type': 'application/json',
        },
      });
      return { success: true };
    } catch (err) {
      const error = err?.response?.data?.error;
      if (error?.code === 'InvalidAuthenticationToken') {
        await this.refreshToken(token);
        await this.deleteOneDriveFile(userId, fileId);
      } else if (error?.code === 'itemNotFound') {
        return { success: true };
      } else {
        throw new InternalServerErrorException(error?.message || 'Failed to delete file');
      }
    }
  };
}

