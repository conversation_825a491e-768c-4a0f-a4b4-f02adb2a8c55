import Client from 'src/modules/clients/entity/client.entity';
import { User } from 'src/modules/users/entities/user.entity';
import {
  BaseEntity,
  Column,
  CreateDateColumn,
  Entity,
  ManyToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import Task from 'src/modules/tasks/entity/task.entity';
import { Organization } from '../organization/entities/organization.entity';

export enum BudgetedHourStatus {
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE',
}

@Entity('budgeted_hours')
class BudgetedHours extends BaseEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ default: BudgetedHourStatus.ACTIVE })
  status: BudgetedHourStatus;

  @CreateDateColumn()
  createdAt: string;

  @UpdateDateColumn()
  updatedAt: string;

  @Column({ type: 'bigint' })
  budgetedHours: number;

  @ManyToOne(() => Organization, (organization) => organization.budgetedHours)
  organization: Organization;

  @ManyToOne(() => Task, (task) => task.taskBudgetedHours, { eager: true })
  task: Task;

  @ManyToOne(() => User, (user) => user.taskBudgetedHours, { eager: true })
  user: User;

  @ManyToOne(() => Client, (client) => client.budgetedHours)
  client: Client;
}

export default BudgetedHours;
