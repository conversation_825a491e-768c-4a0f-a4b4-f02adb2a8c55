import { IsEnum, IsNotEmpty, IsNumber, IsOptional, Matches } from 'class-validator';
import Label from 'src/modules/labels/label.entity';
import { UserStatus } from 'src/modules/users/entities/user.entity';
import { CategoryEnum, SubCategoryEnum } from './types';
import { YesOrNoType } from 'src/modules/gstr-register/entity/gstr-register.entity';

export class UpdateClientDto {
  @IsOptional()
  category: CategoryEnum;

  @IsOptional()
  image: string;

  @IsOptional()
  @IsEnum(UserStatus)
  status: UserStatus;

  @IsOptional()
  subCategory: SubCategoryEnum;

  // @IsOptional()
  // incomeTaxAudit : YesOrNoType;

  // @IsOptional()
  // gstAnnualForm: YesOrNoType;

  @IsOptional()
  @Matches(/^[^<>:"\/\\|?*\.]*$/, {
    message: 'Display name cannot contain <, >, :, ", /, \\, |, ?, *, or .',
  })
  displayName: string;

  @IsOptional()
  clientManager: number;

  @IsOptional()
  clientManagers: number[];

  @IsOptional()
  clientGroupManagers: number[];

  @IsOptional()
  mobileNumber: string;

  @IsOptional()
  alternateMobileNumber: string;

  @IsOptional()
  email: string;

  @IsOptional()
  dob: string;

  @IsOptional()
  authorizedPerson: string;

  @IsOptional()
  designation: string;

  @IsOptional()
  gstVerified: boolean;

  @IsOptional()
  gstNumber: string;

  @IsOptional()
  tradeName: string;

  @IsOptional()
  legalName: string;

  @IsOptional()
  constitutionOfBusiness: string;

  @IsOptional()
  middleName: string;

  @IsOptional()
  placeOfSupply: string;

  @IsOptional()
  gstRegistrationDate: string;

  @IsOptional()
  panVerified: boolean;

  @IsOptional()
  panNumber: string;

  @IsOptional()
  tanNumber: string;

  @IsOptional()
  firstName: string;

  @IsOptional()
  lastName: string;

  @IsOptional()
  fullName: string;

  @IsOptional()
  buildingName: string;

  @IsOptional()
  street: string;

  @IsOptional()
  city: string;

  @IsOptional()
  state: string;

  @IsOptional()
  pincode: string;

  @IsOptional()
  labels: Label[];

  // @IsOptional()
  // localDirectoryPath: string;
  @IsOptional()
  localDirectoryPath: object[];

  @IsOptional()
  notes: string;

  @IsOptional()
  clientPortalAccess: boolean;

  @IsOptional()
  address: object[];

  @IsOptional()
  issameaddress: boolean;

  @IsOptional()
  clientImage: any;

  @IsOptional()
  clientNumber: string;

  @IsOptional()
  countryCode: string;

  @IsOptional()
  alternateCountryCode: string;
}
