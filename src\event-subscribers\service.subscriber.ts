import {
  Connection,
  EntitySubscriberInterface,
  EventSubscriber,
  InsertEvent,
} from 'typeorm';
import { Service }from '../modules/services/entities/service.entity';

@EventSubscriber()
export class ServiceSubscriber implements EntitySubscriberInterface<Service> {
  constructor(private readonly connection: Connection) {
    connection.subscribers.push(this);
  }

  listenTo() {
    return Service;
  }

  async beforeInsert(event: InsertEvent<Service>) {
  }

  async afterInsert(event: InsertEvent<Service>) {
  }
}
