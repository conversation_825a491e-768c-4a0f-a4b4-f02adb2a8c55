import { Injectable, BadRequestException } from '@nestjs/common';
import { S3 } from 'aws-sdk';

@Injectable()
export class AwsService {
  async upload(buffer: Buffer, key: string, contentType = '') {
    // try {
    const bucketS3 = process.env.AWS_BUCKET_NAME;
    const upload = await this.uploadS3(buffer, bucketS3, key, contentType);
    return upload;
    // } 
    // catch (err) {
    // console.log(err,'errr')
    // throw new BadRequestException(err);
    // }
  }

  async get(key: string) {
    try {
      const bucketS3 = process.env.AWS_BUCKET_NAME;
      const upload = await this.getFileFromS3(bucketS3, key);
      return upload;
    } catch (err) {
      throw new BadRequestException(err);
    }
  }

  async getFileFromS3(bucket, key) {

    const s3 = this.getS3();
    const params = {
      Bucket: bucket,
      Key: key,
    };
    return new Promise((resolve, reject) => {
      s3.getObject(params, (err, data) => {
        if (err) {
          // console.error(err);
          reject(err.message);
        }
        resolve(data);
      });
    });
  }

  async uploadS3(file: Buffer, bucket, key, contentType) {
    const s3 = this.getS3();
    const params = {
      Bucket: bucket,
      Key: key,
      Body: file,
      ContentType: contentType,
    };
    return new Promise((resolve, reject) => {
      s3.upload(params, (err, data) => {
        if (err) {
          console.error(err);
          reject(err.message);
        }
        resolve(data);
      });
    });
  }

  async deleteFile(key: string) {
    if (!key) return null;
    try {
      const s3 = this.getS3();
      const params = {
        Bucket: process.env.AWS_BUCKET_NAME,
        Key: key
      };
      return new Promise((resolve, reject) => {
        s3.deleteObject(params, (err, data) => {
          if (err) {
            console.error(err);
            reject(err.message);
          }
          resolve(data);
          return { success: true }
        })
      })
    } catch (err) {
      console.error(err);
      // throw new BadRequestException(err);
    }
  }

  // New deleteFolder method
  async deleteFolder(folderKey: string) {
    if (!folderKey) return null;

    const s3 = this.getS3();

    // Recursive helper function to delete objects
    const deleteAllObjectsInFolder = async (folderKey: string) => {
      try {
        // Step 1: List all objects within the folder
        const listedObjects = await s3
          .listObjectsV2({
            Bucket: process.env.AWS_BUCKET_NAME,
            Prefix: folderKey,
          })
          .promise();

        if (listedObjects.Contents.length === 0) {
          return;
        }

        // Step 2: Delete listed objects
        const deleteParams = {
          Bucket: process.env.AWS_BUCKET_NAME,
          Delete: { Objects: [] as any[] },  // Array to hold objects to delete
        };

        listedObjects.Contents.forEach(({ Key }) => {
          deleteParams.Delete.Objects.push({ Key });
        });

        await s3.deleteObjects(deleteParams).promise();

        // If there are more objects, recursively delete the next batch
        if (listedObjects.IsTruncated) {
          await deleteAllObjectsInFolder(folderKey);
        }

        return { success: true };
      } catch (err) {
        console.error('Error deleting folder:', err);
        throw new Error(err);
      }
    };

    // Start deletion process
    return deleteAllObjectsInFolder(folderKey);
  }

  getS3() {
    return new S3({
      accessKeyId: process.env.AWS_ACCESS_KEY_ID_VIDER,
      secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY_VIDER,
      region: 'ap-south-1',
    });
  }

  getDest() {
    return new S3({
      accessKeyId: process.env.VIDER_AWS_ACCESS_KEY_ID_VIDER,
      secretAccessKey: process.env.VIDER_AWS_SECRET_ACCESS_KEY_VIDER,
      region: 'ap-south-1',
    });

  }

  async copyS3Object(sourceKey: string, newKey: string) {
    try {
      const bucketS3 = process.env.AWS_BUCKET_NAME;
      const s3 = this.getS3();

      const copyParams = {
        Bucket: bucketS3,
        CopySource: `${bucketS3}/${sourceKey}`,
        Key: newKey
      };
      await new Promise((resolve, reject) => {
        s3.copyObject(copyParams, (err, data) => {
          if (err) {
            console.error(err);
            reject(err.message);
          };
          resolve(data);
        });
      });
      return { success: true, newKey };
    } catch (err) {
      throw new BadRequestException(err);
    }
  };




  getOldS3() {

    return new S3({
      accessKeyId: '********************',
      secretAccessKey: 'cmV1nAN0RjrWah+x8Av+WKe+9UATxk+ZhmYzbY7F',
      region: 'ap-south-1',
    });
  }




  async transferFileToNewAccountIfNotExists(key: string, contentType = '') {
    try {
      // Check if the file already exists in the new bucket
      const newS3 = this.getS3();  // Using the new account's S3 client
      const fileExists = await this.checkIfFileExistsInNewBucket(newS3, key);

      if (fileExists) {
        return { success: false, message: `File already exists in the new bucket: ${key}` };
      }

      // Fetch the file from the old account if it does not exist in the new account
      const oldS3 = this.getOldS3();
      const fileData = await this.getFileFromS3UsingCustomClient(oldS3, key);

      // Upload the file to the new account with the same key and content type
      const uploadResponse = await this.uploadS3(fileData.Body, 'vider-prod-bucket', key, contentType);

      return { success: true, message: 'File successfully transferred to the new bucket', uploadResponse };
    } catch (err) {
      throw new BadRequestException('Error transferring file', err);
    }
  }

  // Check if the file already exists in the new bucket
  async checkIfFileExistsInNewBucket(s3Client, key) {
    const params = {
      Bucket: 'vider-prod-bucket',  // The new bucket name
      Key: key,
    };

    try {
      await s3Client.headObject(params).promise();
      return true;  // File exists
    } catch (err) {
      if (err.code === 'NotFound') {
        return false;  // File does not exist
      }
      throw err;  // Any other error, rethrow it
    }
  };

  // Method to get file from S3 using a custom S3 client
  async getFileFromS3UsingCustomClient(s3Client, key: string) {
    const params = {
      Bucket: 'jss-vider-bucket',  // Bucket name from the old account
      Key: key,  // The key (path) of the file to fetch
    };

    try {
      // Use the S3 client to get the object from the old bucket
      const data = await s3Client.getObject(params).promise();
      return data;  // Returns the file data
    } catch (err) {
      console.error(`Error fetching file from old bucket: ${key}`, err);
      throw new Error(`Error fetching file from old bucket: ${key}, ${err.message}`);
    }
  }



}