import Client from 'src/modules/clients/entity/client.entity';
import { User } from 'src/modules/users/entities/user.entity';
import {
  BaseEntity,
  Column,
  CreateDateColumn,
  Entity,
  JoinTable,
  ManyToMany,
  ManyToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import Task from 'src/modules/tasks/entity/task.entity';
import ClientGroup from '../client-group/client-group.entity';
import { EXPENDITURE_STATUS, ExpenditureStatus } from '../expenditure/dto/types';

export enum TimerStatus {
  STARTED = 'started',
  STOPPED = 'stopped',
  PAUSED = 'paused',
}

export enum LogHourType {
  ALL = 'ALL',
  TASK = 'TASK',
  GENERAL = 'GENERAL',
}

@Entity()
class LogHour extends BaseEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ default: TimerStatus.STARTED })
  status: TimerStatus;

  @Column({ type: 'enum', enum: LogHourType, default: LogHourType.TASK })
  type: LogHourType;

  @Column()
  title: string;

  @Column()
  description: string;

  @Column({ type: 'bigint' })
  startTime: number;

  @Column({ type: 'bigint' })
  endTime: number;

  @Column({ type: 'time' })
  startTimeNew: string;

  @Column({ type: 'time' })
  endTimeNew: string;

  @Column({ type: 'bigint' })
  duration: number;

  @Column({ nullable: true, type: 'datetime' })
  completedDate: string;


  @Column({ type: 'varchar' })
  approvalDescription: string;

  @Column({
    type: 'enum',
    enum: EXPENDITURE_STATUS,
    default: null,
  })
  approvalStatus: EXPENDITURE_STATUS;

  @ManyToOne(() => User, (user) => user.expenditureReviewer)
  reviewer: User;

  @Column()
  reviewedAt: string;

  @Column()
  billable: boolean;

  @Column()
  logHourTitleId: number;

  @ManyToMany(() => User, (user) => user.assignedLogHours)
  @JoinTable()
  managers: User[];

  @ManyToOne(() => Task, (task) => task.taskLogHours, { eager: true })
  task: Task;

  @ManyToOne(() => User, (user) => user.taskLogHours, { eager: true })
  user: User;

  @ManyToOne(() => Client, (client) => client.logHours)
  client: Client;

  @ManyToOne(() => ClientGroup, (clientGroup) => clientGroup.logHours)
  clientGroup: ClientGroup;

  @ManyToOne(() => User, (user) => user.logHoursCreatedBy)
  createdBy: User;

  @CreateDateColumn()
  createdAt: string;

  @UpdateDateColumn()
  updatedAt: string;

  @Column()
  requestedAt: string;
}

export default LogHour;
