import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { NotificationsService } from 'src/notifications/notifications.service';
import { ActivityController } from './activity.controller';
import Activity from './activity.entity';

@Module({
  imports: [TypeOrmModule.forFeature([Activity])],
  controllers: [ActivityController],
  providers: [NotificationsService],
})
export class ActivityModule {}
