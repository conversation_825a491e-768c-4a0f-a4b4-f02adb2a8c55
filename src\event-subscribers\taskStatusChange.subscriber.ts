import {
  Connection,
  EntitySubscriberInterface,
  EventSubscriber,
  UpdateEvent,
  getManager,
} from 'typeorm';
import Task from 'src/modules/tasks/entity/task.entity';
import {
  getAdminEmailssBasedOnOrganizationId,
  getAdminIDsBasedOnOrganizationId,
  getAllTaskMemberNames,
  getUserDetails,
  getUserIDs,
  insertINTONotificationUpdate,
} from 'src/utils/re-use';
import { sendnewMail } from 'src/emails/newemails';
import * as moment from 'moment';
import { TaskRecurringStatus } from 'src/modules/tasks/dto/types';
import ViderWhatsappSessions from 'src/modules/whatsapp/entity/viderWhatsappSessions';
import { sendClientWhatsAppTemplateMessage, sendWhatsAppTextMessage } from 'src/modules/whatsapp/whatsapp.service';
import { fullMobileNumberWithCountry } from 'src/utils/validations/fullMobileWithCountry';
import { Organization } from 'src/modules/organization/entities/organization.entity';

@EventSubscriber()
export class TaskStatusSubscriber implements EntitySubscriberInterface<Task> {
  constructor(private readonly connection: Connection) {
    connection.subscribers.push(this);
  }

  listenTo() {
    return Task;
  }

  beforeCurrTaskId = 0;
  async beforeUpdate(event: UpdateEvent<Task>) {
    if (event.databaseEntity) {
    }
  }

  async afterUpdate(event: UpdateEvent<Task>) {
    if (!event.entity.bulkUpdate) {
      const oldTaskStatus = event.entity?.['oldTaskStatus'];
      const templateName = 'task_completed_clientt'
      if (
        oldTaskStatus !== event.entity.status &&
        event.entity.status !== 'undefined' &&
        event.entity.status !== 'deleted' &&
        event.entity.status !== 'terminated' &&
        event.entity.recurringStatus !== TaskRecurringStatus.PENDING
      ) {
        const entityManager = getManager();
        const afterStatus = event.entity.status;
        const taskId = event.entity.id;
        if (taskId) {
          const orgQuery = `SELECT organization_id,client_id, client_group_id FROM task where id = ${taskId};`;
          const getOrgId = await entityManager.query(orgQuery);
          if (getOrgId[0]) {
            const id = getOrgId[0].organization_id;
            const clientId = getOrgId[0].client_id;
            const clientGroupId = getOrgId[0].client_group_id;
            const userIdONe = event.entity['userId'];
            if (userIdONe) {
              const getName = `SELECT full_name FROM user where id = ${userIdONe};`;
              const getUserName = await entityManager.query(getName);
              const fullName = getUserName[0].full_name;
              if (clientId) {
                const clientQuery = `SELECT display_name FROM client where id = ${clientId};`;
                const getClientName = await entityManager.query(clientQuery);
                const displayName = getClientName[0]?.display_name;
                const StartDate = `SELECT task_start_date, user_id FROM task where id=${taskId};`;
                let Taskstartdate = await entityManager.query(StartDate);
                const startDateOfTask = moment(Taskstartdate[0].task_start_date).format(
                  'DD-MM-YYYY',
                );
                const dueDate1 = `SELECT due_date FROM task where id=${taskId};`;
                const EndDate = await entityManager.query(dueDate1);
                const TaskEndDate = moment(EndDate[0].due_date).format('DD-MM-YYYY');
                const userslist = await getUserIDs(taskId);

                let originalList = [];
                for (const user of userslist) {
                  if (originalList.includes(user)) {
                    // console.log("");
                  } else {
                    originalList.push(user);
                  }
                }
                const orgIds = await getAdminIDsBasedOnOrganizationId(id);
                const concatenatedArray = originalList.concat(orgIds);
                const taskdata = await Task.findOne({
                  where: { id: taskId },
                  relations: ['taskLeader'],
                });
                const taskLeader: any = taskdata?.taskLeader;
                if (taskLeader && Array.isArray(taskLeader)) {
                  taskLeader.forEach((leader) => {
                    if (!concatenatedArray.includes(leader.id)) {
                      concatenatedArray.push(leader.id);
                    }
                  });
                }
                const usersLists = [];
                concatenatedArray.forEach(function (element) {
                  if (!usersLists.includes(element)) {
                    usersLists.push(element);
                  }
                });

                const taskMemberNames12 = await getAllTaskMemberNames(userslist);
                const userDetailsList = [];
                for (let i of userslist) {
                  const userDetailsQuery = `SELECT * FROM user WHERE id=${i}`;
                  const userDetails = await entityManager.query(userDetailsQuery);
                  const memberName12 = userDetails[0].full_name;
                  userDetailsList.push(memberName12);
                }
                if (oldTaskStatus !== undefined && afterStatus !== undefined) {
                  if (afterStatus === 'completed' && clientId) {
  const clientDetailsQuery = `SELECT display_name, mobile_number, country_code FROM client WHERE id = ${clientId};`;
  const [clientDetails] = await entityManager.query(clientDetailsQuery);

  if (clientDetails?.mobile_number) {
    const clientName = clientDetails.display_name;
    const clientNumber = fullMobileNumberWithCountry(clientDetails.mobile_number, clientDetails.country_code);
    const taskName = event.entity.name;
    const taskNumber = event.entity.taskNumber;
    const formattedDueDate = new Date(event?.entity?.dueDate).toISOString().split('T')[0];

      const whatsappOptions = {
            to: clientNumber,
            name: templateName,
            header: [
              {
                type: 'text',
                text: clientName
              },
            ],
            body: [fullName, taskNumber, taskName,formattedDueDate],
            title: 'Task Status Change Client',
            userId: userIdONe,
            orgId:id,
            key: 'TASK_COMPLETED_WHATSAPP',
          };
          await sendClientWhatsAppTemplateMessage(whatsappOptions);

  }
}

                  const taskId = event.entity.id;
                  const taskNumber = event?.entity?.taskNumber;
                  const taskName = event.entity.name;
                  const status = event.entity.status;
                  const userIds = await getUserIDs(taskId);
                  const organization = await Organization.findOne({ id: id });

                  const addressParts = [
                    organization.buildingNo || '',
                    organization.floorNumber || '',
                    organization.buildingName || '',
                    organization.street || '',
                    organization.location || '',
                    organization.city || '',
                    organization.district || '',
                    organization.state || '',
                  ].filter((part) => part && part.trim() !== '');
                  const pincode =
                    organization.pincode && organization.pincode.trim() !== ''
                      ? ` - ${organization.pincode}`
                      : '';

                  const address = addressParts.join(', ') + pincode;
                  for (let user of usersLists) {
                    const details = await getUserDetails(user);
                    await sendnewMail({
                      id: details?.id,
                      key: 'TASK_STATUS_CHANGE_MAIL',
                      email: details?.email,
                      
                      data: {
                        userName: details?.full_name,
                        loginUsername: fullName,
                        taskName: taskName,
                        clientName: displayName,
                        FromStatus: oldTaskStatus,
                        status: afterStatus,
                        taskId: taskNumber,
                        taskStartDate: startDateOfTask,
                        taskEndDate: TaskEndDate,
                        memberName: taskMemberNames12,
                        userId: event.entity['userId'],
                        adress: address,
                        phoneNumber: organization?.mobileNumber,
                        mail: organization?.email,
                        legalName: organization?.tradeName || organization?.legalName,
                      },
                      filePath: 'task-status-updated-assigned',
                      subject: 'Task Status Updated',

                    });


                  }

                  try {
                    for (let userId of originalList) {
                      const sessionValidation = await ViderWhatsappSessions.findOne({
                        where: { userId: userId, status: 'ACTIVE' },
                      });
                      const title1 = 'Task Status Change';

                      if (sessionValidation) {
                        const loggedinuserDetails = await getUserDetails(userId);
                        const { full_name: userFullName, mobile_number: userPhoneNumber } =
                          loggedinuserDetails;
                        const key = 'TASK_STATUS_CHANGE_WHATSAPP';
                        const formattedDate = new Date(event?.entity?.dueDate)
                          .toISOString()
                          .split('T')[0];

                        const whatsappMessageBody = `
  Hi ${userFullName},
  ${fullName} has changed the  Task "${event?.entity?.name}" from ${oldTaskStatus} to ${afterStatus}
  Task details:
  Task ID: ${event?.entity?.taskNumber}
  Task Name: ${event?.entity?.name}
  Task Due Date: ${formattedDate}
  Client Name: ${displayName}
  Thanks,
    The ATOM Team `;
                        await sendWhatsAppTextMessage(
                          `91${userPhoneNumber}`,
                          whatsappMessageBody,
                          loggedinuserDetails?.organization_id,
                          title1,
                          userId,
                          key,
                        );
                      }
                    }
                  } catch (error) {
                    console.error('Errorrr sending Client WhatsApp notification:', error);
                  }
                  const orgIds = await getAdminIDsBasedOnOrganizationId(id);
                  const concatenatedArray = originalList.concat(orgIds);
                  const taskdata = await Task.findOne({
                    where: { id: taskId },
                    relations: ['taskLeader'],
                  });
                  const taskLeader: any = taskdata?.taskLeader;
                  if (taskLeader && Array.isArray(taskLeader)) {
                    taskLeader.forEach((leader) => {
                      if (!concatenatedArray.includes(leader.id)) {
                        concatenatedArray.push(leader.id);
                      }
                    });
                  }
                  const usersList = [];
                  concatenatedArray.forEach(function (element) {
                    if (!usersList.includes(element)) {
                      usersList.push(element);
                    }
                  });

                  const title1 = 'Task Status Change';
                  const body1 = `<strong>${fullName}</strong> has changed the "<strong>${taskName}</strong>" with "<strong>${displayName}</strong>" from "<strong>${oldTaskStatus}</strong>" to "<strong>${afterStatus}</strong>"`;
                  const key = 'TASK_STATUS_CHANGE_PUSH';
                  insertINTONotificationUpdate(title1, body1, usersList, id, key, taskId);
                  try {
                    for (let userId of usersList) {
                      const sessionValidation = await ViderWhatsappSessions.findOne({
                        where: { userId: userId, status: 'ACTIVE' },
                      });
                      if (sessionValidation) {
                        const loggedinuserDetails = await getUserDetails(userId);
                        const {
                          full_name: userFullName,
                          mobile_number: userPhoneNumber,
                          country_code: countryCode,
                        } = loggedinuserDetails;
                        const userWhatsAppNumber = fullMobileNumberWithCountry(
                          userPhoneNumber,
                          countryCode,
                        );
                        const key = 'Task Status Change Whatsapp';
                        const formattedDate = new Date(event?.entity?.dueDate)
                          .toISOString()
                          .split('T')[0];

                        const whatsappMessageBody = `
    Hi ${userFullName},
    ${fullName} has changed the  Task "${event?.entity?.name}" from ${oldTaskStatus} to ${afterStatus}
    Task details:
    Task ID: ${event?.entity?.taskNumber}
    Task Name: ${event?.entity?.name}
    Task Due Date: ${formattedDate}
    Client Name: ${displayName}
    Thanks,
    The ATOM Team `;
                        await sendWhatsAppTextMessage(
                          // `91${userPhoneNumber}`,
                          userWhatsAppNumber,
                          whatsappMessageBody,
                          event?.entity?.organizationId,
                          title1,
                          userId,
                          key,
                        );
                      }
                    }
                  } catch (error) {
                    console.error('Errorrr sending Client WhatsApp notification:', error);
                  }
                }
              }
              if (clientGroupId) {
                const clientQuery = `SELECT display_name FROM client_group where id = ${clientGroupId};`;
                const getClientName = await entityManager.query(clientQuery);
                const displayName = getClientName[0]?.display_name;
                const userslist = await getUserIDs(taskId);
                let originalList = [];
                for (const user of userslist) {
                  if (originalList.includes(user)) {
                  } else {
                    originalList.push(user);
                  }
                }
                const orgIds = await getAdminIDsBasedOnOrganizationId(id);
                const concatenatedArray = originalList.concat(orgIds);
                const taskdata = await Task.findOne({
                  where: { id: taskId },
                  relations: ['taskLeader'],
                });
                const taskLeader: any = taskdata?.taskLeader;
                if (taskLeader && Array.isArray(taskLeader)) {
                  taskLeader.forEach((leader) => {
                    if (!concatenatedArray.includes(leader.id)) {
                      concatenatedArray.push(leader.id);
                    }
                  });
                }
                const usersList = [];
                concatenatedArray.forEach(function (element) {
                  if (!usersList.includes(element)) {
                    usersList.push(element);
                  }
                });

                const userDetailsList = [];
                for (let i of userslist) {
                  const userDetailsQuery = `SELECT * FROM user WHERE id=${i}`;
                  const userDetails = await entityManager.query(userDetailsQuery);
                  const memberName12 = userDetails[0].full_name;
                  userDetailsList.push(memberName12);
                }
                if (oldTaskStatus !== undefined && afterStatus !== undefined) {
                  const taskName = event.entity.name;

                  const title1 = 'Task Status Change';
                  const body1 = `<strong>${fullName}</strong> has changed the "<strong>${taskName}</strong>" of "<strong>${displayName}</strong>" from "<strong>${oldTaskStatus}</strong>" to "<strong>${afterStatus}</strong>"`;
                  const key = 'TASK_STATUS_CHANGE_PUSH';
                  insertINTONotificationUpdate(title1, body1, usersList, id, key);
                }
              }
            }
          }
        }
      }
    }
  }
}
