import {
  Connection,
  EntitySubscriberInterface,
  EventSubscriber,
  InsertEvent,
  getManager,
} from 'typeorm';
import { getUserDetails, insertINTONotificationUpdate } from 'src/utils/re-use';
import ChatMessage from 'src/modules/chats/chat-message.entity';
import { sendnewMail } from 'src/emails/newemails';
import Task from 'src/modules/tasks/entity/task.entity';
import { Organization } from 'src/modules/organization/entities/organization.entity';

@EventSubscriber()
export class ChatMessageSubscriber implements EntitySubscriberInterface<ChatMessage> {
  constructor(private readonly connection: Connection) {
    connection.subscribers.push(this);
  }
  listenTo() {
    return ChatMessage;
  }
  async afterInsert(event: InsertEvent<ChatMessage>) {
    const entityManager = getManager();
    const senderId = event.entity.senderId;
    const { id: chatRoomId, type, name: groupName, taskId } = event.entity.room;
    const queryForRoomMembrs = `SELECT user_id from chat_room_members where chat_room_id=${chatRoomId};`;
    const members = await entityManager.query(queryForRoomMembrs);
    const userIds = members.map((item) => item.user_id);
    const userNameSql = `SELECT full_name,organization_id from user where id=${senderId};`;
    const name = await entityManager.query(userNameSql);
    const [{ full_name: userName, organization_id: orgId }] = name;
    const users = userIds.filter((item) => item !== senderId);
    const title = 'New Message Received';
    const key = 'NEW_MESSAGE_RECEIVED_PUSH';
    let body: string;
    if (type === 'GROUP') {
      body = `You've got a new message from ${userName} in the group ${groupName}. Stay conncected and respond promptly to address their needs`;
    } else {
      body = `You've got a new message from ${userName}. Stay connected and respond promptly to address their needs`;
    }
    // insertINTOnotification(title, body, users, orgId);
    insertINTONotificationUpdate(title, body, users, orgId, key);
    const organization = await Organization.findOne({ id: orgId });

    const addressParts = [
      organization.buildingNo || '',
      organization.floorNumber || '',
      organization.buildingName || '',
      organization.street || '',
      organization.location || '',
      organization.city || '',
      organization.district || '',
      organization.state || '',
    ].filter((part) => part && part.trim() !== '');
    const pincode =
      organization.pincode && organization.pincode.trim() !== ''
        ? ` - ${organization.pincode}`
        : '';

    const address = addressParts.join(', ') + pincode;
    if (taskId) {
      const taskDetails = await Task.findOne({
        where: { id: taskId },
        relations: ['client', 'clientGroup'],
      });
      if (users && taskDetails) {
        for (let user of users) {
          const userDetails = await getUserDetails(user);
          await sendnewMail({
            id: userDetails?.id,
            key: 'NEW_CHAT_MESSAGE_RECEIVED_MAIL',
            email: userDetails?.email,
            data: {
              taskUserName: userDetails?.full_name,
              taskName: taskDetails?.name,
              clientName: taskDetails?.client?.displayName,
              taskId: taskDetails?.taskNumber,
              senderName: userName,
              message: event?.entity?.message,
              userId: event.entity['userId'],
              adress: address,
              phoneNumber: organization?.mobileNumber,
              mail: organization?.email,
              legalName: organization?.tradeName || organization?.legalName,
            },
            filePath: 'new-chat-msg-received',
            subject: `New Chat Message: ${taskDetails?.name} with ${taskDetails?.client?.displayName}`,
          });
        }
      }
    }
  }
}
