import {
  BaseEntity,
  Column,
  Entity,
  ManyToOne,
  OneToMany,
  OneToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import DocumentInOut from './document-in-out.entity';
import Kyb from 'src/modules/kyb/kyb.entity';
import DocumentCategory from './document-category.entity';
import Storage from 'src/modules/storage/storage.entity';

@Entity()
class DocumentsData extends BaseEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  type: string;

  @Column()
  returnable: string;

  @Column()
  mode: string;

  @Column()
  manner: string;

  @Column()
  documentType: string;

  @Column()
  documentName: string;

  @ManyToOne(() => DocumentInOut, (documentInOut) => documentInOut.documentData)
  documentInOut: DocumentInOut;

  @ManyToOne(() => DocumentCategory, (documentCategory) => documentCategory.documentInOut)
  documentCategory: DocumentCategory;

  @OneToOne(() => Kyb, (kyb) => kyb.documentsData)
  kyb: Kyb;

  @OneToMany(() => Storage, (storage) => storage.documentsData, {
    cascade: true,
  })
  attachments: Storage[];
}

export default DocumentsData;
