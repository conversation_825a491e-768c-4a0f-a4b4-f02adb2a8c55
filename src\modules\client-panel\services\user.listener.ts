import { Injectable } from '@nestjs/common';
import { OnEvent } from '@nestjs/event-emitter';
import { JwtService } from '@nestjs/jwt';
import email from 'src/emails/email';
import { Organization } from 'src/modules/organization/entities/organization.entity';
import { User } from 'src/modules/users/entities/user.entity';
import { sendNotification } from 'src/notifications/notify';
import notify from 'src/notifications/notify';
import { createQueryBuilder } from 'typeorm';
import { Event_Actions } from 'src/event-listeners/actions';
import { Wallets } from 'src/modules/wallet/entities/wallets.entity';
import { sendWhatsAppTemplateMessage } from 'src/modules/whatsapp/whatsapp.service';

interface Event {
  data: any;
  orgId: number;
  invitedUserId: number;
  orgName: string;
  user: User;
  userId: number;
  email: string;
}

@Injectable()
export class UserListener {

}
