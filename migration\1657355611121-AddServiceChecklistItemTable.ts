import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddServiceChecklistItemTable1657355611121
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `create table service_checklist_item (
            id int not null auto_increment,
            name varchar(255) not null,
            checklist_id int null,
            created_at datetime(6) default current_timestamp(6),
            updated_at datetime(6) default current_timestamp(6),
            primary key (id),
            CONSTRAINT FK_service_checklist_item_checklist_id FOREIGN KEY (checklist_id) REFERENCES service_checklist (id) ON DELETE SET NULL
        );`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}
