import {
  Connection,
  EntitySubscriberInterface,
  EventSubscriber,
  UpdateEvent,
  getManager,
} from 'typeorm';
import Task from 'src/modules/tasks/entity/task.entity';
import {
  getUserIDs,
  getAdminIDsBasedOnOrganizationId,
  getAdminEmailsBasedOnOrganizationId,
  getAllTaskMemberNames,
  getUserDetails,
  getTaskLeaderDetails,
  insertINTONotificationUpdate,
} from 'src/utils/re-use';
import { User } from 'src/modules/users/entities/user.entity';
import { sendnewMail } from 'src/emails/newemails';
import * as moment from 'moment';
import { TaskRecurringStatus } from 'src/modules/tasks/dto/types';
import { Organization } from 'src/modules/organization/entities/organization.entity';

@EventSubscriber()
export class TaskDeleteSubscriber implements EntitySubscriberInterface<Task> {
  entity_manager: any;
  constructor(private readonly connection: Connection) {
    connection.subscribers.push(this);
  }

  listenTo() {
    return Task;
  }

  async beforeUpdate(event: UpdateEvent<Task>) {
  }

  async afterUpdate(event: UpdateEvent<Task>) {
    if(!event.entity.bulkUpdate){
      const entityManager = getManager();
      const taskId = event.entity?.id;
      const taskName = event.entity?.name;
      const oldRecurringStatus = event.entity?.['oldRecurringStatus'];
      const oldDueDate = event.entity?.['oldDueDate'];
  
      const status = event.entity?.status;
      const taskNumber = event.entity?.taskNumber;
      if (taskId && event.entity.recurringStatus !== TaskRecurringStatus.PENDING) {
        const clientIdQuery = `SELECT client_id,client_group_id from task where id = ${taskId}`;
        const clientIdQ = await entityManager.query(clientIdQuery);
        if(clientIdQ[0]?.client_id){
          const [{ client_id: clientId }] = clientIdQ;
          const clientNameQuery = `SELECT display_name from client WHERE id = ${clientId}`;
          const clientNameQ = await entityManager.query(clientNameQuery);
          const [{ display_name: clientName }] = clientNameQ;
          const logInUserId = event.entity['userId'];
          if (logInUserId) {
            const userSql = `SELECT full_name FROM user WHERE id=${logInUserId}`;
            const userQuery = await entityManager.query(userSql);
            const [{ full_name: userName }] = userQuery;
            const taskMembersUserIds = await getUserIDs(taskId);
            const taskdata : any= await Task.findOne({where:{id: taskId}, relations:["taskLeader"]})
            const taskLeader:any= taskdata?.taskLeader;
            if (taskLeader && Array.isArray(taskLeader)) {
              taskLeader.forEach(leader => {
                  if (!taskMembersUserIds.includes(leader.id)) {
                    taskMembersUserIds.push(leader.id);
                  }
              });
          }
          const usersList = [];
          taskMembersUserIds.forEach(function (element) {
            if (!usersList.includes(element)) {
              usersList.push(element);
            }
          });

            const sql = `SELECT organization_id FROM task WHERE id = ${taskId}`;
            const orgIdEntity = await entityManager.query(sql);
            const organizationId = orgIdEntity[0]?.organization_id;
            const taskSqlQuery = `SELECT * FROM task WHERE id=${taskId}`;
            const taskDetails = await entityManager.query(taskSqlQuery);
            const taskStartDate = event.entity?.taskStartDate;
            const afterDueDateChange = event.entity?.dueDate;
            const taskMemberIds = await getUserIDs(taskId);
            const taskMemberNames = await getAllTaskMemberNames(taskMemberIds);
            const taskLeaderId = taskDetails[0]?.task_leader_id;
            const userId = taskDetails[0]?.user_id;
            const userDetails = await getUserDetails(userId);
            const taskLeaderDetails = await getTaskLeaderDetails(taskLeaderId);
            const taskStartDateFormat = moment(taskStartDate).format('DD-MM-YYYY');
            if (status === 'deleted') {
              const title = 'Task Deleted';
              const key = 'TASK_DELETED_PUSH';
              const body = `The task "<strong>${taskName}</strong>" of <strong>${clientName}</strong> has been deleted by <strong>${userName}</strong>`;
              const orgIds = await getAdminIDsBasedOnOrganizationId(organizationId);
              
              const concatenatedArray = taskMembersUserIds.concat(orgIds);
              const taskLeader:any= taskdata?.taskLeader;

              if (taskLeader && Array.isArray(taskLeader)) {
                taskLeader.forEach(leader => {
                    if (!concatenatedArray.includes(leader.id)) {
                        concatenatedArray.push(leader.id);
                    }
                });
            }
              const usersList = [];
              concatenatedArray.forEach(function (element) {
                if (!usersList.includes(element)) {
                  usersList.push(element);
                }
              });
              // insertINTOnotification(title, body, usersList, organizationId)
              
              insertINTONotificationUpdate(title, body, usersList, organizationId, key);
              const userListMails = await getAdminEmailsBasedOnOrganizationId(organizationId);
              const organization = await Organization.findOne({ id: organizationId });


    const addressParts = [
      organization.buildingNo || '',
      organization.floorNumber || '',
      organization.buildingName || '',
      organization.street || '',
      organization.location || '',
      organization.city || '',
      organization.district || '',
      organization.state || ''
    ].filter(part => part && part.trim() !== '');
    const pincode = organization.pincode && organization.pincode.trim() !== '' ? ` - ${organization.pincode}` : '';

    const address = addressParts.join(', ') + pincode;
              if (taskLeaderDetails.length !== 0) {
                await sendnewMail({
                  id: taskLeaderDetails[0]?.id,
                  key: 'NEW_TASK_DELETED_MAIL',
                  email: taskLeaderDetails[0]?.email,
                  data: {
                    taskId: taskNumber,
                    taskName: taskName,
                    taskStartDate: taskStartDateFormat,
                    username: userName,
                    clientName: clientName,
                    taskMembers: taskMemberNames,
                    adminName: taskLeaderDetails[0]?.full_name,
                    userId: event.entity['userId'],
                    adress: address,
                  phoneNumber: organization?.mobileNumber,
                  mail: organization?.email,
                  legalName: organization?.tradeName || organization?.legalName

                  },
                  filePath: 'new-task-deleted',
                  subject: 'Task Deleted| Vider',
                });
              }
    
              for (let user of usersList) {
                const userDetails = await getUserDetails(user);
                await sendnewMail({
                  id: userDetails?.id,
                  key: 'NEW_TASK_DELETED_MAIL',
                  email: userDetails?.email,
                  data: {
                    taskId: taskNumber,
                    taskName: taskName,
                    taskStartDate: taskStartDateFormat,
                    username: userName,
                    clientName: clientName,
                    adminName: userDetails?.full_name,
                    taskMembers: taskMemberNames,
                    userId: event.entity['userId'],
                    adress: address,
                  phoneNumber: organization?.mobileNumber,
                  mail: organization?.email,
                  legalName: organization?.tradeName || organization?.legalName

                  },
                  filePath: 'new-task-deleted',
                  subject: 'Task Deleted| Vider',
                });
              }
            } else if (status === 'terminated') {
              const title = 'Task Terminated';
              const key = 'TASK_TERMINATED_PUSH';
              const body = `Task terminated - "<strong>${taskName}</strong>" of <strong>${clientName}</strong> has been terminated by <strong>${userName}<strong>`;
              // insertINTOnotification(title,body,taskMembersUserIds)
              const orgIds = await getAdminIDsBasedOnOrganizationId(organizationId);
              const concatenatedArray = taskMembersUserIds.concat(orgIds);
              const taskLeader:any= taskdata?.taskLeader

              if (taskLeader && Array.isArray(taskLeader)) {
                taskLeader.forEach(leader => {
                    if (!concatenatedArray.includes(leader.id)) {
                        concatenatedArray.push(leader.id);
                    }
                });
            }
              const usersList = [];
              concatenatedArray.forEach(function (element) {
                if (!usersList.includes(element)) {
                  usersList.push(element);
                }
              });
    
              // insertINTOnotification(title, body, usersList, organizationId);
             
              insertINTONotificationUpdate(title, body, usersList, organizationId, key);
              const organization = await Organization.findOne({ id: organizationId });


    const addressParts = [
      organization.buildingNo || '',
      organization.floorNumber || '',
      organization.buildingName || '',
      organization.street || '',
      organization.location || '',
      organization.city || '',
      organization.district || '',
      organization.state || ''
    ].filter(part => part && part.trim() !== '');
    const pincode = organization.pincode && organization.pincode.trim() !== '' ? ` - ${organization.pincode}` : '';

    const address = addressParts.join(', ') + pincode;
              if (status === 'terminated' && usersList) {
                if (taskId) {
                  const taskMemberIds = await getUserIDs(taskId);
                  const taskMemberNames = await getAllTaskMemberNames(taskMemberIds);
                  for (let user of usersList) {
                    const taskUserDetails = await getUserDetails(user);
                    await sendnewMail({
                      id: taskUserDetails?.id,
                      key: 'TASK_TERMINATED_MAIL',
                      email: taskUserDetails?.email,
                      data: {
                        taskUserName: taskUserDetails?.full_name,
                        taskName,
                        clientName,
                        taskId: event?.entity?.taskNumber,
                        startDate: taskStartDateFormat,
                        taskMembers: taskMemberNames,
                        userId: event.entity['userId'],
                        adress: address,
                  phoneNumber: organization?.mobileNumber,
                  mail: organization?.email,
                  legalName: organization?.tradeName || organization?.legalName

                      },
                      filePath: 'task-terminated',
                      subject: `Task Update: ${taskName} has been Terminated`,
                    });
                  }
                }
              }
            }
            if ((oldDueDate !== afterDueDateChange) && afterDueDateChange && oldRecurringStatus === TaskRecurringStatus.CREATED) {
              const title = 'Task Due Date Changed';
              const body = `The due date for "<strong>${taskName}</strong>" of <strong>${clientName}</strong> has been changed  by <strong>${userName}</strong>.`;
              const key = 'TASK_DUE_DATE_CHANGED_PUSH';
              // insertINTOnotification(title, body, taskMembersUserIds, organizationId);
              insertINTONotificationUpdate(title, body, usersList, organizationId, key);
              //for approvals
              const { id: taskId } = event.entity;
              const sqlApprovals = `SELECT user_id FROM approval WHERE task_id=${taskId}`;
              const approvalIds = await entityManager.query(sqlApprovals);
              const approvals: User[] = approvalIds.map((k) => k.user_id);
              // insertINTOnotification(title, body, approvals, organizationId);
              insertINTONotificationUpdate(title, body, approvals, organizationId, key);
              const organization = await Organization.findOne({ id: organizationId });


    const addressParts = [
      organization.buildingNo || '',
      organization.floorNumber || '',
      organization.buildingName || '',
      organization.street || '',
      organization.location || '',
      organization.city || '',
      organization.district || '',
      organization.state || ''
    ].filter(part => part && part.trim() !== '');
    const pincode = organization.pincode && organization.pincode.trim() !== '' ? ` - ${organization.pincode}` : '';

    const address = addressParts.join(', ') + pincode;
              if (event?.entity?.id) {
                let commaSeparatedNames = '';
                if (event?.entity?.members) {
                  const fullNames = event?.entity?.members.map((member) => member?.fullName);
                  commaSeparatedNames = fullNames.join(', ');
                }
                for (let user of taskMemberIds) {
                  const taskUserDetails = await getUserDetails(user);
                  await sendnewMail({
                    id: taskUserDetails?.id,
                    key: 'TASK_DUE_DATE_CHANGED_MAIL',
                    email: taskUserDetails?.email,
                    data: {
                      taskUserName: taskUserDetails?.full_name,
                      userName,
                      taskName,
                      clientName,
                      oldDueDate: oldDueDate,
                      newDueDate: afterDueDateChange,
                      taskId: event?.entity?.taskNumber,
                      startDate: taskStartDateFormat,
                      taskMembers: commaSeparatedNames,
                      userId: event.entity['userId'],
                      adress: address,
                  phoneNumber: organization?.mobileNumber,
                  mail: organization?.email,
                  legalName: organization?.tradeName || organization?.legalName

                    },
                    filePath: 'task-due-date-change',
                    subject: `Task Update: Due Date Changed for ${taskName} of ${clientName}`,
                  });
                }
              }
            }
          }
        }
        if(clientIdQ[0]?.client_group_id){
          const [{ client_group_id: clientId }] = clientIdQ;
          const clientNameQuery = `SELECT display_name from client_group WHERE id = ${clientId}`;
          const clientNameQ = await entityManager.query(clientNameQuery);
          const [{ display_name: clientName }] = clientNameQ;
          const logInUserId = event.entity['userId'];
          if (logInUserId) {
            const userSql = `SELECT full_name FROM user WHERE id=${logInUserId}`;
            const userQuery = await entityManager.query(userSql);
            const [{ full_name: userName }] = userQuery;
            const taskMembersUserIds = await getUserIDs(taskId);
            const sql = `SELECT organization_id FROM task WHERE id = ${taskId}`;
            const orgIdEntity = await entityManager.query(sql);
            const organizationId = orgIdEntity[0]?.organization_id;
            const afterDueDateChange = event.entity?.dueDate;
            if ((oldDueDate !== afterDueDateChange) && afterDueDateChange && oldRecurringStatus === TaskRecurringStatus.CREATED) {
              const title = 'Task Due Date Changed';
              const body = `The due date for "<strong>${taskName}</strong>" of <strong>${clientName}</strong> has been changed  by <strong>${userName}</strong>.`;
              const key = 'TASK_DUE_DATE_CHANGED_PUSH';
              insertINTONotificationUpdate(title, body, taskMembersUserIds, organizationId, key);
              const { id: taskId } = event.entity;
              const sqlApprovals = `SELECT user_id FROM approval WHERE task_id=${taskId}`;
              const approvalIds = await entityManager.query(sqlApprovals);
              const approvals: User[] = approvalIds.map((k) => k.user_id);
              // insertINTONotificationUpdate(title, body, usersList, organizationId, key);
            }
          }
        }
      }
    }
  }
}
