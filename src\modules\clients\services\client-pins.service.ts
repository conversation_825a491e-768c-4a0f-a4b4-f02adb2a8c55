import { Injectable } from '@nestjs/common';
import Client from 'src/modules/clients/entity/client.entity';
import { User } from 'src/modules/users/entities/user.entity';
import AddPinDto from '../dto/add-pin.dto copy';
import ClientPin from '../entity/client-pin.entity';
import ClientGroup from 'src/modules/client-group/client-group.entity';

@Injectable()
export class ClientPinsService {
  async addPin(userId: number, data: AddPinDto) {
    if(data.client){
      let existingPin = await ClientPin.findOne({
        where: {
          client: { id: data.client },
          user: { id: userId },
        },
      });
  
      if (existingPin) {
        return existingPin;
      }  
    }

    if(data.clientGroup){
      let existingPin = await ClientPin.findOne({
        where: {
          clientGroup: { id: data.clientGroup },
          user: { id: userId },
        },
      });
  
      if (existingPin) {
        return existingPin;
      }  
    }

    let pins = await ClientPin.find({
      where: { user: { id: userId } },
    });

    if (pins.length === 6) {
      await ClientPin.delete(pins[5].id);
    }

    let newPin = new ClientPin();
    let user = await User.findOne({ where: { id: userId } });
    newPin.user = user;
    if(data.client){
      let client = await Client.findOne({ where: { id: data.client } });
      newPin.client = client;
    }
    if(data.clientGroup){
      let clientGroup = await ClientGroup.findOne({ where: { id: data.clientGroup } });
      newPin.clientGroup = clientGroup;
    }
    await newPin.save();

    return newPin;
  }

  async getPins(userId: number) {
    let pins = await ClientPin.find({
      where: { user: { id: userId } },
      relations: ['client','clientGroup'],
    });

    return pins;
  }

  async deletePin(id: number) {
    let pin = await ClientPin.findOne({ where: { id } });
    await pin.remove();
    return 'success';
  }
}
