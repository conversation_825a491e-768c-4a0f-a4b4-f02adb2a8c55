import Client from 'src/modules/clients/entity/client.entity';
import { Organization } from 'src/modules/organization/entities/organization.entity';
import Storage from 'src/modules/storage/storage.entity';
import {
  BaseEntity,
  Column,
  CreateDateColumn,
  Entity,
  JoinColumn,
  JoinTable,
  ManyToMany,
  ManyToOne,
  OneToMany,
  OneToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import BroadcastEmailTemplates from './broadcast-email-templates-entity';
import ClientGroupBroadcast from './client-group-broadcast.entity';
import BroadcastActivityDetails from './broadcast-activity-details.entity';
import { User } from 'src/modules/users/entities/user.entity';

export enum BroadcastMessageTo {
    CLIENT = 'CLIENT',
    CLIENTGROUP = 'CLIENTGROUP'
   
  }
  export enum Status {
    Ready = 'READY',
    SENT = 'SENT',
    ERROR='ERROR',
    PROCESSING = 'PROCESSING'
   
  }
@Entity()
class BroadcastActivity extends BaseEntity {

  @PrimaryGeneratedColumn()
  id: number;

  @ManyToOne(() => ClientGroupBroadcast, (clientGroupBroadcast) => clientGroupBroadcast.broadcastActivities)
  clientGroup: ClientGroupBroadcast;

  @ManyToOne(() => BroadcastEmailTemplates, (broadcastEmailTemplates) => broadcastEmailTemplates.broadcastActivities)
  template: BroadcastEmailTemplates;

  @Column()
  templateName: string;

  @Column()
  groupName: string;

  @Column()
  errorMessage: string;

  @Column({ type: 'enum', enum: BroadcastMessageTo, default: BroadcastMessageTo.CLIENTGROUP })
  broadcastMessageTo: BroadcastMessageTo;

  
  @Column({ type: 'enum', enum: Status, default: Status.Ready })
  Status: Status;


  @CreateDateColumn()
  createdAt: string;

  @UpdateDateColumn()
  updatedAt: Date;

  @ManyToOne(() => User, (user) => user.broadcastActivity)
  user: User;

   @ManyToOne(() => Organization, (organization) => organization.broadcastActivity)
  organization: Organization;

  @OneToMany(() => BroadcastActivityDetails, (broadcastActivitydetails) => broadcastActivitydetails.broadcastActivity)
  details: BroadcastActivityDetails[];



}

export default BroadcastActivity;
