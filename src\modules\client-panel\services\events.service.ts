import { Injectable } from '@nestjs/common';
import Client from 'src/modules/clients/entity/client.entity';
import Task from 'src/modules/tasks/entity/task.entity';
import { User } from 'src/modules/users/entities/user.entity';
import { createQueryBuilder, getConnection } from 'typeorm';
import { CreateEventDto, EventTypeEnum } from 'src/modules/events/dto/create-event.dto';
import Event from 'src/modules/events/event.entity';
import * as moment from 'moment';
import {Permissions} from 'src/modules/events/permission'
import { sendWhatsAppTemplateMessage } from 'src/modules/whatsapp/whatsapp.service';
import { getUserDetails } from 'src/utils/re-use';
import { Cron, CronExpression } from '@nestjs/schedule';

@Injectable()
export class EventsService {

  async getEvents(userId: number, query: any) {
    let user = await User.findOne(userId, { relations: ['organization'] });

    let userDetails = await User.findOne({
      where: { id: userId },
      relations: ['role', 'role.permissions'],
    });

    let eventPermission = userDetails?.role?.permissions?.find(
      (permission) => permission.slug === Permissions.VIEW_CALENDAR,
    );
    if(eventPermission || !userDetails?.role){
      let events = createQueryBuilder(Event, 'event')
      .leftJoinAndSelect('event.task', 'task')
      .leftJoinAndSelect('event.client', 'client')
      .leftJoinAndSelect('event.user', 'user')
      .leftJoinAndSelect('event.organization', 'organization')
      .leftJoinAndSelect('event.members', 'members');

    if (query.taskId) {
      events.where('task.id = :taskId', { taskId: query.taskId });
    }

    if (!query.taskId) {
      events.where('organization.id = :orgId', { orgId: user.organization.id });
    }

    if (query.startDates) {
      const startOfMonth = moment(`${query.startDates}`).startOf('month').format('YYYY-MM-DD');
      const endOfMonth = moment(query.startDates).endOf('month').format('YYYY-MM-DD');
      events.andWhere(`Date(event.date) BETWEEN '${startOfMonth}' AND '${endOfMonth}'`);
    }

    return await events.getMany();
    }
    return []
    
  }

  async getDefaultEvents(query: Date) {
    let events = createQueryBuilder(Event, 'event');
    if (query) {
      const startOfMonth = moment(`${query}`).startOf('month').format('YYYY-MM-DD');
      const endOfMonth = moment(query).endOf('month').format('YYYY-MM-DD');
      events
        .where(`Date(event.date) BETWEEN '${startOfMonth}' AND '${endOfMonth}'`)
        .andWhere('event.defaultOne=:true', { true: true });
    }
    return await events.getMany();
  }


}
