import { Optional } from '@nestjs/common';
import Client from 'src/modules/clients/entity/client.entity';
import { Organization } from 'src/modules/organization/entities/organization.entity';
import {
  BaseEntity,
  Column,
  CreateDateColumn,
  Entity,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import AutClientCredentials from './aut_client_credentials.entity';
import AutDemandResponse from './aut-demand-resposne';

export enum PortalStatus {
  YES = 'YES',
  NO = 'NO',
}

@Entity()
class AutOutstandingDemand extends BaseEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  name:string;

  @Column()
  pan:string;

  @Column()
  demandReferenceNo: string;

  @Column()
  assessmentYear: string;

  @Column()
  outstandingDemandamount: string;

  @Column()
  accruedInterest: string;

  @Column()
  rectificationRightText: string;

  @Column()
  sectionCodeText: string;

  @Column()
  currentStatus: string;

  @Column({type:'json',nullable:true}) 
  dateOfDemandRaised: any;

  @Column()
  organizationId: number;

  @ManyToOne(() => Client, (client) => client.autOutstandingDemand, { onDelete: 'SET NULL' })
  client: Client;

  @ManyToOne(() => AutClientCredentials, (autClientCredentials) => autClientCredentials.autDemand )
  autClientCredentials:AutClientCredentials;

  @Column()
  demandRaisedDate:string;

  @Column()
  originalOutstandingDemandAmount:string;

  @Column()
  interestStartDate:string;

  @Optional()
  @Column()
  timePeriod: string;

  @Column()
  rateOfInterest :string;

  @Column()
  accruedInterestComputed:number;

  @Column()
  outstandingDemandAmount:number;

  @Column()
  computationDataDetails:string;

  @Column()
  transactionId:string;

  @Column()
  customerTrnId:string;

  @Column()
  demandRaisedDateMs:string;

  @Column()
  demandDateMilli:string;

  @Column({type:"enum",enum:PortalStatus,nullable:true})
  isInPortal: PortalStatus | null;

  @OneToMany(()=> AutDemandResponse, (autDemandResponse) => autDemandResponse.autOutstandingDemand)
  autDemandResponse:AutDemandResponse[];
  
  @CreateDateColumn()
  createdAt: string;

  @UpdateDateColumn()
  updatedAt: string;  

}

export default AutOutstandingDemand;
