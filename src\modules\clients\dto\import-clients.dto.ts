import { IsEmail, IsEnum, IsNotEmpty, IsOptional, IsString } from 'class-validator';
import { Organization } from 'src/modules/organization/entities/organization.entity';
import { User } from 'src/modules/users/entities/user.entity';
import { CategoryEnum, SubCategoryEnum } from './types';

export class ImportClientsDto {
  @IsNotEmpty()
  @IsString()
  displayName: string;

  @IsNotEmpty()
  @IsEnum(CategoryEnum)
  category: CategoryEnum;

  @IsOptional()
  @IsEnum(SubCategoryEnum)
  subCategory: SubCategoryEnum;

  @IsNotEmpty()
  createdBy: User;

  @IsNotEmpty()
  organization: Organization;

  @IsNotEmpty()
  @IsEmail()
  email: string;

  @IsNotEmpty()
  mobileNumber: string;

  @IsOptional()
  gstNumber: string;

  @IsOptional()
  panNumber: string;

  @IsOptional()
  authorizedPerson: string;

  @IsOptional()
  designation: string;

  @IsOptional()
  gstVerified: boolean;

  @IsOptional()
  tradeName: string;

  @IsOptional()
  legalName: string;

  @IsOptional()
  constitutionOfBusiness: string;

  @IsOptional()
  placeOfSupply: string;

  @IsOptional()
  panVerified: boolean;

  @IsOptional()
  firstName: string;

  @IsOptional()
  middleName:string;

  @IsOptional()
  lastName: string;

  @IsOptional()
  fullName: string;

  @IsOptional()
  issameaddress: boolean;
  
  @IsOptional()
  alternateMobileNumber: string;

  @IsOptional()
  dob: string;

  @IsOptional()
  buildingName: string;

  @IsOptional()
  street: string;

  @IsOptional()
  city: string;

  @IsOptional()
  state: string;

  @IsOptional()
  pincode: string;

  @IsOptional()
  notes: string;

  @IsOptional()
  address: object;

}