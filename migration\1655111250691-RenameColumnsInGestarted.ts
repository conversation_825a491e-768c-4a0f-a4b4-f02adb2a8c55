import { MigrationInterface, QueryRunner } from 'typeorm';

export class RenameColumnsInGestarted1655111250691
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE get_started
             RENAME COLUMN create_task TO select_services,
             RENAME COLUMN create_client TO import_clients`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}
