import { MigrationInterface, QueryRunner } from 'typeorm';

export class AlterApprovalHierarchyLevel1661486729242 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE approval_heirarchy_level
             DROP FOREIGN KEY FK_approval_heirarchy_level_approval_hierarchy_id,
             ADD FOREIGN KEY (approval_hierarchy_id) REFERENCES approval_heirarchy(id) ON DELETE CASCADE;
          `,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}
