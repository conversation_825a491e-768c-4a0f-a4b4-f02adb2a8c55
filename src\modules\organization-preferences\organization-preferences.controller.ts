import {
  BadRequestException,
  Body,
  Controller,
  Get,
  Param,
  ParseIntPipe,
  Patch,
  Post,
  Put,
  Query,
  Req,
  Request,
  UseGuards,
} from '@nestjs/common';

import { User } from '../users/entities/user.entity';
import { OrganizationPreferencesService } from './organization-preferences.service';
import { JwtAuthGuard } from '../users/jwt/jwt-auth.guard';
import { UpdateInvoicePreferencesDto } from './dto/invoice-preference.dto';
import { UpdateHolidayPreferences } from './dto/holiday-preference.dto';
import { ClientNotificationAction, OrganizationNotification } from '../notification-settings/action';
import { update } from 'lodash';

@Controller('organization-preferences')
export class OrganizationPreferencesController {
  [x: string]: any;
  constructor(private readonly service: OrganizationPreferencesService) { }

  @UseGuards(JwtAuthGuard)
  @Get('getInvoicePreferences')
  async getInvoicePreferences(@Request() req: any) {
    const { userId } = req.user;
    return this.service.get(userId);
  }

  @Get('getTaskPreferences/:userId')
  async getTaskPreferences(@Request() req: any, @Param('userId', ParseIntPipe) userId: number,) {
    return this.service.get(userId);
  }

  @UseGuards(JwtAuthGuard)
  @Post('update')
  async updateInvoicePreferences(@Request() req: any, @Body() body: UpdateInvoicePreferencesDto) {
    const { userId } = req.user;
    const userr = User.findOne({
      where: {
        id: userId,
      },
    });

    return this.service.post(userId, body, (await userr).organization.id);
  }

  @UseGuards(JwtAuthGuard)
  @Post('updateholiday')
  async updateHolidayPreferences(@Request() req: any, @Body() body: UpdateHolidayPreferences) {
    const { userId } = req.user;
    const user = User.findOne({
      where: {
        id: userId,
      },
    });
    return this.service.posts(userId, body, (await user).organization.id);
  }

  @UseGuards(JwtAuthGuard)
  @Post('update-task-preferences')
  async updateTaskPreferences(@Request() req: any, @Body() body: any) {
    const { userId } = req.user;
    return this.service.updateTaskPreferences(userId, body);
  }
  @UseGuards(JwtAuthGuard)
  @Post('update-approval-preferences')
  async updateApprovalPreferences(@Request() req: any, @Body() body: any) {
    const { userId } = req.user;
    return this.service.updateApprovalPreferences(userId, body);
  }

  @UseGuards(JwtAuthGuard)
  @Post('updateWeekendData')
  async updateWeekend(@Request() req: any, @Body() body: UpdateHolidayPreferences) {
    const { userId } = req.user;
    const user = User.findOne({
      where: {
        id: userId,
      },
    });
    return this.service.updateWeekendData(userId, body, (await user).organization.id);
  }

  @UseGuards(JwtAuthGuard)
  @Patch('deleteHoliday')
  async deleteHoliday(@Request() req: any, @Body() body: UpdateHolidayPreferences) {
    const { userId } = req.user;
    const user = User.findOne({
      where: {
        id: userId,
      },
    });
    return this.service.deleteHoliday(userId, body, (await user).organization.id);
  }

  @Get('getClientAction')
  async getAction(@Request() req: any) {
    return ClientNotificationAction;
  }

  @UseGuards(JwtAuthGuard)
  @Get('getClientPreferences')
  async getClientPreferences(@Request() req: any) {
    const { userId } = req.user;
    return this.service.getClientPreferences(userId);
  }

  @UseGuards(JwtAuthGuard)
  @Put('getClientPreferences')
  async updateClientPreferences(@Request() req: any, @Body() body) {
    const { userId } = req.user;
    return this.service.updateClientPreferences(userId, body);
  }

  @UseGuards(JwtAuthGuard)
  @Post('addClientPrefix')
  async addClientPrefix(@Request() req: any, @Body() body) {
    const { userId } = req.user;
    return this.service.addClientPrefix(userId, body);
  }

  @UseGuards(JwtAuthGuard)
  @Get('clientPrefix')
  async getClientPrefix(@Request() req: any) {
    const { userId } = req.user;
    return this.service.getClientPrefix(userId);
  }

  @UseGuards(JwtAuthGuard)
  @Get('clientPrefixId')
  async getClientPrefixId(@Request() req: any) {
    const { userId } = req.user;
    return this.service.getClientPrefixId(userId);
  }

  @UseGuards(JwtAuthGuard)
  @Get('clientGroupPrefixId')
  async getClientGroupPrefixId(@Request() req: any) {
    const { userId } = req.user;
    return this.service.getClientGroupPrefixId(userId);
  };

  @UseGuards(JwtAuthGuard)
  @Get('page-limit')
  async getDefaultPageLimit(@Request() req: any,) {
    const { userId } = req.user;
    return this.service.getDefaultPageLimit(userId);
  };

  @UseGuards(JwtAuthGuard)
  @Patch('update-limit')
  async updatePageLimit(@Request() req: any, @Body() body: any) {
    const { userId } = req.user;
    return this.service.updatePageLimit(userId, body);
  }


   @Get('organizationPreferences')
    async get(@Request() req: any) {
      return OrganizationNotification;
    }

     @UseGuards(JwtAuthGuard)
      @Put('update-preferences')
      async updatePreferencesNew(@Request() req: any, @Body() body) {
        const { userId } = req.user;
        return this.service.updatePreferences(userId, body);
      }
}
