import {
    Body,
    Controller,
    Delete,
    Get,
    Param,
    ParseIntPipe,
    Post,
    Put,
    Query,
    Req,
    Request,
    UseGuards,
  } from '@nestjs/common';
  import { JwtAuthGuard } from 'src/modules/users/jwt/jwt-auth.guard';
import { UdinTaskService } from './udin-task.service';
  
  @Controller('udin-task')
  export class UdinTaskController {
    constructor(private readonly service: UdinTaskService) {}
  
    @UseGuards(JwtAuthGuard)
    @Get()
    getUdinTasks(@Request() req:any ,@Query() query:any) {
        const {userId} = req?.user;
      return this.service.getUdinTasks(query,userId);
    }
    @UseGuards(JwtAuthGuard)
    @Post('/udin-task/udin-tasksexport')
    async exportUdinTasksPageReport(@Req() req: any, @Body() body: any) {
      const { userId } = req?.user;
      const query= body;
      return this.service.exportUdinTasksPageReport(userId, query);
    }

    @UseGuards(JwtAuthGuard)
    @Get("/:id")
    getUdinTask(@Request() req:any ,@Query() query:any, @Param('id', ParseIntPipe) taskId: number) {
      const {userId} = req?.user;
      return this.service.getUdinTask(query,userId);
    }

    @UseGuards(JwtAuthGuard)
    @Put()
    updateUdinTaskDetails(
      @Body() body: any,
      @Request() req: any,
    ) {
      const { userId } = req?.user;
      return this.service.update( body, userId);
    }

    @UseGuards(JwtAuthGuard)
    @Post()
    createUdinTask(@Body() body:any, @Request() req:any){
        const {userId} = req.user;
        return this.service.createUdinTask(userId,body)
    }

    @UseGuards(JwtAuthGuard)
    @Put('/:id')
    updateUdinTask(@Body( ) body:any, @Request() req:any, @Param('id', ParseIntPipe) id: number ){
      const {userId} = req.user;
      return this.service.updateUdinTask(userId,id,body)
    }
  }
  