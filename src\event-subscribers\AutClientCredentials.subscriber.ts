import {
  Connection,
  EntitySubscriberInterface,
  EventSubscriber,
  InsertEvent,
  UpdateEvent,
} from 'typeorm';

import AutClientCredentials from 'src/modules/automation/entities/aut_client_credentials.entity';
import OrganizationPreferences from 'src/modules/organization-preferences/entity/organization-preferences.entity';
const axios = require('axios');

let clientOldDetails: AutClientCredentials;
@EventSubscriber()
export class AutClientCredentialsSubscriber
  implements EntitySubscriberInterface<AutClientCredentials>
{
  constructor(private readonly connection: Connection) {
    connection.subscribers.push(this);
  }

  listenTo() {
    return AutClientCredentials;
  }

  async beforeUpdate(event: UpdateEvent<AutClientCredentials>) {
    clientOldDetails = event?.databaseEntity;
  }

  async afterInsert(event: InsertEvent<AutClientCredentials>) {
    try {
      const { organizationId } = event.entity;
      const organizationPreferences = await OrganizationPreferences.createQueryBuilder('orgPref')
        .where('orgPref.organization = :organizationId', { organizationId })
        .andWhere("JSON_EXTRACT(orgPref.automation_config, '$.incomeTax') = 'YES'")
        .getOne();
      if (organizationPreferences) {
        let data = '';
        let config = {
          method: 'get',
          maxBodyLength: Infinity,
          url: `${process.env.CAMUNDA_URL}/vider/quantum/api/automation/bulk/sync/${organizationId}/incometax`,
          headers: {},
          data: data,
        };

        axios
          .request(config)
          .then((response) => {
            if (response?.data) {
              const schedule = JSON.parse(response?.data?.schedule);
              let data1 = JSON.stringify({
                modules: ['OD', 'EP'],
                orgId: organizationId,
                type: 'INCOMETAX',
                schedules: schedule,
              });

              let config1 = {
                method: 'post',
                maxBodyLength: Infinity,
                url: `${process.env.CAMUNDA_URL}/vider/quantum/api/automation/bulk/sync`,
                headers: {
                  'X-USER-ID': response.data.userId,
                  'Content-Type': 'application/json',
                },
                data: data1,
              };

              axios
                .request(config1)
                .then((response) => {
                })
                .catch((error) => {
                  console.log('error in scheduling call in subscriber camunda', error.message);
                });
            }
          })
          .catch((error) => {
            console.log('error in scheduling call in subscriber camunda', error.message);
          });
      }
    } catch (error) {
      console.log('Error occur in AutClientCredentialsSubscriber', error);
    }
  }

  async afterUpdate(event: UpdateEvent<AutClientCredentials>) {
    try {
      const { status, organizationId } = event.entity;
      if (clientOldDetails.status !== status) {
        const organizationPreferences = await OrganizationPreferences.createQueryBuilder('orgPref')
          .where('orgPref.organization = :organizationId', { organizationId })
          .andWhere("JSON_EXTRACT(orgPref.automation_config, '$.incomeTax') = 'YES'")
          .getOne();
        if (organizationPreferences) {
          let data = '';
          let config = {
            method: 'get',
            maxBodyLength: Infinity,
            url: `${process.env.CAMUNDA_URL}/vider/quantum/api/automation/bulk/sync/${organizationId}/incometax`,
            headers: {},
            data: data,
          };

          axios
            .request(config)
            .then((response) => {
              if (response.data) {
                const schedule = JSON.parse(response?.data?.schedule);
                let data1 = JSON.stringify({
                  modules: ['OD', 'EP'],
                  orgId: organizationId,
                  type: 'INCOMETAX',
                  schedules: schedule,
                });

                let config1 = {
                  method: 'post',
                  maxBodyLength: Infinity,
                  url: `${process.env.CAMUNDA_URL}/vider/quantum/api/automation/bulk/sync`,
                  headers: {
                    'X-USER-ID': response.data.userId,
                    'Content-Type': 'application/json',
                  },
                  data: data1,
                };

                axios
                  .request(config1)
                  .then((response) => {
                  })
                  .catch((error) => {
                    console.log('error in scheduling call in subscriber camunda', error.message);
                  });
              }
            })
            .catch((error) => {
              console.error(
                'error in scheduling call in subscriber camunda Error Message:',
                error.message,
              );
            });
        }
      }
    } catch (error) {
      console.log('Error occur in AutClientCredentialsSubscriber', error);
    }
  }
}
