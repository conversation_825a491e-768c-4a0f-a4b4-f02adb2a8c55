export enum Event_Actions {
  ORG_REGISTERED = 'ORG_REGISTERED',
  CLIENT_CREATED = 'CLIENT_CREATED',
  CLIENT_USER_CREATED = 'CLIENT_USER_CREATED',
  CLIENT_USER_UPDATED = 'CLIENT_USER_UPDATED',
  CLIENT_USER_DELETED = 'CLIENT_USER_DELETED',
  CLIENT_UPDATED = 'CLIENT_UPDATED',
  CLIENT_GROUP_CREATED = "CLIENT_GROUP_CREATED",
  CLIENT_GROUP_UPDATED = "CLIENT_GROUP_UPDATED",
  CLIENTS_GROUP_CLIENTS_UPDATED = "CLIENTS_ADDED_TO_GROUP",
  CLIENTS_GROUP_CLIENTS_DELETED = 'CLIENTS_REMOVED_FROM_GROUP',
  CLIENT_GROUP_CLIENTS_DELETED = 'CLIENT_REMOVED_FROM_GROUP',
  CLIENT_GROUP_CLIENTS_UPDATED = "CLIENT_ADDED_TO_GROUP",
  CLIENT_GROUP_STATUS_UPDATED = 'CLIENT_GROUP_STATUS_UPDATED',
  CLIENT_GROUP_DELETED = "CLIENT_GROUP_DELETED",
  CLIENT_GROUP_RESTORED = "CLIENT_GROUP_RESTORED",
  TASK_SCHEDULED = 'TASK_SCHEDULED',
  TASK_CREATED = 'TASK_CREATED',
  TASK_UPDATED = 'TASK_UPDATED',
  TASK_STATUS_UPDATED = 'TASK_STATUS_UPDATED',
  TASK_STATUS_CHANGED = 'TASK_STATUS_CHANGED',
  TASK_MEMBERS_UPDATED = 'TASK_MEMBERS_UPDATED',
  TASK_LEADERS_UPDATED = 'TASK_LEADERS_UPDATED',
  BUDGETED_HOURS_UPDATED = 'BUDGETED_HOURS_UPDATED',
  PRIORITY_UPDATED = 'PRIOIRTY_UPDATED',
  START_DATE_UPDATED = 'START_DATE_UPDATED',
  STATUTORY_DUE_DATE_CHANGED = 'STATUTORY_DUE_DATE_UPDATED',
  EXPECTED_COMPLETEION_DATE_CHANGED = 'EXPECTED_COMPLETEION_DATE_UPDATED',
  UDIN_ADDED = 'UDIN_ADDED',
  UDIN_UPDATED = 'UDIN_UPDATED',
  UDIN_REMOVED = 'UDIN_REMOVED',
  BILLING_TYPE_CHANGED = 'BILLING_TYPE_CHANGED',
  FEE_TYPE_UPDATED = 'FEE_TYPE_UPDATED',
  FEE_UPDATED = 'FEE_UPDATED',
  TASK_FEE_REMOVED = 'TASK_FEE_REMOVED',
  DESCRIPTION_UPDATED = 'DESCRIPTION_UPDATED',
  DESCRIPTION_REMOVED = 'DESCRIPTION_REMOVED',
  LABELS_UPDATED = 'LABELS_UPDATED',
  DIRECTORY_UPDATED = 'DIRECTORY_UPDATED',
  DATA_COLLECTION_LINK_CREATED = 'DATA_COLLECTION_LINK_CREATED',
  DATA_COLLECTION_LINK_UPDATED = 'DATA_COLLECTION_LINK_UPDATED',
  DATA_COLLECTION_LINK_EXPIRED = 'DATA_COLLECTION_LINK_EXPIRED',
  CHECKLIST_ADDED = 'CHECKLIST_ADDED',
  CHECKLIST_UPDATED = 'CHECKLIST_UPDATED',
  CHECKLIST_ITEM_ADDED = 'CHECKLIST_ITEM_ADDED',
  CHECKLIST_ITEM_UPDATED = 'CHECKLIST_ITEM_UPDATED',
  CHECKLIST_DELETED = 'CHECKLIST_DELETED',
  CHECKLIST_ITEM_DELETED = 'CHECKLIST_ITEM_DELETED',
  COMMENT_REPLIED = 'COMMENT_REPLIED',
  EXPENDITURE_UPDATED = 'EXPENDITURE_UPDATED',
  EXPENDITURE_DELETED = 'EXPENDITURE_DELETED',
  LOG_HOURS_ADDED = 'LOG_HOURS_ADDED',
  LOG_HOURS_UPDATED = 'LOG_HOURS_UPDATED',
  LOG_HOURS_DELETED = 'LOG_HOURS_DELETED',
  APPROVAL_REMOVED = 'APPROVAL_REMOVED',
  APPROVAL_ADDED = 'APPROVAL_ADDED',
  APPROVAL_UPDATED = 'APPROVAL_UPDATED',
  APPROVAL_APPROVED = 'APPROVAL_APPROVED',
  APPROVAL_REJECTED = 'APPROVAL_REJECTED',
  TASK_RECEIVED_FOR_REVIEW = 'TASK_RECEIVED_FOR_REVIEW',
  EVENT_UPDATED = 'EVENT_UPDATED',
  EVENT_DELETED = 'EVENT_DELETED',
  QUANTUM_DOCUMENT_DOWNLOADED = 'QUANTUM_DOCUMENT_DOWNLOADED',
  ATTACHEMENT_ADDED = 'ATTACHEMENT_ADDED',
  LOCAL_DIRECTORY_PATH_ADDED = 'LOCAL_DIRECTORY_PATH_ADDED',
  LOCAL_DIRECTORY_PATH_REMOVED = 'LOCAL_DIRECTORY_PATH_REMOVED',
  CLIENT_PROFILE_UPDATED = 'CLIENT_PROFILE_UPDATED',
  CLIENT_PORTAL_ACCESS = 'CLIENT_PORTAL_ACCESS',
  CLIENT_PORTAL_ACCESS_UPDATED = 'CLIENT_PORTAL_ACCESS_UPDATED',
  CLIENT_STATUS_UPDATED = 'CLIENT_STATUS_UPDATED',
  CLIENT_DELETED = 'CLIENT_DELETED',
  CLIENT_RESTORED = 'CLIENT_RESTORED',
  CLIENT_STATUS_CHANGE = 'CLIENT_STATUS_CHANGE',
  ATTACHMENT_UPLOADED = 'ATTACHMENT_UPLOADED',
  ATTACHEMENT_DELETED = 'ATTACHEMENT_DELETED',
  KYB_INFO_ADDED = 'KYB_INFO_ADDED',
  KYB_INFO_UPDATED = 'KYB_INFO_UPDATED',
  KYB_INFO_DELETED = 'KYB_INFO_DELETED',
  CREDENTIALS_ADDED = 'CREDENTIALS_ADDED',
  CREDENTIALS_UPDATED = 'CREDENTIALS_UPDATED',
  CREDENTIALS_DELETED = 'CREDENTIALS_DELETED',
  RECURRING_PROFILE_CREATED = 'RECURRING_PROFILE_CREATED',
  RECURRING_PROFILE_UPDATED = 'RECURRING_PROFILE_UPDATED',
  RECURRING_PROFILE_TERMINATED = 'RECURRING_PROFILE_TERMINATED',
  DSC_RECORD_ADDED = 'DSC_RECORD_ADDED',
  DSC_RECORD_UPDATED = 'DSC_RECORD_UPDATED',
  DSC_RECORD_DELETED = 'DSC_RECORD_DELETED',
  DSC_RECORD_REMOVED = 'DSC_RECORD_REMOVED',
  INVOICE_CREATED = 'INVOICE_CREATED',
  PROFORMA_INVOICE_CREATED = 'PROFORMA_INVOICE_CREATED',
  INVOICE_UPDATED = 'INVOICE_UPDATED',
  PROFORMA_INVOICE_UPDATED = 'PROFORMA_INVOICE_UPDATED',
  INVOICE_CANCELLED = 'INVOICE_CANCELLED',
  PROFORMA_INVOICE_CANCELLED = 'PROFORMA_INVOICE_CANCELLED',
  PROFORMA_INVOICE_CONVERTED = 'PROFORMA_INVOICE_CONVERTED',
  ADVANCE_RECEIPT_CREATED = 'ADVANCE_RECEIPT_CREATED',
  PAYMENT_RECEIPT_CREATED = 'PAYMENT_RECEIPT_CREATED',
  ADVANCE_RECEIPT_UPDATED = 'ADVANCE_RECEIPT_UPDATED',
  PAYMENT_RECEIPT_UPDATED = 'PAYMENT_RECEIPT_UPDATED',
  PAYMENT_RECEIPT_CANCELLED = 'PAYMENT_RECEIPT_CANCELLED',
  ADVANCE_RECEIPT_CANCELLED = 'ADVANCE_RECEIPT_CANCELLED',
  INVOICE_PARTIALLY_PAID = 'INVOICE_PARTIALLY_PAID',
  INVOICE_PAID = 'INVOICE_PAID',
  TASK_RESTORED = 'TASK_RESTORED',
  TASK_DELETED = 'TASK_DELETED',
  TASK_TERMINATED = 'TASK_TERMINATED',
  FORM_CREATED = 'FORM_CREATED',
  FORM_UPDATED = 'FORM_UPDATED',
  FORM_DELETED = 'FORM_DELETED',
  USER_INVITED = 'USER_INVITED',
  USER_RE_INVITED = 'USER_RE_INVITED',
  USER_JOINED = 'USER_JOINED',
  MILESTONE_CREATED = 'MILESTONE_CREATED',
  MILESTONE_UPDATED = 'MILESTONE_UPDATED',
  MILESTONE_DELETED = 'MILESTONE_DELETED',
  SUBTASK_CREATED = 'SUBTASK_CREATED',
  EVENT_CREATED = 'EVENT_CREATED',
  LEAD_CREATED = 'LEAD_CREATED',
  EXPENDITURE_ADDED = 'EXPENDITURE_ADDED',
  EXPENDITURE_CREATED = 'EXPENDITURE_CREATED',
  SERVICE_ADDED = 'SERVICE_ADDED',
  SERVICE_UPDATED = 'SERVICE_UPDATED',
  ORG_UPDATED = 'ORG_UPDATED',
  COMMENT_ADDED = 'COMMENT_ADDED',
  CREDENTIAL_CREATED = 'CREDENTIAL_CREATED',
  CREDENTIAL_UPDATED = 'CREDENTIAL_UPDATED',
  DSC_REGISTER_ISSUED = 'DSC_REGISTER_ISSUED',
  DSC_REGISTER_RECEIVED = 'DSC_REGISTER_RECEIVED',
  EXPORT_INVOICES = 'EXPORT_INVOICES',
  EXPORT_PROFORMA_INVOICES = 'EXPORT_PROFORMA_INVOICES',
  REPORT_GENERATED = 'REPORT_GENERATED',
  INVOICE_DOWNLOADED = 'INVOICE_DOWNLOADED',
  PROFORMA_INVOICE_DOWNLOADED = 'PROFORMA_INVOICE_DOWNLOADED',
  ORGANIZATION_ADMIN_USER = 'ORGANIZATION_ADMIN_USER',
  TASK_STATUS_TO_UNDER_REVIEW = 'TASK_STATUS_TO_UNDER_REVIEW',
  TASK_STATUS_TO_UNDER_REVIEW_APPROVE = 'TASK_STATUS_TO_UNDER_REVIEW_APPROVE',
  CAMUNDA_TASK_APPROVAL = 'CAMUNDA_TASK_APPROVAL',
  CAMUNDA_TASK_DECLINE = 'CAMUNDA_TASK_DECLINE',
  CAMUNDA_TASK_APPROVAL_COMPLETE = 'CAMUNDA_TASK_APPROVAL_COMPLETE',
  COLLECT_DATA_FILES_UPLOAD = 'COLLECT_DATA_FILES_UPLOAD',
  COLLECT_DATA_FILES_DELETED = 'COLLECT_DATA_FILES_DELETED',
  COLLECT_DATA_FILES_CONFIRM_REJECT = 'COLLECT_DATA_FILES_CONFIRM_REJECT'
}
