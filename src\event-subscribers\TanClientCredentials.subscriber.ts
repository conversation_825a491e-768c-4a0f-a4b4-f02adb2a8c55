import {
  Connection,
  EntitySubscriberInterface,
  EventSubscriber,
  InsertEvent,
  UpdateEvent,
} from 'typeorm';

import OrganizationPreferences from 'src/modules/organization-preferences/entity/organization-preferences.entity';
import TanClientCredentials from 'src/modules/tan-automation/entity/tan-client-credentials.entity';
const axios = require('axios');

let clientOldDetails: TanClientCredentials;
@EventSubscriber()
export class TanClientCredentialsSubscriber
  implements EntitySubscriberInterface<TanClientCredentials>
{
  constructor(private readonly connection: Connection) {
    connection.subscribers.push(this);
  }

  listenTo() {
    return TanClientCredentials;
  }

  async beforeUpdate(event: UpdateEvent<TanClientCredentials>) {
    clientOldDetails = event?.databaseEntity;
  }

  async afterInsert(event: InsertEvent<TanClientCredentials>) {
    try {
      const { organizationId } = event.entity;
      const organizationPreferences = await OrganizationPreferences.createQueryBuilder('orgPref')
        .where('orgPref.organization = :organizationId', { organizationId })
        .andWhere("JSON_EXTRACT(orgPref.automation_config, '$.tan') = 'YES'")
        .getOne();
      if (organizationPreferences) {
        let data = '';
        let config = {
          method: 'get',
          maxBodyLength: Infinity,
          url: `${process.env.CAMUNDA_URL}/vider/quantum/api/automation/bulk/sync/${organizationId}/tan`,
          headers: {},
          data: data,
        };

        axios
          .request(config)
          .then((response) => {
            if (response?.data) {
              const schedule = JSON.parse(response?.data?.schedule);
              let data1 = JSON.stringify({
                modules: ['P', 'F', 'EC'],
                orgId: organizationId,
                type: 'TAN',
                schedules: schedule,
              });

              let config1 = {
                method: 'post',
                maxBodyLength: Infinity,
                url: `${process.env.CAMUNDA_URL}/vider/quantum/api/automation/bulk/sync`,
                headers: {
                  'X-USER-ID': response.data.userId,
                  'Content-Type': 'application/json',
                },
                data: data1,
              };

              axios
                .request(config1)
                .then((response) => {
                })
                .catch((error) => {
                  console.log('error in scheduling call in subscriber camunda', error.message);
                });
            }
          })
          .catch((error) => {
            console.log('error in scheduling call in subscriber camunda', error.message);
          });
      }
    } catch (error) {
      console.log('Error occur in AutClientCredentialsSubscriber', error);
    }
  }

  async afterUpdate(event: UpdateEvent<TanClientCredentials>) {
    try {
      const { status, organizationId } = event.entity;
      if (clientOldDetails.status !== status) {
        const organizationPreferences = await OrganizationPreferences.createQueryBuilder('orgPref')
          .where('orgPref.organization = :organizationId', { organizationId })
          .andWhere("JSON_EXTRACT(orgPref.automation_config, '$.tan') = 'YES'")
          .getOne();
        if (organizationPreferences) {
          let data = '';
          let config = {
            method: 'get',
            maxBodyLength: Infinity,
            url: `${process.env.CAMUNDA_URL}/vider/quantum/api/automation/bulk/sync/${organizationId}/tan`,
            headers: {},
            data: data,
          };

          axios
            .request(config)
            .then((response) => {
              if (response.data) {
                const schedule = JSON.parse(response?.data?.schedule);
                let data1 = JSON.stringify({
                  modules: ['P', 'F', 'EC'],
                  orgId: organizationId,
                  type: 'TAN',
                  schedules: schedule,
                });

                let config1 = {
                  method: 'post',
                  maxBodyLength: Infinity,
                  url: `${process.env.CAMUNDA_URL}/vider/quantum/api/automation/bulk/sync`,
                  headers: {
                    'X-USER-ID': response.data.userId,
                    'Content-Type': 'application/json',
                  },
                  data: data1,
                };

                axios
                  .request(config1)
                  .then((response) => {
                  })
                  .catch((error) => {
                    console.log('error in scheduling call in subscriber camunda', error.message);
                  });
              }
            })
            .catch((error) => {
              console.error(
                'error in scheduling call in subscriber camunda Error Message:',
                error.message,
              );
            });
        }
      }
    } catch (error) {
      console.log('Error occur in AutClientCredentialsSubscriber', error);
    }
  }
}
