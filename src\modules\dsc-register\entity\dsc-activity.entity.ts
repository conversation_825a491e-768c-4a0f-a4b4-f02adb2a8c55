import { BaseEntity, Column, CreateDateColumn, Entity, ManyToOne, PrimaryGeneratedColumn } from 'typeorm';
import DscRegister from './dsc-register.entity';

export enum DscActivityTypeEnum {
  ISSUE = 'issue',
  RECEIVE = 'receive',
}

@Entity()
class DscActivity extends BaseEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  personName: string;

  @Column({ type: 'enum', enum: DscActivityTypeEnum })
  type: DscActivityTypeEnum;

  @ManyToOne(() => DscRegister, (dscRegister) => dscRegister.dscActivity, { onDelete: 'CASCADE' })
  dscRegister: DscRegister;

  @CreateDateColumn()
  date: string;

  @Column()
  whatsappCheck: boolean;

  @Column()
  emailCheck: boolean;
}

export default DscActivity;
