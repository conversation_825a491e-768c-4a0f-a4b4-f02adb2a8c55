import Client from 'src/modules/clients/entity/client.entity';
import Storage from 'src/modules/storage/storage.entity';
import { BaseEntity, Column, Entity, ManyToOne, OneToMany, PrimaryGeneratedColumn } from 'typeorm';

export enum CreatedType {
  MANUAL = 'MANUAL',
  PORTAL = 'PORTAL',
}

export enum StorageSystem {
  AMAZON = 'AMAZON',
  MICROSOFT = 'MICROSOFT',
  GOOGLE = 'GOOGLE',
  BHARATHCLOUD = 'BHARATHCLOUD',
}

@Entity()
class GstrNoticeOrders extends BaseEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  orderNumber: string;

  @Column()
  issuedBy: string;

  @Column()
  type: string;

  @Column()
  description: string;

  @Column()
  dueDate: string;

  @Column()
  dateOfIssuance: string;

  @Column()
  amountOfDemand: string;

  @Column('json')
  attatchments: object;

  @Column({ type: 'enum', enum: StorageSystem, default: null })
  storageSystem: StorageSystem;

  @Column({ type: 'enum', enum: CreatedType, default: null })
  createdType: CreatedType;

  @ManyToOne(() => Client, (client) => client.gstrNoticeOrders, { onDelete: 'SET NULL' })
  client: Client;

  @Column()
  organizationId: number;

  @Column()
  gstrCredentialsId: number;

  @Column()
  appDefId: string;

  @Column()
  gstIn: string;

  @OneToMany(() => Storage, (storage) => storage.gstrNoticeOrders, {
    cascade: true,
  })
  storage: Storage[];
}

export default GstrNoticeOrders;
