import { BadRequestException, Injectable } from '@nestjs/common';
import * as moment from 'moment';
import Client from 'src/modules/clients/entity/client.entity';
import DscRegister from 'src/modules/dsc-register/entity/dsc-register.entity';
import Lead, { LeadStatusEnum } from 'src/modules/leads/lead.entity';
import Task from 'src/modules/tasks/entity/task.entity';
import { User, UserStatus, UserType } from 'src/modules/users/entities/user.entity';
import { createQueryBuilder, getManager, In, Not } from 'typeorm';
import { IQueryWeeklyLogHoursDto } from './stats.controller';
import { dateFormation } from 'src/utils/datesFormation';
import Attendance from '../attendance/attendance.entity';
import OrganizationPreferences from '../organization-preferences/entity/organization-preferences.entity';
import * as ExcelJS from 'exceljs'
import ReceiptParticular, {
  ReceiptParticularStatus,
} from '../billing/entitities/receipt-particular.entity';
import { InvoiceStatus } from '../billing/entitities/invoice.entity';
import ReceiptCredit, {
  CreditType,
  ReceiptCreditStatus,
} from '../billing/entitities/receipt-credit.entity';
import * as xlsx from 'xlsx';
import { LogHourType } from '../log-hours/log-hour.entity';
import { BudgetedHourStatus } from '../budgeted-hours/budgeted-hours.entity';
import { formatDate, getTitle } from 'src/utils';
import { Organization } from '../organization/entities/organization.entity';
import { CLIENT_CATEGORIES } from 'src/utils/clientExport';
import { trusted } from 'mongoose';
import { ReceiptStatus, ReceiptType } from '../billing/entitities/receipt.entity';
import { FindBillingAmounts } from './dto/find-biling-amounts.dto';
import { FindOutStandingClients } from './dto/find-outStanding-clients.dto';
import { Response } from "express";

@Injectable()
export class StatsService {
  async getTaskAnalytics(userId: number, query: any) {
    let user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
    const organizationPreferences = await OrganizationPreferences.findOne({
      where: { organization: user.organization.id },
      order: { id: 'DESC' },
    });
    const completedDays = organizationPreferences?.taskPreferences?.['taskDate'] || 15;
    const date = moment().subtract(completedDays, 'days').format('YYYY-MM-DD HH:mm:ss');
    let sql = `
    select task.status as status, count(task.id) as count,
    sum(case when task.recurring = true then 1 else 0 end) as recurring,
    sum(case when task.recurring = false then 1 else 0 end) as non_recurring,
    sum(case when task.priority = 'high' then 1 else 0 end) as high,
    sum(case when task.priority = 'low' then 1 else 0 end) as low,
    sum(case when task.priority = 'medium' then 1 else 0 end) as medium,
    sum(case when task.priority = 'none' then 1 else 0 end) as none
    from task`;

    if (query.dashboardType === 'user') {
      sql += ` left join task_members_user tmu on tmu.task_id = task.id`;
    }

    if (query.clientId && query.clientId !== '') {
      sql += ` left join client c on c.id = task.client_id`;
    }

    sql += ` where task.organization_id = ${user.organization.id} and task.status not in ('terminated','deleted','completed')`;

    if (query.dashboardType === 'user') {
      sql += ` and tmu.user_id = ${userId}`;
    }

    if (query.clientId && query.clientId !== '') {
      sql += ` and c.id = ${Number(query.clientId)}`;
    }

    sql += ` and task.parent_task_id is null
    and (task.recurring_status is null or task.recurring_status = 'created')
    and task.status <> ''
    group by task.status;`;

    let tasks = await getManager().query(sql);
    let completedSql = `
    SELECT
        task.status AS status,
        COUNT(task.id) AS count,
        SUM(CASE WHEN task.recurring = true THEN 1 ELSE 0 END) AS recurring,
        SUM(CASE WHEN task.recurring = false THEN 1 ELSE 0 END) AS non_recurring,
        SUM(CASE WHEN task.priority = 'high' THEN 1 ELSE 0 END) AS high,
        SUM(CASE WHEN task.priority = 'low' THEN 1 ELSE 0 END) AS low,
        SUM(CASE WHEN task.priority = 'medium' THEN 1 ELSE 0 END) AS medium,
        SUM(CASE WHEN task.priority = 'none' THEN 1 ELSE 0 END) AS none
    FROM
        task `;
    if (query.dashboardType === 'user') {
      completedSql += ` left join task_members_user tmu on tmu.task_id = task.id`;
    }

    if (query.clientId && query.clientId !== '') {
      completedSql += ` left join client c on c.id = task.client_id`;
    }

    completedSql += ` WHERE task.organization_id = ${user.organization.id} AND task.status = 'completed'`;

    if (query.dashboardType === 'user') {
      completedSql += ` and tmu.user_id = ${userId}`;
    }

    if (query.clientId && query.clientId !== '') {
      completedSql += ` and c.id = ${Number(query.clientId)}`;
    }

    completedSql += ` AND task.status_updated_at >= STR_TO_DATE('${date}', '%Y-%m-%d %H:%i:%s')
      AND task.parent_task_id IS NULL
      AND (task.recurring_status IS NULL OR task.recurring_status = 'created')
      AND task.status <> ''
    GROUP BY
      task.status;`;
    let completedtasks = await getManager().query(completedSql);
    tasks = [...tasks, ...completedtasks];

    let recurringTasks = 0;
    let nonRecurringTasks = 0;
    let tasksByStatus: any = {
      todo: 0,
      in_progress: 0,
      under_review: 0,
      on_hold: 0,
      completed: 0,
    };
    let tasksByPriority: any = {
      high: 0,
      low: 0,
      medium: 0,
      none: 0,
    };

    tasks.forEach((task: any) => {
      tasksByStatus[task.status] = +task.count || 0;
      tasksByPriority['high'] += +task['high'] || 0;
      tasksByPriority['low'] += +task['low'] || 0;
      tasksByPriority['medium'] += +task['medium'] || 0;
      tasksByPriority['none'] += +task['none'] || 0;
      recurringTasks += +task.recurring || 0;
      nonRecurringTasks += +task.non_recurring || 0;
    });

    let totalTasks = recurringTasks + nonRecurringTasks;
    let recurringTasksPercentage = Math.round((recurringTasks / totalTasks) * 100);
    let nonRecurringTasksPercentage = Math.round((nonRecurringTasks / totalTasks) * 100);

    return {
      total:
        tasksByStatus.todo +
        tasksByStatus.in_progress +
        tasksByStatus.under_review +
        tasksByStatus.on_hold +
        tasksByStatus.completed,
      tasksByStatus,
      tasksByPriority,
      recurringTasks,
      nonRecurringTasks,
      recurringTasksPercentage: recurringTasksPercentage || 0,
      nonRecurringTasksPercentage: nonRecurringTasksPercentage || 0,
    };
  }

  async getTasksDueThisWeek(userId: number, query: any) {
    let user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
    let tasks = createQueryBuilder(Task, 'task')
      .leftJoin('task.organization', 'organization')
      .leftJoin('task.members', 'taskMembers')
      .leftJoinAndSelect('task.category', 'category')
      .leftJoinAndSelect('task.members', 'members')
      .leftJoinAndSelect('members.imageStorage', 'imageStorage')
      .leftJoinAndSelect('task.client', 'client')
      .leftJoinAndSelect('task.clientGroup', 'clientGroup')
      .where('organization.id = :id', { id: user.organization.id })
      .andWhere('task.status not in (:...status)', {
        status: ['terminated', 'deleted', 'completed'],
      })
      .andWhere("(task.recurring_status is null or task.recurring_status = 'created')")
      .andWhere('task.parentTask is null')
      .andWhere('task.dueDate between :start and :end', {
        start: moment().isoWeekday(1).format('YYYY-MM-DD'),
        end: moment().isoWeekday(7).format('YYYY-MM-DD'),
      })
      .orderBy('task.dueDate', 'ASC');

    if (query.dashboardType === 'user') {
      tasks.andWhere('taskMembers.id = :userId', { userId });
    }

    if (query.clientId && query.clientId !== "") {
      tasks.andWhere('client.id = :clientId', { clientId: query.clientId });
    }

    let result = await tasks.getMany();

    return result;
  }

  async getTasksByCategory(userId: number, query: any) {
    let user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
    const date = moment().subtract(15, 'days').format('YYYY-MM-DD HH:mm:ss');
    const sort = (typeof query?.sort === "string") ? JSON.parse(query.sort) : query?.sort;
    let sortQuery = '';
    if (sort?.column) {
      sortQuery = 'ORDER BY ' + `${sort.column} ${sort.direction}`;
      ;
    }
    let sql = `select category.name, category.id,
    sum(case when task.recurring = true then 1 else 0 end) as recurring,
    sum(case when task.recurring = false then 1 else 0 end) as non_recurring
    from task left join category on category.id = task.category_id
    `;

    if (query.dashboardType === 'user') {
      sql += ` left join task_members_user tmu on tmu.task_id = task.id`;
    }

    sql += ` where task.organization_id = ${user.organization.id}
    AND (task.status IN ('todo', 'in_progress', 'on_hold', 'under_review')
    OR (task.status = 'completed' AND task.status_updated_at >= STR_TO_DATE('${date}', '%Y-%m-%d %H:%i:%s')))
    and task.parent_task_id is null and (task.recurring_status is null or task.recurring_status = 'created')`;

    if (query.dashboardType === 'user') {
      sql += ` and tmu.user_id = ${userId}`;
    }

    if (query.fromDate && query.toDate) {
      const { startTime, endTime } = dateFormation(query.fromDate, query.toDate);
      sql += ` and Date(task.created_at) >= '${startTime}' and Date(task.created_at) <= '${endTime}'`;
    }

    sql += ` group by category.id having category.id is not null ${sortQuery}
      LIMIT ${query.pageCount ? query.pageCount : 10} OFFSET ${query.page ? query.page : 0}`;

    let tasks = await getManager().query(sql);
    return tasks;
  }

  async getTasksByCategoryExport(userId: number, query: any) {
    const newQuery = { ...query, offset: 0, limit: 100000000 };
    let services = await this.getTasksByCategory(userId, newQuery);

    if (!services || !services.length) {
      throw new BadRequestException('No Data for Export');
    }

    // Initialize a new workbook and worksheet
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Task by Service Category');

    // Define the headers for the worksheet
    const headers = [
      { header: 'S.No', key: 'serialNo' },
      { header: 'Service Category', key: 'serviceCategory' },
      { header: 'Recurring Tasks', key: 'recurringTasks' },
      { header: 'Non-Recurring Tasks', key: 'nonRecurringTasks' },
    ];

    // Add headers to the worksheet
    worksheet.columns = headers;
    let serialCounter = 1; // Initialize a counter
    // Map services data to rows
    const rows = services?.map((service) => ({
      serviceCategory: service?.name,
      serialNo: serialCounter++,// Assign and then increment the counter
      recurringTasks: 1 * service?.recurring,  // Ensure valid number
      nonRecurringTasks: 1 * service?.non_recurring,
    }));

    // Add rows to the worksheet
    rows.forEach((row) => worksheet.addRow(row));

    // Adjust column widths based on data length
    worksheet.columns.forEach((column) => {
      let maxLength = 10; // Minimum width for the column
      column.eachCell({ includeEmpty: true }, (cell) => {
        const cellValue = cell.value ? cell.value.toString() : '';
        maxLength = Math.max(maxLength, cellValue.length); // Get the max length for the column width
      });
      column.width = maxLength < 15 ? 15 : maxLength; // Ensure minimum width for readability
    });

    // Add styling and freeze the header row
    worksheet.getRow(1).font = { bold: true };
    worksheet.views = [{ state: 'frozen', ySplit: 1 }];

    // Apply additional styling (optional)
    worksheet.eachRow((row, rowNumber) => {
      row.eachCell((cell) => {
        cell.alignment = { vertical: 'middle', horizontal: 'center' };
        if (rowNumber === 1) {
          cell.fill = {
            type: 'pattern',
            pattern: 'solid',
            fgColor: { argb: '64B5F6' }, // Light blue background for the header
          };
          cell.border = {
            top: { style: 'thin' },
            left: { style: 'thin' },
            bottom: { style: 'thin' },
            right: { style: 'thin' },
          };
        }
      });
    });

    // Generate the Excel file as a buffer
    const buffer = await workbook.xlsx.writeBuffer();
    return buffer;
  }

  async getTasksByClientCategory(userId: number, query: any) {
    let user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
    const sort = (typeof query?.sort === "string") ? JSON.parse(query.sort) : query?.sort;
    let sortQuery = '';
    if (sort?.column) {
      sortQuery = 'ORDER BY ' + `${sort.column} ${sort.direction}`;
      ;
    } else {
      sortQuery = `ORDER BY name ASC`; // Default sorting by name in ascending order
    }
    let sql = `select category as name,
    sub_category as subCategoryname,
    client.id as clientId,
    sum(case when task.recurring = true then 1 else 0 end) as recurring,
    sum(case when task.recurring = false then 1 else 0 end) as non_recurring
    from task left join client on client.id = task.client_id`;

    if (query.dashboardType === 'user') {
      sql += ` left join task_members_user tmu on tmu.task_id = task.id`;
    }

    sql += ` where task.organization_id = ${user.organization.id}
    and task.status not in ('terminated','deleted')
    and task.parent_task_id is null and (task.recurring_status is null or task.recurring_status = 'created')`;

    if (query.dashboardType === 'user') {
      sql += ` and tmu.user_id = ${userId}`;
    }

    if (query.fromDate && query.toDate) {
      const { startTime, endTime } = dateFormation(query.fromDate, query.toDate);
      sql += ` and Date(task.created_at) >= '${startTime}' and Date(task.created_at) <= '${endTime}'`;
    }

    sql += ` group by client.category having clientId is not null ${sortQuery}
      LIMIT ${query.pageCount ? query.pageCount : 10} OFFSET ${query.page ? query.page : 0}`;

    let tasks = await getManager().query(sql);
    return tasks;
  }

  async getTasksByClientCategoryExport(userId: number, query: any) {
    const newQuery = { ...query, offset: 0, limit: 100000000 };
    let clients = await this.getTasksByClientCategory(userId, newQuery);

    if (!clients || !clients.length) {
      throw new BadRequestException('No Data for Export');
    }

    // Initialize a new workbook and worksheet
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Task by Client Category');

    // Define the headers for the worksheet
    const headers = [
      { header: 'S.No', key: 'serialNo' },
      { header: 'Client Category', key: 'clientCategory' },
      { header: 'Sub Category', key: 'subCategory' },
      { header: 'Recurring Tasks', key: 'recurringTasks' },
      { header: 'Non-Recurring Tasks', key: 'nonRecurringTasks' },
    ];

    // Add headers to the worksheet
    worksheet.columns = headers;
    let serialCounter = 1; // Initialize a counter


    // Map clients data to rows
    const rows = clients?.map((client) => {
      const category = CLIENT_CATEGORIES.find(item => item.value === client?.name)?.label || client?.category;

      return {
        serialNo: serialCounter++,// Assign and then increment the counter
        clientCategory: category, // Using getTitle for formatting
        subCategory: getTitle(client?.subCategoryname),
        recurringTasks: 1 * client?.recurring, // Ensure numeric conversion
        nonRecurringTasks: 1 * client?.non_recurring,
      };
    });


    // Add rows to the worksheet
    rows.forEach((row) => worksheet.addRow(row));

    // Adjust column widths dynamically
    worksheet.columns.forEach((column) => {
      let maxLength = 10; // Minimum width for the column
      column.eachCell({ includeEmpty: true }, (cell) => {
        const cellValue = cell.value ? cell.value.toString() : '';
        maxLength = Math.max(maxLength, cellValue.length);
      });
      column.width = maxLength < 15 ? 15 : maxLength; // Set minimum width for readability
    });

    // Style the header row
    worksheet.getRow(1).font = { bold: true };
    worksheet.views = [{ state: 'frozen', ySplit: 1 }];

    // Add optional cell styling
    worksheet.eachRow((row, rowNumber) => {
      row.eachCell((cell) => {
        cell.alignment = { vertical: 'middle', horizontal: 'center' };
        if (rowNumber === 1) {
          cell.fill = {
            type: 'pattern',
            pattern: 'solid',
            fgColor: { argb: '64B5F6' }, // Light blue for headers
          };
          cell.border = {
            top: { style: 'thin' },
            left: { style: 'thin' },
            bottom: { style: 'thin' },
            right: { style: 'thin' },
          };
        }
      });
    });

    // Generate the Excel file as a buffer
    const buffer = await workbook.xlsx.writeBuffer();
    return buffer;
  }

  async getTasksByService(userId: number, query: any) {
    let user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
    const sort = (typeof query?.sort === "string") ? JSON.parse(query.sort) : query?.sort;
    let sortQuery = '';
    if (sort?.column) {
      sortQuery = 'ORDER BY ' + `${sort.column} ${sort.direction}`;
      ;
    }
    else {
      sortQuery = 'ORDER BY count DESC';
    }
    let sql = `left join service s on s.id = task.service_id 
    left join log_hour lh on lh.task_id = task.id`;

    if (query.dashboardType === 'user') {
      sql += ` left join task_members_user tmu on tmu.task_id = task.id 
      
      `;
    }

    sql += ` where task.organization_id = ${user.organization.id}
    and task.status not in ('terminated','deleted')
    and (task.recurring_status is null or task.recurring_status = 'created')
    and task.parent_task_id is null
    `;

    if (query.fromDate && query.toDate) {
      const { startTime, endTime } = dateFormation(query.fromDate, query.toDate);
      sql += ` and Date(task.created_at) >= '${startTime}' and Date(task.created_at) <= '${endTime}'`;
    }

    if (query.dashboardType === 'user') {
      sql += ` and tmu.user_id = ${userId}`;
    }

    if (query.search) {
      sql += ` and s.name like '%${query.search}%'`;
    }

    sql += ` group by s.id having s.id is not null`;

    let countSql = `select count(s.id) from task ${sql}`;

    let resultSql = `select s.name, count(task.id) as count,
      sum(case when lh.id is not null then lh.duration else 0 end) as totalLogHours from task ${sql} 
       ${sortQuery}
      limit ${query?.offset || 0}, ${query?.limit || 100}`;

    let count = await getManager().query(countSql);
    let result = await getManager().query(resultSql);
    return {
      totalCount: count.length,
      result,
    };
  }

  async exportTasksByServiceReport(userId: number, query: any) {
    let services = await this.getTasksByService(userId, query);
    if (!services || !services.result.length) {
      throw new BadRequestException('No Data for Export');
    }

    // Initialize a new workbook and worksheet
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Task by Service Name');

    // Define the headers for the worksheet
    const headers = [
      { header: 'S.No', key: 'serialNo' },
      { header: 'Service Category', key: 'serviceCategory' },
      { header: 'Number of Tasks', key: 'numberOfTasks' },
      { header: 'Total Log Hours', key: 'totalLogHours' },
    ];

    // Add headers to the worksheet
    worksheet.columns = headers;
    let serialCounter = 1; // Initialize a counter

    // Map service data to rows
    const rows = services.result.map((service) => {
      const totalLogHours = service.totalLogHours;

      // Calculate hours and minutes from total milliseconds
      const hours = Math.floor(totalLogHours / (1000 * 60 * 60));
      const remainingMilliseconds = totalLogHours % (1000 * 60 * 60);
      const minutes = Math.floor(remainingMilliseconds / (1000 * 60));

      // Format hours and minutes as HH:MM
      const formattedTime = `${String(hours).padStart(2, '0')}:${String(minutes).padStart(2, '0')}`;

      return {
        serialNo: serialCounter++,// Assign and then increment the counter
        serviceCategory: (service?.name),
        numberOfTasks: 1 * service?.count, // Ensure numeric conversion
        totalLogHours: formattedTime,
      };
    });

    // Add rows to the worksheet
    rows.forEach((row) => worksheet.addRow(row));

    // Adjust column widths dynamically
    worksheet.columns.forEach((column) => {
      let maxLength = 10; // Minimum width for the column

      // Set specific column width and wrap text for 'Service Category'
      if (column.key === 'serviceCategory') {
        column.width = 100; // Set width to 100
        column.eachCell({ includeEmpty: true }, (cell) => {
          cell.alignment = {
            wrapText: true, // Enable wrap text
            vertical: 'middle',
            horizontal: 'center',
          };
        });
      } else {
        column.eachCell({ includeEmpty: true }, (cell) => {
          const cellValue = cell.value ? cell.value.toString() : '';
          maxLength = Math.max(maxLength, cellValue.length);
        });
        column.width = maxLength < 15 ? 15 : maxLength; // Set minimum width for readability
      }
    });

    // Style the header row
    worksheet.getRow(1).font = { bold: true };
    worksheet.views = [{ state: 'frozen', ySplit: 1 }];

    // Add optional cell styling
    worksheet.eachRow((row, rowNumber) => {
      row.eachCell((cell) => {
        cell.alignment = { vertical: 'middle', horizontal: 'center' };
        if (rowNumber === 1) {
          cell.fill = {
            type: 'pattern',
            pattern: 'solid',
            fgColor: { argb: '64B5F6' }, // Light blue for headers
          };
          cell.border = {
            top: { style: 'thin' },
            left: { style: 'thin' },
            bottom: { style: 'thin' },
            right: { style: 'thin' },
          };
        }
      });
    });

    // Generate the Excel file as a buffer
    const buffer = await workbook.xlsx.writeBuffer();
    return buffer;
  }

  async getOverDueTasks(userId: number, query: any) {
    let user = await User.findOne({ where: { id: userId }, relations: ['organization'] });

    let tasks = createQueryBuilder(Task, 'task')
      .leftJoin('task.organization', 'organization')
      .leftJoinAndSelect('task.category', 'category')
      .leftJoinAndSelect('task.members', 'members')
      .leftJoinAndSelect('task.client', 'client')
      .leftJoinAndSelect('task.clientGroup', 'clientGroup')
      .where('organization.id = :id', { id: user.organization.id })
      .andWhere('task.status not in (:...status)', {
        status: ['terminated', 'deleted', 'completed'],
      })
      .andWhere("(task.recurring_status is null or task.recurring_status = 'created')")
      .andWhere('task.dueDate < :date', { date: moment().format('YYYY-MM-DD') })
      .andWhere('task.parentTask is null')

      // .orderBy('task.dueDate', 'ASC')
      .skip(query?.offset || 0)
      .take(query?.limit || 1000);
    const sort = (typeof query?.sort === "string") ? JSON.parse(query.sort) : query?.sort;
    if (sort?.column) {
      const columnMap: Record<string, string> = {
        taskNumber: 'task.taskNumber',
        name: 'task.name',
        dueDate: 'task.dueDate',
        status: 'task.status',
        overDue: 'task.dueDate'
      };
      const column = columnMap[sort.column] || sort.column;
      tasks.orderBy(column, sort.direction.toUpperCase());
    } else {
      tasks.orderBy('task.dueDate', 'ASC');
    };
    if (query.search && query.search !== '') {
      tasks.andWhere('(client.displayName like :search OR clientGroup.displayName like :search OR task.name like :search)', {
        search: `%${query.search}%`,
      });
    }

    if (query.dashboardType === 'user') {
      tasks.andWhere('members.id = :userId', { userId });
    }

    if (query.fromDate && query.toDate) {
      const { startTime, endTime } = dateFormation(query.fromDate, query.toDate);
      tasks.andWhere('task.dueDate >= :startTime', {
        startTime,
      });
      tasks.andWhere('task.dueDate <= :endTime', {
        endTime,
      });

    }


    let result = await tasks.getManyAndCount();

    return {
      totalCount: result[1],
      result: result[0],
    };
  }
  async exportOverDueTasksReport(userId: number, query: any) {
    let tasks = await this.getOverDueTasks(userId, query);

    if (!tasks || !tasks.result.length) {
      throw new BadRequestException('No Data for Export');
    }

    // Initialize a new workbook and worksheet
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Overdue Tasks');

    // Define the headers for the worksheet
    const headers = [
      { header: 'S.No', key: 'serialNo' },
      { header: 'Client/Client Group', key: 'clientGroup', width: 50 },
      { header: 'Task ID', key: 'taskId' },
      { header: 'Task Name', key: 'taskName', width: 50 },
      { header: 'Task Status', key: 'taskStatus' },
      { header: 'Statutory Due Date', key: 'dueDate' },
      { header: 'Over Due By Days', key: 'overDueDays' },
      { header: 'Members', key: 'members', width: 50 },
    ];

    // Add headers to the worksheet
    worksheet.columns = headers;
    let serialCounter = 1; // Initialize a counter

    // Map task data to rows
    const rows = tasks.result.map((task) => {
      const overDueByDays = `${moment().diff(task?.dueDate, 'days')} days`;

      return {
        serialNo: serialCounter++,// Assign and then increment the counter
        clientGroup: task?.client?.displayName || '',
        taskId: task?.taskNumber || '',
        taskName: task?.name || '',
        taskStatus: getTitle(task?.status) || '',
        dueDate: formatDate(task?.dueDate) || '',
        overDueDays: overDueByDays,
        members: task?.members.map((assignee) => assignee.fullName).join(', ') || '',
      };
    });

    // Add rows to the worksheet
    rows.forEach((row) => {
      const worksheetRow = worksheet.addRow(row);

      // Conditional formatting for Task Status
      const statusCell = worksheetRow.getCell('taskStatus');
      switch (row.taskStatus.toLowerCase()) {
        case 'todo':
          statusCell.font = { color: { argb: '149ECD' }, bold: true };
          break;
        case 'in progress':
          statusCell.font = { color: { argb: 'F49752' }, bold: true };
          break;
        case 'on hold':
          statusCell.font = { color: { argb: 'F63338' }, bold: true };
          break;
        case 'under review':
          statusCell.font = { color: { argb: '653BBA' }, bold: true };
          break;
        case 'completed':
          statusCell.font = { color: { argb: '008000' }, bold: true };
          break;
        default:
          statusCell.font = { color: { argb: '000000' }, bold: true }; // Default black for others
          break;
      }
    });

    // Style the header row
    const headerRow = worksheet.getRow(1);
    headerRow.font = { bold: true };
    headerRow.alignment = { vertical: 'middle', horizontal: 'center' };
    headerRow.eachCell((cell) => {
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '64B5F6' },
      };
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' },
      };
    });
    const columnMaxLengths = Array(headers.length).fill(0);
    worksheet.columns.forEach((column, colIndex) => {
      const headerLength = column.header?.length || 0; // Length of header
      const cellLength = rows[column.key]?.toString().length || 0; // Length of cell value
      columnMaxLengths[colIndex] = Math.max(columnMaxLengths[colIndex] || 0, headerLength, cellLength);
    });

    worksheet.columns.forEach((column, colIndex) => {
      column.width = columnMaxLengths[colIndex] + 3; // Add padding for readability
    });


    worksheet.columns.forEach((column) => {
      if (column.key === 'clientGroup' || column.key === 'taskName' || column.key === 'members') {
        column.width = 50;
        column.alignment = { wrapText: true, horizontal: 'center', vertical: 'middle' }; // Enable text wrap
      } else {
        column.alignment = { horizontal: 'center', vertical: 'middle' }; // Apply center alignment for other columns
      }
    });
    // Freeze the header row
    worksheet.views = [{ state: 'frozen', ySplit: 1 }];

    // Generate the Excel file as a buffer
    const buffer = await workbook.xlsx.writeBuffer();
    return buffer;
  }

  async getClientAnalytics(userId: number) {
    let user = await User.findOne({ where: { id: userId }, relations: ['organization'] });

    let clients = await Client.count({
      where: {
        organization: { id: user.organization.id },
        status: Not([UserStatus.DELETED]),
      },
    });

    let leads = await Lead.count({
      where: {
        organization: { id: user.organization.id },
      },
    });

    let converted = await Lead.count({
      where: {
        organization: { id: user.organization.id },
        status: LeadStatusEnum.CONVERTED,
      },
    });

    let notConverted = leads - converted;

    let convertedPercent = Math.round((converted / leads) * 100);
    let notConvertedPercent = Math.round((notConverted / leads) * 100);
    return {
      totalClients: clients,
      convertedLeads: converted,
      notConvertedLeads: notConverted,
      convertedLeadsPercent: convertedPercent || 0,
      notConvertedLeadsPercent: notConvertedPercent || 0,
    };
  }

  async getClientBillingAnalytics(query, userId: number) {
    let user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
    const sql = await getManager().query(`SELECT 
    c.id,
    c.display_name,
    COUNT(DISTINCT CASE WHEN t.billable=false THEN t.id END) AS nonbillabletasks,
    COUNT(DISTINCT CASE WHEN t.billable=true THEN t.id END) AS billabletasks,
    COUNT(DISTINCT CASE WHEN t.payment_status = 'UNBILLED' AND t.billable=true THEN t.id END) AS unbilledtasks,
    COUNT(DISTINCT t.id) AS totaltasks,
    COUNT(DISTINCT CASE WHEN t.payment_status = 'BILLED' AND t.billable=true THEN t.id END) AS billedtasks,
    COUNT(DISTINCT CASE WHEN t.payment_status = 'UNBILLED' AND t.billable=true THEN t.id END) AS unbilledtasks,
    IFNULL((SELECT SUM(CASE WHEN rc.type='CREDIT' THEN rc.amount ELSE 0 END) - SUM(CASE WHEN rc.type='DEBIT' THEN rc.amount ELSE 0 END)
    FROM receipt_credit rc WHERE rc.client_id =${query.clientId} AND rc.status='${ReceiptCreditStatus.CREATED}'), 0) AS credits,

    IFNULL((SELECT SUM(invoice.total_charges) FROM invoice  WHERE invoice.client_id = ${query.clientId} AND invoice.status!='${InvoiceStatus.CANCELLED}'), 0) AS pureagent,

    IFNULL((SELECT SUM(rp.pure_agent_amount) FROM receipt_particular rp INNER JOIN receipt r on rp.receipt_id=r.id where r.client_id=${query.clientId} and r.status ='${ReceiptParticularStatus.CREATED}' and rp.status='${ReceiptParticularStatus.CREATED}' ),0) AS pureagentreceived,

    (IFNULL((SELECT SUM(invoice.total_charges) FROM invoice  WHERE invoice.client_id = ${query.clientId} AND invoice.status!='${InvoiceStatus.CANCELLED}'), 0))-
    IFNULL((IFNULL((SELECT SUM(rp.pure_agent_amount) FROM receipt_particular rp INNER JOIN receipt r on rp.receipt_id=r.id where r.client_id=${query.clientId} and r.status='${ReceiptParticularStatus.CREATED}'  and rp.status='${ReceiptParticularStatus.CREATED}' ),0)),0) AS pureagentdue,
    IFNULL(
  (
    SELECT IFNULL(
    SUM(invoice.sub_total*(CAST(invoice.tds_rate AS CHAR))/100),
    0)
    FROM invoice
    WHERE invoice.client_id = ${query.clientId} 
      AND invoice.status != '${InvoiceStatus.CANCELLED}'
  ), 
  0
) AS tdsamount,

    IFNULL((SELECT ifnull(sum(invoice.grand_total)-(sum(invoice.total_charges)+tdsamount),0) FROM invoice WHERE invoice.client_id = ${query.clientId} AND invoice.status!='${InvoiceStatus.CANCELLED}' ), 0) AS serviceamount,

    IFNULL((SELECT SUM(rp.service_amount) FROM receipt_particular rp INNER JOIN receipt r on rp.receipt_id=r.id where r.client_id=${query.clientId} and r.status ='${ReceiptParticularStatus.CREATED}'  and rp.status='${ReceiptParticularStatus.CREATED}' ),0) AS serviceamountreceived,

    (
    IFNULL(
        (
            SELECT IFNULL(SUM(invoice.grand_total) - SUM(invoice.total_charges), 0)
            FROM invoice
            WHERE invoice.client_id = ${query.clientId}
              AND invoice.status != '${InvoiceStatus.CANCELLED}'
        ),
        0
    )
)
- 
(
    IFNULL(
        (
            SELECT SUM(rp.service_amount)
            FROM receipt_particular rp
            INNER JOIN receipt r ON rp.receipt_id = r.id
            WHERE r.client_id = ${query.clientId}
              AND r.status = '${ReceiptParticularStatus.CREATED}'
              AND rp.status = '${ReceiptParticularStatus.CREATED}'
        ),
        0
    )
)
- 
(
    IFNULL(
        (
            SELECT SUM(invoice.sub_total * (CAST(invoice.tds_rate AS CHAR)) / 100)
            FROM invoice
            WHERE invoice.client_id = ${query.clientId}
              AND invoice.status != '${InvoiceStatus.CANCELLED}'
        ),
        0
    )
) AS serviceamountdue

        

    
FROM 
    client c 
    LEFT JOIN task t ON c.id = t.client_id 
    AND (t.recurring_status = 'created' OR t.recurring_status IS NULL) 
    AND t.status != 'terminated' 
    AND t.status != 'deleted' 
    AND t.parent_task_id IS NULL
WHERE 
    c.id = '${query.clientId}' 
     AND (t.billable is true OR t.id is null OR t.billable is false)
    AND c.status != 'deleted'
    AND c.organization_id = ${user.organization.id};`);

    const [{ id,
      display_name,
      nonbillabletasks,
      billabletasks,
      unbilledtasks,
      totaltasks,
      billedtasks,
      dueamount,
      total_count,
      credits,
      pureagent,
      pureagentreceived,
      pureagentdue,
      serviceamount,
      serviceamountreceived,
      serviceamountdue,
      tdsamount
    }] = sql

    return {
      id,
      display_name,
      nonbillabletasks,
      billabletasks,
      unbilledtasks,
      totaltasks,
      billedtasks,
      dueamount,
      total_count,
      credits,
      pureagent,
      pureagentreceived,
      pureagentdue,
      serviceamount,
      serviceamountreceived,
      serviceamountdue,
      tdsamount
    };
  }

  async getClientGroupBillingAnalytics(query, userId: number) {
    let user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
    const sql = await getManager().query(`SELECT 
    c.id,
    c.display_name,
    COUNT(DISTINCT CASE WHEN t.billable=false THEN t.id END) AS nonbillabletasks,
    COUNT(DISTINCT CASE WHEN t.billable=true THEN t.id END) AS billabletasks,
    COUNT(DISTINCT CASE WHEN t.payment_status = 'UNBILLED' AND t.billable=true THEN t.id END) AS unbilledtasks,
    COUNT(DISTINCT t.id) AS totaltasks,
    COUNT(DISTINCT CASE WHEN t.payment_status = 'BILLED' AND t.billable=true THEN t.id END) AS billedtasks,
    COUNT(DISTINCT CASE WHEN t.payment_status = 'UNBILLED' AND t.billable=true THEN t.id END) AS unbilledtasks,
    IFNULL((SELECT SUM(CASE WHEN rc.type='CREDIT' THEN rc.amount ELSE 0 END) - SUM(CASE WHEN rc.type='DEBIT' THEN rc.amount ELSE 0 END)
    FROM receipt_credit rc WHERE rc.client_group_id =${query.clientGroupId} AND rc.status='${ReceiptCreditStatus.CREATED}'), 0) AS credits,

    IFNULL((SELECT SUM(invoice.total_charges) FROM invoice  WHERE invoice.client_group_id = ${query.clientGroupId} AND invoice.status!='${InvoiceStatus.CANCELLED}'), 0) AS pureagent,

    IFNULL((SELECT SUM(rp.pure_agent_amount) FROM receipt_particular rp INNER JOIN receipt r on rp.receipt_id=r.id where r.client_group_id=${query.clientGroupId} and r.status ='${ReceiptParticularStatus.CREATED}' and rp.status='${ReceiptParticularStatus.CREATED}' ),0) AS pureagentreceived,

    (IFNULL((SELECT SUM(invoice.total_charges) FROM invoice  WHERE invoice.client_group_id = ${query.clientGroupId} AND invoice.status!='${InvoiceStatus.CANCELLED}'), 0))-
    IFNULL((IFNULL((SELECT SUM(rp.pure_agent_amount) FROM receipt_particular rp INNER JOIN receipt r on rp.receipt_id=r.id where r.client_group_id=${query.clientGroupId} and r.status='${ReceiptParticularStatus.CREATED}'  and rp.status='${ReceiptParticularStatus.CREATED}' ),0)),0) AS pureagentdue,
     IFNULL(
  (
    SELECT IFNULL(
    SUM(invoice.sub_total*(CAST(invoice.tds_rate AS CHAR))/100),
    0)
    FROM invoice
    WHERE invoice.client_group_id = ${query.clientGroupId} 
      AND invoice.status != '${InvoiceStatus.CANCELLED}'
  ), 
  0
) AS tdsamount,

    IFNULL((SELECT ifnull(sum(invoice.grand_total)-(sum(invoice.total_charges)+tdsamount),0) FROM invoice WHERE invoice.client_group_id = ${query.clientGroupId} AND invoice.status!='${InvoiceStatus.CANCELLED}' ), 0) AS serviceamount,

    IFNULL((SELECT SUM(rp.service_amount) FROM receipt_particular rp INNER JOIN receipt r on rp.receipt_id=r.id where r.client_group_id=${query.clientGroupId} and r.status ='${ReceiptParticularStatus.CREATED}'  and rp.status='${ReceiptParticularStatus.CREATED}' ),0) AS serviceamountreceived,

    
    IFNULL((SELECT ifnull(sum(invoice.grand_total)-(sum(invoice.total_charges)+tdsamount),0) FROM invoice WHERE invoice.client_group_id = ${query.clientGroupId} AND invoice.status!='${InvoiceStatus.CANCELLED}' ), 0) AS serviceamount,

    IFNULL((SELECT SUM(rp.service_amount) FROM receipt_particular rp INNER JOIN receipt r on rp.receipt_id=r.id where r.client_group_id=${query.clientGroupId} and r.status ='${ReceiptParticularStatus.CREATED}'  and rp.status='${ReceiptParticularStatus.CREATED}' ),0) AS serviceamountreceived,

    (
    IFNULL(
        (
            SELECT IFNULL(SUM(invoice.grand_total) - SUM(invoice.total_charges), 0)
            FROM invoice
            WHERE invoice.client_group_id = ${query.clientGroupId}
              AND invoice.status != '${InvoiceStatus.CANCELLED}'
        ),
        0
    )
)
- 
(
    IFNULL(
        (
            SELECT SUM(rp.service_amount)
            FROM receipt_particular rp
            INNER JOIN receipt r ON rp.receipt_id = r.id
            WHERE r.client_group_id = ${query.clientGroupId}
              AND r.status = '${ReceiptParticularStatus.CREATED}'
              AND rp.status = '${ReceiptParticularStatus.CREATED}'
        ),
        0
    )
)
- 
(
    IFNULL(
        (
            SELECT SUM(invoice.sub_total * (CAST(invoice.tds_rate AS CHAR)) / 100)
            FROM invoice
            WHERE invoice.client_group_id = ${query.clientGroupId}
              AND invoice.status != '${InvoiceStatus.CANCELLED}'
        ),
        0
    )
) AS serviceamountdue
 FROM
    client_group c 
    LEFT JOIN task t ON c.id = t.client_group_id 
    AND (t.recurring_status = 'created' OR t.recurring_status IS NULL) 
    AND t.status != 'terminated' 
    AND t.status != 'deleted' 
    AND t.parent_task_id IS NULL
WHERE 
    c.id = '${query.clientGroupId}' 
     AND (t.billable is true OR t.id is null OR t.billable is false)
    AND c.status != 'deleted'
    AND c.organization_id = ${user.organization.id};`);

    const [{ id,
      display_name,
      nonbillabletasks,
      billabletasks,
      unbilledtasks,
      totaltasks,
      billedtasks,
      dueamount,
      total_count,
      credits,
      pureagent,
      pureagentreceived,
      pureagentdue,
      tdsamount,
      serviceamount,
      serviceamountreceived,
      serviceamountdue
    }] = sql

    return {
      id,
      display_name,
      nonbillabletasks,
      billabletasks,
      unbilledtasks,
      totaltasks,
      billedtasks,
      dueamount,
      total_count,
      credits,
      pureagent,
      pureagentreceived,
      pureagentdue,
      tdsamount,
      serviceamount,
      serviceamountreceived,
      serviceamountdue,
    };
  }

  async getDueDscRegisters(userId: number, query: any) {
    let user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
    let dscRegistersquery = await createQueryBuilder(DscRegister, 'dsc_register')
      .leftJoin('dsc_register.organization', 'organization')
      .leftJoinAndSelect('dsc_register.clients', 'clients')
      .where('organization.id = :id', { id: user.organization.id })
      .andWhere('dsc_register.expiryDate between :start and :end', {
        start: moment().isoWeekday(1).format('YYYY-MM-DD'),
        end: moment().isoWeekday(7).format('YYYY-MM-DD'),
      });

    if (query.clientId && query.clientId !== '') {
      dscRegistersquery.andWhere('clients.id = :clientId', { clientId: query.clientId })
    }

    const dscRegisters = dscRegistersquery.getMany();
    return dscRegisters;
  }

  async getClientsByCategory(userId: number, query: any) {
    let user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
    const sort = (typeof query?.sort === "string") ? JSON.parse(query.sort) : query?.sort;
    let sortQuery = '';
    if (sort?.column) {
      sortQuery = 'ORDER BY ' + `${sort.column} ${sort.direction}`;
      ;
    }
    let clients = await getManager().query(`
      select category,
      sum(case when status = 'ACTIVE' then 1 else 0 end) as activeClients,
      sum(case when status = 'INACTIVE' then 1 else 0 end) as inactiveClients
      from client
      where organization_id = ${user.organization.id}
      group by category ${sortQuery}
      LIMIT ${query.pageCount ? query.pageCount : 10} OFFSET ${query.page ? query.page : 0};
    `);
    return clients;
  }

  async getClientsByCategoryExport(userId: number, query: any) {
    const newQuery = { ...query, offset: 0, limit: 100000000 };
    let clients = await this.getClientsByCategory(userId, newQuery);

    // Initialize a new workbook and worksheet
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Clients by Category');

    // Define the headers for the worksheet
    const headers = [
      { header: 'S.No', key: 'serialNo' },
      { header: 'Category', key: 'category' },
      { header: 'Active Clients', key: 'activeClients' },
      { header: 'Inactive Clients', key: 'inactiveClients' },
    ];

    // Add headers to the worksheet
    worksheet.columns = headers;
    // Map clients data to rows
    let serialCounter = 1; // Initialize a counter
    const rows = clients?.map((client) => (

      {
        serialNo: serialCounter++,// Assign and then increment the counter
        category: CLIENT_CATEGORIES.find(item => item.value === client?.category)?.label || client?.category,
        activeClients: client?.activeClients ? 1 * client?.activeClients : 0, // Ensure valid number
        inactiveClients: client?.inactiveClients ? 1 * client?.inactiveClients : 0, // Ensure valid number
      }));

    if (rows && rows.length) {
      // Add rows to the worksheet
      rows.forEach((row) => worksheet.addRow(row));

      // Auto-adjust column widths for headers and values
      worksheet.columns.forEach((column) => {
        let maxLength = column.header.length; // Start with header length
        column.eachCell({ includeEmpty: true }, (cell) => {
          const cellValue = cell.value ? cell.value.toString() : '';
          maxLength = Math.max(maxLength, cellValue.length); // Get the max length for the column
        });
        column.width = maxLength + 2; // Add some padding for readability
      });

      // Apply uniform header styling
      const headerRow = worksheet.getRow(1);
      headerRow.eachCell((cell) => {
        cell.fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: '64B5F6' },
        };
        cell.border = {
          top: { style: 'thin' },
          left: { style: 'thin' },
          bottom: { style: 'thin' },
          right: { style: 'thin' },
        };
        cell.alignment = { horizontal: 'center', vertical: 'middle' };
        cell.font = { bold: true };
      });

      worksheet.eachRow((row, rowNumber) => {
        if (rowNumber !== 1) {
          row.eachCell((cell) => {
            cell.alignment = { vertical: 'middle', horizontal: 'center' };
          });
        }
      });

      // Freeze the header row
      worksheet.views = [{ state: 'frozen', ySplit: 1 }];

      // Generate the Excel file as a buffer
      const buffer = await workbook.xlsx.writeBuffer();
      return buffer;
    } else {
      throw new BadRequestException('No Data for Export');
    }
  }

  async getTotalLogHours(userId: number, query) {
    let user = await User.findOne({ where: { id: userId }, relations: ['organization'] });

    let sql = `select sum(duration) as totalLogHours from log_hour
    left join user u on u.id = log_hour.user_id
    where u.organization_id = ${user.organization.id}
    and u.type = 'ORGANIZATION' 
  `;

    if (query.dashboardType === 'user') {
      sql += ` and u.id = ${userId}`;
    }
    if (query.fromDate && query.toDate) {
      const { startTime, endTime } = dateFormation(query.fromDate, query.toDate);
      sql += ` and Date(log_hour.completed_date) >= '${startTime}' and Date(log_hour.completed_date) <= '${endTime}'`;
    }

    let data = await getManager().query(sql);
    const milliseconds = data[0].totalLogHours;
    const hours = Math.floor(milliseconds / 1000 / 60 / 60);
    const minutes = Math.floor((milliseconds / 1000 / 60) % 60);
    const formattedTime = `${hours.toString().padStart(2, '0')}:${minutes
      .toString()
      .padStart(2, '0')}`;
    return formattedTime;
  }

  async getWeeklyLogHours(userId: number, query: IQueryWeeklyLogHoursDto) {
    let user = await User.findOne({ where: { id: userId }, relations: ['organization'] });

    let sql = `select date(lh.completed_date) as date, sum(lh.duration) as totalLogHours 
    from log_hour lh left join user u on u.id = lh.user_id 
    left join organization o on u.organization_id = o.id 
    where u.organization_id = ${user.organization.id}`;

    if (query.dashboardType === 'user') {
      sql += ` and u.id = ${userId}`;
    }

    const { startTime, endTime } = dateFormation(query.startDate, query.endDate);

    sql += ` and completed_date >= '${startTime}' and completed_date <= '${endTime}'
    group by date(completed_date);`;

    let data = await getManager().query(sql);
    const getDate = (add: number) => {
      return moment(query.startDate).add(add, 'day').format('DD-MM-YYYY');
    };

    const findData = (date: string) => {
      let result = data?.find((d: any) => moment(d.date).format('DD-MM-YYYY') === date);
      let logHours = result ? Math.round(result?.totalLogHours / 1000 / 60) : 0;
      return logHours;
    };

    let startDate = moment(query.startDate).format('DD-MM-YYYY');

    let result = {
      [startDate]: findData(startDate),
      [getDate(1)]: findData(getDate(1)),
      [getDate(2)]: findData(getDate(2)),
      [getDate(3)]: findData(getDate(3)),
      [getDate(4)]: findData(getDate(4)),
      [getDate(5)]: findData(getDate(5)),
      [getDate(6)]: findData(getDate(6)),
    };
    return result;
  }

  async getEmployeeTasksByStatus(userId: number, query: any) {
    let user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
    let organization = await Organization.findOne({ where: { id: user.organization.id } });
    const organizationPreferences = await OrganizationPreferences.findOne({
      where: { organization },
      order: { id: 'DESC' },
    });
    const sort = (typeof query?.sort === "string") ? JSON.parse(query.sort) : query?.sort;
    let sortQuery = '';
    if (sort?.column) {
      sortQuery = 'ORDER BY ' + `${sort.column} ${sort.direction}`;
      ;
    }
    const date = moment().subtract(organizationPreferences?.taskPreferences?.['taskDate'] || 15, 'days').format('YYYY-MM-DD HH:mm:ss');

    let countSql = `select count(u.id) as count from task
        left join task_members_user tm on tm.task_id = task.id
        left join user u on u.id = tm.user_id
        where task.organization_id = ${user.organization.id} 
        and u.type = 'ORGANIZATION' 
        AND (task.status IN ('todo', 'in_progress', 'on_hold', 'under_review')
        OR (task.status = 'completed' AND task.status_updated_at >= STR_TO_DATE('${date}', '%Y-%m-%d %H:%i:%s'))) AND 
        task.parent_task_id is null and
        (task.recurring_status is null or task.recurring_status = 'created')
        `;

    if (query.dashboardType === 'user') {
      countSql += ` and u.id = ${userId}`;
    }

    if (query.fromDate && query.toDate) {
      countSql += ` and Date(task.task_start_date) >= '${moment(query.fromDate).format(
        'YYYY-MM-DD',
      )}' and Date(task.task_start_date) <= '${moment(query.toDate).format('YYYY-MM-DD')}'`;
    }

    countSql += ` group by u.id having u.id is not null`;

    let count = await getManager().query(countSql);

    let sql = `select u.id as id,u.full_name as fullName,
    sum(case when task.status IN ('todo','in_progress','on_hold','under_review') or (task.status='completed')  then 1 else 0 end) as assigined,
    sum(case when task.status IN ('todo','in_progress','on_hold','under_review') AND task.due_date<'${moment().format('YYYY-MM-DD').toString()}'  then 1 else 0 end) as overdue,
    sum(case when task.status = 'todo' AND task.due_date>='${moment().format('YYYY-MM-DD').toString()}' then 1 else 0 end) as todo,
    sum(case when task.status = 'todo' AND task.due_date<'${moment().format('YYYY-MM-DD').toString()}' then 1 else 0 end) as todoOverdue,
    sum(case when task.status = 'in_progress' AND task.due_date>='${moment().format('YYYY-MM-DD').toString()}' then 1 else 0 end) as inProgress,
    sum(case when task.status = 'in_progress' AND task.due_date<'${moment().format('YYYY-MM-DD').toString()}' then 1 else 0 end) as inProgressOverdue,
    sum(case when task.status = 'completed' then 1 else 0 end) as completed,
    sum(case when task.status = 'on_hold' AND task.due_date>='${moment().format('YYYY-MM-DD').toString()}' then 1 else 0 end) as onHold,
    sum(case when task.status = 'on_hold' AND task.due_date<'${moment().format('YYYY-MM-DD').toString()}' then 1 else 0 end) as onHoldOverdue,
    sum(case when task.status = 'under_review' AND task.due_date>='${moment().format('YYYY-MM-DD').toString()}' then 1 else 0 end) as underReview,
    sum(case when task.status = 'under_review' AND task.due_date<'${moment().format('YYYY-MM-DD').toString()}' then 1 else 0 end) as underReviewOverdue
    from task left join task_members_user tm on tm.task_id = task.id
    left join user u on u.id = tm.user_id
    where task.organization_id = ${user.organization.id} 
    AND u.type = 'ORGANIZATION' 
    AND (task.status IN ('todo', 'in_progress', 'on_hold', 'under_review')
    OR (task.status = 'completed' AND task.status_updated_at >= STR_TO_DATE('${date}', '%Y-%m-%d %H:%i:%s'))) AND 
    task.parent_task_id is null and
    (task.recurring_status is null or task.recurring_status = 'created')`;

    if (query.dashboardType === 'user') {
      sql += ` and u.id = ${userId}`;
    }

    if (query.fromDate && query.toDate) {
      const { startTime, endTime } = dateFormation(query.fromDate, query.toDate);
      sql += ` and Date(task.task_start_date) >= '${startTime}' and Date(task.task_start_date) <= '${endTime}'`;
    }

    sql += ` group by u.id having u.id is not null ${sortQuery}
    limit ${query?.offset || 0}, ${query?.limit || 10}`;

    let result = await getManager().query(sql);

    return {
      totalCount: count.length,
      result,
    };
  }

  async exportEmployeeTasksByStatusReport(userId: number, query: any) {
    const newQuery = { ...query, offset: 0, limit: 100000000 };
    let tasks = await this.getEmployeeTasksByStatus(userId, newQuery);

    if (!tasks?.result || !tasks.result.length) {
      throw new BadRequestException('No Data for Export');
    }

    // Initialize a new workbook and worksheet
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Task Status by Employee');

    // Define the headers for the worksheet
    const headers = [
      { header: 'S.No', key: 'serialNo' },
      { header: 'Employee Name', key: 'employeeName' },
      { header: 'Assigned Task', key: 'assignedTask' },
      { header: 'Todo', key: 'todo' },
      { header: 'Todo Overdue', key: 'todoOverdue' },
      { header: 'In Progress', key: 'inProgress' },
      { header: 'In Progress Overdue', key: 'inProgressOverdue' },
      { header: 'On Hold', key: 'onHold' },
      { header: 'On Hold Overdue', key: 'onHoldOverdue' },
      { header: 'Under Review', key: 'underReview' },
      { header: 'Under Review Overdue', key: 'underReviewOverdue' },
      { header: 'Completed', key: 'completed' },
      { header: 'Completed beyond Due Date', key: 'completedBeyondDueDate' },
    ];

    // Add headers to the worksheet
    worksheet.columns = headers;
    let serialCounter = 1; // Initialize a counter
    // Map tasks data to rows
    const rows = tasks?.result.map((task) => ({
      serialNo: serialCounter++,// Assign and then increment the counter
      employeeName: task?.fullName,
      assignedTask: 1 * task?.assigined,
      todo: 1 * task?.todo,
      inProgress: 1 * task?.inProgress,
      onHold: 1 * task?.onHold,
      underReview: 1 * task?.underReview,
      completed: 1 * task?.completed,
      todoOverdue: 1 * task?.todoOverdue,
      inProgressOverdue: 1 * task?.inProgressOverdue,
      onHoldOverdue: 1 * task?.onHoldOverdue,
      underReviewOverdue: 1 * task?.underReviewOverdue,
      completedBeyondDueDate: 1 * task?.overdue,
    }));

    // Add rows to the worksheet
    rows.forEach((row) => worksheet.addRow(row));
    // Auto-adjust column widths for headers and values
    worksheet.columns.forEach((column) => {
      let maxLength = column.header.length; // Start with header length
      column.eachCell({ includeEmpty: true }, (cell) => {
        const cellValue = cell.value ? cell.value.toString() : '';
        maxLength = Math.max(maxLength, cellValue.length); // Get the max length for the column
      });
      column.width = maxLength + 2; // Add some padding for readability
    });

    // Apply uniform header styling
    const headerRow = worksheet.getRow(1);
    headerRow.eachCell((cell) => {
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '64B5F6' },
      };
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' },
      };
      cell.alignment = { horizontal: 'center', vertical: 'middle' };
      cell.font = { bold: true };
    });


    // Adjust column widths dynamically and wrap text for employeeName
    worksheet.columns.forEach((column) => {
      if (column.key === 'employeeName') {
        column.width = 50; // Fixed width for employeeName
        column.alignment = { wrapText: true, horizontal: 'center', vertical: 'middle' };
      } else {
        let maxLength = column.header.length; // Start with header length
        column.eachCell({ includeEmpty: true }, (cell) => {
          const cellValue = cell.value || '';
          const cellLength = cellValue.toString().length;
          if (cellLength > maxLength) {
            maxLength = cellLength;
          }
        });
        column.width = maxLength + 2; // Add padding for readability
        column.alignment = { horizontal: 'center', vertical: 'middle' };
      }
    });


    // Apply alignment to all rows
    worksheet.eachRow((row) => {
      row.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
    });

    // Freeze the header row
    worksheet.views = [{ state: 'frozen', ySplit: 1 }];

    // Generate the Excel file as a buffer
    const buffer = await workbook.xlsx.writeBuffer();

    return buffer;
  }

  async getEmployeeAttendance(userId: number, query: any) {
    const user = await User.findOne({ where: { id: userId }, relations: ['organization'] });

    const organizationPreferences = await OrganizationPreferences.findOne({
      where: { organization: user.organization },
      order: { id: 'DESC' },
    });

    let holiday = false;
    let holidayType;

    const day = moment(query?.date, 'YYYY-MM-DD').format('dddd');
    const holidayPreferences = organizationPreferences.holidayPreferences;

    const weekends = holidayPreferences['updateweekend']
      ? holidayPreferences['updateweekend'].map((item) => item.label)
      : [];

    holiday = weekends.includes(day) ? true : false;

    if (holiday) {
      holidayType = 'Weekend';
    }

    const holidayDates = holidayPreferences['addholiday'].map((item) =>
      moment(item.date).format('YYYY-MM-DD'),
    );

    if (!holiday) {
      holiday = holidayDates.includes(moment(query?.date).format('YYYY-MM-DD')) ? true : false;
      if (holiday) {
        holidayType = holidayPreferences['addholiday']?.filter(
          (item) =>
            moment(item.date).format('YYYY-MM-DD') === moment(query?.date).format('YYYY-MM-DD'),
        )[0]?.holiday;
      }
    }

    const userLimit = createQueryBuilder(User, 'user')
      .leftJoinAndSelect('user.organization', 'organization')
      .leftJoinAndSelect('user.role', 'role')
      .where('user.organization.id = :orgId', { orgId: user.organization.id })
      .andWhere('user.type = :type', { type: UserType.ORGANIZATION });

    if (query?.search) {
      userLimit.andWhere('user.fullName like :fullName', { fullName: `%${query?.search}%` });
    }

    if (query?.role) {
      userLimit.andWhere('role.name = :name', { name: query?.role });
    }

    const userList = await userLimit.getMany();

    const ActiveUserList = userList.filter((item) => item.status === UserStatus.ACTIVE);
    const newUserList = [...ActiveUserList];

    const getAttendance = await Attendance.find({
      where: {
        organization: user.organization,
        attendanceDate: moment(query?.date).format('YYYY-MM-DD'),
      },
    });

    const userAttendanceList = getAttendance.map((item) => item.userId);

    for (let useritem of newUserList) {
      if (userAttendanceList.includes(useritem.id)) {
        useritem['attendance'] = getAttendance.filter((item) => item.userId === useritem.id)[0];
      }

      if (holiday) {
        if (useritem['attendance']) {
          useritem['attendance'].type = 'Holiday';
          useritem['attendance'].description = holidayType;
        } else {
          useritem['attendance'] = {
            type: 'Holiday',
            description: holidayType,
          };
        }
      }
    }
    return newUserList;
  }

  async exportEmployeeAttendance(userId: number, query: any, res: Response): Promise<void> {
    if (!query?.rows || !query.rows.length) {
      throw new BadRequestException('No Data for Export');
    }
    console.log("data:", query);

    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Daily Attendance');

    // Define headers with explicit widths
    worksheet.columns = [
      { header: 'S.No', key: 'serialNo', width: 8 },
      { header: 'Date', key: 'date', width: 15 },
      { header: 'User', key: 'user', width: 30 },
      { header: 'Role', key: 'role', width: 30 },
      { header: 'Description', key: 'description', width: 20 },
      { header: 'Check In', key: 'checkIn', width: 18 },
      { header: 'Check In Address', key: 'checkInAddress', width: 50 },
      { header: 'Check Out', key: 'checkOut', width: 18 },
      { header: 'Check Out Address', key: 'checkOutAddress', width: 50 },
      { header: 'Duration', key: 'duration', width: 12 },
      { header: 'Location', key: 'location', width: 25 },
      { header: 'Status', key: 'status', width: 15 },
    ];

    // Add rows with serial number
    let serialCounter = 1;
    const rows = query.rows.map((row: any) => ({
      serialNo: serialCounter++,
      date: row.date,
      user: row.user,
      role: row.role,
      description: row.description,
      checkIn: row.checkIn,
      checkInAddress: row.checkInAddress || "-",
      checkOut: row.checkOut,
      checkOutAddress: row.checkOutAddress || "-",
      duration: row.duration,
      location: row.location,
      status: row.status,
    }));

    rows.forEach((r) => worksheet.addRow(r));

    // Style header row
    const headerRow = worksheet.getRow(1);
    headerRow.eachCell((cell) => {
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '64B5F6' },
      };
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' },
      };
      cell.alignment = { horizontal: 'center', vertical: 'middle' };
      cell.font = { bold: true };
    });

    // Center align all rows and set a uniform row height
    worksheet.eachRow((row, rowNumber) => {
      row.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
      row.height = rowNumber === 1 ? 25 : 20; // Header row taller
    });

    // Freeze header row
    worksheet.views = [{ state: 'frozen', ySplit: 1 }];

    // Generate buffer
    const buffer = await workbook.xlsx.writeBuffer();

    // Get date from frontend payload
    const formattedDate = query.date ? moment(query.date).format('DD-MM-YYYY') : 'daily_attendance';

    // Send file to client
    res.setHeader(
      'Content-Type',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    );
    res.setHeader(
      'Content-Disposition',
      `attachment; filename="${formattedDate}_daily_attendance.xlsx"`,
    );
    res.end(buffer);
  }



  //Service Dashboard
  async getServiceAnalytics(userId: number, query: any) {
    const { serviceId, financialYear } = query;
    let user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
    const date = moment().format('YYYY-MM-DD');
    let sql = `
    select status, count(task.id) as count,user_id,SUM(task.fee_amount) as task_fee_amount,
    sum(case when task.recurring = true then 1 else 0 end) as recurring,
    sum(case when task.recurring = false then 1 else 0 end) as non_recurring,
    sum(case when task.priority = 'high' then 1 else 0 end) as high,
    sum(case when task.priority = 'low' then 1 else 0 end) as low,
    sum(case when task.priority = 'medium' then 1 else 0 end) as medium,
    sum(case when task.priority = 'none' then 1 else 0 end) as none,
    SUM(CASE WHEN task.billable = true THEN 1 ELSE 0 END) AS billable_true,
    SUM(CASE WHEN task.billable = false THEN 1 ELSE 0 END) AS billable_false,
    SUM(CASE WHEN task.payment_status = 'UNBILLED' THEN 1 ELSE 0 END) AS unbilled_task,
    SUM(CASE WHEN task.payment_status = 'ESTIMATED' THEN 1 ELSE 0 END) AS estimated_task,
    SUM(CASE WHEN task.payment_status = 'BILLED' THEN 1 ELSE 0 END) AS billed_task,
    SUM(CASE WHEN task.due_date < '${date}' AND task.status NOT IN ('completed','terminated','deleted') THEN 1 ELSE 0 END) AS over_due_tasks
    from task`;

    sql += ` where organization_id = ${user.organization.id} and service_id = ${serviceId} `;
    if (financialYear) {
      sql += `and task.financial_year = '${financialYear}'`;
    }
    sql += ` and task.parent_task_id is null
    and (task.recurring_status is null or task.recurring_status = 'created')
    and task.status <> ''
    group by task.status;`;

    let tasks = await getManager().query(sql);

    const totalClientsInOrgSql = `SELECT COUNT(id) AS total_clients FROM client WHERE organization_id = ${user?.organization?.id}`;
    let totalClientsRes = await getManager().query(totalClientsInOrgSql);

    const serviceAvailedClientsSql = ` SELECT COUNT(DISTINCT(client_id)) AS total_clients_availed FROM task WHERE organization_id = ${user?.organization?.id} AND service_id = ${serviceId} AND  (task.recurring_status is null or task.recurring_status = 'created') AND task.parent_task_id is NULL;`;
    let totalAvaliedClients = await getManager().query(serviceAvailedClientsSql);

    //LOGHOURS USER WISE
    let loghourSql = `
    SELECT 
          u.full_name,
          l.user_id,SUM(l.duration) as duration
    FROM
          service s INNER JOIN task t ON s.id=t.service_id
    INNER JOIN 
          log_hour l ON l.task_id=t.id
    INNER JOIN 
          user u ON l.user_id=u.id
    WHERE 
          s.id=${serviceId}
          AND t.organization_id=${user.organization.id}
          AND l.type='${LogHourType.TASK}'`;

    if (financialYear) {
      loghourSql += ` and t.financial_year = '${financialYear}'`;
    }

    loghourSql += `GROUP BY l.user_id
     ORDER BY duration DESC
     LIMIT 5
     `;

    let loghourRes = await getManager().query(loghourSql);

    let budgetedLoghoursSql = `SELECT 
            SUM(b.budgeted_hours) as total_budgeted_hours
        FROM
             task t 
        INNER JOIN 
            budgeted_hours b ON b.task_id=t.id 
        WHERE 
            t.service_id=${serviceId}
            AND t.organization_id=${user.organization.id}
            AND t.parent_task_id IS NULL
            AND b.status = '${BudgetedHourStatus.ACTIVE}'
            AND (t.recurring_status is null or t.recurring_status = 'created')
            AND t.status <> ''`;

    if (financialYear) {
      budgetedLoghoursSql += ` AND t.financial_year = '${financialYear}'`;
    }

    let budgetedAndLoghoursRes = await getManager().query(budgetedLoghoursSql);

    let logSql = ` SELECT 
          SUM(l.duration) as total_log_hours
        FROM
          task t 
        INNER JOIN 
          log_hour l ON l.task_id = t.id
        WHERE 
          t.service_id=${serviceId}
          AND t.organization_id=${user.organization.id}
          AND 
          l.type = '${LogHourType.TASK}'
          AND t.parent_task_id IS NULL
          AND (t.recurring_status is null or t.recurring_status = 'created')
          AND t.status <> ''`;

    if (financialYear) {
      logSql += ` AND t.financial_year = '${financialYear}'`;
    }

    let logSqlRes = await getManager().query(logSql);



    const budgetedAndLoghours = {
      total_budgeted_hours: budgetedAndLoghoursRes.length > 0 ? budgetedAndLoghoursRes[0].total_budgeted_hours : null,
      total_log_hours: logSqlRes?.length > 0 ? logSqlRes[0].total_log_hours : null
    }

    let remarkStatusSql = `
    SELECT 
        sum(case when a.remark_type = 'pending_at_department' then 1 else 0 end) as pen_at_dept,
        sum(case when a.remark_type = 'pending_at_client' then 1 else 0 end) as pen_at_clin,
        sum(case when a.remark_type = 'others' then 1 else 0 end) as other
    from 
        (
          SELECT 
              type_id,
              MAX(id) AS latest_activity_id
          FROM
              activity
          WHERE 
              type = 'task'
          GROUP BY
              type_id
        ) latest_activities
    INNER JOIN 
        activity a ON latest_activities.latest_activity_id = a.id
    INNER JOIN 
        task t ON a.type_id = t.id
    WHERE  
        t.organization_id = ${user.organization.id} and t.service_id = ${serviceId} AND a.type = 'task' AND t.status = 'on_hold' `;

    if (financialYear) {
      remarkStatusSql += ` and t.financial_year = '${financialYear}'`;
    }
    let remarksStatus = await getManager().query(remarkStatusSql);

    const remarks = remarksStatus?.length > 0 ? remarksStatus[0] : null;

    let expenditureAmountsSql = `
      SELECT 
            SUM(e.amount) AS total_amount,e.task_expense_type
      FROM 
            expenditure e 
      LEFT JOIN 
            task t
      ON 
          t.id = e.task_id 
      WHERE
            t.organization_id = ${user.organization.id} 
            AND t.service_id = ${serviceId}
            AND e.type = 'TASK'`;

    if (financialYear) {
      expenditureAmountsSql += ` AND t.financial_year = '${financialYear}'`;
    }

    expenditureAmountsSql += `GROUP BY
            e.task_expense_type
      `;

    let expenditureAmount = await getManager().query(expenditureAmountsSql);

    let recurringTasks = 0;
    let nonRecurringTasks = 0;
    let billableTasks = 0;
    let nonBillableTasks = 0;
    let unbilledTasks = 0;
    let billedtasks = 0;
    let estimatedTasks = 0;
    let totalClients = 0;
    let totalClientsAvalied = 0;
    let totalClientsUnavalied = 0;
    let total_fee_amount = 0;
    let overDueCount = 0;
    let tasksByStatus: any = {
      todo: 0,
      in_progress: 0,
      under_review: 0,
      on_hold: 0,
      completed: 0,
      terminated: 0,
      deleted: 0,
    };
    let tasksByPriority: any = {
      high: 0,
      low: 0,
      medium: 0,
      none: 0,
    };

    totalAvaliedClients.map((i) => {
      totalClientsAvalied = parseInt(i.total_clients_availed);
    });

    totalClientsRes.map((i) => {
      totalClients = parseInt(i.total_clients);
    });

    tasks.forEach((task: any) => {
      tasksByStatus[task.status] = +task.count || 0;
      tasksByPriority['high'] += +task['high'] || 0;
      tasksByPriority['low'] += +task['low'] || 0;
      tasksByPriority['medium'] += +task['medium'] || 0;
      tasksByPriority['none'] += +task['none'] || 0;
      total_fee_amount += +task.task_fee_amount || 0;
      recurringTasks += +task.recurring || 0;
      nonRecurringTasks += +task.non_recurring || 0;
      billableTasks += +task.billable_true || 0;
      nonBillableTasks += +task.billable_false || 0;
      billedtasks += +task.billed_task || 0;
      estimatedTasks += +task.estimated_task || 0;
      unbilledTasks += +task.unbilled_task || 0;
      overDueCount += +task.over_due_tasks || 0;
    });

    let totalTasks = recurringTasks + nonRecurringTasks;
    let recurringTasksPercentage = Math.round((recurringTasks / totalTasks) * 100);
    let nonRecurringTasksPercentage = Math.round((nonRecurringTasks / totalTasks) * 100);
    return {
      total:
        tasksByStatus.todo +
        tasksByStatus.in_progress +
        tasksByStatus.under_review +
        tasksByStatus.on_hold +
        tasksByStatus.completed +
        tasksByStatus.deleted +
        tasksByStatus.terminated,
      tasksByStatus,
      tasksByPriority,
      recurringTasks,
      nonRecurringTasks,
      recurringTasksPercentage: recurringTasksPercentage || 0,
      nonRecurringTasksPercentage: nonRecurringTasksPercentage || 0,
      billableTasks,
      nonBillableTasks,
      billedtasks,
      unbilledTasks,
      estimatedTasks,
      totalClients,
      totalClientsAvalied,
      totalClientsUnavalied: totalClients - totalClientsAvalied,
      loghourRes,
      remarks,
      total_fee_amount,
      expenditureAmount,
      overDueCount,
      budgetedAndLoghours,
    };
  }


  async getBillingAmount(userId: number, query: FindBillingAmounts) {
    const user = await User.findOne({ where: { id: userId }, relations: ['organization'] })
    let outStandingSql = `
 SELECT 
  IFNULL(SUM(i.grand_total), 0)
  - IFNULL(SUM(i.sub_total * (CAST(i.tds_rate AS DECIMAL(10,2))) / 100), 0)
  - IFNULL((
      SELECT SUM(rp.service_amount + rp.pure_agent_amount)
      FROM receipt_particular rp
      WHERE rp.invoice_id IN (
        SELECT id FROM invoice
        WHERE status NOT IN ('PAID', 'CANCELLED')
          AND organization_id = ?
          AND invoice_date >= ?
          AND invoice_date <= ?
          AND billing_entity_id in (?)
      
      ) AND rp.status=?
  ), 0) AS outstanding
FROM invoice i
INNER JOIN billing_entity be ON i.billing_entity_id = be.id
WHERE i.status NOT IN ('PAID', 'CANCELLED')
  AND i.organization_id = ?
  AND i.invoice_date >= ?
  AND i.invoice_date <= ?

`;
    if (query.billingE && query.billingE.length > 0) {
      outStandingSql += ` AND be.id IN (?)`;
    }

    let outStandingAmounts = await getManager().query(outStandingSql, [
      user.organization.id,
      moment(query.startDate, 'DD-MM-YYYY').format('YYYY-MM-DD'),
      moment(query.endDate, 'DD-MM-YYYY').format('YYYY-MM-DD'),
      query?.billingE,
      ReceiptParticularStatus.CREATED,
      user.organization.id,
      moment(query.startDate, 'DD-MM-YYYY').format('YYYY-MM-DD'),
      moment(query.endDate, 'DD-MM-YYYY').format('YYYY-MM-DD'),
      query?.billingE
    ]);


    let totalReciptsSql = `
    SELECT IFNULL(SUM(amount),0) As receiptstotal
    FROM receipt r INNER JOIN billing_entity be ON r.billing_entity_id=be.id
    WHERE status <> 'CANCELLED'
    AND r.organization_id = ?
    AND receipt_date >= ?
    AND receipt_date <= ?
    `;
    if (query.billingE && query.billingE.length > 0) {
      totalReciptsSql += ` AND be.id IN (?)`;
    }

    let receiptsAmounts = await getManager().query(totalReciptsSql, [
      user.organization.id,
      moment(query.startDate, 'DD-MM-YYYY').format('YYYY-MM-DD'),
      moment(query.endDate, 'DD-MM-YYYY').format('YYYY-MM-DD'),
      query?.billingE
    ]);


    const totalExpenseSql = `
    SELECT IFNULL(SUM(amount),0) AS expensestotal
    FROM expenditure e inner join task t on e.task_id=t.id 
    WHERE t.organization_id=?
    `;

    let expenseAmounts = await getManager().query(totalExpenseSql, [
      user.organization.id
    ]);

    let totalTdsSql = `
 SELECT IFNULL(SUM(sub_total * (CAST(tds_rate AS CHAR)) / 100),0) as tdsTotal
                    FROM invoice i INNER JOIN billing_entity be ON i.billing_entity_id=be.id
                    WHERE
                   i.organization_id = ?
                   AND i.status NOT IN ('CANCELLED')
                   AND invoice_date >= ?
                   AND invoice_date <= ?
                   `;
    if (query.billingE && query.billingE.length > 0) {
      totalTdsSql += ` AND be.id IN (?)`;
    }

    let tdsAmounts = await getManager().query(totalTdsSql, [
      user.organization.id,
      moment(query.startDate, 'DD-MM-YYYY').format('YYYY-MM-DD'),
      moment(query.endDate, 'DD-MM-YYYY').format('YYYY-MM-DD'),
      query?.billingE

    ])

    return {
      outStandingAmounts: outStandingAmounts?.[0].outstanding,
      receiptsAmounts: receiptsAmounts?.[0].receiptstotal,
      expenseAmounts: expenseAmounts?.[0].expensestotal,
      tdsAmounts: tdsAmounts?.[0].tdsTotal

    }

  };

  async getInvoiceStatus(userId: number, query: FindBillingAmounts) {
    let user = await User.findOne({ where: { id: userId }, relations: ['organization'] });

    let sql = `SELECT
     sum(case WHEN i.status='APPROVAL_PENDING' AND i.invoice_due_date>=? then 1 else 0 END) as created,
     sum(case WHEN i.status='APPROVAL_PENDING' AND i.invoice_due_date<? then 1 else 0 END) as over_due,
     sum(case WHEN i.status='PARTIALLY_PAID' then 1 else 0 END) as partially_paid,
     sum(case WHEN i.status='PAID' then 1 else 0 END) as paid,
     sum(case WHEN i.status='CANCELLED' then 1 else 0 END) as cancelled
     FROM invoice i LEFT JOIN billing_entity be ON i.billing_entity_id=be.id
     WHERE 
     i.organization_id = ?
    AND invoice_date >= ?
    AND invoice_date <= ?
     `;
    if (query.billingE && query.billingE.length > 0) {
      sql += ` AND be.id IN (?)`;
    }
    let invoiceStats = await getManager().query(sql, [
      moment().subtract(1, 'day').format('YYYY-MM-DD'),
      moment().subtract(1, 'day').format('YYYY-MM-DD'),
      user.organization.id,
      moment(query.startDate, 'DD-MM-YYYY').format('YYYY-MM-DD'),
      moment(query.endDate, 'DD-MM-YYYY').format('YYYY-MM-DD'),
      query?.billingE
    ]);

    let invoiceByCount = {
      created: invoiceStats?.[0]?.created * 1,
      overDue: invoiceStats?.[0]?.over_due * 1,
      partiallyPaid: invoiceStats?.[0]?.partially_paid * 1,
      paid: invoiceStats?.[0]?.paid * 1,
      cancelled: invoiceStats?.[0]?.cancelled * 1,
    };

    const total = invoiceByCount.created +
      invoiceByCount.partiallyPaid +
      invoiceByCount.paid +
      invoiceByCount.cancelled;
    return invoiceByCount;
  };

  async getReceipsAmounts(userId: number, query: FindBillingAmounts) {
    let user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
    let sql = `SELECT DATE_FORMAT(receipt_date,'%Y-%m') AS month,
    SUM(amount) as receipt_amount
    FROM receipt r INNER JOIN billing_entity be ON r.billing_entity_id=be.id
  WHERE
 r.organization_id = ?
AND r.receipt_date >= ?
AND r.receipt_date <= ?
AND r.status != ?
`;
    if (query.billingE && query.billingE.length > 0) {
      sql += ` AND be.id IN (?)`;
    }
    sql += `GROUP BY month`;
    let receipsAmounts = await getManager().query(sql, [
      user.organization.id,
      moment(query.startDate, 'DD-MM-YYYY').format('YYYY-MM-DD'),
      moment(query.endDate, 'DD-MM-YYYY').format('YYYY-MM-DD'),
      ReceiptStatus.CANCELLED,
      query?.billingE
    ]);


    const monthNames = [
      { "01": "January" },
      { "02": "February" },
      { "03": "March" },
      { "04": "April" },
      { "05": "May" },
      { "06": "June" },
      { "07": "July" },
      { "08": "August" },
      { "09": "September" },
      { "10": "October" },
      { "11": "November" },
      { "12": "December" }
    ];

    const monthMap = monthNames.reduce((acc, obj) => {
      const [key, value] = Object.entries(obj)[0];
      acc[key] = value;
      return acc;
    }, {});


    const generateMonthsInRange = (startMonth, endMonth) => {
      let months = [];
      let currentDate = new Date(startMonth);
      let endDate = new Date(endMonth);

      while (currentDate <= endDate) {
        const year = currentDate.getFullYear();
        const month = ("0" + (currentDate.getMonth() + 1)).slice(-2);  // Always two digits
        months.push(`${year}-${month}`);
        currentDate.setMonth(currentDate.getMonth() + 1); // Move to the next month
      }

      return months;
    };
    const allMonths = generateMonthsInRange(moment(query.startDate, 'DD-MM-YYYY').format('YYYY-MM'),
      moment(query.endDate, 'DD-MM-YYYY').format('YYYY-MM'));

    const result = allMonths.map(month => {
      const dataItem = receipsAmounts?.find(d => d.month === month);
      const monthName = monthMap[month.slice(-2)]
      return {
        month: monthName,
        amount: dataItem ? dataItem.receipt_amount : "0.00"  // Set 0 if no data

      }
    })
    return result;
  };

  async getInvoiceAmounts(userId: number, query: FindBillingAmounts) {
    let user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
    let sql = `SELECT DATE_FORMAT(invoice_date,'%Y-%m') AS month,
    SUM(grand_total) AS invoice_total,
(
 
 SUM(i.sub_total * (CAST(i.tds_rate AS CHAR)) / 100)
) + COALESCE(SUM(rp.total_received), 0) AS due_amount
    FROM invoice i 
    INNER JOIN billing_entity be ON i.billing_entity_id=be.id

    LEFT JOIN (
    SELECT 
        invoice_id, 
        SUM(pure_agent_amount+service_amount) AS total_received
    FROM 
        receipt_particular rp
    WHERE  rp.status=? 
    GROUP BY 
        invoice_id
) rp ON rp.invoice_id = i.id
    WHERE 
    i.organization_id=? AND
    i.invoice_date>=? AND
    i.invoice_date<=? AND 
    i.status !=?`
      ;
    if (query.billingE && query.billingE.length > 0) {
      sql += ` AND be.id IN (?)`;
    }


    sql += ` GROUP BY month
    `;
    let invoiceAmounts = await getManager().query(sql, [
      ReceiptParticularStatus.CREATED,
      user.organization.id,
      moment(query.startDate, 'DD-MM-YYYY').format('YYYY-MM-DD'),
      moment(query.endDate, 'DD-MM-YYYY').format('YYYY-MM-DD'),
      InvoiceStatus.CANCELLED,

      query?.billingE
    ]);

    const monthNames = [
      { "01": "Jan" },
      { "02": "Feb" },
      { "03": "Mar" },
      { "04": "Apr" },
      { "05": "May" },
      { "06": "Jun" },
      { "07": "Jul" },
      { "08": "Aug" },
      { "09": "Sep" },
      { "10": "Oct" },
      { "11": "Nov" },
      { "12": "Dec" }
    ];

    const monthMap = monthNames.reduce((acc, obj) => {
      const [key, value] = Object.entries(obj)[0];
      acc[key] = value;
      return acc;
    }, {});


    const generateMonthsInRange = (startMonth, endMonth) => {
      let months = [];
      let currentDate = new Date(startMonth);
      let endDate = new Date(endMonth);

      while (currentDate <= endDate) {
        const year = currentDate.getFullYear();
        const month = ("0" + (currentDate.getMonth() + 1)).slice(-2);  // Always two digits
        months.push(`${year}-${month}`);
        currentDate.setMonth(currentDate.getMonth() + 1); // Move to the next month
      }

      return months;
    };
    const allMonths = generateMonthsInRange(moment(query.startDate, 'DD-MM-YYYY').format('YYYY-MM'),
      moment(query.endDate, 'DD-MM-YYYY').format('YYYY-MM'));


    const result = allMonths.map(month => {
      const dataItem = invoiceAmounts?.find(d => d.month === month);
      const monthName = monthMap[month.slice(-2)]
      return {
        month: monthName,
        amount: dataItem ? dataItem.invoice_total : "0.00",
        payed: dataItem ? dataItem.due_amount : "0.00"

      }
    })
    return result;
  };

  async getOutStandingClients(userId: number, query: FindOutStandingClients) {
    let user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
    let sql: string;
    if (query.type == "Client") {

      sql = `
      SELECT 
  c.id AS id,
  c.display_name AS display_name,
  'CLIENT' AS type,
  SUM(
    (
      IFNULL(iop.amount, 0) 
      - IFNULL(rp_pure.pure_agent_amount, 0)
    ) +
    IFNULL(
      (i.grand_total - i.total_charges)
      - IFNULL(rp_serv.service_amount, 0)
      - IFNULL(i.sub_total * IFNULL(i.tds_rate, 0) / 100, 0),
    0)
  ) AS due_amount
FROM client c
JOIN invoice i ON c.id = i.client_id
LEFT JOIN billing_entity be ON i.billing_entity_id = be.id
LEFT JOIN invoice_other_particular iop ON i.id = iop.invoice_id
LEFT JOIN (
  SELECT invoice_id, SUM(pure_agent_amount) AS pure_agent_amount
  FROM receipt_particular
  WHERE status = 'CREATED'
  GROUP BY invoice_id
) rp_pure ON i.id = rp_pure.invoice_id
LEFT JOIN (
  SELECT invoice_id, SUM(service_amount) AS service_amount
  FROM receipt_particular
  WHERE status = 'CREATED'
  GROUP BY invoice_id
) rp_serv ON i.id = rp_serv.invoice_id
WHERE 
  i.organization_id = ?
  AND i.status != 'CANCELLED'
  AND i.invoice_date >= ?
  AND i.invoice_date <= ?
  ${query.billingE && query.billingE.length > 0 ? `AND be.id IN (${query.billingE.map(() => '?').join(',')})` : ''}
GROUP BY c.id, c.display_name
HAVING due_amount > 0
ORDER BY due_amount DESC
LIMIT 5;
 `

    } else {

      sql = `
  SELECT 
    cg.id AS id,
    cg.display_name AS display_name,
    'CLIENT_GROUP' AS type,
    SUM(
      IFNULL(iop.amount, 0)
      - IFNULL(rp_pure.pure_agent_amount, 0)
      + IFNULL(
          (i.grand_total - i.total_charges)
          - IFNULL(rp_serv.service_amount, 0)
          - IFNULL(i.sub_total * IFNULL(i.tds_rate, 0) / 100, 0),
        0)
    ) AS due_amount
  FROM client_group cg
  JOIN invoice i ON cg.id = i.client_group_id
  INNER JOIN billing_entity be ON i.billing_entity_id = be.id
  LEFT JOIN invoice_particular ip ON i.id = ip.invoice_id
  LEFT JOIN invoice_other_particular iop ON i.id = iop.invoice_id
  LEFT JOIN (
    SELECT invoice_id, SUM(pure_agent_amount) AS pure_agent_amount
    FROM receipt_particular
    WHERE status = '${ReceiptCreditStatus.CREATED}'
    GROUP BY invoice_id
  ) rp_pure ON i.id = rp_pure.invoice_id
  LEFT JOIN (
    SELECT invoice_id, SUM(service_amount) AS service_amount
    FROM receipt_particular
    WHERE status = '${ReceiptCreditStatus.CREATED}'
    GROUP BY invoice_id
  ) rp_serv ON i.id = rp_serv.invoice_id
  WHERE 
    i.organization_id = ?
    AND i.status != 'CANCELLED'
    AND i.invoice_date >= ?
    AND i.invoice_date <= ?
    ${query.billingE && query.billingE.length > 0 ? `AND be.id IN (${query.billingE.map(() => '?').join(',')})` : ''}
  GROUP BY cg.id, cg.display_name
  HAVING due_amount > 0
  ORDER BY due_amount DESC
  LIMIT 5;
`;

    }
    console.log(sql)
    const clients = await getManager().query(sql, [
      // user.organization.id,
      user.organization.id,
      moment(query.startDate, 'DD-MM-YYYY').format('YYYY-MM-DD'),
      moment(query.endDate, 'DD-MM-YYYY').format('YYYY-MM-DD'),
      query?.billingE
    ]);
    return clients;

  };

  async getGstPayable(userId: number, query: FindBillingAmounts) {
    let user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
    let sql = `SELECT 
  DATE_FORMAT(invoice_date,'%Y-%m') AS month,
  SUM(i.grand_total) as invoice_amount,
  SUM(i.sub_total) as taxable_amount,
  IFNULL(SUM(i.total_charges),0) as pureAgent,



 SUM(CASE 
    WHEN be.has_gst = TRUE AND (
         TRIM(LOWER(SUBSTRING_INDEX(i.place_of_supply, '-', -1))) != TRIM(LOWER(be.location_of_supply))
         OR i.divide_tax = TRUE
    )
    THEN (
      SELECT 
        ROUND(SUM(
          (CAST(p.amount AS DECIMAL(10,2)) * CAST(SUBSTRING(p.gst, 4) AS DECIMAL(5,2)) / 100)
        ), 2)
      FROM invoice_particular p
      WHERE p.invoice_id = i.id
    )
    ELSE 0
  END) AS igst_amount,

 SUM(CASE 
   WHEN be.has_gst = TRUE AND (
     TRIM(LOWER(SUBSTRING_INDEX(i.place_of_supply, '-', -1))) = TRIM(LOWER(be.location_of_supply))
     AND i.divide_tax = FALSE
   )
   THEN (
     SELECT 
       ROUND(SUM(
         (CAST(p.amount AS DECIMAL(10,2)) * CAST(SUBSTRING(p.gst, 4) AS DECIMAL(5,2)) / 100)
       ) / 2, 2)
     FROM invoice_particular p
     WHERE p.invoice_id = i.id
   )
   ELSE 0
END) AS cgst_amount,

SUM(CASE 
   WHEN be.has_gst = TRUE AND (
     TRIM(LOWER(SUBSTRING_INDEX(i.place_of_supply, '-', -1))) = TRIM(LOWER(be.location_of_supply))
     AND i.divide_tax = FALSE
   )
   THEN (
     SELECT 
       ROUND(SUM(
         (CAST(p.amount AS DECIMAL(10,2)) * CAST(SUBSTRING(p.gst, 4) AS DECIMAL(5,2)) / 100)
       ) / 2, 2)
     FROM invoice_particular p
     WHERE p.invoice_id = i.id
   )
   ELSE 0
END) AS sgst_amount

FROM invoice i
JOIN billing_entity be ON be.id = i.billing_entity_id
 WHERE 
    i.organization_id=? AND
    i.invoice_date>=? AND
    i.invoice_date<=? AND 
    i.status !=?
    `;

    if (query.billingE && query.billingE.length > 0) {
      sql += ` AND be.id IN (?)`;
    }


    sql += ` GROUP BY month
;
`;

    let gstPayable = await getManager().query(sql, [
      user.organization.id,
      moment(query.startDate, 'DD-MM-YYYY').format('YYYY-MM-DD'),
      moment(query.endDate, 'DD-MM-YYYY').format('YYYY-MM-DD'),
      InvoiceStatus.CANCELLED,
      query?.billingE
    ]);
    const monthNames = [
      { "01": "January" },
      { "02": "February" },
      { "03": "March" },
      { "04": "April" },
      { "05": "May" },
      { "06": "June" },
      { "07": "July" },
      { "08": "August" },
      { "09": "September" },
      { "10": "October" },
      { "11": "November" },
      { "12": "December" }
    ];

    const monthMap = monthNames.reduce((acc, obj) => {
      const [key, value] = Object.entries(obj)[0];
      acc[key] = value;
      return acc;
    }, {});


    const generateMonthsInRange = (startMonth, endMonth) => {
      let months = [];
      let currentDate = new Date(startMonth);
      let endDate = new Date(endMonth);

      while (currentDate <= endDate) {
        const year = currentDate.getFullYear();
        const month = ("0" + (currentDate.getMonth() + 1)).slice(-2);  // Always two digits
        months.push(`${year}-${month}`);
        currentDate.setMonth(currentDate.getMonth() + 1); // Move to the next month
      }

      return months;

    };
    const allMonths = generateMonthsInRange(moment(query.startDate, 'DD-MM-YYYY').format('YYYY-MM'),
      moment(query.endDate, 'DD-MM-YYYY').format('YYYY-MM'));

    const result = allMonths.map(month => {
      const dataItem = gstPayable?.find(d => d.month === month);
      const monthName = monthMap[month.slice(-2)]
      return {
        month: monthName,
        taxable_amount: dataItem ? dataItem.taxable_amount : "0.00",
        invoice_amount: dataItem ? dataItem.invoice_amount : "0.00",
        igst_amount: dataItem ? dataItem.igst_amount : "0.00",
        cgst_amount: dataItem ? dataItem.cgst_amount : "0.00",
        sgst_amount: dataItem ? dataItem.sgst_amount : "0.00",
        pureAgent: dataItem ? dataItem.pureAgent : "0.00"
      }
    });

    return result;

  };
}
