import { Optional } from '@nestjs/common';
import { Organization } from 'src/modules/organization/entities/organization.entity';
import {
  BaseEntity,
  Column,
  CreateDateColumn,
  Entity,
  ManyToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import AutClientCredentials from './aut_client_credentials.entity';
import { User } from 'src/modules/users/entities/user.entity';
import GstrCredentials from 'src/modules/gstr-automation/entity/gstrCredentials.entity';
import TanClientCredentials from 'src/modules/tan-automation/entity/tan-client-credentials.entity';

export enum TypeEnum {
  INCOMETAX = 'INCOMETAX',
  GSTR = 'GSTR',
  TAN = 'TAN',
}

// ENUM('INCOMETAX', 'GSTR', 'TAN')

// id, client_ids, modules, schedule, user_id, org_id, cron_expression, created_at,
// updated_at, gstr_credentials_id, type, automation_bulk_synccol, enabled, tan_credentials_id

@Entity()
class AutomationBulkSync extends BaseEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column('json')
  modules: object;

  @Column()
  userId: number;

  @Column()
  orgId: number;

  @CreateDateColumn()
  createdAt: string;

  @UpdateDateColumn()
  updatedAt: string;

  @Column({ default: TypeEnum.INCOMETAX, type: 'enum', enum: TypeEnum })
  type: TypeEnum;
}

export default AutomationBulkSync;
