import Client from 'src/modules/clients/entity/client.entity';
import {
  BaseEntity,
  Column,
  CreateDateColumn,
  Entity,
  ManyToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';

@Entity()
class GstrUpdateTracker extends BaseEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column('json')
  noticeAndOrder: object;

  @Column('json')
  additionalNoticeAndOrder: object;

  @Column('json')
  demands: object;

  @Column('json')
  ledgers: object;

  @Column()
  isChange: boolean;

  @Column()
  organizationId: number;

  @Column()
  clientId: number;

  @ManyToOne(() => Client, (client) => client.autUpdateTracker, { onDelete: 'SET NULL' })
  client: Client;

  @Column()
  gstrCredentialsId: number;

  @CreateDateColumn()
  createdAt: string;

  @UpdateDateColumn()
  updatedAt: string;
}

export default GstrUpdateTracker;
