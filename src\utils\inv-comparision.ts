export function compareSpecificUrls(url1: string, url2: string): boolean {
    const urlA = "http://test-cmd-api.vider.in";
    const urlB = "https://test-api.vider.in";

    const urlC = "http://ec2-43-205-230-72.ap-south-1.compute.amazonaws.com";
    const urlD = "https://api-nw.vider.in";

    return ((url1 === urlA && url2 === urlB) || (url1 === urlB && url2 === urlA)) || ((url1 === urlC && url2 === urlD) || (url1 === urlD && url2 === urlC));
};
