import { IsNotEmpty, IsN<PERSON>berString, <PERSON>Option<PERSON> } from "class-validator";

export class FindClientBillingInvoices {
    @IsNotEmpty()
    @IsNumberString()
    clientId: number;

    @IsNotEmpty()
    @IsNumberString()
    billingEntityId: number;

    @IsOptional()
    pageCount: number;

    @IsOptional()
    page: number;

    @IsOptional()
    search: string;

    @IsOptional()
    clientType: string | null;

    @IsOptional()
    invoiceIds: number[];

    @IsOptional()
    sort: Object
}

