import { BadRequestException, Injectable } from '@nestjs/common';
import { User, UserStatus } from 'src/modules/users/entities/user.entity';
import { Brackets, createQ<PERSON>y<PERSON><PERSON>er, getManager, getRepository, Like } from 'typeorm';
import AutomationMachines from '../entities/automation_machines.entity';
import AutClientCredentials, { IncomeTaxStatus } from '../entities/aut_client_credentials.entity';
import AutoIncomeTaxForms from '../entities/aut_income_tax_forms.entity';
import AutIncometaxReturns from '../entities/aut_incometax_returns.entity';
import AutEProceedingFya from '../entities/aut_income_tax_eproceedings_fya.entity';
import AutEProceedingFyi from '../entities/aut_income_tax_eproceedings_fyi.entity';
import AutOutstandingDemand from '../entities/aut-outstanding-demand.entity';
import Client from 'src/modules/clients/entity/client.entity';
import * as moment from 'moment';
import AutFyaNotice from '../entities/aut_income_tax_eproceedings_fya_notice.entity';
import AutFyiNotice from '../entities/aut_income_tax_eproceedings_fyi_notice.entity';
import OrganizationPreferences from 'src/modules/organization-preferences/entity/organization-preferences.entity';
import * as xlsx from 'xlsx';
import { Permissions } from 'src/modules/tasks/permission';
import * as ExcelJS from 'exceljs';
import IncTempEproFya from '../entities/inc_temp_epro_fya.entity';
import IncTempEproFyi from '../entities/inc_temp_epro_fyi.entity';
import { generateAssessmentYear } from 'src/utils/re-use';

@Injectable()
export class AutDashboardService {
  async findCount(userId: number, query: any) {
    const user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
    const organizationId = user?.organization?.id;
    if (organizationId) {
      const clientCredential = await AutClientCredentials.count({
        where: { organizationId },
      });
      let formsCount: any;
      let returnsCount: any;
      let eProceedingFyaCount: any;
      let eProceedingFyiCount: any;
      let outstandingDemandCount: any;
      if (query?.assessmentYear !== '') {
        formsCount = await AutoIncomeTaxForms.count({
          where: { organizationId, refYear: query?.assessmentYear?.substring(0, 4) },
        });
        returnsCount = await AutIncometaxReturns.count({
          where: { organizationId, assmentYear: query?.assessmentYear?.substring(0, 4) },
        });
        outstandingDemandCount = await AutOutstandingDemand.count({
          where: { organizationId, assessmentYear: query?.assessmentYear?.substring(0, 4) },
        });
        eProceedingFyaCount = await AutEProceedingFya.count({
          where: { organizationId, assesmentYear: query?.assessmentYear?.substring(0, 4) },
        });
        eProceedingFyiCount = await AutEProceedingFyi.count({
          where: { organizationId, assessmentYear: query?.assessmentYear?.substring(0, 4) },
        });
      } else {
        formsCount = await AutoIncomeTaxForms.count({ where: { organizationId } });
        returnsCount = await AutIncometaxReturns.count({ where: { organizationId } });
        eProceedingFyaCount = await AutEProceedingFya.count({ where: { organizationId } });
        eProceedingFyiCount = await AutEProceedingFyi.count({ where: { organizationId } });
        outstandingDemandCount = await AutOutstandingDemand.count({
          where: { organizationId },
        });
      }

      return {
        clientCredentialCount: clientCredential,
        formsCount,
        returnsCount,
        eProceedingFyaCount,
        eProceedingFyiCount,
        outstandingDemandCount,
      };
    }
  }

  async statusWiseReturnsCount(userId: number, query: any) {
    const user = await User.findOne({ where: { id: userId }, relations: ['organization', 'role'] });
    let ViewAll = user.role.permissions.find(
      (permission) => permission.slug === Permissions.VIEW_ALL_CLIENT_MANAGERS,
    );
    let ViewAssigned = user.role.permissions.find(
      (permission) => permission.slug === Permissions.VIEW_ASSIGNED_CLIENT_MANAGERS,
    );

    const organizationId = user?.organization?.id;
    if (organizationId) {
      let sql = `
      SELECT
        SUM(CASE WHEN filing_type_cd = 'O' THEN 1 ELSE 0 END) AS original,
        SUM(CASE WHEN filing_type_cd = 'D' THEN 1 ELSE 0 END) AS defective,
        SUM(CASE WHEN filing_type_cd = 'R' THEN 1 ELSE 0 END) AS revised,
        SUM(CASE WHEN filing_type_cd = 'T' THEN 1 ELSE 0 END) AS rectification,
        SUM(CASE WHEN filing_type_cd = 'U' THEN 1 ELSE 0 END) AS updated,
         SUM(
          CASE WHEN filing_type_cd = 'O' THEN 1 ELSE 0 END +
          CASE WHEN filing_type_cd = 'D' THEN 1 ELSE 0 END +
          CASE WHEN filing_type_cd = 'R' THEN 1 ELSE 0 END +
          CASE WHEN filing_type_cd = 'T' THEN 1 ELSE 0 END +
          CASE WHEN filing_type_cd = 'U' THEN 1 ELSE 0 END
         ) AS returnsCount
      FROM 
        aut_incometax_returns
        LEFT JOIN 
          client ON aut_incometax_returns.client_id = client.id
        LEFT JOIN 
          aut_client_credentials ON aut_incometax_returns.aut_client_credentials_id = aut_client_credentials.id
      WHERE
        aut_incometax_returns.organization_id = ${organizationId}
        AND client.status != '${UserStatus.DELETED}'
        AND aut_client_credentials.status != '${IncomeTaxStatus.DISABLE}'
      `;

      if (ViewAssigned && !ViewAll) {
        sql += `
        AND EXISTS (
          SELECT 1
          FROM client_client_managers_user cm
          WHERE cm.client_id = client.id
          AND cm.user_id = ${userId}
        )
      `;
      }

      if (query.assessmentYear && query.assessmentYear !== '') {
        sql += ` AND assment_year = '${query.assessmentYear}'`;
      }
      const result = await getManager().query(sql);
      const statusObj = result[0];
      return statusObj;
    }
  }

  async typeWiseReturnsCount(userId: number, query: any) {
    const user = await User.findOne({ where: { id: userId }, relations: ['organization', 'role'] });
    let ViewAll = user.role.permissions.find(
      (permission) => permission.slug === Permissions.VIEW_ALL_CLIENT_MANAGERS,
    );
    let ViewAssigned = user.role.permissions.find(
      (permission) => permission.slug === Permissions.VIEW_ASSIGNED_CLIENT_MANAGERS,
    );
    const organizationId = user?.organization?.id;
    if (organizationId) {
      let sql = `
    SELECT
      SUM(CASE WHEN formtype_cd = '1' THEN 1 ELSE 0 END) AS ITR1,
      SUM(CASE WHEN formtype_cd = '2' THEN 1 ELSE 0 END) AS ITR2,
      SUM(CASE WHEN formtype_cd = '2A' THEN 1 ELSE 0 END) AS ITR2A,
      SUM(CASE WHEN formtype_cd = '3' THEN 1 ELSE 0 END) AS ITR3,
      SUM(CASE WHEN formtype_cd = '4' THEN 1 ELSE 0 END) AS ITR4,
      SUM(CASE WHEN formtype_cd = '4S' THEN 1 ELSE 0 END) AS ITR4S,
      SUM(CASE WHEN formtype_cd = '5' THEN 1 ELSE 0 END) AS ITR5,
      SUM(CASE WHEN formtype_cd = '6' THEN 1 ELSE 0 END) AS ITR6,
       SUM(CASE WHEN formtype_cd = '7' THEN 1 ELSE 0 END) AS ITR7
    FROM 
      aut_incometax_returns
    LEFT JOIN
      client ON aut_incometax_returns.client_id = client.id
    LEFT JOIN 
      aut_client_credentials ON aut_incometax_returns.aut_client_credentials_id = aut_client_credentials.id
    WHERE
      aut_incometax_returns.organization_id = ${organizationId}
        AND client.status != '${UserStatus.DELETED}'
        AND aut_client_credentials.status != '${IncomeTaxStatus.DISABLE}'
  `;

      if (ViewAssigned && !ViewAll) {
        sql += `
        AND EXISTS (
          SELECT 1
          FROM client_client_managers_user cm
          WHERE cm.client_id = client.id
          AND cm.user_id = ${userId}
        )
      `;
      }

      if (query.assessmentYear && query.assessmentYear !== '') {
        sql += ` AND assment_year = '${query.assessmentYear}'`;
      }

      const result = await getManager().query(sql);
      return result[0];
    }
  }

  async verificationWiseReturnsCount(userId: number, query: any) {
    const user = await User.findOne({ where: { id: userId }, relations: ['organization', 'role'] });
    let ViewAll = user.role.permissions.find(
      (permission) => permission.slug === Permissions.VIEW_ALL_CLIENT_MANAGERS,
    );
    let ViewAssigned = user.role.permissions.find(
      (permission) => permission.slug === Permissions.VIEW_ASSIGNED_CLIENT_MANAGERS,
    );

    const organizationId = user?.organization.id;
    if (organizationId) {
      let sql = `
      SELECT 
        SUM(CASE WHEN ver_status = 'Y' THEN 1 ELSE 0 END) AS verified,
        SUM(CASE WHEN ver_status = 'N' THEN 1 ELSE 0 END) AS notVerified,
        SUM(CASE WHEN ver_status = 'X' THEN 1 ELSE 0 END) AS none
      FROM 
        aut_incometax_returns
    LEFT JOIN 
        client ON aut_incometax_returns.client_id = client.id
    LEFT JOIN 
      aut_client_credentials ON aut_incometax_returns.aut_client_credentials_id = aut_client_credentials.id
    WHERE 
        aut_incometax_returns.organization_id = ${organizationId}
        AND client.status != '${UserStatus.DELETED}'
        AND aut_client_credentials.status != '${IncomeTaxStatus.DISABLE}'
      `;

      if (ViewAssigned && !ViewAll) {
        sql += `
        AND EXISTS (
          SELECT 1
          FROM client_client_managers_user cm
          WHERE cm.client_id = client.id
          AND cm.user_id = ${userId}
        )
      `;
      }

      if (query.assessmentYear && query.assessmentYear !== '') {
        sql += ` AND assment_year = '${query.assessmentYear}'`;
      }

      const result = await getManager().query(sql);
      return result[0];
    }
  }

  async getDemandRaisedDates(
    userId: number,
    interval: '1week' | '15days' | '1month' | '1year',
  ): Promise<number> {
    try {
      const user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
      const organizationId = user?.organization?.id;

      let intervalQuery = '';
      switch (interval) {
        case '1week':
          intervalQuery = 'INTERVAL 1 WEEK';
          break;
        case '15days':
          intervalQuery = 'INTERVAL 15 DAY';
          break;
        case '1month':
          intervalQuery = 'INTERVAL 1 MONTH';
          break;
        case '1year':
          intervalQuery = 'INTERVAL 1 YEAR';
          break;
        default:
          throw new Error('Invalid interval');
      }

      const sql = `
        SELECT COUNT(*) AS count
        FROM aut_outstanding_demand
        WHERE demand_raised_date >= DATE_SUB(CURDATE(), ${intervalQuery})
        AND organization_id = ${organizationId};
      `;

      const result = await getManager().query(sql);
      return parseInt(result[0].count);
    } catch (error) {
      console.error('Error fetching demand raised filter dates:', error);
      throw error;
    }
  }

  async getNoticeDates(
    organizationId: number,
    interval: '1week' | '15days' | '1month' | '1year' | 'today',
    dateColumn: 'issued_on' | 'response_due_date' | 'remark_submitted_on',
    query: any,
    userId: any,
    ViewAll: any,
    ViewAssigned: any,
  ): Promise<number> {
    try {
      if (!organizationId) {
        throw new Error('Organization not found for the user.');
      }

      let intervalQuery = '';
      switch (interval) {
        case 'today':
          intervalQuery = 'INTERVAL 1 DAY';
          break;
        case '1week':
          intervalQuery = 'INTERVAL 1 WEEK';
          break;
        case '15days':
          intervalQuery = 'INTERVAL 15 DAY';
          break;
        case '1month':
          intervalQuery = 'INTERVAL 1 MONTH';
          break;
        case '1year':
          intervalQuery = 'INTERVAL 1 YEAR';
          break;
        default:
          throw new Error('Invalid interval');
      }

      // let additionalCondition = '';
      // if (dateColumn === 'response_due_date') {
      //   additionalCondition = 'AND remark_submitted_on IS NOT NULL';
      // }

      // let sql = `
      //   SELECT COUNT(*) AS count
      //   FROM aut_fya_notice
      //   WHERE ${dateColumn} >= DATE_SUB(CURDATE(), ${intervalQuery})
      //   AND organization_id = ${organizationId}
      //   ${additionalCondition}
      // `;
      let additionalCondition = '';
      let joinClause = '';
      if (dateColumn === 'response_due_date') {
        additionalCondition = 'AND remark_submitted_on IS NOT NULL';
      }

      if (dateColumn === 'remark_submitted_on') {
        joinClause = `
          LEFT JOIN aut_proceeding_response_fya AS r ON aut_fya_notice.id = r.fya_notice_id
        `;

         if (interval === 'today') {
            additionalCondition = `AND DATE(r.submitted_on) = CURDATE()`;
          } else {
            additionalCondition = `AND r.submitted_on >= DATE_SUB(CURDATE(), ${intervalQuery})`;
          }
       
      } else {

        if(interval === 'today'){
          additionalCondition = `AND DATE(aut_fya_notice.${dateColumn}) = CURDATE()`;
        }else{
          additionalCondition = `
            AND aut_fya_notice.${dateColumn} >= DATE_SUB(CURDATE(), ${intervalQuery})
          `;
        }

      }

      //   let sql = `
      //   SELECT COUNT(*) AS count
      //   FROM aut_fya_notice
      //   ${joinClause}
      //   WHERE aut_fya_notice.organization_id = ${organizationId}
      //   ${additionalCondition}
      // `;
      let sql = `
      SELECT COUNT(*) AS count
      FROM aut_fya_notice
      LEFT JOIN client ON aut_fya_notice.client_id = client.id
      LEFT JOIN aut_client_credentials ON aut_client_credentials.client_id = client.id
      ${joinClause}
      WHERE aut_fya_notice.organization_id = ${organizationId}
      AND (client.status IS NULL OR client.status != '${UserStatus.DELETED}')
      AND (aut_client_credentials.status IS NULL OR aut_client_credentials.status != 'DISABLE')
      ${additionalCondition}
    `;
      if (ViewAssigned && !ViewAll) {
        sql += `
          AND EXISTS (
            SELECT 1
            FROM client_client_managers_user cm
            WHERE cm.client_id = client.id
            AND cm.user_id = ${userId}
          )
        `;
      }
      if (query.assessmentYear && query.assessmentYear !== '') {
        sql += `AND assesment_year = '${query.assessmentYear}'`;
      }

      const result = await getManager().query(sql);
      return parseInt(result[0].count);
    } catch (error) {
      console.error(`Error fetching ${dateColumn} dates:`, error);
      throw error;
    }
  }

  async getNoticeFyaResponseDueDates(
    organizationId: number,
    interval: '1week' | '15days' | '1month' | '1year' | 'today',
    dateColumn: 'issued_on' | 'response_due_date' | 'remark_submitted_on' | 'manual_due_date',
    query: any,
    userId: any,
    ViewAll: any,
    ViewAssigned: any,
  ): Promise<number> {
    try {
      if (!organizationId) {
        throw new Error('Organization not found for the user.');
      }

      let intervalQuery = '';
      switch (interval) {
        case 'today':
          intervalQuery = 'INTERVAL 1 DAY';
          break;
        case '1week':
          intervalQuery = 'INTERVAL 1 WEEK';
          break;
        case '15days':
          intervalQuery = 'INTERVAL 15 DAY';
          break;
        case '1month':
          intervalQuery = 'INTERVAL 1 MONTH';
          break;
        case '1year':
          intervalQuery = 'INTERVAL 1 YEAR';
          break;
        default:
          throw new Error('Invalid interval');
      }

      // let additionalCondition = '';

      // if (dateColumn === 'response_due_date') {
      //   additionalCondition = 'AND remark_submitted_on IS NULL';
      // }

      let dueDateCondition = '';
      let additionalCondition = '';

      if (dateColumn === 'response_due_date') {
        dueDateCondition = `
    (
      aut_fya_notice.response_due_date BETWEEN CURDATE() AND DATE_ADD(CURDATE(), ${intervalQuery})
      OR aut_fya_notice.manual_due_date BETWEEN CURDATE() AND DATE_ADD(CURDATE(), ${intervalQuery})
    )
  `;
        additionalCondition = 'AND remark_submitted_on IS NULL';
      } else {
        dueDateCondition = `
    ${dateColumn} BETWEEN CURDATE() AND DATE_ADD(CURDATE(), ${intervalQuery})
  `;
      }
      let sql = `
      SELECT count(*) as count
      FROM aut_fya_notice
      LEFT JOIN client ON aut_fya_notice.client_id = client.id
      LEFT JOIN aut_client_credentials ON aut_client_credentials.client_id = client.id
     WHERE ${dueDateCondition}
      AND aut_fya_notice.organization_id = ${organizationId}
      AND (client.status IS NULL OR client.status != '${UserStatus.DELETED}')
      AND (aut_client_credentials.status IS NULL OR aut_client_credentials.status != 'DISABLE')
      ${additionalCondition}
    `;

      if (ViewAssigned && !ViewAll) {
        sql += `
          AND EXISTS (
            SELECT 1
            FROM client_client_managers_user cm
            WHERE cm.client_id = client.id
            AND cm.user_id = ${userId}
          )
        `;
      }
      if (query.assessmentYear && query.assessmentYear !== '') {
        sql += ` AND assesment_year = '${query.assessmentYear}'`;
      }

      const result = await getManager().query(sql);
      return parseInt(result[0]?.count, 10);
    } catch (error) {
      console.error(`Error fetching ${dateColumn} dates:`, error);
      throw error;
    }
  }

  async getNoticeFyiDates(
    organizationId: number,
    interval: '1week' | '15days' | '1month' | '1year' | 'today',
    dateColumn: 'issued_on' | 'response_due_date' | 'remark_submitted_on',
    query: any,
    userId: any,
    ViewAll: any,
    ViewAssigned: any,
  ): Promise<number> {
    try {
      if (!organizationId) {
        throw new Error('Organization not found for the user.');
      }

      let intervalQuery = '';
      switch (interval) {
         case 'today':
          intervalQuery = 'INTERVAL 1 DAY';
          break;
        case '1week':
          intervalQuery = 'INTERVAL 1 WEEK';
          break;
        case '15days':
          intervalQuery = 'INTERVAL 15 DAY';
          break;
        case '1month':
          intervalQuery = 'INTERVAL 1 MONTH';
          break;
        case '1year':
          intervalQuery = 'INTERVAL 1 YEAR';
          break;
        default:
          throw new Error('Invalid interval');
      }

      let additionalCondition = '';
      let joinClause = '';
      if (dateColumn === 'response_due_date') {
        additionalCondition = 'AND remark_submitted_on IS NOT NULL';
      }

      if (dateColumn === 'remark_submitted_on') {
        joinClause = `
          LEFT JOIN aut_proceeding_response_fyi AS r ON aut_fyi_notice.id = r.fyi_notice_id
        `;
          if (interval === 'today') {
            additionalCondition = `AND DATE(r.submitted_on) = CURDATE()`;
          } else {
            additionalCondition = `AND r.submitted_on >= DATE_SUB(CURDATE(), ${intervalQuery})`;
          }
      } else {
            if (interval === 'today') {
            additionalCondition = `AND DATE(aut_fyi_notice.${dateColumn}) = CURDATE()`;
          } else {
            additionalCondition = `AND aut_fyi_notice.${dateColumn} >= DATE_SUB(CURDATE(), ${intervalQuery})`;
          }
      }

      let sql = `
      SELECT COUNT(*) AS count
      FROM aut_fyi_notice
      LEFT JOIN client ON aut_fyi_notice.client_id = client.id
      LEFT JOIN aut_client_credentials ON aut_client_credentials.client_id = client.id
      ${joinClause}
      WHERE aut_fyi_notice.organization_id = ${organizationId}
      AND (client.status IS NULL OR client.status != '${UserStatus.DELETED}')
      AND (aut_client_credentials.status IS NULL OR aut_client_credentials.status != 'DISABLE')
      ${additionalCondition}
    `;

      if (ViewAssigned && !ViewAll) {
        sql += `
          AND EXISTS (
            SELECT 1
            FROM client_client_managers_user cm
            WHERE cm.client_id = client.id
            AND cm.user_id = ${userId}
          )
        `;
      }
      if (query?.assessmentYear && query?.assessmentYear !== '') {
        sql += ` AND assessment_year = '${query.assessmentYear}'`;
      }

      const result = await getManager().query(sql);
      return parseInt(result[0]?.count);
    } catch (error) {
      console.error(`Error fetching ${dateColumn} dates:`, error);
      throw error;
    }
  }

  async getNoticeFyiResponseDueDates(
    organizationId: number,
    interval: '1week' | '15days' | '1month' | '1year' | 'today',
    dateColumn: 'issued_on' | 'response_due_date' | 'remark_submitted_on',
    query: any,
    userId: any,
    ViewAll: any,
    ViewAssigned: any,
  ): Promise<number> {
    try {
      if (!organizationId) {
        throw new Error('Organization not found for the user.');
      }

      let intervalQuery = '';
      switch (interval) {
        case 'today':
          intervalQuery = 'INTERVAL 1 DAY';
          break;
        case '1week':
          intervalQuery = 'INTERVAL 1 WEEK';
          break;
        case '15days':
          intervalQuery = 'INTERVAL 15 DAY';
          break;
        case '1month':
          intervalQuery = 'INTERVAL 1 MONTH';
          break;
        case '1year':
          intervalQuery = 'INTERVAL 1 YEAR';
          break;
        default:
          throw new Error('Invalid interval');
      }

      let additionalCondition = '';
      if (dateColumn === 'response_due_date') {
        additionalCondition = 'AND remark_submitted_on IS NULL';
      }

      let sql = `
      SELECT COUNT(*) AS count
      FROM aut_fyi_notice
      LEFT JOIN client ON aut_fyi_notice.client_id = client.id
      LEFT JOIN aut_client_credentials ON aut_client_credentials.client_id = client.id
      WHERE ${dateColumn} BETWEEN CURDATE() AND DATE_ADD(CURDATE(), ${intervalQuery})
      AND aut_fyi_notice.organization_id = ${organizationId}
      AND (client.status IS NULL OR client.status != '${UserStatus.DELETED}')
      AND (aut_client_credentials.status IS NULL OR aut_client_credentials.status != 'DISABLE')
      ${additionalCondition}
    `;

      if (ViewAssigned && !ViewAll) {
        sql += `
          AND EXISTS (
            SELECT 1
            FROM client_client_managers_user cm
            WHERE cm.client_id = client.id
            AND cm.user_id = ${userId}
          )
        `;
      }
      if (query?.assessmentYear && query?.assessmentYear !== '') {
        sql += `AND assessment_year = '${query?.assessmentYear}'`;
      }
      const result = await getManager().query(sql);
      return parseInt(result[0]?.count, 10);
    } catch (error) {
      console.error(`Error fetching ${dateColumn} dates:`, error);
      throw error;
    }
  }

  async getCombinedNoticesCount(userId: number, query: any) {
    const user = await User.findOne({ where: { id: userId }, relations: ['organization', 'role'] });
    let ViewAll = user.role.permissions.find(
      (permission) => permission.slug === Permissions.VIEW_ALL_CLIENT_MANAGERS,
    );
    let ViewAssigned = user.role.permissions.find(
      (permission) => permission.slug === Permissions.VIEW_ASSIGNED_CLIENT_MANAGERS,
    );
    const organizationId = user?.organization?.id;
    if (organizationId) {
      // FYA Notices

      const fyaTodayIssued = await this.getNoticeDates(
        organizationId,
        'today',
        'issued_on',
        query,
        userId,
        ViewAll,
        ViewAssigned,
      );
      
      const fyaLast1WeekIssued = await this.getNoticeDates(
        organizationId,
        '1week',
        'issued_on',
        query,
        userId,
        ViewAll,
        ViewAssigned,
      );
      const fyaLast15DaysIssued = await this.getNoticeDates(
        organizationId,
        '15days',
        'issued_on',
        query,
        userId,
        ViewAll,
        ViewAssigned,
      );
      const fyaLast1MonthIssued = await this.getNoticeDates(
        organizationId,
        '1month',
        'issued_on',
        query,
        userId,
        ViewAll,
        ViewAssigned,
      );


       const fyaTodayDue = await this.getNoticeFyaResponseDueDates(
        organizationId,
        'today',
        'response_due_date',
        query,
        userId,
        ViewAll,
        ViewAssigned,
      );

      const fyaLast1WeekDue = await this.getNoticeFyaResponseDueDates(
        organizationId,
        '1week',
        'response_due_date',
        query,
        userId,
        ViewAll,
        ViewAssigned,
      );
      const fyaLast15DaysDue = await this.getNoticeFyaResponseDueDates(
        organizationId,
        '15days',
        'response_due_date',
        query,
        userId,
        ViewAll,
        ViewAssigned,
      );
      const fyaLast1MonthDue = await this.getNoticeFyaResponseDueDates(
        organizationId,
        '1month',
        'response_due_date',
        query,
        userId,
        ViewAll,
        ViewAssigned,
      );

      const fyaLast1WeekManualDue = await this.getNoticeFyaResponseDueDates(
        organizationId,
        '1week',
        'manual_due_date',
        query,
        userId,
        ViewAll,
        ViewAssigned,
      );
      const fyaLast15DaysManualDue = await this.getNoticeFyaResponseDueDates(
        organizationId,
        '15days',
        'manual_due_date',
        query,
        userId,
        ViewAll,
        ViewAssigned,
      );
      const fyaLast1MonthManualDue = await this.getNoticeFyaResponseDueDates(
        organizationId,
        '1month',
        'manual_due_date',
        query,
        userId,
        ViewAll,
        ViewAssigned,
      );

      const fyaTodaySubmit = await this.getNoticeDates(
        organizationId,
        'today',
        'remark_submitted_on',
        query,
        userId,
        ViewAll,
        ViewAssigned,
      );

      const fyaLast1WeekSubmit = await this.getNoticeDates(
        organizationId,
        '1week',
        'remark_submitted_on',
        query,
        userId,
        ViewAll,
        ViewAssigned,
      );
      const fyaLast15DaysSubmit = await this.getNoticeDates(
        organizationId,
        '15days',
        'remark_submitted_on',
        query,
        userId,
        ViewAll,
        ViewAssigned,
      );
      const fyaLast1MonthSubmit = await this.getNoticeDates(
        organizationId,
        '1month',
        'remark_submitted_on',
        query,
        userId,
        ViewAll,
        ViewAssigned,
      );

      // FYI Notices

       const fyiTodayIssued = await this.getNoticeFyiDates(
        organizationId,
        'today',
        'issued_on',
        query,
        userId,
        ViewAll,
        ViewAssigned,
      );

      const fyiLast1WeekIssued = await this.getNoticeFyiDates(
        organizationId,
        '1week',
        'issued_on',
        query,
        userId,
        ViewAll,
        ViewAssigned,
      );
      const fyiLast15DaysIssued = await this.getNoticeFyiDates(
        organizationId,
        '15days',
        'issued_on',
        query,
        userId,
        ViewAll,
        ViewAssigned,
      );
      const fyiLast1MonthIssued = await this.getNoticeFyiDates(
        organizationId,
        '1month',
        'issued_on',
        query,
        userId,
        ViewAll,
        ViewAssigned,
      );

      const fyiTodayDue = await this.getNoticeFyiResponseDueDates(
        organizationId,
        'today',
        'response_due_date',
        query,
        userId,
        ViewAll,
        ViewAssigned,
      );

      const fyiLast1WeekDue = await this.getNoticeFyiResponseDueDates(
        organizationId,
        '1week',
        'response_due_date',
        query,
        userId,
        ViewAll,
        ViewAssigned,
      );
      const fyiLast15DaysDue = await this.getNoticeFyiResponseDueDates(
        organizationId,
        '15days',
        'response_due_date',
        query,
        userId,
        ViewAll,
        ViewAssigned,
      );
      const fyiLast1MonthDue = await this.getNoticeFyiResponseDueDates(
        organizationId,
        '1month',
        'response_due_date',
        query,
        userId,
        ViewAll,
        ViewAssigned,
      );

      const fyiTodaySubmit = await this.getNoticeFyiDates(
        organizationId,
        'today',
        'remark_submitted_on',
        query,
        userId,
        ViewAll,
        ViewAssigned,
      );

      const fyiLast1WeekSubmit = await this.getNoticeFyiDates(
        organizationId,
        '1week',
        'remark_submitted_on',
        query,
        userId,
        ViewAll,
        ViewAssigned,
      );
      const fyiLast15DaysSubmit = await this.getNoticeFyiDates(
        organizationId,
        '15days',
        'remark_submitted_on',
        query,
        userId,
        ViewAll,
        ViewAssigned,
      );
      const fyiLast1MonthSubmit = await this.getNoticeFyiDates(
        organizationId,
        '1month',
        'remark_submitted_on',
        query,
        userId,
        ViewAll,
        ViewAssigned,
      );

      return {
        issueData: {
          last1WeekIssued: fyaLast1WeekIssued + fyiLast1WeekIssued,
          last15DaysIssued: fyaLast15DaysIssued + fyiLast15DaysIssued,
          last1MonthIssued: fyaLast1MonthIssued + fyiLast1MonthIssued,
          todayIssued: fyaTodayIssued + fyiTodayIssued
        },
        responseDueData: {
          last1WeekDue: fyaLast1WeekDue + fyiLast1WeekDue,
          last15DaysDue: fyaLast15DaysDue + fyiLast15DaysDue,
          last1MonthDue: fyaLast1MonthDue + fyiLast1MonthDue,
          todayDue: fyaTodayDue + fyiTodayDue
        },
        submitData: {
          last1WeekSubmit: fyaLast1WeekSubmit + fyiLast1WeekSubmit,
          last15DaysSubmit: fyaLast15DaysSubmit + fyiLast15DaysSubmit,
          last1MonthSubmit: fyaLast1MonthSubmit + fyiLast1MonthSubmit,
          todaySubmit: fyaTodaySubmit + fyiTodaySubmit
        },
      };
    } else {
      return {
        issueData: {
          last1WeekIssued: 0,
          last15DaysIssued: 0,
          last1MonthIssued: 0,
          todayIssued: 0
        },
        responseDueData: {
          last1WeekDue: 0,
          last15DaysDue: 0,
          last1MonthDue: 0,
          todayDue:0
        },
        submitData: {
          last1WeekSubmit: 0,
          last15DaysSubmit: 0,
          last1MonthSubmit: 0,
          todaySubmit: 0
        },
      };
    }
  }

  async demandRaisedfilterDates(userId: number) {
    try {
      const last1Week = await this.getDemandRaisedDates(userId, '1week');
      const last15Days = await this.getDemandRaisedDates(userId, '15days');
      const last1Month = await this.getDemandRaisedDates(userId, '1month');
      const last1Year = await this.getDemandRaisedDates(userId, '1year');

      return {
        last1Week,
        last15Days,
        last1Month,
        last1Year,
      };
    } catch (error) {
      console.error('Error fetching demand raised filter dates:', error);
      throw error;
    }
  }

  async incometaxClientCheck(userId, queryy) {
    const { search, offset, limit } = queryy;
    const user = await User.findOne({
      where: { id: userId },
      relations: ['organization', 'role'],
    });

    let ViewAll = user.role.permissions.find(
      (permission) => permission.slug === Permissions.VIEW_ALL_CLIENT_MANAGERS,
    );
    let ViewAssigned = user.role.permissions.find(
      (permission) => permission.slug === Permissions.VIEW_ASSIGNED_CLIENT_MANAGERS,
    );
    const entityManager = getRepository(AutClientCredentials);

    // Directly use a raw SQL query to get the desired result
    const query = await entityManager
      .createQueryBuilder('autoCredentials')
      .leftJoinAndSelect('autoCredentials.client', 'client')
      .leftJoin('client.clientManagers', 'clientManagers')
      .where('autoCredentials.organizationId = :id', { id: user.organization.id })
      .andWhere('client.status != :status', { status: UserStatus.DELETED })
      .andWhere('autoCredentials.status = :inStatus', { inStatus: IncomeTaxStatus.ENABLE })
      .andWhere('autoCredentials.remarks IN (:...remarks)', {
        remarks: ['Invalid Password, Please retry.'],
      });

    if (search) {
      query.andWhere(
        new Brackets((qb) => {
          qb.where('autoCredentials.pan_number LIKE :search', {
            search: `%${search}%`,
          });
          qb.orWhere('client.displayName LIKE :namesearch', {
            namesearch: `%${search}%`,
          });
        }),
      );
    }

    if (ViewAssigned && !ViewAll) {
      query.andWhere('clientManagers.id = :userId', { userId });
    }

    if (offset) {
      query.skip(offset);
    }

    if (limit) {
      query.take(limit);
    }

    const [filteredRows, totalCount] = await query.getManyAndCount();
    // const filteredRows = query.filter((item) => item.remarks !== 'Success');

    // const totalClients = await AutClientCredentials.count({
    //   where: { organizationId: user.organization.id },
    // });

    const totalClientsQuery = await createQueryBuilder(AutClientCredentials, 'credentials')
      .leftJoin('credentials.client', 'client')
      .leftJoin('client.clientManagers', 'clientManagers')
      .where('credentials.organizationId = :organizationId', {
        organizationId: user?.organization?.id,
      })
      .andWhere('client.status != :status', { status: UserStatus.DELETED })
      .andWhere('credentials.status = :inStatus', { inStatus: IncomeTaxStatus.ENABLE });

    if (ViewAssigned && !ViewAll) {
      totalClientsQuery.andWhere('clientManagers.id = :userId', { userId });
    }

    const totalClients = await totalClientsQuery.getCount();

    // const uniquePansCount = await Client.count({ where: { organization: user.organization } });

    const clientQuery = await createQueryBuilder(Client, 'client')
      .leftJoin('client.clientManagers', 'clientManagers')
      .select('COUNT(DISTINCT client.pan_number)', 'count')
      .where('client.organization_id = :orgId', { orgId: user.organization?.id })
      .andWhere('client.pan_number IS NOT NULL');

    if (ViewAssigned && !ViewAll) {
      clientQuery.andWhere('clientManagers.id = :userId', { userId });
    }

    const client = await clientQuery.getRawOne();

    const count = parseInt(client.count);
    const result = {
      filteredRows,
      totalClients,
      count: filteredRows.length,
      uniquePansCount: count,
      totalCount,
    };
    return result;
  }

  async exportIncomeTaxInvalid(userId: number, query: any) {
    const newQuery = { ...query, offset: 0, limit: ********* };

    let invalidRows = await this.incometaxClientCheck(userId, newQuery);
    if (!invalidRows.filteredRows.length) throw new BadRequestException('No Data for Export');
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Inc Tax Returns');
    const headers = [
      { header: 'S.No', key: 'serialNo' },
      { header: 'Category', key: 'category' },
      { header: 'Client Name', key: 'clientName' },
      { header: 'Client ID', key: 'clientId' },
      { header: 'PAN', key: 'pan' },
      { header: 'Remarks', key: 'remarks' },
    ];

    worksheet.columns = headers;
    const columnMaxLengths = Array(headers.length).fill(0);
    let serialCounter = 1; // Initialize a counter
    if (!invalidRows.filteredRows.length) throw new BadRequestException('No Data for Export');
    invalidRows.filteredRows.forEach((invalidRow) => {
      const rowData = {
        serialNo: serialCounter++, // Assign and then increment the counter
        category: invalidRow?.client?.category,
        clientName: invalidRow?.client?.displayName,
        clientId: invalidRow?.client?.clientId,
        pan: invalidRow?.panNumber,
        remarks: invalidRow?.remarks,
      };

      const row = worksheet.addRow(rowData);

      worksheet.columns.forEach((column, colIndex) => {
        const headerLength = column.header?.length || 0;
        const cellLength = rowData[column.key]?.toString().length || 0;
        columnMaxLengths[colIndex] = Math.max(
          columnMaxLengths[colIndex] || 0,
          headerLength,
          cellLength,
        );
      });
    });

    worksheet.columns.forEach((column, colIndex) => {
      column.width = columnMaxLengths[colIndex] + 2;
    });

    const headerRow = worksheet.getRow(1);
    headerRow.font = { bold: true };
    headerRow.eachCell((cell) => {
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '64B5F6' },
      };
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' },
      };
      cell.alignment = { horizontal: 'center', vertical: 'middle' };
    });

    worksheet.columns.forEach((column) => {
      if (column.key === 'clientName') {
        column.width = 50;
        column.alignment = { wrapText: true, horizontal: 'center', vertical: 'middle' };
      } else {
        column.alignment = { horizontal: 'center', vertical: 'middle' };
      }
    });

    worksheet.eachRow((row) => {
      row.eachCell((cell) => {
        cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
      });
    });

    worksheet.views = [{ state: 'frozen', ySplit: 1 }];

    const buffer = await workbook.xlsx.writeBuffer();
    return buffer;
  }

  async getFyaEvents(userId, query: Date) {
    let user = await User.findOne(userId, { relations: ['organization', 'role'] });
    let ViewAll = user.role.permissions.find(
      (permission) => permission.slug === Permissions.VIEW_ALL_CLIENT_MANAGERS,
    );
    let ViewAssigned = user.role.permissions.find(
      (permission) => permission.slug === Permissions.VIEW_ASSIGNED_CLIENT_MANAGERS,
    );
    let fyaNotice = createQueryBuilder(AutFyaNotice, 'fyaNotice')
      .leftJoinAndSelect('fyaNotice.client', 'client')
      .leftJoin('client.clientManagers', 'clientManagers')
      .leftJoinAndSelect('client.autClientCredentials', 'autClientCredentials')
      .where('fyaNotice.organizationId = :organizationId', {
        organizationId: user?.organization?.id,
      })
      .andWhere('client.status != :status', { status: UserStatus.DELETED })
      .andWhere('autClientCredentials.status != :disStatus', {
        disStatus: IncomeTaxStatus.DISABLE,
      });

    if (ViewAssigned && !ViewAll) {
      fyaNotice.andWhere('clientManagers.id = :userId', { userId });
    }

    if (query) {
      const startOfMonth = moment(`${query}`).startOf('month').format('YYYY-MM-DD');
      const endOfMonth = moment(query).endOf('month').format('YYYY-MM-DD');
      fyaNotice.andWhere(`Date(fyaNotice.issuedOn) BETWEEN '${startOfMonth}' AND '${endOfMonth}'`);
    }

    let fyiNotice = createQueryBuilder(AutFyiNotice, 'fyiNotice')
      .leftJoinAndSelect('fyiNotice.client', 'client')
      .leftJoin('client.clientManagers', 'clientManagers')
      .leftJoinAndSelect('client.autClientCredentials', 'autClientCredentials')
      .where('fyiNotice.organizationId = :organizationId', {
        organizationId: user?.organization?.id,
      })
      .andWhere('client.status != :status', { status: UserStatus.DELETED })
      .andWhere('autClientCredentials.status != :disStatus', {
        disStatus: IncomeTaxStatus.DISABLE,
      });

    if (ViewAssigned && !ViewAll) {
      fyiNotice.andWhere('clientManagers.id = :userId', { userId });
    }

    if (query) {
      const startOfMonth = moment(`${query}`).startOf('month').format('YYYY-MM-DD');
      const endOfMonth = moment(query).endOf('month').format('YYYY-MM-DD');
      fyiNotice.andWhere(`Date(fyiNotice.issuedOn) BETWEEN '${startOfMonth}' AND '${endOfMonth}'`);
    }
    const result = await fyaNotice.getMany();
    const result2 = await fyiNotice.getMany();

    const fyaNotices = result.map((notice) => ({ ...notice, type: 'FYA' }));
    const fyiNotices = result2.map((notice) => ({ ...notice, type: 'FYI' }));
    const result1and2 = [...fyaNotices, ...fyiNotices];
    return result1and2;
  }

  async getResponseDueEvents(userId, query: Date) {
    let user = await User.findOne(userId, { relations: ['organization', 'role'] });
    let ViewAll = user.role.permissions.find(
      (permission) => permission.slug === Permissions.VIEW_ALL_CLIENT_MANAGERS,
    );
    let ViewAssigned = user.role.permissions.find(
      (permission) => permission.slug === Permissions.VIEW_ASSIGNED_CLIENT_MANAGERS,
    );
    let fyaNotice = createQueryBuilder(AutFyaNotice, 'fyaNotice')
      .leftJoinAndSelect('fyaNotice.client', 'client')
      .leftJoin('client.clientManagers', 'clientManagers')
      .leftJoinAndSelect('client.autClientCredentials', 'autClientCredentials')
      .where('fyaNotice.organizationId = :organizationId', {
        organizationId: user?.organization?.id,
      })
      .andWhere('client.status != :status', { status: UserStatus.DELETED })
      .andWhere('autClientCredentials.status != :disStatus', {
        disStatus: IncomeTaxStatus.DISABLE,
      });

    if (ViewAssigned && !ViewAll) {
      fyaNotice.andWhere('clientManagers.id = :userId', { userId });
    }

    if (query) {
      const startOfMonth = moment(`${query}`).startOf('month').format('YYYY-MM-DD');
      const endOfMonth = moment(query).endOf('month').format('YYYY-MM-DD');
      fyaNotice.andWhere(
        `(Date(fyaNotice.responseDueDate) BETWEEN '${startOfMonth}' AND '${endOfMonth}')`,
      );
    }

    let fyiNotice = createQueryBuilder(AutFyiNotice, 'fyiNotice')
      .leftJoinAndSelect('fyiNotice.client', 'client')
      .leftJoin('client.clientManagers', 'clientManagers')
      .leftJoinAndSelect('client.autClientCredentials', 'autClientCredentials')
      .where('fyiNotice.organizationId = :organizationId', {
        organizationId: user?.organization?.id,
      })
      .andWhere('client.status != :status', { status: UserStatus.DELETED })
      .andWhere('autClientCredentials.status != :disStatus', {
        disStatus: IncomeTaxStatus.DISABLE,
      });

    if (ViewAssigned && !ViewAll) {
      fyiNotice.andWhere('clientManagers.id = :userId', { userId });
    }

    if (query) {
      const startOfMonth = moment(`${query}`).startOf('month').format('YYYY-MM-DD');
      const endOfMonth = moment(query).endOf('month').format('YYYY-MM-DD');
      fyiNotice
        .andWhere(`Date(fyiNotice.responseDueDate) BETWEEN '${startOfMonth}' AND '${endOfMonth}'`)
        .andWhere('fyiNotice.remarkSubmittedOn IS NUll');
    }
    const result = await fyaNotice.getMany();
    const result2 = await fyiNotice.getMany();
    const fyaNotices = result.map((notice) => ({ ...notice, type: 'FYA' }));
    const fyiNotices = result2.map((notice) => ({ ...notice, type: 'FYI' }));
    const result1and2 = [...fyaNotices, ...fyiNotices];

    return result1and2;
  }

  async getManualDueDateEvents(userId, query: Date) {
    let user = await User.findOne(userId, { relations: ['organization', 'role'] });
    let ViewAll = user.role.permissions.find(
      (permission) => permission.slug === Permissions.VIEW_ALL_CLIENT_MANAGERS,
    );
    let ViewAssigned = user.role.permissions.find(
      (permission) => permission.slug === Permissions.VIEW_ASSIGNED_CLIENT_MANAGERS,
    );
    let fyaNotice = createQueryBuilder(AutFyaNotice, 'fyaNotice')
      .leftJoinAndSelect('fyaNotice.client', 'client')
      .leftJoin('client.clientManagers', 'clientManagers')
      .leftJoinAndSelect('client.autClientCredentials', 'autClientCredentials')
      .where('fyaNotice.organizationId = :organizationId', {
        organizationId: user?.organization?.id,
      })
      .andWhere('client.status != :status', { status: UserStatus.DELETED })
      .andWhere('autClientCredentials.status != :disStatus', {
        disStatus: IncomeTaxStatus.DISABLE,
      });

    if (ViewAssigned && !ViewAll) {
      fyaNotice.andWhere('clientManagers.id = :userId', { userId });
    }

    if (query) {
      const startOfMonth = moment(`${query}`).startOf('month').format('YYYY-MM-DD');
      const endOfMonth = moment(query).endOf('month').format('YYYY-MM-DD');
      fyaNotice.andWhere(
        `(Date(fyaNotice.manualDueDate) BETWEEN '${startOfMonth}' AND '${endOfMonth}')`,
      );
    }

    const result = await fyaNotice.getMany();
    const fyaNotices = result.map((notice) => ({ ...notice, type: 'FYA' }));
    const result1and2 = [...fyaNotices];
    return result1and2;
  }

  async getIncometaxConfigStatus(userId: any, query: Date) {
    let user = await User.findOne(userId, { relations: ['organization'] });
    const organizationPref: any = await OrganizationPreferences.findOne({
      where: { organization: user?.organization },
    });
    if (organizationPref) {
      const organizationLimit = organizationPref?.automationConfig?.incomeTaxLimit || 300;
      let clientCredentials = createQueryBuilder(AutClientCredentials, 'clientCredentials')
        .leftJoinAndSelect('clientCredentials.client', 'client')
        .where('clientCredentials.organizationId = :id', { id: user?.organization?.id })
        .andWhere('client.status != :status', { status: UserStatus.DELETED })
        .andWhere(
          new Brackets((qb) => {
            qb.where('clientCredentials.status IS NULL').orWhere(
              'clientCredentials.status = :enabledStatus',
              { enabledStatus: IncomeTaxStatus.ENABLE },
            );
          }),
        );
      let result = (await clientCredentials.getCount()) || 0;

      const abc = {
        totalLimit: organizationLimit,
        difference: organizationLimit - result,
        presentClients: result,
      };
      return abc;
    }
  }

  async getFormsUdinAnalytics(userId: number, assesmentYear: any) {
    const user = await User.findOne(userId, { relations: ['organization', 'role'] });

    let ViewAll = user.role.permissions.find(
      (permission) => permission.slug === Permissions.VIEW_ALL_CLIENT_MANAGERS,
    );
    let ViewAssigned = user.role.permissions.find(
      (permission) => permission.slug === Permissions.VIEW_ASSIGNED_CLIENT_MANAGERS,
    );

    const formsData = createQueryBuilder(AutoIncomeTaxForms, 'autForms')
      .leftJoin('autForms.client', 'client')
      .leftJoin('client.autClientCredentials', 'autClientCredentials')
      .leftJoin('client.clientManagers', 'clientManagers')
      .where('autForms.organizationId = :organizationId', {
        organizationId: user?.organization?.id,
      })
      .andWhere('client.status != :status', { status: UserStatus.DELETED })
      .andWhere('autClientCredentials.status != :disStatus', {
        disStatus: IncomeTaxStatus.DISABLE,
      });

    if (ViewAssigned && !ViewAll) {
      formsData.andWhere('clientManagers.id = :userId', { userId });
    }

    if (assesmentYear) {
      formsData.andWhere(
        new Brackets((qb) => {
          qb.where('autForms.refYear = :assY AND autForms.refYearType = :assessmentYearType', {
            assY: assesmentYear,
            assessmentYearType: 'AY',
          }).orWhere('autForms.refYear = :finY AND autForms.refYearType = :financialYearType', {
            finY: parseInt(assesmentYear) - 1,
            financialYearType: 'FY',
          });
        }),
      );
    }

    const result = await formsData.getMany();

    const analytics = {
      totalForms: result.length,
      originalForms: {
        totalOriginalForms: 0,
        udinCompleted: 0,
        udinPending: 0,
        udinNotApplicable: 0,
      },
      revisedFroms: {
        totalRevisedForms: 0,
        udinCompleted: 0,
        udinPending: 0,
        udinNotApplicable: 0,
      },
      notApplicableForms: {
        totalNotApplicableForms: 0,
        udinCompleted: 0,
        udinPending: 0,
        udinNotApplicable: 0,
      },
    };

    result.forEach((form) => {
      if (form?.filingTypeCd === 'Original') {
        analytics.originalForms.totalOriginalForms++;

        if (form?.isUdinApplicable) {
          if (form?.udinNum) {
            analytics.originalForms.udinCompleted++;
          } else {
            analytics.originalForms.udinPending++;
          }
        } else {
          analytics.originalForms.udinNotApplicable++;
        }
      } else if (form?.filingTypeCd === 'Revised') {
        analytics.revisedFroms.totalRevisedForms++;
        if (form?.isUdinApplicable) {
          if (form?.udinNum) {
            analytics.revisedFroms.udinCompleted++;
          } else {
            analytics.revisedFroms.udinPending++;
          }
        } else {
          analytics.revisedFroms.udinNotApplicable++;
        }
      } else {
        analytics.notApplicableForms.totalNotApplicableForms++;
        if (form?.isUdinApplicable) {
          if (form?.udinNum) {
            analytics.notApplicableForms.udinCompleted++;
          } else {
            analytics.notApplicableForms.udinPending++;
          }
        } else {
          analytics.notApplicableForms.udinNotApplicable++;
        }
      }
    });

    return analytics;
  }

  async getExcelFyaEvents(userId, query: Date) {
    let user = await User.findOne(userId, { relations: ['organization'] });
    let fyaNotice = createQueryBuilder(IncTempEproFya, 'incTempEproFya')
      .leftJoinAndSelect('incTempEproFya.client', 'client')
      .leftJoinAndSelect('client.autClientCredentials', 'autClientCredentials')
      .where('incTempEproFya.organizationId = :organizationId', {
        organizationId: user?.organization?.id,
      })
      .andWhere('client.status != :status', { status: UserStatus.DELETED })
      .andWhere('autClientCredentials.status != :disStatus', {
        disStatus: IncomeTaxStatus.DISABLE,
      });
    if (query) {
      const startOfMonth = moment(`${query}`).startOf('month').format('YYYY-MM-DD');
      const endOfMonth = moment(query).endOf('month').format('YYYY-MM-DD');
      // fyaNotice.andWhere(`Date(fyaNotice.issuedOn) BETWEEN '${startOfMonth}' AND '${endOfMonth}'`);
      fyaNotice.andWhere(
        `STR_TO_DATE(incTempEproFya.noticeSentDate, '%d-%m-%Y') BETWEEN '${startOfMonth}' AND '${endOfMonth}'`,
      );
    }

    let fyiNotice = createQueryBuilder(IncTempEproFyi, 'incTempEproFyi')
      .leftJoinAndSelect('incTempEproFyi.client', 'client')
      .leftJoinAndSelect('client.autClientCredentials', 'autClientCredentials')
      .where('incTempEproFyi.organizationId = :organizationId', {
        organizationId: user?.organization?.id,
      })
      .andWhere('client.status != :status', { status: UserStatus.DELETED })
      .andWhere('autClientCredentials.status != :disStatus', {
        disStatus: IncomeTaxStatus.DISABLE,
      });
    if (query) {
      const startOfMonth = moment(`${query}`).startOf('month').format('YYYY-MM-DD');
      const endOfMonth = moment(query).endOf('month').format('YYYY-MM-DD');
      // fyiNotice.andWhere(`Date(fyiNotice.issuedOn) BETWEEN '${startOfMonth}' AND '${endOfMonth}'`);
      fyiNotice.andWhere(
        `STR_TO_DATE(incTempEproFyi.noticeSentDate, '%d-%m-%Y') BETWEEN '${startOfMonth}' AND '${endOfMonth}'`,
      );
    }
    const result = await fyaNotice.getMany();
    const result2 = await fyiNotice.getMany();

    const fyaNotices = result.map((notice) => ({ ...notice, type: 'FYA' }));
    const fyiNotices = result2.map((notice) => ({ ...notice, type: 'FYI' }));
    const result1and2 = [...fyaNotices, ...fyiNotices];
    return result1and2;
  }

  async getExcelResponseDueEvents(userId, query: Date) {
    let user = await User.findOne(userId, { relations: ['organization'] });
    let fyaNotice = createQueryBuilder(IncTempEproFya, 'incTempEproFya')
      .leftJoinAndSelect('incTempEproFya.client', 'client')
      .leftJoinAndSelect('client.autClientCredentials', 'autClientCredentials')
      .where('incTempEproFya.organizationId = :organizationId', {
        organizationId: user?.organization?.id,
      })
      .andWhere('client.status != :status', { status: UserStatus.DELETED })
      .andWhere('autClientCredentials.status != :disStatus', {
        disStatus: IncomeTaxStatus.DISABLE,
      });

    if (query) {
      const startOfMonth = moment(`${query}`).startOf('month').format('YYYY-MM-DD');
      const endOfMonth = moment(query).endOf('month').format('YYYY-MM-DD');
      fyaNotice.andWhere(
        // `Date(fyaNotice.responseDueDate) BETWEEN '${startOfMonth}' AND '${endOfMonth}'`,
        `STR_TO_DATE(incTempEproFya.dateOfCompliance, '%d-%m-%Y') BETWEEN '${startOfMonth}' AND '${endOfMonth}'`,
      );
    }

    let fyiNotice = createQueryBuilder(IncTempEproFyi, 'incTempEproFyi')
      .leftJoinAndSelect('incTempEproFyi.client', 'client')
      .leftJoinAndSelect('client.autClientCredentials', 'autClientCredentials')
      .where('incTempEproFyi.organizationId = :organizationId', {
        organizationId: user?.organization?.id,
      })
      .andWhere('client.status != :status', { status: UserStatus.DELETED })
      .andWhere('autClientCredentials.status != :disStatus', {
        disStatus: IncomeTaxStatus.DISABLE,
      });
    if (query) {
      const startOfMonth = moment(`${query}`).startOf('month').format('YYYY-MM-DD');
      const endOfMonth = moment(query).endOf('month').format('YYYY-MM-DD');
      fyiNotice.andWhere(
        `STR_TO_DATE(incTempEproFyi.dateOfCompliance, '%d-%m-%Y') BETWEEN '${startOfMonth}' AND '${endOfMonth}'`,
      );
      // .andWhere(`Date(fyiNotice.responseDueDate) BETWEEN '${startOfMonth}' AND '${endOfMonth}'`)
      // .andWhere('fyiNotice.remarkSubmittedOn IS NUll');
    }
    const result = await fyaNotice.getMany();
    const result2 = await fyiNotice.getMany();
    const fyaNotices = result.map((notice) => ({ ...notice, type: 'FYA' }));
    const fyiNotices = result2.map((notice) => ({ ...notice, type: 'FYI' }));
    const result1and2 = [...fyaNotices, ...fyiNotices];

    return result1and2;
  }

  async getExcelNoticeDates(
    organizationId: number,
    interval: '1week' | '15days' | '1month' | '1year' | 'today',
    dateColumn: 'notice_sent_date',
    query: any,
    ViewAll: any,
    ViewAssigned: any,
    userId: any,
  ): Promise<number> {
    try {
      if (!organizationId) {
        throw new Error('Organization not found for the user.');
      }

      let intervalQuery = '';
      switch (interval) {
        case 'today':
          intervalQuery = 'INTERVAL 1 DAY';
          break;
        case '1week':
          intervalQuery = 'INTERVAL 1 WEEK';
          break;
        case '15days':
          intervalQuery = 'INTERVAL 15 DAY';
          break;
        case '1month':
          intervalQuery = 'INTERVAL 1 MONTH';
          break;
        case '1year':
          intervalQuery = 'INTERVAL 1 YEAR';
          break;
        default:
          throw new Error('Invalid interval');
      }

      let sql = `
      SELECT COUNT(*) AS count 
      FROM inc_temp_epro_fya 
      LEFT JOIN  client ON inc_temp_epro_fya.client_id = client.id
      LEFT JOIN aut_client_credentials ON aut_client_credentials.client_id = client.id
      WHERE 
        STR_TO_DATE(${dateColumn}, '%d-%m-%Y') BETWEEN DATE_SUB(CURDATE(), ${intervalQuery}) AND CURDATE()
        AND inc_temp_epro_fya.organization_id = ${organizationId}
        AND (client.status != 'DELETED')
        AND (aut_client_credentials.status != 'DISABLE')
      `;

      if (query.assessmentYear && query.assessmentYear !== '') {
        sql += ` AND ay = '${query.assessmentYear}'`;
      }
      if (ViewAssigned && !ViewAll) {
        sql += `
        AND EXISTS (
          SELECT 1
          FROM client_client_managers_user cm
          WHERE cm.client_id = client.id
          AND cm.user_id = ${userId}
        )
      `;
      }

      const result = await getManager().query(sql);
      return parseInt(result[0]?.count, 10);
    } catch (error) {
      console.error(`Error fetching ${dateColumn} dates:`, error);
      throw error;
    }
  }

  async getExcelNoticeFyaResponseDueDates(
    organizationId: number,
    interval: '1week' | '15days' | '1month' | '1year' | 'today',
    dateColumn: 'date_of_compliance',
    query: any,
    ViewAll: any,
    ViewAssigned: any,
    userId: any,
  ): Promise<number> {
    try {
      if (!organizationId) {
        throw new Error('Organization not found for the user.');
      }

      let intervalQuery = '';
      switch (interval) {
         case 'today':
          intervalQuery = 'INTERVAL 1 DAY';
          break;
        case '1week':
          intervalQuery = 'INTERVAL 1 WEEK';
          break;
        case '15days':
          intervalQuery = 'INTERVAL 15 DAY';
          break;
        case '1month':
          intervalQuery = 'INTERVAL 1 MONTH';
          break;
        case '1year':
          intervalQuery = 'INTERVAL 1 YEAR';
          break;
        default:
          throw new Error('Invalid interval');
      }

      let sql = `
      SELECT COUNT(*) AS count 
      FROM inc_temp_epro_fya 
      LEFT JOIN  client ON inc_temp_epro_fya.client_id = client.id
      LEFT JOIN aut_client_credentials ON aut_client_credentials.client_id = client.id
      WHERE 
        STR_TO_DATE(${dateColumn}, '%d-%m-%Y') BETWEEN  CURDATE() AND DATE_ADD(CURDATE(), ${intervalQuery})
        AND inc_temp_epro_fya.organization_id = ${organizationId}
        AND (client.status != 'DELETED')
        AND (aut_client_credentials.status != 'DISABLE')
      `;

      if (query.assessmentYear && query.assessmentYear !== '') {
        sql += ` AND ay = '${query.assessmentYear}'`;
      }

      if (ViewAssigned && !ViewAll) {
        sql += `
        AND EXISTS (
          SELECT 1
          FROM client_client_managers_user cm
          WHERE cm.client_id = client.id
          AND cm.user_id = ${userId}
        )
      `;
      }

      const result = await getManager().query(sql);
      return parseInt(result[0]?.count, 10);
    } catch (error) {
      console.error(`Error fetching ${dateColumn} dates:`, error);
      throw error;
    }
  }

  async getExcelNoticeFyiDates(
    organizationId: number,
    interval: '1week' | '15days' | '1month' | '1year' | 'today',
    dateColumn: 'notice_sent_date',
    query: any,
    ViewAll: any,
    ViewAssigned: any,
    userId: any,
  ): Promise<number> {
    try {
      if (!organizationId) {
        throw new Error('Organization not found for the user.');
      }

      let intervalQuery = '';
      switch (interval) {
         case 'today':
          intervalQuery = 'INTERVAL 1 DAY';
          break;
        case '1week':
          intervalQuery = 'INTERVAL 1 WEEK';
          break;
        case '15days':
          intervalQuery = 'INTERVAL 15 DAY';
          break;
        case '1month':
          intervalQuery = 'INTERVAL 1 MONTH';
          break;
        case '1year':
          intervalQuery = 'INTERVAL 1 YEAR';
          break;
        default:
          throw new Error('Invalid interval');
      }

      let sql = `
      SELECT COUNT(*) AS count 
      FROM inc_temp_epro_fyi 
      LEFT JOIN  client ON inc_temp_epro_fyi.client_id = client.id
      LEFT JOIN aut_client_credentials ON aut_client_credentials.client_id = client.id
      WHERE 
        STR_TO_DATE(${dateColumn}, '%d-%m-%Y') BETWEEN  DATE_SUB(CURDATE(), ${intervalQuery}) AND CURDATE()
        AND inc_temp_epro_fyi.organization_id = ${organizationId}
        AND (client.status != 'DELETED')
        AND (aut_client_credentials.status != 'DISABLE')
      `;

      if (query.assessmentYear && query.assessmentYear !== '') {
        sql += ` AND ay = '${query.assessmentYear}'`;
      }

      if (ViewAssigned && !ViewAll) {
        sql += `
        AND EXISTS (
          SELECT 1
          FROM client_client_managers_user cm
          WHERE cm.client_id = client.id
          AND cm.user_id = ${userId}
        )
      `;
      }

      const result = await getManager().query(sql);
      return parseInt(result[0]?.count, 10);
    } catch (error) {
      console.error(`Error fetching ${dateColumn} dates:`, error);
      throw error;
    }
  }

  async getExcelNoticeFyiResponseDueDates(
    organizationId: number,
    interval: '1week' | '15days' | '1month' | '1year' | 'today',
    dateColumn: 'date_of_compliance',
    query: any,
    ViewAll: any,
    ViewAssigned: any,
    userId: any,
  ): Promise<number> {
    try {
      if (!organizationId) {
        throw new Error('Organization not found for the user.');
      }

      let intervalQuery = '';
      switch (interval) {
         case 'today':
          intervalQuery = 'INTERVAL 1 DAY';
          break;
        case '1week':
          intervalQuery = 'INTERVAL 1 WEEK';
          break;
        case '15days':
          intervalQuery = 'INTERVAL 15 DAY';
          break;
        case '1month':
          intervalQuery = 'INTERVAL 1 MONTH';
          break;
        case '1year':
          intervalQuery = 'INTERVAL 1 YEAR';
          break;
        default:
          throw new Error('Invalid interval');
      }

      let sql = `
      SELECT COUNT(*) AS count 
      FROM inc_temp_epro_fyi 
      LEFT JOIN  client ON inc_temp_epro_fyi.client_id = client.id
      LEFT JOIN aut_client_credentials ON aut_client_credentials.client_id = client.id
      WHERE 
        STR_TO_DATE(${dateColumn}, '%d-%m-%Y') BETWEEN CURDATE() AND DATE_ADD(CURDATE(), ${intervalQuery}) 
        AND inc_temp_epro_fyi.organization_id = ${organizationId}
        AND (client.status != 'DELETED')
        AND (aut_client_credentials.status != 'DISABLE')
      `;

      if (query.assessmentYear && query.assessmentYear !== '') {
        sql += ` AND ay = '${query.assessmentYear}'`;
      }

      if (ViewAssigned && !ViewAll) {
        sql += `
        AND EXISTS (
          SELECT 1
          FROM client_client_managers_user cm
          WHERE cm.client_id = client.id
          AND cm.user_id = ${userId}
        )
      `;
      }

      const result = await getManager().query(sql);
      return parseInt(result[0]?.count, 10);
    } catch (error) {
      console.error(`Error fetching ${dateColumn} dates:`, error);
      throw error;
    }
  }

  async getExcelCombinedNoticesCount(userId: number, query: any) {
    const user = await User.findOne({ where: { id: userId }, relations: ['organization', 'role'] });
    let ViewAll = user.role.permissions.find(
      (permission) => permission.slug === Permissions.VIEW_ALL_CLIENT_MANAGERS,
    );
    let ViewAssigned = user.role.permissions.find(
      (permission) => permission.slug === Permissions.VIEW_ASSIGNED_CLIENT_MANAGERS,
    );
    const organizationId = user?.organization?.id;
    

    if (organizationId) {
      // FYA Notices

       const fyaTodayIssued = await this.getExcelNoticeDates(
        organizationId,
        'today',
        'notice_sent_date',
        query,
        ViewAll,
        ViewAssigned,
        userId,
      );
      const fyaLast1WeekIssued = await this.getExcelNoticeDates(
        organizationId,
        '1week',
        'notice_sent_date',
        query,
        ViewAll,
        ViewAssigned,
        userId,
      );
      const fyaLast15DaysIssued = await this.getExcelNoticeDates(
        organizationId,
        '15days',
        'notice_sent_date',
        query,
        ViewAll,
        ViewAssigned,
        userId,
      );
      const fyaLast1MonthIssued = await this.getExcelNoticeDates(
        organizationId,
        '1month',
        'notice_sent_date',
        query,
        ViewAll,
        ViewAssigned,
        userId,
      );

      const fyaTodayDue = await this.getExcelNoticeFyaResponseDueDates(
        organizationId,
        'today',
        'date_of_compliance',
        query,
        ViewAll,
        ViewAssigned,
        userId,
      );
      const fyaLast1WeekDue = await this.getExcelNoticeFyaResponseDueDates(
        organizationId,
        '1week',
        'date_of_compliance',
        query,
        ViewAll,
        ViewAssigned,
        userId,
      );
      const fyaLast15DaysDue = await this.getExcelNoticeFyaResponseDueDates(
        organizationId,
        '15days',
        'date_of_compliance',
        query,
        ViewAll,
        ViewAssigned,
        userId,
      );
      const fyaLast1MonthDue = await this.getExcelNoticeFyaResponseDueDates(
        organizationId,
        '1month',
        'date_of_compliance',
        query,
        ViewAll,
        ViewAssigned,
        userId,
      );

      // FYI Notices

       const fyiTodayIssued = await this.getExcelNoticeFyiDates(
        organizationId,
        'today',
        'notice_sent_date',
        query,
        ViewAll,
        ViewAssigned,
        userId,
      );
      const fyiLast1WeekIssued = await this.getExcelNoticeFyiDates(
        organizationId,
        '1week',
        'notice_sent_date',
        query,
        ViewAll,
        ViewAssigned,
        userId,
      );
      const fyiLast15DaysIssued = await this.getExcelNoticeFyiDates(
        organizationId,
        '15days',
        'notice_sent_date',
        query,
        ViewAll,
        ViewAssigned,
        userId,
      );
      const fyiLast1MonthIssued = await this.getExcelNoticeFyiDates(
        organizationId,
        '1month',
        'notice_sent_date',
        query,
        ViewAll,
        ViewAssigned,
        userId,
      );

       const fyiTodayDue = await this.getExcelNoticeFyiResponseDueDates(
        organizationId,
        'today',
        'date_of_compliance',
        query,
        ViewAll,
        ViewAssigned,
        userId,
      );
      const fyiLast1WeekDue = await this.getExcelNoticeFyiResponseDueDates(
        organizationId,
        '1week',
        'date_of_compliance',
        query,
        ViewAll,
        ViewAssigned,
        userId,
      );
      const fyiLast15DaysDue = await this.getExcelNoticeFyiResponseDueDates(
        organizationId,
        '15days',
        'date_of_compliance',
        query,
        ViewAll,
        ViewAssigned,
        userId,
      );
      const fyiLast1MonthDue = await this.getExcelNoticeFyiResponseDueDates(
        organizationId,
        '1month',
        'date_of_compliance',
        query,
        ViewAll,
        ViewAssigned,
        userId,
      );

      return {
        issueData: {
          last1WeekIssued: fyaLast1WeekIssued + fyiLast1WeekIssued,
          last15DaysIssued: fyaLast15DaysIssued + fyiLast15DaysIssued,
          last1MonthIssued: fyaLast1MonthIssued + fyiLast1MonthIssued,
          todayIssued: fyaTodayIssued + fyiTodayIssued
        },
        responseDueData: {
          last1WeekDue: fyaLast1WeekDue + fyiLast1WeekDue,
          last15DaysDue: fyaLast15DaysDue + fyiLast15DaysDue,
          last1MonthDue: fyaLast1MonthDue + fyiLast1MonthDue,
          todayDue: fyaTodayDue + fyiTodayDue
        },
      };
    } else {
      return {
        issueData: {
          last1WeekIssued: 0,
          last15DaysIssued: 0,
          last1MonthIssued: 0,
          todayIssued: 0
        },
        responseDueData: {
          last1WeekDue: 0,
          last15DaysDue: 0,
          last1MonthDue: 0,
          todayDue: 0
        },
      };
    }
  }

  async getReturnsFilingStatus(userId: number) {
    const user = await User.findOne(userId, { relations: ['organization'] });
    const organizationId = user?.organization?.id;
    const currentYear = new Date().getFullYear();
    const targetYears = Array.from({ length: 5 }, (_, i) => `${currentYear - i}`);

    const clients = await createQueryBuilder(Client, 'client')
      .leftJoin('client.autClientCredentials', 'credentials')
      .leftJoin('client.autProfileDetails', 'profile')
      .where('credentials.organizationId = :orgId', { orgId: organizationId })
      .andWhere('client.status != :status', { status: UserStatus.DELETED })
      .andWhere('credentials.status != :disStatus', {
        disStatus: IncomeTaxStatus.DISABLE,
      })
      .select(['client.id AS clientId', 'profile.dob AS dob'])
      .getRawMany();

    const clientDobMap = clients.reduce((acc, row) => {
      const dobYear = row.dob ? new Date(row.dob).getFullYear() : null;
      if (dobYear) acc[row.clientId] = dobYear;
      return acc;
    }, {} as Record<number, number>);
    const filedReturns = await createQueryBuilder(AutIncometaxReturns, 'returns')
      .leftJoin('returns.autClientCredentials', 'credentials')
      .leftJoin('returns.client', 'client')
      .where('credentials.organizationId = :orgId', { orgId: organizationId })
      .andWhere('credentials.status != :disStatus', {
        disStatus: IncomeTaxStatus.DISABLE,
      })
      .andWhere('client.status != :status', { status: UserStatus.DELETED })
      .andWhere('returns.filingTypeCd = :filingType', { filingType: 'O' })
      .andWhere('returns.assmentYear IN (:...years)', { years: targetYears })
      .select(['credentials.clientId AS clientId', 'returns.assmentYear AS year'])
      .getRawMany();

    const filedMap = new Set(filedReturns.map((r) => `${r.clientId}_${r.year}`));
    let filedData = [];
    let notFiledData = [];
    const result = targetYears.map((year) => {
      let filed = 0;
      let notFiled = 0;

      for (const [clientIdStr, dobYear] of Object.entries(clientDobMap) as [string, number][]) {
        const clientId = Number(clientIdStr);
        //    const hasReturns = filedReturns.some(r => r.clientId === clientId);
        // if (!hasReturns) continue;

        if (+year >= dobYear) {
          // console.log(`Counting client ${clientIdStr} for year ${year} (DOB: ${dobYear})`);

          const key = `${clientId}_${year}`;
          if (filedMap.has(key)) {
            filed += 1;
            // filedData.push(key)
          } else {
            notFiled += 1;
            // notFiledData.push(key)
          }
        } else {
          // console.log(`Skipping client ${clientIdStr} for year ${year} (DOB: ${dobYear})`);
        }
      }

      return {
        year,
        filed,
        notFiled,
      };
    });
    //  console.log('notFiledData',notFiledData)
    //   console.log('filedData',filedData)
    return result;
  }

  async getNotFiledReturnsList(userId: number, query: any) {
    const { assessmentYear, offset, limit, search } = query;
    const user = await User.findOne(userId, { relations: ['organization'] });
    const organizationId = user?.organization?.id;
    const currentYear = new Date().getFullYear();
    const lastFiveYears = Array.from({ length: 5 }, (_, i) => (currentYear - i).toString());
    const yearsToCheck = assessmentYear ? [assessmentYear] : lastFiveYears;

    const clientsQuery = createQueryBuilder(Client, 'client')
      .leftJoin('client.autClientCredentials', 'credentials')
      .leftJoin('client.autProfileDetails', 'profile')
      .where('credentials.organizationId = :orgId', { orgId: organizationId })
      .andWhere('client.status != :deleted', { deleted: UserStatus.DELETED })
      .andWhere('credentials.status != :disabled', { disabled: IncomeTaxStatus.DISABLE });

    if (search) {
      clientsQuery.andWhere(
        new Brackets((qb) => {
          qb.where('client.displayName LIKE :search', { search: `%${search}%` }).orWhere(
            'profile.pan LIKE :search',
            { search: `%${search}%` },
          );
        }),
      );
    }

    clientsQuery.select([
      'client.id AS clientId',
      'client.displayName AS displayName',
      'client.category AS category',
      'profile.pan AS panNumber',
      'profile.dob AS dob',
    ]);

    const clients = await clientsQuery.getRawMany();

    const filedReturns = await createQueryBuilder(AutIncometaxReturns, 'returns')
      .leftJoin('returns.autClientCredentials', 'credentials')
      .leftJoin('returns.client', 'client')
      .where('credentials.organizationId = :orgId', { orgId: organizationId })
      .andWhere('credentials.status != :disabled', { disabled: IncomeTaxStatus.DISABLE })
      .andWhere('client.status != :deleted', { deleted: UserStatus.DELETED })
      .andWhere('returns.filingTypeCd = :filingType', { filingType: 'O' })
      .andWhere('returns.assmentYear IN (:...years)', { years: yearsToCheck })
      .select(['credentials.clientId AS clientId', 'returns.assmentYear AS year'])
      .getRawMany();

    const filedSet = new Set(filedReturns.map((r) => `${r.clientId}_${r.year}`));
    const yearToCheck = assessmentYear || currentYear.toString();

    const notFiledClients = clients.filter((client) => {
      if (!client.dob) return false;
      const dobYear = new Date(client.dob).getFullYear();
      if (+yearToCheck < dobYear) return false;

      const filedKey = `${client.clientId}_${yearToCheck}`;
      return !filedSet.has(filedKey);
    });

    const totalCount = notFiledClients.length;

    const pagedData = notFiledClients.slice(offset, offset + limit).map((client) => ({
      displayName: client.displayName,
      category: client.category,
      pan: client.panNumber,
      assessmentYear: yearToCheck,
      filingType: 'Original',
      filingStatus: 'Not Filed',
    }));

    return {
      totalCount,
      data: pagedData,
    };
  }

  async exportNotFiledReturnsList(userId: number, query: any) {
    const exportQuery = { ...query, offset: 0, limit: ********* };

    let returnsList = await this.getNotFiledReturnsList(userId, exportQuery);
    if (!returnsList.data.length) throw new BadRequestException('No Data for Export');
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('NotFiledReturns');
    const headers = [
      { header: 'S.No', key: 'serialNo' },
      { header: 'Assessment Year', key: 'assessmentYear' },
      { header: 'Category', key: 'category' },
      { header: 'Client Name', key: 'clientName' },
      { header: 'PAN', key: 'pan' },
      // { header: 'Filing Type', key: 'filingType' },
      { header: 'Filing Status', key: 'filingStatus' },
    ];

    worksheet.columns = headers;
    const columnMaxLengths = Array(headers.length).fill(0);
    let serialCounter = 1; // Initialize a counter
    if (!returnsList.data.length) throw new BadRequestException('No Data for Export');
    returnsList.data.forEach((notice) => {
      const rowData = {
        serialNo: serialCounter++,
        assessmentYear: generateAssessmentYear(notice?.assessmentYear),
        category: notice?.category,
        clientName: notice?.displayName,
        pan: notice?.pan,
        // filingType: notice?.filingType,
        filingStatus: notice?.filingStatus,
      };

      const row = worksheet.addRow(rowData);

      // Apply conditional coloring for the "Type" column
      // const typeCell = row.getCell('proceedingType'); // Get the cell for the "Type" column
      // if (rowData.proceedingType === 'Self') {
      //   typeCell.font = {
      //     color: { argb: '4B0082' },
      //     bold: true, // Bold text
      //   };23

      // } else if (rowData.proceedingType === 'Other') {
      //   typeCell.font = {
      //     color: { argb: 'FF8C00' },
      //     bold: true, // Bold text
      //   };
      // }
      worksheet.columns.forEach((column, colIndex) => {
        const headerLength = column.header?.length || 0;
        const cellLength = rowData[column.key]?.toString().length || 0;
        columnMaxLengths[colIndex] = Math.max(
          columnMaxLengths[colIndex] || 0,
          headerLength,
          cellLength,
        );
      });
    });

    worksheet.columns.forEach((column, colIndex) => {
      column.width = columnMaxLengths[colIndex] + 2;
    });

    const headerRow = worksheet.getRow(1);
    headerRow.font = { bold: true };
    headerRow.eachCell((cell) => {
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '64B5F6' },
      };
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' },
      };
      cell.alignment = { horizontal: 'center', vertical: 'middle' };
    });

    worksheet.columns.forEach((column) => {
      if (column.key === 'clientName') {
        column.width = 50;
        column.alignment = { wrapText: true, horizontal: 'center', vertical: 'middle' };
      } else {
        column.alignment = { horizontal: 'center', vertical: 'middle' };
      }
    });
    worksheet.eachRow((row) => {
      row.eachCell((cell) => {
        cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
      });
    });

    worksheet.views = [{ state: 'frozen', ySplit: 1 }];

    const buffer = await workbook.xlsx.writeBuffer();
    return buffer;
  }
}
