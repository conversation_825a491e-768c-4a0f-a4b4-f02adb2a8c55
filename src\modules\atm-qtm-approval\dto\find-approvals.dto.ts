import {
  IsEnum,
  IsNotEmpty,
  IsNumberString,
  ValidateIf,
} from 'class-validator';

export enum ApprovalType {
  TASK = 'TASK',
  IPRO = 'IPRO',
  SERVICE = 'SERVICE',
  ESTIMATE = 'ESTIMATE',
  INVOICE = 'INVOICE',
}

class FindApprovalsDto {
  @IsNotEmpty()
  @IsEnum(ApprovalType)
  type: ApprovalType;

  @ValidateIf((o) => o.type === ApprovalType.TASK)
  @IsNumberString()
  @IsNotEmpty()
  taskId: number;

  @ValidateIf((o) => o.type === ApprovalType.IPRO)
  @IsNotEmpty()
  iproId: string;

  @ValidateIf((o) => o.type === ApprovalType.ESTIMATE)
  @IsNotEmpty()
  estimateId: string;

  @ValidateIf((o) => o.type === ApprovalType.INVOICE)
  @IsNotEmpty()
  invoiceId: string;
}

export default FindApprovalsDto;
