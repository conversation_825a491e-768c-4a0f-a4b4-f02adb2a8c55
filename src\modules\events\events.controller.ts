import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  ParseIntPipe,
  Post,
  Put,
  Query,
  Req,
  Res,
  UseGuards,
} from '@nestjs/common';
import { JwtAuthGuard } from 'src/modules/users/jwt/jwt-auth.guard';
import { CreateEventDto } from './dto/create-event.dto';
import { EventsService } from './events.service';
import { query } from 'express';
import { Response } from 'express';

@Controller('events')
export class EventsController {
  eventsService: any;
  constructor(private service: EventsService) {}

  @UseGuards(JwtAuthGuard)
  @Get()
  getEvents(@Req() request: any, @Query() query: any) {
    const { userId } = request.user;
    return this.service.getEvents(userId, query);
  }

  @Get('/default')
  getDefaultEvents(@Query() query: any) {
    return this.service.getDefaultEvents(query.startDates);
  }

  @UseGuards(JwtAuthGuard)
  @Post()
  createEvent(@Body() body: CreateEventDto, @Req() request: any) {
    const { userId } = request.user;
    return this.service.create(userId, body);
  }

  @UseGuards(JwtAuthGuard)
  @Put(':id')
  update(@Body() body: CreateEventDto, @Param('id', ParseIntPipe) id: number, @Req() request: any) {
    const { userId } = request.user;
    return this.service.update(id, body, userId);
  }

  @UseGuards(JwtAuthGuard)
  @Delete(':id')
  delete(@Param('id', ParseIntPipe) id: number, @Req() request: any) {
    const { userId } = request.user;
    return this.service.delete(id, userId);
  }

  @UseGuards(JwtAuthGuard)
  @Get('download-compliance-calendar')
  async getCalendarImageBase64(
    @Req() request: any,
    @Res() res: Response,
    @Query('month') month: string,
  ) {
    const { userId } = request.user;
    const base64 = await this.service.generateComplianceCalendar(userId, month);

    res.send({ image: base64 });
  }
}
