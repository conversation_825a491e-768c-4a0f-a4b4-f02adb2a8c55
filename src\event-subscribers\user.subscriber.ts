import {
  Connection,
  EntitySubscriberInterface,
  EventSubscriber,
  InsertEvent,
  UpdateEvent,
  getManager,
} from 'typeorm';
import { User, UserType } from 'src/modules/users/entities/user.entity';
import { getAdminIds, getUserDetails, insertINTONotificationUpdate } from 'src/utils/re-use';
import {
  insertINTOnotification,
  getAdminEmailsBasedOnOrganizationId,
  getAdminIDsBasedOnOrganizationId,
} from 'src/utils/re-use';
import NotificationPreferences from 'src/modules/notification-settings/notifications-preferences.entity';
import { Notification_Actions } from 'src/modules/notification-settings/action';
import { InternalServerErrorException } from '@nestjs/common';
import { sendWhatsAppTemplateMessage, sendWhatsAppTextMessage } from 'src/modules/whatsapp/whatsapp.service';
import { sendnewMail, sendnewMailToBusinessTeam } from 'src/emails/newemails';
import {
  fullMobileNumberWithCountry,
  fullMobileWithCountry,
} from 'src/utils/validations/fullMobileWithCountry';
import ViderWhatsappSessions from 'src/modules/whatsapp/entity/viderWhatsappSessions';

let oldUserStatus: string;
let oldUserName: string;
let oldUserLogIn: any;
@EventSubscriber()
export class UserSubscriber implements EntitySubscriberInterface<User> {
  constructor(private readonly connection: Connection) {
    connection.subscribers.push(this);
  }

  listenTo() {
    return User;
  }

  async beforeUpdate(event: UpdateEvent<User>) {
    oldUserStatus = event.databaseEntity?.status;
    oldUserName = event.databaseEntity?.fullName;
    oldUserLogIn = event.databaseEntity?.lastLogin;
  }

  async afterUpdate(event: UpdateEvent<User>) {
    const userStatus = event.entity?.status;
    const userName = event.entity?.fullName;

    const userId = event.entity?.id
    const oldUserLogInkk = new Date(oldUserLogIn);
    const newLoginString = event.entity?.lastLogin;
    // Convert newLoginString to a Date object
    const newLoginDate = new Date(newLoginString);
    if (oldUserLogInkk.getTime() !== newLoginDate.getTime()) {
      const status = 'user login successfully';
    } else if (oldUserStatus === userStatus) {
      const { id: orgId } = event?.entity?.organization || '';
      if (orgId && event.entity.type === UserType.ORGANIZATION) {
        const adminArry = await getAdminIds(orgId);
        const title = "User Profile Updation"
        const body = `<strong>${userName}</strong> has updated his profile details, check the profile to know full details`;
        const key = 'USER_PROFILE_UPDATION_PUSH';
        insertINTONotificationUpdate(title, body, adminArry, orgId, key, userId);
        //whatsapp

        try {
          if (adminArry !== undefined) {
            const sessionValidation = await ViderWhatsappSessions.findOne({
              where: { userId: adminArry ,status:'ACTIVE'},
            });
            if (sessionValidation) {
              const adminUserDetails = await getUserDetails(adminArry);

              const { full_name: userFullName, mobile_number: userPhoneNumber, id, organization_id } = adminUserDetails;
              const key = 'USER_PROFILE_UPDATION_WHATSAPP';
              const whatsappMessageBody = `
Hi ${userFullName}

 ${oldUserName} profile has been succesfully updated!

We hope this helps!`;
              await sendWhatsAppTextMessage(
                `91${userPhoneNumber}`,
                whatsappMessageBody,
                organization_id,
                title,
                id,
                key,
              );
            }

          }
        } catch (error) {
          console.error('Error sending User WhatsApp notification:', error);
        }
      }
    }
  }

  async beforeInsert(event: InsertEvent<User>) { }

  async afterInsert(event: InsertEvent<User>) {
    const entityManager = getManager();
    //get the username and organization
    const { fullName, email, organization, mobileNumber, id: userId, countryCode } = event.entity;
    if (organization !== undefined) {
      const {
        id,
        legalName,
        email: orgEmail,
        buildingNo,
        floorNumber,
        street,
        city,
        district,
        state,
        pincode,
        primaryContactFullName,
        mobileNumber: orgMobileNumber,
        countryCode: orgCountryCode,
      } = organization;
      if (id !== undefined && event.entity.type === UserType.ORGANIZATION) {
        const userList = await getAdminIDsBasedOnOrganizationId(id);
        const adminMails = await getAdminEmailsBasedOnOrganizationId(id);
        const title = 'User signed up';
        const body = `<strong>${fullName}</strong> have signed up to your organisation.`;
        if (userList !== undefined) {
          insertINTOnotification(title, body, userList, id);
          const data = {
            userName: fullName,
            organizationName: legalName,
            userId: userId,
          };
          const detailsToSendToBusiness = {
            organizationId: id,
            fullName,
            organizationName: legalName,
            userId: userId,
            orgEmail: orgEmail,
            orgMobileNumber: fullMobileWithCountry(orgMobileNumber, orgCountryCode),
            buildingNo,
            floorNumber,
            street,
            city,
            district,
            state,
            pincode,
            primaryContactFullName,
            mobileNumber: fullMobileWithCountry(mobileNumber, countryCode),
            email,
          };
          const mailOptions = {
            data: data,
            email: email,
            filePath: 'user-signed-up',
            subject: "User Signup | Vider's ATOM",
            key: '',
            id: 0,
          };
          const mailOptionsForBusinessTeam = {
            data: detailsToSendToBusiness,
            email: process.env.SOCIAL_EMAIL,
            filePath: 'user-signup-business-team',
            subject: 'New User On-boarded to VIDER',
            key: '',
            id: 0,
          };
          await sendnewMail(mailOptions);
          //Mail To Business Team
          await sendnewMailToBusinessTeam(mailOptionsForBusinessTeam);
          if (adminMails !== undefined) {
            for (let i = 0; i < adminMails.length; i++) {
              const data = {
                userName: fullName,
                organizationName: legalName,
                adminName: adminMails[i]?.full_name,
                userId: userId,
              };
              //const jsonData = JSON.stringify(data);
              const mailOptions = {
                data: data,
                email: adminMails[i]?.email,
                filePath: 'user-signup-admin',
                subject: "User Signup | Vider's ATOM",
                key: '',
                id: 0,
              };
              await sendnewMail(mailOptions);
            }
          }
        }
        //Whatsapp Notification
        const templateName = 'welcome_template_to_users_who_have_added';
        const userPhoneNumber = fullMobileNumberWithCountry(mobileNumber, countryCode);
        const whatsappOptions = {
          to: userPhoneNumber,
          name: templateName,
          header: [
            {
              type: 'text',
              text: fullName,
            },
          ],
          orgId: id,
          title,
          userId,
        };
        await sendWhatsAppTemplateMessage(whatsappOptions);
      }
      try {
        setTimeout(
          async (vev) => {
            const { id: userId } = vev.entity;
            let existingUser = await NotificationPreferences.findOne({ where: { user: userId } });
            if (!existingUser) {
              if (vev.entity && vev.entity.organization) {
                const { id: orgId } = vev.entity.organization;
                const { Mail_Notification, Push_Notification } = Notification_Actions;
                const pushMail = { ...Mail_Notification, ...Push_Notification };
                const stringfyPushMail = JSON.stringify(pushMail);
                let existingUser = await User.findOne({
                  where: {
                    id: userId,
                  },
                });
                let newUser = new NotificationPreferences();
                newUser.organization_id = orgId;
                newUser.user = existingUser;
                newUser.email = null;
                newUser.push = JSON.stringify({});
                await newUser.save();
              }
            }
          },
          1000,
          event,
        );
      } catch (e) {
        console.log(e);
        throw new InternalServerErrorException(e);
      }
    }
  }
}
