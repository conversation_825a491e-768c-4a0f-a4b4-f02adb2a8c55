import { Connection, EntitySubscriberInterface, EventSubscriber, InsertEvent, UpdateEvent } from 'typeorm';
import RecurringProfile from 'src/modules/recurring/entity/recurring-profile.entity';


@EventSubscriber()
export class RecurringSubscriber implements EntitySubscriberInterface<RecurringProfile> {
  constructor(private readonly connection: Connection) {
    connection.subscribers.push(this);
  }

  listenTo() {
    return RecurringProfile;
  }

  async beforeInsert(event: InsertEvent<RecurringProfile>) {
  }

  async afterInsert(event: InsertEvent<RecurringProfile>) {
  }

  async beforeUpdate(event: UpdateEvent<RecurringProfile>){
  }

  async afterUpdate(event: UpdateEvent<RecurringProfile>) {   
  }
}
