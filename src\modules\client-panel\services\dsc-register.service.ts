import { BadRequestException, Injectable, InternalServerErrorException, NotFoundException } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { Event_Actions } from 'src/event-listeners/actions';
import Client from 'src/modules/clients/entity/client.entity';
import { User } from 'src/modules/users/entities/user.entity';
import { Brackets, createQueryBuilder, In } from 'typeorm';
// import DscActivity, { DscActivityTypeEnum } from './entity/dsc-activity.entity';
// import DscRegister, { DscRegisterStatus } from './entity/dsc-register.entity';
// import { CreateDscRegisterDto, UpdateDscRegisterDto } from './dto/create-dsc-register.dto';
// import { DscRegiserQueyType, FindDscRegisterDto } from './dto/find-dsc-register.dto';
// import IssueOrReceiveDto from './dto/issue-receive.dto';
// import { ImportDscRegisterDto } from './dto/import-dsc-registers.dto';
import * as xlsx from 'xlsx';
import * as moment from 'moment';
import { validate } from 'class-validator';
import _ from 'lodash';
// import DscApply, { DscApplicationStatus, DscType } from './entity/dsc-apply.entity';
// import Storage from '../storage/storage.entity';
// import { StorageService } from '../storage/storage.service';
import { getLoginUser } from 'src/utils/re-use';
import { formatDate } from 'src/utils';
// import Activity, { ActivityType } from '../activity/activity.entity';
import mobileWithCountry from 'src/utils/validations/mobileWithCountry';
import countries from 'src/utils/countries';
import { StorageService } from './storage.service';
import { DscRegiserQueyType, FindDscRegisterDto } from 'src/modules/dsc-register/dto/find-dsc-register.dto';
import ClientGroup from 'src/modules/client-group/client-group.entity';
import DscRegister from 'src/modules/dsc-register/entity/dsc-register.entity';
// import ClientGroup from '../client-group/client-group.entity';
// import { DeleteDscClientsDto } from './dto/delete-dsc-clients.dto';
// import { AddDscClientsDto } from './dto/add-dsc-clients.dto';

@Injectable()
export class DscRegisterService {
  constructor(private eventEmitter: EventEmitter2, private storageService: StorageService) { }

  async find(userId: number, query: FindDscRegisterDto) {
    let dscRegisters = createQueryBuilder(DscRegister, 'dscRegister')
      .leftJoinAndSelect('dscRegister.clients', 'client')
      .leftJoinAndSelect('client.clientImage', 'clientStorage')
      .leftJoinAndSelect('dscRegister.clientGroups', 'clientGroup')
      .leftJoinAndSelect('clientGroup.clientGroupImage', 'clientGroupStorage')

      .leftJoin('dscRegister.organization', 'organization');

    const sort = (typeof query?.sort === "string") ? JSON.parse(query.sort) : query?.sort;
    if (sort?.column) {
      const columnMap: Record<string, string> = {
        expiryDate: 'dscRegister.expiryDate',
        daysexpiryDate: 'dscRegister.expiryDate',
      };
      const column = columnMap[sort.column] || sort.column;
      dscRegisters.orderBy(column, sort.direction.toUpperCase());
    } else {
      dscRegisters.orderBy('dscRegister.createdAt', 'ASC');
    };

    if (query.type === DscRegiserQueyType.CLIENT) {
      // Use a subquery to ensure the dscRegister is associated with the clientId
      dscRegisters.andWhere(
        qb => {
          const subQuery = qb.subQuery()
            .select('dsc.id')
            .from(DscRegister, 'dsc')
            .innerJoin('dsc.clients', 'subClient')
            .where('subClient.id = :clientId', { clientId: query.clientId })
            .getQuery();
          return 'dscRegister.id IN ' + subQuery;
        }
      );
    }


    if (query.type === DscRegiserQueyType.CLIENT_GROUP) {
      const user = await User.findOne({ where: { id: userId }, relations: ['organization'] });

      const clientGroup = await createQueryBuilder(ClientGroup, 'clientGroup')
        .leftJoinAndSelect('clientGroup.organization', 'organization')
        .leftJoinAndSelect('clientGroup.clients', 'clients')
        .where('clientGroup.id = :id', { id: query.clientGroupId })
        .andWhere('organization.id = :organizationId', { organizationId: user.organization.id })
        .getOne();

      const clientGroupIDs = clientGroup?.clients?.map((item) => item.id) || [];

      dscRegisters.andWhere(
        new Brackets((qb) => {
          qb.where('clientGroup.id = :clientGroupId', { clientGroupId: query.clientGroupId });

          if (clientGroupIDs.length) {
            qb.orWhere('client.id IN (:...clientGroupIDs)', { clientGroupIDs });
          }
        })
      );
    }

    if (query.type === DscRegiserQueyType.ORGANIZATION) {
      let user = await User.findOne({
        where: { id: userId },
        relations: ['organization'],
      });
      dscRegisters.where('organization.id = :organizationId', {
        organizationId: user.organization.id,
      });
    }

    if (query.search) {
      dscRegisters.andWhere(
        new Brackets((qb) => {
          qb.where('dscRegister.holderName LIKE :search', { search: `%${query.search}%` })
            .orWhere('dscRegister.tokenNumber LIKE :search', { search: `%${query.search}` })
            .orWhere('client.displayName LIKE :search', { search: `%${query.search}%` })
            .orWhere('clientGroup.displayName LIKE :search', { search: `%${query.search}%` });
        })
      );
    }

    dscRegisters.orderBy('dscRegister.createdAt', 'DESC')

    if (query.offset >= 0) {
      dscRegisters.skip(query.offset);
    }

    if (query.limit) {
      dscRegisters.take(query.limit);
    }

    let result = await dscRegisters.getManyAndCount();

    return {
      data: result[0],
      totalCount: result[1],
    };
  }

  async exportClientDscReport(userId, query: FindDscRegisterDto) {

    let dscregisters = await this.find(userId, query);
    let rows = dscregisters.data.map((dscregister) => {
      const expiryDate = new Date(dscregister?.expiryDate);
      const issuedDate = new Date();
      const timeDifference = expiryDate.getTime() - issuedDate.getTime();
      const daysDifference = Math.abs(Math.round(timeDifference / (1000 * 60 * 60 * 24)));


      return {
        
        // 'Client / Client Group': dscregister?.client ? dscregister?.client?.displayName : dscregister?.clientGroup?.displayName,
        'DSC Token #': dscregister?.tokenNumber,
        'DSC Holder': dscregister?.holderName,
        'Token Password': dscregister?.password,
        'Certificate Expiry Date': dscregister?.expiryDate,
        'Token Last Issued Date': dscregister?.issuedDate,
        'Token Last Received Date': dscregister?.receivedDate,
        'PAN': dscregister?.panNumber,
        'Mobile #': dscregister?.mobileNumber,
        'Email ID': dscregister?.email
      };
    });

    if (rows !== undefined && rows.length) {
      const worksheet = xlsx.utils.json_to_sheet(rows);
      const workbook = xlsx.utils.book_new();
      xlsx.utils.book_append_sheet(workbook, worksheet, 'DSC Register');
      let file = xlsx.write(workbook, { type: 'buffer' });
      return file;
    } else {
      throw new BadRequestException('No Data for Export');
    }

  }

  async getOne(id: number, userId: number, query: any) {
    let user = await User.findOne({ where: { id: userId }, relations: ['organization'] });

    const dscRegisterQuery = await createQueryBuilder(DscRegister, 'dscRegister')
      .leftJoinAndSelect('dscRegister.dscActivity', 'dscActivity')
      .leftJoinAndSelect('dscRegister.clients', 'clients')
      .leftJoinAndSelect('dscRegister.clientGroups', 'clientGroups')
      .leftJoinAndSelect('dscRegister.organization', 'organization')
      .where('dscRegister.id = :id', { id })
      .andWhere('organization.id =:orgId', { orgId: user.organization.id });

    if (query.clientId) {
      dscRegisterQuery.andWhere('client.id =:clientId', { clientId: query.clientId })
    }

    let dscRegister = dscRegisterQuery.getOne();

    return dscRegister;
  }

  async findClients(userId: number) {
    let user = await User.findOne({
      where: { id: userId },
      relations: ['organization'],
    });
    let dscRegisters = await Client.find({
      where: { organization: { id: user.organization.id }, status: "ACTIVE" },
      relations: ['contactPersons'],
    });
    return dscRegisters;
  }
}
