import { Organization } from 'src/modules/organization/entities/organization.entity';
import Storage from 'src/modules/storage/storage.entity';
import {
  BaseEntity,
  Column,
  CreateDateColumn,
  Entity,
  JoinColumn,
  ManyToOne,
  OneToMany,
  OneToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';

export enum DscApplicationStatus {
  APPLICATION_SUBMITTED = 'APPLICATION_SUBMITTED',
  PENDING_AT_EMUDHRA = 'pending_at_emudhra',
  DSC_ISSUED = 'dsc_issued',
  NONE = 'none',
}

export enum DscType {
  Individual_DSC_Using_aadhar = 'Individual DSC - Using aadhar',
  Organization_DSC_Gst_holder = 'Organization DSC- Gst holder',
  Apply_DSC_Individual = 'Apply DSC - Individual',
  Apply_DSC_Organization= 'Apply DSC - Organization'
}
@Entity()
class DscApply extends BaseEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  applicantName: string;

  @Column()
  mobileNumber: string;

  @Column({ nullable: true })
  email: string;

  @Column({ type: 'enum', enum: DscApplicationStatus, default: DscApplicationStatus.NONE })
  applicationStatus: DscApplicationStatus;

  
  @Column({ type: 'enum', enum: DscType})
  selectedType: DscType;

  @Column()
  certificateType: string;

  @Column()
  certificateValidity: string;

//   @Column()
//   panId: number;

//   @Column()
//   aadhar: number;

@OneToOne(() => Storage, (storage) => storage.dscAadhar, { cascade: true })
  @JoinColumn()
  dscAadhar: Storage;

@OneToOne(() => Storage, (storage) => storage.dscPan, { cascade: true })
  @JoinColumn()
  dscPan: Storage;

@OneToOne(() => Storage, (storage) => storage.dscPhoto, { cascade: true })
  @JoinColumn()
  dscPhoto: Storage;


//   @Column()
//   photoId: number;

  @ManyToOne(() => Organization, (organization) => organization.dscapply)
  organization: Organization;

  @CreateDateColumn()
  createdAt: string;

  @UpdateDateColumn()
  updatedAt: string;
}

export default DscApply;
