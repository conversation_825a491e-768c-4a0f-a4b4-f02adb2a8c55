import { MigrationInterface, QueryRunner } from 'typeorm';

export class AlterApprovalTable1658948334282 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
        ALTER TABLE approval
        ADD COLUMN invoice_id int null,
        ADD FOREIGN KEY (invoice_id) REFERENCES invoice(id) ON DELETE CASCADE
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}
