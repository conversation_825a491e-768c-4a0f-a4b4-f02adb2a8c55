import { Optional } from '@nestjs/common';
import { Organization } from 'src/modules/organization/entities/organization.entity';
import {
  BaseEntity,
  Column,
  CreateDateColumn,
  Entity,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';

@Entity({ name: 'organization_preferences' })
class OrganizationPreferences extends BaseEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @ManyToOne(() => Organization, (organization) => organization, { onDelete: 'CASCADE' })
  organization: Organization;

  @UpdateDateColumn({ type: 'timestamp' })
  lastUpdated: Date;

  @Column('json')
  invoicePreferences: string;

  @Column('json')
  holidayPreferences: object;

  @Column('json')
  notificationConfig: object;

  @Column('json')
  clientPreferences: object;

  @Column('json')
  quantumConfig: object;

  @Column('json')
  atomClientPrefix: object;

  @Column('json')
  taskPreferences: object;

  @Column('json')
  approvals: object;

  @Column('json')
  metaConfig: object;

  @Column('json')
  automationConfig: object;

  @Column()
  pageLimit: string;

   @Optional()
    @Column('json')
    email: string;
  
    @Optional()
    @Column('json')
    push: string;
  
    @Optional()
    @Column('json')
    whatsapp: string;
}

export default OrganizationPreferences;
