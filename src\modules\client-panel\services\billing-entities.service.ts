import { BadRequestException, Injectable } from '@nestjs/common';
import { User } from 'src/modules/users/entities/user.entity';
import Storage, { StorageSystem } from 'src/modules/storage/storage.entity';
import { createQueryBuilder } from 'typeorm';
import { CreateBillingEntityDto } from 'src/modules/organization/dto/create-billing-entity.dto';
import { FindBillingEntityDto } from 'src/modules/organization/dto/find-billing-entities.dto';
import { BankAccount } from 'src/modules/organization/entities/bank-account.entity';
import { BillingEntity } from 'src/modules/organization/entities/billing-entity.entity';
import { defaultTerms } from 'src/modules/organization/terms';
import { StorageService } from './storage.service';
import { OneDriveStorageService } from './onedrive-storage.service';
import Client from 'src/modules/clients/entity/client.entity';

@Injectable()
export class BillingEntitiesService {
  constructor(
    private storageService: StorageService,
    private oneDriveService: OneDriveStorageService,
  ) { }
  async create(userId: number, body: CreateBillingEntityDto) {
    let user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
    let billingEntity = new BillingEntity();
    billingEntity.legalName = body.legalName;
    billingEntity.tradeName = body.tradeName;
    billingEntity.category = body.category;
    billingEntity.panNumber = body.panNumber;
    billingEntity.gstNumber = body.gstNumber;
    billingEntity.gstVerified = body.gstVerified;
    billingEntity.panVerified = body.panVerified;
    billingEntity.gstStatus = body.gstStatus;
    billingEntity.showDiscount = body.showDiscount === 'true';
    billingEntity.registrationDate = body.registrationDate;
    billingEntity.buildingNumber = body.buildingNumber;
    billingEntity.floorNumber = body.floorNumber;
    billingEntity.buildingName = body.buildingName;
    billingEntity.street = body.street;
    billingEntity.location = body.location;
    billingEntity.city = body.city;
    billingEntity.district = body.district;
    billingEntity.state = body.state;
    billingEntity.pincode = body.pincode;
    billingEntity.organization = user.organization;
    // billingEntity.terms=[...defaultTerms];
    billingEntity.termsCopy = [...defaultTerms];
    // billingEntity.receiptTerms = '';
    billingEntity.receiptTermsCopy = [];
    billingEntity.proformaTerms = [...defaultTerms];
    billingEntity.hasGst = body.hasGst;
    let existingBillingEntites = await BillingEntity.find({
      where: { organization: user.organization.id },
    });
    if (!existingBillingEntites.length) {
      billingEntity.default = true;
    }
    await billingEntity.save();
    return billingEntity;
  }

  async get(userId: number, query: FindBillingEntityDto) {
    let user = await Client.findOne({
      where: { id: query.clientId },
      relations: ['organization', 'organization.udinUsers'],
    });
    let billingEntities = createQueryBuilder(BillingEntity, 'be')
      .leftJoin('be.organization', 'organization')
      .where('organization.id=:organization', { organization: user.organization.id });
    if (query?.gst == 'true') {

      billingEntities.andWhere('be.hasGst=true');
    }
    const data = billingEntities.getMany();

    return data;
  }

  async getActive(userId: number) {
    let user = await User.findOne({
      where: { id: userId },
      relations: ['organization'],
    });

    let billingEntities = await BillingEntity.find({
      where: { organization: { id: user.organization.id }, isActive: true },
      relations: ['signatureStorage', 'logStorage'],
    });
    return billingEntities;
  }

  async getOne(id: number, userId: number) {
    let user = await User.findOne({
      where: { id: userId },
      relations: ['organization'],
    });

    let billingEntity = await BillingEntity.findOne({
      where: { id, organization: { id: user?.organization?.id } },
      relations: ['signatureStorage', 'logStorage'],
    });
    return billingEntity;
  }

  async update(id: number, userId: number, body: any) {
    const user = await User.findOne({ where: { id: userId } });

    let billingEntity = await BillingEntity.findOne({
      where: { id },
      relations: ['signatureStorage', 'logStorage'],
    });
    const banckAccounts = await BankAccount.find({ where: { billingEntity: id } });
    if (!banckAccounts.length) {
      throw new BadRequestException('Atleast One Bank Account Required');
    }

    let logStorage: Storage;
    if (body?.logStorage) {
      if (billingEntity?.logStorage?.id) {
        if (body?.logStorage.name !== billingEntity?.logStorage?.name) {
          if (billingEntity?.logStorage.storageSystem === StorageSystem.AMAZON) {
            this.storageService.deleteAwsFile(billingEntity?.logStorage?.file);
          } else if (billingEntity?.logStorage.storageSystem === StorageSystem.MICROSOFT) {
            this.oneDriveService.deleteOneDriveFile(userId, billingEntity?.logStorage?.fileId);
          }
        }

        logStorage = await Storage.findOne({ where: { id: billingEntity?.logStorage?.id } });
        logStorage.fileType = body?.logStorage.fileType;
        logStorage.fileSize = body?.logStorage.fileSize;
        logStorage.name = body?.logStorage.name;
        logStorage.file = body?.logStorage.upload;
        logStorage.show = body?.logStorage.show;
        logStorage.storageSystem = body?.logStorage?.storageSystem;
        logStorage.webUrl = body?.logStorage?.webUrl;
        logStorage.downloadUrl = body?.logStorage?.downloadUrl;
        logStorage.fileId = body?.logStorage?.fileId;
        logStorage.authId = user.organization.id;
        // billingEntity.logStorage = logStorage;
      } else {
        logStorage = await this.storageService.addAttachements(userId, body?.logStorage);
        // billingEntity.logStorage = storage;
      }
    } else {
      if (billingEntity?.logStorage?.id) {
        const existingAStorage = await Storage.findOne({
          where: { id: billingEntity?.logStorage?.id },
        });
        const removeAStorage = await existingAStorage.remove();
        if (removeAStorage) {
          if (removeAStorage.storageSystem === StorageSystem.MICROSOFT) {
            this.oneDriveService.deleteOneDriveFile(userId, removeAStorage.fileId);
          } else if (removeAStorage.storageSystem === StorageSystem.AMAZON) {
            this.storageService.deleteAwsFile(removeAStorage.file);
          }
        }
        billingEntity.logStorage = null;
      }
    }

    let signatureStorage: Storage;
    if (body?.signatureStorage) {
      if (billingEntity?.signatureStorage?.id) {
        if (body?.signatureStorage.name !== billingEntity?.signatureStorage?.name) {
          if (billingEntity?.signatureStorage.storageSystem === StorageSystem.AMAZON) {
            this.storageService.deleteAwsFile(billingEntity?.signatureStorage?.file);
          } else if (billingEntity?.signatureStorage.storageSystem === StorageSystem.MICROSOFT) {
            this.oneDriveService.deleteOneDriveFile(
              userId,
              billingEntity?.signatureStorage?.fileId,
            );
          }
        }

        signatureStorage = await Storage.findOne({
          where: { id: billingEntity?.signatureStorage?.id },
        });
        signatureStorage.fileType = body?.signatureStorage.fileType;
        signatureStorage.fileSize = body?.signatureStorage.fileSize;
        signatureStorage.name = body?.signatureStorage.name;
        signatureStorage.file = body?.signatureStorage.upload;
        signatureStorage.show = body?.signatureStorage.show;
        signatureStorage.storageSystem = body?.signatureStorage.storageSystem;
        signatureStorage.webUrl = body?.signatureStorage?.webUrl;
        signatureStorage.downloadUrl = body?.signatureStorage?.downloadUrl;
        signatureStorage.authId = user.organization.id;
        signatureStorage.fileId = body?.signatureStorage?.fileId;
        // billingEntity.signatureStorage = signatureStorage;
      } else {
        signatureStorage = await this.storageService.addAttachements(
          userId,
          body?.signatureStorage,
        );
        // billingEntity.signatureStorage = storage;
      }
    } else {
      if (billingEntity?.signatureStorage?.id) {
        const existingAStorage = await Storage.findOne({
          where: { id: billingEntity?.signatureStorage?.id },
        });
        const removeAStorage = await existingAStorage.remove();
        if (removeAStorage) {
          if (removeAStorage.storageSystem === StorageSystem.MICROSOFT) {
            this.oneDriveService.deleteOneDriveFile(userId, removeAStorage.fileId);
          } else if (removeAStorage.storageSystem === StorageSystem.AMAZON) {
            this.storageService.deleteAwsFile(removeAStorage.file);
          }
        }
        billingEntity.signatureStorage = null;
      }
    }

    billingEntity.legalName =
      body.legalName !== null && body.legalName !== undefined
        ? body.legalName.trim()
        : body.legalName;
    billingEntity.tradeName =
      body.tradeName !== null && body.tradeName !== undefined
        ? body.tradeName.trim()
        : body.tradeName;
    billingEntity.constitutionOfBusiness =
      body.constitutionOfBusiness !== undefined && body.constitutionOfBusiness !== null
        ? body.constitutionOfBusiness.trim()
        : body.constitutionOfBusiness;
    billingEntity.locationOfSupply = body.locationOfSupply;
    billingEntity.firstName = body.firstName;
    billingEntity.lastName = body.lastName;
    billingEntity.middleName = body.middleName;
    billingEntity.mobileNumber = body.mobileNumber;
    billingEntity.alternateMobileNumber = body.alternateMobileNumber;
    billingEntity.gstVerified = body.gstVerified;
    billingEntity.gstNumber = body.gstNumber;
    billingEntity.gstAttachment = body.gstAttachment;
    billingEntity.logo = body.logo;
    billingEntity.signature = body.signature;
    // billingEntity.terms = body.terms;
    billingEntity.termsCopy = body.termsCopy;
    billingEntity.tag = body.tag;
    // billingEntity.receiptTerms = (body.receiptTerms);
    billingEntity.receiptTermsCopy = body.receiptTermsCopy;
    billingEntity.proformaTerms = body.proformaTerms;
    billingEntity.panVerified = body.panVerified;
    billingEntity.panNumber = body.panNumber;
    billingEntity.panAttachment = body.panAttachment;
    billingEntity.buildingNumber = body.buildingNumber;
    billingEntity.floorNumber = body.floorNumber;
    billingEntity.district = body.district;
    billingEntity.location = body.location;
    billingEntity.buildingName =
      body.buildingName !== undefined && body.buildingName !== null
        ? body.buildingName.trim()
        : body.buildingName;
    billingEntity.street =
      body.street !== undefined && body.street !== null ? body.street.trim() : body.street;
    billingEntity.state = body.state;
    billingEntity.city =
      body.city !== null && body.city !== undefined ? body.city.trim() : body.city;
    billingEntity.pincode = body.pincode;
    billingEntity.website =
      body.website !== null && body.website !== undefined ? body.website.trim() : body.website;
    billingEntity.primaryContactFullName =
      body.primaryContactFullName !== null && body.primaryContactFullName !== undefined
        ? body.primaryContactFullName.trim()
        : body.primaryContactFullName;
    billingEntity.primaryContactMobileNumber = body.primaryContactMobileNumber;
    billingEntity.primaryContactEmail = body.primaryContactEmail;
    billingEntity.primaryContactDesignation = body.primaryContactDesignation;
    billingEntity.registrationNumber =
      body.registrationNumber !== null && body.registrationNumber !== undefined
        ? body.registrationNumber.trim()
        : body.registrationNumber;
    billingEntity.registrationDate = body.registrationDate;
    billingEntity.category = body.category;
    billingEntity.creditPeriod = body.creditPeriod;
    billingEntity.proformaCreditPeriod = body.proformaCreditPeriod;
    billingEntity.autoGenerate = body.autoGenerate;
    billingEntity.showClientData = body.showClientData;
    billingEntity.showClientDataProforma = body.showClientDataProforma;

    billingEntity.showDiscount = body.showDiscount;
    billingEntity.receiptAutoGenerate = body.receiptAutoGenerate;
    billingEntity.proformaAutoGenerate = body.proformaAutoGenerate;
    billingEntity.proformaLocationOfSupply = body.proformaLocationOfSupply;
    billingEntity.email = body.email;
    billingEntity.locationOfSupply = body.locationOfSupply;
    billingEntity.countryCode = body?.countryCode;
    billingEntity.alternateCountryCode = body?.alternateCountryCode;
    billingEntity.primaryContactCountryCode = body?.primaryContactCountryCode;
    if (billingEntity.autoGenerate) {
      billingEntity.prefix = body.prefix;
      billingEntity.prefixNumber = body.prefixNumber;
    } else {
      billingEntity.prefix = null;
      billingEntity.prefixNumber = null;
    }
    if (billingEntity.receiptAutoGenerate) {
      billingEntity.receiptPrefix = body.receiptPrefix;
      billingEntity.receiptPrefixNumber = body.receiptPrefixNumber;
    } else {
      billingEntity.receiptPrefix = null;
      billingEntity.receiptPrefixNumber = null;
    }
    if (billingEntity.proformaAutoGenerate) {
      billingEntity.proformaPrefix = body.proformaPrefix;
      billingEntity.proformaPrefixNumber = body.proformaPrefixNumber;
    } else {
      billingEntity.proformaPrefix = null;
      billingEntity.proformaPrefixNumber = null;
    }

    const be = await billingEntity.save();
    if (logStorage) {
      logStorage.logStorage = be;
      await logStorage.save();
    }
    if (signatureStorage) {
      signatureStorage.signatureStorage = be;
      await signatureStorage.save();
    }
    return billingEntity;
  }

  async default(id: number, userId: number) {
    let user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
    let defaultBillingEntity = await BillingEntity.findOne({
      where: { default: true, organization: user.organization.id },
    });
    defaultBillingEntity.default = false;
    let newBillingEntity = await BillingEntity.findOne({ where: { id } });
    if (!newBillingEntity.isActive) {
      throw new BadRequestException('Deactivated Billing Entity Should not be Default');
    }
    newBillingEntity.default = true;
    await defaultBillingEntity.save();
    await newBillingEntity.save();
  }

  async changeStatus(id: number, userId: number, body) {
    let billingEntity = await BillingEntity.findOne({ where: { id } });
    if (billingEntity.default) {
      throw new BadRequestException('Default Billing Entity Should not be Deactivated');
    }
    billingEntity.isActive = body.status;
    await billingEntity.save();
    return billingEntity;
  }

  async delete(id: number) {
    let billingEntity = await BillingEntity.findOne({ where: { id } });
    await billingEntity.remove();
    return billingEntity;
  }
}
