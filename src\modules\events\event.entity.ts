import Client from 'src/modules/clients/entity/client.entity';
import Task from 'src/modules/tasks/entity/task.entity';
import { Organization } from 'src/modules/organization/entities/organization.entity';
import { User } from 'src/modules/users/entities/user.entity';
import {
  BaseEntity,
  Column,
  CreateDateColumn,
  Entity,
  JoinTable,
  ManyToMany,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { EventTypeEnum } from './dto/create-event.dto';
import ClientGroup from '../client-group/client-group.entity';
import { EventType, Reminders } from './types';
import Storage from "../storage/storage.entity";

@Entity()
class Event extends BaseEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ default: false })
  defaultOne: boolean;

  @Column()
  title: string;

  @Column({ type: 'enum', enum: EventTypeEnum })
  type: EventTypeEnum;

  @Column({ type: 'date' })
  date: string;

  @Column({ nullable: true })
  location: string;

  @Column({ type: 'datetime', nullable: true })
  startTime: string;

  @Column({ type: 'datetime', nullable: true })
  endTime: string;

  @Column({ type: 'enum', enum: Reminders, nullable: true })
  reminder: Reminders;

  @Column({ type: 'text', nullable: true })
  notes: string;

  @Column({ default: false })
  isSentWhatsapp: boolean;

  @Column({type:'enum',enum:EventType,default:null})
  eventType:EventType;

  @CreateDateColumn()
  createdAt: string;

  @UpdateDateColumn()
  updatedAt: string;

  @ManyToOne(() => Client, (client) => client.events)
  client: Client;

  @ManyToOne(() => ClientGroup, (clientGroup) => clientGroup.events)
  clientGroup: ClientGroup;

  @ManyToOne(() => Task, (task) => task.events)
  task: Task;

  @ManyToOne(() => User, (user) => user.events)
  user: User;

  @ManyToOne(() => Organization, (organization) => organization.events)
  organization: Organization;

  @OneToMany(() => Storage, (storage) => storage.organization)
  storage: Storage[];

  @ManyToMany(() => User)
  @JoinTable()
  members: User[];

  @Column({ default: false })
  isSentWhatsappClient: boolean;

  @Column({ type: 'boolean', default: false })
whatsappEnabled: boolean;
}

export default Event;
