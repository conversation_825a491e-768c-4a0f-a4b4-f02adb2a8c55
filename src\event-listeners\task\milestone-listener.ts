import { Injectable } from '@nestjs/common';
import { OnEvent } from '@nestjs/event-emitter';
import { INotification, sendNotification } from 'src/notifications/notify';
import { User } from 'src/modules/users/entities/user.entity';
import { createQuery<PERSON>uilder } from 'typeorm';
import Task from '../../modules/tasks/entity/task.entity';
import { Event_Actions } from '../actions';

type TaskNotificationType =
  | 'milestone_created'
  | 'milestone_updated'
  | 'milestone_deleted';

interface Event {
  actor?: string;
  userId: number;
  clientId: number;
  task: Task;
}

@Injectable()
export class MilestoneListener {
  @OnEvent(Event_Actions.MILESTONE_CREATED, { async: true })
  async onMilestoneCreated(event: Event) {
    const { task } = event;
    try {
      await this.notifyTaskChanges(task.id, 'milestone_created');
    } catch (err) {
      console.log(err);
    }
  }

  @OnEvent(Event_Actions.MILESTONE_UPDATED, { async: true })
  async onMilestoneUpdated(event: Event) {
    const { task } = event;
    try {
      await this.notifyTaskChanges(task.id, 'milestone_updated');
    } catch (err) {
      console.log(err);
    }
  }

  @OnEvent(Event_Actions.MILESTONE_DELETED, { async: true })
  async onMilestoneDeleted(event: Event) {
    const { task } = event;
    try {
      await this.notifyTaskChanges(task.id, 'milestone_deleted');
    } catch (err) {
      console.log(err);
    }
  }

  async notifyTaskChanges(taskId: number, type: TaskNotificationType) {
    let { taskData, orgAdmin, clientManager, memberIds } =
      await this.getTaskData(taskId);

    let notification: INotification = getNotifiationContent(type);

    function getNotifiationContent(type: TaskNotificationType) {
      switch (type) {
        case 'milestone_created':
          return {
            title: `Milestone created`,
            body: `Milestone has been created for ${taskData.name} by ${taskData.user.fullName}`,
          };
        case 'milestone_updated':
          return {
            title: `Milestone updated`,
            body: `Milestone has been updated for ${taskData.name} by ${taskData.user.fullName}`,
          };
        case 'milestone_deleted':
          return {
            title: `Milestone deleted`,
            body: `Milestone has been deleted for ${taskData.name} by ${taskData.user.fullName}`,
          };

        default:
          return {
            title: '',
            body: '',
          };
      }
    }

    let userIds = [...memberIds];

    if (orgAdmin) {
      userIds.push(orgAdmin.id);
    }

    if (clientManager) {
      userIds.push(clientManager.id);
    }

    await sendNotification(userIds, notification);
  }

  async getTaskData(taskId: number) {
    let taskData = await createQueryBuilder(Task, 'task')
      .leftJoinAndSelect('task.parentTask', 'parentTask')
      .leftJoinAndSelect('task.organization', 'organization')
      .leftJoinAndSelect('task.client', 'client')
      .leftJoinAndSelect('task.clientGroup', 'clientGroup')
      .leftJoinAndSelect('client.clientManager', 'clientManager')
      .leftJoinAndSelect('task.user', 'user')
      .leftJoinAndSelect('task.members', 'members')
      .leftJoinAndSelect('organization.users', 'users')
      .leftJoinAndSelect('users.role', 'role')
      .where('task.id = :taskId', { taskId })
      .getOne();

    let memberIds = taskData.members.map((member: User) => member.id);

    let orgAdmin = taskData.organization.users.find(
      (user: User) => user.role.defaultRole,
    );

    let clientManager = taskData.client.clientManager;

    return {
      taskData,
      memberIds,
      orgAdmin,
      clientManager,
    };
  }
}
