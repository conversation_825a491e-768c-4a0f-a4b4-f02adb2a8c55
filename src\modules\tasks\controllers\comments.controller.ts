import {
  Body,
  Get,
  Param,
  ParseIntPipe,
  Post,
  Query,
  Request,
  UseGuards,
} from '@nestjs/common';
import { JwtAuthGuard } from 'src/modules/users/jwt/jwt-auth.guard';
import AddCommentDto from '../dto/add-comment.dto';
import { TasksController } from './task.controller';

export class CommentsController extends TasksController {
  @UseGuards(JwtAuthGuard)
  @Post('/:taskId/comments')
  addComment(
    @Body() body: AddCommentDto,
    @Request() req,
    @Param('taskId', ParseIntPipe) taskId: number,
  ) {
    const { userId } = req.user;
    return this.commentsService.addComment(userId, taskId, body);
  }

  @Get('/comments')
  getComments(@Query('taskId', ParseIntPipe) taskId: number) {
    return this.commentsService.findComments(taskId);
  }
}
