import {
  BadRequestException,
  Body,
  Controller,
  Get,
  Post,
  Request,
  UseGuards,
} from '@nestjs/common';
import { User } from 'src/modules/users/entities/user.entity';
import { JwtAuthGuard } from 'src/modules/users/jwt/jwt-auth.guard';
import { createQueryBuilder } from 'typeorm';
import { Organization } from '../organization/entities/organization.entity';
import { Permission } from './entities/permission.entity';
import { Role } from './entities/role.entity';

@Controller('permissions')
export class PermissionsController {
  @Get()
  async getAllPermissions() {
    let data = await Permission.find();
    return data;
  }

  @UseGuards(JwtAuthGuard)
  @Get('/mine')
  async getMyPermissions(@Request() req) {
    const { userId } = req.user;
    let data = await createQueryBuilder(User, 'user')
      .leftJoinAndSelect('user.role', 'role')
      .leftJoinAndSelect('role.permissions', 'permissions')
      .where('user.id = :id', { id: userId })
      .getOne();
    return data.role;
  }

  @Get('/tree')
  async getPermissionsTree() {
    let data = await Permission.find();
    let forest = this.makeForest(null, data);
    return forest;
  }

  @Post()
  async create(@Body() body: { name: string; label: string; parentLabel: string }) {
    let permission = new Permission();
    permission.name = body.name;
    permission.label = body.label;
    permission.parentLabel = body.parentLabel;
    return permission.save();
  }

  private makeForest(id: number, list: any) {
    return list
      .filter(({ parentId }) => parentId == id)
      .map((item: any) => {
        let children = this.makeForest(item.id, list);
        if (children.length) item.children = children;
        return item;
      });
  }

  @Post('/update-default-roles')
  async tempUpdate(@Body() body: { orgId: number }) {
    if (!body.orgId) {
      throw new BadRequestException('orgId is required');
    }

    let roles = await Role.find({ where: { defaultOne: true }, relations: ['permissions'] });

    let organization = await Organization.findOne({
      where: { id: body.orgId },
      relations: ['roles'],
    });

    let defaultRoles = roles.map((role) => {
      let newRole = new Role();
      newRole.name = role.name;
      newRole.description = role.description;
      newRole.permissions = role.permissions;
      return newRole;
    });

    organization.roles = [...organization.roles, ...defaultRoles];
    return await organization.save();
  }
}