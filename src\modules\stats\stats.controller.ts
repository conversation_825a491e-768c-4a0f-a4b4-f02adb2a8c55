import { Body, Controller, Get, Param, Post, Query, Req, Res, UseGuards } from '@nestjs/common';
import { IsDateString, IsNotEmpty, IsString } from 'class-validator';
import { JwtAuthGuard } from '../users/jwt/jwt-auth.guard';
import { StatsService } from './stats.service';
import { query } from 'express';
import { FindBillingAmounts } from './dto/find-biling-amounts.dto';
import { FindOutStandingClients } from './dto/find-outStanding-clients.dto';
import { Response } from "express";

export class IQueryWeeklyLogHoursDto {
  @IsNotEmpty()
  @IsDateString()
  startDate: string;

  @IsNotEmpty()
  @IsDateString()
  endDate: string;

  @IsNotEmpty()
  @IsString()
  dashboardType: string;
}

@UseGuards(JwtAuthGuard)
@Controller('stats')
export class StatsController {
  constructor(private service: StatsService) { }

  @Get('/task-analytics')
  async taskAnalytics(@Req() request: any, @Query() query: any) {
    let { userId } = request.user;
    return this.service.getTaskAnalytics(userId, query);
  }

  @Get('/tasks-due-this-week')
  async tasksDueThisWeek(@Req() request: any, @Query() query: any) {
    let { userId } = request.user;
    return this.service.getTasksDueThisWeek(userId, query);
  }

  @Get('/tasks-by-category')
  async getTasksByCategory(@Req() request: any, @Query() query: any) {
    let { userId } = request.user;
    return this.service.getTasksByCategory(userId, query);
  }

  @Post('/tasks-by-category-export')
  async getTasksByCategoryExport(@Req() request: any, @Body() body: any) {
    const query = body;
    let { userId } = request.user;
    return this.service.getTasksByCategoryExport(userId, query);
  }

  @Get('/tasks-by-client-category')
  async getTasksByClientCategory(@Req() request: any, @Query() query: any) {
    let { userId } = request.user;
    return this.service.getTasksByClientCategory(userId, query);
  }

  @Get('/tasks-by-client-category-export')
  async getTasksByClientCategoryExport(@Req() request: any, @Body() body: any) {
    const query = body;
    let { userId } = request.user;
    return this.service.getTasksByClientCategoryExport(userId, query);
  }

  @Get('/tasks-by-service')
  async getTasksByService(@Req() request: any, @Query() query: any) {
    let { userId } = request.user;
    return this.service.getTasksByService(userId, query);
  }

  @Post('/tasks-by-service-export')
  async exportTasksByServiceReport(@Req() request: any, @Body() body: any) {
    const query = body;
    let { userId } = request.user;
    return this.service.exportTasksByServiceReport(userId, query);
  }

  @Get('/over-due-tasks')
  async getOverDueTasks(@Req() request: any, @Query() query: any) {
    let { userId } = request.user;
    return this.service.getOverDueTasks(userId, query);
  }

  @Post('/over-due-tasks-export')
  async exportOverDueTasksReport(@Req() request: any, @Body() body: any) {
    const query = body;
    let { userId } = request.user;
    return this.service.exportOverDueTasksReport(userId, query);
  }

  @Get('/client-analytics')
  async getClientAnalytics(@Req() request: any) {
    let { userId } = request.user;
    return this.service.getClientAnalytics(userId);
  }

  @UseGuards(JwtAuthGuard)
  @Get('/client-billing-analytics')
  async getClientBillingAnalytics(@Query() query: any, @Req() request: any) {
    let { userId } = request.user;
    return query?.clientId ? this.service.getClientBillingAnalytics(query, userId) : this.service.getClientGroupBillingAnalytics(query, userId);
  }

  @Get('/due-dsc-registers')
  async getDueDscRegister(@Req() request: any, @Query() query: any) {
    let { userId } = request.user;
    return this.service.getDueDscRegisters(userId, query);
  }

  @Get('/clients-by-category')
  async getClientsByCategory(@Req() request: any, @Query() query: any) {
    let { userId } = request.user;
    return this.service.getClientsByCategory(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Post('/clients-by-category-export')
  async getClientsByCategoryExport(@Req() request: any, @Query() query: any) {
    let { userId } = request.user;
    return this.service.getClientsByCategoryExport(userId, query);
  }

  @Get('/total-log-hours')
  async getTotalLogHours(@Req() request: any, @Query() query: any) {
    let { userId } = request.user;
    return this.service.getTotalLogHours(userId, query);
  }

  @Get('/weekly-log-hours')
  async getWeeklyLogHours(@Req() request: any, @Query() query: IQueryWeeklyLogHoursDto) {
    let { userId } = request.user;
    return this.service.getWeeklyLogHours(userId, query);
  }

  @Get('/employee-tasks-by-status')
  async getEmployeeTasksByStatus(@Req() request: any, @Query() query: any) {
    let { userId } = request.user;
    return this.service.getEmployeeTasksByStatus(userId, query);
  }

  @Post('/employee-tasks-by-status-report')
  async exportEmployeeTasksByStatusReport(@Req() request: any, @Body() body: any) {
    const query = body;
    let { userId } = request.user;
    return this.service.exportEmployeeTasksByStatusReport(userId, query);
  }

  @Get('/employee-attendance')
  async getEmployeeAttendance(@Req() request: any, @Query() query: any) {
    let { userId } = request.user;
    return this.service.getEmployeeAttendance(userId, query);
  }
  @Post('employee-attendance/export')
  async exportEmployeeAttendance(
    @Body() query: any,
    @Res() res: Response,
    @Req() req: any,
  ) {
    const userId = req.user.id;
    return this.service.exportEmployeeAttendance(userId, query, res);
  }

  //Service Dashboard
  //Get Service Task Frequency By ID
  @Get('/service-analytics')
  async serviceAnalytics(@Req() request: any, @Query() query: any) {
    let { userId } = request.user;
    return this.service.getServiceAnalytics(userId, query);
  }



  @Get('/billing-amounts')
  async billingAmounts(@Req() request: any, @Query() query: FindBillingAmounts) {
    let { userId } = request.user;
    return this.service.getBillingAmount(userId, query);
  }

  @Get('/invoice-status')
  async invoiceStatus(@Req() request: any, @Query() query: FindBillingAmounts) {
    let { userId } = request.user;
    return this.service.getInvoiceStatus(userId, query);
  };

  @Get('/receips-amounts')
  async receipsAmounts(@Req() request: any, @Query() query: FindBillingAmounts) {
    let { userId } = request.user;
    return this.service.getReceipsAmounts(userId, query);
  };

  @Get('/invoice-amounts')
  async invoiceAmounts(@Req() request: any, @Query() query: FindBillingAmounts) {
    let { userId } = request.user;
    return this.service.getInvoiceAmounts(userId, query);
  }

  @Get('/outstanding-clients')
  async outStandingClients(@Req() request: any, @Query() query: FindOutStandingClients) {
    let { userId } = request.user;
    return this.service.getOutStandingClients(userId, query);
  };

  @Get('/gst-payable')
  async getPayable(@Req() request: any, @Query() query: FindBillingAmounts) {
    let { userId } = request.user;
    return this.service.getGstPayable(userId, query);

  }

}
