import { Injectable } from '@nestjs/common';
import { OnEvent } from '@nestjs/event-emitter';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { ActivityType } from 'src/modules/activity/activity.entity';
import Client from 'src/modules/clients/entity/client.entity';
import { FormType } from 'src/modules/forms/dto/types';
import {
  FormActivity,
  FormActivityDocument,
} from 'src/modules/forms/schemas/form-activity.schema';
import { sendNotification } from 'src/notifications/notify';
import { User } from 'src/modules/users/entities/user.entity';
import { createQueryBuilder } from 'typeorm';
import { Event_Actions } from './actions';

interface CreateEvent {
  userId: number;
  formId: any;
  clientId: number;
  formName: string;
  formType: string;
  data: any;
}

@Injectable()
export class FormListner {
  constructor(
    @InjectModel(FormActivity.name)
    private formActivityModel: Model<FormActivityDocument>,
  ) {}

  @OnEvent(Event_Actions.FORM_CREATED, { async: true })
  async handleFormCreate(event: CreateEvent) {
    const { userId, formName, formType } = event;

    if (!formType || formType === FormType.KYB) return;

    try {
      let user = await createQueryBuilder(User, 'user')
        .leftJoinAndSelect('user.organization', 'organization')
        .leftJoinAndSelect('organization.users', 'users')
        .leftJoinAndSelect('users.role', 'role')
        .where('user.id = :id', { id: userId })
        .getOne();

      let activity = new this.formActivityModel();
      activity.action = Event_Actions.FORM_CREATED;
      activity.actorId = user.id;
      activity.type = ActivityType.FORM;
      activity.typeId = event?.formId;
      activity.remarks = `Form added by ${user.fullName}`;
      await activity.save();

      let orgAdmin = user.organization.users.find(
        (user: User) => user.role.defaultRole,
      );

      if (!orgAdmin) return;

      let userIds = [orgAdmin.id];

      let notification = {
        title: 'Form added',
        body: `'${formName}' Form has been added by ${user.fullName}`,
      };

      await sendNotification(userIds, notification);
    } catch (e) {
      console.log(e);
    }
  }

  @OnEvent(Event_Actions.FORM_CREATED, { async: true })
  async sendNotificationOnFormCreate(event: CreateEvent) {
    const { userId, clientId, formType } = event;

    if (!formType || formType !== FormType.KYB) return;

    try {
      let user = await User.findOne({
        where: {
          id: userId,
        },
      });

      let client = await Client.findOne({
        where: {
          id: clientId,
        },
        relations: ['clientManager'],
      });

      if (!client?.clientManager?.id) return;

      let userIds = [client.clientManager.id];

      let notification = {
        title: 'KYB Form Created',
        body: `The KYB Form for ${client.displayName} has been added by ${user.fullName}`,
      };

      await sendNotification(userIds, notification);
    } catch (e) {
      console.log(e);
    }
  }

  @OnEvent(Event_Actions.FORM_UPDATED, { async: true })
  async handleFormUpdated(event: CreateEvent) {
    const { userId, formName } = event;

    try {
      let user = await createQueryBuilder(User, 'user')
        .leftJoinAndSelect('user.organization', 'organization')
        .leftJoinAndSelect('organization.users', 'users')
        .leftJoinAndSelect('users.role', 'role')
        .where('user.id = :id', { id: userId })
        .getOne();

      let activity = new this.formActivityModel();
      activity.action = Event_Actions.FORM_UPDATED;
      activity.actorId = user.id;
      activity.type = ActivityType.FORM;
      activity.typeId = event.formId;
      activity.remarks = `Form updated by ${user.fullName}`;
      await activity.save();

      let orgAdmin = user.organization.users.find(
        (user: User) => user.role.defaultRole,
      );

      if (!orgAdmin) return;

      let userIds = [orgAdmin.id];

      let notification = {
        title: 'Form Updated',
        body: `'${formName}' Form has been updated by ${user.fullName}`,
      };

      await sendNotification(userIds, notification);
    } catch (e) {
      console.log(e);
    }
  }

  @OnEvent(Event_Actions.FORM_DELETED, { async: true })
  async handleFormDelete(event: CreateEvent) {
    const { userId, formName } = event;
    try {
      let user = await createQueryBuilder(User, 'user')
        .leftJoinAndSelect('user.organization', 'organization')
        .leftJoinAndSelect('organization.users', 'users')
        .leftJoinAndSelect('users.role', 'role')
        .where('user.id = :id', { id: userId })
        .getOne();

      let orgAdmin = user.organization.users.find(
        (user: User) => user.role.defaultRole,
      );

      if (!orgAdmin) return;

      let userIds = [orgAdmin.id];

      let notification = {
        title: 'Form Deleted',
        body: `'${formName}' Form has been deleted by ${user.fullName}`,
      };

      await sendNotification(userIds, notification);
    } catch (e) {
      console.log(e);
    }
  }
}
