<!DOCTYPE html>
<html>

<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width,initial-scale=1" />
  <link rel="preconnect" href="https://fonts.googleapis.com" />
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
  <link
    href="https://fonts.googleapis.com/css2?family=Montserrat:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap"
    rel="stylesheet" />
</head>

<body style="margin: 0; padding: 0">
  <div style="background-color: aliceblue" align="center">
    <table style="
          background: #ffffff;
          font-family: Mulish;
          color: #404040;
          padding: 40px;
          margin: auto;
          width: 100%;
        ">
      <!-- <tr>
        <td>
          <img src="https://jss-vider.s3.ap-south-1.amazonaws.com/headerejs.jpg"
            alt="ATOM Pro Logo" width="100%" height="35px" />
        </td>
      </tr> -->
      ..
      <tr>
        <td style="
          margin-top: 20px;
          height: auto;
          display: block;
          font-size: 18px;
          color: #182f53;
        ">
          Dear <%= userName %>,
        </td>
      </tr>
      <tr>
        <td style="
          margin-top: 10px;
          height: auto;
          display: block;
          font-size: 18px;
          color: #182f53;
          margin-bottom: 20px;
        ">
          Daily Updates by ATOM Pro
        </td>
      </tr>

      <tr>
        <td style="
        background-color: #2c5c9c;
        color: white;
        border: none;
        font-size: 20px;
        border-radius: 5px;
        text-align: center;
        text-decoration: none;
      ">Goods & Services Tax</td>
      </tr>
      <% if (noticeAndOrdersArray.length> 0) { %>
        <tr>
          <td style="
            background-color: #64B5F6;
            color: white;
            padding: 5px;
            font-size: 18px;
            border-top-left-radius: 5px;
            border-top-right-radius: 5px;
            display: inline-block; 
            margin-top: 50px;
        ">
            Notice & Orders
          </td>
        </tr>
        <tr>
          <td>
            <table style="border-spacing: 0px; border: 0.923px solid #cfcfcf; margin-bottom: 100px;">
              <thead>
                <tr>
                  <th style="
                      border: 0.923px solid #cfcfcf;
                      background: #e4eeff;
                      padding: 5px;
                      color: #404040;
                      font-size: 11.082px;
                      font-style: normal;
                      font-weight: 500;
                      line-height: 150.523%;
                      letter-spacing: 0.665px;
                      width: 2vw;
                      font-weight: bold;
                    ">
                    S.No
                  </th>
                  <th style="
                      border: 0.923px solid #cfcfcf;
                      background: #e4eeff;
                      padding: 5px;
                      color: #404040;
                      font-size: 11.082px;
                      font-style: normal;
                      font-weight: 500;
                      line-height: 150.523%;
                      letter-spacing: 0.665px;
                      width: 20vw;
                      font-weight: bold;
                    ">
                    Client Name
                  </th>
                  <th style="
                      border: 0.923px solid #cfcfcf;
                      background: #e4eeff;
                      padding: 5px;
                      color: #404040;
                      font-size: 11.082px;
                      font-style: normal;
                      font-weight: 500;
                      line-height: 150.523%;
                      letter-spacing: 0.665px;
                      width: 7vw;
                      font-weight: bold;
                    ">
                    Notice / Order No.
                  </th>
                  <th style="
                      border: 0.923px solid #cfcfcf;
                      background: #e4eeff;
                      padding: 5px;
                      color: #404040;
                      font-size: 11.082px;
                      font-style: normal;
                      font-weight: 500;
                      line-height: 150.523%;
                      letter-spacing: 0.665px;
                      width: 6vw;
                      font-weight: bold;
                    ">
                    Type
                  </th>
                  <th style="
                      border: 0.923px solid #cfcfcf;
                      background: #e4eeff;
                      padding: 5px;
                      color: #404040;
                      font-size: 11.082px;
                      font-style: normal;
                      font-weight: 500;
                      line-height: 150.523%;
                      letter-spacing: 0.665px;
                      width: 5vw;
                      font-weight: bold;
                    ">
                    Amount
                  </th>
                  <th style="
                      border: 0.923px solid #cfcfcf;
                      background: #e4eeff;
                      padding: 5px;
                      color: #404040;
                      font-size: 11.082px;
                      font-style: normal;
                      font-weight: 500;
                      line-height: 150.523%;
                      letter-spacing: 0.665px;
                      width: 15vw;
                      font-weight: bold;
                    ">
                    Date of Issuance
                  </th>

                  <th style="
                      border: 0.923px solid #cfcfcf;
                      background: #e4eeff;
                      padding: 5px;
                      color: #404040;
                      font-size: 11.082px;
                      font-style: normal;
                      font-weight: 500;
                      line-height: 150.523%;
                      letter-spacing: 0.665px;
                      width: 15vw;
                      font-weight: bold;
                    ">
                    Due Date
                  </th>
                </tr>
              </thead>
              <tbody>
                   <% 
                  const MAX_ROWS = 20;
                  const total = noticeAndOrdersArray.length;
                  const showCount = Math.min(MAX_ROWS, total);
                %>

                <% for (let i = 0; i < showCount; i++) { 
                    let incrementedValue = i + 1;
                    const row = noticeAndOrdersArray[i];
                %>
                    <tr>
                      <td style="
                      text-align: center;
                      padding: 5px;
                      color: #5f5f5f;
                      font-size: 11.533px;
                      font-style: normal;
                      font-weight: 600;
                      line-height: 150.523%;
                      letter-spacing: 0.692px;
                      border: 0.923px solid #cfcfcf;
                    ">
                        <%= incrementedValue %>
                      </td>
                      <td style="
                      text-align: center;
                      padding: 5px;
                      color: #5f5f5f;
                      font-size: 11.533px;
                      font-style: normal;
                      font-weight: 600;
                      line-height: 150.523%;
                      letter-spacing: 0.692px;
                      border: 0.923px solid #cfcfcf;
                    ">
                        <%= row.clientName %>
                      </td>
                      <td style="
                      text-align: center;
                      padding: 5px;
                      color: #5f5f5f;
                      font-size: 11.533px;
                      font-style: normal;
                      font-weight: 600;
                      line-height: 150.523%;
                      letter-spacing: 0.692px;
                      border: 0.923px solid #cfcfcf;
                    ">
                        <%= row.orderNumber %>
                      </td>
                      <td style="
                      text-align: center;
                      padding: 5px;
                      color: #5f5f5f;
                      font-size: 11.533px;
                      font-style: normal;
                      font-weight: 600;
                      line-height: 150.523%;
                      letter-spacing: 0.692px;
                      border: 0.923px solid #cfcfcf;
                    ">
                        <%= row.type %>
                      </td>
                      <td style="
                      text-align: center;
                      padding: 5px;
                      color: #5f5f5f;
                      font-size: 11.533px;
                      font-style: normal;
                      font-weight: 600;
                      line-height: 150.523%;
                      letter-spacing: 0.692px;
                      border: 0.923px solid #cfcfcf;
                    ">
                        <%= row.amount %>
                      </td>
                      <td style="
                      text-align: center;
                      padding: 5px;
                      color: #5f5f5f;
                      font-size: 11.533px;
                      font-style: normal;
                      font-weight: 600;
                      line-height: 150.523%;
                      letter-spacing: 0.692px;
                      border: 0.923px solid #cfcfcf;
                    ">
                        <%= row.issuanceDate %>
                      </td>

                      <td style="
                      text-align: center;
                      padding: 5px;
                      color: #5f5f5f;
                      font-size: 11.533px;
                      font-style: normal;
                      font-weight: 600;
                      line-height: 150.523%;
                      letter-spacing: 0.692px;
                      border: 0.923px solid #cfcfcf;
                    ">
                        <%= row.dueDate %>
                      </td>
                    </tr>
                    <% } %>
                 <% if (total > MAX_ROWS) { %>
                <tr>
                  <td colspan="9" style="text-align:right; padding:8px; border:0.923px solid #cfcfcf; background:#fafafa;">
                    Showing <%= showCount %> of <%= total %> records.
                    &nbsp; <a href="<%= websiteUrl %>/atom-pro/gst/notice-orders" target="_blank" style="color:#0e47a1; text-decoration:none;">View all notices</a>
                  </td>
                </tr>
                <% } %>
              </tbody>
            </table>
          </td>
        </tr>
        <% } %>
          <% if (additionalNoticeOrdersArray.length> 0) { %>
            <tr>
              <td style="
            background-color: #64B5F6;
            color: white;
            padding: 5px;
            font-size: 18px;
            border-top-left-radius: 5px;
            border-top-right-radius: 5px;
            display: inline-block; 
        ">
                Additional Notice & Orders
              </td>
            </tr>
            <tr>
              <td>
                <table style="border-spacing: 0px; border: 0.923px solid #cfcfcf">
                  <thead>
                    <tr>
                      <th style="
                      border: 0.923px solid #cfcfcf;
                      background: #e4eeff;
                      padding: 5px;
                      color: #404040;
                      font-size: 11.082px;
                      font-style: normal;
                      font-weight: 500;
                      line-height: 150.523%;
                      letter-spacing: 0.665px;
                      width: 2vw;
                      font-weight: bold;
                    ">
                        S.No
                      </th>
                      <th style="
                      border: 0.923px solid #cfcfcf;
                      background: #e4eeff;
                      padding: 5px;
                      color: #404040;
                      font-size: 11.082px;
                      font-style: normal;
                      font-weight: 500;
                      line-height: 150.523%;
                      letter-spacing: 0.665px;
                      width: 20vw;
                      font-weight: bold;
                    ">
                        Client Name
                      </th>
                      <th style="
                    border: 0.923px solid #cfcfcf;
                    background: #e4eeff;
                    padding: 5px;
                    color: #404040;
                    font-size: 11.082px;
                    font-style: normal;
                    font-weight: 500;
                    line-height: 150.523%;
                    letter-spacing: 0.665px;
                    width: 5vw;
                    font-weight: bold;
                  ">
                        FY
                      </th>
                      <th style="
                  border: 0.923px solid #cfcfcf;
                  background: #e4eeff;
                  padding: 5px;
                  color: #404040;
                  font-size: 11.082px;
                  font-style: normal;
                  font-weight: 500;
                  line-height: 150.523%;
                  letter-spacing: 0.665px;
                  width: 5vw;
                  font-weight: bold;
                ">
                        Folder
                      </th>
                      <th style="
                border: 0.923px solid #cfcfcf;
                background: #e4eeff;
                padding: 5px;
                color: #404040;
                font-size: 11.082px;
                font-style: normal;
                font-weight: 500;
                line-height: 150.523%;
                letter-spacing: 0.665px;
                width: 15vw;
                font-weight: bold;
              ">
                        Type
                      </th>
                      <th style="
              border: 0.923px solid #cfcfcf;
              background: #e4eeff;
              padding: 5px;
              color: #404040;
              font-size: 11.082px;
              font-style: normal;
              font-weight: 500;
              line-height: 150.523%;
              letter-spacing: 0.665px;
              width: 7vw;
              font-weight: bold;
            ">
                        Reference ID
                      </th>
                      <th style="
                      border: 0.923px solid #cfcfcf;
                      background: #e4eeff;
                      padding: 5px;
                      color: #404040;
                      font-size: 11.082px;
                      font-style: normal;
                      font-weight: 500;
                      line-height: 150.523%;
                      letter-spacing: 0.665px;
                      width: 15vw;
                      font-weight: bold;
                    ">
                        Date of Issuance
                      </th>
                      <th style="
                      border: 0.923px solid #cfcfcf;
                      background: #e4eeff;
                      padding: 5px;
                      color: #404040;
                      font-size: 11.082px;
                      font-style: normal;
                      font-weight: 500;
                      line-height: 150.523%;
                      letter-spacing: 0.665px;
                      width: 15vw;
                      font-weight: bold;
                    ">
                        Due Date
                      </th>
                       <th style="
                      border: 0.923px solid #cfcfcf;
                      background: #e4eeff;
                      padding: 5px;
                      color: #404040;
                      font-size: 11.082px;
                      font-style: normal;
                      font-weight: 500;
                      line-height: 150.523%;
                      letter-spacing: 0.665px;
                      width: 10w;
                      font-weight: bold;
                    ">
                        Reply Status
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                       <% 
                  const MAX_ROWS = 20;
                  const total = additionalNoticeOrdersArray.length;
                  const showCount = Math.min(MAX_ROWS, total);
                %>

                <% for (let i = 0; i < showCount; i++) { 
                    let incrementedValue = i + 1;
                    const row = additionalNoticeOrdersArray[i];
                %>
                        <tr>
                          <td style="
                      text-align: center;
                      padding: 5px;
                      color: #5f5f5f;
                      font-size: 11.533px;
                      font-style: normal;
                      font-weight: 600;
                      line-height: 150.523%;
                      letter-spacing: 0.692px;
                      border: 0.923px solid #cfcfcf;
                    ">
                            <%= incrementedValue %>
                          </td>
                          <td style="
                      text-align: center;
                      padding: 5px;
                      color: #5f5f5f;
                      font-size: 11.533px;
                      font-style: normal;
                      font-weight: 600;
                      line-height: 150.523%;
                      letter-spacing: 0.692px;
                      border: 0.923px solid #cfcfcf;
                    ">
                            <%= row.clientName %>
                          </td>
                          <td style="
                    text-align: center;
                    padding: 5px;
                    color: #5f5f5f;
                    font-size: 11.533px;
                    font-style: normal;
                    font-weight: 600;
                    line-height: 150.523%;
                    letter-spacing: 0.692px;
                    border: 0.923px solid #cfcfcf;
                  ">
                            <%= row.financialYear %>
                          </td>
                          <td style="
                  text-align: center;
                  padding: 5px;
                  color: #5f5f5f;
                  font-size: 11.533px;
                  font-style: normal;
                  font-weight: 600;
                  line-height: 150.523%;
                  letter-spacing: 0.692px;
                  border: 0.923px solid #cfcfcf;
                ">
                            <%= row.folder %>
                          </td>
                          <td style="
                text-align: center;
                padding: 5px;
                color: #5f5f5f;
                font-size: 11.533px;
                font-style: normal;
                font-weight: 600;
                line-height: 150.523%;
                letter-spacing: 0.692px;
                border: 0.923px solid #cfcfcf;
              ">
                            <%= row.type %>
                          </td>
                          <td style="
              text-align: center;
              padding: 5px;
              color: #5f5f5f;
              font-size: 11.533px;
              font-style: normal;
              font-weight: 600;
              line-height: 150.523%;
              letter-spacing: 0.692px;
              border: 0.923px solid #cfcfcf;
            ">
                            <%= row.referenceNumber %>
                          </td>
                          <td style="
                      text-align: center;
                      padding: 5px;
                      color: #5f5f5f;
                      font-size: 11.533px;
                      font-style: normal;
                      font-weight: 600;
                      line-height: 150.523%;
                      letter-spacing: 0.692px;
                      border: 0.923px solid #cfcfcf;
                    ">
                            <%= row.issuanceDate %>
                          </td>
                          <td style="
                          text-align: center;
                          padding: 5px;
                          color: #5f5f5f;
                          font-size: 11.533px;
                          font-style: normal;
                          font-weight: 600;
                          line-height: 150.523%;
                          letter-spacing: 0.692px;
                          border: 0.923px solid #cfcfcf;
                        ">
                                <%= row.dueDate %>
                              </td>
                              <td style="
                          text-align: center;
                          padding: 5px;
                          color: #5f5f5f;
                          font-size: 11.533px;
                          font-style: normal;
                          font-weight: 600;
                          line-height: 150.523%;
                          letter-spacing: 0.692px;
                          border: 0.923px solid #cfcfcf;
                        ">
                                <%= row.refStatus %>
                              </td>
                        </tr>
                        <% } %>
                 <% if (total > MAX_ROWS) { %>
                <tr>
                  <td colspan="9" style="text-align:right; padding:8px; border:0.923px solid #cfcfcf; background:#fafafa;">
                    Showing <%= showCount %> of <%= total %> records.
                    &nbsp; <a href="<%= websiteUrl %>/atom-pro/gst/add-notice-orders" target="_blank" style="color:#0e47a1; text-decoration:none;">View all notices</a>
                  </td>
                </tr>
                <% } %>
                  </tbody>
                </table>
              </td>
            </tr>
            <% } %>

              <tr>
                <td align="left">
                  <p style="font-size: 14px;">Please log in to your account on our platform as soon as possible to review the notices.
                    It is crucial to address these promptly to ensure compliance and avoid any potential
                    penalties or disruptions to your business operations.</p>
                </td>
              </tr>
              <tr>
                <td style="font-family: Mulish; font-size: 12px; color: maroon;">
                  <strong>Note:</strong> This is an automated email. Please do not reply, as this mailbox is not
                  monitored.
                </td>
              </tr>
                  <tr>
        <td>
          <p style="margin-top: 3px; font-size: 17px">

            <span style="font-weight: bold; color: #aa1adc;">Disclaimer:</span>
            Please be aware that the preceding message is an automatically generated email originating from our system,
            and the associated mailbox is not actively monitored for incoming replies. Therefore, we kindly request that
            you refrain from attempting to respond directly to this notification. For any inquiries or assistance,
            please contact us via the mobile number or email address provided below. Thank you for your understanding.
        </td>
      </tr>

            
      <tr>
        <td>
          <hr style="border: #1C34FF 3px solid;" />
        </td>
      </tr>
    <tr>
        <td align="center">
          <h2><%= legalName %></h2>
         
        </td>
      </tr>
    <tr>  
        <td align="center">
          <table style="width: 210px">
      
        <tr>
          <td>
            <p style="margin-top: 3px; font-size: 17px">
  
              <span style="font-weight: bold">Address:</span>
              <%= adress %></td>
        </tr>
        <td align="center">
          <table style="width: 700px;">
            <tr align="center">
              <td align="center">
                <p style="margin-top: 3px; font-size: 17px">
                  
                </p>
              </td>
            </tr>
            <tr>
              <td align="center">
                <table style="margin: auto;">
                  <tr>
                    <td align="left" style="padding-right: 5px;">
                      <p style="margin-top: 3px; font-size: 17px;"><strong>Contact:</strong> <%=phoneNumber %></p>
                    </td>
                    <td align="right" style="padding-right: 10px;">
                      <p style="margin-top: 3px; font-size: 17px;">
                        |<strong> Email:</strong> <%=mail %></p>
                    </td>
                  </tr>
                </table>
              </td>
            </tr>
            <!-- <tr>
              <td align="center">
                <h2 style="margin: 0 auto;">Powered By: <img style="height: 30px;" src="https://jss-vider.s3.ap-south-1.amazonaws.com/vider_Logo.jpg" alt="" /></h2>
              </td>
            </tr>  -->
          </table>
        </td>
      </tr>
    </table>
  </div>
</body>

</html>