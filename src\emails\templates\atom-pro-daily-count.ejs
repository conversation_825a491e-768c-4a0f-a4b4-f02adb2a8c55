<!DOCTYPE html>
<html>

<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width,initial-scale=1" />
  <link rel="preconnect" href="https://fonts.googleapis.com" />
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
  <link
    href="https://fonts.googleapis.com/css2?family=Montserrat:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap"
    rel="stylesheet" />
</head>

<body style="margin: 0; padding: 0">
  <div style="background-color: aliceblue; " align="center">
    <table style="
        background: #ffffff;
        font-family: Montserrat;
        color: #121212;
        margin: auto;
        width: 80%;
        padding:20px;
      ">
      </tr>
      <tr>
        <td>
          <h1 style="margin-top: 10px; height: auto; display: block; font-size: 20px; color: #212225">
           Automation Completed Count
          </h1>
        </td>
      </tr>

      <tr>
        <td>
          <table style="border-spacing: 0px; border: 0.923px solid #cfcfcf">
            <thead>
              <tr>
                <th
                  style="
                    border: 0.923px solid #cfcfcf;
                    background: rgba(8, 21, 60, 0.88);
                    padding: 10px;
                    color: #fff;
                    font-size: 11.082px;
                    font-style: normal;
                    font-weight: 500;
                    line-height: 150.523%;
                    letter-spacing: 0.665px;
                    width: 5vw;
                  "
                >
                  S.No
                </th>
                <th
                  style="
                    border: 0.923px solid #cfcfcf;
                    background: rgba(8, 21, 60, 0.88);
                    padding: 10px;
                    color: #fff;
                    font-size: 11.082px;
                    font-style: normal;
                    font-weight: 500;
                    line-height: 150.523%;
                    letter-spacing: 0.665px;
                    width: 20vw;
                  "
                >
                  Automation Type
                </th>
                <th
                  style="
                    border: 0.923px solid #cfcfcf;
                    background: rgba(8, 21, 60, 0.88);
                    padding: 10px;
                    color: #fff;
                    font-size: 11.082px;
                    font-style: normal;
                    font-weight: 500;
                    line-height: 150.523%;
                    letter-spacing: 0.665px;
                    width: 20vw;
                  "
                >
                  Completed Count
                </th>
            
              </tr>
            </thead>
            <tbody>
              <% for(let i=0; i < typeWiseDetails.length; i++ ) { %> <% let incrementedValue=i +
              1; %>
              <tr>
                <td
                  style="
                    text-align: center;
                    padding: 10px;
                    color: #5f5f5f;
                    font-size: 11.533px;
                    font-style: normal;
                    font-weight: 600;
                    line-height: 150.523%;
                    letter-spacing: 0.692px;
                    border: 0.923px solid #cfcfcf;
                  "
                >
                  <%= incrementedValue %>
                </td>
                <td
                  style="
                    text-align: center;
                    padding: 10px;
                    color: #5f5f5f;
                    font-size: 11.533px;
                    font-style: normal;
                    font-weight: 600;
                    line-height: 150.523%;
                    letter-spacing: 0.692px;
                    border: 0.923px solid #cfcfcf;
                  "
                >
                  <%= typeWiseDetails[i].type %>
                </td>
                <td
                  style="
                    text-align: center;
                    padding: 10px;
                    color: #5f5f5f;
                    font-size: 11.533px;
                    font-style: normal;
                    font-weight: 600;
                    line-height: 150.523%;
                    letter-spacing: 0.692px;
                    border: 0.923px solid #cfcfcf;
                  "
                >
                  <%= typeWiseDetails[i].count %>
                </td>
               
              </tr>
              <% } %>
            </tbody>
          </table>
        </td>
      </tr>
    
    </table>
  </div>
</body>

</html>