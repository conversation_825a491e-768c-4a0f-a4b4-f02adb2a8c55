import { Injectable } from '@nestjs/common';
import Storage, { StorageType } from 'src/modules/storage/storage.entity';
import Task from 'src/modules/tasks/entity/task.entity';
import { createQueryBuilder } from 'typeorm';
import { v4 as uuidv4 } from 'uuid';


@Injectable()
export class AttachmentsService {
  async findAttachments(id: number) {
    let taskAttachments = await Storage.find({
      where: { task: { id } },
      relations: ['task'],
    });
    return taskAttachments;
  }

  async existingClientTaskStorage(task: Task) {
    if (!task.client) return null;

    let taskFinYearStorage = await Storage.findOne({
      where: {
        name: task.financialYear,
        client: { id: task.client?.id },
      },
    });

    if (!taskFinYearStorage) {
      taskFinYearStorage = new Storage();
      taskFinYearStorage.name = task.financialYear;
      taskFinYearStorage.client = task?.client;
      taskFinYearStorage.clientGroup = task?.clientGroup;
      taskFinYearStorage.type = StorageType.FOLDER;
      taskFinYearStorage.uid = uuidv4();
      await taskFinYearStorage.save();
    }

    let taskCategoryStorage = await Storage.findOne({
      where: {
        name: task.category?.name,
        parent: { id: taskFinYearStorage.id },
        client: { id: task.client?.id },
      },
      relations: ['parent'],
    });

    if (!taskCategoryStorage && task.category) {
      taskCategoryStorage = new Storage();
      taskCategoryStorage.name = task.category?.name;
      taskCategoryStorage.client = task.client;
      taskCategoryStorage.clientGroup = task?.clientGroup;
      taskCategoryStorage.type = StorageType.FOLDER;
      taskCategoryStorage.parent = taskFinYearStorage;
      taskCategoryStorage.uid = uuidv4();
      await taskCategoryStorage.save();
    }

    let taskSubCategoryStorage = await Storage.findOne({
      where: {
        name: task.subCategory?.name,
        parent: { id: taskCategoryStorage.id },
        client: { id: task.client?.id },
      },
      relations: ['parent'],
    });

    if (!taskSubCategoryStorage && task.subCategory) {
      taskSubCategoryStorage = new Storage();
      taskSubCategoryStorage.name = task.subCategory?.name;
      taskSubCategoryStorage.client = task?.client;
      taskSubCategoryStorage.clientGroup = task?.clientGroup;
      taskSubCategoryStorage.type = StorageType.FOLDER;
      taskSubCategoryStorage.parent = taskCategoryStorage;
      taskSubCategoryStorage.uid = uuidv4();
      await taskSubCategoryStorage.save();
    }

    let taskStorage = await createQueryBuilder(Storage, 'storage')
      .leftJoinAndSelect('storage.parent', 'parent')
      .where('storage.name = :name', { name: task.name })
      .andWhere('(parent.id = :subCategory or parent.id = :category)', {
        subCategory: taskSubCategoryStorage?.id,
        category: taskCategoryStorage?.id,
      })
      .getOne();

    if (!taskStorage) {
      let storage = new Storage();
      storage.name = task.name;
      storage.client = task?.client;
      storage.clientGroup = task?.clientGroup;
      storage.type = StorageType.FOLDER;
      storage.uid = uuidv4();
      storage.parent = taskSubCategoryStorage || taskCategoryStorage || taskFinYearStorage;
      taskStorage = await Storage.save(storage);
    }

    return taskStorage;
  }
}
