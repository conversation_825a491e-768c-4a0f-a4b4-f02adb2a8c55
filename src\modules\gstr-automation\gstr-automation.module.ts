import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import GstrCredentials from './entity/gstrCredentials.entity';
import { GstrClientService } from './service/gstr-client.service';
import { GstrClientController } from './controllers/gstr-client.controller';
import { GstrService } from './service/notices.service';
import { GstrController } from './controllers/notices.controllers';
import GstrNoticeOrders from './entity/noticeOrders.entity';
import GstrMachines from './entity/gstrMachines.entity';
import GstrProfile from './entity/gstrProfile.entity';

import { GstrDashboardService } from './service/dashboard.services';
import { GstrDashboardController } from './controllers/dashboard.controller';
import GstrAdditionalNoticeOrders from './entity/gstrAdditionalOrdersAndNotices.entity';
import { GstrCronJobService } from './service/gstr-cron-job-service';
import GstrUpdateTracker from './entity/gstr_update_tracker.entity';
import { GstrConfigController } from './controllers/config.controller';
import { GstrConfigService } from './service/config.services';
import { GstrCredentialsSubscriber } from 'src/event-subscribers/GstrCredentials.subscriber';
import { GstCronController } from './controllers/gst-cron.controller';
import { AttachmentGstService } from './service/attachments-gst.service';
import { AttachmentGstController } from './controllers/attachments-gst.controller';
import { AwsService } from '../storage/upload.service';
import { OneDriveStorageService } from '../ondrive-storage/onedrive-storage.service';
import { GoogleDriveStorageService } from '../ondrive-storage/googledrive-storage.service';
import { StorageService } from '../storage/storage.service';
import { AttachmentsService } from '../tasks/services/attachments.service';
import { BharathStorageService } from '../storage/bharath-storage.service';
import { BharathCloudService } from '../storage/bharath-upload.service';
import { GstrOutstandingDemand } from './entity/gstrDemands.entity';
import { GstrLedgerBalance } from './entity/gstrLedgersBalance.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      GstrCredentials,
      GstrNoticeOrders,
      GstrMachines,
      GstrProfile,
      GstrAdditionalNoticeOrders,
      GstrUpdateTracker,
      GstrOutstandingDemand,
      GstrLedgerBalance
    ]),
  ],
  controllers: [
    GstrClientController,
    GstrController,
    GstrDashboardController,
    GstrConfigController,
    GstCronController,
    AttachmentGstController,
  ],
  providers: [
    GstrClientService,
    GstrService,
    GstrDashboardService,
    GstrCronJobService,
    GstrConfigService,
    GstrCredentialsSubscriber,
    AttachmentGstService,
    AwsService,
    OneDriveStorageService,
    GoogleDriveStorageService,
    StorageService,
    AttachmentsService,
    BharathStorageService,
    BharathCloudService,
  ],
})
export class GstrAutomationModule {}
