import { Injectable } from '@nestjs/common';
import { User } from 'src/modules/users/entities/user.entity';
import { Brackets, createQueryBuilder } from 'typeorm';
import CreateLabelDto from './dto/create-label.dto';
import Label from './label.entity';

@Injectable()
export class LabelService {
  async findAll(userId: number, query: any) {
    let user = await User.findOne({
      where: { id: userId },
      relations: ['organization'],
    });
  
    let labels = createQueryBuilder(Label, 'label')
      .leftJoinAndSelect('label.organization', 'organization')
      .where(new Brackets(qb => {
        qb.where('organization.id = :orgId', { orgId: user.organization?.id })
          .orWhere('label.defaultOne IS TRUE');
      }));
  
    const sort = (typeof query?.sort === "string") ? JSON.parse(query.sort) : query?.sort;
  
    if (query.search) {
      labels.andWhere('label.name LIKE :search', { search: `%${query.search}%` });
    }
  
    if (sort?.column) {
      const columnMap: Record<string, string> = {
        name: 'label.name',
      };
      const column = columnMap[sort.column] || sort.column;
      labels.orderBy(column, sort.direction.toUpperCase());
    }
  
    if (query.offset >= 0) {
      labels.skip(query.offset);
    }
    if (query.limit) {
      labels.take(query.limit);
    }
  
    return labels.getManyAndCount();
  }
  
  async create(userId: number, data: CreateLabelDto) {
    let user = await User.findOne({
      where: { id: userId },
      relations: ['organization'],
    });

    let label = new Label();
    label.name = data.name;
    label.color = data.color;
    label.organization = user.organization;
    await label.save();
    return label;
  }

  async update(id: number, data: CreateLabelDto) {
    let label = await Label.findOne(id);
    label.name = data.name;
    label.color = data.color;
    await label.save();
    return label;
  }

  async delete(id: number) {
    let label = await Label.findOne({ where: { id } });
    await label.remove();
    return { succcess: true };
  }
}
