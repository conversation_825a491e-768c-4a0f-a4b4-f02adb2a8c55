import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateInvoiceBankDetailsTable1658314275449
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
        CREATE TABLE invoice_bank_details(
            id int NOT NULL AUTO_INCREMENT,
            bank_name varchar(255) NOT NULL,
            account_number varchar(255) NOT NULL,
            ifsc_code varchar(255) NOT NULL,
            branch_name varchar(255) NOT NULL,
            upi_id varchar(255) NOT NULL,
            upi_attachment varchar(255) NOT NULL,
            PRIMARY KEY (id)
        )            
 `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}
