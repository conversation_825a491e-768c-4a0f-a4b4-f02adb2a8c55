import { BaseEntity, Column, CreateDateColumn, Entity, ManyToOne, PrimaryGeneratedColumn, UpdateDateColumn } from "typeorm";
import { User } from "../users/entities/user.entity";

@Entity()
class LeaveApproval extends BaseEntity {
    @PrimaryGeneratedColumn()
    id: number;

    // @ManyToOne(() => User, (user) => user.leaveApproval)
    // user: User;

    @Column({ nullable: true })
    processInstanceId: string;

    @Column({ type: 'varchar' })
    description: string;

    @Column({ type: 'date' })
    date: string;

    @CreateDateColumn()
    createdAt: string;

    @UpdateDateColumn()
    updatedAt: string;

};

export default LeaveApproval;

