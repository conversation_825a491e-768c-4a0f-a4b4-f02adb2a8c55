import {
  Body,
  Delete,
  Get,
  Param,
  ParseIntPipe,
  Post,
  Put,
  Query,
  UseGuards,
  Request,
} from '@nestjs/common';
import {
  AddChecklistDto,
  AddChecklistItems,
  UpdateChecklist,
  UpdateChecklistItem,
} from '../dto/add-checklist.dto';
import { TasksController } from './task.controller';
import { JwtAuthGuard } from 'src/modules/users/jwt/jwt-auth.guard';

export class ChecklistsController extends TasksController {
  @Get('/checklists')
  getChecklists(@Query('taskId', ParseIntPipe) taskId: number) {
    return this.checklistsService.findChecklists(taskId);
  }

  @UseGuards(JwtAuthGuard)
  @Post('/checklists/:checklistId')
  addChecklistItems(
    @Param('checklistId', ParseIntPipe) checklistId: number,
    @Body() body: AddChecklistItems,
    @Request() req: any,
  ) {
    const { userId } = req.user;
    return this.checklistsService.addChecklistItems(userId ,checklistId, body);
  }

  @UseGuards(JwtAuthGuard)
  @Post('/:taskId/checklists')
  addChecklist(
    @Body() body: AddChecklistDto,
    @Param('taskId', ParseIntPipe) taskId: number,
    @Request() req: any,
  ) {
    const { userId } = req.user;
    return this.checklistsService.addChecklist(userId, taskId, body);
  }


  @UseGuards(JwtAuthGuard)
  @Put('/checklists/update')
  updateChecklist(@Body() body: UpdateChecklist, @Request() req: any) {
    const { userId } = req.user;
    return this.checklistsService.updateChecklist(userId, body);
  }

  @UseGuards(JwtAuthGuard)
  @Delete('/checklists/:id')
  deleteChecklist(@Param('id', ParseIntPipe) id: number, @Request() req: any) {
    const { userId } = req.user;
    return this.checklistsService.deleteChecklist(id, userId);
  }

  @UseGuards(JwtAuthGuard)
  @Put('/checklists/checklist-items/update')
  updateChecklistItem(@Body() body: UpdateChecklistItem, @Request() req: any) {
    const { userId } = req.user;
    return this.checklistsService.updateChecklistItem(body, userId);
  }

  @UseGuards(JwtAuthGuard)
  @Delete('/checklists/checklist-items/:id')
  deleteChecklistItem(
    @Param('id', ParseIntPipe) id: number,
    @Query('taskId') taskId: string, 
    @Request() req: any
  ) {
    const { userId } = req.user;
    return this.checklistsService.deleteChecklistItem(id,userId,taskId);
  }
}
