import { MigrationInterface, QueryRunner } from 'typeorm';

export class AlterEstimateTable1658413974276 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
        ALTER TABLE estimate
        ADD COLUMN status enum("DRAFT", "APPROVAL_PENDING", "APPROVED", "CANCELLED", "EMAIL_SENT", "INVOICED") NOT NULL DEFAULT "DRAFT"
   `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}
