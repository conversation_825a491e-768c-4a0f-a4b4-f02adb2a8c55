import { Optional } from '@nestjs/common';
import Client from 'src/modules/clients/entity/client.entity';
import { Organization } from 'src/modules/organization/entities/organization.entity';
import {
  BaseEntity,
  Column,
  CreateDateColumn,
  Entity,
  ManyToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import TanClientCredentials from './tan-client-credentials.entity';

@Entity()
class TanIncomeTaxForms extends BaseEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  formCd: string;

  @Column()
  formDesc: string;

  @Column()
  formName: string;

  @Column()
  formShortName: string;

  @Column()
  ackDt: string;

  @Column()
  ackNum: string;

  @Column()
  caMembershipNo: string;

  @Column()
  caName: string;

  @Column()
  filingTypeCd: string;

  @Column()
  fillingMode: string;

  @Column()
  submitBy: string;

  @Column()
  submitUserId: string;

  @Column()
  udinNum: string;

  @Column('json')
  activities: object;

  @Column('json')
  storageFiles: object;

  @Column()
  verStatus: string;

  @Column()
  refYear: string;

  @Column()
  refYearType: string;

  @Column()
  formStatus: string;

  @Column()
  financialQuarter: string;

  @Column()
  isUdinApplicable: boolean;

  @Column()
  transactionNo: string;

  @Column()
  tempAckNo:string;

  @CreateDateColumn()
  createdAt: string;

  @UpdateDateColumn()
  updatedAt: string;

  @Column()
  organizationId: number;

  @Column()
  clientId:number;

  @Column()
  transactionType:string;

  // @ManyToOne(() => Client, (client) => client.autIncomeTaxForms, { onDelete: 'SET NULL' })
  // client: Client;

  @ManyToOne(() => TanClientCredentials, (tanClientCredentials) => tanClientCredentials.tanForms, { onDelete: 'SET NULL' })
  tanClientCredentials: TanClientCredentials;

  @Column()
  status:string;

  @Column()
  dtOfPrcng:string;
}

export default TanIncomeTaxForms;
