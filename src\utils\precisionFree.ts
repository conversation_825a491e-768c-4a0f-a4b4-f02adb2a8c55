/**
 * Rounds a number to a given number of decimal places to avoid floating-point precision issues.
 *
 * @param value - The number to round
 * @param decimals - Number of decimal places (default: 2)
 * @returns number - Rounded number
 */
export function precisionFree(value: number, decimals: number = 2): number {
    const factor = Math.pow(10, decimals);
    return Math.round((value + Number.EPSILON) * factor) / factor;
}
