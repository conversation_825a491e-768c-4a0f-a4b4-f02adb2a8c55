import { Body, Controller, Get, Post, Req, UseGuards } from '@nestjs/common';
import { JwtAuthGuard } from '../users/jwt/jwt-auth.guard';
import { SandboxService } from './sandbox.service';

@Controller('sandbox')
export class SandboxController {
  constructor(private service: SandboxService) { }

  @Post('/token')
  async getSandboxToken() {
    return this.service.getSandboxToken();
  }

  @Post('/gstdetails')
  async getGstDetails(@Body() body: any) {
    return this.service.getGstDetails(body);
  }


  @Post('/fyngst')
  async getFyngstDetails(@Body() body: any) {
    return this.service.fynGstDetails(body);
  }

  @Post('/pandetails')
  async getPanDetails(@Body() body: any) {
    return this.service.getPanDetails(body);
  }


}

