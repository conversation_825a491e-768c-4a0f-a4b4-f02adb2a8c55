import ClientGroup from 'src/modules/client-group/client-group.entity';
import Client from 'src/modules/clients/entity/client.entity';
import { User } from 'src/modules/users/entities/user.entity';
import { BaseEntity, CreateDateColumn, Entity, ManyToOne, PrimaryGeneratedColumn } from 'typeorm';

@Entity('client_pin')
class ClientPin extends BaseEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @CreateDateColumn()
  createdAt: string;

  @ManyToOne(() => Client, (client) => client.clientPins, { onDelete: 'CASCADE' })
  client: Client;

  @ManyToOne(() => ClientGroup, (clientGroup) => clientGroup.clientPins)
  clientGroup: ClientGroup;

  @ManyToOne(() => User, (user) => user.clientPins)
  user: User;
}

export default ClientPin;
