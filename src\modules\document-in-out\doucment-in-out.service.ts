import { Injectable } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { User } from 'src/modules/users/entities/user.entity';
import { createQueryBuilder, getRepository } from 'typeorm';
import Client from '../clients/entity/client.entity';
import DocumentInOut from './entity/document-in-out.entity';
import DocumentData from './entity/documents-data.entity';
import DocumentsData from './entity/documents-data.entity';
import Kyb from '../kyb/kyb.entity';
import ClientGroup from '../client-group/client-group.entity';
import { isNumber } from 'lodash';
import * as moment from 'moment';
import Storage, { StorageSystem } from '../storage/storage.entity';
import { StorageService } from '../storage/storage.service';
import { OneDriveStorageService } from '../ondrive-storage/onedrive-storage.service';
import { BharathStorageService } from '../storage/bharath-storage.service';
import { AttachmentsService } from '../tasks/services/attachments.service';
import { getUserDetails, insertINTONotificationUpdate } from 'src/utils/re-use';
import { sendnewMail } from 'src/emails/newemails';
import OrganizationPreferences from '../organization-preferences/entity/organization-preferences.entity';
import ViderWhatsappSessions from '../whatsapp/entity/viderWhatsappSessions';
import { sendClientWhatsAppTemplateMessage, sendWhatsAppTextMessage } from '../whatsapp/whatsapp.service';
import { Organization } from '../organization/entities/organization.entity';

@Injectable()
export class DocumentInOutService {
  constructor(
    private storageService: StorageService,
    private oneDriveService: OneDriveStorageService,
    private bharathService: BharathStorageService,
    private attachementService: AttachmentsService

  ) { }

  async create(userId: number, data: any) {
    let user = await User.findOne({
      where: { id: userId },
      relations: ['organization'],
    });

    let documentInOut = new DocumentInOut();
    documentInOut.documentType = data?.inOut;
    documentInOut.useType = data?.type;
    documentInOut.description = data?.description;
    documentInOut.client = data?.clients?.type ? null : data?.clients;
    documentInOut.clientGroup = data?.clients?.type ? data?.clients : null;
    documentInOut.organization = user.organization;
    documentInOut.receivedBy = data?.receivedBy;
    documentInOut.receivedTo = data?.receivedTo;
    documentInOut.givenBy = data?.givenTo;
    documentInOut.user = user;
    documentInOut.updatedBy = user;

    const mobileNumbers = data?.clientWhatsapp?.map((item, index) => ({
      id: index,
      number: item.number,
      countryCode: item.countryCode,
    })) || [];

    const mail = data?.clientMail?.map((item, index) => ({
      id: index,
      email: item?.email,
    })) || [];

    documentInOut.mailData = { whatsapp: mobileNumbers, mails: mail };
    if (data?.keptat?.name) {
      documentInOut.keptAtName = data?.keptat?.name || null;
      documentInOut.keptAt = data?.keptat || null;
    }
    documentInOut.task = data?.task;

    const documentData = new DocumentsData();
    documentData.type = 'original';
    documentData.returnable = "yes";
    documentData.mode = 'hard';
    documentData.manner = 'handdelivery';
    documentData.documentType = data?.inOut ?? null;
    documentData.documentInOut = documentInOut;

    documentInOut.documentData = await Promise.all([documentData]);

    documentInOut.updatedDateTime = moment().utcOffset(330).format("YYYY-MM-DD HH:mm:ss");

    const response = await documentInOut.save();
    await Promise.all(documentInOut.documentData.map(doc => doc.save()));
    return response?.id;
  }

  async createDocumentItem(userId: number, data: any) {
    let user = await User.findOne({
      where: { id: userId },
      relations: ['organization'],
    });

    let documentInOut = await createQueryBuilder(DocumentInOut, 'documentInOut')
      .leftJoinAndSelect('documentInOut.client', 'client')
      .leftJoinAndSelect('documentInOut.clientGroup', 'clientGroup')
      .leftJoinAndSelect('documentInOut.documentData', 'documentData')
      .leftJoinAndSelect('documentData.documentCategory', 'documentCategory')
      .leftJoinAndSelect('documentData.kyb', 'kyb')
      .leftJoinAndSelect('documentInOut.receivedBy', 'receivedBy')
      .leftJoinAndSelect('documentInOut.givenBy', 'givenBy')
      .leftJoinAndSelect('documentInOut.keptAt', 'keptAt')
      .leftJoinAndSelect('documentInOut.task', 'task')
      .leftJoin('documentInOut.organization', 'organization')
      .where('documentInOut.id = :id', { id: data.id })
      .andWhere('organization.id = :organizationId', { organizationId: user.organization.id })
      .getOne();

    const documentData = new DocumentsData();
    documentData.type = 'original';
    documentData.returnable = "yes";
    documentData.mode = 'hard';
    documentData.manner = 'handdelivery';
    documentData.documentType = data?.inOut ?? null;
    documentData.documentInOut = documentInOut;
    await documentData.save();

    return documentInOut?.id;
  }

  async deleteDocumentItem(userId: number, data: any) {
    let user = await User.findOne({
      where: { id: userId },
      relations: ['organization'],
    });

    const existingDocument = await DocumentsData.findOne({ where: { id: data.id }, relations: ['kyb'] });
    if (existingDocument.kyb) {
      let kybs = await createQueryBuilder(Kyb, 'kyb')
        .where('kyb.id = :id', { id: existingDocument?.kyb?.id });

      const data = await kybs.getOne();
      await data.remove()
    }
    await existingDocument.remove();
  }

  async createAndSave(userId: number, data: any) {
    let user = await User.findOne({
      where: { id: userId },
      relations: ['organization'],
    });

    let documentInOut = new DocumentInOut();
    documentInOut.documentType = data?.inOut;
    documentInOut.useType = data?.type;
    documentInOut.description = data?.description;
    documentInOut.client = data?.clients?.type ? null : data?.clients;
    documentInOut.clientGroup = data?.clients?.type ? data?.clients : null;
    documentInOut.organization = user.organization;
    documentInOut.receivedBy = data?.receivedBy;
    documentInOut.receivedTo = data?.receivedTo;
    documentInOut.givenBy = data?.givenTo;
    documentInOut.user = user;
    documentInOut.updatedBy = user;

    const mobileNumbers = data?.clientWhatsapp?.map((item, index) => ({
      id: index,
      number: item.number,
      countryCode: item.countryCode,
    })) || [];

    const mail = data?.clientMail?.map((item, index) => ({
      id: index,
      email: item?.email,
    })) || [];

    documentInOut.mailData = { whatsapp: mobileNumbers, mails: mail };
    if (data?.keptat?.name) {
      documentInOut.keptAtName = data?.keptat?.name || null;
      documentInOut.keptAt = data?.keptat || null;
    }
    documentInOut.task = data?.task;

    if (data?.type === "kyb") {
      documentInOut.documentData = await Promise.all(
        (data?.documents || []).map(async (item) => {
          const documentData = new DocumentsData();
          documentData.type = item.type ?? null;
          documentData.returnable = item.returnable ?? null;
          documentData.mode = item.mode ?? null;
          documentData.manner = item.manner ?? null;
          documentData.documentInOut = documentInOut;

          let client = await Client.findOne({ where: { id: documentInOut.client?.id } });
          let clientGroup = await ClientGroup.findOne({ where: { id: documentInOut?.clientGroup?.id } });

          let kyb = new Kyb();
          kyb.documentName = item.documentName === "custom" ? item?.customName : item.documentName;
          kyb.documentNumber = item.documentNumber ?? null;
          kyb.client = client;
          kyb.clientGroup = clientGroup;
          kyb.user = user;
          kyb.documentsData = documentData;

          documentData.kyb = kyb;
          return documentData;
        })
      );
    } else {
      documentInOut.documentData = await Promise.all(
        (data?.documents || []).map((item) => {
          const documentData = new DocumentsData();
          documentData.type = item.type ?? null;
          documentData.returnable = item.returnable ?? null;
          documentData.mode = item.mode ?? null;
          documentData.manner = item.manner ?? null;
          documentData.documentType = data?.inOut ?? null;
          documentData.documentInOut = documentInOut;
          documentData.documentName = item.documentName;
          documentData.documentCategory = item?.documentCategory;
          return documentData;
        })
      );
    }

    documentInOut.updatedDateTime = moment().utcOffset(330).format("YYYY-MM-DD HH:mm:ss");
    await documentInOut.save();
    await Promise.all(documentInOut.documentData.map(doc => doc.save()));
    if (data?.type === "kyb") {
      await Promise.all(documentInOut.documentData.map(doc => doc.kyb?.save()));
    }
  }

  async get(userId: number, query: any) {
    const user = await User.findOne({
      where: { id: userId },
      relations: ['organization'],
    });
    const documentInOut = createQueryBuilder(DocumentInOut, 'documentInOut')
      .select([
        'documentInOut',
        'client.id',
        'client.displayName',
        'clientGroup.id',
        'clientGroup.displayName',
        'clientGroup.type',
        'task.id',
        'task.name',
        'task.taskNumber',
        'user.id',
        'user.fullName',
        'updatedBy.id',
        'updatedBy.fullName',
        'organization.id'
      ])
      .leftJoin('documentInOut.client', 'client')
      .leftJoin('documentInOut.clientGroup', 'clientGroup')
      .leftJoin('documentInOut.task', 'task')
      .leftJoin('documentInOut.user', 'user')
      .leftJoin('documentInOut.updatedBy', 'updatedBy')
      .leftJoin('documentInOut.organization', 'organization')
      .where('organization.id = :organizationId', { organizationId: user.organization.id });
    const sort = (typeof query?.sort === "string") ? JSON.parse(query.sort) : query?.sort;
    if (sort?.column) {
      const columnMap: Record<string, string> = {
        documentId: 'documentInOut.documentId',
        useType: 'documentInOut.useType',
        displayName: 'client.displayName',
        name: 'task.name',
        createdBy: 'user.fullName',
        updatedBy: 'updatedBy.fullName',
        updatedAt: 'documentInOut.updatedDateTime'
      };
      const column = columnMap[sort.column] || sort.column;
      documentInOut.orderBy(column, sort.direction.toUpperCase());
    } else {
      documentInOut.orderBy('documentInOut.updatedDateTime', 'DESC');
    };
    if (query.type !== "all") {
      documentInOut.andWhere('documentInOut.documentType = :useType', {
        useType: query.type,
      });
    }

    if (query.search) {
      documentInOut.andWhere('client.displayName like :search or clientGroup.displayName like :search or task.taskNumber like :search or task.name like :search', {
        search: `%${query.search}%`,
      });
    }

    if (query?.offset >= 0) {
      documentInOut.skip(query?.offset);
    }

    if (query?.limit) {
      documentInOut.take(query?.limit);
    }

    // documentInOut.orderBy('documentInOut.updatedDateTime', 'DESC');

    let result = await documentInOut.getManyAndCount();
    return result;
  }

  async findOne(id: number, userId: any) {
    const user = await User.findOne({
      where: { id: userId },
      relations: ['organization'],
    });

    const documentInOut = createQueryBuilder(DocumentInOut, 'documentInOut')
      .select([
        'documentInOut',
        'client.id',
        'client.displayName',
        'client.email',
        'client.mobileNumber',
        'clientGroup.id',
        'clientGroup.displayName',
        'clientGroup.type',
        'clientGroup.email',
        'clientGroup.mobileNumber',
        'documentData',
        'attachments',
        'documentCategory',
        'kyb',
        'kybStorage',
        'receivedBy.id',
        'receivedBy.fullName',
        'givenBy.id',
        'givenBy.fullName',
        'keptAt',
        'task.id',
        'task.name',
        'task.taskNumber',
        'user.id',
        'user.fullName',
        'updatedBy.id',
        'updatedBy.fullName',
        'organization.id'
      ])
      .leftJoin('documentInOut.client', 'client')
      .leftJoin('documentInOut.clientGroup', 'clientGroup')
      .leftJoin('documentInOut.documentData', 'documentData')
      .leftJoin('documentData.attachments', 'attachments')
      .leftJoin('documentData.documentCategory', 'documentCategory')
      .leftJoin('documentData.kyb', 'kyb')
      .leftJoin('kyb.storage', 'kybStorage')
      .leftJoin('documentInOut.receivedBy', 'receivedBy')
      .leftJoin('documentInOut.givenBy', 'givenBy')
      .leftJoin('documentInOut.keptAt', 'keptAt')
      .leftJoin('documentInOut.task', 'task')
      .leftJoin('documentInOut.user', 'user')
      .leftJoin('documentInOut.updatedBy', 'updatedBy')
      .leftJoin('documentInOut.organization', 'organization')
      .where('documentInOut.id = :id', { id })
      .andWhere('organization.id = :organizationId', { organizationId: user.organization.id });

    let result = await documentInOut.getOne();
    return result;
  }

  async update(id, body: any, userId: number) {
    let user = await User.findOne({
      where: { id: userId },
      relations: ['organization'],
    });
    const documentRepo = getRepository(DocumentsData);
    const data = body;
    let documentInOut = await createQueryBuilder(DocumentInOut, 'documentInOut')
      .leftJoinAndSelect('documentInOut.client', 'client')
      .leftJoinAndSelect('documentInOut.clientGroup', 'clientGroup')
      .leftJoinAndSelect('documentInOut.documentData', 'documentData')
      .leftJoinAndSelect('documentData.documentCategory', 'documentCategory')
      .leftJoinAndSelect('documentData.kyb', 'kyb')
      .leftJoinAndSelect('kyb.storage', 'storage')
      .leftJoinAndSelect('documentInOut.receivedBy', 'receivedBy')
      .leftJoinAndSelect('documentInOut.givenBy', 'givenBy')
      .leftJoinAndSelect('documentInOut.keptAt', 'keptAt')
      .leftJoinAndSelect('documentInOut.task', 'task')
      .leftJoin('documentInOut.organization', 'organization')
      .where('documentInOut.id = :id', { id })
      .andWhere('organization.id = :organizationId', { organizationId: user.organization.id })
      .getOne();
    const oldDocumentDataIds = documentInOut.documentData.map(item => item.id);
    documentInOut.documentType = data?.documentType;
    documentInOut.useType = data?.useType;
    documentInOut.description = data?.description;
    documentInOut.client = data?.client;
    documentInOut.clientGroup = data?.clientGroup;
    documentInOut.organization = user.organization;
    if (data?.documentType === "in") {
      documentInOut.receivedBy = data?.receivedBy;
    }
    documentInOut.receivedTo = data?.receivedTo;
    documentInOut.givenBy = data?.givenBy;
    documentInOut.updatedBy = user;

    const mobileNumbers = data?.clientWhatsapp?.map((item, index) => ({
      id: index,
      number: item.number,
      countryCode: item.countryCode,
    })) || [];

    const mail = data?.clientMail?.map((item, index) => ({
      id: index,
      email: item?.email,
    })) || [];

    documentInOut.mailData = { whatsapp: mobileNumbers, mails: mail };
    documentInOut.keptAtName = data?.keptAt?.name;
    documentInOut.keptAt = data?.keptAt;
    documentInOut.task = data?.task;

    if (data?.useType === "kyb") {
      documentInOut.documentData = await Promise.all(
        (data?.documents || []).map(async (item) => {
          if (isNumber(item.id)) {
            const existingDocument = await DocumentsData.findOne({ where: { id: item.id } });
            existingDocument.type = item.type ?? null;
            existingDocument.returnable = item.returnable ?? null;
            existingDocument.mode = item.mode ?? null;
            existingDocument.manner = item.manner ?? null;
            existingDocument.documentType = data?.type ?? null;
            existingDocument.documentInOut = documentInOut;
            existingDocument.documentName = item.documentName;

            let client = await Client.findOne({ where: { id: documentInOut.client?.id } });
            let clientGroup = await ClientGroup.findOne({ where: { id: documentInOut?.clientGroup?.id } });

            if (isNumber(item?.kyb?.id)) {

              const existingKyb = await Kyb.findOne({ where: { id: item.kyb.id }, relations: ['storage'] });
              let storage: Storage;

              existingKyb.documentName = item?.kyb?.documentName === "custom" ? item?.kyb?.customName : item?.kyb?.documentName;
              existingKyb.documentNumber = item?.kyb?.documentNumber;
              existingKyb.user = user;
              existingKyb.documentsData = item;
              if (item?.kyb?.storage) {
                if (existingKyb?.storage?.id) {
                  if (item.kyb.storage.name !== existingKyb?.storage?.name) {
                    if (existingKyb?.storage.storageSystem === StorageSystem.AMAZON) {
                      this.storageService.deleteAwsFile(existingKyb?.storage?.file);
                    } else if (existingKyb?.storage.storageSystem === StorageSystem.MICROSOFT) {
                      this.oneDriveService.deleteOneDriveFile(userId, existingKyb?.storage?.fileId);
                    } else if (existingKyb?.storage.storageSystem === StorageSystem.BHARATHCLOUD) {
                      this.bharathService.deleteB3File(userId, existingKyb?.storage?.file)
                    }

                  }
                  existingKyb.storage = storage;
                } else {
                  storage = await this.storageService.addAttachements(userId, item.kyb.storage);
                  existingKyb.storage = storage;
                }
              } else {
                if (existingKyb?.storage?.id) {
                  const existingStorage = await Storage.findOne({ where: { id: existingKyb?.storage?.id } });
                  await existingStorage.remove();
                  if (existingStorage) {
                    if (existingStorage.storageSystem === StorageSystem.AMAZON) {
                      this.storageService.deleteAwsFile(existingKyb.storage.file)
                    } else if (existingStorage.storageSystem === StorageSystem.MICROSOFT) {
                      this.oneDriveService.deleteOneDriveFile(userId, existingKyb.storage.fileId);
                    } else if (existingStorage.storageSystem === StorageSystem.BHARATHCLOUD) {
                      this.bharathService.deleteB3File(user.organization.id, existingKyb.storage.file)
                    }
                  }
                  existingKyb.storage = null;
                }
              }
              existingKyb.client = client;
              existingKyb.clientGroup = clientGroup;

              existingDocument.kyb = existingKyb;
            } else {
              let kyb = new Kyb();
              kyb.documentName = item.documentName === "custom" ? item?.customName : item.documentName;
              kyb.documentNumber = item.documentNumber;
              kyb.client = client;
              kyb.clientGroup = clientGroup;
              kyb.user = user;
              kyb.documentsData = item;
              let storage: Storage | null = null;
              if (item?.kyb?.storage) {
                storage = await this.storageService.addAttachements(userId, item?.kyb?.storage);
                kyb.storage = storage;

              }

              existingDocument.kyb = kyb;
            }

            if (!existingDocument.documentInOut) {
              if (isNumber(item.kyb.id)) {
                const existingKyb = await Kyb.findOne({ where: { id: item.kyb.id } });
                await existingKyb.remove();
                existingDocument.kyb = null;
              }
              await existingDocument.remove();
              return null;
            } else {
              return existingDocument ?? item;
            }
          } else {
            const documentData = new DocumentsData();
            documentData.type = item.type ?? null;
            documentData.returnable = item.returnable ?? null;
            documentData.mode = item.mode ?? null;
            documentData.manner = item.manner ?? null;
            documentData.documentType = data?.useType ?? null;
            documentData.documentInOut = documentInOut;
            documentData.documentName = item.documentName;

            let client = await Client.findOne({ where: { id: documentInOut.client?.id } });
            let clientGroup = await ClientGroup.findOne({ where: { id: documentInOut?.clientGroup?.id } });
            let kyb = new Kyb();
            kyb.documentName = item.kyb?.documentName === "custom" ? item?.kyb?.customName : item?.kyb?.documentName;
            kyb.documentNumber = item?.kyb?.documentNumber;
            kyb.client = client;
            kyb.clientGroup = clientGroup;
            kyb.user = user;
            kyb.documentsData = documentData;
            documentData.kyb = kyb;
            return documentData;
          }
        })
      );
    } else {
      documentInOut.documentData = await Promise.all(
        (data?.documents || []).map(async (item) => {
          if (isNumber(item.id)) {
            const existingDocument = await DocumentsData.findOne({ where: { id: item.id } });
            existingDocument.type = item.type ?? null;
            existingDocument.returnable = item.returnable ?? null;
            existingDocument.mode = item.mode ?? null;
            existingDocument.manner = item.manner ?? null;
            existingDocument.documentType = data?.useType ?? null;
            existingDocument.documentInOut = documentInOut;
            existingDocument.documentName = item.documentName;
            existingDocument.documentCategory = item?.documentCategory;
            if (!existingDocument.documentInOut) {
              await existingDocument.remove();
              return null;
            } else {
              return existingDocument ?? item;
            }
          } else {
            const documentData = new DocumentsData();
            documentData.type = item.type ?? null;
            documentData.returnable = item.returnable ?? null;
            documentData.mode = item.mode ?? null;
            documentData.manner = item.manner ?? null;
            documentData.documentType = data?.useType ?? null;
            documentData.documentInOut = documentInOut;
            documentData.documentName = item.documentName;
            documentData.documentCategory = item?.documentCategory;
            return documentData;
          }
        })
      );
    }

    const newDocumentDataIds = documentInOut.documentData.map(item => item?.id);
    const removedDocumentDataIds = oldDocumentDataIds.filter(item => !newDocumentDataIds.includes(item));

    if (removedDocumentDataIds.length) {
      const getDataToRemove = await createQueryBuilder(DocumentData, 'documentdata')
        .leftJoinAndSelect('documentdata.kyb', 'kyb')
        .where('documentdata.id in (:...ids)', { ids: removedDocumentDataIds })
        .getMany();

      for (let i of getDataToRemove) {
        if (documentInOut.useType === 'kyb') {
          getDataToRemove.map(async item => await item.kyb.remove());
        }
        getDataToRemove.map(async item => await item.remove());
      }
    }

    documentInOut.updatedBy = user;
    documentInOut.updatedDateTime = moment().utcOffset(330).format("YYYY-MM-DD HH:mm");

    await documentInOut.save();
    await documentRepo.save(documentInOut.documentData);
    if (data?.useType === "kyb") {
      await Promise.all(documentInOut.documentData.map(doc => doc.kyb?.save()));
    }

    const usersList = [
      ...documentInOut.givenBy.map(item => item.id),
      ...documentInOut.receivedBy.map(item => item.id)
    ];
    const usersLists = [
      ...documentInOut.givenBy,
      ...documentInOut.receivedBy
    ];

    const users: any = [...new Set(usersList)];
    const uniqueUsersList = [...new Map(usersLists.map(item => [item.id, item])).values()];
    const orgId = user?.organization?.id;
    const orgdetails = await Organization.findOne({ id: orgId });
    const addressParts = [
      orgdetails.buildingNo || '',
      orgdetails.floorNumber || '',
      orgdetails.buildingName || '',
      orgdetails.street || '',
      orgdetails.location || '',
      orgdetails.city || '',
      orgdetails.district || '',
      orgdetails.state || ''
    ].filter(part => part && part.trim() !== '');
    const pincode = orgdetails.pincode && orgdetails.pincode.trim() !== '' ? ` - ${orgdetails.pincode}` : '';

      const address = addressParts.join(', ') + pincode;
    if (documentInOut?.useType === "kyb") {
      const title = (documentInOut?.documentType === "out" && data?.created) ? "Document Outward" : (documentInOut?.documentType === "in" && data?.created) ? 'Document Inward Create' : documentInOut?.documentType === "in" ? "Document Inward Update" : documentInOut?.documentType === "out" ? "Document Outward Update" : "Document Outward Update";
      const body = `<strong>${documentInOut?.documentType === "out" ? documentInOut.receivedTo : documentInOut.receivedBy?.map(item => item.fullName).join(",")}</strong> ${data?.created ? "<strong>recorded</strong>" : "<strong>updated</strong>"} Documents ${documentInOut?.documentType === "in" ? "Inward" : "Outward"} Entry with <strong>${documentInOut?.documentId}</strong> on <strong>${moment().utcOffset(330).format("YYYY-MM-DD HH:mm")}</strong> related to KYB of <strong>${documentInOut.client ? documentInOut.client?.displayName : documentInOut.clientGroup?.displayName}</strong>, was subsequently handed over to <strong>${documentInOut.givenBy?.map(item => item.fullName).join(",")}</strong> ${documentInOut?.keptAtName ? `and is currently kept at <strong>${documentInOut?.keptAtName}</strong>` : ""}. <box>The list of document(s) include:
  <box>
        <ol>
          ${documentInOut?.documentData?.map(item => `<li>${item?.kyb?.documentName}</li>`).join("")}
        </ol>
  </box></box>
  `;
      const key = 'DOCUMENT_IN_OUT_PUSH';
      insertINTONotificationUpdate(title, body, users, orgId, key);
      const organization = await Organization.findOne({ id: orgId });


    const addressParts = [
      organization.buildingNo || '',
      organization.floorNumber || '',
      organization.buildingName || '',
      organization.street || '',
      organization.location || '',
      organization.city || '',
      organization.district || '',
      organization.state || ''
    ].filter(part => part && part.trim() !== '');
    const pincode = organization.pincode && organization.pincode.trim() !== '' ? ` - ${organization.pincode}` : '';

    const address = addressParts.join(', ') + pincode;

      const currentDate = new Date();
      const formattedDate = `${String(currentDate.getDate()).padStart(2, "0")}-${String(currentDate.getMonth() + 1).padStart(2, "0")}-${currentDate.getFullYear()} ${String(currentDate.getHours()).padStart(2, "0")}:${String(currentDate.getMinutes()).padStart(2, "0")}`;
      documentInOut['created'] = data?.created;
      for (let userData of uniqueUsersList) {
        await sendnewMail({
          id: userData?.id,
          key: 'DOCUMENT_IN_OUT_MAIL',
          email: userData?.email,
          data: {
            adminName: userData?.fullName,
            documentInOut: documentInOut,
            formattedDate: formattedDate,
            userId: userData?.id,
            adress: address,
                  phoneNumber: organization?.mobileNumber,
                  mail: organization?.email,
                  legalName: organization?.tradeName || organization?.legalName
            
          },
          filePath: 'docu-in-out-kyb',
          subject: (documentInOut?.documentType === "out" && data?.created) ? "Document Outward" : (documentInOut?.documentType === "in" && data?.created) ? 'Document Inward Create' : documentInOut?.documentType === "in" ? "Document Inward Update" : documentInOut?.documentType === "out" ? "Document Outward Update" : "Document Outward Update",
        });
        // Send WhatsApp notification for Users
        try {
          if (userData?.id !== undefined) {
            const sessionValidation = await ViderWhatsappSessions.findOne({
              where: { userId: userData.id, status: 'ACTIVE' },
            });
            if (sessionValidation) {
              const adminUserDetails = await getUserDetails(userData.id);
              const {
                full_name: userFullName,
                mobile_number: userPhoneNumber,
                id,
                organization_id,
              } = adminUserDetails;

              const key = 'DOCUMENT_IN_OUT_WHATSAPP';


              const whatsappMessageBody = `Hi ${userFullName}, 
Your document ${documentInOut?.documentType === 'out' ? 'outward' : 'inward'} operation has been ${data?.created ? 'recorded' : 'updated'} successfully.
${documentInOut?.documentType === "out" ? documentInOut.receivedTo : documentInOut.receivedBy?.map(item => item.fullName).join(", ")} ${data?.created ? "recorded" : "updated"}
Documents ${documentInOut?.documentType === "in" ? "Inward" : "Outward"} Entry with ${documentInOut?.documentId} 
on ${moment().utcOffset(330).format("YYYY-MM-DD HH:mm")} related to ${documentInOut.client ? documentInOut.client?.displayName : documentInOut.clientGroup?.displayName},
was subsequently handed over to ${documentInOut.givenBy?.map(item => item.fullName).join(", ")}${documentInOut?.keptAtName ? `
and is currently kept at ${documentInOut?.keptAtName}` : ""}. The list of document(s) include: ${documentInOut?.documentData?.map((item, idx) => `${idx + 1}. ${item?.documentCategory?.name ? item?.documentCategory?.name : item?.documentName}`).join(" | ")}.
             
Thanks, Team`;



              await sendWhatsAppTextMessage(
                `91${userPhoneNumber}`,
                whatsappMessageBody,
                organization_id,
                title,
                id,
                key
              );
            }
          }
        } catch (error) {
          console.error('Error sending User WhatsApp notification:', error);
        }
      }
      if (data?.mailCheck && (documentInOut?.client || documentInOut?.clientGroup)) {
        const orgPreferences: any = await OrganizationPreferences.findOne({
          where: { organization: orgId },
        });
        const clientPreferences = orgPreferences?.clientPreferences?.email;
        const key = 'DOCUMENT_IN_OUT_MAIL';
        if (clientPreferences && clientPreferences[key]) {
          const clientMail = documentInOut?.client ? documentInOut?.client?.email : documentInOut?.clientGroup?.email;
          const allMails = documentInOut?.mailData?.['mails'].map((item: any) => item.email);
          const mails = [clientMail, ...allMails];
          for (let mail of mails) {
            const mailOptions = {
              id: user?.id,
              key: 'DOCUMENT_IN_OUT_MAIL',
              email: mail,
              clientMail: 'ORGANIZATION_CLIENT_EMAIL',
              data: {
                adminName: documentInOut?.client ? documentInOut?.client?.displayName : documentInOut?.clientGroup?.displayName,
                documentInOut: documentInOut,
                formattedDate: formattedDate,
                userId: user?.id,
                tradeName: orgdetails?.tradeName,
                legalName: orgdetails?.tradeName || orgdetails?.legalName,
                adress: address,
                phoneNumber: orgdetails?.mobileNumber,
                mail: orgdetails?.email,
              },
              filePath: 'docu-in-out-kyb',
              subject: (documentInOut?.documentType === "out" && data?.created) ? "Document Outward" : (documentInOut?.documentType === "in" && data?.created) ? 'Document Inward Create' : documentInOut?.documentType === "in" ? "Document Inward Update" : documentInOut?.documentType === "out" ? "Document Outward Update" : "Document Outward Update",
            };
            await sendnewMail(mailOptions);
          }
        }
      }
      if (data?.whatsappCheck && (documentInOut?.client || documentInOut?.clientGroup)) {
        const templateName = 'document_inout_client';
        const dateTime = moment().utcOffset(330).format("YYYY-MM-DD HH:mm")
        const receiver = documentInOut?.documentType === "out"
          ? documentInOut.receivedTo
          : documentInOut.receivedBy?.map(item => item.fullName).join(", ");
        const inOut = documentInOut?.documentType === "in" ? "Inward" : "Outward";
        const docId = documentInOut?.documentId;
        const clientOrGroup = documentInOut?.client
          ? documentInOut.client?.displayName
          : (documentInOut.clientGroup?.displayName || "our Organization");
        const handoverTo = documentInOut.givenBy?.map(item => item.fullName).join(", ");
        const keptAt = documentInOut?.keptAtName || "";


        const docList = documentInOut?.documentData
          ?.map((item, index) => {
            const name = item?.documentCategory?.name || item?.documentName || "Unnamed Document";
            return `${index + 1}. ${name}`;
          })
          .join(" | ");
        const orgPreferences: any = await OrganizationPreferences.findOne({
          where: { organization: orgId },
        });
        const clientPreferences = orgPreferences?.clientPreferences?.email;
        const key = 'DOCUMENT_IN_OUT_MAIL';
        if (clientPreferences && clientPreferences[key]) {
          const clientMobileNumber = documentInOut?.client ? documentInOut?.client?.mobileNumber : documentInOut?.clientGroup?.mobileNumber;
          const allMobileNumbers = documentInOut?.mailData?.['whatsapp'].map((item: any) => item.number);

          const mobileNumbers = [clientMobileNumber, ...allMobileNumbers]


          try {
            for (let mobileNumber of mobileNumbers) {
              const whatsappOptions = {
                to: mobileNumber,
                name: templateName,
                header: [
                  {
                    type: 'text',
                    text: clientOrGroup,
                  },
                ],
                body: [
                  receiver,
                  inOut,
                  docId,
                  dateTime,
                  clientOrGroup,
                  handoverTo,
                  keptAt,
                  docList
                ],
                title: 'document_inout_client',
                userId: user.id,
                orgId: user.organization.id,
                key: 'DOCUMENT_IN_OUT_WHATSAPP',
              };
              await sendClientWhatsAppTemplateMessage(whatsappOptions);

            }
          } catch {
            console.log('cannot send whatsapp document-in-out message to client')
          }
        }
      }

    } else if (documentInOut?.useType === "task") {
      const title = (documentInOut?.documentType === "out" && data?.created) ? "Document Outward" : (documentInOut?.documentType === "in" && data?.created) ? 'Document Inward Create' : documentInOut?.documentType === "in" ? "Document Inward Update" : documentInOut?.documentType === "out" ? "Document Outward Update" : "Document Outward Update";
      const body = `<strong>${documentInOut?.documentType === "out" ? documentInOut.receivedTo : documentInOut.receivedBy?.map(item => item.fullName).join(",")}</strong> ${data?.created ? "<strong>recorded</strong>" : "<strong>updated</strong>"} Documents ${documentInOut?.documentType === "in" ? "Inward" : "Outward"} Entry with <strong>${documentInOut?.documentId}</strong> on <strong>${moment().utcOffset(330).format("YYYY-MM-DD HH:mm")}</strong> related to <strong>${documentInOut.client ? documentInOut.client?.displayName : documentInOut.clientGroup?.displayName}</strong>, Task Id <strong>${documentInOut?.task?.['taskNumber']}</strong>, and Task Name <strong>${documentInOut?.task?.['name']}</strong> was subsequently handed over to <strong>${documentInOut.givenBy?.map(item => item.fullName).join(",")}</strong> ${documentInOut?.keptAtName ? `and is currently kept at <strong>${documentInOut?.keptAtName}</strong>` : ""}. <box>The list of document(s) include:
  <box>
        <ol>
          ${documentInOut?.documentData?.map(item => `<li>${item?.documentCategory?.name ? item?.documentCategory?.name : item?.documentName}</li>`).join("")}
        </ol>
  </box></box>
  `;

      const key = 'DOCUMENT_IN_OUT_PUSH';
      insertINTONotificationUpdate(title, body, users, orgId, key);

      const organization = await Organization.findOne({ id: orgId });


      const addressParts = [
        organization.buildingNo || '',
        organization.floorNumber || '',
        organization.buildingName || '',
        organization.street || '',
        organization.location || '',
        organization.city || '',
        organization.district || '',
        organization.state || ''
      ].filter(part => part && part.trim() !== '');
      const pincode = organization.pincode && organization.pincode.trim() !== '' ? ` - ${organization.pincode}` : '';
  
      const address = addressParts.join(', ') + pincode;
      const currentDate = new Date();
      const formattedDate = `${String(currentDate.getDate()).padStart(2, "0")}-${String(currentDate.getMonth() + 1).padStart(2, "0")}-${currentDate.getFullYear()} ${String(currentDate.getHours()).padStart(2, "0")}:${String(currentDate.getMinutes()).padStart(2, "0")}`;
      documentInOut['created'] = data?.created;

      for (let userData of uniqueUsersList) {
        await sendnewMail({
          id: userData?.id,
          key: 'DOCUMENT_IN_OUT_MAIL',
          email: userData?.email,
          data: {
            adminName: userData?.fullName,
            documentInOut: documentInOut,
            formattedDate: formattedDate,
            userId: userData?.id,
            adress: address,
                  phoneNumber: organization?.mobileNumber,
                  mail: organization?.email,
                  legalName: organization?.tradeName || organization?.legalName

          },
          filePath: 'docu-in-out-task',
          subject: (documentInOut?.documentType === "out" && data?.created) ? "Document Outward" : (documentInOut?.documentType === "in" && data?.created) ? 'Document Inward Create' : documentInOut?.documentType === "in" ? "Document Inward Update" : documentInOut?.documentType === "out" ? "Document Outward Update" : "Document Outward Update",
        });
        // Send WhatsApp notification for Users
        try {
          if (userData?.id !== undefined) {
            const sessionValidation = await ViderWhatsappSessions.findOne({
              where: { userId: userData.id, status: 'ACTIVE' },
            });

            if (sessionValidation) {
              const adminUserDetails = await getUserDetails(userData.id);
              const {
                full_name: userFullName,
                mobile_number: userPhoneNumber,
                id,
                organization_id,
              } = adminUserDetails;

              const key = 'DOCUMENT_IN_OUT_WHATSAPP';

              const whatsappMessageBody = `Hi ${userFullName}, 
        
Your document ${documentInOut?.documentType === 'out' ? 'outward' : 'inward'} operation has been ${data?.created ? 'recorded' : 'updated'} successfully.
${documentInOut?.documentType === "out" ? documentInOut.receivedTo : documentInOut.receivedBy?.map(item => item.fullName).join(", ")} ${data?.created ? "recorded" : "updated"}
Documents ${documentInOut?.documentType === "in" ? "Inward" : "Outward"} Entry with ${documentInOut?.documentId} 
on ${moment().utcOffset(330).format("YYYY-MM-DD HH:mm")} related to ${documentInOut.client ? documentInOut.client?.displayName : documentInOut.clientGroup?.displayName},
Task Id ${documentInOut?.task?.['taskNumber']}, and Task Name ${documentInOut?.task?.['name']} was subsequently handed over to ${documentInOut.givenBy?.map(item => item.fullName).join(", ")}${documentInOut?.keptAtName ? `
and is currently kept at ${documentInOut?.keptAtName}` : ""}. The list of document(s) include: ${documentInOut?.documentData?.map((item, idx) => `${idx + 1}. ${item?.documentCategory?.name ? item?.documentCategory?.name : item?.documentName}`).join(" | ")}.
             
Thanks, Team`;


              await sendWhatsAppTextMessage(
                `91${userPhoneNumber}`,
                whatsappMessageBody,
                organization_id,
                title,
                id,
                key
              );
            }
          }
        } catch (error) {
          console.error('Error sending User WhatsApp notification:', error);
        }
      }

      if (data?.mailCheck && (documentInOut?.client || documentInOut?.clientGroup)) {
        const mailOptions = {
          id: user?.id,
          key: 'DOCUMENT_IN_OUT_MAIL',
          email: documentInOut?.client ? documentInOut?.client?.email : documentInOut?.clientGroup?.email,
          clientMail: 'ORGANIZATION_CLIENT_EMAIL',
          data: {
            adminName: documentInOut?.client ? documentInOut?.client?.displayName : documentInOut?.clientGroup?.displayName,
            documentInOut: documentInOut,
            formattedDate: formattedDate,
            userId: user?.id,
            legalName: orgdetails?.tradeName || orgdetails?.legalName,
                adress: address,
                phoneNumber: orgdetails?.mobileNumber,
                mail: orgdetails?.email,
          },
          filePath: 'docu-in-out-task',
          subject: (documentInOut?.documentType === "out" && data?.created) ? "Document Outward" : (documentInOut?.documentType === "in" && data?.created) ? 'Document Inward Create' : documentInOut?.documentType === "in" ? "Document Inward Update" : documentInOut?.documentType === "out" ? "Document Outward Update" : "Document Outward Update",
        };
        const orgPreferences: any = await OrganizationPreferences.findOne({
          where: { organization: orgId },
        });
        const clientPreferences = orgPreferences?.clientPreferences?.email;
        let key = 'DOCUMENT_IN_OUT_MAIL';
        if (clientPreferences && clientPreferences[key]) {
          const clientMail = documentInOut?.client ? documentInOut?.client?.email : documentInOut?.clientGroup?.email;
          const allMails = documentInOut?.mailData?.['mails'].map((item: any) => item.email);
          const mails = [clientMail, ...allMails];


          for (let mail of mails) {
            const mailOptions = {
              id: user?.id,
              key: 'DOCUMENT_IN_OUT_MAIL',
              email: mail,
              clientMail: 'ORGANIZATION_CLIENT_EMAIL',
              data: {
                adminName: documentInOut?.client ? documentInOut?.client?.displayName : documentInOut?.clientGroup?.displayName,
                documentInOut: documentInOut,
                formattedDate: formattedDate,
                userId: user?.id,
                legalName: orgdetails?.tradeName || orgdetails?.legalName,
                adress: address,
                phoneNumber: orgdetails?.mobileNumber,
                mail: orgdetails?.email,
              },
              filePath: 'docu-in-out-task',
              subject: (documentInOut?.documentType === "out" && data?.created) ? "Document Outward" : (documentInOut?.documentType === "in" && data?.created) ? 'Document Inward Create' : documentInOut?.documentType === "in" ? "Document Inward Update" : documentInOut?.documentType === "out" ? "Document Outward Update" : "Document Outward Update",
            };
            await sendnewMail(mailOptions);
          }
        }
      }
      if (data?.whatsappCheck && (documentInOut?.client || documentInOut?.clientGroup)) {
        const templateName = 'document_inout_client';
        const dateTime = moment().utcOffset(330).format("YYYY-MM-DD HH:mm")
        const receiver = documentInOut?.documentType === "out"
          ? documentInOut.receivedTo
          : documentInOut.receivedBy?.map(item => item.fullName).join(", ");
        const inOut = documentInOut?.documentType === "in" ? "Inward" : "Outward";
        const docId = documentInOut?.documentId;
        const clientOrGroup = documentInOut?.client
          ? documentInOut.client?.displayName
          : (documentInOut.clientGroup?.displayName || "our Organization");
        const handoverTo = documentInOut.givenBy?.map(item => item.fullName).join(", ");
        const keptAt = documentInOut?.keptAtName || "";
        const docList = documentInOut?.documentData
          ?.map((item, index) => {
            const name = item?.documentCategory?.name || item?.documentName || "Unnamed Document";
            return `${index + 1}. ${name}`;
          })
          .join(" | ");
        const orgPreferences: any = await OrganizationPreferences.findOne({
          where: { organization: orgId },
        });
        const clientPreferences = orgPreferences?.clientPreferences?.email;
        const key = 'DOCUMENT_IN_OUT_MAIL';
        if (clientPreferences && clientPreferences[key]) {
          const clientMobileNumber = documentInOut?.client ? documentInOut?.client?.mobileNumber : documentInOut?.clientGroup?.mobileNumber;
          const allMobileNumbers = documentInOut?.mailData?.['whatsapp'].map((item: any) => item.number);

          const mobileNumbers = [clientMobileNumber, ...allMobileNumbers]


          try {
            for (let mobileNumber of mobileNumbers) {
              const whatsappOptions = {
                to: mobileNumber,
                name: templateName,
                header: [
                  {
                    type: 'text',
                    text: clientOrGroup,
                  },
                ],
                body: [
                  receiver,
                  inOut,
                  docId,
                  dateTime,
                  clientOrGroup,
                  handoverTo,
                  keptAt,
                  docList
                ],
                title: 'document_inout_client',
                userId: user.id,
                orgId: user.organization.id,
                key: 'DOCUMENT_IN_OUT_WHATSAPP',
              };
              await sendClientWhatsAppTemplateMessage(whatsappOptions);

            }
          } catch {
          }
        }
      }
    } else if (documentInOut?.useType === "general") {
      const title = (documentInOut?.documentType === "out" && data?.created) ? "Document Outward" : (documentInOut?.documentType === "in" && data?.created) ? 'Document Inward Create' : documentInOut?.documentType === "in" ? "Document Inward Update" : documentInOut?.documentType === "out" ? "Document Outward Update" : "Document Outward Update";
      const body = `<strong>${documentInOut?.documentType === "out" ? documentInOut.receivedTo : documentInOut.receivedBy?.map(item => item.fullName).join(",")}</strong> ${data?.created ? "<strong>recorded</strong>" : "<strong>updated</strong>"} Documents ${documentInOut?.documentType === "in" ? "Inward" : "Outward"} Entry with <strong>${documentInOut?.documentId}</strong> on <strong>${moment().utcOffset(330).format("YYYY-MM-DD HH:mm")}</strong> related to <strong>${documentInOut.client ? documentInOut.client?.displayName : documentInOut.clientGroup?.displayName ? documentInOut.clientGroup?.displayName : "our Organization"}</strong> was subsequently handed over to <strong>${documentInOut.givenBy?.map(item => item.fullName).join(",")}</strong> ${documentInOut?.keptAtName ? `and is currently kept at <strong>${documentInOut?.keptAtName}</strong>` : ""}. <box>The list of document(s) include:
<box>
      <ol>
        ${documentInOut?.documentData?.map(item => `<li>${item?.documentCategory?.name ? item?.documentCategory?.name : item?.documentName}</li>`).join("")}
      </ol>
</box></box>
`;
      const key = 'DOCUMENT_IN_OUT_PUSH';
      insertINTONotificationUpdate(title, body, users, orgId, key);
      const organization = await Organization.findOne({ id: orgId });


    const addressParts = [
      organization.buildingNo || '',
      organization.floorNumber || '',
      organization.buildingName || '',
      organization.street || '',
      organization.location || '',
      organization.city || '',
      organization.district || '',
      organization.state || ''
    ].filter(part => part && part.trim() !== '');
    const pincode = organization.pincode && organization.pincode.trim() !== '' ? ` - ${organization.pincode}` : '';

    const address = addressParts.join(', ') + pincode;
      const currentDate = new Date();
      const formattedDate = `${currentDate.getFullYear()}-${String(currentDate.getMonth() + 1).padStart(2, "0")}-${String(currentDate.getDate()).padStart(2, "0")} 
                            ${String(currentDate.getHours()).padStart(2, "0")}:${String(currentDate.getMinutes()).padStart(2, "0")}`;
      documentInOut['created'] = data?.created;
      for (let userData of uniqueUsersList) {
        await sendnewMail({
          id: userData?.id,
          key: 'DOCUMENT_IN_OUT_MAIL',
          email: userData?.email,
          data: {
            adminName: userData?.fullName,
            documentInOut: documentInOut,
            formattedDate: formattedDate,
            userId: userData?.id,
            adress: address,
                  phoneNumber: organization?.mobileNumber,
                  mail: organization?.email,
                  legalName: organization?.tradeName || organization?.legalName

          },
          filePath: 'docu-in-out-general',
          subject: (documentInOut?.documentType === "out" && data?.created) ? "Document Outward" : (documentInOut?.documentType === "in" && data?.created) ? 'Document Inward Create' : documentInOut?.documentType === "in" ? "Document Inward Update" : documentInOut?.documentType === "out" ? "Document Outward Update" : "Document Outward Update",
        });
        // Send WhatsApp notification for Users
        try {
          if (userData?.id !== undefined) {
            const sessionValidation = await ViderWhatsappSessions.findOne({
              where: { userId: userData.id, status: 'ACTIVE' },
            });

            if (sessionValidation) {
              const adminUserDetails = await getUserDetails(userData.id);
              const {
                full_name: userFullName,
                mobile_number: userPhoneNumber,
                id,
                organization_id,
              } = adminUserDetails;

              const key = 'DOCUMENT_IN_OUT_WHATSAPP';

              const whatsappMessageBody = `Hi ${userFullName}, 
        
Your document ${documentInOut?.documentType === 'out' ? 'outward' : 'inward'} operation has been ${data?.created ? 'recorded' : 'updated'} successfully.
${documentInOut?.documentType === "out" ? documentInOut.receivedTo : documentInOut.receivedBy?.map(item => item.fullName).join(", ")} ${data?.created ? "recorded" : "updated"}
Documents ${documentInOut?.documentType === "in" ? "Inward" : "Outward"} Entry with ${documentInOut?.documentId} 
on ${moment().utcOffset(330).format("YYYY-MM-DD HH:mm")} related to ${documentInOut.client ? documentInOut.client?.displayName : documentInOut.clientGroup?.displayName},
was subsequently handed over to ${documentInOut.givenBy?.map(item => item.fullName).join(", ")}${documentInOut?.keptAtName ? `
and is currently kept at ${documentInOut?.keptAtName}` : ""}. The list of document(s) include: ${documentInOut?.documentData?.map((item, idx) => `${idx + 1}. ${item?.documentCategory?.name ? item?.documentCategory?.name : item?.documentName}`).join(" | ")}.
             
Thanks, Team`;

              await sendWhatsAppTextMessage(
                `91${userPhoneNumber}`,
                whatsappMessageBody,
                organization_id,
                title,
                id,
                key
              );
            }
          }
        } catch (error) {
          console.error('Error sending User WhatsApp notification:', error);
        }
      }
      if (data?.mailCheck && (documentInOut?.client || documentInOut?.clientGroup)) {
        const orgPreferences: any = await OrganizationPreferences.findOne({
          where: { organization: orgId },
        });
        const clientPreferences = orgPreferences?.clientPreferences?.email;
        let key = 'DOCUMENT_IN_OUT_MAIL';
        if (clientPreferences && clientPreferences[key]) {
          const clientMail = documentInOut?.client ? documentInOut?.client?.email : documentInOut?.clientGroup?.email;

          const allMails = documentInOut?.mailData?.['mails'].map((item: any) => item.email);

          const mails = [clientMail, ...allMails];



          for (let mail of mails) {
            const mailOptions = {
              id: user?.id,
              key: 'DOCUMENT_IN_OUT_MAIL',
              email: mail,
              clientMail: 'ORGANIZATION_CLIENT_EMAIL',
              data: {
                adminName: documentInOut?.client ? documentInOut?.client?.displayName : documentInOut?.clientGroup?.displayName,
                documentInOut: documentInOut,
                formattedDate: formattedDate,
                userId: user?.id,
                legalName: orgdetails?.tradeName || orgdetails?.legalName,
                adress: address,
                phoneNumber: orgdetails?.mobileNumber,
                mail: orgdetails?.email,
              },
              filePath: 'docu-in-out-general',
              subject: (documentInOut?.documentType === "out" && data?.created) ? "Document Outward" : (documentInOut?.documentType === "in" && data?.created) ? 'Document Inward Create' : documentInOut?.documentType === "in" ? "Document Inward Update" : documentInOut?.documentType === "out" ? "Document Outward Update" : "Document Outward Update",
            };
            await sendnewMail(mailOptions);
          }
        }
      }
      if (data?.whatsappCheck && (documentInOut?.client || documentInOut?.clientGroup)) {
        const templateName = 'document_inout_client';
        const dateTime = moment().utcOffset(330).format("YYYY-MM-DD HH:mm")
        const receiver = documentInOut?.documentType === "out"
          ? documentInOut.receivedTo
          : documentInOut.receivedBy?.map(item => item.fullName).join(", ");
        const inOut = documentInOut?.documentType === "in" ? "Inward" : "Outward";
        const docId = documentInOut?.documentId;
        const clientOrGroup = documentInOut?.client
          ? documentInOut.client?.displayName
          : (documentInOut.clientGroup?.displayName || "our Organization");
        const handoverTo = documentInOut.givenBy?.map(item => item.fullName).join(", ");
        const keptAt = documentInOut?.keptAtName || "";
        const docList = documentInOut?.documentData
          ?.map((item, index) => {
            const name = item?.documentCategory?.name || item?.documentName || "Unnamed Document";
            return `${index + 1}. ${name}`;
          })
          .join(" | ");
        const orgPreferences: any = await OrganizationPreferences.findOne({
          where: { organization: orgId },
        });
        const clientPreferences = orgPreferences?.clientPreferences?.email;
        const key = 'DOCUMENT_IN_OUT_MAIL';
        if (clientPreferences && clientPreferences[key]) {
          const clientMobileNumber = documentInOut?.client ? documentInOut?.client?.mobileNumber : documentInOut?.clientGroup?.mobileNumber;
          const allMobileNumbers = documentInOut?.mailData?.['whatsapp'].map((item: any) => item.number);

          const mobileNumbers = [clientMobileNumber, ...allMobileNumbers]


          try {
            for (let mobileNumber of mobileNumbers) {
              const whatsappOptions = {
                to: mobileNumber,
                name: templateName,
                header: [
                  {
                    type: 'text',
                    text: clientOrGroup,
                  },
                ],
                body: [
                  receiver,
                  inOut,
                  docId,
                  dateTime,
                  clientOrGroup,
                  handoverTo,
                  keptAt,
                  docList
                ],
                title: 'document_inout_client',
                userId: user.id,
                orgId: user.organization.id,
                key: 'DOCUMENT_IN_OUT_WHATSAPP',
              };
              await sendClientWhatsAppTemplateMessage(whatsappOptions);

            }
          } catch {
            console.log('cannot send whatsapp document-in-out message to client')
          }
        }
      }
    }
  }

  // async update(id: number, data: CreateLeadDto, userId: number) {
  //   const lead = await Lead.findOne({ where: { id } });
  //   if (data.name.toLowerCase() !== lead.name.toLowerCase()) {
  //     let user = await User.findOne({
  //       where: { id: userId },
  //       relations: ['organization'],
  //     });

  //     data.name = data.name.trim();
  //     const status = "CONVERTED"
  //     if (data.status !== status.toLowerCase()) {
  //       let existingService = await Lead.findOne({
  //         where: {
  //           name: data.name, organization: user.organization
  //         },
  //       });
  //       if (existingService) {
  //         throw new ConflictException(
  //           'Lead with given display name already exists in the organization',
  //         );
  //       }

  //       let existingUser = await createQueryBuilder(Client, 'client')
  //         .leftJoin('client.organization', 'organization')
  //         .where('organization.id = :organization', { organization: user.organization.id })
  //         .andWhere('(client.displayName = :displayName)', {
  //           displayName: data.name,
  //         })
  //         .getOne();
  //       if (existingUser) {
  //         throw new ConflictException(
  //           'Lead | Client with the given Display Name already Exists in your Organization',
  //         );
  //       }
  //     }
  //   }
  //   lead.name = data.name;
  //   lead.email = data.email.trim();
  //   lead.mobileNumber = data.mobileNumber;
  //   lead.category = data.category;
  //   lead.subCategory = data.subCategory;
  //   lead.description = data.description.trim();
  //   lead.status = data.status;
  //   lead.countryCode = data.countryCode;
  //   lead['userId'] = userId;
  //   await lead.save();
  //   return lead;
  // }

  async delete(id: number, userId, query: any) {
    let user = await User.findOne({
      where: { id: userId },
      relations: ['organization'],
    });

    let documentInOut = await createQueryBuilder(DocumentInOut, 'documentInOut')
      .leftJoinAndSelect('documentInOut.client', 'client')
      .leftJoinAndSelect('documentInOut.clientGroup', 'clientGroup')
      .leftJoinAndSelect('documentInOut.documentData', 'documentData')
      .leftJoinAndSelect('documentData.documentCategory', 'documentCategory')
      .leftJoinAndSelect('documentInOut.receivedBy', 'receivedBy')
      .leftJoinAndSelect('documentInOut.givenBy', 'givenBy')
      .leftJoinAndSelect('documentInOut.keptAt', 'keptAt')
      .leftJoinAndSelect('documentInOut.task', 'task')
      .leftJoin('documentInOut.organization', 'organization')
      .where('documentInOut.id = :id', { id })
      .andWhere('organization.id = :organizationId', { organizationId: user.organization.id })
      .getOne();

    const usersList = [
      ...documentInOut.givenBy.map(item => item.id),
      ...documentInOut.receivedBy.map(item => item.id)
    ];
    const usersLists = [
      ...documentInOut.givenBy,
      ...documentInOut.receivedBy
    ];

    const users: any = [...new Set(usersList)];
    const uniqueUsersList = [...new Map(usersLists.map(item => [item.id, item])).values()];
    const orgId = user?.organization?.id;
    const loggedInUserDetails = await getUserDetails(userId)
    const { full_name: loggedInUser } = loggedInUserDetails
    if (documentInOut?.useType === "kyb") {
      const title = documentInOut?.documentType === "in" ? "Document Inward Delete" : "Document Outward Delete";
      const body = `<strong>${documentInOut?.documentType === "out" ? documentInOut.receivedTo : documentInOut.receivedBy?.map(item => item.fullName).join(",")} deleted </strong> Documents ${documentInOut?.documentType === "in" ? "Inward" : "Outward"} Entry with <strong>${documentInOut?.documentId}</strong> on <strong>${documentInOut.updatedDateTime}</strong> related to KYB of <strong>${documentInOut.client ? documentInOut.client?.displayName : documentInOut.clientGroup?.displayName}</strong>, was subsequently handed over to <strong>${documentInOut.givenBy?.map(item => item.fullName).join(",")}</strong> ${documentInOut?.keptAtName ? `and is currently kept at <strong>${documentInOut?.keptAtName}</strong>` : ""}. <box>The list of document(s) include:
  <box>
        <ol>
          ${documentInOut?.documentData?.map(item => `<li>${item?.kyb?.documentName}</li>`).join("")}
        </ol>
  </box></box>
  `;
      const key = 'DOCUMENT_IN_OUT_PUSH';
      insertINTONotificationUpdate(title, body, users, orgId, key);
      const organization = await Organization.findOne({ id: orgId });


    const addressParts = [
      organization.buildingNo || '',
      organization.floorNumber || '',
      organization.buildingName || '',
      organization.street || '',
      organization.location || '',
      organization.city || '',
      organization.district || '',
      organization.state || ''
    ].filter(part => part && part.trim() !== '');
    const pincode = organization.pincode && organization.pincode.trim() !== '' ? ` - ${organization.pincode}` : '';

    const address = addressParts.join(', ') + pincode;
      const currentDate = new Date();
      const formattedDate = `${String(currentDate.getDate()).padStart(2, "0")}-${String(currentDate.getMonth() + 1).padStart(2, "0")}-${currentDate.getFullYear()} ${String(currentDate.getHours()).padStart(2, "0")}:${String(currentDate.getMinutes()).padStart(2, "0")}`;
      documentInOut['deleted'] = true;
      for (let userData of uniqueUsersList) {
        await sendnewMail({
          id: userData?.id,
          key: 'DOCUMENT_IN_OUT_MAIL',
          email: userData?.email,
          data: {
            adminName: userData?.fullName,
            documentInOut: documentInOut,
            formattedDate: formattedDate,
            userId: userData?.id,
            adress: address,
                  phoneNumber: organization?.mobileNumber,
                  mail: organization?.email,
                  legalName: organization?.tradeName || organization?.legalName
          },
          filePath: 'docu-in-out-kyb',
          subject: documentInOut?.documentType === "in" ? "Document Inward Delete" : "Document Outward Delete",
        });
        //whatsapp for users for deleted
        try {
          if (userData?.id !== undefined) {
            const sessionValidation = await ViderWhatsappSessions.findOne({
              where: { userId: userData.id, status: 'ACTIVE' },
            });

            if (sessionValidation) {
              const adminUserDetails = await getUserDetails(userData.id);
              const {
                full_name: userFullName,
                mobile_number: userPhoneNumber,
                id,
                organization_id,
              } = adminUserDetails;

              const key = 'DOCUMENT_IN_OUT_WHATSAPP';

              const whatsappMessageBody =

                `Hi ${userFullName},
The ${documentInOut?.documentType === 'out' ? 'outward' : 'inward'} document entry has been *deleted* successfully.
${loggedInUser} deleted Documents ${documentInOut?.documentType === "in" ? "Inward" : "Outward"} Entry with ID ${documentInOut?.documentId} on ${documentInOut.updatedDateTime} related to KYB of ${documentInOut.client ? documentInOut.client?.displayName : documentInOut.clientGroup?.displayName},
and it was subsequently handed over to ${documentInOut.givenBy?.map(item => item.fullName).join(", ")}${documentInOut?.keptAtName ? ` and is currently kept at ${documentInOut.keptAtName}` : ""}.
The list of document(s) include:

${documentInOut?.documentData
                  ?.map((item, idx) => {
                    const name = item?.kyb?.documentName || item?.documentCategory?.name || item?.documentName || "Unnamed Document";
                    return `${idx + 1}. ${name}`;
                  })
                  .join(" | ")}

Thanks,  
Team`;


              await sendWhatsAppTextMessage(
                `91${userPhoneNumber}`,
                whatsappMessageBody,
                organization_id,
                title,
                id,
                key
              );
            }
          }
        } catch (error) {
          console.error('Error sending User WhatsApp notification:', error);
        }

      }
      if ((documentInOut?.client || documentInOut?.clientGroup) && (query.emailCheck === "true")) {
        const key = 'DOCUMENT_IN_OUT_MAIL';
        const orgPreferences: any = await OrganizationPreferences.findOne({
          where: { organization: orgId },
        });
        const orgdetails = await Organization.findOne({ id: orgId });
        const addressParts = [
          orgdetails.buildingNo || '',
          orgdetails.floorNumber || '',
          orgdetails.buildingName || '',
          orgdetails.street || '',
          orgdetails.location || '',
          orgdetails.city || '',
          orgdetails.district || '',
          orgdetails.state || ''
        ].filter(part => part && part.trim() !== '');
        const pincode = orgdetails.pincode && orgdetails.pincode.trim() !== '' ? ` - ${orgdetails.pincode}` : '';
    
          const address = addressParts.join(', ') + pincode;
        const clientPreferences = orgPreferences?.clientPreferences?.email;
        if (clientPreferences && clientPreferences[key]) {
          const clientMail = documentInOut?.client ? documentInOut?.client?.email : documentInOut?.clientGroup?.email;
          const allMails = documentInOut?.mailData?.['mails'].map((item: any) => item.email);
          const mails = [clientMail, ...allMails];
          for (let mail of mails) {
            const mailOptions = {
              id: user?.id,
              key: 'DOCUMENT_IN_OUT_MAIL',
              email: mail,
              clientMail: 'ORGANIZATION_CLIENT_EMAIL',
              data: {
                adminName: documentInOut?.client ? documentInOut?.client?.displayName : documentInOut?.clientGroup?.displayName,
                documentInOut: documentInOut,
                formattedDate: formattedDate,
                userId: user?.id,
                tradeName: orgdetails?.tradeName,
                legalName: orgdetails?.tradeName || orgdetails?.legalName,
                adress: address,
                phoneNumber: orgdetails?.mobileNumber,
                mail: orgdetails?.email,
              },
              filePath: 'docu-in-out-kyb',
              subject: documentInOut?.documentType === "in" ? "Document Inward Delete" : "Document Outward Delete",
            };
            await sendnewMail(mailOptions);
          }
        }
      }
    } else if (documentInOut?.useType === "task") {
      const title = documentInOut?.documentType === "in" ? "Document Inward Delete" : "Document Outward Delete";
      const body = `<strong>${documentInOut?.documentType === "out" ? documentInOut.receivedTo : documentInOut.receivedBy?.map(item => item.fullName).join(",")} deleted</strong> Documents ${documentInOut?.documentType === "in" ? "Inward" : "Outward"} Entry with <strong>${documentInOut?.documentId}</strong> on <strong>${documentInOut.updatedDateTime}</strong> related to <strong>${documentInOut.client ? documentInOut.client?.displayName : documentInOut.clientGroup?.displayName}</strong>, Task Id <strong>${documentInOut?.task?.['taskNumber']}</strong>, and Task Name <strong>${documentInOut?.task?.['name']}</strong> related to KYB of <strong>${documentInOut.client ? documentInOut.client?.displayName : documentInOut.clientGroup?.displayName}</strong>, was subsequently handed over to <strong>${documentInOut.givenBy?.map(item => item.fullName).join(",")}</strong> ${documentInOut?.keptAtName ? `and is currently kept at <strong>${documentInOut?.keptAtName}</strong>` : ""}. <box>The list of document(s) include:
<box>
      <ol>
        ${documentInOut?.documentData?.map(item => `<li>${item?.documentCategory?.name ? item?.documentCategory?.name : item?.documentName}</li>`).join("")}
      </ol>
</box></box>
`;
      const key = 'DOCUMENT_IN_OUT_PUSH';
      insertINTONotificationUpdate(title, body, users, orgId, key);
      const organization = await Organization.findOne({ id: orgId });


      const addressParts = [
        organization.buildingNo || '',
        organization.floorNumber || '',
        organization.buildingName || '',
        organization.street || '',
        organization.location || '',
        organization.city || '',
        organization.district || '',
        organization.state || ''
      ].filter(part => part && part.trim() !== '');
      const pincode = organization.pincode && organization.pincode.trim() !== '' ? ` - ${organization.pincode}` : '';
  
      const address = addressParts.join(', ') + pincode;
      const currentDate = new Date();
      const formattedDate = `${String(currentDate.getDate()).padStart(2, "0")}-${String(currentDate.getMonth() + 1).padStart(2, "0")}-${currentDate.getFullYear()} ${String(currentDate.getHours()).padStart(2, "0")}:${String(currentDate.getMinutes()).padStart(2, "0")}`;
      documentInOut['deleted'] = true;
      for (let userData of uniqueUsersList) {
        await sendnewMail({
          id: userData?.id,
          key: 'DOCUMENT_IN_OUT_MAIL',
          email: userData?.email,
          data: {
            adminName: userData?.fullName,
            documentInOut: documentInOut,
            formattedDate: formattedDate,
            userId: userData?.id,
            adress: address,
                  phoneNumber: organization?.mobileNumber,
                  mail: organization?.email,
                  legalName: organization?.tradeName || organization?.legalName
          },
          filePath: 'docu-in-out-task',
          subject: documentInOut?.documentType === "in" ? "Document Inward Delete" : "Document Outward Delete",
        });
        //whatsapp for users for deleted
        try {
          if (userData?.id !== undefined) {
            const sessionValidation = await ViderWhatsappSessions.findOne({
              where: { userId: userData.id, status: 'ACTIVE' },
            });

            if (sessionValidation) {
              const adminUserDetails = await getUserDetails(userData.id);
              const {
                full_name: userFullName,
                mobile_number: userPhoneNumber,
                id,
                organization_id,
              } = adminUserDetails;

              const key = 'DOCUMENT_IN_OUT_WHATSAPP';

              const whatsappMessageBody =
                `Hi ${userFullName},

The ${documentInOut?.documentType === 'out' ? 'outward' : 'inward'} document entry has been *deleted* successfully.
${loggedInUser} deleted Documents ${documentInOut?.documentType === "in" ? "Inward" : "Outward"} Entry with ID ${documentInOut?.documentId} on ${documentInOut.updatedDateTime} related to KYB of ${documentInOut.client ? documentInOut.client?.displayName : documentInOut.clientGroup?.displayName},
Task Id ${documentInOut?.task?.['taskNumber']}, and Task Name ${documentInOut?.task?.['name']} and it was subsequently handed over to ${documentInOut.givenBy?.map(item => item.fullName).join(", ")}${documentInOut?.keptAtName ? `
and is currently kept at ${documentInOut.keptAtName}` : ""}.

The list of document(s) include:

${documentInOut?.documentData
                  ?.map((item, idx) => {
                    const name = item?.kyb?.documentName || item?.documentCategory?.name || item?.documentName || "Unnamed Document";
                    return `${idx + 1}. ${name}`;
                  })
                  .join(" | ")}
Thanks,  
Team`;


              await sendWhatsAppTextMessage(
                `91${userPhoneNumber}`,
                whatsappMessageBody,
                organization_id,
                title,
                id,
                key
              );
            }
          }
        } catch (error) {
          console.error('Error sending User WhatsApp notification:', error);
        }
      }
      if ((documentInOut?.client || documentInOut?.clientGroup) && (query.emailCheck === "true")) {
        const key = 'DOCUMENT_IN_OUT_MAIL';
        const orgPreferences: any = await OrganizationPreferences.findOne({
          where: { organization: orgId },
        });
        const orgdetails = await Organization.findOne({ id: orgId });
        const addressParts = [
          orgdetails.buildingNo || '',
          orgdetails.floorNumber || '',
          orgdetails.buildingName || '',
          orgdetails.street || '',
          orgdetails.location || '',
          orgdetails.city || '',
          orgdetails.district || '',
          orgdetails.state || ''
        ].filter(part => part && part.trim() !== '');
        const pincode = orgdetails.pincode && orgdetails.pincode.trim() !== '' ? ` - ${orgdetails.pincode}` : '';
    
          const address = addressParts.join(', ') + pincode;

        const clientPreferences = orgPreferences?.clientPreferences?.email;
        if (clientPreferences && clientPreferences[key]) {
          const clientMail = documentInOut?.client ? documentInOut?.client?.email : documentInOut?.clientGroup?.email;
          const allMails = documentInOut?.mailData?.['mails'].map((item: any) => item.email);
          const mails = [clientMail, ...allMails];
          for (let mail of mails) {
            const mailOptions = {
              id: user?.id,
              key: 'DOCUMENT_IN_OUT_MAIL',
              email: mail,
              clientMail: 'ORGANIZATION_CLIENT_EMAIL',
              data: {
                adminName: documentInOut?.client ? documentInOut?.client?.displayName : documentInOut?.clientGroup?.displayName,
                documentInOut: documentInOut,
                formattedDate: formattedDate,
                userId: user?.id,
                legalName: orgdetails?.tradeName || orgdetails?.legalName,
                adress: address,
                phoneNumber: orgdetails?.mobileNumber,
                mail: orgdetails?.email,
              },
              filePath: 'docu-in-out-task',
              subject: documentInOut?.documentType === "in" ? "Document Inward Delete" : "Document Outward Delete",
            };
            await sendnewMail(mailOptions);
          }
        }
      }

    } else if (documentInOut?.useType === "general") {
      const title = documentInOut?.documentType === "in" ? "Document Inward Delete" : "Document Outward Delete";
      const body = `<strong>${documentInOut?.documentType === "out" ? documentInOut.receivedTo : documentInOut.receivedBy?.map(item => item.fullName).join(",")}</strong> deleted Documents ${documentInOut?.documentType === "in" ? "Inward" : "Outward"} Entry with <strong>${documentInOut?.documentId}</strong> on <strong>${moment().utcOffset(330).format("YYYY-MM-DD HH:mm")}</strong> related to <strong>${documentInOut.client ? documentInOut.client?.displayName : documentInOut.clientGroup?.displayName ? documentInOut.clientGroup?.displayName : "our Organization"}</strong> was subsequently handed over to <strong>${documentInOut.givenBy?.map(item => item.fullName).join(",")}</strong> ${documentInOut?.keptAtName ? `and is currently kept at <strong>${documentInOut?.keptAtName}</strong>` : ""}. <box>The list of document(s) include:
<box>
      <ol>
        ${documentInOut?.documentData?.map(item => `<li>${item?.documentCategory?.name ? item?.documentCategory?.name : item?.documentName}</li>`).join("")}
      </ol>
</box></box>
`;
      const key = 'DOCUMENT_IN_OUT_PUSH';
      insertINTONotificationUpdate(title, body, users, orgId, key);
      const organization = await Organization.findOne({ id: orgId });


    const addressParts = [
      organization.buildingNo || '',
      organization.floorNumber || '',
      organization.buildingName || '',
      organization.street || '',
      organization.location || '',
      organization.city || '',
      organization.district || '',
      organization.state || ''
    ].filter(part => part && part.trim() !== '');
    const pincode = organization.pincode && organization.pincode.trim() !== '' ? ` - ${organization.pincode}` : '';

    const address = addressParts.join(', ') + pincode;
      const currentDate = new Date();
      const formattedDate = `${String(currentDate.getDate()).padStart(2, "0")}-${String(currentDate.getMonth() + 1).padStart(2, "0")}-${currentDate.getFullYear()} ${String(currentDate.getHours()).padStart(2, "0")}:${String(currentDate.getMinutes()).padStart(2, "0")}`;
      documentInOut['deleted'] = true;
      for (let userData of uniqueUsersList) {
        await sendnewMail({
          id: userData?.id,
          key: 'DOCUMENT_IN_OUT_MAIL',
          email: userData?.email,
          data: {
            adminName: userData?.fullName,
            documentInOut: documentInOut,
            formattedDate: formattedDate,
            userId: userData?.id,
            adress: address,
                  phoneNumber: organization?.mobileNumber,
                  mail: organization?.email,
                  legalName: organization?.tradeName || organization?.legalName
          },
          filePath: 'docu-in-out-general',
          subject: documentInOut?.documentType === "in" ? "Document Inward Delete" : "Document Outward Delete",
        });
        //whatsapp for users for deleted
        try {
          if (userData?.id !== undefined) {
            const sessionValidation = await ViderWhatsappSessions.findOne({
              where: { userId: userData.id, status: 'ACTIVE' },
            });

            if (sessionValidation) {
              const adminUserDetails = await getUserDetails(userData.id);
              const {
                full_name: userFullName,
                mobile_number: userPhoneNumber,
                id,
                organization_id,
              } = adminUserDetails;

              const key = 'DOCUMENT_IN_OUT_WHATSAPP';

              const whatsappMessageBody =
                `Hi ${userFullName},
The ${documentInOut?.documentType === 'out' ? 'outward' : 'inward'} document entry has been *deleted* successfully.
${loggedInUser} deleted Documents ${documentInOut?.documentType === "in" ? "Inward" : "Outward"} Entry with ID ${documentInOut?.documentId} on ${documentInOut.updatedDateTime}
related to KYB of ${documentInOut.client ? documentInOut.client?.displayName : documentInOut.clientGroup?.displayName}, and it was subsequently handed over to ${documentInOut.givenBy?.map(item => item.fullName).join(", ")}${documentInOut?.keptAtName ? ` and is currently kept at ${documentInOut.keptAtName}` : ""}.
The list of document(s) include:

${documentInOut?.documentData
                  ?.map((item, idx) => {
                    const name = item?.kyb?.documentName || item?.documentCategory?.name || item?.documentName || "Unnamed Document";
                    return `${idx + 1}. ${name}`;
                  })
                  .join(" | ")}
Thanks,  
Team`;


              await sendWhatsAppTextMessage(
                `91${userPhoneNumber}`,
                whatsappMessageBody,
                organization_id,
                title,
                id,
                key
              );
            }
          }
        } catch (error) {
          console.error('Error sending User WhatsApp notification:', error);
        }
      }
      if ((documentInOut?.client || documentInOut?.clientGroup) && (query.emailCheck === "true")) {
        const key = 'DOCUMENT_IN_OUT_MAIL';
        const orgPreferences: any = await OrganizationPreferences.findOne({
          where: { organization: orgId },
        });
        const orgdetails = await Organization.findOne({ id: orgId });
        const addressParts = [
          orgdetails.buildingNo || '',
          orgdetails.floorNumber || '',
          orgdetails.buildingName || '',
          orgdetails.street || '',
          orgdetails.location || '',
          orgdetails.city || '',
          orgdetails.district || '',
          orgdetails.state || ''
        ].filter(part => part && part.trim() !== '');
        const pincode = orgdetails.pincode && orgdetails.pincode.trim() !== '' ? ` - ${orgdetails.pincode}` : '';
    
          const address = addressParts.join(', ') + pincode;

        const clientPreferences = orgPreferences?.clientPreferences?.email;
        if (clientPreferences && clientPreferences[key]) {
          const clientMail = documentInOut?.client ? documentInOut?.client?.email : documentInOut?.clientGroup?.email;
          const allMails = documentInOut?.mailData?.['mails'].map((item: any) => item.email);
          const mails = [clientMail, ...allMails];
          for (let mail of mails) {
            const mailOptions = {
              id: user?.id,
              key: 'DOCUMENT_IN_OUT_MAIL',
              email: mail,
              clientMail: 'ORGANIZATION_CLIENT_EMAIL',
              data: {
                adminName: documentInOut?.client ? documentInOut?.client?.displayName : documentInOut?.clientGroup?.displayName,
                documentInOut: documentInOut,
                formattedDate: formattedDate,
                userId: user?.id,
                legalName: orgdetails?.tradeName || orgdetails?.legalName,
                adress: address,
                phoneNumber: orgdetails?.mobileNumber,
                mail: orgdetails?.email,
              },
              filePath: 'docu-in-out-general',
              subject: documentInOut?.documentType === "in" ? "Document Inward Delete" : "Document Outward Delete",
            };
            await sendnewMail(mailOptions);
          }
        }
      }
    }
    for (let i of documentInOut.documentData) {
      if (documentInOut.useType === 'kyb') {
        documentInOut?.documentData?.map(async item => await item?.kyb?.remove());
      }
      documentInOut.documentData.map(async item => await item.remove());
    }

    await documentInOut.remove();

    return { success: true };
  }

  async saveAttachments(taskId: number, docId: number, files: Express.Multer.File[], userId: number) {
    return this.attachementService.saveAttachments(taskId, files, userId, docId)
  };
}
