import { Logger, QueryRunner } from "typeorm";
import axios from 'axios';
import { InternalServerErrorException } from "@nestjs/common";

// const fs = require('fs');

export class MyCustomLogger implements Logger {
    logQueryError(error: string | Error, query: string, parameters?: any[], queryRunner?: QueryRunner) {
        // throw new Error("Method not implemented.");
        console.log("Method not implemented.");
    }
    logQuerySlow(time: number, query: string, parameters?: any[], queryRunner?: QueryRunner) {
        // throw new Error("Method not implemented.");
        console.log("Method not implemented.");
    }
    logSchemaBuild(message: string, queryRunner?: QueryRunner) {
        // throw new Error("Method not implemented.");
        console.log("Method not implemented.");
    }
    logMigration(message: string, queryRunner?: QueryRunner) {
        // throw new Error("Method not implemented.");
        console.log("Method not implemented.");
    }
    log(level: "warn" | "info" | "log", message: any, queryRunner?: QueryRunner) {
        // throw new Error("Method not implemented.");
        console.log("Method not implemented.");
    }
    // implement all methods from logger class
    logQuery(query: string, parameters?: any[], queryRunner?: QueryRunner) {
        const requestUrl = queryRunner && queryRunner.data["request"] ? "(" + queryRunner.data["request"].url + ") " : "";
        // console.log(requestUrl + "executing query: " + query);

        // var printlog = new Date() + "---START------>" + requestUrl + "executing query: " + query + "<------END\n";
        // fs.writeFile("./logs.txt", JSON.stringify(printlog),  {'flag':'a+'}, (err: any) => {
        //     if(err) {
        //         return console.log(err);
        //     }
        // });
        // const requestUrl = queryRunner && queryRunner.data["request"] ? "(" + queryRunner.data["request"].url + ") " : "";
        // console.log(requestUrl + "executing query: " + query);

        // TODO: uncomment if sql record to save in espo
        // try {
        //     axios({
        //         url: 'http://vidersupport.com/espo/crm/api/v1/Queries',
        //         method: 'POST',
        //         headers: {
        //             'X-Api-Key': 'cb7397dc8250e64516602f5894f7bf5f',
        //         },
        //         data: {
        //             assignedUserId: "1",
        //             assignedUserName: "Admin",
        //             name: "sql",
        //             query: "executedQuery::" + query,
        //             teamsIds: [],
        //             teamsNames: {}
        //         }
        //     });

        // } catch (error) {
        //     throw new InternalServerErrorException(error);
        // }
    }
}