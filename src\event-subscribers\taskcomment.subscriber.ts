import {
  Connection,
  EntitySubscriberInterface,
  EventSubscriber,
  InsertEvent,
  getManager,
} from 'typeorm';
import TaskComment from 'src/modules/tasks/entity/comment.entity';
import {
  getUserDetails,
  insertINTONotificationUpdate,
  insertINTOnotification,
} from 'src/utils/re-use';
import { getUserIDs } from 'src/utils/re-use';
import { sendnewMail } from 'src/emails/newemails';
import ViderWhatsappSessions from 'src/modules/whatsapp/entity/viderWhatsappSessions';
import { sendWhatsAppTextMessage } from 'src/modules/whatsapp/whatsapp.service';
import { fullMobileNumberWithCountry } from 'src/utils/validations/fullMobileWithCountry';
import { Organization } from 'src/modules/organization/entities/organization.entity';

@EventSubscriber()
export class TaskcommentSubscriber implements EntitySubscriberInterface<TaskComment> {
  constructor(private readonly connection: Connection) {
    connection.subscribers.push(this);
  }

  listenTo() {
    return TaskComment;
  }

  async beforeInsert(event: InsertEvent<TaskComment>) { }

  async afterInsert(event: InsertEvent<TaskComment>) {
    const entityManager = getManager();
    const { task, user, text } = event.entity;
    const taskName = event.entity?.task?.name;
    const taskId = task?.id;
    const userName = user?.fullName;
    const userId: any = [user?.id];
    const userEmails = [user?.email];
    if (taskId) {
      const getTaskQuery = `SELECT client_id, client_group_id FROM task where id = ${taskId};`;
      let getTask = await entityManager.query(getTaskQuery);
      const clientId = getTask[0]?.client_id;
      const getClientQuery = `SELECT display_name, organization_id,client_id FROM client where id = ${clientId};`;
      let getClient = await entityManager.query(getClientQuery);
      const clientName = getClient[0]?.display_name;
      const clientGroupId = getTask[0]?.client_group_id;
      const getClientGroupQuery = `SELECT display_name, organization_id,client_id FROM client_group where id = ${clientGroupId};`;
      let getClientGroup = await entityManager.query(getClientGroupQuery);
      const clientGroupName = getClientGroup[0]?.display_name;
      const clientNumber = getClient[0]?.client_id;
      const orgId = getClient[0]?.organization_id ? getClient[0]?.organization_id : getClientGroup[0]?.organization_id;
      const organization = await Organization.findOne({ id: orgId });


      const addressParts = [
        organization.buildingNo || '',
        organization.floorNumber || '',
        organization.buildingName || '',
        organization.street || '',
        organization.location || '',
        organization.city || '',
        organization.district || '',
        organization.state || ''
      ].filter(part => part && part.trim() !== '');
      const pincode = organization.pincode && organization.pincode.trim() !== '' ? ` - ${organization.pincode}` : '';

      const address = addressParts.join(', ') + pincode;
      const taskMemberIds = await getUserIDs(taskId);
      const userDetailsList = [];
      for (let i of taskMemberIds) {
        const userDetailsQuery = `SELECT * FROM user WHERE id=${i}`;
        const userDetails = await entityManager.query(userDetailsQuery);
        userDetailsList.push(userDetails);
      }
      for (let i of userDetailsList) {
        await sendnewMail({
          id: i[0]?.id,
          key: 'COMMENT_ADDED_MAIL',
          email: i[0]?.email,
          data: {
            memberName: i[0]?.full_name,
            taskName: taskName,
            userName: userName,
            clientName: clientName,
            comment: text,
            userId: userId,
            adress: address,
            phoneNumber: organization?.mobileNumber,
            mail: organization?.email,
            legalName: organization?.tradeName || organization?.legalName
          },
          filePath: 'comment-added',
          subject: 'Comment Added | Vider',
        });
      }
      const body = `<strong>${userName}</strong> has commented on "<strong>${taskName}</strong>" of <strong>${clientName ? clientName : clientGroupName}</strong>`;
      const title = 'Task Comments';
      const key = 'TASK_COMMENTS_PUSH';
      // insertINTOnotification(title, body, userId, orgId);
      insertINTONotificationUpdate(title, body, taskMemberIds, orgId, key, taskId, clientNumber);
      const taskMemberIdss = await getUserIDs(taskId);

      // whatsapp
      try {
        if (taskMemberIdss !== undefined) {
          for (let userId of taskMemberIdss) {
            const sessionValidation = await ViderWhatsappSessions.findOne({
              where: { userId: userId, status: 'ACTIVE' },
            });
            if (sessionValidation) {
              const loggedinuserDetails = await getUserDetails(userId);
              const {
                full_name: userFullName,
                mobile_number: userPhoneNumber,
                country_code: countryCode,
              } = loggedinuserDetails;
              const userWhatsAppNumber = fullMobileNumberWithCountry(userPhoneNumber, countryCode);
              const key = 'TASK_COMMENTS_WHATSAPP';
              const whatsappMessageBody = `
  Hi ${userFullName},
  There's a new comment on the task: ${taskName} for client ${clientName}.
  ${userName} commented: "${text}"

  Task details:            
  Task ID: ${taskId}
  Task Name: ${taskName}
  Task Due Date: ${task?.dueDate}
  Client Name: ${clientName} `;
              await sendWhatsAppTextMessage(
                // `91${userPhoneNumber}`,
                userWhatsAppNumber,
                whatsappMessageBody,
                orgId,
                title,
                loggedinuserDetails?.id,
                key,
              );
            }
          }
        }
      } catch (error) {
        console.error('Error sending Client WhatsApp notification:', error);
      }
    }
  }
}
