import Client from 'src/modules/clients/entity/client.entity';
import { Organization } from 'src/modules/organization/entities/organization.entity';
import Storage from 'src/modules/storage/storage.entity';
import { User } from 'src/modules/users/entities/user.entity';
import {
  BaseEntity,
  Column,
  CreateDateColumn,
  Entity,
  JoinColumn,
  JoinTable,
  ManyToMany,
  ManyToOne,
  OneToMany,
  OneToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import BroadcastActivity from './broadcast-activity.entity';
@Entity()
class BroadcastActivityDetails extends BaseEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  clientName: string;

  @Column()
  email: string;
  
  @Column()
  broadcastActivityId: number;

  @CreateDateColumn()
  createdAt: string;

  @UpdateDateColumn()
  updatedAt: string;
  
  @ManyToOne(() => BroadcastActivity, (broadcastActivity) => broadcastActivity.details)
  broadcastActivity: BroadcastActivity;

}

export default BroadcastActivityDetails;
