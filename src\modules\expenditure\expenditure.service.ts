import {
  BadRequestException,
  ConflictException,
  Injectable,
  InternalServerErrorException,
} from '@nestjs/common';
import { User } from 'src/modules/users/entities/user.entity';
import { Brackets, createQ<PERSON>y<PERSON>uilder, getManager, In } from 'typeorm';
import Client from '../clients/entity/client.entity';
import Task from '../tasks/entity/task.entity';
import AddExpenditureDto from './dto/add-expenditure.dto';
import FindExpenditureDto, { FindExpenditureQueryType } from './dto/find-expenditure.dto';
import { EXPENDITURE_STATUS, ExpenditureStatus, ExpenditureType } from './dto/types';
import Expenditure from './expenditure.entity';
import exp from 'constants';
import { StorageService } from '../storage/storage.service';
import Storage, { StorageSystem } from '../storage/storage.entity';
import Activity, { ActivityType } from '../activity/activity.entity';
import { Event_Actions } from 'src/event-listeners/actions';
import { AwsService } from '../storage/upload.service';
import { OneDriveStorageService } from '../ondrive-storage/onedrive-storage.service';
import { BharathCloudService } from '../storage/bharath-upload.service';
import * as xlsx from 'xlsx';
import * as ExcelJS from 'exceljs';
import * as moment from 'moment';
import { dateFormation } from 'src/utils/datesFormation';
import { getExpenseType } from 'src/utils/re-use';
import { Permissions } from '../events/permission';
import { GoogleDriveStorageService } from '../ondrive-storage/googledrive-storage.service';

@Injectable()
export class ExpenditureService {

  constructor(
    private storageService: StorageService,
    private awsService: AwsService,
    private bharathService: BharathCloudService,
    private oneDriveService: OneDriveStorageService,
    private googleDriveService: GoogleDriveStorageService,
  ) { }
  async add(userId: number, data: AddExpenditureDto) {
    try {
      let storage: Storage;
      let user = await User.findOne({
        where: { id: userId },
        relations: ['organization', 'organization.organizationPreferences'],
      });
      let expenditure = new Expenditure();
      expenditure.particularName = data.particularName.trim();
      expenditure.date = data.date;
      expenditure.amount = data.amount;
      expenditure.type = data.type;
      expenditure.includeInInvoice = data.includeInInvoice;
      expenditure.attachment = data.attachment;
      expenditure.remarks = data.remarks;
      expenditure.user = user;
      if (data.storage) {
        storage = await this.storageService.addAttachements(userId, data.storage);
      }

      if (data.type === ExpenditureType.TASK) {
        let task = await Task.findOne({
          where: { id: data.task },
          relations: ['client', 'clientGroup'],
        });
        expenditure.task = task;
        expenditure.client = task.client;
        expenditure.clientGroup = task.clientGroup;
        expenditure.taskExpenseType = data.taskExpenseType;
      }

      if (data.type === ExpenditureType.GENERAL && data.client) {
        let client = await Client.findOne({ where: { id: data.client } });
        expenditure.client = client;
      }

      if (user.organization.organizationPreferences?.[0]?.approvals?.['expenditure']) {
        const entityManager = getManager();

        let adminUser = await createQueryBuilder(User, 'user')
          .leftJoinAndSelect('user.organization', 'organization')
          .leftJoinAndSelect('user.role', 'role')
          .where('organization.id = :id', { id: user.organization.id })
          .andWhere('role.defaultRole = :defaultRole', { defaultRole: 1 })
          .getOne();
        const result = await entityManager.query(
          `
                WITH RECURSIVE manager_hierarchy AS (
                    -- Base case: Start with the given user_id
                    SELECT user_id, manager_id
                    FROM organization_hierarchy
                    WHERE user_id = ?
                    
                    UNION ALL
                    
                    -- Recursive case: Fetch the next level manager until manager_id = 123826
                    SELECT oh.user_id, oh.manager_id
                    FROM organization_hierarchy oh
                    INNER JOIN manager_hierarchy mh ON oh.user_id = mh.manager_id
                    WHERE mh.manager_id != ? -- Stop recursion when manager_id = 123826
                )
                -- Final output: Get all user_id in the hierarchy
                SELECT user_id
                FROM manager_hierarchy
            `,
          [userId, adminUser.id],
        );

        const mangers = result?.map((result) => result.user_id);
        mangers.push(adminUser.id);
        const filterReqUser = mangers.filter((user) => user != userId);
        const users = await User.find({
          where: {
            id: In(filterReqUser),
          },
        });
        if (userId != adminUser.id) {
          expenditure.approvalStatus = EXPENDITURE_STATUS.PENDING;
          expenditure.managers = users;
          expenditure.requestedAt = moment().format('YYYY-MM-DD HH:mm:ss.SSSSSS');
        }
      }
      expenditure['userId'] = userId;
      const ex = await expenditure.save();
      if (storage) {
        storage.expenditure = ex;
        await storage.save();
      }
      let taskactivity = new Activity();
      taskactivity.action = Event_Actions.EXPENDITURE_CREATED;
      taskactivity.actorId = userId;
      taskactivity.type = ActivityType.TASK;
      taskactivity.typeId = data.task;
      taskactivity.remarks = `Expenditure "${expenditure.particularName}" Created by ${user.fullName}`;
      await taskactivity.save();
      return true;
    } catch (e) {
      console.log(e);
      throw new InternalServerErrorException(e);
    }
  }

  async find(userId: number, query: FindExpenditureDto) {
    const { limit, offset } = query;

    let repo = createQueryBuilder(Expenditure, 'expenditure')
      .leftJoinAndSelect('expenditure.task', 'task')
      .leftJoinAndSelect('task.category', 'category')
      .leftJoinAndSelect('expenditure.client', 'client')
      .leftJoinAndSelect('expenditure.clientGroup', 'clientGroup')
      .leftJoinAndSelect('expenditure.user', 'user')
      .leftJoinAndSelect('user.imageStorage', 'imageStorage')
      .leftJoinAndSelect('expenditure.storage', 'storage')
      .orderBy('expenditure.createdAt', 'DESC');

    if (query.type === FindExpenditureQueryType.TASK) {
      repo.where('task.id = :taskId', { taskId: query.taskId });
    }

    if (query.type === FindExpenditureQueryType.USER) {
      repo.where('user.id = :userId', { userId });
    }

    if (query.type === FindExpenditureQueryType.SELF) {
      repo.where('user.id = :userId', { userId });
    }
    const sort = typeof query?.sort === 'string' ? JSON.parse(query.sort) : query?.sort;
    if (sort?.column) {
      const columnMap: Record<string, string> = {
        type: 'expenditure.type',
        date: 'expenditure.date',
        displayName: 'client.displayName',
        expensiveType: 'expenditure.taskExpenseType',
        taskNumber: 'task.taskNumber',
        name: 'task.name',
        particularName: 'expenditure.particularName',
        amount: 'expenditure.amount',
      };
      const column = columnMap[sort.column] || sort.column;
      repo.orderBy(column, sort.direction.toUpperCase());
    } else {
      repo.orderBy('expenditure.createdAt', 'DESC');
    }
    if (query.search) {
      repo.andWhere(
        '(expenditure.particularName LIKE :search OR task.name LIKE :search OR task.taskNumber LIKE :search OR client.displayName LIKE :search)',
        {
          search: `%${query.search}%`,
        },
      );
    }
    if (offset >= 0) {
      repo.skip(offset);
    }

    if (limit) {
      repo.take(limit);
    }

    let result = await repo.getManyAndCount();

    return {
      count: result[1],
      result: result[0],
    };

    // return await repo.getMany();
  }

  async export(userId: number, body: FindExpenditureDto) {
    const newQuery = { ...body, offset: 0, limit: 100000000 };
    let expenditures = await this.find(userId, newQuery);

    if (!expenditures.result.length) throw new BadRequestException('No Data for Export');

    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Expenditure');
    const headers = [
      { header: 'S.No', key: 'serialNo' },
      { header: 'Expenditure ID', key: 'expenditureNumber' },
      { header: 'Expense Nature', key: 'expenseNature' },
      { header: 'Expense Type', key: 'expenseType' },
      { header: 'Expense Date', key: 'date' },
      { header: 'Client / Client Group', key: 'client' },
      { header: 'Task ID', key: 'taskId' },
      { header: 'Task Name', key: 'taskName' },
      { header: 'Expense Title', key: 'expenseTitle' },
      { header: 'Amount', key: 'amount' },
      { header: 'Updated Date & Time', key: 'updatedAt' },
      { header: 'Status', key: 'status' },
      { header: 'Remarks', key: 'remarks' }
    ];

    worksheet.columns = headers;

    let serialCounter = 1; // Initialize a counter

    // Initialize array to store max lengths for each column
    const columnMaxLengths = Array(headers.length).fill(0);

    expenditures.result.forEach((expenditure) => {
      function capitalizeFirstLetter(string) {
        return string.charAt(0).toUpperCase() + string.slice(1);
      }
      const rowData = {
        serialNo: serialCounter++,
        expenditureNumber: expenditure?.expenditureNumber,
        expenseNature: capitalizeFirstLetter(expenditure?.type.toLowerCase()),
        expenseType: getExpenseType(expenditure?.taskExpenseType),
        date: expenditure?.date ? moment(expenditure.date).format('DD-MM-YYYY') : null,
        client: expenditure?.client
          ? expenditure?.client?.displayName
          : expenditure?.clientGroup?.displayName,
        taskId: expenditure?.task?.taskNumber,
        taskName: expenditure?.task?.name,
        expenseTitle: expenditure?.particularName,
        updatedAt: moment(expenditure?.updatedAt).format("DD-MM-YYYY HH:mm:ss"),
        amount: 1 * expenditure?.amount,
        status: capitalizeFirstLetter(expenditure?.status.toLowerCase()),
        remarks: expenditure?.remarks


      }


      const row = worksheet.addRow(rowData);
      const statusCell = row.getCell('status');
      switch (rowData.status?.toLowerCase()) {
        case 'pending':
          statusCell.font = { color: { argb: 'FFA500' }, bold: true }; // Orange
          break;
        case 'rejected':
          statusCell.font = { color: { argb: 'F63338' }, bold: true };
          break;
        case 'approved':
          statusCell.font = { color: { argb: '008000' }, bold: true };
          break;
        default:
          statusCell.font = { color: { argb: '000000' }, bold: true };
          break;
      }

      worksheet.columns.forEach((column, colIndex) => {
        const headerLength = column.header?.length || 0; // Length of header
        const cellLength = rowData[column.key]?.toString().length || 0; // Length of cell value
        columnMaxLengths[colIndex] = Math.max(
          columnMaxLengths[colIndex] || 0,
          headerLength,
          cellLength,
        );
      });
    });

    // Apply column widths with padding
    worksheet.columns.forEach((column, colIndex) => {
      column.width = columnMaxLengths[colIndex] + 3; // Add padding for readability
    });

    // Style the header row and center alignment
    const headerRow = worksheet.getRow(1);
    headerRow.font = { bold: true };
    headerRow.eachCell((cell) => {
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '64B5F6' },
      };
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' },
      };
      cell.alignment = { horizontal: 'center', vertical: 'middle' };
    });

    worksheet.columns.forEach((column) => {
      if (column.key === 'taskName' || column.key === 'members' || column.key === 'taskLeaders') {
        column.width = 50;
        column.alignment = { wrapText: true, horizontal: 'center', vertical: 'middle' };
      } else if (column.key === 'remarks') {
        column.width = 100;
        column.alignment = { wrapText: true, horizontal: 'center', vertical: 'middle' };
      } else {
        column.alignment = { horizontal: 'center', vertical: 'middle' };
      }
    });

    // Apply wrapText for all rows
    worksheet.eachRow((row) => {
      row.eachCell((cell) => {
        cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true }; // Center alignment and wrap text
      });
    });

    // Freeze the header row
    worksheet.views = [{ state: 'frozen', ySplit: 1 }];

    const buffer = await workbook.xlsx.writeBuffer();
    return buffer;
  }
  async findOrgExpenditure(userId: number, query: FindExpenditureDto) {
    const { limit, offset } = query;
    const user = await User.findOne({
      where: { id: userId },
      relations: ['organization', 'role'],
    });
    const ViewAll = user.role.permissions.some(
      (permission) => permission.slug === Permissions.VIEW_ALL_CLIENT_MANAGERS,
    );
    const ViewAssigned = user.role.permissions.some(
      (permission) => permission.slug === Permissions.VIEW_ASSIGNED_CLIENT_MANAGERS,
    );
    let repo = createQueryBuilder(Expenditure, 'expenditure')
      .select([
        'expenditure.id',
        'expenditure.type',
        'expenditure.date',
        'client.displayName',
        'expenditure.taskExpenseType',
        'expenditure.expenditureNumber',
        'expenditure.updatedAt',
        'task.taskNumber',
        'task.name',
        'expenditure.particularName',
        'expenditure.amount',
        'expenditure.createdAt',
        'clientGroup.displayName',
      ])
      .leftJoin('expenditure.task', 'task')
      .leftJoinAndSelect('expenditure.user', 'user')
      .leftJoin('user.organization', 'organization')
      .leftJoin('expenditure.client', 'client')
      .leftJoin('client.clientManagers', 'clientManagers')
      .leftJoin('expenditure.clientGroup', 'clientGroup')
      .leftJoin('clientGroup.clientGroupManagers', 'clientGroupManagers')
      .leftJoinAndSelect('expenditure.storage', 'storage')
      .leftJoinAndSelect('user.imageStorage', 'imageStorage')
      .where('organization.id = :org', { org: user.organization.id });
    if (query?.expenseType?.length) {
      repo.andWhere('expenditure.taskExpenseType in (:...expenseType)', {
        expenseType: query?.expenseType,
      });
    }

    if (!ViewAll && ViewAssigned) {
      repo.andWhere(
        new Brackets((qb) => {
          qb.where('client.id IS NOT NULL AND clientManagers.id = :userId', { userId })
            .orWhere('clientGroup.id IS NOT NULL AND clientGroupManagers.id = :userId', { userId })
            .orWhere('client.id IS NULL AND clientGroup.id IS NULL');
        }),
      );
    } else if (!ViewAll && !ViewAssigned) {
      repo.andWhere('client.id IS NULL AND clientGroup.id IS NULL');
    }

    if (query?.expenseNature?.length) {
      repo.andWhere('expenditure.type in (:...type)', { type: query?.expenseNature });
    }
    if (query?.users?.length) {
      repo.andWhere('user.id in (:...users)', { users: query?.users });
    }

    if (query.fromDate && query.toDate) {
      const { startTime, endTime } = dateFormation(query.fromDate, query.toDate);
      repo.andWhere('expenditure.date BETWEEN :startDate AND :endDate', {
        startDate: startTime,
        endDate: endTime,
      });
    }

    if (query.search) {
      repo.andWhere(
        '(expenditure.particularName LIKE :search OR task.name LIKE :search OR task.taskNumber LIKE :search OR client.displayName LIKE :search)',
        {
          search: `%${query.search}%`,
        },
      );
    }

    const sort = typeof query?.sort === 'string' ? JSON.parse(query.sort) : query?.sort;
    if (sort?.column) {
      const columnMap: Record<string, string> = {
        date: 'expenditure.date',
        amount: 'expenditure.amount',
        type: 'expenditure.type',
        displayName: 'client.displayName',
        taskNumber: 'tasl.taskNumber',
        name: 'task.name',
        particularName: 'expenditure.particularName',
      };
      const column = columnMap[sort.column] || sort.column;
      repo.orderBy(column, sort.direction.toUpperCase());
    } else {
      repo.orderBy('expenditure.createdAt', 'DESC');
    }

    if (offset >= 0) {
      repo.skip(offset);
    }

    if (limit) {
      repo.take(limit);
    }
    let result = await repo.getManyAndCount();
    return {
      count: result[1],
      result: result[0],
    };
  }

  async exportOrgExpenditure(userId: number, body: FindExpenditureDto) {
    const newQuery = { ...body, offset: 0, limit: 100000000 };
    let expenditures = await this.findOrgExpenditure(userId, newQuery);

    if (!expenditures?.result?.length) throw new BadRequestException('No Data for Export');

    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Expenditure');
    const headers = [
      { header: 'S.No', key: 'serialNo' },
      { header: 'Expenditure ID', key: 'expenditureNumber' },
      { header: 'Expense Nature', key: 'expenseNature' },
      { header: 'Expense Date', key: 'date' },
      { header: 'Client', key: 'client' },
      { header: 'TASK ID', key: 'taskId' },
      { header: 'TASK NAME', key: 'taskName' },
      { header: 'Expense Type', key: 'expenseType' },
      { header: 'Expense Title', key: 'expenseTitle' },
      { header: 'Amount (₹)', key: 'amount' },
      { header: 'Employee', key: 'employee' },
      { header: 'Updated Date & Time', key: 'updatedAt' },
      { header: ' Remarks', key: 'remarks' }


    ]
    worksheet.columns = headers;
    let serialCounter = 1; // Initialize a counter
    // Initialize array to store max lengths for each column
    const columnMaxLengths = Array(headers.length).fill(0);

    expenditures?.result?.forEach((expenditure) => {
      const date = new Date(expenditure.user.updatedAt);
      function capitalizeFirstLetter(string) {
        return string.charAt(0).toUpperCase() + string.slice(1);
      }

      const rowData = {
        serialNo: serialCounter++,
        expenditureNumber: expenditure?.expenditureNumber,
        expenseNature: capitalizeFirstLetter(expenditure?.type.toLowerCase()),
        date: expenditure?.date ? moment(expenditure.date).format('DD-MM-YYYY') : null,
        client: expenditure?.client?.displayName,
        taskId: expenditure?.task?.taskNumber,
        taskName: expenditure?.task?.name,
        expenseType: getExpenseType(expenditure?.taskExpenseType),
        expenseTitle: expenditure?.particularName,
        amount: 1 * expenditure?.amount,
        employee: expenditure?.user?.fullName,
        updatedAt: moment(expenditure?.updatedAt).format("DD-MM-YYYY HH:mm:ss"),
        remarks: expenditure?.remarks

      }

      const row = worksheet.addRow(rowData);
      const expenseNatureCell = row.getCell('expenseNature'); // Get the cell for the "Type" column

      if (rowData.expenseNature === 'General') {
        expenseNatureCell.font = {
          color: { argb: 'FF800000' }, // ARGB for Maroon
          bold: true, // Bold text
        };
        expenseNatureCell.value = 'General'; // Add text
      } else if (rowData.expenseNature === 'Task') {
        expenseNatureCell.font = {
          color: { argb: 'FF008000' }, // ARGB for Green
          bold: true, // Bold text
        };
        expenseNatureCell.value = 'Task'; // Add text
      }
      worksheet.columns.forEach((column, colIndex) => {
        const headerLength = column.header?.length || 0; // Length of header
        const cellLength = rowData[column.key]?.toString().length || 0; // Length of cell value
        columnMaxLengths[colIndex] = Math.max(
          columnMaxLengths[colIndex] || 0,
          headerLength,
          cellLength,
        );
      });
    });

    // Apply column widths with padding
    worksheet.columns.forEach((column, colIndex) => {
      column.width = columnMaxLengths[colIndex] + 3; // Add padding for readability
    });

    // Style the header row and center alignment
    const headerRow = worksheet.getRow(1);
    headerRow.font = { bold: true };
    headerRow.eachCell((cell) => {
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '64B5F6' },
      };
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' },
      };
      cell.alignment = { horizontal: 'center', vertical: 'middle' };
    });

    worksheet.columns.forEach((column) => {
      if (column.key === 'client' || column.key === 'taskName' || column.key === 'employee') {
        column.width = 50;
        column.alignment = { wrapText: true, horizontal: 'center', vertical: 'middle' };
      } else if (column.key === 'remarks') {
        column.width = 100;
        column.alignment = { wrapText: true, horizontal: 'center', vertical: 'middle' };
      } else {
        column.alignment = { horizontal: 'center', vertical: 'middle' };
      }
    });

    // Apply wrapText for all rows
    worksheet.eachRow((row) => {
      row.eachCell((cell) => {
        cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true }; // Center alignment and wrap text
      });
    });

    // Freeze the header row
    worksheet.views = [{ state: 'frozen', ySplit: 1 }];

    const buffer = await workbook.xlsx.writeBuffer();
    return buffer;
  }
  async update(id: number, userId: number, data: Omit<AddExpenditureDto, 'taskId'>) {
    try {
      let user = await User.findOne({
        where: { id: userId },
        relations: ['imageStorage', 'organization'],
      });
      let expenditure = await Expenditure.findOne({
        where: { id: id },
        relations: ['storage', 'user', 'managers'],
      });
      expenditure.particularName = data.particularName.trim();
      expenditure.date = data.date;
      expenditure.amount = data.amount;
      expenditure.taskExpenseType = data.taskExpenseType;
      expenditure.includeInInvoice = data.includeInInvoice;
      expenditure.attachment = data.attachment;
      expenditure.remarks = data?.remarks;
      let storage: Storage;
      if (data?.storage) {
        if (expenditure?.storage?.id) {
          if (data?.storage.name !== expenditure?.storage?.name) {
            if (expenditure?.storage.storageSystem === StorageSystem.AMAZON) {
              this.storageService.deleteAwsFile(expenditure?.storage?.file);
            } else if (expenditure?.storage.storageSystem === StorageSystem.MICROSOFT) {
              this.oneDriveService.deleteOneDriveFile(userId, expenditure?.storage?.fileId);
            } else if (expenditure?.storage.storageSystem === StorageSystem.BHARATHCLOUD) {
              this.bharathService.deleteFile(user.organization.id, expenditure?.storage.file);
            }
          }

          storage = await Storage.findOne({ where: { id: expenditure?.storage?.id } });
          storage.fileType = data?.storage.fileType;
          storage.fileSize = data?.storage.fileSize;
          storage.name = data?.storage.name;
          storage.file = data?.storage.upload;
          storage.show = data?.storage.show;
          storage.storageSystem = data?.storage.storageSystem;
          storage.webUrl = data?.storage?.webUrl;
          storage.downloadUrl = data?.storage?.downloadUrl;
          storage.fileId = data?.storage?.fileId;
          storage.authId = data?.storage?.authId;
          storage.filePath = data?.storage?.filePath;
          // expenditure.storage = storage;
        } else {
          storage = await this.storageService.addAttachements(userId, data?.storage);
          // expenditure.storage = storage;
        }
      } else {
        if (expenditure?.storage?.id) {
          const existingStorage = await Storage.findOne({
            where: { id: expenditure?.storage?.id },
          });
          const removeStorage = await existingStorage.remove();
          expenditure.storage = null;
          if (removeStorage) {
            if (removeStorage.storageSystem === StorageSystem.MICROSOFT) {
              this.oneDriveService.deleteOneDriveFile(userId, removeStorage.fileId);
            } else if (removeStorage.storageSystem === StorageSystem.AMAZON) {
              this.storageService.deleteAwsFile(removeStorage.file);
            } else if (removeStorage.storageSystem === StorageSystem.BHARATHCLOUD) {
              this.bharathService.deleteFile(user.organization.id, removeStorage.file);
            }
          }
        }
      }
      expenditure['userId'] = userId;
      if (data.approvalStatus == ExpenditureStatus.REJECTED) {
        expenditure.reviewer = null;
        expenditure.approvalStatus = EXPENDITURE_STATUS.PENDING;
        expenditure.reviewedAt = null;
        expenditure.requestedAt = moment().format('YYYY-MM-DD HH:mm:ss.SSSSSS');
      }
      const ex = await expenditure.save();
      if (storage) {
        storage.expenditure = ex;
        await storage.save();
      }

      let taskactivity = new Activity();
      taskactivity.action = Event_Actions.EXPENDITURE_UPDATED;
      taskactivity.actorId = userId;
      taskactivity.type = ActivityType.TASK;
      taskactivity.typeId = data.task['id'];
      taskactivity.remarks = `Expenditure "${expenditure.particularName}" Updated by ${user.fullName}`;
      await taskactivity.save();
    } catch (err) {
      console.log(err);
      throw new InternalServerErrorException(err);
    }
  }

  async edit(id: number, data: AddExpenditureDto, userId: number) {
    let user = await User.findOne({ where: { id: userId }, relations: ['organization'] });

    let expenditure = await Expenditure.findOne({
      where: { id: id },
      relations: ['storage', 'managers'],
    });
    expenditure.particularName = data.particularName.trim();
    expenditure.amount = data.amount;
    expenditure.date = data.date;
    expenditure.type = data.type;
    expenditure.includeInInvoice = data.includeInInvoice;
    expenditure.attachment = data.attachment;
    expenditure.remarks = data?.remarks;
    let storage: Storage;
    if (data?.storage) {
      if (expenditure?.storage?.id) {
        if (data.storage.name !== expenditure.storage.name) {
          if (expenditure.storage.storageSystem === StorageSystem.AMAZON) {
            this.awsService.deleteFile(expenditure.storage.file);
          } else if (expenditure.storage.storageSystem === StorageSystem.MICROSOFT) {
            this.oneDriveService.deleteOneDriveFile(userId, expenditure.storage.fileId);
          } else if (expenditure.storage.storageSystem === StorageSystem.BHARATHCLOUD) {
            this.bharathService.deleteFile(user.organization.id, expenditure.storage.file);
          }
        }
        storage = await Storage.findOne({ where: { id: expenditure?.storage?.id } });
        storage.fileType = data?.storage.fileType;
        storage.fileSize = data?.storage.fileSize;
        storage.name = data?.storage.name;
        storage.file = data?.storage.upload;
        storage.show = data?.storage.show;
        storage.downloadUrl = data?.storage.downloadUrl;
        storage.fileId = data?.storage.fileId;
        storage.storageSystem = data?.storage.storageSystem;
        storage.webUrl = data?.storage.webUrl;
        storage.authId = user.organization.id;
        storage.filePath = data?.storage.filePath;
      } else {
        storage = await this.storageService.addAttachements(userId, data?.storage);
        // expenditure.storage = storage;
      }
    } else {
      if (expenditure?.storage?.id) {
        const existingStorage = await Storage.findOne({ where: { id: expenditure?.storage?.id } });
        await existingStorage.remove();
        if (existingStorage) {
          if (existingStorage.storageSystem === StorageSystem.AMAZON) {
            this.awsService.deleteFile(expenditure.storage.file);
          } else if (existingStorage.storageSystem === StorageSystem.MICROSOFT) {
            this.oneDriveService.deleteOneDriveFile(userId, expenditure.storage.fileId);
          } else if (existingStorage.storageSystem === StorageSystem.BHARATHCLOUD) {
            this.bharathService.deleteFile(user.organization.id, expenditure.storage.file);
          }
        }
        expenditure.storage = null;
      }
    }
    expenditure.user = user;
    if (data.type === ExpenditureType.TASK) {
      let task = await Task.findOne({
        where: { id: data.task },
        relations: ['client', 'clientGroup'],
      });
      expenditure.task = task;
      expenditure.client = task.client;
      expenditure.clientGroup = task.clientGroup;
      expenditure.taskExpenseType = data.taskExpenseType;
    }

    if (data.type === ExpenditureType.GENERAL) {
      let client = await Client.findOne({ where: { id: data.client } });
      expenditure.client = client;
    }
    expenditure['userId'] = userId;
    if (data.approvalStatus == ExpenditureStatus.REJECTED) {
      expenditure.reviewer = null;
      expenditure.approvalStatus = EXPENDITURE_STATUS.PENDING;
      expenditure.reviewedAt = null;
      expenditure.requestedAt = moment().format('YYYY-MM-DD HH:mm:ss.SSSSSS');
    }
    const ex = await expenditure.save();
    if (storage) {
      storage.expenditure = ex;
      await storage.save();
    }

    if (data.type === ExpenditureType.TASK) {
      let taskactivity = new Activity();
      taskactivity.action = Event_Actions.EXPENDITURE_UPDATED;
      taskactivity.actorId = userId;
      taskactivity.type = ActivityType.TASK;
      taskactivity.typeId = data.task;
      taskactivity.remarks = `Expenditure "${expenditure.particularName}" Updated by ${user.fullName}`;
      await taskactivity.save();
    }
  }

  async approve(id: number, userId: number) {
    let expenditure = await Expenditure.findOne(id);
    expenditure.status = ExpenditureStatus.APPROVED;
    expenditure['userId'] = userId;
    await expenditure.save();
    return expenditure;
  }

  async reject(id: number, body: { reason: string }, userId: number) {
    let expenditure = await Expenditure.findOne(id);
    expenditure.status = ExpenditureStatus.REJECTED;
    expenditure.rejectedReason = body.reason;
    expenditure['userId'] = userId;
    await expenditure.save();
    return expenditure;
  }

  async delete(id: number, userId: number, query) {
    let expenditure = await Expenditure.findOne({ where: { id }, relations: ['storage'] });
    let fileItem: string;
    let storageType: string;

    if (expenditure?.storage) {
      storageType = expenditure.storage.storageSystem;
      if (storageType === StorageSystem.AMAZON || storageType === StorageSystem.BHARATHCLOUD) {
        fileItem = expenditure.storage.file;
      } else if (storageType === StorageSystem.MICROSOFT) {
        fileItem = expenditure.storage.fileId;
      } else if (storageType === StorageSystem.GOOGLE) {
        fileItem = expenditure.storage.fileId;
      }
    }

    let user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
    expenditure['userId'] = userId;
    await expenditure.remove();
    const deleteFileActions = {
      [StorageSystem.AMAZON]: () => this.awsService.deleteFile(fileItem),
      [StorageSystem.BHARATHCLOUD]: () =>
        this.bharathService.deleteFile(user.organization.id, fileItem),
      [StorageSystem.MICROSOFT]: () => this.oneDriveService.deleteOneDriveFile(userId, fileItem),
      [StorageSystem.GOOGLE]: () => this.googleDriveService.deleteGoogleDriveFile(userId, fileItem),
    };

    if (fileItem && deleteFileActions[storageType]) {
      await deleteFileActions[storageType]();
    }

    let taskactivity = new Activity();
    taskactivity.action = Event_Actions.EXPENDITURE_DELETED;
    taskactivity.actorId = userId;
    taskactivity.type = ActivityType.TASK;
    taskactivity.typeId = query.taskId;
    taskactivity.remarks = `Expenditure "${expenditure.particularName}" Updated by ${user.fullName}`;
    await taskactivity.save();
  }

  async approvalsStatusChange(userId: number, body: any) {
    const user = await User.findOne(userId);
    const attendances = await Expenditure.find({
      where: {
        id: In(body.expenditureIds),
        approvalStatus: ExpenditureStatus.REJECTED,
      },
    });
    if (attendances.length) {
      throw new ConflictException("Can't Approve Declined Requests");
    }
    const attendancesApproved = await Expenditure.find({
      where: {
        id: In(body.expenditureIds),
        approvalStatus: ExpenditureStatus.APPROVED,
      },
    });

    if (attendancesApproved.length) {
      throw new ConflictException("Can't Re Approved");
    }
    const expenditureRecords = await Expenditure.find({
      where: { id: In(body.expenditureIds) },
      relations: ['managers', 'user', 'client', 'clientGroup', 'task'],
    });

    expenditureRecords.forEach((record) => {
      record.approvalStatus = body?.status;
      record.reviewer = user;
      record.reviewedAt = moment().format('YYYY-MM-DD HH:mm:ss.SSSSSS');
      record.approvalDescription = body.description;
      record["userId"] = userId
    });
    await Expenditure.save(expenditureRecords);

    return true;
  }

  async bulkDelete(userId: number, ids: number[],) {
    const user = await User.findOne({
      where: { id: userId },
      relations: ['organization'],
    });
    if (!user) throw new Error('User not found');

    const expenditures = await Expenditure.find({
      where: {
        id: In(ids),
        user: { organization: { id: user.organization.id } },
      },
      relations: ['storage', 'task', 'user'],
    });

    for (const ex of expenditures) {
      if (ex.approvalStatus == EXPENDITURE_STATUS.APPROVED) {
        throw new BadRequestException("You can't delete Approved Expenditures");
      }
    }

    for (const expenditure of expenditures) {
      let fileItem: string | undefined;
      const storageType = expenditure.storage?.storageSystem;

      // Determine the correct file identifier
      if (expenditure.storage) {
        if (
          storageType === StorageSystem.AMAZON ||
          storageType === StorageSystem.BHARATHCLOUD
        ) {
          fileItem = expenditure.storage.file;
        } else if (
          storageType === StorageSystem.MICROSOFT ||
          storageType === StorageSystem.GOOGLE
        ) {
          fileItem = expenditure.storage.fileId;
        }
      }

      // Delete from respective storage
      const deleteFileActions = {
        [StorageSystem.AMAZON]: () => this.awsService.deleteFile(fileItem),
        [StorageSystem.BHARATHCLOUD]: () =>
          this.bharathService.deleteFile(user.organization.id, fileItem),
        [StorageSystem.MICROSOFT]: () => this.oneDriveService.deleteOneDriveFile(userId, fileItem),
        [StorageSystem.GOOGLE]: () => this.googleDriveService.deleteGoogleDriveFile(userId, fileItem),
      };

      if (fileItem && deleteFileActions[storageType]) {
        try {
          await deleteFileActions[storageType]();
        } catch (err) {
          console.warn(`File deletion failed for expenditure ${expenditure.id}:`, err.message);
        }
      }

      // Delete the expenditure
      await expenditure.remove();

      // Log activity only if taskId is present
      if (expenditure?.task?.id) {
        const activity = new Activity();
        activity.action = Event_Actions.EXPENDITURE_DELETED;
        activity.actorId = userId;
        activity.type = ActivityType.TASK;
        activity.typeId = expenditure.task?.id;
        activity.remarks = `Expenditure "${expenditure.particularName}" deleted by ${user.fullName}`;
        await activity.save();
      }
    }

    return {
      success: true
    };
  }


}
