import { BaseEntity, Column, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, OneToOne, PrimaryGeneratedColumn } from 'typeorm';
import GstrCredentials from './gstrCredentials.entity';

@Entity()
class GstrProfile extends BaseEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  gstin: string;

  @Column()
  tradeName: string;

  @Column()
  legalName: string;

  @Column()
  centreJurisdiction: string;

  @Column()
  stateJurisdiction: string;

  @Column()
  registerDate: string;

  @Column()
  status: string;

  @Column()
  constitutionOfBusiness: string;

  @Column()
  taxType: string;

  @Column()
  clientId: number;

  @Column()
  organizationId: number;


  @OneToOne(() => GstrCredentials, (gstrCredentials) => gstrCredentials.profile)
  @JoinColumn()
  gstrCredentials: GstrCredentials;
}

export default GstrProfile;
