import { MigrationInterface, QueryRunner } from 'typeorm';

export class AlterOrganizationTable1663140821794 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
        ALTER TABLE organization
        MODIFY COLUMN category enum('INDIVIDUAL', 'COMPANY', 'FIRM','TRUST','AOP') default 'COMPANY' not null;
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}
