import { BadRequestException, Injectable, InternalServerErrorException } from '@nestjs/common';

import * as nodemailer from 'nodemailer';
import { User } from '../users/entities/user.entity';
import ClientGroupBroadcast from './entity/client-group-broadcast.entity';
import { Brackets, createQuery<PERSON>uilder, getConnection, Not } from 'typeorm';
import BroadcastEmailTemplates from './entity/broadcast-email-templates-entity';
import BroadcastActivity, { Status } from './entity/broadcast-activity.entity';
import { Cron, CronExpression } from '@nestjs/schedule';
import { sendMailViaAny } from 'src/emails/newemails';
import { AwsService } from '../storage/upload.service';
import BroadcastActivityDetails from './entity/broadcast-activity-details.entity';
import Label from '../labels/label.entity';
import Client from '../clients/entity/client.entity';

let transporter: any = nodemailer.createTransport({
  host: 'email-smtp.ap-south-1.amazonaws.com',
  port: 587,
  auth: {
    user: 'AKIA5GHOVJDTRJ3PAQ6E',
    pass: 'BFt/gc++ytmTt24jK/317ARm7RQPk9eS12ThV1hZ5Jgc',
  },
});
export enum BroadcastMessageTo {
  CLIENT = 'CLIENT',
  CLIENTGROUP = 'CLIENTGROUP',
}
@Injectable()
export class CommunicationService {
  constructor(private awsService: AwsService) { }
  async sendEmailTemplatetoclients(data: any) {
    // if (process.env.Cron_Running === 'true') {

    try {
      let subject = `Email template`;

      let mailOptions = {
        from: {
          name: 'Vider',
          address: '<EMAIL>',
        },
        to: data.emails,
        subject: data?.subject,
        html: data.content,
      };

      transporter.sendMail(mailOptions, function (error: any, info: any) {
        if (error) {
          console.log(error);
          return error;
        } else {
          console.log(info.response);
          return info.response;
        }
      });
    } catch (error) {
      console.log(error);
    }
    // }
  }



  @Cron(CronExpression.EVERY_5_MINUTES)
  async cronEmailTemplatetoclients() {
    if (process.env.Cron_Running === 'true') {
      const broadcastActivities = await BroadcastActivity.createQueryBuilder('broadcastActivity')
        .leftJoinAndSelect('broadcastActivity.details', 'details')
        .leftJoinAndSelect('broadcastActivity.user', 'user')
        .leftJoinAndSelect('broadcastActivity.template', 'template')
        .where('broadcastActivity.status = :status', { status: 'READY' })
        .getMany();
      for (const br of broadcastActivities) {
        const { template, details } = br;

        let hasError = false;
        let errorMessage = '';
        br.Status = Status.PROCESSING
        await br.save();

        for (const detail of details) {

          await new Promise((resolve: any) => {
            setTimeout(async () => {
              let mailOptions = {
                from: {
                  name: 'Vider',
                  address: process.env.FROM_EMAIL,
                },
                to: detail?.email,
                subject: template?.subject,
                html: template?.content,
                userId: br?.user?.id,
                fromTemplate: true,
              };

              try {
                await sendMailViaAny(mailOptions);
                console.log(`Email sent to ${detail.email} for broadcast activity ID: ${br.id}`);
              } catch (error) {
                console.log(`Failed to send email to ${detail.email} for broadcast activity ID: ${br.id}`, error);
                hasError = true;
              }

              resolve();
            }, 10000);
          });
          if (hasError) break;
        }

        // Update the status of the broadcastActivity
        br.Status = hasError ? Status.ERROR : Status.SENT;
        br.errorMessage = hasError ? errorMessage : null;
        await br.save();
      }
    }
  }



  async createClientGroup(data: any, userId) {
    let user = await User.findOne({
      where: { id: userId },
      relations: ['organization'],
    });
    // Check if the groupName already exists within the organization
    const existingGroup = await ClientGroupBroadcast.findOne({
      where: {
        groupName: data.groupName,
        organization: { id: user.organization.id }, // Ensure it's within the same organization
      },
    });

    // If the group name exists, throw an error
    if (existingGroup) {
      throw new BadRequestException('GroupName already exists in your organization');
    }
    let newClientGroupBroadcast = new ClientGroupBroadcast();
    newClientGroupBroadcast.groupName = data.groupName;
    if (data?.tags) {
      newClientGroupBroadcast.label = data.tags;
    }
    // Only set description if it is provided
    if (data?.description) {
      newClientGroupBroadcast.description = data.description;
    }
    newClientGroupBroadcast.clients = data?.users;
    newClientGroupBroadcast.organization = user.organization;

    await newClientGroupBroadcast.save();
    return newClientGroupBroadcast;
  }

  //   async findClientGroup(userId, query: any) {
  //     let user = await User.findOne({
  //       where: { id: userId },
  //       relations: ['organization'],
  //     });
  //     const clientGroupData = await ClientGroupBroadcast.createQueryBuilder('clientGroupBroadcast')
  //       .leftJoinAndSelect(
  //         'clientGroupBroadcast.clients',
  //         'client',
  //         'client.status != :deletedStatus',
  //         { deletedStatus: 'DELETED' },
  //       )
  //       .leftJoinAndSelect('clientGroupBroadcast.organization', 'organization')
  //       .leftJoinAndSelect('clientGroupBroadcast.label', 'label')

  //       .where('organization.id = :organizationId', { organizationId: user.organization.id })
  //       .orderBy('clientGroupBroadcast.createdAt', 'DESC');

  //     // .groupBy('clientGroupBroadcast.id')
  //     if (query.search) {
  //       clientGroupData.andWhere('clientGroupBroadcast.groupName LIKE :search', {
  //         search: `%${query.search}%`,
  //       });
  //     }
  // const result = await clientGroupData.getMany()


  //     return result;
  //   }

  async findClientGroup(userId, query: any) {
    let user = await User.findOne({
      where: { id: userId },
      relations: ['organization'],
    });

    const clientGroupData = await ClientGroupBroadcast.createQueryBuilder('clientGroupBroadcast')
      .leftJoin(
        'clientGroupBroadcast.clients',
        'client',
        'client.status != :deletedStatus',
        { deletedStatus: 'DELETED' },
      )
      .leftJoin('clientGroupBroadcast.organization', 'organization')
      .addSelect('organization.id')
      .leftJoinAndSelect('clientGroupBroadcast.label', 'label')
      .where('organization.id = :organizationId', { organizationId: user.organization.id })
      .addSelect('COUNT(client.id)', 'clientCount') // Select the count of clients
      .groupBy('clientGroupBroadcast.id') // Group by clientGroupBroadcast ID to aggregate
      .orderBy('clientGroupBroadcast.createdAt', 'DESC');

    if (query.search) {
      clientGroupData.andWhere('clientGroupBroadcast.groupName LIKE :search', {
        search: `%${query.search}%`,
      });
    }

    const result = await clientGroupData.getRawAndEntities(); // Retrieve both raw data and entities

    // Combine client count with the clientGroupBroadcast entities
    const formattedResult = result.entities.map((group, index) => ({
      ...group,
      clientCount: parseInt(result.raw[index].clientCount, 10),
    }));

    return formattedResult;
  }


  async deleteClientGroup(ids: number, userId) {
    let clienGroup = await ClientGroupBroadcast.delete(ids);
    return clienGroup;
  }

  async updateClientGroup(userId, id, body) {
    // Find the user and their organization
    let user = await User.findOne({
      where: { id: userId },
      relations: ['organization'],
    });

    if (!user) {
      throw new Error('User not found');
    }

    // Find the ClientGroupBroadcast with the given id
    let clientGroupData = await ClientGroupBroadcast.findOne({
      where: { id: id },
      relations: ['organization', 'clients'],
    });

    if (!clientGroupData) {
      throw new Error('ClientGroupBroadcast not found');
    }

    // Check if the groupName already exists in the organization (excluding the current one)
    let existingGroup = await ClientGroupBroadcast.findOne({
      where: {
        groupName: body.groupName,
        organization: { id: user.organization.id },
        id: Not(id), // Exclude the current group from the check
      },
    });

    if (existingGroup) {
      throw new BadRequestException('Group name already exists in this organization');
    }

    // Update the ClientGroupBroadcast with the new data
    clientGroupData.groupName = body.groupName;
    clientGroupData.label = body.label;
    clientGroupData.description = body?.description;
    clientGroupData.organization = user.organization;

    // Save the updated ClientGroupBroadcast
    await clientGroupData.save();

    return clientGroupData;
  }

  async addClientstoClientGroup(userId, id, body) {
    let clientGroupData = await ClientGroupBroadcast.findOne({
      where: { id: id },
      relations: ['organization', 'clients'],
    });


    clientGroupData.clients = [...clientGroupData.clients, ...body.clients];

    await clientGroupData.save();

    return clientGroupData;
  }

  async removeClientsFromClientGroup(userId, id, body) {
    const ids = body.clients.map(item => item.id);

    let user = await User.findOne({
      where: { id: userId },
      relations: ['organization'],
    });

    if (!user) {
      throw new Error('User not found');
    }

    // Find the ClientGroupBroadcast with the given id
    let clientGroupData = await ClientGroupBroadcast.findOne({
      where: { id: id },
      relations: ['organization', 'clients'],
    });

    const clients = clientGroupData.clients.filter(item => !ids.includes(item.id));


    clientGroupData.clients = clients;

    if (!clientGroupData.clients.length) {
      throw new BadRequestException('Atleast one client is mandatory');

    }

    await clientGroupData.save();

    return clientGroupData;

  }

  async findOneClientGroup(userId, id, query) {
    const { limit, offset } = query;
    // Fetch the user with their organization relation
    let user = await User.findOne({
      where: { id: userId },
      relations: ['organization'],
    });
    let clientGroupData = ClientGroupBroadcast.createQueryBuilder('clientGroupBroadcast')
      .leftJoinAndSelect('clientGroupBroadcast.clients', 'client')
      .leftJoinAndSelect('clientGroupBroadcast.label', 'label')

      .leftJoinAndSelect('clientGroupBroadcast.organization', 'organization')

      .where('clientGroupBroadcast.id = :id', { id: id });

    const sort = (typeof query?.sort === "string") ? JSON.parse(query.sort) : query?.sort;
    if (sort?.column) {
      const columnMap: Record<string, string> = {
        clientId: 'client.clientId',
        clientNumber: 'client.clientNumber',
        displayName: 'client.displayName',
        category: 'client.category',
        active: 'client.active',

      };
      const column = columnMap[sort.column] || sort.column;
      clientGroupData.orderBy(column, sort.direction.toUpperCase());
    } else {
      clientGroupData.orderBy('clientGroupBroadcast.createdAt', 'DESC');
    };
    if (query.search) {
      clientGroupData.andWhere(
        '(client.displayName LIKE :search OR client.email LIKE :search)',
        { search: `%${query.search}%` }
      );
    }
    let clientGroup = await clientGroupData
      .getOne();


    const checkOrganization = await ClientGroupBroadcast.findOne({ where: { id }, relations: ["organization"] })
    const checkAccess = checkOrganization?.organization?.id === user?.organization?.id
    let clientCount = 0;
    if (clientGroup && clientGroup?.clients) {
      // Get the total count of clients before applying limit and offset
      clientCount = clientGroup?.clients?.length;

      // Apply limit and offset to the clients array
      const start = offset * limit ?? 0;
      const end = parseInt(limit) ? start + parseInt(limit) : clientCount;
      clientGroup.clients = clientGroup?.clients.slice(start, end);
    }

    return { clientGroup, clientCount, checkAccess };

    // return result;
  }


  async createEmailTemplate(data: any, userId: any) {
    let user = await User.findOne({
      where: { id: userId },
      relations: ['organization'],
    });
    const existingGroup = await BroadcastEmailTemplates.findOne({
      where: {
        title: data.title,
        organization: { id: user.organization.id }, // Ensure it's within the same organization
      },
    });

    // If the group name exists, throw an error
    if (existingGroup) {
      throw new BadRequestException('Email Title already exists in your organization');
    }
    let broadcastEmailTemplates = new BroadcastEmailTemplates();
    broadcastEmailTemplates.title = data.title;
    broadcastEmailTemplates.label = data.tags;
    broadcastEmailTemplates.content = data.content;
    broadcastEmailTemplates.subject = data.subject;

    broadcastEmailTemplates.organization = user.organization;
    // broadcastEmailTemplates.user = userId;

    await broadcastEmailTemplates.save();
    return broadcastEmailTemplates;
  }

  async getEmailTemplates(userId: string, query: any) {
    // Fetch the user and their organization
    let user = await User.findOne({
      where: { id: userId },
      relations: ['organization'],
    });

    // Fetch broadcast email templates for the user's organization
    const broadcastEmailTemplatesQuery = BroadcastEmailTemplates.createQueryBuilder(
      'broadcastEmailTemplates',
    )
      .leftJoin('broadcastEmailTemplates.organization', 'organization')
      .leftJoinAndSelect('broadcastEmailTemplates.label', 'label')
      .where(
        new Brackets(qb => {
          qb.where('organization.id = :organizationId', { organizationId: user.organization.id })
            .orWhere('broadcastEmailTemplates.default = :default', { default: 1 });
        })
      )
      .orderBy('broadcastEmailTemplates.createdAt', 'DESC');

    // Add the search filter if provided
    if (query.search) {
      broadcastEmailTemplatesQuery.andWhere('broadcastEmailTemplates.title LIKE :search', {
        search: `%${query.search}%`,
      });
    }

    let emailTemplates = await broadcastEmailTemplatesQuery.getMany();

    // Extract unique labels from the email templates (assuming they are strings)
    const templateLabels = emailTemplates
      .map((template) => template.labels) // Get the label from each template
      .filter((label) => !!label); // Filter out any undefined or null labels

    if (templateLabels.length === 0) {
      return emailTemplates; // Return early if no labels are found
    }
    // Fetch all labels for the user's organization that match the template labels
    const labelColors = await Label.createQueryBuilder('label')
      .leftJoin('label.organization', 'organization')
      .where('organization.id = :orgId OR label.defaultOne = true', {
        orgId: user.organization?.id,
      })

      .andWhere('label.name IN (:...templateLabels)', { templateLabels }) // Match template labels with label names
      .getMany();

    // Format the result by including the label color in the template response
    const formattedTemplates = emailTemplates.map((template) => {
      const matchingLabel = labelColors.find((label) => label.name === template.labels); // Find the matching label by name
      return {
        ...template,
        labelColor: matchingLabel ? matchingLabel.color : null, // Add the color if there's a match
      };
    });

    return formattedTemplates;
  }

  async getOneEmailTemplate(userId, id) {
    // Fetch the user with their organization relation
    let user = await User.findOne({
      where: { id: userId },
      relations: ['organization'],
    });
    const getOneEmailTemplate = await BroadcastEmailTemplates.createQueryBuilder('EmailTemplate')
    .leftJoinAndSelect('EmailTemplate.organization', 'organization')
    .leftJoinAndSelect('EmailTemplate.label', 'label')
    .where('EmailTemplate.id = :id', { id })
    .andWhere(new Brackets(qb => {
      qb.where('organization.id = :orgId', { orgId: user.organization.id })
        .orWhere('EmailTemplate.default = true');
    }))
    .getOne();
    return getOneEmailTemplate;
  }

  async updateEmailTemplate(userId, id, body) {
    // Find the user and their organization
    let user = await User.findOne({
      where: { id: userId },
      relations: ['organization'],
    });

    if (!user) {
      throw new Error('User not found');
    }

    // Find the ClientGroupBroadcast with the given id
    let emailTemplateData = await BroadcastEmailTemplates.findOne({
      where: { id: id },
      relations: ['organization'],
    });

    if (!emailTemplateData) {
      throw new Error('emailTemplateData not found');
    }
    const existingGroup = await BroadcastEmailTemplates.findOne({
      where: {
        title: body.title,
        organization: { id: user.organization.id }, // Ensure it's within the same organization
        id: Not(id),
      },
    });

    // If the group name exists, throw an error
    if (existingGroup) {
      throw new BadRequestException('Email Title already exists in your organization');
    }
    // Update the ClientGroupBroadcast with the new data
    emailTemplateData.title = body.title;
    emailTemplateData.labels = body.tags;
    emailTemplateData.content = body.content;
    emailTemplateData.label = body.tags;
    emailTemplateData.subject = body.subject;

    // clientGroupData.clients = body.clients;
    // clientGroupData.organization = user.organization;

    // Save the updated ClientGroupBroadcast
    await emailTemplateData.save();

    return emailTemplateData;
  }
  async deleteEmailTemplate(ids: number, userId) {
    let clienGroup = await BroadcastEmailTemplates.delete(ids);
    return clienGroup;
  }

  async createBroadcastActivity(data: any, userId) {
    let user = await User.findOne({
      where: { id: userId },
      relations: ['organization'],
    });
    let clientGroupData = ClientGroupBroadcast.createQueryBuilder('clientGroupBroadcast')
      .leftJoinAndSelect('clientGroupBroadcast.clients', 'client')
      .where('clientGroupBroadcast.id = :id', { id: data.clientGroupId });
    let clientGroup = await clientGroupData.getOne();
    let newBroadcastActivity = new BroadcastActivity();
    newBroadcastActivity.template = data.templateId;
    newBroadcastActivity.clientGroup = data.clientGroupId;
    newBroadcastActivity.templateName = data.templateName;
    newBroadcastActivity.groupName = data.clientGroupName;
    newBroadcastActivity.broadcastMessageTo = BroadcastMessageTo.CLIENTGROUP;
    newBroadcastActivity.Status = data.status;
    newBroadcastActivity.organization = user.organization;
    newBroadcastActivity.user = user;
    await newBroadcastActivity.save();

    let broadcastActivityDetailsArray: BroadcastActivityDetails[] = [];

    if (clientGroup?.clients?.length) {
      for (const client of clientGroup.clients) {
        let newBroadcastActivityDetails = new BroadcastActivityDetails();
        newBroadcastActivityDetails.broadcastActivityId = newBroadcastActivity.id;
        newBroadcastActivityDetails.clientName = client.displayName;
        newBroadcastActivityDetails.email = client.email;

        // Push each detail to the array
        broadcastActivityDetailsArray.push(newBroadcastActivityDetails);
      }
    }

    // Save all BroadcastActivityDetails at once
    if (broadcastActivityDetailsArray.length) {
      await BroadcastActivityDetails.save(broadcastActivityDetailsArray);
    }

    return newBroadcastActivity;
  }

  async getBroadcastActivity(userId, query) {
    const { limit, offset } = query;

    let user = await User.findOne({
      where: { id: userId },
      relations: ['organization'],
    });
    const broadcastActivity = await BroadcastActivity.createQueryBuilder('broadcastActivity')
      .leftJoinAndSelect('broadcastActivity.template', 'template')
      .leftJoinAndSelect('broadcastActivity.clientGroup', 'clientGroup')
      .leftJoinAndSelect('broadcastActivity.details', 'details')
      .leftJoinAndSelect('broadcastActivity.user', 'user') // Include user information

      // .leftJoinAndSelect('clientGroup.clients', 'client')

      .leftJoinAndSelect('broadcastActivity.organization', 'organization')
      .where('organization.id = :organizationId', { organizationId: user.organization.id })

      .orderBy('broadcastActivity.createdAt', 'DESC');

    const sort = (typeof query?.sort === "string") ? JSON.parse(query.sort) : query?.sort;
    if (sort?.column) {
      const columnMap: Record<string, string> = {
        template: 'broadcastActivity.templateName',
        clientGroup: 'broadcastActivity.groupName',
        clients: 'broadcastActivity.groupName',
        createdAt: 'broadcastActivity.createdAt',
        sentAt: 'broadcastActivity.updatedAt',

      };
      const column = columnMap[sort.column] || sort.column;
      broadcastActivity.orderBy(column, sort.direction.toUpperCase());
    } else {
      broadcastActivity.orderBy('broadcastActivity.createdAt', 'DESC');
    };

    if (query.search) {
      broadcastActivity.andWhere(
        'broadcastActivity.templateName LIKE :search OR broadcastActivity.groupName LIKE :search',
        { search: `%${query.search}%` },
      );
    }
    // Search handling

    if (offset >= 0) {
      broadcastActivity.skip(offset);
    }

    if (limit) {
      broadcastActivity.take(limit);
    }
    let result = await broadcastActivity.getManyAndCount();
    return result;
  }

  async getBroadcastActivityDetails(userId, id, query) {

    const { limit, offset, search } = query;

    let user = await User.findOne({
      where: { id: userId },
      relations: ['organization'],
    });

    const queryBuilder = await BroadcastActivity.createQueryBuilder('broadcastActivity')
      .leftJoinAndSelect('broadcastActivity.details', 'details')
      .leftJoinAndSelect('broadcastActivity.organization', 'organization')
      .leftJoinAndSelect('broadcastActivity.clientGroup', 'clientGroup') // Add clientGroup relation
      .where('organization.id = :orgId', { orgId: user?.organization?.id })
      .andWhere('broadcastActivity.id = :id', { id: id });
    if (search) {
      queryBuilder.andWhere(
        '(details.clientName LIKE :search OR details.email LIKE :search)',
        { search: `%${search}%` }
      );
    }


    const getOneEmailTemplate = await queryBuilder.getOne();
    // if (getOneEmailTemplate?.organization?.id !== user?.organization?.id) {
    //   console.log('sssss');
    //   return undefined; // Return undefined if the organization does not match
    // }

    const checkOrganization = await BroadcastActivity.findOne({ where: { id }, relations: ["organization"] })
    const checkAccess = checkOrganization?.organization?.id === user?.organization?.id

    return { getOneEmailTemplate, checkAccess };
  }

  async upload(buffer: Buffer, key, contentType = '') {
    try {
      const bucketS3 = process.env.AWS_BUCKET_NAME;
      const upload = await this.awsService.uploadS3(buffer, bucketS3, key, contentType);
      return upload;
    } catch (err) {
      throw new BadRequestException(err);
    }
  }
  async filteredClients(userId: number, id: number, query: any) {
    const { limit, offset } = query;

    let user = await User.findOne({
      where: { id: userId },
      relations: ['organization'],
    });

    // Step 1: Get the IDs of clients already in the specified group
    const existingClients = await createQueryBuilder(ClientGroupBroadcast, 'clientgroup')
      .leftJoinAndSelect('clientgroup.clients', 'client')
      .where('clientgroup.id = :id', { id })
      .getOne();

    // const existingClientIds = existingClients.map(client => client.id);
    const existingClientIds = existingClients
      ? existingClients.clients.map((client) => client.id)
      : [];
    // Step 2: Fetch all clients from the organization, excluding the existing clients
    const repo = createQueryBuilder(Client, 'client')
      .leftJoinAndSelect('client.organization', 'organization')
      .where('organization.id = :organizationId', { organizationId: user.organization.id })
      .andWhere('client.status != :deletedStatus', { deletedStatus: 'DELETED' });

    // Only add the NOT IN clause if existingClientIds has values
    if (existingClientIds.length > 0) {
      repo.andWhere('client.id NOT IN (:...existingClientIds)', { existingClientIds });
    }

    // Step 3: Apply search filter if provided
    if (query.search) {
      repo.andWhere('(client.displayName LIKE :search OR client.email LIKE :search)', {
        search: `%${query.search}%`,
      });
    }

    // Step 4: Implement pagination
    if (offset >= 0) {
      repo.skip(offset);
    }

    if (limit) {
      repo.take(limit);
    }

    const result = await repo.getManyAndCount();

    return {
      count: result[1],
      result: result[0]
    };
  }
}
