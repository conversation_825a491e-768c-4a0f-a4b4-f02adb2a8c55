import { MigrationInterface, QueryRunner } from 'typeorm';

export class AlterEstimateTable1658859659686 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
        ALTER TABLE invoice_particular
        DROP COLUMN igst,
        DROP COLUMN cgst,
        DROP COLUMN sgst,
        ADD COLUMN gst varchar(255) null
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}
