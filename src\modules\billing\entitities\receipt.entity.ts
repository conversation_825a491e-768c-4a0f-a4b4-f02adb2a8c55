import Client from 'src/modules/clients/entity/client.entity';
import { BillingEntity } from 'src/modules/organization/entities/billing-entity.entity';
import { Organization } from 'src/modules/organization/entities/organization.entity';
import {
  BaseEntity,
  Column,
  CreateDateColumn,
  Entity,
  ManyToOne,
  OneToMany,
  OneToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import ReceiptParticular from './receipt-particular.entity';
import ReceiptCredit from './receipt-credit.entity';
import ClientGroup from 'src/modules/client-group/client-group.entity';

export enum ReceiptType {
  TASK = 'TASK',
  INVOICE = 'INVOICE',
  ADVANCE = 'ADVANCE',
}

export enum PaymentMode {
  CASH = 'CASH',
  CHEQUE = 'CHEQUE',
  BANK_TRANSFER_NEFT_RTGS_IMPS = 'BANK_TRANSFER_NEFT_RTGS_IMPS',
  CREDIT_CARD = 'CREDIT_CARD',
  UPI = 'UPI',
  DEBIT_CARD = 'DEBIT_CARD',
}

export enum ReceiptStatus {
  CREATED = 'CREATED',
  CANCELLED = 'CANCELLED',
}

@Entity()
class Receipt extends BaseEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({
    type: 'enum',
    enum: ReceiptType,
  })
  type: ReceiptType;

  @Column()
  receiptNumber: string;

  @Column({ type: 'date' })
  receiptDate: string;


  @Column({ type: 'decimal', precision: 19, scale: 2 })
  amount: number;

  @Column({ type: 'decimal', precision: 19, scale: 2, nullable: true })
  dueAmount: number;

  @Column({ nullable: true })
  tds: string;

  @Column({ type: 'decimal', precision: 19, scale: 2, nullable: true })
  tdsAmount: number;

  // @Column({ type: 'decimal', precision: 19, scale: 2, nullable: true })
  // creditsUsed: number;
  @Column()
  creditsUsed: string;

  @Column({
    type: 'enum',
    enum: PaymentMode,
  })

  paymentMode: PaymentMode;

  @Column({ type: 'enum', enum: ReceiptStatus })
  status: ReceiptStatus;

  @Column({ type: 'date' })
  paymentDate: string;

  @Column()
  referenceNumber: string;

  @Column()
  termsAndConditions: string;

  @Column('json')
  termsAndConditionsCopy: object[];

  @ManyToOne(() => Organization, (org) => org.receipts)
  organization: Organization;

  @ManyToOne(() => Client, (client) => client.receipts)
  client: Client;

  @ManyToOne(() => ClientGroup, (clientGroup) => clientGroup.receipts)
  clientGroup: ClientGroup;

  @ManyToOne(() => BillingEntity, (billingEntity) => billingEntity.receipts)
  billingEntity: BillingEntity;

  @OneToMany(() => ReceiptParticular, (rp) => rp.receipt)
  receiptParticular: ReceiptParticular[];

  @OneToOne(() => ReceiptCredit, (receiptCredit) => receiptCredit.receipt)
  receiptCredit: ReceiptCredit;


  @Column({ type: 'decimal', precision: 19, scale: 2, nullable: true })
  previousCredits: number;

  @Column({ type: 'decimal', precision: 19, scale: 2, nullable: true })
  totalCredits: number;

  @Column('json')
  invoicesEdited: object[];

  @CreateDateColumn()
  createdAt: string;

  @UpdateDateColumn()
  updatedAt: string;

  @Column()
  whatsappCheck: boolean;

  @Column()
  emailCheck: boolean;
}

export default Receipt;
