import {
  Connection,
  EntitySubscriberInterface,
  EventSubscriber,
  InsertEvent,
  UpdateEvent,
  getManager,
} from 'typeorm';
import Client from '../modules/clients/entity/client.entity';
import {
  getAdminEmailsBasedOnOrganizationId,
  getAdminIDsBasedOnOrganizationId,
  getAdminIdsWithClientGroupId,
  getAdminIdsWithClientId,
  getAllOrganizationUsersBasedOnClientGroupId,
  getAllOrganizationUsersBasedOnClientId,
  getUserDetails,
  getUserNamewithUserId,
  insertINTONotificationUpdate,
  insertINTOnotification,
} from 'src/utils/re-use';
import { sendWhatsAppTextMessage } from 'src/modules/whatsapp/whatsapp.service';
import ViderWhatsappSessions from 'src/modules/whatsapp/entity/viderWhatsappSessions';
import { sendnewMail } from 'src/emails/newemails';
import * as moment from 'moment';
import GstrRegister from 'src/modules/gstr-register/entity/gstr-register.entity';
import ClientGroup, { UserStatus } from 'src/modules/client-group/client-group.entity';
import Activity, { ActivityType } from 'src/modules/activity/activity.entity';
import { Event_Actions } from 'src/event-listeners/actions';
import { User } from 'src/modules/users/entities/user.entity';
import { fullMobileNumberWithCountry } from 'src/utils/validations/fullMobileWithCountry';

let clientOldDetails: ClientGroup;
@EventSubscriber()
export class ClientGroupSubscriber implements EntitySubscriberInterface<ClientGroup> {
  constructor(private readonly connection: Connection) {
    connection.subscribers.push(this);
  }

  listenTo() {
    return ClientGroup;
  }

  async beforeInsert(event: InsertEvent<ClientGroup>) {
    let orgId = event?.entity?.organization?.id;
    let user = await User.findOne({
      where: { id: event.entity['userId'] },
      relations: ['organization'],
    });

    function generateEmpoyeeId(org: string, id: number) {
      if (id < 10000) {
        return org.slice(0, 4).toUpperCase() + id.toString().padStart(4, '0');
      }
      return org.slice(0, 4).toUpperCase() + id;
    }
    let count = await this.connection.manager.getRepository(ClientGroup).count({
      where: {
        organization: {
          id: orgId,
        },
      },
    });
    let orgName = event?.entity?.organization?.legalName?.slice(0, 3) + 'G';
    if (orgName) {
      event.entity.clientId = generateEmpoyeeId(orgName, count + 1);
    }
    let activity = new Activity();
    activity.action = Event_Actions.CLIENT_GROUP_CREATED;
    activity.actorId = event.entity['userId'];
    activity.type = ActivityType.CLIENT_GROUP;
    activity.typeId = event.entity.id;
    activity.remarks = `"${event.entity.displayName}" Client Group Profile Created by ${user.fullName}`;
    await activity.save();
  }

  async afterInsert(event: InsertEvent<ClientGroup>) {
    if (event.entity['userId']) {
      const userName = await getUserNamewithUserId(event.entity['userId']);
      const entityManager = getManager();
      const userId = event.entity['userId'];
      let user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
      const { displayName, email, mobileNumber } = event.entity;
      let fullName;
      if (user) {
        fullName = user.fullName;
      } else {
        fullName = displayName;
      }
      const getuserQuery = `SELECT full_name, organization_id FROM user where id=${userId}`;
      const getCreated_name = await entityManager.query(getuserQuery);
      const created_name = getCreated_name[0]?.full_name;
      const orgId = getCreated_name[0]?.organization_id;
      const adminsList = await getAdminIDsBasedOnOrganizationId(orgId);
      const title = 'Client Group Created';
      const body = `<strong>${created_name}</strong> has created <strong>${displayName}</strong> profile.`;
      const key = 'CLIENT_GROUP_CREATED_PUSH';
      insertINTONotificationUpdate(title, body, adminsList, orgId, key);
      try {
        if (adminsList) {
          for (let adminId of adminsList) {
            const sessionValidation = await ViderWhatsappSessions.findOne({
              where: { userId: userId, status: 'ACTIVE' },
            });

            if (sessionValidation) {
              const adminUserDetails = await getUserDetails(userId);
              const {
                full_name: userFullName,
                mobile_number: userPhoneNumber,
                country_code: countryCode,
              } = adminUserDetails;
              const key = 'CLIENT_GROUP_CREATED_WHATSAPP';
              const whatsappMessageBody = `
 Hi ${userFullName}
 A new Client Group has been created in ATOM:
    
Client Group Name: ${displayName}
Email address: ${email}
Phone number: ${mobileNumber}
    
We hope this helps!
`;

              await sendWhatsAppTextMessage(
                // `91${userPhoneNumber}`,
                fullMobileNumberWithCountry(userPhoneNumber, countryCode),
                whatsappMessageBody,
                orgId,
                title,
                userId,
                key,
              );
            }
          }
        }
      } catch (error) {
        console.error('Error sending Task WhatsApp notification:', error);
      }
    }
  }

  async beforeUpdate(event: UpdateEvent<ClientGroup>) {
    clientOldDetails = event?.databaseEntity;
  }

  async afterUpdate(event: UpdateEvent<ClientGroup>) {
    const clientNewDetials = event.entity;
    const entity_manager = getManager();
    const { displayName, id } = event.entity;
    if (event.entity['userId']) {
      const userName = await getUserNamewithUserId(event.entity['userId']);
      const orgQuery = `SELECT organization_id FROM client_group where id = ${id};`;
      const orgIdSql = await entity_manager.query(orgQuery);
      const orgId = orgIdSql[0].organization_id;
      const ignoreKeys = ['inactiveAt', 'createdAt', 'updatedAt'];
      const labelNames = {
        clientManager: 'Client Manager',
        id: 'ID',
        clientId: 'Client ID',
        slug: 'Slug',
        displayName: 'Display Name',
        category: 'Category',
        subCategory: 'Subcategory',
        email: 'Email',
        mobileNumber: 'Mobile Number',
        alternateMobileNumber: 'Alternate Mobile Number',
        authorizedPerson: 'Authorized Person',
        designation: 'Designation',
        gstVerified: 'GST Verified',
        gstNumber: 'GST Number',
        legalName: 'Legal Name',
        tradeName: 'Trade Name',
        placeOfSupply: 'Place of Supply',
        constitutionOfBusiness: 'Constitution of Business',
        panVerified: 'PAN Verified',
        panNumber: 'PAN Number',
        firstName: 'First Name',
        lastName: 'Last Name',
        fullName: 'Full Name',
        buildingName: 'Building Name',
        street: 'Street',
        city: 'City',
        state: 'State',
        pincode: 'Pincode',
        notes: 'Notes',
        dob: 'Date of Birth',
        image: 'Image',
        clientPortalAccess: 'Client Portal Access',
        status: 'Status',
        labels: 'Labels',
        issameaddress: 'Address same as above',
      };
      if (clientOldDetails !== undefined) {
        const admins_list = await getAdminIdsWithClientGroupId(id);
        const allOrgUsers = await getAllOrganizationUsersBasedOnClientGroupId(id);
        const listOfUsersIds: any = allOrgUsers.map((obj) => obj.id);
        if (
          event.entity.status !== clientOldDetails['status'] &&
          event.entity.status !== undefined
        ) {
          const title =
            event.entity.status === UserStatus.ACTIVE
              ? `Client Group Status Updated`
              : event.entity.status === UserStatus.INACTIVE
              ? `Client Group Status Updated`
              : event.entity.status === UserStatus.DELETED
              ? 'Client Group Deleted'
              : '';
          const body =
            event.entity.status === UserStatus.ACTIVE
              ? `<b>${userName}</b> has made "<b>${clientOldDetails['displayName']}</b>" Active`
              : event.entity.status === UserStatus.INACTIVE
              ? `<b>${userName}</b> has made "<b>${clientOldDetails['displayName']}</b>" Inactive`
              : event.entity.status === UserStatus.DELETED
              ? `<b>${userName}</b> has deleted "<b>${clientOldDetails['displayName']}</b>" profile`
              : '';
          const key = 'CLIENT_GROUP_STATUS_HAS_BEEN_UPDATED_PUSH';
          insertINTONotificationUpdate(title, body, listOfUsersIds, orgId, key);
          try {
            for (const listOfUsersId of listOfUsersIds) {
              const sessionValidation = await ViderWhatsappSessions.findOne({
                where: { userId: listOfUsersId, status: 'ACTIVE' },
              });
              if (sessionValidation) {
                const loggedinuserDetails = await getUserDetails(listOfUsersId);
                const {
                  full_name: userFullName,
                  mobile_number: userPhoneNumber,
                  country_code: countryCode,
                } = loggedinuserDetails;
                const key = 'CLIENT_GROUP_STATUS_UPDATED_WHATSAPP';
                const whatsappMessageBody = `
Hi ${userFullName},
       
Status of ${displayName} has been changed to ${event.entity.status}  by  ${userName}
please revert the same if this was done by mistake!

Thanks,
The ATOM Team`;

                await sendWhatsAppTextMessage(
                  // `91${userPhoneNumber}`,
                  fullMobileNumberWithCountry(userPhoneNumber, countryCode),
                  whatsappMessageBody,
                  orgId,
                  title,
                  listOfUsersId,
                  key,
                );
              }
            }
          } catch (error) {
            console.error('Error sending WhatsApp notification:', error);
          }
        } else {
          const title = 'Client Group Profile Updation';
          const body = `<b>${userName}</b> have succesfully updated "<b>${displayName}</b>" profile`;
          const key = 'CLIENT_PROFILE_UPDATION_PUSH';
          insertINTONotificationUpdate(title, body, admins_list, orgId, key);
          try {
            for (const admin of admins_list) {
              const sessionValidation = await ViderWhatsappSessions.findOne({
                where: { userId: admin, status: 'ACTIVE' },
              });
              if (sessionValidation) {
                const loggedinuserDetails = await getUserDetails(admin);
                const {
                  full_name: userFullName,
                  mobile_number: userPhoneNumber,
                  country_code: countryCode,
                } = loggedinuserDetails;
                const key = 'CLIENT_GROUP_PROFILE_UPDATION_WHATSAPP';
                const whatsappMessageBody = `
Hi ${userFullName},
       
${userName} have successfully updated ${displayName} profile

Thanks,
The ATOM Team`;

                await sendWhatsAppTextMessage(
                  // `91${userPhoneNumber}`,
                  fullMobileNumberWithCountry(userPhoneNumber, countryCode),
                  whatsappMessageBody,
                  orgId,
                  title,
                  admin,
                  key,
                );
              }
            }
          } catch (error) {
            console.error('Error sending WhatsApp notification:', error);
          }
        }
      }
    }
    if (!clientOldDetails?.gstVerified && clientNewDetials?.gstVerified) {
      let clientGroup = await ClientGroup.findOne({
        where: {
          id: event?.entity?.id,
        },
        relations: ['organization'],
      });
      const existingRecord = await GstrRegister.findOne({
        where: {
          clientGroup: clientGroup,
        },
      });
      if (!existingRecord) {
        let gstrRegister = new GstrRegister();
        gstrRegister.clientGroup = clientOldDetails;
        gstrRegister.registrationType = event.entity.registrationType;
        gstrRegister.organization = clientGroup.organization;
        // await gstrRegister.save();
      }
    }
  }
}
