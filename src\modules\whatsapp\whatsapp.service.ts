import { <PERSON><PERSON>, CronExpression } from '@nestjs/schedule';
import axios, { AxiosRequestConfig } from 'axios';
import { Injectable } from '@nestjs/common';
import ViderWhatsappSessions from './entity/viderWhatsappSessions';
import { <PERSON><PERSON><PERSON>, create<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, getManager } from 'typeorm';
import { User } from '../users/entities/user.entity';
import { UserStatus } from './entity/viderWhatsappSessions';
import { getUserDetails, checkUserTosendMorningMsg } from 'src/utils/re-use';
import Task from '../tasks/entity/task.entity';
import * as moment from 'moment';
import DscRegister from '../dsc-register/entity/dsc-register.entity';
import Event from '../events/event.entity';
import { EventTypeEnum } from '../events/dto/create-event.dto';
import NotificationPreferences from '../notification-settings/notifications-preferences.entity';
import WhatsappAudit, { MessageType } from './entity/whatsappAudit';
import OrganizationPreferences from '../organization-preferences/entity/organization-preferences.entity';
import WhatsappRequests from './entity/whatsappRequests';
import { fullMobileNumberWithCountry } from 'src/utils/validations/fullMobileWithCountry';
import WhatsappConversation from './entity/whatsappConversation';
import Client from '../clients/entity/client.entity';
import CreateMetaTemplate from './entity/createMetaTemplate';


const whatsappAccessToken =
  'EAALleIgpd0QBOx6wyMKR1uEo4rnCscXE4ZBCV4JqHykfpdvqzrqy7cZA0wFhIrVZCGP0jNiLZARZCZCBgl9g6OfWGY1UGMaGqUvevsMbZBbwnQH13DkSg9IIWYmZAQEoQhTSXpshoYIu8gZCcqAeO9VVCouxzxtuMcQ6JPtbuLsFzyFZAknnzfs6mxZBxSp2wGe5XRL';

@Injectable()
export class WhatsappService {
  private readonly whatsappAccessToken =
    'EAALleIgpd0QBOx6wyMKR1uEo4rnCscXE4ZBCV4JqHykfpdvqzrqy7cZA0wFhIrVZCGP0jNiLZARZCZCBgl9g6OfWGY1UGMaGqUvevsMbZBbwnQH13DkSg9IIWYmZAQEoQhTSXpshoYIu8gZCcqAeO9VVCouxzxtuMcQ6JPtbuLsFzyFZAknnzfs6mxZBxSp2wGe5XRL';
  async sendWhatsAppTemplateMessage(options: {
    to: string;
    userId: any;
    orgId: number;
    title: string;
    name: string;
    header?: object[];
    body?: any[];
    button?: string;
  }): Promise<any> {
    const { to, name, header, body, button, userId, orgId, title } = options;
    if (orgId) {
      const orgPreferences: any = await OrganizationPreferences.findOne({
        where: { organization: orgId },
      });
      const webhookacesstoken= orgPreferences?.metaConfig?.accessToken
          const businessId = orgPreferences?.metaConfig?.businessId
    const templateData = {
      messaging_product: 'whatsapp',
      recipient_type: 'individual',
      to,
      type: 'template',
      template: {
        name,
        language: {
          code: 'en',
        },
        components: [],
      },
    };

    // Dynamically construct header, body, and button components
    if (header && header.length > 0) {
      templateData.template.components.push({
        type: 'header',
        parameters: header
          .map((header: any) => {
            if (header.type === 'text') {
              return {
                type: 'text',
                text: header.text,
              };
            } else if (header.type === 'document') {
              return {
                type: 'document',
                document: {
                  link: header.link,
                },
              };
            }
            // Add more conditions for other types if needed
            return null; // Handle other types accordingly
          })
          .filter(Boolean), // Filter out null values
      });
    }

    if (body && body.length > 0) {
      templateData.template.components.push({
        type: 'body',
        parameters: body.map((bodyText) => ({
          type: 'text',
          text: `${bodyText}`,
        })),
      });
    }

    if (button && button.length > 0) {
      templateData.template.components.push({
        type: 'button',
        sub_type: 'URL',
        index: 0,
        parameters: [
          {
            type: 'text',
            text: button,
          },
        ],
      });
    }

    const config: AxiosRequestConfig = {
      method: 'post',
      url: `https://graph.facebook.com/v18.0/${businessId? businessId: 108161499033381}/messages`,
      headers: {
        'Authorization': `Bearer ${webhookacesstoken? webhookacesstoken: whatsappAccessToken}`,
        'Content-Type': 'application/json',
      },
      data: JSON.stringify(templateData),
    };
  
    try {
      const response = await axios(config);
      if (response?.data) {
        const whatsappAudit = new WhatsappAudit();
        whatsappAudit.mobileNumber = to;
        whatsappAudit.userId = userId;
        whatsappAudit.organizationId = orgId;
        whatsappAudit.title = title;
        whatsappAudit.messageType = MessageType.TEMPLATE;
        whatsappAudit.phoneNumberId = businessId;
        await whatsappAudit.save();
      }
      return response?.data;
    } catch (error) {
      console.error(error.message);
      return error;
    }
  }
  }
  async sendWhatsAppTextMessage(body:any,userId) {
    let user = await User.findOne({
      where: { id: userId },
      relations: ['organization'],
    });
    let data = {
      messaging_product: 'whatsapp',
      recipient_type: 'individual',
      to: body?.mobileNumber,
      orgId: user?.organization?.id,
      userId: userId,
      type: 'text',
      text: {
        preview_url: false,
        body: body?.message,
      },
    };
    if (user?.organization?.id) {
      const orgPreferences: any = await OrganizationPreferences.findOne({
        where: { organization: user?.organization?.id},
      });
      const webhookacesstoken= orgPreferences?.metaConfig?.accessToken
          const businessId = orgPreferences?.metaConfig?.businessId
    const config: AxiosRequestConfig = {
      method: 'post',
      maxBodyLength: Infinity,
      url: `https://graph.facebook.com/v17.0/${businessId? businessId: 531157636751620}/messages`,
      headers: {
        'Authorization': `Bearer ${webhookacesstoken? webhookacesstoken: whatsappAccessToken}`,
        'Content-Type': 'application/json',
      },
      data: data,
    };

    try {
      const response = await axios(config);
      if (response?.data) {
        const whatsappConversation = new WhatsappConversation();
        whatsappConversation.mobileNumber = body?.mobileNumber;
        whatsappConversation.userId = userId;
        whatsappConversation.organizationId = user?.organization?.id;
        whatsappConversation.message = body?.message;

        whatsappConversation.direction = 'outgoing';
        await whatsappConversation.save();
      }
      return response?.data;
    } catch (error) {
      console.error(error.message);
      return error;
    }
  }
  }

   async sendWhatsAppTextMessageConvo(body:any,userId) {
    let user = await User.findOne({
      where: { id: userId },
      relations: ['organization'],
    });
    let data = {
      messaging_product: 'whatsapp',
      recipient_type: 'individual',
      to: body?.mobileNumber,
      orgId: user?.organization?.id,
      userId: userId,
      type: 'text',
      text: {
        preview_url: false,
        body: body?.message,
      },
    };
    if (user?.organization?.id) {
      const orgPreferences: any = await OrganizationPreferences.findOne({
        where: { organization: user?.organization?.id},
      });
      const webhookacesstoken= orgPreferences?.metaConfig?.accessToken
          const businessId = orgPreferences?.metaConfig?.businessId
    const config: AxiosRequestConfig = {
      method: 'post',
      maxBodyLength: Infinity,
      url: `https://graph.facebook.com/v17.0/${businessId? businessId: 108161499033381}/messages`,
      headers: {
        'Authorization': `Bearer ${webhookacesstoken? webhookacesstoken: whatsappAccessToken}`,
        'Content-Type': 'application/json',
      },
      data: data,
    };

    try {
      const response = await axios(config);
      if (response?.data) {
        const whatsappConversation = new WhatsappConversation();
        whatsappConversation.mobileNumber = body?.mobileNumber;
        whatsappConversation.userId = userId;
        whatsappConversation.organizationId = user?.organization?.id;
        whatsappConversation.message = body?.message;

        whatsappConversation.direction = 'outgoing';
        await whatsappConversation.save();
      }
      return response?.data;
    } catch (error) {
      console.error(error.message);
      return error;
    }
  }
  }

  async createMetaTemplate(body: any,userId) {
    const waba_id = 109404492224707
    try {
      const response = await axios.post(
        `https://graph.facebook.com/v17.0/${waba_id}/message_templates`,
        body,
        {
          headers: {
            'Authorization': 'Bearer EAALleIgpd0QBOx6wyMKR1uEo4rnCscXE4ZBCV4JqHykfpdvqzrqy7cZA0wFhIrVZCGP0jNiLZARZCZCBgl9g6OfWGY1UGMaGqUvevsMbZBbwnQH13DkSg9IIWYmZAQEoQhTSXpshoYIu8gZCcqAeO9VVCouxzxtuMcQ6JPtbuLsFzyFZAknnzfs6mxZBxSp2wGe5XRL',
            'Content-Type': 'application/json',
          },
        },
      );
      let user = await User.findOne({
        where: { id: userId },
        relations: ['organization'],
      });
      if (response.data) {
        const metaTemplate = new CreateMetaTemplate();
        metaTemplate.templateName = body?.name;
        metaTemplate.language = body?.language;
        metaTemplate.category = body?.category;
      
        // Extract components
        const headerComponent = body.components?.find(comp => comp.type === 'HEADER');
        const bodyComponent = body.components?.find(comp => comp.type === 'BODY');
        const footerComponent = body.components?.find(comp => comp.type === 'FOOTER');
        const buttonComponents = body.components?.filter(comp => comp.type === 'BUTTON');
      
        metaTemplate.header = headerComponent?.text || null;
        metaTemplate.body = bodyComponent?.text || null;
        metaTemplate.footer = footerComponent?.text || null;
        metaTemplate.buttons = JSON.stringify(buttonComponents || []);
        metaTemplate.variable = JSON.stringify(bodyComponent?.example || []); // or other logic if applicable
      
        metaTemplate.userId = userId;
        metaTemplate.organizationId = user?.organization?.id;
        metaTemplate.wabaId = waba_id;
      
        await metaTemplate.save();
      }
      
      return {
        success: true,
        data: response.data,
        message: 'Template created and saved successfully',
      };
    
    } catch (error) {
      console.error('Error creating template:', error?.response?.data || error.message);
      return {
        success: false,
        error: error?.response?.data || error.message,
      };
    }
  }


   async getDefaultMetaTemplates() {
    const defaultTemplates = await CreateMetaTemplate.find({
      where: {
        default: true,
      },
    });
    return defaultTemplates;
  }

  async getConversationClients(userId: number) {
    let user = await User.findOne({
      where: { id: userId },
      relations: ['organization'],
    });

    const entityManager = getManager();

    const query = `
   SELECT
  id,
  country_code AS countryCode,
  mobile_number AS mobileNumber,
  display_name AS displayName,
  status
FROM client
WHERE organization_id = ${user?.organization.id}
  AND status != 'DELETED'
  AND CONCAT(
        CASE country_code
          WHEN 'IN' THEN '91'
          ELSE ''
        END,
        mobile_number
      ) NOT IN (
    SELECT mobile_number
    FROM whatsapp_conversation
    WHERE organization_id = ${user?.organization.id}
      AND client_id IS NOT NULL
  );

    `;

    let clients = await entityManager.query(query);
    return clients;
  }
  

  @Cron(CronExpression.EVERY_30_MINUTES)
  async validateWhatsappSession() {
    if (
      process.env.WEBSITE_URL === 'https://atom.vider.in' &&
      process.env.Cron_Running === 'true'
    ) {
      const queryBuilder = createQueryBuilder(ViderWhatsappSessions, 'sessions') // Change alias from 'vid' to 'sessions'
        .select([
          'sessions.id',
          'sessions.userId',
          'sessions.organizationId',
          'sessions.createdTimestamp',
          'sessions.status',
          'sessions.statusUpdatedTimestamp',
          `IFNULL(TIMESTAMPDIFF(MINUTE, sessions.statusUpdatedTimestamp, NOW()), 0) as minutesDifference`,
        ])
        .where('TIMESTAMPDIFF(MINUTE, sessions.statusUpdatedTimestamp, NOW()) >= 23*60')
        .andWhere("sessions.status = 'ACTIVE'");
      const results = await queryBuilder.getMany();

      // Extract the IDs from the filtered result00000000000000000000
      const idsToRemove = results.map((entity) => entity.id);

      // Use the remove method with the array of IDs
      for (const id of idsToRemove) {
        const session = await ViderWhatsappSessions.findOne({
          where: { id: id },
        });

        if (session) {
          // await session.remove();
          session.status = UserStatus.INACTIVE;
          session.isSent = false;
          session.save();
        }
      }
    }
  }

  @Cron(CronExpression.EVERY_30_MINUTES)

  async validateStatusAndSendWelcome() {
    if (
      process.env.WEBSITE_URL === 'https://atom.vider.in' &&
      process.env.Cron_Running === 'true'
    ) {
      const queryBuilder = createQueryBuilder(ViderWhatsappSessions, 'sessions')
        .select([
          'sessions.id',
          'sessions.userId',
          'sessions.organizationId',
          'sessions.createdTimestamp',
          'sessions.status',
          'sessions.statusUpdatedTimestamp',
          'NOW() as now',
          `IFNULL(TIMESTAMPDIFF(MINUTE, sessions.statusUpdatedTimestamp, NOW()), 0) as minutesDifference`,
        ])
        .where('TIMESTAMPDIFF(MINUTE, sessions.statusUpdatedTimestamp, NOW()) >= 30')
        .andWhere('sessions.isSent = false')
        .andWhere("sessions.status = 'INACTIVE'");

      const results = await queryBuilder.getMany();
      for (const whatsappSessions of results) {
        const userIsPermissions = await checkUserTosendMorningMsg(whatsappSessions?.userId);
        const organizationPreferences: any = await OrganizationPreferences.findOne({
          where: { organization: whatsappSessions?.organizationId },
        });
        const whatsappCheck = organizationPreferences?.notificationConfig?.whatsappPreferences;
if(whatsappCheck){
        if (userIsPermissions) {
          if (!whatsappSessions.isSent) {
            const userDetails = await getUserDetails(whatsappSessions.userId);
            const {
              full_name: fullName,
              mobile_number: mobileNumber,
              id,
              organization_id,
            } = userDetails;
            const whatsappOptions = {
              title: 'everyday message',
              userId: id,
              orgId: organization_id,
              to: `${mobileNumber}`,
              name: 'every_day_morning_message',
              header: [
                {
                  type: 'text',
                  text: fullName,
                },
              ],
            };
            const response = await this.sendWhatsAppTemplateMessage(whatsappOptions);
            if (response) {
              const result = await ViderWhatsappSessions.findOne({
                where: { userId: whatsappSessions.userId },
              });
              result.isSent = true;
              result.save();
            }
          }
        }
      }
      }
    }
  }

  async whatsappRequests(query) {
    try {
      const whatsappRequests = WhatsappRequests.createQueryBuilder('whatsappRequests');
      if (query?.search) {
        whatsappRequests
          .where('whatsappRequests.mobileNumber LIKE :mobileNumber', {
            mobileNumber: `%${query.search}%`,
          })
          .orWhere('whatsappRequests.message LIKE :message', {
            message: `%${query.search}%`,
          });
      }

      const WhatsappRequestsdata = await whatsappRequests.getMany();
      return WhatsappRequestsdata;
    } catch (error) {
    }
  }

  async whatsappConversation(userId, query) {
    const { search } = query;
    
    const user = await User.findOne({
      where: { id: userId },
      relations: ['organization'],
    });
  
    try {
      const qb = WhatsappConversation.createQueryBuilder('whatsappConversation')
        .where('whatsappConversation.organizationId = :organizationId', {
          organizationId: user.organization.id,
        });
  
      if (search && search.trim()) {
        qb.andWhere('whatsappConversation.mobileNumber LIKE :search', {
          search: `%${search}%`,
        });
      }
  
      const whatsappConversationData = await qb.getMany();
      return whatsappConversationData;
    } catch (error) {
      console.error('Error occurred while getting WhatsApp conversation', error);
      throw error;
    }
  }
  

  async allApprovedTemplates(){
    const whatsappId = 525570653977535
    const axios = require('axios');


let config = {
  method: 'get',
  maxBodyLength: Infinity,
  url: `https://graph.facebook.com/v17.0/${whatsappId}/message_templates`,
  headers: { 
    'Authorization': 'Bearer EAAQoOMDMYXwBO4pWsjFj4zOv1s4zTrZArDEdUPDzD9nOZAAUKt5YCfzWAFUBjb3ykZCZAsBcffjzc1tQRUma1jrpxsbNXCdhAJ9M54cWhY8ZCcexaOOgGqpVk15CgP0RoO1qQyPEkcEWOfsiR8r210u2mZCohmqGRr409ivgVNSpftKHEfvdLLj0LS9bxFZBKpBMwZDZD'
  }
};

try {
  const response = await axios.request(config);

  const approvedTemplates = response.data.data
    .filter((tpl) => tpl.status === 'APPROVED')
    .map((tpl) => ({
      name: tpl.name,
      language: tpl.language
    }));
  return approvedTemplates; 
} catch (error) {
  console.error('Failed to fetch templates:', error);
  throw new Error('Failed to fetch templates');
}

  }
async sendTemplateMessageConversation(body,userId){
    const whatsappId = 108161499033381;
    const config:any = {
      method: 'post',
      url: `https://graph.facebook.com/v17.0/${whatsappId}/messages`,
      headers: {
      'Authorization': 'Bearer EAALleIgpd0QBOx6wyMKR1uEo4rnCscXE4ZBCV4JqHykfpdvqzrqy7cZA0wFhIrVZCGP0jNiLZARZCZCBgl9g6OfWGY1UGMaGqUvevsMbZBbwnQH13DkSg9IIWYmZAQEoQhTSXpshoYIu8gZCcqAeO9VVCouxzxtuMcQ6JPtbuLsFzyFZAknnzfs6mxZBxSp2wGe5XRL'
  ,
        'Content-Type': 'application/json',
      },
      data: {
        messaging_product: 'whatsapp',
        to: body?.mobileNumber,
        type: 'template',
        template: {
          name: body?.templateName,
          language: {
            code: body?.language, // optional
          },
        },
      },
    };
  
    try {
      const response = await axios.request(config);
      if (response?.data) {
        const whatsappConversation = new WhatsappConversation();
        whatsappConversation.mobileNumber = body?.mobileNumber;
        whatsappConversation.userId = userId;
        let user = await User.findOne({
          where: { id: userId },
          relations: ['organization'],
        });
        whatsappConversation.organizationId = user?.organization?.id;
        whatsappConversation.templateName = body?.templateName;
        whatsappConversation.msgType = 'template';
  
        whatsappConversation.direction = 'outgoing';
        await whatsappConversation.save();
  
      return { success: true, data: response.data };
      }
    } catch (err) {
      console.error('Error sending template message:', err);
      return { success: false, error: 'Failed to send template message' };
    }
  
  }
  async sendWhatsAppTextMessagee(to: string, body: string, orgId, title, userId) {
    let data = {
      messaging_product: 'whatsapp',
      recipient_type: 'individual',
      to: to,
      orgId: orgId,
      title: title,
      userId: userId,
      type: 'text',
      text: {
        preview_url: false,
        body: body,
      },
    };
    if (orgId) {
      const orgPreferences: any = await OrganizationPreferences.findOne({
        where: { organization: orgId },
      });
      const webhookacesstoken= orgPreferences?.metaConfig?.accessToken
          const businessId = orgPreferences?.metaConfig?.businessId
    const config: AxiosRequestConfig = {
      method: 'post',
      maxBodyLength: Infinity,
      url: `https://graph.facebook.com/v17.0/${businessId? businessId: 108161499033381}/messages`,
      headers: {
        'Authorization': `Bearer ${webhookacesstoken? webhookacesstoken: whatsappAccessToken}`,
        'Content-Type': 'application/json',
      },
      data: data,
    };

    try {
      const response = await axios(config);
      if (response?.data) {
        const whatsappAudit = new WhatsappAudit();
        whatsappAudit.mobileNumber = to;
        whatsappAudit.userId = userId;
        whatsappAudit.organizationId = orgId;
        whatsappAudit.title = title;
        whatsappAudit.messageType = MessageType.TEXT;
                whatsappAudit.phoneNumberId = businessId;

        await whatsappAudit.save();
      }
      return response?.data;
    } catch (error) {
      console.error(error.message);
      return error;
    }
  }
  }
}

export async function sendWhatsAppTemplateMessage(options: {
  to: any;
  orgId: number;
  title: string;
  userId?: number;
  name: string;
  header?: object[];
  body?: any[];
  button?: string;
  fileName?: string;
}): Promise<any> {
  const { to, name, header, body, button, fileName, orgId, userId, title } = options;

  const templateData = {
    messaging_product: 'whatsapp',
    recipient_type: 'individual',
    to,
    type: 'template',
    template: {
      name,
      language: {
        code: 'en',
      },
      components: [],
    },
  };

  // Dynamically construct header, body, and button components
  if (header && header.length > 0) {
    templateData.template.components.push({
      type: 'header',
      parameters: header
        .map((header: any) => {
          if (header.type === 'text') {
            return {
              type: 'text',
              text: header.text,
            };
          } else if (header.type === 'document') {
            return {
              type: 'document',
              document: {
                link: header.link,
                filename: fileName,
              },
            };
          }
          // Add more conditions for other types if needed
          return null; // Handle other types accordingly
        })
        .filter(Boolean), // Filter out null values
    });
  }

  if (body && body.length > 0) {
    templateData.template.components.push({
      type: 'body',
      parameters: body.map((bodyText) => ({
        type: 'text',
        text: `${bodyText}`,
      })),
    });
  }

  if (button && button.length > 0) {
    templateData.template.components.push({
      type: 'button',
      sub_type: 'URL',
      index: 0,
      parameters: [
        {
          type: 'text',
          text: button,
        },
      ],
    });
  }
  if (orgId) {
    const orgPreferences: any = await OrganizationPreferences.findOne({
      where: { organization: orgId },
    });
    const webhookacesstoken= orgPreferences?.metaConfig?.accessToken
        const businessId = orgPreferences?.metaConfig?.businessId
  const config: AxiosRequestConfig = {
    method: 'post',
    url: `https://graph.facebook.com/v18.0/${businessId? businessId: 108161499033381}/messages`,
    headers: {
      'Authorization': `Bearer ${webhookacesstoken? webhookacesstoken: whatsappAccessToken}`,
      'Content-Type': 'application/json',
    },
    data: JSON.stringify(templateData),
  };
  try {
    const response = await axios(config);
    if (response?.data) {
      const whatsappAudit = new WhatsappAudit();
      whatsappAudit.mobileNumber = to;
      whatsappAudit.userId = userId;
      whatsappAudit.organizationId = orgId;
      whatsappAudit.title = title;
      whatsappAudit.messageType = MessageType.TEMPLATE;
      whatsappAudit.phoneNumberId = businessId;

      await whatsappAudit.save();
    }
    return response?.data;
  } catch (error) {
    console.error(error.message);
    return error;
  }
}
}

export async function sendWhatsAppMarketingTextMessage(to: string, body: string, title) {
  let data = {
    messaging_product: 'whatsapp',
    recipient_type: 'individual',
    to: to,
    title: title,
    type: 'text',
    text: {
      preview_url: false,
      body: body,
    },
  };
const marketingAccessToken = 'EAAQoOMDMYXwBO4pWsjFj4zOv1s4zTrZArDEdUPDzD9nOZAAUKt5YCfzWAFUBjb3ykZCZAsBcffjzc1tQRUma1jrpxsbNXCdhAJ9M54cWhY8ZCcexaOOgGqpVk15CgP0RoO1qQyPEkcEWOfsiR8r210u2mZCohmqGRr409ivgVNSpftKHEfvdLLj0LS9bxFZBKpBMwZDZD'
   
  const config: AxiosRequestConfig = {
    method: 'post',
    maxBodyLength: Infinity,
    url: `https://graph.facebook.com/v17.0/${531157636751620}/messages`,
    headers: {
      'Authorization': `Bearer ${marketingAccessToken}`,
      'Content-Type': 'application/json',
    },
    data: data,
  };

  try {
    const response = await axios(config);
    return response?.data;
  } catch (error) {
    console.error(error.message);
    return error;
  }

}

export async function sendWhatsAppTemplateMessageMarketing(options: {
  to: any;
  orgId: number;
  title: string;
  userId?: number;
  name: string;
  header?: object[];
  body?: any[];
  button?: string;
  fileName?: string;
}): Promise<any> {
  const { to, name, header, body, button, fileName, orgId, userId, title } = options;
const marketingAccessToken = 'EAAQoOMDMYXwBO4pWsjFj4zOv1s4zTrZArDEdUPDzD9nOZAAUKt5YCfzWAFUBjb3ykZCZAsBcffjzc1tQRUma1jrpxsbNXCdhAJ9M54cWhY8ZCcexaOOgGqpVk15CgP0RoO1qQyPEkcEWOfsiR8r210u2mZCohmqGRr409ivgVNSpftKHEfvdLLj0LS9bxFZBKpBMwZDZD'
  const templateData = {
    messaging_product: 'whatsapp',
    recipient_type: 'individual',
    to,
    type: 'template',
    template: {
      name,
      language: {
        code: 'en',
      },
      components: [],
    },
  };

  // Dynamically construct header, body, and button components
  if (header && header.length > 0) {
    templateData.template.components.push({
      type: 'header',
      parameters: header
        .map((header: any) => {
          if (header.type === 'text') {
            return {
              type: 'text',
              text: header.text,
            };
          } else if (header.type === 'document') {
            return {
              type: 'document',
              document: {
                link: header.link,
                filename: fileName,
              },
            }
          } 
          else if (header.type === 'image') { 
            return {
              type: 'image',
              image: {
                link: header.link,
              },
            };
          }
      
          // Add more conditions for other types if needed
          return null; // Handle other types accordingly
        })
        .filter(Boolean), // Filter out null values
    });
  }
  if (body && body.length > 0) {
    templateData.template.components.push({
      type: 'body',
      parameters: body.map((bodyText) => ({
        type: 'text',
        text: `${bodyText}`,
      })),
    });
  }
  if (button && button.length > 0) {
    templateData.template.components.push({
      type: 'button',
      sub_type: 'URL',
      index: 0,
      parameters: [
        {
          type: 'text',
          text: button,
        },
      ],
    });
  }
  const config: AxiosRequestConfig = {
    method: 'post',
    url: `https://graph.facebook.com/v18.0/${531157636751620}/messages`,
    headers: {
      'Authorization': `Bearer ${marketingAccessToken}`,
      'Content-Type': 'application/json',
    },
    data: JSON.stringify(templateData),
  };
  try {
    const response = await axios(config);
    if (response?.data) {
      const whatsappAudit = new WhatsappAudit();
      whatsappAudit.mobileNumber = to;
      whatsappAudit.userId = userId;
      whatsappAudit.organizationId = orgId;
      whatsappAudit.title = title;
      whatsappAudit.messageType = MessageType.TEMPLATE;
      whatsappAudit.phoneNumberId = 'check marketing';

      await whatsappAudit.save();
    }
    return response?.data;
  } catch (error) {
    console.error(error.message);
    return error;
  }

}
export async function sendClientWhatsAppTemplateMessage(options: {
  to: any;
  orgId: number;
  title: string;
  userId?: number;
  name: string;
  header?: object[];
  body?: any[];
  button?: string;
  fileName?: string;
  key: string;
}): Promise<any> {
  const { to, name, header, body, button, fileName, orgId, userId, title, key } = options;
  try {
    if (orgId) {
      const orgPreferences: any = await OrganizationPreferences.findOne({
        where: { organization: orgId },
      });

      const webhookacesstoken= orgPreferences?.metaConfig?.accessToken 
      const businessId = orgPreferences?.metaConfig?.businessId
      const notificationConfig = orgPreferences?.notificationConfig?.whatsappPreferences;
      const clientPreferences = orgPreferences?.clientPreferences?.whatsapp;

      if (notificationConfig) {

        if (clientPreferences && clientPreferences[key]) {
          const templateData = {
            messaging_product: 'whatsapp',
            recipient_type: 'individual',
            to,
            type: 'template',
            template: {
              name,
              language: {
                code: 'en',
              },
              components: [],
            },
          };

          // Dynamically construct header, body, and button components
          if (header && header.length > 0) {
            templateData.template.components.push({
              type: 'header',
              parameters: header
                .map((header: any) => {
                  if (header.type === 'text') {
                    return {
                      type: 'text',
                      text: header.text,
                    };
                  } else if (header.type === 'document') {
                    return {
                      type: 'document',
                      document: {
                        link: header.link,
                        filename: fileName,
                      },
                    };
                  }
                  // Add more conditions for other types if needed
                  return null; // Handle other types accordingly
                })
                .filter(Boolean), // Filter out null values
            });
          }

          if (body && body.length > 0) {
            templateData.template.components.push({
              type: 'body',
              parameters: body.map((bodyText) => ({
                type: 'text',
                text: `${bodyText}`,
              })),
            });
          }

          if (button && button.length > 0) {
            templateData.template.components.push({
              type: 'button',
              sub_type: 'URL',
              index: 0,
              parameters: [
                {
                  type: 'text',
                  text: button,
                },
              ],
            });
          }

          const webhookacesstoken= orgPreferences?.metaConfig?.accessToken
          const businessId = orgPreferences?.metaConfig?.businessId

          const config: AxiosRequestConfig = {
            method: 'post',
            url: `https://graph.facebook.com/v18.0/${businessId? businessId: 108161499033381}/messages`,
            headers: {
              'Authorization': `Bearer ${webhookacesstoken? webhookacesstoken: whatsappAccessToken}`,
              'Content-Type': 'application/json',
            },
            data: JSON.stringify(templateData),
          };
          try {
            console.log(to,'to')
            const response = await axios(config);
            if (response?.data) {
              const whatsappAudit = new WhatsappAudit();
              whatsappAudit.mobileNumber = to;
              whatsappAudit.userId = userId;
              whatsappAudit.organizationId = orgId;
              whatsappAudit.title = title;
              whatsappAudit.messageType = MessageType.TEMPLATE;
              whatsappAudit.phoneNumberId = businessId;

              await whatsappAudit.save();
            }
            return response?.data;
          } catch (error) {
// console.error('errrrrrrrrrrrrrrrrrr');
            return error;
          }
        }
      }
    }
  } catch (error) {
  }
}

export async function sendWhatsAppTemplateMessageUS(options: {
  to: any;
  name: string;
  header?: object[];
  body?: any[];
  button?: string;
  fileName?: string;
  orgId: number;
  title: string;
  userId: number;
  key: string;
}): Promise<any> {
  const { to, name, header, body, button, fileName, orgId, title, userId, key } = options;
  console.log('11111')
  try {
    if (orgId) {
      console.log('2222222')
      const orgPreferences: any = await OrganizationPreferences.findOne({
        where: { organization: orgId },
      });
console.log('33333')
      const notificationConfig = orgPreferences?.notificationConfig?.whatsappPreferences;
      const clientPreferences = orgPreferences?.clientPreferences?.whatsapp;
      if (notificationConfig) {
        console.log('4444444')
        if (clientPreferences && clientPreferences[key]) {
          console.log('55555')
          const templateData = {
            messaging_product: 'whatsapp',
            recipient_type: 'individual',
            to,
            type: 'template',
            template: {
              name,
              language: {
                code: 'en_US',
              },
              components: [],
            },
          };

          // Dynamically construct header, body, and button components
          if (header && header.length > 0) {
            templateData.template.components.push({
              type: 'header',
              parameters: header
                .map((header: any) => {
                  if (header.type === 'text') {
                    return {
                      type: 'text',
                      text: header.text,
                    };
                  } else if (header.type === 'document') {
                    return {
                      type: 'document',
                      document: {
                        link: header.link,
                        filename: fileName,
                      },
                    };
                  }
                  // Add more conditions for other types if needed
                  return null; // Handle other types accordingly
                })
                .filter(Boolean), // Filter out null values
            });
          }

          if (body && body.length > 0) {
            templateData.template.components.push({
              type: 'body',
              parameters: body.map((bodyText) => ({
                type: 'text',
                text: `${bodyText}`,
              })),
            });
          }

          if (button && button.length > 0) {
            templateData.template.components.push({
              type: 'button',
              sub_type: 'URL',
              index: 0,
              parameters: [
                {
                  type: 'text',
                  text: button,
                },
              ],
            });
          }

          const webhookacesstoken= orgPreferences?.metaConfig?.accessToken
          const businessId = orgPreferences?.metaConfig?.businessId
          const config: AxiosRequestConfig = {
            method: 'post',
            url: `https://graph.facebook.com/v18.0/${businessId? businessId: 108161499033381}/messages`,
            headers: {
              'Authorization': `Bearer ${webhookacesstoken? webhookacesstoken: whatsappAccessToken}`,
              'Content-Type': 'application/json',
            },
            data: JSON.stringify(templateData),
          };
console.log('666666')
          try {
            const response = await axios(config);
            if (response?.data) {
              const whatsappAudit = new WhatsappAudit();
              whatsappAudit.mobileNumber = to;
              whatsappAudit.userId = userId;
              whatsappAudit.organizationId = orgId;
              whatsappAudit.title = title;
              whatsappAudit.messageType = MessageType.TEMPLATE;
              whatsappAudit.phoneNumberId = businessId;

              await whatsappAudit.save();
            }
            return response?.data;
          } catch (error) {
            console.error(error.message);
            return error;
          }
        }
      }
    }
  } catch (error) {
  }
}

export async function sendWhatsAppTemplateMessageNotices(options: {
  to: any;
  name: string;
  header?: object[];
  body?: any[];
  button?: string;
  fileName?: string;
  orgId: number;
  title: string;
  userId: number;
  key: string;
  preference: string;
}): Promise<any> {
  const { to, name, header, body, button, fileName, orgId, title, userId, key, preference } = options;

  try {
    if (orgId) {
      const orgPreferences: any = await OrganizationPreferences.findOne({
        where: { organization: orgId },
      });

      if(orgPreferences?.notificationConfig?.whatsappPreferences){

      const userPresentCheck = await NotificationPreferences.findOne({
        where: { user: userId },
      });

      if (userPresentCheck && preference) {
        if (userPresentCheck?.whatsapp) {
          const whatsUpKeys = Object.keys(userPresentCheck?.whatsapp);
          const checkKeyPresent = whatsUpKeys.includes(preference);

          if (checkKeyPresent) {
            const templateData = {
              messaging_product: 'whatsapp',
              recipient_type: 'individual',
              to,
              type: 'template',
              template: {
                name,
                language: {
                  code: 'en',
                },
                components: [],
              },
            };

            // Header
            if (header && header.length > 0) {
              templateData.template.components.push({
                type: 'header',
                parameters: header
                  .map((header: any) => {
                    if (header.type === 'text') {
                      return {
                        type: 'text',
                        text: header.text,
                      };
                    } else if (header.type === 'document') {
                      return {
                        type: 'document',
                        document: {
                          link: header.link,
                          filename: fileName,
                        },
                      };
                    }
                    return null;
                  })
                  .filter(Boolean),
              });
            }

            // Body
            if (body && body.length > 0) {
              templateData.template.components.push({
                type: 'body',
                parameters: body.map((bodyText) => ({
                  type: 'text',
                  text: `${bodyText}`,
                })),
              });
            }

            // Button
            if (button && button.length > 0) {
              templateData.template.components.push({
                type: 'button',
                sub_type: 'URL',
                index: 0,
                parameters: [
                  {
                    type: 'text',
                    text: button,
                  },
                ],
              });
            }

            const webhookacesstoken = orgPreferences?.metaConfig?.accessToken;
            const businessId = orgPreferences?.metaConfig?.businessId;

            const config: AxiosRequestConfig = {
              method: 'post',
              url: `https://graph.facebook.com/v18.0/${businessId ? businessId : 108161499033381}/messages`,
              headers: {
                'Authorization': `Bearer ${webhookacesstoken ? webhookacesstoken : whatsappAccessToken}`,
                'Content-Type': 'application/json',
              },
              data: JSON.stringify(templateData),
            };

            try {
              const response = await axios(config);
              if (response?.data) {
                const whatsappAudit = new WhatsappAudit();
                whatsappAudit.mobileNumber = to;
                whatsappAudit.userId = userId;
                whatsappAudit.organizationId = orgId;
                whatsappAudit.title = title;
                whatsappAudit.messageType = MessageType.TEMPLATE;
                whatsappAudit.phoneNumberId = businessId;

                await whatsappAudit.save();
              }
              return response?.data;
            } catch (error) {
              console.error(error.message);
              return error;
            }
          }
        }
      }


      }


    }
  } catch (error) {
  }
}

export async function sendWhatsAppTextMessageDocument(to: string, body: string, orgId, title, userId,link,caption,fileName) {
  let data = {
    messaging_product: 'whatsapp',
    recipient_type: 'individual',
    to: to,
    orgId: orgId,
    title: title,
    userId: userId,
    type: 'document',
    document: {
     link: link,
  caption: caption,
  filename: fileName
}
  };
  if (orgId) {
    const orgPreferences: any = await OrganizationPreferences.findOne({
      where: { organization: orgId },
    });
    const webhookacesstoken= orgPreferences?.metaConfig?.accessToken
        const businessId = orgPreferences?.metaConfig?.businessId
  const config: AxiosRequestConfig = {
    method: 'post',
    maxBodyLength: Infinity,
    url: `https://graph.facebook.com/v17.0/${businessId? businessId: 108161499033381}/messages`,
    headers: {
      'Authorization': `Bearer ${webhookacesstoken? webhookacesstoken: whatsappAccessToken}`,
      'Content-Type': 'application/json',
    },
    data: data,
  };

  try {
    const response = await axios(config);
    if (response?.data) {
      const whatsappAudit = new WhatsappAudit();
      whatsappAudit.mobileNumber = to;
      whatsappAudit.userId = userId;
      whatsappAudit.organizationId = orgId;
      whatsappAudit.title = title;
      whatsappAudit.messageType = MessageType.TEXT;
              whatsappAudit.phoneNumberId = businessId;

      await whatsappAudit.save();
    }
    return response?.data;
  } catch (error) {
    console.error(error.message);
    return error;
  }
}
}

export async function sendWhatsAppTextMessage(
  to: any,
  body: string,
  orgId: number,
  title: string,
  userId: number,
  key?: string,
) {
  if (key === 'OVERVIEW_MESSAGE') {
    const abcd: any = to;
    let data = {
      messaging_product: 'whatsapp',
      recipient_type: 'individual',
      to: to,
      orgId: orgId,
      title: title,
      userId: userId,
      type: 'text',
      text: {
        preview_url: false,
        body: body,
      },
    };
    const orgPreferences: any = await OrganizationPreferences.findOne({
      where: { organization: orgId },
    });
    const webhookacesstoken= orgPreferences?.metaConfig?.accessToken
        const businessId = orgPreferences?.metaConfig?.businessId
    const config: AxiosRequestConfig = {
      method: 'post',
      maxBodyLength: Infinity,
      url: `https://graph.facebook.com/v17.0/${businessId? businessId: 108161499033381}/messages`,
      headers: {
        'Authorization': `Bearer ${webhookacesstoken? webhookacesstoken: whatsappAccessToken}`,
        'Content-Type': 'application/json',
      },
      data: data,
    };
    try {
      const response = await axios(config);
      if (response?.data) {
        const whatsappAudit = new WhatsappAudit();
        whatsappAudit.mobileNumber = to;
        whatsappAudit.userId = userId;
        whatsappAudit.organizationId = orgId;
        whatsappAudit.title = title;
        whatsappAudit.messageType = MessageType.TEXT;
        whatsappAudit.phoneNumberId = businessId;

        await whatsappAudit.save();
      }
      return response?.data;
    } catch (error) {
      console.error(error.message);
      return error;
    }
  } else {
    if (userId) {
      const userPresentCheck = await NotificationPreferences.findOne({
        where: { user: userId },
      });
      if (userPresentCheck && key) {
        if (userPresentCheck?.whatsapp) {
          const whatsUpKeys = Object.keys(userPresentCheck?.whatsapp);
          const checkKeyPresent = whatsUpKeys.includes(key);
          if (checkKeyPresent) {
            const abcd: any = to;
            let data = {
              messaging_product: 'whatsapp',
              recipient_type: 'individual',
              to: to,
              orgId: orgId,
              title: title,
              userId: userId,
              type: 'text',
              text: {
                preview_url: false,
                body: body,
              },
            };
            const orgPreferences: any = await OrganizationPreferences.findOne({
              where: { organization: orgId },
            });
            const webhookacesstoken= orgPreferences?.metaConfig?.accessToken

                const businessId = orgPreferences?.metaConfig?.businessId

            const config: AxiosRequestConfig = {
              method: 'post',
              maxBodyLength: Infinity,
              url: `https://graph.facebook.com/v17.0/${businessId? businessId: 108161499033381}/messages`,
              headers: {
                'Authorization': `Bearer ${webhookacesstoken? webhookacesstoken: whatsappAccessToken}`,
                'Content-Type': 'application/json',
              },
              data: data,
            };
            try {
              const response = await axios(config);
              if (response?.data) {
                const whatsappAudit = new WhatsappAudit();
                whatsappAudit.mobileNumber = to;
                whatsappAudit.userId = userId;
                whatsappAudit.organizationId = orgId;
                whatsappAudit.title = title;
                whatsappAudit.messageType = MessageType.TEXT;
                whatsappAudit.phoneNumberId = businessId;

                await whatsappAudit.save();
              }
              return response?.data;
            } catch (error) {
              console.error(error.message);
              return error;
            }
          }
        }
      }
    }
  }
}

export async function sendWhatsAppTextMessageWithOrgPreference(
  to: string,
  body: string,
  orgId: number,
  title: string,
  userId: number,
  key?: string,
) {
  if (!orgId) return;

  const orgPreferences: any = await OrganizationPreferences.findOne({
    where: { organization: orgId },
  });

  if (orgPreferences && key) {
    if (orgPreferences?.whatsapp) {
      const whatsUpKeys = Object.keys(orgPreferences.whatsapp);
      const checkKeyPresent = whatsUpKeys.includes(key);

      if (checkKeyPresent) {
        const data = {
          messaging_product: 'whatsapp',
          recipient_type: 'individual',
          to,
          orgId,
          title,
          userId,
          type: 'text',
          text: {
            preview_url: false,
            body,
          },
        };

        const webhookAccessToken =
          orgPreferences?.metaConfig?.accessToken || whatsappAccessToken;
        const businessId =
          orgPreferences?.metaConfig?.businessId || '108161499033381';

        const config: AxiosRequestConfig = {
          method: 'post',
          maxBodyLength: Infinity,
          url: `https://graph.facebook.com/v17.0/${businessId}/messages`,
          headers: {
            Authorization: `Bearer ${webhookAccessToken}`,
            'Content-Type': 'application/json',
          },
          data,
        };

        try {
          const response = await axios(config);

          if (response?.data) {
            const whatsappAudit = new WhatsappAudit();
            whatsappAudit.mobileNumber = to;
            whatsappAudit.userId = userId;
            whatsappAudit.organizationId = orgId;
            whatsappAudit.title = title;
            whatsappAudit.messageType = MessageType.TEXT;
            whatsappAudit.phoneNumberId = businessId;

            await whatsappAudit.save();
          }

          return response?.data;
        } catch (error: any) {
          console.error(error.message);
          return error;
        }
      }
    }
  }
}
export async function sendDocumentTextMessage(
  to: string, 
  pdfLink: string, 
  caption: string, 
  filename: string, 
  orgId: number, 
  userId: number, 
  title: string, 
  key?: string
) {

  if (userId) {
    const userPresentCheck = await NotificationPreferences.findOne({
      where: { user: userId },
    });

    if (userPresentCheck && key) {
      if (userPresentCheck?.whatsapp) {
        const whatsUpKeys = Object.keys(userPresentCheck?.whatsapp);
        const checkKeyPresent = whatsUpKeys.includes(key);

        if (checkKeyPresent) {
          let data = {
            messaging_product: "whatsapp",
            recipient_type: "individual",
            to: to,
            type: "document",
            document: {
              link: pdfLink,
              caption: caption,
              filename: filename,
            },
          };

          const orgPreferences: any = await OrganizationPreferences.findOne({
            where: { organization: orgId },
          });

          const webhookAccessToken = orgPreferences?.metaConfig?.accessToken;
          const businessId = orgPreferences?.metaConfig?.businessId;


          const config: AxiosRequestConfig = {
            method: 'post',
            maxBodyLength: Infinity,
            url: `https://graph.facebook.com/v17.0/${businessId ? businessId : 108161499033381}/messages`,
            headers: {
              'Authorization': `Bearer ${webhookAccessToken ? webhookAccessToken : whatsappAccessToken}`,
              'Content-Type': 'application/json',
            },
            data: data,
          };

          try {
            const response = await axios(config);

            if (response?.data) {
              const whatsappAudit = new WhatsappAudit();
              whatsappAudit.mobileNumber = to;
              whatsappAudit.userId = userId;
              whatsappAudit.organizationId = orgId;
              whatsappAudit.title = title;
              whatsappAudit.messageType = MessageType.DOCUMENT;
              whatsappAudit.phoneNumberId = businessId;

              await whatsappAudit.save();
            }

            return response?.data;
          } catch (error) {
            console.error('Error sending document message:', error.response?.data || error.message);
            return error;
          }
        }
      }
    }
  }
}


export async function sendDocumentInvoiceData(
  to: string,
  location,
  invoiceNumber,
  orgId,
  title,
  userId,
  key?: string,
) {
  try {
    if (userId) {
      const userPresentCheck = await NotificationPreferences.findOne({
        where: { user: userId },
      });
      if (userPresentCheck && key) {
        if (userPresentCheck?.whatsapp) {
          const whatsUpKeys = Object.keys(userPresentCheck?.whatsapp);
          const checkKeyPresent = whatsUpKeys.includes(key);
          if (checkKeyPresent) {
            let data = JSON.stringify({
              messaging_product: 'whatsapp',
              recipient_type: 'individual',
              to: to,
              userId: userId,
              orgId: orgId,
              title: title,
              type: 'document',
              document: {
                link: location,
                filename: `invoice-${invoiceNumber}.pdf`,
              },
            });

            let config: AxiosRequestConfig = {
              method: 'post',
              maxBodyLength: Infinity,
              url: 'https://graph.facebook.com/v18.0/108161499033381/messages',
              headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${whatsappAccessToken}`,
              },
              data: data,
            };
            try {
              const response = await axios(config);
              if (response?.data) {
                const whatsappAudit = new WhatsappAudit();
                whatsappAudit.mobileNumber = to;
                whatsappAudit.userId = userId;
                whatsappAudit.organizationId = orgId;
                whatsappAudit.title = title;
                whatsappAudit.messageType = MessageType.DOCUMENT;
                await whatsappAudit.save();
              }
              return response?.data;
            } catch (error) {
              console.error(error.message);
              return error;
            }
          }
        }
      }
    }
  } catch (error) {
    console.error(error);
  }
}

export async function sendDocumentReceiptData(
  to: string,
  location,
  receiptNumber,
  orgId,
  title,
  userId,
  key?: string,
) {
  try {
    if (userId) {
      const userPresentCheck = await NotificationPreferences.findOne({
        where: { user: userId },
      });
      if (userPresentCheck && key) {
        if (userPresentCheck?.whatsapp) {
          const whatsUpKeys = Object.keys(userPresentCheck?.whatsapp);
          const checkKeyPresent = whatsUpKeys.includes(key);
          if (checkKeyPresent) {
            let data = JSON.stringify({
              messaging_product: 'whatsapp',
              recipient_type: 'individual',
              to: to,
              userId: userId,
              orgId: orgId,
              title: title,
              type: 'document',
              document: {
                link: location,
                filename: `receipt-${receiptNumber}.pdf`,
              },
            });

            let config: AxiosRequestConfig = {
              method: 'post',
              maxBodyLength: Infinity,
              url: 'https://graph.facebook.com/v18.0/108161499033381/messages',
              headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${whatsappAccessToken}`,
              },
              data: data,
            };
            try {
              const response = await axios(config);
              if (response?.data) {
                const whatsappAudit = new WhatsappAudit();
                whatsappAudit.mobileNumber = to;
                whatsappAudit.userId = userId;
                whatsappAudit.organizationId = orgId;
                whatsappAudit.title = title;
                whatsappAudit.messageType = MessageType.DOCUMENT;
                await whatsappAudit.save();
              }
              return response?.data;
            } catch (error) {
              console.error(error.message);
              return error;
            }
          }
        }
      }
    }
  } catch (error) {
    console.error(error);
  }
}

export async function getOverviewMessageData(userId: number, organizationId: number) {
  const tasks = await createQueryBuilder(Task, 'task')
    .leftJoinAndSelect('task.user', 'user')
    .leftJoinAndSelect('task.organization', 'organization')
    .leftJoinAndSelect('task.members', 'members')
    .leftJoinAndSelect('task.client', 'client')

    .where('task.organization = :organization', { organization: organizationId })
    .andWhere('members.id = :userId', { userId })
    .andWhere('DATE(task.dueDate) = :today', {
      today: moment().format('YYYY-MM-DD'),
    })
    .getMany();

  //DSC due today
  const Dsc = await createQueryBuilder(DscRegister, 'dsc')
    .leftJoinAndSelect('dsc.organization', 'organization')

    .where('dsc.organization = :organization', { organization: organizationId })

    .andWhere('DATE(dsc.expiryDate ) = :today', {
      today: moment().format('YYYY-MM-DD'),
    })
    .getMany();

  //events due today
  const events = await createQueryBuilder(Event, 'event')
    .leftJoinAndSelect('event.members', 'members')
    .leftJoinAndSelect('event.user', 'user')
    .leftJoinAndSelect('event.client', 'client')
    .leftJoinAndSelect('event.task', 'task')
    .where('event.type=:type', { type: EventTypeEnum.TASK })
    .andWhere('members.id = :userId', { userId })
    .andWhere('DATE(event.date ) = :today', {
      today: moment().format('YYYY-MM-DD'),
    })
    .orWhere('event.type=:newType AND user.id=:createUser AND DATE(event.date ) = :today2', {
      newType: EventTypeEnum.EVENT,
      createUser: userId,
      today2: moment().format('YYYY-MM-DD'),
    })
    .getMany();


  //overdue
  let overduetasks = await createQueryBuilder(Task, 'task')
    .leftJoin('task.organization', 'organization')
    .leftJoinAndSelect('task.category', 'category')
    .leftJoinAndSelect('task.members', 'members')
    .leftJoinAndSelect('task.client', 'client')
    .where('organization.id = :id', { id: organizationId })
    .andWhere('task.status not in (:...status)', {
      status: ['terminated', 'deleted', 'completed'],
    })
    .andWhere("(task.recurring_status is null or task.recurring_status = 'created')")
    .andWhere('task.dueDate < :date', { date: moment().format('YYYY-MM-DD') })
    .andWhere('task.parentTask is null')
    .andWhere('members.id = :userId', { userId })
    .getMany();
  const data = { Tasks: tasks, Dsc: Dsc, Events: events, Overduetasks: overduetasks };
  return data;
}

export async  function getDailyAdminMessageData(userId: number, organizationId: number){


let user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
let tasks = createQueryBuilder(Task, 'task')
.leftJoin('task.organization', 'organization')
.leftJoin('task.members', 'taskMembers')
.leftJoinAndSelect('task.category', 'category')
.leftJoinAndSelect('task.members', 'members')
.leftJoinAndSelect('members.imageStorage', 'imageStorage')
.leftJoinAndSelect('task.client', 'client')
.leftJoinAndSelect('task.clientGroup', 'clientGroup')
.where('organization.id = :id', { id: user.organization.id })
.andWhere('task.status not in (:...status)', {
  status: ['terminated', 'deleted', 'completed'],
})
.andWhere("(task.recurring_status is null or task.recurring_status = 'created')")
.andWhere('task.parentTask is null')
.andWhere('DATE(task.dueDate) = :today', {
    today: moment().format('YYYY-MM-DD'),
  })
.orderBy('task.dueDate', 'ASC');



let dueresult = await tasks.getMany();


// return result;
//task overdue

let userr = await User.findOne({ where: { id: userId }, relations: ['organization'] });

let overdueTasks = createQueryBuilder(Task, 'task')
  .leftJoin('task.organization', 'organization')
  .leftJoinAndSelect('task.category', 'category')
  .leftJoinAndSelect('task.members', 'members')
  .leftJoinAndSelect('task.client', 'client')
  .leftJoinAndSelect('task.clientGroup', 'clientGroup')
  .where('organization.id = :id', { id: userr.organization.id })
  .andWhere('task.status not in (:...status)', {
    status: ['terminated', 'deleted', 'completed'],
  })
  .andWhere("(task.recurring_status is null or task.recurring_status = 'created')")
  .andWhere('task.dueDate < :date', { date: moment().format('YYYY-MM-DD') })
  .andWhere('task.parentTask is null')

  // .orderBy('task.dueDate', 'ASC')
 




let overdueresult = await overdueTasks.getCount();


return {dueresult,overdueresult}

}

export async function buildBodyAndSendMessage(userId: number, organizationId: number) {
  const data = await getOverviewMessageData(userId, organizationId);
  const adminData = await getDailyAdminMessageData(userId, organizationId)
  const { Tasks, Dsc, Events, Overduetasks } = data;
  const {dueresult,overdueresult} = adminData
  // const userDetails = await getUserDetails(userId);
  let userDetails = await User.findOne({
    where: { id: userId },
    relations: ['organization','role'],
  });
  
if(userDetails.role.name !== "Admin"){
  const {
    fullName: userFullName,
    mobileNumber: userPhoneNumber,
    id: userIDD,
    countryCode: countryCode,
  } = userDetails;
  // Overview Body
  const overviewBody = `
  Hi *${userFullName}*,
  
  This is the overview of today's updates!

  *No.of Tasks Due Today:* ${Tasks.length}
  *No.of DSC's Expiring Today:* ${Dsc.length}
  *No.of Events Today:* ${Events.length}
  *No.of Overdue Tasks till Today:* ${Overduetasks.length}
  
  We hope this helps!
  `;
  //---------------------------------------------------------------------------------------------
  //Task Dues Body
  let taskDuesMessageBody = `
  Hi *${userFullName}*,
  `;
  for (let task of Tasks) {
    const { name, taskNumber, user, client, organization, members } = task;
    const clientName = client?.displayName ? client?.displayName : client?.fullName;
    const membersList = members?.map((member) => member?.fullName).join(', ');

    taskDuesMessageBody += `
  *Task Name:* ${name}
  *Task Number:* ${taskNumber}
  *Client Name:* ${clientName}
  *Members:* ${membersList}
  ----------------------------
  `;
  }
  taskDuesMessageBody += `
  We hope this helps!`;

  //---------------------------------------------------------------------------------------------
  //DSC Expiry
  let dscExpiryMessageBody = `
  Hi *${userFullName}*
  `;
  for (let dsc of Dsc) {
    const { holderName, mobileNumber, email } = dsc;
    dscExpiryMessageBody += `
  *Holder Name:* ${holderName}
  ${mobileNumber ? `*Mobile Number:* ${mobileNumber}` : ''}
  ${email ? `*Email:* ${email}` : ''}
  ----------------------------
  `;
  }
  dscExpiryMessageBody += `
  We hope this helps!`;

  //---------------------------------------------------------------------------------------------
  // Events Due
  let eventsDueTodayMessageBody = `
  Hi *${userFullName}*
  `;
  for (let event of Events) {
    const { type, title, client, date, location, task, members } = event;

    const clientName = client?.displayName ? client?.displayName : client?.fullName;
    const taskName = task?.name;

    const inputDate = new Date(date);

    const day = String(inputDate.getDate()).padStart(2, '0');
    const month = String(inputDate.getMonth() + 1).padStart(2, '0'); // Month is zero-based
    const year = inputDate.getFullYear();

    const localDate = new Date(date).toLocaleString('en-US', {
      hour: 'numeric',
      minute: 'numeric',
      hour12: true,
    });
    const [formattedTime] = localDate.split(', ');
const eventDate = moment(event?.date).utcOffset('+05:30').format("MMMM DD, YYYY");
    const formattedStartTime = moment(event?.startTime, "YYYY-MM-DD HH:mm:ss").utcOffset('+05:30').format("hh:mm a");
    const formattedEndTime = moment(event?.endTime, "YYYY-MM-DD HH:mm:ss").utcOffset('+05:30').format("hh:mm a");
    const timeRange = `${formattedStartTime} - ${formattedEndTime}`;
    const Time = `${eventDate},${timeRange}`;
    const formattedDateTime = `${day}-${month}-${year} | ${formattedTime}`;

    const eventTypeSuffix = type === 'TASK' ? '(Task Event)' : '(General Event)';
    const typeEvent = `${title} ${eventTypeSuffix}`;

    eventsDueTodayMessageBody += `
  *Event name:* ${typeEvent}
  *Date and Time:* ${Time}
  *Location:* ${location}
  ${clientName ? `*Client Name:* ${clientName}` : ''}
  ${taskName ? `*Task Name:* ${taskName}` : ''}
  ----------------------------
  `;
  }
  eventsDueTodayMessageBody += `
  We hope this helps!`;

  // ------------------------------------------------------------------------------------------
  // Tasksoverduetilltoday
  let tasksoverduetilltodayMessageBody = `
  Hi *${userFullName}*,
  `;
  for (let overduetask of Overduetasks) {
    const { name, taskNumber, user, client, organization, members } = overduetask;
    const clientName = client?.displayName ? client?.displayName : client?.fullName;
    const membersList = members?.map((member) => member?.fullName).join(', ');

    tasksoverduetilltodayMessageBody += `
  *Task Name:* ${name}
  *Task Number:* ${taskNumber}
  *Client Name:* ${clientName}
  *Members:* ${membersList}
  ----------------------------
  `;
  }
  tasksoverduetilltodayMessageBody += `
  We hope this helps!`;
  //Tasks, Dsc, Events
  const title = 'overview message';
  const key = 'OVERVIEW_MESSAGE';
  try {
    const overviewMessageResponse = await sendWhatsAppTextMessage(
      // `91${userPhoneNumber}`,
      fullMobileNumberWithCountry(userPhoneNumber, countryCode),
      overviewBody,
      organizationId,
      title,
      userIDD,
      key,
    );

    let taskdueMessageResponse = '';
    if (Tasks?.length > 0) {
      taskdueMessageResponse = await sendWhatsAppTextMessage(
        // `91${userPhoneNumber}`,
        fullMobileNumberWithCountry(userPhoneNumber, countryCode),
        taskDuesMessageBody,
        organizationId,
        title,
        userIDD,
        key,
      );
    }

    let dscExpiryMessageResponse = '';

    if (Dsc?.length > 0) {
      dscExpiryMessageResponse = await sendWhatsAppTextMessage(
        // `91${userPhoneNumber}`,
        fullMobileNumberWithCountry(userPhoneNumber, countryCode),
        dscExpiryMessageBody,
        organizationId,
        title,
        userIDD,
        key,
      );
    }

    let eventsMessageResponse = '';
    if (Events?.length > 0) {
      eventsMessageResponse = await sendWhatsAppTextMessage(
        // `91${userPhoneNumber}`,
        fullMobileNumberWithCountry(userPhoneNumber, countryCode),
        eventsDueTodayMessageBody,
        organizationId,
        title,
        userIDD,
        key,
      );
    }
    const messagesResponse = {
      overviewMessage: overviewMessageResponse,
      taskMessage: taskdueMessageResponse,
      dscMessage: dscExpiryMessageResponse,
      eventMessage: eventsMessageResponse,
    };
    return messagesResponse;
  } catch (error) {
    console.error('Error sending WhatsApp messages:', error);
    // Handle the error as needed
  }
}else{
  const {
    fullName: userFullName,
    mobileNumber: userPhoneNumber,
    id: userIDD,
    countryCode: countryCode,
  } = userDetails;

  // Overview Body
  const overviewBody = `
  Hi *${userFullName}*,
  
  This is the overview of today's updates!
  
  *No.of Tasks Due Today:* ${dueresult.length}
  *No.of DSC's Expiring Today:* ${Dsc.length}
  *No.of Events Today:* ${Events.length}
  *No.of Overdue Tasks till Today:* ${overdueresult}
  
  We hope this helps!
  `;
  //---------------------------------------------------------------------------------------------
  //Task Dues Body
  let taskDuesMessageBody = `
  Hi *${userFullName}*,
  `;
  for (let task of dueresult) {
    const { name, taskNumber, user, client, organization, members } = task;
    const clientName = client?.displayName ? client?.displayName : client?.fullName;
    const membersList = members?.map((member) => member?.fullName).join(', ');

    taskDuesMessageBody += `
  *Task Name:* ${name}
  *Task Number:* ${taskNumber}
  *Client Name:* ${clientName}
  *Members:* ${membersList}
  ----------------------------
  `;
  }
  taskDuesMessageBody += `
  We hope this helps!`;

  //---------------------------------------------------------------------------------------------
  //DSC Expiry
  let dscExpiryMessageBody = `
  Hi *${userFullName}*
  `;
  for (let dsc of Dsc) {
    const { holderName, mobileNumber, email } = dsc;
    dscExpiryMessageBody += `
  *Holder Name:* ${holderName}
  ${mobileNumber ? `*Mobile Number:* ${mobileNumber}` : ''}
  ${email ? `*Email:* ${email}` : ''}
  ----------------------------
  `;
  }
  dscExpiryMessageBody += `
  We hope this helps!`;

  //---------------------------------------------------------------------------------------------
  // Events Due
  let eventsDueTodayMessageBody = `
  Hi *${userFullName}*
  `;
  for (let event of Events) {
    const { type, title, client, date, location, task, members } = event;

    const clientName = client?.displayName ? client?.displayName : client?.fullName;
    const taskName = task?.name;

    const inputDate = new Date(date);

    const day = String(inputDate.getDate()).padStart(2, '0');
    const month = String(inputDate.getMonth() + 1).padStart(2, '0'); // Month is zero-based
    const year = inputDate.getFullYear();

    const localDate = new Date(date).toLocaleString('en-US', {
      hour: 'numeric',
      minute: 'numeric',
      hour12: true,
    });
    const [formattedTime] = localDate.split(', ');
 const eventDate = moment(event?.date).utcOffset('+05:30').format("MMMM DD, YYYY");
    const formattedStartTime = moment(event?.startTime, "YYYY-MM-DD HH:mm:ss").utcOffset('+05:30').format("hh:mm a");
    const formattedEndTime = moment(event?.endTime, "YYYY-MM-DD HH:mm:ss").utcOffset('+05:30').format("hh:mm a");
    const timeRange = `${formattedStartTime} - ${formattedEndTime}`;
    const Time = `${eventDate},${timeRange}`;
    const formattedDateTime = `${day}-${month}-${year} | ${formattedTime}`;

    const eventTypeSuffix = type === 'TASK' ? '(Task Event)' : '(General Event)';
    const typeEvent = `${title} ${eventTypeSuffix}`;

    eventsDueTodayMessageBody += `
  *Event name:* ${typeEvent}
  *Date and Time:* ${Time}
  *Location:* ${location}
  ${clientName ? `*Client Name:* ${clientName}` : ''}
  ${taskName ? `*Task Name:* ${taskName}` : ''}
  ----------------------------
  `;
  }
  eventsDueTodayMessageBody += `
  We hope this helps!`;

  // ------------------------------------------------------------------------------------------
  // Tasksoverduetilltoday
  let tasksoverduetilltodayMessageBody = `
  Hi *${userFullName}*,
  `;
  for (let overduetask of Overduetasks) {
    const { name, taskNumber, user, client, organization, members } = overduetask;
    const clientName = client?.displayName ? client?.displayName : client?.fullName;
    const membersList = members?.map((member) => member?.fullName).join(', ');

    tasksoverduetilltodayMessageBody += `
  *Task Name:* ${name}
  *Task Number:* ${taskNumber}
  *Client Name:* ${clientName}
  *Members:* ${membersList}
  ----------------------------
  `;
  }
  tasksoverduetilltodayMessageBody += `
  We hope this helps!`;
  //Tasks, Dsc, Events
  const title = 'overview message';
  const key = 'OVERVIEW_MESSAGE';
  try {
    const overviewMessageResponse = await sendWhatsAppTextMessage(
      // `91${userPhoneNumber}`,
      fullMobileNumberWithCountry(userPhoneNumber, countryCode),
      overviewBody,
      organizationId,
      title,
      userIDD,
      key,
    );

    let taskdueMessageResponse = '';
    if (dueresult?.length > 0) {
      taskdueMessageResponse = await sendWhatsAppTextMessage(
        // `91${userPhoneNumber}`,
        fullMobileNumberWithCountry(userPhoneNumber, countryCode),
        taskDuesMessageBody,
        organizationId,
        title,
        userIDD,
        key,
      );
    }

    let dscExpiryMessageResponse = '';

    if (Dsc?.length > 0) {
      dscExpiryMessageResponse = await sendWhatsAppTextMessage(
        // `91${userPhoneNumber}`,
        fullMobileNumberWithCountry(userPhoneNumber, countryCode),
        dscExpiryMessageBody,
        organizationId,
        title,
        userIDD,
        key,
      );
    }

    let eventsMessageResponse = '';
    if (Events?.length > 0) {
      eventsMessageResponse = await sendWhatsAppTextMessage(
        // `91${userPhoneNumber}`,
        fullMobileNumberWithCountry(userPhoneNumber, countryCode),
        eventsDueTodayMessageBody,
        organizationId,
        title,
        userIDD,
        key,
      );
    }
    const messagesResponse = {
      overviewMessage: overviewMessageResponse,
      taskMessage: taskdueMessageResponse,
      dscMessage: dscExpiryMessageResponse,
      eventMessage: eventsMessageResponse,
    };
    return messagesResponse;
  } catch (error) {
    console.error('Error sending WhatsApp messages:', error);
    // Handle the error as needed
  }
}

}

export async function sendConversationData( from,text){
  const formattedNumber = from.replace(/^91/, ''); // Always remove leading '91'
  const client = await Client.findOne({
    where: [
      // { mobileNumber: from }, // Check as-is
      { mobileNumber: formattedNumber, 
        countryCode: "IN"  } // Check by removing 91 if IN
  ],    relations: ['organization', 'user'], // Ensure relations are fetched
});
const organizationId = client.organization ? client.organization.id : null;
    const userId = client.user ? client.user.id : null;
if (!client) {
    return null;
}
// Create a new conversation entry
const conversation = new WhatsappConversation();
conversation.mobileNumber = from;
conversation.message = text;
conversation.organizationId = organizationId;
conversation.userId = userId;
conversation.direction = 'incoming';
conversation.windowTime = moment().add(24, 'hours').toDate();


// Save to database
await conversation.save();




return conversation;
}

export async function sendDocumentConversationData( from,uploadedData,type){
  const formattedNumber = from.replace(/^91/, ''); // Always remove leading '91'
  const client = await Client.findOne({
    where: [
      // { mobileNumber: from }, // Check as-is
      { mobileNumber: formattedNumber, 
        countryCode: "IN"  } // Check by removing 91 if IN
  ],    relations: ['organization', 'user'], // Ensure relations are fetched
});
const organizationId = client.organization ? client.organization.id : null;
    const userId = client.user ? client.user.id : null;
if (!client) {
    return null;
}
// Create a new conversation entry
const conversation = new WhatsappConversation();
conversation.mobileNumber = from;
conversation.type = type;
conversation.link = uploadedData?.Location;
// conversation.message = text;
conversation.organizationId = organizationId;
conversation.userId = userId;
conversation.direction = 'incoming'
conversation.windowTime = moment().add(24, 'hours').toDate();

// Save to database
await conversation.save();




return conversation;
}