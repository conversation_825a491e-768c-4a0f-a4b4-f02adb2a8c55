import { IsNotEmpty, IsN<PERSON>berString, IsOptional } from 'class-validator';
import { Organization } from 'src/modules/organization/entities/organization.entity';
import { User } from '../entities/user.entity';

class InviteUserDto {
  @IsOptional()
  id: number;

  @IsNotEmpty()
  mobileNumber: string;

  @IsNotEmpty()
  email: string;

  @IsNotEmpty()
  fullName: string;

  @IsNotEmpty()
  @IsNumberString()
  role: number;

  @IsOptional()
  organization: Organization;

  @IsOptional()
  status: string;

  @IsNotEmpty()
  countryCode: string;

  @IsOptional()
  dialCode?: string;

  @IsNotEmpty()
  manager?: User;


}

export default InviteUserDto;
