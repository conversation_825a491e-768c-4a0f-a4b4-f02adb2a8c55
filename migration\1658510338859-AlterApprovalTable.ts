import { MigrationInterface, QueryRunner } from 'typeorm';

export class AlterApprovalTable1658510338859 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
        ALTER TABLE approval
        ADD COLUMN estimate_id int null,
        ADD FOREIGN KEY (estimate_id) REFERENCES estimate(id) ON DELETE CASCADE
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}
