import { <PERSON><PERSON> } from "aws-sdk/clients/robomaker";
import { BaseEntity, Column, CreateDateColumn, Entity, PrimaryColumn, PrimaryGeneratedColumn, UpdateDateColumn } from "typeorm";

export enum UserStatus {
    ACTIVE = 'ACTIVE',
    INACTIVE = 'INACTIVE',
    DELETED = 'DELETED',
  }
@Entity()
class ViderWhatsappSessions extends BaseEntity {
    @PrimaryGeneratedColumn()
    id: number;

    @Column()
    userId: number;

    @Column()
    organizationId: number;

    @Column('json')
    comments: string;

    @Column({ default: false })
    isSent: boolean;

    @Column({ type: 'timestamp', nullable: true })
    createdTimestamp: Date;

    @UpdateDateColumn()
    statusUpdatedTimestamp : Date;

    @Column({ type: 'enum', enum: UserStatus, default: UserStatus.ACTIVE })
  status: UserStatus;
    
}

export default ViderWhatsappSessions;