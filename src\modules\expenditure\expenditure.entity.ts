import { User } from 'src/modules/users/entities/user.entity';
import {
  AfterLoad,
  BaseEntity,
  Column,
  CreateDateColumn,
  Entity,
  JoinColumn,
  JoinTable,
  ManyToMany,
  ManyToOne,
  OneToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import Task from 'src/modules/tasks/entity/task.entity';
import {
  EXPENDITURE_STATUS,
  ExpenditureStatus,
  ExpenditureType,
  TaskExpenditureType,
} from './dto/types';
import Client from '../clients/entity/client.entity';
import Storage from '../storage/storage.entity';
import ClientGroup from '../client-group/client-group.entity';

@Entity()
class Expenditure extends BaseEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  particularName: string;

  @Column({ type: 'date' })
  date: string;

  @Column({ type: 'bigint' })
  amount: number;

  @Column({ type: 'varchar' })
  approvalDescription: string;

  @Column({ type: 'enum', enum: ExpenditureType, default: ExpenditureType.GENERAL })
  type: ExpenditureType;

  @Column({
    type: 'enum',
    enum: TaskExpenditureType,
    nullable: true,
  })
  taskExpenseType: TaskExpenditureType;

  @Column({ default: false })
  includeInInvoice: boolean;

  @CreateDateColumn()
  createdAt: string;

  @UpdateDateColumn()
  updatedAt: string;

  @Column({ nullable: true })
  attachment: string;

  @Column()
  remarks: string;

  @OneToOne(() => Storage, (storage) => storage.expenditure, { cascade: true })
  storage: Storage;

  @ManyToOne(() => Task, (task) => task.expenditure)
  task: Task;

  @ManyToOne(() => User, (user) => user.expenditure)
  user: User;

  @ManyToOne(() => Client, (client) => client.expenditure)
  client: Client;

  @ManyToOne(() => ClientGroup, (clientGroup) => clientGroup.expenditure)
  clientGroup: ClientGroup;

  @Column({
    type: 'enum',
    enum: ExpenditureStatus,
    default: ExpenditureStatus.PENDING,
  })
  status: ExpenditureStatus;

  @Column({ nullable: true, type: 'text' })
  rejectedReason: string;

  attachmentUrl: string;

  @ManyToMany(() => User, (user) => user.assignedExpenditure)
  @JoinTable()
  managers: User[];

  @ManyToOne(() => User, (user) => user.expenditureReviewer)
  reviewer: User;

  @Column()
  reviewedAt: string;

  @Column({
    type: 'enum',
    enum: EXPENDITURE_STATUS,
    default: null,
  })
  approvalStatus: EXPENDITURE_STATUS;

  @Column()
  requestedAt: string;

  @Column()
  expenditureNumber: string;

  @AfterLoad()
  renderUrl() {
    if (this.attachment) {
      this.attachmentUrl = `${process.env.AWS_BASE_URL}/${this.attachment}`;
    }
  }
}

export default Expenditure;
