import { Injectable } from '@nestjs/common';
import { Organization } from 'src/modules/organization/entities/organization.entity';
import * as moment from 'moment';
import { Brackets, createQ<PERSON><PERSON><PERSON><PERSON><PERSON>, getManager } from 'typeorm';
import { Cron, CronExpression } from '@nestjs/schedule';
import { User, UserStatus, UserType } from 'src/modules/users/entities/user.entity';
import { sendnewMail, sendnewMailToBusinessTeam } from 'src/emails/newemails';
import AutFyiNotice from '../entities/aut_income_tax_eproceedings_fyi_notice.entity';
import AutFyaNotice from '../entities/aut_income_tax_eproceedings_fya_notice.entity';
import CronActivity from 'src/modules/cron-activity/cron-activity.entity';
import {
  sendDocumentTextMessage,
  sendWhatsAppTemplateMessageNotices,
} from 'src/modules/whatsapp/whatsapp.service';
import ViderWhatsappSessions from 'src/modules/whatsapp/entity/viderWhatsappSessions';
import * as AWS from 'aws-sdk';
import * as ejs from 'ejs';
import * as puppeteer from 'puppeteer';
import AutomationMachines from '../entities/automation_machines.entity';
import IncTempEproFya from '../entities/inc_temp_epro_fya.entity';
import IncTempEproFyi from '../entities/inc_temp_epro_fyi.entity';
import AutomationBulkSync, { TypeEnum } from '../entities/automation_bulk_sync';
import axios from 'axios';
import Client from 'src/modules/clients/entity/client.entity';
import { Permissions } from 'src/modules/events/permission';
import TanCommunicationInbox from 'src/modules/tan-automation/entity/tan-communication-inbox.entity';
import { getUserDetails } from 'src/utils/re-use';
import AutomationServers from '../entities/automationServers.entity';

const os = require('os');
const util = require('util');
const { exec } = require('child_process');
const execAsync = util.promisify(exec);

const s3 = new AWS.S3();
@Injectable()
export class IncomeTaxCronJobService {
  // @Cron('0 30 2 * * *')
  //EVERY_DAY_AT_2.30AM

  // async getEproceedingNotices() {
  //   if (process.env.Cron_Running === 'true') {
  //     const cronData = new CronActivity();
  //     cronData.cronType = 'IT NOTICES';
  //     cronData.cronDate = moment().toDate().toString();
  //     cronData.startTime = moment().format('YYYY-MM-DD HH:mm:ss');
  //     const cornActivityID = await cronData.save();
  //     const errorList = [];
  //     try {
  //       const today = moment().format('YYYY-MM-DD');
  //       const previousSeventhDay = moment().subtract(6, 'days').format('YYYY-MM-DD');
  //       const nextSeventhDay = moment().add(6, 'days').format('YYYY-MM-DD');
  //       const totalOrganization = await Organization.createQueryBuilder('organization')
  //         .leftJoinAndSelect('organization.users', 'user')
  //         .where(
  //           "DATE_FORMAT(STR_TO_DATE(JSON_UNQUOTE(JSON_EXTRACT(config, '$.expirydate')), '%Y-%m-%d'), '%Y-%m-%d') >= :expirydate",
  //           { expirydate: moment().format('YYYY-MM-DD') },
  //         )
  //         .andWhere('user.status = :status', { status: 'active' })
  //         .andWhere('user.type = :type', { type: UserType.ORGANIZATION })
  //         .getMany();

  //       try {
  //         for (let organization of totalOrganization) {
  //           let fyiRecordsArray = [];
  //           let fyaRecordsArray = [];
  //           const fyiRecords = await createQueryBuilder(AutFyiNotice, 'autFyiNotice')
  //             .leftJoinAndSelect('autFyiNotice.eProceeding', 'eProceeding')
  //             .where('autFyiNotice.organizationId = :orgId', { orgId: organization?.id })
  //             .andWhere(
  //               new Brackets((qb) => {
  //                 qb.where('DATE(autFyiNotice.issuedOn) BETWEEN :previousSeventhDay AND :today', {
  //                   previousSeventhDay,
  //                   today,
  //                 }).orWhere(
  //                   'DATE(autFyiNotice.responseDueDate) BETWEEN :today AND :nextSeventhDay',
  //                   {
  //                     today,
  //                     nextSeventhDay,
  //                   },
  //                 );
  //               }),
  //             )
  //             .leftJoinAndSelect('autFyiNotice.client', 'client')
  //             .getMany();
  //           if (fyiRecords?.length > 0) {
  //             fyiRecordsArray = fyiRecords.map((item) => ({
  //               orgId: organization?.id,
  //               id: item.id,
  //               clientName: item?.client?.displayName,
  //               panNumber: item?.pan,
  //               proceedingName: item?.proceedingName,
  //               din: item?.documentIdentificationNumber || '-',
  //               ay: item?.eProceeding?.assessmentYear
  //                 ? `${item?.eProceeding?.assessmentYear}-${
  //                     parseInt(item?.eProceeding?.assessmentYear) + 1
  //                   }`
  //                 : '-',
  //               issuedOnDate:
  //                 item?.issuedOn &&
  //                 moment(item?.issuedOn, 'YYYY-MM-DD HH:mm:ss.SSS', true).isValid()
  //                   ? moment(item?.issuedOn).format('DD-MM-YYYY')
  //                   : '-',
  //               responseDueDate:
  //                 item?.responseDueDate &&
  //                 moment(item?.responseDueDate, 'YYYY-MM-DD HH:mm:ss.SSS', true).isValid()
  //                   ? moment(item?.responseDueDate).format('DD-MM-YYYY')
  //                   : '-',
  //             }));
  //           }

  //           const fyaRecords = await createQueryBuilder(AutFyaNotice, 'autFyaNotice')
  //             .where('autFyaNotice.organizationId = :orgId', { orgId: organization?.id })
  //             .andWhere(
  //               new Brackets((qb) => {
  //                 qb.where('DATE(autFyaNotice.issuedOn) BETWEEN :previousSeventhDay AND :today', {
  //                   previousSeventhDay,
  //                   today,
  //                 }).orWhere(
  //                   'DATE(autFyaNotice.responseDueDate) BETWEEN :today AND :nextSeventhDay',
  //                   {
  //                     today,
  //                     nextSeventhDay,
  //                   },
  //                 );
  //               }),
  //             )
  //             .leftJoinAndSelect('autFyaNotice.client', 'client')
  //             .getMany();
  //           if (fyaRecords?.length > 0) {
  //             fyaRecordsArray = fyaRecords.map((item) => ({
  //               orgId: organization?.id,
  //               id: item.id,
  //               clientName: item?.client?.displayName,
  //               panNumber: item?.pan,
  //               proceedingName: item?.proceedingName,
  //               din: item?.documentIdentificationNumber || '-',
  //               ay: item?.assesmentYear
  //                 ? `${item.assesmentYear}-${parseInt(item?.assesmentYear) + 1}`
  //                 : '-',
  //               issuedOnDate:
  //                 item?.issuedOn &&
  //                 moment(item?.issuedOn, 'YYYY-MM-DD HH:mm:ss.SSS', true).isValid()
  //                   ? moment(item?.issuedOn).format('DD-MM-YYYY')
  //                   : '-',
  //               responseDueDate:
  //                 item?.responseDueDate &&
  //                 moment(item?.responseDueDate, 'YYYY-MM-DD HH:mm:ss.SSS', true).isValid()
  //                   ? moment(item?.responseDueDate).format('DD-MM-YYYY')
  //                   : '-',
  //             }));
  //           }

  //           if (fyaRecordsArray?.length > 0 || fyiRecordsArray?.length > 0) {
  //             const users = organization?.users;

  //             if (users?.length > 0) {
  //               for (let user of users) {
  //                 const mailOptions = {
  //                   data: {
  //                     fyaRecordsArray,
  //                     fyiRecordsArray,
  //                     userName: user?.fullName,
  //                     userId: user?.id,
  //                     websiteUrl: process.env.WEBSITE_URL,
  //                   },
  //                   email: user?.email,
  //                   filePath: 'it-notice',
  //                   subject: 'Incometax Notices',
  //                   key: 'INCOMETAX_NOTICE_MAIL',
  //                   id: user?.id,
  //                 };
  //                 await sendnewMail(mailOptions);
  //               }
  //             }
  //           }
  //         }
  //       } catch (error) {
  //         console.log(`Error in getting Income tax Notice records in cron:`, error);
  //       }
  //     } catch (error) {
  //       errorList.push(error.message);
  //       const getcornActivityID = await createQueryBuilder(CronActivity, 'cronActivity')
  //         .where('id = :id', { id: cornActivityID.id })
  //         .getOne();
  //       getcornActivityID.responseData = errorList.length ? errorList.join(',') : 'Success';
  //       getcornActivityID.endTime = moment().format('YYYY-MM-DD HH:mm:ss');
  //       await getcornActivityID.save();
  //       return console.log(error.message);
  //     }
  //     const getcornActivityID = await createQueryBuilder(CronActivity, 'cronActivity')
  //       .where('id = :id', { id: cornActivityID.id })
  //       .getOne();
  //     getcornActivityID.responseData = errorList.length ? errorList.join(',') : 'Success';
  //     getcornActivityID.endTime = moment().format('YYYY-MM-DD HH:mm:ss');
  //     await getcornActivityID.save();
  //   }
  // }

  // @Cron('0 30 2 * * *') // (EVERY DAY AT 2.30AM - Server Time) (08.00 AM)
  async getEproceedingNotices() {
    if (process.env.Cron_Running === 'true') {
      console.log('-------- INCOME TAX PAN NOTICE CRON EXECUTION STARTED ---------');
      const cronData = new CronActivity();
      cronData.cronType = 'IT NOTICES';
      cronData.cronDate = moment().toDate().toString();
      cronData.startTime = moment().format('YYYY-MM-DD HH:mm:ss');
      const cornActivityID = await cronData.save();
      const errorList = [];
      try {
        const today = moment().format('YYYY-MM-DD');
        const previousSeventhDay = moment().subtract(7, 'days').format('YYYY-MM-DD');
        const nextSeventhDay = moment().add(7, 'days').format('YYYY-MM-DD');
        const totalOrganization = await Organization.createQueryBuilder('organization')
          .leftJoinAndSelect('organization.users', 'user')
          .where(
            "DATE_FORMAT(STR_TO_DATE(JSON_UNQUOTE(JSON_EXTRACT(config, '$.expirydate')), '%Y-%m-%d'), '%Y-%m-%d') >= :expirydate",
            { expirydate: moment().format('YYYY-MM-DD') },
          )
          .andWhere('user.status = :status', { status: 'active' })
          .andWhere('user.type = :type', { type: UserType.ORGANIZATION })
          .getMany();

        try {
          for (let organization of totalOrganization) {
            const users = organization?.users;

            if (users?.length > 0) {
              for (let user of users) {
                if (user.status === UserStatus.DELETED) return;
                const userData = await User.findOne({
                  where: { id: user.id },
                  relations: ['organization', 'role'],
                });

                const { role } = userData;
                const ViewAll = role.permissions.some(
                  (p) => p.slug === Permissions.VIEW_ALL_CLIENT_MANAGERS,
                );
                const ViewAssigned = role.permissions.some(
                  (p) => p.slug === Permissions.VIEW_ASSIGNED_CLIENT_MANAGERS,
                );

                let fyiRecordsArray = [];
                let fyaRecordsArray = [];
                if (ViewAll || ViewAssigned) {
                  const fyiRecordsQuery = await createQueryBuilder(IncTempEproFyi, 'incTempEproFyi')
                    .leftJoinAndSelect('incTempEproFyi.client', 'client')
                    .where('incTempEproFyi.organizationId = :orgId', { orgId: organization?.id })
                    .andWhere(
                      new Brackets((qb) => {
                        qb.where(
                          'STR_TO_DATE(incTempEproFyi.noticeSentDate, "%d-%m-%Y") BETWEEN :previousSeventhDay AND :today',
                          {
                            previousSeventhDay,
                            today,
                          },
                        ).orWhere(
                          'STR_TO_DATE(incTempEproFyi.dateOfCompliance, "%d-%m-%Y") BETWEEN :today AND :nextSeventhDay',
                          {
                            today,
                            nextSeventhDay,
                          },
                        ).orWhere(
                          'STR_TO_DATE(incTempEproFyi.dateResponseSubmitted, "%d-%m-%Y") BETWEEN :previousSeventhDay AND :today',
                          {
                            previousSeventhDay,
                            today,
                          },
                        );
                      }),
                    )
                    .andWhere('client.status != :status', { status: UserStatus.DELETED });

                  if (!ViewAll) {
                    fyiRecordsQuery.andWhere(
                      (qb) => {
                        const subQuery = qb
                          .subQuery()
                          .select('1')
                          .from('client_client_managers_user', 'cm')
                          .where('cm.client_id = client.id')
                          .andWhere('cm.user_id = :userId')
                          .getQuery();
                        return `EXISTS (${subQuery})`;
                      },
                      { userId: userData.id },
                    );
                  }

                  const fyiRecords = await fyiRecordsQuery.getMany();
                  if (fyiRecords?.length > 0) {
                    fyiRecordsArray = fyiRecords.map((item) => ({
                      orgId: organization?.id,
                      id: item.id,
                      clientName: item?.client?.displayName,
                      panNumber: item?.pan,
                      proceedingName: item?.proceedingName,
                      din: item?.noticeDin || '-',
                      section: item?.noticeSection || '-',
                      ay: item?.ay === '0-01' ? 'NA' : item?.ay,
                      issuedOnDate: item?.noticeSentDate ? item?.noticeSentDate : '-',
                      responseDueDate: item?.dateOfCompliance ? item?.dateOfCompliance : '-',
                      responseSubmitted: item?.dateResponseSubmitted ? item?.dateResponseSubmitted : "-"
                    }));
                  }

                  const fyaRecordsQuery = await createQueryBuilder(IncTempEproFya, 'incTempEproFya')
                    .leftJoinAndSelect('incTempEproFya.client', 'client')
                    .where('incTempEproFya.organizationId = :orgId', { orgId: organization?.id })
                    .andWhere(
                      new Brackets((qb) => {
                        qb.where(
                          'STR_TO_DATE(incTempEproFya.noticeSentDate, "%d-%m-%Y") BETWEEN :previousSeventhDay AND :today',
                          {
                            previousSeventhDay,
                            today,
                          },
                        ).orWhere(
                          'STR_TO_DATE(incTempEproFya.dateOfCompliance, "%d-%m-%Y") BETWEEN :today AND :nextSeventhDay',
                          {
                            today,
                            nextSeventhDay,
                          },
                        ).orWhere(
                          'STR_TO_DATE(incTempEproFya.dateResponseSubmitted, "%d-%m-%Y") BETWEEN :previousSeventhDay AND :today',
                          {
                            previousSeventhDay,
                            today,
                          },
                        );
                      }),
                    )
                    .andWhere('client.status != :status', { status: UserStatus.DELETED });
                  if (!ViewAll) {
                    fyaRecordsQuery.andWhere(
                      (qb) => {
                        const subQuery = qb
                          .subQuery()
                          .select('1')
                          .from('client_client_managers_user', 'cm')
                          .where('cm.client_id = client.id')
                          .andWhere('cm.user_id = :userId')
                          .getQuery();
                        return `EXISTS (${subQuery})`;
                      },
                      { userId: userData.id },
                    );
                  }
                  const orgId = organization?.id;

                  const organizations = await Organization.findOne({ id: orgId });

                  const addressParts = [
                    organizations.buildingNo || '',
                    organizations.floorNumber || '',
                    organizations.buildingName || '',
                    organizations.street || '',
                    organizations.location || '',
                    organizations.city || '',
                    organizations.district || '',
                    organizations.state || '',
                  ].filter((part) => part && part.trim() !== '');
                  const pincode =
                    organizations.pincode && organizations.pincode.trim() !== ''
                      ? ` - ${organizations.pincode}`
                      : '';

                  const address = addressParts.join(', ') + pincode;

                  const fyaRecords = await fyaRecordsQuery.getMany();
                  if (fyaRecords?.length > 0) {
                    fyaRecordsArray = fyaRecords.map((item) => ({
                      orgId: organization?.id,
                      id: item.id,
                      clientName: item?.client?.displayName,
                      panNumber: item?.pan,
                      proceedingName: item?.proceedingName,
                      section: item?.noticeSection || '-',
                      din: item?.noticeDin || '-',
                      ay: item?.ay === '0-01' ? 'NA' : item?.ay,
                      issuedOnDate: item?.noticeSentDate ? item?.noticeSentDate : '-',
                      responseDueDate: item?.dateOfCompliance ? item?.dateOfCompliance : '-',
                      responseSubmitted: item?.dateResponseSubmitted ? item?.dateResponseSubmitted : "-"
                    }));
                  }

                  if (fyaRecordsArray?.length > 0 || fyiRecordsArray?.length > 0) {
                    const mailOptions = {
                      data: {
                        fyaRecordsArray,
                        fyiRecordsArray,
                        userName: user?.fullName,
                        userId: user?.id,
                        websiteUrl: process.env.WEBSITE_URL,
                        adress: address,
                        phoneNumber: organization?.mobileNumber,
                        mail: organization?.email,
                        legalName: organization?.tradeName || organization?.legalName,
                      },
                      email: userData.email,
                      filePath: 'it-notice',
                      subject: 'Incometax Notices',
                      key: 'INCOMETAX_NOTICE_MAIL',
                      id: user?.id,
                    };
                    await sendnewMail(mailOptions);
                  }
                }
              }
            }
          }
        } catch (error) {
          console.log(`Error in getting Income tax Notice records in cron:`, error);
        }
      } catch (error) {
        errorList.push(error.message);
        const getcornActivityID = await createQueryBuilder(CronActivity, 'cronActivity')
          .where('id = :id', { id: cornActivityID.id })
          .getOne();
        getcornActivityID.responseData = errorList.length ? errorList.join(',') : 'Success';
        getcornActivityID.endTime = moment().format('YYYY-MM-DD HH:mm:ss');
        await getcornActivityID.save();
        return console.log(error.message);
      }
      const getcornActivityID = await createQueryBuilder(CronActivity, 'cronActivity')
        .where('id = :id', { id: cornActivityID.id })
        .getOne();
      getcornActivityID.responseData = errorList.length ? errorList.join(',') : 'Success';
      getcornActivityID.endTime = moment().format('YYYY-MM-DD HH:mm:ss');
      await getcornActivityID.save();
      console.log('INCOME TAX PAN NOTICE CRON EXECUTION COMPLETED!!!!');
      return 'INCOME TAX PAN NOTICE CRON EXECUTION COMPLETED!!!!';
    }
  }

  //   @Cron(CronExpression.EVERY_DAY_AT_2AM)
  //   async getEproceedingNoticesWhatsapp() {
  //     if (process.env.Cron_Running === 'true') {
  //       const cronData = new CronActivity();
  //       cronData.cronType = 'IT NOTICES WHATSAPP';
  //       cronData.cronDate = moment().toDate().toString();
  //       cronData.startTime = moment().format('YYYY-MM-DD HH:mm:ss');
  //       const cornActivityID = await cronData.save();
  //       const errorList = [];
  //       try {
  //         const today = moment().format('YYYY-MM-DD');
  //         const previousSeventhDay = moment().subtract(6, 'days').format('YYYY-MM-DD');
  //         const nextSeventhDay = moment().add(6, 'days').format('YYYY-MM-DD');
  //         const totalOrganization = await Organization.createQueryBuilder('organization')
  //           .leftJoinAndSelect('organization.users', 'user')
  //           .where(
  //             "DATE_FORMAT(STR_TO_DATE(JSON_UNQUOTE(JSON_EXTRACT(config, '$.expirydate')), '%Y-%m-%d'), '%Y-%m-%d') >= :expirydate",
  //             { expirydate: moment().format('YYYY-MM-DD') },
  //           )
  //           .andWhere('user.status = :status', { status: 'active' })
  //           .andWhere('user.type = :type', { type: UserType.ORGANIZATION })
  //           .getMany();

  //         try {
  //           for (let organization of totalOrganization) {
  //             let fyiRecordsArray = [];
  //             let fyaRecordsArray = [];
  //             const fyiRecords = await createQueryBuilder(AutFyiNotice, 'autFyiNotice')
  //               .leftJoinAndSelect('autFyiNotice.eProceeding', 'eProceeding')
  //               .where('autFyiNotice.organizationId = :orgId', { orgId: organization?.id })
  //               .andWhere(
  //                 new Brackets((qb) => {
  //                   qb.where('DATE(autFyiNotice.issuedOn) BETWEEN :previousSeventhDay AND :today', {
  //                     previousSeventhDay,
  //                     today,
  //                   }).orWhere(
  //                     'DATE(autFyiNotice.responseDueDate) BETWEEN :today AND :nextSeventhDay',
  //                     {
  //                       today,
  //                       nextSeventhDay,
  //                     },
  //                   );
  //                 }),
  //               )
  //               .leftJoinAndSelect('autFyiNotice.client', 'client')
  //               .getMany();
  //             if (fyiRecords?.length > 0) {
  //               fyiRecordsArray = fyiRecords.map((item) => ({
  //                 orgId: organization?.id,
  //                 id: item.id,
  //                 clientName: item?.client?.displayName,
  //                 panNumber: item?.pan,
  //                 proceedingName: item?.proceedingName,
  //                 din: item?.documentIdentificationNumber || '-',
  //                 ay: item?.eProceeding?.assessmentYear
  //                   ? `${item?.eProceeding?.assessmentYear}-${
  //                       parseInt(item?.eProceeding?.assessmentYear) + 1
  //                     }`
  //                   : '-',
  //                 issuedOnDate:
  //                   item?.issuedOn &&
  //                   moment(item?.issuedOn, 'YYYY-MM-DD HH:mm:ss.SSS', true).isValid()
  //                     ? moment(item?.issuedOn).format('DD-MM-YYYY')
  //                     : '-',
  //                 responseDueDate:
  //                   item?.responseDueDate &&
  //                   moment(item?.responseDueDate, 'YYYY-MM-DD HH:mm:ss.SSS', true).isValid()
  //                     ? moment(item?.responseDueDate).format('DD-MM-YYYY')
  //                     : '-',
  //               }));
  //             }

  //             const fyaRecords = await createQueryBuilder(AutFyaNotice, 'autFyaNotice')
  //               .where('autFyaNotice.organizationId = :orgId', { orgId: organization?.id })
  //               .andWhere(
  //                 new Brackets((qb) => {
  //                   qb.where('DATE(autFyaNotice.issuedOn) BETWEEN :previousSeventhDay AND :today', {
  //                     previousSeventhDay,
  //                     today,
  //                   }).orWhere(
  //                     'DATE(autFyaNotice.responseDueDate) BETWEEN :today AND :nextSeventhDay',
  //                     {
  //                       today,
  //                       nextSeventhDay,
  //                     },
  //                   );
  //                 }),
  //               )
  //               .leftJoinAndSelect('autFyaNotice.client', 'client')
  //               .getMany();
  //             if (fyaRecords?.length > 0) {
  //               fyaRecordsArray = fyaRecords.map((item) => ({
  //                 orgId: organization?.id,
  //                 id: item.id,
  //                 clientName: item?.client?.displayName,
  //                 panNumber: item?.pan,
  //                 proceedingName: item?.proceedingName,
  //                 din: item?.documentIdentificationNumber || '-',
  //                 ay: item?.assesmentYear
  //                   ? `${item.assesmentYear}-${parseInt(item?.assesmentYear) + 1}`
  //                   : '-',
  //                 issuedOnDate:
  //                   item?.issuedOn &&
  //                   moment(item?.issuedOn, 'YYYY-MM-DD HH:mm:ss.SSS', true).isValid()
  //                     ? moment(item?.issuedOn).format('DD-MM-YYYY')
  //                     : '-',
  //                 responseDueDate:
  //                   item?.responseDueDate &&
  //                   moment(item?.responseDueDate, 'YYYY-MM-DD HH:mm:ss.SSS', true).isValid()
  //                     ? moment(item?.responseDueDate).format('DD-MM-YYYY')
  //                     : '-',
  //               }));
  //             }

  //             if (fyaRecordsArray?.length > 0 || fyiRecordsArray?.length > 0) {
  //               const users = organization?.users;

  //               if (users?.length > 0) {
  //                 for (let user of users) {
  //                   const ejsData = {
  //                     fyaRecordsArray,
  //                     fyiRecordsArray,
  //                     userName: user?.fullName,
  //                     userId: user?.id,
  //                     websiteUrl: process.env.WEBSITE_URL,
  //                   };

  //                   const htmlContent = await ejs.renderFile(
  //                     'src/emails/templates/it-noticewhatsapp.ejs',
  //                     ejsData,
  //                   );
  //                   // console.log(htmlContent)

  //                   // Convert HTML to PDF using Puppeteer
  //                   const browser = await puppeteer.launch({
  //                     headless: true, // Run in headless mode
  //                     // executablePath: '/home/<USER>/.cache/puppeteer/chrome/linux-131.0.6778.108/chrome-linux64/chrome', // Path to Chrome
  // executablePath: '/usr/bin/chromium-browser',
  //                     args: ['--no-sandbox', '--disable-setuid-sandbox'], // Required for servers
  //                   });
  //                   const page = await browser.newPage();
  //                   await page.setContent(htmlContent);
  //                   const pdfBuffer = await page.pdf({
  //                     format: 'A4', // Standard page size
  //                     printBackground: true, // Include background colors and images
  //                     margin: {
  //                       top: '1cm',
  //                       bottom: '1cm',
  //                       left: '1cm',
  //                       right: '1cm',
  //                     },
  //                   });
  //                   await browser.close();

  //                   // Upload PDF to S3
  //                   const s3Params = {
  //                     Bucket: process.env.AWS_BUCKET_NAME,
  //                     Key: `incometax-notice-report-${organization.id}-${moment().format(
  //                       'YYYY-MM-DD HH:mm:ss',
  //                     )}.pdf`,
  //                     Body: pdfBuffer,
  //                     ContentType: 'application/pdf',
  //                   };
  //                   const uploadResult = await s3.upload(s3Params).promise();
  //                   const pdfLink = uploadResult.Location;
  //                   // Send PDF via WhatsApp
  //                   const title = 'Income notice';
  //                   const users = organization?.users;

  //                   const sessionValidation = await ViderWhatsappSessions.findOne({
  //                     where: { userId: user?.id, status: 'ACTIVE' },
  //                   });
  //                   if (sessionValidation) {
  //                     const key = 'INCOMETAX_NOTICE_WHATSAPP';

  //                     try {
  //                       const caption = 'Income Tax Notices Report';
  //                       const filename = 'Incometax_Notice_Report.pdf';

  //                       await sendDocumentTextMessage(
  //                         user?.mobileNumber,
  //                         pdfLink,
  //                         caption,
  //                         filename,
  //                         user?.organization?.id,
  //                         user?.id,
  //                         title,
  //                         key,
  //                       );
  //                     } catch (error) {
  //                       console.error(
  //                         'Error sending document message for user:',
  //                         user.mobileNumber,
  //                         error,
  //                       );
  //                     }
  //                   }
  //                 }
  //               }
  //             }
  //           }
  //         } catch (error) {
  //           console.log(`Error in getting Income tax Notice records in cron:`, error);
  //         }
  //       } catch (error) {
  //         errorList.push(error.message);
  //         const getcornActivityID = await createQueryBuilder(CronActivity, 'cronActivity')
  //           .where('id = :id', { id: cornActivityID.id })
  //           .getOne();
  //         getcornActivityID.responseData = errorList.length ? errorList.join(',') : 'Success';
  //         getcornActivityID.endTime = moment().format('YYYY-MM-DD HH:mm:ss');
  //         await getcornActivityID.save();
  //         return console.log(error.message);
  //       }
  //       const getcornActivityID = await createQueryBuilder(CronActivity, 'cronActivity')
  //         .where('id = :id', { id: cornActivityID.id })
  //         .getOne();
  //       getcornActivityID.responseData = errorList.length ? errorList.join(',') : 'Success';
  //       getcornActivityID.endTime = moment().format('YYYY-MM-DD HH:mm:ss');
  //       await getcornActivityID.save();
  //     }
  //   }

  // @Cron('9 11 * * *')

  //  @Cron(CronExpression.EVERY_DAY_AT_2AM)
  //   async getEproceedingNoticesWhatsapp() {
  //     if (process.env.Cron_Running === 'true') {
  //       const cronData = new CronActivity();
  //       cronData.cronType = 'IT NOTICES WHATSAPP';
  //       cronData.cronDate = moment().toDate().toString();
  //       cronData.startTime = moment().format('YYYY-MM-DD HH:mm:ss');
  //       const cornActivityID = await cronData.save();
  //       const errorList = [];
  //       try {
  //         const today = moment().format('YYYY-MM-DD');
  //         const previousSeventhDay = moment().subtract(6, 'days').format('YYYY-MM-DD');
  //         const nextSeventhDay = moment().add(6, 'days').format('YYYY-MM-DD');
  //         const totalOrganization = await Organization.createQueryBuilder('organization')
  //           .leftJoinAndSelect('organization.users', 'user')
  //           .where(
  //             "DATE_FORMAT(STR_TO_DATE(JSON_UNQUOTE(JSON_EXTRACT(config, '$.expirydate')), '%Y-%m-%d'), '%Y-%m-%d') >= :expirydate",
  //             { expirydate: moment().format('YYYY-MM-DD') },
  //           )
  //           .andWhere('user.status = :status', { status: 'active' })
  //           .andWhere('user.type = :type', { type: UserType.ORGANIZATION })
  //           .getMany();

  //         try {
  //           for (let organization of totalOrganization) {
  //             let fyiRecordsArray = [];
  //             let fyaRecordsArray = [];
  //             const fyiRecords = await createQueryBuilder(AutFyiNotice, 'autFyiNotice')
  //               .leftJoinAndSelect('autFyiNotice.eProceeding', 'eProceeding')
  //               .where('autFyiNotice.organizationId = :orgId', { orgId: organization?.id })
  //               .andWhere(
  //                 new Brackets((qb) => {
  //                   qb.where('DATE(autFyiNotice.issuedOn) BETWEEN :previousSeventhDay AND :today', {
  //                     previousSeventhDay,
  //                     today,
  //                   }).orWhere(
  //                     'DATE(autFyiNotice.responseDueDate) BETWEEN :today AND :nextSeventhDay',
  //                     {
  //                       today,
  //                       nextSeventhDay,
  //                     },
  //                   );
  //                 }),
  //               )
  //               .leftJoinAndSelect('autFyiNotice.client', 'client')
  //               .getMany();
  //             if (fyiRecords?.length > 0) {
  //               fyiRecordsArray = fyiRecords.map((item) => ({
  //                 orgId: organization?.id,
  //                 id: item.id,
  //                 clientName: item?.client?.displayName,
  //                 panNumber: item?.pan,
  //                 proceedingName: item?.proceedingName,
  //                 din: item?.documentIdentificationNumber || '-',
  //                 ay: item?.eProceeding?.assessmentYear
  //                   ? `${item?.eProceeding?.assessmentYear}-${
  //                       parseInt(item?.eProceeding?.assessmentYear) + 1
  //                     }`
  //                   : '-',
  //                 issuedOnDate:
  //                   item?.issuedOn &&
  //                   moment(item?.issuedOn, 'YYYY-MM-DD HH:mm:ss.SSS', true).isValid()
  //                     ? moment(item?.issuedOn).format('DD-MM-YYYY')
  //                     : '-',
  //                 responseDueDate:
  //                   item?.responseDueDate &&
  //                   moment(item?.responseDueDate, 'YYYY-MM-DD HH:mm:ss.SSS', true).isValid()
  //                     ? moment(item?.responseDueDate).format('DD-MM-YYYY')
  //                     : '-',
  //               }));
  //             }

  //             const fyaRecords = await createQueryBuilder(AutFyaNotice, 'autFyaNotice')
  //               .where('autFyaNotice.organizationId = :orgId', { orgId: organization?.id })
  //               .andWhere(
  //                 new Brackets((qb) => {
  //                   qb.where('DATE(autFyaNotice.issuedOn) BETWEEN :previousSeventhDay AND :today', {
  //                     previousSeventhDay,
  //                     today,
  //                   }).orWhere(
  //                     'DATE(autFyaNotice.responseDueDate) BETWEEN :today AND :nextSeventhDay',
  //                     {
  //                       today,
  //                       nextSeventhDay,
  //                     },
  //                   );
  //                 }),
  //               )
  //               .leftJoinAndSelect('autFyaNotice.client', 'client')
  //               .getMany();
  //             if (fyaRecords?.length > 0) {
  //               fyaRecordsArray = fyaRecords.map((item) => ({
  //                 orgId: organization?.id,
  //                 id: item.id,
  //                 clientName: item?.client?.displayName,
  //                 panNumber: item?.pan,
  //                 proceedingName: item?.proceedingName,
  //                 din: item?.documentIdentificationNumber || '-',
  //                 ay: item?.assesmentYear
  //                   ? `${item.assesmentYear}-${parseInt(item?.assesmentYear) + 1}`
  //                   : '-',
  //                 issuedOnDate:
  //                   item?.issuedOn &&
  //                   moment(item?.issuedOn, 'YYYY-MM-DD HH:mm:ss.SSS', true).isValid()
  //                     ? moment(item?.issuedOn).format('DD-MM-YYYY')
  //                     : '-',
  //                 responseDueDate:
  //                   item?.responseDueDate &&
  //                   moment(item?.responseDueDate, 'YYYY-MM-DD HH:mm:ss.SSS', true).isValid()
  //                     ? moment(item?.responseDueDate).format('DD-MM-YYYY')
  //                     : '-',
  //               }));
  //             }

  //             if (fyaRecordsArray?.length > 0 || fyiRecordsArray?.length > 0) {
  //               const users = organization?.users;

  //               if (users?.length > 0) {
  //                 for (let user of users) {
  //                   const ejsData = {
  //                     fyaRecordsArray,
  //                     fyiRecordsArray,
  //                     userName: user?.fullName,
  //                     userId: user?.id,
  //                     websiteUrl: process.env.WEBSITE_URL,
  //                   };

  //                   const htmlContent = await ejs.renderFile(
  //                     'src/emails/templates/it-noticewhatsapp.ejs',
  //                     ejsData,
  //                   );

  //                   // Convert HTML to PDF using Puppeteer
  //                   const browser = await puppeteer.launch({
  //                     headless: true, // Run in headless mode
  //                     // executablePath: '/home/<USER>/.cache/puppeteer/chrome/linux-131.0.6778.108/chrome-linux64/chrome', // Path to Chrome
  // executablePath: '/usr/bin/google-chrome',
  //                     args: ['--no-sandbox', '--disable-setuid-sandbox'], // Required for servers
  //                   });
  //                   const page = await browser.newPage();
  //                   await page.setContent(htmlContent);
  //                   const pdfBuffer = await page.pdf({
  //                     format: 'A4', // Standard page size
  //                     printBackground: true, // Include background colors and images
  //                     margin: {
  //                       top: '1cm',
  //                       bottom: '1cm',
  //                       left: '1cm',
  //                       right: '1cm',
  //                     },
  //                   });
  //                   await browser.close();

  //                   // Upload PDF to S3
  //                   const s3Params = {
  //                     Bucket: process.env.AWS_BUCKET_NAME,
  //                     Key: `incometax-notice-report-${organization.id}-${moment().format(
  //                       'YYYY-MM-DD HH:mm:ss',
  //                     )}.pdf`,
  //                     Body: pdfBuffer,
  //                     ContentType: 'application/pdf',
  //                   };
  //                   const uploadResult = await s3.upload(s3Params).promise();
  //                   const pdfLink = uploadResult.Location;
  //                   // Send PDF via WhatsApp
  //                   const title = 'Income notice';
  //                   const users = organization?.users;

  //                   const sessionValidation = await ViderWhatsappSessions.findOne({
  //                     where: { userId: user?.id, status: 'ACTIVE' },
  //                   });
  //                   if (sessionValidation) {
  //                     const key = 'INCOMETAX_NOTICE_WHATSAPP';

  //                     try {
  //                       const caption = 'Income Tax Notices Report';
  //                       const filename = 'Incometax_Notice_Report.pdf';

  //                       await sendDocumentTextMessage(
  //                         user?.mobileNumber,
  //                         pdfLink,
  //                         caption,
  //                         filename,
  //                         user?.organization?.id,
  //                         user?.id,
  //                         title,
  //                         key,
  //                       );
  //                     } catch (error) {
  //                       console.error(
  //                         'Error sending document message for user:',
  //                         user.mobileNumber,
  //                         error,
  //                       );
  //                     }
  //                   }
  //                 }
  //               }
  //             }
  //           }
  //         } catch (error) {
  //           console.log(`Error in getting Income tax Notice records in cron:`, error);
  //         }
  //       } catch (error) {
  //         errorList.push(error.message);
  //         const getcornActivityID = await createQueryBuilder(CronActivity, 'cronActivity')
  //           .where('id = :id', { id: cornActivityID.id })
  //           .getOne();
  //         getcornActivityID.responseData = errorList.length ? errorList.join(',') : 'Success';
  //         getcornActivityID.endTime = moment().format('YYYY-MM-DD HH:mm:ss');
  //         await getcornActivityID.save();
  //         return console.log(error.message);
  //       }
  //       const getcornActivityID = await createQueryBuilder(CronActivity, 'cronActivity')
  //         .where('id = :id', { id: cornActivityID.id })
  //         .getOne();
  //       getcornActivityID.responseData = errorList.length ? errorList.join(',') : 'Success';
  //       getcornActivityID.endTime = moment().format('YYYY-MM-DD HH:mm:ss');
  //       await getcornActivityID.save();
  //     }
  //   }
  @Cron(CronExpression.EVERY_DAY_AT_11AM)
  async getEproceedingNoticesWhatsapp() {
    if (process.env.Cron_Running === 'true') {
      console.log('-------- INCOME TAX PAN NOTICE CRON EXECUTION STARTED ---------');
      const cronData = new CronActivity();
      cronData.cronType = 'IT NOTICES WHATSAPP';
      cronData.cronDate = moment().toDate().toString();
      cronData.startTime = moment().format('YYYY-MM-DD HH:mm:ss');
      const cornActivityID = await cronData.save();
      const errorList = [];
      const caption = 'INCOME TAX Notices Report';
      const filename = 'INCOME_TAX_NOTICE_REPORT.pdf';
      const key = 'INCOMETAX_NOTICE_WHATSAPP';
      try {
        const today = moment().format('YYYY-MM-DD');
        const previousSeventhDay = moment().subtract(7, 'days').format('YYYY-MM-DD');
        const nextSeventhDay = moment().add(7, 'days').format('YYYY-MM-DD');
        const totalOrganization = await Organization.createQueryBuilder('organization')
          .leftJoinAndSelect('organization.users', 'user')
          .where(
            "DATE_FORMAT(STR_TO_DATE(JSON_UNQUOTE(JSON_EXTRACT(config, '$.expirydate')), '%Y-%m-%d'), '%Y-%m-%d') >= :expirydate",
            { expirydate: moment().format('YYYY-MM-DD') },
          )
          .andWhere('user.status = :status', { status: 'active' })
          .andWhere('user.type = :type', { type: UserType.ORGANIZATION })
          .getMany();

        try {
          for (let organization of totalOrganization) {
            const users = organization?.users;

            if (users?.length > 0) {
              for (let user of users) {
                const userData = await User.findOne({
                  where: { id: user.id },
                  relations: ['organization', 'role'],
                });

                const { role } = userData;
                const ViewAll = role.permissions.some(
                  (p) => p.slug === Permissions.VIEW_ALL_CLIENT_MANAGERS,
                );
                const ViewAssigned = role.permissions.some(
                  (p) => p.slug === Permissions.VIEW_ASSIGNED_CLIENT_MANAGERS,
                );

                const clientCount = await createQueryBuilder(Client, 'client')
                  .leftJoin('client.clientManagers', 'clientManagers')
                  .where('clientManagers.id = :userId', { userId: userData.id })
                  .andWhere('client.status != :status', { status: UserStatus.DELETED })
                  .getCount();

                let fyiRecordsArray = [];
                let fyaRecordsArray = [];
                if ((ViewAll || ViewAssigned) && clientCount) {
                  const fyiRecordsQuery = await createQueryBuilder(IncTempEproFyi, 'incTempEproFyi')
                    .where('incTempEproFyi.organizationId = :orgId', { orgId: organization?.id })
                    .andWhere(
                      new Brackets((qb) => {
                        qb.where(
                          'STR_TO_DATE(incTempEproFyi.noticeSentDate, "%d-%m-%Y") BETWEEN :previousSeventhDay AND :today',
                          {
                            previousSeventhDay,
                            today,
                          },
                        ).orWhere(
                          'STR_TO_DATE(incTempEproFyi.dateOfCompliance, "%d-%m-%Y") BETWEEN :today AND :nextSeventhDay',
                          {
                            today,
                            nextSeventhDay,
                          },
                        );
                      }),
                    )
                    .leftJoinAndSelect('incTempEproFyi.client', 'client');

                  if (!ViewAll) {
                    fyiRecordsQuery.andWhere(
                      (qb) => {
                        const subQuery = qb
                          .subQuery()
                          .select('1')
                          .from('client_client_managers_user', 'cm')
                          .where('cm.client_id = client.id')
                          .andWhere('cm.user_id = :userId')
                          .getQuery();
                        return `EXISTS (${subQuery})`;
                      },
                      { userId: userData.id },
                    );
                  }

                  const fyiRecords = await fyiRecordsQuery.getMany();
                  if (fyiRecords?.length > 0) {
                    fyiRecordsArray = fyiRecords.map((item) => ({
                      orgId: organization?.id,
                      id: item.id,
                      clientName: item?.client?.displayName,
                      panNumber: item?.pan,
                      proceedingName: item?.proceedingName,
                      din: item?.noticeDin || '-',
                      ay: item?.ay === '0-01' ? 'NA' : item?.ay,
                      issuedOnDate: item?.noticeSentDate ? item?.noticeSentDate : '-',
                      responseDueDate: item?.dateOfCompliance ? item?.dateOfCompliance : '-',
                    }));
                  }

                  const fyaRecordsQuery = await createQueryBuilder(IncTempEproFya, 'incTempEproFya')
                    .where('incTempEproFya.organizationId = :orgId', { orgId: organization?.id })
                    .andWhere(
                      new Brackets((qb) => {
                        qb.where(
                          'STR_TO_DATE(incTempEproFya.noticeSentDate, "%d-%m-%Y") BETWEEN :previousSeventhDay AND :today',
                          {
                            previousSeventhDay,
                            today,
                          },
                        ).orWhere(
                          'STR_TO_DATE(incTempEproFya.dateOfCompliance, "%d-%m-%Y") BETWEEN :today AND :nextSeventhDay',
                          {
                            today,
                            nextSeventhDay,
                          },
                        );
                      }),
                    )
                    .leftJoinAndSelect('incTempEproFya.client', 'client');
                  if (!ViewAll) {
                    fyaRecordsQuery.andWhere(
                      (qb) => {
                        const subQuery = qb
                          .subQuery()
                          .select('1')
                          .from('client_client_managers_user', 'cm')
                          .where('cm.client_id = client.id')
                          .andWhere('cm.user_id = :userId')
                          .getQuery();
                        return `EXISTS (${subQuery})`;
                      },
                      { userId: userData.id },
                    );
                  }
                  const orgId = organization?.id;

                  const organizations = await Organization.findOne({ id: orgId });

                  const addressParts = [
                    organizations.buildingNo || '',
                    organizations.floorNumber || '',
                    organizations.buildingName || '',
                    organizations.street || '',
                    organizations.location || '',
                    organizations.city || '',
                    organizations.district || '',
                    organizations.state || '',
                  ].filter((part) => part && part.trim() !== '');
                  const pincode =
                    organizations.pincode && organizations.pincode.trim() !== ''
                      ? ` - ${organizations.pincode}`
                      : '';

                  const address = addressParts.join(', ') + pincode;

                  const fyaRecords = await fyaRecordsQuery.getMany();
                  if (fyaRecords?.length > 0) {
                    fyaRecordsArray = fyaRecords.map((item) => ({
                      orgId: organization?.id,
                      id: item.id,
                      clientName: item?.client?.displayName,
                      panNumber: item?.pan,
                      proceedingName: item?.proceedingName,
                      din: item?.noticeDin || '-',
                      ay: item?.ay === '0-01' ? 'NA' : item?.ay,
                      issuedOnDate: item?.noticeSentDate ? item?.noticeSentDate : '-',
                      responseDueDate: item?.dateOfCompliance ? item?.dateOfCompliance : '-',
                    }));
                  }

                  if (fyaRecordsArray?.length > 0 || fyiRecordsArray?.length > 0) {
                    const users = organization?.users;

                    if (users?.length > 0) {
                      // console.log("user1", users?.length)
                      // if(user.id === 702){
                      const ejsData = {
                        fyaRecordsArray,
                        fyiRecordsArray,
                        userName: user?.fullName,
                        userId: user?.id,
                        websiteUrl: process.env.WEBSITE_URL,
                      };
                      const userDetails = await getUserDetails(user?.id);

                      const htmlContent = await ejs.renderFile(
                        'src/emails/templates/it-noticewhatsapp.ejs',
                        ejsData,
                      );
                      // console.log(htmlContent)

                      // Convert HTML to PDF using Puppeteer
                      const browser = await puppeteer.launch({
                        headless: true, // Run in headless mode
                        // executablePath: '/home/<USER>/.cache/puppeteer/chrome/linux-131.0.6778.108/chrome-linux64/chrome', // Path to Chrome
                        executablePath: '/usr/bin/chromium-browser',
                        args: ['--no-sandbox', '--disable-setuid-sandbox'], // Required for servers
                      });
                      const page = await browser.newPage();
                      await page.setContent(htmlContent);
                      const pdfBuffer = await page.pdf({
                        format: 'A4', // Standard page size
                        printBackground: true, // Include background colors and images
                        margin: {
                          top: '1cm',
                          bottom: '1cm',
                          left: '1cm',
                          right: '1cm',
                        },
                      });
                      await browser.close();

                      // Upload PDF to S3
                      const s3Params = {
                        Bucket: process.env.AWS_BUCKET_NAME,
                        Key: `incometax-notice-report-${organization.id}-${moment().format(
                          'YYYY-MM-DD HH:mm:ss',
                        )}.pdf`,
                        Body: pdfBuffer,
                        ContentType: 'application/pdf',
                      };
                      const uploadResult = await s3.upload(s3Params).promise();
                      const pdfLink = uploadResult.Location;
                      // Send PDF via WhatsApp
                      const preference = 'INCOMETAX_NOTICE_WHATSAPP';
                      const title = 'Income notice';
                      const users = organization?.users;
                      // const whatsappOptions = {
                      //   preference,
                      //   title: 'income-tax-notice',
                      //   userId: userId,
                      //   orgId: organization?.id,
                      //   to: `91${mobileNumber}`,
                      //   name: 'incometaxnotice',
                      //   header: [
                      //     {
                      //       type: 'document',
                      //       link: pdfLink,
                      //     },
                      //   ],

                      //   body: [fullName],

                      //   fileName: `Incometax Notice Report.pdf`,
                      //   key: `Incometax-Notice-Report-${moment().format(
                      //                       'YYYY-MM-DD HH:mm:ss'
                      //                     )}.pdf`,
                      // };
                      // await sendWhatsAppTemplateMessageNotices(whatsappOptions);
                      const sessionValidation = await ViderWhatsappSessions.findOne({
                        // where: { userId: user?.id, status: 'ACTIVE' },
                        where: { userId: user?.id, status: 'ACTIVE', organizationId: 136 },
                      });

                      if (sessionValidation) {
                        try {
                          await sendDocumentTextMessage(
                            user?.mobileNumber,
                            pdfLink,
                            caption,
                            filename,
                            user?.organization?.id,
                            user?.id,
                            title,
                            key,
                          );
                        } catch (error) {
                          errorList.push(error.message);
                          const getcornActivityID = await createQueryBuilder(
                            CronActivity,
                            'cronActivity',
                          )
                            .where('id = :id', { id: cornActivityID.id })
                            .getOne();
                          getcornActivityID.responseData = errorList.length
                            ? errorList.join(',')
                            : 'Success';
                          getcornActivityID.endTime = moment().format('YYYY-MM-DD HH:mm:ss');
                          await getcornActivityID.save();
                          console.error(
                            'Error sending document message for user:',
                            user.mobileNumber,
                            error.message,
                          );
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        } catch (error) {
          errorList.push(error.message);
          const getcornActivityID = await createQueryBuilder(CronActivity, 'cronActivity')
            .where('id = :id', { id: cornActivityID.id })
            .getOne();
          getcornActivityID.responseData = errorList.length ? errorList.join(',') : 'Success';
          getcornActivityID.endTime = moment().format('YYYY-MM-DD HH:mm:ss');
          await getcornActivityID.save();
          console.log(`Error in getting Income tax Notice records in cron:`, error);
        }
      } catch (error) {
        console.log(error, 'err');
        errorList.push(error.message);
        const getcornActivityID = await createQueryBuilder(CronActivity, 'cronActivity')
          .where('id = :id', { id: cornActivityID.id })
          .getOne();
        getcornActivityID.responseData = errorList.length ? errorList.join(',') : 'Success';
        getcornActivityID.endTime = moment().format('YYYY-MM-DD HH:mm:ss');
        await getcornActivityID.save();
      }

      const getcornActivityID = await createQueryBuilder(CronActivity, 'cronActivity')
        .where('id = :id', { id: cornActivityID.id })
        .getOne();
      getcornActivityID.responseData = errorList.length ? errorList.join(',') : 'Success';
      getcornActivityID.endTime = moment().format('YYYY-MM-DD HH:mm:ss');
      await getcornActivityID.save();
      console.log('execution ended');
    }
  }
  // @Cron(CronExpression.EVERY_5_MINUTES)
  async dailyArchivalOfAutomationMachinesTable() {
    const entityManager = getManager();
    //Insert and delete query for automation
    const query = `
         INSERT INTO automation_machines_archive (
      id, machine_name, auto_credentials_id, modules, status, user_id, remarks,
      created_at, updated_at, complete_modules, type, doc_download_status,
       gstr_credentials_id, tan_credentials_id, failed_modules,
      trace_user_id, trace_password, traceuserid, tracepassword
    )
    SELECT
      id, machine_name, auto_credentials_id, modules, status, user_id, remarks,
      created_at, updated_at, complete_modules, type, doc_download_status,
     gstr_credentials_id, tan_credentials_id, failed_modules,
      trace_user_id, trace_password, traceuserid, tracepassword
    FROM
      automation_machines
        WHERE
          updated_at < DATE_SUB(NOW(), INTERVAL 1 WEEK)
      `;

    const machineDetails = await entityManager.query(query);
    console.log(machineDetails);
    console.log('insert successfully completed');
    const deleteQuery = `DELETE FROM automation_machines
        WHERE
          updated_at < DATE_SUB(NOW(), INTERVAL 1 WEEK)
      `;

    const deleteMachines = await entityManager.query(deleteQuery);
    console.log('deleteMachines', deleteMachines);
  }

  // @Cron(CronExpression.EVERY_MINUTE)
  async checkAutomationServerStatusAndManage() {
    const query = `SELECT * FROM vider_preprod.automation_servers`;

    const entityManager = getManager();
    const serverDetails = await entityManager.query(query);

    for (let server of serverDetails) {
      // try {
      //   let config: any = {
      //     method: 'get',
      //     maxBodyLength: Infinity,
      //     url: server.health_check_url,
      //     headers: {},
      //     data: '',
      //   };
      //   const response = await axios.request(config);
      //   console.log(response?.data);
      // } catch (error) {
      //   console.log('error while server');
      // }

      const pendingMachines = await AutomationMachines.find({
        where: { status: 'PENDING', machineName: server?.machine_name },
      });
    }
  }

  async getIpWiseCount() {
    try {
      const entityManager = getManager();
      // Step 1: Get machine_name → ip_address mapping
      const serverMachines = await entityManager.query(`
  SELECT machine_name, ip_address
  FROM automation_servers
`);

      const machineToIP = {};
      for (const { machine_name, ip_address } of serverMachines) {
        machineToIP[machine_name] = ip_address;
      }
      const completedTasks = await entityManager.query(`
  SELECT machine_name, COUNT(*) as count
  FROM automation_machines
  WHERE status = 'COMPLETED'
    AND updated_at >= NOW() - INTERVAL 1 HOUR
  GROUP BY machine_name
`);

      // Step 4: Group completed counts by ip_address
      const ipCountMap = {};

      for (const task of completedTasks) {
        const machineName = task?.machine_name;
        const count = Number(task?.count);
        const ip = machineToIP[machineName];

        if (!ip) continue; // skip if no matching server

        ipCountMap[ip] = (ipCountMap[ip] || 0) + count;
      }

      // Step 5: Convert to final array
      const completedItems = Object.entries(ipCountMap)?.map(([ip_address, count]) => ({
        ip_address,
        count,
      }));

      return completedItems;
    } catch (error) {
      console.log('Error Occur While checkAutomationServerSendMessege', error);
    }
  }

  @Cron(CronExpression.EVERY_HOUR)
  async checkAutomationServerSendMessege() {
    if (process.env.Cron_Running === 'true') {
      try {
        const query = `SELECT 
                          ip_address,
                          COUNT(*) AS busy_count
                      FROM 
                          automation_servers
                      WHERE 
                          server_status = 'busy'
                        AND updated_at < NOW() - INTERVAL 1 HOUR
                      GROUP BY 
                          ip_address;`;

        const entityManager = getManager();
        const automationServerDetails = await entityManager.query(query);
        const typeQuery = `SELECT type, COUNT(*) AS count
      FROM automation_machines
      WHERE status = 'COMPLETED'
        AND updated_at >= NOW() - INTERVAL 1 HOUR
      GROUP BY type;`;
        const typeWiseDetails = await entityManager.query(typeQuery);

        const completedItems = await this.getIpWiseCount();
        if (true) {
          const data = {
            automationServerDetails,
            typeWiseDetails,
            completedItems,
          };

          const mailOptionsForBusiness = {
            data: data,
            email: process.env.SOCIAL_EMAIL,
            filePath: 'atom-pro-machines',
            subject: 'Atom Pro Machines Status',
            key: '',
            id: 0,
          };
          await sendnewMailToBusinessTeam(mailOptionsForBusiness);
        }
      } catch (error) {
        console.log('Error Occur While checkAutomationServerSendMessege', error);
      }
    }
  }

  @Cron(CronExpression.EVERY_DAY_AT_7PM)
  async sendEveryDayAutomationCount() {
    if (process.env.Cron_Running === 'true') {
      try {
        const entityManager = getManager();
        const typeQuery = `SELECT type, COUNT(*) AS count
      FROM automation_machines
      WHERE status = 'COMPLETED'
        AND updated_at >= NOW() - INTERVAL 24 HOUR
      GROUP BY type;`;
        const typeWiseDetails = await entityManager.query(typeQuery);
        if (true) {
          const data = {
            typeWiseDetails,
          };

          const mailOptionsForBusiness = {
            data: data,
            email: process.env.SOCIAL_EMAIL,
            filePath: 'atom-pro-daily-count',
            subject: 'Atom Pro Daily Complete Status',
            key: '',
            id: 0,
          };
          await sendnewMailToBusinessTeam(mailOptionsForBusiness);
        }
      } catch (error) {
        console.log('Error Occur While checkAutomationServerSendMessege', error);
      }
    }
  }

  // @Cron(CronExpression.EVERY_10_MINUTES)
  async modulesChangeInEproceeding() {
    const totalOrgsbulkSync = await AutomationBulkSync.find({
      where: { type: TypeEnum.INCOMETAX },
    });

    if (totalOrgsbulkSync) {
      if (totalOrgsbulkSync) {
        for (let bulkSync of totalOrgsbulkSync) {
          const organizationId = bulkSync.orgId;
          if (organizationId) {
            let data = '';
            let config: any = {
              method: 'get',
              maxBodyLength: Infinity,
              url: `${process.env.CAMUNDA_URL}/vider/quantum/api/automation/bulk/sync/${bulkSync.orgId}/incometax`,
              headers: {},
              data: data,
            };

            axios
              .request(config)
              .then((response) => {
                if (response.data) {
                  const schedule = JSON.parse(response?.data?.schedule);
                  let data1 = JSON.stringify({
                    modules: ['OD', 'EP'],
                    orgId: organizationId,
                    type: 'INCOMETAX',
                    schedules: schedule,
                  });

                  let config1: any = {
                    method: 'post',
                    maxBodyLength: Infinity,
                    url: `${process.env.CAMUNDA_URL}/vider/quantum/api/automation/bulk/sync`,
                    headers: {
                      'X-USER-ID': response.data.userId,
                      'Content-Type': 'application/json',
                    },
                    data: data1,
                  };

                  axios
                    .request(config1)
                    .then((response) => {})
                    .catch((error) => {
                      console.log('error in scheduling call in subscriber camunda', error.message);
                    });
                }
              })
              .catch((error) => {
                console.error(
                  'error in scheduling call in subscriber camunda Error Message:',
                  error.message,
                );
              });
          }
        }
      }
    }
  }

  // @Cron(CronExpression.EVERY_MINUTE)
  async moduleChangeForIncometexOneOrg() {
    const bulkSync = await AutomationBulkSync.findOne({
      where: { type: TypeEnum.INCOMETAX, orgId: 822 },
    });

    if (bulkSync) {
      const organizationId = bulkSync.orgId;
      if (organizationId) {
        let data = '';
        let config: any = {
          method: 'get',
          maxBodyLength: Infinity,
          url: `${process.env.CAMUNDA_URL}/vider/quantum/api/automation/bulk/sync/${bulkSync.orgId}/incometax`,
          headers: {},
          data: data,
        };

        axios
          .request(config)
          .then((response) => {
            if (response.data) {
              const schedule = JSON.parse(response?.data?.schedule);
              let data1 = JSON.stringify({
                modules: ['OD', 'EP'],
                orgId: organizationId,
                type: 'INCOMETAX',
                schedules: schedule,
              });

              let config1: any = {
                method: 'post',
                maxBodyLength: Infinity,
                url: `${process.env.CAMUNDA_URL}/vider/quantum/api/automation/bulk/sync`,
                headers: {
                  'X-USER-ID': response.data.userId,
                  'Content-Type': 'application/json',
                },
                data: data1,
              };

              axios
                .request(config1)
                .then((response) => {})
                .catch((error) => {
                  console.log('error in scheduling call in subscriber camunda', error.message);
                });
            }
          })
          .catch((error) => {
            console.log(error);
            console.error(
              'error in scheduling call in subscriber camunda Error Message:',
              error.message,
            );
          });
      }
    }
  }

  // @Cron(CronExpression.EVERY_5_HOURS)
  async deleteTempStorage() {
    try {
      console.log('TEEEEEEEEEEEEEEMMMMMMMMMMMMMMMMMMMPPPPPPPPPPPPP');
      const platform = os.platform();
      const tempDir = os.tmpdir();

      console.log(`[${new Date().toISOString()}] Temp directory: ${tempDir}`);
      console.log(`[${new Date().toISOString()}] Cleaning temp directory on ${platform}...`);

      let command = '';

      if (platform === 'win32') {
        command = `del /f /s /q "${tempDir}\\*.*" & for /d %x in ("${tempDir}\\*") do rd /s /q "%x"`;
      } else {
        command = `sudo rm -rf ${tempDir}/*`;
      }

      console.log('command', command);
      console.log(`[${new Date().toISOString()}] Executing command: ${command}`);
      await execAsync(command);
      console.log(`[${new Date().toISOString()}] Temp files cleaned`);
      console.log('TEEEEEEEEEEEEEEMMMMMMMMMMMMMMMMMMMPPPPPPPPPPPPP');
    } catch (error) {
      console.error(`❌ Error while deleting temp files: ${error.message || error}`);
    }
  }

  @Cron(CronExpression.EVERY_MINUTE)
  async sendTanItemsInqueueToPending() {
    if (process.env.Cron_Running === 'true') {
      try {
        // 1️⃣ Get all available servers
        const availableServers = await AutomationServers.find({
          where: { serverStatus: 'availableTan', syncType: 'TAN' },
        });

        if (!availableServers?.length) return;

        // 2️⃣ Pick the same number of INQUEUE machines
        const inqueueMachines = await AutomationMachines.find({
          where: { type: TypeEnum.TAN, status: 'INQUEUE' },
          order: { createdAt: 'ASC' }, // optional: oldest first
          take: availableServers?.length, // one per available server
        });

        if (!inqueueMachines?.length) return;

        // 3️⃣ Assign each server to exactly one machine
        for (let i = 0; i < inqueueMachines.length; i++) {
          const machine = inqueueMachines[i];
          const server = availableServers[i];
          machine.machineName = server?.machineName;
          machine.status = 'PENDING';
          await machine.save();

          server.serverStatus = 'busy';
          await server.save();
        }

        console.log('Assigned one INQUEUE machine per available server & marked servers busy');
      } catch (error) {
        console.log('Error in sendTanItemsInqueueToPending', error?.message);
      }
    }
  }
}
