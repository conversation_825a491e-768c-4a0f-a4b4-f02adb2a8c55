import { Organization } from 'src/modules/organization/entities/organization.entity';
import Storage from "../storage/storage.entity";
import {
  BaseEntity,
  Column,
  Entity,
  JoinColumn,
  ManyToOne,
  OneToOne,
  PrimaryGeneratedColumn,

} from 'typeorm';


@Entity()
class PosterConfig extends BaseEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  firmName: string;

  @Column()
  firmTagLine: string;

  @Column()
  mobileNumber: string;

  @Column()
  email: string;

  @Column()
  website: string;

  @Column()
  address: string;


  @Column('json', { array: true })
  branchesInfo: object[];

  @ManyToOne(() => Organization, (organization) => organization.events)
  organization: Organization;

  @OneToOne(() => Storage, (storage) => storage.posterConfig, { cascade: true, eager: true })
  @JoinColumn()
  orgLogo: Storage;

}

export default PosterConfig;