import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  ParseIntPipe,
  Post,
  Put,
  Query,
  Req,
  Request,
  UseGuards,
} from '@nestjs/common';
import { JwtAuthGuard } from 'src/modules/users/jwt/jwt-auth.guard';
import { GstrConfigService } from '../service/config.services';

@Controller('gstr-config')
export class GstrConfigController {
  constructor(private service: GstrConfigService) {}

  @UseGuards(JwtAuthGuard)
  @Get('atom-client/:id')
  async gstAtomClient(@Request() req: any, @Param('id', ParseIntPipe) id: number) {
    const { userId } = req.user;
    return this.service.gstAtomClient(userId, id);
  }

  @UseGuards(JwtAuthGuard)
  @Put('disableGstrClients')
  disableAtomProGstrClient(@Body() body: any, @Req() req: any) {
    const { userId } = req.user;
    return this.service.disableAtomProGstrClient(userId, body);
  }

  @UseGuards(JwtAuthGuard)
  @Put('disableGstr/:id')
  disableGstrSingleClient(@Req() req: any, @Param('id', ParseIntPipe) id: number) {
    const { userId } = req.user;
    return this.service.disableGstrSingleClient(id, userId);
  }

  @UseGuards(JwtAuthGuard)
  @Get('deletedGstrClients')
  getDeletedGstrClients(@Req() req: any, @Query() query: any) {
    const { userId } = req.user;
    return this.service.getDeletedGstrClients(userId, query);
  }
  @UseGuards(JwtAuthGuard)
  @Post('/export-deletedGstrClients')
  async exportdeletedGstClient(@Req() req: any, @Body() body: any) {
    const query = body;
    const { userId } = req.user;
    return this.service.exportdeletedGstClient(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @Put('enableGstr/:id')
  enableGstrClient(@Req() req: any, @Param('id', ParseIntPipe) id: number) {
    const { userId } = req.user;
    return this.service.enableGstrClient(id, userId);
  }

  @UseGuards(JwtAuthGuard)
  @Post('enableGstr/bulk-enable')
  enableBulkGstrClient(@Req() req: any, @Body() body: any) {
    const { userId } = req.user;
    return this.service.enableBulkGstrClient(userId, body);
  }

  @UseGuards(JwtAuthGuard)
  @Get('bulkSyncStatus')
  getBulkSyncStatus(@Req() req: any) {
    const { userId } = req.user;
    return this.service.getBulkSyncStatus(userId);
  }

  @UseGuards(JwtAuthGuard)
  @Put('enableStatus')
  enableStatus(@Req() req: any) {
    const { userId } = req.user;
    return this.service.updateEnableStatus(userId);
  }

  @UseGuards(JwtAuthGuard)
  @Put('disableStatus')
  updateDisableStatus(@Req() req: any) {
    const { userId } = req.user;
    return this.service.updateDisableStatus(userId);
  }

  @UseGuards(JwtAuthGuard)
  @Post('scheduling')
  async organizationScheduling(@Req() req: any, @Body() body: any) {
    const { userId } = req.user;
    return this.service.organizationGstrScheduling(userId, body);
  }

  @UseGuards(JwtAuthGuard)
  @Post('noticeAndOrder')
  createNoticeAndOrderItem(@Req() req: any, @Body() body: any) {
    const { userId } = req.user;
    return this.service.createNoticeAndOrderItem(userId, body);
  }

  @UseGuards(JwtAuthGuard)
  @Put('noticeAndOrder')
  updateNoticeAndOrder(@Req() req: any, @Body() body: any) {
    const { userId } = req.user;
    return this.service.updateNoticeAndOrder(userId, body);
  }

  @UseGuards(JwtAuthGuard)
  @Delete('noticeAndOrder/:id')
  deleteNoticeAndOrder(@Req() req: any, @Param('id', ParseIntPipe) id: number) {
    const { userId } = req.user;
    return this.service.deleteNoticeAndOrder(userId, id);
  }

  @UseGuards(JwtAuthGuard)
  @Post('additionalNotice')
  createAdditionalNotice(@Req() req: any, @Body() body: any) {
    const { userId } = req.user;
    return this.service.createAdditionalNotice(userId, body);
  }

  @UseGuards(JwtAuthGuard)
  @Put('additionalNotice')
  updateAdditionalNotice(@Req() req: any, @Body() body: any) {
    const { userId } = req.user;
    return this.service.updateAdditionalNotice(userId, body);
  }

  @UseGuards(JwtAuthGuard)
  @Delete('additionalNotice/:id')
  deleteAdditionalNotice(@Req() req: any, @Param('id', ParseIntPipe) id: number) {
    const { userId } = req.user;
    return this.service.deleteAdditionalNotice(userId, id);
  }

  @UseGuards(JwtAuthGuard)
  @Post('completeTaskGstrOne')
  completeTaskGstrOne(@Req() req: any, @Body() body: any) {
    const { userId } = req.user;
    return this.service.completeTasksWithAtomProSync(userId, body);
    // return this.service.completeTaskGstrOne(userId);
  }
}
