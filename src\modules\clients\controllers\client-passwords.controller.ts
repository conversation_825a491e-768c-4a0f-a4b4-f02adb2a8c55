import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  ParseIntPipe,
  Post,
  Put,
  Query,
  Req,
  UseGuards,
} from '@nestjs/common';
import { JwtAuthGuard } from 'src/modules/users/jwt/jwt-auth.guard';
import { CreateClientPasswordDto, UpdateClientPasswordDto } from '../dto/create-password.dto';
import { ClientPasswordService } from '../services/client-passwords.service';

@Controller('client-passwords')
export class ClientPasswordController {
  constructor(private service: ClientPasswordService) {}

  @UseGuards(JwtAuthGuard)
  @Post()
  async create(@Req() req: any, @Body() body: CreateClientPasswordDto) {
    const { userId } = req.user;
    return this.service.create(userId, body);
  }

  @Get()
  async get(@Query() query: any,@Req() req: any) {
    return this.service.find(query);
  }

  @UseGuards(JwtAuthGuard)
  @Post('/client-passwords-export')
  async exportClientPasswordexport(@Req() req: any, @Body() body: any) {
    const query = body;
    const clientId = body.clientId;
    const { userId } = req.user;
    return this.service.exportClientPasswordexport(clientId, query, userId);
  }

  @UseGuards(JwtAuthGuard)
  @Put('/:id')
  async update(
    @Req() req: any,
    @Param('id', ParseIntPipe) id: number,
    @Body() body: UpdateClientPasswordDto,
  ) {
    const { userId } = req.user;
    return this.service.update(userId, id, body);
  }

  @UseGuards(JwtAuthGuard)
  @Delete('/:id')
  async delete(@Param('id', ParseIntPipe) id: number, @Req() req: any) {
    const { userId } = req.user;
    return this.service.delete(id, userId);
  }

  @UseGuards(JwtAuthGuard)
  @Get('/atom-pro')
  async getClientCredentials( @Query() query: any,@Req() req: any) {
    const { userId } = req.user;
    return this.service.getClientCredentials(query,userId);
  }
}
