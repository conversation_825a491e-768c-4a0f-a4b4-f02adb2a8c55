import Category from 'src/modules/categories/categories.entity';
import { ArrayMinSize, ArrayNotEmpty, IsArray, IsNotEmpty, IsOptional, IsString, Matches, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';


class CreateSubCategoryDto {
  @IsNotEmpty()
  @IsString()
  @Matches(/^[^<>:"\/\\|?*\.]*$/, {
    message: 'name cannot contain <, >, :, ", /, \\, |, ?, *, or .',
  })
  name: string;
}

class CreateCategoryDto {

  @IsNotEmpty()
  @IsString()
  @Matches(/^[^<>:"\/\\|?*\.]*$/, {
    message: 'Category name cannot contain <, >, :, ", /, \\, |, ?, *, or .',
  })
  name: string;

  @IsOptional()
  @IsString()
  image: string;

  @IsOptional()
  @IsString()
  color: string;

  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CreateSubCategoryDto)
  subCategories: Category[];
}

export default CreateCategoryDto;
