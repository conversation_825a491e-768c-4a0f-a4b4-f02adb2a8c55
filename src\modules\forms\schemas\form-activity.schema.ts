import { <PERSON>p, Schem<PERSON>, SchemaFactory } from '@nestjs/mongoose';
import { Document, Schema as MangooseSchema } from 'mongoose';
import { Event_Actions } from 'src/event-listeners/actions';
import { ActivityType } from 'src/modules/activity/activity.entity';

export type FormActivityDocument = FormActivity & Document;

@Schema({ timestamps: true })
export class FormActivity extends Document {
  @Prop({ enum: ActivityType, required: true })
  type: ActivityType;

  @Prop({ type: MangooseSchema.Types.Mixed, required: true })
  typeId: any;

  @Prop({ required: true, enum: Event_Actions })
  action: Event_Actions;

  @Prop({ required: true })
  actorId: number;

  @Prop({ type: MangooseSchema.Types.Mixed, default: null })
  prevState: any;

  @Prop({ type: MangooseSchema.Types.Mixed, default: null })
  newState: any;

  @Prop({ type: String, default: '' })
  remarks: string;
}

export const FormActivitySchema = SchemaFactory.createForClass(FormActivity);
