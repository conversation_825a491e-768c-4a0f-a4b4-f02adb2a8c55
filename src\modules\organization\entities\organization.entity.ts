import Category from 'src/modules/categories/categories.entity';
import Client from 'src/modules/clients/entity/client.entity';
import Lead from 'src/modules/leads/lead.entity';
import Event from 'src/modules/events/event.entity';
import Label from 'src/modules/labels/label.entity';
import { Role } from 'src/modules/roles/entities/role.entity';
import { Service } from 'src/modules/services/entities/service.entity';
import Storage, { StorageType } from 'src/modules/storage/storage.entity';
import Task from 'src/modules/tasks/entity/task.entity';
import { User } from 'src/modules/users/entities/user.entity';

import {
  AfterLoad,
  BaseEntity,
  Column,
  CreateDateColumn,
  Entity,
  JoinColumn,
  JoinTable,
  ManyToMany,
  OneToMany,
  OneToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { BankAccount } from './bank-account.entity';
import { BillingEntity } from './billing-entity.entity';
import { OrganizationLicense } from './organization-license.entity';
import Team from './team.entity';
import { Invoice } from 'src/modules/billing/entitities/invoice.entity';
import Receipt from 'src/modules/billing/entitities/receipt.entity';
import DscRegister from 'src/modules/dsc-register/entity/dsc-register.entity';
import GstrRegister from 'src/modules/gstr-register/entity/gstr-register.entity';
import { GstrPromise } from 'src/modules/gstr-register/entity/promise.entity';
import OrganizationPreferences from 'src/modules/organization-preferences/entity/organization-preferences.entity';
import { Wallets } from 'src/modules/wallet/entities/wallets.entity';
import OrgApprovals from 'src/modules/atm-qtm-approval/entities/org-approvals.entity';
import DscApply from 'src/modules/dsc-register/entity/dsc-apply.entity';
import BudgetedHours from 'src/modules/budgeted-hours/budgeted-hours.entity';
import UdinTask from 'src/modules/udin-task/udin-task.entity';
import ClientGroupBroadcast from 'src/modules/communication/entity/client-group-broadcast.entity';
import BroadcastEmailTemplates from 'src/modules/communication/entity/broadcast-email-templates-entity';
import BroadcastActivity from 'src/modules/communication/entity/broadcast-activity.entity';
import { ProformaInvoice } from 'src/modules/billing/entitities/proforma-invoice.entity';
import DocumentInOut from 'src/modules/document-in-out/entity/document-in-out.entity';
import DocumentCategory from 'src/modules/document-in-out/entity/document-category.entity';

export enum OrganizationCategory {
  INDIVIDUAL = 'INDIVIDUAL',
  HINDU_UNDIVIDED_FAMILY = 'HINDU_UNDIVIDED_FAMILY',
  PARTNERSHIP_FIRM = 'PARTNERSHIP_FIRM',
  LLP = 'LLP',
  COMPANY = 'COMPANY',
  ASSOCIATION_OF_PERSONS = 'ASSOCIATION_OF_PERSONS',
  BODY_OF_INDIVIDUALS = 'BODY_OF_INDIVIDUALS',
  TRUST = 'TRUST',
  GOVERNMENT = 'GOVERNMENT',
  LOCAL_AUTHORITY = 'LOCAL_AUTHORITY',
  ARTIFICIAL_JURIDICAL_PERSON = 'ARTIFICIAL_JURIDICAL_PERSON',
}

export enum StorageSystem {
  AMAZON = 'AMAZON',
  MICROSOFT = 'MICROSOFT',
  GOOGLE = 'GOOGLE',
  BHARATHCLOUD = 'BHARATHCLOUD'
}

@Entity()
export class Organization extends BaseEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ type: 'enum', enum: OrganizationCategory, default: OrganizationCategory.COMPANY })
  category: OrganizationCategory;

  @Column({ type: 'enum', enum: StorageSystem, default: StorageSystem.AMAZON })
  storageSystem: StorageSystem;

  @Column({ nullable: true })
  legalName: string;

  @Column({ nullable: true })
  tradeName: string;

  @Column({ nullable: true })
  placeOfSupply: string;

  @Column({ nullable: true })
  constitutionOfBusiness: string;

  @Column({ nullable: true })
  registrationNumber: string;

  @Column({ nullable: true })
  registrationDate: string;

  @Column({ nullable: true })
  dateOfFormation: string;

  @Column({ default: false })
  gstVerified: boolean;

  @Column({ nullable: true })
  gstNumber: string;

  @Column({ nullable: true })
  gstStatus: string;

  @Column({ nullable: true })
  gstAttachment: string;

  @OneToOne(() => Storage, (storage) => storage.orgGstStorage, { cascade: true })
  orgGstStorage: Storage;

  @OneToOne(() => Storage, (storage) => storage.orgPanStorage, { cascade: true })
  orgPanStorage: Storage;

  @Column({ nullable: true })
  panNumber: string;

  @Column({ default: false })
  panVerified: boolean;

  @Column({ nullable: true })
  panAttachment: string;

  @Column({ nullable: true })
  firstName: string;

  @Column({ nullable: true })
  middleName: string;

  @Column({ nullable: true })
  lastName: string;

  @Column({ nullable: true })
  mobileNumber: string;

  @Column({ nullable: true })
  alternateMobileNumber: string;

  @Column({ nullable: true })
  email: string;

  @Column({ nullable: true })
  website: string;

  @Column({ nullable: true })
  primaryContactFullName: string;

  @Column({ nullable: true })
  primaryContactEmail: string;

  @Column({ nullable: true })
  primaryContactMobileNumber: string;

  @Column({ nullable: true })
  primaryContactDesignation: string;

  @Column({ nullable: true })
  buildingNo: string;

  @Column({ nullable: true })
  floorNumber: string;

  @Column({ nullable: true })
  buildingName: string;

  @Column({ nullable: true })
  street: string;

  @Column({ nullable: true })
  location: string;

  @Column({ nullable: true })
  city: string;

  @Column({ nullable: true })
  district: string;

  @Column({ nullable: true })
  state: string;

  @Column({ nullable: true })
  pincode: string;

  @Column({ default: true })
  isActive: boolean;

  @Column({ nullable: true })
  logo: string;

  @Column({ nullable: true })
  config: string;

  @Column({ nullable: true })
  loggedIn: string;

  @Column('json', { array: true })
  smtp: object;

  @Column('json', { array: true })
  broadcastSmtp: object;

  @Column('json', { array: true })
  clientSmtp: object;

  @Column('json', { array: true })
  iproSmtp: object;

  @Column('json', { array: true })
  billingSmtp: object;

  @Column('json', { array: true })
  othersSmtp: object;

  @Column({ nullable: true })
  countryCode: string;

  @Column({ nullable: true })
  alternateCountryCode: string;

  @Column({ nullable: true })
  primaryContactCountryCode: string;

  @OneToMany(() => User, (user) => user.organization)
  users: User[];

  @ManyToMany(() => User, (user) => user.udinUsers)
  @JoinTable()
  udinUsers: User[];

  @OneToMany(() => Role, (role) => role.organization, { cascade: true })
  roles: Role[];

  @OneToMany(() => Task, (task) => task.organization)
  tasks: Task[];

  @OneToMany(() => DocumentInOut, (documentInOut) => documentInOut.organization)
  documentInOut: DocumentInOut[];

  @OneToMany(() => DocumentCategory, (documentCategory) => documentCategory.organization)
  documentCategory: DocumentCategory[];

  @OneToMany(() => UdinTask, (udinTask) => udinTask.organization)
  udinTasks: UdinTask[];

  @OneToMany(() => BudgetedHours, (budgetedHours) => budgetedHours.organization)
  budgetedHours: BudgetedHours[];

  @OneToMany(() => Client, (client) => client.organization)
  clients: Client[];

  @OneToMany(() => Client, (client) => client.organization)
  clientGroup: Client[];

  @OneToMany(() => Event, (event) => event.organization)
  events: Event[];

  @OneToMany(() => Lead, (lead) => lead.organization)
  leads: Lead[];

  @OneToMany(() => DscRegister, (dscRegister) => dscRegister.organization)
  dscRegisters: DscRegister[];

  @OneToMany(() => OrgApprovals, (orgApprovals) => orgApprovals.organization)
  orgApprovals: OrgApprovals[];

  @OneToMany(() => GstrRegister, (gstrRegister) => gstrRegister.client)
  gstrRegisters: GstrRegister[];

  @OneToMany(
    () => OrganizationPreferences,
    (organizationPreferences) => organizationPreferences.organization,
  )
  organizationPreferences: OrganizationPreferences[];

  @OneToMany(() => GstrPromise, (gstrPromise) => gstrPromise.organization)
  gstrPromises: GstrPromise[];

  @OneToMany(() => Storage, (storage) => storage.organization)
  storage: Storage[];

  @OneToMany(() => Invoice, (invoice) => invoice.organization)
  invoices: Invoice[];

  @OneToMany(() => ProformaInvoice, (proformaInvoice) => proformaInvoice.organization)
  proformaInvoice: ProformaInvoice[];


  @OneToMany(() => Team, (team) => team.organization)
  teams: Team[];

  @OneToMany(() => OrganizationLicense, (license) => license.organization)
  licenses: OrganizationLicense[];

  @OneToMany(() => BillingEntity, (billingEntity) => billingEntity.organization)
  billingEntities: BillingEntity[];

  @OneToMany(() => BankAccount, (bankAccount) => bankAccount.organization)
  bankAccounts: BankAccount[];

  @OneToMany(() => Category, (category) => category.organization)
  categories: Category[];

  @OneToMany(() => Label, (label) => label.organization)
  labels: Label[];

  @OneToMany(() => Service, (service) => service.organization)
  services: Service[];

  @OneToMany(() => Receipt, (receipt) => receipt.organization)
  receipts: Receipt[];

  @OneToMany(() => Wallets, (wallets) => wallets.organization)
  wallets: Wallets[];

  @OneToMany(() => DscApply, (dscapply) => dscapply.organization)
  dscapply: DscApply[];

  @OneToMany(() => ClientGroupBroadcast, (clientGroupBroadcast) => clientGroupBroadcast.organization)
  clientGroupBroadcast: ClientGroupBroadcast[];

  @OneToMany(() => BroadcastEmailTemplates, (broadcastEmailTemplates) => broadcastEmailTemplates.organization)
  broadcastEmailTemplates: BroadcastEmailTemplates[];

  @OneToMany(() => BroadcastActivity, (broadcastActivity) => broadcastActivity.organization)
  broadcastActivity: BroadcastActivity[];

  @Column({ default: true })
  hasStorage: boolean;

  @Column({ default: false })
  onedriveAuthorize: boolean;

  @CreateDateColumn()
  createdAt: string;

  @UpdateDateColumn()
  updatedAt: string;

  logoUrl: string;

  gstAttachmentUrl: string;

  panAttachmentUrl: string;

  @AfterLoad()
  renderUrl() {
    if (this.logo) {
      this.logoUrl = `${process.env.AWS_BASE_URL}/${this.logo}`;
    }
    if (this.gstAttachment) {
      this.gstAttachmentUrl = `${process.env.AWS_BASE_URL}/${this.gstAttachment}`;
    }
    if (this.panAttachment) {
      this.panAttachmentUrl = `${process.env.AWS_BASE_URL}/${this.panAttachment}`;
    }
  }
}
